﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.InteropServices;
using FileTranser;

namespace TJ.EMR.Utility
{
    public class FileTrans
    {
        #region 变量声明
        [DllImport("fsrv.dll")]
        public static extern int put_file(string host_addr, string local_file, string remote_file, int option);

        [DllImport("fsrv.dll")]
        public static extern int get_file(string host_addr, string remote_file, string local_file, int option);

        [DllImport("fsrv.dll")]
        public static extern int delete_file(string host_addr, string remote_file);
        #endregion 

        /// <summary>
        /// 上传本地的指定文件到服务器
        /// </summary>
        /// <param name="HostAddr">服务器地址</param>
        /// <param name="LocalFile">本地文件完全路径</param>
        /// <param name="RemoteFile">服务器文件完全路径</param>
        /// <param name="transType">传输类型</param>
        /// <param name="File_User">用户名</param>
        /// <param name="File_Pwd">密码</param>
        /// <param name="option"></param>
        /// <returns>0 成功</returns>
        public static int PutFile(String HostAddr, String File_User, String File_Pwd, String LocalFile, String RemoteFile, int Option, int transType)
        {
            if (transType == 1)
            {
                return put_file(HostAddr, LocalFile, RemoteFile, Option);
            }
            else if (transType == 3)
            {
                FileSend myFileClient = new FileSend(LocalFile, RemoteFile, HostAddr, 520);
                return int.Parse(myFileClient.SendStr());
            }
            else
            {
                FtpUpDown ftpUpDown = new FtpUpDown(HostAddr,File_User,File_Pwd);
                RemoteFile.Substring(RemoteFile.LastIndexOf(':')+1);
                return ftpUpDown.Upload(RemoteFile.Replace('\\','/').Replace("//","/"),LocalFile);
            }
        }

        /// <summary>
        /// 下载服务器上指定的文件到本地
        /// </summary>
        /// <param name="HostAddr">服务器地址</param>        
        /// <param name="RemoteFile">服务器文件完全路径</param>
        /// <param name="LocalFile">本地文件完全路径</param>
        /// <param name="transType">传输类型</param>
        /// <param name="File_User">用户名</param>
        /// <param name="File_Pwd">密码</param>
        /// <param name="option"></param>
        /// <returns>0 成功</returns>
        public static int GetFile(String HostAddr, String File_User, String File_Pwd, String RemoteFile, String LocalFile, int Option, int transType)
        {
            if (transType == 1)
            {
                return get_file(HostAddr, RemoteFile, LocalFile, Option);
            }
            else if (transType == 3)
            {
                string WantedPath = RemoteFile.Substring(3, RemoteFile.LastIndexOf(@"\") - 2);
                string CmdStr = "@Download|" + RemoteFile + "|" + WantedPath + "|" + LocalFile;
                MsgSend myMsgClient = new MsgSend(CmdStr, HostAddr, 520);
                return int.Parse(myMsgClient.MsgSendStr());
            }
            else
            {
                FtpUpDown ftpUpDown = new FtpUpDown(HostAddr, File_User, File_Pwd);
                RemoteFile.Substring(RemoteFile.LastIndexOf(':') + 1);
                return ftpUpDown.Download(RemoteFile.Replace('\\', '/').Replace("//", "/"), LocalFile);
            }
        }


        /// <summary>
        /// 删除服务器上的指定文件
        /// </summary>
        /// <param name="HostAddr">服务器地址</param>        
        /// <param name="RemoteFile">服务器文件完全路径</param>
        /// <returns>0 成功</returns>
        public static int DeleteFile(String HostAddr, String RemoteFile, int transType)
        {
            if (transType == 1)
            {
                return delete_file(HostAddr, RemoteFile);
            }
            else if (transType == 3)
            {
                string WantedPath = RemoteFile.Substring(3, RemoteFile.LastIndexOf(@"\") - 2);
                RemoteFile = RemoteFile.Substring(0, RemoteFile.LastIndexOf(@"\"));
                string CmdStr = "@Download|" + RemoteFile + "|" + WantedPath;
                MsgSend myMsgClient = new MsgSend(CmdStr, HostAddr, 520);
                return int.Parse(myMsgClient.MsgSendStr());
            }
            else 
            {
                return -1;
            }
        }
    }
}
