﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Utility
{
    public static class UntilityConstant
    {
        /// <summary>
        /// 数据库连接方式，app参数设置KEY值
        /// </summary>
        public static String DataBaseConnectionMode = "DataBaseConnectionMode";
        /// <summary>
        /// 数据库连接方式,系统默认模式
        /// </summary>
        public static String DataBaseConnectionDefaultMode = "ODP";
        /// <summary>
        /// 数据库连接方式,连接字符串
        /// </summary>
        public static String DataConnectionString = "";
        /// <summary>
        /// OracleClient连接，方便青蛙跟踪
        /// </summary>
        public static System.Data.OracleClient.OracleConnection Connection = null;
        /// <summary>
        /// 数据库连接方式 <!--数据库连接模式 0 默认OracleClient 方便工具跟踪数据库操作，1 Oracle官方的高效数据库连接，工具不能跟踪数据库操作-->
        /// </summary>
        public static int DBConnectionMode = 0;
        /// <summary>
        /// 是否写日志 1-写，其他不写
        /// </summary>
        public static string LogIsWrite = "0";
        /// <summary>
        /// 连接方式，webservice还是客户端 0默认本地客户端，1是web服务
        /// </summary>
        public static string ConnectionMethod = "0";
    }
}
