﻿/*----------------------------------------------------------------
            // Copyright (C) 2014 天健源达科技有限公司
            // 版权所有。 
            //
            // 文件名：LogFile.cs
            // 文件功能描述：通用日志文件类
            //
            // 
            // 创建标识：杨帆 20140701
            //
            // 修改标识：
            // 修改描述：
            //
            // 修改标识：
            // 修改描述：
//----------------------------------------------------------------*/
using System;
using System.IO;
using System.Text;
using System.Net;
namespace Utility
{
    public class LogFile
    {
        /// <summary>
        /// 判断是否是隐藏文件
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns></returns>
        public bool IsHidden(string path)
        {
            FileAttributes MyAttributes = File.GetAttributes(path);
            string MyFileType = MyAttributes.ToString();
            if (MyFileType.LastIndexOf("Hidden") != -1) //是否隐藏文件
            {
                return true;
            }
            else
                return false;
        }

        ///<summary>
        ///创建指定目录
        ///</summary>
        ///<param name="targetDir"></param>
        public void CreateDirectory(string targetDir)
        {
            DirectoryInfo dir = new DirectoryInfo(targetDir);
            if (!dir.Exists)
                dir.Create();

        }

        //==删除文件或目录======================================================//
        /// <summary>
        /// 删除指定的文件
        /// </summary>
        /// <param name="targetDir"></param>
        /// <param name="fileName"></param>
        public static void DeleteFile(string targetDir, string FileName)
        {
            foreach (string fileName in Directory.GetFiles(targetDir))
            {
                string[] fname = fileName.Split('\\');
                if (fname.Length > 1)
                {
                    if (fname[fname.Length - 1].Trim() == FileName.Trim())
                    {
                        File.SetAttributes(fileName, FileAttributes.Normal);
                        File.Delete(fileName);
                    }
                }
            }
        }


        ///<summary>
        ///删除指定目录的所有文件和子目录
        ///</summary>
        ///<param name="targetDir">操作目录</param>
        ///<param name="delSubDir">如果为true,包含对子目录的操作</param>
        public static void DeleteFiles(string targetDir, bool delSubDir)
        {
            foreach (string fileName in Directory.GetFiles(targetDir))
            {
                File.SetAttributes(fileName, FileAttributes.Normal);
                File.Delete(fileName);
            }
            if (delSubDir)
            {
                DirectoryInfo dir = new DirectoryInfo(targetDir);
                foreach (DirectoryInfo subDi in dir.GetDirectories())
                {
                    DeleteFiles(subDi.FullName, true);
                    subDi.Delete();
                }

            }
        }

        ///<summary>
        ///删除指定目录的所有子目录,不包括对当前目录文件的删除
        ///</summary>
        ///<param name="targetDir">目录路径</param>
        public static void DeleteSubDirectory(string targetDir)
        {
            foreach (string subDir in Directory.GetDirectories(targetDir))
            {
                DeleteDirectory(subDir);
            }
        }

        ///<summary>
        ///删除指定目录,包括当前目录和所有子目录和文件
        ///</summary>
        ///<param name="targetDir">目录路径</param>
        public static void DeleteDirectory(string targetDir)
        {
            DirectoryInfo dirInfo = new DirectoryInfo(targetDir);
            if (dirInfo.Exists)
            {
                DeleteFiles(targetDir, true);
                dirInfo.Delete(true);
            }
        }

        //==移动（剪切）文件或目录===============================================//

        ///<summary>
        ///剪切指定目录下的文件
        ///</summary>
        ///<param name="sourceDir">原始目录</param>
        ///<param name="targetDir">目标目录</param>
        ///<param name="overWrite">如果为true,覆盖同名文件,否则不覆盖</param>
        ///<param name="fileName">需要移动的文件名</param>
        public static void MoveFile(string sourceDir, string targetDir, bool overWrite, string fileName)
        {
            //移动当前目录文件
            foreach (string sourceFileName in Directory.GetFiles(sourceDir))
            {
                string[] fname = sourceFileName.Split('\\');
                if (fname.Length > 1)
                {
                    if (fname[fname.Length - 1].Trim() == fileName.Trim())
                    {
                        string targetFileName = Path.Combine(targetDir, sourceFileName.Substring(sourceFileName.LastIndexOf("\\") + 1));
                        if (File.Exists(targetFileName))
                        {
                            if (overWrite == true)
                            {
                                File.SetAttributes(targetFileName, FileAttributes.Normal);
                                File.Delete(targetFileName);
                                File.Move(sourceFileName, targetFileName);
                            }
                        }
                        else
                        {
                            File.Move(sourceFileName, targetFileName);
                        }
                    }
                }
            }
        }

        ///<summary>
        ///剪切指定目录的所有文件
        ///</summary>
        ///<param name="sourceDir">原始目录</param>
        ///<param name="targetDir">目标目录</param>
        ///<param name="overWrite">如果为true,覆盖同名文件,否则不覆盖</param>
        ///<param name="moveSubDir">如果为true,包含目录,否则不包含</param>
        public static void MoveFiles(string sourceDir, string targetDir, bool overWrite, bool moveSubDir)
        {
            //移动当前目录文件
            foreach (string sourceFileName in Directory.GetFiles(sourceDir))
            {
                string targetFileName = Path.Combine(targetDir, sourceFileName.Substring(sourceFileName.LastIndexOf("\\") + 1));
                if (File.Exists(targetFileName))
                {
                    if (overWrite == true)
                    {
                        File.SetAttributes(targetFileName, FileAttributes.Normal);
                        File.Delete(targetFileName);
                        File.Move(sourceFileName, targetFileName);
                    }
                }
                else
                {
                    File.Move(sourceFileName, targetFileName);
                }
            }
            if (moveSubDir)
            {
                foreach (string sourceSubDir in Directory.GetDirectories(sourceDir))
                {
                    string targetSubDir = Path.Combine(targetDir, sourceSubDir.Substring(sourceSubDir.LastIndexOf("\\") + 1));
                    if (!Directory.Exists(targetSubDir))
                        Directory.CreateDirectory(targetSubDir);
                    MoveFiles(sourceSubDir, targetSubDir, overWrite, true);
                    Directory.Delete(sourceSubDir);
                }
            }
        }

        //==复制文件或目录==================================================//

        ///<summary>
        ///复制指定目录的所有文件
        ///</summary>
        ///<param name="sourceDir">原始目录</param>
        ///<param name="targetDir">目标目录</param>
        ///<param name="overWrite">如果为true,覆盖同名文件,否则不覆盖</param>
        ///<param name="copySubDir">如果为true,包含目录,否则不包含</param>
        public static void CopyFiles(string sourceDir, string targetDir, bool overWrite, bool copySubDir)
        {
            //复制当前目录文件
            foreach (string sourceFileName in Directory.GetFiles(sourceDir))
            {
                string targetFileName = Path.Combine(targetDir, sourceFileName.Substring(sourceFileName.LastIndexOf("\\") + 1));
                if (File.Exists(targetFileName))
                {
                    if (overWrite == true)
                    {
                        File.SetAttributes(targetFileName, FileAttributes.Normal);
                        File.Copy(sourceFileName, targetFileName, overWrite);
                    }
                }
                else
                {
                    File.Copy(sourceFileName, targetFileName, overWrite);
                }
            }
            //复制子目录
            if (copySubDir)
            {
                foreach (string sourceSubDir in Directory.GetDirectories(sourceDir))
                {
                    string targetSubDir = Path.Combine(targetDir, sourceSubDir.Substring(sourceSubDir.LastIndexOf("\\") + 1));
                    if (!Directory.Exists(targetSubDir))
                        Directory.CreateDirectory(targetSubDir);
                    CopyFiles(sourceSubDir, targetSubDir, overWrite, true);
                }
            }
        }

        //==扫描文件夹及其子目录================================================//

        /// <summary>
        /// 获取子目录文件信息
        /// </summary>
        /// <param name="targetDir">文件夹名称</param>
        public void getChildFileInfo(string targetDir)
        {
            try
            {
                foreach (string fileName in Directory.GetFiles(targetDir))
                {
                    FileInfo fileinfo = new FileInfo(fileName);
                    string name = fileName.Substring(fileName.LastIndexOf("\\") + 1);//文件名
                    string address = fileName; //地址
                    string size = fileinfo.Length.ToString();//文件大小,字节
                    string createtime = fileinfo.CreationTime.ToString();//文件创建时
                }
                foreach (string directory in Directory.GetDirectories(targetDir))
                {

                    //在此可以获取文件夹相关信息
                    getChildFileInfo(directory);//递归调用
                }

            }
            catch
            {
            }
        }

        //int n = 要删掉的行数;            
        //string[] lines = System.IO.File.ReadAllLines(@"d:\a.txt");
        //System.IO.File.WriteAllText(@"d:\a.txt", string.Join(Environment.NewLine, lines, n, lines.Length - n));
        /// <summary>
        /// 查找字符串在原字符串中出现的行数
        /// </summary>
        /// <param name="content">主文本</param>
        /// <param name="findText">查询文本</param>
        /// <param name="startIndex">开始行</param>
        /// <returns></returns>
        public int GetFindTextLine(string content, string findText, int startIndex)
        {
            //标准化参数
            string text1 = content.Replace("\r\n", "\r");
            findText = findText.Replace("\r\n", "\r");
            if (startIndex < 0)
            {
                startIndex = 0;
            }
            string[] t1 = text1.Split('\r');
            int count = t1.Length;
            int len = findText.Split('\r').Length;
            int intStartIndex = startIndex;
            StringBuilder strb = new StringBuilder();
            for (int i = 0; i < count; i++)
            {
                int t_i = 0;
                for (int j = 0; j < len; j++)
                {
                    if (j > 0)
                    {
                        strb.Append("\r");
                    }
                    t_i = i + j;
                    if (t_i < count)
                    {
                        strb.Append(t1[i + j]);
                    }
                }
                //查找是否相等
                intStartIndex = strb.ToString().IndexOf(findText, 0);
                if (intStartIndex > -1)
                {
                    return i + 1;
                }
                strb.Remove(0, strb.Length);
            }
            return 0;
        }

        public static void WriteLogAuto(String msgstr, String FileName)
        {
            try
            {
                //if (msgstr == String.Empty)
                //{
                //    return;                         //ex = null 返回
                //}
                //StreamWriter write = null;
                //DateTime dt = DateTime.Now;       // 设置日志时间
                //string time = dt.ToString("yyyy-MM-dd HH:mm:ss"); //年-月-日 时：分：秒
                //string LogName = FileName + ".log";       //日志名称
                //string Temp_Path = System.Reflection.Assembly.GetExecutingAssembly().Location;
                //Temp_Path = Temp_Path.Substring(0, Temp_Path.LastIndexOf(@"\"));
                //string LogPath = Temp_Path + "\\LOG\\" + dt.ToString("yyyy-MM-dd") + "\\";   //日志存放路径
                //string Log = LogPath + LogName;   //路径 + 名称
                //if (!Directory.Exists(LogPath))
                //{
                //    Directory..CreateDirectory(LogPath);   //创建文件夹
                //}
                //if (!File.Exists(Log))             //是否存在
                //{

                //    write = File.CreateText(Log);     // 创建日志
                //}
                //else
                //{
                //    System.IO.FileInfo file = new System.IO.FileInfo(Log);
                //    if (file.Length > 1000 * 1000)
                //    {
                //        if (!File.Exists(LogPath + @"Back"))             //是否存在
                //        {
                //            Directory.CreateDirectory(LogPath + @"Back");   //创建文件夹
                //        }
                //        file.MoveTo(LogPath + @"Back\" + FileName + "" + DateTime.Now.ToString("yyyy年MM月dd日HH时mm分ss") + ".txt");
                //        Directory.CreateDirectory(LogPath);   //创建文件夹
                //        write = File.CreateText(Log);     // 创建日志
                //    }
                //    else
                //    {
                //        write = File.AppendText(Log);         //追加，添加错误信息；
                //    }
                //}
                //write.WriteLine(time);
                //write.WriteLine(msgstr);
                //write.WriteLine("-----------------------------------");
                //write.Flush();
                //write.Dispose();


                //String biaoti = "通辽市医院，机器号：" + Dns.GetHostName() + "出现异常";
                //String neirong = msgstr;
                if (msgstr == String.Empty)
                {
                    return;                         //ex = null 返回
                }
                DateTime dt = DateTime.Now;       // 设置日志时间
                string time = dt.ToString("yyyy-MM-dd HH:mm:ss"); //年-月-日 时：分：秒
                string LogName = FileName + ".log";       //日志名称
                string Temp_Path = System.Reflection.Assembly.GetExecutingAssembly().Location;
                Temp_Path = Temp_Path.Substring(0, Temp_Path.LastIndexOf(@"\"));
                string LogPath = Temp_Path + "\\LOG\\" + dt.ToString("yyyy-MM-dd") + "\\";   //日志存放路径
                string Log = LogPath + LogName;   //路径 + 名称
                if (!Directory.Exists(LogPath))
                {
                    Directory.CreateDirectory(LogPath);   //创建文件夹
                }
                string createflag = "0";
                if (!File.Exists(Log))             //是否存在
                {
                    //write = File.CreateText(Log);     // 创建日志
                }
                else
                {
                    System.IO.FileInfo file = new System.IO.FileInfo(Log);
                    if (file.Length > 1000 * 1000)
                    {
                        if (!File.Exists(LogPath + @"Back"))             //是否存在
                        {
                            Directory.CreateDirectory(LogPath + @"Back");   //创建文件夹
                        }
                        file.MoveTo(LogPath + @"Back\" + FileName + "" + DateTime.Now.ToString("yyyy年MM月dd日HH时mm分ss") + ".txt");
                        //Directory.CreateDirectory(LogPath);   //创建文件夹
                        //write = File.CreateText(Log);     // 创建日志
                        createflag = "0";
                    }
                    else
                    {
                        createflag = "1";
                        //write = File.AppendText(Log);         //追加，添加错误信息；
                    }
                }
                if ("0".Equals(createflag))
                {
                    using (StreamWriter write = File.CreateText(Log))
                    {
                        write.WriteLine(time);
                        write.WriteLine(msgstr);
                        write.WriteLine("-----------------------------------");
                        write.Flush();
                    }
                }
                else
                {
                    using (StreamWriter write = File.AppendText(Log))
                    {
                        write.WriteLine(time);
                        write.WriteLine(msgstr);
                        write.WriteLine("-----------------------------------");
                        write.Flush();
                    }
                }
            }
            catch (Exception e)
            {
                WriteLogAutoError(e, "日志写入异常", "日志写入失败");
            }
        }
        public static void WriteLogAutoError(Exception ex, string str, String FileName)
        {
            try
            {
                if (ex == null)
                {
                    return;                         //ex = null 返回
                }
                //StreamWriter write = null;
                //DateTime dt = DateTime.Now;       // 设置日志时间
                //string time = dt.ToString("yyyy-MM-dd HH:mm:ss"); //年-月-日 时：分：秒
                //string LogName = FileName + ".log";    //日志名称
                //string Temp_Path = System.Reflection.Assembly.GetExecutingAssembly().Location;
                //Temp_Path = Temp_Path.Substring(0, Temp_Path.LastIndexOf(@"\"));
                //string LogPath = Temp_Path + "\\LOG\\" + dt.ToString("yyyy-MM-dd") + "\\";  //日志存放路径
                //string Log = LogPath + LogName;   //路径 + 名称
                //if (!File.Exists(Log))             //是否存在
                //{
                //    Directory.CreateDirectory(LogPath);   //创建文件夹
                //    write = File.CreateText(Log);     // 创建日志
                //}
                //else
                //{
                //    System.IO.FileInfo file = new System.IO.FileInfo(Log);
                //    if (file.Length > 1000 * 1000)
                //    {
                //        if (!File.Exists(LogPath + @"Back"))             //是否存在
                //        {
                //            Directory.CreateDirectory(LogPath + @"Back");   //创建文件夹
                //        }
                //        file.MoveTo(LogPath + @"Back\" + FileName + "" + DateTime.Now.ToString("yyyy年MM月dd日HH时mm分ss") + ".txt");
                //        Directory.CreateDirectory(LogPath);   //创建文件夹
                //        write = File.CreateText(Log);     // 创建日志
                //    }
                //    else
                //    {
                //        write = File.AppendText(Log);         //追加，添加错误信息；
                //    }
                //}
                DateTime dt = DateTime.Now;       // 设置日志时间
                string time = dt.ToString("yyyy-MM-dd HH:mm:ss"); //年-月-日 时：分：秒
                string LogName = FileName + ".log";       //日志名称
                string Temp_Path = System.Reflection.Assembly.GetExecutingAssembly().Location;
                Temp_Path = Temp_Path.Substring(0, Temp_Path.LastIndexOf(@"\"));
                string LogPath = Temp_Path + "\\LOG\\" + dt.ToString("yyyy-MM-dd") + "\\";   //日志存放路径
                string Log = LogPath + LogName;   //路径 + 名称
                if (!Directory.Exists(LogPath))
                {
                    Directory.CreateDirectory(LogPath);   //创建文件夹
                }
                string createflag = "0";
                if (!File.Exists(Log))             //是否存在
                {
                    //write = File.CreateText(Log);     // 创建日志
                }
                else
                {
                    System.IO.FileInfo file = new System.IO.FileInfo(Log);
                    if (file.Length > 1000 * 1000)
                    {
                        if (!File.Exists(LogPath + @"Back"))             //是否存在
                        {
                            Directory.CreateDirectory(LogPath + @"Back");   //创建文件夹
                        }
                        file.MoveTo(LogPath + @"Back\" + FileName + "" + DateTime.Now.ToString("yyyy年MM月dd日HH时mm分ss") + ".txt");
                        //Directory.CreateDirectory(LogPath);   //创建文件夹
                        //write = File.CreateText(Log);     // 创建日志
                        createflag = "0";
                    }
                    else
                    {
                        createflag = "1";
                        //write = File.AppendText(Log);         //追加，添加错误信息；
                    }
                }
                if ("0".Equals(createflag))
                {
                    using (StreamWriter write = File.CreateText(Log))
                    {
                        write.WriteLine(time);
                        write.WriteLine(ex.Message);
                        write.WriteLine("异    常：" + str);
                        write.WriteLine("异常信息：" + ex.ToString());
                        write.WriteLine("异常堆栈：" + ex.StackTrace.ToString());
                        write.WriteLine("-------------------------------");
                        write.Flush();
                    }
                }
                else
                {
                    using (StreamWriter write = File.AppendText(Log))
                    {
                        write.WriteLine(time);
                        write.WriteLine(ex.Message);
                        write.WriteLine("异    常：" + str);
                        write.WriteLine("异常信息：" + ex.ToString());
                        write.WriteLine("异常堆栈：" + ex.StackTrace.ToString());
                        write.WriteLine("-------------------------------");
                        write.Flush();
                    }
                }
                //String biaoti = "通辽市医院，机器号：" + Dns.GetHostName() + "出现异常";
                //String neirong = ex.Message + "\r\n";
                //neirong += "异    常：" + str + "\r\n";
                //neirong += "异常信息：" + ex.ToString() + "\r\n";
                //neirong += "异常堆栈：" + ex.StackTrace.ToString() + "\r\n";
                
            }
            catch (Exception e)
            {
                throw new Exception( "异常日志写入失败"+ e.Message);
            }
        }
    }
}