﻿//-----------------------------------------------------------------------
//  系统名称        : 基础类库
//  子系统名称      : 数据模型
//  功能概要        : 功能模块参数
//  作  者          : 付军
//  创建时间        : 2018-04-03
//  版本            : 1.0.0.0
//-----------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.Web;

namespace Tjhis.Report.Custom.Base
{    
    /// <summary>
    /// 功能模块参数
    /// </summary>
    public class ModuleParam
    {
        /// <summary>
        /// 参数名
        /// </summary>
        public string ParamName { get; set; }
        
        // 参数类型
        public ModuleParamType ParamType { get; set; }
        
        // 参数说明
        public string ParamDesc { get; set; }

        // 参数值
        public object ParamValue { get; set; }


        public ModuleParam(string paramName, ModuleParamType paramType, string paramDesc, object paramValue)
        {
            this.ParamName = paramName;
            this.ParamType = paramType;
            this.ParamDesc = paramDesc;
            this.ParamValue = paramValue;
        }
    }


    /// <summary>
    /// 参数类型
    /// </summary>
    public enum ModuleParamType
    {
        String = 0,
        Number = 1,
        DateTime = 2
    };
}