﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using DevExpress.Utils;
using DevExpress.XtraGrid.Columns;

namespace Tjhis.Controls.StatisticsControls
{
    public partial class ucPersonalDate : RadiusUserControl
    {
        public DateTime StartDate { get; set; }
        public DataTable dtSchedule { get; set; }
        public event EventHandler NextButtonClick;
        public event EventHandler PreButtonClick;
        public event EventHandler ScheduleAddButtonClick;
        public DataTable dtScheduleMemorandum { get; set; }
        private List<string> TimeDescs = new List<string>
        {
            "上午",
            "下午",
            "晚上"
        };
        
        private List<DateTime> dateTimes;
        public ucPersonalDate()
        {
            InitializeComponent();
        }

        private void GvView_CustomDrawRowIndicator(object sender, DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventArgs e)
        {
            if (e.Info.IsRowIndicator)
            {
                e.Info.DisplayText = TimeDescs[e.RowHandle];
            }
            else
            {
                e.Info.DisplayText = "时间";
            }
        }

        public override void InitData()
        {
            DataTable dt = dtSchedule;
            CreateWeekDateList(StartDate);
            DataTable dataTable = CreatScheduleTable();
            this.dateEdit1.DateTime = StartDate;
            List<DataRow> datas = dt.AsEnumerable().ToList();
            TimeDescs.ForEach(time =>
            {
                DataRow targetSchedule = dataTable.NewRow();
                targetSchedule["SCHEDULE_TIME"] = time;
                for (int i = 0; i < 7; i++)
                {
                    DateTime date = dateTimes[i];
                    targetSchedule["SCHEDULE_DAY"] = date;
                    string dateString = date.ToString("yyyy-MM-dd");
                    DataRow drSchedule = datas.FirstOrDefault(searchSchedule => dateString.Equals(Convert.ToDateTime(searchSchedule["SCHEDULE_DAY"]).ToString("yyyy-MM-dd")) && searchSchedule["SCHEDULE_TIME"].ToString().Equals(time));
                    if (null != drSchedule)
                    {
                        targetSchedule["SCHEDULE_SUB" + (i + 1)] = drSchedule["SCHEDULE_SUB"];
                        targetSchedule["SCHEDULE_CONTENT" + (i + 1)] = drSchedule["SCHEDULE_CONTENT"];
                    }

                    SetColumnsHeader(i, date);
                }
                dataTable.Rows.Add(targetSchedule);

            });
            this.GCDate.DataSource = dataTable;
        }

        public DateTime GetStartDate(DateTime today)
        {
            return today.Date.AddDays(-(int)today.DayOfWeek);
        }

        protected override void SetContentSize()
        {
            this.MainPanel.Width = this.Width;
            this.MainPanel.Height = this.Height - TopBannerHeight;
        }



        private DataTable CreatScheduleTable()
        {
            DataTable dataTable = new DataTable();
            dataTable.Columns.AddRange(new DataColumn[]
            {
                new DataColumn("SCHEDULE_DAY",typeof(DateTime)),
                new DataColumn("SCHEDULE_TIME",typeof(string)),
                new DataColumn("SCHEDULE_SUB1",typeof(string)),
                new DataColumn("SCHEDULE_SUB2",typeof(string)),
                new DataColumn("SCHEDULE_SUB3",typeof(string)),
                new DataColumn("SCHEDULE_SUB4",typeof(string)),
                new DataColumn("SCHEDULE_SUB5",typeof(string)),
                new DataColumn("SCHEDULE_SUB6",typeof(string)),
                new DataColumn("SCHEDULE_SUB7",typeof(string)),
                new DataColumn("SCHEDULE_CONTENT1",typeof(string)),
                new DataColumn("SCHEDULE_CONTENT2",typeof(string)),
                new DataColumn("SCHEDULE_CONTENT3",typeof(string)),
                new DataColumn("SCHEDULE_CONTENT4",typeof(string)),
                new DataColumn("SCHEDULE_CONTENT5",typeof(string)),
                new DataColumn("SCHEDULE_CONTENT6",typeof(string)),
                new DataColumn("SCHEDULE_CONTENT7",typeof(string)),
            });
            return dataTable;
        }

        private void CreateWeekDateList(DateTime startDate)
        {
            dateTimes = new List<DateTime>
            {
                startDate,
                startDate.Date.AddDays(1),
                startDate.Date.AddDays(2),
                startDate.Date.AddDays(3),
                startDate.Date.AddDays(4),
                startDate.Date.AddDays(5),
                startDate.Date.AddDays(6),
            };
        }

        private void SetColumnsHeader(int i, DateTime date)
        {
            GridColumn gridColumn = GvView.Columns[i];
            gridColumn.Caption = string.Concat(date.ToString("MM/dd"), Environment.NewLine, WeekString[(int)date.DayOfWeek]);
        }

        private void GvView_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {

            if (string.IsNullOrEmpty(e.DisplayText)) return;
            RectangleF rectangleF = e.Bounds;
            RectangleF back = new RectangleF();
            back.Location = new PointF(rectangleF.Left, rectangleF.Top + 15);
            back.Size = new SizeF(rectangleF.Width, rectangleF.Height - 30);
            e.Graphics.FillRectangle(new SolidBrush(Color.FromArgb(204, 255, 204)), back.Left, back.Top, back.Width, back.Height);

        }
        public DataTable CreateTestDate()
        {
            DataTable dt = new DataTable();
            dt.Columns.AddRange(new DataColumn[]
            {
                new DataColumn("SCHEDULE_DAY",typeof(DateTime)),
                new DataColumn("SCHEDULE_TIME",typeof(string)),
                new DataColumn("SCHEDULE_SUB",typeof(string)),
                new DataColumn("SCHEDULE_CONTENT",typeof(string))
            });
            DateTime dateTime = DateTime.Now.Date.AddDays(-(int)DateTime.Now.DayOfWeek);
            dt.Rows.Add(dateTime, "上午", "门诊", "安德森发顺丰");
            dt.Rows.Add(dateTime, "下午", "住院", "阿迪斯发士大夫");
            dt.Rows.Add(dateTime, "晚上", "", "");
            dt.Rows.Add(dateTime.AddDays(1), "上午", "", "");
            dt.Rows.Add(dateTime.AddDays(1), "下午", "", "");
            dt.Rows.Add(dateTime.AddDays(1), "晚上", "", "");
            dt.Rows.Add(dateTime.AddDays(2), "上午", "", "");
            dt.Rows.Add(dateTime.AddDays(2), "下午", "门诊", "");
            dt.Rows.Add(dateTime.AddDays(2), "晚上", "", "");
            dt.Rows.Add(dateTime.AddDays(3), "上午", "", "");
            dt.Rows.Add(dateTime.AddDays(3), "下午", "", "");
            dt.Rows.Add(dateTime.AddDays(3), "晚上", "", "");
            dt.Rows.Add(dateTime.AddDays(4), "上午", "住院", "");
            dt.Rows.Add(dateTime.AddDays(4), "下午", "", "");
            dt.Rows.Add(dateTime.AddDays(4), "晚上", "", "");
            dt.Rows.Add(dateTime.AddDays(5), "上午", "", "");
            dt.Rows.Add(dateTime.AddDays(5), "下午", "", "");
            dt.Rows.Add(dateTime.AddDays(5), "晚上", "", "");
            dt.Rows.Add(dateTime.AddDays(6), "上午", "", "");
            dt.Rows.Add(dateTime.AddDays(6), "下午", "", "");
            dt.Rows.Add(dateTime.AddDays(6), "晚上", "", "");
            dt.Rows.Add(dateTime.AddDays(7), "上午", "", "");
            dt.Rows.Add(dateTime.AddDays(7), "下午", "", "");
            dt.Rows.Add(dateTime.AddDays(7), "晚上", "", "");
            return dt;
        }

        private void BtnNext_Click(object sender, EventArgs e)
        {
            this.NextButtonClick?.Invoke(sender, e);
        }

        private void BtnPre_Click(object sender, EventArgs e)
        {
            this.PreButtonClick?.Invoke(sender, e);
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            this.ScheduleAddButtonClick?.Invoke(sender, e);
        }

        private void ToolTip_GetActiveObjectInfo(object sender, DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventArgs e)
        {
            GridHitInfo hitInfo = GvView.CalcHitInfo(e.ControlMousePosition);

            if (hitInfo.RowHandle < 0 || hitInfo.Column == null || hitInfo.HitTest != GridHitTest.RowCell)
            {
                ToolTip.HideHint();
                return;
            }

            DataRow row = GvView.GetDataRow(hitInfo.RowHandle);
            string fieldName = hitInfo.Column.FieldName;
            //如果是SCHEDULE_SUB列 就显示自定义的tooltip
            if (fieldName.StartsWith("SCHEDULE_SUB") && row[fieldName] != DBNull.Value && !string.IsNullOrEmpty(row[fieldName].ToString()))
            {
                int columnIndex = Convert.ToInt32(fieldName.Replace("SCHEDULE_SUB", ""));
                e.Info = new ToolTipControlInfo("", row["SCHEDULE_CONTENT" + columnIndex].ToString());
            }
        }
    }
}
