﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace PlatCommon.Common
{
    //连接
   public  class SqlConnectionPublic
    {
        public static int GetSqlConnection(string connsql, string sql,ref DataTable dt)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection())
                {
                    conn.ConnectionString = connsql;
                    conn.Open(); // 打开数据库连接
                    SqlDataAdapter myda = new SqlDataAdapter(sql, conn); // 实例化适配器
                    dt = new DataTable(); // 实例化数据表
                    myda.Fill(dt); // 保存数据 

                    conn.Close(); // 关闭数据库连接

                }

            }
            catch (Exception ex) 
            {
                XtraMessageBox.Show("连接sql数据库错误"+ex.Message,"提示");
            }
            return 0;
        }
    }
}
