﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using System.ComponentModel;
namespace Model
{

    /// <summary>
    ///应用程序菜单字典
    /// </summary>

    [DataContract]
    public class SEC_RIGHT_GROUP_VS_MENUS : NotificationObject
    {

        /// <summary>
        /// 应用程序代码
        /// </summary>		
        private string _application_code;
        [DataMember]
        public string APPLICATION_CODE
        {
            get { return _application_code; }
            set
            {
                if (_application_code != value)
                {
                    _application_code = value;
                    this.RaisePropertyChanged("APPLICATION_CODE");
                }
            }
        }
        /// <summary>
        /// 权限组代码
        /// </summary>		
        private string _right_group_code;
        [DataMember]
        public string RIGHT_GROUP_CODE
        {
            get { return _right_group_code; }
            set
            {
                if (_right_group_code != value)
                {
                    _right_group_code = value;
                    this.RaisePropertyChanged("RIGHT_GROUP_CODE");
                }
            }
        }
        /// <summary>
        /// 菜单标识
        /// </summary>		
        private string _menu_name;
        [DataMember]
        public string MENU_NAME
        {
            get { return _menu_name; }
            set
            {
                if (_menu_name != value)
                {
                    _menu_name = value;
                    this.RaisePropertyChanged("MENU_NAME");
                }
            }
        }
        /// <summary>
        /// 备注	
        /// </summary>		
        private string _security_memos;
        [DataMember]
        public string SECURITY_MEMOS
        {
            get { return _security_memos; }
            set
            {
                if (_security_memos != value)
                {
                    _security_memos = value;
                    this.RaisePropertyChanged("SECURITY_MEMOS");
                }
            }
        }

    }
}