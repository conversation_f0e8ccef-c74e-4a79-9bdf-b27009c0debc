﻿using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;

namespace PlatCommonForm.Report
{
    public partial class SecondHomesAttachedPage : DevExpress.XtraReports.UI.XtraReport
    {
        public SecondHomesAttachedPage()
        {
            InitializeComponent();
        }
        public SecondHomesAttachedPage(Hashtable ht)
        {
            InitializeComponent();
            bindData(ht);
        }

        private void bindData(Hashtable ht)
        {
            foreach (string item in ht.Keys)
            {
                try
                {
                    XRControl xrc = this.FindControl(item, true);
                    xrc.Text = ht[item].ToString();
                }
                catch { }
            }


        }
    }
}
