﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace Tjhis.Controls.StatisticsControls
{
    public partial class ucIncomeChart : RadiusUserControl
    {
        [CategoryAttribute("设计"),DisplayNameAttribute("控件总标题")]
        public string ControlTile
        {
            get
            {
                return this.labelControl1.Text;
            }
            set
            {
                this.labelControl1.Text = value;
            }
        }
        [CategoryAttribute("设计"), DisplayNameAttribute("第一行第一列")]
        public string Row1Column1Title
        {
            get
            {
                return this.labelControl6.Text;
            }
            set
            {
                this.labelControl6.Text = value;
            }
        }
        [CategoryAttribute("设计"), DisplayNameAttribute("第一行第二列")]
        public string Row1Column2Title
        {
            get
            {
                return this.labelControl5.Text;
            }
            set
            {
                this.labelControl5.Text = value;
            }
        }
        [CategoryAttribute("设计"), DisplayNameAttribute("第一行第三列")]
        public string Row1Column3Title
        {
            get
            {
                return this.labelControl4.Text;
            }
            set
            {
                this.labelControl4.Text = value;
            }
        }
        private const string NomalNumStringFormt = "N";

        public IncomeChartData incomeChartData { get; set; }
        public ucIncomeChart()
        {
            InitializeComponent();
        }

        protected override void SetContentSize()
        {
            this.tableMain.Width = this.Width;
            this.tableMain.Height = this.Height - TopBannerHeight;
        }
        public override void InitData()
        {
            if(null != incomeChartData)
            {
                this.LSum.Text = GetLabelHtmlText(incomeChartData.IncomeType.Sum, CNYUnit);
                this.LRefund.Text = GetLabelHtmlText(incomeChartData.IncomeType.Refund, CNYUnit);
                this.LOther.Text = GetLabelHtmlText(incomeChartData.IncomeType.Other, CNYUnit);
                SetucCompareValue(this.ucCompareSum, incomeChartData.IncomeType.CompareSum);
                SetucCompareValue(this.ucCompareRefund, incomeChartData.IncomeType.CompareRefund);
                SetucCompareValue(this.ucCompareOther, incomeChartData.IncomeType.CompareOther);
                this.ChartIncomeType.DataSource = incomeChartData.ChartDate.Income;
                this.ChartPayType.DataSource = incomeChartData.ChartDate.PayType;
            }
        }

        protected override string GetLabelHtmlText(decimal value, string unit)
        {
            return string.Concat(string.Format(LabelTextHtmlFontLargeSize, value.ToString(NomalNumStringFormt)), GetLabelUnitString(unit));
        }
        
        public class IncomeChartData
        {
            public IncomeChartData()
            {
                IncomeType = new IncomeType();
                ChartDate = new ChartDate();
            }
            public IncomeType IncomeType { get; set; }
            public ChartDate ChartDate { get; set; }

            public DataTable CreateIncomeTypeBase()
            {
                DataTable dt = new DataTable();
                dt.Columns.AddRange(new DataColumn[]
                {
                new DataColumn("IncomeType",typeof(string)),
                new DataColumn("IncomeSum",typeof(decimal))
                });
                return dt;
            }

            public DataTable CreatePayTypeBase()
            {
                DataTable dt = new DataTable();
                dt.Columns.AddRange(new DataColumn[]
                {
                new DataColumn("PayType",typeof(string)),
                new DataColumn("PaySum",typeof(decimal))
                });
                return dt;
            }
        }

        public class IncomeType
        {
            public decimal Sum { get; set; }
            public decimal CompareSum { get; set; }
            public decimal Refund { get; set; }
            public decimal CompareRefund { get; set; }
            public decimal CompareOther { get; set; }
            public decimal Other { get; set; }
        }
        public class ChartDate
        {
            /// <summary>
            /// 费用构成，结构（IncomeType[string][费用类型]，IncomeSum[decimal][费用总额]）
            /// </summary>
            public DataTable Income { get; set; }
            /// <summary>
            /// 支付方式，结构（PayType[string][支付方式]，PaySum[decimal][支付总额]）
            /// </summary>
            public DataTable PayType { get; set; }
        }
        public static class CLINIC_ITEM_CLASS
        {
            public const string ClassNamePresc = "处方";
            public const string ClassNameExam = "检查";
            public const string ClassNameLab = "检验";
            public const string ClassNameTreat = "处置";
            public const string ClassNameOther = "其他";
        }
    }
}
