﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Microsoft.Practices.EnterpriseLibrary.Data.Sql;
using System.Data.Common;
using System.Data;
using System.ServiceModel;
using System.ServiceModel.Channels;
using Utility.YangFan;
namespace OracleDAL
{
 /// <summary>
	/// 数据访问类HOSPITAL_CONFIG。
	/// </summary>
	public class HOSPITAL_CONFIG
	{
		public HOSPITAL_CONFIG()
		{}
		#region  成员方法
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
            strSql.Append("select HOSPITAL,AUTHORIZED_KEY,UNIT_CODE,LOCATION,MAILING_ADDRESS,ZIP_CODE");//,APPROVED_BED_NUM,VERIFY_KEY,COMPANY_PRINCIPAL,MR_UP_OPERATOR,MR_UP_PHONE,MR_UP_EMAIL,MR_UP_MOBILE 
			strSql.Append(" FROM HOSPITAL_CONFIG ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("HOSPITAL_CONFIG", "GetList", strSql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, strSql.ToString());
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
		}

		#endregion  成员方法
	}
}
