﻿/*-----------------------------------------------------------------------
 * 类名称    ：FrmValidateUser
 * 类描述    ：
 * 创建人    ：梁吉lions
 * 创建时间  ：2016/7/4 15:20:47
 * 修改人    ：
 * 修改时间  ：
 * 修改备注  ：
 * 版本      ：
 * ----------------------------------------------------------------------
 */
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using NM_Service.NMService;
using PlatCommon.SysBase;

namespace PlatCommonForm
{
    public partial class FrmValidateUser : ParentForm
    {
        public string user_name="";
        public string user_id="";
        public string user_name_alias = "";
        
        public FrmValidateUser()
        {
            InitializeComponent();
            
        }
		public FrmValidateUser(string user)
        {
            InitializeComponent();
            txtInputNo.Text = user;
            txtInputNo.ReadOnly = true;
        }
        public void SetCaption(String cap)
        {
            this.labelControl1.Text = cap;
        }
        #region  自定义方法
        /// <summary>
        /// 判断验证用户是否当前护理单元用户
        /// </summary>
        /// <param name="username">要验证的用户</param>
        /// <param name="uname">返回已验证用户姓名</param>
        /// <returns></returns>
        public bool IsWardUser(string username,string pwd, ref string uname)
        {
            using (ServerPublicClient client = new ServerPublicClient())
            {
                
                String ls_db_user_name = username.ToUpper();
                string strSql = "";
                strSql = "SELECT  STAFF_DICT.NAME,STAFF_DICT.PASSWORD FROM STAFF_DICT ,STAFF_VS_GROUP "
                        + "WHERE  STAFF_DICT.EMP_NO = STAFF_VS_GROUP.EMP_NO AND STAFF_DICT.USER_NAME = '" + ls_db_user_name + "'";
                if("DOCTWS".Equals(this.AppCode))
                {
                    strSql += " AND	STAFF_VS_GROUP.GROUP_CODE = '" + this.DeptCode + "'"; 
                }
                else if ("NURSWS".Equals(this.AppCode))
                {
                    strSql += " AND	STAFF_VS_GROUP.GROUP_CODE = '" + this.WardCode + "'";
                }
                //string strSql = "SELECT NAME, PASSWORD FROM STAFF_DICT WHERE USER_NAME = '" + ls_db_user_name + "'";
                DataSet ds = client.GetDataBySql(strSql);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    //string user_ward = ds.Tables[0].Rows[0]["USER_DEPT"] == DBNull.Value ? "" : ds.Tables[0].Rows[0]["USER_DEPT"].ToString();
                    string password = ds.Tables[0].Rows[0]["PASSWORD"] == DBNull.Value ? "" : ds.Tables[0].Rows[0]["PASSWORD"].ToString().Trim();
                    if (!password.Equals(Utility.Gloobal.EncryptHIS(pwd)))
                    {
                        XtraMessageBox.Show("用户密码错误！", "提示信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return false;
                    }
                    uname = ds.Tables[0].Rows[0]["NAME"] == DBNull.Value ? "" : ds.Tables[0].Rows[0]["NAME"].ToString();

                    return true;
                }
                else
                {
                    if (!username.ToUpper().Equals("ADMIN"))
                    {
                        XtraMessageBox.Show("用户名单中你不是本病房人员！", "提示信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return false;
                    }
           
                }
                return true;
            }
        }
        /// <summary>
        ///  获取用户 信息
        /// </summary>
        /// <param name="username"></param>
        /// <param name="ls_userid"></param>
        /// <param name="ls_user_name"></param>
        /// <param name="ls_user_dept"></param>
        /// <returns></returns>
        public bool GetUserInfo(string username, ref string ls_userid, ref string ls_user_name, ref string ls_user_dept)
        {
            using (ServerPublicClient client = new ServerPublicClient())
            {
                String ls_db_user_name = username.ToUpper();
                string strSql = "SELECT USERS.USER_ID,USERS.USER_DEPT , USERS.USER_NAME,STAFF_DICT.ID FROM USERS LEFT JOIN STAFF_DICT ON USERS.DB_USER=STAFF_DICT.USER_NAME WHERE Upper(USERS.DB_USER) ='" + ls_db_user_name + "' ";

                DataSet ds = client.GetDataBySql(strSql);
                if (ds != null && ds.Tables.Count > 0)
                {
                    ls_userid = ds.Tables[0].Rows[0]["ID"] == DBNull.Value ? "" : ds.Tables[0].Rows[0]["ID"].ToString();
                    ls_user_name = ds.Tables[0].Rows[0]["USER_NAME"] == DBNull.Value ? "" : ds.Tables[0].Rows[0]["USER_NAME"].ToString();
                    ls_user_dept = ds.Tables[0].Rows[0]["USER_DEPT"] == DBNull.Value ? "" : ds.Tables[0].Rows[0]["USER_DEPT"].ToString();
                    return true;
                }
                else
                {
                    XtraMessageBox.Show("用户表中无此用户！", "提示信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }

            }
        }
        /// <summary>
        /// 判断是否有当前窗口权限
        /// </summary>
        /// <param name="username"></param>
        /// <param name="menu"></param>
        /// <returns></returns>
        public bool IsWardPolicy(string username, string menu)
        {
            using (ServerPublicClient client = new ServerPublicClient())
            {
                //string strSql = "SELECT USER_DEPT , USER_NAME FROM USERS WHERE DB_USER ='" + username + "' ";
                String ls_db_user_name = username.ToUpper();
                string strSql = "SELECT t2.APPLICATION_CODE  FROM "
                                + "  MR_RIGHT_DICT T2 " 
                                + "LEFT JOIN MR_ROLE_RIGHT T3 "
                                + "ON T2.RIGHT_ID=T3.RIGHT_ID "
                                + "LEFT JOIN MR_USER_ROLE T4 "
                                + "ON T4.ROLE_CODE = T3.ROLE_CODE "
                                + "WHERE T4.DB_USER='" + username + "' "
                                + "AND UPPER(t2.APPLICATION_CODE)='" + this.AppCode+"' "
                                + "GROUP BY t2.APPLICATION_CODE";
                //string strSql = "SELECT T1.*  FROM SEC_MENUS_DICT    T1, "
                //                + "SEC_RIGHT_GROUP_VS_USERS T2, "
                //                + "SEC_RIGHT_GROUP_VS_MENUS T3 "
                //                + "WHERE T1.MENU_NAME = T3.MENU_NAME  "
                //                + "AND T1.APPLICATION_CODE = T3.APPLICATION_CODE "
                //                + "AND T3.RIGHT_GROUP_CODE=T2.RIGHT_GROUP_CODE "
                //                + "AND T3.APPLICATION_CODE = T2.APPLICATION_CODE "
                //                + "AND T1.MENU_VISIBLE = '1' "
                //                + "AND T2.USER_CODE = '" + ls_db_user_name + "' "
                //                + "AND T1.APPLICATION_CODE = '" + SystemParm.AppName + "' "
                //                + "AND T1.OPEN_FORM = '" + menu + "' "
                //                + "ORDER BY SERIAL_NO";
                try
                {
                    DataSet ds = client.GetDataBySql(strSql);
                    if (ds != null && ds.Tables.Count > 0)
                    {
                        return true;
                    }
                    else
                    {
                        XtraMessageBox.Show("你不是此系统的合法用户！", "提示信息", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
                        return false;
                    }
                }
                catch(Exception e) {
                    return false;
                }
                

            }
        }
        #endregion
        private void FrmValidateUser_Load(object sender, EventArgs e)
        {
            //this.ControlBox = false;
            if (string.IsNullOrEmpty(txtInputNo.Text.Trim()))
            {
                txtInputNo.Text = SystemParm.LoginUser.USER_NAME;
            }
        }

        private void FrmValidateUser_Activated(object sender, EventArgs e)
        {
            txtPwd.Focus();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtInputNo.Text.Trim()))
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("用户名不能为空！", "提示信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            if (string.IsNullOrEmpty(txtPwd.Text.Trim()))
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("密码不能为空！", "提示信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            string uname = txtInputNo.Text.Trim().ToUpper();
            //验证登录用户
            if (string.IsNullOrEmpty(uname))
            {
                this.Close();
                return;
            }
            //验证用户所属护理单元，返回姓名
            string u_Name = "";
            try
            {
                //if ("NURSWS".Equals(this.AppCode))
                //{
                    if (!this.IsWardUser(uname, txtPwd.Text.Trim().ToUpper(), ref u_Name))
                    {
                        //this.Close();
                        return;
                    }
                //}
                
                // 获取用户信息
                string is_userid = "", ls_user_name = "", ls_user_dept = "";
                if (!this.GetUserInfo(uname, ref is_userid, ref ls_user_name, ref ls_user_dept))
                {
                    //this.Close();
                    return;
                }
                //权限菜单判断
                if (!this.IsWardPolicy(uname, this.GetType().FullName))
                {
                    //this.Close();
                    XtraMessageBox.Show("用户菜单权限验证失败！", "提示信息", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);

                    return;
                }
                this.user_name = uname;
                this.user_id = is_userid;
                this.user_name_alias = u_Name;
                DialogResult = System.Windows.Forms.DialogResult.OK;
            }
            catch(Exception ex){
                XtraMessageBox.Show("校验用户异常！" + ex.Message, "提示信息", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
            }
            
            this.Close();
        }

        private void txtInputNo_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == '\r')
            {
                txtPwd.Focus();
            }
        }

        private void txtPwd_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == '\r')
            {
                //回车验证用户
                this.btnOk_Click(btnOk, null);
            }
        }

        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ClassStyle |= 0x200;  // CP_NOCLOSE_BUTTON
                return cp;
            }
        }
    }
}
