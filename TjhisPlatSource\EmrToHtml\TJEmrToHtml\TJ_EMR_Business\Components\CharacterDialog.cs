﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace TJ.EMR.Business.Components
{
    internal partial class CharacterDialog : Form
    {
        public string SelectText { get; set; }
        ToolTip tt = new ToolTip();
        public CharacterDialog()
        {
            InitializeComponent();
            list_ShowOwners.Clear();
            //显示表格线
            this.list_ShowOwners.GridLines = true;
            #region 单位符号
            string[] c1 = { "°", "′", "″", " ＄ ", "￥ ", "〒", " ￠", " ￡", " ％", " ＠", " ℃", " ℉", " ﹩", " ﹪", " ‰ ", "﹫", " ㏕", " ㎜", " ㎝", " ㎞", " ㏎", " ㎡", " ㎎", " ㎏", " ㏄", " °", " ○", " ¤", "mmHg", "kPa", "g/L", "mg/dl", "mol/L", "μmol/L", "mmol/L", "mol/24h", "μmol/24h", "ml/s", "U/L", "μg/L", "pmol/L", "U/ml", "mg/L", "nmol/L", "KIU/L", "mIU/L", "μg", "μmol", "10^9/L", "10^12/L", "次/min", "R/s", "℅", "µ", "㏑", "㏒", "ml", "mol", "♂", "♀", };
            for (int i = 0; i < c1.Length; i++)
            {
                ListViewItem lvt = new ListViewItem();
                lvt.Text = c1[i].ToString();
                this.list_ShowOwners.Items.Add(lvt);

            }
            #endregion

            #region 数字符号
            lw2.Clear();
            string[] c2 = { "Ⅰ", "Ⅱ", "Ⅲ", "Ⅳ", "Ⅴ", "Ⅵ", "Ⅶ", "Ⅷ", "Ⅸ", "Ⅹ", "Ⅺ", "Ⅻ", "ⅰ", "ⅱ", "ⅲ", "ⅳ", "ⅴ", "ⅵ", "ⅶ", "ⅷ", "ⅸ", "ⅹ", "⒈", "⒉", "⒊", "⒋", "⒌", "⒍", "⒎", "⒏", "⒐", "⒑", "⒒", "⒓", "⒔", "⒕", "⒖", "⒗", "⒘", "⒙", "⒚", "⒛", "⑴", "⑵", "⑶", "⑷", "⑸", "⑹", "⑺", "⑻", "⑼", "⑽", "⑾", "⑿", "⒀", "⒁", "⒂", "⒃", "⒄", "⒅", "⒆", "⒇", "①", "②", "③", "④", "⑤", "⑥", "⑦", "⑧", "⑨", "⑩", "㈠", "㈡", "㈢", "㈣", "㈤", "㈥", "㈦", "㈧", "㈨", "㈩" };
            for (int i = 0; i < c2.Length; i++)
            {
                ListViewItem lvt = new ListViewItem();
                lvt.Text = c2[i].ToString();

                this.lw2.Items.Add(lvt);

            }
            #endregion

            #region 拼音符号
            lv3.Clear();
            string[] c3 = { "ā", " á", "  ǎ ", " à ", " ō ", " ó ", " ǒ ", " ò ", " ē ", " é ", " ě ", " è ", " ī ", " í ", " ǐ ", " ì ", " ū ", " ú ", " ǔ ", " ù ", " ǖ ", " ǘ ", " ǚ ", " ǜ ", " ü ", " ê ", " ɑ ", "  ", " ń ", " ň ", "  ", " ɡ" };
            for (int i = 0; i < c3.Length; i++)
            {
                ListViewItem lvt = new ListViewItem();
                lvt.Text = c3[i].ToString();

                this.lv3.Items.Add(lvt);

            }
            #endregion

            #region 数学符号
            lv4.Clear();
            string[] c4 = { "≈", "≡", "≠", "＝", "≤", "≥", "＜", "＞", "≮", "≯", "∷", "±", "＋", "－", "×", "÷", "／", "∫", "∮", "∝", "∞", "∧", "∨", "∑", "∏", "∪", "∩", "∈", "∵", "∴", "⊥", "∥", "∠", "⌒", "⊙", "≌", "∽", "√", "≦", "≧", "≒", "≡", "﹢", "﹣", "﹤", "﹥", "﹦", "～", "∟", "⊿", "㏒", "㏑" };
            for (int i = 0; i < c4.Length; i++)
            {
                ListViewItem lvt = new ListViewItem();
                lvt.Text = c4[i].ToString();

                this.lv4.Items.Add(lvt);

            }
            #endregion

            #region 特殊符号
            lv5.Clear();
            string[] c5 = { "α", "β", "γ", "δ", "κ", "λ", "μ", "ε", "ζ", "η", "θ", "ι", "§", "№", "∽", "§", "＃", "＠", "＆", "＊", "※", "§", "〃", "№", "〓", "○", "●", "△", "▲", "◎", "☆", "★", "◇", "◆", "□", "■", "▽", "▼", "㊣", "℅", "ˉ", "￣", "＿", "﹉", "﹊", "﹍", "﹎", "﹋", "﹌", "♀", "♂", "⊕", "⊙", "↑", "↓", "←", "→", "↖", "↗", "↙", "↘", "∥", "∣", "／", "＼", "∕", "﹨" ,
                          "▷","◁","♤","♡","♢","♧","☼","☺","☏","•","▶","◀","♠","♥","♦","♣","☀","☻","☎","▪","•","‥","…","……","*","☉","Θ","❤","¤","✪","の","⊿","☜","☞","▁","▂","▃","▄","▅","▆","▇","█","▉","▊","▋","▌","▍","▎","▏","⊱","⋛","⋌","⋚","⊰","⊹","⌒","®","©","¢","℡","™","ª","㈱","╱","▁","╲","▏","▕","╲","▔","╱","↔","卍","卐","∷","#","&","@","◤","◥","◣","◢","♩","♪","♫","♬","€","฿","¶","♭","♯","$","Ψ","¥","£","∮","‖","▫","◈","▣","◐","◑","◕","†","‡","¬","￢","░","▒","▓","▬","≡","☌","☍","☋","▧","▤","▨","▥","▩","▥","▨","▤","▧","▦","✟"};
            for (int i = 0; i < c5.Length; i++)
            {
                ListViewItem lvt = new ListViewItem();
                lvt.Text = c5[i].ToString();

                this.lv5.Items.Add(lvt);

            }
            #endregion

            #region 标点符号
            lv6.Clear();
            string[] c6 = { ".", "。", ",", "、", "！", "？", "：", "；", "`", "﹑", "•", "＂", "^", "…",
                              "‘", "’", "“", "”", "〝", "〞", "~", "\\", "∕", "|", "¦", "‖", "—",
                              "　", "(", ")", "﹛", "﹜", "〈", "〉", "﹝", "﹞", "「", "」", "‹", "›",
                              "〖", "〗", "[", "]", "{", "}", "《", "》", "〔", "〕", "『", "』", "«", "»",
                              "【", "】", "﹐", "﹕", "﹔", "！", "？", "﹖", "﹏", "＇", "ˊ", "-", "﹫",
                              "︳", "_", "＿", "¸", "︰", ";", "¡", "¿", "﹌", "﹋", "´", "ˋ", "―", "@",
                              "︴", "¯", "￣", "﹢", "+", "﹦", "=", "﹤", "<", "­", "‐", "˜", "~", "﹟", "#",
                              "﹩", "$", "﹠", "&", "﹪", "%", "﹡", "*", "﹨", "\\", "﹍", "﹉", "﹎", "﹊",
                              "ˆ", "ˇ", "︵", "︶", "︷", "︸", "︿", "﹀", "︹", "︺", "︽", "︾",
                              "_", "ˉ", "﹁", "﹂", "﹃", "﹄", "︻", "︼"};
            for (int i = 0; i < c6.Length; i++)
            {
                ListViewItem lvt = new ListViewItem();
                lvt.Text = c6[i].ToString();
                this.lv6.Items.Add(lvt);

            }
            #endregion
            tt.IsBalloon = true;
            #region
            //list_ShowOwners就是listView控件了,最好每次先清空下.要不每次添加的时候还是全部添加.就不是一条信息了.
            // Owners[] ows = 类.getAllOwners();//调用你刚才写那个方法给一个新的属性方法;
            //list_ShowOwners.Columns.Add("ID", 50);
            //list_ShowOwners.Columns.Add("主人姓名", 100);
            //list_ShowOwners.Columns.Add("城市", 100);
            //list_ShowOwners.Columns.Add("地址", 100);
            //list_ShowOwners.Columns.Add("电话", 100);
            //              string ls_dw[]={"°","℃","℉","‰","﹪","＄","﹩","￡","￥","@","㏕","㎜","㎝","㎞","㏎","㎡","㎎","㎏","㏄"}
            //string ls_xh[]={"Ⅰ","Ⅱ","Ⅲ","Ⅳ","Ⅴ","Ⅵ","Ⅶ","Ⅷ","Ⅸ","Ⅹ","Ⅺ","Ⅻ","①","②","③","④","⑤","⑥","⑦","⑧","⑨","⑩","⑴","⑵","⑶","⑷","⑸","⑹","⑺","⑻","⑼","⑽","⒈","⒉","⒊","⒋","⒌","⒍","⒎","⒏","⒐","⒑","㈠","㈡","㈢","㈣","㈤","㈥","㈦","㈧","㈨","㈩","○","一","二","三","四","五","六","七","八","九","十"}
            //string ls_ts[]={"√","～","＋","－","×","÷","／","±","≠","＝","≤","≥","≮","≯","α","β","γ","δ", "κ","λ","μ","ε","ζ","η","θ","ι","§","№","∑","∠","≌","∽","∫","∮","∝","∞","∈","∑","∏","∪","∩","§","※"}
            //string ls_yx[]={"mmHg","kPa","g/L","mg/dl","mol/L","μmol/L","mmol/L","mol/24h","μmol/24h","ml/s","U/L","μg/L","pmol/L","U/ml","mg/L","nmol/L","KIU/L","mIU/L","μg","μmol","10^9/L","10^12/L","次/min","R/s"}


            //以上是在界面Load的时候先设置好标题名和长度.
            //for (int i = 0; i < ows.Length; i++)
            //{
            // ListViewItem lvi = new ListViewItem(new string[]{"0","1","2","3","4"});
            //  list_ShowOwners.Items.Add(lvi);



            //lvi.SubItems.Add("00");
            //lvi.SubItems.Add("11");
            //lvi.SubItems.Add("22");
            //lvi.SubItems.Add("33");
            //lvi.SubItems.Add("44");

            //list_ShowOwners.Items.Add(lvi);
            //}
            #endregion
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Yes;
            this.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        #region 选中事件


        private void list_ShowOwners_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (this.list_ShowOwners.SelectedItems.Count == 0) return;
            SelectText = this.list_ShowOwners.Items[this.list_ShowOwners.SelectedIndices[0]].Text;
        }
        private void lw2_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (this.lw2.SelectedItems.Count == 0) return;
            SelectText = this.lw2.Items[this.lw2.SelectedIndices[0]].Text;
        }

        private void lv3_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (this.lv3.SelectedItems.Count == 0) return;
            SelectText = this.lv3.Items[this.lv3.SelectedIndices[0]].Text;
        }

        private void lv4_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (this.lv4.SelectedItems.Count == 0) return;
            SelectText = this.lv4.Items[this.lv4.SelectedIndices[0]].Text;
        }

        private void lv5_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (this.lv5.SelectedItems.Count == 0) return;
            SelectText = this.lv5.Items[this.lv5.SelectedIndices[0]].Text;
        }
        private void lv6_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (this.lv6.SelectedItems.Count == 0) return;
            SelectText = this.lv6.Items[this.lv6.SelectedIndices[0]].Text;
        }
        #endregion

        #region 双击


        private void list_ShowOwners_DoubleClick(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Yes;
            this.Close();
        }
        private void lw2_DoubleClick(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Yes;
            this.Close();
        }
        private void lv3_DoubleClick(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Yes;
            this.Close();
        }

        private void lv4_DoubleClick(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Yes;
            this.Close();
        }

        private void lv5_DoubleClick(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Yes;
            this.Close();
        }
        private void lv6_DoubleClick(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Yes;
            this.Close();
        }
        #endregion

        #region 鼠标悬浮


        private void list_ShowOwners_MouseUp(object sender, MouseEventArgs e)
        {
            tt.SetToolTip(this.list_ShowOwners, SelectText);
        }
        private void lw2_MouseUp(object sender, MouseEventArgs e)
        {
            tt.SetToolTip(this.list_ShowOwners, SelectText);
        }

        private void lv3_MouseUp(object sender, MouseEventArgs e)
        {
            tt.SetToolTip(this.list_ShowOwners, SelectText);
        }

        private void lv4_MouseUp(object sender, MouseEventArgs e)
        {
            tt.SetToolTip(this.list_ShowOwners, SelectText);
        }

        private void lv5_MouseUp(object sender, MouseEventArgs e)
        {
            tt.SetToolTip(this.list_ShowOwners, SelectText);
        }
        private void lv6_MouseUp(object sender, MouseEventArgs e)
        {
            tt.SetToolTip(this.list_ShowOwners, SelectText);
        }

        #endregion
    }
}
