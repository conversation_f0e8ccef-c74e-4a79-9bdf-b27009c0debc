/// <XRTypeInfo>
///   <AssemblyFullName>DevExpress.XtraReports.v18.1, Version=18.1.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</AssemblyFullName>
///   <AssemblyLocation>D:\李庆来\消毒供应追溯管理系统（DISINFECT）\消毒供应程序20230217\Disinfect\DevExpress.XtraReports.v18.1.dll</AssemblyLocation>
///   <TypeName>DevExpress.XtraReports.UI.XtraReport</TypeName>
///   <Localization>zh-Hans</Localization>
///   <Version>18.1</Version>
///   <Resources>
///     <Resource Name="XtraReportSerialization.XtraReport">
/// zsrvvgEAAACRAAAAbFN5c3RlbS5SZXNvdXJjZXMuUmVzb3VyY2VSZWFkZXIsIG1zY29ybGliLCBWZXJzaW9uPTQuMC4wLjAsIEN1bHR1cmU9bmV1dHJhbCwgUHVibGljS2V5VG9rZW49Yjc3YTVjNTYxOTM0ZTA4OSNTeXN0ZW0uUmVzb3VyY2VzLlJ1bnRpbWVSZXNvdXJjZVNldAIAAAACAAAAAAAAAFBBRFBBRFA141seO0eBSgAAAAAxAAAADAEAACwkAHQAaABpAHMALgBEAGEAdABhAFMAbwB1AHIAYwBlAFMAYwBoAGUAbQBhAAAAAAASJAB0AGgAaQBzAC4AVABhAGcA0goAAAHPFTw/eG1sIHZlcnNpb249IjEuMCI/Pg0KPHhzOnNjaGVtYSBpZD0iTmV3RGF0YVNldCIgeG1sbnM9IiIgeG1sbnM6eHM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDEvWE1MU2NoZW1hIiB4
/// bWxuczptc2RhdGE9InVybjpzY2hlbWFzLW1pY3Jvc29mdC1jb206eG1sLW1zZGF0YSIgeG1sbnM6bXNwcm9wPSJ1cm46c2NoZW1hcy1taWNyb3NvZnQtY29tOnhtbC1tc3Byb3AiPg0KICA8eHM6ZWxlbWVudCBuYW1lPSJOZXdEYXRhU2V0IiBtc2RhdGE6SXNEYXRhU2V0PSJ0cnVlIiBtc2RhdGE6VXNlQ3VycmVudExvY2FsZT0idHJ1ZSI+DQogICAgPHhzOmNvbXBsZXhUeXBlPg0KICAgICAgPHhzOmNob2ljZSBtaW5PY2N1cnM9IjAiIG1heE9jY3Vycz0idW5ib3VuZGVkIj4NCiAgICAgICAgPHhzOmVsZW1lbnQgbmFtZT0iVGFibGVEYXRhIiBtc3Byb3A6QmFzZVRhYmxlLjI9IkJFRF9SRUMiIG1zcHJvcDpCYXNlVGFibGUuMD0iUFJFUEFZTUVOVF9SQ1BUIiBt
/// c3Byb3A6QmFzZVRhYmxlLjE9IlBBVF9NQVNURVJfSU5ERVgiPg0KICAgICAgICAgIDx4czpjb21wbGV4VHlwZT4NCiAgICAgICAgICAgIDx4czpzZXF1ZW5jZT4NCiAgICAgICAgICAgICAgPHhzOmVsZW1lbnQgbmFtZT0iSE9TUElUQUxfTkFNRSIgbXNkYXRhOlJlYWRPbmx5PSJ0cnVlIiBtc3Byb3A6T3JhRGJUeXBlPSIxMjYiIG1pbk9jY3Vycz0iMCI+DQogICAgICAgICAgICAgICAgPHhzOnNpbXBsZVR5cGU+DQogICAgICAgICAgICAgICAgICA8eHM6cmVzdHJpY3Rpb24gYmFzZT0ieHM6c3RyaW5nIj4NCiAgICAgICAgICAgICAgICAgICAgPHhzOm1heExlbmd0aCB2YWx1ZT0iNDAiIC8+DQogICAgICAgICAgICAgICAgICA8L3hzOnJlc3RyaWN0aW9uPg0K
/// ICAgICAgICAgICAgICAgIDwveHM6c2ltcGxlVHlwZT4NCiAgICAgICAgICAgICAgPC94czplbGVtZW50Pg0KICAgICAgICAgICAgICA8eHM6ZWxlbWVudCBuYW1lPSJQQVRJRU5UX0lEIiBtc3Byb3A6QmFzZUNvbHVtbj0iUEFUSUVOVF9JRCIgbXNwcm9wOk9yYURiVHlwZT0iMTI2IiBtaW5PY2N1cnM9IjAiPg0KICAgICAgICAgICAgICAgIDx4czpzaW1wbGVUeXBlPg0KICAgICAgICAgICAgICAgICAgPHhzOnJlc3RyaWN0aW9uIGJhc2U9InhzOnN0cmluZyI+DQogICAgICAgICAgICAgICAgICAgIDx4czptYXhMZW5ndGggdmFsdWU9IjIwIiAvPg0KICAgICAgICAgICAgICAgICAgPC94czpyZXN0cmljdGlvbj4NCiAgICAgICAgICAgICAgICA8L3hzOnNpbXBs
/// ZVR5cGU+DQogICAgICAgICAgICAgIDwveHM6ZWxlbWVudD4NCiAgICAgICAgICAgICAgPHhzOmVsZW1lbnQgbmFtZT0iTkFNRSIgbXNwcm9wOkJhc2VDb2x1bW49Ik5BTUUiIG1zcHJvcDpPcmFEYlR5cGU9IjEyNiIgbWluT2NjdXJzPSIwIj4NCiAgICAgICAgICAgICAgICA8eHM6c2ltcGxlVHlwZT4NCiAgICAgICAgICAgICAgICAgIDx4czpyZXN0cmljdGlvbiBiYXNlPSJ4czpzdHJpbmciPg0KICAgICAgICAgICAgICAgICAgICA8eHM6bWF4TGVuZ3RoIHZhbHVlPSI0MCIgLz4NCiAgICAgICAgICAgICAgICAgIDwveHM6cmVzdHJpY3Rpb24+DQogICAgICAgICAgICAgICAgPC94czpzaW1wbGVUeXBlPg0KICAgICAgICAgICAgICA8L3hzOmVsZW1lbnQ+DQog
/// ICAgICAgICAgICAgIDx4czplbGVtZW50IG5hbWU9IkFNT1VOVCIgbXNkYXRhOlJlYWRPbmx5PSJ0cnVlIiBtc3Byb3A6T3JhRGJUeXBlPSIxMDciIHR5cGU9InhzOmRlY2ltYWwiIG1pbk9jY3Vycz0iMCIgLz4NCiAgICAgICAgICAgICAgPHhzOmVsZW1lbnQgbmFtZT0iVklTSVRfSUQiIG1zcHJvcDpCYXNlQ29sdW1uPSJWSVNJVF9JRCIgbXNwcm9wOk9yYURiVHlwZT0iMTExIiB0eXBlPSJ4czpzaG9ydCIgbWluT2NjdXJzPSIwIiAvPg0KICAgICAgICAgICAgICA8eHM6ZWxlbWVudCBuYW1lPSJCRURfTEFCRUwiIG1zcHJvcDpCYXNlQ29sdW1uPSJCRURfTEFCRUwiIG1zcHJvcDpPcmFEYlR5cGU9IjEyNiIgbWluT2NjdXJzPSIwIj4NCiAgICAgICAgICAgICAg
/// ICA8eHM6c2ltcGxlVHlwZT4NCiAgICAgICAgICAgICAgICAgIDx4czpyZXN0cmljdGlvbiBiYXNlPSJ4czpzdHJpbmciPg0KICAgICAgICAgICAgICAgICAgICA8eHM6bWF4TGVuZ3RoIHZhbHVlPSI4IiAvPg0KICAgICAgICAgICAgICAgICAgPC94czpyZXN0cmljdGlvbj4NCiAgICAgICAgICAgICAgICA8L3hzOnNpbXBsZVR5cGU+DQogICAgICAgICAgICAgIDwveHM6ZWxlbWVudD4NCiAgICAgICAgICAgIDwveHM6c2VxdWVuY2U+DQogICAgICAgICAgPC94czpjb21wbGV4VHlwZT4NCiAgICAgICAgPC94czplbGVtZW50Pg0KICAgICAgICA8eHM6ZWxlbWVudCBuYW1lPSJTSU5HTEVfUk9XX1RBQkxFIj4NCiAgICAgICAgICA8eHM6Y29tcGxleFR5cGU+DQog
/// ICAgICAgICAgICA8eHM6c2VxdWVuY2U+DQogICAgICAgICAgICAgIDx4czplbGVtZW50IG5hbWU9IldBUkRfQ09ERSIgdHlwZT0ieHM6c3RyaW5nIiBtaW5PY2N1cnM9IjAiIC8+DQogICAgICAgICAgICAgIDx4czplbGVtZW50IG5hbWU9IldBUkRfTkFNRSIgdHlwZT0ieHM6c3RyaW5nIiBtaW5PY2N1cnM9IjAiIC8+DQogICAgICAgICAgICAgIDx4czplbGVtZW50IG5hbWU9IkRBVEVfQkVHSU4iIHR5cGU9InhzOnN0cmluZyIgbWluT2NjdXJzPSIwIiAvPg0KICAgICAgICAgICAgPC94czpzZXF1ZW5jZT4NCiAgICAgICAgICA8L3hzOmNvbXBsZXhUeXBlPg0KICAgICAgICA8L3hzOmVsZW1lbnQ+DQogICAgICA8L3hzOmNob2ljZT4NCiAgICA8L3hzOmNvbXBs
/// ZXhUeXBlPg0KICA8L3hzOmVsZW1lbnQ+DQo8L3hzOnNjaGVtYT4B8wRTRUxFQ1QgKHNlbGVjdCBob3NwaXRhbCBmcm9tIGhvc3BpdGFsX2NvbmZpZykgaG9zcGl0YWxfbmFtZSxhLlBBVElFTlRfSUQsICAgICAgIGMuTkFNRSwgICAgICAgc3VtKGEuQU1PVU5UKSBBTU9VTlQsICAgICAgIGEuVklTSVRfSUQsICAgICAgIGQuQkVEX0xBQkVMICBGUk9NIFBSRVBBWU1FTlRfUkNQVCBhLCBQQVRTX0lOX0hPU1BJVEFMIGIsIFBBVF9NQVNURVJfSU5ERVggYywgQkVEX1JFQyBkIHdoZXJlIGEuVFJBTlNBQ1RfVFlQRSA9ICfkuqTmrL4nICAgYW5kIChhLlJFRlVOREVEX1JDUFRfTk8gPSAnJyBvciBhLlJFRlVOREVEX1JDUFRfTk8gaXMgbnVsbCkgICBBTkQgYS5QQVRJ
/// RU5UX0lEID0gYy5QQVRJRU5UX0lEICAgQU5EIGEuUEFUSUVOVF9JRCA9IGIuUEFUSUVOVF9JRCAgIEFORCBhLlZJU0lUX0lEID0gYi5WSVNJVF9JRCAgIEFORCBiLldBUkRfQ09ERSA9IGQuV0FSRF9DT0RFICAgQU5EIGIuQkVEX05PID0gZC5CRURfTk8gICBhbmQgYi5XQVJEX0NPREUgPSB7V0FSRENPREV9IGdyb3VwIGJ5IGEuUEFUSUVOVF9JRCwgICAgICAgICAgYy5OQU1FLCAgICAgICAgICBhLlZJU0lUX0lELCAgICAgICAgICBiLkJFRF9OTywgICAgICAgICAgZC5CRURfTEFCRUwgb3JkZXIgYnkgYi5CRURfTk8=</Resource>
///   </Resources>
/// </XRTypeInfo>
namespace XtraReportSerialization {
    
    public class XtraReport : DevExpress.XtraReports.UI.XtraReport {
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.XRLabel label3;
        private DevExpress.XtraReports.UI.XRLabel label2;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.DetailBand Detail;
        private System.Resources.ResourceManager _resources;
        private string _resourceString;
        public XtraReport() {
            this._resourceString = DevExpress.XtraReports.Serialization.XRResourceManager.GetResourceFor("XtraReportSerialization.XtraReport");
            this.InitializeComponent();
        }
        private System.Resources.ResourceManager resources {
            get {
                if (_resources == null) {
                    this._resources = new DevExpress.XtraReports.Serialization.XRResourceManager(this._resourceString);
                }
                return this._resources;
            }
        }
        private void InitializeComponent() {
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.label3 = new DevExpress.XtraReports.UI.XRLabel();
            this.label2 = new DevExpress.XtraReports.UI.XRLabel();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // BottomMargin
            // 
            this.BottomMargin.Dpi = 254F;
            this.BottomMargin.HeightF = 0F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Visible = false;
            // 
            // PageHeader
            // 
            this.PageHeader.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.label3,
                        this.label2});
            this.PageHeader.Dpi = 254F;
            this.PageHeader.HeightF = 569F;
            this.PageHeader.Name = "PageHeader";
            this.PageHeader.StylePriority.UseBorders = false;
            this.PageHeader.StylePriority.UseTextAlignment = false;
            this.PageHeader.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // TopMargin
            // 
            this.TopMargin.Dpi = 254F;
            this.TopMargin.HeightF = 15F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Visible = false;
            // 
            // Detail
            // 
            this.Detail.Dpi = 254F;
            this.Detail.HeightF = 0F;
            this.Detail.Name = "Detail";
            this.Detail.Visible = false;
            // 
            // label3
            // 
            this.label3.Dpi = 254F;
            this.label3.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[BARCODE_DISP]")});
            this.label3.Font = new System.Drawing.Font("3 of 9 Barcode", 24F);
            this.label3.LocationFloat = new DevExpress.Utils.PointFloat(43.52084F, 55F);
            this.label3.Multiline = true;
            this.label3.Name = "label3";
            this.label3.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label3.SizeF = new System.Drawing.SizeF(660.4063F, 118.1433F);
            this.label3.StylePriority.UseFont = false;
            this.label3.StylePriority.UseTextAlignment = false;
            this.label3.Text = "label3";
            this.label3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label2
            // 
            this.label2.Dpi = 254F;
            this.label2.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[PACK_DISP]")});
            this.label2.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold);
            this.label2.LocationFloat = new DevExpress.Utils.PointFloat(43.52084F, 173.1433F);
            this.label2.Multiline = true;
            this.label2.Name = "label2";
            this.label2.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label2.SizeF = new System.Drawing.SizeF(660.4063F, 83.81996F);
            this.label2.StylePriority.UseFont = false;
            this.label2.StylePriority.UseTextAlignment = false;
            this.label2.Text = "label2";
            this.label2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // XtraReport
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
                        this.BottomMargin,
                        this.PageHeader,
                        this.TopMargin,
                        this.Detail});
            this.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.DataSourceSchema = resources.GetString("$this.DataSourceSchema");
            this.Dpi = 254F;
            this.Margins = new System.Drawing.Printing.Margins(0, 0, 15, 0);
            this.Name = "XtraReport";
            this.PageHeight = 508;
            this.PageWidth = 762;
            this.PaperKind = System.Drawing.Printing.PaperKind.Custom;
            this.ReportUnit = DevExpress.XtraReports.UI.ReportUnit.TenthsOfAMillimeter;
            this.ShowPrintMarginsWarning = false;
            this.SnapGridSize = 31.75F;
            this.Tag = resources.GetString("$this.Tag");
            this.Version = "18.1";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();
        }
    }
}
