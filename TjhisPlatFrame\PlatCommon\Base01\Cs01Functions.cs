﻿/*********************************************
* 文 件 名：Cs01Functions
* 类 名 称：Cs01Functions
* 功能说明：静态基本类全局函数
/*********************************************/
using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using System.Net;
using System.Net.Sockets;
using System.IO;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;

namespace PlatCommon.Base01
{
    /// <summary>
    /// 静态基本类全局函数
    /// </summary>
    public static class Cs01Functions
    {
        private static String[] Ls_ShZ = { "零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾" };
        private static String[] Ls_DW_Zh = { "元", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿", "拾", "佰", "仟", "万" };
        private static String[] Num_DW = { "", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿", "拾", "佰", "仟", "万" };
        private static String[] Ls_DW_X = { "角", "分" };

        /// <summary>
        /// 强制转换为Int
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="t"></param>
        /// <returns></returns>
        public static int CInt<T>(T t)
        {
            int i = 0;
            try
            {
                i = Convert.ToInt32(t);
            }
            catch (Exception) { i = 0; }
            return i;
        }

        /// <summary>
        /// 强制转换为Long
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="t"></param>
        /// <returns></returns>
        public static Int64 CLng<T>(T t)
        {
            Int64 i = 0;
            try
            {
                i = Convert.ToInt64(t);
            }
            catch (Exception) { i = 0; }
            return i;
        }

        /// <summary>
        /// 强制转换为Decimal
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="t"></param>
        /// <returns></returns>
        public static decimal CDecimal<T>(T t)
        {
            decimal i = 0M;
            try
            {
                i = Convert.ToDecimal(t);
            }
            catch (Exception) { i = 0; }
            return i;
        }

        /// <summary>
        /// 强制转换为Double
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="t"></param>
        /// <returns></returns>
        public static double CDbl<T>(T t)
        {
            Double i = 0.00d;
            try
            {
                i = Convert.ToDouble(t);
                if (i != 0)
                {
                    if (i > 0)
                    {
                        i = Math.Round(i + 0.00000001, 6, MidpointRounding.AwayFromZero);
                    }
                    else
                    {
                        i = Math.Round(i - 0.00000001, 6, MidpointRounding.AwayFromZero);
                    }
                }
            }
            catch (Exception) { i = 0; }
            return i;
        }

        /// <summary>
        /// 强制转换为DateTime
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="t"></param>
        /// <returns></returns>
        public static DateTime CDate<T>(T t)
        {
            DateTime dtTmp = new DateTime(1, 1, 1);
            try
            {
                dtTmp = Convert.ToDateTime(t);
            }
            catch (Exception)
            {

            }
            return dtTmp;
        }

        /// <summary>
        /// 强制转换为Boolean
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="t"></param>
        /// <returns></returns>
        public static bool CBool<T>(T t)
        {
            bool bOk = false;
            string val = (t == null ? "0" : t.ToString());
            if (bool.TryParse(val, out bOk))
            {
                return bOk;
            }
            return false;
        }

        /// <summary>
        /// 强制转换为String
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="t"></param>
        /// <returns></returns>
        public static string CStr<T>(T t)
        {
            if (t == null) return "";
            return t.ToString();
        }

        /// <summary>
        /// 强制转换为指定长度的String
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="t"></param>
        /// <param name="iLen"></param>
        /// <returns></returns>
        public static string CStr<T>(T t, int iLen)
        {
            string strTmp = CStr(t);
            return (iLen > strTmp.Length ? strTmp : strTmp.Substring(0, iLen));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="t"></param>
        /// <returns></returns>
        public static string CStrEx<T>(T t)
        {
            string s = CStr(t);
            if (string.IsNullOrWhiteSpace(s)) return "";
            return Regex.Replace(s, @"^\s+|\s+$", "");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="t"></param>
        /// <param name="len"></param>
        /// <returns></returns>
        public static string CStrEx<T>(T t, int len)
        {
            string s = CStr(t);
            if (string.IsNullOrWhiteSpace(s)) return "";
            s = Regex.Replace(s, @"^\s+|\s+$", "");
            return (len > s.Length ? s : s.Substring(0, len));
        }

        /// <summary>
        /// 取字符串长度
        /// </summary>
        /// <param name="strSource"></param>
        /// <returns></returns>
        public static int GetNvarcharLength(string strSource)
        {
            if (string.IsNullOrWhiteSpace(strSource)) return 0;
            byte[] bs = System.Text.Encoding.Default.GetBytes(strSource);
            return bs.Length;
        }

        /// <summary>
        /// 返回指定长度的串
        /// </summary>
        /// <param name="strSource">源串</param>
        /// <param name="iLen">长度</param>
        /// <returns></returns>
        public static string GetVarcharLengthStr(string strSource, int iLen)
        {
            if (string.IsNullOrWhiteSpace(strSource)) return "";
            byte[] bs = System.Text.Encoding.Default.GetBytes(strSource);
            int n = bs.Length;
            if (n <= iLen) return strSource;
            if (bs[iLen - 1] <= 127) return System.Text.Encoding.Default.GetString(bs, 0, iLen);
            return System.Text.Encoding.Default.GetString(bs, 0, iLen - 1);
        }

        /// <summary>
        /// 去掉字符串中的空格
        /// </summary>
        /// <param name="strSource">源串</param>
        /// <returns></returns>
        public static string Trim(string strSource)
        {
            string s = strSource.Trim();
            if (string.IsNullOrWhiteSpace(strSource)) return "";
            return Regex.Replace(strSource, @"^\s+|\s+$", "");
        }

        /// <summary>
        /// 数字串转换为Int
        /// </summary>
        /// <param name="strSource">源串</param>
        /// <returns></returns>
        public static int ParseInt(string strSource)
        {
            if (strSource == null || strSource.Length == 0) return 0;

            int n = strSource.Length;
            int k = n;
            for (int i = 0; i < n; i++)
            {
                string c = strSource.Substring(i, 1);
                if (c.CompareTo("0") < 0 || c.CompareTo("9") > 0)
                {
                    k = i;
                    break;
                }
            }
            if (k == 0) return 0;
            return Cs01Functions.CInt(strSource.Substring(0, k));
        }

        /// <summary>
        /// 判断一个输入字符串是否完全是数字，null和空字符串会返回false
        /// </summary>
        /// <param name="strSource">输入字符串</param>
        /// <returns>true表示合法</returns>
        public static bool IsAllNumber(string strSource)
        {
            string strCheck = CStrEx(strSource);
            int iLen = strCheck.Length;
            if (iLen == 0) return false;
            int i = 0;
            while (i < iLen)
            {
                char c = strCheck[i];
                if (c != '.' && (c < '0' || c > '9'))
                {
                    return false;
                }
                i++;
            }
            return true;
        }

        /// <summary>
        /// 判断字符串是否是ASCII码字符串
        /// </summary>
        /// <param name="strSource">字符串</param>
        /// <returns></returns>
        public static bool IsAscIIString(string strSource)
        {
            bool isok = true;
            if (string.IsNullOrWhiteSpace(strSource)) return true;
            int len = strSource.Length;
            for (int i = 0; i < len; i++)
            {
                char c = strSource[i];
                if (Convert.ToByte(c) > 127)
                {
                    isok = false;
                    break;
                }
            }
            return isok;
        }

        /// <summary>
        /// 判断是否是Email
        /// </summary>
        /// <param name="strSource">字符串</param>
        /// <returns></returns>
        public static bool IsMail(string strSource)
        {
            Regex reg = new Regex(@"^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$");
            return reg.IsMatch(strSource);
        }

        /// <summary>
        /// 判断是否是手机号
        /// </summary>
        /// <param name="strSource">输入字符串</param>
        /// <returns></returns>
        public static bool IsMobile(string strSource)
        {
            Regex reg = new Regex(@"[1-9]\d{10}");
            return reg.IsMatch(strSource);
        }

        /// <summary>
        /// 判断是否是手机号
        /// </summary>
        /// <param name="mobile">输入数字</param>
        /// <returns></returns>
        public static bool IsMobile(Int64 mobile)
        {
            if (mobile >= 10000000000 && mobile <= 99999999999) return true;
            return false;
        }

        /// <summary>
        /// 判断字符串是否是申明变量要求的字符串
        /// </summary>
        /// <param name="strSource">输入字符串</param>
        /// <returns>true:是， false：否</returns>
        public static bool IsVariableString(string strSource)
        {
            Regex reg = new Regex(@"^[_a-zA-Z]\w*$");
            if (string.IsNullOrWhiteSpace(strSource)) return false;
            return reg.IsMatch(strSource);
        }

        /// <summary>
        /// 随机生成长度为N的字符串，每个字符都随机生成
        /// </summary>
        /// <param name="n">长度</param>
        /// <returns></returns>
        public static string Random(int n)
        {
            StringBuilder sb = new StringBuilder();
            int seed = Cs01Functions.CInt((System.DateTime.Now - new DateTime(0)).TotalMilliseconds);
            System.Random r = new Random(seed);
            string arr = @"1234567890ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz!@$%^&*()-_=+[]{}|\;:’”,.<>?/`~";
            for (int i = 0; i < n; i++)
            {
                int i2 = r.Next(90);
                sb.Append(arr[i2]);
            }
            return sb.ToString();
        }

        /// <summary>
        /// 随机生成长度为N的字符串，随机一个整数串
        /// </summary>
        /// <param name="iLength">长度</param>
        /// <returns></returns>
        public static string RandomInt(int iLength)
        {
            if (iLength > 9)
                throw new Exception("数字太大了，分两段生成吧！");

            int seed = Cs01Functions.CInt((System.DateTime.Now - new DateTime(0)).TotalMilliseconds);
            System.Random random = new Random(seed);
            int iMax = (int)Math.Pow(10, iLength) - 1;
            int iResult = random.Next(iMax);
            string strResult = iResult.ToString().PadLeft(iLength, '0');
            return strResult;
        }

        /// <summary>
        /// 将DateTime转为Oracle SQL中的时间串
        /// </summary>
        /// <param name="dtmDatetime">DateTime时间</param>
        /// <returns></returns>
        public static string SqlDateStr(DateTime dtmDatetime)
        {
            if (dtmDatetime.Year <= 1) return "NULL";
            return $"TO_DATE('{DateTimeStr(dtmDatetime)}', 'YYYY-MM-DD HH24:MI:SS')";
        }

        /// <summary>
        /// 将DateTime转为Oracle SQL中的时间串
        /// </summary>
        /// <param name="dateObj">对象</param>
        /// <returns></returns>
        public static string SqlDateStr(object dateObj)
        {
            DateTime d;
            if (dateObj == DBNull.Value) return "NULL";
            if (dateObj.GetType().Name == "DateTime")
            {
                d = Convert.ToDateTime(dateObj);
                return $"TO_DATE('{DateTimeStr(d)}', 'YYYY-MM-DD HH24:MI:SS')";
            }
            string sdate = "NULL";
            if (DateTime.TryParse(dateObj.ToString(), out d))
            {
                sdate = $"TO_DATE('{DateTimeStr(d)}', 'YYYY-MM-DD HH24:MI:SS')";
            }
            return sdate;
        }

        /// <summary>
        /// 格式化Date
        /// </summary>
        /// <param name="dtmDatetime">DateTime日期时间</param>
        /// <returns></returns>
        public static string DateStr(DateTime dtmDatetime)
        {
            return dtmDatetime.ToString("yyyy-MM-dd");
        }

        /// <summary>
        /// 格式化DateTime
        /// </summary>
        /// <param name="dtmDatetime">DateTime日期时间</param>
        /// <returns></returns>
        public static string DateTimeStr(DateTime dtmDatetime)
        {
            if ((dtmDatetime.Hour + dtmDatetime.Minute + dtmDatetime.Second) == 0) return dtmDatetime.ToString("yyyy-MM-dd");
            return dtmDatetime.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 当前时间串yyyy-MM-dd HH:mm:ss
        /// </summary>
        /// <returns></returns>
        public static string Now()
        {
            return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 当前日期串yyyy-MM-dd
        /// </summary>
        /// <returns></returns>
        public static string Today()
        {
            return DateTime.Now.Date.ToString("yyyy-MM-dd");
        }

        /// <summary>
        /// 返回时间戳
        /// </summary>
        /// <param name="d">DateTime日期时间</param>
        /// <returns></returns>
        public static int GetTimeStamp(DateTime d)
        {
            /*时间戳*/
            return Cs01Functions.CInt(d.Subtract(DateTime.Parse("1970-01-01")).TotalSeconds);
        }

        /// <summary>
        /// Unix时间戳
        /// </summary>
        /// <param name="d">DateTime日期时间</param>
        /// <returns></returns>
        public static int GetUnixTimeStamp(DateTime d)
        {
            return Cs01Functions.CInt(d.Subtract(DateTime.Parse("1970-01-01 08:00:00")).TotalSeconds);
            /* Unix时间戳：long epoch = (d.ToUniversalTime().Ticks - 621355968000000000) / 10000000;*/
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="i64">64位整数</param>
        /// <returns></returns>
        public static string BigIntToTimestamp(Int64 i64)
        {
            string x = i64.ToString("X2");
            int n = x.Length;
            return "0x0000000000000000".Substring(0, 18 - n) + x;
        }

        /// <summary>
        /// 整数转换为时间
        /// </summary>
        /// <param name="totalSeconds">总秒数</param>
        /// <returns></returns>
        public static DateTime IntToTime(int totalSeconds)
        {
            return DateTime.Parse("1970-01-01").AddSeconds(totalSeconds + 86400);
        }

        /// <summary>
        /// 当前时间转为整数
        /// </summary>
        /// <returns></returns>
        public static long TimeOf()
        {
            return TimeOf(DateTime.Now);
        }

        /// <summary>
        /// 指定时间转为整数
        /// </summary>
        /// <param name="t">DateTime日期时间</param>
        /// <returns></returns>
        public static long TimeOf(DateTime t)
        {
            DateTime t2 = new DateTime(1970, 1, 1);
            TimeSpan ts = t - t2;
            return (long)ts.TotalSeconds;
        }

        /// <summary>
        /// 当前时间转换Oracle时间(去掉毫秒)
        /// </summary>
        /// <returns></returns>
        public static DateTime OracleTime()
        {
            return OracleTime(DateTime.Now);
        }

        /// <summary>
        /// 指定时间转换Oracle时间(去掉毫秒)
        /// </summary>
        /// <param name="d">DateTime日期时间</param>
        /// <returns></returns>
        public static DateTime OracleTime(DateTime d)
        {
            return new DateTime(d.Year, d.Month, d.Day, d.Hour, d.Minute, d.Second);
        }

        /// <summary>
        /// 字符串转换为字典
        /// </summary>
        /// <param name="strSource">字符串</param>
        /// <param name="strRowDeli">行分隔串</param>
        /// <param name="strColDeli">列分隔串</param>
        /// <returns></returns>
        public static Dictionary<string, string> StringToDict(string strSource, string strRowDeli, string strColDeli)
        {
            string[] strArrRows = Regex.Split(strSource.Trim(), strRowDeli);
            int count = strArrRows.Length;

            Dictionary<string, string> dic = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            string key;
            string[] strArrCols;
            for (int i = 0; i < count; i++)
            {
                strArrCols = Regex.Split(strArrRows[i], strColDeli);
                if (strArrCols.Length > 1)
                {
                    key = strArrCols[0].Trim();
                    if (key.Length > 0)
                    {
                        dic[key] = strArrCols[1].Trim();
                    }
                }
            }
            return dic;
        }

        /// <summary>
        /// 字符串转换为字典
        /// </summary>
        /// <param name="strSource">字符串</param>
        /// <param name="cRowDeli">行分隔符</param>
        /// <param name="cColDeli">列分隔符</param>
        /// <returns></returns>
        public static Dictionary<string, string> StringToDict(string strSource, char cRowDeli, char cColDeli)
        {
            //string strSource = str.Trim();
            string[] strArrRows = strSource.Trim().Split(cRowDeli);
            int count = strArrRows.Length;

            Dictionary<string, string> dic = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            string key;
            string[] strArrCols;
            for (int i = 0; i < count; i++)
            {
                strArrCols = strArrRows[i].Split(cColDeli);
                if (strArrCols.Length > 1)
                {
                    key = strArrCols[0].Trim();
                    if (key.Length > 0)
                    {
                        dic[key] = strArrCols[1].Trim();
                    }
                }
            }
            return dic;
        }

        /// <summary>
        /// 字符串转换为字典
        /// </summary>
        /// <param name="strSource">字符串</param>
        /// <param name="cRowDeli">行分隔符</param>
        /// <param name="cColDeli">列分隔符</param>
        /// <returns></returns>
        public static Dictionary<string, int> StringToDictInt(string strSource, char cRowDeli, char cColDeli)
        {
            string[] strArrRows = strSource.Trim().Split(cRowDeli);
            int count = strArrRows.Length;

            Dictionary<string, int> dic = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            string key;
            string[] strArrCols;
            for (int i = 0; i < count; i++)
            {
                strArrCols = strArrRows[i].Split(cColDeli);
                if (strArrCols.Length > 1)
                {
                    key = strArrCols[0].Trim();
                    if (key.Length > 0)
                    {
                        dic[key] = CInt<string>(strArrCols[1].Trim());
                    }
                }
            }
            return dic;
        }

        /// <summary>
        /// 取借贷字典
        /// </summary>
        /// <returns></returns>
        public static Dictionary<string, string> GetDeditCredit()
        {
            Dictionary<string, string> dictDeditCredit = new Dictionary<string, string>();
            dictDeditCredit.Add("0", "借");
            dictDeditCredit.Add("1", "贷");
            return dictDeditCredit;
        }

        /// <summary>
        /// 字典转换为字符串
        /// </summary>
        /// <typeparam name="TKey"></typeparam>
        /// <typeparam name="TValue"></typeparam>
        /// <param name="dic">字典</param>
        /// <param name="strRowDeli">行分隔串</param>
        /// <param name="strColDeli">列分隔串</param>
        /// <returns></returns>
        public static string DictionaryToString<TKey, TValue>(Dictionary<TKey, TValue> dic, string strRowDeli, string strColDeli)
        {
            int n = dic.Count;
            string[] arr = new string[n];
            n = 0;
            foreach (KeyValuePair<TKey, TValue> kv in dic)
            {
                arr[n++] = $"{kv.Key}{strColDeli}{kv.Value.ToString()}";
            }
            return string.Join(strRowDeli, arr);
        }

        /// <summary>
        /// 字典转换为字符串
        /// </summary>
        /// <param name="dic">字典</param>
        /// <param name="strRowDeli">行分隔串</param>
        /// <param name="strColDeli">列分隔串</param>
        /// <returns></returns>
        public static string DictionaryToString(System.Collections.IDictionary dic, string strRowDeli, string strColDeli)
        {
            int n = dic.Count;
            string[] arr = new string[n];
            n = 0;
            foreach (DictionaryEntry kv in dic)
            {
                if (kv.Value.GetType().Name.IndexOf("Dictionary") >= 0)
                {
                    Type[] types = kv.Value.GetType().GetGenericArguments();
                    if (types.Length == 2)
                    {
                        string value = DictionaryToString((System.Collections.IDictionary)kv.Value, strRowDeli, strColDeli);
                        arr[n++] = $"{kv.Key}{strColDeli}{{{value}}}";
                    }
                }
                else
                {
                    arr[n++] = $"{kv.Key}{strColDeli}{kv.Value.ToString()}";
                }
            }
            return string.Join(strRowDeli, arr);
        }

        /// <summary>
        /// 各种字符串之间的转换
        /// </summary>
        /// <param name="strSource">源串</param>
        /// <param name="charsetFrom">源字符集</param>
        /// <param name="charsetTo">目的字符集</param>
        /// <returns></returns>
        public static string ConvertCodeString(string strSource, string charsetFrom, string charsetTo)
        {  //各种字符串之间的转换 如GB2312->UTF-8 : ConvertCodeString(tbstr, "GB2312", "UTF-8");
            if (strSource == null) return "";
            if (strSource.Length == 0) return "";
            Encoding encode1 = Encoding.GetEncoding(charsetFrom);
            Encoding encode2 = Encoding.GetEncoding(charsetTo);
            byte[] chars1 = encode1.GetBytes(strSource);
            byte[] chars2 = Encoding.Convert(encode1, encode2, chars1);
            string result = encode2.GetString(chars2);
            return result;
        }

        /// <summary>
        /// Utf8转换为GBK
        /// </summary>
        /// <param name="utf8">Utf8串</param>
        /// <returns></returns>
        public static string Utf8ToGBK(string utf8)
        {
            Encoding encode1 = Encoding.GetEncoding("UTF-8");
            Encoding encode2 = Encoding.GetEncoding("GBK");
            byte[] chars1 = encode1.GetBytes(utf8);
            byte[] chars2 = Encoding.Convert(encode1, encode2, chars1);
            string gbk = BytesToHexString(chars2, '%');
            return gbk;
        }

        /// <summary>
        /// 获得本机电脑名称
        /// </summary>
        /// <returns></returns>
        public static string GetHostName()
        {
            return Dns.GetHostName();
        }

        /// <summary>
        /// 获得本机电脑IP
        /// </summary>
        /// <returns></returns>
        public static List<string> GetIP()
        {
            List<string> list = new List<string>();
            IPHostEntry host = Dns.GetHostEntry(Dns.GetHostName());

            foreach (IPAddress ip in host.AddressList)
            {
                if (ip.AddressFamily == AddressFamily.InterNetwork)
                {
                    list.Add(ip.ToString());
                }
            }
            if (list.Count == 0) list.Add("127.0.0.1");
            return list;
        }

        /// <summary>
        /// 获取本机Mac地址
        /// </summary>
        /// <returns></returns>
        public static string GetLocalMac()
        {
            try
            {
                NetworkInterface[] interfaces = NetworkInterface.GetAllNetworkInterfaces();
                foreach (NetworkInterface ni in interfaces)
                {
                    return BitConverter.ToString(ni.GetPhysicalAddress().GetAddressBytes());
                }
            }
            catch (Exception)
            {
            }
            return "00-00-00-00-00-00";
        }

        /// <summary>
        /// 字符串重复
        /// </summary>
        /// <param name="strSource">源字符串</param>
        /// <param name="iNum">重复次数</param>
        /// <returns></returns>
        public static string RepeatString(string strSource, int iNum)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < iNum; i++)
            {
                sb.Append(strSource);
            }
            return sb.ToString();
        }

        /// <summary>
        /// 逗号分隔的数字字符串转换为整数数组
        /// </summary>
        /// <param name="strInts">逗号分隔的数字字符串</param>
        /// <returns></returns>
        public static int[] StringToIntArray(string strInts)
        {
            List<int> list = new List<int>();
            string[] arr = strInts.Split(',');
            for (int i = 0, n = arr.Length; i < n; i++)
            {
                if (arr[i].Trim().Length > 0)
                {
                    list.Add(Cs01Functions.CInt(arr[i]));
                }
            }
            return list.ToArray();
        }

        /// <summary>
        /// SQL串格式化
        /// </summary>
        /// <param name="strSQL"></param>
        /// <returns></returns>
        public static string SqlEncode(string strSQL)
        {
            return strSQL.Replace("'", "''");
        }

        /// <summary>
        /// 读文件到byte[]
        /// </summary>
        /// <param name="strFileFullName">全路径文件名</param>
        /// <returns></returns>
        public static byte[] ReadFileBinary(string strFileFullName)
        {
            //读方式打开文件，得到文件流
            FileStream fs = new FileStream(strFileFullName, FileMode.Open);
            int iLength = (int)fs.Length;
            byte[] data = new byte[iLength];
            fs.Read(data, 0, iLength);
            fs.Close();
            fs.Dispose();
            return data;
        }

        /// <summary>
        /// 分析数据库联接字符串
        /// </summary>
        /// <param name="strValue">字符串</param>
        /// <returns></returns>
        public static string PraseDataSourceString(string strValue)
        {
            string ServerName = String.Empty;
            string DbServiceName = String.Empty;
            int iPort = 1521;

            string val = strValue.Replace(" ", "");
            int i1 = 0, i2 = 0, n = 0;
            n = "(HOST=".Length;
            i1 = val.IndexOf("(HOST=", StringComparison.OrdinalIgnoreCase);
            if (i1 > 0)
            {
                i2 = val.IndexOf(")", i1 + n, StringComparison.OrdinalIgnoreCase);
                if (i2 > i1 + n)
                {
                    ServerName = val.Substring(i1 + n, i2 - i1 - n);
                }
            }

            n = "(PORT=".Length;
            i1 = val.IndexOf("(PORT=", StringComparison.OrdinalIgnoreCase);
            if (i1 > 0)
            {
                i2 = val.IndexOf(")", i1 + n, StringComparison.OrdinalIgnoreCase);
                if (i2 > i1 + n)
                {
                    iPort = Cs01Functions.CInt(val.Substring(i1 + n, i2 - i1 - n));
                    if (iPort <= 0) iPort = 1521;
                }
            }

            n = "(SERVICE_NAME=".Length;
            i1 = val.IndexOf("(SERVICE_NAME=", StringComparison.OrdinalIgnoreCase);
            if (i1 > 0)
            {
                i2 = val.IndexOf(")", i1 + n, StringComparison.OrdinalIgnoreCase);
                if (i2 > i1 + n)
                {
                    DbServiceName = val.Substring(i1 + n, i2 - i1 - n);
                }
            }
            return ServerName + ":" + iPort.ToString() + "/" + DbServiceName;
        }

        /// <summary>
        /// 字符串转为表
        /// </summary>
        /// <param name="strSource">源串</param>
        /// <param name="cColumn">列分隔符</param>
        /// <param name="cRow">行分隔符</param>
        /// <returns></returns>
        public static string SortedKeyValue(string strSource, char cColumn, char cRow)
        {
            SortedDictionary<string, string> d = new SortedDictionary<string, string>();
            string[] a2;
            string[] a1 = strSource.Split(cRow);
            string key = "";
            foreach (string s in a1)
            {
                if (s.Length > 0)
                {
                    a2 = s.Split(cColumn);
                    if (a2.Length == 2)
                    {
                        key = a2[0].Trim();
                        if (key.Length > 0)
                        {
                            d[key] = a2[1];
                        }
                    }
                }
            }
            return SortedKeyValue(d, cColumn, cRow);
        }

        /// <summary>
        /// DIC转为字符串
        /// </summary>
        /// <param name="d"></param>
        /// <param name="cColumn">列分隔符</param>
        /// <param name="cRow">行分隔符</param>
        /// <returns></returns>
        public static string SortedKeyValue(SortedDictionary<string, string> d, char cColumn, char cRow)
        {
            StringBuilder sb = new StringBuilder();
            int count = 0;
            string rows = (cRow == '\0' ? "" : cRow.ToString());
            foreach (KeyValuePair<string, string> kv in d)
            {
                if (count > 0) sb.Append(rows); else count++;
                sb.Append(kv.Key + cColumn.ToString() + kv.Value);
            }
            return sb.ToString();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="d"></param>
        /// <param name="colchar"></param>
        /// <param name="rowchar"></param>
        /// <returns></returns>
        public static string SortedKeyValue(Dictionary<string, string> d, char colchar, char rowchar)
        {
            SortedDictionary<string, string> d2 = new SortedDictionary<string, string>();
            foreach (KeyValuePair<string, string> kv in d)
            {
                d2.Add(kv.Key, kv.Value);
            }
            return SortedKeyValue(d2, colchar, rowchar);
        }

        /// <summary>
        /// Json串编码
        /// </summary>
        /// <param name="strKeyVal">值</param>
        /// <returns></returns>
        public static string JsonString(string strKeyVal)
        {
            return "\"" + strKeyVal + "\"";
        }

        /// <summary>
        /// Json串编码转义
        /// </summary>
        /// <param name="strSource">源串</param>
        /// <returns></returns>
        public static string EncodeJsonValue(string strSource)
        {
            if (string.IsNullOrEmpty(strSource)) return "";
            return strSource.Replace("\"", "\\\"").Replace(Convert.ToString((char)13), "\\r").Replace(Convert.ToString((char)10), "\\n");
        }

        /// <summary>
        /// Html串转义
        /// </summary>
        /// <param name="strSource">源串</param>
        /// <returns></returns>
        public static string HtmlDecode(string strSource)
        {
            return strSource.Replace("&amp;", "&").Replace("&lt;", "<").Replace("&gt;", ">").Replace("&nbsp;", " ").Replace("&apos;", "\'").Replace("&quot;", "\"");
        }

        /// <summary>
        /// 字符串按逗号转字符串数组
        /// </summary>
        /// <param name="strSource">源串</param>
        /// <returns></returns>
        public static string[] StringToStringArray(string strSource)
        {
            List<string> list = new List<string>();
            string[] arr = strSource.Split(',');
            for (int i = 0, n = arr.Length; i < n; i++)
            {
                list.Add(arr[i].Trim());
            }
            return list.ToArray();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idsstr"></param>
        /// <param name="deli"></param>
        /// <returns></returns>
        public static string InSQLStringDecimal(string idsstr, char deli)
        {
            string s2 = idsstr.Replace(" ", "");
            if (s2 == "") throw new Exception($"Invalid Parameter(idsstr={idsstr})。");

            string[] ids = idsstr.Split(deli);
            Dictionary<string, decimal> d = new Dictionary<string, decimal>();
            decimal uid = 0;
            for (int i = 0, n = ids.Length; i < n; i++)
            {
                uid = CDecimal(ids[i]);
                d[uid.ToString()] = uid;
            }
            return string.Join(",", d.Keys);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public static string GetIdsTable(string ids)
        {
            string[] idarr = ids.Split(',');
            return GetIdsTable(idarr);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="deli"></param>
        /// <returns></returns>
        public static string GetIdsTable(string ids, char deli)
        {
            string[] idarr = ids.Split(deli);
            return GetIdsTable(idarr);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idarr"></param>
        /// <returns></returns>
        public static string GetIdsTable(string[] idarr)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < idarr.Length; i++)
            {
                if (i > 0) sb.Append(" UNION ");
                sb.Append($"SELECT {i} as xh, {idarr[i]} as id");
            }
            return sb.ToString();
        }

        /// <summary>
        /// 16进制字符串转化为字节数组
        /// </summary>
        /// <param name="hexstr"></param>
        /// <returns></returns>
        public static byte[] HexStringToBytes(string hexstr)
        {
            byte[] bits;
            int n2 = hexstr.Length;
            if ((n2 % 2) != 0)
            {
                hexstr += " ";
                n2++;
            }
            int n = n2 / 2;
            bits = new byte[n];
            string s2 = "";
            for (int i = 0; i < n; i++)
            {
                s2 = hexstr.Substring(i * 2, 2);
                bits[i] = Convert.ToByte(Convert.ToInt16(s2, 16));
            }
            return bits;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="hexstr"></param>
        /// <param name="seperator"></param>
        /// <returns></returns>
        public static byte[] HexStringToBytes(string hexstr, char seperator)
        {
            //将用分割符分开的16进制字符串转化为字节数组
            string[] s2 = hexstr.Split(seperator);
            int n = s2.Length;
            byte[] bits = new byte[n];
            string s1 = "";
            for (int i = 0; i < n; i++)
            {
                s1 = s2[i];
                bits[i] = Convert.ToByte(Convert.ToInt32(s1, 16));
            }
            return bits;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="bits"></param>
        /// <returns></returns>
        public static string BytesToHexString(byte[] bits)
        {  //字节数组转化为16进制字符串
            int n = bits.Length;
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < n; i++)
            {
                sb.Append(bits[i].ToString("x2")); //Convert.ToString(bits[i], 16)
            }
            return sb.ToString();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="bits"></param>
        /// <param name="seperator"></param>
        /// <returns></returns>
        public static string BytesToHexString(byte[] bits, char seperator)
        {
            //将字节数组转化为用分割符分开的16进制字符串
            int n = bits.Length;
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < n; i++)
            {
                sb.Append(seperator + Convert.ToString(bits[i], 16));
            }
            return sb.ToString();
        }

        /// <summary>
        /// 从汉字转换到16进制
        /// </summary>
        /// <param name="strSource">源串</param>
        /// <param name="charset">字符集</param>
        /// <returns></returns>
        public static string StringToHexString(string strSource, string charset)
        {
            Encoding encode = Encoding.GetEncoding(charset);
            byte[] bytes = encode.GetBytes(strSource);
            return BytesToHexString(bytes);
        }

        /// <summary>
        /// 字符串转化为16进制字符串
        /// </summary>
        /// <param name="strSource">源串</param>
        /// <param name="encode">字符编码</param>
        /// <returns></returns>
        public static string StringToHexString(string strSource, Encoding encode)
        {   //
            byte[] b = encode.GetBytes(strSource);//按照指定编码将string编程字节数组
            return BytesToHexString(b);
        }

        /// <summary>
        /// 16进制字符串转化为字符串
        /// </summary>
        /// <param name="strHexSource">16进制源串</param>
        /// <param name="encode">字符编码</param>
        /// <returns></returns>
        public static string HexStringToString(string strHexSource, Encoding encode)
        {   //
            byte[] chars;
            if (strHexSource == null) return "";
            if (strHexSource.Length == 0) return "";
            chars = HexStringToBytes(strHexSource);
            //按照指定编码将字节数组变为字符串
            return encode.GetString(chars);
        }

        /// <summary>
        /// 16进制字符串转化为字符串
        /// </summary>
        /// <param name="strHexSource">16进制源串</param>
        /// <param name="charset">字符集</param>
        /// <returns></returns>
        public static string HexStringToString(string strHexSource, string charset)
        {  //16进制转换成汉字
            byte[] chars;
            if (strHexSource == null) return "";
            if (strHexSource.Length == 0) return "";
            chars = HexStringToBytes(strHexSource);
            Encoding encode = Encoding.GetEncoding(charset);
            return encode.GetString(chars);
        }

        /// <summary>
        /// 金额小写转中文大写。
        /// 整数支持到万亿；小数部分支持到分(超过两位将进行Banker舍入法处理)
        /// </summary>
        /// <param name="value">需要转换的双精度浮点数</param>
        /// <returns>转换后的字符串</returns>
        public static String ToMoneyStr<T>(T value)
        {
            Boolean iXSh_bool = false;//是否含有小数，默认没有(0则视为没有)
            Boolean iZhSh_bool = true;//是否含有整数,默认有(0则视为没有)
            Boolean iFushu_bool = false;//是否含有整数,默认有(0则视为没有)

            string NumStr;//整个数字字符串
            string NumStr_Zh;//整数部分
            string NumSr_X = "";//小数部分
            string NumStr_DQ;//当前的数字字符
            string NumStr_R = "";//返回的字符串

            decimal Num = Cs01Functions.CDecimal<T>(value);

            Num = Math.Round(Num, 2);//四舍五入取两位

            //各种非正常情况处理
            if (Num == 0)
                return Ls_ShZ[0];

            if (Num > Cs01Functions.CDecimal(9999999999999.99))
                return "金额太大了！";

            //负数
            if (Num < 0)
            {
                Num = 0 - Num;
                iFushu_bool = true;
            }

            //判断是否有整数
            if (Num < Cs01Functions.CDecimal(1.00))
                iZhSh_bool = false;

            NumStr = Num.ToString();

            NumStr_Zh = NumStr;//默认只有整数部分
            if (NumStr_Zh.Contains("."))
            {//分开整数与小数处理
                NumStr_Zh = NumStr.Substring(0, NumStr.IndexOf("."));
                NumSr_X = NumStr.Substring((NumStr.IndexOf(".") + 1), (NumStr.Length - NumStr.IndexOf(".") - 1));
                iXSh_bool = true;
            }

            if (NumSr_X == "" || int.Parse(NumSr_X) <= 0)
            {//判断是否含有小数部分
                iXSh_bool = false;
            }

            if (iZhSh_bool)
            {//整数部分处理
                // NumStr_Zh = Reversion_Str(NumStr_Zh);// 反转字符串

                // 反转字符串
                char[] cs = NumStr_Zh.ToCharArray();
                Array.Reverse(cs);
                NumStr_Zh = new string(cs);

                for (int a = 0; a < NumStr_Zh.Length; a++)
                {//整数部分转换
                    NumStr_DQ = NumStr_Zh.Substring(a, 1);
                    if (int.Parse(NumStr_DQ) != 0)
                        NumStr_R = Ls_ShZ[int.Parse(NumStr_DQ)] + Ls_DW_Zh[a] + NumStr_R;
                    else if (a == 0 || a == 4 || a == 8)
                    {
                        if (NumStr_Zh.Length > 8 && a == 4)
                            continue;
                        NumStr_R = Ls_DW_Zh[a] + NumStr_R;
                    }
                    else if (int.Parse(NumStr_Zh.Substring(a - 1, 1)) != 0)
                        NumStr_R = Ls_ShZ[int.Parse(NumStr_DQ)] + NumStr_R;

                }

                if (!iXSh_bool)
                    return NumStr_R + "整";

                //NumStr_R += "零";
            }

            for (int b = 0; b < NumSr_X.Length; b++)
            {//小数部分转换
                NumStr_DQ = NumSr_X.Substring(b, 1);
                if (int.Parse(NumStr_DQ) != 0)
                    NumStr_R += Ls_ShZ[int.Parse(NumStr_DQ)] + Ls_DW_X[b];
                else if (b != 1 && iZhSh_bool)
                    NumStr_R += Ls_ShZ[int.Parse(NumStr_DQ)];
            }

            if (iFushu_bool)
                return "负" + NumStr_R;
            else
                return NumStr_R;
        }
    }

    /// <summary>
    /// 结构体与byte数组互转
    /// </summary>
    public static class Struct2Bytes
    {
        /// <summary>
        /// 结构体转byte数组
        /// </summary>
        /// <param name="structObj">要转换的结构体</param>
        /// <returns>转换后的byte数组</returns>
        public static byte[] StructToBytes(object structObj)
        {
            //得到结构体的大小
            int iSize = Marshal.SizeOf(structObj);

            //创建byte数组
            byte[] bArrBytes = new byte[iSize];

            //分配结构体大小的内存空间
            IntPtr structPtr = Marshal.AllocHGlobal(iSize);
            try
            {
                // 将结构体拷到分配好的内存空间
                Marshal.StructureToPtr(structObj, structPtr, false);

                //从内存空间拷到byte数组
                Marshal.Copy(structPtr, bArrBytes, 0, iSize);

                //返回byte数组
                return bArrBytes;
            }
            finally
            {
                //释放内存空间
                Marshal.FreeHGlobal(structPtr);
            }

        }

        /// <summary>
        /// byte数组转结构体
        /// </summary>
        /// <param name="bytes">byte数组</param>
        /// <param name="type">结构体类型</param>
        /// <returns>转换后的结构体</returns>
        public static object BytesToStruct(byte[] bytes, Type type)
        {
            //得到结构体的大小
            int iSize = Marshal.SizeOf(type);

            //byte数组长度小于结构体的大小
            if (iSize > bytes.Length)
            {
                //返回空
                return null;
            }

            //分配结构体大小的内存空间
            IntPtr structPtr = Marshal.AllocHGlobal(iSize);
            try
            {
                //将byte数组拷到分配好的内存空间
                Marshal.Copy(bytes, 0, structPtr, iSize);

                //将内存空间转换为目标结构体
                object obj = Marshal.PtrToStructure(structPtr, type);

                //返回结构体
                return obj;
            }
            finally
            {
                //释放内存空间
                Marshal.FreeHGlobal(structPtr);
            }
        }

        /// <summary>
        /// byte数组转结构体
        /// </summary>
        /// <typeparam name="T">结构体类型</typeparam>
        /// <param name="bytes">byte数组</param>
        /// <returns>转换后的结构体</returns>
        public static T BytesToStruct<T>(byte[] bytes) where T : struct
        {
            //得到结构体的大小
            int iSize = Marshal.SizeOf(typeof(T));

            //byte数组长度小于结构体的大小
            if (iSize > bytes.Length)
            {
                //返回空
                return default(T);
            }

            //分配结构体大小的内存空间
            IntPtr structPtr = Marshal.AllocHGlobal(iSize);
            try
            {
                //将byte数组拷到分配好的内存空间
                Marshal.Copy(bytes, 0, structPtr, iSize);

                //将内存空间转换为目标结构体
                object obj = Marshal.PtrToStructure(structPtr, typeof(T));

                //返回结构体
                return (T)obj;
            }
            finally
            {
                //释放内存空间
                Marshal.FreeHGlobal(structPtr);
            }
        }
    }
}
