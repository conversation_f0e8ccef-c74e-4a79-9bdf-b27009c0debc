/// <XRTypeInfo>
///   <AssemblyFullName>DevExpress.XtraReports.v18.1, Version=18.1.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</AssemblyFullName>
///   <AssemblyLocation>D:\李庆来\消毒供应追溯管理系统（DISINFECT）\消毒供应程序20230305（正式）\Disinfect\DevExpress.XtraReports.v18.1.dll</AssemblyLocation>
///   <TypeName>DevExpress.XtraReports.UI.XtraReport</TypeName>
///   <Localization>zh-Hans</Localization>
///   <Version>18.1</Version>
///   <Resources>
///     <Resource Name="XtraReportSerialization.XtraReport">
/// zsrvvgEAAACRAAAAbFN5c3RlbS5SZXNvdXJjZXMuUmVzb3VyY2VSZWFkZXIsIG1zY29ybGliLCBWZXJzaW9uPTQuMC4wLjAsIEN1bHR1cmU9bmV1dHJhbCwgUHVibGljS2V5VG9rZW49Yjc3YTVjNTYxOTM0ZTA4OSNTeXN0ZW0uUmVzb3VyY2VzLlJ1bnRpbWVSZXNvdXJjZVNldAIAAAACAAAAAAAAAFBBRFBBRFA141seO0eBSgAAAAAxAAAADAEAACwkAHQAaABpAHMALgBEAGEAdABhAFMAbwB1AHIAYwBlAFMAYwBoAGUAbQBhAAAAAAASJAB0AGgAaQBzAC4AVABhAGcA0goAAAHPFTw/eG1sIHZlcnNpb249IjEuMCI/Pg0KPHhzOnNjaGVtYSBpZD0iTmV3RGF0YVNldCIgeG1sbnM9IiIgeG1sbnM6eHM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDEvWE1MU2NoZW1hIiB4
/// bWxuczptc2RhdGE9InVybjpzY2hlbWFzLW1pY3Jvc29mdC1jb206eG1sLW1zZGF0YSIgeG1sbnM6bXNwcm9wPSJ1cm46c2NoZW1hcy1taWNyb3NvZnQtY29tOnhtbC1tc3Byb3AiPg0KICA8eHM6ZWxlbWVudCBuYW1lPSJOZXdEYXRhU2V0IiBtc2RhdGE6SXNEYXRhU2V0PSJ0cnVlIiBtc2RhdGE6VXNlQ3VycmVudExvY2FsZT0idHJ1ZSI+DQogICAgPHhzOmNvbXBsZXhUeXBlPg0KICAgICAgPHhzOmNob2ljZSBtaW5PY2N1cnM9IjAiIG1heE9jY3Vycz0idW5ib3VuZGVkIj4NCiAgICAgICAgPHhzOmVsZW1lbnQgbmFtZT0iVGFibGVEYXRhIiBtc3Byb3A6QmFzZVRhYmxlLjI9IkJFRF9SRUMiIG1zcHJvcDpCYXNlVGFibGUuMD0iUFJFUEFZTUVOVF9SQ1BUIiBt
/// c3Byb3A6QmFzZVRhYmxlLjE9IlBBVF9NQVNURVJfSU5ERVgiPg0KICAgICAgICAgIDx4czpjb21wbGV4VHlwZT4NCiAgICAgICAgICAgIDx4czpzZXF1ZW5jZT4NCiAgICAgICAgICAgICAgPHhzOmVsZW1lbnQgbmFtZT0iSE9TUElUQUxfTkFNRSIgbXNkYXRhOlJlYWRPbmx5PSJ0cnVlIiBtc3Byb3A6T3JhRGJUeXBlPSIxMjYiIG1pbk9jY3Vycz0iMCI+DQogICAgICAgICAgICAgICAgPHhzOnNpbXBsZVR5cGU+DQogICAgICAgICAgICAgICAgICA8eHM6cmVzdHJpY3Rpb24gYmFzZT0ieHM6c3RyaW5nIj4NCiAgICAgICAgICAgICAgICAgICAgPHhzOm1heExlbmd0aCB2YWx1ZT0iNDAiIC8+DQogICAgICAgICAgICAgICAgICA8L3hzOnJlc3RyaWN0aW9uPg0K
/// ICAgICAgICAgICAgICAgIDwveHM6c2ltcGxlVHlwZT4NCiAgICAgICAgICAgICAgPC94czplbGVtZW50Pg0KICAgICAgICAgICAgICA8eHM6ZWxlbWVudCBuYW1lPSJQQVRJRU5UX0lEIiBtc3Byb3A6QmFzZUNvbHVtbj0iUEFUSUVOVF9JRCIgbXNwcm9wOk9yYURiVHlwZT0iMTI2IiBtaW5PY2N1cnM9IjAiPg0KICAgICAgICAgICAgICAgIDx4czpzaW1wbGVUeXBlPg0KICAgICAgICAgICAgICAgICAgPHhzOnJlc3RyaWN0aW9uIGJhc2U9InhzOnN0cmluZyI+DQogICAgICAgICAgICAgICAgICAgIDx4czptYXhMZW5ndGggdmFsdWU9IjIwIiAvPg0KICAgICAgICAgICAgICAgICAgPC94czpyZXN0cmljdGlvbj4NCiAgICAgICAgICAgICAgICA8L3hzOnNpbXBs
/// ZVR5cGU+DQogICAgICAgICAgICAgIDwveHM6ZWxlbWVudD4NCiAgICAgICAgICAgICAgPHhzOmVsZW1lbnQgbmFtZT0iTkFNRSIgbXNwcm9wOkJhc2VDb2x1bW49Ik5BTUUiIG1zcHJvcDpPcmFEYlR5cGU9IjEyNiIgbWluT2NjdXJzPSIwIj4NCiAgICAgICAgICAgICAgICA8eHM6c2ltcGxlVHlwZT4NCiAgICAgICAgICAgICAgICAgIDx4czpyZXN0cmljdGlvbiBiYXNlPSJ4czpzdHJpbmciPg0KICAgICAgICAgICAgICAgICAgICA8eHM6bWF4TGVuZ3RoIHZhbHVlPSI0MCIgLz4NCiAgICAgICAgICAgICAgICAgIDwveHM6cmVzdHJpY3Rpb24+DQogICAgICAgICAgICAgICAgPC94czpzaW1wbGVUeXBlPg0KICAgICAgICAgICAgICA8L3hzOmVsZW1lbnQ+DQog
/// ICAgICAgICAgICAgIDx4czplbGVtZW50IG5hbWU9IkFNT1VOVCIgbXNkYXRhOlJlYWRPbmx5PSJ0cnVlIiBtc3Byb3A6T3JhRGJUeXBlPSIxMDciIHR5cGU9InhzOmRlY2ltYWwiIG1pbk9jY3Vycz0iMCIgLz4NCiAgICAgICAgICAgICAgPHhzOmVsZW1lbnQgbmFtZT0iVklTSVRfSUQiIG1zcHJvcDpCYXNlQ29sdW1uPSJWSVNJVF9JRCIgbXNwcm9wOk9yYURiVHlwZT0iMTExIiB0eXBlPSJ4czpzaG9ydCIgbWluT2NjdXJzPSIwIiAvPg0KICAgICAgICAgICAgICA8eHM6ZWxlbWVudCBuYW1lPSJCRURfTEFCRUwiIG1zcHJvcDpCYXNlQ29sdW1uPSJCRURfTEFCRUwiIG1zcHJvcDpPcmFEYlR5cGU9IjEyNiIgbWluT2NjdXJzPSIwIj4NCiAgICAgICAgICAgICAg
/// ICA8eHM6c2ltcGxlVHlwZT4NCiAgICAgICAgICAgICAgICAgIDx4czpyZXN0cmljdGlvbiBiYXNlPSJ4czpzdHJpbmciPg0KICAgICAgICAgICAgICAgICAgICA8eHM6bWF4TGVuZ3RoIHZhbHVlPSI4IiAvPg0KICAgICAgICAgICAgICAgICAgPC94czpyZXN0cmljdGlvbj4NCiAgICAgICAgICAgICAgICA8L3hzOnNpbXBsZVR5cGU+DQogICAgICAgICAgICAgIDwveHM6ZWxlbWVudD4NCiAgICAgICAgICAgIDwveHM6c2VxdWVuY2U+DQogICAgICAgICAgPC94czpjb21wbGV4VHlwZT4NCiAgICAgICAgPC94czplbGVtZW50Pg0KICAgICAgICA8eHM6ZWxlbWVudCBuYW1lPSJTSU5HTEVfUk9XX1RBQkxFIj4NCiAgICAgICAgICA8eHM6Y29tcGxleFR5cGU+DQog
/// ICAgICAgICAgICA8eHM6c2VxdWVuY2U+DQogICAgICAgICAgICAgIDx4czplbGVtZW50IG5hbWU9IldBUkRfQ09ERSIgdHlwZT0ieHM6c3RyaW5nIiBtaW5PY2N1cnM9IjAiIC8+DQogICAgICAgICAgICAgIDx4czplbGVtZW50IG5hbWU9IldBUkRfTkFNRSIgdHlwZT0ieHM6c3RyaW5nIiBtaW5PY2N1cnM9IjAiIC8+DQogICAgICAgICAgICAgIDx4czplbGVtZW50IG5hbWU9IkRBVEVfQkVHSU4iIHR5cGU9InhzOnN0cmluZyIgbWluT2NjdXJzPSIwIiAvPg0KICAgICAgICAgICAgPC94czpzZXF1ZW5jZT4NCiAgICAgICAgICA8L3hzOmNvbXBsZXhUeXBlPg0KICAgICAgICA8L3hzOmVsZW1lbnQ+DQogICAgICA8L3hzOmNob2ljZT4NCiAgICA8L3hzOmNvbXBs
/// ZXhUeXBlPg0KICA8L3hzOmVsZW1lbnQ+DQo8L3hzOnNjaGVtYT4B8wRTRUxFQ1QgKHNlbGVjdCBob3NwaXRhbCBmcm9tIGhvc3BpdGFsX2NvbmZpZykgaG9zcGl0YWxfbmFtZSxhLlBBVElFTlRfSUQsICAgICAgIGMuTkFNRSwgICAgICAgc3VtKGEuQU1PVU5UKSBBTU9VTlQsICAgICAgIGEuVklTSVRfSUQsICAgICAgIGQuQkVEX0xBQkVMICBGUk9NIFBSRVBBWU1FTlRfUkNQVCBhLCBQQVRTX0lOX0hPU1BJVEFMIGIsIFBBVF9NQVNURVJfSU5ERVggYywgQkVEX1JFQyBkIHdoZXJlIGEuVFJBTlNBQ1RfVFlQRSA9ICfkuqTmrL4nICAgYW5kIChhLlJFRlVOREVEX1JDUFRfTk8gPSAnJyBvciBhLlJFRlVOREVEX1JDUFRfTk8gaXMgbnVsbCkgICBBTkQgYS5QQVRJ
/// RU5UX0lEID0gYy5QQVRJRU5UX0lEICAgQU5EIGEuUEFUSUVOVF9JRCA9IGIuUEFUSUVOVF9JRCAgIEFORCBhLlZJU0lUX0lEID0gYi5WSVNJVF9JRCAgIEFORCBiLldBUkRfQ09ERSA9IGQuV0FSRF9DT0RFICAgQU5EIGIuQkVEX05PID0gZC5CRURfTk8gICBhbmQgYi5XQVJEX0NPREUgPSB7V0FSRENPREV9IGdyb3VwIGJ5IGEuUEFUSUVOVF9JRCwgICAgICAgICAgYy5OQU1FLCAgICAgICAgICBhLlZJU0lUX0lELCAgICAgICAgICBiLkJFRF9OTywgICAgICAgICAgZC5CRURfTEFCRUwgb3JkZXIgYnkgYi5CRURfTk8=</Resource>
///   </Resources>
/// </XRTypeInfo>
namespace XtraReportSerialization {
    
    public class XtraReport : DevExpress.XtraReports.UI.XtraReport {
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.XRLabel label18;
        private DevExpress.XtraReports.UI.XRLabel label5;
        private DevExpress.XtraReports.UI.XRLabel label32;
        private DevExpress.XtraReports.UI.XRLabel label31;
        private DevExpress.XtraReports.UI.XRLabel label4;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.GroupHeaderBand GroupHeader1;
        private DevExpress.XtraReports.UI.GroupFooterBand GroupFooter1;
        private DevExpress.XtraReports.UI.XRLabel label27;
        private DevExpress.XtraReports.UI.XRLabel label3;
        private DevExpress.XtraReports.UI.GroupHeaderBand GroupHeader2;
        private DevExpress.XtraReports.UI.XRLabel label26;
        private DevExpress.XtraReports.UI.XRLabel label25;
        private DevExpress.XtraReports.UI.XRLabel label2;
        private DevExpress.XtraReports.UI.XRLabel label8;
        private DevExpress.XtraReports.UI.XRLabel label16;
        private DevExpress.XtraReports.UI.XRLabel label15;
        private DevExpress.XtraReports.UI.XRLabel label17;
        private DevExpress.XtraReports.UI.XRLabel label1;
        private DevExpress.XtraReports.UI.XRLabel label6;
        private DevExpress.XtraReports.UI.XRLabel label12;
        private DevExpress.XtraReports.UI.XRLabel label9;
        private DevExpress.XtraReports.UI.XRLabel label7;
        private DevExpress.XtraReports.UI.XRLabel label24;
        private DevExpress.XtraReports.UI.XRLabel label23;
        private DevExpress.XtraReports.UI.XRLabel label22;
        private DevExpress.XtraReports.UI.XRLabel label19;
        private DevExpress.XtraReports.UI.XRLabel label14;
        private DevExpress.XtraReports.UI.XRLabel label13;
        private DevExpress.XtraReports.UI.XRLabel label11;
        private DevExpress.XtraReports.UI.XRLabel label10;
        private DevExpress.XtraReports.UI.XRLabel label20;
        private DevExpress.XtraReports.UI.GroupFooterBand GroupFooter2;
        private DevExpress.XtraReports.UI.XRLine line4;
        private System.Resources.ResourceManager _resources;
        private string _resourceString;
        public XtraReport() {
            this._resourceString = DevExpress.XtraReports.Serialization.XRResourceManager.GetResourceFor("XtraReportSerialization.XtraReport");
            this.InitializeComponent();
        }
        private System.Resources.ResourceManager resources {
            get {
                if (_resources == null) {
                    this._resources = new DevExpress.XtraReports.Serialization.XRResourceManager(this._resourceString);
                }
                return this._resources;
            }
        }
        private void InitializeComponent() {
            DevExpress.XtraReports.UI.XRSummary summary1 = new DevExpress.XtraReports.UI.XRSummary();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.GroupHeader1 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.GroupFooter1 = new DevExpress.XtraReports.UI.GroupFooterBand();
            this.GroupHeader2 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.GroupFooter2 = new DevExpress.XtraReports.UI.GroupFooterBand();
            this.label18 = new DevExpress.XtraReports.UI.XRLabel();
            this.label5 = new DevExpress.XtraReports.UI.XRLabel();
            this.label32 = new DevExpress.XtraReports.UI.XRLabel();
            this.label31 = new DevExpress.XtraReports.UI.XRLabel();
            this.label4 = new DevExpress.XtraReports.UI.XRLabel();
            this.label27 = new DevExpress.XtraReports.UI.XRLabel();
            this.label3 = new DevExpress.XtraReports.UI.XRLabel();
            this.label26 = new DevExpress.XtraReports.UI.XRLabel();
            this.label25 = new DevExpress.XtraReports.UI.XRLabel();
            this.label2 = new DevExpress.XtraReports.UI.XRLabel();
            this.label8 = new DevExpress.XtraReports.UI.XRLabel();
            this.label16 = new DevExpress.XtraReports.UI.XRLabel();
            this.label15 = new DevExpress.XtraReports.UI.XRLabel();
            this.label17 = new DevExpress.XtraReports.UI.XRLabel();
            this.label1 = new DevExpress.XtraReports.UI.XRLabel();
            this.label6 = new DevExpress.XtraReports.UI.XRLabel();
            this.label12 = new DevExpress.XtraReports.UI.XRLabel();
            this.label9 = new DevExpress.XtraReports.UI.XRLabel();
            this.label7 = new DevExpress.XtraReports.UI.XRLabel();
            this.label24 = new DevExpress.XtraReports.UI.XRLabel();
            this.label23 = new DevExpress.XtraReports.UI.XRLabel();
            this.label22 = new DevExpress.XtraReports.UI.XRLabel();
            this.label19 = new DevExpress.XtraReports.UI.XRLabel();
            this.label14 = new DevExpress.XtraReports.UI.XRLabel();
            this.label13 = new DevExpress.XtraReports.UI.XRLabel();
            this.label11 = new DevExpress.XtraReports.UI.XRLabel();
            this.label10 = new DevExpress.XtraReports.UI.XRLabel();
            this.label20 = new DevExpress.XtraReports.UI.XRLabel();
            this.line4 = new DevExpress.XtraReports.UI.XRLine();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // TopMargin
            // 
            this.TopMargin.Dpi = 254F;
            this.TopMargin.HeightF = 16F;
            this.TopMargin.Name = "TopMargin";
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.label18,
                        this.label5,
                        this.label32,
                        this.label31,
                        this.label4});
            this.Detail.Dpi = 254F;
            this.Detail.Font = new System.Drawing.Font("Times New Roman", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Detail.HeightF = 60.68541F;
            this.Detail.Name = "Detail";
            this.Detail.StylePriority.UseFont = false;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Dpi = 254F;
            this.BottomMargin.HeightF = 7.496581F;
            this.BottomMargin.Name = "BottomMargin";
            // 
            // GroupHeader1
            // 
            this.GroupHeader1.Dpi = 254F;
            this.GroupHeader1.GroupFields.AddRange(new DevExpress.XtraReports.UI.GroupField[] {
                        new DevExpress.XtraReports.UI.GroupField("DEPT_NAME", DevExpress.XtraReports.UI.XRColumnSortOrder.Ascending),
                        new DevExpress.XtraReports.UI.GroupField("PACK_NAME", DevExpress.XtraReports.UI.XRColumnSortOrder.Ascending)});
            this.GroupHeader1.HeightF = 0F;
            this.GroupHeader1.Name = "GroupHeader1";
            // 
            // GroupFooter1
            // 
            this.GroupFooter1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.label27,
                        this.label3});
            this.GroupFooter1.Dpi = 254F;
            this.GroupFooter1.HeightF = 86.78599F;
            this.GroupFooter1.Name = "GroupFooter1";
            // 
            // GroupHeader2
            // 
            this.GroupHeader2.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.label26,
                        this.label25,
                        this.label2,
                        this.label8,
                        this.label16,
                        this.label15,
                        this.label17,
                        this.label1,
                        this.label6,
                        this.label12,
                        this.label9,
                        this.label7,
                        this.label24,
                        this.label23,
                        this.label22,
                        this.label19,
                        this.label14,
                        this.label13,
                        this.label11,
                        this.label10,
                        this.label20});
            this.GroupHeader2.Dpi = 254F;
            this.GroupHeader2.GroupFields.AddRange(new DevExpress.XtraReports.UI.GroupField[] {
                        new DevExpress.XtraReports.UI.GroupField("DEPT_NAME", DevExpress.XtraReports.UI.XRColumnSortOrder.Ascending)});
            this.GroupHeader2.HeightF = 425.0972F;
            this.GroupHeader2.Level = 1;
            this.GroupHeader2.Name = "GroupHeader2";
            // 
            // GroupFooter2
            // 
            this.GroupFooter2.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.line4});
            this.GroupFooter2.Dpi = 254F;
            this.GroupFooter2.HeightF = 82.90272F;
            this.GroupFooter2.Level = 1;
            this.GroupFooter2.Name = "GroupFooter2";
            // 
            // label18
            // 
            this.label18.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label18.Dpi = 254F;
            this.label18.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[PACK_CODE]")});
            this.label18.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label18.LocationFloat = new DevExpress.Utils.PointFloat(365.381F, 0F);
            this.label18.Multiline = true;
            this.label18.Name = "label18";
            this.label18.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label18.SizeF = new System.Drawing.SizeF(189.0161F, 58.41999F);
            this.label18.StylePriority.UseBorders = false;
            this.label18.StylePriority.UseFont = false;
            this.label18.StylePriority.UseTextAlignment = false;
            this.label18.Text = "label31";
            this.label18.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label5
            // 
            this.label5.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label5.Dpi = 254F;
            this.label5.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[MEMO]")});
            this.label5.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label5.LocationFloat = new DevExpress.Utils.PointFloat(890.3691F, 0F);
            this.label5.Multiline = true;
            this.label5.Name = "label5";
            this.label5.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label5.SizeF = new System.Drawing.SizeF(183.2121F, 58.41999F);
            this.label5.StylePriority.UseBorders = false;
            this.label5.StylePriority.UseFont = false;
            this.label5.StylePriority.UseTextAlignment = false;
            this.label5.Text = "label32";
            this.label5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label32
            // 
            this.label32.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label32.Dpi = 254F;
            this.label32.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[LOOP_NO]")});
            this.label32.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label32.LocationFloat = new DevExpress.Utils.PointFloat(740.144F, 0F);
            this.label32.Multiline = true;
            this.label32.Name = "label32";
            this.label32.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label32.SizeF = new System.Drawing.SizeF(150.2252F, 58.41999F);
            this.label32.StylePriority.UseBorders = false;
            this.label32.StylePriority.UseFont = false;
            this.label32.StylePriority.UseTextAlignment = false;
            this.label32.Text = "label32";
            this.label32.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label31
            // 
            this.label31.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label31.Dpi = 254F;
            this.label31.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[PACK_BARCODE]")});
            this.label31.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label31.LocationFloat = new DevExpress.Utils.PointFloat(554.3969F, 0F);
            this.label31.Multiline = true;
            this.label31.Name = "label31";
            this.label31.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label31.SizeF = new System.Drawing.SizeF(185.7411F, 58.41999F);
            this.label31.StylePriority.UseBorders = false;
            this.label31.StylePriority.UseFont = false;
            this.label31.StylePriority.UseTextAlignment = false;
            this.label31.Text = "label31";
            this.label31.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label4
            // 
            this.label4.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label4.Dpi = 254F;
            this.label4.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[PACK_NAME]")});
            this.label4.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label4.LocationFloat = new DevExpress.Utils.PointFloat(70.94705F, 0F);
            this.label4.Multiline = true;
            this.label4.Name = "label4";
            this.label4.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label4.SizeF = new System.Drawing.SizeF(294.434F, 58.41999F);
            this.label4.StylePriority.UseBorders = false;
            this.label4.StylePriority.UseFont = false;
            this.label4.StylePriority.UseTextAlignment = false;
            this.label4.Text = "label4";
            this.label4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label27
            // 
            this.label27.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label27.Dpi = 254F;
            this.label27.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "sumCount()")});
            this.label27.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.label27.LocationFloat = new DevExpress.Utils.PointFloat(573.6844F, 14.24681F);
            this.label27.Multiline = true;
            this.label27.Name = "label27";
            this.label27.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label27.SizeF = new System.Drawing.SizeF(171.9792F, 57.76738F);
            this.label27.StylePriority.UseBorders = false;
            this.label27.StylePriority.UseFont = false;
            this.label27.StylePriority.UseTextAlignment = false;
            summary1.Running = DevExpress.XtraReports.UI.SummaryRunning.Group;
            this.label27.Summary = summary1;
            this.label27.Text = "小计";
            this.label27.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label3
            // 
            this.label3.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label3.Dpi = 254F;
            this.label3.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.label3.LocationFloat = new DevExpress.Utils.PointFloat(461.2365F, 14.24681F);
            this.label3.Multiline = true;
            this.label3.Name = "label3";
            this.label3.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label3.SizeF = new System.Drawing.SizeF(112.4479F, 57.76738F);
            this.label3.StylePriority.UseBorders = false;
            this.label3.StylePriority.UseFont = false;
            this.label3.StylePriority.UseTextAlignment = false;
            this.label3.Text = "小计:";
            this.label3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // label26
            // 
            this.label26.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label26.Dpi = 254F;
            this.label26.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[PACK_AMOUNT_DEPT]")});
            this.label26.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label26.LocationFloat = new DevExpress.Utils.PointFloat(922.8823F, 287.937F);
            this.label26.Multiline = true;
            this.label26.Name = "label26";
            this.label26.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label26.SizeF = new System.Drawing.SizeF(140.027F, 58.42001F);
            this.label26.StylePriority.UseBorders = false;
            this.label26.StylePriority.UseFont = false;
            this.label26.StylePriority.UseTextAlignment = false;
            this.label26.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label25
            // 
            this.label25.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label25.Dpi = 254F;
            this.label25.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label25.LocationFloat = new DevExpress.Utils.PointFloat(757.0534F, 287.9372F);
            this.label25.Multiline = true;
            this.label25.Name = "label25";
            this.label25.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label25.SizeF = new System.Drawing.SizeF(165.8289F, 58.42001F);
            this.label25.StylePriority.UseBorders = false;
            this.label25.StylePriority.UseFont = false;
            this.label25.StylePriority.UseTextAlignment = false;
            this.label25.Text = "回收数量：";
            this.label25.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label2
            // 
            this.label2.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label2.Dpi = 254F;
            this.label2.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[RECYCLE_NURSE]")});
            this.label2.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label2.LocationFloat = new DevExpress.Utils.PointFloat(569.8008F, 287.9372F);
            this.label2.Multiline = true;
            this.label2.Name = "label2";
            this.label2.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label2.SizeF = new System.Drawing.SizeF(187.2527F, 58.42F);
            this.label2.StylePriority.UseBorders = false;
            this.label2.StylePriority.UseFont = false;
            this.label2.StylePriority.UseTextAlignment = false;
            this.label2.Text = "label2";
            this.label2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label8
            // 
            this.label8.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label8.Dpi = 254F;
            this.label8.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label8.LocationFloat = new DevExpress.Utils.PointFloat(451.1975F, 287.9372F);
            this.label8.Multiline = true;
            this.label8.Name = "label8";
            this.label8.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label8.SizeF = new System.Drawing.SizeF(118.6033F, 58.41998F);
            this.label8.StylePriority.UseBorders = false;
            this.label8.StylePriority.UseFont = false;
            this.label8.StylePriority.UseTextAlignment = false;
            this.label8.Text = "回收人:";
            this.label8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label16
            // 
            this.label16.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label16.Dpi = 254F;
            this.label16.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[APPOINT_NURSE]")});
            this.label16.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label16.LocationFloat = new DevExpress.Utils.PointFloat(263.945F, 287.9371F);
            this.label16.Multiline = true;
            this.label16.Name = "label16";
            this.label16.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label16.SizeF = new System.Drawing.SizeF(187.2525F, 58.42F);
            this.label16.StylePriority.UseBorders = false;
            this.label16.StylePriority.UseFont = false;
            this.label16.StylePriority.UseTextAlignment = false;
            this.label16.Text = "label2";
            this.label16.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label15
            // 
            this.label15.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label15.Dpi = 254F;
            this.label15.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label15.LocationFloat = new DevExpress.Utils.PointFloat(62.6771F, 287.9371F);
            this.label15.Multiline = true;
            this.label15.Name = "label15";
            this.label15.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label15.SizeF = new System.Drawing.SizeF(201.2679F, 58.42004F);
            this.label15.StylePriority.UseBorders = false;
            this.label15.StylePriority.UseFont = false;
            this.label15.StylePriority.UseTextAlignment = false;
            this.label15.Text = "回收申请人:";
            this.label15.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label17
            // 
            this.label17.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label17.Dpi = 254F;
            this.label17.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label17.LocationFloat = new DevExpress.Utils.PointFloat(365.381F, 366.6771F);
            this.label17.Multiline = true;
            this.label17.Name = "label17";
            this.label17.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label17.SizeF = new System.Drawing.SizeF(193.3285F, 58.42001F);
            this.label17.StylePriority.UseBorders = false;
            this.label17.StylePriority.UseFont = false;
            this.label17.StylePriority.UseTextAlignment = false;
            this.label17.Text = "消毒包编码";
            this.label17.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label1
            // 
            this.label1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label1.Dpi = 254F;
            this.label1.Font = new System.Drawing.Font("Times New Roman", 9.75F, System.Drawing.FontStyle.Bold);
            this.label1.LocationFloat = new DevExpress.Utils.PointFloat(0F, 71.43749F);
            this.label1.Name = "label1";
            this.label1.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label1.SizeF = new System.Drawing.SizeF(1200F, 58.42F);
            this.label1.StylePriority.UseBorders = false;
            this.label1.StylePriority.UseFont = false;
            this.label1.StylePriority.UseTextAlignment = false;
            this.label1.Text = "消毒包回收任务单";
            this.label1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label6
            // 
            this.label6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label6.Dpi = 254F;
            this.label6.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[POSITION]")});
            this.label6.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label6.LocationFloat = new DevExpress.Utils.PointFloat(747.699F, 229.517F);
            this.label6.Multiline = true;
            this.label6.Name = "label6";
            this.label6.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label6.SizeF = new System.Drawing.SizeF(405.0241F, 58.41998F);
            this.label6.StylePriority.UseBorders = false;
            this.label6.StylePriority.UseFont = false;
            this.label6.StylePriority.UseTextAlignment = false;
            this.label6.Text = "label6";
            this.label6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label12
            // 
            this.label12.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label12.Dpi = 254F;
            this.label12.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label12.LocationFloat = new DevExpress.Utils.PointFloat(596.1268F, 229.517F);
            this.label12.Multiline = true;
            this.label12.Name = "label12";
            this.label12.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label12.SizeF = new System.Drawing.SizeF(151.5722F, 58.42001F);
            this.label12.StylePriority.UseBorders = false;
            this.label12.StylePriority.UseFont = false;
            this.label12.StylePriority.UseTextAlignment = false;
            this.label12.Text = "回收位置:";
            this.label12.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label9
            // 
            this.label9.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label9.Dpi = 254F;
            this.label9.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[DEPT_NAME]")});
            this.label9.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label9.LocationFloat = new DevExpress.Utils.PointFloat(214.2493F, 229.5172F);
            this.label9.Multiline = true;
            this.label9.Name = "label9";
            this.label9.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label9.SizeF = new System.Drawing.SizeF(381.8775F, 58.42F);
            this.label9.StylePriority.UseBorders = false;
            this.label9.StylePriority.UseFont = false;
            this.label9.StylePriority.UseTextAlignment = false;
            this.label9.Text = "label9";
            this.label9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label7
            // 
            this.label7.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label7.Dpi = 254F;
            this.label7.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label7.LocationFloat = new DevExpress.Utils.PointFloat(62.6771F, 229.5172F);
            this.label7.Multiline = true;
            this.label7.Name = "label7";
            this.label7.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label7.SizeF = new System.Drawing.SizeF(151.5722F, 58.41998F);
            this.label7.StylePriority.UseBorders = false;
            this.label7.StylePriority.UseFont = false;
            this.label7.StylePriority.UseTextAlignment = false;
            this.label7.Text = "回收科室:";
            this.label7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label24
            // 
            this.label24.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label24.Dpi = 254F;
            this.label24.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label24.LocationFloat = new DevExpress.Utils.PointFloat(894.6816F, 366.6771F);
            this.label24.Multiline = true;
            this.label24.Name = "label24";
            this.label24.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label24.SizeF = new System.Drawing.SizeF(183.2122F, 58.42001F);
            this.label24.StylePriority.UseBorders = false;
            this.label24.StylePriority.UseFont = false;
            this.label24.StylePriority.UseTextAlignment = false;
            this.label24.Text = "备注";
            this.label24.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label23
            // 
            this.label23.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label23.Dpi = 254F;
            this.label23.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label23.LocationFloat = new DevExpress.Utils.PointFloat(75.25953F, 366.6772F);
            this.label23.Multiline = true;
            this.label23.Name = "label23";
            this.label23.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label23.SizeF = new System.Drawing.SizeF(290.1215F, 58.42001F);
            this.label23.StylePriority.UseBorders = false;
            this.label23.StylePriority.UseFont = false;
            this.label23.StylePriority.UseTextAlignment = false;
            this.label23.Text = "消毒包名称";
            this.label23.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label22
            // 
            this.label22.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label22.Dpi = 254F;
            this.label22.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label22.LocationFloat = new DevExpress.Utils.PointFloat(744.4564F, 366.6772F);
            this.label22.Multiline = true;
            this.label22.Name = "label22";
            this.label22.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label22.SizeF = new System.Drawing.SizeF(150.2253F, 58.42001F);
            this.label22.StylePriority.UseBorders = false;
            this.label22.StylePriority.UseFont = false;
            this.label22.StylePriority.UseTextAlignment = false;
            this.label22.Text = "循环次数";
            this.label22.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label19
            // 
            this.label19.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label19.Dpi = 254F;
            this.label19.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label19.LocationFloat = new DevExpress.Utils.PointFloat(558.7094F, 366.6771F);
            this.label19.Multiline = true;
            this.label19.Name = "label19";
            this.label19.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label19.SizeF = new System.Drawing.SizeF(185.7412F, 58.41998F);
            this.label19.StylePriority.UseBorders = false;
            this.label19.StylePriority.UseFont = false;
            this.label19.StylePriority.UseTextAlignment = false;
            this.label19.Text = "消毒包条码";
            this.label19.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label14
            // 
            this.label14.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label14.Dpi = 254F;
            this.label14.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[APPOINT_NO]")});
            this.label14.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label14.LocationFloat = new DevExpress.Utils.PointFloat(781.8678F, 171.0972F);
            this.label14.Multiline = true;
            this.label14.Name = "label14";
            this.label14.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label14.SizeF = new System.Drawing.SizeF(370.8553F, 58.42F);
            this.label14.StylePriority.UseBorders = false;
            this.label14.StylePriority.UseFont = false;
            this.label14.StylePriority.UseTextAlignment = false;
            this.label14.Text = "label6";
            this.label14.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label13
            // 
            this.label13.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label13.Dpi = 254F;
            this.label13.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label13.LocationFloat = new DevExpress.Utils.PointFloat(596.1268F, 171.0972F);
            this.label13.Multiline = true;
            this.label13.Name = "label13";
            this.label13.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label13.SizeF = new System.Drawing.SizeF(185.741F, 58.41998F);
            this.label13.StylePriority.UseBorders = false;
            this.label13.StylePriority.UseFont = false;
            this.label13.StylePriority.UseTextAlignment = false;
            this.label13.Text = "回收申请号 :";
            this.label13.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label11
            // 
            this.label11.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label11.Dpi = 254F;
            this.label11.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[RECYCLE_SERIAL_NO]")});
            this.label11.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label11.LocationFloat = new DevExpress.Utils.PointFloat(214.2493F, 171.0972F);
            this.label11.Multiline = true;
            this.label11.Name = "label11";
            this.label11.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label11.SizeF = new System.Drawing.SizeF(381.8775F, 58.41998F);
            this.label11.StylePriority.UseBorders = false;
            this.label11.StylePriority.UseFont = false;
            this.label11.StylePriority.UseTextAlignment = false;
            this.label11.Text = "label9";
            this.label11.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label10
            // 
            this.label10.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label10.Dpi = 254F;
            this.label10.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.label10.LocationFloat = new DevExpress.Utils.PointFloat(62.6771F, 171.0972F);
            this.label10.Multiline = true;
            this.label10.Name = "label10";
            this.label10.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label10.SizeF = new System.Drawing.SizeF(151.5722F, 58.41998F);
            this.label10.StylePriority.UseBorders = false;
            this.label10.StylePriority.UseFont = false;
            this.label10.StylePriority.UseTextAlignment = false;
            this.label10.Text = "回收批次:";
            this.label10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label20
            // 
            this.label20.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label20.Dpi = 254F;
            this.label20.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.label20.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.label20.Multiline = true;
            this.label20.Name = "label20";
            this.label20.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label20.SizeF = new System.Drawing.SizeF(1198.677F, 54.23957F);
            this.label20.StylePriority.UseBorders = false;
            this.label20.StylePriority.UseFont = false;
            this.label20.StylePriority.UseTextAlignment = false;
            this.label20.Text = "鹤壁煤业（集团）有限责任公司总医院";
            this.label20.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // line4
            // 
            this.line4.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.line4.Dpi = 254F;
            this.line4.LocationFloat = new DevExpress.Utils.PointFloat(0F, 24.99993F);
            this.line4.Name = "line4";
            this.line4.SizeF = new System.Drawing.SizeF(1189.013F, 16.96865F);
            this.line4.StylePriority.UseBorders = false;
            // 
            // XtraReport
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
                        this.TopMargin,
                        this.Detail,
                        this.BottomMargin,
                        this.GroupHeader1,
                        this.GroupFooter1,
                        this.GroupHeader2,
                        this.GroupFooter2});
            this.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.DataSourceSchema = resources.GetString("$this.DataSourceSchema");
            this.Dpi = 254F;
            this.Margins = new System.Drawing.Printing.Margins(0, 0, 16, 7);
            this.Name = "XtraReport";
            this.PageHeight = 1200;
            this.PageWidth = 1200;
            this.PaperKind = System.Drawing.Printing.PaperKind.Custom;
            this.ReportUnit = DevExpress.XtraReports.UI.ReportUnit.TenthsOfAMillimeter;
            this.RollPaper = true;
            this.ShowPrintMarginsWarning = false;
            this.SnapGridSize = 31.75F;
            this.Tag = resources.GetString("$this.Tag");
            this.Version = "18.1";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();
        }
    }
}
