﻿/*-----------------------------------------------------------------------
 * 名称      ：表复制
 * 功能      ：赋值表结构和数据和行状态
 * 创建人    ：梁吉
 * 创建时间  ：2016-05-12
 * ----------------------------------------------------------------------
 */
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using PlatCommon.SysBase;

namespace PlatCommon.Comm
{
    public class Utility
    {
        #region API函数声明

        [DllImport("kernel32")]//返回0表示失败，非0为成功
        private static extern long WritePrivateProfileString(string section, string key,
            string val, string filePath);

        [DllImport("kernel32")]//返回取得字符串缓冲区的长度
        private static extern long GetPrivateProfileString(string section, string key,
            string def, StringBuilder retVal, int size, string filePath);


        #endregion
        static public DataTable CopyFrom(DataTable source)
        {
            DataTable ret = source.Clone();
            ret.PrimaryKey = new DataColumn[] { ret.Columns["APPLICATION_CODE"], ret.Columns["RIGHT_GROUP_CODE"] };
            DataRow drow;
            for (int k = 0; k < source.Rows.Count; k++)
            {
                drow = ret.NewRow();
                source.Rows[k].CopyValueTo(drow);
                DataRowState drs = source.Rows[k].RowState;
                
                ret.Rows.Add(drow);
                if (drs == DataRowState.Unchanged)
                {
                    drow.AcceptChanges();
                }
                else if (drs == DataRowState.Modified)
                {
                    drow.AcceptChanges();
                    drow.SetModified();
                }
                else if (drs == DataRowState.Deleted)
                {
                    drow.AcceptChanges();
                    drow.Delete();
                }
            }
            return ret;
        }
        /// <summary>
        /// 获取xml节点属性
        /// </summary>
        /// <param name="section"></param>
        /// <param name="attribute"></param>
        /// <returns></returns>
        static public string GetXmlAttr(string filepath,string section,string sectionname,string attribute)
        {
            System.Xml.XmlDocument doc = new System.Xml.XmlDocument();
            //获得配置文件的全路径
            string strFileName = filepath;
            doc.Load(strFileName);
            string returnvalue="";
            System.Xml.XmlElement element = doc.GetElementById(sectionname);
            if(element!=null)
                returnvalue = element.Value;
            //找出名称为“add”的所有元素
            System.Xml.XmlNodeList nodes = doc.GetElementsByTagName(section);
            for (int i = 0; i < nodes.Count; i++)
            {
                //获得将当前元素的key属性
                System.Xml.XmlAttribute att = nodes[i].Attributes["name"];
                //根据元素的第一个属性来判断当前的元素是不是目标元素
                if (att.Value == sectionname)
                {
                    //对目标元素中的第二个属性赋值
                    att = nodes[i].Attributes[attribute];
                    returnvalue = att.Value;
                    break;
                }
            }
            return returnvalue;
        }

        #region 读Ini文件

        public static string ReadIniData(string Section, string Key, string NoText, string iniFilePath)
        {
            if (File.Exists(iniFilePath))
            {
                StringBuilder temp = new StringBuilder(1024);
                GetPrivateProfileString(Section, Key, NoText, temp, 1024, iniFilePath);
                return temp.ToString();
            }
            else
            {
                return String.Empty;
            }
        }

        #endregion

        #region 写Ini文件

        public static bool WriteIniData(string Section, string Key, string Value, string iniFilePath)
        {
            if (File.Exists(iniFilePath))
            {
                long OpStation = WritePrivateProfileString(Section, Key, Value, iniFilePath);
                if (OpStation == 0)
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
            else
            {
                return false;
            }
        }

        #endregion
    }
}
