﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using NM_Service.NMService;
using DevExpress.XtraEditors;

namespace PlatCommon.Comm
{
    public class class_obilling_public
    {

        //long il_curr_no;
        //long il_max_no;
        //long il_min_no;
        //int il_sequence_length;
        //string is_prefix;   //前缀
        //string is_suffix;  //后缀

        //描  述: 取得就诊序列号
        public static long f_get_visit_no()
        {
            //long ll_visit_no;
            //string sql = "Select VISIT_NO.NEXTVAL VISIT_NO  From DUAL";

            //DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];

            //ll_visit_no = long.Parse(dt.Rows[0]["VISIT_NO"].ToString());

            //return ll_visit_no;
            string visit_no = "-1";
            PlatCommon.Common.PublicFunction.GetSequeceFromAuto("就诊序列", PlatCommon.SysBase.SystemParm.HisUnitCode, ref visit_no);
            return Convert.ToInt32(visit_no);
        }

        #region
        // 函数名：	f_calc_charge_price
        //
        //	功能：
        //
        // 	计算一个收费项目对特定病人的应收费。调用时指定病人的费别，项目以及默认
        //		系数（一般由费别确定），该函数检查指定项目是否为特殊项目。如果是，则按
        //		特殊项目定义的系数或免费限额计算该项目的应收费价格；否则，按默认的收费
        //		系数计算该项目的应收费价格。在访问数据库时，本函数使用调用者指定的事务
        //		对象。如果访问数据库出错，本函数显示错误信息。
        // 参数：
        // 				db_object：transaction，输入，使用的数据库事务对象
        //					charge_type，string，输入，病人费别
        //					item_class，string，输入，项目类别
        //					item_code，string，输入，项目代码
        //					item_spec，string，输入，项目规格
        //					price，decimal，输入，项目单价
        //					default_factor，decimal，输入，默认收费系数，在非特殊项目情况下，
        //								按此系数收费
        //					charge_price，decimal，输出，考虑特殊项目因素后计算得到的应收价格（单价）
        //
        // 返回值：
        //					0-非特殊项目
        //					1-特殊项目，按比例计算
        //					3-特殊项目，按限额计算
        //					2-特殊排斥项目，按比例计算
        //					4-特殊排斥项目，按限额计算
        //					<0，计算出错，返回数据库错误码
        #endregion
        public static int f_his21_calc_charge_price_outp(string charge_type, string item_class, string item_code, string item_spec, decimal price, decimal default_factor, ref decimal charge_price, ref int proportion_numerator, ref int proportion_denominator, ref decimal personal_price)
        {
            int v_proportion_numerator = 0, v_proportion_denominator = 0, v_free_limit = 0;
            int ind_proportion_numerator, ind_proportion_denominator, ind_free_limit;
            int return_val = 0;
            //2024年6月4日16:34:46 孙萍 添加说明：
            //return_val: 0: 非特殊项目 ;1:特殊项目，按比例计算;2:特殊排斥项目，按比例计算;3:特殊项目，按限额计算;4:特殊排斥项目，按限额计算;

            string sql = "SELECT NUMERATOR_OUTP,DENOMINATOR_OUTP,FREE_LIMIT_OUTP	FROM charge_special_except_dict  WHERE charge_type='" + charge_type + "' AND ";
            sql = sql + " item_class='" + item_class + "' AND(item_code='" + item_code + "' OR item_code='*') AND  (item_spec='" + item_spec + "' OR item_spec='*') AND HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";

            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            if (dt.Rows.Count > 0) ////存在排斥项目
            {
                return_val = 2;
                v_proportion_numerator = int.Parse(dt.Rows[0]["NUMERATOR_OUTP"].ToString());
                ind_proportion_numerator = int.Parse(dt.Rows[0]["NUMERATOR_OUTP"].ToString());
                v_proportion_denominator = int.Parse(dt.Rows[0]["DENOMINATOR_OUTP"].ToString());
                ind_proportion_denominator = int.Parse(dt.Rows[0]["DENOMINATOR_OUTP"].ToString());
                v_free_limit = int.Parse(dt.Rows[0]["FREE_LIMIT_OUTP"].ToString());
                ind_free_limit = int.Parse(dt.Rows[0]["FREE_LIMIT_OUTP"].ToString());
                #region oldCode
                //if (ind_free_limit == -1 || v_free_limit == 0) //按比例
                //{
                //    if (ind_proportion_numerator == 0 && ind_proportion_denominator == 0)
                //    {
                //        charge_price = (price * v_proportion_numerator) / v_proportion_denominator;
                //    }
                //    else
                //    {
                //        charge_price = price;
                //    }
                //}
                //else    //按限额
                //{
                //    charge_price = price - v_free_limit;
                //    if (charge_price < 0) charge_price = 0;  //价格低于限额

                //    return_val = 4;
                //}
                #endregion
                // 2024年6月4日16:13:00 孙萍调整 有限额时，按照：1.单价-（单价*分子/分母）>限额 返回 单价-限额 (意思是优惠价格超出限额，则按限额优惠)；2.单价-（单价*分子/分母）<限额 返回 单价*分子/分母（意思是优惠价格未超限额，则按比例优惠）；无限额时，按照：1.无限额，分母不为0=单价*分子/分母
                if (!string.IsNullOrEmpty(v_free_limit.ToString()) && v_free_limit > 0)
                {
                    decimal sumTemp1 = price - price * v_proportion_numerator / v_proportion_denominator;

                    if (sumTemp1 > v_free_limit)
                    {
                        charge_price = price - v_free_limit;
                    }
                    else
                    {
                        charge_price = price * v_proportion_numerator / v_proportion_denominator;
                    }
                    return_val = 4;
                }
                else    //无限额 分母不为0=单价*分子/分母
                {
                    if (!string.IsNullOrEmpty(v_proportion_numerator.ToString()) && !string.IsNullOrEmpty(v_proportion_denominator.ToString()) && v_proportion_denominator != 0)
                    {
                        charge_price = price * v_proportion_numerator / v_proportion_denominator;
                    }
                }
            }
            else //不存在排斥项目
            {
                sql = "SELECT NUMERATOR_OUTP,DENOMINATOR_OUTP,FREE_LIMIT_OUTP FROM charge_special_item_dict WHERE charge_type='" + charge_type + "' ";
                sql = sql + "AND item_class='" + item_class + "' AND (item_code='" + item_code + "' OR item_code='*') AND (item_spec='" + item_spec + "' OR item_spec='*')  AND HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
                DataTable dt1 = new ServerPublicClient().GetList(sql).Tables[0];


                if (dt1.Rows.Count > 0)
                {

                    v_proportion_numerator = int.Parse(dt1.Rows[0]["NUMERATOR_OUTP"].ToString());
                    ind_proportion_numerator = int.Parse(dt1.Rows[0]["NUMERATOR_OUTP"].ToString());
                    v_proportion_denominator = int.Parse(dt1.Rows[0]["DENOMINATOR_OUTP"].ToString());
                    ind_proportion_denominator = int.Parse(dt1.Rows[0]["DENOMINATOR_OUTP"].ToString());
                    v_free_limit = int.Parse(dt1.Rows[0]["FREE_LIMIT_OUTP"].ToString());
                    ind_free_limit = int.Parse(dt1.Rows[0]["FREE_LIMIT_OUTP"].ToString());

                    return_val = 1;
                    #region oldCode
                    //if (ind_free_limit == -1 || v_free_limit == 0) //按比例
                    //{
                    //    if (ind_proportion_numerator == 0 && ind_proportion_denominator == 0)
                    //    {
                    //        charge_price = (price * v_proportion_numerator) / v_proportion_denominator;
                    //    }
                    //    else
                    //    {
                    //        charge_price = price;
                    //    }
                    //}
                    //else    //按限额
                    //{
                    //    charge_price = price - v_free_limit;
                    //    if (charge_price < 0) charge_price = 0;  //价格低于限额

                    //    return_val = 4;
                    //}
                    #endregion

                    // 2024年6月4日16:13:00 孙萍调整 有限额时，按照：1.单价-（单价*分子/分母）>限额 返回 单价-限额 (意思是优惠价格超出限额，则按限额优惠)；2.单价-（单价*分子/分母）<限额 返回 单价*分子/分母（意思是优惠价格未超限额，则按比例优惠）；无限额时，按照：1.无限额，分母不为0=单价*分子/分母
                    if (!string.IsNullOrEmpty(ind_free_limit.ToString()) && v_free_limit > 0)
                    {
                        decimal sum1 = price * v_proportion_numerator / v_proportion_denominator;
                        decimal sumTemp1 = price - sum1;
                        if (sumTemp1 > v_free_limit)
                        {
                            charge_price = price - v_free_limit;
                        }
                        else
                        {
                            charge_price = price * v_proportion_numerator / v_proportion_denominator;
                        }
                        return_val = 3;
                    }
                    else    //无限额 分母不为0=单价*分子/分母
                    {
                        if (!string.IsNullOrEmpty(v_proportion_numerator.ToString()) && !string.IsNullOrEmpty(v_proportion_denominator.ToString()) && v_proportion_denominator != 0)
                        {
                            charge_price = price * v_proportion_numerator / v_proportion_denominator;
                        }
                    }
                }
                else    //不存在特殊项目
                {
                    return_val = 0;
                    charge_price = price * default_factor;
                }
            }
            proportion_numerator = v_proportion_numerator;
            proportion_denominator = v_proportion_denominator;
            personal_price = price - v_free_limit;

            return return_val;
        }
        //通用获取科室信息做关联用
        public static DataTable GetDeptName() 
        {
            string sql = "select dept_code DEPT_CODE,dept_name DEPT_NAME  from dept_dict where his_unit_code='"+PlatCommon.SysBase.SystemParm.HisUnitCode+"' ";
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];


            return dt;
        }


        // 函数名：	f_check_official_catalog
        //
        //	功能：
        //
        // 	判断给定项目是否在公费目录中，如果是，给出限定的提示信息
        //
        // 参数：
        // 				db_object：transaction，输入，使用的数据库事务对象
        //					charge_type，string，输入，病人费别
        //					item_class，string，输入，项目类别
        //					item_code，string，输入，项目代码
        //					item_spec，string，输入，项目规格
        //					typex，string，输入，类别，inp代表住院，outp代表门诊，其它不区分。	//2005-10-09		龚丽炜	增加
        //					msg，string，输出，目录提示信息

        // 返回值：
        //					0-公费目录，无限制（含NULL）
        //					1-公费目录，部分公费
        //					2-公费目录，限适应症
        //					3-非公费范围
        //					<0，出错，返回数据库错误码

        public static int f_his21_check_official_catalog(string charge_type, string item_class, string item_code, string item_spec, string typex, ref string msg)
        {
            int return_val=0;
            string  ls_memo,v_constrained_level;
            if (!typex.Equals("inp") && !typex.Equals("outp")) 
            {
                typex = "*";
            }
            string sql = "SELECT constrained_level,memo FROM official_drug_catalog WHERE charge_type='" + charge_type + "' AND ";
            sql = sql + "  drug_code='" + item_code + "' AND (drug_spec='" + item_spec + "' OR drug_spec='*') AND (typex = '" + typex + "' or typex = '*')";
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            if (dt.Rows.Count > 0) //公费类型在数据表中取
            {
                ls_memo = dt.Rows[0]["MEMO"].ToString();
                v_constrained_level = dt.Rows[0]["CONSTRAINED_LEVEL"].ToString();
                if (string.IsNullOrEmpty(v_constrained_level))
                {
                    return_val = 0;
                    msg = "范围内药品";
                }
                else
                {
                    sql = "SELECT class_name  FROM official_drug_catalog_class 	WHERE class_code ='" + v_constrained_level + "' ";
                    DataTable dt1 = new ServerPublicClient().GetList(sql).Tables[0];
                    if (dt1.Rows.Count <= 0)
                    {
                        XtraMessageBox.Show("提示", "取公费类型表出错！");
                        return_val = -1;
                    }
                    else
                    {
                        msg = msg + "  " + ls_memo;
                        return_val = Int32.Parse(v_constrained_level);
                    }
                }
            }
            else  //原来pb的代码还重新查下上面的语句觉得没有意义直接不要了
            {
			       return_val = 99;
                   msg = "自费";
            }


            return return_val;
        }

        // 函数名：	f_calc_charge_price
        //
        //	功能：
        //
        // 	计算一个收费项目对特定病人的应收费。调用时指定病人的费别，项目以及默认
        //		系数（一般由费别确定），该函数检查指定项目是否为特殊项目。如果是，则按
        //		特殊项目定义的系数或免费限额计算该项目的应收费价格；否则，按默认的收费
        //		系数计算该项目的应收费价格。在访问数据库时，本函数使用调用者指定的事务
        //		对象。如果访问数据库出错，本函数显示错误信息。
        //
        // 参数：
        // 				db_object：transaction，输入，使用的数据库事务对象
        //					charge_type，string，输入，病人费别
        //					item_class，string，输入，项目类别
        //					item_code，string，输入，项目代码
        //					item_spec，string，输入，项目规格
        //					price，decimal，输入，项目单价
        //					default_factor，decimal，输入，默认收费系数，在非特殊项目情况下，
        //								按此系数收费
        //					charge_price，decimal，输出，考虑特殊项目因素后计算得到的应收价格（单价）
        //
        // 返回值：
        //					0-非特殊项目，按默认系数计算
        //					1-特殊项目，按特殊项目计算
        //					2-特殊排斥项目，按特殊排斥项目计算
        //					<0，计算出错，返回数据库错误码
        //
        public static int f_his21_calc_charge_price(string charge_type, string item_class, string item_code, string item_spec, decimal price, decimal default_factor, ref decimal charge_price)
        {
            int v_proportion_numerator = 0, v_proportion_denominator = 0;
            int ind_proportion_numerator, ind_proportion_denominator, ind_free_limit;
            int return_val = 0;
            decimal calc_limit, v_free_limit;
            try
            {
                string sql = "select charge_special_indicator from CHARGE_PRICE_SCHEDULE where charge_type='" + charge_type + "' AND HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
                DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
                if (dt.Rows.Count < 1 || dt.Rows[0]["charge_special_indicator"] == null || Convert.ToInt32(dt.Rows[0]["charge_special_indicator"]) != 1)
                {
                    charge_price = price * default_factor;
                    return 0;
                }

                sql = "SELECT proportion_numerator,proportion_denominator,free_limit FROM charge_special_except_dict WHERE charge_type='" + charge_type + "' AND	item_class='" + item_class + "' AND	(item_code='" + item_code + "' OR item_code='*') AND		(item_spec='" + item_spec + "' OR item_spec='*') AND HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
                dt = new ServerPublicClient().GetList(sql).Tables[0];
                if (dt.Rows.Count > 0)
                {
                    return_val = 2;
                    v_proportion_numerator = int.Parse(dt.Rows[0]["PROPORTION_NUMERATOR"].ToString());
                    ind_proportion_numerator = int.Parse(dt.Rows[0]["PROPORTION_NUMERATOR"].ToString());
                    v_proportion_denominator = int.Parse(dt.Rows[0]["PROPORTION_DENOMINATOR"].ToString());
                    ind_proportion_denominator = int.Parse(dt.Rows[0]["PROPORTION_DENOMINATOR"].ToString());
                    v_free_limit = int.Parse(dt.Rows[0]["FREE_LIMIT"].ToString());
                    ind_free_limit = int.Parse(dt.Rows[0]["FREE_LIMIT"].ToString());

                    if (ind_free_limit == -1 || v_free_limit == 0) //按比例
                    {
                        if (ind_proportion_numerator == 0 && ind_proportion_denominator == 0)
                        {
                            charge_price = (price * v_proportion_numerator) / v_proportion_denominator;
                        }
                        else
                        {
                            charge_price = price;
                        }
                    }
                    else 	//按限额
                    {
                        charge_price = (price * v_proportion_numerator) / v_proportion_denominator;
                        calc_limit = price - charge_price;  //优惠的金额
                        if (calc_limit - v_free_limit > 0)
                        {
                            charge_price = price - v_free_limit;
                        }
                        if (charge_price < 0)
                        {
                            charge_price = 0;  //价格低于限额
                        }
                    }
                }
                else //不存在排斥项目
                {
                    sql = "SELECT proportion_numerator,proportion_denominator,free_limit FROM charge_special_item_dict WHERE charge_type='" + charge_type + "' ";
                    sql = sql + "AND item_class='" + item_class + "' AND (item_code='" + item_code + "' OR item_code='*') AND (item_spec='" + item_spec + "' OR item_spec='*') AND HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
                    DataTable dt1 = new ServerPublicClient().GetList(sql).Tables[0];
                    if (dt1.Rows.Count > 0)
                    {

                        v_proportion_numerator = int.Parse(dt1.Rows[0]["PROPORTION_NUMERATOR"].ToString());
                        ind_proportion_numerator = int.Parse(dt1.Rows[0]["PROPORTION_NUMERATOR"].ToString());
                        v_proportion_denominator = int.Parse(dt1.Rows[0]["PROPORTION_DENOMINATOR"].ToString());
                        ind_proportion_denominator = int.Parse(dt1.Rows[0]["PROPORTION_DENOMINATOR"].ToString());
                        v_free_limit = int.Parse(dt1.Rows[0]["FREE_LIMIT"].ToString());
                        ind_free_limit = int.Parse(dt1.Rows[0]["FREE_LIMIT"].ToString());

                        return_val = 1;

                        if (ind_free_limit == -1 || v_free_limit == 0) //按比例
                        {
                            if (ind_proportion_numerator == 0 && ind_proportion_denominator == 0)
                            {
                                charge_price = (price * v_proportion_numerator) / v_proportion_denominator;
                            }
                            else
                            {
                                charge_price = price;
                            }
                        }
                        else 	//按限额
                        {
                            charge_price = (price * v_proportion_numerator) / v_proportion_denominator;
                            calc_limit = price - charge_price;  //优惠的金额
                            if (calc_limit - v_free_limit > 0)
                            {
                                charge_price = price - v_free_limit;
                            }
                            if (charge_price < 0)
                            {
                                charge_price = 0;  //价格低于限额
                            }
                        }
                    }
                    else  	//不存在特殊项目
                    {
                        return_val = 0;
                        charge_price = price * default_factor;
                    }
                }
            }
            catch
            {
                return_val = -1;
            }

            return return_val;
        }
        /// <summary>
         /// 大小写转换
         /// </summary>    
        public static string DaXie(string money)
        {
            //将小写金额转换成大写金额
            double MyNumber = Convert.ToDouble(money);
            String[] MyScale = { "分", "角", "元", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿", "拾", "佰", "仟", "兆", "拾", "佰", "仟" };
            String[] MyBase = { "零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖" };
            String
             M = "";
            bool
             isPoint = false;
            if (money.IndexOf(".") != -1)
            {
                money =
                 money.Remove(money.IndexOf("."), 1);
                isPoint = true;
            }
            for (int i = money.Length; i > 0; i--)
            {
                int MyData = Convert.ToInt16(money[money.Length - i].ToString());
                M += MyBase[MyData];
                if (isPoint == true)
                {
                    M += MyScale[i - 1];
                }
                else
                {
                    M += MyScale[i + 1];
                }
            }
            return M;
        }


        /// <summary> 
        /// 转换人民币大小金额 
        /// </summary> 
        /// <param name="num">金额</param> 
        /// <returns>返回大写形式</returns> 
        public static string CmycurD(decimal num)
        {
            string str1 = "零壹贰叁肆伍陆柒捌玖";            //0-9所对应的汉字 
            string str2 = "万仟佰拾亿仟佰拾万仟佰拾元角分"; //数字位所对应的汉字 
            string str3 = "";    //从原num值中取出的值 
            string str4 = "";    //数字的字符串形式 
            string str5 = "";  //人民币大写金额形式 
            int i;    //循环变量 
            int j;    //num的值乘以100的字符串长度 
            string ch1 = "";    //数字的汉语读法 
            string ch2 = "";    //数字位的汉字读法 
            int nzero = 0;  //用来计算连续的零值是几个 
            int temp;            //从原num值中取出的值 

            num = Math.Round(Math.Abs(num), 2);    //将num取绝对值并四舍五入取2位小数 
            str4 = ((long)(num * 100)).ToString();        //将num乘100并转换成字符串形式 
            j = str4.Length;      //找出最高位 
            if (j > 15) { return "溢出"; }
            str2 = str2.Substring(15 - j);   //取出对应位数的str2的值。如：200.55,j为5所以str2=佰拾元角分 

            //循环取出每一位需要转换的值 
            for (i = 0; i < j; i++)
            {
                str3 = str4.Substring(i, 1);          //取出需转换的某一位的值 
                temp = Convert.ToInt32(str3);      //转换为数字 
                if (i != (j - 3) && i != (j - 7) && i != (j - 11) && i != (j - 15))
                {
                    //当所取位数不为元、万、亿、万亿上的数字时 
                    if (str3 == "0")
                    {
                        ch1 = "";
                        ch2 = "";
                        nzero = nzero + 1;
                    }
                    else
                    {
                        if (str3 != "0" && nzero != 0)
                        {
                            ch1 = "零" + str1.Substring(temp * 1, 1);
                            ch2 = str2.Substring(i, 1);
                            nzero = 0;
                        }
                        else
                        {
                            ch1 = str1.Substring(temp * 1, 1);
                            ch2 = str2.Substring(i, 1);
                            nzero = 0;
                        }
                    }
                }
                else
                {
                    //该位是万亿，亿，万，元位等关键位 
                    if (str3 != "0" && nzero != 0)
                    {
                        ch1 = "零" + str1.Substring(temp * 1, 1);
                        ch2 = str2.Substring(i, 1);
                        nzero = 0;
                    }
                    else
                    {
                        if (str3 != "0" && nzero == 0)
                        {
                            ch1 = str1.Substring(temp * 1, 1);
                            ch2 = str2.Substring(i, 1);
                            nzero = 0;
                        }
                        else
                        {
                            if (str3 == "0" && nzero >= 3)
                            {
                                ch1 = "";
                                ch2 = "";
                                nzero = nzero + 1;
                            }
                            else
                            {
                                if (j >= 11)
                                {
                                    ch1 = "";
                                    nzero = nzero + 1;
                                }
                                else
                                {
                                    ch1 = "";
                                    ch2 = str2.Substring(i, 1);
                                    nzero = nzero + 1;
                                }
                            }
                        }
                    }
                }
                if (i == (j - 11) || i == (j - 3))
                {
                    //如果该位是亿位或元位，则必须写上 
                    ch2 = str2.Substring(i, 1);
                }
                str5 = str5 + ch1 + ch2;

                if (i == j - 1 && str3 == "0")
                {
                    //最后一位（分）为0时，加上“整” 
                    str5 = str5 + '整';
                }
            }
            if (num == 0)
            {
                str5 = "零元整";
            }
            return str5;
        }

        /**/
        /// <summary> 
        /// 一个重载，将字符串先转换成数字在调用CmycurD(decimal num) 
        /// </summary> 
        /// <param name="num">用户输入的金额，字符串形式未转成decimal</param> 
        /// <returns></returns> 
        public static string CmycurD(string numstr)
        {
            try
            {
                decimal num = Convert.ToDecimal(numstr);
                return CmycurD(num);
            }
            catch
            {
                return "非数字形式！";
            }
        }
    }
}
