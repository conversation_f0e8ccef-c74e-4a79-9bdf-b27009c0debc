﻿namespace Tjhis.Report.Custom.Custom
{
    partial class frmStatisticalSetting2
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmStatisticalSetting2));
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.treeTempleList = new DevExpress.XtraTreeList.TreeList();
            this.treeListColumn1 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            this.imageList1 = new System.Windows.Forms.ImageList(this.components);
            this.splitContainer2 = new System.Windows.Forms.SplitContainer();
            this.groupReportParam = new DevExpress.XtraEditors.GroupControl();
            this.gcReportParam = new DevExpress.XtraGrid.GridControl();
            this.dgvReportParam = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gc_SerialNo = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_PARAM_NAME = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_EDIT_TYPE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.lueEditType = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.gc_CAPTION = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_DISPLAY_MEMBER = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_VALUE_MEMBER = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_SOURCE_TYPE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.lueSourceType = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.gc_NEXT_PARAM_NAME = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_DEFAULT_VALUE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_CONTROL_WIDTH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_DATE_FORMAT = new DevExpress.XtraGrid.Columns.GridColumn();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.txtSql = new DevExpress.XtraEditors.MemoEdit();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.txt_memo = new DevExpress.XtraEditors.TextEdit();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barButtonMainten = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barButtonParaDict = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barButtonNew = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barButtonCopy = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barButtonChoosePara = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barButtonNewPara = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barButtonEditPara = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barButtonDeleteParam = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barButtonTest = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barButtonBinding = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barButtonPrintDesignSpecial = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barButtonItem_importRepx = new DevExpress.XtraBars.BarLargeButtonItem();
            this.popupMenu_import = new DevExpress.XtraBars.PopupMenu(this.components);
            this.barSubItem_ImportDisplay = new DevExpress.XtraBars.BarSubItem();
            this.barSubItem_importSpecialPrint = new DevExpress.XtraBars.BarSubItem();
            this.barLargeButton_export = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barButtonPreview = new DevExpress.XtraBars.BarLargeButtonItem();
            this.popupMenu_Preview = new DevExpress.XtraBars.PopupMenu(this.components);
            this.barSubItem_display = new DevExpress.XtraBars.BarSubItem();
            this.barSubItem_specialPrint = new DevExpress.XtraBars.BarSubItem();
            this.barButtonSave = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barButtonDelete = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barButtonClose = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.panelReportRole = new System.Windows.Forms.Panel();
            this.groupReportRole = new DevExpress.XtraEditors.GroupControl();
            this.txtPageSettings = new DevExpress.XtraEditors.ButtonEdit();
            this.checkEdit_IsTable = new DevExpress.XtraEditors.CheckEdit();
            this.textEdit_ReportName = new DevExpress.XtraEditors.TextEdit();
            this.slueReportClass = new DevExpress.XtraEditors.SearchLookUpEdit();
            this.searchLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.APP_NAME = new DevExpress.XtraGrid.Columns.GridColumn();
            this.CLASS_NAME = new DevExpress.XtraGrid.Columns.GridColumn();
            this.CLASS_ID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.beRole = new DevExpress.XtraEditors.ButtonEdit();
            this.txtRole = new DevExpress.XtraEditors.ButtonEdit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeTempleList)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer2)).BeginInit();
            this.splitContainer2.Panel1.SuspendLayout();
            this.splitContainer2.Panel2.SuspendLayout();
            this.splitContainer2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupReportParam)).BeginInit();
            this.groupReportParam.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcReportParam)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvReportParam)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lueEditType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lueSourceType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtSql.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txt_memo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenu_import)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenu_Preview)).BeginInit();
            this.panelReportRole.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupReportRole)).BeginInit();
            this.groupReportRole.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtPageSettings.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEdit_IsTable.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit_ReportName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.slueReportClass.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.searchLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.beRole.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRole.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 64);
            this.splitContainer1.Name = "splitContainer1";
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.groupControl1);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.splitContainer2);
            this.splitContainer1.Panel2.Controls.Add(this.panelReportRole);
            this.splitContainer1.Size = new System.Drawing.Size(1122, 632);
            this.splitContainer1.SplitterDistance = 263;
            this.splitContainer1.TabIndex = 1;
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.treeTempleList);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(263, 632);
            this.groupControl1.TabIndex = 2;
            this.groupControl1.Text = "报表列表";
            // 
            // treeTempleList
            // 
            this.treeTempleList.Appearance.EvenRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(242)))), ((int)(((byte)(246)))), ((int)(((byte)(251)))));
            this.treeTempleList.Appearance.EvenRow.Options.UseBackColor = true;
            this.treeTempleList.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(199)))), ((int)(((byte)(212)))), ((int)(((byte)(255)))));
            this.treeTempleList.Appearance.SelectedRow.Options.UseBackColor = true;
            this.treeTempleList.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.treeListColumn1});
            this.treeTempleList.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeTempleList.Location = new System.Drawing.Point(2, 21);
            this.treeTempleList.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.treeTempleList.MinWidth = 17;
            this.treeTempleList.Name = "treeTempleList";
            this.treeTempleList.OptionsView.EnableAppearanceEvenRow = true;
            this.treeTempleList.OptionsView.FocusRectStyle = DevExpress.XtraTreeList.DrawFocusRectStyle.RowFocus;
            this.treeTempleList.RowHeight = 25;
            this.treeTempleList.SelectImageList = this.imageList1;
            this.treeTempleList.Size = new System.Drawing.Size(259, 609);
            this.treeTempleList.TabIndex = 0;
            this.treeTempleList.TreeLevelWidth = 16;
            this.treeTempleList.GetSelectImage += new DevExpress.XtraTreeList.GetSelectImageEventHandler(this.treeTempleList_GetSelectImage);
            this.treeTempleList.FocusedNodeChanged += new DevExpress.XtraTreeList.FocusedNodeChangedEventHandler(this.treeTempleList_FocusedNodeChanged);
            // 
            // treeListColumn1
            // 
            this.treeListColumn1.Caption = "treeListColumn1";
            this.treeListColumn1.FieldName = "DICT_NAME";
            this.treeListColumn1.MinWidth = 17;
            this.treeListColumn1.Name = "treeListColumn1";
            this.treeListColumn1.Visible = true;
            this.treeListColumn1.VisibleIndex = 0;
            this.treeListColumn1.Width = 66;
            // 
            // imageList1
            // 
            this.imageList1.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList1.ImageStream")));
            this.imageList1.TransparentColor = System.Drawing.Color.Transparent;
            this.imageList1.Images.SetKeyName(0, "文件夹.png");
            this.imageList1.Images.SetKeyName(1, "页面.png");
            this.imageList1.Images.SetKeyName(2, "文件夹_close.png");
            this.imageList1.Images.SetKeyName(3, "首页.png");
            // 
            // splitContainer2
            // 
            this.splitContainer2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer2.Location = new System.Drawing.Point(0, 60);
            this.splitContainer2.Name = "splitContainer2";
            this.splitContainer2.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer2.Panel1
            // 
            this.splitContainer2.Panel1.Controls.Add(this.groupReportParam);
            // 
            // splitContainer2.Panel2
            // 
            this.splitContainer2.Panel2.Controls.Add(this.groupControl4);
            this.splitContainer2.Panel2.Controls.Add(this.groupControl2);
            this.splitContainer2.Size = new System.Drawing.Size(855, 572);
            this.splitContainer2.SplitterDistance = 231;
            this.splitContainer2.TabIndex = 1;
            // 
            // groupReportParam
            // 
            this.groupReportParam.Controls.Add(this.gcReportParam);
            this.groupReportParam.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupReportParam.Location = new System.Drawing.Point(0, 0);
            this.groupReportParam.Name = "groupReportParam";
            this.groupReportParam.Size = new System.Drawing.Size(855, 231);
            this.groupReportParam.TabIndex = 0;
            this.groupReportParam.Text = "报表参数";
            // 
            // gcReportParam
            // 
            this.gcReportParam.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcReportParam.Location = new System.Drawing.Point(2, 21);
            this.gcReportParam.MainView = this.dgvReportParam;
            this.gcReportParam.Name = "gcReportParam";
            this.gcReportParam.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.lueEditType,
            this.lueSourceType});
            this.gcReportParam.Size = new System.Drawing.Size(851, 208);
            this.gcReportParam.TabIndex = 24;
            this.gcReportParam.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.dgvReportParam});
            // 
            // dgvReportParam
            // 
            this.dgvReportParam.ColumnPanelRowHeight = 0;
            this.dgvReportParam.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gc_SerialNo,
            this.gc_PARAM_NAME,
            this.gc_EDIT_TYPE,
            this.gc_CAPTION,
            this.gc_DISPLAY_MEMBER,
            this.gc_VALUE_MEMBER,
            this.gc_SOURCE_TYPE,
            this.gc_NEXT_PARAM_NAME,
            this.gc_DEFAULT_VALUE,
            this.gc_CONTROL_WIDTH,
            this.gc_DATE_FORMAT});
            this.dgvReportParam.FooterPanelHeight = 0;
            this.dgvReportParam.GridControl = this.gcReportParam;
            this.dgvReportParam.GroupRowHeight = 0;
            this.dgvReportParam.LevelIndent = 0;
            this.dgvReportParam.Name = "dgvReportParam";
            this.dgvReportParam.OptionsCustomization.AllowFilter = false;
            this.dgvReportParam.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CellSelect;
            this.dgvReportParam.OptionsView.ColumnAutoWidth = false;
            this.dgvReportParam.OptionsView.ShowGroupPanel = false;
            this.dgvReportParam.PreviewIndent = 0;
            this.dgvReportParam.RowHeight = 0;
            this.dgvReportParam.ViewCaptionHeight = 0;
            this.dgvReportParam.RowClick += new DevExpress.XtraGrid.Views.Grid.RowClickEventHandler(this.dgvReportParam_RowClick);
            // 
            // gc_SerialNo
            // 
            this.gc_SerialNo.Caption = "显示序号";
            this.gc_SerialNo.FieldName = "SERIAL_NO";
            this.gc_SerialNo.MinWidth = 60;
            this.gc_SerialNo.Name = "gc_SerialNo";
            this.gc_SerialNo.OptionsColumn.AllowEdit = false;
            this.gc_SerialNo.Visible = true;
            this.gc_SerialNo.VisibleIndex = 0;
            this.gc_SerialNo.Width = 60;
            // 
            // gc_PARAM_NAME
            // 
            this.gc_PARAM_NAME.Caption = "参数";
            this.gc_PARAM_NAME.FieldName = "PARAM_NAME";
            this.gc_PARAM_NAME.MinWidth = 76;
            this.gc_PARAM_NAME.Name = "gc_PARAM_NAME";
            this.gc_PARAM_NAME.OptionsColumn.ReadOnly = true;
            this.gc_PARAM_NAME.Visible = true;
            this.gc_PARAM_NAME.VisibleIndex = 1;
            this.gc_PARAM_NAME.Width = 103;
            // 
            // gc_EDIT_TYPE
            // 
            this.gc_EDIT_TYPE.Caption = "参数控件类型";
            this.gc_EDIT_TYPE.ColumnEdit = this.lueEditType;
            this.gc_EDIT_TYPE.FieldName = "EDIT_TYPE";
            this.gc_EDIT_TYPE.MinWidth = 100;
            this.gc_EDIT_TYPE.Name = "gc_EDIT_TYPE";
            this.gc_EDIT_TYPE.OptionsColumn.AllowEdit = false;
            this.gc_EDIT_TYPE.Visible = true;
            this.gc_EDIT_TYPE.VisibleIndex = 4;
            this.gc_EDIT_TYPE.Width = 100;
            // 
            // lueEditType
            // 
            this.lueEditType.AutoHeight = false;
            this.lueEditType.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_CODE", "编码"),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_NAME", "项目")});
            this.lueEditType.Name = "lueEditType";
            this.lueEditType.NullText = "";
            // 
            // gc_CAPTION
            // 
            this.gc_CAPTION.Caption = "标题名称";
            this.gc_CAPTION.FieldName = "CAPTION";
            this.gc_CAPTION.MinWidth = 120;
            this.gc_CAPTION.Name = "gc_CAPTION";
            this.gc_CAPTION.OptionsColumn.AllowEdit = false;
            this.gc_CAPTION.Visible = true;
            this.gc_CAPTION.VisibleIndex = 2;
            this.gc_CAPTION.Width = 120;
            // 
            // gc_DISPLAY_MEMBER
            // 
            this.gc_DISPLAY_MEMBER.Caption = "显示列";
            this.gc_DISPLAY_MEMBER.FieldName = "DISPLAY_MEMBER";
            this.gc_DISPLAY_MEMBER.MinWidth = 100;
            this.gc_DISPLAY_MEMBER.Name = "gc_DISPLAY_MEMBER";
            this.gc_DISPLAY_MEMBER.OptionsColumn.AllowEdit = false;
            this.gc_DISPLAY_MEMBER.Visible = true;
            this.gc_DISPLAY_MEMBER.VisibleIndex = 6;
            this.gc_DISPLAY_MEMBER.Width = 100;
            // 
            // gc_VALUE_MEMBER
            // 
            this.gc_VALUE_MEMBER.Caption = "数据列";
            this.gc_VALUE_MEMBER.FieldName = "VALUE_MEMBER";
            this.gc_VALUE_MEMBER.MinWidth = 100;
            this.gc_VALUE_MEMBER.Name = "gc_VALUE_MEMBER";
            this.gc_VALUE_MEMBER.OptionsColumn.AllowEdit = false;
            this.gc_VALUE_MEMBER.Visible = true;
            this.gc_VALUE_MEMBER.VisibleIndex = 7;
            this.gc_VALUE_MEMBER.Width = 100;
            // 
            // gc_SOURCE_TYPE
            // 
            this.gc_SOURCE_TYPE.Caption = "数据源类型";
            this.gc_SOURCE_TYPE.ColumnEdit = this.lueSourceType;
            this.gc_SOURCE_TYPE.FieldName = "SOURCE_TYPE";
            this.gc_SOURCE_TYPE.MinWidth = 100;
            this.gc_SOURCE_TYPE.Name = "gc_SOURCE_TYPE";
            this.gc_SOURCE_TYPE.OptionsColumn.AllowEdit = false;
            this.gc_SOURCE_TYPE.Visible = true;
            this.gc_SOURCE_TYPE.VisibleIndex = 8;
            this.gc_SOURCE_TYPE.Width = 100;
            // 
            // lueSourceType
            // 
            this.lueSourceType.AutoHeight = false;
            this.lueSourceType.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_CODE", "编码"),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_NAME", "项目")});
            this.lueSourceType.Name = "lueSourceType";
            this.lueSourceType.NullText = "";
            // 
            // gc_NEXT_PARAM_NAME
            // 
            this.gc_NEXT_PARAM_NAME.Caption = "联动参数";
            this.gc_NEXT_PARAM_NAME.FieldName = "NEXT_PARAM_NAME";
            this.gc_NEXT_PARAM_NAME.MinWidth = 120;
            this.gc_NEXT_PARAM_NAME.Name = "gc_NEXT_PARAM_NAME";
            this.gc_NEXT_PARAM_NAME.OptionsColumn.AllowEdit = false;
            this.gc_NEXT_PARAM_NAME.Visible = true;
            this.gc_NEXT_PARAM_NAME.VisibleIndex = 9;
            this.gc_NEXT_PARAM_NAME.Width = 120;
            // 
            // gc_DEFAULT_VALUE
            // 
            this.gc_DEFAULT_VALUE.Caption = "默认值";
            this.gc_DEFAULT_VALUE.FieldName = "DEFAULT_VALUE";
            this.gc_DEFAULT_VALUE.MinWidth = 80;
            this.gc_DEFAULT_VALUE.Name = "gc_DEFAULT_VALUE";
            this.gc_DEFAULT_VALUE.OptionsColumn.AllowEdit = false;
            this.gc_DEFAULT_VALUE.Visible = true;
            this.gc_DEFAULT_VALUE.VisibleIndex = 3;
            this.gc_DEFAULT_VALUE.Width = 80;
            // 
            // gc_CONTROL_WIDTH
            // 
            this.gc_CONTROL_WIDTH.Caption = "控件大小";
            this.gc_CONTROL_WIDTH.FieldName = "CONTROL_WIDTH";
            this.gc_CONTROL_WIDTH.MinWidth = 60;
            this.gc_CONTROL_WIDTH.Name = "gc_CONTROL_WIDTH";
            this.gc_CONTROL_WIDTH.OptionsColumn.AllowEdit = false;
            this.gc_CONTROL_WIDTH.Visible = true;
            this.gc_CONTROL_WIDTH.VisibleIndex = 5;
            this.gc_CONTROL_WIDTH.Width = 60;
            // 
            // gc_DATE_FORMAT
            // 
            this.gc_DATE_FORMAT.Caption = "日期格式";
            this.gc_DATE_FORMAT.FieldName = "DATE_FORMAT";
            this.gc_DATE_FORMAT.Name = "gc_DATE_FORMAT";
            this.gc_DATE_FORMAT.Visible = true;
            this.gc_DATE_FORMAT.VisibleIndex = 10;
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.txtSql);
            this.groupControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl4.Location = new System.Drawing.Point(0, 0);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(855, 279);
            this.groupControl4.TabIndex = 1;
            this.groupControl4.Text = "SQL脚本";
            // 
            // txtSql
            // 
            this.txtSql.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txtSql.Location = new System.Drawing.Point(2, 21);
            this.txtSql.Name = "txtSql";
            this.txtSql.Size = new System.Drawing.Size(851, 256);
            this.txtSql.TabIndex = 9;
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.txt_memo);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.groupControl2.Location = new System.Drawing.Point(0, 279);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(855, 58);
            this.groupControl2.TabIndex = 2;
            this.groupControl2.Text = "备注";
            // 
            // txt_memo
            // 
            this.txt_memo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txt_memo.Location = new System.Drawing.Point(2, 21);
            this.txt_memo.MenuManager = this.barManager1;
            this.txt_memo.Name = "txt_memo";
            this.txt_memo.Size = new System.Drawing.Size(851, 20);
            this.txt_memo.TabIndex = 28;
            // 
            // barManager1
            // 
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barButtonNew,
            this.barButtonCopy,
            this.barButtonChoosePara,
            this.barButtonNewPara,
            this.barButtonEditPara,
            this.barButtonBinding,
            this.barButtonTest,
            this.barButtonDelete,
            this.barButtonSave,
            this.barButtonMainten,
            this.barButtonParaDict,
            this.barButtonDeleteParam,
            this.barButtonClose,
            this.barButtonPrintDesignSpecial,
            this.barButtonPreview,
            this.barButtonItem_importRepx,
            this.barSubItem_display,
            this.barSubItem_specialPrint,
            this.barSubItem_ImportDisplay,
            this.barSubItem_importSpecialPrint,
            this.barLargeButton_export});
            this.barManager1.MaxItemId = 25;
            // 
            // bar1
            // 
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(57, 193);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonMainten, "", true, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonParaDict, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonNew, "", true, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonCopy, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonChoosePara, "", true, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonNewPara, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonEditPara, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonDeleteParam, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonTest, "", true, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonBinding, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonPrintDesignSpecial, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem_importRepx),
            new DevExpress.XtraBars.LinkPersistInfo(this.barLargeButton_export),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonPreview),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonSave, "", true, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonDelete, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonClose, true)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.UseWholeRow = true;
            this.bar1.Text = "Tools";
            // 
            // barButtonMainten
            // 
            this.barButtonMainten.Caption = "分类维护";
            this.barButtonMainten.Id = 10;
            this.barButtonMainten.ImageOptions.Image = global::Tjhis.Report.Custom.Properties.Resources.large_tiles;
            this.barButtonMainten.Name = "barButtonMainten";
            this.barButtonMainten.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonMainten_ItemClick);
            // 
            // barButtonParaDict
            // 
            this.barButtonParaDict.Caption = "参数字典";
            this.barButtonParaDict.Id = 11;
            this.barButtonParaDict.ImageOptions.Image = global::Tjhis.Report.Custom.Properties.Resources.report;
            this.barButtonParaDict.Name = "barButtonParaDict";
            this.barButtonParaDict.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonParaDict_ItemClick);
            // 
            // barButtonNew
            // 
            this.barButtonNew.Caption = "新增报表";
            this.barButtonNew.Id = 0;
            this.barButtonNew.ImageOptions.Image = global::Tjhis.Report.Custom.Properties.Resources.application_form_add;
            this.barButtonNew.Name = "barButtonNew";
            this.barButtonNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonNew_ItemClick);
            // 
            // barButtonCopy
            // 
            this.barButtonCopy.Caption = "复制并创建";
            this.barButtonCopy.Id = 1;
            this.barButtonCopy.ImageOptions.Image = global::Tjhis.Report.Custom.Properties.Resources.application_double;
            this.barButtonCopy.Name = "barButtonCopy";
            this.barButtonCopy.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonCopy_ItemClick);
            // 
            // barButtonChoosePara
            // 
            this.barButtonChoosePara.Caption = "选择参数";
            this.barButtonChoosePara.Id = 3;
            this.barButtonChoosePara.ImageOptions.Image = global::Tjhis.Report.Custom.Properties.Resources.page_code;
            this.barButtonChoosePara.Name = "barButtonChoosePara";
            this.barButtonChoosePara.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonChoosePara_ItemClick);
            // 
            // barButtonNewPara
            // 
            this.barButtonNewPara.Caption = "新建参数";
            this.barButtonNewPara.Id = 4;
            this.barButtonNewPara.ImageOptions.Image = global::Tjhis.Report.Custom.Properties.Resources.page_add;
            this.barButtonNewPara.Name = "barButtonNewPara";
            this.barButtonNewPara.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonNewPara_ItemClick);
            // 
            // barButtonEditPara
            // 
            this.barButtonEditPara.Caption = "编辑参数";
            this.barButtonEditPara.Id = 5;
            this.barButtonEditPara.ImageOptions.Image = global::Tjhis.Report.Custom.Properties.Resources.page_edit;
            this.barButtonEditPara.Name = "barButtonEditPara";
            this.barButtonEditPara.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonEditPara_ItemClick);
            // 
            // barButtonDeleteParam
            // 
            this.barButtonDeleteParam.Caption = "删除参数";
            this.barButtonDeleteParam.Id = 12;
            this.barButtonDeleteParam.ImageOptions.Image = global::Tjhis.Report.Custom.Properties.Resources.删除参数;
            this.barButtonDeleteParam.Name = "barButtonDeleteParam";
            this.barButtonDeleteParam.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonDeleteParam_ItemClick);
            // 
            // barButtonTest
            // 
            this.barButtonTest.Caption = "测试脚本";
            this.barButtonTest.Id = 7;
            this.barButtonTest.ImageOptions.Image = global::Tjhis.Report.Custom.Properties.Resources.spellcheck;
            this.barButtonTest.Name = "barButtonTest";
            this.barButtonTest.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonTest_ItemClick);
            // 
            // barButtonBinding
            // 
            this.barButtonBinding.Caption = "显示设计";
            this.barButtonBinding.Id = 6;
            this.barButtonBinding.ImageOptions.Image = global::Tjhis.Report.Custom.Properties.Resources.script_gear;
            this.barButtonBinding.Name = "barButtonBinding";
            this.barButtonBinding.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonBinding_ItemClick);
            // 
            // barButtonPrintDesignSpecial
            // 
            this.barButtonPrintDesignSpecial.Caption = "特殊打印设计";
            this.barButtonPrintDesignSpecial.Id = 15;
            this.barButtonPrintDesignSpecial.ImageOptions.Image = global::Tjhis.Report.Custom.Properties.Resources.特殊打印;
            this.barButtonPrintDesignSpecial.Name = "barButtonPrintDesignSpecial";
            this.barButtonPrintDesignSpecial.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonPrintDesignSpecial_ItemClick);
            // 
            // barButtonItem_importRepx
            // 
            this.barButtonItem_importRepx.ActAsDropDown = true;
            this.barButtonItem_importRepx.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.barButtonItem_importRepx.Caption = "导入repx";
            this.barButtonItem_importRepx.DropDownControl = this.popupMenu_import;
            this.barButtonItem_importRepx.Id = 17;
            this.barButtonItem_importRepx.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem_importRepx.ImageOptions.Image")));
            this.barButtonItem_importRepx.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem_importRepx.ImageOptions.LargeImage")));
            this.barButtonItem_importRepx.Name = "barButtonItem_importRepx";
            // 
            // popupMenu_import
            // 
            this.popupMenu_import.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barSubItem_ImportDisplay),
            new DevExpress.XtraBars.LinkPersistInfo(this.barSubItem_importSpecialPrint)});
            this.popupMenu_import.Manager = this.barManager1;
            this.popupMenu_import.Name = "popupMenu_import";
            // 
            // barSubItem_ImportDisplay
            // 
            this.barSubItem_ImportDisplay.AllowDrawArrow = DevExpress.Utils.DefaultBoolean.False;
            this.barSubItem_ImportDisplay.Caption = "显示用repx";
            this.barSubItem_ImportDisplay.Id = 22;
            this.barSubItem_ImportDisplay.Name = "barSubItem_ImportDisplay";
            this.barSubItem_ImportDisplay.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barSubItem_ImportDisplay_ItemClick);
            // 
            // barSubItem_importSpecialPrint
            // 
            this.barSubItem_importSpecialPrint.AllowDrawArrow = DevExpress.Utils.DefaultBoolean.False;
            this.barSubItem_importSpecialPrint.Caption = "特殊打印repx";
            this.barSubItem_importSpecialPrint.Id = 23;
            this.barSubItem_importSpecialPrint.Name = "barSubItem_importSpecialPrint";
            this.barSubItem_importSpecialPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barSubItem_importSpecialPrint_ItemClick);
            // 
            // barLargeButton_export
            // 
            this.barLargeButton_export.Caption = "导出";
            this.barLargeButton_export.Id = 24;
            this.barLargeButton_export.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barLargeButton_export.ImageOptions.Image")));
            this.barLargeButton_export.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barLargeButton_export.ImageOptions.LargeImage")));
            this.barLargeButton_export.Name = "barLargeButton_export";
            this.barLargeButton_export.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barLargeButton_export_ItemClick);
            // 
            // barButtonPreview
            // 
            this.barButtonPreview.ActAsDropDown = true;
            this.barButtonPreview.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.barButtonPreview.Caption = "预览";
            this.barButtonPreview.DropDownControl = this.popupMenu_Preview;
            this.barButtonPreview.Id = 16;
            this.barButtonPreview.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonPreview.ImageOptions.Image")));
            this.barButtonPreview.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonPreview.ImageOptions.LargeImage")));
            this.barButtonPreview.Name = "barButtonPreview";
            // 
            // popupMenu_Preview
            // 
            this.popupMenu_Preview.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barSubItem_display),
            new DevExpress.XtraBars.LinkPersistInfo(this.barSubItem_specialPrint)});
            this.popupMenu_Preview.Manager = this.barManager1;
            this.popupMenu_Preview.Name = "popupMenu_Preview";
            // 
            // barSubItem_display
            // 
            this.barSubItem_display.AllowDrawArrow = DevExpress.Utils.DefaultBoolean.False;
            this.barSubItem_display.Caption = "显示预览";
            this.barSubItem_display.Id = 20;
            this.barSubItem_display.Name = "barSubItem_display";
            this.barSubItem_display.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barSubItem_display_ItemClick);
            // 
            // barSubItem_specialPrint
            // 
            this.barSubItem_specialPrint.AllowDrawArrow = DevExpress.Utils.DefaultBoolean.False;
            this.barSubItem_specialPrint.Caption = "特殊打印预览";
            this.barSubItem_specialPrint.Id = 21;
            this.barSubItem_specialPrint.Name = "barSubItem_specialPrint";
            this.barSubItem_specialPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barSubItem_specialPrint_ItemClick);
            // 
            // barButtonSave
            // 
            this.barButtonSave.Caption = "保存报表";
            this.barButtonSave.Id = 9;
            this.barButtonSave.ImageOptions.Image = global::Tjhis.Report.Custom.Properties.Resources.disk;
            this.barButtonSave.Name = "barButtonSave";
            this.barButtonSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonSave_ItemClick);
            // 
            // barButtonDelete
            // 
            this.barButtonDelete.Caption = "删除报表";
            this.barButtonDelete.Id = 8;
            this.barButtonDelete.ImageOptions.Image = global::Tjhis.Report.Custom.Properties.Resources.delete;
            this.barButtonDelete.Name = "barButtonDelete";
            this.barButtonDelete.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonDelete_ItemClick);
            // 
            // barButtonClose
            // 
            this.barButtonClose.Caption = "关闭";
            this.barButtonClose.Id = 13;
            this.barButtonClose.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonClose.ImageOptions.Image")));
            this.barButtonClose.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonClose.ImageOptions.LargeImage")));
            this.barButtonClose.Name = "barButtonClose";
            this.barButtonClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonClose_ItemClick);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Manager = this.barManager1;
            this.barDockControlTop.Size = new System.Drawing.Size(1122, 64);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 696);
            this.barDockControlBottom.Manager = this.barManager1;
            this.barDockControlBottom.Size = new System.Drawing.Size(1122, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 64);
            this.barDockControlLeft.Manager = this.barManager1;
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 632);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1122, 64);
            this.barDockControlRight.Manager = this.barManager1;
            this.barDockControlRight.Size = new System.Drawing.Size(0, 632);
            // 
            // panelReportRole
            // 
            this.panelReportRole.Controls.Add(this.groupReportRole);
            this.panelReportRole.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelReportRole.Location = new System.Drawing.Point(0, 0);
            this.panelReportRole.Name = "panelReportRole";
            this.panelReportRole.Size = new System.Drawing.Size(855, 60);
            this.panelReportRole.TabIndex = 0;
            // 
            // groupReportRole
            // 
            this.groupReportRole.AutoSize = true;
            this.groupReportRole.Controls.Add(this.txtPageSettings);
            this.groupReportRole.Controls.Add(this.checkEdit_IsTable);
            this.groupReportRole.Controls.Add(this.textEdit_ReportName);
            this.groupReportRole.Controls.Add(this.slueReportClass);
            this.groupReportRole.Controls.Add(this.labelControl4);
            this.groupReportRole.Controls.Add(this.labelControl3);
            this.groupReportRole.Controls.Add(this.labelControl2);
            this.groupReportRole.Controls.Add(this.labelControl1);
            this.groupReportRole.Controls.Add(this.beRole);
            this.groupReportRole.Controls.Add(this.txtRole);
            this.groupReportRole.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupReportRole.Location = new System.Drawing.Point(0, 0);
            this.groupReportRole.Name = "groupReportRole";
            this.groupReportRole.Size = new System.Drawing.Size(855, 60);
            this.groupReportRole.TabIndex = 0;
            this.groupReportRole.Text = "报表权限";
            // 
            // txtPageSettings
            // 
            this.txtPageSettings.Location = new System.Drawing.Point(745, 30);
            this.txtPageSettings.MenuManager = this.barManager1;
            this.txtPageSettings.Name = "txtPageSettings";
            this.txtPageSettings.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txtPageSettings.Size = new System.Drawing.Size(100, 20);
            this.txtPageSettings.TabIndex = 30;
            this.txtPageSettings.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.txtPageSettings_ButtonClick);
            // 
            // checkEdit_IsTable
            // 
            this.checkEdit_IsTable.Location = new System.Drawing.Point(685, 31);
            this.checkEdit_IsTable.MenuManager = this.barManager1;
            this.checkEdit_IsTable.Name = "checkEdit_IsTable";
            this.checkEdit_IsTable.Properties.Caption = "表格";
            this.checkEdit_IsTable.Size = new System.Drawing.Size(54, 19);
            this.checkEdit_IsTable.TabIndex = 27;
            this.checkEdit_IsTable.CheckedChanged += new System.EventHandler(this.checkEdit_IsTable_CheckedChanged);
            // 
            // textEdit_ReportName
            // 
            this.textEdit_ReportName.Location = new System.Drawing.Point(256, 30);
            this.textEdit_ReportName.MenuManager = this.barManager1;
            this.textEdit_ReportName.Name = "textEdit_ReportName";
            this.textEdit_ReportName.Size = new System.Drawing.Size(135, 20);
            this.textEdit_ReportName.TabIndex = 26;
            // 
            // slueReportClass
            // 
            this.slueReportClass.EditValue = "请选择";
            this.slueReportClass.Location = new System.Drawing.Point(76, 30);
            this.slueReportClass.MenuManager = this.barManager1;
            this.slueReportClass.Name = "slueReportClass";
            this.slueReportClass.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.slueReportClass.Properties.PopupView = this.searchLookUpEdit1View;
            this.slueReportClass.Size = new System.Drawing.Size(100, 20);
            this.slueReportClass.TabIndex = 25;
            this.slueReportClass.EditValueChanged += new System.EventHandler(this.slueReportClass_EditValueChanged);
            // 
            // searchLookUpEdit1View
            // 
            this.searchLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.APP_NAME,
            this.CLASS_NAME,
            this.CLASS_ID});
            this.searchLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.searchLookUpEdit1View.Name = "searchLookUpEdit1View";
            this.searchLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.searchLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            // 
            // APP_NAME
            // 
            this.APP_NAME.Caption = "应用程序名";
            this.APP_NAME.FieldName = "APP_NAME";
            this.APP_NAME.Name = "APP_NAME";
            this.APP_NAME.Visible = true;
            this.APP_NAME.VisibleIndex = 0;
            // 
            // CLASS_NAME
            // 
            this.CLASS_NAME.Caption = "报表分类名";
            this.CLASS_NAME.FieldName = "CLASS_NAME";
            this.CLASS_NAME.Name = "CLASS_NAME";
            this.CLASS_NAME.Visible = true;
            this.CLASS_NAME.VisibleIndex = 1;
            // 
            // CLASS_ID
            // 
            this.CLASS_ID.Caption = "报表分类ID";
            this.CLASS_ID.FieldName = "CLASS_ID";
            this.CLASS_ID.Name = "CLASS_ID";
            this.CLASS_ID.Visible = true;
            this.CLASS_ID.VisibleIndex = 2;
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(186, 33);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(60, 14);
            this.labelControl4.TabIndex = 24;
            this.labelControl4.Text = "报表名称：";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(6, 33);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(60, 14);
            this.labelControl3.TabIndex = 23;
            this.labelControl3.Text = "报表分类：";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(397, 33);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(28, 14);
            this.labelControl2.TabIndex = 22;
            this.labelControl2.Text = "角色:";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(537, 33);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(64, 14);
            this.labelControl1.TabIndex = 21;
            this.labelControl1.Text = "自定义用户:";
            // 
            // beRole
            // 
            this.beRole.Location = new System.Drawing.Point(433, 30);
            this.beRole.Name = "beRole";
            this.beRole.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.beRole.Size = new System.Drawing.Size(93, 20);
            this.beRole.TabIndex = 20;
            this.beRole.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.beRole_ButtonClick);
            this.beRole.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.beRole_EditValueChanging);
            this.beRole.DoubleClick += new System.EventHandler(this.beRole_DoubleClick);
            // 
            // txtRole
            // 
            this.txtRole.Location = new System.Drawing.Point(614, 30);
            this.txtRole.Name = "txtRole";
            this.txtRole.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txtRole.Size = new System.Drawing.Size(58, 20);
            this.txtRole.TabIndex = 19;
            this.txtRole.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.txtRole_ButtonClick);
            this.txtRole.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.txtRole_EditValueChanging);
            this.txtRole.DoubleClick += new System.EventHandler(this.txtRole_DoubleClick);
            // 
            // frmStatisticalSetting2
            // 
            this.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(239)))));
            this.Appearance.Options.UseBackColor = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1122, 696);
            this.Controls.Add(this.splitContainer1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.MinimizeBox = false;
            this.Name = "frmStatisticalSetting2";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "自定义报表维护";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frmStatisticalSetting2_FormClosing);
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.frmStatisticalSetting2_FormClosed);
            this.Load += new System.EventHandler(this.frmStatisticalSetting_Load);
            this.Shown += new System.EventHandler(this.frmStatisticalSetting2_Shown);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frmStatisticalSetting2_KeyUp);
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).EndInit();
            this.splitContainer1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeTempleList)).EndInit();
            this.splitContainer2.Panel1.ResumeLayout(false);
            this.splitContainer2.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer2)).EndInit();
            this.splitContainer2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupReportParam)).EndInit();
            this.groupReportParam.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcReportParam)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvReportParam)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lueEditType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lueSourceType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtSql.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txt_memo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenu_import)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenu_Preview)).EndInit();
            this.panelReportRole.ResumeLayout(false);
            this.panelReportRole.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupReportRole)).EndInit();
            this.groupReportRole.ResumeLayout(false);
            this.groupReportRole.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtPageSettings.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEdit_IsTable.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit_ReportName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.slueReportClass.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.searchLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.beRole.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRole.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.SplitContainer splitContainer2;
        private System.Windows.Forms.Panel panelReportRole;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupReportRole;
        private DevExpress.XtraEditors.GroupControl groupReportParam;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private DevExpress.XtraEditors.ButtonEdit txtRole;
        private DevExpress.XtraGrid.GridControl gcReportParam;
        private DevExpress.XtraGrid.Views.Grid.GridView dgvReportParam;
        private DevExpress.XtraGrid.Columns.GridColumn gc_SerialNo;
        private DevExpress.XtraGrid.Columns.GridColumn gc_PARAM_NAME;
        private DevExpress.XtraGrid.Columns.GridColumn gc_EDIT_TYPE;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit lueEditType;
        private DevExpress.XtraGrid.Columns.GridColumn gc_CAPTION;
        private DevExpress.XtraGrid.Columns.GridColumn gc_DISPLAY_MEMBER;
        private DevExpress.XtraGrid.Columns.GridColumn gc_VALUE_MEMBER;
        private DevExpress.XtraGrid.Columns.GridColumn gc_SOURCE_TYPE;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit lueSourceType;
        private DevExpress.XtraGrid.Columns.GridColumn gc_NEXT_PARAM_NAME;
        private DevExpress.XtraGrid.Columns.GridColumn gc_DEFAULT_VALUE;
        private DevExpress.XtraGrid.Columns.GridColumn gc_CONTROL_WIDTH;
        private DevExpress.XtraEditors.MemoEdit txtSql;
        private DevExpress.XtraGrid.Columns.GridColumn gc_DATE_FORMAT;
        private DevExpress.XtraEditors.ButtonEdit beRole;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarLargeButtonItem barButtonNew;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarLargeButtonItem barButtonCopy;
        private DevExpress.XtraBars.BarLargeButtonItem barButtonChoosePara;
        private DevExpress.XtraBars.BarLargeButtonItem barButtonNewPara;
        private DevExpress.XtraBars.BarLargeButtonItem barButtonEditPara;
        private DevExpress.XtraBars.BarLargeButtonItem barButtonBinding;
        private DevExpress.XtraBars.BarLargeButtonItem barButtonTest;
        private DevExpress.XtraBars.BarLargeButtonItem barButtonDelete;
        private DevExpress.XtraBars.BarLargeButtonItem barButtonSave;
        private DevExpress.XtraBars.BarLargeButtonItem barButtonMainten;
        private DevExpress.XtraBars.BarLargeButtonItem barButtonParaDict;
        private DevExpress.XtraEditors.CheckEdit checkEdit_IsTable;
        private DevExpress.XtraEditors.TextEdit textEdit_ReportName;
        private DevExpress.XtraEditors.SearchLookUpEdit slueReportClass;
        private DevExpress.XtraGrid.Views.Grid.GridView searchLookUpEdit1View;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraGrid.Columns.GridColumn APP_NAME;
        private DevExpress.XtraGrid.Columns.GridColumn CLASS_NAME;
        private DevExpress.XtraGrid.Columns.GridColumn CLASS_ID;
        private DevExpress.XtraTreeList.TreeList treeTempleList;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn1;
        private DevExpress.XtraBars.BarLargeButtonItem barButtonDeleteParam;
        private DevExpress.XtraBars.BarLargeButtonItem barButtonClose;
        private DevExpress.XtraBars.BarLargeButtonItem barButtonPrintDesignSpecial;
        private System.Windows.Forms.ImageList imageList1;
        private DevExpress.XtraEditors.TextEdit txt_memo;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraBars.BarLargeButtonItem barButtonPreview;
        private DevExpress.XtraBars.BarLargeButtonItem barButtonItem_importRepx;
        private DevExpress.XtraBars.BarSubItem barSubItem_display;
        private DevExpress.XtraBars.BarSubItem barSubItem_specialPrint;
        private DevExpress.XtraBars.PopupMenu popupMenu_Preview;
        private DevExpress.XtraBars.BarSubItem barSubItem_ImportDisplay;
        private DevExpress.XtraBars.BarSubItem barSubItem_importSpecialPrint;
        private DevExpress.XtraBars.PopupMenu popupMenu_import;
        private DevExpress.XtraBars.BarLargeButtonItem barLargeButton_export;
        private DevExpress.XtraEditors.ButtonEdit txtPageSettings;
    }
}