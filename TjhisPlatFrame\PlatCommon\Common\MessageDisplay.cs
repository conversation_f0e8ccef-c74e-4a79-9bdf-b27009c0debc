﻿//**********************************************
//说明:
//计算机名称：LINDP
//创建日期：2016/7/27 15:48:45
//作者：林大鹏
//版本号：V1.00
//**********************************************
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace PlatCommon.Common
{
    public class MessageDisplay
    {
        //消息类型 1 医嘱有变化
        public string MsgType { get; set; }
       
        public string BedLabel { get; set; }
        //患者编号
        public string PatientID { get; set; }
        //住院次数
        public string VisitID { get; set; }
       
        public string Name { get; set; }
        //发送者工号
        public string Memo { get; set; }

        //会话类型
        public string SessionType { get; set; }
        
        //接收工作站
        public string MsgStation { get; set; }
        //护理单元
        public string MsgWardCode { get; set; }
        
        //换床时使用床号
        public string BedLabelTo { get; set; }
        
        //部门
        public string DeptCode { get; set; }
        //发送者工号
        public string SendEmpNo { get; set; }
        //发送者名称
        public string SendUserName { get; set; }
        //发送消息内容
        public string MsgContent { get; set; }
        //接收者工号
        public string RecvEmpNo { get; set; }
        //接收者名称
        public string RecvUserName { get; set; }
        //接收者IP
        public string RecvIP { get; set; }
    }
}
