﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace PlatCommonForm
{
    public partial class FrmPlatFormDemo : PlatCommon.SysBase.ParentForm
    {
        public FrmPlatFormDemo()
        {
            InitializeComponent();
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            //PlatFormServiceDictSerialParame pf = new PlatFormServiceDictSerialParame();
            //string strSystemName = "OBILLING";
            //string strServiceHisName = "OBILLING_OUTP_PRESC";
            //string strPatientId = string.Empty;
            //string strApplyNo = string.Empty;
            //string strSqlMaster = "SELECT SERIAL_NO FROM OUTP_ORDERS WHERE SERIAL_NO ='472687'";
            //string strSqlDetail = "SELECT SERIAL_NO,ITEM_NO FROM OUTP_PRESC WHERE SERIAL_NO = '472687'";
            //string strDbMasterName = "OUTP_ORDERS";
            //string strDbDetailName = "OUTP_PRESC";

            //int intPf = pf.HisPlatFormInterface(strSystemName, strServiceHisName, strPatientId, strApplyNo, strSqlMaster, strSqlDetail, strDbMasterName, strDbDetailName);

        }
    }
}