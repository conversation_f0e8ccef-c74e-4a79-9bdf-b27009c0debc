﻿/*********************************************
* 文 件 名：Cs02Controls
* 类 名 称：Cs02Controls
* 功能说明：公共控件扩展
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：吴新才
* 创建时间：2018-05-22 15:38:26
* 版 本 号：1.0.0.1
* 修改时间：2020-02-18 15:38:26
* 修 改 人：吴新才
* CLR 版本：4.0.30319.42000
/*********************************************/

using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Reflection;
using System.Windows.Forms;
using System.IO;
using DevExpress.Utils;
using DevExpress.XtraEditors.Controls;
using DevExpress.Data.Filtering;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraTreeList;
using DevExpress.XtraLayout;
using DevExpress.XtraSplashScreen;
using System.Runtime.InteropServices;
using PlatCommon.Base01;

namespace PlatCommon.Base02
{
    /// <summary>
    /// 公共控件扩展
    /// </summary>
    public static class Cs02Controls
    {
        /// <summary>
        /// 从Dll中，打开Form
        /// </summary>
        /// <param name="strDllName">DLL文件名</param>
        /// <param name="strTypeName">Form名，带命名空间的全名，如：Tjhis.HisComm.His06FrmAbout </param>
        /// <returns></returns>
        public static Form GetFormFromDll(string strDllName, string strTypeName)
        {
           if (!File.Exists(strDllName))
                throw new Exception($"文件：{strDllName}不存在！");

            try
            {
                Assembly asmAssembly = Assembly.LoadFrom(strDllName);
                Type typeToLoad = asmAssembly.GetType(strTypeName);
                object GenericInstance = Activator.CreateInstance(typeToLoad);
                Form frmToLoad= (Form)(GenericInstance);

                return frmToLoad;
            }
            catch (Exception ex)
            {
                string strErrMsg = "DLL中，没有包含要创建的对象！\r\nDLL名称：" + strDllName + "\r\n类名：" + strTypeName + "\r\n";
                throw new Exception(strErrMsg + ex.Message);
            }
        }

        /// <summary>
        /// 将DateEdit设置为DateTime控件模式
        /// </summary>
        /// <param name="edit">编辑控件</param>
        public static void SetDateEditToDateTime(DevExpress.XtraEditors.DateEdit edit)
        {
            DevExpress.XtraEditors.Repository.RepositoryItemDateEdit p = edit.Properties;
            edit.Properties.VistaDisplayMode = DevExpress.Utils.DefaultBoolean.True;
            edit.Properties.VistaEditTime = DevExpress.Utils.DefaultBoolean.True;
            edit.Properties.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            edit.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            edit.Properties.EditFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            edit.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            edit.Properties.Mask.EditMask = "yyyy-MM-dd HH:mm:ss";
        }

        /// <summary>
        /// 另保存Excel文件
        /// </summary>
        /// <param name="GridControl">数据源(GridControl)</param>
        /// <param name="strFileName">文件名</param>
        public static void ExportToExcel(GridControl GridControl, string strFileName = "")
        {
            SaveFileDialog fileDialog = new SaveFileDialog
            {
                Title = "导出Excel",
                Filter = "Excel文件(*.xls)|*.xls"
            };
            fileDialog.FileName = strFileName;
            DialogResult dialogResult = fileDialog.ShowDialog();
            if (dialogResult == DialogResult.OK)
            {
                GridControl.ExportToXls(fileDialog.FileName);
                Cs02MessageBox.ShowInfo("导出成功！");
            }
        }

        /// <summary>
        /// 另保存Excel文件
        /// </summary>
        /// <param name="gv">数据源(GridView)</param>
        /// <param name="strFileName">文件名</param>
        public static void ExportToExcel(GridView gv, string strFileName = "")
        {
            SaveFileDialog saveFile = new SaveFileDialog();
            saveFile.Title = "请选择文件存放路径";
            saveFile.Filter = "Excel文档(*.xls)|*.xls|Excel文档(*.xlsx)|*.xlsx";
            saveFile.FileName = strFileName;
            if (saveFile.ShowDialog() == DialogResult.OK)
            {
                DevExpress.XtraPrinting.XlsExportOptions options = new DevExpress.XtraPrinting.XlsExportOptions();
                options.TextExportMode = DevExpress.XtraPrinting.TextExportMode.Text;
                options.SheetName = strFileName;
                gv.OptionsPrint.AutoWidth = false;
                gv.OptionsPrint.AllowCancelPrintExport = false;
                gv.AppearancePrint.Row.Font = new System.Drawing.Font("宋体", 9);
                try
                {
                    gv.ExportToXls(saveFile.FileName, options);
                    Cs02MessageBox.ShowInfo("导出成功！");
                }
                catch (Exception ex)
                {
                    Cs02MessageBox.ShowError("导出失败！\r\n错误信息：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 设置某列过滤
        /// </summary>
        /// <param name="gv">目标gv</param>
        /// <param name="strFieldName">过滤字段</param>
        /// <param name="strSearchText">过滤值</param>
        public static void SetColumnFilter(GridView gv, string strFieldName, string strSearchText)
        {
            SetColumnFilter(gv.Columns[strFieldName], strSearchText);
        }

        /// <summary>
        /// 设置GridView全列包含过滤
        /// </summary>
        /// <param name="gv">目标gv</param>
        /// <param name="strSearchText">过滤值</param>
        public static void SetAllColumnFilter(GridView gv, string strSearchText)
        {
            foreach (GridColumn item in gv.Columns)
            {
                //筛选条件设置为包含
                item.OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;
            }
            gv.FindFilterText = strSearchText;
        }

        /// <summary>
        /// 清除GridView过滤列
        /// </summary>
        /// <param name="gv"></param>
        public static void ClearFilterColumns(GridView gv)
        {
            foreach (GridColumn item in gv.Columns)
            {
                item.OptionsFilter.AllowAutoFilter = false;
                item.OptionsFilter.AllowFilter = false;
            }
        }

        /// <summary>
        /// 清除GridView各列的排序
        /// </summary>
        /// <param name="gv"></param>
        public static void ClearColumnSortOrder(GridView gv)
        {
            GridColumnCollection cols = gv.Columns;
            int n = cols.Count;
            for (int i = 0; i < n; i++)
            {
                if (cols[i].SortOrder != DevExpress.Data.ColumnSortOrder.None)
                {
                    cols[i].SortOrder = DevExpress.Data.ColumnSortOrder.None;
                }
            }
        }

        /// <summary>
        /// 返回空白列数据的行号
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="strFieldName">列名</param>
        /// <returns></returns>
        public static int GetEmptyRow(GridView gridView, string strFieldName)
        {
            int iRowHandle = -1;
            if (string.IsNullOrEmpty(strFieldName)) return iRowHandle;

            GridView gv = gridView;
            int iRowCount = gv.RowCount;

            for (int i = 0; i < iRowCount; i++)
            {
                string strValue = gv.GetDataRow(i)[strFieldName]?.ToString().Trim();
                if (String.IsNullOrEmpty(strValue))
                {
                    iRowHandle = i;
                    break;
                }
            }
            return iRowHandle;
        }

        /// <summary>
        /// 返回空白列数据的行号
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="strArrFieldNames">列名数组</param>
        /// <returns></returns>
        public static int GetEmptyRow(GridView gridView, string[] strArrFieldNames)
        {
            int iRowHandle = -1;
            if (strArrFieldNames.Length < 1) return iRowHandle;

            GridView gv = gridView;
            int iRowCount = gv.RowCount;

            for (int i = 0; i < iRowCount; i++)
            {
                foreach (var strFieldName in strArrFieldNames)
                {
                    if (string.IsNullOrEmpty(strFieldName)) continue;

                    string strValue = gv.GetDataRow(i)[strFieldName]?.ToString().Trim();
                    if (String.IsNullOrEmpty(strValue))
                    {
                        iRowHandle = i;
                        break;
                    }
                }
                if (iRowHandle >= 0) break;
            }
            return iRowHandle;
        }

        /// <summary>
        /// 返回存在数据的行号
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="strFieldName">列名</param>
        /// <param name="strFieldValue">值</param>
        /// <returns></returns>
        public static int GetExistedRow(GridView gridView, string strFieldName, string strFieldValue)
        {
            GridView gv = gridView;
            int iCount = gv.RowCount;
            int row = -1;
            for (int i = 0; i < iCount; i++)
            {
                string strValue = gv.GetDataRow(i)?[strFieldName].ToString();
                if (strValue == strFieldValue)
                {
                    row = i;
                    break;
                }
            }
            return row;
        }

        /// <summary>
        /// 判断是否有重复数据，返回重复的行号对
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="strFieldName">列名</param>
        /// <returns></returns>
        public static int[] GetRepeatedRow(GridView gridView, string strFieldName)
        {
            GridView gv = gridView;
            int iCount = gv.RowCount;
            int[] iArrRows = new int[2] { -1, -1 };
            Dictionary<string, int> dic = new Dictionary<string, int>();
            for (int i = 0; i < iCount; i++)
            {
                string strValue = gv.GetDataRow(i)[strFieldName].ToString();
                if (dic.ContainsKey(strValue))
                {
                    iArrRows[1] = i + 1;
                    iArrRows[0] = dic[strValue];
                    break;
                }
                else
                {
                    dic[strValue] = i + 1;
                }
            }
            return iArrRows;
        }

        /// <summary>
        /// 判断是否有重复数据，返回重复的行号对
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="strArrFieldName">列名数组</param>
        /// <returns></returns>
        public static int[] GetRepeatedRow(GridView gridView, string[] strArrFieldName)
        {

            GridView gv = gridView;
            int count = gv.RowCount;
            int[] iArrRows = new int[2] { -1, -1 };
            Dictionary<string, int> dic = new Dictionary<string, int>();
            int iFieldCount = strArrFieldName.Length;
            for (int i = 0; i < count; i++)
            {
                int k;
                DataRow dr = gv.GetDataRow(i);
                string key = dr[strArrFieldName[0]].ToString();
                for (k = 1; k < iFieldCount; k++)
                {
                    key += "." + dr[strArrFieldName[k]].ToString();
                }
                if (dic.ContainsKey(key))
                {
                    iArrRows[1] = i + 1;
                    iArrRows[0] = dic[key];
                    break;
                }
                else
                {
                    dic[key] = i + 1;
                }
            }
            return iArrRows;
        }

        /// <summary>
        /// 读取GridView各列的标题字符串（格式:FieldName=标题[,FieldName=标题])
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <returns></returns>
        public static string GetGridViewColumnsCaption(GridView gridView)
        {
            List<string> list = new List<string>();
            foreach (GridColumn col in gridView.Columns)
            {
                string strCaption = string.Empty;
                if (col.Visible)
                {
                    strCaption = col.Caption;
                    if (string.IsNullOrEmpty(strCaption))
                    {
                        strCaption = col.CustomizationSearchCaption;
                    }
                    list.Add($"{col.FieldName}={strCaption}");
                }
            }
            return string.Join(",", list);
        }

        /// <summary>
        /// 设定GridView各列标题（格式:FieldName=标题[,FieldName=标题])
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="strFieldsCaption">fieldsCaption</param>
        /// <returns></returns>
        public static bool SetGridViewColumnsCaption(GridView gridView, string strFieldsCaption)
        {
            Dictionary<string, string> dic = Cs01Functions.StringToDict(strFieldsCaption, ',', '=');
            foreach (GridColumn col in gridView.Columns)
            {
                string strFieldName = col.FieldName;
                if (dic.ContainsKey(strFieldName))
                {
                    col.Caption = dic[strFieldName];
                }
            }
            return true;
        }

        /// <summary>
        /// 读取GridView各列宽度的表示字符串（格式:FieldName=Width[,FieldName=Width])
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <returns></returns>
        public static string GetGridViewColumnsWidth(GridView gridView)
        {
            List<string> list = new List<string>();
            foreach (GridColumn col in gridView.Columns)
            {
                list.Add($"{col.FieldName}={col.Width}");
            }
            return string.Join(",", list);
        }

        /// <summary>
        /// 设定GridView各列宽度（格式:FieldName=Width[,FieldName=Width])
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="strFieldsWidth">GridView</param>
        /// <returns></returns>
        public static bool SetGridViewColumnsWidth(GridView gridView, string strFieldsWidth)
        {
            Dictionary<string, int> dic = Cs01Functions.StringToDictInt(strFieldsWidth, ',', '=');
            foreach (GridColumn col in gridView.Columns)
            {
                int iWidth;
                string strFieldName = col.FieldName;
                if (dic.ContainsKey(strFieldName))
                {
                    iWidth = dic[strFieldName];
                    if (iWidth > 0)
                    {
                        col.Width = iWidth;
                    }
                    else
                    {
                        col.Visible = false;
                        col.Width = -1;
                    }
                }
            }
            return true;
        }

        /// <summary>
        /// 设置TreeList的属性
        /// </summary>
        /// <param name="treelist">树形控件</param>
        public static void SetTreeListProperty(TreeList treelist)
        {
            //是否可编辑
            treelist.OptionsBehavior.Editable = false;
            //聚焦的样式是否只适用于聚焦CELL，或者适用失去焦点后的所有CELL
            treelist.OptionsSelection.InvertSelection = true;

            TreeListOptionsView tov = treelist.OptionsView;
            tov.ShowColumns = false;   //是否显示选中的行
            tov.ShowIndicator = false; //指示面板是否显示
            tov.ShowHorzLines = false; //水平线是否显示
            tov.ShowVertLines = false; //垂直线条是否显示

            //设置treeList的折叠样式为 +  - 号
            treelist.LookAndFeel.UseDefaultLookAndFeel = false;
            treelist.LookAndFeel.UseWindowsXPTheme = true;
        }

        /// <summary>
        /// GridView回车是TAB
        /// </summary>
        /// <param name="gridView">目标GridView</param>
        public static void SetEnterKeyDownAsTab(GridView gridView)
        {
            gridView.KeyDown += EnterKeyDownAsTab;
        }

        /// <summary>
        /// 获取页面中控件的绝对坐标
        /// </summary>
        /// <param name="ctrl"></param>
        /// <returns></returns>
        public static Point GetScreenPoint(Control ctrl)
        {
            Point p = new Point(0, 0);
            Control ctl = ctrl;
            while (ctl.Parent != null)
            {
                p.X += ctrl.Left;
                p.Y += ctl.Top;
                ctl = ctl.Parent;
            }
            return p;
        }

        /// <summary>
        /// 设定GridView编辑状态
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="iWidth">标志位宽度</param>
        public static void SetGridViewEditState(GridView gridView, int iWidth)
        {
            //gridView.OptionsNavigation.EnterMoveNextColumn = true; 有选择列时，光标在选择列时无效。
            gridView.KeyDown += EnterKeyDownAsTab;
            if (iWidth > 20)
            {
                gridView.OptionsView.ShowIndicator = true;
                gridView.IndicatorWidth = iWidth;
                gridView.CustomDrawRowIndicator += CustomDrawRowIndicator;
            }
            else
            {
                gridView.OptionsView.ShowIndicator = false;
                gridView.IndicatorWidth = -1;
            }
            gridView.RowCellStyle += RowCellStyle;
        }

        #region 等待窗口
        /// <summary>
        /// 等待窗口：waitDialog 字段与方法作者：曹顺想 2019/01/21
        /// </summary>
        public static WaitDialogForm waitDialog = null;

        /// <summary>
        /// 显示进度窗口
        /// </summary>
        /// <param name="strCaption">说明文本</param>
        /// <param name="strTitle">窗口标题</param>
        public static void SetWaitDialogCaption(string strCaption, string strTitle = "正在执行")
        {
            if (waitDialog == null)
            {
                waitDialog = new WaitDialogForm(strCaption, strTitle);
            }
            else
            {
                waitDialog.Caption = strCaption;
                waitDialog.Text = strTitle;
            }
            waitDialog.Visible = true;
        }

        /// <summary>
        /// 设置等待窗口相对于屏幕中心的位置
        /// </summary>
        /// <param name="x">x坐标的偏移量</param>
        /// <param name="y">y坐标的偏移量</param>
        public static void SetWaitDialogPosition(int x, int y)
        {
            if (waitDialog != null)
            {
                waitDialog.StartPosition = FormStartPosition.CenterScreen;
                waitDialog.Top -= x;
                waitDialog.Left -= y;
                if (waitDialog.Top < 0) waitDialog.Top = 0;
                if (waitDialog.Left < 0) waitDialog.Left = 0;
            }
        }

        /// <summary>
        /// 隐藏进度窗口
        /// </summary>
        public static void HideWaitDialog()
        {
            waitDialog.Hide();
        }
        #endregion

        /// <summary>
        /// 设置GridView日期型列的显示字符串
        /// </summary>
        /// <param name="gc">GridColumn</param>
        /// <param name="strFormatString">格式化串</param>
        public static void SetDateTimeColumnFormat(GridColumn gc, string strFormatString = "yyyy-MM-dd HH:mm:ss")
        {
            string strFormat = strFormatString;
            if (string.IsNullOrEmpty(strFormat))
                strFormat = "yyyy-MM-dd HH:mm:ss";
            gc.DisplayFormat.FormatString = strFormat;
            gc.UnboundType = DevExpress.Data.UnboundColumnType.DateTime;
        }

        /// <summary>
        /// 设置GridView日期型列的显示字符串
        /// </summary>
        /// <param name="gv">GridView</param>
        /// <param name="strFieldName">字段名</param>
        /// <param name="strFormatString">格式化串</param>
        public static void SetDateTimeColumnFormat(GridView gv, string strFieldName, string strFormatString = "yyyy-MM-dd HH:mm:ss")
        {
            GridColumn gc = gv.Columns[strFieldName];
            string strFormat = strFormatString;
            if (string.IsNullOrEmpty(strFormat))
                strFormat = "yyyy-MM-dd HH:mm:ss";

            gc.DisplayFormat.FormatString = strFormat;
            gc.UnboundType = DevExpress.Data.UnboundColumnType.DateTime;
        }

        /// <summary>
        /// 设置GridLookUpEdit全列搜索属性。 此方法在GridView中的下拉GridLookUpEdit控件中存在问题，有待解决。
        /// </summary>
        /// <param name="control"></param>
        /// <param name="edit">RepositoryItemGridLookUpEdit</param>
        public static void SetSearchAllColumn(Control control, RepositoryItemGridLookUpEdit edit)
        {
            edit.PopupFilterMode = DevExpress.XtraEditors.PopupFilterMode.Contains;//包含即可
            edit.ImmediatePopup = true;             //是否马上弹出窗体
            edit.ValidateOnEnterKey = true;         //回车确认
            edit.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;//文本框可输入
            edit.CloseUpKey = new KeyShortcut(Keys.F9);
            SetGridLookUpEditMoreColumnFilter(control, edit);
        }

        private static void SetGridLookUpEditMoreColumnFilter(Control control, RepositoryItemGridLookUpEdit repGLUEdit)
        {
            repGLUEdit.EditValueChanging += (sender, e) =>
            {
                //System.Windows.Forms.MethodInvoker
                control.BeginInvoke(new MethodInvoker(() =>
                {
                    GridLookUpEdit edit = sender as GridLookUpEdit;
                    if (edit == null)
                    {
                        return;
                    }
                    GridView view = edit.Properties.View;
                    Type type = view.GetType();
                    //获取GriView私有变量 System.Reflection
                    FieldInfo extraFilter = type.GetField("extraFilter", BindingFlags.NonPublic | BindingFlags.Instance);
                    List<CriteriaOperator> columnsOperators = new List<CriteriaOperator>();
                    foreach (GridColumn col in view.VisibleColumns)
                    {
                        if (col.Visible && col.ColumnType == typeof(string))
                            columnsOperators.Add(new FunctionOperator(FunctionOperatorType.Contains, new OperandProperty(col.FieldName), new OperandValue(edit.Text)));
                    }
                    string filterCondition = new GroupOperator(GroupOperatorType.Or, columnsOperators).ToString();
                    extraFilter?.SetValue(view, filterCondition);
                    //获取GriView中处理列过滤的私有方法System.Reflection
                    MethodInfo applyColumnsFilterEx = type.GetMethod("ApplyColumnsFilterEx", BindingFlags.NonPublic | BindingFlags.Instance);
                    applyColumnsFilterEx?.Invoke(view, null);
                }));
            };
        }

        /// <summary>
        /// gridView单项过滤方法， 是gridView1.ActiveFilterCriteria的参数
        /// </summary>
        /// <param name="strFieldName">过滤字段</param>
        /// <param name="strFieldValue">过滤值</param>
        /// <returns></returns>
        public static GroupOperator BulidFilterCriteria(string strFieldName, string strFieldValue)
        {
            CriteriaOperatorCollection filterCollection = new CriteriaOperatorCollection();
            filterCollection.Add(CriteriaOperator.Parse($"{strFieldName} LIKE '%{strFieldValue}%'"));
            return new GroupOperator(filterCollection.ToArray());
        }

        /// <summary>
        /// gridView多项过滤方法。 是gridView1.ActiveFilterCriteria的参数
        /// </summary>
        /// <param name="strArrFieldNames">过滤字段数组</param>
        /// <param name="strArrFieldValues">过滤值数组</param>
        /// <param name="type"></param>
        /// <returns></returns>
        public static GroupOperator BulidFilterCriteria(string[] strArrFieldNames, string[] strArrFieldValues, GroupOperatorType type = GroupOperatorType.And)
        {
            CriteriaOperatorCollection filterCollection = new CriteriaOperatorCollection();
            int n = strArrFieldNames.Length;
            for (int i = 0; i < n; i++)
            {
                filterCollection.Add(CriteriaOperator.Parse($"{strArrFieldNames[i]} LIKE '%{strArrFieldValues[i]}%'"));
            }
            return new GroupOperator(type, filterCollection);
        }

        /// <summary>
        /// 设置某列过滤
        /// </summary>
        /// <param name="gridColumn">数据列</param>
        /// <param name="strSearchText">过滤值</param>
        public static void SetColumnFilter(GridColumn gridColumn, string strSearchText)
        {
            gridColumn.FilterInfo = new ColumnFilterInfo($"[{gridColumn.FieldName}] LIKE '%{strSearchText}%'");
        }

        /// <summary>
        /// 为GridLookUpEdit设置数据源（RepositoryItemGridLookUpEdit）
        /// </summary>
        /// <param name="gle">RepositoryItemGridLookUpEdit</param>
        /// <param name="listData">数据源</param>
        /// <param name="valueMember">实际列名</param>
        /// <param name="displayMember">显示列名</param>
        /// <param name="valueMemberLength">实际列名宽度</param>
        /// <param name="displayMemberLength">显示列名宽度</param>
        /// <param name="bAllowEdit">是否允许编辑</param>
        /// <returns></returns>
        public static void SetGridLookUpEditDataSource<T>(RepositoryItemGridLookUpEdit gle, List<T> listData, string valueMember = "CODE", string displayMember = "NAME", int valueMemberLength = 80, int displayMemberLength = 180, Boolean bAllowEdit = false)
        {
            gle.CloseUpKey = new KeyShortcut(Keys.F9);
            GridOptionsView gov = gle.View.OptionsView;
            gov.ShowIndicator = false;
            gov.ColumnAutoWidth = false;

            gov.BestFitMode = GridBestFitMode.Full;
            gle.ShowFooter = false;
            gle.NullText = string.Empty;
            GridColumnCollection gcc = gle.View.Columns;
            gcc.Clear();
            GridColumn col = gcc.AddField(valueMember);
            col.VisibleIndex = 0;
            col.Width = valueMemberLength;
            col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            gcc.Add(col);
            if (displayMember != valueMember)
            {
                col = gcc.AddField(displayMember);
                col.VisibleIndex = 1;
                col.Width = displayMemberLength;
                gcc.Add(col);
            }
            else
            {
                gov.ColumnAutoWidth = true;
            }
            gle.ValueMember = valueMember;
            gle.DisplayMember = displayMember;
            gle.NullText = "";
            if (bAllowEdit)
                gle.TextEditStyle = TextEditStyles.Standard;
            else
                gle.TextEditStyle = TextEditStyles.DisableTextEditor;
            gle.DataSource = listData;
        }

        /// <summary>
        /// 为GridLookUpEdit设置数据源（RepositoryItemGridLookUpEdit）
        /// </summary>
        /// <param name="gle">RepositoryItemGridLookUpEdit</param>
        /// <param name="dataTable">数据源</param>
        /// <param name="valueMember">实际列名</param>
        /// <param name="displayMember">显示列名</param>
        /// <param name="valueMemberLength">实际列名宽度</param>
        /// <param name="displayMemberLength">显示列名宽度</param>
        /// <param name="bAllowEdit">是否允许编辑</param>
        /// <returns></returns>
        public static void SetGridLookUpEditDataSource(RepositoryItemGridLookUpEdit gle, DataTable dataTable, string valueMember = "CODE", string displayMember = "NAME", int valueMemberLength = 80, int displayMemberLength = 180, Boolean bAllowEdit = false)
        {
            gle.CloseUpKey = new KeyShortcut(Keys.F9);
            GridOptionsView gov = gle.View.OptionsView;
            gov.ShowIndicator = false;
            gov.ColumnAutoWidth = false;
            gov.BestFitMode = GridBestFitMode.Full;
            gle.ShowFooter = false;
            gle.NullText = string.Empty;
            GridColumnCollection gcc = gle.View.Columns;
            gcc.Clear();
            GridColumn col = gcc.AddField(valueMember);
            col.VisibleIndex = 0;
            col.Width = valueMemberLength;
            col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            gcc.Add(col);
            if (displayMember != valueMember)
            {
                col = gcc.AddField(displayMember);
                col.VisibleIndex = 1;
                col.Width = displayMemberLength;
                gcc.Add(col);
            }
            else
            {
                gov.ColumnAutoWidth = true;
            }
            gle.ValueMember = valueMember;
            gle.DisplayMember = displayMember;
            gle.NullText = "";
            if (bAllowEdit)
                gle.TextEditStyle = TextEditStyles.Standard;
            else
                gle.TextEditStyle = TextEditStyles.DisableTextEditor;
            gle.DataSource = dataTable;
        }

        /// <summary>
        /// 为某列创建GridLookUpEdit及设置数据源（RepositoryItemGridLookUpEdit）
        /// </summary>
        /// <param name="gridColumn">GridView的列</param>
        /// <param name="listData">数据源</param>
        /// <param name="valueMember">实际列名</param>
        /// <param name="displayMember">显示列名</param>
        /// <param name="valueMemberLength">实际列名宽度</param>
        /// <param name="displayMemberLength">显示列名宽度</param>
        /// <param name="bAllowEdit">是否允许编辑</param>
        /// <returns></returns>
        public static RepositoryItemGridLookUpEdit SetRepositoryItemGridLookUpEdit<T>(GridColumn gridColumn, List<T> listData, string valueMember = "CODE", string displayMember = "NAME", int valueMemberLength = 80, int displayMemberLength = 180, Boolean bAllowEdit = false)
        {
            RepositoryItemGridLookUpEdit gle = new RepositoryItemGridLookUpEdit();
            gle.CloseUpKey = new KeyShortcut(Keys.F9);
            GridOptionsView gov = gle.View.OptionsView;
            gov.ShowIndicator = false;
            gov.ColumnAutoWidth = false;
            gov.BestFitMode = GridBestFitMode.Full;
            gle.ShowFooter = false;
            gle.NullText = string.Empty;
            GridColumnCollection gcc = gle.View.Columns;
            gcc.Clear();
            GridColumn col = gcc.AddField(valueMember);
            col.VisibleIndex = 0;
            col.Width = valueMemberLength;
            col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            gcc.Add(col);
            if (displayMember != valueMember)
            {
                col = gcc.AddField(displayMember);
                col.VisibleIndex = 1;
                col.Width = displayMemberLength;
                gcc.Add(col);
            }
            else
            {
                gov.ColumnAutoWidth = true;
            }
            gle.ValueMember = valueMember;
            gle.DisplayMember = displayMember;
            gle.NullText = "";

            //是否允许编辑
            if (bAllowEdit)
                gle.TextEditStyle = TextEditStyles.Standard;
            else
                gle.TextEditStyle = TextEditStyles.DisableTextEditor;

            gle.DataSource = listData;
            gridColumn.ColumnEdit = gle;
            return gle;
        }

        /// <summary>
        /// 为某列创建GridLookUpEdit及设置数据源（RepositoryItemGridLookUpEdit）
        /// </summary>
        /// <param name="gridColumn">GridView的列</param>
        /// <param name="dataTable">数据源</param>
        /// <param name="valueMember">实际列名</param>
        /// <param name="displayMember">显示列名</param>
        /// <param name="valueMemberLength">实际列名宽度</param>
        /// <param name="displayMemberLength">显示列名宽度</param>
        /// <param name="bAllowEdit">允许编辑</param>
        /// <returns></returns>
        public static RepositoryItemGridLookUpEdit SetRepositoryItemGridLookUpEdit(GridColumn gridColumn, DataTable dataTable, string valueMember = "CODE", string displayMember = "NAME", int valueMemberLength = 80, int displayMemberLength = 180, Boolean bAllowEdit = false)
        {
            RepositoryItemGridLookUpEdit gle = new RepositoryItemGridLookUpEdit();
            gle.CloseUpKey = new KeyShortcut(Keys.F9);
            GridOptionsView gov = gle.View.OptionsView;
            gov.ShowIndicator = false;
            gov.ColumnAutoWidth = false;
            gov.BestFitMode = GridBestFitMode.Full;
            gle.ShowFooter = false;
            gle.NullText = string.Empty;
            GridColumnCollection gcc = gle.View.Columns;
            gcc.Clear();
            GridColumn col = gcc.AddField(valueMember);
            col.VisibleIndex = 0;
            col.Width = valueMemberLength;
            col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            gcc.Add(col);
            if (displayMember != valueMember)
            {
                col = gcc.AddField(displayMember);
                col.VisibleIndex = 1;
                col.Width = displayMemberLength;
                gcc.Add(col);
            }
            else
            {
                gov.ColumnAutoWidth = true;
            }
            gle.ValueMember = valueMember;
            gle.DisplayMember = displayMember;
            gle.NullText = "";
            //是否允许编辑
            if (bAllowEdit)
                gle.TextEditStyle = TextEditStyles.Standard;
            else
                gle.TextEditStyle = TextEditStyles.DisableTextEditor;

            gle.DataSource = dataTable;
            gridColumn.ColumnEdit = gle;
            return gle;
        }

        /// <summary>
        /// 创建GridLookUpEdit及设置数据源（RepositoryItemGridLookUpEdit）
        /// </summary>
        /// <param name="listData">数据源</param>
        /// <param name="valueMember">实际列名</param>
        /// <param name="displayMember">显示列名</param>
        /// <param name="bAllowEdit">是否允许编辑</param>
        /// <returns></returns>
        public static RepositoryItemGridLookUpEdit GetRepositoryItemGridLookUpEdit<T>(List<T> listData, string valueMember = "CODE", string displayMember = "NAME", Boolean bAllowEdit = false)
        {
            RepositoryItemGridLookUpEdit gle = new RepositoryItemGridLookUpEdit();
            gle.CloseUpKey = new KeyShortcut(Keys.F9);

            GridOptionsView gov = gle.View.OptionsView;
            gov.ShowIndicator = false;
            gov.ColumnAutoWidth = false;
            gov.BestFitMode = GridBestFitMode.Full;
            gle.ShowFooter = false;
            gle.NullText = string.Empty;
            gle.ValueMember = valueMember;
            gle.DisplayMember = displayMember;

            GridColumnCollection gcc = gle.View.Columns;
            GridColumn col = gcc.AddField(valueMember);
            col.VisibleIndex = 0;
            col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            gcc.Add(col);
            if (displayMember != valueMember)
            {
                col = gcc.AddField(displayMember);
                col.VisibleIndex = 1;
                gcc.Add(col);
            }
            else
            {
                gov.ColumnAutoWidth = true;
            }

            //是否允许编辑
            if (bAllowEdit)
                gle.TextEditStyle = TextEditStyles.Standard;
            else
                gle.TextEditStyle = TextEditStyles.DisableTextEditor;

            gle.DataSource = listData;
            return gle;
        }

        /// <summary>
        /// 创建GridLookUpEdit及设置数据源（RepositoryItemGridLookUpEdit）
        /// </summary>
        /// <param name="listData">数据源</param>
        /// <param name="isShowHeader">显示表头</param>
        /// <param name="valueDisplayMember">唯一列名</param>
        /// <param name="bAllowEdit">是否允许编辑</param>
        /// <returns></returns>
        public static RepositoryItemGridLookUpEdit GetRepositoryItemGridLookUpEdit<T>(List<T> listData, bool isShowHeader, string valueDisplayMember = "NAME", Boolean bAllowEdit = false)
        {
            RepositoryItemGridLookUpEdit gle = new RepositoryItemGridLookUpEdit();
            gle.CloseUpKey = new KeyShortcut(Keys.F9);

            GridOptionsView gov = gle.View.OptionsView;
            gov.ShowColumnHeaders = isShowHeader;
            gov.ShowIndicator = false;
            gov.BestFitMode = GridBestFitMode.Full;
            gle.ShowFooter = false;
            gle.NullText = string.Empty;
            gle.ValueMember = valueDisplayMember;
            gle.DisplayMember = valueDisplayMember;
            gle.DataSource = listData;
            //是否允许编辑
            if (bAllowEdit)
                gle.TextEditStyle = TextEditStyles.Standard;
            else
                gle.TextEditStyle = TextEditStyles.DisableTextEditor;

            return gle;
        }

        /// <summary>
        /// 设置显示行号
        /// </summary>
        /// <param name="sender">object</param>
        /// <param name="e">RowIndicatorCustomDrawEventArgs</param>
        public static void CustomDrawRowIndicator(object sender, DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventArgs e)
        {
            if (e.Info.IsRowIndicator)
            {
                if (e.RowHandle >= 0)
                {
                    e.Info.DisplayText = (e.RowHandle + 1).ToString();   //行号
                }
            }
            else
            {
                e.Info.DisplayText = "No.";   //行号列标题
            }
            e.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center; //对齐方式
        }

        /// <summary>
        /// 设置焦点行背景蓝色，前景白色， 不可编辑列设置为灰色
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        public static void RowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {
            //设置焦点行背景蓝色，前景白色
            GridView gv = (sender as GridView);
            int[] rows = gv.GetSelectedRows();
            int focusedRow = gv.FocusedRowHandle;
            int i, row = e.RowHandle;
            AppearanceObject ape = e.Appearance;

            if (row == focusedRow)
            {
                i = Array.IndexOf<int>(rows, focusedRow);
                if (i == 0)
                {
                    ape.BackColor = Color.Blue;
                    ape.ForeColor = Color.White;
                    return;
                }
            }
            else
            {
                i = Array.IndexOf<int>(rows, row);
                if (i >= 0)
                {
                    ape.BackColor = Color.Blue;
                    ape.ForeColor = Color.FromArgb(90, 200, 250);
                    return;
                }
            }

            var o = e.Column.OptionsColumn;
            if (o.ReadOnly || o.AllowEdit == false || o.AllowFocus == false)
            {
                //不可编辑列设置为灰色
                ape.BackColor = Color.LightGray;
            }
        }

        /// <summary>
        /// GridView回车
        /// </summary>
        private static void EnterKeyDownAsTab(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                SendKeys.Send("{TAB}");
            }
        }

        /// <summary>
        /// 支票信息输入类
        /// </summary>
        public class UCCheckInfoInput : XtraUserControl
        {
            /// <summary>
            /// 银行输入
            /// </summary>
            public TextEdit txtBank = new TextEdit();
            /// <summary>
            /// 支票号输入
            /// </summary>
            public TextEdit txtCheckNo = new TextEdit();

            /// <summary>
            /// 支票信息输入
            /// </summary>
            public UCCheckInfoInput()
            {
                LayoutControl mylayoutControl = new LayoutControl();
                mylayoutControl.Dock = DockStyle.Fill;

                txtBank.Properties.MaxLength = 30;
                txtCheckNo.Properties.MaxLength = 20;

                SeparatorControl separatorControl = new SeparatorControl();
                mylayoutControl.AddItem("银行名称：", txtBank).TextVisible = true;
                mylayoutControl.AddItem("  支票号：", txtCheckNo).TextVisible = true;
                this.Controls.Add(mylayoutControl);
                this.Height = 70;
                this.Dock = DockStyle.Top;

            }
        }

        /// <summary>
        /// 担保人输入类
        /// </summary>
        public class UCGuarantorInput : XtraUserControl
        {
            /// <summary>
            /// 担保人
            /// </summary>
            public TextEdit txtGuarantor = new TextEdit();
            /// <summary>
            /// 担保人ID
            /// </summary>
            public TextEdit txtGuarantorID = new TextEdit();
            /// <summary>
            /// 担保人单位
            /// </summary>
            public TextEdit txtGuarantorUnit = new TextEdit();

            /// <summary>
            /// 担保信息
            /// </summary>
            public UCGuarantorInput()
            {
                LayoutControl mylayoutControl = new LayoutControl();
                mylayoutControl.Dock = DockStyle.Fill;

                txtGuarantor.Properties.MaxLength = 20;
                txtGuarantorID.Properties.MaxLength = 18;
                txtGuarantorUnit.Properties.MaxLength = 50;

                SeparatorControl separatorControl = new SeparatorControl();
                mylayoutControl.AddItem("    担保人：", txtGuarantor).TextVisible = true;
                mylayoutControl.AddItem("担保身份证：", txtGuarantorID).TextVisible = true;
                mylayoutControl.AddItem("担保人单位：", txtGuarantorUnit).TextVisible = true;
                this.Controls.Add(mylayoutControl);
                this.Height = 100;
                this.Dock = DockStyle.Top;
            }
        }

        /// <summary>
        /// 单行信息输入类
        /// </summary>
        public class UCSingleInfoInput : XtraUserControl
        {
            /// <summary>
            /// 文本输入控件
            /// </summary>
            public TextEdit txtInput = new TextEdit();

            /// <summary>
            /// 构造函数
            /// </summary>
            /// <param name="strInputTitle">输入的说明</param>
            /// <param name="iMaxLength">最大长度</param>
            /// <param name="bPassWord">密码标志</param>
            public UCSingleInfoInput(string strInputTitle, int iMaxLength = 20, bool bPassWord = false)
            {
                LayoutControl mylayoutControl = new LayoutControl();
                mylayoutControl.Dock = DockStyle.Fill;

                txtInput.Properties.MaxLength = iMaxLength;
                txtInput.Properties.UseSystemPasswordChar = bPassWord;

                SeparatorControl separatorControl = new SeparatorControl();
                mylayoutControl.AddItem(strInputTitle, txtInput).TextVisible = true;
                this.Controls.Add(mylayoutControl);
                this.Height = 50;
                this.Dock = DockStyle.Top;
            }
        }

        /// <summary>
        /// 两行信息输入类
        /// </summary>
        public class UCDoubleInfoInput : XtraUserControl
        {
            /// <summary>
            /// 信息输入1
            /// </summary>
            public TextEdit txtInput1 = new TextEdit();
            /// <summary>
            /// 信息输入2
            /// </summary>
            public TextEdit txtInput2 = new TextEdit();

            /// <summary>
            /// 构造函数
            /// </summary>
            /// <param name="strInputTitle1">输入的说明1</param>
            /// <param name="strInputTitle2">输入的说明2</param>
            /// <param name="iMaxLength1">最大长度1</param>
            /// <param name="iMaxLength2">最大长度2</param>
            public UCDoubleInfoInput(string strInputTitle1, string strInputTitle2, int iMaxLength1 = 20, int iMaxLength2 = 20)
            {
                LayoutControl mylayoutControl = new LayoutControl();
                mylayoutControl.Dock = DockStyle.Fill;

                txtInput1.Properties.MaxLength = iMaxLength1;
                txtInput2.Properties.MaxLength = iMaxLength2;

                SeparatorControl separatorControl = new SeparatorControl();
                mylayoutControl.AddItem(strInputTitle1, txtInput1).TextVisible = true;
                mylayoutControl.AddItem(strInputTitle2, txtInput2).TextVisible = true;
                this.Controls.Add(mylayoutControl);
                this.Height = 70;
                this.Dock = DockStyle.Top;
            }
        }

        /// <summary>
        /// 三行信息输入类
        /// </summary>
        public class UCTriInfoInput : XtraUserControl
        {
            /// <summary>
            /// 信息输入1
            /// </summary>
            public TextEdit txtInput1 = new TextEdit();
            /// <summary>
            /// 信息输入2
            /// </summary>
            public TextEdit txtInput2 = new TextEdit();
            /// <summary>
            /// 信息输入3
            /// </summary>
            public TextEdit txtInput3 = new TextEdit();

            /// <summary>
            /// 构造函数
            /// </summary>
            /// <param name="strInputTitle1">输入的说明1</param>
            /// <param name="strInputTitle2">输入的说明2</param>
            /// <param name="strInputTitle3">输入的说明3</param>
            /// <param name="iMaxLength1">最大长度1</param>
            /// <param name="iMaxLength2">最大长度2</param>
            /// <param name="iMaxLength3">最大长度3</param>
            public UCTriInfoInput(string strInputTitle1, string strInputTitle2, string strInputTitle3, int iMaxLength1 = 20, int iMaxLength2 = 20, int iMaxLength3 = 20)
            {
                LayoutControl mylayoutControl = new LayoutControl();
                mylayoutControl.Dock = DockStyle.Fill;

                txtInput1.Properties.MaxLength = iMaxLength1;
                txtInput2.Properties.MaxLength = iMaxLength2;
                txtInput3.Properties.MaxLength = iMaxLength3;

                SeparatorControl separatorControl = new SeparatorControl();
                mylayoutControl.AddItem(strInputTitle1, txtInput1).TextVisible = true;
                mylayoutControl.AddItem(strInputTitle2, txtInput2).TextVisible = true;
                mylayoutControl.AddItem(strInputTitle3, txtInput3).TextVisible = true;
                this.Controls.Add(mylayoutControl);
                this.Height = 95;
                this.Dock = DockStyle.Top;
            }
        }

        /// <summary>
        /// 打开等待提示
        /// </summary>
        public static void OpenWait()
        {
            OpenWait("系统提示", "正在准备数据，请等待...");
        }

        /// <summary>
        /// 打开等待提示
        /// </summary>
        /// <param name="strShowMsg"></param>
        /// <param name="strTitle"></param>
        public static void OpenWait(string strShowMsg, string strTitle = "系统提示")
        {
            try
            {
                SplashScreenManager.ShowDefaultWaitForm(strTitle, $"\r\n{strShowMsg}");
            }
            catch (Exception)
            {
                try
                {
                    SplashScreenManager.Default.SetWaitFormDescription($"\r\n{strShowMsg}");
                }
                catch (Exception)
                {
                }
            }
        }

        /// <summary>
        /// 关闭等待提示
        /// </summary>
        public static void CloseWait()
        {
            try
            {
                SplashScreenManager.CloseDefaultWaitForm();
            }
            catch (Exception)
            {
            }
        }

        private const int WM_SETREDRAW = 0xB;
        private const int FALSE = 0x0000;
        private const int TRUE = 0x0001;

        /// <summary>
        /// 发送消息
        /// </summary>
        /// <param name="hwnd"></param>
        /// <param name="wMsg"></param>
        /// <param name="wParam"></param>
        /// <param name="lParam"></param>
        /// <returns></returns>
        [DllImport("user32")] private static extern int SendMessage(IntPtr hwnd, int wMsg, int wParam, IntPtr lParam);

        /// <summary>
        /// 控件重画
        /// </summary>
        /// <param name="hwnd"></param>
        /// <param name="bRedraw"></param>
        public static void SetRedraw(IntPtr hwnd, Boolean bRedraw)
        {
            if (bRedraw)
                SendMessage(hwnd, WM_SETREDRAW, FALSE, IntPtr.Zero);
            else
                SendMessage(hwnd, WM_SETREDRAW, TRUE, IntPtr.Zero);
        }


    }
}
