﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{480DE8D0-5D9C-41DF-8FD6-8772E5655552}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Utility</RootNamespace>
    <AssemblyName>Utility</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\..\TjhisPlatSource\TJHisPlatEXE\Client\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
    <DocumentationFile>..\..\..\TjhisPlatSource\TJHisPlatEXE\Client\Utility.xml</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\DLL\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Devart.Data, Version=5.0.2375.0, Culture=neutral, PublicKeyToken=09af7300eec23701, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\TjhisPlatSource\TJHisPlatEXE\Client\Devart.Data.dll</HintPath>
    </Reference>
    <Reference Include="Devart.Data.Oracle, Version=9.11.951.0, Culture=neutral, PublicKeyToken=09af7300eec23701, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\TjhisPlatSource\TJHisPlatEXE\Client\Devart.Data.Oracle.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess, Version=4.122.19.1, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\TjhisPlatSource\TJHisPlatEXE\Client\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Data.OracleClient">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml">
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ConfigHelper.cs" />
    <Compile Include="Gloobal.cs" />
    <Compile Include="LogFile.cs" />
    <Compile Include="OracleODP\OracleBaseClass.cs" />
    <Compile Include="OracleODP\OracleBase_Settings1.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>OracleBase_Settings.settings</DependentUpon>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SystemOperation\SystemTime.cs" />
    <Compile Include="SystemOperation\TJLicensingOperation.cs" />
    <Compile Include="TJ_DataBase_Operation_Factory.cs" />
    <Compile Include="TJ_DataBase_Operation_Interface.cs" />
    <Compile Include="UntilityConstant.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="数据库操作及相关基本功能-内附详情.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="OracleODP\OracleBase_Settings.settings">
      <Generator>PublicSettingsSingleFileGenerator</Generator>
      <LastGenOutput>OracleBase_Settings1.Designer.cs</LastGenOutput>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>