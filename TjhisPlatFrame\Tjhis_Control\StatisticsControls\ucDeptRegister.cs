﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace Tjhis.Controls.StatisticsControls
{
    public partial class ucDeptRegister : RadiusUserControl
    {
        public DeptRegisterData RegisterData { get; set; }
        public ucDeptRegister()
        {
            InitializeComponent();
        }
        protected override void SetContentSize()
        {
            this.TablePanelMain.Width = this.Width;
            this.TablePanelMain.Height = this.Height - TopBannerHeight;
        }
        

        public override void InitData()
        {
            if (null != RegisterData)
            {
                this.ChartRegister.DataSource = RegisterData.Data;
            }
        }

        public class DeptRegisterData
        {
            public DataTable Data { get; set; }
            public void CreatTestData()
            {
                DataTable data = CreateDataBase();
                data.DefaultView.Sort = "SUM ASC";
                data.Rows.Add("内科", 500);
                data.Rows.Add("儿科", 100);
                data.Rows.Add("皮肤科", 700);
                data.Rows.Add("外科", 200);
                data.Rows.Add("口腔科", 300);
                data.Rows.Add("内科1", 500);
                data.Rows.Add("儿科1", 100);
                data.Rows.Add("皮肤科1", 700);
                data.Rows.Add("外科1", 200);
                data.Rows.Add("口腔科1", 300);
                this.Data = data;
            }

            public DataTable CreateDataBase()
            {
                DataTable data = new DataTable();
                data.Columns.AddRange(new DataColumn[] {
                    new DataColumn("DEPT_NAME", typeof(string)),
                    new DataColumn("SUM",typeof(decimal))
                });
                return data;
            }
        }
    }
}
