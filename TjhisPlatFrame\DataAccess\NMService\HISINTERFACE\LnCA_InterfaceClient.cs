﻿/*-----------------------------------------------------------------------
 * 类名称    ：LnCA_InterfaceClient
 * 类描述    ：
 * 创建人    ：梁吉lions
 * 创建时间  ：2017/5/6 13:20:02
 * 修改人    ：
 * 修改时间  ：
 * 修改备注  ：
 * 版本      ：
 * ----------------------------------------------------------------------
 */
using System;
using System.Text;
using System.Collections.Generic;
using System.Data;
using Model;
using Utility.OracleODP;

namespace NM_Service.NMService
{

    /// <summary>
    /// 辽宁CA接口实现类
    /// </summary> 	

    public class LnCA_InterfaceClient : INMService.ILNCA_INTERFACE, IDisposable
    {
        #region  Method
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists_LNCA_INTERFACE(string LNCA_ID)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.LNCA_INTERFACE_Dao().Exists(LNCA_ID, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }

        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add_LNCA_INTERFACE(Model.LNCA_INTERFACE model)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.LNCA_INTERFACE_Dao().Add(model, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update_LNCA_INTERFACE(Model.LNCA_INTERFACE model)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.LNCA_INTERFACE_Dao().Update(model, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool UpdateByHisID_LNCA_INTERFACE(Model.LNCA_INTERFACE model)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.LNCA_INTERFACE_Dao().UpdateByHisID(model, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }
        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete_LNCA_INTERFACE(string LNCA_ID)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.LNCA_INTERFACE_Dao().Delete(LNCA_ID, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.LNCA_INTERFACE GetModel_LNCA_INTERFACE(string LNCA_ID)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    Model.LNCA_INTERFACE ret = new OracleDAL.LNCA_INTERFACE_Dao().GetModel(LNCA_ID, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList_All_LNCA_INTERFACE(string strWhere)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    DataSet ret = new OracleDAL.LNCA_INTERFACE_Dao().GetList(strWhere, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>   
        public DataSet GetList_LNCA_INTERFACE(int startIndex, int endIndex, string strWhere, string filedOrder)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    DataSet ret = new OracleDAL.LNCA_INTERFACE_Dao().GetList(startIndex, endIndex, strWhere, filedOrder, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>  
        public System.Collections.ObjectModel.ObservableCollection<Model.LNCA_INTERFACE> GetObservableCollection_All_LNCA_INTERFACE(string strWhere)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    System.Collections.ObjectModel.ObservableCollection<Model.LNCA_INTERFACE> ret = new OracleDAL.LNCA_INTERFACE_Dao().GetObservableCollection(strWhere, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 获得ObservableCollection根据分页获得数据列表
        /// </summary>  
        public System.Collections.ObjectModel.ObservableCollection<Model.LNCA_INTERFACE> GetObservableCollection_LNCA_INTERFACE(int startIndex, int endIndex, string strWhere, string filedOrder)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    System.Collections.ObjectModel.ObservableCollection<Model.LNCA_INTERFACE> ret = new OracleDAL.LNCA_INTERFACE_Dao().GetObservableCollection(startIndex, endIndex, strWhere, filedOrder, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }
        #endregion
        #region IDisposable 成员

        /// <summary>
        /// 实现IDisposable接口
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            //.NET Framework 类库
            // GC..::.SuppressFinalize 方法
            //请求系统不要调用指定对象的终结器。
            GC.SuppressFinalize(this);
        }
        /// <summary>
        /// 虚方法，可供子类重写
        /// </summary>
        /// <param name="disposing"></param>
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Release managed resources
            }
        }
        /// <summary>
        /// 析构函数
        /// 当客户端没有显示调用Dispose()时由GC完成资源回收功能
        /// </summary>
        ~LnCA_InterfaceClient()
        {
            Dispose();
        }

        #endregion

    }
}