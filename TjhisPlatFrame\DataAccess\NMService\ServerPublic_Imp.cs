﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using OracleDAL;
using Utility.OracleODP;
using System.Data;
using System.Collections;
using Oracle.ManagedDataAccess.Client;

namespace NM_Service.NMService
{
    public class ServerPublicClient : INMService.IServerPublic,IDisposable
    {
        #region IServerPublic 成员

        #region --<---add 20220303------
        /// <summary>
        /// 查询sql语句  可以传入指定返回的 TableName
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="strTableName"></param>
        /// <param name="blnWithKey"></param>
        /// <returns></returns>
        public System.Data.DataSet GetDataBySql(string sql,string strTableName="",Boolean blnWithKey=false)
        {
            //return new ServerPublic_Dao().GetDataBySql(sql);
            DataSet ds = new ServerPublic_Dao().GetDataBySql(sql);
            if (!string.IsNullOrEmpty(strTableName))
            {
                ds.Tables[0].TableName = strTableName;
            }            
            return ds;
        }

        /// <summary>
        /// 执行SQL ，成功提交，失败回滚
        /// </summary>
        /// <param name=" SQL">SQL语句List</param>
        /// <returns></returns>
        public int ExecuteSQL(string strSQL)
        {
            if (string.IsNullOrEmpty(strSQL)) return 0;
            try
            {
                int iRowCount = 0;
                using (OracleBaseClass db = new OracleBaseClass())
                {
                    if (db.OpenDB())
                    {
                        int a = db.ExecuteATransaction(strSQL);
                        if (a>0)
                        {
                            iRowCount = 1;
                        }
                        db.CloseDB();
                    }
                }
                return iRowCount;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        /// 执行只有一个结果的SQL语句
        /// </summary>
        /// <param name="strSQL">SQL语句</param>
        /// <returns></returns>
        public string ExecuteScalarStr(string strSQL)
        {
            return GetSingleValue(strSQL);       
            //return new ServerPublic_Dao().GetSingleValue(strSql);
        }

        public int ExecuteSqlByParameter(string strSQL,List<Oracle.ManagedDataAccess.Client.OracleParameter> Pars)
        {
            if (string.IsNullOrEmpty(strSQL)) return 0;
            try
            {
                int iRowCount = 0;
                using (OracleBaseClass db = new OracleBaseClass())
                {
                    if (db.OpenDB())
                    {
                        int a = db.ExecuteATransaction(strSQL, Pars);
                        if (a > 0)
                        {
                            iRowCount = 1;
                        }
                        db.CloseDB();
                    }
                }
                return iRowCount;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString());
            }
        }
        /// <summary>
        /// 执行Sql语句, 成功没有返回值 失败返回的是定义好传入的报错信息
        /// </summary>
        /// <param name="dicSQL">sql语句Dictionary 第一项为SQL语句, 第二项为出错提示</param>
        /// <returns>执行失败时的错误提示</returns>
        public string ExecuteNoQuery(Dictionary<string, string> dicSQL)
        {
            string strErrMsg = string.Empty;

            // 条件检查
            if (dicSQL == null || dicSQL.Count == 0)
                return strErrMsg;

            // 执行Sql语句
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                if (db.OpenDB())
                {                  
                    try
                    {
                        foreach (var item in dicSQL)
                        {
                            if (string.IsNullOrEmpty(item.Key)) continue;

                            strErrMsg = item.Value;
                            db.ExecuteATransaction(item.Key);
                        }                    
                        db.CloseDB();
                        strErrMsg = string.Empty;
                    }
                    catch (Exception ex)
                    {                       
                        db.CloseDB();
                        strErrMsg = (strErrMsg.Length > 0 ? strErrMsg : ex.Message);
                    }
                }
            }            
            return strErrMsg;
        }

        /// <summary>
        /// 批量SQL执行 返回值大于0 为成功
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public int ExecuteSqlArray(ArrayList arrlistSQL)
        {
            if (arrlistSQL == null || arrlistSQL.Count < 1) return 0;

            int iRowCount = 0;
            Dictionary<string, string> idc = new Dictionary<string, string>();
            foreach (string strSQL in arrlistSQL)
            {
                if (string.IsNullOrEmpty(strSQL)) continue;

                iRowCount += ExecuteSQL(strSQL);
            }
            return iRowCount;
        }

        #endregion -->--add 20220303-----

        /// <summary>
        /// 传入sql语句 查询数据 返回的是 DataSet
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public System.Data.DataSet GetDataBySql(string sql)
        {
            return new ServerPublic_Dao().GetDataBySql(sql); 
        }
        /// <summary>
        /// 传入sql语句 查询数据 返回的是 DataSet
        /// </summary>
        /// <param name="strSql"></param>
        /// <returns></returns>
        public System.Data.DataSet GetList(string strSql)
        {
            return new ServerPublic_Dao().GetDataBySql(strSql);
        }
        /// <summary>
        /// 存储过程调用方法
        /// </summary>
        /// <param name="procedureName"></param>
        /// <param name="paramKey"></param>
        /// <param name="paramValue"></param>
        /// <param name="tableName"></param>
        /// <param name="sql"></param>
        /// <returns></returns>
        public System.Data.DataSet GetProcedureDataSet(string procedureName, List<string> paramKey, List<string> paramValue, string tableName, string sql)
        {
            return new ServerPublic_Dao().GetProcedureDataSet(procedureName, paramKey, paramValue, tableName, sql);
        }
        /// <summary>
        /// 门诊收费对应的存储过程调用发法 也可以用作它用但是格式要跟收费的存储过程出参格式一致
        /// </summary>
        /// <param name="procedureName"></param>
        /// <param name="paramKey"></param>
        /// <param name="paramValue"></param>
        /// <param name="para1"></param>
        /// <param name="para2"></param>
        /// <param name="para3"></param>
        /// <returns></returns>
        public bool GetPubicProcedureDs(string procedureName, List<string> paramKey, List<string> paramValue, ref string para1, ref String para2, ref DataTable para3)
        {
            return new ServerPublic_Dao().GetPubicProcedureDs(procedureName, paramKey, paramValue, ref para1, ref para2, ref para3);
        }
        /// <summary>
        /// 执行只有一个结果的sql语句 ExecuteScalarStr 方法里面实际调用的也是它
        /// </summary>
        /// <param name="strSql"></param>
        /// <returns></returns>
        public string GetSingleValue(string strSql)
        {
            return new ServerPublic_Dao().GetSingleValue(strSql);
        }
        /// <summary>
        /// 获取日期格式的系统时间
        /// </summary>
        /// <returns></returns>
        public DateTime GetSysDate()
        {
            return new ServerPublic_Dao().GetSysDate();
        }
        /// <summary>
        /// 传入表名和 where 条件获取查询数据 返回dataset 
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public System.Data.DataSet GetTableData(string tableName, string strWhere)
        {
            return new ServerPublic_Dao().GetTableData(tableName,strWhere);
        }
        /// <summary>
        ///  ArrayList 集合形式的DataSet 数据进行统一保存 但是需要给对应的 TableName属性指定实际的数据库表名 不能用默认的Table[0]
        /// </summary>
        /// <param name="dsList"></param>
        /// <returns></returns>
        public bool SaveTablesData(System.Collections.ArrayList dsList)
        {
            foreach (DataSet ds in dsList)
            {
                foreach (DataTable dt in ds.Tables)
                {
                    string updateSql = GetUpdateSql(dt, dt.TableName);
                    if (string.IsNullOrEmpty(updateSql))
                    {
                        Utility.LogFile.WriteLogAuto("取表名【"+dt.TableName+"】对应更新语句失败！", "ServerPublicImp");
                        return false;
                    }
                    if (dt.ExtendedProperties.Contains("UpdateSQL"))
                    {
                        dt.ExtendedProperties["UpdateSQL"] = updateSql;
                    }
                    else
                    {
                        dt.ExtendedProperties.Add("UpdateSQL", updateSql);
                    }
                }
            } 
            return new ServerPublic_Dao().SaveTablesData(dsList);
            //using (OracleBaseClass db = new OracleBaseClass())
            //{
            //    if (db.OpenDB())
            //    {
            //        db.BeginTransaction();
            //        bool ret =new ServerPublic_Dao().SaveTablesData(dsList,db);
            //        if (!ret)
            //        {
            //            db.RollbackTransaction();
            //        }
            //        else
            //        {
            //            db.CommitTransaction();
            //        }
            //        db.CloseDB();
            //        return ret;
            //    }
            //    else
            //    {
            //        return false;
            //    }
            //}
        }
        /// <summary>
        /// 保存数据集合，如果取更新语句空，返回-9   需要注意的是此方法每一个datatable的TableName属性需要指定保存的对应的表名  
        /// </summary>
        /// <param name="dsList"></param>
        /// <param name="drsChange"></param>
        /// <returns></returns>
        public int SaveDsArray(System.Collections.ArrayList dsList, List<DataRow> drsChange = null)
        {
            foreach (DataSet ds in dsList)
            {
                foreach (DataTable dt in ds.Tables)
                {
                    string updateSql = GetUpdateSql(dt, dt.TableName);
                    if (string.IsNullOrEmpty(updateSql))
                    {
                        Utility.LogFile.WriteLogAuto("取表名【" + dt.TableName + "】对应更新语句失败！", "ServerPublicImp");
                        return -9;
                    }
                    if (dt.ExtendedProperties.Contains("UpdateSQL"))
                    {
                        dt.ExtendedProperties["UpdateSQL"] = updateSql;
                    }
                    else
                    {
                        dt.ExtendedProperties.Add("UpdateSQL", updateSql);
                    }
                }
            }
            return new ServerPublic_Dao().SaveDsArry(dsList);
            //using (OracleBaseClass db = new OracleBaseClass())
            //{
            //    if (db.OpenDB())
            //    {
            //        db.BeginTransaction();
            //        bool ret =new ServerPublic_Dao().SaveTablesData(dsList,db);
            //        if (!ret)
            //        {
            //            db.RollbackTransaction();
            //        }
            //        else
            //        {
            //            db.CommitTransaction();
            //        }
            //        db.CloseDB();
            //        return ret;
            //    }
            //    else
            //    {
            //        return false;
            //    }
            //}
        }
        /// <summary>
        /// 对dataset 数据进行保存 可以多个datatable 一起add到一个dataset进行保存 
        /// </summary>
        /// <param name="dataSet"></param>
        /// <returns></returns>
        public int SaveDataSet(System.Data.DataSet dataSet)
        {
            return new ServerPublic_Dao().SaveDataSet(dataSet);

            //using (OracleBaseClass db = new OracleBaseClass())
            //{
            //    if (db.OpenDB())
            //    {
            //        db.BeginTransaction();
            //        int ret = new ServerPublic_Dao().SaveDataSet(dataset, db);
            //        if (ret <= 0)
            //        {
            //            db.RollbackTransaction();
            //        }
            //        else
            //        {
            //            db.CommitTransaction();
            //        }
            //        db.CloseDB();
            //        return ret;
            //    }
            //    else
            //    {
            //        return -1;
            //    }
            //}
        }
        /// <summary>
        ///  直接保存datatable 的方法 返回值 大于 0 成功
        /// </summary>
        /// <param name="dataTable"></param>
        /// <param name="autoUpateSgl"></param>
        /// <returns></returns>
        public int SaveDataTable(DataTable dataTable, bool autoUpateSgl = false)
        {
            DataSet dataSet = new DataSet();
            dataSet.Tables.Add(dataTable);
            int count = new ServerPublic_Dao().SaveDataSet(dataSet);
            return count;
        }
        /// <summary>
        /// 护理专用保存更新DATASET 返回值 大于 0 成功
        /// </summary>
        /// <param name="dataSet"></param>
        /// <returns></returns>
        public int SaveDataSetNur(System.Data.DataSet dataSet)
        {
            return new ServerPublic_Dao().SaveDataSetNur(dataSet);

            //using (OracleBaseClass db = new OracleBaseClass())
            //{
            //    if (db.OpenDB())
            //    {
            //        db.BeginTransaction();
            //        int ret = new ServerPublic_Dao().SaveDataSet(dataset, db);
            //        if (ret <= 0)
            //        {
            //            db.RollbackTransaction();
            //        }
            //        else
            //        {
            //            db.CommitTransaction();
            //        }
            //        db.CloseDB();
            //        return ret;
            //    }
            //    else
            //    {
            //        return -1;
            //    }
            //}
        }
        /// <summary>
        /// 直接执行增删改语句 判断返回值不为空就是报错信息 空为成功
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public string SaveTable(Dictionary<string, string> list)
        {
            return new ServerPublic_Dao().SaveTable(list);
        }
        /// <summary>
        /// 直接带事物方式执行增删改语句  判断返回值不为空就是报错信息 空为成功
        /// </summary>
        /// <param name="list"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public string SaveExcSql(Dictionary<string, string> list, Utility.OracleODP.OracleBaseClass db)
        {
            return new ServerPublic_Dao().SaveExcSql(list, db);
        }
        public int SaveDataSet(DataSet dataset, Utility.OracleODP.OracleBaseClass db)
        {
            return new ServerPublic_Dao().SaveDataSet(dataset, db);
        }
        /// <summary>
        /// 对dataset 数据 和 语句同时 执行保存  返回结果 string类型 不为空返回报错原因
        /// </summary>
        /// <param name="dataset"></param>
        /// <param name="list"></param>
        /// <returns></returns>
        public string SaveDataSetAndDictionary(DataSet dataset, Dictionary<string, string> list)
        {
            return new ServerPublic_Dao().SaveDataSetAndDictionary(dataset, list);
        }
        /// <summary>
        /// 带db 外部事物调用去查询语句 比如跟其他insert执行后未提交 配合使用在一个事物下取insert后的数值等
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public System.Data.DataSet GetListUseDB(string sql, Utility.OracleODP.OracleBaseClass db)
        {
            return new ServerPublic_Dao().GetListUseDB(sql, db);
        }
        /// <summary>
        /// 获取字符串格式的系统时间
        /// </summary>
        /// <returns></returns>
        public string GetSysDateTime()
        {
            return new ServerPublic_Dao().GetSysDateTime();
        }
        public DataSet GetDataTable_Para(string sql, List<string> ParaList, System.Collections.ArrayList ValueList)
        {
            return new ServerPublic_Dao().GetDataTable_Para(sql, ParaList, ValueList);
        }
        public DataSet GetDataTable_Para(string sql, List<string> ParaList, ArrayList ValueList, Utility.OracleODP.OracleBaseClass db)
        {
            return new ServerPublic_Dao().GetDataTable_Para(sql, ParaList, ValueList,db);
        }

        /// <summary>
        /// 取dtUpdate里对应tableName表的字段sql  多表关联sql数据进行保存 需要重新整理成单表语句 然后才能进行数据保存 
        /// </summary>
        /// <param name="dtUpdate">要更新的DatatTable</param>
        /// <param name="tableName">要更新的表名</param>
        /// <returns></returns>
        public string GetUpdateSql(DataTable dtUpdate, string tableName, OracleBaseClass db = null)
        {
            string sql = "SELECT * FROM " + tableName + " WHERE 1=0";
            DataTable dataTable;
            if (db != null)
            {
                dataTable = db.SelectDataSet(sql).Tables[0];
            }
            else
            {
                DataSet ds = GetDataBySql(sql);
                if(ds!=null && ds.Tables.Count > 0)
                {
                    dataTable = ds.Tables[0];
                }
                else
                {
                    dataTable = null;
                }
            }
            if (dataTable == null)
            {
                return "";
            }
            string updateSql = "";
            string selstr = "";
            //查询在表里的字段
            foreach (DataColumn col in dataTable.Columns)
            {
                if (dtUpdate.Columns.Contains(col.ColumnName))
                {
                    selstr += col.ColumnName + ",";
                }
            }
            ////删除不在表里的多余列
            //for (int i = dtUpdate.Columns.Count - 1; i >= 0; i--)
            //{
            //    if (!dataTable.Columns.Contains(dtUpdate.Columns[i].ColumnName))
            //    {
            //        dtUpdate.Rows.RemoveAt(i);
            //    }
            //}
            if (selstr.Length > 0)
            {
                selstr = selstr.Remove(selstr.Length - 1);
                updateSql = "SELECT " + selstr + " FROM " + tableName;
            }
            return updateSql;
        }

        /// <summary>
        /// 根据sql返回DataTable 一个是sql语句 一个指定TableName 名称，一个是限制字段必须大小写一致 返回datatable
        /// </summary>
        /// <param name="sql">查询语句</param>
        /// <param name="strTableName">表名</param>
        /// <param name="caseSensitive">大小写</param>
        /// <returns></returns>
        public DataTable SelectDataTable(string sql, string strTableName = "Table1", Boolean caseSensitive = false)
        {
            DataSet ds = new ServerPublic_Dao().GetDataBySql(sql);
            if (!string.IsNullOrEmpty(strTableName))
            {
                ds.Tables[0].TableName = strTableName;
            }
            ds.CaseSensitive = caseSensitive;
            if (ds != null && ds.Tables.Count > 0)
            {
                return ds.Tables[0];
            }
            else
            {
                return null;
            }
        }
        #endregion
        #region IDisposable 成员

        /// <summary>
        　　　　/// 实现IDisposable接口
        　　　　/// </summary>
        public void Dispose()
　　　　{
　　　　　　Dispose(true);
　　　　　　//.NET Framework 类库
　　　　　　// GC..::.SuppressFinalize 方法
　　　　　　//请求系统不要调用指定对象的终结器。
　　　　　　GC.SuppressFinalize(this);
　　　　}
　　　　/// <summary>
　　　　/// 虚方法，可供子类重写
　　　　/// </summary>
　　　　/// <param name="disposing"></param>
　　　　protected virtual void Dispose(bool disposing)
　　　　{
　　　　　　if (disposing)
　　　　　　　　{
　　　　　　　　　　// Release managed resources
　　　　　　　　}
　　　　}

        public DataSet GetDataTable_Para_tran(string sql, List<string> ParaList, ArrayList ValueList)
        {
            return new ServerPublic_Dao().GetDataTable_Para_tran(sql, ParaList, ValueList);
        }
        /// <summary>
        /// 开启事物查询 与 GetDataBySql 的区别 主要是 查询语句中涉及带dblink的查询准备的
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public DataSet GetDataBySql_Tran(string sql)
        {
            return new ServerPublic_Dao().GetDataBySql_Tran(sql);
        }

        /// <summary>
        　　　　/// 析构函数
        　　　　/// 当客户端没有显示调用Dispose()时由GC完成资源回收功能
        　　　　/// </summary>
        ~ServerPublicClient()
　　　　{
　　　　　　Dispose(false);
　　　　}

        #endregion

    }
}
