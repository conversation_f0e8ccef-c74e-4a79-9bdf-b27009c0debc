﻿//**********************************************
//说明:短信发送
//计算机名称：LINDP
//创建日期：2017-11-13 14:55:58
//作者：林大鹏
//版本号：V1.00
//**********************************************

using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace PlatCommon.Common
{
    public class SMSInfo
    {
        [DllImport("ZZSMSDLL.DLL")]
        public static extern int SendInfo(string ServerIP,string message);

        //入院
        public static string SendMessageForAdmit(string ServerIP, string name, string sex, string tel, string department, string indate)
        {
            //校验数据
            if (string.IsNullOrEmpty(ServerIP))
                return "服务端地址不能为空";
            if (string.IsNullOrEmpty(name))
                return "姓名不能为空";
            if (string.IsNullOrEmpty(sex))
                return "性别不能为空";
            if (string.IsNullOrEmpty(tel))
                return "电话不能为空";
            if (string.IsNullOrEmpty(department))
                return "科室不能为空";
            if (string.IsNullOrEmpty(indate))
                return "入院日期不能为空";
            string json = "{\"datatype\":\"in\",\"name\":\""+name+"\",\"sex\":\""+sex+"\",\"tel\":\""+tel;
            json += "\",\"department\":\""+department+"\",\"indate\":\""+indate+"\"}";
            int rvn = SendInfo(ServerIP, json);
            if (rvn == 1)
                return "";
            else
                return "发送" + json + "失败";
            
        }
        //结算或出院
        public static string SendMessageForIbilling(string ServerIP, string name, string sex,
            string tel, string department, string prepay, string cost, string restore, string outdate)
        {
            //校验数据
            if (string.IsNullOrEmpty(ServerIP))
                return "服务端地址不能为空";
            if (string.IsNullOrEmpty(name))
                return "姓名不能为空";
            if (string.IsNullOrEmpty(sex))
                return "性别不能为空";
            if (string.IsNullOrEmpty(tel))
                return "电话不能为空";
            if (string.IsNullOrEmpty(department))
                return "科室不能为空";
            
            string json = "{\"datatype\":\"in\",\"name\":\"" + name + "\",\"sex\":\"" + sex + "\",\"tel\":\"" + tel;
            json += "\",\"department\":\"" + department + "\",\"prepay\":\"" + prepay + "\",\"cost\":\""+cost+"\"";
            json += ",\"restore\":\""+restore+"\",\"outdate\":\""+outdate+"\"}";
            int rvn = SendInfo(ServerIP, json);
            if (rvn == 1)
                return "";
            else
                return "发送" + json + "失败";
        }
    }
}
