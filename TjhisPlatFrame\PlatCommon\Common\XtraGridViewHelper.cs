﻿using System.Text;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using System.IO;
using System.Collections;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraGrid.Views.Base;
using SQL = PlatCommon.Base02.Cs02StringHelper;
using System;
using PlatCommon.Base01;
using PlatCommon.Base02;

namespace PlatCommon.Common
{
    /// <summary>
    /// DataGridView 助手
    /// </summary>
    public class XtraGridViewHelper
    {
        /// <summary>
        /// 生成配置文件结构
        /// </summary>
        /// <returns></returns>
        public static DataSet GetConfigSchema()
        {
            DataSet ds = new DataSet("DataGridView_Cols_Config");
            DataTable dt = ds.Tables.Add("DataGridView_Cols");

            dt.Columns.Add("DATA_PROPERTY_NAME", typeof(System.String));    // 值字段
            dt.Columns.Add("HEADER_TEXT", typeof(System.String));           // 列标题            
            dt.Columns.Add("STYLE_FORMAT", typeof(System.String));          // 格式
            dt.Columns.Add("COL_WIDTH", typeof(System.Int32));              // 列宽
            dt.Columns.Add("SHOW_ORDER", typeof(System.Int32));             // 显示顺序

            return ds;
        }


        /// <summary>
        /// 保存列的配置文件
        /// </summary>
        /// <param name="dsColConfig"></param>
        /// <param name="dgvId"></param>
        public static void SaveColumnConfig(DataSet dsColConfig, string dgvId)
        {
            // 保存
            string fileName = Path.Combine(Cs01FileHelper.GetCurrDllPath(), "Config");
            if (Directory.Exists(fileName) == false)
            {
                Directory.CreateDirectory(fileName);
            }

            fileName = Path.Combine(fileName, dgvId + ".xml");

            dsColConfig.WriteXml(fileName, XmlWriteMode.WriteSchema);
        }


        /// <summary>
        /// 保存列宽
        /// </summary>
        public static void SaveColumnConfig(GridView dgv, string dgvId)
        {
            // 获取本地配置文件
            DataSet ds = GetColumnConfig(dgvId);

            // 更新内容
            foreach (GridColumn dgvCol in dgv.Columns)
            {
                if (dgvCol.Visible == true)
                {
                    DataRow drEdit = null;

                    string colName = dgvCol.Name;
                    string filter = "DATA_PROPERTY_NAME = " + SQL.SqlConvert(colName);
                    DataRow[] drFind = ds.Tables[0].Select(filter);
                    if (drFind.Length == 0)
                    {
                        drEdit = ds.Tables[0].NewRow();
                        drEdit["DATA_PROPERTY_NAME"] = colName;                 // 列名
                        ds.Tables[0].Rows.Add(drEdit);
                    }
                    else
                    {
                        drEdit = drFind[0];
                    }

                    drEdit["HEADER_TEXT"] = dgvCol.Caption;                     // 标题                   
                    drEdit["COL_WIDTH"] = dgvCol.Width;                         // 列宽
                    drEdit["SHOW_ORDER"] = dgvCol.SortIndex;                    // 显示顺序
                }
            }

            // 保存本地配置文件
            SaveColumnConfig(ds, dgvId);
        }


        /// <summary>
        /// 获取DataGridView的列配置信息
        /// </summary>
        /// <param name="dgvId"></param>
        /// <returns></returns>
        public static DataSet GetColumnConfig(string dgvId)
        {
            string fileName = Path.Combine(Cs01FileHelper.GetCurrDllPath(), "Config");
            fileName = Path.Combine(fileName, dgvId + ".xml");

            if (File.Exists(fileName))
            {
                DataSet ds = new DataSet();
                ds.ReadXml(fileName, XmlReadMode.ReadSchema);
                return ds;
            }
            else
            {
                return GetConfigSchema();
            }
        }


        /// <summary>
        /// DataGridView的列配置信息, 从数据源与配置文件中获取
        /// </summary>
        /// <param name="dsSrc"></param>
        /// <param name="dgvId"></param>
        /// <returns></returns>
        public static DataSet GetColumnConfig(DataSet dsSrc, string dgvId)
        {
            DataSet ds = GetColumnConfig(dgvId);

            if (dsSrc == null || dsSrc.Tables.Count == 0)
            {
                return ds;
            }

            foreach (DataColumn dc in dsSrc.Tables[0].Columns)
            {
                string filter = "DATA_PROPERTY_NAME = " + SQL.SqlConvert(dc.ColumnName);
                if (ds.Tables[0].Select(filter).Length > 0) continue;

                DataRow drNew = ds.Tables[0].NewRow();

                drNew["DATA_PROPERTY_NAME"] = dc.ColumnName;    // 值字段
                drNew["HEADER_TEXT"] = dc.ColumnName;           // 列标题            
                drNew["STYLE_FORMAT"] = string.Empty;           // 格式
                drNew["COL_WIDTH"] = 40;                        // 列宽
                drNew["SHOW_ORDER"] = -1;                       // 显示顺序

                ds.Tables[0].Rows.Add(drNew);
            }

            for (int i = ds.Tables[0].Rows.Count - 1; i >= 0; i--)
            {
                string colName = ds.Tables[0].Rows[i]["DATA_PROPERTY_NAME"].ToString();

                if (dsSrc.Tables[0].Columns.Contains(colName) == false)
                {
                    ds.Tables[0].Rows[i].Delete();
                }
            }

            return ds;
        }


        /// <summary>
        /// 通过配置信息生成DataGridView的列
        /// </summary>
        public static void AddColByConfig(GridView dgv, DataSet ds)
        {
            try
            {
                if (Cs02DataSetHelper.HasRecord(ds) == false) return;

                // 显示已配置的列
                DataRow[] drFind = ds.Tables[0].Select();

                for (int i = 0; i < drFind.Length; i++)
                {
                    DataRow dr = drFind[i];

                    string colName = dr["DATA_PROPERTY_NAME"].ToString();   // 列名
                    string styleFormat = dr["STYLE_FORMAT"].ToString();     // 格式

                    // 判断列是否存在
                    GridColumn dgvColumn = null;
                    foreach (GridColumn dgvCol in dgv.Columns)
                    {
                        if (dgvCol.Name.Equals(colName))
                        {
                            dgvColumn = dgvCol;
                            break;
                        }
                    }

                    if (dgvColumn == null) continue;

                    // 设置列宽
                    dgvColumn.Width = Converter.ToInt(dr["COL_WIDTH"].ToString());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }


        /// <summary>
        /// 通过配置信息生成DataGridView的列
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="dgvId"></param>
        public static void AddColByConfig(GridView dgv, string dgvId)
        {
            DataSet ds = GetColumnConfig(dgvId);
            AddColByConfig(dgv, ds);
        }


        /// <summary>
        /// 获取界面展现的值
        /// </summary>
        /// <returns></returns>
        public static DataSet GetDisplayData(GridView dgv, string tableName)
        {
            if (string.IsNullOrEmpty(tableName)) tableName = "Table1";

            // 创建结构
            DataSet ds = new DataSet();
            DataTable dt = ds.Tables.Add(tableName);

            string colName = string.Empty;
            for (int col = 0; col < dgv.VisibleColumns.Count; col++)
            {
                colName = dgv.VisibleColumns[col].Name;
                dt.Columns.Add(colName);
            }
            //for (int col = 0; col < dgv.VisibleColumns.Count; col++)
            //{
            //    colName = col.ToString() + "_" + dgv.VisibleColumns[col].Name;
            //    dt.Columns.Add(colName);
            //}

            // 获取值
            for (int row = 0; row < dgv.RowCount; row++)
            {
                DataRow drEdit = dt.NewRow();

                for (int col = 0; col < dgv.VisibleColumns.Count; col++)
                {
                    colName = dgv.VisibleColumns[col].Name;

                    drEdit[colName] = dgv.GetRowCellDisplayText(row, dgv.VisibleColumns[col]);
                }

                dt.Rows.Add(drEdit);
            }
            //for (int row = 0; row < dgv.RowCount; row++)
            //{
            //    DataRow drEdit = dt.NewRow();

            //    for (int col = 0; col < dgv.VisibleColumns.Count; col++)
            //    {
            //        colName = col.ToString() + "_" + dgv.VisibleColumns[col].Name;

            //        drEdit[colName] = dgv.GetRowCellDisplayText(row, dgv.VisibleColumns[col]);
            //    }

            //    dt.Rows.Add(drEdit);
            //}

            return ds;
        }



        /// <summary>
        /// 获取界面展现的值
        /// </summary>
        /// <returns></returns>
        public static DataSet GetDisplayData_OneRowOneTable(GridView dgv, string tableName)
        {
            if (string.IsNullOrEmpty(tableName)) tableName = "Table";

            // 创建结构
            DataSet ds = new DataSet();

            // 获取值
            for (int row = 0; row < dgv.RowCount; row++)
            {
                // 增加一张表
               // DataTable dt = ds.Tables.Add(tableName);
                DataTable dt = ds.Tables.Add(tableName+"_"+ row.ToString());
                string colName = string.Empty;
                for (int col = 0; col < dgv.VisibleColumns.Count; col++)
                {
                    colName = dgv.VisibleColumns[col].Name;
                    dt.Columns.Add(colName);
                }

                // 增加一行记录
                DataRow drEdit = dt.NewRow();

                for (int col = 0; col < dgv.VisibleColumns.Count; col++)
                {
                    colName = dgv.VisibleColumns[col].Name;

                    drEdit[colName] = dgv.GetRowCellDisplayText(row, dgv.VisibleColumns[col]);
                }

                dt.Rows.Add(drEdit);
            }
            //for (int row = 0; row < dgv.RowCount; row++)
            //{
            //    // 增加一张表
            //    DataTable dt = ds.Tables.Add(tableName + "_" + row.ToString());

            //    string colName = string.Empty;
            //    for (int col = 0; col < dgv.VisibleColumns.Count; col++)
            //    {
            //        colName = col.ToString() + "_" + dgv.VisibleColumns[col].Name;
            //        dt.Columns.Add(colName);
            //    }

            //    // 增加一行记录
            //    DataRow drEdit = dt.NewRow();

            //    for (int col = 0; col < dgv.VisibleColumns.Count; col++)
            //    {
            //        colName = col.ToString() + "_" + dgv.VisibleColumns[col].Name;

            //        drEdit[colName] = dgv.GetRowCellDisplayText(row, dgv.VisibleColumns[col]);
            //    }

            //    dt.Rows.Add(drEdit);
            //}

            return ds;
        }
        #region 排序 Yzw20181221
        /// <summary>
        /// 更新现有排序
        /// </summary>
        /// <param name="gv">DEV GridView引用</param>
        /// <param name="ds">DataSet引用</param>
        /// <param name="colNameOrder">排序列</param>
        public static void ResetShowOrder(ref GridView gv, ref DataSet ds, string colNameOrder)
        {
            int min = 1000;
            int max = min + gv.RowCount - 1;

            for (int i = gv.RowCount - 1; i >= 0; i--)
            {
                DataRow dr = gv.GetDataRow(i);
                dr[colNameOrder] = min + i;
            }

            DataRow[] drFind = ds.Tables[0].Select(colNameOrder + " >= 0", colNameOrder);
            for (int i = 0; i < drFind.Length; i++)
            {
                drFind[i][colNameOrder] = i + 1;
            }
        }


        /// <summary>
        /// 向上移动
        /// </summary>
        /// <param name="gv">DEV GridView引用</param>
        /// <param name="ds">DataSet引用</param>
        /// <param name="colNameOrder">排序列</param>
        public static void MoveUpRow(ref GridView gv, ref DataSet ds, string colNameOrder)
        {
            if (gv.SelectedRowsCount == 0) return;

            ResetShowOrder(ref gv, ref ds, colNameOrder);

            // 选中行上移
            DataRow drSel = gv.GetFocusedDataRow();
            int showOrder = Converter.ToInt(drSel[colNameOrder]);
            if (showOrder <= 0) return;

            DataRow[] drFind = ds.Tables[0].Select(colNameOrder + " = " + (showOrder - 1).ToString());
            if (drFind.Length > 0)
            {
                drFind[0][colNameOrder] = showOrder;
            }
            drSel[colNameOrder] = showOrder - 1;
        }

        /// <summary>
        /// 向下移动
        /// </summary>
        /// <param name="gv">DEV GridView引用</param>
        /// <param name="ds">DataSet引用</param>
        /// <param name="colNameOrder">排序列</param>
        public static void MoveDownRow(ref GridView gv, ref DataSet ds, string colNameOrder)
        {
            if (gv.SelectedRowsCount == 0) return;

            // 重新编号            
            ResetShowOrder(ref gv, ref ds, colNameOrder);

            // 选中行下移
            DataRow drSel = gv.GetFocusedDataRow();
            int showOrder = Converter.ToInt(drSel[colNameOrder]);
            if (showOrder >= gv.RowCount - 1) return;

            DataRow[] drFind = ds.Tables[0].Select(colNameOrder + " = " + (showOrder + 1).ToString());
            if (drFind.Length > 0)
            {
                drFind[0][colNameOrder] = showOrder;
            }
            drSel[colNameOrder] = showOrder + 1;
        }
        #endregion
        /// <summary>
        /// 行异常处理
        /// </summary>
        /// <param name="xgrv"></param>
        /// <param name="e"></param>
        public static void HandleInvalidRowException(GridView xgrv, ref InvalidRowExceptionEventArgs e)
        {
            string MSG1 = "";// "字段" + xgrvNrDictInspection.FocusedColumn.FieldName + "的数据不合法！ 你是否想修改此项？";
            string[] strs = e.ErrorText.Split(new char[] { '“', '”' }, StringSplitOptions.None);
            string[] strs1 = strs[1].Split(',');
            MSG1 = "列";
            foreach (string str in strs1)
            {
                if (xgrv.Columns.ColumnByFieldName(str.Trim()).Visible)
                {
                    MSG1 = MSG1 + "[" + xgrv.Columns.ColumnByFieldName(str.Trim()).Caption + "],";
                }
            }
            MSG1 = MSG1.Substring(0, MSG1.Length - 1);
            if (e.ErrorText.Contains("nulls"))
            {
                MSG1 = MSG1 + "不能为空！ 你是否想修改此项？";
            }
            else if (e.ErrorText.Contains("唯一"))
            {
                MSG1 = MSG1 + "被约束为是唯一的！ 你是否想修改此项？";
            }
            else if (e.ErrorText.Contains("MaxLength"))
            {
                MSG1 = MSG1 + "超出了长度限制！ 你是否想修改此项？";
            }
            string MSG2 = "数据错误";
            if (DialogResult.Yes == MessageBox.Show(MSG1, MSG2, MessageBoxButtons.YesNo))
            {
                e.ExceptionMode = ExceptionMode.NoAction;
            }
            else
            {
                e.ExceptionMode = ExceptionMode.Ignore;
            }
        }


        /// <summary>
        /// 进行定位
        /// </summary>
        public static void FocusRow(GridView xgrv, Hashtable hasColValues)
        {
            int focusedRowHandle = -1;

            int rowIndex = 0;

            foreach (DictionaryEntry de in hasColValues)
            {
                if (de.Value == null) continue;

                while (rowIndex < xgrv.DataRowCount)
                {
                    DataRow dr = xgrv.GetDataRow(rowIndex);
                    if (dr[de.Key.ToString()].ToString().Equals(de.Value.ToString()))
                    {
                        focusedRowHandle = rowIndex;
                        break;
                    }
                    else
                    {
                        rowIndex++;
                    }
                }
            }

            if (rowIndex < xgrv.DataRowCount)
            {
                xgrv.FocusedRowHandle = focusedRowHandle;
            }
        }
    }
}
