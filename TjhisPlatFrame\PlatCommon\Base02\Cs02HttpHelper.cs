﻿/*********************************************
* 文 件 名：Cs02HttpHelper
* 类 名 称：Cs02HttpHelper
* 功能说明：HTTP类
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：刘成刚
* 创建时间：2019-05-18 19:41:55
* 版 本 号：1.0.0.1
* 修 改 人：刘成刚
* 修改时间：2020-02-18
* CLR 版本：4.0.30319.42000
/*********************************************/

using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using System.Web;

namespace PlatCommon.Base02
{
    /// <summary>
    /// Http客户端
    /// </summary>
    public class Cs02HttpClient
    {
        // 请求数据的内容类型
        //public string Text = "text/plain";
        //public string JSON = "application/json";
        //public string Javascript = "application/javascript";
        //public string XML = "application/xml";
        //public string TextXML = "text/xml";
        //public string HTML = "text/html";
        private string _strMethodEnum = ",GET,POST,PUT,DELETE,";

        /// <summary>
        /// 请求的url地址
        /// </summary>
        private string _strUrl { get; set; }

        /// <summary>
        /// 请求的方法:GET,POST,PUT,DELETE
        /// </summary>
        private string _strMethod { get; set; }

        /// <summary>
        /// 格式类型
        /// </summary>
        private string _strContentType { get; set; }

        /// <summary>
        /// 传送的数据
        /// </summary>
        private string _strPostData { get; set; }

        /// <summary>
        /// 编码方式
        /// </summary>
        private Encoding _encoding { get; set; }

        /// <summary>
        /// 初始化
        /// <param name="strUrl">Url地址</param>
        /// </summary>
        public Cs02HttpClient(string strUrl)
        {
            _strUrl = strUrl;
            _strMethod = "GET";
            _strContentType = "text/xml";
            _strPostData = "";
            _encoding = new UTF8Encoding();
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strUrl">Url地址</param>
        /// <param name="strContentType">格式类型</param>
        public Cs02HttpClient(string strUrl, string strContentType)
        {
            _strUrl = strUrl;
            _strMethod = "GET";
            _strContentType = strContentType;
            _strPostData = "";
            _encoding = new UTF8Encoding();
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strUrl">Url地址</param>
        /// <param name="strMethod">执行方法</param>
        /// <param name="strContentType">格式类型</param>
        public Cs02HttpClient(string strUrl, string strMethod, string strContentType)
        {
            _strUrl = strUrl;
            _strContentType = strContentType;
            _encoding = new UTF8Encoding();
            _strPostData = "";
            if (_strMethodEnum.IndexOf("," + strMethod.ToUpper() + ",") >= 0)
                _strMethod = strMethod.ToUpper();
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strUrl">Url地址</param>
        /// <param name="strMethod">执行方法</param>
        /// <param name="strContentType">格式类型</param>
        /// <param name="strPostData">POST数据</param>
        public Cs02HttpClient(string strUrl, string strMethod, string strContentType, string strPostData)
        {
            _strUrl = strUrl;
            _strContentType = strContentType;
            _strPostData = strPostData;
            _encoding = new UTF8Encoding();
            if (_strMethodEnum.IndexOf("," + strMethod.ToUpper() + ",") >= 0)
                _strMethod = strMethod.ToUpper();
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strUrl">Url地址</param>
        /// <param name="strMethod">执行方法</param>
        /// <param name="strContentType">格式类型</param>
        /// <param name="strPostData">POST数据</param>
        /// <param name="encoding">编码方式</param>
        /// 
        public Cs02HttpClient(string strUrl, string strMethod, string strContentType, string strPostData, Encoding encoding)
        {
            _strUrl = strUrl;
            _strContentType = strContentType;
            _strPostData = strPostData;
            _encoding = encoding;
            if (_strMethodEnum.IndexOf("," + strMethod.ToUpper() + ",") >= 0)
                _strMethod = strMethod.ToUpper();
        }

        /// <summary>
        /// 执行HTTP调用
        /// </summary>
        /// <returns></returns>
        public string GetResponse()
        {
            return GetResponse("");
        }

        /// <summary>
        /// 执行HTTP调用
        /// </summary>
        /// <param name="strParams">参数</param>
        /// <returns></returns>
        public string GetResponse(string strParams)
        {
            string strResponse = string.Empty;
            try
            {
                HttpWebRequest myRequest = (HttpWebRequest)WebRequest.Create(_strUrl + strParams);

                // 检查https        
                if (_strUrl.Substring(0, 8).ToLower() == "https://")
                    ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);

                //设置提交方式
                myRequest.Method = _strMethod;
                myRequest.ContentLength = 0;
                myRequest.ContentType = _strContentType;
                //是否允许自动重定向
                myRequest.AllowAutoRedirect = false;

                //方法是POST/PUT时，传送数据
                if (!string.IsNullOrEmpty(_strPostData) && (_strMethod == "PUT" || _strMethod == "POST"))
                {
                    //编码方式
                    byte[] bytes = _encoding.GetBytes(_strPostData);
                    myRequest.ContentLength = bytes.Length;

                    Stream writeStream = myRequest.GetRequestStream();
                    writeStream.Write(bytes, 0, bytes.Length);
                    writeStream.Close();
                }

                HttpWebResponse myResponse = (HttpWebResponse)myRequest.GetResponse();
                if (myResponse.StatusCode != HttpStatusCode.OK)
                {
                    string strErrMsg = String.Format($"调用Http出错了！错误码{0}", myResponse.StatusCode);
                    myResponse.Close();
                    myRequest.Abort();
                    throw new ApplicationException(strErrMsg);
                }

                //取返回值
                Stream responseStream = myResponse.GetResponseStream();
                if (responseStream != null)
                {
                    StreamReader reader = new StreamReader(responseStream);
                    strResponse = reader.ReadToEnd();
                    //strResponse = HttpUtility.UrlDecode(reader.ReadToEnd());
                    reader.Close();
                }
                responseStream.Close();
                myResponse.Close();
                myRequest.Abort();
            }
            catch (Exception ex)
            {
                throw new ApplicationException("调用Http出错了！\r\n错误信息：" + ex.Message);
            }

            return strResponse;
        }

        /// <summary>
        /// 检查URL状态
        /// </summary>
        /// <param name="strParams">参数</param>
        /// <returns></returns>
        public bool CheckUrl(string strParams)
        {
            bool bResult = false;

            HttpWebRequest myRequest = (HttpWebRequest)WebRequest.Create(_strUrl + strParams);

            //设置提交方式可以为GET，POST等  
            myRequest.Method = _strMethod.ToString();
            //设置网页响应时间长度          
            myRequest.Timeout = 10000;
            //是否允许自动重定向  
            myRequest.AllowAutoRedirect = false;
            try
            {
                HttpWebResponse myResponse = (HttpWebResponse)myRequest.GetResponse();
                //返回响应的状态
                bResult = (myResponse.StatusCode == HttpStatusCode.OK);
                myResponse.Close();
                myRequest.Abort();
            }
            catch (Exception ex)
            {
                throw new ApplicationException("调用Http出错了！\r\n错误信息：" + ex.Message);
            }
            return bResult;
        }

        // 添加https      
        //private static readonly string DefaultUserAgent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.2; SV1; .NET CLR 1.1.4322; .NET CLR 2.0.50727)";
        private static bool CheckValidationResult(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors)
        {
            return true;
            //总是接受       
        }
    }

    /// <summary>
    /// HTTP类
    /// </summary>
    public static class Cs02HttpHelper
    {
        /// <summary>
        /// Get方式的HTTP请求
        /// </summary>
        /// <param name="strUrl">Url地址</param>
        /// <param name="strReturn">返回值</param>
        /// <param name="strErrMsg">错误信息</param>
        /// <param name="iTimeout">超时(毫秒)</param>
        /// <returns>0:成功，其它:错误</returns>
        public static int GetHttpResponse(string strUrl, ref string strReturn, ref string strErrMsg, int iTimeout = 3000)
        {
            int iResult = -1;
            strReturn = string.Empty;
            strErrMsg = string.Empty;
            HttpWebRequest myRequest = (HttpWebRequest)WebRequest.Create(strUrl);
            myRequest.Method = "GET";
            myRequest.ContentType = "text/html;charset=UTF-8";
            myRequest.UserAgent = null;
            myRequest.Timeout = iTimeout;
            try
            {
                HttpWebResponse myResponse = (HttpWebResponse)myRequest.GetResponse();
                if (myResponse.StatusCode != HttpStatusCode.OK)
                {
                    Stream myResponseStream = myResponse.GetResponseStream();
                    StreamReader myStreamReader = new StreamReader(myResponseStream, Encoding.GetEncoding("utf-8"));
                    strReturn = myStreamReader.ReadToEnd();
                    myStreamReader.Close();
                    myResponseStream.Close();
                }
                myResponse.Close();
                myRequest.Abort();
                iResult = 0;
            }
            catch (Exception ex)
            {
                strErrMsg = ex.Message;
            }

            return iResult;
        }

        /// <summary>
        /// POST方式的HTTP请求
        /// </summary>
        /// <param name="strUrl">Url地址</param>
        /// <param name="strContentType">编码类型</param>
        /// <param name="dicParameters">参数表string, string</param>
        /// <param name="strReturn">返回值</param>
        /// <param name="strErrMsg">错误信息</param>
        /// <param name="iTimeout">超时(毫秒)</param>
        /// <returns>0:成功，其它:错误</returns>
        public static int PostHttpResponse(string strUrl, string strContentType, Dictionary<string, string> dicParameters, ref string strReturn, ref string strErrMsg, int iTimeout = 3000)
        {
            return PostHttpResponse(strUrl, strContentType, dicParameters, ref strReturn, ref strErrMsg, "", null, iTimeout);
        }

        /// <summary>
        /// POST方式的HTTP请求
        /// </summary>
        /// <param name="strUrl">Url地址</param>
        /// <param name="strContentType">编码类型</param>
        /// <param name="dicParameters">参数表string, string</param>
        /// <param name="strReturn">返回值</param>
        /// <param name="strErrMsg">错误信息</param>
        /// <param name="cookies">cookies</param>
        /// <param name="iTimeout">超时</param>
        /// <returns>0:成功，其它:错误</returns>
        public static int PostHttpResponse(string strUrl, string strContentType, Dictionary<string, string> dicParameters, ref string strReturn, ref string strErrMsg, CookieCollection cookies, int iTimeout = 3000)
        {
            return PostHttpResponse(strUrl, strContentType, dicParameters, ref strReturn, ref strErrMsg, "", cookies, iTimeout);
        }

        /// <summary>
        /// POST方式的HTTP请求
        /// </summary>
        /// <param name="strUrl">Url地址</param>
        /// <param name="strContentType">编码类型</param>
        /// <param name="dicParameters">参数表string, string</param>
        /// <param name="strReturn">返回值</param>
        /// <param name="strErrMsg">错误信息</param>
        /// <param name="iTimeout">超时(毫秒)</param>
        /// <param name="strUserAgent">代理</param>
        /// <returns>0:成功，其它:错误</returns>
        public static int PostHttpResponse(string strUrl, string strContentType, Dictionary<string, string> dicParameters, ref string strReturn, ref string strErrMsg, string strUserAgent, int iTimeout = 3000)
        {
            return PostHttpResponse(strUrl, strContentType, dicParameters, ref strReturn, ref strErrMsg, strUserAgent, null, iTimeout);
        }

        /// <summary>
        /// POST方式的HTTP请求
        /// </summary>
        /// <param name="strUrl">Url地址</param>
        /// <param name="strContentType">编码类型</param>
        /// <param name="dicParameters">参数表string, string</param>
        /// <param name="strReturn">返回值</param>
        /// <param name="strErrMsg">错误信息</param>
        /// <param name="iTimeout">超时(毫秒)</param>
        /// <param name="strUserAgent">代理</param>
        /// <param name="cookies">cookies</param>
        /// <returns>0:成功，其它:错误</returns>
        public static int PostHttpResponse(string strUrl, string strContentType, Dictionary<string, string> dicParameters, ref string strReturn, ref string strErrMsg, string strUserAgent, CookieCollection cookies, int iTimeout = 3000)
        {
            int iResult = -1;
            strReturn = string.Empty;
            strErrMsg = string.Empty;

            HttpWebRequest myRequest = null;

            //如果是发送HTTPS请求  
            if (strUrl.StartsWith("https", StringComparison.OrdinalIgnoreCase))
            {
                myRequest = WebRequest.Create(strUrl) as HttpWebRequest;
            }
            else
            {
                myRequest = WebRequest.Create(strUrl) as HttpWebRequest;
            }

            myRequest.Method = "POST";
            myRequest.ContentType = strContentType;

            //设置代理UserAgent
            if (!string.IsNullOrEmpty(strUserAgent))
            {
                myRequest.UserAgent = strUserAgent;
            }

            //设置超时
            if (iTimeout <= 0)
            {
                iTimeout = 3000;
            }
            myRequest.Timeout = iTimeout;

            //设置Cookie
            if (cookies != null)
            {
                myRequest.CookieContainer = new CookieContainer();
                myRequest.CookieContainer.Add(cookies);
            }

            //处理参数  
            if (!(dicParameters == null || dicParameters.Count == 0))
            {
                int i = 0;
                StringBuilder sbParameter = new StringBuilder();
                foreach (string strKey in dicParameters.Keys)
                {
                    if (i > 0)
                    {
                        sbParameter.AppendFormat("&{0}={1}", strKey, dicParameters[strKey]);
                    }
                    else
                    {
                        sbParameter.AppendFormat("{0}={1}", strKey, dicParameters[strKey]);
                        i++;
                    }
                }
                //传参数
                try
                {
                    byte[] byteArrData = Encoding.UTF8.GetBytes(sbParameter.ToString());
                    using (Stream streamWrite = myRequest.GetRequestStream())
                    {
                        streamWrite.Write(byteArrData, 0, byteArrData.Length);
                        streamWrite.Close();
                    }
                }
                catch (Exception ex)
                {
                    strErrMsg = ex.Message;
                    myRequest.Abort();
                    return iResult;
                }
            }

            //发送POST数据  
            try
            {
                HttpWebResponse myResponse = myRequest.GetResponse() as HttpWebResponse;
                if (myResponse.StatusCode != HttpStatusCode.OK)
                {
                    strErrMsg = String.Format($"调用Http出错了！错误码{0}", myResponse.StatusCode);
                    myResponse.Close();
                    myRequest.Abort();
                    throw new ApplicationException(strErrMsg);
                }

                //取返回值
                using (Stream streamResponse = myResponse.GetResponseStream())
                {
                    StreamReader reader = new StreamReader(streamResponse, Encoding.UTF8);
                    strReturn = reader.ReadToEnd();
                    reader.Close();
                    streamResponse.Close();
                    iResult = 0;
                }
                myResponse.Close();
                myRequest.Abort();
            }
            catch (Exception ex)
            {
                if (myRequest != null)
                    myRequest.Abort();

                strErrMsg = ex.Message;
            }

            return iResult;
        }

        ///<summary>
        /// Http下载文件
        /// </summary>
        /// <param name="strUrl">下载文件地址</param>
        /// <param name="strFileName">保存文件全路径</param>
        public static bool DownloadFile(string strUrl, string strFileName)
        {
            try
            {
                HttpWebRequest myRequest = (System.Net.HttpWebRequest)System.Net.HttpWebRequest.Create(strUrl);
                myRequest.Headers.Add("X-Token", "c3405a3b-8066-4a82-afec-62047f7f7801");
                HttpWebResponse myResponse = (System.Net.HttpWebResponse)myRequest.GetResponse();
                Stream fsRead = myResponse.GetResponseStream();
                Stream fsWrite = new System.IO.FileStream(strFileName, System.IO.FileMode.Create);
                byte[] by = new byte[1024];
                int osize = fsRead.Read(by, 0, (int)by.Length);
                while (osize > 0)
                {
                    fsWrite.Write(by, 0, osize);
                    osize = fsRead.Read(by, 0, (int)by.Length);
                }
                fsWrite.Close();
                fsRead.Close();
                myResponse.Close();
                myRequest.Abort();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Http上传文件
        /// </summary>
        /// <param name="strUrl">上传文件地址</param>
        /// <param name="strFileFullName">上传文件全路径</param>
        /// <returns></returns>
        public static string UploadFile(string strUrl, string strFileFullName)
        {
            // 设置参数
            HttpWebRequest request = WebRequest.Create(strUrl) as HttpWebRequest;
            CookieContainer cookieContainer = new CookieContainer();
            request.CookieContainer = cookieContainer;
            request.AllowAutoRedirect = true;
            request.Method = "POST";
            string strBoundary = DateTime.Now.Ticks.ToString("X"); // 随机分隔线
            request.ContentType = "multipart/form-data;charset=utf-8;boundary=" + strBoundary;
            byte[] itemBoundaryBytes = Encoding.UTF8.GetBytes("\r\n--" + strBoundary + "\r\n");
            byte[] endBoundaryBytes = Encoding.UTF8.GetBytes("\r\n--" + strBoundary + "--\r\n");

            int iPos = strFileFullName.LastIndexOf("\\");
            string strFileName = strFileFullName.Substring(iPos + 1);

            //请求头部信息 
            StringBuilder sbHeader = new StringBuilder(
                string.Format("Content-Disposition:form-data;name=\"file\";filename=\"{0}\"\r\nContent-Type:application/octet-stream\r\n\r\n", strFileName));
            byte[] postHeaderBytes = Encoding.UTF8.GetBytes(sbHeader.ToString());

            FileStream fsRead = new FileStream(strFileFullName, FileMode.Open, FileAccess.Read);
            byte[] bArrContent = new byte[fsRead.Length];
            fsRead.Read(bArrContent, 0, bArrContent.Length);
            fsRead.Close();

            Stream fsWrite = request.GetRequestStream();
            fsWrite.Write(itemBoundaryBytes, 0, itemBoundaryBytes.Length);
            fsWrite.Write(postHeaderBytes, 0, postHeaderBytes.Length);
            fsWrite.Write(bArrContent, 0, bArrContent.Length);
            fsWrite.Write(endBoundaryBytes, 0, endBoundaryBytes.Length);
            fsWrite.Close();

            //发送请求并获取相应回应数据
            HttpWebResponse response = request.GetResponse() as HttpWebResponse;

            //直到request.GetResponse()程序才开始向目标网页发送Post请求
            Stream instream = response.GetResponseStream();
            StreamReader sr = new StreamReader(instream, Encoding.UTF8);

            //返回结果网页（html）代码
            string strContent = sr.ReadToEnd();
            sr.Close();
            response.Close();
            request.Abort();
            return strContent;
        }

        /// <summary>
        /// POST 
        /// </summary>
        /// <param name="strUrl">Url地址</param>
        /// <param name="strContentType">Url地址</param>
        /// <param name="strBody">XML内容</param>
        /// <returns></returns>
        public static string HttpPost1(string strUrl, string strContentType, string strBody)
        {
            //一定要有这一句
            ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(strUrl);
            request.Method = "POST";
            request.Accept = "text/html, application/xhtml+xml, */*";
            request.ContentType = strContentType;// "application/json";
            byte[] buffer = Encoding.UTF8.GetBytes(strBody);
            request.ContentLength = buffer.Length;
            request.GetRequestStream().Write(buffer, 0, buffer.Length);
            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
            {
                return reader.ReadToEnd();
            }
        }

        /// <summary>
        /// POST方式调用
        /// </summary>
        /// <param name="strUrl">调用地址</param>
        /// <param name="strContentType">application/json;application/xml;application/x-www-form-urlencoded</param>
        /// <param name="strCallMethod">调用方法</param>
        /// <param name="strPostData">POST参数</param>
        /// <param name="bShowErrMsg">显示错误信息</param>
        /// <returns></returns>
        public static string PostCallUrlForTxt(string strUrl, string strContentType, string strCallMethod, string strPostData, bool bShowErrMsg = true)
        {
            string strReturn = string.Empty;
            try
            {
                byte[] dataArray = null;
                //设置编码规格
                if (strPostData.Length > 0)
                {
                    dataArray = Encoding.UTF8.GetBytes(strPostData);
                }
                //创建Web请求
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(strUrl + "/" + strCallMethod);
                request.Method = "POST";
                request.ContentType = strContentType;
                if (strPostData.Length > 0)
                {
                    request.ContentLength = dataArray.Length;
                    //写入post参数;获取用于写入请求数据的Stream对象
                    Stream Writer = request.GetRequestStream();
                    //把参数数据写入请求数据流
                    Writer.Write(dataArray, 0, dataArray.Length);
                    Writer.Close();
                }
                else
                {
                    request.ContentLength = 0;
                }
                //返回值用以下方法解析;获得相应
                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                //获取响应流
                Stream stream = response.GetResponseStream();
                Encoding encoding = Encoding.GetEncoding("UTF-8");
                StreamReader sr = new StreamReader(stream, encoding);
                strReturn = sr.ReadToEnd();
                sr.Close();
                response.Close();
            }
            catch (Exception ex)
            {
                if (bShowErrMsg)
                    Cs02MessageBox.ShowWarning($"调用POST接口出错了！\r\n错误信息：{ex.Message}");
                strReturn = string.Empty;
            }
            return strReturn;
        }

        /// <summary>
        /// Get方式调用
        /// </summary>
        /// <param name="strUrl">调用地址</param>
        /// <param name="strContentType">application/json;application/xml;application/x-www-form-urlencoded</param>
        /// <param name="strCallMethod">调用方法</param>
        /// <param name="strGetParam">GET参数</param>
        /// <param name="bShowErrMsg">显示错误信息</param>
        /// <returns></returns>
        public static string GetCallUrlForTxt(string strUrl, string strContentType, string strCallMethod, string strGetParam, bool bShowErrMsg = true)
        {
            string strReturn = string.Empty;
            try
            {
                //创建Web请求
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(strUrl + "/" + strCallMethod + "?" + strGetParam);
                request.Method = "GET";
                request.ContentType = strContentType;
                //返回值用以下方法解析;获得相应
                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                //获取响应流
                Stream stream = response.GetResponseStream();
                Encoding encoding = Encoding.GetEncoding("UTF-8");
                StreamReader sr = new StreamReader(stream, encoding);
                strReturn = sr.ReadToEnd();
                sr.Close();
                response.Close();
            }
            catch (Exception ex)
            {
                if (bShowErrMsg)
                    Cs02MessageBox.ShowWarning($"调用GET接口出错了！\r\n错误信息：{ex.Message}");
                strReturn = string.Empty;
            }
            return strReturn;
        }

        /// <summary>
        /// IDictionary转换string
        /// </summary>
        /// <param name="dicParams"></param>
        /// <returns></returns>
        private static string IDictConvertStr(IDictionary<string, string> dicParams)
        {
            StringBuilder sbBuffer = new StringBuilder();
            if (dicParams != null && dicParams.Count > 0)
            {
                int i = 0;
                foreach (string key in dicParams.Keys)
                {
                    if (string.IsNullOrEmpty(dicParams[key]))
                    {
                        continue;
                    }
                    if (i > 0)
                    {
                        sbBuffer.AppendFormat("&{0}={1}", key, dicParams[key]);
                    }
                    else
                    {
                        sbBuffer.AppendFormat("{0}={1}", key, dicParams[key]);
                    }
                    i++;
                }
            }
            return sbBuffer.ToString();
        }

        /// <summary>
        /// Get方式调用
        /// </summary>
        /// <param name="strUrl">调用地址</param>
        /// <param name="strContentType">application/json;application/xml;application/x-www-form-urlencoded</param>
        /// <param name="strCallMethod">调用方法</param>
        /// <param name="dicParam">GET参数字典</param>
        /// <param name="bShowErrMsg">显示错误信息</param>
        /// <returns></returns>
        public static string GetCallUrlForTxt(string strUrl, string strContentType, string strCallMethod, IDictionary<string, string> dicParam, bool bShowErrMsg = true)
        {
            string strParam = IDictConvertStr(dicParam);
            return GetCallUrlForTxt(strUrl, strContentType, strCallMethod, strParam, bShowErrMsg);
        }

    }

}
