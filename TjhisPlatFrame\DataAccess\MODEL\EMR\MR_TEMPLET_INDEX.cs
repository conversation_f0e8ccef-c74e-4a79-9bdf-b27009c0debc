﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Model.EMR
{
    public class MR_TEMPLET_INDEX
    {
        #region Model
        private string templetId;
        private string templetFileName;
        private string accessPath;
        private string topic;
        private string deptCode;
        private string creatorId;
        private DateTime? createDateTime;
        private DateTime? lastModifyDateTime;
        private string permission;
        private string dataModelClass;
        private string dataModelCode;
        private string mrTypeCode;
        private int? operateLevel;
        private int? isNew;
        private string mrVersion;
        private string nursingTypeId;
        /// <summary>
        /// 草稿模板标识
        /// </summary>
        public string Nursing_Type_ID
        {
            set { nursingTypeId = value; }
            get { return nursingTypeId; }
        }
        /// <summary>
        /// 草稿模板标识
        /// </summary>
        public string TEMPLET_ID
        {
            set { templetId = value; }
            get { return templetId; }
        }
        /// <summary>
        /// 草稿模板文件名
        /// </summary>
        public string TEMPLET_FILE_NAME
        {
            set { templetFileName = value; }
            get { return templetFileName; }
        }
        /// <summary>
        /// 存取路径
        /// </summary>
        public string ACCESS_PATH
        {
            set { accessPath = value; }
            get { return accessPath; }
        }
        /// <summary>
        /// 主题
        /// </summary>
        public string TOPIC
        {
            set { topic = value; }
            get { return topic; }
        }
        /// <summary>
        /// 所属科室
        /// </summary>
        public string DEPT_CODE
        {
            set { deptCode = value; }
            get { return deptCode; }
        }
        /// <summary>
        /// 创建者
        /// </summary>
        public string CREATOR_ID
        {
            set { creatorId = value; }
            get { return creatorId; }
        }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CREATE_DATE_TIME
        {
            set { createDateTime = value; }
            get { return createDateTime; }
        }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MODIFY_DATE_TIME
        {
            set { lastModifyDateTime = value; }
            get { return lastModifyDateTime; }
        }
        /// <summary>
        /// 允许访问权 T=允许 1=不允许
        /// </summary>
        public string PERMISSION
        {
            set { permission = value; }
            get { return permission; }
        }
        /// <summary>
        /// 数据模型类别
        /// </summary>
        public string DATA_MODEL_CLASS
        {
            set { dataModelClass = value; }
            get { return dataModelClass; }
        }
        /// <summary>
        /// 数据模型代码
        /// </summary>
        public string DATA_MODEL_CODE
        {
            set { dataModelCode = value; }
            get { return dataModelCode; }
        }
        /// <summary>
        /// 模板类型分类
        /// </summary>
        public string MR_TYPE_CODE
        {
            set { mrTypeCode = value; }
            get { return mrTypeCode; }
        }
        /// <summary>
        /// 操作级别
        /// </summary>
        public int? OPERATE_LEVEL
        {
            set { operateLevel = value; }
            get { return operateLevel; }
        }
        /// <summary>
        /// 模板来源
        /// </summary>
        public int? IS_NEW
        {
            set { isNew = value; }
            get { return isNew; }
        }
        /// <summary>
        /// 模板类型分类
        /// </summary>
        public string MR_VERSION
        {
            set { mrVersion = value; }
            get { return mrVersion; }
        }
        #endregion Model
    }
}
