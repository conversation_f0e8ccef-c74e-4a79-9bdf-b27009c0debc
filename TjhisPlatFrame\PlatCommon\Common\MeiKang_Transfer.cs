﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace PlatPublic.Common
{
    public class MeiKang_Transfer
    {
        /// <summary>
        /// 美康接口初始化
        /// </summary>
        /// <param name="pcCheckMode">住院类型</param>
        /// <param name="pcHisCode">医院名称(汉字)</param>
        /// <param name="pcDoctorCode">医生编码</param>
        public static int MeiKang_Load(String pcCheckMode, String pcHisCode, String pcDoctorCode)
        {
            try
            {
                int Load_State = MeiKang_Imp.MDC_Init(pcCheckMode, pcHisCode, pcDoctorCode);

                switch (Load_State)
                {
                    case 0:
                        //MeiKang_Imp.MDC_GetLastError();
                        XtraMessageBox.Show("合理用药接口调用PASS初始化接口失败!");
                        break;
                    case 1:
                        //XtraMessageBox.Show("合理用药接口调用成功!");
                        break;
                    case 2:
                        //MeiKang_Imp.MDC_GetLastError();
                        XtraMessageBox.Show("合理用药接口连接PASS服务器失败!");
                        break;
                    case 3:
                        //MeiKang_Imp.MDC_GetLastError();
                        XtraMessageBox.Show("合理用药接口获取审查、查询列表出错!");
                        break;
                    case 4:
                        //MeiKang_Imp.MDC_GetLastError();
                        XtraMessageBox.Show("合理用药接口初始化工具条出错!");
                        break;
                    case 5:
                        //MeiKang_Imp.MDC_GetLastError();
                        XtraMessageBox.Show("合理用药接口更新资源文件出错!");
                        break;
                }
                return Load_State;
            }
            catch (Exception ex)
            {
                //MeiKang_Imp.MDC_GetLastError();
                XtraMessageBox.Show("合理用药接口PASS4Invoke.dll文件不存在,合理用药监测系统无法正常使用!请与系统管理员联系!", "提示");
                return 0;
            }
        }

        /// <summary>
        /// 病人基本信息函数
        /// </summary>
        /// <param name="pcPatCode">病人ID</param>
        /// <param name="pcInHospNo">病人门诊号或住院号</param>
        /// <param name="pcVisitCode">病人就诊次数或住院次数</param>
        /// <param name="pcName">病人姓名</param>
        /// <param name="pcSex">病人性别</param>
        /// <param name="pcBirthday">病人出生日期</param>
        /// <param name="pcHeightCM">病人身高</param>
        /// <param name="pcWeighKG">体重</param>
        /// <param name="pcDeptCode">科室编码</param>
        /// <param name="pcDeptName">科室名称</param>
        /// <param name="pcDoctorCode">医生编码</param>
        /// <param name="pcDoctorName">医生名称</param>
        /// <param name="piPatStatus">病人状态1表示住院病人（默认），2表示门诊病人，3表示急诊病人。用于采集病人的类型</param>
        /// <param name="piIsLactation">哺乳状态 取值： -1-无法获取哺乳状态（默认）;0-不是;1-是</param>
        /// <param name="piIsPregnancy">妊娠状态 取值： -1-无法获取妊娠状态（默认）;0-不是;1-是</param>
        /// <param name="pcPregStartDate">妊娠开始日期</param>
        /// <param name="piHepDamageDegree">肝损害程度 取值： -1-不确定（默认）；0-无肝损害；1-肝功能不全；2-轻度肝损害；3-中度肝损害；4-重度肝损害</param>
        /// <param name="piRenDamageDegree">肾损害程度 取值： -1-不确定（默认）；0-无肾损害；1-肾功能不全；2-轻度肾损害；3-中度肾损害；4-重度肾损害</param>
        /// <returns></returns>
        public static int MDC_SetPatient(string pcPatCode, string pcVisitCode)
        {
            int piPatStatus = 0, piIsLactation = -1, piIsPregnancy = -1, piHepDamageDegree = -1, piRenDamageDegree = -1;
            string sql = string.Empty;
            if (pcVisitCode.Length < 5)
            {
                //住院患者
                piPatStatus = 1;
                sql = @"
                    select a.patient_id,
                           a.inp_no,
                           a.visit_id,
                           b.name,
                           b.sex,
                           b.date_of_birth,
                           a.body_height,
                           a.body_weight,
                           c.dept_code,
                           (select d.dept_name from dept_dict d where d.dept_code = c.dept_code) as dept_name,
                           c.Doctor_In_Charge,
                           (select e.name from staff_dict e where e.user_name = c.doctor_in_charge) as Uname,
                           (select f.diagnosis_code
                              from diagnosis f
                             where f.patient_id = c.patient_id
                               and f.visit_id = c.visit_id
                               and f.diagnosis_type = '2'
                               and f.diagnosis_no = 1) as diagnosis_no,
                           (select f.diagnosis_desc
                              from diagnosis f
                             where f.patient_id = c.patient_id
                               and f.visit_id = c.visit_id
                               and f.diagnosis_type = '2'
                               and f.diagnosis_no = 1) as diagnosis_desc
                      from pat_visit a, pat_master_index b, pats_in_hospital c
                     where a.patient_id = b.patient_id
                       and b.patient_id = c.patient_id
                       and a.patient_id = c.patient_id
                       and a.visit_id = c.visit_id
                       and a.patient_id = '" + pcPatCode + @"'
                       and a.visit_id = '" + pcVisitCode + "' ";
            }
            else
            {
                //门诊患者
                piPatStatus = 2;
                sql = @"
                    select a.patient_id,
                           b.inp_no as inp_no1,
                           a.visit_no as inp_no,
                           a.visit_no,
                           b.name,
                           b.sex,
                           b.date_of_birth,
                           '' body_height,
                           '' body_weight,
                           a.visit_dept dept_code,
                           (select d.dept_name from dept_dict d where d.dept_code = a.visit_dept) as dept_name,
                           a.doctor Doctor_In_Charge,
                           (select e.name from staff_dict e where e.user_name = a.doctor) as Uname,
                           (select f.diagnosis_code
                              from outp_diagnosis f
                             where f.clinic_no = a.clinic_no
                               and rownum = 1) as diagnosis_no,
                           (select f.diagnosis_desc
                              from outp_diagnosis f
                             where f.clinic_no = a.clinic_no
                               and rownum = 1) as diagnosis_desc
                      from clinic_master a, pat_master_index b
                     where a.patient_id = b.patient_id
                       and a.clinic_no = '" + pcVisitCode + "' ";
            }

            try
            {
                DataSet ds = new NM_Service.NMService.ServerPublicClient().GetList(sql);
                int result = 2;
                if (ds.Tables[0].Rows.Count > 0)
                {
                    string pcInHospNo = ds.Tables[0].Rows[0]["inp_no"].ToString(), pcName = ds.Tables[0].Rows[0]["name"].ToString(), pcSex = ds.Tables[0].Rows[0]["sex"].ToString(), pcBirthday = Convert.ToDateTime(ds.Tables[0].Rows[0]["date_of_birth"].ToString()).ToString("yyyy-MM-dd"), pcHeightCM = ds.Tables[0].Rows[0]["body_height"].ToString(), pcWeighKG = ds.Tables[0].Rows[0]["body_weight"].ToString(),
                        pcDeptCode = ds.Tables[0].Rows[0]["dept_code"].ToString(), pcDeptName = ds.Tables[0].Rows[0]["dept_name"].ToString(), pcDoctorCode = ds.Tables[0].Rows[0]["Doctor_In_Charge"].ToString(), pcDoctorName = ds.Tables[0].Rows[0]["Uname"].ToString(), pcPregStartDate = "";
                    return MeiKang_Imp.MDC_SetPatient(pcPatCode, pcInHospNo, pcVisitCode, pcName, pcSex, pcBirthday, pcHeightCM, pcWeighKG, pcDeptCode, pcDeptName, pcDoctorCode, pcDoctorName, piPatStatus, piIsLactation, piIsPregnancy, pcPregStartDate, piHepDamageDegree, piRenDamageDegree);
                }
                if (result == 0)
                {
                    //MeiKang_Imp.MDC_GetLastError();
                    XtraMessageBox.Show("合理用药接口病人基本信息函数调用失败!", "提示");
                }
            }
            catch
            {
                XtraMessageBox.Show("查询患者信息失败，请重新尝试审核用药!", "提示");
                return 0;
            }

            return 0;
        }


        /// <summary>
        /// 传入病人过敏史函数
        /// </summary>
        /// <param name="pcPatCode"></param>
        /// <param name="pcVisitCode"></param>
        /// <returns></returns>
        public static int MDC_AddAller(string pcPatCode, string pcVisitCode)
        {
            string pcIndex = string.Empty; //过敏源序号
            string pcAllerCode = string.Empty; //过敏源代码
            string pcAllerName = string.Empty; //过敏源名称
            string pcAllerSymptom = string.Empty; //过敏源症状
            int result = -1;
            string sql = string.Empty;

            //截图测试用
            pcIndex = "1";
            pcAllerCode = "10101";
            pcAllerName = "青霉素类";
            pcAllerSymptom = "红疹";

            result = MeiKang_Imp.MDC_AddAller(pcIndex, pcAllerCode, pcAllerName, pcAllerSymptom);
            if (result == 0)
            {
                XtraMessageBox.Show("合理用药接口传入病人过敏史信息函数调用失败!", "提示");
            }
            return result;
        }

        /// <summary>
        /// 传入病人诊断信息函数
        /// </summary>
        /// <param name="pcIndex">诊断序号</param>
        /// <param name="pcDiseaseCode">诊断唯一码</param>
        /// <param name="pcDiseaseName">诊断名称</param>
        /// <param name="pcRecipNo">处方号</param>
        /// <returns></returns>
        public static int MDC_AddMedCond(string pcPatCode, string pcVisitCode)
        {
            string sql;
            if (pcVisitCode.Length < 5)
            {
                //住院患者
                sql = @"
                    select (select f.diagnosis_code
                              from diagnosis f
                             where f.patient_id = c.patient_id
                               and f.visit_id = c.visit_id
                               and f.diagnosis_type = '2'
                               and f.diagnosis_no = 1) as diagnosis_no,
                           (select f.diagnosis_desc
                              from diagnosis f
                             where f.patient_id = c.patient_id
                               and f.visit_id = c.visit_id
                               and f.diagnosis_type = '2'
                               and f.diagnosis_no = 1) as diagnosis_desc
                      from pat_visit a, pat_master_index b, pats_in_hospital c
                     where a.patient_id = b.patient_id
                       and b.patient_id = c.patient_id
                       and a.patient_id = c.patient_id
                       and a.visit_id = c.visit_id
                       and a.patient_id = '" + pcPatCode + @"'
                       and a.visit_id = '" + pcVisitCode + "' ";
            }
            else
            {
                //门诊患者
                sql = @"
                    select diagnosis_code as diagnosis_no, diagnosis_desc as diagnosis_desc
                      from (select *
                              from outp_diagnosis a
                             where a.clinic_no = '" + pcVisitCode + @"'
                             order by a.diagnosis_time desc)
                     /*where rownum = 1*/ ";
            }

            DataSet ds = new NM_Service.NMService.ServerPublicClient().GetList(sql);
            string pcDiseaseCode = string.Empty;
            string pcDiseaseName = string.Empty;
            int result = -1;
            if (ds.Tables[0].Rows.Count > 0)
            {
                for (int i = 1; i <= ds.Tables[0].Rows.Count; i++)
                {
                    pcDiseaseCode = ds.Tables[0].Rows[0]["diagnosis_no"].ToString();
                    pcDiseaseName = ds.Tables[0].Rows[0]["diagnosis_desc"].ToString();
                    string pcIndex = i.ToString(), pcRecipNo = "";
                    result = MeiKang_Imp.MDC_AddMedCond(pcIndex, pcDiseaseCode, pcDiseaseName, pcRecipNo);
                    if (result == 0)
                    {
                        //MeiKang_Imp.MDC_GetLastError();
                        XtraMessageBox.Show("合理用药接口传入病人诊断信息函数调用失败!", "提示");
                        break;
                    }
                }

            }

            return result;
        }

        /// <summary>
        /// 传入病人入院诊断信息函数 住院
        /// </summary>
        /// <param name="pcIndex">诊断序号</param>
        /// <param name="pcDiseaseCode">诊断唯一码</param>
        /// <param name="pcDiseaseName">诊断名称</param>
        /// <param name="pcRecipNo">处方号</param>
        /// <returns></returns>
        public static int MDC_AddMedCondIn(string pcPatCode, string pcVisitCode)
        {

            string sql = " select * from diagnosis where patient_id='" + pcPatCode + "' and visit_id ='" + pcVisitCode + "'";
            try
            {
                int result = 1;
                DataSet ds = new NM_Service.NMService.ServerPublicClient().GetList(sql);
                string pcIndex = "", pcDiseaseCode = "", pcDiseaseName = "", pcRecipNo = "";
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow dr in ds.Tables[0].Rows)
                    {
                        pcDiseaseCode = dr["DIAGNOSIS_NO"].ToString();
                        if (string.IsNullOrEmpty(pcDiseaseCode)) continue;
                        pcDiseaseName = dr["DIAGNOSIS_DESC"].ToString();
                        pcIndex = pcPatCode + "-" + pcVisitCode + "-" + dr["DIAGNOSIS_TYPE"].ToString() + "-" + pcDiseaseCode;
                        result = result * MeiKang_Imp.MDC_AddMedCond(pcIndex, pcDiseaseCode, pcDiseaseName, pcRecipNo);
                    }
                }
                else
                {
                    result = 0;
                }
                if (result == 0)
                {
                    //MeiKang_Imp.MDC_GetLastError();
                    XtraMessageBox.Show("合理用药接口传入病人诊断信息函数调用失败!", "提示");
                }
                return result;
            }
            catch
            {
                return 0;
            }

        }

        /// <summary>
        /// 传入查询药品函数
        /// </summary>
        /// <param name="pcDrugUniqueCode">药品唯一码</param>
        /// <param name="pcDrugName">药品名称</param>
        /// <returns></returns>
        public static int MDC_DoSetDrug(string pcDrugUniqueCode, string pcDrugName)
        {
            int result = MeiKang_Imp.MDC_DoSetDrug(pcDrugUniqueCode, pcDrugName);
            if (result == 0)
            {
                //MeiKang_Imp.MDC_GetLastError();
                XtraMessageBox.Show("合理用药接口传入查询药品函数调用失败!", "提示");
            }
            return result;
        }

        /// <summary>
        /// 药品信息查询函数
        /// </summary>
        /// <param name="piQueryType">11-药品说明书,51-简要信息(浮动窗口)</param>
        /// <returns></returns>
        public static int MDC_DoRefDrug(int piQueryType)
        {
            int result = MeiKang_Imp.MDC_DoRefDrug(piQueryType);
            if (result == 0)
            {
                //MeiKang_Imp.MDC_GetLastError();
                XtraMessageBox.Show("合理用药接口药品信息查询函数调用失败!", "提示");
            }
            return result;
        }

        /// <summary>
        /// 传入病人药品记录信息
        /// </summary>
        /// <param name="pcIndex">主键</param>
        /// <param name="piOrderNo">医嘱编号</param>
        /// <param name="pcDrugUniqueCode">药品代码</param>
        /// <param name="pcDrugName">药品名称</param>
        /// <param name="pcDosePerTime">每次使用剂量的数字部分</param>
        /// <param name="pcDoseUnit">每次服用剂量单位</param>
        /// <param name="pcFrequency">频次</param>
        /// <param name="pcRouteCode">给药途径编码</param>
        /// <param name="pcRouteName">给药途径名称</param>
        /// <param name="pcStartTime">开立医嘱日期 yyyy-mm-dd hh:mm:ss</param>
        /// <param name="pcEndTime">停嘱日期 yyyy-mm-dd hh:mm:ss</param>
        /// <param name="pcExecuteTime">执行医嘱时间 yyyy-mm-dd hh:mm:ss</param>
        /// <param name="pcGroupTag">成组医嘱标记，如果此参数值相同，则表示是配制在一起用</param>
        /// <param name="pcIsTempDrug">长期医嘱还是临时医嘱 0长期 1临时。由于临时医嘱不审重复用药，所以门诊临时处方应传’0’，以免漏审重复用药。</param>
        /// <param name="pcOrderType">医嘱类别，0在用(默认) 1已作废 2已停嘱 3出院带药 4取药医嘱 除了0其它都不参与审核</param>
        /// <param name="pcDeptCode">开嘱科室编码</param>
        /// <param name="pcDeptName">开嘱科室名称</param>
        /// <param name="pcDoctorCode">开嘱医生编码</param>
        /// <param name="pcDoctorName">开嘱医生姓名</param>
        /// <param name="pcRecipNo">处方号，门诊处方处方专用，住院传空</param>
        /// <param name="pcNum">药品开出数量，门诊处方审查专用，住院传空</param>
        /// <param name="pcNumUnit">药品开出数量单位，门诊处方审查专用，住院传空</param>
        /// <param name="pcPurpose">用药目的，1可能预防 2可能治疗 3预防 4治疗 5预防+治疗, 默认值为0</param>
        /// <param name="pcOprCode">手术编号，如果对应多手术，用'，'隔开，表示该药为该编号对应的手术用药</param>
        /// <param name="pcMediTime">手术用药时机类型 0非手术用药 1术前0.5h以内用药 2术前0.5-2h内 3术前大于2h用药 4术中用药 5术后用药</param>
        /// <param name="pcRemark">医嘱备注信息</param>
        /// <returns></returns>
        public static int MDC_AddScreenDrug(
                    String pcIndex, int piOrderNo, String pcDrugUniqueCode, String pcDrugName, String pcDosePerTime,
                    String pcDoseUnit, String pcFrequency, String pcRouteCode, String pcRouteName, String pcStartTime,
                    String pcEndTime, String pcExecuteTime, String pcGroupTag, String pcIsTempDrug, String pcOrderType,
                    String pcDeptCode, String pcDeptName, String pcDoctorCode, String pcDoctorName, String pcRecipNo,
                    String pcNum, String pcNumUnit, String pcPurpose, String pcOprCode, String pcMediTime,
                    String pcRemark)
        {
            int result = MeiKang_Imp.MDC_AddScreenDrug(
                pcIndex, piOrderNo, pcDrugUniqueCode, pcDrugName, pcDosePerTime,
                pcDoseUnit, pcFrequency, pcRouteCode, pcRouteName, pcStartTime,
                pcEndTime, pcExecuteTime, pcGroupTag, pcIsTempDrug, pcOrderType,
                pcDeptCode, pcDeptName, pcDoctorCode, pcDoctorName, pcRecipNo,
                pcNum, pcNumUnit, pcPurpose, pcOprCode, pcMediTime,
                pcRemark);
            if (result == 0)
            {
                //MeiKang_Imp.MDC_GetLastError();
                XtraMessageBox.Show("合理用药接口传入病人诊断信息函数调用失败!", "提示");
            }
            return result;
        }

        /// <summary>
        /// 调用病人补充信息
        /// </summary>
        /// <param name="pcJson"></param>
        /// <returns></returns>
        public static int MDC_AddJsonInfo(string pcJson)
        {
            int result = MeiKang_Imp.MDC_AddJsonInfo(pcJson);
            if (result == 0)
            {
                XtraMessageBox.Show("合理用药接口补充信息调用失败!", "提示");
            }
            return result;
        }

        /// <summary>
        /// 审查函数
        /// </summary>
        /// <param name="piShowMode"></param>
        /// <param name="piIsSave"></param>
        /// <returns></returns>
        public static int MDC_DoCheck(int piShowMode, int piIsSave)
        {
            int result = MeiKang_Imp.MDC_DoCheck(piShowMode, piIsSave);
            if (result == 0)
            {
                //MeiKang_Imp.MDC_GetLastError();
                XtraMessageBox.Show("合理用药接口审查函数调用失败!", "提示");
            }
            return result;
        }

        /// <summary>
        /// 获取药品医嘱警示级别
        /// </summary>
        /// <param name="pcIndex">正常传主键获取单独药品警示结果，传空的话获取所有药品的最大警示结果</param>
        /// <returns></returns>
        public static int MDC_GetWarningCode(string pcIndex)
        {
            return MeiKang_Imp.MDC_GetWarningCode(pcIndex);
        }

        /// <summary>
        /// 封装的审核函数
        /// </summary>
        /// <returns>0正常, -1非正常</returns>
        public static int DoCheck(bool messageFlag = true)
        {
            //审查函数
            MDC_DoCheck(1, 1);

            //获取审查结果 
            //0 - 正常监测，无监测结果，蓝灯。
            //1 - 正常监测，结果为禁忌或严重，黑灯。
            //2 - 正常监测，结果为不推荐，红灯。
            //3 - 正常监测，结果为慎用，橙灯。
            //4 - 正常监测，结果为关注，黄灯。
            int r = MDC_GetWarningCode("");
            if (r > 0)
            {
                string result = string.Empty;
                switch (r)
                {
                    case 1:
                        result = "禁忌";
                        break;
                    case 2:
                        result = "不推荐";
                        break;
                    case 3:
                        result = "慎用";
                        break;
                    case 4:
                        result = "关注";
                        break;
                    default:
                        result = "正常处方";
                        break;
                }
                if (messageFlag)
                {
                    if (XtraMessageBox.Show("合理用药审查结果为：'" + result + "',是否继续？", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.Cancel)
                    {
                        return -1;
                    }
                }
            }
            return 0;
        }

        /// <summary>
        /// PASS退出函数
        /// </summary>
        /// <returns>1成功 0失败</returns>
        public static int MDC_Quit()
        {
            return MeiKang_Imp.MDC_Quit();
        }

        /// <summary>
        /// 审查结果详细信息函数
        /// </summary>
        /// <param name="pcIndex"></param>
        /// <returns></returns>
        public static int MDC_GetResultDetail(string pcIndex)
        {
            int result = MeiKang_Imp.MDC_GetResultDetail(pcIndex);
            if (result == 0)
            {
                //MeiKang_Imp.MDC_GetLastError();
                //XtraMessageBox.Show("提示", "合理用药接口获取审查结果详细信息函数失败!");
            }
            return result;
        }

        /// <summary>
        /// 获取药师干预系统结果
        /// </summary>
        /// <param name="pcPatCode"></param>
        /// <param name="pcInHospNo"></param>
        /// <param name="pcVisitCode"></param>
        /// <param name="pcRecipNo"></param>
        /// <param name="piTaskType"></param>
        /// <returns>1通过 0不通过</returns>
        public static int MDC_GetTaskStatus(String pcPatCode,
                    String pcInHospNo,
                    String pcVisitCode,
                    String pcRecipNo,
                    int piTaskType)
        {
            int result = MeiKang_Imp.MDC_GetTaskStatus(pcPatCode, pcInHospNo, pcVisitCode, pcRecipNo, piTaskType);
            return result;
        }

        /// <summary>
        /// 获取药师干预系统明细
        /// </summary>
        /// <param name="pcPatCode"></param>
        /// <param name="pcInHospNo"></param>
        /// <param name="pcVisitCode"></param>
        /// <param name="pcRecipNo"></param>
        /// <param name="piTaskType"></param>
        /// <returns>详细json串</returns>
        public static string MDC_GetTaskDetail(String pcPatCode,
                    String pcInHospNo,
                    String pcVisitCode,
                    String pcRecipNo,
                    int piTaskType)
        {
            object obj = MeiKang_Imp.MDC_GetTaskDetail(pcPatCode, pcInHospNo, pcVisitCode, pcRecipNo, piTaskType);
            if (obj == null) return "";
            return obj.ToString();
        }
    }
}
