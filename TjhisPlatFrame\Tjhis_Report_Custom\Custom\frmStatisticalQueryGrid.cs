﻿using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraReports.UserDesigner;
using DevExpress.XtraTab;
using PlatCommon.Common;
using PlatCommon.SysBase;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Drawing.Printing;
using System.Windows.Forms;
using Tjhis.Report.Custom.Base;
using Tjhis.Report.Custom.Common;
using Tjhis.Report.Custom.Properties;
using Tjhis.Report.Custom.Srv;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmStatisticalQueryGrid : frmReportBase
    {
        private srvStatisticalQuery srv = null;
        public DataRow drTemplet;
        public string reportID { get; set; }
        public string myAppName { get; set; }

        public string _reportNo;
        //打印报表设计的模板名称
        private string templetePrintFile;

        #region 构造函数
        public frmStatisticalQueryGrid()
        {
            SetStyle(ControlStyles.UserPaint, true);
            SetStyle(ControlStyles.AllPaintingInWmPaint, true); // 禁止擦除背景.
            SetStyle(ControlStyles.DoubleBuffer, true); // 双缓冲

            InitializeComponent();
            //布局重绘闪烁问题

            this.TempleteFile = "";
            gridView1.OptionsCustomization.AllowFilter = false;
            gridView1.OptionsView.ShowGroupPanel = false;
            gridView1.OptionsView.ShowAutoFilterRow = false;
            gridView1.OptionsSelection.MultiSelect = true;
            gridView1.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CellSelect;
            gridView1.OptionsClipboard.CopyColumnHeaders = DevExpress.Utils.DefaultBoolean.False;
        }
        public frmStatisticalQueryGrid(string deptCode,string appCode)
        {
            this.AppCode = appCode;
            this.deptCode = deptCode;
            SetStyle(ControlStyles.UserPaint, true);
            SetStyle(ControlStyles.AllPaintingInWmPaint, true); // 禁止擦除背景.
            SetStyle(ControlStyles.DoubleBuffer, true); // 双缓冲

            InitializeComponent();
            //布局重绘闪烁问题

            this.TempleteFile = "";
            gridView1.OptionsCustomization.AllowFilter = false;
            gridView1.OptionsView.ShowGroupPanel = false;
            gridView1.OptionsView.ShowAutoFilterRow = false;
            gridView1.OptionsSelection.MultiSelect = true;
            gridView1.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CellSelect;
            gridView1.OptionsClipboard.CopyColumnHeaders = DevExpress.Utils.DefaultBoolean.False;
        }

        public frmStatisticalQueryGrid(DataRow drTemplet) : this()
        {
            this.drTemplet = drTemplet;
            this.reportID = drTemplet["DICT_ID"].ToString();
            this.TempleteFile = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString();
            this.TempleteSql = drTemplet["TEMPLET_SQL"].ToString();
        }
        public frmStatisticalQueryGrid(string dictID) : this()
        {
            this.reportID = dictID;
            TempletInfo_load();
        }
        #endregion

        #region 窗体加载
        /// <summary>
        /// 窗体加载
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Form_Load(object sender, EventArgs e)
        {
            try
            {
                if (this.DesignMode)
                    return;
                srv = new srvStatisticalQuery();
                if (string.IsNullOrEmpty(reportID) && this.Tag != null)
                {
                    reportID = Tag.ToString().Split('|')[1];
                    myAppName = Tag.ToString().Split('|')[0];
                    Const.customAppCode = myAppName;
                }
                TempletInfo_load();
                InitBarEditItems();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }
        protected virtual void TempletInfo_load()
        {
            if (drTemplet == null)
            {
                drTemplet = GetTempletNew();

                this.TempleteFile = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString();
                this.TempleteSql = drTemplet["TEMPLET_SQL"].ToString();
                this.Text = drTemplet["DICT_NAME"].ToString();//给窗体标题赋值为报表名称加ID
            }
        }
        protected virtual DataRow GetTempletNew()
        {
            return srv.GetTempletByIDNew(reportID, myAppName);
        }
        #endregion

        protected virtual bool InitRecordColumns(string dictId)
        {
            string strLastReportNo = _reportNo;
            if (ParamList != null)
            {
                foreach (ParamClass a in ParamList)
                {
                    if (a.GetParamName().ToUpper() == "REPORTNO")
                    {
                        _reportNo = a.EditValue().ToString();
                        break;
                    }
                }
            }
            int iReportNo = 0;
            if (!string.IsNullOrEmpty(_reportNo))
            {
                int.TryParse(_reportNo, out iReportNo);
                if (iReportNo <= 0)
                {
                    MessageBox.Show("参数[REPORTNO]配置错误，请修改(值必须是大于0的整数)！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
            }
            if (strLastReportNo == _reportNo && strLastReportNo != null && _reportNo != null)
            {
                //分类选择未有改变不需要重新设置表格列头
            }
            else
            {
                strLastReportNo = _reportNo;
                gridView1.Columns.Clear();
                DataSet dsRecordConfig;
                if (iReportNo <= 0)
                {
                    dsRecordConfig = GetReportConfig(dictId, myAppName);
                }
                else
                {
                    dsRecordConfig = srv.GetReportConfigReportNo(dictId, myAppName, iReportNo);
                }
                if (dsRecordConfig != null || dsRecordConfig.Tables[0].Rows.Count > 0)
                {
                    GridViewHelper.InitGridViewColumns(gridView1, dsRecordConfig.Tables[0]);
                }
                gridView1.OptionsCustomization.AllowFilter = true;
            }
            return true;
        }
        protected virtual DataSet GetReportConfig(string dictId, string strAppName)
        {
            return srv.GetReportConfig(dictId, strAppName);
        }

        #region 自定义参数控件初始化
        /// <summary>
        /// 自定义参数控件初始化
        /// </summary>
        protected virtual void InitBarEditItems()
        {
            DataSet dsParam = GetReportParamNew(myAppName, reportID);
            ImpBarEditItems(dsParam);
            InitFixedBarItems();
        }

        protected virtual DataSet GetReportParamNew(string strAppName, string reportID)
        {
            return srv.GetReportParamNew(strAppName, reportID);
        }
        protected void ClearBarManager()
        {
            bar2.ItemLinks.Clear();
        }
        protected void ImpBarEditItems(DataSet ds)
        {
            // 基础数据准备
            ClearBarManager();
            DataSet dsParam = ds;
            ParamList = new List<ParamClass>();
            if (dsParam == null)
                return;
            foreach (DataRow drParam in dsParam.Tables[0].Rows)
            {
                try
                {
                    CreateParamControl(dsParam, drParam, ParamList);
                }
                catch (Exception e)
                {
                    MessageBox.Show(drParam["PARAM_NAME"].ToString() + "：初始化错误，信息：" + e.Message);
                }
            }
        }

        private void InitFixedBarItems()
        {
            // 
            // barQuery
            // 
            DevExpress.XtraBars.BarLargeButtonItem barQuery = new BarLargeButtonItem();
            barQuery.Caption = "查询(&S)";
            barQuery.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Bottom;
            barQuery.Id = 8;
            barQuery.ImageOptions.Image = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("Find_32x32")));
            barQuery.ImageOptions.LargeImage = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("Find_32x32")));
            barQuery.Name = "barQuery";
            barQuery.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barQuery_ItemClick);
            bar2.ItemLinks.Add(barQuery, barQuery.Caption);
            if (IsShowFunctionButton)
            {


                DevExpress.XtraBars.BarSubItem barSubItem1 = new BarSubItem();
                DevExpress.XtraBars.BarSubItem barExportPdf = new BarSubItem();
                DevExpress.XtraBars.PopupMenu popupMenu_export = new DevExpress.XtraBars.PopupMenu();

                // barExport
                // 
                DevExpress.XtraBars.BarLargeButtonItem barExport = new BarLargeButtonItem(barManager1, "导出");
                bar2.AddItem(barExport);

                barExport.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
                barExport.ImageOptions.Image = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("Export_32x32")));
                barExport.ImageOptions.LargeImage = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("Export_32x32")));
                barExport.Name = "barExport";
                barExport.ActAsDropDown = true;
                barExport.DropDownEnabled = true;
                barExport.DropDownControl = popupMenu_export;

                // 
                // popupMenu_export
                // 
                popupMenu_export.ItemLinks.Add(barSubItem1);
                popupMenu_export.ItemLinks.Add(barExportPdf);

            //    popupMenu_export.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            //new DevExpress.XtraBars.LinkPersistInfo(barSubItem1),
            //new DevExpress.XtraBars.LinkPersistInfo(barExportPdf)});
                popupMenu_export.Manager = this.barManager1;
                popupMenu_export.Name = "popupMenu_export";
                // 
                // barSubItem1
                // 
                barSubItem1.AllowDrawArrow = DevExpress.Utils.DefaultBoolean.False;
                barSubItem1.Caption = "XLS";
                barSubItem1.Id = 38;
                barSubItem1.Name = "barSubItem1";
                barSubItem1.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barExport_Xls_ItemClick);
                // 
                // barExportPdf
                // 
                barExportPdf.AllowDrawArrow = DevExpress.Utils.DefaultBoolean.False;
                barExportPdf.Caption = "PDF";
                barExportPdf.Id = 39;
                barExportPdf.Name = "barExportPdf";
                barExportPdf.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barExportPdf_ItemClick);


                DevExpress.XtraBars.BarLargeButtonItem barPrint = new BarLargeButtonItem();
                // 
                // barPrint
                // 
                barPrint.Caption = "显示打印(&P)";
                barPrint.Id = 10;
                barPrint.ImageOptions.Image = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("Print_32x32")));
                barPrint.ImageOptions.LargeImage = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("Print_32x32")));
                barPrint.Name = "barPrint";
                barPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barPrint_ItemClick);
                bar2.ItemLinks.Add(barPrint, barPrint.Caption);


                // 
                // barReportPrint
                // 
                if (GetPrintTemplateFile())
                {
                    DevExpress.XtraBars.BarLargeButtonItem barReportPrint = new BarLargeButtonItem();
                    barReportPrint.Caption = "特殊打印";
                    barReportPrint.Id = 36;
                    barReportPrint.ImageOptions.Image = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("DefaultPrinterNetwork_32x32")));
                    barReportPrint.ImageOptions.LargeImage = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("DefaultPrinterNetwork_32x32")));
                    barReportPrint.Name = "barReportPrint";
                    barReportPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barReportPrint_ItemClick);
                    bar2.ItemLinks.Add(barReportPrint, barReportPrint.Caption);
                }
                // 
                // barClose
                //
                DevExpress.XtraBars.BarLargeButtonItem barClose = new BarLargeButtonItem();
                barClose.Caption = "关闭(&C)";
                barClose.Id = 11;
                barClose.ImageOptions.Image = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("Cancel_32x32")));
                barClose.ImageOptions.LargeImage = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("Cancel_32x32")));
                barClose.Name = "barClose";
                barClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barClose_ItemClick);
                bar2.ItemLinks.Add(barClose, barClose.Caption);
            }
        }

        private bool GetPrintTemplateFile()
        {
            templetePrintFile = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString() + "_Print";
            if (!string.IsNullOrEmpty(_reportNo))
            {
                templetePrintFile = templetePrintFile + "_" + _reportNo;
            }

            string fileName = System.IO.Path.Combine(Application.StartupPath, @"Reports\" + myAppName + "\\" + templetePrintFile + ".repx");
            if (System.Diagnostics.Debugger.IsAttached)
            {
                fileName = System.IO.Path.Combine(Application.StartupPath.Replace(@"bin\Debug", ""), "Reports\\" + myAppName + "\\") + templetePrintFile + ".repx";
            }

            return System.IO.File.Exists(fileName);
        }
        #endregion

        protected List<ParamClass> ParamList;
        public Hashtable Map;
        /// <summary>
        /// 创建自定义参数控件
        /// </summary>
        /// <param name="dsParam">报表所包含的自定义参数数据</param>
        /// <param name="dr">要创建的参数数据</param>
        /// <param name="list">创建参数控件完成的集合</param>
        protected void CreateParamControl(DataSet dsParam, DataRow dr, List<ParamClass> list)
        {
            ParamClass newPc = ParamList.Find(pc => pc.GetParamName().Equals(dr["PARAM_NAME"].ToString()));
            if (newPc == null)
            {
                DataTable dt = null;

                if ("1".Equals(dr["EDIT_TYPE"]) || "5".Equals(dr["EDIT_TYPE"]))
                    dt = srv.GetEditDataSource(dr["SOURCE_TYPE"].ToString(), dr["DATA_SOURCE_SQL"].ToString());
                //ParamClass editItems = new ParamClass(dr["PARAM_NAME"].ToString(), dr["EDIT_TYPE"].ToString(), dr["CAPTION"].ToString(), dr["DISPLAY_MEMBER"].ToString(), dr["VALUE_MEMBER"].ToString(), dt, dr["DEFAULT_VALUE"].ToString(), int.Parse(dr["CONTROL_WIDTH"].ToString()));
                ParamClass editItems = new ParamClass(dr, dt);
                bar2.ItemLinks.Add(editItems.GetEditItem(barManager1), dr["CAPTION"].ToString());
                list.Add(editItems);

                if (dsParam.Tables[0].Columns.Contains("NEXT_PARAM_NAME") && !string.IsNullOrEmpty(dr["NEXT_PARAM_NAME"].ToString()))
                {
                    DataRow[] drr = dsParam.Tables[0].Select("PARAM_NAME = '" + dr["NEXT_PARAM_NAME"].ToString() + "'");
                    if (drr.Length > 0)
                    {
                        CreateParamControl(dsParam, drr[0], list);
                        ParamClass nextPc = ParamList.Find(pc => pc.GetParamName().Equals(drr[0]["PARAM_NAME"].ToString()));
                        if (nextPc != null)
                        {
                            editItems.SetNextEdit(nextPc);
                        }
                    }
                }
            }
        }

        #region 获取参数
        /// <summary>
        /// 获取参数
        /// </summary>
        /// <returns></returns>
        protected override Hashtable getParams()
        {
            // 向SQL语句传递参数
            //lxm20230627新增 科室代码 和模块代码变量传入 ~
            Hashtable hasParam = srv.AddSystemParam(this.deptCode,this.AppCode);
            //hasParam.Add("LOGIN_USER_NAME", SystemParm.LoginUser.USER_NAME);
            //hasParam.Add("LOGIN_DEPT_CODE", SystemParm.LoginUser.DEPT_CODE);
            //hasParam.Add("HospitalID", SystemParm.HospitalID);
            //hasParam.Add("HIS_UNIT_CODE", SystemParm.HisUnitCode);
            //hasParam.Add("STORAGE", Const.customDeptCode);
            ParamList.ForEach(param =>
            {

                hasParam.Add(param.GetParamName(), param.EditValue());

            });
            if (Map != null)
            {
                foreach (DictionaryEntry entry in Map)
                {
                    if (Map.Contains(entry.Key))
                    {
                        hasParam[entry.Key] = entry.Value;
                    }
                    else
                        hasParam.Add(entry.Key, entry.Value);
                }
            }
            return hasParam;
        }
        #endregion
        /// <summary>
        /// 默认执行一次查询
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void frmStatisticalQuery_Shown(object sender, EventArgs e)
        {
            if (this.DesignMode) return;
            if (drTemplet == null) return;
            bool b = InitRecordColumns(reportID);
            string tag = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString();
            if (!string.IsNullOrEmpty(_reportNo))
            {
                tag = tag + "_" + _reportNo;
            }
            tag = ReportHelper.GetXmlFileNameFull(tag);
            ReportHelper.GetReportColumnWidthXml(SystemParm.HisUnitCode, myAppName, reportID, _reportNo, tag);
            if (!b)
            {
                return;
            }
            //QueryData();
        }
        /// <summary>
        /// 关闭窗体，垃圾回收
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void frmStatisticalQueryDoc_FormClosed(object sender, FormClosedEventArgs e)
        {
            this.Dispose();
            GC.Collect();
        }

        /// <summary>
        /// 查询数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barQuery_ItemClick(object sender, ItemClickEventArgs e)
        {
            QueryData();
        }

        private void QueryData()
        {
            try
            {
                if (!string.IsNullOrEmpty(_reportNo))
                {
                    bool b = InitRecordColumns(reportID);
                    if (b)
                        gridControl1.DataSource = XtraReportHelper.GetPrintData(TempleteSql, getParams(), Const.customAppCode, true)?.Tables[_reportNo.ToInt() - 1];
                }
                else
                {
                    gridControl1.DataSource = XtraReportHelper.GetPrintData(TempleteSql, getParams(), Const.customAppCode, true)?.Tables[0];
                }
                string tag = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString();
                if (!string.IsNullOrEmpty(_reportNo))
                {
                    tag = tag + "_" + _reportNo;
                }
                // 设置列宽
                XtraReportHelper.AddColByConfig(this.gridView1, tag);
            }
            catch (Exception e)
            {
                XtraMessageBox.Show(e.Message, "提示");
            }
        }

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barPrint_ItemClick(object sender, ItemClickEventArgs e)
        {
            //gridView1.ShowPrintPreview();
            System.Drawing.Printing.PaperKind kind = System.Drawing.Printing.PaperKind.A4;
            bool landscape = false;
            string pageSettings = drTemplet["PAGE_SETTINGS"].ToString();
            if (!string.IsNullOrEmpty(pageSettings))
            {
                string[] settings = pageSettings.Split(',');
                if (settings != null && settings.Length == 2)
                {
                    landscape = string.Equals(settings[1], "横向") ? true : false;
                    try
                    {
                        kind = (System.Drawing.Printing.PaperKind)Enum.Parse(typeof(System.Drawing.Printing.PaperKind), settings[0]);
                    }
                    catch (Exception ex)
                    {

                    }
                }
            }

            DevExpress.XtraPrinting.PrintingSystem ps = new DevExpress.XtraPrinting.PrintingSystem();
            DevExpress.XtraPrinting.PrintableComponentLink link = null;
            link = new DevExpress.XtraPrinting.PrintableComponentLink(ps);
            ps.Links.Add(link);
            link.Component = gridControl1;
            link.CreateDocument();
            ps.PageSettings.PaperKind = kind;                        //纸张大小
            ps.PageSettings.Landscape = landscape;                                   //是否为横向打印
            //ps.PageSettings.TopMargin = 76;                                     //上边距
            //ps.PageSettings.BottomMargin = 76;                                //下边距
            //ps.PageSettings.LeftMargin = 44;                                   //左边距
            //ps.PageSettings.RightMargin = 44;                                     //右边距
            //ps.Print() ;　//直接打印，不显示打印预览
            link.ShowPreview();
        }

        /// <summary>
        /// 导出
        /// </summary>
        private void GridExportFile()
        {
            try
            {
                SaveFileDialog sfd = new SaveFileDialog();
                sfd.FilterIndex = 1;
                sfd.RestoreDirectory = true;

                if (true)
                {
                    // 获取文件名
                    //sfd.Filter = "Excel 97-2003（*.xlsx）|*.xlsx";
                    sfd.FileName = drTemplet["DICT_NAME"].ToString();
                    sfd.Filter = "Excel（*.xlsx）| *.xlsx";
                    
                    if (sfd.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                    {
                        return;
                    }
                    gridView1.ExportToXlsx(sfd.FileName);
                    return;
                }
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 导出数据到excel文件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barExport_Xls_ItemClick(object sender, ItemClickEventArgs e)
        {
            GridExportFile();
        }

        private void barClose_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                if (this.Parent != null && this.Parent is TJReportViewF parentForm
                     && parentForm.Parent != null && parentForm.Parent is XtraTabPage tabPage)
                {
                    XtraTabControl xtraTabControl = tabPage.Parent as XtraTabControl;
                    if (xtraTabControl == null) return;
                    string strName = tabPage.Text.ToString();
                    int iCount = xtraTabControl.TabPages.Count;
                    for (int i = iCount - 1; i >= 0; i--)
                    {
                        if (xtraTabControl.TabPages[i].Text.ToString() == strName)
                        {
                            xtraTabControl.TabPages.RemoveAt(i);
                        }
                    }

                    tabPage.Dispose();
                }
                else
                    this.Close();
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }

        private void barReportPrintDesign_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                templetePrintFile = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString() + "_Print";

                if (!string.IsNullOrEmpty(_reportNo))
                {
                    templetePrintFile = templetePrintFile + "_" + _reportNo;
                }
                //string strReportFileName = Application.StartupPath + $"\\Reports\\{TempleteFile}.repx";
                string strReportFileName = ReportHelper.GetTempleteFileNameFull(templetePrintFile, Const.customAppCode);
                ReportHelper.GetReportTemplateById(SystemParm.HisUnitCode, myAppName, reportID, _reportNo, strReportFileName, true, ref DocViewer, ref mReport);
                if (string.IsNullOrEmpty(TempleteSql.Trim()))
                {
                    XtraMessageBox.Show("该报表缺少SQL脚本，请您先在报表维护中填写！", "系统提示");
                    return;
                }
                //if (mReport.DataSource == null)//多个类型查询的报表情况会有问题 删除此条件
                //{
                mReport.DataSource = srv.GetDataStruct(TempleteSql, getParams(), _reportNo.ToInt());
                //}

                // 打开模板设计器
                XRDesignFormEx frm = new XRDesignFormEx();
                frm.FileName = strReportFileName;
                frm.OpenReport(mReport);
                frm.ShowDialog();
                frm.Dispose();

                //保存报表设计模板到数据库
                srv.SaveReportRepx(myAppName, reportID, _reportNo, strReportFileName, true);

                //重新加载模板
                this.Cursor = Cursors.WaitCursor;
                // XtraReportHelper.LoadTemplet(ref DocViewer, ref mReport, TempleteFile);
                if (!string.IsNullOrEmpty(_reportNo))
                {
                    mReport.DataSource = XtraReportHelper.GetPrintData(TempleteSql, getParams(), Const.customAppCode, true);
                }
                else
                {
                    mReport.DataSource = XtraReportHelper.GetPrintData(TempleteSql, getParams(), Const.customAppCode, true);
                }
                mReport.CreateDocument();

                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }

        private void barReportPrint_ItemClick(object sender, ItemClickEventArgs e)
        {
            templetePrintFile = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString() + "_Print";
            if (!string.IsNullOrEmpty(_reportNo))
            {
                templetePrintFile = templetePrintFile + "_" + _reportNo;
            }

            //if (string.IsNullOrEmpty(templetePrintFile))
            //{
            //    XtraMessageBox.Show("请先报表设计！");
            //}           
            XtraReportHelper.PrinterName = PrinterConfigFrm.GetPrinterName(templetePrintFile);
            DataSet printDs = new DataSet(); 
            if (!string.IsNullOrEmpty(_reportNo))
            {
                printDs = XtraReportHelper.GetPrintData(TempleteSql, getParams(), Const.customAppCode, true);
            }
            else
            {
                printDs = XtraReportHelper.GetPrintData(TempleteSql, getParams(), Const.customAppCode, true);
            }
          
            XtraReportHelper.Print(templetePrintFile, printDs, true, false, Const.customAppCode);
        }

        private void barReportDesign_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                string templeteFile = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString();

                if (!string.IsNullOrEmpty(_reportNo))
                {
                    templeteFile = templeteFile + "_" + _reportNo;
                }
                //string strReportFileName = Application.StartupPath + $"\\Reports\\{TempleteFile}.repx";
                string strReportFileName = ReportHelper.GetTempleteFileNameFull(templeteFile, Const.customAppCode);
                ReportHelper.GetReportTemplateById(SystemParm.HisUnitCode, myAppName, reportID, _reportNo, strReportFileName, false, ref DocViewer, ref mReport);
                if (string.IsNullOrEmpty(TempleteSql.Trim()))
                {
                    XtraMessageBox.Show("该报表缺少SQL脚本，请您先在报表维护中填写！", "系统提示");
                    return;
                }
                //if (mReport.DataSource == null)//多个类型查询的报表情况会有问题 删除此条件
                //{
                mReport.DataSource = srv.GetDataStruct(TempleteSql, getParams(), _reportNo.ToInt());
                //}

                // 打开模板设计器
                XRDesignFormEx frm = new XRDesignFormEx();
                frm.FileName = strReportFileName;
                frm.OpenReport(mReport);
                frm.ShowDialog();
                frm.Dispose();

                //保存报表设计模板到数据库
                srv.SaveReportRepx(myAppName, reportID, _reportNo, strReportFileName, false);

                //重新加载模板
                this.Cursor = Cursors.WaitCursor;
                // XtraReportHelper.LoadTemplet(ref DocViewer, ref mReport, TempleteFile);
                if (!string.IsNullOrEmpty(_reportNo))
                {
                    mReport.DataSource = XtraReportHelper.GetPrintData(TempleteSql, getParams(), Const.customAppCode, true)?.Tables[_reportNo.ToInt() - 1];
                }
                else
                {
                    mReport.DataSource = XtraReportHelper.GetPrintData(TempleteSql, getParams(), Const.customAppCode, true)?.Tables[0];
                }
                mReport.CreateDocument();

                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }

        private void gridView1_ColumnWidthChanged(object sender, DevExpress.XtraGrid.Views.Base.ColumnEventArgs e)
        {
            try
            {
                string tag = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString();
                if (!string.IsNullOrEmpty(_reportNo))
                {
                    tag = tag + "_" + _reportNo;
                }
                XtraReportHelper.SaveColumnConfig(this.gridView1, tag);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }


        public void SaveReportConfig()
        {
            //关闭窗体时,保存列宽配置到数据库   
            string tag = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString();
            if (!string.IsNullOrEmpty(_reportNo))
            {
                tag = tag + "_" + _reportNo;
            }
            tag = ReportHelper.GetXmlFileNameFull(tag);
            srv.SaveReportConfig(myAppName, reportID, _reportNo, tag);
        }

        private void barExportPdf_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                SaveFileDialog sfd = new SaveFileDialog();
                sfd.FilterIndex = 1;
                sfd.RestoreDirectory = true;

                if (true)
                {
                    // 获取文件名
                    sfd.Filter = "Pdf（*.pdf）| *.pdf";
                    sfd.FileName = drTemplet["DICT_NAME"].ToString();
                    if (sfd.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                    {
                        return;
                    }
                    gridView1.AppearancePrint.HeaderPanel.Font = new System.Drawing.Font("宋体", gridView1.AppearancePrint.HeaderPanel.Font.Size);
                    gridView1.AppearancePrint.HeaderPanel.Options.UseFont = true;
                    gridView1.AppearancePrint.Row.Font = new System.Drawing.Font("宋体", gridView1.AppearancePrint.HeaderPanel.Font.Size);
                    gridView1.AppearancePrint.Row.Options.UseFont = true;
                    gridView1.ExportToPdf(sfd.FileName);
                    return;
                }
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }
    }

}
