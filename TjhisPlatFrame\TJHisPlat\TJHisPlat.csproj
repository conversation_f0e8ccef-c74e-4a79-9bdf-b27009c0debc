﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{3EC77B6D-539A-4C45-92F2-A3293425F0C3}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>TJHisPlat</RootNamespace>
    <AssemblyName>TJHisPlat</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>logo.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <WarningLevel>2</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>false</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup />
  <ItemGroup>
    <Reference Include="DevExpress.BonusSkins.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.BonusSkins.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Data.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.Data.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Images.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.Images.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Pdf.v19.1.Drawing, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.Pdf.v19.1.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.Utils.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraBars.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraBars.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraEditors.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraGrid.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraGrid.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraLayout.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraLayout.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraNavBar.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraNavBar.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraPdfViewer.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraPdfViewer.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraTreeList.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraTreeList.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="INMService, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\INMService.dll</HintPath>
    </Reference>
    <Reference Include="Model, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\Model.dll</HintPath>
    </Reference>
    <Reference Include="NMService, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\NMService.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess, Version=4.122.1.0, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="OracleDAL, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\OracleDAL.dll</HintPath>
    </Reference>
    <Reference Include="PlatCommon, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\PlatCommon.dll</HintPath>
    </Reference>
    <Reference Include="PlatCommonForm, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\PlatCommonForm.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Tjhis.Interface.Station, Version=6.6.4.3, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\Tjhis.Interface.Station.dll</HintPath>
    </Reference>
    <Reference Include="Utility, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\Utility.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="frmLayoutView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmLayoutView.designer.cs">
      <DependentUpon>frmLayoutView.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmSelectOutpDoctorS.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmSelectOutpDoctorS.Designer.cs">
      <DependentUpon>FrmSelectOutpDoctorS.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmShowMessage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmShowMessage.Designer.cs">
      <DependentUpon>FrmShowMessage.cs</DependentUpon>
    </Compile>
    <Compile Include="frmSplashScreen.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSplashScreen.designer.cs">
      <DependentUpon>frmSplashScreen.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmDbConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmDbConfig.designer.cs">
      <DependentUpon>FrmDbConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmLoginNew.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmLoginNew.Designer.cs">
      <DependentUpon>FrmLoginNew.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Settings.cs" />
    <EmbeddedResource Include="frmLayoutView.resx">
      <DependentUpon>frmLayoutView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmSelectOutpDoctorS.resx">
      <DependentUpon>FrmSelectOutpDoctorS.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmShowMessage.resx">
      <DependentUpon>FrmShowMessage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSplashScreen.resx">
      <DependentUpon>frmSplashScreen.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmLoginNew.resx">
      <DependentUpon>FrmLoginNew.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmDbConfig.resx">
      <DependentUpon>FrmDbConfig.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Config\connection.xml">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Config\XMLFile1.xml" />
    <Content Include="Config\门诊开票请求示例报文（不含医保）.xml" />
    <Content Include="logo.ico" />
    <None Include="Resources\Close.png" />
    <None Include="Resources\Ok.png" />
    <None Include="Resources\backImage.bmp" />
    <None Include="Resources\backImage.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\TjhisPlatSource\TjhisInterfaceCA\TjhisInterfaceCA.csproj">
      <Project>{1051351e-92ea-4f94-96f1-8eaa5cf84244}</Project>
      <Name>TjhisInterfaceCA</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>