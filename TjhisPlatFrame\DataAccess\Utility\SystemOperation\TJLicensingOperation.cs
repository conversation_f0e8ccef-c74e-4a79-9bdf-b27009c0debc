﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.InteropServices;

namespace Utility.SystemOperation
{
    public static class TJLicensingOperation
    {
        [DllImport(@"EncryptDecrypt.dll", CallingConvention = CallingConvention.StdCall)]
        public static extern bool EncryptStringW(string plainText, StringBuilder outputText);

        [DllImport(@"EncryptDecrypt.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode)]
        public static extern bool EncryptStringForCS(string plainText, StringBuilder outputText);

        [DllImport(@"EncryptDecrypt.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode)]
        public static extern bool DecryptStringForCS(string cipher, StringBuilder outputText);

        [DllImport(@"EncryptDecrypt.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode)]
        public static extern bool DecryptStringW(string cipher, StringBuilder outputText);

        [DllImport(@"EncryptDecrypt.dll", CallingConvention = CallingConvention.StdCall)]
        public static extern bool MessageCopyW(StringBuilder outputText, string cipher);

        public static bool DecryptKey(String licenseKey, ref String Customer, ref String CommunityNum, ref String ProductName, ref String Validate, ref String Versions)
        {
            StringBuilder outtext = new StringBuilder(500);
            DecryptStringForCS(licenseKey, outtext);
            String[] res = outtext.ToString().Split(',');
            if (res != null && res.Length > 5)
            {
                Customer = res[0];
                CommunityNum = res[1];
                ProductName = res[2];
                Validate = res[3];
                Versions = res[4];
                return true;
            }
            else
            {
                return false;
            }
            
        }
    }
}
