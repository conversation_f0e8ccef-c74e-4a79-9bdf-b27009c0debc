﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
namespace Utility.SystemOperation
{
    public static class SystemTime
    {
        public static DateTime GetSystemTimeNow()
        {
            String SQL = "select sysdate from dual ";
            TJ_DataBase_Operation_Interface db = TJ_DataBase_Operation_Factory.CreateDataBaseOperation();
            db.OpenDB();
            DataSet ds = db.SelectDataSet(SQL);
            db.CloseDB();
            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
            {
                DateTime ret = Convert.ToDateTime(ds.Tables[0].Rows[0][0]);
                return ret;
            }
            else
            {
                throw new Exception("获取数据库系统时间失败");
            }
        }
    }
}
