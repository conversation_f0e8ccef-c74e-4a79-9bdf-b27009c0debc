﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Data;

namespace PlatCommonForm
{
    /// <summary>
    /// 界面控件帮助类
    /// </summary>
    public class ViewDataHelper
    {
        /// <summary>
        /// 清除界面控件值
        /// </summary>
        /// <param name="parentCtrl"></param>
        public static void ClearData(Control parentCtrl)
        {
            Label lbl = null;
            TextBox txtEdit = null;
            ComboBox cmb = null;
            RadioButton rdo = null;
            DateTimePicker dtp = null;
            CheckBox chk = null;
            CheckedListBox chkList = null;
            DevExpress.XtraEditors.ButtonEdit txtButtonEdit = null;
            DevExpress.XtraEditors.TextEdit txtTextEdit = null;
            string colName = string.Empty;

            foreach (Control ctrl in parentCtrl.Controls)
            {
                // 标签控件
                lbl = ctrl as Label;
                if (lbl != null && lbl.Tag != null)
                {
                    lbl.Text = string.Empty;
                    continue;
                }
                // DevExpress.XtraEditors.ButtonEdit控件
                txtButtonEdit = ctrl as DevExpress.XtraEditors.ButtonEdit;
                if (txtButtonEdit != null && txtButtonEdit.Tag != null)
                {
                    txtButtonEdit.Text = string.Empty;
                    continue;
                }
                // DevExpress.XtraEditors.TextEdit控件
                txtTextEdit = ctrl as DevExpress.XtraEditors.TextEdit;
                if (txtTextEdit != null && txtTextEdit.Tag != null)
                {
                    txtTextEdit.Text = string.Empty;
                    continue;
                }
                // 文本框控件处理
                txtEdit = ctrl as TextBox;
                if (txtEdit != null)
                {
                    txtEdit.Text = string.Empty;

                    continue;
                }

                // 日期控件
                dtp = ctrl as DateTimePicker;
                if (dtp != null)
                {
                    dtp.Checked = false;

                    continue;
                }

                // 下拉选择框
                cmb = ctrl as ComboBox;
                if (cmb != null)
                {
                    cmb.SelectedIndex = -1;

                    continue;
                }

                // 复选按钮
                chk = ctrl as CheckBox;
                if (chk != null)
                {
                    chk.Checked = false;

                    continue;
                }

                // 复选列表
                chkList = ctrl as CheckedListBox;
                if (chkList != null)
                {
                    for (int i = 0; i < chkList.Items.Count; i++)
                    {
                        chkList.SetItemChecked(i, false);
                    }

                    continue;
                }

                // GroupBox处理
                if (ctrl.Controls.Count > 0)
                {
                    ClearData(ctrl);
                }
            }
        }


        /// <summary>
        /// 显示数据
        /// </summary>
        public static void ShowData(Control parentCtrl, ref DataRow dr)
        {
            if (dr == null) return;

            TextBox txtEdit = null;
            ComboBox cmb = null;
            RadioButton rdo = null;
            DateTimePicker dtp = null;
            NumericUpDown nUpDown = null;
            Label lbl = null;
            CheckBox chk = null;
            DevExpress.XtraEditors.ButtonEdit txtButtonEdit = null;
            DevExpress.XtraEditors.TextEdit txtTextEdit = null;
            DevExpress.XtraEditors.CheckEdit xchkEdit = null;

            string colName = string.Empty;

            foreach (Control ctrl in parentCtrl.Controls)
            {
                // 标签控件处理
                lbl = ctrl as Label;
                if (lbl != null && Convert.ToString(lbl.Tag).Trim().Length > 0)
                {
                    colName = lbl.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName))
                    {
                        lbl.Text = Convert.ToString(dr[colName]);
                    }

                    continue;
                }
                // ButtonEdit
                txtButtonEdit = ctrl as DevExpress.XtraEditors.ButtonEdit;
                if (txtButtonEdit != null && Convert.ToString(txtButtonEdit.Tag).Trim().Length > 0)
                {
                    colName = txtButtonEdit.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName))
                    {
                        txtButtonEdit.Text = Convert.ToString(dr[colName]);
                    }

                    continue;
                }
                // TextEdit
                txtTextEdit = ctrl as DevExpress.XtraEditors.TextEdit;
                if (txtTextEdit != null && Convert.ToString(txtTextEdit.Tag).Trim().Length > 0)
                {
                    colName = txtTextEdit.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName))
                    {
                        txtTextEdit.Text = Convert.ToString(dr[colName]);
                    }

                    continue;
                }
                // 文本框控件处理
                txtEdit = ctrl as TextBox;
                if (txtEdit != null && Convert.ToString(txtEdit.Tag).Trim().Length > 0)
                {
                    colName = txtEdit.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName))
                    {
                        txtEdit.Text = Convert.ToString(dr[colName]);
                    }

                    continue;
                }

                // 日期控件
                dtp = ctrl as DateTimePicker;
                if (dtp != null && Convert.ToString(dtp.Tag).Trim().Length > 0)
                {
                    colName = dtp.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName) && dr[colName] != DBNull.Value)
                    {
                        dtp.Value = DateTime.Parse(dr[colName].ToString());
                        dtp.Checked = true;
                    }
                    else
                    {
                        dtp.Checked = false;
                    }

                    continue;
                }

                // 下拉选择框
                cmb = ctrl as ComboBox;
                if (cmb != null && Convert.ToString(cmb.Tag).Trim().Length > 0)
                {
                    colName = cmb.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName))
                    {
                        try
                        {
                            if (dr[colName] == DBNull.Value || string.IsNullOrEmpty(dr[colName].ToString()))
                            {
                                cmb.SelectedIndex = -1;
                            }
                            else
                            {
                                cmb.SelectedValue = Convert.ToString(dr[colName]);
                            }
                        }
                        catch
                        {
                            cmb.SelectedIndex = -1;
                        }
                    }

                    continue;
                }

                // 单选按钮
                rdo = ctrl as RadioButton;
                if (rdo != null && Convert.ToString(rdo.Tag).Trim().Length > 0)
                {
                    if (rdo.Tag.ToString().Split('|').Length < 2) continue;

                    colName = rdo.Tag.ToString().Split('|')[0].Trim();
                    string checkedValue = rdo.Tag.ToString().Split('|')[1].Trim();

                    if (dr.Table.Columns.Contains(colName))
                    {
                        rdo.Checked = Convert.ToString(dr[colName]).Equals(checkedValue);
                    }

                    continue;
                }

                // 复选按钮
                chk = ctrl as CheckBox;
                if (chk != null && Convert.ToString(chk.Tag).Trim().Length > 0)
                {
                    if (chk.Tag.ToString().Split('|').Length < 2) continue;

                    colName = chk.Tag.ToString().Split('|')[0].Trim();
                    string checkedValue = chk.Tag.ToString().Split('|')[1].Trim();

                    if (dr.Table.Columns.Contains(colName))
                    {
                        string colValue = Convert.ToString(dr[colName]).Trim();

                        if (string.IsNullOrEmpty(checkedValue) &&
                            colValue.Length > 0 && colValue.Equals("0") == false && colValue.Equals("-1") == false)
                        {
                            chk.Checked = true;
                            chk.Tag = colName + " | " + dr[colName].ToString().Trim();
                        }
                        else
                        {
                            chk.Checked = Convert.ToString(dr[colName]).Equals(checkedValue);
                        }
                    }

                    continue;
                }

                // 复选框
                xchkEdit = ctrl as DevExpress.XtraEditors.CheckEdit;
                if (xchkEdit != null)
                {
                    xchkEdit.Checked = false;
                    continue;
                }

                // 数值选择
                nUpDown = ctrl as NumericUpDown;
                if (nUpDown != null && Convert.ToString(nUpDown.Tag).Trim().Length > 0)
                {
                    colName = nUpDown.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName))
                    {
                        nUpDown.Value = decimal.Parse(dr[colName].ToString());//Comm.Converter.ToDeci(dr[colName]);
                    }

                    continue;
                }

                // GroupBox处理
                if (ctrl.Controls.Count > 0)
                {
                    ShowData(ctrl, ref dr);
                }
            }
        }

    }
}
