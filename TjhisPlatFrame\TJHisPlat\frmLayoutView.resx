﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="layoutViewCard1.ContentImageOptions.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAOMAAADKCAYAAABXLfeyAAAABGdBTUEAALGPC/xhBQAACNZJREFUeF7t
        l2lTFUsQRP3//+g9N1wRBVERcUFERBB3FkU2fZPV1TO3xwtqeKe7I945EWXenOzmW0a151693vqxvvG2
        HTweX8af0z8AUBYro5oJAGVRDykjQAVQRoBKoIwAlWBlXFt/4xYASqEeshkBKoDNCFAJthkpI0B5rIwv
        X1FGgNKoh2xGgArwzbjpFgBKoR5SRoAKsGcqZQQoD5sRoBKsjKtrG24BoBSra1ZGNiNAabyMbEaA0qiH
        lBGgAqyML15SRoDSqIeUEaACfDO+dgsApVAP2YwAFWDP1JVVNiNAadRDnqkAFWDPVDYjQHlWVtcpI0AN
        2DP1+Yt1twBQCt+MlBGgNGxGgEpQDykjQAV4GV+5BYBSqIdsRoAKsM24vMJmBCiNesgzFaAC7Jm6vLLm
        FgBKYZuRZypAeayMz56zGQFKox6yGQEqQP9dZDMCVIBtRsoIUB4v40u3AFAK9ZDNCFABthmfLrMZAUqj
        HlJGgAqwZyplBCgPmxGgEqyMT56tugWAUqiHlBGgAtiMAJXAZgSoBCvj46cv3AJAKdiMAJXAZgSoBPWQ
        MgJUgJVx6QllBCgNmxGgEnwzrrgFgFKohzxTASpAPWQzAlSAb0bKCFAaK+Ojx5QRoDTqIZsRoAJ8Mz53
        CwClUA8pI0AF2DN1cYkyApRGPWQzAlSAPVMXl5bdAkApbDPyTAUoj5eRzQhQGvWQMgJUgJXx4SPKCFAa
        9ZAyAlSAb8ZnbgGgFOohmxGgAqyMC4tsRoDSqIc8UwEqgM0IUAkLi08pI0AN2DP1wcOnbgGgFL4ZKSNA
        aayMbEaA8qiHlBGgAryMT9wCQCnUQzYjQAXYZry/wGYEKI16yDMVoAKsjPcXHrsFgFJ4GdmMAKWxMt57
        wGYEKI16yDMVoALUQzYjQAXYZqSMAOXxMi65BYBSzN9fYjMC1IBtRjXyLL5//+6/xkNOfhbkv5fbZvxV
        GcUv/h45+ZmQ+48zSMoYG4qiaH7tlTEEQfu+r+SpJ0+VPPVn58LKePfeIzMxOHHF4/F5vFAPR8oYD3zv
        XcDj8UN6kWzGk5NwAEXRvCqSzaiPDMPkH2FlnJtfNGNBU9Xjk5PuIB6PH9yLuflTNuPxyG88Hj+sF8lm
        PD4OjUVRNK8K9XCkjCFQa02j7yv5+O/kQcmDRt/XMbmwMs7eDWU88oOmzQF8o3h8Bi96m/HED6IomlOF
        b8aHZo6O/IBp01g8Hp/FC/Wwe6Y2H238AB6Pz+OFethuxkMPDo+O7RAej8/jhW/G7pmqA3FS3/0B8jDk
        o578b3JhZbwz55vx8DiEjdrBMUpOTj75XNyZWxh5ph6mF35ScnLyQXKhpdhsxgUzBx6YNhfxeHweL2wz
        xjKqpQri4PH4PF7YZrw965vxwEMURbOqUA+TZ+rvXERRdLIq7Jl6e/aBmW8HRxZ0Gg6ab36Tk5MPkwv1
        sH2mKuwm/oHOk6eePPXkqf+TXNgztd2M3/wCiqJZVczcsc3YPVPTg3g8PocX9kxVI8W+ByiK5lVhm3G0
        jAzD5B/hm/G+mf19D1EUzapCPUw3429cRFF0siqsjLduh834df/QAhRF86pQD0fKqCDOIR6Pz+SFb8Z7
        Zr58jQeCRt9XcnLyyeZCPUyeqeFAN9H3lZycfHK5sGfq9Ey6GdOLeDx+aC98M3ZlZBgm/wgrY9yMe18O
        LNjzA3g8Po8X6mHyTG0PRv3S830lJx/3PSr5b+XCynjz1ryZtqkoimZVMT0zr80YyxgCFEXzqkg2464H
        KIrmVaEeJs/UcABF0ZwqvIx3zezuKWia2qgO4fH4PF4km9EOMQyTfYSV8cZ02Iw7e98sQFE0rwr1sH2m
        7lgQmhp/4/H44b1QD7vNuOtNbVSH8Hh8Hi9sM3Zl9INx4sV2yBNPnnry1P9BLqyM12/Omdm24ABF0cwq
        1MN2M+ojwzD5R6Sbcceb2qgdMh+08+Tk5JPOhW3G0WeqHYyDx+OzeJGU8fPOvgUoiuZV0StjCILGwePx
        Q3thZbx2Y9bM520/2NNPp3wnJyefTC7Uw66M8UCcEZ/8wTjk5P6b/O9ykWxGu7DdNTnxfSUnJ59YLpLN
        qI8Mw+QfYWW8ev2OmU+fQ/Dx89dwCI/HZ/Hi6vX+ZtSBZj76QTweP7wXyWaMjU3ULo75Tk5OPrFcqIcj
        ZQwBiqJ5VVgZr1zzMn4KwQdXPB6fx4veZmxCHWgmudB6cnLyIXLhm/G2mXghHhyv5OTkk86Fetg+U/WR
        YZj8I9TDdjO+/xiC9x+/uOLx+Bxe+GbsnqndARRFc6mwMk5djZsxNhZF0Zwqpq7OpM9UBSiK5lWhpdhs
        xhkz7z6EoFU/+NN3cnLyiebCNmMso1qqQIfCBf896snJySeeCyvj5SvdZuwOxgaPXCQnJx8kF+ph8kxl
        GCb/CN+Mt8y89eDt+72geDw+ixfqYfJM1QGbkQt4PH5YL+yZ2m7G5kA82Fe7SE5OPkguLk3ZZoxl9AOt
        xul/Jycnn2Qu7Jl6aWrazJYHW+9c8Xh8Fi9sM+ofobAdu5B68tSTp5489X+SCy+jb8Z4AUXRrCrUw7SM
        DMNkH2FlvHg5lPHNu10L3rx1xQfF4wf2Qj0cKaMO+DQH8Hh8Hi98M9400x5AUTSrCvWw24wKx45fPHXI
        x3+PQz7+exxyYc/UC5fCZtz0YHPLNfG75OTkA+VCPWyfqQpPnebC2O9xyMd/j0M+/nuc/3ku7JkaN+NG
        vOCKx+PzeGGbsX2mNh/twMhE31dycvLJ5cLKeP7iDTMbWzt+AEXRnCouXLqhzRjLqCDOzilKTk4+6Vwk
        m/H1mxCgKJpXhXrYPVObj3YARdGsKryM182EwBsbD8YGR09OPurJJ5KLZDPqI8Mw+UdYGf+9EDbj+ua2
        Beub4QAej8/jhXrYPVObj3YQRdGsKqyMP2/GbTuEx+PzeNErYzwYJ71ITk4+TC6sjP+cv2bm1UYIUBTN
        q0I97DZj8zEcCBonenJy8mFykWxGACiHbUb9wzBM6bn24z/MzFvRCIr6gAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA2b6RJtCveHjHnlmpy6Vmy8ynau/Lp2nzzKdq88yoa+/Oq3CXAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAxZlRMMGRQbm8hi//u4Qs/7uELP+7hCz/u4Qs/7uELP+7hCz/u4Qs/7yH
        Mf3AkEEiAAAAAAAAAADNqGpQv406cObVuAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAMGQP1q8hi7zu4Us/7uELf+7hCz/u4Us/7uELf+7hSz/u4Qt/7uE
        LP+7hCz/u4Ut/7+NOp8AAAAAAAAAANm+kgq0eBXttnsa28WXTD4AAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADGmlE6uoIn47iAI+e4gCPnuIAj57iAI+e4gCPnuIAj57iA
        I+e4gCPnuIAj57iAI+e4gCPnuoIn48edWBwAAAAAAAAAALmCJ4OxcAb/snIL+7+MOXAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAL6KNrW4fiDnuH4g57h+IOe4fiDnuH4g57h+
        IOe4fiDnuH4g57h+IOe4fiDnuH4g57h+IOe4fiDnv4w5hQAAAAAAAAAAxZlSFLJzDfexcAf/sXAI/72J
        M3z+/fwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADfyKMkuoMp57qCJue6gibnuoIm57qC
        Jue6gibnuoIm57qCJue6gibnuoIm57qCJue6gibnuoIm57qCJue9iDLb6Ni9EAAAAAAAAAAAt3wem7Bv
        Bv+xcAb/sXEJ/8COPFoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMCNPGawbwf/sG8G/7Bw
        Bv+wbwb/sHAH/7BvB/+wbwf/sHAG/7BvBv+wbwb/sG8H/7BvBv+wbwb/sG8H/7BwBv+8iDKFAAAAAAAA
        AADFmU8qsXIK/bFwB/+wcAf/s3UP8cabVRgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAxZdMbrmC
        Kf+5gij/uYIo/7mCKP+5gij/uYIo/7mCKP+5gij/uYIo/7mCKP+5gij/uYIo/7mCKP+5gij/uYIo/76M
        Oe3fyaMMAAAAAAAAAAC3fR+1sXAH/7FwB/+xcAb/t3weiQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AADElUhasG8G/7BvBv+wbwb/sG8G/7BvBv+wbwb/sG8G/7BvBv+wbwb/sG8G/7BvBv+wbwb/sG8G/7Bv
        Bv+wbwb/sG8G/76LN3QAAAAAAAAAAMSXTEqwbwb/sXAH/7FwB/+0dxTl9O3hAgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAN3DmBqzdRD9sG8G/7BvBv+wbwb/sG8G/7BvBv+wbwb/sG8G/7BvBv+wbwb/sG8G/7Bv
        Bv+wbwb/sG8G/7BvBv+wbwb/tXkY5eTQsQYAAAAAvYo0cLBvBv+wbwb/sG8G/7BwB//NqGogAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAANGtczTOqWtAzqlrQM6pa0DOqWtAzqlrQM6pa0DOqWtAzqlrQM6p
        a0DOqWtAzqlrQM6pa0DOqWtAzqlrQM6pa0DOqWtA28GVCgAAAADSsHgqzqlrQM6pa0DOqWtAzqlrQLyH
        MMfGmFBGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAyqJfVsGQP7m+izerAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAMGQQTa4fyLNxZhPgcWZUHa4fyLNvYo2QAAAAAAAAAAAAAAAAAAAAAAAAAAAyZ9bTrmB
        Jce5gSTHuYEkx7mBJMe5gSTHuYEkx7mBJMe5gSTHuYEkx7mBJMe5gSTHuYEkx7mBJMe5gSTHuYEkx7mB
        JMe5gSTHuYEkx7mBJMe5gCTHx5tUcLuGLlDInlocz6tuFsCOO1CpXIlmiBeD96ZUoUwAAAAAAAAAAAAA
        AADy6t4AuYIobrJyC/uxcAf/sXAH/7FwB/+xcAf/sXAH/7FwB/+xcAf/sXAH/7FwB/+xcAf/sXAH/7Fw
        B/+xcAf/sXAH/7FwB/+xcAf/sHAG/7V5GL/YvI4CAAAAAAAAAAAAAAAAAAAAAK5kqQyXN5EmsmytCgAA
        AAAAAAAAAAAAAAAAAAAAAAAAwpJDNrZ7Gs+wcAf/sXAH/7FwB/+xcAf/sXAH/7FwB/+xcAf/sXAH/7Fw
        B/+xcAf/sXAH/7FwB/+xcAf/sW8H/7BwBv+3fR+7wpNHDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA5dW5AsGRQVa5fyPHsnMM/7BwBv+wcAb/sXAH/7Fw
        B/+xcAf/sXAH/7FwB/+xcAf/sXAG/7BwBv+1eBXtvos3eO/jzwQAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP7+/QDVtYAkwI48cLqC
        J6u4fyHftXkX+bN1EP+0dhL/tnsa97h+INW7hSujwpNEWOPOrAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAD7+PQG7uPSDPLp3Az8+/gEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA//////////////////////////////////////4D///wA///4AHP/8AB
        x/+AAOP/gADh/4AAcP+AAHB/gAB4f4AAOH////+/////z////5fwAAD9+AAA//wAAf//AAf//+Af////
        //////////////////////////////////8=
</value>
  </data>
</root>