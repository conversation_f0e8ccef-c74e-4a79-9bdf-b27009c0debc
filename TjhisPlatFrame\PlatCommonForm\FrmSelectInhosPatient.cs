﻿//using PlatCommon.System;
//**********************************************
//说明:
//计算机名称：LINDP
//创建日期：2016/6/19 10:54:12
//作者：林大鹏
//版本号：V1.00
//**********************************************
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using static DevExpress.XtraEditors.BaseCheckedListBoxControl;

namespace PlatCommonForm
{
    public partial class FrmSelectInhosPatient : Form//PlatCommon.SysBase.ParentForm
    {
        public DevExpress.XtraEditors.BaseCheckedListBoxControl.CheckedItemCollection checkedItemCollection = null;
        private bool single = false;
        DataTable oldData;
        string isSelectName = "";
        public FrmSelectInhosPatient()
        {
            InitializeComponent();
            //GetInHospitalList();
        }
        public FrmSelectInhosPatient(string sql,string displaymember,string valuemember,bool single)
        {
            isSelectName = displaymember;
            InitializeComponent();
            GetInHospitalList(sql,displaymember,valuemember);
            this.single = single;
            barCheckItem1.Enabled = !single;

        }
        //取在院患者列表
        private void GetInHospitalList(string sql, string displaymember, string valuemember)
        {
            //string sql = "SELECT PATS_IN_HOSPITAL.BED_NO,PAT_MASTER_INDEX.NAME,BED_REC.BED_LABEL,PATS_IN_HOSPITAL.Patient_Id,PATS_IN_HOSPITAL.Visit_Id,BED_REC.BED_LABEL || ' (' || PAT_MASTER_INDEX.NAME || ')' as LABELNAME ";
            //sql += " FROM PAT_MASTER_INDEX,PATS_IN_HOSPITAL ,BED_REC WHERE ( PAT_MASTER_INDEX.PATIENT_ID = PATS_IN_HOSPITAL.PATIENT_ID ) and ";
            //sql += "( ( PATS_IN_HOSPITAL.WARD_CODE = '" + SystemParm.Ward_ID + "' ) ) AND (PATS_IN_HOSPITAL.WARD_CODE = BED_REC.WARD_CODE) AND (PATS_IN_HOSPITAL.BED_NO = BED_REC.BED_NO)";

            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            oldData = dt.Copy();
            checkedListBoxControl1.DataSource = dt;
            checkedListBoxControl1.DisplayMember = displaymember;//"LABELNAME";
            checkedListBoxControl1.ValueMember = valuemember;//"PATIENT_ID";
        }

 
        //处理全选
        private void barCheckItem1_CheckedChanged(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            
            checkedListBoxControl1.BeginUpdate();
            try
            {
                int i = 0;
                while (checkedListBoxControl1.GetItem(i) != null)
                {
                    checkedListBoxControl1.SetItemChecked(i, barCheckItem1.Checked);
                    i++;
                }
            }
            finally
            {
                checkedListBoxControl1.EndUpdate();
            }
        }

        private void barLargeButtonItem2_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
        //确定
        private void barLargeButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            checkedItemCollection = checkedListBoxControl1.CheckedItems;
            this.Close();
            //DataRowView drv;
            //foreach (var item in checkedItemCollection)
            //{
            //    drv = item as DataRowView;
            //    //MessageBox.Show(drv["PATIENT_ID"].ToString() + "    " + drv["NAME"].ToString());
            //}
        }

        
        private void checkedListBoxControl1_SelectedValueChanged(object sender, EventArgs e)
        {

            if (this.single)
            {
                checkedListBoxControl1.UnCheckAll();

                checkedListBoxControl1.BeginUpdate();
                checkedListBoxControl1.SetItemCheckState(checkedListBoxControl1.SelectedIndex, checkedListBoxControl1.GetItemChecked(checkedListBoxControl1.SelectedIndex) ? CheckState.Unchecked : CheckState.Checked);

                checkedListBoxControl1.EndUpdate();
            }
            
        }

        private void barEditItem1_EditValueChanged(object sender, EventArgs e)
        {
            string isFilter = barEditItem1.EditValue.ToString();
            isFilter = "" + isSelectName + " like  '%" + isFilter + "%' ";
            DataRow[] dr = oldData.Select(isFilter);
            if (dr.Length > 0)
            {
                DataTable dt = dr.CopyToDataTable();
                checkedListBoxControl1.DataSource = dt;
            }
            else
            {
                checkedListBoxControl1.DataSource = oldData;
            }

        }
    }
}
