﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace PlatCommon.Common
{
    public static class Constants
    {
        /// <summary>
        /// 
        /// </summary>
        public static readonly string Tem_HistoryFilePath = Application.StartupPath + @"\FileTemp\History_Temp";
        /// <summary>
        /// 
        /// </summary>
        public static readonly string LOG4NET_CONFIG = Application.StartupPath + "\\Log4net.config";
        /// <summary>
        /// 
        /// </summary>
        public static readonly string MESSAGE_CONFIG = Application.StartupPath + "\\Messages.xml";
        /// <summary>
        /// 
        /// </summary>
        public static readonly string ICON_PATH = Application.StartupPath + "\\Resources\\Menu\\";
        /// <summary>
        /// 
        /// </summary>
        public static string COMPANY_NAME = "北京天健源达科技股份有限公司";
        /// <summary>
        /// 
        /// </summary>
        public const string KEY = "6960a66f-1180-4c49-8b07-a2578947d51c";
        /// <summary>
        /// 
        /// </summary>
        public const string IV = "22fa5f2d-ee84-40bb-8651-d35ea9b4eeb6";
        /// <summary>
        /// 
        /// </summary>
        public static Dictionary<string, DataTable> TableListsDictionary = new Dictionary<string, DataTable>();
        /// <summary>
        /// 
        /// </summary>
        public const int WM_KEYDOWN = 0x0100;
        public const int WM_LBUTTONDOWN = 0x0201;
        public const int WM_RBUTTONDOWN = 0x0204;
        public const string FONT_NAME = "宋体";
        public const float FONT_SIZE = 10F;
        public const string TO_0DIGIT = "0";
        public const string TO_1DIGIT = "#0.0";
        public const string TO_2DIGIT = "#0.00";
        public const string COMPLEX_BEGIN = "╔";
        public const string COMPLEX_AMONG = "║";
        public const string COMPLEX_END = "╚";
        public const string CLINICAL_EXECUTED = "√";
        public const string CLINICAL_EXECUTING = "→";

        /// <summary>
        /// 大括号: "{"
        /// </summary>
        public const string BRACE_LEFT = "{";

        /// <summary>
        /// 大括号: "}"
        /// </summary>
        public const string BRACE_RIGHT = "}";

        /// <summary>
        /// 反斜线: "\\"
        /// </summary>
        public const string BACKSLASH = "\\";

        /// <summary>
        /// 斜线: "/"
        /// </summary>
        public const string SLASH = "/";

        /// <summary>
        /// 点: "."
        /// </summary>
        public const string POINT = ".";

        /// <summary>
        /// 省略号: "..."
        /// </summary>
        public const string SUSPENSION_POINTS = "...";

        /// <summary>
        /// 中划线: "-"
        /// </summary>
        public const string LINEATION = "-";

        /// <summary>
        /// 等号: "="
        /// </summary>
        public const string EQUAL = "=";

        /// <summary>
        /// 回车换行符: "\r\n"
        /// </summary>
        public const string CRLF = "\r\n";

        /// <summary>
        /// 跳格键: "\t"
        /// </summary>
        public const string TAB = "\t";

        /// <summary>
        /// 空白符: " "
        /// </summary>
        public const string BLANK = " ";

        /// <summary>
        /// 冒号: ":"
        /// </summary>
        public const string COLON = ":";

        /// <summary>
        /// 逗号: ","
        /// </summary>
        public const string COMMA = ",";

        /// <summary>
        /// 分号: ";"
        /// </summary>
        public const string SEMICOLON = ";";

        /// <summary>
        /// 单引号: "'"
        /// </summary>
        public const string SINGLE_QUOTE = "'";

        /// <summary>
        /// 双引号: "''"
        /// </summary>
        public const string DOUBLE_QUOTE = "''";

        /// <summary>
        /// 年字符串: "岁"
        /// </summary>
        public const string YEAR = "岁";

        /// <summary>
        /// 月字符串: "月"
        /// </summary>
        public const string MONTH = "月";

        /// <summary>
        /// 天字符串: "天"
        /// </summary>
        public const string DAY = "天";

        /// <summary>
        /// 长日期格式: yyyy-MM-dd HH:mm:ss
        /// </summary>
        public const string DATE_LONG = "yyyy-MM-dd HH:mm:ss";

        /// <summary>
        /// 长日期格式，精确到毫秒: yyyy-MM-dd HH:mm:ss:fff
        /// </summary>
        public const string DATE_LONG_PREC = "yyyy-MM-dd HH:mm:ss:fff";

        /// <summary>
        /// 压缩的日期格式: yyyyMMddHHmmss
        /// </summary>
        public const string DATE_LONG_COMPACT = "yyyyMMddHHmmss";

        /// <summary>
        /// 长日期格式: yyyy-MM-dd HH:mm
        /// </summary>
        public const string DATE_LONG_MINUTE = "yyyy-MM-dd HH:mm";

        /// <summary>
        /// 短日期格式: yyyy-MM-dd
        /// </summary>
        public const string DATE_SHORT = "yyyy-MM-dd";

        /// <summary>
        /// 短日期格式: yyyyMMdd
        /// </summary>
        public const string DATE_SHORT_COMPACT = "yyyyMMdd";

        /// <summary>
        /// 时间格式: HH:mm:ss
        /// </summary>
        public const string TIME = "HH:mm:ss";
        /// <summary>
        /// 短时间格式: HHmmss
        /// </summary>
        public const string TIME_COMPACT = "HHmmss";

        /// <summary>
        /// 短时间格式: HH:mm
        /// </summary>
        public const string TIME_SHORT = "HH:mm";

        /// <summary>
        /// 时间每段的格式: 00
        /// </summary>
        public const string TIME_ITEM = "00";

        /// <summary>
        /// 月日格式: MM-dd
        /// </summary>
        public const string MONTH_DAY = "MM-dd";

        /// <summary>
        /// Oracle 日期格式
        /// </summary>
        public const string ORA_DATE_FMT_LONG = "yyyy-MM-dd HH24:mi:ss";
        public const string DATE_LONG_MINUTE_CN = "yyyy年MM月dd日 HH时";
        public const string HEADER_FONT_NAME = "宋体";//表头标准字体
        public const float HEADER_FONT_SIZE = 10F;//表头标准字号
        public const string COLUMN_FONT_NAME = "宋体";//列标准字体
        public const float COLUMN_FONT_SIZE = 10.75F;//
        public const string BUTTON_FONT_NAME = "宋体";//按钮字体
        public const float BUTTON_FONT_SIZE = 10F;//按钮字号
        public const string CHECK_FONT_NAME = "宋体";//Check 字体
        public const float CHECK_FONT_SIZE = 10F;//字号
        public const string LOOK_UP_HEADER_FONT_NAME = "宋体";//按钮字体
        public const float LOOK_UP_HEADER_FONT_SIZE = 10F;//按钮字号
        public const string LOOK_UP_COLUMN_FONT_NAME = "宋体";//列标准字体
        public const float LOOK_UP_COLUMN_FONT_SIZE = 10F;//
        public const string RADIO_GROUP_FONT_NAME = "宋体";// RadioGroup 字体
        public const float RADIO_GROUP_FONT_SIZE = 10F;// RadioGroup 字号
        public const string DATE_EDIT_FONT_NAME = "宋体";// RadioGroup 字体
        public const float DATE_EDIT_FONT_SIZE = 10F;// RadioGroup 字号
        public const string TOOL_STRIP_STATUS_LABEL_FONT_NAME = "宋体"; //toolStripStatusLabel 字体
        public const float TOOL_STRIP_STATUS_LABEL_FONT_SIZE = 10F;// toolStripStatusLabel 字号
        public const string GROUP_BOX_FONT_NAME = "宋体"; //GroupControl  字体
        public const float GROUP_BOX_FONT_SIZE = 10F;//GroupControl  字号
        public const string BAR_FONT_NAME = "宋体";//XtraBars.Bar 字体
        public const float BAR_FONT_SIZE = 10F;//XtraBars.Bar  字号
        public const string MEMO_FONT_NAME = "宋体";//MemoEdit 字体
        public const float MEMO_FONT_SIZE = 10F;//MemoEdit  字号
        public const string SPIN_FONT_NAME = "宋体";//SpinEdit 字体
        public const float SPIN_FONT_SIZE = 10F;//SpinEdit  字号
        public const string TAB_FONT_NAME = "宋体";//SpinEdit 字体
        public const float TAB_FONT_SIZE = 10F;//SpinEdit  字号
        public const string COMBO_BOX_FONT_NAME = "宋体";//ComboBox 字体
        public const float COMBO_BOX_FONT_SIZE = 10F;//ComboBox  字号 
        public const float TAB_MDI_FONT_SIZE = 10F;//XtraTabbedMdiManager  字号
        public const string TAB_MDI_FONT_NAME = "宋体";//XtraTabbedMdiManager 字体

        /// <summary>
        /// 多院区查询条件 BTE 20210824
        /// </summary>
        public static string AND_HIS_UNIT_CODE = null;
        public static string HIS_UNIT_CODE = null;
        /// <summary>
        /// 是否为申请项目标识
        /// </summary>
        public static string APPLY_FLAG = "√";
        /// <summary>
        /// 未收费标识
        /// </summary>
        public const int UN_CHARGE_INDICATOR_VALUE = 0;
        /// <summary>
        /// 已收费标识
        /// </summary>
        public const int CHARGE_INDICATOR_VALUE = 1;

        /// <summary>
        /// 病人来源，门诊为1
        /// </summary>
        public const string PATIENT_SOURCE = "1";
        /// <summary>
        /// 门诊默认VISIT_ID为0
        /// </summary>
        public const int DEFAULT_VISIT_ID = 0;

        public const string NEW_ORDER_STATE_STR = "录入";
        public const string UPDATE_ORDER_STATE_STR = "修改";
        public const string PASS_HIS_CODE = "0";

        public const string PASS_CHECK_MODE = "mz";
    }
}
