﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace Tjhis.Controls.StatisticsControls
{
    public partial class ucPersonalInfo : RadiusUserControl
    {
        public DateTime Today
        {
            set
            {
                if(null != value)
                {
                    this.LDate.Text = string.Concat(value.ToString("yyyy-MM-dd"), " ", WeekString[(int)value.DayOfWeek]);
                }
            }
        }
        public string hospitalName
        {
            set
            {
                if(null != value)
                {
                    this.LHospitalInfo.Text = value;
                }
            }
        }
        private const int PersonalPhotoSize = 180;
        private const int RowDateHeight = 80;
        private const int RowAppInfoHeight = 30;
        private const int RowBottomHeight = 50;
        private const int MinPersonalMargin = 5;
        public ucPersonalInfo()
        {
            InitializeComponent();
        }

        protected override void SetContentSize()
        {
            //计算头像行高度
            int rowPicHeight = this.Height - RowDateHeight - RowAppInfoHeight - RowBottomHeight;
            int marginTopAndBottom = MinPersonalMargin;
            int marginLeftAndRight = (this.Width - PersonalPhotoSize) / 2;
            if (rowPicHeight > PersonalPhotoSize)
            {
                marginTopAndBottom = (rowPicHeight - PersonalPhotoSize) / 2;
            }
            this.picturePersonalPhoto.Margin = new Padding(marginLeftAndRight, marginTopAndBottom, marginLeftAndRight, marginTopAndBottom);
        }
        
    }
}
