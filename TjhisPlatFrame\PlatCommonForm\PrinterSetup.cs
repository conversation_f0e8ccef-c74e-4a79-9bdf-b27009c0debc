﻿using PlatCommon.Comm;
using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace PlatCommonForm
{
    public partial class PrinterSetup : ParentForm
    {
        public PrinterSetup()
        {
            InitializeComponent();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            print_load();
        }
        /// <summary>
        /// 加载打印机列表
        /// </summary>
        private void print_load()
        {
            PrintDocument print = new PrintDocument();
            string sDefault = print.PrinterSettings.PrinterName;//默认打印机名
            lblDefault.Text = sDefault;

            foreach (string sPrint in PrinterSettings.InstalledPrinters)//获取所有打印机名称
            {
                lstPrinter.Items.Add(sPrint);
                if (sPrint == sDefault)
                    lstPrinter.SelectedIndex = lstPrinter.Items.IndexOf(sPrint);
            }
        }
        private void simpleButton1_Click(object sender, EventArgs e)
        {
            if (lstPrinter.SelectedItem != null) //判断是否有选中值
            {
                if (Externs.SetDefaultPrinter(lstPrinter.SelectedItem.ToString())) //设置默认打印机
                {
                    //MessageBox.Show(lstPrinter.SelectedItem.ToString() + "设置为默认打印机成功！");
                    PrintDocument print = new PrintDocument();
                    string sDefault = print.PrinterSettings.PrinterName;//默认打印机名
                    lblDefault.Text = sDefault;
                    this.Close();
                }
                else
                {
                    MessageBox.Show(lstPrinter.SelectedItem.ToString() + "设置为默认打印机失败！");
                }
            }
        }

        private void SIB_EXIT_Click(object sender, EventArgs e)
        {
            this.Dispose();
            this.Close();
            
        }
    }
}
