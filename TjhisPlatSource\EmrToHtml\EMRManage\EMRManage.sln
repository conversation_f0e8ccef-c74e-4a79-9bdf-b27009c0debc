﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 15
VisualStudioVersion = 15.0.28307.572
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Utility", "Component\Utility.csproj", "{A0D06D5B-E58C-4BC3-96D7-3AF1EB8FE9D2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Model", "Model\Model.csproj", "{3FD7C7D8-F473-4CBA-BD16-2522368F8B12}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OracleDAL", "OracalDAL\OracleDAL.csproj", "{4A249B34-DAEE-465A-9A0F-77735326CF56}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ClinicalPath", "ClinicalPath\ClinicalPath.csproj", "{9F78A5F8-05F5-49D5-A3F8-753F1487D081}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A0D06D5B-E58C-4BC3-96D7-3AF1EB8FE9D2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A0D06D5B-E58C-4BC3-96D7-3AF1EB8FE9D2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A0D06D5B-E58C-4BC3-96D7-3AF1EB8FE9D2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A0D06D5B-E58C-4BC3-96D7-3AF1EB8FE9D2}.Debug|x86.ActiveCfg = Debug|x86
		{A0D06D5B-E58C-4BC3-96D7-3AF1EB8FE9D2}.Debug|x86.Build.0 = Debug|x86
		{A0D06D5B-E58C-4BC3-96D7-3AF1EB8FE9D2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A0D06D5B-E58C-4BC3-96D7-3AF1EB8FE9D2}.Release|Any CPU.Build.0 = Release|Any CPU
		{A0D06D5B-E58C-4BC3-96D7-3AF1EB8FE9D2}.Release|x64.ActiveCfg = Release|Any CPU
		{A0D06D5B-E58C-4BC3-96D7-3AF1EB8FE9D2}.Release|x86.ActiveCfg = Release|x86
		{A0D06D5B-E58C-4BC3-96D7-3AF1EB8FE9D2}.Release|x86.Build.0 = Release|x86
		{3FD7C7D8-F473-4CBA-BD16-2522368F8B12}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3FD7C7D8-F473-4CBA-BD16-2522368F8B12}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3FD7C7D8-F473-4CBA-BD16-2522368F8B12}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3FD7C7D8-F473-4CBA-BD16-2522368F8B12}.Debug|x86.ActiveCfg = Debug|x86
		{3FD7C7D8-F473-4CBA-BD16-2522368F8B12}.Debug|x86.Build.0 = Debug|x86
		{3FD7C7D8-F473-4CBA-BD16-2522368F8B12}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3FD7C7D8-F473-4CBA-BD16-2522368F8B12}.Release|Any CPU.Build.0 = Release|Any CPU
		{3FD7C7D8-F473-4CBA-BD16-2522368F8B12}.Release|x64.ActiveCfg = Release|Any CPU
		{3FD7C7D8-F473-4CBA-BD16-2522368F8B12}.Release|x86.ActiveCfg = Release|x86
		{3FD7C7D8-F473-4CBA-BD16-2522368F8B12}.Release|x86.Build.0 = Release|x86
		{4A249B34-DAEE-465A-9A0F-77735326CF56}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4A249B34-DAEE-465A-9A0F-77735326CF56}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4A249B34-DAEE-465A-9A0F-77735326CF56}.Debug|x64.ActiveCfg = Debug|Any CPU
		{4A249B34-DAEE-465A-9A0F-77735326CF56}.Debug|x86.ActiveCfg = Debug|x86
		{4A249B34-DAEE-465A-9A0F-77735326CF56}.Debug|x86.Build.0 = Debug|x86
		{4A249B34-DAEE-465A-9A0F-77735326CF56}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4A249B34-DAEE-465A-9A0F-77735326CF56}.Release|Any CPU.Build.0 = Release|Any CPU
		{4A249B34-DAEE-465A-9A0F-77735326CF56}.Release|x64.ActiveCfg = Release|Any CPU
		{4A249B34-DAEE-465A-9A0F-77735326CF56}.Release|x86.ActiveCfg = Release|x86
		{4A249B34-DAEE-465A-9A0F-77735326CF56}.Release|x86.Build.0 = Release|x86
		{9F78A5F8-05F5-49D5-A3F8-753F1487D081}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9F78A5F8-05F5-49D5-A3F8-753F1487D081}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9F78A5F8-05F5-49D5-A3F8-753F1487D081}.Debug|x64.ActiveCfg = Debug|x64
		{9F78A5F8-05F5-49D5-A3F8-753F1487D081}.Debug|x64.Build.0 = Debug|x64
		{9F78A5F8-05F5-49D5-A3F8-753F1487D081}.Debug|x86.ActiveCfg = Debug|x86
		{9F78A5F8-05F5-49D5-A3F8-753F1487D081}.Debug|x86.Build.0 = Debug|x86
		{9F78A5F8-05F5-49D5-A3F8-753F1487D081}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9F78A5F8-05F5-49D5-A3F8-753F1487D081}.Release|Any CPU.Build.0 = Release|Any CPU
		{9F78A5F8-05F5-49D5-A3F8-753F1487D081}.Release|x64.ActiveCfg = Release|x64
		{9F78A5F8-05F5-49D5-A3F8-753F1487D081}.Release|x64.Build.0 = Release|x64
		{9F78A5F8-05F5-49D5-A3F8-753F1487D081}.Release|x86.ActiveCfg = Release|x86
		{9F78A5F8-05F5-49D5-A3F8-753F1487D081}.Release|x86.Build.0 = Release|x86
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {63D6663A-E1F2-4B02-AEBD-0337B606836E}
	EndGlobalSection
EndGlobal
