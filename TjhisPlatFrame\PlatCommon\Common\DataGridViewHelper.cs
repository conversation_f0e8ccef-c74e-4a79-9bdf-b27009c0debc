﻿//-----------------------------------------------------------------------
//  系统名称        : 基础类库
//  子系统名称      : 辅助类
//  功能概要        : DataGridView 助手
//  作  者          : 付军
//  创建时间        : 2016-10-28
//  版本            : 1.0.0.0
//-----------------------------------------------------------------------
using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using System.IO;
using SQL = PlatCommon.Base02.Cs02StringHelper;
using PlatCommon.Base01;

namespace PlatCommon.Common
{
    /// <summary>
    /// DataGridView的显示模式
    /// </summary>
    public enum DataGridViewMode
    {
        SINGLE_ROW_VIEW = 1,
        SINGLE_ROW = 2,
        EDIT = 3,
        Other = 0
    }


    /// <summary>
    /// DataGridView 助手
    /// </summary>
    public class DataGridViewHelper
    {
        /// <summary>
        /// 设置DataGridView的属性
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="viewMode"></param>
        public static void SetGridMode(DataGridView dgv, DataGridViewMode viewMode)
        {
            dgv.BackgroundColor = SystemColors.ButtonFace;

            dgv.RowsDefaultCellStyle.BackColor = Color.Bisque;
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.Beige;

            switch (viewMode)
            {
                case DataGridViewMode.SINGLE_ROW_VIEW:
                    dgv.AutoGenerateColumns = false;
                    dgv.RowHeadersVisible = false;
                    dgv.AllowUserToResizeRows = false;

                    dgv.RowHeadersDefaultCellStyle.WrapMode = DataGridViewTriState.False;
                    dgv.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
                    dgv.AllowUserToAddRows = false;
                    dgv.AllowUserToDeleteRows = false;
                    dgv.AllowUserToResizeRows = false;
                    dgv.MultiSelect = false;
                    dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
                    dgv.ReadOnly = true;

                    break;

                case DataGridViewMode.SINGLE_ROW:
                    dgv.AutoGenerateColumns = false;
                    dgv.RowHeadersVisible = false;
                    dgv.AllowUserToResizeRows = false;

                    dgv.RowHeadersDefaultCellStyle.WrapMode = DataGridViewTriState.False;
                    dgv.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
                    dgv.AllowUserToAddRows = false;
                    dgv.AllowUserToDeleteRows = false;
                    dgv.AllowUserToResizeRows = false;
                    dgv.MultiSelect = false;
                    dgv.SelectionMode = DataGridViewSelectionMode.CellSelect;

                    break;

                case DataGridViewMode.EDIT:
                    dgv.AutoGenerateColumns = false;
                    dgv.RowHeadersVisible = false;
                    dgv.AllowUserToResizeRows = false;

                    dgv.RowHeadersDefaultCellStyle.WrapMode = DataGridViewTriState.False;
                    dgv.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
                    dgv.AllowUserToAddRows = true;
                    dgv.AllowUserToDeleteRows = true;
                    dgv.AllowUserToResizeRows = false;
                    dgv.MultiSelect = false;
                    dgv.SelectionMode = DataGridViewSelectionMode.CellSelect;
                    dgv.ReadOnly = false;

                    break;
                default:
                    break;
            }
        }


        /// <summary>
        /// 生成配置文件结构
        /// </summary>
        /// <returns></returns>
        public static DataSet GetConfigSchema()
        {
            DataSet ds = new DataSet("DataGridView_Cols_Config");
            DataTable dt = ds.Tables.Add("DataGridView_Cols");

            dt.Columns.Add("DATA_PROPERTY_NAME", typeof(System.String));    // 值字段
            dt.Columns.Add("HEADER_TEXT", typeof(System.String));           // 列标题            
            dt.Columns.Add("STYLE_FORMAT", typeof(System.String));          // 格式
            dt.Columns.Add("COL_WIDTH", typeof(System.Int32));              // 列宽
            dt.Columns.Add("SHOW_ORDER", typeof(System.Int32));             // 显示顺序

            return ds;
        }


        /// <summary>
        /// 保存列的配置文件
        /// </summary>
        /// <param name="dsColConfig"></param>
        /// <param name="dgvId"></param>
        public static void SaveColumnConfig(DataSet dsColConfig, string dgvId)
        {
            // 保存
            string fileName = Path.Combine(Cs01FileHelper.GetCurrDllPath(), "Config");
            if (Directory.Exists(fileName) == false)
            {
                Directory.CreateDirectory(fileName);
            }

            fileName = Path.Combine(fileName, dgvId + ".xml");

            dsColConfig.WriteXml(fileName, XmlWriteMode.WriteSchema);
        }


        /// <summary>
        /// 保存列宽
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="dgvId"></param>
        public static void SaveColumnConfig(DataGridView dgv, string dgvId)
        {
            // 获取本地配置文件
            DataSet ds = GetColumnConfig(dgvId);

            // 更新内容
            foreach (DataGridViewColumn dgvCol in dgv.Columns)
            {
                if (dgvCol.Visible == true)
                {
                    DataRow drEdit = null;

                    string colName = dgvCol.Name;
                    string filter = "DATA_PROPERTY_NAME = " + SQL.SqlConvert(colName);
                    DataRow[] drFind = ds.Tables[0].Select(filter);
                    if (drFind.Length == 0)
                    {
                        drEdit = ds.Tables[0].NewRow();
                        drEdit["DATA_PROPERTY_NAME"] = colName;                 // 列名
                        ds.Tables[0].Rows.Add(drEdit);
                    }
                    else
                    {
                        drEdit = drFind[0];
                    }

                    drEdit["HEADER_TEXT"] = dgvCol.HeaderText;                  // 标题                   
                    drEdit["COL_WIDTH"] = dgvCol.Width;                         // 列宽
                    drEdit["SHOW_ORDER"] = dgvCol.DisplayIndex;                 // 显示顺序

                    string styleFormat = dgvCol.DefaultCellStyle.Format;
                    DataGridViewImageColumn dgvImgCol = dgvCol as DataGridViewImageColumn;
                    if (dgvImgCol != null)
                    {
                        styleFormat = "P";
                    }

                    DataGridViewCheckBoxColumn dgvChkCol = dgvCol as DataGridViewCheckBoxColumn;
                    if (dgvChkCol != null)
                    {
                        styleFormat = "CB";
                    }

                    drEdit["STYLE_FORMAT"] = styleFormat;                       // 格式
                }
            }

            // 保存本地配置文件
            SaveColumnConfig(ds, dgvId);
        }


        /// <summary>
        /// 获取DataGridView的列配置信息
        /// </summary>
        /// <param name="dgvId"></param>
        /// <returns></returns>
        public static DataSet GetColumnConfig(string dgvId)
        {
            string fileName = Path.Combine(Cs01FileHelper.GetCurrDllPath(), "Config");
            fileName = Path.Combine(fileName, dgvId + ".xml");

            if (File.Exists(fileName))
            {
                DataSet ds = new DataSet();
                ds.ReadXml(fileName, XmlReadMode.ReadSchema);
                return ds;
            }
            else
            {
                return GetConfigSchema();
            }
        }


        /// <summary>
        /// DataGridView的列配置信息, 从数据源与配置文件中获取
        /// </summary>
        /// <param name="dsSrc"></param>
        /// <param name="dgvId"></param>
        /// <returns></returns>
        public static DataSet GetColumnConfig(DataSet dsSrc, string dgvId)
        {
            DataSet ds = GetColumnConfig(dgvId);

            if (dsSrc == null || dsSrc.Tables.Count == 0)
            {
                return ds;
            }

            foreach (DataColumn dc in dsSrc.Tables[0].Columns)
            {
                string filter = "DATA_PROPERTY_NAME = " + SQL.SqlConvert(dc.ColumnName);
                if (ds.Tables[0].Select(filter).Length > 0) continue;

                DataRow drNew = ds.Tables[0].NewRow();

                drNew["DATA_PROPERTY_NAME"] = dc.ColumnName;    // 值字段
                drNew["HEADER_TEXT"] = dc.ColumnName;           // 列标题            
                drNew["STYLE_FORMAT"] = string.Empty;           // 格式
                drNew["COL_WIDTH"] = 40;                        // 列宽
                drNew["SHOW_ORDER"] = -1;                       // 显示顺序

                ds.Tables[0].Rows.Add(drNew);
            }

            for (int i = ds.Tables[0].Rows.Count - 1; i >= 0; i--)
            {
                string colName = ds.Tables[0].Rows[i]["DATA_PROPERTY_NAME"].ToString();

                if (dsSrc.Tables[0].Columns.Contains(colName) == false)
                {
                    ds.Tables[0].Rows[i].Delete();
                }
            }

            return ds;
        }


        /// <summary>
        /// 通过配置信息生成DataGridView的列
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="ds"></param>
        public static void AddColByConfig(DataGridView dgv, DataSet ds)
        {
            try
            {
                if (DataSetHelper.HasRecord(ds) == false) return;

                // 隐藏所有的列
                foreach (DataGridViewColumn dgvCol in dgv.Columns)
                {
                    dgvCol.Visible = false;
                }

                // 显示已配置的列
                DataRow[] drFind = ds.Tables[0].Select("SHOW_ORDER >= 0", "SHOW_ORDER");

                for (int i = 0; i < drFind.Length; i++)
                {
                    DataRow dr = drFind[i];

                    string colName = dr["DATA_PROPERTY_NAME"].ToString();     // 列名
                    string styleFormat = dr["STYLE_FORMAT"].ToString();     // 格式

                    // 其它
                    DataGridViewColumn dgvColumn = null;

                    // 如果列不存在
                    if (dgv.Columns.Contains(colName) == false)
                    {
                        // 图片
                        if (styleFormat.Equals("P"))
                        {
                            dgvColumn = new DataGridViewImageColumn();
                        }
                        // 选择框
                        else if (styleFormat.Equals("CB"))
                        {
                            DataGridViewCheckBoxColumn dgvC = new DataGridViewCheckBoxColumn();
                            dgvC.TrueValue = 1;
                            dgvC.FalseValue = 0;
                            dgvColumn = dgvC;
                        }
                        // 其他
                        else
                        {
                            dgvColumn = new DataGridViewTextBoxColumn();
                            dgvColumn.DefaultCellStyle.Format = dr["STYLE_FORMAT"].ToString();
                        }

                        dgvColumn.Name = colName;
                        dgv.Columns.Add(dgvColumn);
                    }
                    else
                    {
                        if (styleFormat.Equals("P"))
                        {
                            dgvColumn = dgv.Columns[colName] as DataGridViewImageColumn;
                        }
                        else if (styleFormat.Equals("CB"))
                        {
                            dgvColumn = dgv.Columns[colName] as DataGridViewCheckBoxColumn;
                        }
                        else
                        {
                            dgvColumn = dgv.Columns[colName] as DataGridViewTextBoxColumn;
                            dgvColumn.DefaultCellStyle.Format = dr["STYLE_FORMAT"].ToString();
                        }
                    }

                    // 设置列属性
                    dgvColumn.DataPropertyName = colName;
                    dgvColumn.HeaderText = dr["HEADER_TEXT"].ToString();

                    // 如果是数值型, 右对齐
                    if (dr["STYLE_FORMAT"].ToString().StartsWith("N"))
                    {
                        dgvColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                    }

                    dgvColumn.Width = Converter.ToInt(dr["COL_WIDTH"].ToString());

                    dgvColumn.DisplayIndex = i;

                    // 显示列
                    dgvColumn.Visible = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }


        /// <summary>
        /// 通过配置信息生成DataGridView的列
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="ds"></param>
        public static void AddColByConfig(DataGridView dgv, string dgvId)
        {
            DataSet ds = GetColumnConfig(dgvId);
            AddColByConfig(dgv, ds);
        }


        /// <summary>
        /// 更新现有排序
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="ds"></param>
        /// <param name="colNameOrder"></param>
        public static void ResetShowOrder(ref DataGridView dgv, ref DataSet ds, string colNameOrder)
        {
            int min = 1000;
            int max = min + dgv.Rows.Count - 1;

            for (int i = dgv.Rows.Count - 1; i >= 0; i--)
            {
                DataRow dr = (dgv.Rows[i].DataBoundItem as DataRowView).Row;
                dr[colNameOrder] = min + i;
            }

            DataRow[] drFind = ds.Tables[0].Select(colNameOrder + " >= 0", colNameOrder);
            for (int i = 0; i < drFind.Length; i++)
            {
                drFind[i][colNameOrder] = i;
            }
        }


        /// <summary>
        /// 向上移动
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="ds"></param>
        /// <param name="colNameOrder"></param>
        public static void MoveUpRow(ref DataGridView dgv, ref DataSet ds, string colNameOrder)
        {
            if (dgv.SelectedRows.Count == 0) return;

            ResetShowOrder(ref dgv, ref ds, colNameOrder);

            // 选中行上移
            DataRow drSel = (dgv.SelectedRows[0].DataBoundItem as DataRowView).Row;
            int showOrder = Converter.ToInt(drSel[colNameOrder]);
            if (showOrder <= 0) return;

            DataRow[] drFind = ds.Tables[0].Select(colNameOrder + " = " + (showOrder - 1).ToString());
            if (drFind.Length > 0)
            {
                drFind[0][colNameOrder] = showOrder;
            }

            drSel[colNameOrder] = showOrder - 1;
        }


        /// <summary>
        /// 向下移动
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="ds"></param>
        /// <param name="colNameOrder"></param>
        public static void MoveDownRow(ref DataGridView dgv, ref DataSet ds, string colNameOrder)
        {
            if (dgv.SelectedRows.Count == 0) return;

            // 重新编号            
            ResetShowOrder(ref dgv, ref ds, colNameOrder);

            // 选中行下移
            DataRow drSel = (dgv.SelectedRows[0].DataBoundItem as DataRowView).Row;
            int showOrder = Converter.ToInt(drSel[colNameOrder]);
            if (showOrder >= dgv.Rows.Count - 1) return;

            DataRow[] drFind = ds.Tables[0].Select(colNameOrder + " = " + (showOrder + 1).ToString());
            if (drFind.Length > 0)
            {
                drFind[0][colNameOrder] = showOrder;
            }

            drSel[colNameOrder] = showOrder + 1;
        }


        /// <summary>
        /// 定位当前行
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="keyColName"></param>
        /// <param name="keyValue"></param>
        public static void SetCurrRow(ref DataGridView dgv, string keyColName, string keyValue)
        {
            if (dgv.SelectedRows.Count > 0)
            {
                if ((dgv.SelectedRows[0].DataBoundItem as DataRowView).Row[keyColName].ToString().Equals(keyValue))
                {
                    return;
                }
            }

            foreach (DataGridViewRow dgvRow in dgv.Rows)
            {
                if ((dgvRow.DataBoundItem as DataRowView).Row[keyColName].ToString().Equals(keyValue))
                {
                    dgvRow.Selected = true;
                    dgv.FirstDisplayedScrollingRowIndex = dgvRow.Index;
                    break;
                }
            }
        }
    }
}
