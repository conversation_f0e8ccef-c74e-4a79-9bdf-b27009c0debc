﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace PlatCommon.Base01
{
    /// <summary>
    /// 显示信息类
    /// </summary>
    public static class Cs01ShowInfo
    {
        private static Dictionary<int, string> _data = null;
        private static Dictionary<int, string> Data
        {
            get
            {
                if (_data == null)
                {
                    _data = new Dictionary<int, string>();
                    string s = "";
                    s = "ORA-00933: SQL command not properly ended";
                    _data.Add(s.GetHashCode(), "SQL 命令未正确结束。");
                    s = "ORA-00936: missing expression";
                    _data.Add(s.GetHashCode(), "缺失表达式。");
                    s = "ORA-01017: invalid username/password; logon denied";
                    _data.Add(s.GetHashCode(), "用户和密码无法登录，登录被拒。");
                    s = "ORA-12154: TNS:could not resolve the connect identifier specified";
                    _data.Add(s.GetHashCode(), "数据库不能连接，请重新设置。");
                    s = "ORA-12151: TNS:no listener";
                    _data.Add(s.GetHashCode(), "没有侦听，请检查服务器数据库是否运行，或者关闭防火墙。");
                    s = "ORA-12514 TNS:listener does not currently know of service requested in connect descriptor";
                    _data.Add(s.GetHashCode(), "TNS: 监听程序当前无法识别连接描述符中请求的服务");
                }
                return _data;
            }
        }

        /// <summary>
        /// 取字段信息
        /// </summary>
        /// <param name="strFieldName">字段名称</param>
        /// <returns></returns>
        public static string GetFriendlyInfo(string strFieldName)
        {
            int key = strFieldName.GetHashCode();
            if (Data.ContainsKey(key)) return _data[key];
            return strFieldName;
        }
    }
}
