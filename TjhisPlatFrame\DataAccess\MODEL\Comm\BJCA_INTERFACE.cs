﻿/*-----------------------------------------------------------------------
 * 类名称    ：BJCA_INTERFACE
 * 类描述    ：
 * 创建人    ：梁吉lions
 * 创建时间  ：2016/9/18 9:58:08
 * 修改人    ：
 * 修改时间  ：
 * 修改备注  ：
 * 版本      ：
 * ----------------------------------------------------------------------
 */
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using System.ComponentModel;

namespace Model
{

    /// <summary>
    ///数字签名
    /// </summary>

    [DataContract]
    public class BJCA_INTERFACE : NotificationObject
    {

        /// <summary>
        /// BJCA_ID
        /// </summary>		
        private string _bjca_id;
        [DataMember]
        public string BJCA_ID
        {
            get { return _bjca_id; }
            set
            {
                if (_bjca_id != value)
                {
                    _bjca_id = value;
                    this.RaisePropertyChanged("BJCA_ID");
                }
            }
        }
        /// <summary>
        /// BJCA_INFO
        /// </summary>		
        private String  _bjca_info;
        [DataMember]
        public String BJCA_INFO
        {
            get { return _bjca_info; }
            set
            {
                if (_bjca_info != value)
                {
                    _bjca_info = value;
                    this.RaisePropertyChanged("BJCA_INFO");
                }
            }
        }
        /// <summary>
        /// HIS_ID
        /// </summary>		
        private string _his_id;
        [DataMember]
        public string HIS_ID
        {
            get { return _his_id; }
            set
            {
                if (_his_id != value)
                {
                    _his_id = value;
                    this.RaisePropertyChanged("HIS_ID");
                }
            }
        }
        /// <summary>
        /// BJCA_IMG
        /// </summary>		
        private byte[] _bjca_img;
        [DataMember]
        public byte[] BJCA_IMG
        {
            get { return _bjca_img; }
            set
            {
                if (_bjca_img != value)
                {
                    _bjca_img = value;
                    this.RaisePropertyChanged("BJCA_IMG");
                }
            }
        }
        /// <summary>
        /// BJCA_IMG_STR
        /// </summary>		
        private string _bjca_img_str;
        [DataMember]
        public string BJCA_IMG_STR
        {
            get { return _bjca_img_str; }
            set
            {
                if (_bjca_img_str != value)
                {
                    _bjca_img_str = value;
                    this.RaisePropertyChanged("BJCA_IMG_STR");
                }
            }
        }
        /// <summary>
        /// CA_ID
        /// </summary>		
        private string _ca_id;
        [DataMember]
        public string CA_ID
        {
            get { return _ca_id; }
            set
            {
                if (_ca_id != value)
                {
                    _ca_id = value;
                    this.RaisePropertyChanged("CA_ID");
                }
            }
        }

    }
}