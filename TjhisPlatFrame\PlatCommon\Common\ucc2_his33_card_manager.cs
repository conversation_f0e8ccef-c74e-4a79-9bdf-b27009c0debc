﻿using DevExpress.XtraEditors;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace PlatCommon.Common
{
    public class ucc2_his33_card_manager
    {
        public string is_pass_word = "";
        public string is_card_type = "";
        public string of_get_status(int ai_status)
        {
            string ls_tmp = "";
            switch (ai_status)
            {
                case 0:
                    ls_tmp = "正常";
                    break;
                case 1:
                    ls_tmp = "挂失";
                    break;
                case 2:
                    ls_tmp = "注销";
                    break;
                default:
                    ls_tmp = "无效的状态值：" + ai_status.ToString();
                    break;
            }
            return ls_tmp;
        }
        private int of_write_log(string as_card_no, string as_msg, string as_operator_no, string as_patientid, ref Dictionary<string, string> idc)
        {
            if (string.IsNullOrEmpty(is_card_type)) is_card_type = "";
            string sql = "Insert Into medical_card_log (card_no, change_date, change_cause, operator, enter_date, patient_id, card_type) Values ('" + as_card_no + "', sysdate, '" + as_msg + "', '" + as_operator_no + "', sysdate, '" + as_patientid + "','" + is_card_type + "')";
            string ret_info = "保存medical_card_log错误";
            idc.Add(sql, ret_info);
            return 0;
        }
        //========================================
        //查找正在使用的卡
        //--------------------------------------
        //Return：0     没有正在使用的卡
        //        1     有正在使用的卡，同时参数as_cardno
        //                为其卡号
        //       -1     执行错误
        //---------------------------------------
        //“正在使用的卡”，指非注销的卡
        //=========================================
        public int uf_search_usingcard(string as_pid, ref string as_cardno)
        {
            string sql = "Select card_no  From medrec.medical_card_memo Where patient_id = '" + as_pid + "' And account_status <> 2 ";
            as_cardno = new NM_Service.NMService.ServerPublicClient().GetSingleValue(sql);
            if (string.IsNullOrEmpty(as_cardno))
            {
                XtraMessageBox.Show("检索卡信息失败", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return 0;
            }
            else
            {
                return 1;
            }
        }
        public int uf_loss_card(string as_card_no, string as_operator_no, string as_patientid, ref Dictionary<string, string> idc)
        {
            string sql = "Update medical_card_memo Set account_status = 1 Where card_no = '" + as_card_no + "'";
            idc.Add(sql, "保存medical_card_memo失败");
            // 根据卡号获取患者ID
            if (string.IsNullOrEmpty(as_patientid))
            {
                sql = "select patient_id into :as_patientid from medical_card_memo where card_no = '" + as_card_no + "'";
                as_patientid = new NM_Service.NMService.ServerPublicClient().GetSingleValue(sql);
            }
            //写日志
            int liRtn = of_write_log(as_card_no, "挂失", as_operator_no, as_patientid, ref idc);
            return liRtn;
        }

        public int uf_new_card(string as_card_no, string as_patientid, string as_card_type, DateTime adt_stop_date, decimal adc_cardmoney, string as_operator_no, string as_old_card, ref Dictionary<string, string> idc)
        {
            //==============================
            //办卡
            //==============================
            int liRtn, li_count;
            string ls_temp, ls_pwd;
            //检查是否已经存在的卡号
            string sql = "Select Count(*) From medrec.medical_card_memo Where card_no = '" + as_card_no + "'";
            string value = new NM_Service.NMService.ServerPublicClient().GetSingleValue(sql);
            if (string.IsNullOrEmpty(value))
            {
                XtraMessageBox.Show("读取MEDICAL_CARD_MEMO错误", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return -1;
            }
            li_count = Convert.ToInt32(value);
            if (li_count > 0)
            {
                XtraMessageBox.Show("当前卡号" + as_card_no + "已经被使用，更新!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return -1;
            }
            //输入密码
            //Open(w_his33_input_pwd)
            //ls_pwd = Message.stringParm
            //ls_pwd=is_pass_word
            ls_pwd = is_pass_word;
            if (string.IsNullOrEmpty(ls_pwd))
            {
                ls_pwd = "666666";
            }
            //加密
            //ls_pwd= GetEncryptPW('国宝熊猫', trim(ls_pwd) )
            if (as_card_no.Length >= 18 && as_card_type == "门诊卡")
            {
                XtraMessageBox.Show("信息有误，请重新选择读卡!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return -1;
            }
            //保存卡信息  as_old_card
            sql = "Insert into medrec.medical_card_memo (patient_id, card_no, card_type, expiration_date, operate_date, account_status, operator_no, account_pwd, card_money, ID_NO)" +
                " Values ('" + as_patientid + "','" + as_card_no + "','" + as_card_type + "',to_date('" + adt_stop_date + "','yyyy-MM-dd hh24:mi:ss'), sysdate, 0,'" + as_operator_no + "','" + ls_pwd + "'," + adc_cardmoney + ", '" + as_old_card + "')";
            idc.Add(sql, "保存卡信息错误");
            //写日志
            ls_temp = "新办卡，病人ID：" + as_patientid;
            liRtn = of_write_log(as_card_no, ls_temp, as_operator_no, as_patientid, ref idc);
            return liRtn;
        }
        public int uf_update_pwd(string as_old_pwd, string as_new_pwd, string as_card_no, ref Dictionary<string, string> idc)
        {
            int liRtn = 0, li_status = 0;
            string ls_msg = "", ls_pwd = "";
            //取卡的密码 
            string sql = " Select account_pwd, account_status From medrec.medical_card_memo Where card_no =:as_card_no";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("as_card_no");
            para_val.Add(as_card_no);
            DataSet ds = spc.GetDataTable_Para(sql, para, para_val);
            ls_pwd = ds.Tables[0].Rows[0][0].ToString();
            li_status = int.Parse(ds.Tables[0].Rows[0][1].ToString());
            if (ds == null)
            {
                ls_msg = "数据库错误";
                XtraMessageBox.Show(ls_msg, "提示", MessageBoxButtons.OK);
                return -1;
            }
            if (ds.Tables[0].Rows.Count == 0)
            {
                ls_msg = "没有指定卡号" + as_card_no + "的数据信息";
                XtraMessageBox.Show(ls_msg, "提示", MessageBoxButtons.OK);
                return -1;
            }
            if (li_status != 0)
            {
                ls_msg = "此卡已'" + of_get_status(li_status) + "',不能够修改密码.";
            }
            else
            {
                //解密
                ls_pwd = ls_pwd;
                //比较
                if (ls_pwd != as_old_pwd)
                {
                    ls_msg = "原密码错误";
                }
                else
                {
                    //加密
                    as_new_pwd = as_new_pwd;
                    //更新密码
                    string sqltext = string.Format("Update medrec.medical_card_memo Set account_pwd ='{0}' Where card_no ='{1}'", as_new_pwd, as_card_no);
                    idc.Add(sqltext, "更新密码错误！");
                }
            }
            if (ls_msg != "")
            {
                XtraMessageBox.Show(ls_msg, "提示", MessageBoxButtons.OK);
                return -1;
            }
            return liRtn;
        }
        public int uf_reuse_card(string as_card_no, string as_patientid, DateTime adt_expiration_date, string as_operator_no, ref Dictionary<string, string> idc)
        {
            //============================================
            //挂失卡启用
            //============================================
            int liRtn;
            int li_count;
            //写0 -- 启用状态;有效日期
            string sql = "Update medical_card_memo Set account_status = 0, expiration_date = to_date('" + adt_expiration_date + "','yyyy-mm-dd hh24:mi:ss') Where card_no = '" + as_card_no + "'";
            idc.Add(sql, "保存medical_card_memo失败");
            if (string.IsNullOrEmpty(as_patientid))
            {
                sql = "select patient_id from medical_card_memo where card_no = '" + as_card_no + "'";
                as_patientid = new NM_Service.NMService.ServerPublicClient().GetSingleValue(sql);
            }
            //写日志
            liRtn = of_write_log(as_card_no, "重新启用", as_operator_no, as_patientid, ref idc);
            return liRtn;
        }
        public int uf_logout_card(string as_card_no, string as_operator_no, string as_patientid, ref Dictionary<string, string> idc)
        {
            //========================================
            //卡注销
            //========================================
            int lirtn;
            //注销状态
            string sql = "Update medical_card_memo Set account_status = 2 Where card_no = '" + as_card_no + "'";
            idc.Add(sql, "保存medical_card_memo失败");
            if (string.IsNullOrEmpty(as_patientid))
            {
                sql = "Select patient_id  From medical_card_memo Where card_no ='" + as_card_no + "'";
                as_patientid = new NM_Service.NMService.ServerPublicClient().GetSingleValue(sql);
            }
            //写日志
            lirtn = of_write_log(as_card_no, "注销", as_operator_no, as_patientid, ref idc);
            return lirtn;
        }
        public int uf_renew_card(string as_old_card, string as_new_card, string as_patientid, string as_card_type, DateTime adt_stop_date, decimal adc_cardmoney, string as_operator_no, ref Dictionary<string, string> idc)
        {
            //==============================================
            //补办卡
            //==============================================
            int liRtn;
            //注销旧的
            if (as_old_card != "APP")
            {
                liRtn = uf_logout_card(as_old_card, as_operator_no, as_patientid,ref idc);
                if (liRtn == -1) return -1;
            }

            //建立新的
            liRtn = uf_new_card(as_new_card, as_patientid, as_card_type, adt_stop_date, adc_cardmoney, as_operator_no, as_old_card, ref idc);
            return liRtn;
        }
        //===========================================
        //检查卡的有效性：uf_check_valid
        //-----------------------------------------
        //参数：string as_pid 病人id
        //      string as_card_no 卡号
        //      boolean ab_check_pwd 是否要求输入密码
        //-----------------------------------------
        //返回：
        //===========================================
        public int uf_check_valid(ref string as_pid, string as_card_no, bool ab_check_pwd)
        {
            int liRtn = 0;
            string ls_pwd = "", ls_check_pwd = "", ls_status = "";
            //进行校验
            string sql = "select account_pwd, account_status, patient_id From medrec.medical_card_memo Where(card_no = :as_card_no)";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("as_card_no");
            para_val.Add(as_card_no);
            DataSet ds = spc.GetDataTable_Para(sql, para, para_val);
            if (ds == null)
            {
                //数据库错误
                XtraMessageBox.Show("卡校验检索表错误.", "提示");
                return -1;
            }
            else if (ds.Tables[0].Rows.Count <= 0)
            {
                //没 数据
                XtraMessageBox.Show("没有记录此卡的数据信息.", "提示");
                return -1;
            }
            else
            {
                ls_pwd = ds.Tables[0].Rows[0][0].ToString();
                ls_status = ds.Tables[0].Rows[0][1].ToString();
                as_pid = ds.Tables[0].Rows[0][2].ToString();
                if (ab_check_pwd)
                {
                    //需要验证密码时,请输入密码
                    using (w_his33_input_pwd input_pwd = new w_his33_input_pwd())
                    {
                        if (input_pwd.ShowDialog() == DialogResult.OK)
                        {
                            ls_check_pwd = w_his33_input_pwd.ls_temp;
                        }

                    }
                    //加密
                    ls_check_pwd = ls_check_pwd;
                    //密码对比
                    if (ls_pwd != ls_check_pwd)
                    {
                        //密码错误
                        XtraMessageBox.Show("输入的密码错误.", "提示");
                        return -1;
                    }
                }
                switch (ls_status)
                {
                    case "0":
                        //卡验证通过
                        break;
                    case "1":
                        XtraMessageBox.Show("此卡已被“挂失”，请取消挂失后再使用此卡.", "提示");
                        liRtn = -1;
                        break;
                    case "2":
                        XtraMessageBox.Show("此卡已被“注销”，不能够再使用.", "提示");
                        liRtn = -1;
                        break;
                    default:
                        XtraMessageBox.Show("无效标记卡，请到相关部门核实后再使用.", "提示");
                        break;
                }
            }
            return liRtn;
        }
    }
}
