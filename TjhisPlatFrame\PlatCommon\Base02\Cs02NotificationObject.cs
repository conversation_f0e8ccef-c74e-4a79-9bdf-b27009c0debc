﻿/*********************************************
* 文 件 名：Cs02NotificationObject
* 类 名 称：Cs02NotificationObject
* 功能说明：实现数据更新广播
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：尹志伟
* 创建时间：2016-10-28
* 版 本 号：1.0.0.1
* 修改时间：尹志伟
* 修 改 人：2018-06-12
* CLR 版本：4.0.30319.42000
/*********************************************/

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Runtime.Serialization;

namespace PlatCommon.Base02
{
    /// <summary>
    /// 监听对象，实现数据更新广播，及时刷新显示
    /// </summary>
    [DataContractAttribute]
    public class Cs02NotificationObject : INotifyPropertyChanged
    {
        /// <summary>
        /// 监听对象，实现数据更新广播，及时刷新显示
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="action"></param>
        protected void RaisePropertyChanged<T>(Expression<Func<T>> action)
        {
            var propertyName = GetPropertyName(action);
            RaisePropertyChanged(propertyName);
        }

        private static string GetPropertyName<T>(Expression<Func<T>> action)
        {
            var expression = (MemberExpression)action.Body;
            var propertyName = expression.Member.Name;
            return propertyName;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="propertyName"></param>
        protected void RaisePropertyChanged(string propertyName)
        {
            if (PropertyChanged != null)
                PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 
        /// </summary>
        //[field: NonSerialized]
        [field: NonSerializedAttribute()]
        public event PropertyChangedEventHandler PropertyChanged;
    }
}
