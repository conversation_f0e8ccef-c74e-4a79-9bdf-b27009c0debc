﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using System.ComponentModel;
using System.Data;

namespace Model
{
    /// <summary>
    ///工作人员字典
    /// </summary>
    [DataContract]
    public class STAFF_DICT : NotificationObject
    {

        		
        private string _emp_no;
        /// <summary>
        /// 人员编号
        /// </summary>
        [DataMember]
        public string EMP_NO
        {
            get { return _emp_no; }
            set
            {
                if (_emp_no != value)
                {
                    _emp_no = value;
                    this.RaisePropertyChanged("EMP_NO");
                }
            }
        }
        		
        private string _dept_code;
        /// <summary>
        /// 所在科室编码
        /// </summary>
        [DataMember]
        public string DEPT_CODE
        {
            get { return _dept_code; }
            set
            {
                if (_dept_code != value)
                {
                    _dept_code = value;
                    this.RaisePropertyChanged("DEPT_CODE");
                }
            }
        }
        		
        private string _dept_name;
        /// <summary>
        /// 所在科室名称
        /// </summary>
        [DataMember]
        public string DEPT_NAME
        {
            get { return _dept_name; }
            set
            {
                if (_dept_name != value)
                {
                    _dept_name = value;
                    this.RaisePropertyChanged("DEPT_NAME");
                }
            }
        }
        		
        private string _name;
        /// <summary>
        /// 姓名
        /// </summary>
        [DataMember]
        public string NAME
        {
            get { return _name; }
            set
            {
                if (_name != value)
                {
                    _name = value;
                    this.RaisePropertyChanged("NAME");
                }
            }
        }
        		
        private string _input_code;
        /// <summary>
        /// 拼音码
        /// </summary>
        [DataMember]
        public string INPUT_CODE
        {
            get { return _input_code; }
            set
            {
                if (_input_code != value)
                {
                    _input_code = value;
                    this.RaisePropertyChanged("INPUT_CODE");
                }
            }
        }
        		
        private string _job;
        /// <summary>
        /// 工作类别
        /// </summary>
        [DataMember]
        public string JOB
        {
            get { return _job; }
            set
            {
                if (_job != value)
                {
                    _job = value;
                    this.RaisePropertyChanged("JOB");
                }
            }
        }
        		
        private string _title;
        /// <summary>
        /// 职称
        /// </summary>
        [DataMember]
        public string TITLE
        {
            get { return _title; }
            set
            {
                if (_title != value)
                {
                    _title = value;
                    this.RaisePropertyChanged("TITLE");
                }
            }
        }
        		
        private string _user_name;
        /// <summary>
        /// 系统用户名
        /// </summary>
        [DataMember]
        public string USER_NAME
        {
            get { return _user_name; }
            set
            {
                if (_user_name != value)
                {
                    _user_name = value;
                    this.RaisePropertyChanged("USER_NAME");
                }
            }
        }
        		
        private string _input_code_wb;
        /// <summary>
        /// 五笔码
        /// </summary>
        [DataMember]
        public string INPUT_CODE_WB
        {
            get { return _input_code_wb; }
            set
            {
                if (_input_code_wb != value)
                {
                    _input_code_wb = value;
                    this.RaisePropertyChanged("INPUT_CODE_WB");
                }
            }
        }
        		
        private string _id;
        /// <summary>
        /// 主键ID
        /// </summary>
        [DataMember]
        public string ID
        {
            get { return _id; }
            set
            {
                if (_id != value)
                {
                    _id = value;
                    this.RaisePropertyChanged("ID");
                }
            }
        }
        		
        private DateTime _create_date;
        /// <summary>
        /// 创建日期
        /// </summary>
        [DataMember]
        public DateTime CREATE_DATE
        {
            get { return _create_date; }
            set
            {
                if (_create_date != value)
                {
                    _create_date = value;
                    this.RaisePropertyChanged("CREATE_DATE");
                }
            }
        }
        		
        private string _password;
        /// <summary>
        /// 加密后口令
        /// </summary>
        [DataMember]
        public string PASSWORD
        {
            get { return _password; }
            set
            {
                if (_password != value)
                {
                    _password = value;
                    this.RaisePropertyChanged("PASSWORD");
                }
            }
        }
        		
        private decimal _sys_flag;
        /// <summary>
        /// 是否oracle用户
        /// </summary>
        [DataMember]
        public decimal SYS_FLAG
        {
            get { return _sys_flag; }
            set
            {
                if (_sys_flag != value)
                {
                    _sys_flag = value;
                    this.RaisePropertyChanged("SYS_FLAG");
                }
            }
        }
        		
        private decimal _status;
        /// <summary>
        /// 用户标志：1 正常 0 停用
        /// </summary>
        [DataMember]
        public decimal STATUS
        {
            get { return _status; }
            set
            {
                if (_status != value)
                {
                    _status = value;
                    this.RaisePropertyChanged("STATUS");
                }
            }
        }
        		
        private decimal _roleid;
        /// <summary>
        /// 角色ID
        /// </summary>
        [DataMember]
        public decimal ROLEID
        {
            get { return _roleid; }
            set
            {
                if (_roleid != value)
                {
                    _roleid = value;
                    this.RaisePropertyChanged("ROLEID");
                }
            }
        }
        		
        private string _ip;
        /// <summary>
        /// 用户登录机子的IP
        /// </summary>
        [DataMember]
        public string IP
        {
            get { return _ip; }
            set
            {
                if (_ip != value)
                {
                    _ip = value;
                    this.RaisePropertyChanged("IP");
                }
            }
        }
        		
        private byte[] _autograph;
        /// <summary>
        /// 用户图片
        /// </summary>
        [DataMember]
        public byte[] AUTOGRAPH
        {
            get { return _autograph; }
            set
            {
                if (_autograph != value)
                {
                    _autograph = value;
                    this.RaisePropertyChanged("AUTOGRAPH");
                }
            }
        }
        		
        private string _community_code;
        /// <summary>
        /// 社区编码
        /// </summary>
        [DataMember]
        public string COMMUNITY_CODE
        {
            get { return _community_code; }
            set
            {
                if (_community_code != value)
                {
                    _community_code = value;
                    this.RaisePropertyChanged("COMMUNITY_CODE");
                }
            }
        }
        		
        private string _sex;
        /// <summary>
        /// 性别
        /// </summary>
        [DataMember]
        public string SEX
        {
            get { return _sex; }
            set
            {
                if (_sex != value)
                {
                    _sex = value;
                    this.RaisePropertyChanged("SEX");
                }
            }
        }
        		
        private string _id_no;
        /// <summary>
        /// 身份证号
        /// </summary>
        [DataMember]
        public string ID_NO
        {
            get { return _id_no; }
            set
            {
                if (_id_no != value)
                {
                    _id_no = value;
                    this.RaisePropertyChanged("ID_NO");
                }
            }
        }
        		
        private string _nation;
        /// <summary>
        /// 民族
        /// </summary>
        [DataMember]
        public string NATION
        {
            get { return _nation; }
            set
            {
                if (_nation != value)
                {
                    _nation = value;
                    this.RaisePropertyChanged("NATION");
                }
            }
        }
        		
        private string _mailing_address;
        /// <summary>
        /// 籍贯
        /// </summary>
        [DataMember]
        public string MAILING_ADDRESS
        {
            get { return _mailing_address; }
            set
            {
                if (_mailing_address != value)
                {
                    _mailing_address = value;
                    this.RaisePropertyChanged("MAILING_ADDRESS");
                }
            }
        }
        /// <summary>
        /// 护士或医生执照号码
        /// </summary>		
        private string _work_id;
        [DataMember]
        public string WORK_ID
        {
            get { return _work_id; }
            set
            {
                if (_work_id != value)
                {
                    _work_id = value;
                    this.RaisePropertyChanged("WORK_ID");
                }
            }
        }
        /// <summary>
        /// 在职状态
        /// </summary>		
        private string _work_state;
        [DataMember]
        public string WORK_STATE
        {
            get { return _work_state; }
            set
            {
                if (_work_state != value)
                {
                    _work_state = value;
                    this.RaisePropertyChanged("WORK_STATE");
                }
            }
        }
        /// <summary>
        /// 编制
        /// </summary>		
        private string _formation;
        [DataMember]
        public string FORMATION
        {
            get { return _formation; }
            set
            {
                if (_formation != value)
                {
                    _formation = value;
                    this.RaisePropertyChanged("FORMATION");
                }
            }
        }

        /// <summary>
        /// 医院编号  张鹏20170614
        /// </summary>		
        private string _hisunitcode;
        [DataMember]
        public string HISUNITCODE
        {
            get { return _hisunitcode; }
            set
            {
                if (_hisunitcode != value)
                {
                    _hisunitcode = value;
                    this.RaisePropertyChanged("HISUNITCODE");
                }
            }
        }

        /// <summary>
        /// 姓名
        /// </summary>		
        private string _ca_enabled;
        [DataMember]
        public string CA_ENABLED
        {
            get { return _ca_enabled; }
            set
            {
                if (_ca_enabled != value)
                {
                    _ca_enabled = value;
                    this.RaisePropertyChanged("CA_ENABLED");
                }
            }
        }

        /// <summary>
        /// 复制信息到人员信息类(HIS)
        /// </summary>
        public static STAFF_DICT CopyToModel(DataRow dRow)
        {
            STAFF_DICT model1 = new STAFF_DICT();

            if (dRow["EMP_NO"] != null && dRow["EMP_NO"].ToString() != "")
            {
                model1.EMP_NO = dRow["EMP_NO"].ToString();
            }

            if (dRow["CREATE_DATE"] != null && dRow["CREATE_DATE"].ToString() != "")
            {
                model1.CREATE_DATE = DateTime.Parse(dRow["CREATE_DATE"].ToString());
            }

            if (dRow["PASSWORD"] != null && dRow["PASSWORD"].ToString() != "")
            {
                model1.PASSWORD = dRow["PASSWORD"].ToString();
            }

            if (dRow["SYS_FLAG"] != null && dRow["SYS_FLAG"].ToString() != "")
            {
                model1.SYS_FLAG = decimal.Parse(dRow["SYS_FLAG"].ToString());
            }

            if (dRow["STATUS"] != null && dRow["STATUS"].ToString() != "")
            {
                model1.STATUS = decimal.Parse(dRow["STATUS"].ToString());
            }

            if (dRow["ROLEID"] != null && dRow["ROLEID"].ToString() != "")
            {
                model1.ROLEID = decimal.Parse(dRow["ROLEID"].ToString());
            }

            //if (dRow["IP"] != null && dRow["IP"].ToString() != "")
            //{
            //    model1.IP = dRow["IP"].ToString();
            //}

            if (dRow["AUTOGRAPH"] != null && dRow["AUTOGRAPH"].ToString() != "")
            {
                model1.AUTOGRAPH = (byte[])dRow["AUTOGRAPH"];
            }

            if (dRow["COMMUNITY_CODE"] != null && dRow["COMMUNITY_CODE"].ToString() != "")
            {
                model1.COMMUNITY_CODE = dRow["COMMUNITY_CODE"].ToString();
            }

            if (dRow["SEX"] != null && dRow["SEX"].ToString() != "")
            {
                model1.SEX = dRow["SEX"].ToString();
            }

            if (dRow["ID_NO"] != null && dRow["ID_NO"].ToString() != "")
            {
                model1.ID_NO = dRow["ID_NO"].ToString();
            }

            if (dRow["dept_code"] != null && dRow["dept_code"].ToString() != "")
            {
                model1.DEPT_CODE = dRow["dept_code"].ToString();
            }

            if (dRow["DEPT_CODE"] != null && dRow["DEPT_CODE"].ToString() != "")
            {
                model1.DEPT_CODE = dRow["DEPT_CODE"].ToString();
            }

            if (dRow["NATION"] != null && dRow["NATION"].ToString() != "")
            {
                model1.NATION = dRow["NATION"].ToString();
            }

            if (dRow["MAILING_ADDRESS"] != null && dRow["MAILING_ADDRESS"].ToString() != "")
            {
                model1.MAILING_ADDRESS = dRow["MAILING_ADDRESS"].ToString();
            }

            if (dRow["WORK_ID"] != null && dRow["WORK_ID"].ToString() != "")
            {
                model1.WORK_ID = dRow["WORK_ID"].ToString();
            }

            if (dRow["WORK_STATE"] != null && dRow["WORK_STATE"].ToString() != "")
            {
                model1.WORK_STATE = dRow["WORK_STATE"].ToString();
            }

            if (dRow["FORMATION"] != null && dRow["FORMATION"].ToString() != "")
            {
                model1.FORMATION = dRow["FORMATION"].ToString();
            }

            if (dRow["NAME"] != null && dRow["NAME"].ToString() != "")
            {
                model1.NAME = dRow["NAME"].ToString();
            }

            if (dRow["INPUT_CODE"] != null && dRow["INPUT_CODE"].ToString() != "")
            {
                model1.INPUT_CODE = dRow["INPUT_CODE"].ToString();
            }

            if (dRow["JOB"] != null && dRow["JOB"].ToString() != "")
            {
                model1.JOB = dRow["JOB"].ToString();
            }

            if (dRow["TITLE"] != null && dRow["TITLE"].ToString() != "")
            {
                model1.TITLE = dRow["TITLE"].ToString();
            }

            if (dRow["USER_NAME"] != null && dRow["USER_NAME"].ToString() != "")
            {
                model1.USER_NAME = dRow["USER_NAME"].ToString();
            }

            if (dRow["INPUT_CODE_WB"] != null && dRow["INPUT_CODE_WB"].ToString() != "")
            {
                model1.INPUT_CODE_WB = dRow["INPUT_CODE_WB"].ToString();
            }

            if (dRow["ID"] != null && dRow["ID"].ToString() != "")
            {
                model1.ID = dRow["ID"].ToString();
            }

            return model1;
        }

        /// <summary>
        /// 复制信息到人员信息类(护理管理系统)
        /// </summary>
        public static STAFF_DICT CopyToModel_NURADM(DataRow dRow)
        {
            STAFF_DICT model1 = new STAFF_DICT();

            if (dRow.Table.Columns.Contains("NURSE_ID") && CheckData(dRow["NURSE_ID"]))
            {
                model1.EMP_NO = dRow["NURSE_ID"].ToString();
            }

            if (dRow.Table.Columns.Contains("PASSWORD") && (CheckData(dRow["PASSWORD"])))
            {
                model1.PASSWORD = dRow["PASSWORD"].ToString();
            }

            if (dRow.Table.Columns.Contains("NURSE_PICTURES") && (CheckData(dRow["NURSE_PICTURES"])))
            {
                model1.AUTOGRAPH = (byte[])dRow["NURSE_PICTURES"];
            }

            if (dRow.Table.Columns.Contains("SEX") && (CheckData(dRow["SEX"])))
            {
                model1.SEX = dRow["SEX"].ToString();
            }

            if (dRow.Table.Columns.Contains("ID_NO") && (CheckData(dRow["ID_NO"])))
            {
                model1.ID_NO = dRow["ID_NO"].ToString();
            }

            if (dRow.Table.Columns.Contains("WARD_CODE") && (CheckData(dRow["WARD_CODE"])))
            {
                model1.DEPT_CODE = dRow["WARD_CODE"].ToString();
            }

            if (dRow.Table.Columns.Contains("NATION") && (CheckData(dRow["NATION"])))
            {
                model1.NATION = dRow["NATION"].ToString();
            }

            if (dRow.Table.Columns.Contains("NATIVE_PLACE") && (CheckData(dRow["NATIVE_PLACE"])))
            {
                model1.MAILING_ADDRESS = dRow["NATIVE_PLACE"].ToString();
            }

            if (dRow.Table.Columns.Contains("REGISTER_NO") && (CheckData(dRow["REGISTER_NO"])))
            {
                model1.WORK_ID = dRow["REGISTER_NO"].ToString();
            }

            if (dRow.Table.Columns.Contains("WORKING_STATUS") && (CheckData(dRow["WORKING_STATUS"])))
            {
                model1.WORK_STATE = dRow["WORKING_STATUS"].ToString();
            }

            if (dRow.Table.Columns.Contains("NURSE_TYPE") && (CheckData(dRow["NURSE_TYPE"])))
            {
                model1.FORMATION = dRow["NURSE_TYPE"].ToString();
            }

            if (dRow.Table.Columns.Contains("NAME") && (CheckData(dRow["NAME"])))
            {
                model1.NAME = dRow["NAME"].ToString();
            }

            if (dRow.Table.Columns.Contains("JOB") && (CheckData(dRow["JOB"])))
            {
                model1.JOB = dRow["JOB"].ToString();
            }

            if (dRow.Table.Columns.Contains("TITLE") && (CheckData(dRow["TITLE"])))
            {
                model1.TITLE = dRow["TITLE"].ToString();
            }

            if (dRow.Table.Columns.Contains("NURSE_ID") && (CheckData(dRow["NURSE_ID"])))
            {
                model1.USER_NAME = dRow["NURSE_ID"].ToString();
            }

            if (dRow.Table.Columns.Contains("NURSE_ID") && (CheckData(dRow["NURSE_ID"])))
            {
                model1.ID = dRow["NURSE_ID"].ToString();
            }

            return model1;
        }
        /// <summary>
        /// 验证数据有效
        /// </summary>
        /// <param name="obj">数据对象</param>
        /// <returns></returns>
        private static bool CheckData(Object obj)
        {
            if (obj != null && !string.IsNullOrEmpty(obj.ToString()))
            {
                return true;
            }
            return false;
        }

        private DataTable _userGroup; //liulei 护理用20220308
        /// <summary>
        /// 所属用户组
        /// </summary>
        [DataMember]
        public DataTable USER_GROUP
        {
            get { return _userGroup; }
            set
            {
                if (_userGroup != value)
                {
                    _userGroup = value;
                    this.RaisePropertyChanged("USER_GROUP");
                }
            }
        }

        private string _userSrc;
        /// <summary>
        /// 用户来源,0普通用户（默认）,1管理员用户ADMIN
        /// </summary>          
        [DataMember]
        public string USER_SRC
        {
            get { return _userSrc; }
            set
            {
                if (_userSrc != value)
                {
                    _userSrc = value;
                    this.RaisePropertyChanged("USER_SRC");
                }
            }
        }

        private string _ward_code;
        /// <summary>
        /// 所在病区编码
        /// </summary>         
        [DataMember]
        public string ward_code
        {
            get { return _ward_code; }
            set
            {
                if (_ward_code != value)
                {
                    _ward_code = value;
                    this.RaisePropertyChanged("dept_code");
                }
            }
        }
    }
}