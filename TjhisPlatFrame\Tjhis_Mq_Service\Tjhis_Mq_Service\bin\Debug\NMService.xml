<?xml version="1.0"?>
<doc>
    <assembly>
        <name>NMService</name>
    </assembly>
    <members>
        <member name="T:NM_Service.CollectionHelper">
            <summary>
            集合帮助类
            </summary>
        </member>
        <member name="M:NM_Service.CollectionHelper.#ctor">
            <summary>
            本对象不能实例化
            </summary>
        </member>
        <member name="M:NM_Service.CollectionHelper.DictionaryAddItem(System.Collections.Generic.Dictionary{System.String,System.String}@,System.String,System.String)">
            <summary>
            向SQL集合中添加Sql
            </summary>
            <param name="idc"></param>
            <param name="sql"></param>
            <param name="errMsg"></param>
        </member>
        <member name="T:NM_Service.NMService.BILL_PATTERN_DETAILClient">
            <summary>
            费用模板明细记录实现类
            </summary> 	
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_DETAILClient.Exists_BILL_PATTERN_DETAIL(System.String,System.Decimal,System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_DETAILClient.Add_BILL_PATTERN_DETAIL(Model.BILL_PATTERN_DETAIL)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_DETAILClient.Update_BILL_PATTERN_DETAIL(Model.BILL_PATTERN_DETAIL)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_DETAILClient.Delete_BILL_PATTERN_DETAIL(System.String,System.Decimal,System.String)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_DETAILClient.GetModel_BILL_PATTERN_DETAIL(System.String,System.Decimal,System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_DETAILClient.GetList_All_BILL_PATTERN_DETAIL(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_DETAILClient.GetList_BILL_PATTERN_DETAIL(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>   
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_DETAILClient.GetObservableCollection_All_BILL_PATTERN_DETAIL(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_DETAILClient.GetObservableCollection_BILL_PATTERN_DETAIL(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_DETAILClient.Dispose">
            <summary>
            实现IDisposable接口
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_DETAILClient.Dispose(System.Boolean)">
            <summary>
            虚方法，可供子类重写
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_DETAILClient.Finalize">
            <summary>
            析构函数
            当客户端没有显示调用Dispose()时由GC完成资源回收功能
            </summary>
        </member>
        <member name="T:NM_Service.NMService.BILL_PATTERN_MASTERClient">
            <summary>
            费用模板主记录实现类
            </summary> 	
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_MASTERClient.Exists_BILL_PATTERN_MASTER(System.Decimal,System.String,System.String,System.String,System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_MASTERClient.Add_BILL_PATTERN_MASTER(Model.BILL_PATTERN_MASTER)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_MASTERClient.Update_BILL_PATTERN_MASTER(Model.BILL_PATTERN_MASTER)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_MASTERClient.Delete_BILL_PATTERN_MASTER(System.Decimal,System.String,System.String,System.String,System.String)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_MASTERClient.GetModel_BILL_PATTERN_MASTER(System.Decimal,System.String,System.String,System.String,System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_MASTERClient.GetList_All_BILL_PATTERN_MASTER(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_MASTERClient.GetList_BILL_PATTERN_MASTER(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>   
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_MASTERClient.GetObservableCollection_All_BILL_PATTERN_MASTER(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_MASTERClient.GetObservableCollection_BILL_PATTERN_MASTER(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_MASTERClient.Dispose">
            <summary>
            实现IDisposable接口
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_MASTERClient.Dispose(System.Boolean)">
            <summary>
            虚方法，可供子类重写
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:NM_Service.NMService.BILL_PATTERN_MASTERClient.Finalize">
            <summary>
            析构函数
            当客户端没有显示调用Dispose()时由GC完成资源回收功能
            </summary>
        </member>
        <member name="T:NM_Service.NMService.BJCA_INTERFACEClient">
            <summary>
            数字签名实现类
            </summary> 	
        </member>
        <member name="M:NM_Service.NMService.BJCA_INTERFACEClient.Exists_BJCA_INTERFACE(System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BJCA_INTERFACEClient.Add_BJCA_INTERFACE(Model.BJCA_INTERFACE)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BJCA_INTERFACEClient.Update_BJCA_INTERFACE(Model.BJCA_INTERFACE)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BJCA_INTERFACEClient.Delete_BJCA_INTERFACE(System.String,System.String)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BJCA_INTERFACEClient.GetModel_BJCA_INTERFACE(System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BJCA_INTERFACEClient.GetList_All_BJCA_INTERFACE(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BJCA_INTERFACEClient.GetList_BJCA_INTERFACE(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>   
        </member>
        <member name="M:NM_Service.NMService.BJCA_INTERFACEClient.GetObservableCollection_All_BJCA_INTERFACE(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.BJCA_INTERFACEClient.GetObservableCollection_BJCA_INTERFACE(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.BJCA_INTERFACEClient.Dispose">
            <summary>
            实现IDisposable接口
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BJCA_INTERFACEClient.Dispose(System.Boolean)">
            <summary>
            虚方法，可供子类重写
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:NM_Service.NMService.BJCA_INTERFACEClient.Finalize">
            <summary>
            析构函数
            当客户端没有显示调用Dispose()时由GC完成资源回收功能
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MAIN_MENUClient.Exists_MAIN_MENU(System.String,System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MAIN_MENUClient.Add_MAIN_MENU(Model.MAIN_MENU)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MAIN_MENUClient.Update_MAIN_MENU(Model.MAIN_MENU)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MAIN_MENUClient.Delete_MAIN_MENU(System.String,System.String)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MAIN_MENUClient.GetModel_MAIN_MENU(System.String,System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MAIN_MENUClient.GetList_All_MAIN_MENU(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MAIN_MENUClient.GetList_MAIN_MENU(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>   
        </member>
        <member name="M:NM_Service.NMService.MAIN_MENUClient.GetObservableCollection_All_MAIN_MENU(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.MAIN_MENUClient.GetObservableCollection_MAIN_MENU(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.MAIN_MENUClient.Dispose">
            <summary>
            实现IDisposable接口
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MAIN_MENUClient.Dispose(System.Boolean)">
            <summary>
            虚方法，可供子类重写
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:NM_Service.NMService.MAIN_MENUClient.Finalize">
            <summary>
            析构函数
            当客户端没有显示调用Dispose()时由GC完成资源回收功能
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MODEL_GROUPClient.Exists_MODEL_GROUP(System.String,System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MODEL_GROUPClient.Add_MODEL_GROUP(Model.MODEL_GROUP)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MODEL_GROUPClient.Update_MODEL_GROUP(Model.MODEL_GROUP)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MODEL_GROUPClient.Delete_MODEL_GROUP(System.String,System.String)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MODEL_GROUPClient.GetModel_MODEL_GROUP(System.String,System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MODEL_GROUPClient.GetList_All_MODEL_GROUP(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MODEL_GROUPClient.GetList_MODEL_GROUP(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>   
        </member>
        <member name="M:NM_Service.NMService.MODEL_GROUPClient.GetObservableCollection_All_MODEL_GROUP(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.MODEL_GROUPClient.GetObservableCollection_MODEL_GROUP(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.MODEL_GROUPClient.Dispose">
            <summary>
            实现IDisposable接口
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MODEL_GROUPClient.Dispose(System.Boolean)">
            <summary>
            虚方法，可供子类重写
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:NM_Service.NMService.MODEL_GROUPClient.Finalize">
            <summary>
            析构函数
            当客户端没有显示调用Dispose()时由GC完成资源回收功能
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MODELSClient.Exists_MODELS(System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MODELSClient.Add_MODELS(Model.MODELS)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MODELSClient.Update_MODELS(Model.MODELS)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MODELSClient.Delete_MODELS(System.String)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MODELSClient.GetModel_MODELS(System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MODELSClient.GetList_All_MODELS(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MODELSClient.GetList_MODELS(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>   
        </member>
        <member name="M:NM_Service.NMService.MODELSClient.GetObservableCollection_All_MODELS(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.MODELSClient.GetObservableCollection_MODELS(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.MODELSClient.Dispose">
            <summary>
            实现IDisposable接口
            </summary>
        </member>
        <member name="M:NM_Service.NMService.MODELSClient.Dispose(System.Boolean)">
            <summary>
            虚方法，可供子类重写
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:NM_Service.NMService.MODELSClient.Finalize">
            <summary>
            析构函数
            当客户端没有显示调用Dispose()时由GC完成资源回收功能
            </summary>
        </member>
        <member name="T:NM_Service.NMService.SEC_MENUS_DICTClient">
            <summary>
            应用程序菜单字典实现类
            </summary> 	
        </member>
        <member name="M:NM_Service.NMService.SEC_MENUS_DICTClient.Exists_SEC_MENUS_DICT(System.String,System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_MENUS_DICTClient.Add_SEC_MENUS_DICT(Model.SEC_MENUS_DICT)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_MENUS_DICTClient.Update_SEC_MENUS_DICT(Model.SEC_MENUS_DICT)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_MENUS_DICTClient.Delete_SEC_MENUS_DICT(System.String,System.String)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_MENUS_DICTClient.GetModel_SEC_MENUS_DICT(System.String,System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_MENUS_DICTClient.GetList_All_SEC_MENUS_DICT(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_MENUS_DICTClient.GetList_SEC_MENUS_DICT(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>   
        </member>
        <member name="M:NM_Service.NMService.SEC_MENUS_DICTClient.GetObservableCollection_All_SEC_MENUS_DICT(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.SEC_MENUS_DICTClient.GetObservableCollection_SEC_MENUS_DICT(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.SEC_MENUS_DICTClient.Dispose">
            <summary>
            实现IDisposable接口
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_MENUS_DICTClient.Dispose(System.Boolean)">
            <summary>
            虚方法，可供子类重写
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:NM_Service.NMService.SEC_MENUS_DICTClient.Finalize">
            <summary>
            析构函数
            当客户端没有显示调用Dispose()时由GC完成资源回收功能
            </summary>
        </member>
        <member name="T:NM_Service.NMService.SEC_RIGHT_GROUPClient">
            <summary>
            应用程序菜单字典实现类
            </summary> 	
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUPClient.Exists_SEC_RIGHT_GROUP(System.String,System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUPClient.Add_SEC_RIGHT_GROUP(Model.SEC_RIGHT_GROUP)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUPClient.Update_SEC_RIGHT_GROUP(Model.SEC_RIGHT_GROUP)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUPClient.Delete_SEC_RIGHT_GROUP(System.String,System.String)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUPClient.GetModel_SEC_RIGHT_GROUP(System.String,System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUPClient.GetList_All_SEC_RIGHT_GROUP(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUPClient.GetList_SEC_RIGHT_GROUP(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>   
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUPClient.GetObservableCollection_All_SEC_RIGHT_GROUP(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUPClient.GetObservableCollection_SEC_RIGHT_GROUP(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUPClient.Dispose">
            <summary>
            实现IDisposable接口
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUPClient.Dispose(System.Boolean)">
            <summary>
            虚方法，可供子类重写
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUPClient.Finalize">
            <summary>
            析构函数
            当客户端没有显示调用Dispose()时由GC完成资源回收功能
            </summary>
        </member>
        <member name="T:NM_Service.NMService.SEC_RIGHT_GROUP_VS_MENUSClient">
            <summary>
            应用程序菜单字典实现类
            </summary> 	
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_MENUSClient.Exists_SEC_RIGHT_GROUP_VS_MENUS(System.String,System.String,System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_MENUSClient.Add_SEC_RIGHT_GROUP_VS_MENUS(Model.SEC_RIGHT_GROUP_VS_MENUS)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_MENUSClient.Update_SEC_RIGHT_GROUP_VS_MENUS(Model.SEC_RIGHT_GROUP_VS_MENUS)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_MENUSClient.Delete_SEC_RIGHT_GROUP_VS_MENUS(System.String,System.String,System.String)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_MENUSClient.GetModel_SEC_RIGHT_GROUP_VS_MENUS(System.String,System.String,System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_MENUSClient.GetList_All_SEC_RIGHT_GROUP_VS_MENUS(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_MENUSClient.GetList_SEC_RIGHT_GROUP_VS_MENUS(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>   
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_MENUSClient.GetObservableCollection_All_SEC_RIGHT_GROUP_VS_MENUS(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_MENUSClient.GetObservableCollection_SEC_RIGHT_GROUP_VS_MENUS(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_MENUSClient.Dispose">
            <summary>
            实现IDisposable接口
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_MENUSClient.Dispose(System.Boolean)">
            <summary>
            虚方法，可供子类重写
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_MENUSClient.Finalize">
            <summary>
            析构函数
            当客户端没有显示调用Dispose()时由GC完成资源回收功能
            </summary>
        </member>
        <member name="T:NM_Service.NMService.SEC_RIGHT_GROUP_VS_USERSClient">
            <summary>
            应用程序菜单字典实现类
            </summary> 	
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_USERSClient.Exists_SEC_RIGHT_GROUP_VS_USERS(System.String,System.String,System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_USERSClient.Add_SEC_RIGHT_GROUP_VS_USERS(Model.SEC_RIGHT_GROUP_VS_USERS)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_USERSClient.Update_SEC_RIGHT_GROUP_VS_USERS(Model.SEC_RIGHT_GROUP_VS_USERS)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_USERSClient.Delete_SEC_RIGHT_GROUP_VS_USERS(System.String,System.String,System.String)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_USERSClient.GetModel_SEC_RIGHT_GROUP_VS_USERS(System.String,System.String,System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_USERSClient.GetList_All_SEC_RIGHT_GROUP_VS_USERS(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_USERSClient.GetList_SEC_RIGHT_GROUP_VS_USERS(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>   
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_USERSClient.GetObservableCollection_All_SEC_RIGHT_GROUP_VS_USERS(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_USERSClient.GetObservableCollection_SEC_RIGHT_GROUP_VS_USERS(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_USERSClient.Dispose">
            <summary>
            实现IDisposable接口
            </summary>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_USERSClient.Dispose(System.Boolean)">
            <summary>
            虚方法，可供子类重写
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:NM_Service.NMService.SEC_RIGHT_GROUP_VS_USERSClient.Finalize">
            <summary>
            析构函数
            当客户端没有显示调用Dispose()时由GC完成资源回收功能
            </summary>
        </member>
        <member name="T:NM_Service.NMService.LnCA_InterfaceClient">
            <summary>
            辽宁CA接口实现类
            </summary> 	
        </member>
        <member name="M:NM_Service.NMService.LnCA_InterfaceClient.Exists_LNCA_INTERFACE(System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:NM_Service.NMService.LnCA_InterfaceClient.Add_LNCA_INTERFACE(Model.LNCA_INTERFACE)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.LnCA_InterfaceClient.Update_LNCA_INTERFACE(Model.LNCA_INTERFACE)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.LnCA_InterfaceClient.UpdateByHisID_LNCA_INTERFACE(Model.LNCA_INTERFACE)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.LnCA_InterfaceClient.Delete_LNCA_INTERFACE(System.String)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.LnCA_InterfaceClient.GetModel_LNCA_INTERFACE(System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:NM_Service.NMService.LnCA_InterfaceClient.GetList_All_LNCA_INTERFACE(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:NM_Service.NMService.LnCA_InterfaceClient.GetList_LNCA_INTERFACE(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>   
        </member>
        <member name="M:NM_Service.NMService.LnCA_InterfaceClient.GetObservableCollection_All_LNCA_INTERFACE(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.LnCA_InterfaceClient.GetObservableCollection_LNCA_INTERFACE(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.LnCA_InterfaceClient.Dispose">
            <summary>
            实现IDisposable接口
            </summary>
        </member>
        <member name="M:NM_Service.NMService.LnCA_InterfaceClient.Dispose(System.Boolean)">
            <summary>
            虚方法，可供子类重写
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:NM_Service.NMService.LnCA_InterfaceClient.Finalize">
            <summary>
            析构函数
            当客户端没有显示调用Dispose()时由GC完成资源回收功能
            </summary>
        </member>
        <member name="T:NM_Service.NMService.BED_RECClient">
            <summary>
            床位记录实现类
            </summary> 	
        </member>
        <member name="M:NM_Service.NMService.BED_RECClient.Exists_BED_REC(System.String,System.Decimal)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BED_RECClient.Add_BED_REC(Model.BED_REC)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BED_RECClient.Update_BED_REC(Model.BED_REC)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BED_RECClient.Delete_BED_REC(System.String,System.Decimal)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BED_RECClient.GetModel_BED_REC(System.String,System.Decimal)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BED_RECClient.GetList_All_BED_REC(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BED_RECClient.GetList_BED_REC(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>   
        </member>
        <member name="M:NM_Service.NMService.BED_RECClient.GetObservableCollection_All_BED_REC(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.BED_RECClient.GetObservableCollection_BED_REC(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.BED_RECClient.Dispose">
            <summary>
            实现IDisposable接口
            </summary>
        </member>
        <member name="M:NM_Service.NMService.BED_RECClient.Dispose(System.Boolean)">
            <summary>
            虚方法，可供子类重写
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:NM_Service.NMService.BED_RECClient.Finalize">
            <summary>
            析构函数
            当客户端没有显示调用Dispose()时由GC完成资源回收功能
            </summary>
        </member>
        <member name="M:NM_Service.NMService.PatientInDeptClient.Dispose">
            <summary>
            实现IDisposable接口
            </summary>
        </member>
        <member name="M:NM_Service.NMService.PatientInDeptClient.Dispose(System.Boolean)">
            <summary>
            虚方法，可供子类重写
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:NM_Service.NMService.PatientInDeptClient.Finalize">
            <summary>
            析构函数
            当客户端没有显示调用Dispose()时由GC完成资源回收功能
            </summary>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.GetDataBySql(System.String,System.String,System.Boolean)">
            <summary>
            查询sql语句  可以传入指定返回的 TableName
            </summary>
            <param name="sql"></param>
            <param name="strTableName"></param>
            <param name="blnWithKey"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.ExecuteSQL(System.String)">
            <summary>
            执行SQL ，成功提交，失败回滚
            </summary>
            <param name=" SQL">SQL语句List</param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.ExecuteScalarStr(System.String)">
            <summary>
            执行只有一个结果的SQL语句
            </summary>
            <param name="strSQL">SQL语句</param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.ExecuteNoQuery(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            执行Sql语句, 成功没有返回值 失败返回的是定义好传入的报错信息
            </summary>
            <param name="dicSQL">sql语句Dictionary 第一项为SQL语句, 第二项为出错提示</param>
            <returns>执行失败时的错误提示</returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.ExecuteSqlArray(System.Collections.ArrayList)">
            <summary>
            批量SQL执行 返回值大于0 为成功
            </summary>
            <param name="list"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.GetDataBySql(System.String)">
            <summary>
            传入sql语句 查询数据 返回的是 DataSet
            </summary>
            <param name="sql"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.GetList(System.String)">
            <summary>
            传入sql语句 查询数据 返回的是 DataSet
            </summary>
            <param name="strSql"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.GetProcedureDataSet(System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.String,System.String)">
            <summary>
            存储过程调用方法
            </summary>
            <param name="procedureName"></param>
            <param name="paramKey"></param>
            <param name="paramValue"></param>
            <param name="tableName"></param>
            <param name="sql"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.GetPubicProcedureDs(System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.String@,System.String@,System.Data.DataTable@)">
            <summary>
            门诊收费对应的存储过程调用发法 也可以用作它用但是格式要跟收费的存储过程出参格式一致
            </summary>
            <param name="procedureName"></param>
            <param name="paramKey"></param>
            <param name="paramValue"></param>
            <param name="para1"></param>
            <param name="para2"></param>
            <param name="para3"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.GetSingleValue(System.String)">
            <summary>
            执行只有一个结果的sql语句 ExecuteScalarStr 方法里面实际调用的也是它
            </summary>
            <param name="strSql"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.GetSysDate">
            <summary>
            获取日期格式的系统时间
            </summary>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.GetTableData(System.String,System.String)">
            <summary>
            传入表名和 where 条件获取查询数据 返回dataset 
            </summary>
            <param name="tableName"></param>
            <param name="strWhere"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.SaveTablesData(System.Collections.ArrayList)">
            <summary>
             ArrayList 集合形式的DataSet 数据进行统一保存 但是需要给对应的 TableName属性指定实际的数据库表名 不能用默认的Table[0]
            </summary>
            <param name="dsList"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.SaveDsArray(System.Collections.ArrayList,System.Collections.Generic.List{System.Data.DataRow})">
            <summary>
            保存数据集合，如果取更新语句空，返回-9   需要注意的是此方法每一个datatable的TableName属性需要指定保存的对应的表名  
            </summary>
            <param name="dsList"></param>
            <param name="drsChange"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.SaveDataSet(System.Data.DataSet)">
            <summary>
            对dataset 数据进行保存 可以多个datatable 一起add到一个dataset进行保存 
            </summary>
            <param name="dataSet"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.SaveDataTable(System.Data.DataTable,System.Boolean)">
            <summary>
             直接保存datatable 的方法 返回值 大于 0 成功
            </summary>
            <param name="dataTable"></param>
            <param name="autoUpateSgl"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.SaveDataSetNur(System.Data.DataSet)">
            <summary>
            护理专用保存更新DATASET 返回值 大于 0 成功
            </summary>
            <param name="dataSet"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.SaveTable(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            直接执行增删改语句 判断返回值不为空就是报错信息 空为成功
            </summary>
            <param name="list"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.SaveExcSql(System.Collections.Generic.Dictionary{System.String,System.String},Utility.OracleODP.OracleBaseClass)">
            <summary>
            直接带事物方式执行增删改语句  判断返回值不为空就是报错信息 空为成功
            </summary>
            <param name="list"></param>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.SaveDataSetAndDictionary(System.Data.DataSet,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            对dataset 数据 和 语句同时 执行保存  返回结果 string类型 不为空返回报错原因
            </summary>
            <param name="dataset"></param>
            <param name="list"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.GetListUseDB(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            带db 外部事物调用去查询语句 比如跟其他insert执行后未提交 配合使用在一个事物下取insert后的数值等
            </summary>
            <param name="sql"></param>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.GetSysDateTime">
            <summary>
            获取字符串格式的系统时间
            </summary>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.GetUpdateSql(System.Data.DataTable,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            取dtUpdate里对应tableName表的字段sql  多表关联sql数据进行保存 需要重新整理成单表语句 然后才能进行数据保存 
            </summary>
            <param name="dtUpdate">要更新的DatatTable</param>
            <param name="tableName">要更新的表名</param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.SelectDataTable(System.String,System.String,System.Boolean)">
            <summary>
            根据sql返回DataTable 一个是sql语句 一个指定TableName 名称，一个是限制字段必须大小写一致 返回datatable
            </summary>
            <param name="sql">查询语句</param>
            <param name="strTableName">表名</param>
            <param name="caseSensitive">大小写</param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.Dispose">
            <summary>
            实现IDisposable接口
            </summary>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.Dispose(System.Boolean)">
            <summary>
            虚方法，可供子类重写
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.GetDataBySql_Tran(System.String)">
            <summary>
            开启事物查询 与 GetDataBySql 的区别 主要是 查询语句中涉及带dblink的查询准备的
            </summary>
            <param name="sql"></param>
            <returns></returns>
        </member>
        <member name="M:NM_Service.NMService.ServerPublicClient.Finalize">
            <summary>
            析构函数
            当客户端没有显示调用Dispose()时由GC完成资源回收功能
            </summary>
        </member>
        <member name="T:NM_Service.NMService.STAFF_DICTClient">
            <summary>
            工作人员字典实现类
            </summary> 	
        </member>
        <member name="M:NM_Service.NMService.STAFF_DICTClient.Exists_STAFF_DICT(System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:NM_Service.NMService.STAFF_DICTClient.Add_STAFF_DICT(Model.STAFF_DICT)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.STAFF_DICTClient.Update_STAFF_DICT(Model.STAFF_DICT)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.STAFF_DICTClient.Delete_STAFF_DICT(System.String)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:NM_Service.NMService.STAFF_DICTClient.GetModel_STAFF_DICT(System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:NM_Service.NMService.STAFF_DICTClient.GetList_All_STAFF_DICT(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:NM_Service.NMService.STAFF_DICTClient.GetList_STAFF_DICT(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>   
        </member>
        <member name="M:NM_Service.NMService.STAFF_DICTClient.GetObservableCollection_All_STAFF_DICT(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.STAFF_DICTClient.GetObservableCollection_STAFF_DICT(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>  
        </member>
        <member name="M:NM_Service.NMService.STAFF_DICTClient.Dispose">
            <summary>
            实现IDisposable接口
            </summary>
        </member>
        <member name="M:NM_Service.NMService.STAFF_DICTClient.Dispose(System.Boolean)">
            <summary>
            虚方法，可供子类重写
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:NM_Service.NMService.STAFF_DICTClient.Finalize">
            <summary>
            析构函数
            当客户端没有显示调用Dispose()时由GC完成资源回收功能
            </summary>
        </member>
    </members>
</doc>
