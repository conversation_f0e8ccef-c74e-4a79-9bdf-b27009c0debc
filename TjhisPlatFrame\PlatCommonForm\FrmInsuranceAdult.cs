﻿/*-----------------------------------------------------------------------
 * 类名称    ：FrmInsuranceVsItemInput
 * 类描述    ：
 * 创建人    ：梁吉lions
 * 创建时间  ：2017/1/11 9:24:06
 * 修改人    ：
 * 修改时间  ：
 * 修改备注  ：
 * 版本      ：
 * ----------------------------------------------------------------------
 */
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using PlatCommon.SysBase;

namespace PlatCommonForm
{
    public partial class FrmInsuranceAdult : ParentForm
    {
        public FrmInsuranceAdult()
        {
            InitializeComponent();
        }
        public FrmInsuranceAdult(string _his_code,string _his_name,string _his_class,string _insur_code,string _insur_name,string _insur_level,string _insur_scale,string _insur_xzbs,string _insur_xzsm)
        {
            InitializeComponent();
            his_code = _his_code;
            his_name = _his_name;
            his_class = _his_class;
            insur_code = _insur_code;
            insur_name = _insur_name;
            insur_level = _insur_level;
            insur_scale = _insur_scale;
            insur_xzbs = _insur_xzbs;
            insur_xzsm = _insur_xzsm;
        }

        string his_code = "";
        string his_name = "";
        string his_class = "";
        string insur_code = "";
        string insur_name = "";
        string insur_level = "";
        string insur_scale = "";
        string insur_xzbs = "";
        string insur_xzsm = "";
        public string insur_adult = "";

        #region 自定义方法 

        
        #endregion
        private void FrmInsuranceVsItemInput_Load(object sender, EventArgs e)
        {
            try
            {
                mmo_ptxzfw.EditValue = insur_xzsm;
                lblc_histype.Text = his_class;
                labelControl1.Text = his_code;
                labelControl3.Text = his_name;
                lblc_insurcode.Text = insur_code;
                labelControl4.Text = insur_name;
                lblc_INSUR_LEVEL.Text = insur_level;
                lblc_INSUR_SCALE.Text = insur_scale;
            }
            catch(Exception ex){
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "错误信息");
                return;
            }
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            this.insur_adult = cbox_xzdj.EditValue.ToString();
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }
        //限制等级变化，显示对应等级的范围，不限制，不显示
    }
}
