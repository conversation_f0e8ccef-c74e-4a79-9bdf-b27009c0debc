﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Utility.OracleODP {
    
    
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "15.9.0.0")]
    public sealed partial class OracleBase_Settings : global::System.Configuration.ApplicationSettingsBase {
        
        private static OracleBase_Settings defaultInstance = ((OracleBase_Settings)(global::System.Configuration.ApplicationSettingsBase.Synchronized(new OracleBase_Settings())));
        
        public static OracleBase_Settings Default {
            get {
                return defaultInstance;
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("Data Source=(DESCRIPTION =(ADDRESS_LIST =(ADDRESS = (PROTOCOL = TCP)(HOST = 168.1" +
            "68.1.186)(PORT = 1521)))(CONNECT_DATA =(SERVICE_NAME = develop)));\r\nUser ID=syst" +
            "em;Password=manager;")]
        public string DBConnectString {
            get {
                return ((string)(this["DBConnectString"]));
            }
        }
    }
}
