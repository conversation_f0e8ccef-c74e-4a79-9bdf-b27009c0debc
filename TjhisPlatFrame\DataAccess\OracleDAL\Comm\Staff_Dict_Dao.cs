﻿using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using Utility;
using Utility.OracleODP;
using Oracle.ManagedDataAccess.Client;

namespace OracleDAL
{
    /// <summary>
    /// 工作人员字典操作类
    /// </summary>
    public class STAFF_DICT_Dao:STAFF_DICT_Dao_Base, IDisposable
    {
        #region Method
        /// <summary>
        /// 登录用户验证
        /// </summary>
        /// <param name="USER_NAME">用户名</param>
        /// <param name="PASSWORD">密码</param>
        /// <returns>返回值：-1-用户名错误，0-密码错误 1-正常登录，2-不可用账号</returns>
        public int Exists(string USER_NAME, string PASSWORD)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select USER_NAME,PASSWORD,STATUS from COMM.STAFF_DICT where USER_NAME='" + USER_NAME + "'");//" and PASSWORD='"+ PASSWORD+"'");
            try
            {
                using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
                {
                    if (db.OpenDB())
                    {
                        db.BeginTransaction();
                        DataSet ds = db.SelectDataSet(strSql.ToString());
                        int ret = -1;
                        if (ds != null && ds.Tables[0].Rows.Count > 0)
                        {
                            int status = Convert.ToInt32(ds.Tables[0].Rows[0]["STATUS"]);
                            String pass = ds.Tables[0].Rows[0]["PASSWORD"].ToString();
                            PASSWORD = Gloobal.EncryptHIS(PASSWORD);
                            if (status != 1)
                            {
                                ret = 2;
                            }
                            else if (!pass.Equals(PASSWORD))
                            {
                                ret = 0;
                            }
                            else
                            {
                                ret = 1;
                            }
                        }
                        db.CommitTransaction();
                        db.CloseDB();
                        return ret;
                    }
                    else
                        return -2;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 得到一个用户对象实体
        /// </summary>
        public Model.STAFF_DICT GetModelByUserName(string UserName)
        {
            string sql = string.Format(@"SELECT A.EMP_NO, 
                                   A.CREATE_DATE,
                                   A.PASSWORD, 
                                   A.SYS_FLAG, 
                                   A.STATUS, 
                                   A.ROLEID, 
                                   A.IP, 
                                   A.AUTOGRAPH, 
                                   A.COMMUNITY_CODE, 
                                   A.SEX, 
                                   A.ID_NO, 
                                   A.DEPT_CODE, 
                                   A.NATION, 
                                   A.MAILING_ADDRESS, 
                                   A.WORK_ID, 
                                   A.WORK_STATE, 
                                   A.FORMATION, 
                                   A.NAME, 
                                   A.INPUT_CODE, 
                                   A.JOB, 
                                   A.TITLE, 
                                   A.USER_NAME, 
                                   A.INPUT_CODE_WB, 
                                   A.ID,
                                   A.HIS_UNIT_CODE,
                                   A.CA_ENABLED,
                                   B.DEPT_NAME
                             FROM COMM.STAFF_DICT A
                             LEFT JOIN COMM.DEPT_DICT B ON A.DEPT_CODE = B.DEPT_CODE
                             WHERE  USER_NAME='{0}'",UserName);
            //StringBuilder strSql = new StringBuilder();
            //strSql.Append("select EMP_NO, CREATE_DATE, PASSWORD, SYS_FLAG, STATUS, ROLEID, IP, AUTOGRAPH, COMMUNITY_CODE, SEX, ID_NO, DEPT_CODE, NATION, MAILING_ADDRESS, WORK_ID, WORK_STATE, FORMATION, NAME, INPUT_CODE, JOB, TITLE, USER_NAME, INPUT_CODE_WB, ID,HIS_UNIT_CODE,CA_ENABLED  ");
            //strSql.Append("  from STAFF_DICT ");
           
            ////strSql.Append(" where USER_NAME=:USER_NAME ");
            //strSql.Append(" where USER_NAME='");
            //strSql.Append(UserName);
            //strSql.Append("'");
            try
            {
                using (OracleBaseClass db = new OracleBaseClass())
                {
                    if (db.OpenDB())
                    {
                        db.BeginTransaction();
                        DataSet ds = db.SelectDataSet(sql);
                        db.CommitTransaction();
                        db.CloseDB();
                        if (ds != null && ds.Tables.Count > 0)
                        {
                            int cmdresult = ds.Tables[0].Rows.Count;
                            Model.STAFF_DICT model = new Model.STAFF_DICT();

                            if (cmdresult > 0)
                            {

                                model = CopyToModel(ds.Tables[0].Rows[0]);

                                return model;
                            }
                            else
                            {
                                return null;
                            }
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }

        }

        /// <summary>
        /// 得到一个对象实体,增加医院编号
        /// </summary>
        public Model.STAFF_DICT GetModelByUserName(string UserName, bool isHisUnitCode = true)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select EMP_NO, CREATE_DATE, PASSWORD, SYS_FLAG, STATUS, ROLEID, IP, AUTOGRAPH, COMMUNITY_CODE, SEX, ID_NO, DEPT_CODE, NATION, MAILING_ADDRESS, WORK_ID, WORK_STATE, FORMATION, NAME, INPUT_CODE, JOB, TITLE, USER_NAME, INPUT_CODE_WB, ID");
            if (isHisUnitCode)
                strSql.Append(@", HIS_UNIT_CODE");
            strSql.Append("  from STAFF_DICT ");

            //strSql.Append(" where USER_NAME=:USER_NAME ");
            strSql.Append(" where USER_NAME='");
            strSql.Append(UserName);
            strSql.Append("'");
            //List<OracleParameter> parameters = new List<OracleParameter>();
            //OracleParameter p;

            //p = new OracleParameter(":USER_NAME", OracleDbType.Varchar2, 5);
            //p.Value = UserName;
            //parameters.Add(p);

            try
            {
                using (OracleBaseClass db = new OracleBaseClass())
                {
                    if (db.OpenDB())
                    {
                        db.BeginTransaction();
                        //DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                        DataSet ds = db.SelectDataSet(strSql.ToString());
                        db.CommitTransaction();
                        db.CloseDB();
                        if (ds != null && ds.Tables.Count > 0)
                        {
                            int cmdresult = ds.Tables[0].Rows.Count;
                            Model.STAFF_DICT model = new Model.STAFF_DICT();

                            if (cmdresult > 0)
                            {

                                model = CopyToModel(ds.Tables[0].Rows[0], isHisUnitCode);

                                return model;
                            }
                            else
                            {
                                return null;
                            }
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }

        private bool disposedValue = false; // 要检测冗余调用

        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    // TODO: 释放托管状态(托管对象)。
                }

                // TODO: 释放未托管的资源(未托管的对象)并在以下内容中替代终结器。
                // TODO: 将大型字段设置为 null。

                disposedValue = true;
            }
        }

        /// <summary>
        /// TODO: 仅当以上 Dispose(bool disposing) 拥有用于释放未托管资源的代码时才替代终结器。
        /// </summary>
        ~STAFF_DICT_Dao()
        {
            // 请勿更改此代码。将清理代码放入以上 Dispose(bool disposing) 中。
            Dispose(false);
        }

        /// <summary>
        /// 添加此代码以正确实现可处置模式。
        /// </summary>
        public void Dispose()
        {
            // 请勿更改此代码。将清理代码放入以上 Dispose(bool disposing) 中。
            Dispose(true);
            // TODO: 如果在以上内容中替代了终结器，则取消注释以下行。
            // GC.SuppressFinalize(this);
        }
        #endregion
    }
}

