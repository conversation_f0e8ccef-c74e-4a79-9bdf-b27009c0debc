﻿namespace Tjhis.Controls.StatisticsControls
{
    partial class ucIncomeChart
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.DoughnutSeriesView doughnutSeriesView1 = new DevExpress.XtraCharts.DoughnutSeriesView();
            DevExpress.XtraCharts.RadarDiagram radarDiagram1 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series2 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarAreaSeriesView radarAreaSeriesView1 = new DevExpress.XtraCharts.RadarAreaSeriesView();
            DevExpress.XtraCharts.RadarRangeAreaSeriesView radarRangeAreaSeriesView1 = new DevExpress.XtraCharts.RadarRangeAreaSeriesView();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.tableMain = new System.Windows.Forms.Panel();
            this.panel3 = new System.Windows.Forms.Panel();
            this.ChartPayType = new DevExpress.XtraCharts.ChartControl();
            this.ChartIncomeType = new DevExpress.XtraCharts.ChartControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.panel1 = new System.Windows.Forms.Panel();
            this.ucCompareOther = new Tjhis.Controls.StatisticsControls.ucCompare();
            this.ucCompareRefund = new Tjhis.Controls.StatisticsControls.ucCompare();
            this.ucCompareSum = new Tjhis.Controls.StatisticsControls.ucCompare();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.LRefund = new DevExpress.XtraEditors.LabelControl();
            this.LOther = new DevExpress.XtraEditors.LabelControl();
            this.LSum = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.tableMain.SuspendLayout();
            this.panel3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ChartPayType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(doughnutSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ChartIncomeType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarAreaSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarRangeAreaSeriesView1)).BeginInit();
            this.panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("微软雅黑", 13F, System.Drawing.FontStyle.Bold);
            this.labelControl1.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(78)))), ((int)(((byte)(87)))), ((int)(((byte)(128)))));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Appearance.Options.UseForeColor = true;
            this.labelControl1.Location = new System.Drawing.Point(23, 28);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(72, 25);
            this.labelControl1.TabIndex = 5;
            this.labelControl1.Text = "费用统计";
            // 
            // tableMain
            // 
            this.tableMain.BackColor = System.Drawing.Color.White;
            this.tableMain.Controls.Add(this.panel3);
            this.tableMain.Controls.Add(this.panel1);
            this.tableMain.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.tableMain.Location = new System.Drawing.Point(0, 43);
            this.tableMain.Name = "tableMain";
            this.tableMain.Size = new System.Drawing.Size(798, 567);
            this.tableMain.TabIndex = 6;
            // 
            // panel3
            // 
            this.panel3.Controls.Add(this.ChartPayType);
            this.panel3.Controls.Add(this.ChartIncomeType);
            this.panel3.Controls.Add(this.labelControl3);
            this.panel3.Controls.Add(this.labelControl2);
            this.panel3.Location = new System.Drawing.Point(0, 174);
            this.panel3.Name = "panel3";
            this.panel3.Size = new System.Drawing.Size(795, 390);
            this.panel3.TabIndex = 21;
            // 
            // ChartPayType
            // 
            this.ChartPayType.BorderOptions.Visibility = DevExpress.Utils.DefaultBoolean.False;
            this.ChartPayType.Legend.AlignmentHorizontal = DevExpress.XtraCharts.LegendAlignmentHorizontal.Center;
            this.ChartPayType.Legend.Direction = DevExpress.XtraCharts.LegendDirection.LeftToRight;
            this.ChartPayType.Legend.Name = "Default Legend";
            this.ChartPayType.Location = new System.Drawing.Point(344, 50);
            this.ChartPayType.Margin = new System.Windows.Forms.Padding(10);
            this.ChartPayType.Name = "ChartPayType";
            series1.ArgumentDataMember = "PayType";
            series1.LegendTextPattern = "{A}";
            series1.Name = "缴费方式";
            series1.ToolTipEnabled = DevExpress.Utils.DefaultBoolean.True;
            series1.ToolTipPointPattern = "{A}：{V}元";
            series1.ValueDataMembersSerializable = "PaySum";
            doughnutSeriesView1.TotalLabel.Font = new System.Drawing.Font("微软雅黑", 6F, System.Drawing.FontStyle.Bold);
            doughnutSeriesView1.TotalLabel.TextPattern = "总计：\n{TV}元";
            doughnutSeriesView1.TotalLabel.Visible = true;
            series1.View = doughnutSeriesView1;
            this.ChartPayType.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1};
            this.ChartPayType.Size = new System.Drawing.Size(226, 286);
            this.ChartPayType.TabIndex = 11;
            // 
            // ChartIncomeType
            // 
            this.ChartIncomeType.BorderOptions.Visibility = DevExpress.Utils.DefaultBoolean.False;
            radarDiagram1.AxisX.NumericScaleOptions.AutoGrid = false;
            radarDiagram1.AxisX.WholeRange.AutoSideMargins = false;
            radarDiagram1.AxisX.WholeRange.SideMarginsValue = 0D;
            radarDiagram1.AxisY.Color = System.Drawing.Color.FromArgb(((int)(((byte)(162)))), ((int)(((byte)(175)))), ((int)(((byte)(205)))));
            radarDiagram1.AxisY.NumericScaleOptions.AutoGrid = false;
            radarDiagram1.AxisY.NumericScaleOptions.GridSpacing = 250D;
            radarDiagram1.AxisY.WholeRange.Auto = false;
            radarDiagram1.AxisY.WholeRange.MaxValueSerializable = "1000";
            radarDiagram1.AxisY.WholeRange.MinValueSerializable = "0";
            radarDiagram1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(162)))), ((int)(((byte)(175)))), ((int)(((byte)(205)))));
            radarDiagram1.DrawingStyle = DevExpress.XtraCharts.RadarDiagramDrawingStyle.Polygon;
            this.ChartIncomeType.Diagram = radarDiagram1;
            this.ChartIncomeType.Legend.Name = "Default Legend";
            this.ChartIncomeType.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            this.ChartIncomeType.Location = new System.Drawing.Point(21, 50);
            this.ChartIncomeType.Margin = new System.Windows.Forms.Padding(10);
            this.ChartIncomeType.Name = "ChartIncomeType";
            series2.ArgumentDataMember = "IncomeType";
            series2.Name = "Series 1";
            series2.ToolTipEnabled = DevExpress.Utils.DefaultBoolean.True;
            series2.ToolTipPointPattern = "{A}：{V}元";
            series2.ValueDataMembersSerializable = "IncomeSum";
            radarAreaSeriesView1.MarkerOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(24)))), ((int)(((byte)(144)))), ((int)(((byte)(255)))));
            series2.View = radarAreaSeriesView1;
            this.ChartIncomeType.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series2};
            this.ChartIncomeType.SeriesTemplate.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            this.ChartIncomeType.SeriesTemplate.LegendTextPattern = "{A}：{V}元";
            this.ChartIncomeType.SeriesTemplate.View = radarRangeAreaSeriesView1;
            this.ChartIncomeType.Size = new System.Drawing.Size(273, 257);
            this.ChartIncomeType.TabIndex = 10;
            // 
            // labelControl3
            // 
            this.labelControl3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.labelControl3.Appearance.Font = new System.Drawing.Font("微软雅黑", 13F, System.Drawing.FontStyle.Bold);
            this.labelControl3.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(78)))), ((int)(((byte)(87)))), ((int)(((byte)(128)))));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Appearance.Options.UseForeColor = true;
            this.labelControl3.Location = new System.Drawing.Point(429, 15);
            this.labelControl3.Margin = new System.Windows.Forms.Padding(23, 30, 3, 3);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(72, 25);
            this.labelControl3.TabIndex = 8;
            this.labelControl3.Text = "缴费方式";
            // 
            // labelControl2
            // 
            this.labelControl2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.labelControl2.Appearance.Font = new System.Drawing.Font("微软雅黑", 13F, System.Drawing.FontStyle.Bold);
            this.labelControl2.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(78)))), ((int)(((byte)(87)))), ((int)(((byte)(128)))));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Appearance.Options.UseForeColor = true;
            this.labelControl2.Location = new System.Drawing.Point(21, 15);
            this.labelControl2.Margin = new System.Windows.Forms.Padding(23, 50, 3, 3);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(72, 25);
            this.labelControl2.TabIndex = 7;
            this.labelControl2.Text = "费用构成";
            // 
            // panel1
            // 
            this.panel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(242)))), ((int)(((byte)(245)))), ((int)(((byte)(250)))));
            this.panel1.BackgroundImage = global::Tjhis.Controls.Properties.Resources.接诊统计bg;
            this.panel1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.panel1.Controls.Add(this.ucCompareOther);
            this.panel1.Controls.Add(this.ucCompareRefund);
            this.panel1.Controls.Add(this.ucCompareSum);
            this.panel1.Controls.Add(this.labelControl13);
            this.panel1.Controls.Add(this.labelControl11);
            this.panel1.Controls.Add(this.labelControl9);
            this.panel1.Controls.Add(this.LRefund);
            this.panel1.Controls.Add(this.LOther);
            this.panel1.Controls.Add(this.LSum);
            this.panel1.Controls.Add(this.labelControl5);
            this.panel1.Controls.Add(this.labelControl4);
            this.panel1.Controls.Add(this.labelControl6);
            this.panel1.Location = new System.Drawing.Point(31, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(646, 168);
            this.panel1.TabIndex = 20;
            // 
            // ucCompareOther
            // 
            this.ucCompareOther.BackColor = System.Drawing.SystemColors.Control;
            this.ucCompareOther.ButtonMarginLeft = 10;
            this.ucCompareOther.Location = new System.Drawing.Point(536, 110);
            this.ucCompareOther.Margin = new System.Windows.Forms.Padding(2);
            this.ucCompareOther.Name = "ucCompareOther";
            this.ucCompareOther.Radius = 15;
            this.ucCompareOther.RadiusColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.ucCompareOther.ShowRightButton = false;
            this.ucCompareOther.Size = new System.Drawing.Size(64, 20);
            this.ucCompareOther.TabIndex = 31;
            this.ucCompareOther.TitleText = null;
            // 
            // ucCompareRefund
            // 
            this.ucCompareRefund.BackColor = System.Drawing.SystemColors.Control;
            this.ucCompareRefund.ButtonMarginLeft = 10;
            this.ucCompareRefund.Location = new System.Drawing.Point(274, 110);
            this.ucCompareRefund.Margin = new System.Windows.Forms.Padding(2);
            this.ucCompareRefund.Name = "ucCompareRefund";
            this.ucCompareRefund.Radius = 15;
            this.ucCompareRefund.RadiusColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.ucCompareRefund.ShowRightButton = false;
            this.ucCompareRefund.Size = new System.Drawing.Size(64, 20);
            this.ucCompareRefund.TabIndex = 30;
            this.ucCompareRefund.TitleText = null;
            // 
            // ucCompareSum
            // 
            this.ucCompareSum.BackColor = System.Drawing.SystemColors.Control;
            this.ucCompareSum.ButtonMarginLeft = 10;
            this.ucCompareSum.Location = new System.Drawing.Point(74, 110);
            this.ucCompareSum.Margin = new System.Windows.Forms.Padding(2);
            this.ucCompareSum.Name = "ucCompareSum";
            this.ucCompareSum.Radius = 15;
            this.ucCompareSum.RadiusColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.ucCompareSum.ShowRightButton = false;
            this.ucCompareSum.Size = new System.Drawing.Size(64, 20);
            this.ucCompareSum.TabIndex = 29;
            this.ucCompareSum.TitleText = null;
            // 
            // labelControl13
            // 
            this.labelControl13.Appearance.Font = new System.Drawing.Font("微软雅黑", 13F, System.Drawing.FontStyle.Bold);
            this.labelControl13.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(162)))), ((int)(((byte)(175)))), ((int)(((byte)(205)))));
            this.labelControl13.Appearance.Options.UseFont = true;
            this.labelControl13.Appearance.Options.UseForeColor = true;
            this.labelControl13.Location = new System.Drawing.Point(483, 108);
            this.labelControl13.Name = "labelControl13";
            this.labelControl13.Size = new System.Drawing.Size(36, 25);
            this.labelControl13.TabIndex = 28;
            this.labelControl13.Text = "同比";
            // 
            // labelControl11
            // 
            this.labelControl11.Appearance.Font = new System.Drawing.Font("微软雅黑", 13F, System.Drawing.FontStyle.Bold);
            this.labelControl11.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(162)))), ((int)(((byte)(175)))), ((int)(((byte)(205)))));
            this.labelControl11.Appearance.Options.UseFont = true;
            this.labelControl11.Appearance.Options.UseForeColor = true;
            this.labelControl11.Location = new System.Drawing.Point(221, 108);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(36, 25);
            this.labelControl11.TabIndex = 27;
            this.labelControl11.Text = "同比";
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("微软雅黑", 13F, System.Drawing.FontStyle.Bold);
            this.labelControl9.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(162)))), ((int)(((byte)(175)))), ((int)(((byte)(205)))));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Appearance.Options.UseForeColor = true;
            this.labelControl9.Location = new System.Drawing.Point(32, 108);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(36, 25);
            this.labelControl9.TabIndex = 26;
            this.labelControl9.Text = "同比";
            // 
            // LRefund
            // 
            this.LRefund.AllowHtmlString = true;
            this.LRefund.Appearance.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.LRefund.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(78)))), ((int)(((byte)(87)))), ((int)(((byte)(128)))));
            this.LRefund.Appearance.Options.UseFont = true;
            this.LRefund.Appearance.Options.UseForeColor = true;
            this.LRefund.Location = new System.Drawing.Point(483, 60);
            this.LRefund.Name = "LRefund";
            this.LRefund.Size = new System.Drawing.Size(44, 24);
            this.LRefund.TabIndex = 25;
            this.LRefund.Text = "<size=13>50</size><size=9>人次</size>";
            // 
            // LOther
            // 
            this.LOther.AllowHtmlString = true;
            this.LOther.Appearance.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.LOther.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(78)))), ((int)(((byte)(87)))), ((int)(((byte)(128)))));
            this.LOther.Appearance.Options.UseFont = true;
            this.LOther.Appearance.Options.UseForeColor = true;
            this.LOther.Location = new System.Drawing.Point(221, 60);
            this.LOther.Name = "LOther";
            this.LOther.Size = new System.Drawing.Size(44, 24);
            this.LOther.TabIndex = 24;
            this.LOther.Text = "<size=13>50</size><size=9>人次</size>";
            // 
            // LSum
            // 
            this.LSum.AllowHtmlString = true;
            this.LSum.Appearance.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.LSum.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(78)))), ((int)(((byte)(87)))), ((int)(((byte)(128)))));
            this.LSum.Appearance.Options.UseFont = true;
            this.LSum.Appearance.Options.UseForeColor = true;
            this.LSum.Location = new System.Drawing.Point(32, 60);
            this.LSum.Name = "LSum";
            this.LSum.Size = new System.Drawing.Size(44, 24);
            this.LSum.TabIndex = 23;
            this.LSum.Text = "<size=13>50</size><size=9>人次</size>";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("微软雅黑", 13F, System.Drawing.FontStyle.Bold);
            this.labelControl5.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(162)))), ((int)(((byte)(175)))), ((int)(((byte)(205)))));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Appearance.Options.UseForeColor = true;
            this.labelControl5.Location = new System.Drawing.Point(221, 30);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(72, 25);
            this.labelControl5.TabIndex = 22;
            this.labelControl5.Text = "退费金额";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("微软雅黑", 13F, System.Drawing.FontStyle.Bold);
            this.labelControl4.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(162)))), ((int)(((byte)(175)))), ((int)(((byte)(205)))));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Appearance.Options.UseForeColor = true;
            this.labelControl4.Location = new System.Drawing.Point(483, 30);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(36, 25);
            this.labelControl4.TabIndex = 21;
            this.labelControl4.Text = "其他";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("微软雅黑", 13F, System.Drawing.FontStyle.Bold);
            this.labelControl6.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(162)))), ((int)(((byte)(175)))), ((int)(((byte)(205)))));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Appearance.Options.UseForeColor = true;
            this.labelControl6.Location = new System.Drawing.Point(32, 30);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(72, 25);
            this.labelControl6.TabIndex = 20;
            this.labelControl6.Text = "费用总额";
            // 
            // ucIncomeChart
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.tableMain);
            this.Controls.Add(this.labelControl1);
            this.Name = "ucIncomeChart";
            this.Padding = new System.Windows.Forms.Padding(0, 0, 0, 5);
            this.Size = new System.Drawing.Size(798, 615);
            this.tableMain.ResumeLayout(false);
            this.panel3.ResumeLayout(false);
            this.panel3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(doughnutSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ChartPayType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarAreaSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarRangeAreaSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ChartIncomeType)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.Panel tableMain;
        private System.Windows.Forms.Panel panel3;
        private DevExpress.XtraCharts.ChartControl ChartPayType;
        private DevExpress.XtraCharts.ChartControl ChartIncomeType;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private System.Windows.Forms.Panel panel1;
        private ucCompare ucCompareOther;
        private ucCompare ucCompareRefund;
        private ucCompare ucCompareSum;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl LRefund;
        private DevExpress.XtraEditors.LabelControl LOther;
        private DevExpress.XtraEditors.LabelControl LSum;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl6;
    }
}
