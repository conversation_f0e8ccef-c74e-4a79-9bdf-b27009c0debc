﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace OracleDAL
{
    public class SEC_RIGHT_GROUP_VS_USERS_Dao:SEC_RIGHT_GROUP_VS_USERS_Dao_Base
    {
        public System.Data.DataSet GetSelectedGroups(string userName, Utility.OracleODP.OracleBaseClass db)
        {
            System.Data.DataSet ds = null;
            //右外连接，查询非本人权限
            String sqlStr = " select t1.*,t2.right_group_text  from sec_right_group_vs_users t1 Left Join sec_right_group t2 on t1.right_group_code=t2.right_group_code where t1.user_code='" + userName + "'";
            ds = db.SelectDataSet(sqlStr);
            return ds;
        }

        public System.Data.DataSet GetUnSelectedGroups(string userName, Utility.OracleODP.OracleBaseClass db)
        {
            System.Data.DataSet ds = null;
            //右外连接，查询非本人权限
            String sqlStr = "select t2.* from "
                +"(select * from sec_right_group_vs_users t where t.user_code='"+ userName+"') t1 "
                +" RIGHT OUTER join sec_right_group t2"
                +" on t1.application_code = t2.application_code"
                +" and t1.right_group_code = t2.right_group_code"
                + " Where t1.right_group_code IS NULL";
            ds = db.SelectDataSet(sqlStr);
            return ds;
        }
    }
}
