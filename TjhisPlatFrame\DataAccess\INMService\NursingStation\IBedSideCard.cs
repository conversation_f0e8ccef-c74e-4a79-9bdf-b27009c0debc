﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.ServiceModel;
using System.Text;
using Model;

namespace INMService
{
    [ServiceContract]
    public interface IBedSideCard
    {
        /// <summary>
        /// 根据护理单元和其他条件查询床头卡片信息
        /// </summary>
        /// <param name="warcodes">护理单元编码列表，逗号间隔</param>
        /// <param name="otherWhere">其他where条件</param>
        /// <returns></returns>
        [OperationContract]
        DataSet GetBedSideCardBySql(string warcodes, string otherWhere);
        /// <summary>
        /// 获取右键权限菜单
        /// </summary>
        /// <param name="applicationcode">应用代码</param>
        /// <param name="username">用户名</param>
        /// <param name="menugroup">菜单组</param>
        /// <param name="orderBy">排序字段</param>
        /// <returns></returns>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<SEC_MENUS_DICT> GetBedSideRightMenus(string applicationcode, string username, string menugroup, string orderBy);
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<SEC_MENUS_DICT> GetBedSideRightMenus_EMR(string applicationcode, string username, string menugroup, string orderBy,string hisunitcode);
    }
}
