﻿using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using Utility;
using Utility.OracleODP;
using Oracle.ManagedDataAccess.Client;

namespace OracleDAL
{

    /// <summary>
    /// 费用模板主记录 数据库操作类
    /// </summary>

    public class BILL_PATTERN_MASTER_Dao_Base
    {
        #region   Method
        public bool Exists(decimal SERIAL_NO, string PATTERN_NAME, string INPUT_CODE, string INPUT_CODE_WB, string DEPT_CODE, OracleBaseClass db)
        {
            #region  init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from BILL_PATTERN_MASTER");
            strSql.Append(" where ");
            strSql.Append(" SERIAL_NO = :SERIAL_NO and  ");
            strSql.Append(" PATTERN_NAME = :PATTERN_NAME and  ");
            strSql.Append(" INPUT_CODE = :INPUT_CODE and  ");
            strSql.Append(" INPUT_CODE_WB = :INPUT_CODE_WB and  ");
            strSql.Append(" DEPT_CODE = :DEPT_CODE ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":SERIAL_NO", OracleDbType.Decimal, 4);
            p.Value = SERIAL_NO;
            parameters.Add(p);

            p = new OracleParameter(":PATTERN_NAME", OracleDbType.Varchar2, 40);
            p.Value = PATTERN_NAME;
            parameters.Add(p);

            p = new OracleParameter(":INPUT_CODE", OracleDbType.Varchar2, 8);
            p.Value = INPUT_CODE;
            parameters.Add(p);

            p = new OracleParameter(":INPUT_CODE_WB", OracleDbType.Varchar2, 8);
            p.Value = INPUT_CODE_WB;
            parameters.Add(p);

            p = new OracleParameter(":DEPT_CODE", OracleDbType.Varchar2, 8);
            p.Value = DEPT_CODE;
            parameters.Add(p);

            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    int cmdresult;
                    cmdresult = int.Parse(ds.Tables[0].Rows[0][0].ToString());
                    if (cmdresult <= 0)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                    return false;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.BILL_PATTERN_MASTER model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into BILL_PATTERN_MASTER(");
            strSql.Append("SERIAL_NO,PATTERN_NAME,INPUT_CODE,INPUT_CODE_WB,DEPT_CODE");
            strSql.Append(") values (");
            strSql.Append(":SERIAL_NO,:PATTERN_NAME,:INPUT_CODE,:INPUT_CODE_WB,:DEPT_CODE");
            strSql.Append(") ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":SERIAL_NO", OracleDbType.Decimal, 4);
            p.Value = model.SERIAL_NO;
            parameters.Add(p);

            p = new OracleParameter(":PATTERN_NAME", OracleDbType.Varchar2, 40);
            p.Value = model.PATTERN_NAME;
            parameters.Add(p);

            p = new OracleParameter(":INPUT_CODE", OracleDbType.Varchar2, 8);
            p.Value = model.INPUT_CODE;
            parameters.Add(p);

            p = new OracleParameter(":INPUT_CODE_WB", OracleDbType.Varchar2, 8);
            p.Value = model.INPUT_CODE_WB;
            parameters.Add(p);

            p = new OracleParameter(":DEPT_CODE", OracleDbType.Varchar2, 8);
            p.Value = model.DEPT_CODE;
            parameters.Add(p);
            #endregion
            try
            {

                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.BILL_PATTERN_MASTER model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update BILL_PATTERN_MASTER set ");

            strSql.Append(" SERIAL_NO = :SERIAL_NO , ");
            strSql.Append(" PATTERN_NAME = :PATTERN_NAME , ");
            strSql.Append(" INPUT_CODE = :INPUT_CODE , ");
            strSql.Append(" INPUT_CODE_WB = :INPUT_CODE_WB , ");
            strSql.Append(" DEPT_CODE = :DEPT_CODE  ");
            strSql.Append(" where SERIAL_NO=:SERIAL_NO and PATTERN_NAME=:PATTERN_NAME and INPUT_CODE=:INPUT_CODE and INPUT_CODE_WB=:INPUT_CODE_WB and DEPT_CODE=:DEPT_CODE  ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":SERIAL_NO", OracleDbType.Decimal, 4);
            p.Value = model.SERIAL_NO;
            parameters.Add(p);

            p = new OracleParameter(":PATTERN_NAME", OracleDbType.Varchar2, 40);
            p.Value = model.PATTERN_NAME;
            parameters.Add(p);

            p = new OracleParameter(":INPUT_CODE", OracleDbType.Varchar2, 8);
            p.Value = model.INPUT_CODE;
            parameters.Add(p);

            p = new OracleParameter(":INPUT_CODE_WB", OracleDbType.Varchar2, 8);
            p.Value = model.INPUT_CODE_WB;
            parameters.Add(p);

            p = new OracleParameter(":DEPT_CODE", OracleDbType.Varchar2, 8);
            p.Value = model.DEPT_CODE;
            parameters.Add(p);
            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(decimal SERIAL_NO, string PATTERN_NAME, string INPUT_CODE, string INPUT_CODE_WB, string DEPT_CODE, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from BILL_PATTERN_MASTER ");
            strSql.Append(" where SERIAL_NO=:SERIAL_NO and PATTERN_NAME=:PATTERN_NAME and INPUT_CODE=:INPUT_CODE and INPUT_CODE_WB=:INPUT_CODE_WB and DEPT_CODE=:DEPT_CODE ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":SERIAL_NO", OracleDbType.Decimal, 4);
            p.Value = SERIAL_NO;
            parameters.Add(p);

            p = new OracleParameter(":PATTERN_NAME", OracleDbType.Varchar2, 40);
            p.Value = PATTERN_NAME;
            parameters.Add(p);

            p = new OracleParameter(":INPUT_CODE", OracleDbType.Varchar2, 8);
            p.Value = INPUT_CODE;
            parameters.Add(p);

            p = new OracleParameter(":INPUT_CODE_WB", OracleDbType.Varchar2, 8);
            p.Value = INPUT_CODE_WB;
            parameters.Add(p);

            p = new OracleParameter(":DEPT_CODE", OracleDbType.Varchar2, 8);
            p.Value = DEPT_CODE;
            parameters.Add(p);

            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }



        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.BILL_PATTERN_MASTER GetModel(decimal SERIAL_NO, string PATTERN_NAME, string INPUT_CODE, string INPUT_CODE_WB, string DEPT_CODE, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select SERIAL_NO, PATTERN_NAME, INPUT_CODE, INPUT_CODE_WB, DEPT_CODE  ");
            strSql.Append("  from BILL_PATTERN_MASTER ");
            strSql.Append(" where SERIAL_NO=:SERIAL_NO and PATTERN_NAME=:PATTERN_NAME and INPUT_CODE=:INPUT_CODE and INPUT_CODE_WB=:INPUT_CODE_WB and DEPT_CODE=:DEPT_CODE ");
            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":SERIAL_NO", OracleDbType.Decimal, 4);
            p.Value = SERIAL_NO;
            parameters.Add(p);

            p = new OracleParameter(":PATTERN_NAME", OracleDbType.Varchar2, 40);
            p.Value = PATTERN_NAME;
            parameters.Add(p);

            p = new OracleParameter(":INPUT_CODE", OracleDbType.Varchar2, 8);
            p.Value = INPUT_CODE;
            parameters.Add(p);

            p = new OracleParameter(":INPUT_CODE_WB", OracleDbType.Varchar2, 8);
            p.Value = INPUT_CODE_WB;
            parameters.Add(p);

            p = new OracleParameter(":DEPT_CODE", OracleDbType.Varchar2, 8);
            p.Value = DEPT_CODE;
            parameters.Add(p);
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;
                    Model.BILL_PATTERN_MASTER model = new Model.BILL_PATTERN_MASTER();

                    if (cmdresult > 0)
                    {
                        model = CopyToModel(ds.Tables[0].Rows[0]);
                        return model;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM BILL_PATTERN_MASTER ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得几行数据
        /// </summary>
        public DataSet GetList(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            #region 初始化参数
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM BILL_PATTERN_MASTER T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.BILL_PATTERN_MASTER> GetObservableCollection(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM BILL_PATTERN_MASTER ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.BILL_PATTERN_MASTER> list = new System.Collections.ObjectModel.ObservableCollection<Model.BILL_PATTERN_MASTER>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.BILL_PATTERN_MASTER model = new Model.BILL_PATTERN_MASTER();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表 
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.BILL_PATTERN_MASTER> GetObservableCollection(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM BILL_PATTERN_MASTER T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }

            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.BILL_PATTERN_MASTER> list = new System.Collections.ObjectModel.ObservableCollection<Model.BILL_PATTERN_MASTER>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.BILL_PATTERN_MASTER model = new Model.BILL_PATTERN_MASTER();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion   Method
        #region
        /// <summary>
        /// 
        /// </summary>
        protected Model.BILL_PATTERN_MASTER CopyToModel(DataRow dRow)
        {
            Model.BILL_PATTERN_MASTER model1 = new Model.BILL_PATTERN_MASTER();

            if (dRow["SERIAL_NO"] != null && dRow["SERIAL_NO"].ToString() != "")
            {
                model1.SERIAL_NO = decimal.Parse(dRow["SERIAL_NO"].ToString());
            }

            if (dRow["PATTERN_NAME"] != null && dRow["PATTERN_NAME"].ToString() != "")
            {
                model1.PATTERN_NAME = dRow["PATTERN_NAME"].ToString();
            }

            if (dRow["INPUT_CODE"] != null && dRow["INPUT_CODE"].ToString() != "")
            {
                model1.INPUT_CODE = dRow["INPUT_CODE"].ToString();
            }

            if (dRow["INPUT_CODE_WB"] != null && dRow["INPUT_CODE_WB"].ToString() != "")
            {
                model1.INPUT_CODE_WB = dRow["INPUT_CODE_WB"].ToString();
            }

            if (dRow["DEPT_CODE"] != null && dRow["DEPT_CODE"].ToString() != "")
            {
                model1.DEPT_CODE = dRow["DEPT_CODE"].ToString();
            }

            return model1;
        }
        #endregion

    }
}

