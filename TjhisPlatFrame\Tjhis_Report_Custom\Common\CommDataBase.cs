﻿using NM_Service.NMService;
using System;
using System.Collections.Generic;
using System.Data;

namespace Tjhis.Report.Custom.Common
{
    public class CommDataBase
    {
        public static ServerPublicClient dbHelper = new ServerPublicClient();

        /// <summary>
        /// SQL执行
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public static int ExecuteWithTrans(string sql)
        {
            try
            {
                if (dbHelper == null)
                {
                    dbHelper = new ServerPublicClient();
                }
                Dictionary<string, string> idc = new Dictionary<string, string>();
                idc.Add(sql, "执行失败");
                string rev = dbHelper.SaveTable(idc);
                if (!string.IsNullOrEmpty(rev))
                {
                    return 0;
                }
                return 1;
            }
            catch (Exception ex)
            {
                PlatCommon.Common.PublicFunction.WriteWrrLog(ex.ToString());
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="tableName"></param>
        /// <param name="blnWithKey"></param>
        /// <returns></returns>
        public static DataSet GetDataSet(string sql, string tableName = "", bool blnWithKey = false)
        {
            try
            {
                if (dbHelper == null)
                {
                    dbHelper = new ServerPublicClient();
                }
                DataSet ds = new DataSet();
                DataTable dt = dbHelper.GetDataBySql(sql).Tables[0];
                dt.TableName = tableName;
                ds.Tables.Add(dt.Copy());
                return ds;
            }
            catch (Exception ex)
            {
                PlatCommon.Common.PublicFunction.WriteWrrLog(ex.ToString());
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="tableName"></param>
        /// <param name="blnWithKey"></param>
        /// <returns></returns>
        public static DataTable GetDataTable(string sql, string tableName = "", bool blnWithKey = false)
        {
            try
            {
                if (dbHelper == null)
                {
                    dbHelper = new ServerPublicClient();
                }
                DataTable dt = dbHelper.GetDataBySql(sql).Tables[0];
                dt.TableName = tableName;
                return dt;
            }
            catch (Exception ex)
            {
                PlatCommon.Common.PublicFunction.WriteWrrLog(ex.ToString());
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        /// SQL执行
        /// </summary>
        /// <param name="ds"></param>
        /// <returns></returns>
        public static int SaveDataWithTrans(DataSet ds)
        {
            try
            {
                if (dbHelper == null)
                {
                    dbHelper = new ServerPublicClient();
                }
                return dbHelper.SaveDataSet(ds);
            }
            catch (Exception ex)
            {
                PlatCommon.Common.PublicFunction.WriteWrrLog(ex.ToString());
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public static string ExecuteScalar(string sql)
        {
            try
            {
                if (dbHelper == null)
                {
                    dbHelper = new ServerPublicClient();
                }
                return dbHelper.GetSingleValue(sql);
            }
            catch (Exception ex)
            {
                PlatCommon.Common.PublicFunction.WriteWrrLog(ex.ToString());
                throw new Exception(ex.ToString());
            }
        }

        /// <summary>
        /// 批量SQL执行
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static Boolean ExecuteSql(List<string> list)
        {
            try
            {
                if (dbHelper == null)
                {
                    dbHelper = new ServerPublicClient();
                }
                Dictionary<string, string> idc = new Dictionary<string, string>();
                for (int i = 0; i < list.Count; i++)
                {
                    idc.Add(list[i], "执行失败" + list[i].ToString());
                }
                string rev = dbHelper.SaveTable(idc);
                if (!string.IsNullOrEmpty(rev))
                {
                    return false;
                }
                return true;
            }
            catch (Exception ex)
            {
                PlatCommon.Common.PublicFunction.WriteWrrLog(ex.ToString());
                throw new Exception(ex.ToString());
            }
        }


        /// <summary>
        /// 保存dataTable数据到数据库
        /// </summary>
        /// <param name="dtTableSource"></param>
        /// <returns></returns>
        public static int DataTableSave(DataTable dtTableSource)
        {
            try
            {
                if (dbHelper == null)
                {
                    dbHelper = new ServerPublicClient();
                }
                DataSet ds = new DataSet();
                string strUpdateSQL = UpdateTableColumnSql(dtTableSource.TableName, dtTableSource.Namespace); //strUpdateSQL 是要更新的语句
                string sqlpd = @" SELECT count(*) FROM all_tables a left join user_tab_comments b on a.TABLE_NAME = b.TABLE_NAME
                                    WHERE a.TABLE_NAME = '" + dtTableSource.TableName + "'";
                int ipd = int.Parse(dbHelper.GetSingleValue(sqlpd));
                if (ipd <= 0)
                {
                    if (dtTableSource.Rows.Count > 0)
                    {
                        return 0;
                    }
                }
                ds.Tables.Add(dtTableSource.Copy());
                return dbHelper.SaveDataSet(ds);
            }
            catch (Exception ee)
            {
                throw new Exception("DataTableSave:" + "\r\n" + ee.Message);
            }
        }

        /// <summary>
        /// 更新数据库列
        /// </summary>
        public static Dictionary<string, string> UpdateTableColumn = new Dictionary<string, string>();
        /// <summary>
        /// 获取更新列Sql
        /// </summary>
        /// <param name="tableName">数据库表名</param>
        /// <param name="appSaveName">更新存储的名称</param>
        /// <returns></returns>
        public static string UpdateTableColumnSql(string tableName, string appSaveName)
        {
            try
            {
                string strUpdateSQL = "SELECT * FROM " + tableName;
                if (UpdateTableColumn.ContainsKey(appSaveName))
                {
                    strUpdateSQL = UpdateTableColumn[appSaveName];
                }
                return strUpdateSQL;
            }
            catch (Exception ee)
            {
                throw new Exception("获取更新列错误:" + "\r\n" + ee.Message);
            }
        }
    }
}
