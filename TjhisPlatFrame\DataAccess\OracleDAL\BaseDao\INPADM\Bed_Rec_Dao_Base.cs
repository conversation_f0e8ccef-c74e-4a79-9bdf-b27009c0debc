﻿using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using Utility;
using Utility.OracleODP;
using Oracle.ManagedDataAccess.Client;

namespace OracleDAL
{

    /// <summary>
    /// 床位记录 数据库操作类
    /// </summary>

    public class BED_REC_Dao_Base
    {
        #region   Method
        public bool Exists(string WARD_CODE, decimal BED_NO, OracleBaseClass db)
        {
            #region  init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from BED_REC");
            strSql.Append(" where ");
            strSql.Append(" WARD_CODE = :WARD_CODE and  ");
            strSql.Append(" BED_NO = :BED_NO ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":WARD_CODE", OracleDbType.Varchar2, 8);
            p.Value = WARD_CODE;
            parameters.Add(p);

            p = new OracleParameter(":BED_NO", OracleDbType.Decimal, 8);
            p.Value = BED_NO;
            parameters.Add(p);

            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    int cmdresult;
                    cmdresult = int.Parse(ds.Tables[0].Rows[0][0].ToString());
                    if (cmdresult <= 0)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                    return false;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.BED_REC model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into BED_REC(");
            strSql.Append("WARD_CODE,LEND_ATTR,LEND_BED_NO,LEND_BED_DEPT,LEND_BED_WARD,LOCK_STATUS,LOCK_OPERATOR,AIRCONDITION_CLASS,PATIENT_ID,BED_NO,BED_LABEL,ROOM_NO,DEPT_CODE,BED_APPROVED_TYPE,BED_SEX_TYPE,BED_CLASS,BED_STATUS");
            strSql.Append(") values (");
            strSql.Append(":WARD_CODE,:LEND_ATTR,:LEND_BED_NO,:LEND_BED_DEPT,:LEND_BED_WARD,:LOCK_STATUS,:LOCK_OPERATOR,:AIRCONDITION_CLASS,:PATIENT_ID,:BED_NO,:BED_LABEL,:ROOM_NO,:DEPT_CODE,:BED_APPROVED_TYPE,:BED_SEX_TYPE,:BED_CLASS,:BED_STATUS");
            strSql.Append(") ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":WARD_CODE", OracleDbType.Varchar2, 8);
            p.Value = model.WARD_CODE;
            parameters.Add(p);

            p = new OracleParameter(":LEND_ATTR", OracleDbType.Varchar2, 1);
            p.Value = model.LEND_ATTR;
            parameters.Add(p);

            p = new OracleParameter(":LEND_BED_NO", OracleDbType.Decimal, 3);
            p.Value = model.LEND_BED_NO;
            parameters.Add(p);

            p = new OracleParameter(":LEND_BED_DEPT", OracleDbType.Varchar2, 8);
            p.Value = model.LEND_BED_DEPT;
            parameters.Add(p);

            p = new OracleParameter(":LEND_BED_WARD", OracleDbType.Varchar2, 8);
            p.Value = model.LEND_BED_WARD;
            parameters.Add(p);

            p = new OracleParameter(":LOCK_STATUS", OracleDbType.Varchar2, 1);
            p.Value = model.LOCK_STATUS;
            parameters.Add(p);

            p = new OracleParameter(":LOCK_OPERATOR", OracleDbType.Varchar2, 20);
            p.Value = model.LOCK_OPERATOR;
            parameters.Add(p);

            p = new OracleParameter(":AIRCONDITION_CLASS", OracleDbType.Varchar2, 20);
            p.Value = model.AIRCONDITION_CLASS;
            parameters.Add(p);

            p = new OracleParameter(":PATIENT_ID", OracleDbType.Varchar2, 20);
            p.Value = model.PATIENT_ID;
            parameters.Add(p);

            p = new OracleParameter(":BED_NO", OracleDbType.Decimal, 8);
            p.Value = model.BED_NO;
            parameters.Add(p);

            p = new OracleParameter(":BED_LABEL", OracleDbType.Varchar2, 8);
            p.Value = model.BED_LABEL;
            parameters.Add(p);

            p = new OracleParameter(":ROOM_NO", OracleDbType.Varchar2, 8);
            p.Value = model.ROOM_NO;
            parameters.Add(p);

            p = new OracleParameter(":DEPT_CODE", OracleDbType.Varchar2, 8);
            p.Value = model.DEPT_CODE;
            parameters.Add(p);

            p = new OracleParameter(":BED_APPROVED_TYPE", OracleDbType.Varchar2, 1);
            p.Value = model.BED_APPROVED_TYPE;
            parameters.Add(p);

            p = new OracleParameter(":BED_SEX_TYPE", OracleDbType.Varchar2, 1);
            p.Value = model.BED_SEX_TYPE;
            parameters.Add(p);

            p = new OracleParameter(":BED_CLASS", OracleDbType.Varchar2, 20);
            p.Value = model.BED_CLASS;
            parameters.Add(p);

            p = new OracleParameter(":BED_STATUS", OracleDbType.Varchar2, 1);
            p.Value = model.BED_STATUS;
            parameters.Add(p);
            #endregion
            try
            {

                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.BED_REC model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update BED_REC set ");

            strSql.Append(" WARD_CODE = :WARD_CODE , ");
            strSql.Append(" LEND_ATTR = :LEND_ATTR , ");
            strSql.Append(" LEND_BED_NO = :LEND_BED_NO , ");
            strSql.Append(" LEND_BED_DEPT = :LEND_BED_DEPT , ");
            strSql.Append(" LEND_BED_WARD = :LEND_BED_WARD , ");
            strSql.Append(" LOCK_STATUS = :LOCK_STATUS , ");
            strSql.Append(" LOCK_OPERATOR = :LOCK_OPERATOR , ");
            strSql.Append(" AIRCONDITION_CLASS = :AIRCONDITION_CLASS , ");
            strSql.Append(" PATIENT_ID = :PATIENT_ID , ");
            strSql.Append(" BED_NO = :BED_NO , ");
            strSql.Append(" BED_LABEL = :BED_LABEL , ");
            strSql.Append(" ROOM_NO = :ROOM_NO , ");
            strSql.Append(" DEPT_CODE = :DEPT_CODE , ");
            strSql.Append(" BED_APPROVED_TYPE = :BED_APPROVED_TYPE , ");
            strSql.Append(" BED_SEX_TYPE = :BED_SEX_TYPE , ");
            strSql.Append(" BED_CLASS = :BED_CLASS , ");
            strSql.Append(" BED_STATUS = :BED_STATUS  ");
            strSql.Append(" where WARD_CODE=:WARD_CODE and BED_NO=:BED_NO  ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":WARD_CODE", OracleDbType.Varchar2, 8);
            p.Value = model.WARD_CODE;
            parameters.Add(p);

            p = new OracleParameter(":LEND_ATTR", OracleDbType.Varchar2, 1);
            p.Value = model.LEND_ATTR;
            parameters.Add(p);

            p = new OracleParameter(":LEND_BED_NO", OracleDbType.Decimal, 3);
            p.Value = model.LEND_BED_NO;
            parameters.Add(p);

            p = new OracleParameter(":LEND_BED_DEPT", OracleDbType.Varchar2, 8);
            p.Value = model.LEND_BED_DEPT;
            parameters.Add(p);

            p = new OracleParameter(":LEND_BED_WARD", OracleDbType.Varchar2, 8);
            p.Value = model.LEND_BED_WARD;
            parameters.Add(p);

            p = new OracleParameter(":LOCK_STATUS", OracleDbType.Varchar2, 1);
            p.Value = model.LOCK_STATUS;
            parameters.Add(p);

            p = new OracleParameter(":LOCK_OPERATOR", OracleDbType.Varchar2, 20);
            p.Value = model.LOCK_OPERATOR;
            parameters.Add(p);

            p = new OracleParameter(":AIRCONDITION_CLASS", OracleDbType.Varchar2, 20);
            p.Value = model.AIRCONDITION_CLASS;
            parameters.Add(p);

            p = new OracleParameter(":PATIENT_ID", OracleDbType.Varchar2, 20);
            p.Value = model.PATIENT_ID;
            parameters.Add(p);

            p = new OracleParameter(":BED_NO", OracleDbType.Decimal, 8);
            p.Value = model.BED_NO;
            parameters.Add(p);

            p = new OracleParameter(":BED_LABEL", OracleDbType.Varchar2, 8);
            p.Value = model.BED_LABEL;
            parameters.Add(p);

            p = new OracleParameter(":ROOM_NO", OracleDbType.Varchar2, 8);
            p.Value = model.ROOM_NO;
            parameters.Add(p);

            p = new OracleParameter(":DEPT_CODE", OracleDbType.Varchar2, 8);
            p.Value = model.DEPT_CODE;
            parameters.Add(p);

            p = new OracleParameter(":BED_APPROVED_TYPE", OracleDbType.Varchar2, 1);
            p.Value = model.BED_APPROVED_TYPE;
            parameters.Add(p);

            p = new OracleParameter(":BED_SEX_TYPE", OracleDbType.Varchar2, 1);
            p.Value = model.BED_SEX_TYPE;
            parameters.Add(p);

            p = new OracleParameter(":BED_CLASS", OracleDbType.Varchar2, 20);
            p.Value = model.BED_CLASS;
            parameters.Add(p);

            p = new OracleParameter(":BED_STATUS", OracleDbType.Varchar2, 1);
            p.Value = model.BED_STATUS;
            parameters.Add(p);
            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string WARD_CODE, decimal BED_NO, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from BED_REC ");
            strSql.Append(" where WARD_CODE=:WARD_CODE and BED_NO=:BED_NO ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":WARD_CODE", OracleDbType.Varchar2, 8);
            p.Value = WARD_CODE;
            parameters.Add(p);

            p = new OracleParameter(":BED_NO", OracleDbType.Decimal, 8);
            p.Value = BED_NO;
            parameters.Add(p);

            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }



        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.BED_REC GetModel(string WARD_CODE, decimal BED_NO, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select WARD_CODE, LEND_ATTR, LEND_BED_NO, LEND_BED_DEPT, LEND_BED_WARD, LOCK_STATUS, LOCK_OPERATOR, AIRCONDITION_CLASS, PATIENT_ID, BED_NO, BED_LABEL, ROOM_NO, DEPT_CODE, BED_APPROVED_TYPE, BED_SEX_TYPE, BED_CLASS, BED_STATUS  ");
            strSql.Append("  from BED_REC ");
            strSql.Append(" where WARD_CODE=:WARD_CODE and BED_NO=:BED_NO ");
            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":WARD_CODE", OracleDbType.Varchar2, 8);
            p.Value = WARD_CODE;
            parameters.Add(p);

            p = new OracleParameter(":BED_NO", OracleDbType.Decimal, 8);
            p.Value = BED_NO;
            parameters.Add(p);
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;
                    Model.BED_REC model = new Model.BED_REC();

                    if (cmdresult > 0)
                    {
                        model = CopyToModel(ds.Tables[0].Rows[0]);
                        return model;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM BED_REC ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得几行数据
        /// </summary>
        public DataSet GetList(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            #region 初始化参数
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM BED_REC T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.BED_REC> GetObservableCollection(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM BED_REC ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.BED_REC> list = new System.Collections.ObjectModel.ObservableCollection<Model.BED_REC>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.BED_REC model = new Model.BED_REC();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表 
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.BED_REC> GetObservableCollection(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM BED_REC T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }

            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.BED_REC> list = new System.Collections.ObjectModel.ObservableCollection<Model.BED_REC>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.BED_REC model = new Model.BED_REC();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion   Method
        #region
        /// <summary>
        /// 
        /// </summary>
        protected Model.BED_REC CopyToModel(DataRow dRow)
        {
            Model.BED_REC model1 = new Model.BED_REC();

            if (dRow["WARD_CODE"] != null && dRow["WARD_CODE"].ToString() != "")
            {
                model1.WARD_CODE = dRow["WARD_CODE"].ToString();
            }

            if (dRow["LEND_ATTR"] != null && dRow["LEND_ATTR"].ToString() != "")
            {
                model1.LEND_ATTR = dRow["LEND_ATTR"].ToString();
            }

            if (dRow["LEND_BED_NO"] != null && dRow["LEND_BED_NO"].ToString() != "")
            {
                model1.LEND_BED_NO = decimal.Parse(dRow["LEND_BED_NO"].ToString());
            }

            if (dRow["LEND_BED_DEPT"] != null && dRow["LEND_BED_DEPT"].ToString() != "")
            {
                model1.LEND_BED_DEPT = dRow["LEND_BED_DEPT"].ToString();
            }

            if (dRow["LEND_BED_WARD"] != null && dRow["LEND_BED_WARD"].ToString() != "")
            {
                model1.LEND_BED_WARD = dRow["LEND_BED_WARD"].ToString();
            }

            if (dRow["LOCK_STATUS"] != null && dRow["LOCK_STATUS"].ToString() != "")
            {
                model1.LOCK_STATUS = dRow["LOCK_STATUS"].ToString();
            }

            if (dRow["LOCK_OPERATOR"] != null && dRow["LOCK_OPERATOR"].ToString() != "")
            {
                model1.LOCK_OPERATOR = dRow["LOCK_OPERATOR"].ToString();
            }

            if (dRow["AIRCONDITION_CLASS"] != null && dRow["AIRCONDITION_CLASS"].ToString() != "")
            {
                model1.AIRCONDITION_CLASS = dRow["AIRCONDITION_CLASS"].ToString();
            }

            if (dRow["PATIENT_ID"] != null && dRow["PATIENT_ID"].ToString() != "")
            {
                model1.PATIENT_ID = dRow["PATIENT_ID"].ToString();
            }

            if (dRow["BED_NO"] != null && dRow["BED_NO"].ToString() != "")
            {
                model1.BED_NO = decimal.Parse(dRow["BED_NO"].ToString());
            }

            if (dRow["BED_LABEL"] != null && dRow["BED_LABEL"].ToString() != "")
            {
                model1.BED_LABEL = dRow["BED_LABEL"].ToString();
            }

            if (dRow["ROOM_NO"] != null && dRow["ROOM_NO"].ToString() != "")
            {
                model1.ROOM_NO = dRow["ROOM_NO"].ToString();
            }

            if (dRow["DEPT_CODE"] != null && dRow["DEPT_CODE"].ToString() != "")
            {
                model1.DEPT_CODE = dRow["DEPT_CODE"].ToString();
            }

            if (dRow["BED_APPROVED_TYPE"] != null && dRow["BED_APPROVED_TYPE"].ToString() != "")
            {
                model1.BED_APPROVED_TYPE = dRow["BED_APPROVED_TYPE"].ToString();
            }

            if (dRow["BED_SEX_TYPE"] != null && dRow["BED_SEX_TYPE"].ToString() != "")
            {
                model1.BED_SEX_TYPE = dRow["BED_SEX_TYPE"].ToString();
            }

            if (dRow["BED_CLASS"] != null && dRow["BED_CLASS"].ToString() != "")
            {
                model1.BED_CLASS = dRow["BED_CLASS"].ToString();
            }

            if (dRow["BED_STATUS"] != null && dRow["BED_STATUS"].ToString() != "")
            {
                model1.BED_STATUS = dRow["BED_STATUS"].ToString();
            }

            return model1;
        }
        #endregion

    }
}

