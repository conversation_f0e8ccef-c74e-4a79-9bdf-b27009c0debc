﻿using DevExpress.XtraEditors;
//using PlatCommon.System;
//**********************************************
//说明:高质耗材录入
//计算机名称：LINDP
//创建日期：2016/7/19 13:51:04
//作者：林大鹏
//版本号：V1.00
//**********************************************
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace PlatCommonForm
{
    public partial class FrmHighQuality : PlatCommon.SysBase.ParentForm
    {
        public FrmHighQuality()
        {
            InitializeComponent();
        }
        public string is_dept_code;
        private DataTable HighQualityList;
        public DataSet revdetail;
        //取耗材列表
        public void GetList(string storage_code,string barcode_no)
        {
            string sql = "SELECT 	req_storage_code,item_no,item_sub_no,ex_code,ex_name,ex_spec,units,package_spec,";
            sql += "package_units,purchase_price,(select b.price from current_price_list b where (b.item_class,b.item_code,b.item_spec,b.units) = ";
            sql += "(select a.item_class,a.item_code,a.item_spec,a.item_units from ex_vs_price a where a.ex_code = t.ex_code and a.ex_spec = t.package_spec)  ";
            sql += ") RETAIL_PRICE,firm_id,supplier_id,batch_code,expire_date,barcode_no,status FROM ex_prepare_supplier t ";
            sql += " where   t.confirm_storage_code = '"+storage_code+"' and t.barcode_no = '"+barcode_no+"'  and	t.status = '已备货确认'";

            HighQualityList = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            gridControl1.DataSource = HighQualityList;

        }

        private void FrmHighQuality_Load(object sender, EventArgs e)
        {
            textEdit1.Focus();
        }

        private void simpleButton2_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void textEdit1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                //库存里是否有此条码
                string ls_barcode_no = textEdit1.Text.Trim();
                if (string.IsNullOrEmpty(ls_barcode_no))
                return;
                string sql = "select count(*) from ex_prepare_supplier t where t.confirm_storage_code='" + is_dept_code + "' and t.barcode_no = '" + ls_barcode_no + "' and t.status='已备货确认'";
                DataTable dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
                if (int.Parse(dt.Rows[0][0].ToString()) > 0)
                {
                    //耗材与价表是否对照
                    sql = "select t.ex_code,t.package_spec from ex_prepare_supplier t  ";
                    sql += "where t.confirm_storage_code='"+is_dept_code+"' and t.barcode_no = '"+ls_barcode_no+"' and t.status='已备货确认'";
                    dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
                    string ls_ex_code;
                    string ls_ex_spec;
                    if (dt.Rows.Count > 0)
                    {
                        ls_ex_code = dt.Rows[0][0].ToString();
                        ls_ex_spec = dt.Rows[0][1].ToString();
                        sql = "select count(*) from exstock.EX_VS_PRICE where ex_code = '"+ls_ex_code+"' and ex_spec = '"+ls_ex_spec+"'";
                        dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
                        if (int.Parse(dt.Rows[0][0].ToString()) < 1)
                        {
                            XtraMessageBox.Show("耗材" + ls_ex_code + "与价表未对照，无法给患者使用!", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            textEdit1.Text = "";
                            textEdit1.Focus();
                            return;
                        }
                    }
                    else
                    {
                        XtraMessageBox.Show("取耗材失败", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        textEdit1.Text = "";
                        textEdit1.Focus();
                        return;
                    }
                    GetList(is_dept_code, ls_barcode_no);

                }
                else
                {
                    XtraMessageBox.Show("条码号无效", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    textEdit1.Text = "";
                    textEdit1.Focus();	
                }
                
            }
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            DataRow dr = gridView1.GetFocusedDataRow();
            if (dr == null)
                return;
            string ls_ex_code = dr["ex_code"].ToString();
            string ls_ex_spec = dr["package_spec"].ToString();
            string sql = "select ITEM_NAME,ITEM_SPEC,1 AMOUNT,ITEM_CLASS,ITEM_CODE,item_units UNITS,'' PERFORMED_BY,'" + dr["BARCODE_NO"].ToString() + "' BARCODE_NO ";
            sql += "from ex_vs_price where ex_code = '"+ls_ex_code+"' and ex_spec = '"+ls_ex_spec+"'";
            revdetail = new NM_Service.NMService.ServerPublicClient().GetList(sql);

            if (revdetail.Tables[0].Rows.Count < 1)
            {
                XtraMessageBox.Show("耗材" + ls_ex_code + "与价表未对照，无法给患者使用!", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            else
            {
                this.Close();
            }
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            simpleButton1_Click(sender, e);
        }

    }
}
