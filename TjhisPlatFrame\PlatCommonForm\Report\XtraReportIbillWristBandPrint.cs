﻿using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;

namespace NursingPlatform.Report
{
    public partial class XtraReportIbillWristBandPrint : DevExpress.XtraReports.UI.XtraReport
    {
        public XtraReportIbillWristBandPrint()
        {
            InitializeComponent();
            ico = Image.FromFile(@"logo.png");
            if (ico != null)
            {
                ico = ResizeImage(ico, 16, 16);
            }
        }
        Image ico = null;
        public XtraReportIbillWristBandPrint(string HospitalID,string Bar_PATIENT_ID, string DEPT_NAME, string pNAME, string PATIENT_ID, string Ages, string BED_LABEL, string INP_NO,string SEX)
        {
            InitializeComponent();
            ico = Image.FromFile(@"logo.png");
            
            this.xrBarCode1.Text = Bar_PATIENT_ID;
            this.xrBarCode1.Text = Bar_PATIENT_ID;
            //this.lblAge.Text = Ages;
            //this.lblBed_Label.Text = BED_LABEL;
            //this.lblDept_Name.Text = DEPT_NAME;
            this.lblID2345678.Text = PATIENT_ID;
            this.lblINP_NO.Text = INP_NO;
            this.lblName.Text = pNAME;
            this.lblSex.Text = SEX;
            this.lblTitle.Text = HospitalID;
            if (ico != null)
            {
                ico = ResizeImage(ico, 16, 16);
            }
        }
        public Bitmap ResizeImage(Image image, int height, int maxWidth)
        {
            int width = Math.Min(image.Width * height / image.Height, maxWidth);
            int heightImage = image.Height * width / image.Width;
            Rectangle destRect = new Rectangle(0, (height - heightImage) / 2, width, heightImage);
            Bitmap destImage = new Bitmap(width, height);

            using (Graphics graphics = Graphics.FromImage(destImage))
            {
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.DrawImage(image, destRect, 0, 0, image.Width, image.Height, GraphicsUnit.Pixel);
            }

            return destImage;
        }
        private void xrBarCode1_Draw(object sender, DrawEventArgs e)
        {
            XRBarCode xrb = (XRBarCode)sender;
            if (ico != null)
                e.UniGraphics.DrawImage(ico, new Point((int)(e.UniGraphics.ClipBounds.X) + (int)xrb.WidthF / 2 - 8, (int)(e.UniGraphics.ClipBounds.Y) + (int)xrb.HeightF / 2 - 8));
        }

    }
}
