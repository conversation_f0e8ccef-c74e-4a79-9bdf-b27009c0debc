/// <XRTypeInfo>
///   <AssemblyFullName>DevExpress.XtraReports.v18.1, Version=18.1.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</AssemblyFullName>
///   <AssemblyLocation>C:\Windows\Microsoft.Net\assembly\GAC_MSIL\DevExpress.XtraReports.v18.1\v4.0_18.1.12.0__b88d1754d700e49a\DevExpress.XtraReports.v18.1.dll</AssemblyLocation>
///   <TypeName>DevExpress.XtraReports.UI.XtraReport</TypeName>
///   <Localization>zh-Hans</Localization>
///   <Version>18.1</Version>
///   <Resources>
///     <Resource Name="XtraReportSerialization.XtraReport">
/// zsrvvgEAAACRAAAAbFN5c3RlbS5SZXNvdXJjZXMuUmVzb3VyY2VSZWFkZXIsIG1zY29ybGliLCBWZXJzaW9uPTQuMC4wLjAsIEN1bHR1cmU9bmV1dHJhbCwgUHVibGljS2V5VG9rZW49Yjc3YTVjNTYxOTM0ZTA4OSNTeXN0ZW0uUmVzb3VyY2VzLlJ1bnRpbWVSZXNvdXJjZVNldAIAAAACAAAAAAAAAFBBRFBBRFA141seO0eBSgAAAAAxAAAADAEAACwkAHQAaABpAHMALgBEAGEAdABhAFMAbwB1AHIAYwBlAFMAYwBoAGUAbQBhAAAAAAASJAB0AGgAaQBzAC4AVABhAGcA0goAAAHPFTw/eG1sIHZlcnNpb249IjEuMCI/Pg0KPHhzOnNjaGVtYSBpZD0iTmV3RGF0YVNldCIgeG1sbnM9IiIgeG1sbnM6eHM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDEvWE1MU2NoZW1hIiB4
/// bWxuczptc2RhdGE9InVybjpzY2hlbWFzLW1pY3Jvc29mdC1jb206eG1sLW1zZGF0YSIgeG1sbnM6bXNwcm9wPSJ1cm46c2NoZW1hcy1taWNyb3NvZnQtY29tOnhtbC1tc3Byb3AiPg0KICA8eHM6ZWxlbWVudCBuYW1lPSJOZXdEYXRhU2V0IiBtc2RhdGE6SXNEYXRhU2V0PSJ0cnVlIiBtc2RhdGE6VXNlQ3VycmVudExvY2FsZT0idHJ1ZSI+DQogICAgPHhzOmNvbXBsZXhUeXBlPg0KICAgICAgPHhzOmNob2ljZSBtaW5PY2N1cnM9IjAiIG1heE9jY3Vycz0idW5ib3VuZGVkIj4NCiAgICAgICAgPHhzOmVsZW1lbnQgbmFtZT0iVGFibGVEYXRhIiBtc3Byb3A6QmFzZVRhYmxlLjI9IkJFRF9SRUMiIG1zcHJvcDpCYXNlVGFibGUuMD0iUFJFUEFZTUVOVF9SQ1BUIiBt
/// c3Byb3A6QmFzZVRhYmxlLjE9IlBBVF9NQVNURVJfSU5ERVgiPg0KICAgICAgICAgIDx4czpjb21wbGV4VHlwZT4NCiAgICAgICAgICAgIDx4czpzZXF1ZW5jZT4NCiAgICAgICAgICAgICAgPHhzOmVsZW1lbnQgbmFtZT0iSE9TUElUQUxfTkFNRSIgbXNkYXRhOlJlYWRPbmx5PSJ0cnVlIiBtc3Byb3A6T3JhRGJUeXBlPSIxMjYiIG1pbk9jY3Vycz0iMCI+DQogICAgICAgICAgICAgICAgPHhzOnNpbXBsZVR5cGU+DQogICAgICAgICAgICAgICAgICA8eHM6cmVzdHJpY3Rpb24gYmFzZT0ieHM6c3RyaW5nIj4NCiAgICAgICAgICAgICAgICAgICAgPHhzOm1heExlbmd0aCB2YWx1ZT0iNDAiIC8+DQogICAgICAgICAgICAgICAgICA8L3hzOnJlc3RyaWN0aW9uPg0K
/// ICAgICAgICAgICAgICAgIDwveHM6c2ltcGxlVHlwZT4NCiAgICAgICAgICAgICAgPC94czplbGVtZW50Pg0KICAgICAgICAgICAgICA8eHM6ZWxlbWVudCBuYW1lPSJQQVRJRU5UX0lEIiBtc3Byb3A6QmFzZUNvbHVtbj0iUEFUSUVOVF9JRCIgbXNwcm9wOk9yYURiVHlwZT0iMTI2IiBtaW5PY2N1cnM9IjAiPg0KICAgICAgICAgICAgICAgIDx4czpzaW1wbGVUeXBlPg0KICAgICAgICAgICAgICAgICAgPHhzOnJlc3RyaWN0aW9uIGJhc2U9InhzOnN0cmluZyI+DQogICAgICAgICAgICAgICAgICAgIDx4czptYXhMZW5ndGggdmFsdWU9IjIwIiAvPg0KICAgICAgICAgICAgICAgICAgPC94czpyZXN0cmljdGlvbj4NCiAgICAgICAgICAgICAgICA8L3hzOnNpbXBs
/// ZVR5cGU+DQogICAgICAgICAgICAgIDwveHM6ZWxlbWVudD4NCiAgICAgICAgICAgICAgPHhzOmVsZW1lbnQgbmFtZT0iTkFNRSIgbXNwcm9wOkJhc2VDb2x1bW49Ik5BTUUiIG1zcHJvcDpPcmFEYlR5cGU9IjEyNiIgbWluT2NjdXJzPSIwIj4NCiAgICAgICAgICAgICAgICA8eHM6c2ltcGxlVHlwZT4NCiAgICAgICAgICAgICAgICAgIDx4czpyZXN0cmljdGlvbiBiYXNlPSJ4czpzdHJpbmciPg0KICAgICAgICAgICAgICAgICAgICA8eHM6bWF4TGVuZ3RoIHZhbHVlPSI0MCIgLz4NCiAgICAgICAgICAgICAgICAgIDwveHM6cmVzdHJpY3Rpb24+DQogICAgICAgICAgICAgICAgPC94czpzaW1wbGVUeXBlPg0KICAgICAgICAgICAgICA8L3hzOmVsZW1lbnQ+DQog
/// ICAgICAgICAgICAgIDx4czplbGVtZW50IG5hbWU9IkFNT1VOVCIgbXNkYXRhOlJlYWRPbmx5PSJ0cnVlIiBtc3Byb3A6T3JhRGJUeXBlPSIxMDciIHR5cGU9InhzOmRlY2ltYWwiIG1pbk9jY3Vycz0iMCIgLz4NCiAgICAgICAgICAgICAgPHhzOmVsZW1lbnQgbmFtZT0iVklTSVRfSUQiIG1zcHJvcDpCYXNlQ29sdW1uPSJWSVNJVF9JRCIgbXNwcm9wOk9yYURiVHlwZT0iMTExIiB0eXBlPSJ4czpzaG9ydCIgbWluT2NjdXJzPSIwIiAvPg0KICAgICAgICAgICAgICA8eHM6ZWxlbWVudCBuYW1lPSJCRURfTEFCRUwiIG1zcHJvcDpCYXNlQ29sdW1uPSJCRURfTEFCRUwiIG1zcHJvcDpPcmFEYlR5cGU9IjEyNiIgbWluT2NjdXJzPSIwIj4NCiAgICAgICAgICAgICAg
/// ICA8eHM6c2ltcGxlVHlwZT4NCiAgICAgICAgICAgICAgICAgIDx4czpyZXN0cmljdGlvbiBiYXNlPSJ4czpzdHJpbmciPg0KICAgICAgICAgICAgICAgICAgICA8eHM6bWF4TGVuZ3RoIHZhbHVlPSI4IiAvPg0KICAgICAgICAgICAgICAgICAgPC94czpyZXN0cmljdGlvbj4NCiAgICAgICAgICAgICAgICA8L3hzOnNpbXBsZVR5cGU+DQogICAgICAgICAgICAgIDwveHM6ZWxlbWVudD4NCiAgICAgICAgICAgIDwveHM6c2VxdWVuY2U+DQogICAgICAgICAgPC94czpjb21wbGV4VHlwZT4NCiAgICAgICAgPC94czplbGVtZW50Pg0KICAgICAgICA8eHM6ZWxlbWVudCBuYW1lPSJTSU5HTEVfUk9XX1RBQkxFIj4NCiAgICAgICAgICA8eHM6Y29tcGxleFR5cGU+DQog
/// ICAgICAgICAgICA8eHM6c2VxdWVuY2U+DQogICAgICAgICAgICAgIDx4czplbGVtZW50IG5hbWU9IldBUkRfQ09ERSIgdHlwZT0ieHM6c3RyaW5nIiBtaW5PY2N1cnM9IjAiIC8+DQogICAgICAgICAgICAgIDx4czplbGVtZW50IG5hbWU9IldBUkRfTkFNRSIgdHlwZT0ieHM6c3RyaW5nIiBtaW5PY2N1cnM9IjAiIC8+DQogICAgICAgICAgICAgIDx4czplbGVtZW50IG5hbWU9IkRBVEVfQkVHSU4iIHR5cGU9InhzOnN0cmluZyIgbWluT2NjdXJzPSIwIiAvPg0KICAgICAgICAgICAgPC94czpzZXF1ZW5jZT4NCiAgICAgICAgICA8L3hzOmNvbXBsZXhUeXBlPg0KICAgICAgICA8L3hzOmVsZW1lbnQ+DQogICAgICA8L3hzOmNob2ljZT4NCiAgICA8L3hzOmNvbXBs
/// ZXhUeXBlPg0KICA8L3hzOmVsZW1lbnQ+DQo8L3hzOnNjaGVtYT4B8wRTRUxFQ1QgKHNlbGVjdCBob3NwaXRhbCBmcm9tIGhvc3BpdGFsX2NvbmZpZykgaG9zcGl0YWxfbmFtZSxhLlBBVElFTlRfSUQsICAgICAgIGMuTkFNRSwgICAgICAgc3VtKGEuQU1PVU5UKSBBTU9VTlQsICAgICAgIGEuVklTSVRfSUQsICAgICAgIGQuQkVEX0xBQkVMICBGUk9NIFBSRVBBWU1FTlRfUkNQVCBhLCBQQVRTX0lOX0hPU1BJVEFMIGIsIFBBVF9NQVNURVJfSU5ERVggYywgQkVEX1JFQyBkIHdoZXJlIGEuVFJBTlNBQ1RfVFlQRSA9ICfkuqTmrL4nICAgYW5kIChhLlJFRlVOREVEX1JDUFRfTk8gPSAnJyBvciBhLlJFRlVOREVEX1JDUFRfTk8gaXMgbnVsbCkgICBBTkQgYS5QQVRJ
/// RU5UX0lEID0gYy5QQVRJRU5UX0lEICAgQU5EIGEuUEFUSUVOVF9JRCA9IGIuUEFUSUVOVF9JRCAgIEFORCBhLlZJU0lUX0lEID0gYi5WSVNJVF9JRCAgIEFORCBiLldBUkRfQ09ERSA9IGQuV0FSRF9DT0RFICAgQU5EIGIuQkVEX05PID0gZC5CRURfTk8gICBhbmQgYi5XQVJEX0NPREUgPSB7V0FSRENPREV9IGdyb3VwIGJ5IGEuUEFUSUVOVF9JRCwgICAgICAgICAgYy5OQU1FLCAgICAgICAgICBhLlZJU0lUX0lELCAgICAgICAgICBiLkJFRF9OTywgICAgICAgICAgZC5CRURfTEFCRUwgb3JkZXIgYnkgYi5CRURfTk8=</Resource>
///   </Resources>
/// </XRTypeInfo>
namespace XtraReportSerialization {
    
    public class XtraReport : DevExpress.XtraReports.UI.XtraReport {
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.XRLabel label14;
        private DevExpress.XtraReports.UI.XRLabel label13;
        private DevExpress.XtraReports.UI.XRLabel label12;
        private DevExpress.XtraReports.UI.XRLabel label11;
        private DevExpress.XtraReports.UI.XRLabel label4;
        private DevExpress.XtraReports.UI.XRLabel label32;
        private DevExpress.XtraReports.UI.XRLabel label31;
        private DevExpress.XtraReports.UI.XRLabel label30;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.XRLabel label9;
        private DevExpress.XtraReports.UI.XRLabel label8;
        private DevExpress.XtraReports.UI.XRLabel label6;
        private DevExpress.XtraReports.UI.XRLabel label5;
        private DevExpress.XtraReports.UI.XRLabel label3;
        private DevExpress.XtraReports.UI.XRLabel label2;
        private DevExpress.XtraReports.UI.XRLabel label10;
        private DevExpress.XtraReports.UI.XRLine line1;
        private DevExpress.XtraReports.UI.XRLabel label7;
        private DevExpress.XtraReports.UI.XRLabel label1;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter;
        private DevExpress.XtraReports.UI.XRLine line4;
        private DevExpress.XtraReports.Parameters.Parameter HOSPITAL_NAME;
        private DevExpress.XtraReports.Parameters.Parameter OPER_NURSE;
        private DevExpress.XtraReports.Parameters.Parameter DEPT_NAME;
        private System.Resources.ResourceManager _resources;
        private string _resourceString;
        public XtraReport() {
            this._resourceString = DevExpress.XtraReports.Serialization.XRResourceManager.GetResourceFor("XtraReportSerialization.XtraReport");
            this.InitializeComponent();
        }
        private System.Resources.ResourceManager resources {
            get {
                if (_resources == null) {
                    this._resources = new DevExpress.XtraReports.Serialization.XRResourceManager(this._resourceString);
                }
                return this._resources;
            }
        }
        private void InitializeComponent() {
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.ReportFooter = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.label4 = new DevExpress.XtraReports.UI.XRLabel();
            this.label32 = new DevExpress.XtraReports.UI.XRLabel();
            this.label31 = new DevExpress.XtraReports.UI.XRLabel();
            this.label30 = new DevExpress.XtraReports.UI.XRLabel();
            this.label2 = new DevExpress.XtraReports.UI.XRLabel();
            this.label10 = new DevExpress.XtraReports.UI.XRLabel();
            this.line1 = new DevExpress.XtraReports.UI.XRLine();
            this.label7 = new DevExpress.XtraReports.UI.XRLabel();
            this.label1 = new DevExpress.XtraReports.UI.XRLabel();
            this.line4 = new DevExpress.XtraReports.UI.XRLine();
            this.HOSPITAL_NAME = new DevExpress.XtraReports.Parameters.Parameter();
            this.OPER_NURSE = new DevExpress.XtraReports.Parameters.Parameter();
            this.DEPT_NAME = new DevExpress.XtraReports.Parameters.Parameter();
            this.label3 = new DevExpress.XtraReports.UI.XRLabel();
            this.label5 = new DevExpress.XtraReports.UI.XRLabel();
            this.label6 = new DevExpress.XtraReports.UI.XRLabel();
            this.label8 = new DevExpress.XtraReports.UI.XRLabel();
            this.label9 = new DevExpress.XtraReports.UI.XRLabel();
            this.label11 = new DevExpress.XtraReports.UI.XRLabel();
            this.label12 = new DevExpress.XtraReports.UI.XRLabel();
            this.label13 = new DevExpress.XtraReports.UI.XRLabel();
            this.label14 = new DevExpress.XtraReports.UI.XRLabel();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // TopMargin
            // 
            this.TopMargin.HeightF = 6.333288F;
            this.TopMargin.Name = "TopMargin";
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.label14,
                        this.label13,
                        this.label12,
                        this.label11,
                        this.label4,
                        this.label32,
                        this.label31,
                        this.label30});
            this.Detail.Font = new System.Drawing.Font("Times New Roman", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Detail.HeightF = 36.45833F;
            this.Detail.Name = "Detail";
            this.Detail.StylePriority.UseFont = false;
            // 
            // BottomMargin
            // 
            this.BottomMargin.HeightF = 43.49998F;
            this.BottomMargin.Name = "BottomMargin";
            // 
            // PageHeader
            // 
            this.PageHeader.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.label9,
                        this.label8,
                        this.label6,
                        this.label5,
                        this.label3,
                        this.label2,
                        this.label10,
                        this.line1,
                        this.label7,
                        this.label1});
            this.PageHeader.HeightF = 103.5416F;
            this.PageHeader.Name = "PageHeader";
            this.PageHeader.StylePriority.UseBorders = false;
            this.PageHeader.StylePriority.UseTextAlignment = false;
            this.PageHeader.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // ReportFooter
            // 
            this.ReportFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.line4});
            this.ReportFooter.HeightF = 58.3333F;
            this.ReportFooter.Name = "ReportFooter";
            // 
            // label4
            // 
            this.label4.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label4.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[EXT_APPOINT_NO]")});
            this.label4.LocationFloat = new DevExpress.Utils.PointFloat(41.87497F, 3.999964F);
            this.label4.Multiline = true;
            this.label4.Name = "label4";
            this.label4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label4.SizeF = new System.Drawing.SizeF(99.11128F, 23F);
            this.label4.StylePriority.UseBorders = false;
            this.label4.Text = "label4";
            // 
            // label32
            // 
            this.label32.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label32.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[OPERATION_NAME]")});
            this.label32.LocationFloat = new DevExpress.Utils.PointFloat(240.0975F, 3.999996F);
            this.label32.Multiline = true;
            this.label32.Name = "label32";
            this.label32.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label32.SizeF = new System.Drawing.SizeF(181.4029F, 23F);
            this.label32.StylePriority.UseBorders = false;
            this.label32.Text = "label32";
            // 
            // label31
            // 
            this.label31.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label31.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[DEPT_NAME]")});
            this.label31.LocationFloat = new DevExpress.Utils.PointFloat(140.9863F, 3.999996F);
            this.label31.Multiline = true;
            this.label31.Name = "label31";
            this.label31.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label31.SizeF = new System.Drawing.SizeF(99.11121F, 23F);
            this.label31.StylePriority.UseBorders = false;
            this.label31.Text = "label31";
            // 
            // label30
            // 
            this.label30.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label30.LocationFloat = new DevExpress.Utils.PointFloat(9.999981F, 3.999987F);
            this.label30.Multiline = true;
            this.label30.Name = "label30";
            this.label30.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label30.SizeF = new System.Drawing.SizeF(21.87499F, 23F);
            this.label30.StylePriority.UseBorders = false;
            this.label30.StylePriority.UseTextAlignment = false;
            this.label30.Text = "●";
            this.label30.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label2
            // 
            this.label2.LocationFloat = new DevExpress.Utils.PointFloat(140.9862F, 76.99998F);
            this.label2.Multiline = true;
            this.label2.Name = "label2";
            this.label2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label2.SizeF = new System.Drawing.SizeF(99.11125F, 23F);
            this.label2.StylePriority.UseTextAlignment = false;
            this.label2.Text = "申请科室";
            this.label2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label10
            // 
            this.label10.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Parameters].[HOSPITAL_NAME]")});
            this.label10.LocationFloat = new DevExpress.Utils.PointFloat(260.162F, 0F);
            this.label10.Multiline = true;
            this.label10.Name = "label10";
            this.label10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label10.SizeF = new System.Drawing.SizeF(206.25F, 23F);
            this.label10.StylePriority.UseTextAlignment = false;
            this.label10.Text = "dffsfsf";
            this.label10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // line1
            // 
            this.line1.LocationFloat = new DevExpress.Utils.PointFloat(3.000228F, 101.4583F);
            this.line1.Name = "line1";
            this.line1.SizeF = new System.Drawing.SizeF(722.9997F, 2.083328F);
            // 
            // label7
            // 
            this.label7.LocationFloat = new DevExpress.Utils.PointFloat(41.87497F, 76.99998F);
            this.label7.Multiline = true;
            this.label7.Name = "label7";
            this.label7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label7.SizeF = new System.Drawing.SizeF(99.11125F, 23F);
            this.label7.StylePriority.UseTextAlignment = false;
            this.label7.Text = "申请单号";
            this.label7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label1
            // 
            this.label1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label1.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.LocationFloat = new DevExpress.Utils.PointFloat(3.000069F, 38.625F);
            this.label1.Name = "label1";
            this.label1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label1.SizeF = new System.Drawing.SizeF(722.9998F, 23F);
            this.label1.StylePriority.UseBorders = false;
            this.label1.StylePriority.UseFont = false;
            this.label1.StylePriority.UseTextAlignment = false;
            this.label1.Text = "外来器械申请单";
            this.label1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // line4
            // 
            this.line4.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.line4.LocationFloat = new DevExpress.Utils.PointFloat(3.861177F, 0F);
            this.line4.Name = "line4";
            this.line4.SizeF = new System.Drawing.SizeF(722.1388F, 11.54168F);
            this.line4.StylePriority.UseBorders = false;
            // 
            // HOSPITAL_NAME
            // 
            this.HOSPITAL_NAME.Description = "Parameter1";
            this.HOSPITAL_NAME.Name = "HOSPITAL_NAME";
            // 
            // OPER_NURSE
            // 
            this.OPER_NURSE.Description = "Parameter1";
            this.OPER_NURSE.Name = "OPER_NURSE";
            // 
            // DEPT_NAME
            // 
            this.DEPT_NAME.Description = "Parameter1";
            this.DEPT_NAME.Name = "DEPT_NAME";
            this.DEPT_NAME.Type = typeof(int);
            this.DEPT_NAME.ValueInfo = "0";
            // 
            // label3
            // 
            this.label3.LocationFloat = new DevExpress.Utils.PointFloat(240.0975F, 76.99998F);
            this.label3.Multiline = true;
            this.label3.Name = "label3";
            this.label3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label3.SizeF = new System.Drawing.SizeF(181.4029F, 23F);
            this.label3.StylePriority.UseTextAlignment = false;
            this.label3.Text = "手术名称";
            this.label3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label5
            // 
            this.label5.LocationFloat = new DevExpress.Utils.PointFloat(421.5004F, 76.99998F);
            this.label5.Multiline = true;
            this.label5.Name = "label5";
            this.label5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label5.SizeF = new System.Drawing.SizeF(55.3613F, 23F);
            this.label5.StylePriority.UseTextAlignment = false;
            this.label5.Text = "包名称";
            this.label5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label6
            // 
            this.label6.LocationFloat = new DevExpress.Utils.PointFloat(476.8617F, 76.99998F);
            this.label6.Multiline = true;
            this.label6.Name = "label6";
            this.label6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label6.SizeF = new System.Drawing.SizeF(60.56958F, 23F);
            this.label6.StylePriority.UseTextAlignment = false;
            this.label6.Text = "包规格";
            this.label6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label8
            // 
            this.label8.LocationFloat = new DevExpress.Utils.PointFloat(537.4313F, 76.99998F);
            this.label8.Multiline = true;
            this.label8.Name = "label8";
            this.label8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label8.SizeF = new System.Drawing.SizeF(99.11125F, 23F);
            this.label8.StylePriority.UseTextAlignment = false;
            this.label8.Text = "申请时间";
            this.label8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label9
            // 
            this.label9.LocationFloat = new DevExpress.Utils.PointFloat(636.5426F, 76.99998F);
            this.label9.Multiline = true;
            this.label9.Name = "label9";
            this.label9.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label9.SizeF = new System.Drawing.SizeF(79.45746F, 23F);
            this.label9.StylePriority.UseTextAlignment = false;
            this.label9.Text = "患者名称";
            this.label9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label11
            // 
            this.label11.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label11.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[PACK_NAME]")});
            this.label11.LocationFloat = new DevExpress.Utils.PointFloat(421.5004F, 3.458341F);
            this.label11.Multiline = true;
            this.label11.Name = "label11";
            this.label11.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label11.SizeF = new System.Drawing.SizeF(55.3613F, 23F);
            this.label11.StylePriority.UseBorders = false;
            this.label11.Text = "label32";
            // 
            // label12
            // 
            this.label12.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label12.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[PACK_SPEC]")});
            this.label12.LocationFloat = new DevExpress.Utils.PointFloat(476.8617F, 3.458341F);
            this.label12.Multiline = true;
            this.label12.Name = "label12";
            this.label12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label12.SizeF = new System.Drawing.SizeF(55.3613F, 23F);
            this.label12.StylePriority.UseBorders = false;
            this.label12.Text = "label32";
            // 
            // label13
            // 
            this.label13.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label13.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[APPOINT_DATE]")});
            this.label13.LocationFloat = new DevExpress.Utils.PointFloat(537.4313F, 3.458341F);
            this.label13.Multiline = true;
            this.label13.Name = "label13";
            this.label13.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label13.SizeF = new System.Drawing.SizeF(99.11133F, 23F);
            this.label13.StylePriority.UseBorders = false;
            this.label13.Text = "label32";
            // 
            // label14
            // 
            this.label14.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label14.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[NAME]")});
            this.label14.LocationFloat = new DevExpress.Utils.PointFloat(636.5427F, 3.999996F);
            this.label14.Multiline = true;
            this.label14.Name = "label14";
            this.label14.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label14.SizeF = new System.Drawing.SizeF(79.45734F, 23F);
            this.label14.StylePriority.UseBorders = false;
            this.label14.Text = "label32";
            // 
            // XtraReport
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
                        this.TopMargin,
                        this.Detail,
                        this.BottomMargin,
                        this.PageHeader,
                        this.ReportFooter});
            this.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.DataSourceSchema = resources.GetString("$this.DataSourceSchema");
            this.Margins = new System.Drawing.Printing.Margins(50, 51, 6, 43);
            this.Name = "XtraReport";
            this.PageHeight = 1169;
            this.PageWidth = 827;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.Parameters.AddRange(new DevExpress.XtraReports.Parameters.Parameter[] {
                        this.HOSPITAL_NAME,
                        this.OPER_NURSE,
                        this.DEPT_NAME});
            this.ShowPrintMarginsWarning = false;
            this.Tag = resources.GetString("$this.Tag");
            this.Version = "18.1";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();
        }
    }
}
