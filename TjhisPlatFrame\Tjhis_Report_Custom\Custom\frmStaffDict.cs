﻿using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Windows.Forms;
using Tjhis.Report.Custom.Srv;

namespace Tjhis.Report.Custom.Custom
{
    /// <summary>
    /// 选择员工
    /// </summary>
    public partial class frmStaffDict : ParentForm
    {
        srvStatisticalQuery srv;
        DataSet dsDeptDict;
        DataTable dtStaffDict;

        string deptFilter; //科室过滤
        string staffFilter;//人员过滤
        string displayFilter;//显示过滤
        string _appName = string.Empty;
        public string usersArray { get; set; }

        public frmStaffDict(string appName = "")
        {
            InitializeComponent();
            _appName = appName;
        }

        /// <summary>
        /// 加载事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void frmStaffDict_Load(object sender, EventArgs e)
        {
            srv = new srvStatisticalQuery();
            InitData(_appName);

            SetSelectStaff();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        void InitData(string appName)
        {
            dsDeptDict = srv.GetDeptDictByHisUnitCode();//20211018-modify
            //dtStaffDict = srv.GetStaffDict().Tables[0];
            dtStaffDict = srv.GetUserDict(appName).Tables[0];
            

            lookUpEdit1.Properties.DataSource = dsDeptDict.Tables[0];
            lookUpEdit1.EditValue = "*";

            dtStaffDict.Columns["COL_SEL"].ReadOnly = false;
            gridControl1.DataSource = dtStaffDict.DefaultView;
        }

        /// <summary>
        /// 设置已经选择过的人员
        /// </summary>
        void SetSelectStaff()
        {
            if (string.IsNullOrEmpty(usersArray)) return;

            string[] users = usersArray.Split(',');
            List<DataRow> drUsers = dtStaffDict.Select().ToList() ;
            //users.ToList().ForEach();
            foreach (string user in users)
            {
                DataRow drUser = drUsers.Find(dr => dr["emp_no"].Equals(user));
                if(drUser != null)
                {
                    drUser["COL_SEL"] = "1";
                }
            }
        }

        /// <summary>
        /// 获取选中员工的字符串
        /// </summary>
        /// <returns></returns>
        string GetSelectStaff()
        {
            usersArray = "";

            List<DataRow> drUsers = dtStaffDict.Select("col_sel = '1'").ToList();
            drUsers.ForEach(dr => usersArray += dr["emp_no"]+",");

            return usersArray;
        }

        /// <summary>
        /// 科室代码切换
        /// </summary>
        void DeptChange()
        {
            if (lookUpEdit1.EditValue.ToString().Equals("*"))
                deptFilter = "";
            else
                deptFilter = " and dept_code = '"+lookUpEdit1.EditValue.ToString()+"'";
            SetFilter();
        }

        /// <summary>
        /// 人员过滤
        /// </summary>
        /// <param name="str"></param>
        void TxtStaffChanging(string str)
        {
            if (string.IsNullOrEmpty(str))
                staffFilter = "";
            else
                staffFilter = " and (emp_no like '%" + str + "%' or input_code like '%" + str + "%' or name like '%" + str +"%')";
            SetFilter();
        }
        /// <summary>
        /// [查询]按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void simpleButton1_Click(object sender, EventArgs e)
        {
            TxtStaffChanging(textEdit1.Text);
        }

        /// <summary>
        /// 更换科室
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lookUpEdit1_EditValueChanged(object sender, EventArgs e)
        {
            DeptChange();
        }

        /// <summary>
        /// 确认并退出当前窗体
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void simpleButton2_Click(object sender, EventArgs e)
        {
            GetSelectStaff();
            this.DialogResult = DialogResult.OK;
        }

        /// <summary>
        /// 取消并退出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void simpleButton3_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        /// <summary>
        /// 显示已选择的人员数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void cbDisplayChecks_CheckedChanged(object sender, EventArgs e)
        {
            if (cbDisplayChecks.Checked)
            {
                displayFilter = " and col_sel = '1'";
            }
            else
            {
                displayFilter = "";
            }
            SetFilter();
        }

        /// <summary>
        /// 设置过滤条件
        /// </summary>
        void SetFilter()
        {
            dtStaffDict.DefaultView.RowFilter = " 1=1" + deptFilter + staffFilter + displayFilter ;
        }

        /// <summary>
        /// 回车事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void textEdit1_KeyDown(object sender, KeyEventArgs e)
        {
            if(e.KeyCode == Keys.Enter)
            {
                TxtStaffChanging(textEdit1.Text);
            }
        }
    }
}
