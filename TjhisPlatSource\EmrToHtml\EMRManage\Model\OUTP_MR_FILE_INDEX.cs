﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;

namespace Model
{
    [DataContract]
    public class OUTP_MR_FILE_INDEX
    {
        /// <summary>
        /// 实体类OUTP_MR_FILE_INDEX 。(属性说明自动提取数据库字段的描述信息)
        /// </summary>


        public OUTP_MR_FILE_INDEX()
        { }
        #region Model
        /// <summary>
        /// 就诊日期
        /// </summary>
        [DataMember]
        public DateTime VISIT_DATE
        {
            set;
            get;
        }
        /// <summary>
        /// 就诊序号
        /// </summary>
        [DataMember]
        public int VISIT_NO
        {
            set;
            get;
        }
        /// <summary>
        /// 文件序号
        /// </summary>
        [DataMember]
        public int FILE_NO
        {
            set;
            get;
        }
        /// <summary>
        /// 病人ID
        /// </summary>
        [DataMember]
        public string PATIENT_ID
        {
            set;
            get;
        }
        /// <summary>
        /// 文件名
        /// </summary>
        [DataMember]
        public string FILE_NAME
        {
            set;
            get;
        }
        /// <summary>
        /// 主题
        /// </summary>
        [DataMember]
        public string TOPIC
        {
            set;
            get;
        }
        /// <summary>
        /// 创建者姓名
        /// </summary>
        [DataMember]
        public string CREATOR_NAME
        {
            set;
            get;
        }
        /// <summary>
        /// 创建者ID
        /// </summary>
        [DataMember]
        public string CREATOR_ID
        {
            set;
            get;
        }
        /// <summary>
        /// 创建时间
        /// </summary>
        [DataMember]
        public DateTime CREATE_DATE_TIME
        {
            set;
            get;
        }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        [DataMember]
        public DateTime? LAST_MODIFY_DATE_TIME
        {
            set;
            get;
        }
        /// <summary>
        /// 病历模板编码
        /// </summary>
        [DataMember]
        public string MR_CODE
        {
            set;
            get;
        }
        [DataMember]
        public string FILE_ATTR
        {
            set;
            get;
        }


        [DataMember]
        public string FILE_FLAG
        {
            set;
            get;
        }


        /// <summary>
        /// 实习医生id
        /// </summary>
        [DataMember]
        public string STUDY_DOCTOR_ID { get; set; }
        /// <summary>
        /// 实习医生姓名
        /// </summary>
        [DataMember]
        public string STUDY_DOCTOR_NAME { get; set; }
        [DataMember]
        public int ORDINAL { get; set; }
        #endregion Model

    }
}
