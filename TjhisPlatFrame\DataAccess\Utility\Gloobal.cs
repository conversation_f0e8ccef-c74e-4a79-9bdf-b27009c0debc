﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Utility
{
    public class Gloobal
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="EncryptPwd"></param>
        /// <returns></returns>
        public static String DecryptHIS(String EncryptPwd)
        {
            String ret = "";
            int k = 0;
            String lsstr2;
            if (String.IsNullOrEmpty(EncryptPwd))
            {
                return ret;
            }
            for (int i = 0; i < EncryptPwd.Length; i++)
            {
                lsstr2 = EncryptPwd.Substring(i, 1);
                k = Convert.ToByte(char.Parse(lsstr2));
                if ((i % 2) != 0)
                {
                    k = k - i - 1 + 32;
                }
                else
                {
                    k = k + i + 1 - 8;
                }

                ret = ret + Convert.ToChar(k);
            }

            return ret;

        }

        public static String EncryptHIS(String mingWen)
        {
            String ret = "";
            int k;
            String lsstr2;
            if (String.IsNullOrEmpty(mingWen))
            {
                return ret;
            }
            mingWen = mingWen.Trim();
            for (int i = 0; i < mingWen.Length; i++)
            {
                lsstr2 = mingWen.Substring(i, 1);
                if ((i % 2) != 0)
                {
                    k = Convert.ToByte(char.Parse(lsstr2)) + i + 1 - 32;
                }
                else
                {
                    k = Convert.ToByte(char.Parse(lsstr2)) - i - 1 + 8;
                }

                ret = ret + Convert.ToChar(k);
            }

            return ret;

        }
        /// <summary>
        /// 设置初始化数据库新连接方式的全局连接
        /// </summary>
        public static void SetOracleClientConnection()
        {
            if (UntilityConstant.DBConnectionMode == 0)
            {
                //ORACLECLIENT连接方式,初始化全局的连接变量
                UntilityConstant.Connection = new System.Data.OracleClient.OracleConnection(UntilityConstant.DataConnectionString); 
            }
        }
    }
}
