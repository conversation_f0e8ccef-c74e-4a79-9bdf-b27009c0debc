﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using TJ.EMR.Utility;

namespace TJ_EMR_Business
{
    public class MRFile : FileTrans
    {

        /// <summary>
        /// 下载电子病历文件,只需要指定下载模板文件的的服务器相对路径，不需要指定服务器完整路径
        /// </summary>
        /// <param name="RemoteFile">服务器文件完全路径</param>
        /// <param name="LocalFile">本地文件完全路径</param>
        /// <param name="option"></param>
        /// <returns>0 成功</returns>
        public static int GetMRFile(string RemoteFile, string LocalFile, int Option)
        {
            string selectsql = "select * from mr_work_path";
            DataSet dataSet = DBSettingClass.GetDataSet(selectsql, "mr_work_path");
            if (dataSet != null && dataSet.Tables[0].Rows.Count > 0)
            {
                DataRow dr = dataSet.Tables[0].Rows[0];
                string HostAddr = dr["IP_ADDR"].ToString();
                string MR_PATH = dr["MR_PATH"].ToString();
                string File_User = dr["FILE_USER"].ToString();
                string File_Pwd = dr["FILE_PWD"].ToString();
                string SERVER_TYPE = dr["SERVER_TYPE"].ToString();
                return GetFile(HostAddr, File_User, File_Pwd, MR_PATH + @"\" + RemoteFile, LocalFile, Option, TransType(SERVER_TYPE));
            }
            else
            {
                return 0;
            }
        }

        /// <summary>
        /// 获取病历文件
        /// </summary>
        /// <param name="PatientID"></param>
        /// <param name="filename"></param>
        /// <param name="LocalFile"></param>
        /// <returns></returns>
        public static int GetMRFileByFileName(string PatientID, string filename, string LocalFile, DataSet dataSet)
        {
            string HostAddr = "";
            string File_User = "";
            string File_Pwd = "";
            string TEMPLET_PATH = string.Empty;
            string SERVER_TYPE = string.Empty;
            if (dataSet != null && dataSet.Tables[0].Rows.Count > 0)
            {
                DataRow dr = dataSet.Tables[0].Rows[0];
                HostAddr = dr["IP_ADDR"].ToString();
                File_User = dr["FILE_USER"].ToString();
                File_Pwd = dr["FILE_PWD"].ToString();
                TEMPLET_PATH = dr["MR_PATH"].ToString();
                SERVER_TYPE = dr["SERVER_TYPE"].ToString();
            }
            string strFile = HostAddr + "|" + TEMPLET_PATH + @"\" + GetPathbyPatientID(PatientID) + filename + "|" + LocalFile + "&" + TransType(SERVER_TYPE).ToString();;
            return GetFile(HostAddr, File_User, File_Pwd, TEMPLET_PATH + @"\" + GetPathbyPatientID(PatientID) + filename, LocalFile, 1, TransType(SERVER_TYPE));
        }
        /// <summary>
        /// 截取patientid前8位生成病历文件路径
        /// </summary>
        private static string GetPathbyPatientID(string PatientID)
        {
            Char padLeftChar = '0';
            string padLeftStr = string.Empty;
            if (!string.IsNullOrEmpty(padLeftStr))
                padLeftChar = padLeftStr[0];
            if (PatientID.Length < 2)
                PatientID = PatientID.PadLeft(2, padLeftChar);
            return PatientID.Substring(PatientID.Length - 2, 2) + @"\" + PatientID.Remove(PatientID.Length - 2).PadLeft(8, padLeftChar) + @"\";
        }
        private static int TransType(string SERVER_TYPE)
        {
            int Type = 0;
            if (SERVER_TYPE == "FILESVR")
            {
                Type = 1;
            }
            else if (SERVER_TYPE == "FTSERVER")
            {
                Type = 3;
            }
            return Type;
        }
    }
}
