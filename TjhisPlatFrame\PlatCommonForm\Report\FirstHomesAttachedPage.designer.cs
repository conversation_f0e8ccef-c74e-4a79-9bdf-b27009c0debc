﻿namespace PlatCommonForm.Report
{
    partial class FirstHomesAttachedPage
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_deptname = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel18 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_InpNo = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_bedno = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_name = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_sex = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel10 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_age = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SFZRYBR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel9 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_BCRYYSCRYJG = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel12 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel11 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ZRYYY = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel14 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel13 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ZRYBQFX = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel16 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel23 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_RYHQZRQ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel15 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ZYZDLX = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel19 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel17 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ZDFUQK_MZYCY = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel21 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ZDFUQK_RYYCY = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel24 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ZDFHQK_SQYSH = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel26 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ZDFUQK_LCYBL = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel28 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel29 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ZDFUQK_FSXYBL = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel20 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_QJQKQJCS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel25 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_QJCGCS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel30 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel22 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel27 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SYQK = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel32 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SYFY = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel34 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel35 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SXQK = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel37 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel38 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SXFY = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel40 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel31 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel33 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_WZBL = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel39 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_YNBL = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel42 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel41 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SS_ZL_JC_ZD = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel44 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel43 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_LCLJBL = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel46 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel47 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_LCLJWCQK = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel49 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel50 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel51 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_DBZZLKZBL = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel45 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_XGJBZDFZ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel52 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel48 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel53 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SFZSBR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel55 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel56 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SFWCSS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel54 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SSXZ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel58 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel59 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel60 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SSQBRZYC = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel57 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel61 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SSLB = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel62 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_MZFJ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel64 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel65 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel66 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_MZBFZ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel63 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SSSFDYFDSS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel68 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel67 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SSSFYBFZ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel70 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel69 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SSBFZMC = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel71 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SSBWSFGR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel73 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel74 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_GRBW = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel76 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel72 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SZBDJC = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel77 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel75 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel78 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SSBDYZLBLZD = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel79 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel80 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_YWZSSJH = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel81 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_FJHZSS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel83 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel84 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SSGCZSFYYWYL = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel86 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel82 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SFYYYXSH = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel87 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel85 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel88 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_YYXSHLX_SFSW = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel90 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel91 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_YYXSH_SWYY = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel89 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel92 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_CKHZCY_SFFSCS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel94 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel93 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel95 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_CKHZCY_FMFS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel96 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel97 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_CKHZCY_HCYE = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel98 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel99 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_CKHZCY_XSESFFSCS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel100 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel101 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ZLHZCY_SFZL = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel103 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel102 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel104 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ZLHZCY_ZGZDYJ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel106 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ZLHZCY_ZLFQ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel108 = new DevExpress.XtraReports.UI.XRLabel();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.HeightF = 5.208333F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // TopMargin
            // 
            this.TopMargin.HeightF = 28.04166F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // BottomMargin
            // 
            this.BottomMargin.HeightF = 0F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // PageHeader
            // 
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrL_ZLHZCY_ZLFQ,
            this.xrLabel108,
            this.xrLabel106,
            this.xrLabel104,
            this.xrL_ZLHZCY_ZGZDYJ,
            this.xrLabel102,
            this.xrLabel101,
            this.xrL_ZLHZCY_SFZL,
            this.xrLabel103,
            this.xrLabel100,
            this.xrLabel99,
            this.xrL_CKHZCY_XSESFFSCS,
            this.xrLabel98,
            this.xrLabel97,
            this.xrL_CKHZCY_HCYE,
            this.xrLabel96,
            this.xrLabel95,
            this.xrL_CKHZCY_FMFS,
            this.xrLabel93,
            this.xrLabel92,
            this.xrL_CKHZCY_SFFSCS,
            this.xrLabel94,
            this.xrLabel89,
            this.xrLabel91,
            this.xrL_YYXSH_SWYY,
            this.xrLabel90,
            this.xrLabel88,
            this.xrL_YYXSHLX_SFSW,
            this.xrLabel85,
            this.xrLabel82,
            this.xrL_SFYYYXSH,
            this.xrLabel87,
            this.xrL_SSGCZSFYYWYL,
            this.xrLabel86,
            this.xrLabel84,
            this.xrL_FJHZSS,
            this.xrLabel83,
            this.xrLabel81,
            this.xrLabel80,
            this.xrL_YWZSSJH,
            this.xrLabel79,
            this.xrLabel78,
            this.xrL_SSBDYZLBLZD,
            this.xrLabel75,
            this.xrLabel72,
            this.xrL_SZBDJC,
            this.xrLabel77,
            this.xrLabel74,
            this.xrL_GRBW,
            this.xrLabel76,
            this.xrLabel71,
            this.xrL_SSBWSFGR,
            this.xrLabel73,
            this.xrL_SSBFZMC,
            this.xrLabel69,
            this.xrL_SSSFYBFZ,
            this.xrLabel70,
            this.xrLabel67,
            this.xrL_SSSFDYFDSS,
            this.xrLabel68,
            this.xrLabel63,
            this.xrLabel66,
            this.xrL_MZBFZ,
            this.xrLabel65,
            this.xrLabel62,
            this.xrL_MZFJ,
            this.xrLabel64,
            this.xrLabel61,
            this.xrL_SSLB,
            this.xrLabel57,
            this.xrLabel60,
            this.xrL_SSQBRZYC,
            this.xrLabel54,
            this.xrL_SSXZ,
            this.xrLabel58,
            this.xrLabel59,
            this.xrLabel55,
            this.xrLabel56,
            this.xrL_SFWCSS,
            this.xrLabel48,
            this.xrLabel53,
            this.xrL_SFZSBR,
            this.xrL_XGJBZDFZ,
            this.xrLabel52,
            this.xrLabel45,
            this.xrLabel50,
            this.xrLabel51,
            this.xrL_DBZZLKZBL,
            this.xrLabel43,
            this.xrL_LCLJBL,
            this.xrLabel46,
            this.xrLabel47,
            this.xrL_LCLJWCQK,
            this.xrLabel49,
            this.xrL_SS_ZL_JC_ZD,
            this.xrLabel44,
            this.xrLabel41,
            this.xrLabel39,
            this.xrL_YNBL,
            this.xrLabel42,
            this.xrLabel33,
            this.xrL_WZBL,
            this.xrLabel31,
            this.xrLabel38,
            this.xrL_SXFY,
            this.xrLabel40,
            this.xrLabel35,
            this.xrL_SXQK,
            this.xrLabel37,
            this.xrLabel32,
            this.xrL_SYFY,
            this.xrLabel34,
            this.xrLabel27,
            this.xrL_SYQK,
            this.xrLabel22,
            this.xrLabel30,
            this.xrL_QJCGCS,
            this.xrLabel25,
            this.xrLabel20,
            this.xrL_QJQKQJCS,
            this.xrLabel29,
            this.xrL_ZDFUQK_FSXYBL,
            this.xrL_ZDFUQK_LCYBL,
            this.xrLabel28,
            this.xrL_ZDFHQK_SQYSH,
            this.xrLabel26,
            this.xrL_ZDFUQK_RYYCY,
            this.xrLabel24,
            this.xrL_ZDFUQK_MZYCY,
            this.xrLabel21,
            this.xrLabel17,
            this.xrLabel15,
            this.xrL_ZYZDLX,
            this.xrLabel19,
            this.xrLabel23,
            this.xrL_RYHQZRQ,
            this.xrLabel13,
            this.xrL_ZRYBQFX,
            this.xrLabel16,
            this.xrLabel11,
            this.xrL_ZRYYY,
            this.xrLabel14,
            this.xrL_BCRYYSCRYJG,
            this.xrLabel12,
            this.xrLabel9,
            this.xrLabel6,
            this.xrL_SFZRYBR,
            this.xrLabel5,
            this.xrLabel3,
            this.xrLabel10,
            this.xrL_age,
            this.xrLabel8,
            this.xrL_sex,
            this.xrL_name,
            this.xrLabel7,
            this.xrL_bedno,
            this.xrLabel4,
            this.xrLabel1,
            this.xrL_InpNo,
            this.xrL_deptname,
            this.xrLabel18,
            this.xrLabel2});
            this.PageHeader.HeightF = 1067.708F;
            this.PageHeader.Name = "PageHeader";
            // 
            // xrLabel2
            // 
            this.xrLabel2.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel2.Font = new System.Drawing.Font("黑体", 16F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(0F, 27.70834F);
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(757.0001F, 29F);
            this.xrLabel2.StylePriority.UseBorders = false;
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            this.xrLabel2.Text = "住院病案首页附页 (一)";
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrL_deptname
            // 
            this.xrL_deptname.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_deptname.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_deptname.LocationFloat = new DevExpress.Utils.PointFloat(51.62493F, 72.33334F);
            this.xrL_deptname.Name = "xrL_deptname";
            this.xrL_deptname.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_deptname.SizeF = new System.Drawing.SizeF(121.5417F, 26F);
            this.xrL_deptname.StylePriority.UseBorders = false;
            this.xrL_deptname.StylePriority.UseFont = false;
            this.xrL_deptname.StylePriority.UsePadding = false;
            this.xrL_deptname.StylePriority.UseTextAlignment = false;
            this.xrL_deptname.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_deptname.WordWrap = false;
            // 
            // xrLabel18
            // 
            this.xrLabel18.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel18.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel18.LocationFloat = new DevExpress.Utils.PointFloat(0F, 72.33334F);
            this.xrLabel18.Name = "xrLabel18";
            this.xrLabel18.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel18.SizeF = new System.Drawing.SizeF(51.625F, 26F);
            this.xrLabel18.StylePriority.UseBorders = false;
            this.xrLabel18.StylePriority.UseFont = false;
            this.xrLabel18.StylePriority.UsePadding = false;
            this.xrLabel18.StylePriority.UseTextAlignment = false;
            this.xrLabel18.Text = "科室：";
            this.xrLabel18.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel18.WordWrap = false;
            // 
            // xrLabel1
            // 
            this.xrLabel1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel1.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(179.4999F, 72.33334F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(59.95834F, 26F);
            this.xrLabel1.StylePriority.UseBorders = false;
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UsePadding = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "住院号：";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel1.WordWrap = false;
            // 
            // xrL_InpNo
            // 
            this.xrL_InpNo.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_InpNo.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_InpNo.LocationFloat = new DevExpress.Utils.PointFloat(239.4583F, 72.33334F);
            this.xrL_InpNo.Name = "xrL_InpNo";
            this.xrL_InpNo.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_InpNo.SizeF = new System.Drawing.SizeF(72.58334F, 26.00001F);
            this.xrL_InpNo.StylePriority.UseBorders = false;
            this.xrL_InpNo.StylePriority.UseFont = false;
            this.xrL_InpNo.StylePriority.UsePadding = false;
            this.xrL_InpNo.StylePriority.UseTextAlignment = false;
            this.xrL_InpNo.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_InpNo.WordWrap = false;
            // 
            // xrLabel4
            // 
            this.xrLabel4.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel4.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel4.LocationFloat = new DevExpress.Utils.PointFloat(312.0416F, 72.33334F);
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel4.SizeF = new System.Drawing.SizeF(45.375F, 26.00001F);
            this.xrLabel4.StylePriority.UseBorders = false;
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UsePadding = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            this.xrLabel4.Text = "床号：";
            this.xrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel4.WordWrap = false;
            // 
            // xrL_bedno
            // 
            this.xrL_bedno.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_bedno.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_bedno.LocationFloat = new DevExpress.Utils.PointFloat(357.4166F, 72.33334F);
            this.xrL_bedno.Name = "xrL_bedno";
            this.xrL_bedno.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_bedno.SizeF = new System.Drawing.SizeF(39.25003F, 26.00001F);
            this.xrL_bedno.StylePriority.UseBorders = false;
            this.xrL_bedno.StylePriority.UseFont = false;
            this.xrL_bedno.StylePriority.UsePadding = false;
            this.xrL_bedno.StylePriority.UseTextAlignment = false;
            this.xrL_bedno.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_bedno.WordWrap = false;
            // 
            // xrL_name
            // 
            this.xrL_name.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_name.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_name.LocationFloat = new DevExpress.Utils.PointFloat(442.0417F, 72.33332F);
            this.xrL_name.Name = "xrL_name";
            this.xrL_name.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_name.SizeF = new System.Drawing.SizeF(101.75F, 26.00001F);
            this.xrL_name.StylePriority.UseBorders = false;
            this.xrL_name.StylePriority.UseFont = false;
            this.xrL_name.StylePriority.UsePadding = false;
            this.xrL_name.StylePriority.UseTextAlignment = false;
            this.xrL_name.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_name.WordWrap = false;
            // 
            // xrLabel7
            // 
            this.xrLabel7.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel7.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel7.LocationFloat = new DevExpress.Utils.PointFloat(396.6667F, 72.33332F);
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel7.SizeF = new System.Drawing.SizeF(45.375F, 26.00001F);
            this.xrLabel7.StylePriority.UseBorders = false;
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UsePadding = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            this.xrLabel7.Text = "姓名：";
            this.xrLabel7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel7.WordWrap = false;
            // 
            // xrLabel8
            // 
            this.xrLabel8.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel8.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel8.LocationFloat = new DevExpress.Utils.PointFloat(564.9167F, 72.33334F);
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel8.SizeF = new System.Drawing.SizeF(45.375F, 26.00001F);
            this.xrLabel8.StylePriority.UseBorders = false;
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UsePadding = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            this.xrLabel8.Text = "性别：";
            this.xrLabel8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel8.WordWrap = false;
            // 
            // xrL_sex
            // 
            this.xrL_sex.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_sex.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_sex.LocationFloat = new DevExpress.Utils.PointFloat(610.2917F, 72.33334F);
            this.xrL_sex.Name = "xrL_sex";
            this.xrL_sex.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_sex.SizeF = new System.Drawing.SizeF(39.25006F, 26.00001F);
            this.xrL_sex.StylePriority.UseBorders = false;
            this.xrL_sex.StylePriority.UseFont = false;
            this.xrL_sex.StylePriority.UsePadding = false;
            this.xrL_sex.StylePriority.UseTextAlignment = false;
            this.xrL_sex.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel10
            // 
            this.xrLabel10.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel10.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel10.LocationFloat = new DevExpress.Utils.PointFloat(657.625F, 72.33334F);
            this.xrLabel10.Name = "xrLabel10";
            this.xrLabel10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel10.SizeF = new System.Drawing.SizeF(45.375F, 26.00001F);
            this.xrLabel10.StylePriority.UseBorders = false;
            this.xrLabel10.StylePriority.UseFont = false;
            this.xrLabel10.StylePriority.UsePadding = false;
            this.xrLabel10.StylePriority.UseTextAlignment = false;
            this.xrLabel10.Text = "年龄：";
            this.xrLabel10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrL_age
            // 
            this.xrL_age.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_age.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_age.LocationFloat = new DevExpress.Utils.PointFloat(703F, 72.33334F);
            this.xrL_age.Name = "xrL_age";
            this.xrL_age.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_age.SizeF = new System.Drawing.SizeF(39.25006F, 26.00001F);
            this.xrL_age.StylePriority.UseBorders = false;
            this.xrL_age.StylePriority.UseFont = false;
            this.xrL_age.StylePriority.UsePadding = false;
            this.xrL_age.StylePriority.UseTextAlignment = false;
            this.xrL_age.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel3
            // 
            this.xrLabel3.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel3.Font = new System.Drawing.Font("黑体", 10F, System.Drawing.FontStyle.Bold);
            this.xrLabel3.LocationFloat = new DevExpress.Utils.PointFloat(0F, 98.33333F);
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel3.SizeF = new System.Drawing.SizeF(219.3333F, 25.99998F);
            this.xrLabel3.StylePriority.UseBorders = false;
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UsePadding = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            this.xrLabel3.Text = "一、出院时医生填写";
            this.xrLabel3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel3.WordWrap = false;
            // 
            // xrLabel5
            // 
            this.xrLabel5.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel5.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel5.LocationFloat = new DevExpress.Utils.PointFloat(0F, 124.3333F);
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel5.SizeF = new System.Drawing.SizeF(126.625F, 25.99999F);
            this.xrLabel5.StylePriority.UseBorders = false;
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UsePadding = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            this.xrLabel5.Text = "1、是否再入院病人";
            this.xrLabel5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel5.WordWrap = false;
            // 
            // xrL_SFZRYBR
            // 
            this.xrL_SFZRYBR.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SFZRYBR.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SFZRYBR.LocationFloat = new DevExpress.Utils.PointFloat(126.625F, 124.3333F);
            this.xrL_SFZRYBR.Name = "xrL_SFZRYBR";
            this.xrL_SFZRYBR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SFZRYBR.SizeF = new System.Drawing.SizeF(28.00002F, 25.99999F);
            this.xrL_SFZRYBR.StylePriority.UseBorders = false;
            this.xrL_SFZRYBR.StylePriority.UseFont = false;
            this.xrL_SFZRYBR.StylePriority.UsePadding = false;
            this.xrL_SFZRYBR.StylePriority.UseTextAlignment = false;
            this.xrL_SFZRYBR.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SFZRYBR.WordWrap = false;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel6.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel6.LocationFloat = new DevExpress.Utils.PointFloat(158.6666F, 124.3333F);
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel6.SizeF = new System.Drawing.SizeF(80.7917F, 25.99999F);
            this.xrLabel6.StylePriority.UseBorders = false;
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UsePadding = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            this.xrLabel6.Text = "1.是  2.否";
            this.xrLabel6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel6.WordWrap = false;
            // 
            // xrLabel9
            // 
            this.xrLabel9.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel9.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel9.LocationFloat = new DevExpress.Utils.PointFloat(260.75F, 124.3333F);
            this.xrLabel9.Name = "xrLabel9";
            this.xrLabel9.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel9.SizeF = new System.Drawing.SizeF(152.125F, 25.99999F);
            this.xrLabel9.StylePriority.UseBorders = false;
            this.xrLabel9.StylePriority.UseFont = false;
            this.xrLabel9.StylePriority.UsePadding = false;
            this.xrLabel9.StylePriority.UseTextAlignment = false;
            this.xrLabel9.Text = "本次入院与上次入院间隔";
            this.xrLabel9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel9.WordWrap = false;
            // 
            // xrL_BCRYYSCRYJG
            // 
            this.xrL_BCRYYSCRYJG.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_BCRYYSCRYJG.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_BCRYYSCRYJG.LocationFloat = new DevExpress.Utils.PointFloat(414.0417F, 124.3333F);
            this.xrL_BCRYYSCRYJG.Name = "xrL_BCRYYSCRYJG";
            this.xrL_BCRYYSCRYJG.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_BCRYYSCRYJG.SizeF = new System.Drawing.SizeF(28F, 25.99998F);
            this.xrL_BCRYYSCRYJG.StylePriority.UseBorders = false;
            this.xrL_BCRYYSCRYJG.StylePriority.UseFont = false;
            this.xrL_BCRYYSCRYJG.StylePriority.UsePadding = false;
            this.xrL_BCRYYSCRYJG.StylePriority.UseTextAlignment = false;
            this.xrL_BCRYYSCRYJG.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_BCRYYSCRYJG.WordWrap = false;
            // 
            // xrLabel12
            // 
            this.xrLabel12.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel12.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel12.LocationFloat = new DevExpress.Utils.PointFloat(454.2917F, 124.3333F);
            this.xrLabel12.Name = "xrLabel12";
            this.xrLabel12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel12.SizeF = new System.Drawing.SizeF(249.5834F, 25.99999F);
            this.xrLabel12.StylePriority.UseBorders = false;
            this.xrLabel12.StylePriority.UseFont = false;
            this.xrLabel12.StylePriority.UsePadding = false;
            this.xrLabel12.StylePriority.UseTextAlignment = false;
            this.xrLabel12.Text = "1.当天 2. 2-15天  3. 16-31天 4. >31天";
            this.xrLabel12.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel12.WordWrap = false;
            // 
            // xrLabel11
            // 
            this.xrLabel11.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel11.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel11.LocationFloat = new DevExpress.Utils.PointFloat(158.6666F, 150.3333F);
            this.xrLabel11.Name = "xrLabel11";
            this.xrLabel11.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel11.SizeF = new System.Drawing.SizeF(246F, 25.99998F);
            this.xrLabel11.StylePriority.UseBorders = false;
            this.xrLabel11.StylePriority.UseFont = false;
            this.xrLabel11.StylePriority.UsePadding = false;
            this.xrLabel11.StylePriority.UseTextAlignment = false;
            this.xrLabel11.Text = "1.与上次同一疾病  2.其他疾病";
            this.xrLabel11.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel11.WordWrap = false;
            // 
            // xrL_ZRYYY
            // 
            this.xrL_ZRYYY.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ZRYYY.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ZRYYY.LocationFloat = new DevExpress.Utils.PointFloat(126.625F, 150.3333F);
            this.xrL_ZRYYY.Name = "xrL_ZRYYY";
            this.xrL_ZRYYY.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ZRYYY.SizeF = new System.Drawing.SizeF(28F, 25.99998F);
            this.xrL_ZRYYY.StylePriority.UseBorders = false;
            this.xrL_ZRYYY.StylePriority.UseFont = false;
            this.xrL_ZRYYY.StylePriority.UsePadding = false;
            this.xrL_ZRYYY.StylePriority.UseTextAlignment = false;
            this.xrL_ZRYYY.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ZRYYY.WordWrap = false;
            // 
            // xrLabel14
            // 
            this.xrLabel14.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel14.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel14.LocationFloat = new DevExpress.Utils.PointFloat(0F, 150.3333F);
            this.xrLabel14.Name = "xrLabel14";
            this.xrLabel14.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel14.SizeF = new System.Drawing.SizeF(126.625F, 25.99998F);
            this.xrLabel14.StylePriority.UseBorders = false;
            this.xrLabel14.StylePriority.UseFont = false;
            this.xrLabel14.StylePriority.UsePadding = false;
            this.xrLabel14.StylePriority.UseTextAlignment = false;
            this.xrLabel14.Text = "2、再入院原因";
            this.xrLabel14.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel14.WordWrap = false;
            // 
            // xrLabel13
            // 
            this.xrLabel13.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel13.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel13.LocationFloat = new DevExpress.Utils.PointFloat(0F, 176.3333F);
            this.xrLabel13.Name = "xrLabel13";
            this.xrLabel13.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel13.SizeF = new System.Drawing.SizeF(126.625F, 25.99998F);
            this.xrLabel13.StylePriority.UseBorders = false;
            this.xrLabel13.StylePriority.UseFont = false;
            this.xrLabel13.StylePriority.UsePadding = false;
            this.xrLabel13.StylePriority.UseTextAlignment = false;
            this.xrLabel13.Text = "3、入院病情分级";
            this.xrLabel13.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel13.WordWrap = false;
            // 
            // xrL_ZRYBQFX
            // 
            this.xrL_ZRYBQFX.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ZRYBQFX.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ZRYBQFX.LocationFloat = new DevExpress.Utils.PointFloat(126.625F, 176.3333F);
            this.xrL_ZRYBQFX.Name = "xrL_ZRYBQFX";
            this.xrL_ZRYBQFX.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ZRYBQFX.SizeF = new System.Drawing.SizeF(28.00002F, 25.99998F);
            this.xrL_ZRYBQFX.StylePriority.UseBorders = false;
            this.xrL_ZRYBQFX.StylePriority.UseFont = false;
            this.xrL_ZRYBQFX.StylePriority.UsePadding = false;
            this.xrL_ZRYBQFX.StylePriority.UseTextAlignment = false;
            this.xrL_ZRYBQFX.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ZRYBQFX.WordWrap = false;
            // 
            // xrLabel16
            // 
            this.xrLabel16.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel16.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel16.LocationFloat = new DevExpress.Utils.PointFloat(158.6666F, 176.3333F);
            this.xrLabel16.Name = "xrLabel16";
            this.xrLabel16.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel16.SizeF = new System.Drawing.SizeF(246F, 25.99998F);
            this.xrLabel16.StylePriority.UseBorders = false;
            this.xrLabel16.StylePriority.UseFont = false;
            this.xrLabel16.StylePriority.UsePadding = false;
            this.xrLabel16.StylePriority.UseTextAlignment = false;
            this.xrLabel16.Text = "1.病危 2.病重  3.一般";
            this.xrLabel16.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel16.WordWrap = false;
            // 
            // xrLabel23
            // 
            this.xrLabel23.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel23.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel23.LocationFloat = new DevExpress.Utils.PointFloat(0F, 202.3333F);
            this.xrLabel23.Name = "xrLabel23";
            this.xrLabel23.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel23.SizeF = new System.Drawing.SizeF(154.625F, 25.99998F);
            this.xrLabel23.StylePriority.UseBorders = false;
            this.xrLabel23.StylePriority.UseFont = false;
            this.xrLabel23.StylePriority.UsePadding = false;
            this.xrLabel23.StylePriority.UseTextAlignment = false;
            this.xrLabel23.Text = "4、入院后确诊日期：";
            this.xrLabel23.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel23.WordWrap = false;
            // 
            // xrL_RYHQZRQ
            // 
            this.xrL_RYHQZRQ.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_RYHQZRQ.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_RYHQZRQ.LocationFloat = new DevExpress.Utils.PointFloat(154.625F, 202.3333F);
            this.xrL_RYHQZRQ.Name = "xrL_RYHQZRQ";
            this.xrL_RYHQZRQ.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_RYHQZRQ.SizeF = new System.Drawing.SizeF(142F, 25.99998F);
            this.xrL_RYHQZRQ.StylePriority.UseBorders = false;
            this.xrL_RYHQZRQ.StylePriority.UseFont = false;
            this.xrL_RYHQZRQ.StylePriority.UsePadding = false;
            this.xrL_RYHQZRQ.StylePriority.UseTextAlignment = false;
            this.xrL_RYHQZRQ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrL_RYHQZRQ.WordWrap = false;
            // 
            // xrLabel15
            // 
            this.xrLabel15.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel15.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel15.LocationFloat = new DevExpress.Utils.PointFloat(158.6666F, 228.3333F);
            this.xrLabel15.Name = "xrLabel15";
            this.xrLabel15.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel15.SizeF = new System.Drawing.SizeF(376.9165F, 25.99997F);
            this.xrLabel15.StylePriority.UseBorders = false;
            this.xrLabel15.StylePriority.UseFont = false;
            this.xrLabel15.StylePriority.UsePadding = false;
            this.xrLabel15.StylePriority.UseTextAlignment = false;
            this.xrLabel15.Text = "主要诊断疗效：1 治愈 2 好转 3未愈 4死亡 5分娩 6其他";
            this.xrLabel15.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel15.WordWrap = false;
            // 
            // xrL_ZYZDLX
            // 
            this.xrL_ZYZDLX.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ZYZDLX.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ZYZDLX.LocationFloat = new DevExpress.Utils.PointFloat(126.625F, 228.3333F);
            this.xrL_ZYZDLX.Name = "xrL_ZYZDLX";
            this.xrL_ZYZDLX.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ZYZDLX.SizeF = new System.Drawing.SizeF(28.00002F, 25.99997F);
            this.xrL_ZYZDLX.StylePriority.UseBorders = false;
            this.xrL_ZYZDLX.StylePriority.UseFont = false;
            this.xrL_ZYZDLX.StylePriority.UsePadding = false;
            this.xrL_ZYZDLX.StylePriority.UseTextAlignment = false;
            this.xrL_ZYZDLX.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ZYZDLX.WordWrap = false;
            // 
            // xrLabel19
            // 
            this.xrLabel19.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel19.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel19.LocationFloat = new DevExpress.Utils.PointFloat(0F, 228.3333F);
            this.xrLabel19.Name = "xrLabel19";
            this.xrLabel19.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel19.SizeF = new System.Drawing.SizeF(126.625F, 25.99997F);
            this.xrLabel19.StylePriority.UseBorders = false;
            this.xrLabel19.StylePriority.UseFont = false;
            this.xrLabel19.StylePriority.UsePadding = false;
            this.xrLabel19.StylePriority.UseTextAlignment = false;
            this.xrLabel19.Text = "5、主要诊断疗效：";
            this.xrLabel19.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel19.WordWrap = false;
            // 
            // xrLabel17
            // 
            this.xrLabel17.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel17.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel17.LocationFloat = new DevExpress.Utils.PointFloat(7.947286E-06F, 254.3332F);
            this.xrLabel17.Name = "xrLabel17";
            this.xrLabel17.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel17.SizeF = new System.Drawing.SizeF(396.6667F, 25.99992F);
            this.xrLabel17.StylePriority.UseBorders = false;
            this.xrLabel17.StylePriority.UseFont = false;
            this.xrLabel17.StylePriority.UsePadding = false;
            this.xrLabel17.StylePriority.UseTextAlignment = false;
            this.xrLabel17.Text = "6、诊断符合情况：0.未做 1.符合 2.不符合 3.不肯定";
            this.xrLabel17.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel17.WordWrap = false;
            // 
            // xrL_ZDFUQK_MZYCY
            // 
            this.xrL_ZDFUQK_MZYCY.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ZDFUQK_MZYCY.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ZDFUQK_MZYCY.LocationFloat = new DevExpress.Utils.PointFloat(126.625F, 280.3331F);
            this.xrL_ZDFUQK_MZYCY.Name = "xrL_ZDFUQK_MZYCY";
            this.xrL_ZDFUQK_MZYCY.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ZDFUQK_MZYCY.SizeF = new System.Drawing.SizeF(28F, 25.99997F);
            this.xrL_ZDFUQK_MZYCY.StylePriority.UseBorders = false;
            this.xrL_ZDFUQK_MZYCY.StylePriority.UseFont = false;
            this.xrL_ZDFUQK_MZYCY.StylePriority.UsePadding = false;
            this.xrL_ZDFUQK_MZYCY.StylePriority.UseTextAlignment = false;
            this.xrL_ZDFUQK_MZYCY.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ZDFUQK_MZYCY.WordWrap = false;
            // 
            // xrLabel21
            // 
            this.xrLabel21.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel21.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel21.LocationFloat = new DevExpress.Utils.PointFloat(46.54163F, 280.3331F);
            this.xrLabel21.Name = "xrLabel21";
            this.xrLabel21.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel21.SizeF = new System.Drawing.SizeF(80.08336F, 25.99997F);
            this.xrLabel21.StylePriority.UseBorders = false;
            this.xrLabel21.StylePriority.UseFont = false;
            this.xrLabel21.StylePriority.UsePadding = false;
            this.xrLabel21.StylePriority.UseTextAlignment = false;
            this.xrLabel21.Text = "门诊与出院";
            this.xrLabel21.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel21.WordWrap = false;
            // 
            // xrL_ZDFUQK_RYYCY
            // 
            this.xrL_ZDFUQK_RYYCY.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ZDFUQK_RYYCY.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ZDFUQK_RYYCY.LocationFloat = new DevExpress.Utils.PointFloat(259.5833F, 280.3331F);
            this.xrL_ZDFUQK_RYYCY.Name = "xrL_ZDFUQK_RYYCY";
            this.xrL_ZDFUQK_RYYCY.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ZDFUQK_RYYCY.SizeF = new System.Drawing.SizeF(28F, 25.99994F);
            this.xrL_ZDFUQK_RYYCY.StylePriority.UseBorders = false;
            this.xrL_ZDFUQK_RYYCY.StylePriority.UseFont = false;
            this.xrL_ZDFUQK_RYYCY.StylePriority.UsePadding = false;
            this.xrL_ZDFUQK_RYYCY.StylePriority.UseTextAlignment = false;
            this.xrL_ZDFUQK_RYYCY.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ZDFUQK_RYYCY.WordWrap = false;
            // 
            // xrLabel24
            // 
            this.xrLabel24.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel24.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel24.LocationFloat = new DevExpress.Utils.PointFloat(179.4999F, 280.3331F);
            this.xrLabel24.Name = "xrLabel24";
            this.xrLabel24.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel24.SizeF = new System.Drawing.SizeF(80.08334F, 25.99994F);
            this.xrLabel24.StylePriority.UseBorders = false;
            this.xrLabel24.StylePriority.UseFont = false;
            this.xrLabel24.StylePriority.UsePadding = false;
            this.xrLabel24.StylePriority.UseTextAlignment = false;
            this.xrLabel24.Text = "入院与出院";
            this.xrLabel24.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel24.WordWrap = false;
            // 
            // xrL_ZDFHQK_SQYSH
            // 
            this.xrL_ZDFHQK_SQYSH.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ZDFHQK_SQYSH.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ZDFHQK_SQYSH.LocationFloat = new DevExpress.Utils.PointFloat(392.125F, 280.3331F);
            this.xrL_ZDFHQK_SQYSH.Name = "xrL_ZDFHQK_SQYSH";
            this.xrL_ZDFHQK_SQYSH.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ZDFHQK_SQYSH.SizeF = new System.Drawing.SizeF(28F, 25.99997F);
            this.xrL_ZDFHQK_SQYSH.StylePriority.UseBorders = false;
            this.xrL_ZDFHQK_SQYSH.StylePriority.UseFont = false;
            this.xrL_ZDFHQK_SQYSH.StylePriority.UsePadding = false;
            this.xrL_ZDFHQK_SQYSH.StylePriority.UseTextAlignment = false;
            this.xrL_ZDFHQK_SQYSH.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ZDFHQK_SQYSH.WordWrap = false;
            // 
            // xrLabel26
            // 
            this.xrLabel26.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel26.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel26.LocationFloat = new DevExpress.Utils.PointFloat(312.0416F, 280.3331F);
            this.xrLabel26.Name = "xrLabel26";
            this.xrLabel26.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel26.SizeF = new System.Drawing.SizeF(80.08334F, 25.99997F);
            this.xrLabel26.StylePriority.UseBorders = false;
            this.xrLabel26.StylePriority.UseFont = false;
            this.xrLabel26.StylePriority.UsePadding = false;
            this.xrLabel26.StylePriority.UseTextAlignment = false;
            this.xrLabel26.Text = "术前与术后";
            this.xrLabel26.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel26.WordWrap = false;
            // 
            // xrL_ZDFUQK_LCYBL
            // 
            this.xrL_ZDFUQK_LCYBL.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ZDFUQK_LCYBL.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ZDFUQK_LCYBL.LocationFloat = new DevExpress.Utils.PointFloat(522.125F, 280.3331F);
            this.xrL_ZDFUQK_LCYBL.Name = "xrL_ZDFUQK_LCYBL";
            this.xrL_ZDFUQK_LCYBL.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ZDFUQK_LCYBL.SizeF = new System.Drawing.SizeF(28F, 25.99997F);
            this.xrL_ZDFUQK_LCYBL.StylePriority.UseBorders = false;
            this.xrL_ZDFUQK_LCYBL.StylePriority.UseFont = false;
            this.xrL_ZDFUQK_LCYBL.StylePriority.UsePadding = false;
            this.xrL_ZDFUQK_LCYBL.StylePriority.UseTextAlignment = false;
            this.xrL_ZDFUQK_LCYBL.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ZDFUQK_LCYBL.WordWrap = false;
            // 
            // xrLabel28
            // 
            this.xrLabel28.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel28.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel28.LocationFloat = new DevExpress.Utils.PointFloat(442.0417F, 280.3331F);
            this.xrLabel28.Name = "xrLabel28";
            this.xrLabel28.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel28.SizeF = new System.Drawing.SizeF(80.08331F, 25.99997F);
            this.xrLabel28.StylePriority.UseBorders = false;
            this.xrLabel28.StylePriority.UseFont = false;
            this.xrLabel28.StylePriority.UsePadding = false;
            this.xrLabel28.StylePriority.UseTextAlignment = false;
            this.xrLabel28.Text = "临床与病理";
            this.xrLabel28.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel28.WordWrap = false;
            // 
            // xrLabel29
            // 
            this.xrLabel29.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel29.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel29.LocationFloat = new DevExpress.Utils.PointFloat(577.5416F, 280.3331F);
            this.xrLabel29.Name = "xrLabel29";
            this.xrLabel29.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel29.SizeF = new System.Drawing.SizeF(80.08331F, 25.99997F);
            this.xrLabel29.StylePriority.UseBorders = false;
            this.xrLabel29.StylePriority.UseFont = false;
            this.xrLabel29.StylePriority.UsePadding = false;
            this.xrLabel29.StylePriority.UseTextAlignment = false;
            this.xrLabel29.Text = "放射线与病理 ";
            this.xrLabel29.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel29.WordWrap = false;
            // 
            // xrL_ZDFUQK_FSXYBL
            // 
            this.xrL_ZDFUQK_FSXYBL.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ZDFUQK_FSXYBL.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ZDFUQK_FSXYBL.LocationFloat = new DevExpress.Utils.PointFloat(657.625F, 280.3331F);
            this.xrL_ZDFUQK_FSXYBL.Name = "xrL_ZDFUQK_FSXYBL";
            this.xrL_ZDFUQK_FSXYBL.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ZDFUQK_FSXYBL.SizeF = new System.Drawing.SizeF(28F, 25.99997F);
            this.xrL_ZDFUQK_FSXYBL.StylePriority.UseBorders = false;
            this.xrL_ZDFUQK_FSXYBL.StylePriority.UseFont = false;
            this.xrL_ZDFUQK_FSXYBL.StylePriority.UsePadding = false;
            this.xrL_ZDFUQK_FSXYBL.StylePriority.UseTextAlignment = false;
            this.xrL_ZDFUQK_FSXYBL.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel20
            // 
            this.xrLabel20.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel20.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel20.LocationFloat = new DevExpress.Utils.PointFloat(0F, 306.3331F);
            this.xrLabel20.Name = "xrLabel20";
            this.xrLabel20.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel20.SizeF = new System.Drawing.SizeF(144.3334F, 26F);
            this.xrLabel20.StylePriority.UseBorders = false;
            this.xrLabel20.StylePriority.UseFont = false;
            this.xrLabel20.StylePriority.UsePadding = false;
            this.xrLabel20.StylePriority.UseTextAlignment = false;
            this.xrLabel20.Text = "7、抢救情况：抢救次数";
            this.xrLabel20.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel20.WordWrap = false;
            // 
            // xrL_QJQKQJCS
            // 
            this.xrL_QJQKQJCS.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_QJQKQJCS.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_QJQKQJCS.LocationFloat = new DevExpress.Utils.PointFloat(146.75F, 306.3331F);
            this.xrL_QJQKQJCS.Name = "xrL_QJQKQJCS";
            this.xrL_QJQKQJCS.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_QJQKQJCS.SizeF = new System.Drawing.SizeF(44.45836F, 26F);
            this.xrL_QJQKQJCS.StylePriority.UseBorders = false;
            this.xrL_QJQKQJCS.StylePriority.UseFont = false;
            this.xrL_QJQKQJCS.StylePriority.UsePadding = false;
            this.xrL_QJQKQJCS.StylePriority.UseTextAlignment = false;
            this.xrL_QJQKQJCS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_QJQKQJCS.WordWrap = false;
            // 
            // xrLabel25
            // 
            this.xrLabel25.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel25.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel25.LocationFloat = new DevExpress.Utils.PointFloat(191.2083F, 306.3331F);
            this.xrLabel25.Name = "xrLabel25";
            this.xrLabel25.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel25.SizeF = new System.Drawing.SizeF(68.37495F, 26F);
            this.xrLabel25.StylePriority.UseBorders = false;
            this.xrLabel25.StylePriority.UseFont = false;
            this.xrLabel25.StylePriority.UsePadding = false;
            this.xrLabel25.StylePriority.UseTextAlignment = false;
            this.xrLabel25.Text = "次    成功";
            this.xrLabel25.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel25.WordWrap = false;
            // 
            // xrL_QJCGCS
            // 
            this.xrL_QJCGCS.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_QJCGCS.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_QJCGCS.LocationFloat = new DevExpress.Utils.PointFloat(260.75F, 306.3331F);
            this.xrL_QJCGCS.Name = "xrL_QJCGCS";
            this.xrL_QJCGCS.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_QJCGCS.SizeF = new System.Drawing.SizeF(44.45834F, 25.99997F);
            this.xrL_QJCGCS.StylePriority.UseBorders = false;
            this.xrL_QJCGCS.StylePriority.UseFont = false;
            this.xrL_QJCGCS.StylePriority.UsePadding = false;
            this.xrL_QJCGCS.StylePriority.UseTextAlignment = false;
            this.xrL_QJCGCS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_QJCGCS.WordWrap = false;
            // 
            // xrLabel30
            // 
            this.xrLabel30.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel30.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel30.LocationFloat = new DevExpress.Utils.PointFloat(305.2084F, 306.3331F);
            this.xrLabel30.Name = "xrLabel30";
            this.xrLabel30.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel30.SizeF = new System.Drawing.SizeF(68.37494F, 26F);
            this.xrLabel30.StylePriority.UseBorders = false;
            this.xrLabel30.StylePriority.UseFont = false;
            this.xrLabel30.StylePriority.UsePadding = false;
            this.xrLabel30.StylePriority.UseTextAlignment = false;
            this.xrLabel30.Text = "次";
            this.xrLabel30.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel30.WordWrap = false;
            // 
            // xrLabel22
            // 
            this.xrLabel22.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel22.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel22.LocationFloat = new DevExpress.Utils.PointFloat(7.947286E-06F, 332.3331F);
            this.xrLabel22.Name = "xrLabel22";
            this.xrLabel22.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel22.SizeF = new System.Drawing.SizeF(96.41673F, 26F);
            this.xrLabel22.StylePriority.UseBorders = false;
            this.xrLabel22.StylePriority.UseFont = false;
            this.xrLabel22.StylePriority.UsePadding = false;
            this.xrLabel22.StylePriority.UseTextAlignment = false;
            this.xrLabel22.Text = "8、输液情况：";
            this.xrLabel22.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel22.WordWrap = false;
            // 
            // xrLabel27
            // 
            this.xrLabel27.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel27.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel27.LocationFloat = new DevExpress.Utils.PointFloat(130.6665F, 332.3331F);
            this.xrLabel27.Name = "xrLabel27";
            this.xrLabel27.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel27.SizeF = new System.Drawing.SizeF(80.7917F, 26F);
            this.xrLabel27.StylePriority.UseBorders = false;
            this.xrLabel27.StylePriority.UseFont = false;
            this.xrLabel27.StylePriority.UsePadding = false;
            this.xrLabel27.StylePriority.UseTextAlignment = false;
            this.xrLabel27.Text = "1.有  2.无";
            this.xrLabel27.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel27.WordWrap = false;
            // 
            // xrL_SYQK
            // 
            this.xrL_SYQK.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SYQK.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SYQK.LocationFloat = new DevExpress.Utils.PointFloat(98.62497F, 332.3331F);
            this.xrL_SYQK.Name = "xrL_SYQK";
            this.xrL_SYQK.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SYQK.SizeF = new System.Drawing.SizeF(28.00002F, 26F);
            this.xrL_SYQK.StylePriority.UseBorders = false;
            this.xrL_SYQK.StylePriority.UseFont = false;
            this.xrL_SYQK.StylePriority.UsePadding = false;
            this.xrL_SYQK.StylePriority.UseTextAlignment = false;
            this.xrL_SYQK.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SYQK.WordWrap = false;
            // 
            // xrLabel32
            // 
            this.xrLabel32.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel32.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel32.LocationFloat = new DevExpress.Utils.PointFloat(315.0416F, 332.3332F);
            this.xrLabel32.Name = "xrLabel32";
            this.xrLabel32.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel32.SizeF = new System.Drawing.SizeF(77.0834F, 26F);
            this.xrLabel32.StylePriority.UseBorders = false;
            this.xrLabel32.StylePriority.UseFont = false;
            this.xrLabel32.StylePriority.UsePadding = false;
            this.xrLabel32.StylePriority.UseTextAlignment = false;
            this.xrLabel32.Text = "1.有  2.无";
            this.xrLabel32.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel32.WordWrap = false;
            // 
            // xrL_SYFY
            // 
            this.xrL_SYFY.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SYFY.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SYFY.LocationFloat = new DevExpress.Utils.PointFloat(283F, 332.3332F);
            this.xrL_SYFY.Name = "xrL_SYFY";
            this.xrL_SYFY.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SYFY.SizeF = new System.Drawing.SizeF(28.00006F, 26F);
            this.xrL_SYFY.StylePriority.UseBorders = false;
            this.xrL_SYFY.StylePriority.UseFont = false;
            this.xrL_SYFY.StylePriority.UsePadding = false;
            this.xrL_SYFY.StylePriority.UseTextAlignment = false;
            this.xrL_SYFY.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SYFY.WordWrap = false;
            // 
            // xrLabel34
            // 
            this.xrLabel34.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel34.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel34.LocationFloat = new DevExpress.Utils.PointFloat(211.4583F, 332.3331F);
            this.xrLabel34.Name = "xrLabel34";
            this.xrLabel34.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel34.SizeF = new System.Drawing.SizeF(71.54167F, 26F);
            this.xrLabel34.StylePriority.UseBorders = false;
            this.xrLabel34.StylePriority.UseFont = false;
            this.xrLabel34.StylePriority.UsePadding = false;
            this.xrLabel34.StylePriority.UseTextAlignment = false;
            this.xrLabel34.Text = "输液反应：";
            this.xrLabel34.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel34.WordWrap = false;
            // 
            // xrLabel35
            // 
            this.xrLabel35.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel35.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel35.LocationFloat = new DevExpress.Utils.PointFloat(500.7915F, 332.3332F);
            this.xrLabel35.Name = "xrLabel35";
            this.xrLabel35.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel35.SizeF = new System.Drawing.SizeF(80.79169F, 26F);
            this.xrLabel35.StylePriority.UseBorders = false;
            this.xrLabel35.StylePriority.UseFont = false;
            this.xrLabel35.StylePriority.UsePadding = false;
            this.xrLabel35.StylePriority.UseTextAlignment = false;
            this.xrLabel35.Text = "1.有  2.无";
            this.xrLabel35.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel35.WordWrap = false;
            // 
            // xrL_SXQK
            // 
            this.xrL_SXQK.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SXQK.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SXQK.LocationFloat = new DevExpress.Utils.PointFloat(468.7499F, 332.3332F);
            this.xrL_SXQK.Name = "xrL_SXQK";
            this.xrL_SXQK.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SXQK.SizeF = new System.Drawing.SizeF(28F, 26F);
            this.xrL_SXQK.StylePriority.UseBorders = false;
            this.xrL_SXQK.StylePriority.UseFont = false;
            this.xrL_SXQK.StylePriority.UsePadding = false;
            this.xrL_SXQK.StylePriority.UseTextAlignment = false;
            this.xrL_SXQK.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SXQK.WordWrap = false;
            // 
            // xrLabel37
            // 
            this.xrLabel37.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel37.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel37.LocationFloat = new DevExpress.Utils.PointFloat(395.8333F, 332.3332F);
            this.xrLabel37.Name = "xrLabel37";
            this.xrLabel37.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel37.SizeF = new System.Drawing.SizeF(71.41675F, 26F);
            this.xrLabel37.StylePriority.UseBorders = false;
            this.xrLabel37.StylePriority.UseFont = false;
            this.xrLabel37.StylePriority.UsePadding = false;
            this.xrLabel37.StylePriority.UseTextAlignment = false;
            this.xrLabel37.Text = "输血情况：";
            this.xrLabel37.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel37.WordWrap = false;
            // 
            // xrLabel38
            // 
            this.xrLabel38.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel38.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel38.LocationFloat = new DevExpress.Utils.PointFloat(584.7083F, 332.3332F);
            this.xrLabel38.Name = "xrLabel38";
            this.xrLabel38.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel38.SizeF = new System.Drawing.SizeF(71.41681F, 26F);
            this.xrLabel38.StylePriority.UseBorders = false;
            this.xrLabel38.StylePriority.UseFont = false;
            this.xrLabel38.StylePriority.UsePadding = false;
            this.xrLabel38.StylePriority.UseTextAlignment = false;
            this.xrLabel38.Text = "输血反应：";
            this.xrLabel38.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel38.WordWrap = false;
            // 
            // xrL_SXFY
            // 
            this.xrL_SXFY.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SXFY.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SXFY.LocationFloat = new DevExpress.Utils.PointFloat(657.625F, 332.3332F);
            this.xrL_SXFY.Name = "xrL_SXFY";
            this.xrL_SXFY.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SXFY.SizeF = new System.Drawing.SizeF(28F, 26F);
            this.xrL_SXFY.StylePriority.UseBorders = false;
            this.xrL_SXFY.StylePriority.UseFont = false;
            this.xrL_SXFY.StylePriority.UsePadding = false;
            this.xrL_SXFY.StylePriority.UseTextAlignment = false;
            this.xrL_SXFY.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel40
            // 
            this.xrLabel40.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel40.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel40.LocationFloat = new DevExpress.Utils.PointFloat(689.6666F, 332.3332F);
            this.xrLabel40.Name = "xrLabel40";
            this.xrLabel40.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel40.SizeF = new System.Drawing.SizeF(67.3335F, 25.99997F);
            this.xrLabel40.StylePriority.UseBorders = false;
            this.xrLabel40.StylePriority.UseFont = false;
            this.xrLabel40.StylePriority.UsePadding = false;
            this.xrLabel40.StylePriority.UseTextAlignment = false;
            this.xrLabel40.Text = "1.有  2.无";
            this.xrLabel40.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel31
            // 
            this.xrLabel31.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel31.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel31.LocationFloat = new DevExpress.Utils.PointFloat(0F, 358.3331F);
            this.xrLabel31.Name = "xrLabel31";
            this.xrLabel31.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel31.SizeF = new System.Drawing.SizeF(96.41672F, 26.00003F);
            this.xrLabel31.StylePriority.UseBorders = false;
            this.xrLabel31.StylePriority.UseFont = false;
            this.xrLabel31.StylePriority.UsePadding = false;
            this.xrLabel31.StylePriority.UseTextAlignment = false;
            this.xrLabel31.Text = "9、危重病例：";
            this.xrLabel31.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel31.WordWrap = false;
            // 
            // xrLabel33
            // 
            this.xrLabel33.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel33.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel33.LocationFloat = new DevExpress.Utils.PointFloat(130.6665F, 358.3331F);
            this.xrLabel33.Name = "xrLabel33";
            this.xrLabel33.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel33.SizeF = new System.Drawing.SizeF(80.7917F, 25.99997F);
            this.xrLabel33.StylePriority.UseBorders = false;
            this.xrLabel33.StylePriority.UseFont = false;
            this.xrLabel33.StylePriority.UsePadding = false;
            this.xrLabel33.StylePriority.UseTextAlignment = false;
            this.xrLabel33.Text = "1.是  2.否";
            this.xrLabel33.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel33.WordWrap = false;
            // 
            // xrL_WZBL
            // 
            this.xrL_WZBL.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_WZBL.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_WZBL.LocationFloat = new DevExpress.Utils.PointFloat(98.62497F, 358.3331F);
            this.xrL_WZBL.Name = "xrL_WZBL";
            this.xrL_WZBL.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_WZBL.SizeF = new System.Drawing.SizeF(28.00002F, 25.99997F);
            this.xrL_WZBL.StylePriority.UseBorders = false;
            this.xrL_WZBL.StylePriority.UseFont = false;
            this.xrL_WZBL.StylePriority.UsePadding = false;
            this.xrL_WZBL.StylePriority.UseTextAlignment = false;
            this.xrL_WZBL.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_WZBL.WordWrap = false;
            // 
            // xrLabel39
            // 
            this.xrLabel39.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel39.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel39.LocationFloat = new DevExpress.Utils.PointFloat(211.4583F, 358.3332F);
            this.xrLabel39.Name = "xrLabel39";
            this.xrLabel39.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel39.SizeF = new System.Drawing.SizeF(96.41673F, 26.00003F);
            this.xrLabel39.StylePriority.UseBorders = false;
            this.xrLabel39.StylePriority.UseFont = false;
            this.xrLabel39.StylePriority.UsePadding = false;
            this.xrLabel39.StylePriority.UseTextAlignment = false;
            this.xrLabel39.Text = "疑难病例：";
            this.xrLabel39.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel39.WordWrap = false;
            // 
            // xrL_YNBL
            // 
            this.xrL_YNBL.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_YNBL.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_YNBL.LocationFloat = new DevExpress.Utils.PointFloat(310.0832F, 358.3332F);
            this.xrL_YNBL.Name = "xrL_YNBL";
            this.xrL_YNBL.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_YNBL.SizeF = new System.Drawing.SizeF(28.00006F, 25.99997F);
            this.xrL_YNBL.StylePriority.UseBorders = false;
            this.xrL_YNBL.StylePriority.UseFont = false;
            this.xrL_YNBL.StylePriority.UsePadding = false;
            this.xrL_YNBL.StylePriority.UseTextAlignment = false;
            this.xrL_YNBL.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_YNBL.WordWrap = false;
            // 
            // xrLabel42
            // 
            this.xrLabel42.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel42.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel42.LocationFloat = new DevExpress.Utils.PointFloat(342.1248F, 358.3332F);
            this.xrLabel42.Name = "xrLabel42";
            this.xrLabel42.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel42.SizeF = new System.Drawing.SizeF(80.79169F, 25.99997F);
            this.xrLabel42.StylePriority.UseBorders = false;
            this.xrLabel42.StylePriority.UseFont = false;
            this.xrLabel42.StylePriority.UsePadding = false;
            this.xrLabel42.StylePriority.UseTextAlignment = false;
            this.xrLabel42.Text = "1.是  2.否";
            this.xrLabel42.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel42.WordWrap = false;
            // 
            // xrLabel41
            // 
            this.xrLabel41.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel41.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel41.LocationFloat = new DevExpress.Utils.PointFloat(7.947286E-06F, 384.3331F);
            this.xrLabel41.Name = "xrLabel41";
            this.xrLabel41.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel41.SizeF = new System.Drawing.SizeF(271.5F, 26F);
            this.xrLabel41.StylePriority.UseBorders = false;
            this.xrLabel41.StylePriority.UseFont = false;
            this.xrLabel41.StylePriority.UsePadding = false;
            this.xrLabel41.StylePriority.UseTextAlignment = false;
            this.xrLabel41.Text = "10、手术、治疗、检查、诊断为本院第一例 ：";
            this.xrLabel41.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel41.WordWrap = false;
            // 
            // xrL_SS_ZL_JC_ZD
            // 
            this.xrL_SS_ZL_JC_ZD.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SS_ZL_JC_ZD.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SS_ZL_JC_ZD.LocationFloat = new DevExpress.Utils.PointFloat(277.2083F, 384.3332F);
            this.xrL_SS_ZL_JC_ZD.Name = "xrL_SS_ZL_JC_ZD";
            this.xrL_SS_ZL_JC_ZD.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SS_ZL_JC_ZD.SizeF = new System.Drawing.SizeF(28.00003F, 25.99994F);
            this.xrL_SS_ZL_JC_ZD.StylePriority.UseBorders = false;
            this.xrL_SS_ZL_JC_ZD.StylePriority.UseFont = false;
            this.xrL_SS_ZL_JC_ZD.StylePriority.UsePadding = false;
            this.xrL_SS_ZL_JC_ZD.StylePriority.UseTextAlignment = false;
            this.xrL_SS_ZL_JC_ZD.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SS_ZL_JC_ZD.WordWrap = false;
            // 
            // xrLabel44
            // 
            this.xrLabel44.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel44.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel44.LocationFloat = new DevExpress.Utils.PointFloat(309.2499F, 384.3332F);
            this.xrLabel44.Name = "xrLabel44";
            this.xrLabel44.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel44.SizeF = new System.Drawing.SizeF(80.79169F, 25.99994F);
            this.xrLabel44.StylePriority.UseBorders = false;
            this.xrLabel44.StylePriority.UseFont = false;
            this.xrLabel44.StylePriority.UsePadding = false;
            this.xrLabel44.StylePriority.UseTextAlignment = false;
            this.xrLabel44.Text = "1.是  2.否";
            this.xrLabel44.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel44.WordWrap = false;
            // 
            // xrLabel43
            // 
            this.xrLabel43.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel43.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel43.LocationFloat = new DevExpress.Utils.PointFloat(347.0832F, 410.3333F);
            this.xrLabel43.Name = "xrLabel43";
            this.xrLabel43.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel43.SizeF = new System.Drawing.SizeF(211.4582F, 25.99997F);
            this.xrLabel43.StylePriority.UseBorders = false;
            this.xrLabel43.StylePriority.UseFont = false;
            this.xrLabel43.StylePriority.UsePadding = false;
            this.xrLabel43.StylePriority.UseTextAlignment = false;
            this.xrLabel43.Text = "1.完成  2.变异 3.退出";
            this.xrLabel43.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel43.WordWrap = false;
            // 
            // xrL_LCLJBL
            // 
            this.xrL_LCLJBL.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_LCLJBL.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_LCLJBL.LocationFloat = new DevExpress.Utils.PointFloat(126.625F, 410.3331F);
            this.xrL_LCLJBL.Name = "xrL_LCLJBL";
            this.xrL_LCLJBL.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_LCLJBL.SizeF = new System.Drawing.SizeF(28.00002F, 25.99994F);
            this.xrL_LCLJBL.StylePriority.UseBorders = false;
            this.xrL_LCLJBL.StylePriority.UseFont = false;
            this.xrL_LCLJBL.StylePriority.UsePadding = false;
            this.xrL_LCLJBL.StylePriority.UseTextAlignment = false;
            this.xrL_LCLJBL.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_LCLJBL.WordWrap = false;
            // 
            // xrLabel46
            // 
            this.xrLabel46.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel46.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel46.LocationFloat = new DevExpress.Utils.PointFloat(158.6666F, 410.3331F);
            this.xrLabel46.Name = "xrLabel46";
            this.xrLabel46.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel46.SizeF = new System.Drawing.SizeF(80.7917F, 25.99994F);
            this.xrLabel46.StylePriority.UseBorders = false;
            this.xrLabel46.StylePriority.UseFont = false;
            this.xrLabel46.StylePriority.UsePadding = false;
            this.xrLabel46.StylePriority.UseTextAlignment = false;
            this.xrLabel46.Text = "1.是  2.否";
            this.xrLabel46.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel46.WordWrap = false;
            // 
            // xrLabel47
            // 
            this.xrLabel47.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel47.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel47.LocationFloat = new DevExpress.Utils.PointFloat(239.4583F, 410.3332F);
            this.xrLabel47.Name = "xrLabel47";
            this.xrLabel47.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel47.SizeF = new System.Drawing.SizeF(72.58331F, 26.00003F);
            this.xrLabel47.StylePriority.UseBorders = false;
            this.xrLabel47.StylePriority.UseFont = false;
            this.xrLabel47.StylePriority.UsePadding = false;
            this.xrLabel47.StylePriority.UseTextAlignment = false;
            this.xrLabel47.Text = "完成情况：";
            this.xrLabel47.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel47.WordWrap = false;
            // 
            // xrL_LCLJWCQK
            // 
            this.xrL_LCLJWCQK.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_LCLJWCQK.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_LCLJWCQK.LocationFloat = new DevExpress.Utils.PointFloat(315.0416F, 410.3333F);
            this.xrL_LCLJWCQK.Name = "xrL_LCLJWCQK";
            this.xrL_LCLJWCQK.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_LCLJWCQK.SizeF = new System.Drawing.SizeF(28.00003F, 25.99997F);
            this.xrL_LCLJWCQK.StylePriority.UseBorders = false;
            this.xrL_LCLJWCQK.StylePriority.UseFont = false;
            this.xrL_LCLJWCQK.StylePriority.UsePadding = false;
            this.xrL_LCLJWCQK.StylePriority.UseTextAlignment = false;
            this.xrL_LCLJWCQK.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_LCLJWCQK.WordWrap = false;
            // 
            // xrLabel49
            // 
            this.xrLabel49.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel49.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel49.LocationFloat = new DevExpress.Utils.PointFloat(0F, 410.3331F);
            this.xrLabel49.Name = "xrLabel49";
            this.xrLabel49.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel49.SizeF = new System.Drawing.SizeF(126.625F, 26F);
            this.xrLabel49.StylePriority.UseBorders = false;
            this.xrLabel49.StylePriority.UseFont = false;
            this.xrLabel49.StylePriority.UsePadding = false;
            this.xrLabel49.StylePriority.UseTextAlignment = false;
            this.xrLabel49.Text = "11、临床路径病例：";
            this.xrLabel49.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel49.WordWrap = false;
            // 
            // xrLabel50
            // 
            this.xrLabel50.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel50.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel50.LocationFloat = new DevExpress.Utils.PointFloat(7.947286E-06F, 436.3331F);
            this.xrLabel50.Name = "xrLabel50";
            this.xrLabel50.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel50.SizeF = new System.Drawing.SizeF(154.625F, 26F);
            this.xrLabel50.StylePriority.UseBorders = false;
            this.xrLabel50.StylePriority.UseFont = false;
            this.xrLabel50.StylePriority.UsePadding = false;
            this.xrLabel50.StylePriority.UseTextAlignment = false;
            this.xrLabel50.Text = "12、单病种质量控制病例：";
            this.xrLabel50.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel50.WordWrap = false;
            // 
            // xrLabel51
            // 
            this.xrLabel51.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel51.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel51.LocationFloat = new DevExpress.Utils.PointFloat(195.2499F, 436.3332F);
            this.xrLabel51.Name = "xrLabel51";
            this.xrLabel51.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel51.SizeF = new System.Drawing.SizeF(80.7917F, 25.99997F);
            this.xrLabel51.StylePriority.UseBorders = false;
            this.xrLabel51.StylePriority.UseFont = false;
            this.xrLabel51.StylePriority.UsePadding = false;
            this.xrLabel51.StylePriority.UseTextAlignment = false;
            this.xrLabel51.Text = "1.是  2.否";
            this.xrLabel51.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel51.WordWrap = false;
            // 
            // xrL_DBZZLKZBL
            // 
            this.xrL_DBZZLKZBL.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_DBZZLKZBL.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_DBZZLKZBL.LocationFloat = new DevExpress.Utils.PointFloat(163.2083F, 436.3332F);
            this.xrL_DBZZLKZBL.Name = "xrL_DBZZLKZBL";
            this.xrL_DBZZLKZBL.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_DBZZLKZBL.SizeF = new System.Drawing.SizeF(28.00005F, 25.99997F);
            this.xrL_DBZZLKZBL.StylePriority.UseBorders = false;
            this.xrL_DBZZLKZBL.StylePriority.UseFont = false;
            this.xrL_DBZZLKZBL.StylePriority.UsePadding = false;
            this.xrL_DBZZLKZBL.StylePriority.UseTextAlignment = false;
            this.xrL_DBZZLKZBL.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_DBZZLKZBL.WordWrap = false;
            // 
            // xrLabel45
            // 
            this.xrLabel45.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel45.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel45.LocationFloat = new DevExpress.Utils.PointFloat(0F, 462.3331F);
            this.xrLabel45.Name = "xrLabel45";
            this.xrLabel45.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel45.SizeF = new System.Drawing.SizeF(195.2499F, 26F);
            this.xrLabel45.StylePriority.UseBorders = false;
            this.xrLabel45.StylePriority.UseFont = false;
            this.xrLabel45.StylePriority.UsePadding = false;
            this.xrLabel45.StylePriority.UseTextAlignment = false;
            this.xrLabel45.Text = "13、相关疾病诊断分组（DRGS）：";
            this.xrLabel45.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel45.WordWrap = false;
            // 
            // xrL_XGJBZDFZ
            // 
            this.xrL_XGJBZDFZ.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_XGJBZDFZ.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_XGJBZDFZ.LocationFloat = new DevExpress.Utils.PointFloat(195.2499F, 462.3331F);
            this.xrL_XGJBZDFZ.Name = "xrL_XGJBZDFZ";
            this.xrL_XGJBZDFZ.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_XGJBZDFZ.SizeF = new System.Drawing.SizeF(28.00005F, 26F);
            this.xrL_XGJBZDFZ.StylePriority.UseBorders = false;
            this.xrL_XGJBZDFZ.StylePriority.UseFont = false;
            this.xrL_XGJBZDFZ.StylePriority.UsePadding = false;
            this.xrL_XGJBZDFZ.StylePriority.UseTextAlignment = false;
            this.xrL_XGJBZDFZ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_XGJBZDFZ.WordWrap = false;
            // 
            // xrLabel52
            // 
            this.xrLabel52.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel52.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel52.LocationFloat = new DevExpress.Utils.PointFloat(227.2915F, 462.3331F);
            this.xrLabel52.Name = "xrLabel52";
            this.xrLabel52.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel52.SizeF = new System.Drawing.SizeF(80.79173F, 26F);
            this.xrLabel52.StylePriority.UseBorders = false;
            this.xrLabel52.StylePriority.UseFont = false;
            this.xrLabel52.StylePriority.UsePadding = false;
            this.xrLabel52.StylePriority.UseTextAlignment = false;
            this.xrLabel52.Text = "1.是  2.否";
            this.xrLabel52.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel52.WordWrap = false;
            // 
            // xrLabel48
            // 
            this.xrLabel48.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel48.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel48.LocationFloat = new DevExpress.Utils.PointFloat(7.947286E-06F, 488.3331F);
            this.xrLabel48.Name = "xrLabel48";
            this.xrLabel48.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel48.SizeF = new System.Drawing.SizeF(154.625F, 26.00003F);
            this.xrLabel48.StylePriority.UseBorders = false;
            this.xrLabel48.StylePriority.UseFont = false;
            this.xrLabel48.StylePriority.UsePadding = false;
            this.xrLabel48.StylePriority.UseTextAlignment = false;
            this.xrLabel48.Text = "14、是否手术病人 ：";
            this.xrLabel48.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel48.WordWrap = false;
            // 
            // xrLabel53
            // 
            this.xrLabel53.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel53.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel53.LocationFloat = new DevExpress.Utils.PointFloat(195.2499F, 488.3331F);
            this.xrLabel53.Name = "xrLabel53";
            this.xrLabel53.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel53.SizeF = new System.Drawing.SizeF(80.79173F, 26.00003F);
            this.xrLabel53.StylePriority.UseBorders = false;
            this.xrLabel53.StylePriority.UseFont = false;
            this.xrLabel53.StylePriority.UsePadding = false;
            this.xrLabel53.StylePriority.UseTextAlignment = false;
            this.xrLabel53.Text = "1.是  2.否";
            this.xrLabel53.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel53.WordWrap = false;
            // 
            // xrL_SFZSBR
            // 
            this.xrL_SFZSBR.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SFZSBR.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SFZSBR.LocationFloat = new DevExpress.Utils.PointFloat(163.2083F, 488.3331F);
            this.xrL_SFZSBR.Name = "xrL_SFZSBR";
            this.xrL_SFZSBR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SFZSBR.SizeF = new System.Drawing.SizeF(28.00005F, 26.00003F);
            this.xrL_SFZSBR.StylePriority.UseBorders = false;
            this.xrL_SFZSBR.StylePriority.UseFont = false;
            this.xrL_SFZSBR.StylePriority.UsePadding = false;
            this.xrL_SFZSBR.StylePriority.UseTextAlignment = false;
            this.xrL_SFZSBR.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SFZSBR.WordWrap = false;
            // 
            // xrLabel55
            // 
            this.xrLabel55.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel55.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel55.LocationFloat = new DevExpress.Utils.PointFloat(283F, 488.3331F);
            this.xrLabel55.Name = "xrLabel55";
            this.xrLabel55.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel55.SizeF = new System.Drawing.SizeF(109.125F, 26F);
            this.xrLabel55.StylePriority.UseBorders = false;
            this.xrLabel55.StylePriority.UseFont = false;
            this.xrLabel55.StylePriority.UsePadding = false;
            this.xrLabel55.StylePriority.UseTextAlignment = false;
            this.xrLabel55.Text = "是否微创手术 ：";
            this.xrLabel55.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel55.WordWrap = false;
            // 
            // xrLabel56
            // 
            this.xrLabel56.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel56.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel56.LocationFloat = new DevExpress.Utils.PointFloat(428.7083F, 488.3331F);
            this.xrLabel56.Name = "xrLabel56";
            this.xrLabel56.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel56.SizeF = new System.Drawing.SizeF(80.79172F, 26F);
            this.xrLabel56.StylePriority.UseBorders = false;
            this.xrLabel56.StylePriority.UseFont = false;
            this.xrLabel56.StylePriority.UsePadding = false;
            this.xrLabel56.StylePriority.UseTextAlignment = false;
            this.xrLabel56.Text = "1.是  2.否";
            this.xrLabel56.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel56.WordWrap = false;
            // 
            // xrL_SFWCSS
            // 
            this.xrL_SFWCSS.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SFWCSS.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SFWCSS.LocationFloat = new DevExpress.Utils.PointFloat(396.6667F, 488.3331F);
            this.xrL_SFWCSS.Name = "xrL_SFWCSS";
            this.xrL_SFWCSS.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SFWCSS.SizeF = new System.Drawing.SizeF(28.00009F, 26F);
            this.xrL_SFWCSS.StylePriority.UseBorders = false;
            this.xrL_SFWCSS.StylePriority.UseFont = false;
            this.xrL_SFWCSS.StylePriority.UsePadding = false;
            this.xrL_SFWCSS.StylePriority.UseTextAlignment = false;
            this.xrL_SFWCSS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SFWCSS.WordWrap = false;
            // 
            // xrLabel54
            // 
            this.xrLabel54.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel54.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel54.LocationFloat = new DevExpress.Utils.PointFloat(260.75F, 514.3332F);
            this.xrLabel54.Name = "xrLabel54";
            this.xrLabel54.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel54.SizeF = new System.Drawing.SizeF(131.375F, 26F);
            this.xrLabel54.StylePriority.UseBorders = false;
            this.xrLabel54.StylePriority.UseFont = false;
            this.xrLabel54.StylePriority.UsePadding = false;
            this.xrLabel54.StylePriority.UseTextAlignment = false;
            this.xrLabel54.Text = "手术前病人占用床：";
            this.xrLabel54.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel54.WordWrap = false;
            // 
            // xrL_SSXZ
            // 
            this.xrL_SSXZ.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SSXZ.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SSXZ.LocationFloat = new DevExpress.Utils.PointFloat(98.62497F, 514.3333F);
            this.xrL_SSXZ.Name = "xrL_SSXZ";
            this.xrL_SSXZ.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SSXZ.SizeF = new System.Drawing.SizeF(28.00005F, 26F);
            this.xrL_SSXZ.StylePriority.UseBorders = false;
            this.xrL_SSXZ.StylePriority.UseFont = false;
            this.xrL_SSXZ.StylePriority.UsePadding = false;
            this.xrL_SSXZ.StylePriority.UseTextAlignment = false;
            this.xrL_SSXZ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SSXZ.WordWrap = false;
            // 
            // xrLabel58
            // 
            this.xrLabel58.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel58.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel58.LocationFloat = new DevExpress.Utils.PointFloat(130.6665F, 514.3332F);
            this.xrLabel58.Name = "xrLabel58";
            this.xrLabel58.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel58.SizeF = new System.Drawing.SizeF(129.2917F, 26F);
            this.xrLabel58.StylePriority.UseBorders = false;
            this.xrLabel58.StylePriority.UseFont = false;
            this.xrLabel58.StylePriority.UsePadding = false;
            this.xrLabel58.StylePriority.UseTextAlignment = false;
            this.xrLabel58.Text = "1.择期 2.急诊 ";
            this.xrLabel58.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel58.WordWrap = false;
            // 
            // xrLabel59
            // 
            this.xrLabel59.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel59.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel59.LocationFloat = new DevExpress.Utils.PointFloat(7.947286E-06F, 514.3331F);
            this.xrLabel59.Name = "xrLabel59";
            this.xrLabel59.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel59.SizeF = new System.Drawing.SizeF(96.41671F, 26.00012F);
            this.xrLabel59.StylePriority.UseBorders = false;
            this.xrLabel59.StylePriority.UseFont = false;
            this.xrLabel59.StylePriority.UsePadding = false;
            this.xrLabel59.StylePriority.UseTextAlignment = false;
            this.xrLabel59.Text = "15、手术性质：";
            this.xrLabel59.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel59.WordWrap = false;
            // 
            // xrLabel60
            // 
            this.xrLabel60.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel60.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel60.LocationFloat = new DevExpress.Utils.PointFloat(436.5834F, 514.3333F);
            this.xrLabel60.Name = "xrLabel60";
            this.xrLabel60.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel60.SizeF = new System.Drawing.SizeF(68.37497F, 25.99994F);
            this.xrLabel60.StylePriority.UseBorders = false;
            this.xrLabel60.StylePriority.UseFont = false;
            this.xrLabel60.StylePriority.UsePadding = false;
            this.xrLabel60.StylePriority.UseTextAlignment = false;
            this.xrLabel60.Text = "日";
            this.xrLabel60.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel60.WordWrap = false;
            // 
            // xrL_SSQBRZYC
            // 
            this.xrL_SSQBRZYC.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_SSQBRZYC.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SSQBRZYC.LocationFloat = new DevExpress.Utils.PointFloat(392.125F, 514.3333F);
            this.xrL_SSQBRZYC.Name = "xrL_SSQBRZYC";
            this.xrL_SSQBRZYC.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_SSQBRZYC.SizeF = new System.Drawing.SizeF(44.45834F, 25.99994F);
            this.xrL_SSQBRZYC.StylePriority.UseBorders = false;
            this.xrL_SSQBRZYC.StylePriority.UseFont = false;
            this.xrL_SSQBRZYC.StylePriority.UsePadding = false;
            this.xrL_SSQBRZYC.StylePriority.UseTextAlignment = false;
            this.xrL_SSQBRZYC.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SSQBRZYC.WordWrap = false;
            // 
            // xrLabel57
            // 
            this.xrLabel57.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel57.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel57.LocationFloat = new DevExpress.Utils.PointFloat(0F, 540.3332F);
            this.xrLabel57.Name = "xrLabel57";
            this.xrLabel57.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel57.SizeF = new System.Drawing.SizeF(98.62497F, 26.00006F);
            this.xrLabel57.StylePriority.UseBorders = false;
            this.xrLabel57.StylePriority.UseFont = false;
            this.xrLabel57.StylePriority.UsePadding = false;
            this.xrLabel57.StylePriority.UseTextAlignment = false;
            this.xrLabel57.Text = "16、手术类别：";
            this.xrLabel57.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel57.WordWrap = false;
            // 
            // xrLabel61
            // 
            this.xrLabel61.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel61.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel61.LocationFloat = new DevExpress.Utils.PointFloat(130.6665F, 540.3333F);
            this.xrLabel61.Name = "xrLabel61";
            this.xrLabel61.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel61.SizeF = new System.Drawing.SizeF(391.4585F, 26F);
            this.xrLabel61.StylePriority.UseBorders = false;
            this.xrLabel61.StylePriority.UseFont = false;
            this.xrLabel61.StylePriority.UsePadding = false;
            this.xrLabel61.StylePriority.UseTextAlignment = false;
            this.xrLabel61.Text = "1.浅层组织手术 2.深部组织手术 3.器官手术 4.腔隙手术";
            this.xrLabel61.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel61.WordWrap = false;
            // 
            // xrL_SSLB
            // 
            this.xrL_SSLB.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SSLB.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SSLB.LocationFloat = new DevExpress.Utils.PointFloat(98.62495F, 540.3334F);
            this.xrL_SSLB.Name = "xrL_SSLB";
            this.xrL_SSLB.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SSLB.SizeF = new System.Drawing.SizeF(28.00004F, 26.00006F);
            this.xrL_SSLB.StylePriority.UseBorders = false;
            this.xrL_SSLB.StylePriority.UseFont = false;
            this.xrL_SSLB.StylePriority.UsePadding = false;
            this.xrL_SSLB.StylePriority.UseTextAlignment = false;
            this.xrL_SSLB.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SSLB.WordWrap = false;
            // 
            // xrLabel62
            // 
            this.xrLabel62.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel62.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel62.LocationFloat = new DevExpress.Utils.PointFloat(7.947286E-06F, 566.3334F);
            this.xrLabel62.Name = "xrLabel62";
            this.xrLabel62.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel62.SizeF = new System.Drawing.SizeF(98.62497F, 26.00006F);
            this.xrLabel62.StylePriority.UseBorders = false;
            this.xrLabel62.StylePriority.UseFont = false;
            this.xrLabel62.StylePriority.UsePadding = false;
            this.xrLabel62.StylePriority.UseTextAlignment = false;
            this.xrLabel62.Text = "17、麻醉分级：";
            this.xrLabel62.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel62.WordWrap = false;
            // 
            // xrL_MZFJ
            // 
            this.xrL_MZFJ.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_MZFJ.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_MZFJ.LocationFloat = new DevExpress.Utils.PointFloat(98.62497F, 566.3336F);
            this.xrL_MZFJ.Name = "xrL_MZFJ";
            this.xrL_MZFJ.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_MZFJ.SizeF = new System.Drawing.SizeF(28.00005F, 26.00012F);
            this.xrL_MZFJ.StylePriority.UseBorders = false;
            this.xrL_MZFJ.StylePriority.UseFont = false;
            this.xrL_MZFJ.StylePriority.UsePadding = false;
            this.xrL_MZFJ.StylePriority.UseTextAlignment = false;
            this.xrL_MZFJ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_MZFJ.WordWrap = false;
            // 
            // xrLabel64
            // 
            this.xrLabel64.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel64.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel64.LocationFloat = new DevExpress.Utils.PointFloat(130.6665F, 566.3336F);
            this.xrLabel64.Name = "xrLabel64";
            this.xrLabel64.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel64.SizeF = new System.Drawing.SizeF(241.75F, 26F);
            this.xrLabel64.StylePriority.UseBorders = false;
            this.xrLabel64.StylePriority.UseFont = false;
            this.xrLabel64.StylePriority.UsePadding = false;
            this.xrLabel64.StylePriority.UseTextAlignment = false;
            this.xrLabel64.Text = "1.一级 2.二级 3.三级 4.四级 5.五级";
            this.xrLabel64.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel64.WordWrap = false;
            // 
            // xrLabel65
            // 
            this.xrLabel65.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel65.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel65.LocationFloat = new DevExpress.Utils.PointFloat(372.4166F, 566.3337F);
            this.xrLabel65.Name = "xrLabel65";
            this.xrLabel65.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel65.SizeF = new System.Drawing.SizeF(82.3335F, 26.00006F);
            this.xrLabel65.StylePriority.UseBorders = false;
            this.xrLabel65.StylePriority.UseFont = false;
            this.xrLabel65.StylePriority.UsePadding = false;
            this.xrLabel65.StylePriority.UseTextAlignment = false;
            this.xrLabel65.Text = "麻醉并发症：";
            this.xrLabel65.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel65.WordWrap = false;
            // 
            // xrLabel66
            // 
            this.xrLabel66.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel66.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel66.LocationFloat = new DevExpress.Utils.PointFloat(486.7916F, 566.3334F);
            this.xrLabel66.Name = "xrLabel66";
            this.xrLabel66.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel66.SizeF = new System.Drawing.SizeF(80.79169F, 26.00006F);
            this.xrLabel66.StylePriority.UseBorders = false;
            this.xrLabel66.StylePriority.UseFont = false;
            this.xrLabel66.StylePriority.UsePadding = false;
            this.xrLabel66.StylePriority.UseTextAlignment = false;
            this.xrLabel66.Text = "1.有  2.无";
            this.xrLabel66.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel66.WordWrap = false;
            // 
            // xrL_MZBFZ
            // 
            this.xrL_MZBFZ.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_MZBFZ.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_MZBFZ.LocationFloat = new DevExpress.Utils.PointFloat(454.7501F, 566.3334F);
            this.xrL_MZBFZ.Name = "xrL_MZBFZ";
            this.xrL_MZBFZ.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_MZBFZ.SizeF = new System.Drawing.SizeF(28F, 26.00006F);
            this.xrL_MZBFZ.StylePriority.UseBorders = false;
            this.xrL_MZBFZ.StylePriority.UseFont = false;
            this.xrL_MZBFZ.StylePriority.UsePadding = false;
            this.xrL_MZBFZ.StylePriority.UseTextAlignment = false;
            this.xrL_MZBFZ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_MZBFZ.WordWrap = false;
            // 
            // xrLabel63
            // 
            this.xrLabel63.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel63.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel63.LocationFloat = new DevExpress.Utils.PointFloat(0F, 592.3336F);
            this.xrLabel63.Name = "xrLabel63";
            this.xrLabel63.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel63.SizeF = new System.Drawing.SizeF(173.1666F, 26.00006F);
            this.xrLabel63.StylePriority.UseBorders = false;
            this.xrLabel63.StylePriority.UseFont = false;
            this.xrLabel63.StylePriority.UsePadding = false;
            this.xrLabel63.StylePriority.UseTextAlignment = false;
            this.xrLabel63.Text = "18、手术是否带有附带手术：";
            this.xrLabel63.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel63.WordWrap = false;
            // 
            // xrL_SSSFDYFDSS
            // 
            this.xrL_SSSFDYFDSS.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SSSFDYFDSS.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SSSFDYFDSS.LocationFloat = new DevExpress.Utils.PointFloat(174.75F, 592.3336F);
            this.xrL_SSSFDYFDSS.Name = "xrL_SSSFDYFDSS";
            this.xrL_SSSFDYFDSS.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SSSFDYFDSS.SizeF = new System.Drawing.SizeF(28.00002F, 26.00006F);
            this.xrL_SSSFDYFDSS.StylePriority.UseBorders = false;
            this.xrL_SSSFDYFDSS.StylePriority.UseFont = false;
            this.xrL_SSSFDYFDSS.StylePriority.UsePadding = false;
            this.xrL_SSSFDYFDSS.StylePriority.UseTextAlignment = false;
            this.xrL_SSSFDYFDSS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SSSFDYFDSS.WordWrap = false;
            // 
            // xrLabel68
            // 
            this.xrLabel68.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel68.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel68.LocationFloat = new DevExpress.Utils.PointFloat(206.7916F, 592.3336F);
            this.xrLabel68.Name = "xrLabel68";
            this.xrLabel68.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel68.SizeF = new System.Drawing.SizeF(80.79172F, 26.00006F);
            this.xrLabel68.StylePriority.UseBorders = false;
            this.xrLabel68.StylePriority.UseFont = false;
            this.xrLabel68.StylePriority.UsePadding = false;
            this.xrLabel68.StylePriority.UseTextAlignment = false;
            this.xrLabel68.Text = "1.有  2.无";
            this.xrLabel68.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel68.WordWrap = false;
            // 
            // xrLabel67
            // 
            this.xrLabel67.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel67.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel67.LocationFloat = new DevExpress.Utils.PointFloat(7.947286E-06F, 618.3336F);
            this.xrLabel67.Name = "xrLabel67";
            this.xrLabel67.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel67.SizeF = new System.Drawing.SizeF(173.1666F, 26.00012F);
            this.xrLabel67.StylePriority.UseBorders = false;
            this.xrLabel67.StylePriority.UseFont = false;
            this.xrLabel67.StylePriority.UsePadding = false;
            this.xrLabel67.StylePriority.UseTextAlignment = false;
            this.xrLabel67.Text = "19、手术是否有并发症：";
            this.xrLabel67.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel67.WordWrap = false;
            // 
            // xrL_SSSFYBFZ
            // 
            this.xrL_SSSFYBFZ.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SSSFYBFZ.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SSSFYBFZ.LocationFloat = new DevExpress.Utils.PointFloat(174.7501F, 618.3337F);
            this.xrL_SSSFYBFZ.Name = "xrL_SSSFYBFZ";
            this.xrL_SSSFYBFZ.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SSSFYBFZ.SizeF = new System.Drawing.SizeF(28.00003F, 26.00012F);
            this.xrL_SSSFYBFZ.StylePriority.UseBorders = false;
            this.xrL_SSSFYBFZ.StylePriority.UseFont = false;
            this.xrL_SSSFYBFZ.StylePriority.UsePadding = false;
            this.xrL_SSSFYBFZ.StylePriority.UseTextAlignment = false;
            this.xrL_SSSFYBFZ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SSSFYBFZ.WordWrap = false;
            // 
            // xrLabel70
            // 
            this.xrLabel70.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel70.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel70.LocationFloat = new DevExpress.Utils.PointFloat(206.7916F, 618.3337F);
            this.xrLabel70.Name = "xrLabel70";
            this.xrLabel70.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel70.SizeF = new System.Drawing.SizeF(80.79172F, 26.00012F);
            this.xrLabel70.StylePriority.UseBorders = false;
            this.xrLabel70.StylePriority.UseFont = false;
            this.xrLabel70.StylePriority.UsePadding = false;
            this.xrLabel70.StylePriority.UseTextAlignment = false;
            this.xrLabel70.Text = "1.有  2.无";
            this.xrLabel70.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel70.WordWrap = false;
            // 
            // xrLabel69
            // 
            this.xrLabel69.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel69.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel69.LocationFloat = new DevExpress.Utils.PointFloat(290.0831F, 618.3337F);
            this.xrLabel69.Name = "xrLabel69";
            this.xrLabel69.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel69.SizeF = new System.Drawing.SizeF(106.5836F, 26.00006F);
            this.xrLabel69.StylePriority.UseBorders = false;
            this.xrLabel69.StylePriority.UseFont = false;
            this.xrLabel69.StylePriority.UsePadding = false;
            this.xrLabel69.StylePriority.UseTextAlignment = false;
            this.xrLabel69.Text = "手术并发症名称：";
            this.xrLabel69.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel69.WordWrap = false;
            // 
            // xrL_SSBFZMC
            // 
            this.xrL_SSBFZMC.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_SSBFZMC.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SSBFZMC.LocationFloat = new DevExpress.Utils.PointFloat(403.2083F, 618.3337F);
            this.xrL_SSBFZMC.Name = "xrL_SSBFZMC";
            this.xrL_SSBFZMC.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_SSBFZMC.SizeF = new System.Drawing.SizeF(320.6249F, 26F);
            this.xrL_SSBFZMC.StylePriority.UseBorders = false;
            this.xrL_SSBFZMC.StylePriority.UseFont = false;
            this.xrL_SSBFZMC.StylePriority.UsePadding = false;
            this.xrL_SSBFZMC.StylePriority.UseTextAlignment = false;
            this.xrL_SSBFZMC.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrL_SSBFZMC.WordWrap = false;
            // 
            // xrLabel71
            // 
            this.xrLabel71.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel71.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel71.LocationFloat = new DevExpress.Utils.PointFloat(190.7083F, 644.3338F);
            this.xrLabel71.Name = "xrLabel71";
            this.xrLabel71.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel71.SizeF = new System.Drawing.SizeF(80.79176F, 26F);
            this.xrLabel71.StylePriority.UseBorders = false;
            this.xrLabel71.StylePriority.UseFont = false;
            this.xrLabel71.StylePriority.UsePadding = false;
            this.xrLabel71.StylePriority.UseTextAlignment = false;
            this.xrLabel71.Text = "1.是  2.否";
            this.xrLabel71.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel71.WordWrap = false;
            // 
            // xrL_SSBWSFGR
            // 
            this.xrL_SSBWSFGR.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SSBWSFGR.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SSBWSFGR.LocationFloat = new DevExpress.Utils.PointFloat(158.6666F, 644.3338F);
            this.xrL_SSBWSFGR.Name = "xrL_SSBWSFGR";
            this.xrL_SSBWSFGR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SSBWSFGR.SizeF = new System.Drawing.SizeF(28.00006F, 26F);
            this.xrL_SSBWSFGR.StylePriority.UseBorders = false;
            this.xrL_SSBWSFGR.StylePriority.UseFont = false;
            this.xrL_SSBWSFGR.StylePriority.UsePadding = false;
            this.xrL_SSBWSFGR.StylePriority.UseTextAlignment = false;
            this.xrL_SSBWSFGR.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SSBWSFGR.WordWrap = false;
            // 
            // xrLabel73
            // 
            this.xrLabel73.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel73.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel73.LocationFloat = new DevExpress.Utils.PointFloat(0F, 644.3337F);
            this.xrLabel73.Name = "xrLabel73";
            this.xrLabel73.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel73.SizeF = new System.Drawing.SizeF(154.625F, 26.00006F);
            this.xrLabel73.StylePriority.UseBorders = false;
            this.xrLabel73.StylePriority.UseFont = false;
            this.xrLabel73.StylePriority.UsePadding = false;
            this.xrLabel73.StylePriority.UseTextAlignment = false;
            this.xrLabel73.Text = "20、手术部位是否感染：";
            this.xrLabel73.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel73.WordWrap = false;
            // 
            // xrLabel74
            // 
            this.xrLabel74.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel74.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel74.LocationFloat = new DevExpress.Utils.PointFloat(271.5F, 644.3338F);
            this.xrLabel74.Name = "xrLabel74";
            this.xrLabel74.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel74.SizeF = new System.Drawing.SizeF(82.33356F, 26.00006F);
            this.xrLabel74.StylePriority.UseBorders = false;
            this.xrLabel74.StylePriority.UseFont = false;
            this.xrLabel74.StylePriority.UsePadding = false;
            this.xrLabel74.StylePriority.UseTextAlignment = false;
            this.xrLabel74.Text = "感染部位：";
            this.xrLabel74.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel74.WordWrap = false;
            // 
            // xrL_GRBW
            // 
            this.xrL_GRBW.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_GRBW.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_GRBW.LocationFloat = new DevExpress.Utils.PointFloat(353.8335F, 644.3335F);
            this.xrL_GRBW.Name = "xrL_GRBW";
            this.xrL_GRBW.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_GRBW.SizeF = new System.Drawing.SizeF(28F, 26F);
            this.xrL_GRBW.StylePriority.UseBorders = false;
            this.xrL_GRBW.StylePriority.UseFont = false;
            this.xrL_GRBW.StylePriority.UsePadding = false;
            this.xrL_GRBW.StylePriority.UseTextAlignment = false;
            this.xrL_GRBW.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_GRBW.WordWrap = false;
            // 
            // xrLabel76
            // 
            this.xrLabel76.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel76.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel76.LocationFloat = new DevExpress.Utils.PointFloat(385.875F, 644.3335F);
            this.xrLabel76.Name = "xrLabel76";
            this.xrLabel76.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel76.SizeF = new System.Drawing.SizeF(337.9582F, 26F);
            this.xrLabel76.StylePriority.UseBorders = false;
            this.xrLabel76.StylePriority.UseFont = false;
            this.xrLabel76.StylePriority.UsePadding = false;
            this.xrLabel76.StylePriority.UseTextAlignment = false;
            this.xrLabel76.Text = "1.切口浅部感染 2.切口深部感染 3.器官感染 4.腔隙感染";
            this.xrLabel76.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel76.WordWrap = false;
            // 
            // xrLabel72
            // 
            this.xrLabel72.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel72.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel72.LocationFloat = new DevExpress.Utils.PointFloat(162.7083F, 670.3339F);
            this.xrLabel72.Name = "xrLabel72";
            this.xrLabel72.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel72.SizeF = new System.Drawing.SizeF(80.79176F, 26F);
            this.xrLabel72.StylePriority.UseBorders = false;
            this.xrLabel72.StylePriority.UseFont = false;
            this.xrLabel72.StylePriority.UsePadding = false;
            this.xrLabel72.StylePriority.UseTextAlignment = false;
            this.xrLabel72.Text = "1.是  2.否";
            this.xrLabel72.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel72.WordWrap = false;
            // 
            // xrL_SZBDJC
            // 
            this.xrL_SZBDJC.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SZBDJC.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SZBDJC.LocationFloat = new DevExpress.Utils.PointFloat(130.6665F, 670.3339F);
            this.xrL_SZBDJC.Name = "xrL_SZBDJC";
            this.xrL_SZBDJC.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SZBDJC.SizeF = new System.Drawing.SizeF(28.00008F, 26F);
            this.xrL_SZBDJC.StylePriority.UseBorders = false;
            this.xrL_SZBDJC.StylePriority.UseFont = false;
            this.xrL_SZBDJC.StylePriority.UsePadding = false;
            this.xrL_SZBDJC.StylePriority.UseTextAlignment = false;
            this.xrL_SZBDJC.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SZBDJC.WordWrap = false;
            // 
            // xrLabel77
            // 
            this.xrLabel77.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel77.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel77.LocationFloat = new DevExpress.Utils.PointFloat(7.947286E-06F, 670.3338F);
            this.xrLabel77.Name = "xrLabel77";
            this.xrLabel77.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel77.SizeF = new System.Drawing.SizeF(126.625F, 26.00006F);
            this.xrLabel77.StylePriority.UseBorders = false;
            this.xrLabel77.StylePriority.UseFont = false;
            this.xrLabel77.StylePriority.UsePadding = false;
            this.xrLabel77.StylePriority.UseTextAlignment = false;
            this.xrLabel77.Text = "21、术中冰冻检查：";
            this.xrLabel77.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel77.WordWrap = false;
            // 
            // xrLabel75
            // 
            this.xrLabel75.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel75.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel75.LocationFloat = new DevExpress.Utils.PointFloat(0F, 696.3338F);
            this.xrLabel75.Name = "xrLabel75";
            this.xrLabel75.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel75.SizeF = new System.Drawing.SizeF(243.5F, 26.00006F);
            this.xrLabel75.StylePriority.UseBorders = false;
            this.xrLabel75.StylePriority.UseFont = false;
            this.xrLabel75.StylePriority.UsePadding = false;
            this.xrLabel75.StylePriority.UseTextAlignment = false;
            this.xrLabel75.Text = "    手术冰冻与石蜡病理诊断符合情况：";
            this.xrLabel75.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel75.WordWrap = false;
            // 
            // xrLabel78
            // 
            this.xrLabel78.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel78.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel78.LocationFloat = new DevExpress.Utils.PointFloat(273.0417F, 696.3338F);
            this.xrLabel78.Name = "xrLabel78";
            this.xrLabel78.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel78.SizeF = new System.Drawing.SizeF(236.4583F, 26F);
            this.xrLabel78.StylePriority.UseBorders = false;
            this.xrLabel78.StylePriority.UseFont = false;
            this.xrLabel78.StylePriority.UsePadding = false;
            this.xrLabel78.StylePriority.UseTextAlignment = false;
            this.xrLabel78.Text = " 0.未做 1.符合 2.不符合";
            this.xrLabel78.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel78.WordWrap = false;
            // 
            // xrL_SSBDYZLBLZD
            // 
            this.xrL_SSBDYZLBLZD.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SSBDYZLBLZD.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SSBDYZLBLZD.LocationFloat = new DevExpress.Utils.PointFloat(243.4999F, 696.3338F);
            this.xrL_SSBDYZLBLZD.Name = "xrL_SSBDYZLBLZD";
            this.xrL_SSBDYZLBLZD.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SSBDYZLBLZD.SizeF = new System.Drawing.SizeF(28.00006F, 26F);
            this.xrL_SSBDYZLBLZD.StylePriority.UseBorders = false;
            this.xrL_SSBDYZLBLZD.StylePriority.UseFont = false;
            this.xrL_SSBDYZLBLZD.StylePriority.UsePadding = false;
            this.xrL_SSBDYZLBLZD.StylePriority.UseTextAlignment = false;
            this.xrL_SSBDYZLBLZD.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SSBDYZLBLZD.WordWrap = false;
            // 
            // xrLabel79
            // 
            this.xrLabel79.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel79.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel79.LocationFloat = new DevExpress.Utils.PointFloat(0F, 722.3339F);
            this.xrLabel79.Name = "xrLabel79";
            this.xrLabel79.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel79.SizeF = new System.Drawing.SizeF(144.3334F, 26.00012F);
            this.xrLabel79.StylePriority.UseBorders = false;
            this.xrLabel79.StylePriority.UseFont = false;
            this.xrLabel79.StylePriority.UsePadding = false;
            this.xrLabel79.StylePriority.UseTextAlignment = false;
            this.xrLabel79.Text = "22、有无再手术计划：";
            this.xrLabel79.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel79.WordWrap = false;
            // 
            // xrLabel80
            // 
            this.xrLabel80.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel80.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel80.LocationFloat = new DevExpress.Utils.PointFloat(178.7915F, 722.3338F);
            this.xrLabel80.Name = "xrLabel80";
            this.xrLabel80.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel80.SizeF = new System.Drawing.SizeF(80.79172F, 26.00006F);
            this.xrLabel80.StylePriority.UseBorders = false;
            this.xrLabel80.StylePriority.UseFont = false;
            this.xrLabel80.StylePriority.UsePadding = false;
            this.xrLabel80.StylePriority.UseTextAlignment = false;
            this.xrLabel80.Text = "1.有  2.无";
            this.xrLabel80.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel80.WordWrap = false;
            // 
            // xrL_YWZSSJH
            // 
            this.xrL_YWZSSJH.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_YWZSSJH.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_YWZSSJH.LocationFloat = new DevExpress.Utils.PointFloat(146.75F, 722.3338F);
            this.xrL_YWZSSJH.Name = "xrL_YWZSSJH";
            this.xrL_YWZSSJH.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_YWZSSJH.SizeF = new System.Drawing.SizeF(28.00003F, 26.00006F);
            this.xrL_YWZSSJH.StylePriority.UseBorders = false;
            this.xrL_YWZSSJH.StylePriority.UseFont = false;
            this.xrL_YWZSSJH.StylePriority.UsePadding = false;
            this.xrL_YWZSSJH.StylePriority.UseTextAlignment = false;
            this.xrL_YWZSSJH.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_YWZSSJH.WordWrap = false;
            // 
            // xrLabel81
            // 
            this.xrLabel81.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel81.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel81.LocationFloat = new DevExpress.Utils.PointFloat(7.947286E-06F, 748.334F);
            this.xrLabel81.Name = "xrLabel81";
            this.xrLabel81.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel81.SizeF = new System.Drawing.SizeF(126.625F, 26.00012F);
            this.xrLabel81.StylePriority.UseBorders = false;
            this.xrLabel81.StylePriority.UseFont = false;
            this.xrLabel81.StylePriority.UsePadding = false;
            this.xrLabel81.StylePriority.UseTextAlignment = false;
            this.xrLabel81.Text = "    非计划再手术：";
            this.xrLabel81.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel81.WordWrap = false;
            // 
            // xrL_FJHZSS
            // 
            this.xrL_FJHZSS.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_FJHZSS.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_FJHZSS.LocationFloat = new DevExpress.Utils.PointFloat(128.25F, 748.334F);
            this.xrL_FJHZSS.Name = "xrL_FJHZSS";
            this.xrL_FJHZSS.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_FJHZSS.SizeF = new System.Drawing.SizeF(26.37505F, 26.00006F);
            this.xrL_FJHZSS.StylePriority.UseBorders = false;
            this.xrL_FJHZSS.StylePriority.UseFont = false;
            this.xrL_FJHZSS.StylePriority.UsePadding = false;
            this.xrL_FJHZSS.StylePriority.UseTextAlignment = false;
            this.xrL_FJHZSS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_FJHZSS.WordWrap = false;
            // 
            // xrLabel83
            // 
            this.xrLabel83.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel83.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel83.LocationFloat = new DevExpress.Utils.PointFloat(160.2915F, 748.334F);
            this.xrLabel83.Name = "xrLabel83";
            this.xrLabel83.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel83.SizeF = new System.Drawing.SizeF(281.7501F, 26.00006F);
            this.xrLabel83.StylePriority.UseBorders = false;
            this.xrLabel83.StylePriority.UseFont = false;
            this.xrLabel83.StylePriority.UsePadding = false;
            this.xrLabel83.StylePriority.UseTextAlignment = false;
            this.xrLabel83.Text = "1.是（并发手术、医源性手术、非预期手术）2.否";
            this.xrLabel83.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel83.WordWrap = false;
            // 
            // xrLabel84
            // 
            this.xrLabel84.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel84.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel84.LocationFloat = new DevExpress.Utils.PointFloat(0F, 774.3341F);
            this.xrLabel84.Name = "xrLabel84";
            this.xrLabel84.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel84.SizeF = new System.Drawing.SizeF(195.2499F, 26.00018F);
            this.xrLabel84.StylePriority.UseBorders = false;
            this.xrLabel84.StylePriority.UseFont = false;
            this.xrLabel84.StylePriority.UsePadding = false;
            this.xrLabel84.StylePriority.UseTextAlignment = false;
            this.xrLabel84.Text = "23、手术过程中是否有异物遗留：";
            this.xrLabel84.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel84.WordWrap = false;
            // 
            // xrL_SSGCZSFYYWYL
            // 
            this.xrL_SSGCZSFYYWYL.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SSGCZSFYYWYL.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SSGCZSFYYWYL.LocationFloat = new DevExpress.Utils.PointFloat(195.25F, 774.3342F);
            this.xrL_SSGCZSFYYWYL.Name = "xrL_SSGCZSFYYWYL";
            this.xrL_SSGCZSFYYWYL.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SSGCZSFYYWYL.SizeF = new System.Drawing.SizeF(28.00002F, 26.00012F);
            this.xrL_SSGCZSFYYWYL.StylePriority.UseBorders = false;
            this.xrL_SSGCZSFYYWYL.StylePriority.UseFont = false;
            this.xrL_SSGCZSFYYWYL.StylePriority.UsePadding = false;
            this.xrL_SSGCZSFYYWYL.StylePriority.UseTextAlignment = false;
            this.xrL_SSGCZSFYYWYL.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SSGCZSFYYWYL.WordWrap = false;
            // 
            // xrLabel86
            // 
            this.xrLabel86.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel86.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel86.LocationFloat = new DevExpress.Utils.PointFloat(227.2915F, 774.3342F);
            this.xrLabel86.Name = "xrLabel86";
            this.xrLabel86.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel86.SizeF = new System.Drawing.SizeF(80.7917F, 26.00012F);
            this.xrLabel86.StylePriority.UseBorders = false;
            this.xrLabel86.StylePriority.UseFont = false;
            this.xrLabel86.StylePriority.UsePadding = false;
            this.xrLabel86.StylePriority.UseTextAlignment = false;
            this.xrLabel86.Text = "1.有  2.无";
            this.xrLabel86.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel86.WordWrap = false;
            // 
            // xrLabel82
            // 
            this.xrLabel82.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel82.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel82.LocationFloat = new DevExpress.Utils.PointFloat(7.947286E-06F, 800.3343F);
            this.xrLabel82.Name = "xrLabel82";
            this.xrLabel82.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel82.SizeF = new System.Drawing.SizeF(144.3333F, 26.00024F);
            this.xrLabel82.StylePriority.UseBorders = false;
            this.xrLabel82.StylePriority.UseFont = false;
            this.xrLabel82.StylePriority.UsePadding = false;
            this.xrLabel82.StylePriority.UseTextAlignment = false;
            this.xrLabel82.Text = "24、是否有医源性伤害：";
            this.xrLabel82.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel82.WordWrap = false;
            // 
            // xrL_SFYYYXSH
            // 
            this.xrL_SFYYYXSH.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SFYYYXSH.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SFYYYXSH.LocationFloat = new DevExpress.Utils.PointFloat(146.75F, 800.3344F);
            this.xrL_SFYYYXSH.Name = "xrL_SFYYYXSH";
            this.xrL_SFYYYXSH.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SFYYYXSH.SizeF = new System.Drawing.SizeF(28.00003F, 26.00018F);
            this.xrL_SFYYYXSH.StylePriority.UseBorders = false;
            this.xrL_SFYYYXSH.StylePriority.UseFont = false;
            this.xrL_SFYYYXSH.StylePriority.UsePadding = false;
            this.xrL_SFYYYXSH.StylePriority.UseTextAlignment = false;
            this.xrL_SFYYYXSH.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SFYYYXSH.WordWrap = false;
            // 
            // xrLabel87
            // 
            this.xrLabel87.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel87.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel87.LocationFloat = new DevExpress.Utils.PointFloat(178.7915F, 800.3344F);
            this.xrLabel87.Name = "xrLabel87";
            this.xrLabel87.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel87.SizeF = new System.Drawing.SizeF(80.79169F, 26.00018F);
            this.xrLabel87.StylePriority.UseBorders = false;
            this.xrLabel87.StylePriority.UseFont = false;
            this.xrLabel87.StylePriority.UsePadding = false;
            this.xrLabel87.StylePriority.UseTextAlignment = false;
            this.xrLabel87.Text = "1.有  2.无";
            this.xrLabel87.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel87.WordWrap = false;
            // 
            // xrLabel85
            // 
            this.xrLabel85.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel85.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel85.LocationFloat = new DevExpress.Utils.PointFloat(2.384186E-05F, 826.3345F);
            this.xrLabel85.Name = "xrLabel85";
            this.xrLabel85.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel85.SizeF = new System.Drawing.SizeF(195.2499F, 26.00012F);
            this.xrLabel85.StylePriority.UseBorders = false;
            this.xrLabel85.StylePriority.UseFont = false;
            this.xrLabel85.StylePriority.UsePadding = false;
            this.xrLabel85.StylePriority.UseTextAlignment = false;
            this.xrLabel85.Text = "    医源性伤害类型：是否死亡";
            this.xrLabel85.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel85.WordWrap = false;
            // 
            // xrLabel88
            // 
            this.xrLabel88.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel88.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel88.LocationFloat = new DevExpress.Utils.PointFloat(227.2916F, 826.3346F);
            this.xrLabel88.Name = "xrLabel88";
            this.xrLabel88.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel88.SizeF = new System.Drawing.SizeF(80.79176F, 26.00006F);
            this.xrLabel88.StylePriority.UseBorders = false;
            this.xrLabel88.StylePriority.UseFont = false;
            this.xrLabel88.StylePriority.UsePadding = false;
            this.xrLabel88.StylePriority.UseTextAlignment = false;
            this.xrLabel88.Text = "1.是  2.否";
            this.xrLabel88.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel88.WordWrap = false;
            // 
            // xrL_YYXSHLX_SFSW
            // 
            this.xrL_YYXSHLX_SFSW.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_YYXSHLX_SFSW.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_YYXSHLX_SFSW.LocationFloat = new DevExpress.Utils.PointFloat(195.25F, 826.3346F);
            this.xrL_YYXSHLX_SFSW.Name = "xrL_YYXSHLX_SFSW";
            this.xrL_YYXSHLX_SFSW.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_YYXSHLX_SFSW.SizeF = new System.Drawing.SizeF(28.00006F, 26.00006F);
            this.xrL_YYXSHLX_SFSW.StylePriority.UseBorders = false;
            this.xrL_YYXSHLX_SFSW.StylePriority.UseFont = false;
            this.xrL_YYXSHLX_SFSW.StylePriority.UsePadding = false;
            this.xrL_YYXSHLX_SFSW.StylePriority.UseTextAlignment = false;
            this.xrL_YYXSHLX_SFSW.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_YYXSHLX_SFSW.WordWrap = false;
            // 
            // xrLabel90
            // 
            this.xrLabel90.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel90.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel90.LocationFloat = new DevExpress.Utils.PointFloat(315.0416F, 826.3346F);
            this.xrLabel90.Name = "xrLabel90";
            this.xrLabel90.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel90.SizeF = new System.Drawing.SizeF(66.79196F, 26.00012F);
            this.xrLabel90.StylePriority.UseBorders = false;
            this.xrLabel90.StylePriority.UseFont = false;
            this.xrLabel90.StylePriority.UsePadding = false;
            this.xrLabel90.StylePriority.UseTextAlignment = false;
            this.xrLabel90.Text = "死亡原因";
            this.xrLabel90.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel90.WordWrap = false;
            // 
            // xrLabel91
            // 
            this.xrLabel91.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel91.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel91.LocationFloat = new DevExpress.Utils.PointFloat(415.9582F, 826.3345F);
            this.xrLabel91.Name = "xrLabel91";
            this.xrLabel91.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel91.SizeF = new System.Drawing.SizeF(222.4584F, 26.00006F);
            this.xrLabel91.StylePriority.UseBorders = false;
            this.xrLabel91.StylePriority.UseFont = false;
            this.xrLabel91.StylePriority.UsePadding = false;
            this.xrLabel91.StylePriority.UseTextAlignment = false;
            this.xrLabel91.Text = "1.用药错误 2.术后并发症 3.麻醉死亡";
            this.xrLabel91.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel91.WordWrap = false;
            // 
            // xrL_YYXSH_SWYY
            // 
            this.xrL_YYXSH_SWYY.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_YYXSH_SWYY.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_YYXSH_SWYY.LocationFloat = new DevExpress.Utils.PointFloat(383.9165F, 826.3345F);
            this.xrL_YYXSH_SWYY.Name = "xrL_YYXSH_SWYY";
            this.xrL_YYXSH_SWYY.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_YYXSH_SWYY.SizeF = new System.Drawing.SizeF(28.00006F, 26.00006F);
            this.xrL_YYXSH_SWYY.StylePriority.UseBorders = false;
            this.xrL_YYXSH_SWYY.StylePriority.UseFont = false;
            this.xrL_YYXSH_SWYY.StylePriority.UsePadding = false;
            this.xrL_YYXSH_SWYY.StylePriority.UseTextAlignment = false;
            this.xrL_YYXSH_SWYY.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_YYXSH_SWYY.WordWrap = false;
            // 
            // xrLabel89
            // 
            this.xrLabel89.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel89.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel89.LocationFloat = new DevExpress.Utils.PointFloat(0F, 852.3347F);
            this.xrLabel89.Name = "xrLabel89";
            this.xrLabel89.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel89.SizeF = new System.Drawing.SizeF(144.3334F, 26.00024F);
            this.xrLabel89.StylePriority.UseBorders = false;
            this.xrLabel89.StylePriority.UseFont = false;
            this.xrLabel89.StylePriority.UsePadding = false;
            this.xrLabel89.StylePriority.UseTextAlignment = false;
            this.xrLabel89.Text = "25、产科患者出院：";
            this.xrLabel89.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel89.WordWrap = false;
            // 
            // xrLabel92
            // 
            this.xrLabel92.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel92.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel92.LocationFloat = new DevExpress.Utils.PointFloat(227.2916F, 878.335F);
            this.xrLabel92.Name = "xrLabel92";
            this.xrLabel92.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel92.SizeF = new System.Drawing.SizeF(80.79175F, 26.00006F);
            this.xrLabel92.StylePriority.UseBorders = false;
            this.xrLabel92.StylePriority.UseFont = false;
            this.xrLabel92.StylePriority.UsePadding = false;
            this.xrLabel92.StylePriority.UseTextAlignment = false;
            this.xrLabel92.Text = "1.是  2.否";
            this.xrLabel92.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel92.WordWrap = false;
            // 
            // xrL_CKHZCY_SFFSCS
            // 
            this.xrL_CKHZCY_SFFSCS.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_CKHZCY_SFFSCS.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_CKHZCY_SFFSCS.LocationFloat = new DevExpress.Utils.PointFloat(195.2499F, 878.335F);
            this.xrL_CKHZCY_SFFSCS.Name = "xrL_CKHZCY_SFFSCS";
            this.xrL_CKHZCY_SFFSCS.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_CKHZCY_SFFSCS.SizeF = new System.Drawing.SizeF(28.00006F, 26.00006F);
            this.xrL_CKHZCY_SFFSCS.StylePriority.UseBorders = false;
            this.xrL_CKHZCY_SFFSCS.StylePriority.UseFont = false;
            this.xrL_CKHZCY_SFFSCS.StylePriority.UsePadding = false;
            this.xrL_CKHZCY_SFFSCS.StylePriority.UseTextAlignment = false;
            this.xrL_CKHZCY_SFFSCS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_CKHZCY_SFFSCS.WordWrap = false;
            // 
            // xrLabel94
            // 
            this.xrLabel94.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel94.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel94.LocationFloat = new DevExpress.Utils.PointFloat(0F, 878.3349F);
            this.xrLabel94.Name = "xrLabel94";
            this.xrLabel94.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel94.SizeF = new System.Drawing.SizeF(195.2499F, 26.00012F);
            this.xrLabel94.StylePriority.UseBorders = false;
            this.xrLabel94.StylePriority.UseFont = false;
            this.xrLabel94.StylePriority.UsePadding = false;
            this.xrLabel94.StylePriority.UseTextAlignment = false;
            this.xrLabel94.Text = "    产科病人是否发生发生产伤";
            this.xrLabel94.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel94.WordWrap = false;
            // 
            // xrLabel93
            // 
            this.xrLabel93.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel93.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel93.LocationFloat = new DevExpress.Utils.PointFloat(312.0416F, 878.335F);
            this.xrLabel93.Name = "xrLabel93";
            this.xrLabel93.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel93.SizeF = new System.Drawing.SizeF(69.79193F, 26.00012F);
            this.xrLabel93.StylePriority.UseBorders = false;
            this.xrLabel93.StylePriority.UseFont = false;
            this.xrLabel93.StylePriority.UsePadding = false;
            this.xrLabel93.StylePriority.UseTextAlignment = false;
            this.xrLabel93.Text = " 分娩方式";
            this.xrLabel93.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel93.WordWrap = false;
            // 
            // xrLabel95
            // 
            this.xrLabel95.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel95.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel95.LocationFloat = new DevExpress.Utils.PointFloat(415.9582F, 878.335F);
            this.xrLabel95.Name = "xrLabel95";
            this.xrLabel95.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel95.SizeF = new System.Drawing.SizeF(222.4584F, 26.00006F);
            this.xrLabel95.StylePriority.UseBorders = false;
            this.xrLabel95.StylePriority.UseFont = false;
            this.xrLabel95.StylePriority.UsePadding = false;
            this.xrLabel95.StylePriority.UseTextAlignment = false;
            this.xrLabel95.Text = "1 .器械辅助分娩 2.非器械辅助分娩";
            this.xrLabel95.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel95.WordWrap = false;
            // 
            // xrL_CKHZCY_FMFS
            // 
            this.xrL_CKHZCY_FMFS.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_CKHZCY_FMFS.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_CKHZCY_FMFS.LocationFloat = new DevExpress.Utils.PointFloat(383.9165F, 878.335F);
            this.xrL_CKHZCY_FMFS.Name = "xrL_CKHZCY_FMFS";
            this.xrL_CKHZCY_FMFS.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_CKHZCY_FMFS.SizeF = new System.Drawing.SizeF(28.00009F, 26.00006F);
            this.xrL_CKHZCY_FMFS.StylePriority.UseBorders = false;
            this.xrL_CKHZCY_FMFS.StylePriority.UseFont = false;
            this.xrL_CKHZCY_FMFS.StylePriority.UsePadding = false;
            this.xrL_CKHZCY_FMFS.StylePriority.UseTextAlignment = false;
            this.xrL_CKHZCY_FMFS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_CKHZCY_FMFS.WordWrap = false;
            // 
            // xrLabel96
            // 
            this.xrLabel96.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel96.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel96.LocationFloat = new DevExpress.Utils.PointFloat(3.178914E-05F, 904.335F);
            this.xrLabel96.Name = "xrLabel96";
            this.xrLabel96.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel96.SizeF = new System.Drawing.SizeF(96.41669F, 26.00024F);
            this.xrLabel96.StylePriority.UseBorders = false;
            this.xrLabel96.StylePriority.UseFont = false;
            this.xrLabel96.StylePriority.UsePadding = false;
            this.xrLabel96.StylePriority.UseTextAlignment = false;
            this.xrLabel96.Text = "    活产婴儿";
            this.xrLabel96.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel96.WordWrap = false;
            // 
            // xrLabel97
            // 
            this.xrLabel97.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel97.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel97.LocationFloat = new DevExpress.Utils.PointFloat(144.3334F, 904.3353F);
            this.xrLabel97.Name = "xrLabel97";
            this.xrLabel97.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel97.SizeF = new System.Drawing.SizeF(30.41666F, 26F);
            this.xrLabel97.StylePriority.UseBorders = false;
            this.xrLabel97.StylePriority.UseFont = false;
            this.xrLabel97.StylePriority.UsePadding = false;
            this.xrLabel97.StylePriority.UseTextAlignment = false;
            this.xrLabel97.Text = "个";
            this.xrLabel97.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel97.WordWrap = false;
            // 
            // xrL_CKHZCY_HCYE
            // 
            this.xrL_CKHZCY_HCYE.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_CKHZCY_HCYE.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_CKHZCY_HCYE.LocationFloat = new DevExpress.Utils.PointFloat(99.87503F, 904.3353F);
            this.xrL_CKHZCY_HCYE.Name = "xrL_CKHZCY_HCYE";
            this.xrL_CKHZCY_HCYE.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_CKHZCY_HCYE.SizeF = new System.Drawing.SizeF(44.45834F, 26F);
            this.xrL_CKHZCY_HCYE.StylePriority.UseBorders = false;
            this.xrL_CKHZCY_HCYE.StylePriority.UseFont = false;
            this.xrL_CKHZCY_HCYE.StylePriority.UsePadding = false;
            this.xrL_CKHZCY_HCYE.StylePriority.UseTextAlignment = false;
            this.xrL_CKHZCY_HCYE.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_CKHZCY_HCYE.WordWrap = false;
            // 
            // xrLabel98
            // 
            this.xrLabel98.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel98.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel98.LocationFloat = new DevExpress.Utils.PointFloat(178.7915F, 904.3353F);
            this.xrLabel98.Name = "xrLabel98";
            this.xrLabel98.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel98.SizeF = new System.Drawing.SizeF(133.2501F, 26.00006F);
            this.xrLabel98.StylePriority.UseBorders = false;
            this.xrLabel98.StylePriority.UseFont = false;
            this.xrLabel98.StylePriority.UsePadding = false;
            this.xrLabel98.StylePriority.UseTextAlignment = false;
            this.xrLabel98.Text = "新生儿是否发生产伤：";
            this.xrLabel98.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel98.WordWrap = false;
            // 
            // xrLabel99
            // 
            this.xrLabel99.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel99.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel99.LocationFloat = new DevExpress.Utils.PointFloat(347.0832F, 904.3353F);
            this.xrLabel99.Name = "xrLabel99";
            this.xrLabel99.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel99.SizeF = new System.Drawing.SizeF(94.9585F, 26.00012F);
            this.xrLabel99.StylePriority.UseBorders = false;
            this.xrLabel99.StylePriority.UseFont = false;
            this.xrLabel99.StylePriority.UsePadding = false;
            this.xrLabel99.StylePriority.UseTextAlignment = false;
            this.xrLabel99.Text = "1.是  2.否";
            this.xrLabel99.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel99.WordWrap = false;
            // 
            // xrL_CKHZCY_XSESFFSCS
            // 
            this.xrL_CKHZCY_XSESFFSCS.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_CKHZCY_XSESFFSCS.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_CKHZCY_XSESFFSCS.LocationFloat = new DevExpress.Utils.PointFloat(315.0415F, 904.3353F);
            this.xrL_CKHZCY_XSESFFSCS.Name = "xrL_CKHZCY_XSESFFSCS";
            this.xrL_CKHZCY_XSESFFSCS.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_CKHZCY_XSESFFSCS.SizeF = new System.Drawing.SizeF(28.00006F, 26.00012F);
            this.xrL_CKHZCY_XSESFFSCS.StylePriority.UseBorders = false;
            this.xrL_CKHZCY_XSESFFSCS.StylePriority.UseFont = false;
            this.xrL_CKHZCY_XSESFFSCS.StylePriority.UsePadding = false;
            this.xrL_CKHZCY_XSESFFSCS.StylePriority.UseTextAlignment = false;
            this.xrL_CKHZCY_XSESFFSCS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_CKHZCY_XSESFFSCS.WordWrap = false;
            // 
            // xrLabel100
            // 
            this.xrLabel100.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel100.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel100.LocationFloat = new DevExpress.Utils.PointFloat(3.178914E-05F, 930.3353F);
            this.xrLabel100.Name = "xrLabel100";
            this.xrLabel100.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel100.SizeF = new System.Drawing.SizeF(144.3334F, 26.00024F);
            this.xrLabel100.StylePriority.UseBorders = false;
            this.xrLabel100.StylePriority.UseFont = false;
            this.xrLabel100.StylePriority.UsePadding = false;
            this.xrLabel100.StylePriority.UseTextAlignment = false;
            this.xrLabel100.Text = "26、肿瘤患者出院：";
            this.xrLabel100.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel100.WordWrap = false;
            // 
            // xrLabel101
            // 
            this.xrLabel101.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel101.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel101.LocationFloat = new DevExpress.Utils.PointFloat(141.8749F, 956.3356F);
            this.xrLabel101.Name = "xrLabel101";
            this.xrLabel101.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel101.SizeF = new System.Drawing.SizeF(80.79175F, 26.00006F);
            this.xrLabel101.StylePriority.UseBorders = false;
            this.xrLabel101.StylePriority.UseFont = false;
            this.xrLabel101.StylePriority.UsePadding = false;
            this.xrLabel101.StylePriority.UseTextAlignment = false;
            this.xrLabel101.Text = "1.是  2.否";
            this.xrLabel101.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel101.WordWrap = false;
            // 
            // xrL_ZLHZCY_SFZL
            // 
            this.xrL_ZLHZCY_SFZL.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ZLHZCY_SFZL.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ZLHZCY_SFZL.LocationFloat = new DevExpress.Utils.PointFloat(109.8332F, 956.3356F);
            this.xrL_ZLHZCY_SFZL.Name = "xrL_ZLHZCY_SFZL";
            this.xrL_ZLHZCY_SFZL.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ZLHZCY_SFZL.SizeF = new System.Drawing.SizeF(28.00005F, 26.00006F);
            this.xrL_ZLHZCY_SFZL.StylePriority.UseBorders = false;
            this.xrL_ZLHZCY_SFZL.StylePriority.UseFont = false;
            this.xrL_ZLHZCY_SFZL.StylePriority.UsePadding = false;
            this.xrL_ZLHZCY_SFZL.StylePriority.UseTextAlignment = false;
            this.xrL_ZLHZCY_SFZL.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ZLHZCY_SFZL.WordWrap = false;
            // 
            // xrLabel103
            // 
            this.xrLabel103.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel103.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel103.LocationFloat = new DevExpress.Utils.PointFloat(3.178914E-05F, 956.3356F);
            this.xrLabel103.Name = "xrLabel103";
            this.xrLabel103.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel103.SizeF = new System.Drawing.SizeF(109.8332F, 26.00012F);
            this.xrLabel103.StylePriority.UseBorders = false;
            this.xrLabel103.StylePriority.UseFont = false;
            this.xrLabel103.StylePriority.UsePadding = false;
            this.xrLabel103.StylePriority.UseTextAlignment = false;
            this.xrLabel103.Text = "    是否肿瘤患者";
            this.xrLabel103.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel103.WordWrap = false;
            // 
            // xrLabel102
            // 
            this.xrLabel102.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel102.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel102.LocationFloat = new DevExpress.Utils.PointFloat(2.384186E-05F, 982.3358F);
            this.xrLabel102.Name = "xrLabel102";
            this.xrLabel102.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel102.SizeF = new System.Drawing.SizeF(109.8332F, 26.00006F);
            this.xrLabel102.StylePriority.UseBorders = false;
            this.xrLabel102.StylePriority.UseFont = false;
            this.xrLabel102.StylePriority.UsePadding = false;
            this.xrLabel102.StylePriority.UseTextAlignment = false;
            this.xrLabel102.Text = "    最高诊断依据";
            this.xrLabel102.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel102.WordWrap = false;
            // 
            // xrLabel104
            // 
            this.xrLabel104.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel104.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel104.LocationFloat = new DevExpress.Utils.PointFloat(141.8749F, 982.3358F);
            this.xrLabel104.Name = "xrLabel104";
            this.xrLabel104.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel104.SizeF = new System.Drawing.SizeF(600.3751F, 26.00006F);
            this.xrLabel104.StylePriority.UseBorders = false;
            this.xrLabel104.StylePriority.UseFont = false;
            this.xrLabel104.StylePriority.UsePadding = false;
            this.xrLabel104.StylePriority.UseTextAlignment = false;
            this.xrLabel104.Text = "1.临床 2.手术 3.病理（原发） 4.影像学 5.生化、免疫 6.细胞学、血片 7.尸检";
            this.xrLabel104.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel104.WordWrap = false;
            // 
            // xrL_ZLHZCY_ZGZDYJ
            // 
            this.xrL_ZLHZCY_ZGZDYJ.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ZLHZCY_ZGZDYJ.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ZLHZCY_ZGZDYJ.LocationFloat = new DevExpress.Utils.PointFloat(109.8332F, 982.3358F);
            this.xrL_ZLHZCY_ZGZDYJ.Name = "xrL_ZLHZCY_ZGZDYJ";
            this.xrL_ZLHZCY_ZGZDYJ.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ZLHZCY_ZGZDYJ.SizeF = new System.Drawing.SizeF(28.00005F, 26.00006F);
            this.xrL_ZLHZCY_ZGZDYJ.StylePriority.UseBorders = false;
            this.xrL_ZLHZCY_ZGZDYJ.StylePriority.UseFont = false;
            this.xrL_ZLHZCY_ZGZDYJ.StylePriority.UsePadding = false;
            this.xrL_ZLHZCY_ZGZDYJ.StylePriority.UseTextAlignment = false;
            this.xrL_ZLHZCY_ZGZDYJ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ZLHZCY_ZGZDYJ.WordWrap = false;
            // 
            // xrLabel106
            // 
            this.xrLabel106.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel106.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel106.LocationFloat = new DevExpress.Utils.PointFloat(3.178914E-05F, 1008.336F);
            this.xrLabel106.Name = "xrLabel106";
            this.xrLabel106.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel106.SizeF = new System.Drawing.SizeF(109.8332F, 26F);
            this.xrLabel106.StylePriority.UseBorders = false;
            this.xrLabel106.StylePriority.UseFont = false;
            this.xrLabel106.StylePriority.UsePadding = false;
            this.xrLabel106.StylePriority.UseTextAlignment = false;
            this.xrLabel106.Text = "    肿瘤患者出院";
            this.xrLabel106.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel106.WordWrap = false;
            // 
            // xrL_ZLHZCY_ZLFQ
            // 
            this.xrL_ZLHZCY_ZLFQ.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ZLHZCY_ZLFQ.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ZLHZCY_ZLFQ.LocationFloat = new DevExpress.Utils.PointFloat(109.8332F, 1008.336F);
            this.xrL_ZLHZCY_ZLFQ.Name = "xrL_ZLHZCY_ZLFQ";
            this.xrL_ZLHZCY_ZLFQ.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ZLHZCY_ZLFQ.SizeF = new System.Drawing.SizeF(28.00005F, 26.00006F);
            this.xrL_ZLHZCY_ZLFQ.StylePriority.UseBorders = false;
            this.xrL_ZLHZCY_ZLFQ.StylePriority.UseFont = false;
            this.xrL_ZLHZCY_ZLFQ.StylePriority.UsePadding = false;
            this.xrL_ZLHZCY_ZLFQ.StylePriority.UseTextAlignment = false;
            this.xrL_ZLHZCY_ZLFQ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ZLHZCY_ZLFQ.WordWrap = false;
            // 
            // xrLabel108
            // 
            this.xrLabel108.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel108.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel108.LocationFloat = new DevExpress.Utils.PointFloat(141.8749F, 1008.336F);
            this.xrLabel108.Name = "xrLabel108";
            this.xrLabel108.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel108.SizeF = new System.Drawing.SizeF(457.8751F, 26.00012F);
            this.xrLabel108.StylePriority.UseBorders = false;
            this.xrLabel108.StylePriority.UseFont = false;
            this.xrLabel108.StylePriority.UsePadding = false;
            this.xrLabel108.StylePriority.UseTextAlignment = false;
            this.xrLabel108.Text = "1. I期 2. II期  3. III期   4. IV期";
            this.xrLabel108.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel108.WordWrap = false;
            // 
            // FirstHomesAttachedPage
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageHeader});
            this.Margins = new System.Drawing.Printing.Margins(33, 27, 28, 0);
            this.PageHeight = 1169;
            this.PageWidth = 827;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.Version = "17.2";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel xrL_deptname;
        private DevExpress.XtraReports.UI.XRLabel xrLabel18;
        private DevExpress.XtraReports.UI.XRLabel xrLabel10;
        private DevExpress.XtraReports.UI.XRLabel xrL_age;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel xrL_sex;
        private DevExpress.XtraReports.UI.XRLabel xrL_name;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel xrL_bedno;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel xrL_InpNo;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLabel xrL_SFZRYBR;
        private DevExpress.XtraReports.UI.XRLabel xrLabel9;
        private DevExpress.XtraReports.UI.XRLabel xrL_BCRYYSCRYJG;
        private DevExpress.XtraReports.UI.XRLabel xrLabel12;
        private DevExpress.XtraReports.UI.XRLabel xrLabel11;
        private DevExpress.XtraReports.UI.XRLabel xrL_ZRYYY;
        private DevExpress.XtraReports.UI.XRLabel xrLabel14;
        private DevExpress.XtraReports.UI.XRLabel xrLabel13;
        private DevExpress.XtraReports.UI.XRLabel xrL_ZRYBQFX;
        private DevExpress.XtraReports.UI.XRLabel xrLabel16;
        private DevExpress.XtraReports.UI.XRLabel xrLabel23;
        private DevExpress.XtraReports.UI.XRLabel xrL_RYHQZRQ;
        private DevExpress.XtraReports.UI.XRLabel xrLabel15;
        private DevExpress.XtraReports.UI.XRLabel xrL_ZYZDLX;
        private DevExpress.XtraReports.UI.XRLabel xrLabel19;
        private DevExpress.XtraReports.UI.XRLabel xrLabel17;
        private DevExpress.XtraReports.UI.XRLabel xrLabel30;
        private DevExpress.XtraReports.UI.XRLabel xrL_QJCGCS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel25;
        private DevExpress.XtraReports.UI.XRLabel xrLabel20;
        private DevExpress.XtraReports.UI.XRLabel xrL_QJQKQJCS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel29;
        private DevExpress.XtraReports.UI.XRLabel xrL_ZDFUQK_FSXYBL;
        private DevExpress.XtraReports.UI.XRLabel xrL_ZDFUQK_LCYBL;
        private DevExpress.XtraReports.UI.XRLabel xrLabel28;
        private DevExpress.XtraReports.UI.XRLabel xrL_ZDFHQK_SQYSH;
        private DevExpress.XtraReports.UI.XRLabel xrLabel26;
        private DevExpress.XtraReports.UI.XRLabel xrL_ZDFUQK_RYYCY;
        private DevExpress.XtraReports.UI.XRLabel xrLabel24;
        private DevExpress.XtraReports.UI.XRLabel xrL_ZDFUQK_MZYCY;
        private DevExpress.XtraReports.UI.XRLabel xrLabel21;
        private DevExpress.XtraReports.UI.XRLabel xrLabel22;
        private DevExpress.XtraReports.UI.XRLabel xrLabel27;
        private DevExpress.XtraReports.UI.XRLabel xrL_SYQK;
        private DevExpress.XtraReports.UI.XRLabel xrLabel35;
        private DevExpress.XtraReports.UI.XRLabel xrL_SXQK;
        private DevExpress.XtraReports.UI.XRLabel xrLabel37;
        private DevExpress.XtraReports.UI.XRLabel xrLabel32;
        private DevExpress.XtraReports.UI.XRLabel xrL_SYFY;
        private DevExpress.XtraReports.UI.XRLabel xrLabel34;
        private DevExpress.XtraReports.UI.XRLabel xrLabel38;
        private DevExpress.XtraReports.UI.XRLabel xrL_SXFY;
        private DevExpress.XtraReports.UI.XRLabel xrLabel40;
        private DevExpress.XtraReports.UI.XRLabel xrLabel39;
        private DevExpress.XtraReports.UI.XRLabel xrL_YNBL;
        private DevExpress.XtraReports.UI.XRLabel xrLabel42;
        private DevExpress.XtraReports.UI.XRLabel xrLabel33;
        private DevExpress.XtraReports.UI.XRLabel xrL_WZBL;
        private DevExpress.XtraReports.UI.XRLabel xrLabel31;
        private DevExpress.XtraReports.UI.XRLabel xrLabel41;
        private DevExpress.XtraReports.UI.XRLabel xrLabel43;
        private DevExpress.XtraReports.UI.XRLabel xrL_LCLJBL;
        private DevExpress.XtraReports.UI.XRLabel xrLabel46;
        private DevExpress.XtraReports.UI.XRLabel xrLabel47;
        private DevExpress.XtraReports.UI.XRLabel xrL_LCLJWCQK;
        private DevExpress.XtraReports.UI.XRLabel xrLabel49;
        private DevExpress.XtraReports.UI.XRLabel xrL_SS_ZL_JC_ZD;
        private DevExpress.XtraReports.UI.XRLabel xrLabel44;
        private DevExpress.XtraReports.UI.XRLabel xrLabel50;
        private DevExpress.XtraReports.UI.XRLabel xrLabel51;
        private DevExpress.XtraReports.UI.XRLabel xrL_DBZZLKZBL;
        private DevExpress.XtraReports.UI.XRLabel xrLabel45;
        private DevExpress.XtraReports.UI.XRLabel xrLabel55;
        private DevExpress.XtraReports.UI.XRLabel xrLabel56;
        private DevExpress.XtraReports.UI.XRLabel xrL_SFWCSS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel48;
        private DevExpress.XtraReports.UI.XRLabel xrLabel53;
        private DevExpress.XtraReports.UI.XRLabel xrL_SFZSBR;
        private DevExpress.XtraReports.UI.XRLabel xrL_XGJBZDFZ;
        private DevExpress.XtraReports.UI.XRLabel xrLabel52;
        private DevExpress.XtraReports.UI.XRLabel xrLabel54;
        private DevExpress.XtraReports.UI.XRLabel xrL_SSXZ;
        private DevExpress.XtraReports.UI.XRLabel xrLabel58;
        private DevExpress.XtraReports.UI.XRLabel xrLabel59;
        private DevExpress.XtraReports.UI.XRLabel xrLabel60;
        private DevExpress.XtraReports.UI.XRLabel xrL_SSQBRZYC;
        private DevExpress.XtraReports.UI.XRLabel xrLabel61;
        private DevExpress.XtraReports.UI.XRLabel xrL_SSLB;
        private DevExpress.XtraReports.UI.XRLabel xrLabel57;
        private DevExpress.XtraReports.UI.XRLabel xrLabel62;
        private DevExpress.XtraReports.UI.XRLabel xrL_MZFJ;
        private DevExpress.XtraReports.UI.XRLabel xrLabel64;
        private DevExpress.XtraReports.UI.XRLabel xrLabel66;
        private DevExpress.XtraReports.UI.XRLabel xrL_MZBFZ;
        private DevExpress.XtraReports.UI.XRLabel xrLabel65;
        private DevExpress.XtraReports.UI.XRLabel xrLabel63;
        private DevExpress.XtraReports.UI.XRLabel xrLabel67;
        private DevExpress.XtraReports.UI.XRLabel xrL_SSSFDYFDSS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel68;
        private DevExpress.XtraReports.UI.XRLabel xrL_SSBFZMC;
        private DevExpress.XtraReports.UI.XRLabel xrLabel69;
        private DevExpress.XtraReports.UI.XRLabel xrL_SSSFYBFZ;
        private DevExpress.XtraReports.UI.XRLabel xrLabel70;
        private DevExpress.XtraReports.UI.XRLabel xrLabel71;
        private DevExpress.XtraReports.UI.XRLabel xrL_SSBWSFGR;
        private DevExpress.XtraReports.UI.XRLabel xrLabel73;
        private DevExpress.XtraReports.UI.XRLabel xrLabel74;
        private DevExpress.XtraReports.UI.XRLabel xrL_GRBW;
        private DevExpress.XtraReports.UI.XRLabel xrLabel76;
        private DevExpress.XtraReports.UI.XRLabel xrLabel72;
        private DevExpress.XtraReports.UI.XRLabel xrL_SZBDJC;
        private DevExpress.XtraReports.UI.XRLabel xrLabel77;
        private DevExpress.XtraReports.UI.XRLabel xrLabel75;
        private DevExpress.XtraReports.UI.XRLabel xrLabel78;
        private DevExpress.XtraReports.UI.XRLabel xrL_SSBDYZLBLZD;
        private DevExpress.XtraReports.UI.XRLabel xrLabel81;
        private DevExpress.XtraReports.UI.XRLabel xrLabel80;
        private DevExpress.XtraReports.UI.XRLabel xrL_YWZSSJH;
        private DevExpress.XtraReports.UI.XRLabel xrLabel79;
        private DevExpress.XtraReports.UI.XRLabel xrLabel82;
        private DevExpress.XtraReports.UI.XRLabel xrL_SFYYYXSH;
        private DevExpress.XtraReports.UI.XRLabel xrLabel87;
        private DevExpress.XtraReports.UI.XRLabel xrL_SSGCZSFYYWYL;
        private DevExpress.XtraReports.UI.XRLabel xrLabel86;
        private DevExpress.XtraReports.UI.XRLabel xrLabel84;
        private DevExpress.XtraReports.UI.XRLabel xrL_FJHZSS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel83;
        private DevExpress.XtraReports.UI.XRLabel xrLabel88;
        private DevExpress.XtraReports.UI.XRLabel xrL_YYXSHLX_SFSW;
        private DevExpress.XtraReports.UI.XRLabel xrLabel85;
        private DevExpress.XtraReports.UI.XRLabel xrLabel91;
        private DevExpress.XtraReports.UI.XRLabel xrL_YYXSH_SWYY;
        private DevExpress.XtraReports.UI.XRLabel xrLabel90;
        private DevExpress.XtraReports.UI.XRLabel xrLabel89;
        private DevExpress.XtraReports.UI.XRLabel xrLabel92;
        private DevExpress.XtraReports.UI.XRLabel xrL_CKHZCY_SFFSCS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel94;
        private DevExpress.XtraReports.UI.XRLabel xrLabel95;
        private DevExpress.XtraReports.UI.XRLabel xrL_CKHZCY_FMFS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel93;
        private DevExpress.XtraReports.UI.XRLabel xrLabel98;
        private DevExpress.XtraReports.UI.XRLabel xrLabel97;
        private DevExpress.XtraReports.UI.XRLabel xrL_CKHZCY_HCYE;
        private DevExpress.XtraReports.UI.XRLabel xrLabel96;
        private DevExpress.XtraReports.UI.XRLabel xrLabel101;
        private DevExpress.XtraReports.UI.XRLabel xrL_ZLHZCY_SFZL;
        private DevExpress.XtraReports.UI.XRLabel xrLabel103;
        private DevExpress.XtraReports.UI.XRLabel xrLabel100;
        private DevExpress.XtraReports.UI.XRLabel xrLabel99;
        private DevExpress.XtraReports.UI.XRLabel xrL_CKHZCY_XSESFFSCS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel104;
        private DevExpress.XtraReports.UI.XRLabel xrL_ZLHZCY_ZGZDYJ;
        private DevExpress.XtraReports.UI.XRLabel xrLabel102;
        private DevExpress.XtraReports.UI.XRLabel xrL_ZLHZCY_ZLFQ;
        private DevExpress.XtraReports.UI.XRLabel xrLabel108;
        private DevExpress.XtraReports.UI.XRLabel xrLabel106;
    }
}
