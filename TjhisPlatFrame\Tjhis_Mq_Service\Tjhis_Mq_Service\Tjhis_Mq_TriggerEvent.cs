﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Tjhis_Mq_Service
{
    public class Tjhis_Mq_TriggerEvent
    {
        public delegate void GetInfoEventHandler(object sender, EventArgs e);
        public event GetInfoEventHandler infoEvent;

        //存储信息变量
        public string Message = "";

        //编写引发事件的函数(在程序任意域使用)
        public void OnMessage()
        {
            if (infoEvent != null)
            {
                //发送信息
                infoEvent(this, new EventArgs());
            }
        }
    }
}
