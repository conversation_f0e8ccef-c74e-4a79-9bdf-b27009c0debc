﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Collections;
using System.Configuration;

namespace Utility.DbInfo
{
    /// <summary>
    /// 统一的数据库访问类
    /// 1、OracleClient: providerName="System.Data.OracleClient" connectionString="Data Source = myvm1; User Id = system; Password = ****;"
    /// 2、OleDb       : providerName="System.Data.OleDb"        connectionString="Provider=MSDAORA.1;Data Source = EN; User Id = system; Password = *******;"
    ///                  providerName="System.Data.OleDb"        connectionString="Provider=OraOLEDB.Oracle.1;Data Source = EN; User Id = system; Password = *******;"
    /// 3、ODP                  
    /// </summary>
    public class DataBaseAccess
    {
        private static DbInfo _dbInfo = new DbInfo(); 
        private static int CheckAccessStyle()
        {
            string providerName = ConfigurationManager.ConnectionStrings["connectionString"].ProviderName;
            switch (providerName)
            {
                case "System.Data.OracleClient":
                    return 1;
                case "System.Data.OleDb":
                    return 2;
                default:
                    throw new Exception("配置文件中数据访问方式有误，目前支持：System.Data.OracleClient、System.Data.OleDb。");
            }
        }

        /// <summary>
        /// 单值查询
        /// </summary>
        /// <returns></returns>
        public static string GetValueBySql(string sql)
        {
            switch (CheckAccessStyle())
            {
                case 1:
                    return _dbInfo.GetValueBySql(sql);
            }
            return "";
        }

        /// <summary>
        /// 通过SQL语句获取数据
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public static DataSet GetData(string sql, string tableName)
        {
            switch (CheckAccessStyle())
            {
                case 1:
                    return _dbInfo.GetData(sql, tableName);
            }
            return null;
        }

        /// <summary>
        /// 获取表中数据
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        public static DataSet GetTableData(string tableName, string filter)
        {
            switch (CheckAccessStyle())
            {
                case 1:
                    return _dbInfo.GetTableData(tableName, filter);
            }
            return null;
        }

        /// <summary>
        /// 保存表中数据
        /// </summary>
        /// <param name="dsChanged"></param>
        /// <returns></returns>
        public static bool SaveTableData(DataSet dsChanged)
        {
            switch (CheckAccessStyle())
            {
                case 1:
                    return _dbInfo.SaveTableData(dsChanged);
            }
            return true;
        }

        /// <summary>
        /// 多表保存
        /// </summary>
        /// <param name="dsChanged1"></param>
        /// <param name="dsChanged2"></param>
        /// <param name="dsChanged3"></param>
        /// <returns></returns>
        public static bool SaveTablesData(DataSet dsChanged1, DataSet dsChanged2, DataSet dsChanged3)
        {
            switch (CheckAccessStyle())
            {
                case 1:
                    return _dbInfo.SaveTablesData(dsChanged1, dsChanged2, dsChanged3);
            }
            return true;
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="arrSql"></param>
        /// <param name="dataSets"></param>
        /// <returns></returns>
        public static bool SaveData(ArrayList arrSql, object[] arrDataSet)
        {
            switch (CheckAccessStyle())
            {
                case 1:
                    return _dbInfo.SaveData(arrSql, arrDataSet);
            }
            return true;
        }

        /// <summary>
        /// 执行SQL语句
        /// </summary>
        /// <returns></returns>
        public static bool ExecuteSql(string sql)
        {
            switch (CheckAccessStyle())
            {
                case 1:
                    return _dbInfo.ExecuteSql(sql);
            }
            return true;
        }

    }
}
