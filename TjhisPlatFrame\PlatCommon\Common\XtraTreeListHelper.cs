﻿using DevExpress.Utils;
using DevExpress.XtraTreeList;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PlatCommon.Common
{
    public static class XtraTreeListHelper
    {
        public static void Initialize(params TreeList[] treeLists)
        {
            if (treeLists?.Length > 0)
            {
                foreach (TreeList tl in treeLists)
                {
                    tl.Appearance.EvenRow.BackColor = Color.FromArgb(248, 249, 251);
                    //tl.Appearance.OddRow.BackColor = Color.FromArgb(248, 249, 251);
                    //tl.Appearance.FocusedRow.BackColor = Color.FromArgb(191, 212, 255);
                    //tl.Appearance.HideSelectionRow.BackColor = Color.FromArgb(248, 249, 251);
                    //tl.Appearance.FocusedCell.BackColor = Color.FromArgb(191, 212, 255);
                    tl.Appearance.HeaderPanel.Options.UseTextOptions = true;
                    tl.Appearance.HeaderPanel.TextOptions.HAlignment = HorzAlignment.Center;
                    tl.Appearance.HeaderPanel.BackColor = Color.FromArgb(248, 249, 251);
                    tl.IndicatorWidth = 20;
                    tl.OptionsView.ShowIndicator = false;
                    tl.OptionsView.ShowRoot = true;
                    tl.OptionsView.ShowButtons = true;
                    tl.RowHeight = 25;
                    tl.OptionsCustomization.AllowFilter = false;
                    tl.OptionsCustomization.AllowSort = false;
                    tl.OptionsMenu.EnableColumnMenu = false;
                    tl.OptionsNavigation.EnterMovesNextColumn = true;
                    tl.OptionsSelection.MultiSelect = false;
                    tl.OptionsView.EnableAppearanceEvenRow = true;
                    tl.OptionsView.EnableAppearanceOddRow = false;
                    tl.OptionsView.ShowVertLines = false;
                    tl.OptionsView.ShowHorzLines = false;
                    tl.OptionsView.ShowTreeLines = DefaultBoolean.False;
                    tl.OptionsView.FocusRectStyle = DrawFocusRectStyle.RowFocus;
                    tl.OptionsSelection.EnableAppearanceFocusedCell = false;
                }
            }
        }

    }
}
