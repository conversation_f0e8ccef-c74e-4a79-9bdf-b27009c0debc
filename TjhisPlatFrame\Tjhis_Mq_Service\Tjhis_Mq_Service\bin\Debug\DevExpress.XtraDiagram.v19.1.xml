<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.XtraDiagram.v19.1</name>
  </assembly>
  <members>
    <member name="N:DevExpress.XtraDiagram">
      <summary>
        <para>Contains classes that enable you to create diagrams.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraDiagram.CustomDrawBackgroundEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.CustomDrawBackground"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.CustomDrawBackgroundEventArgs.#ctor(System.Drawing.Graphics,DevExpress.XtraDiagram.DiagramDrawingContext,System.Drawing.RectangleF,System.Drawing.RectangleF,System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramPageInfo},System.Drawing.RectangleF,System.Nullable{System.Drawing.RectangleF},System.Nullable{System.Drawing.RectangleF},System.Nullable{System.Int32})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.CustomDrawBackgroundEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="graphics">A System.Drawing.Graphics object that provides painting facilities.</param>
      <param name="context">A <see cref="T:DevExpress.XtraDiagram.DiagramDrawingContext"/> enumeration value.</param>
      <param name="contentBounds">The bounds of the background excluding margins.</param>
      <param name="totalBounds">The total bounds of the background.</param>
      <param name="pages">A list of diagram pages represented by DevExpress.XtraDiagram.DiagramPageInfo objects.</param>
      <param name="viewportBounds">The viewport bounds.</param>
      <param name="printBounds">The total bounds of the background when printing the diagram.</param>
      <param name="printClientBounds">The bounds of the background excluding margins when printing the diagram.</param>
      <param name="printIndex">A zero-based index of the page that is currently being rendered when printing the diagram.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomDrawBackgroundEventArgs.ContentBounds">
      <summary>
        <para>Gets the rectangle which encompasses the background area excluding margins.</para>
      </summary>
      <value>The bounds of the background excluding margins.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomDrawBackgroundEventArgs.Context">
      <summary>
        <para>Gets whether the item is to be drawn on the canvas, toolbox, in the print or export output or as the drag preview.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramDrawingContext"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomDrawBackgroundEventArgs.Graphics">
      <summary>
        <para>Returns an object that provides painting facilities.</para>
      </summary>
      <value>A System.Drawing.Graphics object that provides painting facilities.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomDrawBackgroundEventArgs.PagesInfo">
      <summary>
        <para>Gets the list of diagram pages.</para>
      </summary>
      <value>A list of DevExpress.XtraDiagram.DiagramPageInfo objects.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomDrawBackgroundEventArgs.PrintBounds">
      <summary>
        <para>Gets the rectangle which encompasses the total background area when printing the diagram.</para>
      </summary>
      <value>The total bounds of the background.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomDrawBackgroundEventArgs.PrintClientBounds">
      <summary>
        <para>Gets the rectangle which encompasses the background area excluding margins when printing the diagram.</para>
      </summary>
      <value>The bounds of the background excluding margins.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomDrawBackgroundEventArgs.PrintIndex">
      <summary>
        <para>Gets the zero-based index of the page that is currently being rendered when printing the diagram.</para>
      </summary>
      <value>A zero-based index of the page that is currently being rendered.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomDrawBackgroundEventArgs.TotalBounds">
      <summary>
        <para>Gets the rectangle which encompasses the total background area.</para>
      </summary>
      <value>The total bounds of the background.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomDrawBackgroundEventArgs.ViewportBounds">
      <summary>
        <para>Gets the rectangle which encompasses the viewport area.</para>
      </summary>
      <value>The viewport bounds in the control-relative coordinates.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.CustomDrawItemEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.CustomDrawItem"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.CustomDrawItemEventArgs.#ctor(System.Drawing.Graphics,DevExpress.XtraDiagram.DiagramItem,System.Action{DevExpress.XtraDiagram.DiagramAppearanceObject,DevExpress.XtraDiagram.CustomDrawItemMode},DevExpress.XtraDiagram.DiagramAppearanceObject,System.Drawing.SizeF,DevExpress.XtraDiagram.DiagramDrawingContext)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.CustomDrawItemEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="graphics">An object that provides painting facilities.</param>
      <param name="item">A diagram item to process.</param>
      <param name="defaultDraw">A <see cref="T:DevExpress.XtraDiagram.CustomDrawItemMode"/> value that determines which elements of the default painting should be painted.</param>
      <param name="appearance">An object that provides the item&#39;s appearance settings.</param>
      <param name="size">A System.Drawing.SizeF object that is the item size.</param>
      <param name="context">A <see cref="T:DevExpress.XtraDiagram.DiagramDrawingContext"/> value that indicates whether the item is to be drawn on the canvas, toolbox, in the print or export output or as the drag preview.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomDrawItemEventArgs.Appearance">
      <summary>
        <para>Provides access to the item&#39;s appearance settings.</para>
      </summary>
      <value>The object that provides the item&#39;s appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomDrawItemEventArgs.Context">
      <summary>
        <para>Returns the value that indicates whether the item is to be drawn on the canvas, toolbox, in the print or export output or as the drag preview.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramDrawingContext"/> value.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.CustomDrawItemEventArgs.DefaultDraw(DevExpress.XtraDiagram.CustomDrawItemMode)">
      <summary>
        <para>Allows you to define which elements of the default painting should be painted.</para>
      </summary>
      <param name="drawMode">A <see cref="T:DevExpress.XtraDiagram.CustomDrawItemMode"/> value that determines which elements of the default painting should be painted.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomDrawItemEventArgs.Graphics">
      <summary>
        <para>Returns an object that provides painting facilities.</para>
      </summary>
      <value>A System.Drawing.Graphics object that provides painting facilities.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomDrawItemEventArgs.Item">
      <summary>
        <para>Gets the processed diagram item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomDrawItemEventArgs.Size">
      <summary>
        <para>Gets the item size.</para>
      </summary>
      <value>The System.Drawing.SizeF object.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.CustomDrawItemMode">
      <summary>
        <para>Lists values that specify which elements of the default painting should be enabled when handling the <see cref="E:DevExpress.XtraDiagram.DiagramControl.CustomDrawItem"/> event.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraDiagram.CustomDrawItemMode.All">
      <summary>
        <para>Enable the default painting of all elements of the item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraDiagram.CustomDrawItemMode.Background">
      <summary>
        <para>Enable only the default painting of the item&#39;s background.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraDiagram.CustomDrawItemMode.Content">
      <summary>
        <para>Enable only the default painting of the item&#39;s content.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraDiagram.CustomDrawItemMode.None">
      <summary>
        <para>Disable the default painting of all elements of the item.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraDiagram.CustomHitTestEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.CustomHitTest"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.CustomHitTestEventArgs.#ctor(DevExpress.XtraDiagram.DiagramItem,System.Drawing.PointF,System.Drawing.RectangleF,System.Single)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.CustomHitTestEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> to which the test point belongs.</param>
      <param name="point">A <see cref="T:System.Drawing.PointF"/> value that is the current test point position within the item.</param>
      <param name="bounds">A A <see cref="T:System.Drawing.RectangleF"/> object that represents the bounds of the item to which the test point belongs.</param>
      <param name="angle">A <see cref="T:System.Single"/> value that is the angle in degrees by which the item is rotated around its anchor point.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomHitTestEventArgs.Angle">
      <summary>
        <para>Gets the angle by which the diagram item is rotated around its anchor point relative to the diagram canvas.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value specifying an angle in degrees relative to the diagram canvas.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomHitTestEventArgs.Bounds">
      <summary>
        <para>Gets the bounds of the item to which the test points belongs.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.RectangleF"/> object that represents the item bounds.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomHitTestEventArgs.Item">
      <summary>
        <para>Gets the item to which the test point belongs.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomHitTestEventArgs.ItemHeight">
      <summary>
        <para>Gets the height of the item to which the test point belongs.</para>
      </summary>
      <value>A System.Single value that is the item&#39;s height.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomHitTestEventArgs.ItemWidth">
      <summary>
        <para>Gets the width of the item to which the test point belongs.</para>
      </summary>
      <value>A System.Single value that is the item&#39;s width.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomHitTestEventArgs.Point">
      <summary>
        <para>Gets the object that represents the position of the test point relative to the item it belongs to.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.PointF"/> value that is the current test point position relative to the item it belongs to.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.CustomHitTestEventArgs.Result">
      <summary>
        <para>Gets or sets whether the test point belongs to a diagram item.</para>
      </summary>
      <value>true, if the test point belongs to a diagram item; otherwise, false. null, if the default hit test logic should be applied.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramAppearanceObject">
      <summary>
        <para>Provides appearance settings applied to a diagram item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramAppearanceObject.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramAppearanceObject"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramAppearanceObject.#ctor(DevExpress.Utils.AppearanceDefault)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramAppearanceObject"/> class with specified settings.</para>
      </summary>
      <param name="appearanceDefault">A DevExpress.Utils.AppearanceDefault object containing the default appearance settings.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramAppearanceObject.#ctor(DevExpress.Utils.IAppearanceOwner,DevExpress.Utils.AppearanceObject,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramAppearanceObject"/> class with specified settings.</para>
      </summary>
      <param name="owner">An object that implements the DevExpress.Utils.IAppearanceOwner interface and will own the created collection.</param>
      <param name="parentAppearance">A DevExpress.Utils.AppearanceObject object which represents the parent appearance object. This value is assigned to the ParentAppearance property.</param>
      <param name="name">A System.String value specifying the name of the created object. This value is assigned to the Name property.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramAppearanceObject.Assign(DevExpress.Utils.AppearanceObject)">
      <summary>
        <para>Copies all available property settings from the target <see cref="T:DevExpress.XtraDiagram.DiagramAppearanceObject"/> to this <see cref="T:DevExpress.XtraDiagram.DiagramAppearanceObject"/>.</para>
      </summary>
      <param name="val">An object whose settings are to be copied.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramAppearanceObject.BorderDashPattern">
      <summary>
        <para>Gets or sets a collection of System.Double values that indicate the pattern of dashes and gaps that is used to outline diagram items.</para>
      </summary>
      <value>A collection of System.Double values that specify the pattern of dashes and gaps.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramAppearanceObject.BorderSize">
      <summary>
        <para>Gets or sets the value that specifies the border thickness.</para>
      </summary>
      <value>An integer value that specifies the border thickness.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramAppearanceObject.Clone">
      <summary>
        <para>Creates a copy of the current DiagramAppearanceObject object.</para>
      </summary>
      <returns>A DevExpress.XtraDiagram.DiagramAppearanceObject object which is a copy of the current object.</returns>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramAppearanceObject.ContentBackground">
      <summary>
        <para>Gets or sets the item&#39;s background color.</para>
      </summary>
      <value>A System.Drawing.Color value that specifies the background color.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramAppearanceObject.GradientMode">
      <summary>
        <para>This property supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramAppearanceObject.Image">
      <summary>
        <para>This property supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramAppearanceObject.IsEqual(DevExpress.Utils.AppearanceObject)">
      <summary>
        <para>Tests whether two objects have the same property values.</para>
      </summary>
      <param name="val">The <see cref="T:DevExpress.XtraDiagram.DiagramAppearanceObject"/> object to which the current object is compared.</param>
      <returns>true, if the current object has the same property values as the specified object; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramAppearanceObject.Options">
      <summary>
        <para>Provides options that determine which appearance settings should be applied to a diagram item.</para>
      </summary>
      <value>A DevExpress.XtraDiagram.DiagramAppearanceOptions object.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramAppearanceOptions">
      <summary>
        <para>Provides Boolean options that determine which appearance settings should be applied to a diagram item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramAppearanceOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramAppearanceOptions"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramAppearanceOptions.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies all available property settings from the target <see cref="T:DevExpress.XtraDiagram.DiagramAppearanceOptions"/> to this <see cref="T:DevExpress.XtraDiagram.DiagramAppearanceOptions"/>.</para>
      </summary>
      <param name="options">An object whose settings are to be copied.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramAppearanceOptions.IsEqual(DevExpress.Utils.AppearanceOptions)">
      <summary>
        <para>Tests whether two objects have the same property values.</para>
      </summary>
      <param name="options">The <see cref="T:DevExpress.XtraDiagram.DiagramAppearanceOptions"/> object to which the current object is compared.</param>
      <returns>true if the current object has the same property values as the specified object; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramAppearanceOptions.UseBorderDashPattern">
      <summary>
        <para>Gets or sets whether to apply the dash pattern to the item&#39;s borders.</para>
      </summary>
      <value>true to apply the dash pattern to the item&#39;s borders; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramAppearanceOptions.UseBorderSize">
      <summary>
        <para>Gets or sets whether to apply the size setting to the item&#39;s borders.</para>
      </summary>
      <value>true to apply the size setting to the item&#39;s borders; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramAppearanceOptions.UseContentBackground">
      <summary>
        <para>Gets or sets whether to paint the item&#39;s background with the specified brush.</para>
      </summary>
      <value>true to paint the item&#39;s background with the specified brush; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramAppearanceOptions.UseFont">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramAppearanceOptions.UseFontBold">
      <summary>
        <para>Gets or sets whether to apply the bold font style.</para>
      </summary>
      <value>true to apply the bold font style; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramAppearanceOptions.UseFontFamily">
      <summary>
        <para>Gets or sets whether to apply the font family to the item&#39;s text.</para>
      </summary>
      <value>true to apply the font family; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramAppearanceOptions.UseFontItalic">
      <summary>
        <para>Gets or sets whether to apply the italic font style to the item&#39;s text.</para>
      </summary>
      <value>true to apply the italic font style; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramAppearanceOptions.UseFontSize">
      <summary>
        <para>Gets or sets whether to apply the font size to the item&#39;s text.</para>
      </summary>
      <value>true to apply the font size; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramAppearanceOptions.UseFontStrikeout">
      <summary>
        <para>Gets or sets whether to apply the strikeout font style to the item&#39;s text.</para>
      </summary>
      <value>true to apply the strikeout font style; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramAppearanceOptions.UseFontUnderline">
      <summary>
        <para>Gets or sets whether to apply the underline font style to the item&#39;s text.</para>
      </summary>
      <value>true to apply the underline font style; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramBeforeItemsMovingEventArgs">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramBeforeItemsMovingEventArgs.#ctor(System.Collections.Generic.IList{DevExpress.XtraDiagram.DiagramItem},DevExpress.Diagram.Core.ItemsActionSource,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="items"></param>
      <param name="actionSource"></param>
      <param name="isCopying"></param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramBeforeItemsMovingEventArgs.ActionSource">
      <summary>
        <para>Indicates whether the moving operation is performed using drag-and-drop, the Properties Panel or by pressing key shortcuts.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ItemsActionSource"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramBeforeItemsMovingEventArgs.IsCopying">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramBeforeItemsMovingEventArgs.Items">
      <summary>
        <para>Returns the collection of diagram items that are being moved.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> descendants.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramBeforeItemsResizingEventArgs">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramBeforeItemsResizingEventArgs.#ctor(System.Collections.Generic.IList{DevExpress.XtraDiagram.DiagramItem},DevExpress.Diagram.Core.ItemsActionSource,DevExpress.Diagram.Core.ResizeMode)">
      <summary>
        <para></para>
      </summary>
      <param name="items"></param>
      <param name="actionSource"></param>
      <param name="mode"></param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramBeforeItemsResizingEventArgs.ActionSource">
      <summary>
        <para>Indicates whether the resizing operation is performed using the selection handles or the Properties Panel.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ItemsActionSource"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramBeforeItemsResizingEventArgs.Items">
      <summary>
        <para>Returns the collection of diagram items that are being resized.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> descendants.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramBeforeItemsResizingEventArgs.Mode">
      <summary>
        <para>Indicates the direction of resizing.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.ResizeMode enumeration value.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramBeforeItemsRotatingEventArgs">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramBeforeItemsRotatingEventArgs.#ctor(System.Collections.Generic.IList{DevExpress.XtraDiagram.DiagramItem},DevExpress.Diagram.Core.ItemsActionSource,System.Nullable{DevExpress.Utils.PointFloat})">
      <summary>
        <para></para>
      </summary>
      <param name="items"></param>
      <param name="actionSource"></param>
      <param name="centerOfRotation"></param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramBeforeItemsRotatingEventArgs.ActionSource">
      <summary>
        <para>Indicates whether the rotating operation is performed using the rotation handle or the Properties Panel.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ItemsActionSource"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramBeforeItemsRotatingEventArgs.CenterOfRotation">
      <summary>
        <para>Returns the coordinates of the center of rotation.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.PointFloat"/> value that is the coordinates of the center of rotation.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramBeforeItemsRotatingEventArgs.Items">
      <summary>
        <para>Returns the collection of diagram items that are being rotated.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> descendants.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramClosedEditorEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ClosedEditor"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramClosedEditorEventArgs.#ctor(DevExpress.XtraDiagram.DiagramItem,System.Boolean,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramClosedEditorEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="item">The item for which the text editor has been closed.</param>
      <param name="valueChanged">true, if the shape text has been changed; otherwise, false.</param>
      <param name="oldValue">The previously assigned shape text.</param>
      <param name="newValue">The new shape text.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramClosedEditorEventArgs.Item">
      <summary>
        <para>Gets the item for which the text editor has been closed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramClosedEditorEventArgs.NewValue">
      <summary>
        <para>Gets or sets the new shape text.</para>
      </summary>
      <value>A string value that is the new shape text.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramClosedEditorEventArgs.OldValue">
      <summary>
        <para>Gets the previously assigned shape text.</para>
      </summary>
      <value>A string value that is the previously assigned shape text.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramClosedEditorEventArgs.ValueChanged">
      <summary>
        <para>Gets whether the item&#39;s text has been changed.</para>
      </summary>
      <value>true, if the item&#39;s text has been changed; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramConnectionChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ConnectionChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramConnectionChangedEventArgs.#ctor(DevExpress.XtraDiagram.DiagramConnector,DevExpress.XtraDiagram.DiagramItem,DevExpress.XtraDiagram.DiagramItem,System.Int32,System.Int32,DevExpress.Utils.PointFloat,DevExpress.Utils.PointFloat,DevExpress.Diagram.Core.ConnectorPointType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramConnectionChangedEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="connector">The connector whose beginning/ending item has been changed.</param>
      <param name="oldItem">The diagram item the connector has been detached from.</param>
      <param name="newItem">The diagram item the connector has been attached to.</param>
      <param name="oldIndex">The index of the connection point on the diagram item the connector has been detached from.</param>
      <param name="newIndex">The index of the connection point on the diagram item the connector has been attached to.</param>
      <param name="oldPoint">The initial coordinates of the connection point which has been moved.</param>
      <param name="newPoint">The current coordinates of the connection point which has been moved.</param>
      <param name="connectorPointType">The type of the connection point which has been moved.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnectionChangedEventArgs.Connector">
      <summary>
        <para>Returns the connector whose beginning/ending item has been changed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnectionChangedEventArgs.ConnectorPointType">
      <summary>
        <para>Returns the type of the connection point which has been moved.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ConnectorPointType"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnectionChangedEventArgs.NewIndex">
      <summary>
        <para>Returns the index of the connection point on the diagram item the connector has been attached to.</para>
      </summary>
      <value>A zero-based index of the connection point on the diagram item the connector has been attached to. -1 if the connector was not attached to an item.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnectionChangedEventArgs.NewItem">
      <summary>
        <para>Returns the diagram item the connector has been attached to.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> descendant that represents the diagram item the connector has been attached to.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnectionChangedEventArgs.NewPoint">
      <summary>
        <para>Returns the current coordinates of the connection point which has been moved.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.PointFloat"/> value that is the current coordinates of the connection point which has been moved.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnectionChangedEventArgs.OldIndex">
      <summary>
        <para>Returns the index of the connection point on the diagram item the connector has been detached from.</para>
      </summary>
      <value>A zero-based index of the connection point on the diagram item the connector has been detached from. -1 if the connector was not attached to an item.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnectionChangedEventArgs.OldItem">
      <summary>
        <para>Returns the diagram item the connector has been detached from.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> descendant that represents the diagram item the connector has been detached from.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnectionChangedEventArgs.OldPoint">
      <summary>
        <para>Returns the initial coordinates of the connection point which has been moved.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.PointFloat"/> value that is the initial coordinates of the connection point which has been moved.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramConnectionChangingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ConnectionChanging"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramConnectionChangingEventArgs.#ctor(DevExpress.XtraDiagram.DiagramConnector,DevExpress.XtraDiagram.DiagramItem,DevExpress.XtraDiagram.DiagramItem,System.Int32,System.Int32,DevExpress.Utils.PointFloat,DevExpress.Utils.PointFloat,DevExpress.Diagram.Core.ConnectorPointType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramConnectionChangingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="connector">The connector whose beginning/ending item is about to be changed.</param>
      <param name="oldItem">The diagram item the connector is about to be detached from.</param>
      <param name="newItem">The diagram item the connector is about to be attached to.</param>
      <param name="oldIndex">The index of the connection point on the diagram item the connector is about to be detached from.</param>
      <param name="newIndex">The index of the connection point on the diagram item the connector is about to be attached to.</param>
      <param name="oldPoint">The initial coordinates of the connection point which is about to be moved.</param>
      <param name="newPoint">The new coordinates of the connection point which is about to be moved.</param>
      <param name="connectorPointType">The type of the connection point which is about to be moved.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnectionChangingEventArgs.Connector">
      <summary>
        <para>Returns the connector whose beginning/ending item is about to be changed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnectionChangingEventArgs.ConnectorPointType">
      <summary>
        <para>Returns the type of the connection point which is about to be moved.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ConnectorPointType"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnectionChangingEventArgs.NewIndex">
      <summary>
        <para>Returns the index of the connection point on the diagram item to which the connector is about to be attached.</para>
      </summary>
      <value>A zero-based index of the connection point on the diagram item to which the connector is about to be attached. -1 if the connector is not getting attached to an item.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnectionChangingEventArgs.NewItem">
      <summary>
        <para>Returns the diagram item the connector is about to be attached to.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> descendant that represents the diagram item the connector is about to be attached to.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnectionChangingEventArgs.NewPoint">
      <summary>
        <para>Returns the new coordinates of the connection point which is about to be moved.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.PointFloat"/> value that is the new coordinates of the connection point which is about to be moved.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnectionChangingEventArgs.OldIndex">
      <summary>
        <para>Returns the index of the connection point on the diagram item from which the connector is about to be detached.</para>
      </summary>
      <value>A zero-based index of the connection point on the diagram item from which the connector is about to be detached. -1 if the connector is not attached to an item.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnectionChangingEventArgs.OldItem">
      <summary>
        <para>Returns the diagram item from which the connector is about to be detached.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> descendant that represents the diagram item from which the connector is about to be detached.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnectionChangingEventArgs.OldPoint">
      <summary>
        <para>Returns the initial coordinates of the connection point which is about to be moved.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.PointFloat"/> value that is the initial coordinates of the connection point which is about to be moved.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramConnector">
      <summary>
        <para>Connects two diagram items or two points.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramConnector.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramConnector.#ctor(DevExpress.Diagram.Core.ConnectorType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class with the specified settings.</para>
      </summary>
      <param name="connectorType">The connector type. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.Type"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramConnector.#ctor(DevExpress.Diagram.Core.ConnectorType,DevExpress.Utils.PointFloat,DevExpress.Utils.PointFloat)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class with the specified settings.</para>
      </summary>
      <param name="connectorType">The connector type. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.Type"/> property.</param>
      <param name="beginPoint">The connector&#39;s starting point. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.BeginPoint"/> property.</param>
      <param name="endPoint">The connector&#39;s ending point. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.EndPoint"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramConnector.#ctor(DevExpress.Diagram.Core.ConnectorType,DevExpress.Utils.PointFloat,DevExpress.Utils.PointFloat,DevExpress.Utils.PointFloat[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class with the specified settings.</para>
      </summary>
      <param name="connectorType">The connector type. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.Type"/> property.</param>
      <param name="beginPoint">The connector&#39;s starting point. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.BeginPoint"/> property.</param>
      <param name="endPoint">The connector&#39;s ending point. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.EndPoint"/> property.</param>
      <param name="points">A collection of turn points. This object is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.Points"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramConnector.#ctor(DevExpress.Diagram.Core.ConnectorType,DevExpress.XtraDiagram.DiagramItem,DevExpress.XtraDiagram.DiagramItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class with the specified settings.</para>
      </summary>
      <param name="connectorType">The connector type. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.Type"/> property.</param>
      <param name="beginItem">The connector&#39;s starting item. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.BeginItem"/> property.</param>
      <param name="endItem">The connector&#39;s ending item. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.EndItem"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramConnector.#ctor(DevExpress.Diagram.Core.ConnectorType,System.Drawing.PointF,System.Drawing.PointF)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class with the specified settings.</para>
      </summary>
      <param name="connectorType">The connector type. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.Type"/> property.</param>
      <param name="beginPoint">The connector&#39;s starting point. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.BeginPoint"/> property.</param>
      <param name="endPoint">The connector&#39;s ending point. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.EndPoint"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramConnector.#ctor(DevExpress.Diagram.Core.ConnectorType,System.Drawing.PointF,System.Drawing.PointF,System.Drawing.PointF[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class with the specified settings.</para>
      </summary>
      <param name="connectorType">The connector type. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.Type"/> property.</param>
      <param name="beginPoint">The connector&#39;s starting point. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.BeginPoint"/> property.</param>
      <param name="endPoint">The connector&#39;s ending point. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.EndPoint"/> property.</param>
      <param name="points">A collection of turn points. This object is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.Points"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramConnector.#ctor(DevExpress.Utils.PointFloat,DevExpress.Utils.PointFloat)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class with the specified settings.</para>
      </summary>
      <param name="beginPoint">The connector&#39;s starting point. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.BeginPoint"/> property.</param>
      <param name="endPoint">The connector&#39;s ending point. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.EndPoint"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramConnector.#ctor(DevExpress.Utils.PointFloat,DevExpress.Utils.PointFloat,DevExpress.Utils.PointFloat[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class with the specified settings.</para>
      </summary>
      <param name="beginPoint">The connector&#39;s starting point. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.BeginPoint"/> property.</param>
      <param name="endPoint">The connector&#39;s ending point. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.EndPoint"/> property.</param>
      <param name="points">A collection of turn points. This object is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.Points"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramConnector.#ctor(DevExpress.XtraDiagram.DiagramItem,DevExpress.XtraDiagram.DiagramItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class with the specified settings.</para>
      </summary>
      <param name="beginItem">The connector&#39;s starting item. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.BeginItem"/> property.</param>
      <param name="endItem">The connector&#39;s ending item. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.EndItem"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramConnector.#ctor(DevExpress.XtraDiagram.DiagramItem,DevExpress.XtraDiagram.DiagramItem,System.Drawing.PointF[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class with the specified settings.</para>
      </summary>
      <param name="beginItem">The connector&#39;s starting item. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.BeginItem"/> property.</param>
      <param name="endItem">The connector&#39;s ending item. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.EndItem"/> property.</param>
      <param name="points">A collection of turn points. This object is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.Points"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramConnector.#ctor(System.Drawing.PointF,System.Drawing.PointF)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class with the specified settings.</para>
      </summary>
      <param name="beginPoint">The connector&#39;s starting point. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.BeginPoint"/> property.</param>
      <param name="endPoint">The connector&#39;s ending point. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.EndPoint"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramConnector.#ctor(System.Drawing.PointF,System.Drawing.PointF,System.Drawing.PointF[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class with the specified settings.</para>
      </summary>
      <param name="beginPoint">The connector&#39;s starting point. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.BeginPoint"/> property.</param>
      <param name="endPoint">The connector&#39;s ending point. This value is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.EndPoint"/> property.</param>
      <param name="points">A collection of turn points. This object is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.Points"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.ActualConnectionPoints">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.Anchors">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.Angle">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.AreSubordinatesVisible">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.BeginArrow">
      <summary>
        <para>Gets or sets the connector&#39;s start arrow style.</para>
      </summary>
      <value>An object that specifies the start arrow style.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.BeginArrowSize">
      <summary>
        <para>Gets or sets the connector&#39;s starting arrow size.</para>
      </summary>
      <value>The starting arrow size.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.BeginItem">
      <summary>
        <para>Gets or sets the start item for the connector.</para>
      </summary>
      <value>The connector&#39;s start item.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.BeginItemPointIndex">
      <summary>
        <para>Gets or sets the connection point on the start diagram item to which the connector is glued.</para>
      </summary>
      <value>A zero-based index of the connection point on the start diagram item.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.BeginPoint">
      <summary>
        <para>Gets or sets the connector&#39;s starting point within the diagram canvas.</para>
      </summary>
      <value>The starting point of the connector.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.BeginPointRestrictions">
      <summary>
        <para>Specifies the restrictions applied to the end-user actions with the connector&#39;s begin point.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ConnectorPointRestrictions"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.CanAttachConnectorBeginPoint">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.CanAttachConnectorEndPoint">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.CanChangeParent">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.CanChangeRoute">
      <summary>
        <para>Specifies whether to allow end-users to modify the connector&#39;s route.</para>
      </summary>
      <value>true to allow end-users to modify the connector&#39;s route; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.CanDragBeginPoint">
      <summary>
        <para>Specifies whether to allow end-users to drag the connector&#39;s begin point.</para>
      </summary>
      <value>true to allow end-users to drag the connector&#39;s begin point; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.CanDragEndPoint">
      <summary>
        <para>Specifies whether to allow end-users to drag the connector&#39;s end point.</para>
      </summary>
      <value>true to allow end-users to drag the connector&#39;s end point; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.CanHideSubordinates">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.CanResize">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.CanRotate">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.CanSnapToOtherItems">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.CanSnapToThisItem">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.CollapseButtonHorizontalAlignment">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.CollapseButtonOffset">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.CollapseButtonPosition">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.CollapseButtonVerticalAlignment">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.CollapseButtonVisibilityMode">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.ConnectionPoints">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.Content">
      <summary>
        <para>Gets or sets the text displayed within the connector.</para>
      </summary>
      <value>The text displayed within the connector.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramConnector.ContentChanged">
      <summary>
        <para>Fires each time the value of the <see cref="P:DevExpress.XtraDiagram.DiagramConnector.Content"/> property is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.EndArrow">
      <summary>
        <para>Gets or sets the connector&#39;s end arrow style.</para>
      </summary>
      <value>An object that specifies the end arrow style.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.EndArrowSize">
      <summary>
        <para>Gets or sets the connector&#39;s ending arrow size.</para>
      </summary>
      <value>The ending arrow size.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.EndItem">
      <summary>
        <para>Gets or sets the end item for the connector.</para>
      </summary>
      <value>The connector&#39;s end item.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.EndItemPointIndex">
      <summary>
        <para>Gets or sets the connection point on the end diagram item to which the connector is glued.</para>
      </summary>
      <value>A zero-based index of the connection point on the end diagram item.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.EndPoint">
      <summary>
        <para>Gets or sets the connector&#39;s end point on the diagram canvas.</para>
      </summary>
      <value>The end point of the connector.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.EndPointRestrictions">
      <summary>
        <para>Specifies the restrictions applied to the end-user actions with the connector&#39;s end point.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ConnectorPointRestrictions"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.IncomingConnectors">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.MinSize">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.MoveWithSubordinates">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.OutgoingConnectors">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.Padding">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.Points">
      <summary>
        <para>Gets or sets a collection of turn points for the connector.</para>
      </summary>
      <value>A collection of turn points.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.Position">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.Size">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.Text">
      <summary>
        <para>Gets or sets the text displayed within the connector.</para>
      </summary>
      <value>The text displayed within the connector.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramConnector.Type">
      <summary>
        <para>Gets or sets the connector&#39;s type.</para>
      </summary>
      <value>The connector&#39;s type.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramConnector.UpdateRoute">
      <summary>
        <para>Recalculates the diagram connector&#39;s route.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramContainer">
      <summary>
        <para>Represents a diagram container item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramContainer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramContainer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramContainer.#ctor(System.Drawing.RectangleF)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramContainer"/> class with specified settings.</para>
      </summary>
      <param name="bounds">A <see cref="T:System.Drawing.RectangleF"/> object that represents the <see cref="T:DevExpress.XtraDiagram.DiagramContainer"/> bounds.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramContainer.#ctor(System.Single,System.Single,System.Single,System.Single)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramContainer"/> class with specified settings.</para>
      </summary>
      <param name="x">The horizontal coordinate of the shape&#39;s upper left corner.</param>
      <param name="y">The vertical coordinate of the shape&#39;s upper left corner.</param>
      <param name="width">The width of the container.</param>
      <param name="height">The height of the container.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.ActualIsContainerCollapseButtonVisible">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.CanAddItems">
      <summary>
        <para>Specifies whether end-users can add items to the container.</para>
      </summary>
      <value>true to allow adding items to the container; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.CanCollapse">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.ClipItemsToBounds">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.CollapseContainerButtonPadding">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.CollapseContainerButtonSize">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.DragMode">
      <summary>
        <para>Specifies whether the container can be dragged by any point or only by header and bounds.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ContainerDragMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.EditorBounds">
      <summary>
        <para>For internal use. Gets the header editor&#39;s bounds.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.RectangleF"/> structure.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.Header">
      <summary>
        <para>Gets or sets the container header.</para>
      </summary>
      <value>A string value that is the container header.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramContainer.HeaderChanged">
      <summary>
        <para>Fires each time the value of the <see cref="P:DevExpress.XtraDiagram.DiagramContainer.Header"/> property is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.HeaderPadding">
      <summary>
        <para>Gets or sets the amount of space between the container header&#39;s borders and its contents.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.Padding"/> value that represents the margin in pixels between the container header&#39;s borders and its contents.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.IsCollapsed">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.Items">
      <summary>
        <para>Provides access to the collection of diagram items placed within the container.</para>
      </summary>
      <value>The collection of diagram items.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.ItemsCanAttachConnectorBeginPoint">
      <summary>
        <para>Specifies whether end-users can attach a connector&#39;s beginning point to the diagram items within the container.</para>
      </summary>
      <value>true, to allow end-users to attach a connector&#39;s beginning point to the diagram items within the container; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.ItemsCanAttachConnectorEndPoint">
      <summary>
        <para>Specifies whether end-users can attach a connector&#39;s end point to the diagram items within the container.</para>
      </summary>
      <value>true to allow end-users to attach a connector&#39;s end point to the diagram items within the container; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.ItemsCanChangeParent">
      <summary>
        <para>Specifies whether end-users can move the diagram items from this container to another.</para>
      </summary>
      <value>true, to allow end-users to move the diagram items from this container to another; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.ItemsCanCopyWithoutParent">
      <summary>
        <para>Specifies whether to allow end-users to copy items from the container.</para>
      </summary>
      <value>true to allow end-users to copy items from the container; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.ItemsCanDeleteWithoutParent">
      <summary>
        <para>Specifies whether to allow end-users to remove items from the container.</para>
      </summary>
      <value>true to allow end-users to remove items from the container; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.ItemsCanEdit">
      <summary>
        <para>Specifies whether to allow end-users to edit the text displayed by the items within the container.</para>
      </summary>
      <value>true to allow end-users to edit the text displayed by the items within the container; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.ItemsCanMove">
      <summary>
        <para>Specifies whether the diagram items within the container can be moved by end-users.</para>
      </summary>
      <value>true, to allow end-users to move the diagram items within the container; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.ItemsCanResize">
      <summary>
        <para>Specifies whether to allow end-users to resize items within the container.</para>
      </summary>
      <value>true to allow end-users to resize items within the container; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.ItemsCanRotate">
      <summary>
        <para>Specifies whether to allow end-users to rotate items within the container.</para>
      </summary>
      <value>true to allow end-users to rotate items within the container; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.ItemsCanSelect">
      <summary>
        <para>Specifies whether to allow end-users to select items within the container.</para>
      </summary>
      <value>true to allow end-users to select items within the container; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.ItemsCanSnapToOtherItems">
      <summary>
        <para>Specifies whether an item within the container can snap to other items.</para>
      </summary>
      <value>true if an item within the container can snap to other items; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.ItemsCanSnapToThisItem">
      <summary>
        <para>Specifies whether items can snap to an item within the container . This is a dependency property.</para>
      </summary>
      <value>true, items can snap to an item within the container; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.Shape">
      <summary>
        <para>Gets or sets the container shape kind.</para>
      </summary>
      <value>An object that specifies the container shape kind.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.ShapeGeometry">
      <summary>
        <para>Gets the object that identifies the geometry of the container.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.ShapeGeometry object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainer.ShowHeader">
      <summary>
        <para>Gets or sets whether to show the container header.</para>
      </summary>
      <value>true to show the container header; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramContainerBase">
      <summary>
        <para>The base class for containers.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramContainerBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramContainerBase"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramContainerBase.#ctor(System.Drawing.RectangleF)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramContainerBase"/> class with specified settings.</para>
      </summary>
      <param name="bounds">A <see cref="T:System.Drawing.RectangleF"/> object that represents the <see cref="T:DevExpress.XtraDiagram.DiagramContainerBase"/> bounds.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramContainerBase.#ctor(System.Single,System.Single,System.Single,System.Single)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramContainerBase"/> class with specified settings.</para>
      </summary>
      <param name="x">The horizontal coordinate of the shape&#39;s upper left corner.</param>
      <param name="y">The vertical coordinate of the shape&#39;s upper left corner.</param>
      <param name="width">The width of the container.</param>
      <param name="height">The height of the container.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainerBase.AdjustBoundsBehavior">
      <summary>
        <para>Specifies the behavior when the end-user moves items close to the container boundaries.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.AdjustBoundaryBehavior"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainerBase.Angle">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramContainerBase"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainerBase.CanRotate">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramContainerBase"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainerBase.IsSnapScope">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramContainerBase.Items">
      <summary>
        <para>Provides access to the collection of diagram items placed within the container.</para>
      </summary>
      <value>The collection of diagram items.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramControl">
      <summary>
        <para>Provides a canvas where you or your end-users can display, create and edit various diagrams.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramControl"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.About">
      <summary>
        <para>Invokes the About dialog.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.AddingNewItem">
      <summary>
        <para>Fires each time a new item is added to the diagram by the end-user (e.g., dropped from the toolbox, drawn using a tool).</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.AlignCanvas(System.Nullable{DevExpress.Utils.HorzAlignment},System.Nullable{DevExpress.Utils.VertAlignment})">
      <summary>
        <para>Applies the specified alignment to the canvas.</para>
      </summary>
      <param name="horizontalAlignment">A <see cref="T:DevExpress.Utils.HorzAlignment"/> enumeration value that is the horizontal alignment of the canvas.</param>
      <param name="verticalAlignment">A <see cref="T:DevExpress.Utils.VertAlignment"/> enumeration value that is the vertical alignment of the canvas.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.AlignPage(System.Nullable{DevExpress.Utils.HorzAlignment},System.Nullable{DevExpress.Utils.VertAlignment})">
      <summary>
        <para>Scrolls the viewport according to the specified horizontal and vertical alignment.</para>
      </summary>
      <param name="horizontalAlignment">A <see cref="T:DevExpress.Utils.HorzAlignment"/> enumeration value.</param>
      <param name="verticalAlignment">A <see cref="T:DevExpress.Utils.VertAlignment"/> enumeration value.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.Appearance">
      <summary>
        <para>Provides access to the properties that control the appearance of the <see cref="T:DevExpress.XtraDiagram.DiagramControl"/>&#39;s elements.</para>
      </summary>
      <value>A DiagramAppearance object.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyCircularLayout(DevExpress.Diagram.Core.Layout.CircularLayoutSettings,System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Applies the circular layout algorithm.</para>
      </summary>
      <param name="settings">A <see cref="T:DevExpress.Diagram.Core.Layout.CircularLayoutSettings"/> object.</param>
      <param name="items">A collection of items to which to apply the algorithm.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyCircularLayout(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Applies the circular layout algorithm.</para>
      </summary>
      <param name="items">A collection of items to which to apply the algorithm.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyMindMapTreeLayout(DevExpress.Diagram.Core.Layout.MindMapTreeLayoutSettings,DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode,System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Applies the mind-map tree layout algorithm.</para>
      </summary>
      <param name="settings">A DevExpress.Diagram.Core.Layout.MindMapTreeLayoutSettings object.</param>
      <param name="splitMode">A <see cref="T:DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode"/> enumeration value that specifies which diagram items are realigned.</param>
      <param name="items">A collection of items to which to apply the algorithm.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyMindMapTreeLayout(DevExpress.Diagram.Core.OrientationKind,System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem},System.Nullable{DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode})">
      <summary>
        <para>Applies the mind-map tree layout algorithm.</para>
      </summary>
      <param name="orientation">An <see cref="T:DevExpress.Diagram.Core.OrientationKind"/> enumeration value.</param>
      <param name="items">A collection of items to which to apply the algorithm.</param>
      <param name="splitMode">A <see cref="T:DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode"/> enumeration value that specifies which diagram items are realigned.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyMindMapTreeLayout(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem},System.Nullable{DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode})">
      <summary>
        <para>Applies the mind-map tree layout algorithm.</para>
      </summary>
      <param name="items">A collection of items to which to apply the algorithm.</param>
      <param name="splitMode">A <see cref="T:DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode"/> enumeration value that specifies which diagram items are realigned.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyMindMapTreeLayoutForSubordinates(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Applies the mind-map tree layout algorithm to the subordinates of the specified diagram items.</para>
      </summary>
      <param name="items">A collection of items which subordinates should be rearranged.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyMindMapTreeLayoutForSubordinates(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem},DevExpress.Diagram.Core.Layout.MindMapTreeLayoutSettings)">
      <summary>
        <para>Applies the mind-map tree layout algorithm to the subordinates of the specified diagram items.</para>
      </summary>
      <param name="items">A collection of diagram items whose subordinates will be repositioned.</param>
      <param name="settings">A DevExpress.Diagram.Core.Layout.MindMapTreeLayoutSettings object.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplySugiyamaLayout(DevExpress.Diagram.Core.Direction,System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Applies the Sugiyama (layered) layout algorithm.</para>
      </summary>
      <param name="direction">A <see cref="T:DevExpress.Diagram.Core.Direction"/></param>
      <param name="items">A collection of items to which to apply the algorithm.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplySugiyamaLayout(DevExpress.Diagram.Core.Layout.LayoutDirection,System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Applies the Sugiyama (layered) layout algorithm.</para>
      </summary>
      <param name="direction">A <see cref="T:DevExpress.Diagram.Core.Layout.LayoutDirection"/> enumeration value.</param>
      <param name="items">A collection of items to which to apply the algorithm.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplySugiyamaLayout(DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings,System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Applies the Sugiyama (layered) layout algorithm.</para>
      </summary>
      <param name="settings">A <see cref="T:DevExpress.Diagram.Core.Layout.SugiyamaLayoutSettings"/> object.</param>
      <param name="items">A collection of items to which to apply the algorithm.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplySugiyamaLayout(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Applies the Sugiyama (layered) layout algorithm.</para>
      </summary>
      <param name="items">A collection of items to which to apply the algorithm.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyTipOverTreeLayout(DevExpress.Diagram.Core.Layout.TipOverDirection,System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem},System.Nullable{DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode})">
      <summary>
        <para>Applies the tip-over tree layout algorithm.</para>
      </summary>
      <param name="direction">A <see cref="T:DevExpress.Diagram.Core.Layout.TipOverDirection"/> enumeration value.</param>
      <param name="items">A collection of items to which to apply the algorithm.</param>
      <param name="splitMode">A <see cref="T:DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode"/> enumeration value specifying whether to apply the layout algorithim only items that are connected to at least one other item.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyTipOverTreeLayout(DevExpress.Diagram.Core.Layout.TipOverTreeLayoutSettings,DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode,System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Applies the tip-over tree layout algorithm.</para>
      </summary>
      <param name="settings">A <see cref="T:DevExpress.Diagram.Core.Layout.TipOverTreeLayoutSettings"/> object.</param>
      <param name="splitMode">A <see cref="T:DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode"/> enumeration value specifying whether to apply the layout algorithm only to items that are connected to at least one other item.</param>
      <param name="items">A collection of items to which to apply the algorithm.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyTipOverTreeLayout(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem},System.Nullable{DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode})">
      <summary>
        <para>Applies the tip-over tree layout algorithm.</para>
      </summary>
      <param name="items">A collection of items to which to apply the algorithm.</param>
      <param name="splitMode">A <see cref="T:DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode"/> enumeration value specifying whether to apply the layout algorithm only to items that are connected to at least one other item.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyTipOverTreeLayoutForSubordinates(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Applies the tip-over tree layout to the subordinates of the specified diagram items.</para>
      </summary>
      <param name="items">A collection of diagram items whose subordinates will be repositioned.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyTipOverTreeLayoutForSubordinates(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem},DevExpress.Diagram.Core.Layout.TipOverTreeLayoutSettings)">
      <summary>
        <para>Applies the tip-over tree layout to the subordinates of the specified diagram items.</para>
      </summary>
      <param name="items">A collection of diagram items whose subordinates will be repositioned.</param>
      <param name="settings">An object that contains the tip-over tree layout settings.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyTreeLayout(DevExpress.Diagram.Core.Direction,System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Applies the tree layout algorithm.</para>
      </summary>
      <param name="direction">A <see cref="T:DevExpress.Diagram.Core.Direction"/> enumeration value.</param>
      <param name="items">A collection of items to which to apply the algorithm.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyTreeLayout(DevExpress.Diagram.Core.Layout.LayoutDirection,System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem},System.Nullable{DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode})">
      <summary>
        <para>Applies the tree layout algorithm.</para>
      </summary>
      <param name="direction">A <see cref="T:DevExpress.Diagram.Core.Layout.LayoutDirection"/> enumeration value.</param>
      <param name="items">A collection of items to which to apply the algorithm.</param>
      <param name="splitMode">A <see cref="T:DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode"/> enumeration value specifying whether to apply the layout algorithm only to items that are connected to at least one other item.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyTreeLayout(DevExpress.Diagram.Core.Layout.TreeLayoutSettings,DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode,System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Applies the tree layout algorithm.</para>
      </summary>
      <param name="settings">A <see cref="T:DevExpress.Diagram.Core.Layout.TreeLayoutSettings"/> object.</param>
      <param name="splitMode">A <see cref="T:DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode"/> enumeration value specifying whether to apply the layout algorithm only to items that are connected to at least one other item.</param>
      <param name="items">A collection of items to which to apply the algorithm.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyTreeLayout(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem},System.Nullable{DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode})">
      <summary>
        <para>Applies the tree layout algorithm.</para>
      </summary>
      <param name="items">A collection of items to which to apply the algorithm.</param>
      <param name="splitMode">A <see cref="T:DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode"/> enumeration value specifying whether to apply the layout algorithm only to items that are connected to at least one other item.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyTreeLayoutForSubordinates(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Applies the tree layout to the subordinates of the specified diagram items.</para>
      </summary>
      <param name="items">A collection of diagram items whose subordinates will be repositioned.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ApplyTreeLayoutForSubordinates(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem},DevExpress.Diagram.Core.Layout.TreeLayoutSettings)">
      <summary>
        <para>Applies the tree layout to the subordinates of the specified diagram items.</para>
      </summary>
      <param name="items">A collection of diagram items whose subordinates will be repositioned.</param>
      <param name="settings">An object that contains the Tree layout settings.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.AttachToForm">
      <summary>
        <para>Binds the form&#39;s Text property to the name of the current document and subscribes the diagram to the form&#39;s OnClosing event. When the end-user attempts to close the window with the diagram that contains unsaved changes, a dialog window is invoked prompting the user to save changes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.BackColor">
      <summary>
        <para>This property is not supported. Use the <see cref="P:DevExpress.XtraDiagram.DiagramControl.Appearance"/> property instead.</para>
      </summary>
      <value></value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.BackColorChanged">
      <summary>
        <para>This member is not supported.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.BeforeItemsMoving">
      <summary>
        <para>Raises each time the end-user attempts to move a diagram item.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.BeforeItemsResizing">
      <summary>
        <para>Raises each time the end-user attempts to resize a diagram item.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.BeforeItemsRotating">
      <summary>
        <para>Raises each time the end-user attempts to rotate a diagram item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.BeginUpdate">
      <summary>
        <para>Locks the <see cref="T:DevExpress.XtraDiagram.DiagramControl"></see>, preventing visual updates of the object and its elements until the EndUpdate or CancelUpdate method is called.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.BringItemsForward(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Brings the specified diagram items forward one step in the stack.</para>
      </summary>
      <param name="items">The diagram items to be brought forward one step in the stack.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.BringItemsIntoView(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem},DevExpress.Diagram.Core.BringIntoViewMode)">
      <summary>
        <para>Scrolls the diagram and changes its zoom factor to bring the specified diagram items into view.</para>
      </summary>
      <param name="items">The diagram items to be brought into view.</param>
      <param name="viewMode">A <see cref="T:DevExpress.Diagram.Core.BringIntoViewMode"/> enumeration value that specifies whether all items should be brought into view when other items are partially visible.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.BringItemsToFront(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Brings the specified diagram items to the front of the stack.</para>
      </summary>
      <param name="items">The diagram items to be brought to the front of the stack.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.BringSelectedItemsForward">
      <summary>
        <para>Brings the selected diagram items forward one step in the stack.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.BringSelectedItemsToFront">
      <summary>
        <para>Brings the specified diagram items to the front of the stack.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.BringSelectionIntoView(DevExpress.Diagram.Core.BringIntoViewMode)">
      <summary>
        <para>Scrolls the diagram and changes its zoom factor to bring the selected diagram items into view.</para>
      </summary>
      <param name="viewMode">A <see cref="T:DevExpress.Diagram.Core.BringIntoViewMode"/> enumeration value that specifies whether all items should be brought into view when other items are partially visible.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.CalcHitInfo(System.Drawing.PointF)">
      <summary>
        <para>Returns information on an object located at the specified position.</para>
      </summary>
      <param name="diagramRelativePoint">A test point where a target element is located.</param>
      <returns>A DiagramHitInfo descendant that contains information on an object located at the specified position.</returns>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.CalcHitItem(System.Drawing.PointF)">
      <summary>
        <para>Returns the diagram item located at the specified position.</para>
      </summary>
      <param name="diagramRelativePoint">A test point where a target element is located.</param>
      <returns>A diagram item located at the specified position.</returns>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.CanRedo">
      <summary>
        <para>Gets whether the Redo action can currently be performed.</para>
      </summary>
      <value>true, if the Redo action can be performed; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CanRedoChanged">
      <summary>
        <para>Fires each time the value of the <see cref="P:DevExpress.XtraDiagram.DiagramControl.CanRedo"/> property is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.CanUndo">
      <summary>
        <para>Gets whether the Undo action can currently be performed.</para>
      </summary>
      <value>true, if the Undo action can be performed; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CanUndoChanged">
      <summary>
        <para>Fires each time the value of the <see cref="P:DevExpress.XtraDiagram.DiagramControl.CanUndo"/> property is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CanvasBoundsChanged">
      <summary>
        <para>Occurs when the size of the canvas is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.CanvasSizeMode">
      <summary>
        <para>Gets or sets whether the canvas size is automatically expanded/shrunk to fit the current shapes layout.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.CanvasSizeMode"/> enumeration value.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ChangeConnectorsType(DevExpress.Diagram.Core.ConnectorType,System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramConnector})">
      <summary>
        <para>Changes the type of the specified connectors.</para>
      </summary>
      <param name="type">A ConnectorType class member that specifies the type of the connectors.</param>
      <param name="connectors">A collection of diagram connectors.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ChangeSelectedConnectorsType(DevExpress.Diagram.Core.ConnectorType)">
      <summary>
        <para>Specifies the connector type for the currently selected connectors.</para>
      </summary>
      <param name="type">A ConnectorType class member that specifies the type of the connectors.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ClearSelection">
      <summary>
        <para>Clears the current selection.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ClosedEditor">
      <summary>
        <para>Fires after the end-user closes the diagram item&#39;s text editor.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.Commands">
      <summary>
        <para>Provides access to the set of available diagram commands.</para>
      </summary>
      <value>A DevExpress.XtraDiagram.Commands.DiagramCommands object that provides a set of diagram commands.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ConnectionChanged">
      <summary>
        <para>Occurs after the connection has been changed by the end-user.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ConnectionChanging">
      <summary>
        <para>Occurs when the connection is about to be changed by the end-user.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ContainerIsCollapsedChanged">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ContainerIsCollapsedChanging">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.CopySelectedItems">
      <summary>
        <para>Copies the currently selected diagram items to the clipboard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.CreateDocking">
      <summary>
        <para>Creates the Properties Panel and Shapes Panel on the sides of the form.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.CreateDocking(DevExpress.XtraDiagram.Docking.DockingMode)">
      <summary>
        <para></para>
      </summary>
      <param name="dockingMode"></param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.CreatePrintDocument">
      <summary>
        <para>Creates an object that can be used to print the diagram.</para>
      </summary>
      <returns>A <see cref="T:System.Drawing.Printing.PrintDocument"/> object.</returns>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.CreateRibbon">
      <summary>
        <para>Creates a Ribbon menu at the top of the form with diagram-specific commands.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.Cursor">
      <summary>
        <para>This member is not supported.</para>
      </summary>
      <value></value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CustomCursor">
      <summary>
        <para>Occurs when the mouse cursor enters the diagram control.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CustomDrawBackground">
      <summary>
        <para>Occurs before the Canvas background is rendered.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CustomDrawItem">
      <summary>
        <para>Provides the ability to customize the appearance of diagram items.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CustomGetEditableItemProperties">
      <summary>
        <para>Allows you to modify the list of diagram item properties that can be edited by end-users in the Properties Panel.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CustomGetEditableItemPropertiesCacheKey">
      <summary>
        <para>Allows you to update the set of properties displayed by the property grid.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CustomGetSerializableItemProperties">
      <summary>
        <para>Allows you to modify the list of serializable properties of diagram items.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CustomHitTest">
      <summary>
        <para>Occurs each time the cursor moves while hovering over a diagram item.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CustomItemDrag">
      <summary>
        <para>Occurs when an end-user starts dragging a diagram item.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CustomItemDragResult">
      <summary>
        <para>Occurs before a drag and drop operation is completed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CustomItemGiveFeedback">
      <summary>
        <para>Occurs while diagram items are dragged.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CustomItemQueryContinueDrag">
      <summary>
        <para>Occurs during a drag-and-drop operation.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CustomLoadDocument">
      <summary>
        <para>Fires after an end-user selects a file in the &#39;Open File&#39; dialog, or after a document load is initiated by setting the <see cref="P:DevExpress.XtraDiagram.DiagramControl.DocumentSource"/> property in code.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CustomLoadImage">
      <summary>
        <para>Fires after an end-user selects a file in the &#39;Insert Picture&#39; dialog.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.CustomSaveDocument">
      <summary>
        <para>Fires when the diagram is about to be saved (when an end-user uses Save actions in the Diagram&#39;s Ribbon menu, or when the <see cref="M:DevExpress.XtraDiagram.DiagramControl.SaveFile"/>/<see cref="M:DevExpress.XtraDiagram.DiagramControl.SaveFileAs"/> method is called). The event allows you to implement custom saving logic.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.CutSelectedItems">
      <summary>
        <para>Moves the currently selected diagram items to the Clipboard.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.DeleteItems(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Removes the specified diagram items.</para>
      </summary>
      <param name="items">The diagram items to be removed.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.DeleteSelectedItems">
      <summary>
        <para>Removes the currently selected diagram items.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.DeletingItems">
      <summary>
        <para>Fires when the end-user removes items from the diagram canvas. Obsoleted. Use the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ItemsDeleting"/> event instead.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.DetachFromForm">
      <summary>
        <para>Removes changes made by the <see cref="M:DevExpress.XtraDiagram.DiagramControl.AttachToForm"/> method.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.DiagramViewInfo">
      <summary>
        <para>Returns the object which contains the internal information used to render the DiagramControl.</para>
      </summary>
      <value>An object that contains the internal information used to render the DiagramControl.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.DocumentLoaded">
      <summary>
        <para>Occurs after a document is loaded into the <see cref="T:DevExpress.XtraDiagram.DiagramControl"/>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.DocumentSource">
      <summary>
        <para>Gets or sets the object from which a diagram is to be loaded or has been loaded. The current diagram is also saved to this object by certain Save actions.
The DocumentSource can be a file full path, Uri object, stream and array of bytes.</para>
      </summary>
      <value>The diagram source.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.EndUpdate">
      <summary>
        <para>Unlocks the <see cref="T:DevExpress.XtraDiagram.DiagramControl"></see> object after a call to the BeginUpdate method and causes an immediate visual update.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ExceptionMessage">
      <summary>
        <para>Allows you to handle exceptions that may raise during various actions on the Diagram Control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ExportDiagram(DevExpress.Diagram.Core.DiagramExportFormat)">
      <summary>
        <para>Invokes the Export dialog window.</para>
      </summary>
      <param name="exportFormat">A DevExpress.Diagram.Core.DiagramExportFormat enumeration value that is the file format which is selected in the dialog window by default.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ExportDiagram(System.IO.Stream,DevExpress.Diagram.Core.DiagramExportFormat,System.Drawing.RectangleF,System.Nullable{System.Single},System.Nullable{System.Single})">
      <summary>
        <para></para>
      </summary>
      <param name="stream"></param>
      <param name="exportFormat"></param>
      <param name="exportBounds"></param>
      <param name="dpi"></param>
      <param name="scale"></param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ExportDiagram(System.IO.Stream,DevExpress.Diagram.Core.DiagramExportFormat,System.Nullable{System.Single},System.Nullable{System.Single})">
      <summary>
        <para>Exports the diagram to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which the created file should be sent.</param>
      <param name="exportFormat">A <see cref="T:DevExpress.Diagram.Core.DiagramExportFormat"/> enumeration value.</param>
      <param name="dpi">A System.Single value which represents the resolution (in dpi).</param>
      <param name="scale">A System.Single value that specifies the scale factor.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ExportDiagram(System.String)">
      <summary>
        <para>Exports the diagram in the specified format to a file.</para>
      </summary>
      <param name="path">A string value that is the path to the file to which to save the diagram.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ExportDiagram(System.String,System.Double,System.Double)">
      <summary>
        <para>Exports the diagram in the specified format to a file.</para>
      </summary>
      <param name="path">A string value that is the path to the file to which to save the diagram.</param>
      <param name="exportDPI">A System.Double value which represents the resolution (in dpi).</param>
      <param name="exportScale">A System.Double value that specifies the scale factor.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ExportDiagram(System.String,System.Drawing.RectangleF,System.Double,System.Double)">
      <summary>
        <para></para>
      </summary>
      <param name="path"></param>
      <param name="exportBounds"></param>
      <param name="exportDPI"></param>
      <param name="exportScale"></param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ExportToPdf(DevExpress.XtraPrinting.PdfExportOptions,System.Boolean)">
      <summary>
        <para>Exports the diagram to a single-page PDF file specified by the user in the invoked dialog window.</para>
      </summary>
      <param name="options">The options that define how a document is exported.</param>
      <param name="showOptionsDialog">true, to show the dialog window that allows the user to configure export options; otherwise, false. By default, false.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ExportToPdf(System.IO.Stream,System.Nullable{System.Drawing.RectangleF},DevExpress.XtraPrinting.PdfExportOptions)">
      <summary>
        <para>Exports the diagram to a single-page PDF document which is sent to the specified stream.</para>
      </summary>
      <param name="stream">A System.IO.Stream object to which the created PDF file should be sent.</param>
      <param name="exportBounds">A System.Windows.Rect object specifying the rectangle which encompasses the total background area when printing the diagram.</param>
      <param name="options">The options that define how a document is exported.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.FileDialogFilter">
      <summary>
        <para>This property is obsolete. Use the <see cref="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.FileDialogFilter"/> property instead.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.FitToDrawing">
      <summary>
        <para>Changes the page size to fit the diagram contents.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.FitToItems(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Scrolls the diagram and changes its zoom factor so that the viewport is sufficiently large to display the specified items.</para>
      </summary>
      <param name="items">The diagram items to fit within the viewport.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.FitToPage">
      <summary>
        <para>Sets the diagram zoom factor value to fit all pages with content within the viewport.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.FitToWidth">
      <summary>
        <para>Sets the diagram zoom factor value to fit the entire diagram width.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.Font">
      <summary>
        <para>This member is not supported. Use the <see cref="P:DevExpress.XtraDiagram.DiagramControl.Appearance"/> property instead.</para>
      </summary>
      <value></value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.FontChanged">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramControl"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.ForeColor">
      <summary>
        <para>This member is not supported. Use the <see cref="P:DevExpress.XtraDiagram.DiagramControl.Appearance"/> property instead.</para>
      </summary>
      <value></value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ForeColorChanged">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramControl"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.HasChanges">
      <summary>
        <para>Gets whether the current document has been changed using DiagramControl&#39;s UI.</para>
      </summary>
      <value>true, if the document has unsaved changes; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.HasChangesChanged">
      <summary>
        <para>Fires each time the value of the <see cref="P:DevExpress.XtraDiagram.DiagramControl.HasChanges"/> property is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.InitializeDocking(DevExpress.XtraBars.Docking.DockManager)">
      <summary>
        <para>Adds docking functionality managed by the specified DockManager to the diagram.</para>
      </summary>
      <param name="dockManager">A <see cref="T:DevExpress.XtraBars.Docking.DockManager"/> object.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.InitializeDocking(DevExpress.XtraBars.Docking.DockManager,DevExpress.XtraDiagram.Docking.DockingMode)">
      <summary>
        <para>Adds docking functionality managed by the specified DockManager to the diagram.</para>
      </summary>
      <param name="dockManager">A <see cref="T:DevExpress.XtraBars.Docking.DockManager"/> object.</param>
      <param name="dockingMode">A <see cref="T:DevExpress.XtraDiagram.Docking.DockingMode"/> enumeration value that specifies which Diagram Designer elements should be added to the diagram.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.InitializeRibbon(DevExpress.XtraBars.Ribbon.RibbonControl)">
      <summary>
        <para>Sets the specified RibbonControl as the diagram&#39;s Ribbon.</para>
      </summary>
      <param name="ribbon">A <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/> object to be used as the diagram&#39;s Ribbon.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.InsertImage">
      <summary>
        <para>Invokes the Insert Picture dialog that allows the end-user to add an image item to the diagram.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.IsContainerToolsVisible">
      <summary>
        <para>Gets whether the Container Tools Ribbon category is currently displayed.</para>
      </summary>
      <value>true if the Container Tools ribbon category is displayed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.IsHScrollVisible">
      <summary>
        <para>Gets whether the horizontal scroll bar is visible.</para>
      </summary>
      <value>true, if the horizontal scroll bar is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.IsImageToolsVisible">
      <summary>
        <para>Gets whether the Image Tools Ribbon category is currently displayed.</para>
      </summary>
      <value>true if the Image Tools ribbon category is displayed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.IsLoading">
      <summary>
        <para>Indicates whether the Diagram control is currently being initialized.</para>
      </summary>
      <value>true if the Diagram control is being initialized; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.IsTextEditMode">
      <summary>
        <para>Gets whether a diagram item&#39;s text is currently being edited.</para>
      </summary>
      <value>true, if diagram item text editing is in progress; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.IsTextEditModeChanged">
      <summary>
        <para>Fires each time the value of the <see cref="P:DevExpress.XtraDiagram.DiagramControl.IsTextEditMode"/> property is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.IsVScrollVisible">
      <summary>
        <para>Gets whether the vertical scroll bar is visible.</para>
      </summary>
      <value>true, if the vertical scroll bar is visible; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ItemBoundsChanged">
      <summary>
        <para>Fires each time an item&#39;s bounds are changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ItemContentChanged">
      <summary>
        <para>Fires each time the end-user changes the item&#39;s text.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ItemCreating">
      <summary>
        <para>Occurs when the DiagramControl creates a diagram item.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ItemDrawing">
      <summary>
        <para>Fires repeatedly as the end-user is drawing a shape using a drawing tool (Rectangle, Ellipse, Right Triangle or Hexagon).</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ItemInitializing">
      <summary>
        <para>Fires each time an item is created via the DevExpress.Diagram.Core.ItemTool.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.Items">
      <summary>
        <para>Provides access to the collection of diagram items displayed by the <see cref="T:DevExpress.XtraDiagram.DiagramControl"/>.</para>
      </summary>
      <value>The collection of diagram items.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ItemsChanged">
      <summary>
        <para>Fires each time an item is added or removed from the diagram.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ItemsDeleting">
      <summary>
        <para>Fires each time diagram items have been removed from the canvas.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ItemsMoving">
      <summary>
        <para>Fires as the end-user is moving diagram items.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ItemsPasting">
      <summary>
        <para>Fires as the end-user pastes the diagram items from the clipboard onto the canvas.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ItemsResizing">
      <summary>
        <para>Fires when a diagram item is resized by the end-user or programmatically.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ItemsRotating">
      <summary>
        <para>Fires when the end-user attempts to rotate a diagram item.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.ItemTypeRegistrator">
      <summary>
        <para>Provides access to methods used to create serializable custom diagram item types.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.DiagramItemTypeRegistrator"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.LastShapeStyles">
      <summary>
        <para>Provides access to the collection of the recently used shape styles.</para>
      </summary>
      <value>A read-only collection of the recently used shape styles.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.LoadDocument(System.IO.Stream)">
      <summary>
        <para>Loads a diagram stored in the specified stream.</para>
      </summary>
      <param name="stream">A stream that stores the diagram to be loaded.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.LoadDocument(System.String)">
      <summary>
        <para>Loads a diagram stored in the specified file.</para>
      </summary>
      <param name="fileName">The full path to the file that stores the diagram to be loaded.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.LoadImage">
      <summary>
        <para>Invokes the Insert Picture dialog that allows the end-user to change the selected image item.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.MenuManager">
      <summary>
        <para>Gets or sets the menu manager which controls the look and feel of the context menus.</para>
      </summary>
      <value>An object which implements the DevExpress.Utils.Menu.IDXMenuManager interface.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.NewDocument">
      <summary>
        <para>Discards the current document and creates a new one.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.NewFile">
      <summary>
        <para>Closes the current file and creates a new one.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.OpenFile">
      <summary>
        <para>Displays the &quot;Open File&quot; dialog that allows an end-user to load an XML file containing a saved diagram.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.OptionsBehavior">
      <summary>
        <para>Contains options that control the DiagramControl&#39;s behavior.</para>
      </summary>
      <value>The DiagramControl&#39;s behavior options.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.OptionsCircularLayout">
      <summary>
        <para>Contains options that control the circular layout.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsCircularLayout"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.OptionsConnector">
      <summary>
        <para>Contains options that control connectors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsConnector"/> object that stores the connector settings.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.OptionsExport">
      <summary>
        <para>Contains options that control diagram export.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsExport"/> object that stores the export settings.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.OptionsMindMapTreeLayout">
      <summary>
        <para>Contains options that control the mind-map tree layout.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsMindMapTreeLayout"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.OptionsProtection">
      <summary>
        <para>Contains options that control the ability of the end-user to perform certain operations.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsProtection"/> object that stores the protection settings.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.OptionsSugiyamaLayout">
      <summary>
        <para>Contains options that control the Sugiyama (layered) layout.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsSugiyamaLayout"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.OptionsTipOverTreeLayout">
      <summary>
        <para>Contains options that control the tip-over tree layout.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsTipOverTreeLayout"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.OptionsTreeLayout">
      <summary>
        <para>Contains options that control the tree layout.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsTreeLayout"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.OptionsView">
      <summary>
        <para>Contains the DiagramControl&#39;s view settings.</para>
      </summary>
      <value>An object that specifies the DiagramControl&#39;s view settings.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.Page">
      <summary>
        <para>Gets the diagram canvas.</para>
      </summary>
      <value>A DevExpress.XtraDiagram.DiagramRoot object that represents the diagram canvas.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.PanAndZoom">
      <summary>
        <para>Gets or sets the Pan and Zoom Panel.</para>
      </summary>
      <value>An object implementing the DevExpress.XtraDiagram.Utils.IPanZoom interface.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.Paste">
      <summary>
        <para>Inserts the diagram items from the Clipboard onto the canvas.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.PlatformCache">
      <summary>
        <para>This property supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.PointToControl(DevExpress.Utils.PointFloat)">
      <summary>
        <para>Converts the coordinates of a point on the canvas to the control-relative coordinates.</para>
      </summary>
      <param name="documentPoint">A <see cref="T:System.Windows.Point"/> object that represents a point on the canvas.</param>
      <returns>A <see cref="T:System.Windows.Point"/> object that represents the control-relative coordinates.</returns>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.PointToDocument(DevExpress.Utils.PointFloat)">
      <summary>
        <para>Converts the control-relative coordinates to the coordinates of a point on the canvas.</para>
      </summary>
      <param name="controlPoint">A <see cref="T:System.Windows.Point"/> object that represents the control-relative coordinates.</param>
      <returns>A <see cref="T:System.Windows.Point"/> object that represents a point on the canvas.</returns>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.PrimarySelection">
      <summary>
        <para>Gets the object that is currently the primary selected diagram item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Diagram.DiagramItem"/> object.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.Print">
      <summary>
        <para>Invokes the Print dialog window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.PrintToPdf(DevExpress.XtraPrinting.PdfExportOptions,System.Nullable{System.Drawing.SizeF},System.Nullable{System.Windows.Forms.Padding},System.Boolean)">
      <summary>
        <para>Exports the diagram to a multi-page PDF file specified by the user in the invoked dialog window.</para>
      </summary>
      <param name="options">The options that define how a document is exported.</param>
      <param name="pageSize">A System.Drawing.SizeF object specifying the PDF page size.</param>
      <param name="pageMargin">A System.Windows.Forms.Padding object specifying the PDF page margins.</param>
      <param name="showOptionsDialog">true, to show the dialog window that allows the user to configure export options; otherwise, false. By default, true.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.PrintToPdf(System.IO.Stream,System.Nullable{System.Drawing.RectangleF},DevExpress.XtraPrinting.PdfExportOptions,System.Nullable{System.Drawing.SizeF},System.Nullable{System.Windows.Forms.Padding})">
      <summary>
        <para>Exports the diagram to a multi-page PDF document which is sent to the specified stream.</para>
      </summary>
      <param name="stream">A System.IO.Stream object to which the created PDF document should be sent.</param>
      <param name="printBounds">A System.Drawing.RectangleF object specifying the rectangle which encompasses the total background area when printing the diagram.</param>
      <param name="options">The options that define how a document is exported.</param>
      <param name="pageSize">A System.Drawing.SizeF object specifying the PDF page size.</param>
      <param name="pageMargin">A System.Windows.Forms.Padding object specifying the PDF page margins.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.PropertyGrid">
      <summary>
        <para>Gets or sets the Property Grid control that displays the properties of a diagram item, once it is selected on the diagram canvas, at runtime.</para>
      </summary>
      <value>The Property Grid control displaying a selected diagram item&#39;s settings.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.QueryConnectionPoints">
      <summary>
        <para>Fires each time the end-user moves the cursor with the active Connector Tool near shapes or their connection points.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.QueryItemDrawAction">
      <summary>
        <para>Fires repeatedly as the end-user hovers over the diagram canvas while a drawing tool (Rectangle, Ellipse, Right Triangle or Hexagon) is selected.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.QueryItemEditAction">
      <summary>
        <para>Fires each time the end-user attempts to initiate text editing to edit the text within a diagram item.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.QueryItemsAction">
      <summary>
        <para>Fires each time the end-user attempts to perform an action on a diagram item.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.QueryItemSnapping">
      <summary>
        <para>Fires repeatedly as the end-user triggers snapping while dragging or resizing a diagram shape.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.QuickPrint">
      <summary>
        <para>Prints the current diagram to the default printer.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.RegisterRoutingStrategy(DevExpress.Diagram.Core.ConnectorType,DevExpress.Diagram.Core.Routing.DiagramRoutingStrategy)">
      <summary>
        <para>Registers a custom routing strategy for the specified connector type.</para>
      </summary>
      <param name="connectorType">A connector type to which to apply the routing strategy.</param>
      <param name="routingStrategy">A DevExpress.Diagram.Core.Routing.DiagramRoutingStrategy descendant that defines the custom routing strategy.</param>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.RelayoutItemsCompleted">
      <summary>
        <para>Occurs immediately after an Automatic Layout algorithm is completed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ResetBackColor">
      <summary>
        <para>Sets the <see cref="P:DevExpress.XtraDiagram.DiagramControl.BackColor"/> property to Color.Empty.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ResetSelectedImages">
      <summary>
        <para>Rolls the selected images back to their original state, reverting all resizing and rotation transformations.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.RootToolsModel">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.SaveDocument(System.IO.Stream)">
      <summary>
        <para>Saves the diagram to the specified stream.</para>
      </summary>
      <param name="stream">The stream where the diagram will be saved.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.SaveDocument(System.String)">
      <summary>
        <para>Saves the diagram to the specified file.</para>
      </summary>
      <param name="fileName">The full path to the file where the diagram will be saved.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.SaveFile">
      <summary>
        <para>Saves the diagram to storage specified by the <see cref="P:DevExpress.XtraDiagram.DiagramControl.DocumentSource"/>. If the <see cref="P:DevExpress.XtraDiagram.DiagramControl.DocumentSource"/> is not set, the <see cref="M:DevExpress.XtraDiagram.DiagramControl.SaveFileAs"/> method is called, that invokes the &quot;Save File As&quot; dialog.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.SaveFileAs">
      <summary>
        <para>Displays the &quot;Save File As&quot; dialog that allows an end-user to save the current diagram to an XML file.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.ScrollPos">
      <summary>
        <para>Gets or sets the current scrolling position.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.PointF"/> value that is the current scrolling position.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ScrollToPoint(DevExpress.Utils.PointFloat,System.Nullable{DevExpress.Utils.HorzAlignment},System.Nullable{DevExpress.Utils.VertAlignment})">
      <summary>
        <para>Scrolls the diagram to the specified point.</para>
      </summary>
      <param name="diagramPoint">A <see cref="T:DevExpress.Utils.PointFloat"/> object that represents the point to which to scroll the document.</param>
      <param name="horizontalAlignment">A <see cref="T:DevExpress.Utils.HorzAlignment"/> enumeration value that specifies how to align the point relative to the viewport. By default, Center.</param>
      <param name="verticalAlignment">A <see cref="T:DevExpress.Utils.VertAlignment"/> enumeration value that specifies how to align the point relative to the viewport. By default, Center.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.SelectAll">
      <summary>
        <para>Selects all shapes and connectors within the current document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.SelectedItems">
      <summary>
        <para>Provides access to the collection of the currently selected diagram items.</para>
      </summary>
      <value>A read-only collection of the currently selected diagram items.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.SelectedStencils">
      <summary>
        <para>This property is obsolete. Use the <see cref="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.SelectedStencils"/> property instead.</para>
      </summary>
      <value></value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.SelectionChanged">
      <summary>
        <para>Fires when the selection changes (new items are selected, or currently selected items are deselected).</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.SelectionModel">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.SelectionToolsModel">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.SelectItem(DevExpress.XtraDiagram.DiagramItem,DevExpress.Diagram.Core.ModifySelectionMode)">
      <summary>
        <para>Selects the specified diagram item.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object that is the item to select.</param>
      <param name="modifySelectionMode">A <see cref="T:DevExpress.Diagram.Core.ModifySelectionMode"/> enumeration value that specifies whether to add or replace the existing selection with the specified diagram item.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.SelectItems(DevExpress.XtraDiagram.DiagramItem[])">
      <summary>
        <para>Clears the existing selection and then selects the specified items.</para>
      </summary>
      <param name="items">A comma-separated list of <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> objects, or an array of these objects.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.SelectItems(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem},DevExpress.Diagram.Core.ModifySelectionMode)">
      <summary>
        <para>Selects the specified diagram items.</para>
      </summary>
      <param name="items">An array of <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> objects representing the diagram items to select.</param>
      <param name="modifySelectionMode">A <see cref="T:DevExpress.Diagram.Core.ModifySelectionMode"/> enumeration value that specifies whether to add or replace the existing selection with the specified diagram items.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.SendItemsBackward(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Sends the specified diagram items back one step in the stack.</para>
      </summary>
      <param name="items">The diagram items to be sent back one step in the stack.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.SendItemsToBack(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Sends the specified diagram items to the back of the stack.</para>
      </summary>
      <param name="items">The diagram items to be sent to the back of the stack.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.SendSelectedItemsBackward">
      <summary>
        <para>Sends the selected diagram items back one step in the stack.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.SendSelectedItemsToBack">
      <summary>
        <para>Sends the specified diagram items to the back of the stack.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.SetContainersIsCollapsed(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramContainer},System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="containers"></param>
      <param name="isCollapsed"></param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.SetSelectedImagesScale(System.Double)">
      <summary>
        <para>Sets the specified zoom factor for selected image items.</para>
      </summary>
      <param name="scale">A System.Double value that is the zoom factor to apply to selected image items.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.SetSelectedImagesStretchMode(DevExpress.Diagram.Core.StretchMode)">
      <summary>
        <para>Sets the specified stretch mode for selected image items.</para>
      </summary>
      <param name="mode">A <see cref="T:DevExpress.Diagram.Core.StretchMode"/> enumeration value.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ShowEditor">
      <summary>
        <para>Invokes the text editor for the primary selected diagram shape.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ShowingEditor">
      <summary>
        <para>Enables you to prevent an end-user from activating the editors of individual diagram items.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ShowingOpenDialog">
      <summary>
        <para>Fires before showing an &#39;Open File&#39; dialog, and allows it to be customized or replaced.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ShowingOpenImageDialog">
      <summary>
        <para>Fires before showing an &#39;Insert Picture&#39; dialog, and allows it to be customized.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ShowingSaveDialog">
      <summary>
        <para>Fires before showing a &#39;Save File As&#39; dialog, and allows it to be customized or replaced.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ShowPrintPreview">
      <summary>
        <para>Invokes the Print Preview.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.SubordinatesHidden">
      <summary>
        <para>Occurs after a <see cref="T:DevExpress.XtraDiagram.DiagramItem"/>&#39;s subordinates are hidden.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.SubordinatesHiding">
      <summary>
        <para>Occurs before a <see cref="T:DevExpress.XtraDiagram.DiagramItem"/>&#39;s subordinates are hidden.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.SubordinatesShowing">
      <summary>
        <para>Occurs before a <see cref="T:DevExpress.XtraDiagram.DiagramItem"/>&#39;s subordinates are shown.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.SubordinatesShown">
      <summary>
        <para>Occurs after a <see cref="T:DevExpress.XtraDiagram.DiagramItem"/>&#39;s subordinates are shown.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.Title">
      <summary>
        <para>The document title to which the window title can be bound.</para>
      </summary>
      <value>A string object that represents the document title.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.TitleChanged">
      <summary>
        <para>Fires each time the value of the <see cref="P:DevExpress.XtraDiagram.DiagramControl.Title"/> property is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ToggleSubordinatesVisibility(DevExpress.XtraDiagram.DiagramItem)">
      <summary>
        <para>Shows or hides the item&#39;s subordinate items.</para>
      </summary>
      <param name="item">The <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> for which to toggle the visibility of the subordinate items.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.Toolbox">
      <summary>
        <para>Gets or sets a Toolbox Control linked to the <see cref="T:DevExpress.XtraDiagram.DiagramControl"/> and populated with diagram shapes.</para>
      </summary>
      <value>A Toolbox Control.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramControl.UndoManager">
      <summary>
        <para>Provides access to an object that manages the undo and redo operations in the <see cref="T:DevExpress.XtraDiagram.DiagramControl"/>.</para>
      </summary>
      <value>A DevExpress.Utils.UndoManager object.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.UnregisterRoutingStrategy(DevExpress.Diagram.Core.ConnectorType)">
      <summary>
        <para>Removes the custom routing strategy applied to the specified connector type.</para>
      </summary>
      <param name="connectorType">A connector type for which to reset the routing strategy.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.UnselectItem(DevExpress.XtraDiagram.DiagramItem)">
      <summary>
        <para>Unselects the specified diagram item.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> to unselect.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.UpdateConnectorsRoute(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramConnector})">
      <summary>
        <para>Recalculates the route for the specified diagram connectors.</para>
      </summary>
      <param name="connectors">A list of connectors whose routes should be recalculated.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.UpdateRoute">
      <summary>
        <para>Recalculates the routes of all diagram connectors.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramControl.ZoomFactorChanged">
      <summary>
        <para>Raises each time the current diagram zoom factor is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ZoomIn">
      <summary>
        <para>Increases the document zoom factor.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramControl.ZoomOut">
      <summary>
        <para>Decreases the document zoom factor.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramCustomItemDragEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.CustomItemDrag"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramCustomItemDragEventArgs.#ctor(DevExpress.XtraDiagram.DiagramItem,DevExpress.Utils.PointFloat,System.Collections.Generic.IList{DevExpress.XtraDiagram.DiagramItem},DevExpress.Diagram.Core.Native.LazyArg{System.Runtime.InteropServices.ComTypes.IDataObject},System.Windows.Forms.DragDropEffects)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramCustomItemDragEventArgs"/> class.</para>
      </summary>
      <param name="sourceItem">A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> descendant that represents the item the end-user is dragging.</param>
      <param name="sourceItemPoint">A point on the canvas where the end-user initiated the drag-and-drop operation.</param>
      <param name="items">A list of <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> descendants that are objects of the drag-and-drop operation.</param>
      <param name="data">A System.Windows.Forms.IDataObject object that provides data about the diagram items the end-user is dragging.</param>
      <param name="result"></param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramCustomItemDragEventArgs.Data">
      <summary>
        <para>Provides data about the diagram items the end-user is dragging.</para>
      </summary>
      <value>A System.Windows.Forms.IDataObject object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramCustomItemDragEventArgs.Items">
      <summary>
        <para>Returns the list of items that are objects of the drag-and-drop operation.</para>
      </summary>
      <value>A list of <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> descendants.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramCustomItemDragEventArgs.Result">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramCustomItemDragEventArgs.SourceItem">
      <summary>
        <para>Returns the item the end-user is dragging.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> descendant.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramCustomItemDragEventArgs.SourceItemPoint">
      <summary>
        <para>Returns the point on the canvas where the end-user initiated the drag-and-drop operation.</para>
      </summary>
      <value>A point on the canvas where the end-user initiated the drag-and-drop operation.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramCustomItemQueryContinueDragEventArgs">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramCustomItemQueryContinueDragEventArgs.#ctor(System.Collections.ObjectModel.ReadOnlyCollection{DevExpress.XtraDiagram.DiagramItem},DevExpress.Diagram.Core.DragDropKeyState,System.Boolean,System.Windows.Forms.DragAction)">
      <summary>
        <para></para>
      </summary>
      <param name="items"></param>
      <param name="keyStates"></param>
      <param name="isCancellationRequested"></param>
      <param name="action"></param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramCustomItemQueryContinueDragEventArgs.Action">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramCustomItemQueryContinueDragEventArgs.IsCancellationRequested">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramCustomItemQueryContinueDragEventArgs.Items">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramCustomItemQueryContinueDragEventArgs.KeyStates">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramDataBindingController">
      <summary>
        <para>Allows you to generate diagrams from a data source.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramDataBindingController.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramDataBindingController"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramDataBindingController.#ctor(System.ComponentModel.IContainer)">
      <summary>
        <para>Initializes a new <see cref="T:DevExpress.XtraDiagram.DiagramDataBindingController"/> class instance with the specified container.</para>
      </summary>
      <param name="container">A <see cref="T:System.ComponentModel.IContainer"/> object that provides functionality for containers.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramDataBindingController.ConnectorFromMember">
      <summary>
        <para>Specifies the name of the data field that identifies the item to use as the connector&#39;s begin item.</para>
      </summary>
      <value>A string value that is the name of the data field that identifies the item to use as the connector&#39;s begin item.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramDataBindingController.ConnectorKeyMember">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramDataBindingController.ConnectorsSource">
      <summary>
        <para>Gets or sets the data source for connectors.</para>
      </summary>
      <value>An IEnumerable object that provides data to the <see cref="T:DevExpress.XtraDiagram.DiagramDataBindingController"/>.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramDataBindingController.ConnectorToMember">
      <summary>
        <para>Specifies the name of the data field that identifies the item to use as the connector&#39;s end item.</para>
      </summary>
      <value>A string value that is the name of the data field that identifies the item to use as the connector&#39;s end item.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramDataBindingController.DiagramConnectorAdded">
      <summary>
        <para>Fires each time the data binding controller generates a connector.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramDataBindingController.DiagramConnectorAdding">
      <summary>
        <para>Fires each time the data binding controller generates a connector.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramDataBindingController.LayoutKind">
      <summary>
        <para>Specifies the automatic layout to be applied to the generated diagram.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.DiagramLayoutKind enumeration value.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramDataBindingControllerBase">
      <summary>
        <para>Serves as a base for behaviors that are used to bind the Diagram Control to data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.#ctor(System.ComponentModel.IContainer)">
      <summary>
        <para>Initializes a new <see cref="T:DevExpress.XtraDiagram.DiagramDataBindingControllerBase"/> class instance with the specified container.</para>
      </summary>
      <param name="container">A <see cref="T:System.ComponentModel.IContainer"/> object that provides functionality for containers.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.BeginInit">
      <summary>
        <para>Starts the <see cref="T:DevExpress.XtraDiagram.DiagramDataBindingControllerBase"/>&#39;s initialization. Initialization occurs at runtime.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.CanRefresh">
      <summary>
        <para>Gets whether the diagram can fetch data from the source collections.</para>
      </summary>
      <value>true, if the diagram can fetch data from the source collections; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.CanRefreshChanged">
      <summary>
        <para>Fires each time the value of the <see cref="P:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.CanRefresh"/> property is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.ClearExistingItems">
      <summary>
        <para>Specifies whether to remove existing items before generating a diagram from the data source.</para>
      </summary>
      <value>true to remove existing items before generating a diagram from the data source; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.ConnectorsZOrder">
      <summary>
        <para>Specifies whether the connectors overlap diagram items.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.ConnectorsZOrder enumeration value.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.CustomLayoutItems">
      <summary>
        <para>Occurs before the generated items are added to the diagram.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.DataSource">
      <summary>
        <para>Gets or sets the data source for items.</para>
      </summary>
      <value>An IEnumerable object that provides data to the <see cref="T:DevExpress.XtraDiagram.DiagramDataBindingControllerBase"/>.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.Diagram">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.XtraDiagram.DiagramControl"/> associated with the current <see cref="T:DevExpress.XtraDiagram.DiagramDataBindingControllerBase"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramControl"/> object.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.DiagramItemAdded">
      <summary>
        <para>Fires each time the data binding controller generates a diagram item.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.DiagramItemAdding">
      <summary>
        <para>Fires each time the data binding controller generates a diagram item.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.EnableNotificationToSource">
      <summary>
        <para>Gets or sets whether to send the data change notifications to the data source.</para>
      </summary>
      <value>true, to enable the data change notifications to the data source; otherwise, false. By default, true.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.EndInit">
      <summary>
        <para>Ends the <see cref="T:DevExpress.XtraDiagram.DiagramDataBindingControllerBase"/>&#39;s initialization.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.GenerateConnector">
      <summary>
        <para>Fires before generating a connector from the data source and allows you to customize it.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.GenerateItem">
      <summary>
        <para>Fires before generating a diagram item representing a data object and allows you to customize it.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.ItemsGenerated">
      <summary>
        <para>Occurs when diagram items are generated from the data source.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.ItemsPath">
      <summary>
        <para>Specifies the name of the data field that identifies the collection of nested data items.</para>
      </summary>
      <value>A string value that is the name of the data field that identifies the collection of data items.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.ItemsSelector">
      <summary>
        <para>Gets or sets the selector that returns the data field that identifies the collection of nested data items.</para>
      </summary>
      <value>An object implementing the DevExpress.Diagram.Core.IChildrenSelector interface.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.KeyMember">
      <summary>
        <para>Specifies the name of the data field that identifies the data item.</para>
      </summary>
      <value>A System.String value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.KeySelector">
      <summary>
        <para>Gets or sets the selector that returns the data field that identifies the data item.</para>
      </summary>
      <value>An object implementing the DevExpress.Diagram.Core.IKeySelector interface.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.Refresh">
      <summary>
        <para>Fetches data from the source collections.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.TemplateDiagram">
      <summary>
        <para>Gets or sets the diagram whose settings are used to generate the diagram from the data source.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramControl"/> object.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.UpdateConnector">
      <summary>
        <para>Occurs when a property of the data object represented by a diagram connector has changed its value.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramDataBindingControllerBase.UpdateItem">
      <summary>
        <para>Occurs when a property of the data object represented by a diagram item has changed its value.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramDrawingContext">
      <summary>
        <para>Lists values used to indicate whether the item is to be drawn on the canvas, toolbox, in the print or export output or as the drag preview.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraDiagram.DiagramDrawingContext.Canvas">
      <summary>
        <para>The item is to be drawn on the canvas.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraDiagram.DiagramDrawingContext.DragPreview">
      <summary>
        <para>The item is to be drawn as the drag preview.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraDiagram.DiagramDrawingContext.ExportToImage">
      <summary>
        <para>The item is to be drawn on the canvas when exporting the diagram to image.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraDiagram.DiagramDrawingContext.ExportToPdf">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraDiagram.DiagramDrawingContext.Print">
      <summary>
        <para>The item is to be drawn on the canvas when printing the diagram.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraDiagram.DiagramDrawingContext.Toolbox">
      <summary>
        <para>The item is to be drawn on the toolbox.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramImage">
      <summary>
        <para>Represents a diagram image item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramImage.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramImage"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramImage.AllowPlaceholder">
      <summary>
        <para>Gets or sets whether the current <see cref="T:DevExpress.XtraDiagram.DiagramImage"/> displays a placeholder when its <see cref="P:DevExpress.XtraDiagram.DiagramImage.Image"/> property is null. This is a dependency property.</para>
      </summary>
      <value>true, to display a placeholder; otherwise, false. The default is true.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramImage.CanEdit">
      <summary>
        <para>This member is not supported by the <see cref="T:DevExpress.XtraDiagram.DiagramImage"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramImage.FitToImage">
      <summary>
        <para>Changes the size of the diagram image item so that it matches the size of the source image.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramImage.FlipMode">
      <summary>
        <para>Gets or sets the flip effect that is currently applied to the image.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ImageFlipMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramImage.Image">
      <summary>
        <para>Gets or sets the source of the image displayed by the image item.</para>
      </summary>
      <value>An image source.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramImage.Padding">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramImage.PlaceholderGeometry">
      <summary>
        <para>Gets the object that identifies the geometry of the image placeholder.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.ShapeGeometry object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramImage.SourceImageSize">
      <summary>
        <para>Gets the size of the source image.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Size"/> instance that is the size of the source image.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramImage.StretchMode">
      <summary>
        <para>Specifies whether to preserve the aspect ratio of the source image when resizing the diagram image item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.StretchMode"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramItem">
      <summary>
        <para>The base class for diagram items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItem.#ctor(System.Drawing.RectangleF)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> class.</para>
      </summary>
      <param name="bounds">A <see cref="T:System.Drawing.RectangleF"/> object that represents the <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> bounds.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItem.#ctor(System.Single,System.Single,System.Single,System.Single)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> class.</para>
      </summary>
      <param name="x">A <see cref="T:System.Single"/> value, specifying the horizontal coordinate of the item&#39;s upper left corner.</param>
      <param name="y">A <see cref="T:System.Single"/> value, specifying the vertical coordinate of the item&#39;s upper left corner.</param>
      <param name="width">A <see cref="T:System.Single"/> value, specifying the item width.</param>
      <param name="height">A <see cref="T:System.Single"/> value, specifying the item height.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.ActualConnectionPoints">
      <summary>
        <para>Returns the list of connection point locations.</para>
      </summary>
      <value>A list of connection point locations.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.ActualPadding">
      <summary>
        <para>Gets the actual padding for the diagram item.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.Padding"/> value that represents the margin between the item&#39;s borders and its contents.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.ActualPaddingF">
      <summary>
        <para>Gets the actual padding for the diagram item.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.ThicknessInfo value that represents the margin between the item&#39;s borders and its contents.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.AffectedByLayoutAlgorithms">
      <summary>
        <para>Specifies whether the item can be realigned by the automatic layout algoritms.</para>
      </summary>
      <value>true if the item can be realigned by the automatic layout algorithms; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.Anchors">
      <summary>
        <para>Gets or sets the sides of the root item (page) to which the current <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object is anchored.</para>
      </summary>
      <value>The sides of the root item (page) to which the current <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object is anchored.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.Angle">
      <summary>
        <para>Specifies the angle by which the diagram item is rotated counterclockwise around its anchor point. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value specifying an angle in degrees.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramItem.AngleChanged">
      <summary>
        <para>Fires each time the value of the <see cref="P:DevExpress.XtraDiagram.DiagramItem.Angle"/> property is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.Appearance">
      <summary>
        <para>Provides access to the item&#39;s appearance settings.</para>
      </summary>
      <value>The object that provides the item&#39;s appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.AreSubordinatesVisible">
      <summary>
        <para>Gets whether the current item&#39;s subordinates are visible. This is a dependency property.</para>
      </summary>
      <value>true, if current item&#39;s subordinates are visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.BackgroundId">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.Bindings">
      <summary>
        <para>Gets a collection of current diagram item&#39;s bindings. This is a dependency property.</para>
      </summary>
      <value>A DiagramBindingCollection object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.Bounds">
      <summary>
        <para>Gets or sets the item bounds.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.RectangleF"/> object that represents the item bounds.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramItem.BoundsChanged">
      <summary>
        <para>Fires each time the value of the <see cref="P:DevExpress.XtraDiagram.DiagramItem.Bounds"/> property is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CanAttachConnectorBeginPoint">
      <summary>
        <para>Specifies whether end-users can attach a connector&#39;s beginning point to the item.</para>
      </summary>
      <value>true to allow end-users to attach the begin point of a connector to the item; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CanAttachConnectorEndPoint">
      <summary>
        <para>Specifies whether end-users can attach the end point of a connector to the item.</para>
      </summary>
      <value>true to allow end-users to attach the end point of a connector to the item; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CanChangeParent">
      <summary>
        <para>Specifies whether to allow end-users to place the item into another container.</para>
      </summary>
      <value>true to allow end-users to place the item into another container; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CanCopy">
      <summary>
        <para>Specifies whether to allow end-users to copy the item to the clipboard.</para>
      </summary>
      <value>true, if the item can be copied; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CanCopyWithoutParent">
      <summary>
        <para>Specifies whether to allow end-users to copy the item placed in a container.</para>
      </summary>
      <value>true to allow end-users to copy the item placed in a container; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CanDelete">
      <summary>
        <para>Specifies whether to allow end-users to delete the item.</para>
      </summary>
      <value>true, if the item can be deleted; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CanDeleteWithoutParent">
      <summary>
        <para>Specifies whether to allow end-users to delete the item placed in a container.</para>
      </summary>
      <value>true to allow end-users to delete the item placed in a container; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CanEdit">
      <summary>
        <para>Specifies whether to allow end-users to edit the text displayed by the item.</para>
      </summary>
      <value>true to allow end-users to edit the item&#39;s text; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CanHideSubordinates">
      <summary>
        <para>Gets or sets whether whether to display the expand-collapse button below the diagram item that allows an end-user to show/hide the item&#39;s subordinates.</para>
      </summary>
      <value>true, to allow hiding subordinates of the current item; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CanMove">
      <summary>
        <para>Specifies whether to allow end-users to move the item across the canvas.</para>
      </summary>
      <value>true to allow end-users to move the item across the canvas; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CanResize">
      <summary>
        <para>Specifies whether to allow end-users to resize the item.</para>
      </summary>
      <value>true to allow end-users to resize the item; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CanRotate">
      <summary>
        <para>Specifies whether to allow end-users to rotate the item.</para>
      </summary>
      <value>true to allow end-users to rotate the item; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CanSelect">
      <summary>
        <para>Specifies whether to allow end-users to select the item.</para>
      </summary>
      <value>true to allow end-users to select the item; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CanSnapToOtherItems">
      <summary>
        <para>Specifies whether the item can snap to other items.</para>
      </summary>
      <value>true, if the item can snap to other items; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CanSnapToThisItem">
      <summary>
        <para>Specifies whether other items can snap to this item.</para>
      </summary>
      <value>true, if the item can snap to other items; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CollapseButtonHorizontalAlignment">
      <summary>
        <para>Gets or sets the horizontal alignment of the collapse button&#39;s border relative to its actual position.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Diagram.Core.Alignment"/> enumeration value that specifies the horizontal alignment of the collapse button.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CollapseButtonOffset">
      <summary>
        <para>Gets or sets the collapse button&#39;s offset.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.PointFloat"/> object that is the collapse button&#39;s offset.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CollapseButtonPosition">
      <summary>
        <para>Gets or sets the position of the collapse button.</para>
      </summary>
      <value><see cref="T:DevExpress.Utils.PointFloat"/> object that specifies the position of the collapse button in relative coordinates. By default, 0.5,1</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CollapseButtonVerticalAlignment">
      <summary>
        <para>Gets or sets the vertical alignment of the collapse button&#39;s border relative to its actual position.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Diagram.Core.Alignment"/> enumeration value that specifies the vertical alignment of the collapse button.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CollapseButtonVisibilityMode">
      <summary>
        <para>Gets or sets a value that indicates when the collapse button is displayed. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.CollapseButtonVisibilityMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.ConnectionPoints">
      <summary>
        <para>Specifies the list of points on the perimeter of a shape at which connectors can terminate.</para>
      </summary>
      <value>A list of points on the perimeter of a shape at which connectors can terminate.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.CustomStyleId">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.DataContext">
      <summary>
        <para>Gets or sets the data item bound to the diagram item.</para>
      </summary>
      <value>A data item.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.ForegroundId">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItem.GetDiagram">
      <summary>
        <para>Gets the diagram control that is the owner of the diagram item.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraDiagram.DiagramControl"/> object that is the owner of the <see cref="T:DevExpress.XtraDiagram.DiagramItem"/>.</returns>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.Height">
      <summary>
        <para>Gets or sets the item height.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value, specifying the item height in pixels.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramItem.HeightChanged">
      <summary>
        <para>Fires each time the value of the <see cref="P:DevExpress.XtraDiagram.DiagramItem.Height"/> property is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.IncomingConnectors">
      <summary>
        <para>Gets the collection of the current item&#39;s incoming connectors.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> objects.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.IsDisposed">
      <summary>
        <para>Gets whether the current diagram item is disposed.</para>
      </summary>
      <value>true, if the current diagram item is disposed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.IsSelected">
      <summary>
        <para>Gets whether the current item is selected.</para>
      </summary>
      <value>true, if the current item is selected; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.IsTextEditMode">
      <summary>
        <para>Gets whether the diagram item&#39;s text is currently being edited.</para>
      </summary>
      <value>true, if diagram item text editing is in progress; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.IsVisible">
      <summary>
        <para>Gets whether the current diagram item is visible.</para>
      </summary>
      <value>true, if the item is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.MinHeight">
      <summary>
        <para>Gets or sets the minimum item height.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value, specifying the minimum item height in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.MinSize">
      <summary>
        <para>Gets or sets the minimum item size.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.SizeF"/> value that is the item&#39;s minimum size.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.MinWidth">
      <summary>
        <para>Gets or sets the minimum item width.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value, specifying the minimum item width in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.MoveWithSubordinates">
      <summary>
        <para>Gets or sets whether moving the current diagram item also moves its subordinates. This is a dependency property.</para>
      </summary>
      <value>true, to move the current item together with its subordinates; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.OutgoingConnectors">
      <summary>
        <para>Gets the collection of the current item&#39;s outgoing connectors.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> objects.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.Owner">
      <summary>
        <para>Gets the owner of the current diagram item.</para>
      </summary>
      <value>An IDiagramItem implementation.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.Padding">
      <summary>
        <para>Gets or sets the amount of space between the item&#39;s borders and its contents.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.Padding"/> value that specifies the amount of space between the item&#39;s borders and its contents.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.ParentItem">
      <summary>
        <para>Returns the current item&#39;s parent item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object that represents the current item&#39;s parent item.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.Position">
      <summary>
        <para>Gets or sets the coordinates of the item&#39;s upper left corner.</para>
      </summary>
      <value>The coordinates of the item&#39;s upper left corner.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramItem.PositionChanged">
      <summary>
        <para>Fires each time the value of the <see cref="P:DevExpress.XtraDiagram.DiagramItem.Position"/> property is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.SelectionLayer">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.Size">
      <summary>
        <para>Gets or sets the size of the item.</para>
      </summary>
      <value>A System.Drawing.SizeF object that is the size of the item.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramItem.SizeChanged">
      <summary>
        <para>Fires each time the value of either <see cref="P:DevExpress.XtraDiagram.DiagramItem.Height"/> or <see cref="P:DevExpress.XtraDiagram.DiagramItem.Width"/> property is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.StrokeId">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.TabStop">
      <summary>
        <para>Gets or sets whether the current item can be navigated to during item navigation using the TAB key.</para>
      </summary>
      <value>true, if the current item can be navigated to during item navigation using the TAB key; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.Tag">
      <summary>
        <para>Gets or sets an arbitrary object value that can be used to store custom information about this element.</para>
      </summary>
      <value>A unique identifier for the diagram item.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.TemplateCondition">
      <summary>
        <para>Gets or sets a condition that is used to choose a template for the diagram item. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.TemplateName">
      <summary>
        <para>Identifies the item as a template used by data binding controllers.</para>
      </summary>
      <value>A string value that is the name of the template.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.ThemeStyleId">
      <summary>
        <para>Gets or sets the identifier of a style applied to the diagram item.</para>
      </summary>
      <value>The style identifier.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.Weight">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.Width">
      <summary>
        <para>Gets or sets the item width.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value, specifying the item width in pixels.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramItem.WidthChanged">
      <summary>
        <para>Fires each time the value of the <see cref="P:DevExpress.XtraDiagram.DiagramItem.Width"/> property is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.X">
      <summary>
        <para>Returns the horizontal coordinate of the item&#39;s upper left corner.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value, specifying the horizontal coordinate of the item&#39;s upper left corner.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramItem.XChanged">
      <summary>
        <para>Fires each time the value of the <see cref="P:DevExpress.XtraDiagram.DiagramItem.X"/> property is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItem.Y">
      <summary>
        <para>Returns the vertical coordinate of the item&#39;s upper left corner.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value, specifying the vertical coordinate of the item&#39;s upper left corner.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramItem.YChanged">
      <summary>
        <para>Fires each time the value of the <see cref="P:DevExpress.XtraDiagram.DiagramItem.Y"/> property is changed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramItemBoundsChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ItemBoundsChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItemBoundsChangedEventArgs.#ctor(DevExpress.XtraDiagram.DiagramItem,DevExpress.XtraDiagram.DiagramItem,System.Int32,DevExpress.Utils.PointFloat,System.Drawing.SizeF,System.Single,DevExpress.XtraDiagram.DiagramItem,System.Int32,DevExpress.Utils.PointFloat,System.Drawing.SizeF,System.Single)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramItemBoundsChangedEventArgs"/> class.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object representing the diagram item.</param>
      <param name="oldParent">A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object representing the item&#39;s previous parent.</param>
      <param name="oldIndex">The item&#39;s previous index within the Items collection of its parent.</param>
      <param name="oldPosition">The previous coordinates of the item&#39;s upper left corner.</param>
      <param name="oldSize">The previous size.</param>
      <param name="oldAngle">The previous rotation angle (in degrees).</param>
      <param name="newParent">A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object representing the item&#39;s new parent.</param>
      <param name="newIndex">The item&#39;s new index within the Items collection of its parent.</param>
      <param name="newPosition">The new coordinates of the item&#39;s upper left corner.</param>
      <param name="newSize">The new size.</param>
      <param name="newAngle">The new rotation angle (in degrees).</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemBoundsChangedEventArgs.Item">
      <summary>
        <para>Returns the item whose bounds are changed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemBoundsChangedEventArgs.NewAngle">
      <summary>
        <para>Returns the new angle at which the item is rotated.</para>
      </summary>
      <value>The new angle (in degrees).</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemBoundsChangedEventArgs.NewIndex">
      <summary>
        <para>Gets the previous item&#39;s index within the Items collection of its parent.</para>
      </summary>
      <value>The item&#39;s index.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemBoundsChangedEventArgs.NewParent">
      <summary>
        <para>Returns the item&#39;s new parent.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemBoundsChangedEventArgs.NewPosition">
      <summary>
        <para>Returns the new coordinates of the item&#39;s upper left corner.</para>
      </summary>
      <value>The coordinates of the item&#39;s upper left corner.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemBoundsChangedEventArgs.NewSize">
      <summary>
        <para>Gets the new size of the item.</para>
      </summary>
      <value>A System.Drawing.SizeF object that is the size of the item.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemBoundsChangedEventArgs.OldAngle">
      <summary>
        <para>Returns the angle at which the item was previously rotated.</para>
      </summary>
      <value>The previous angle (in degrees).</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemBoundsChangedEventArgs.OldIndex">
      <summary>
        <para>Gets the previous item&#39;s index within the Items collection of its parent.</para>
      </summary>
      <value>The item&#39;s index.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemBoundsChangedEventArgs.OldParent">
      <summary>
        <para>Returns the item&#39;s previous parent.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemBoundsChangedEventArgs.OldPosition">
      <summary>
        <para>Returns the previous coordinates of the item&#39;s upper left corner.</para>
      </summary>
      <value>The coordinates of the item&#39;s upper left corner.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemBoundsChangedEventArgs.OldSize">
      <summary>
        <para>Gets the previous size of the item.</para>
      </summary>
      <value>A System.Drawing.SizeF object that is the size of the item.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramItemCollection">
      <summary>
        <para>A collection of <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> objects.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItemCollection.AddRange(DevExpress.XtraDiagram.DiagramItem[])">
      <summary>
        <para>Adds an array of items to the collection.</para>
      </summary>
      <param name="items">A comma-separated list of items or an array of items.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItemCollection.BeginUpdate">
      <summary>
        <para>Locks the <see cref="T:DevExpress.XtraDiagram.DiagramItemCollection"></see> by preventing change notifications from being fired, preventing visual updates until the EndUpdate method is called.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItemCollection.EndUpdate">
      <summary>
        <para>Unlocks the <see cref="T:DevExpress.XtraDiagram.DiagramItemCollection"></see> object after a call to the BeginUpdate method and causes an immediate visual update.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramItemCollection.ListChanged">
      <summary>
        <para>Fires when the collection changes.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramItemContentChangedEventArgs">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItemContentChangedEventArgs.#ctor(DevExpress.XtraDiagram.DiagramItem,System.String,System.String,DevExpress.Diagram.Core.ActionType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramItemContentChangedEventArgs"/> class.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object that is the item which text has been changed.</param>
      <param name="oldValue">A string value that is the previous item&#39;s text.</param>
      <param name="newValue">A string value that is the new item&#39;s text.</param>
      <param name="actionType">A <see cref="T:DevExpress.Diagram.Core.ActionType"/> enumeration value that indicates whether the text has been changed directly or using the Undo/Redo operations.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemContentChangedEventArgs.ActionType">
      <summary>
        <para>Indicates whether the text has been changed directly or using the Undo/Redo operations.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ActionType"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemContentChangedEventArgs.Item">
      <summary>
        <para>Returns the item which text has been changed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemContentChangedEventArgs.NewValue">
      <summary>
        <para>Returns the new item&#39;s text.</para>
      </summary>
      <value>A string value that is the new item&#39;s text.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemContentChangedEventArgs.OldValue">
      <summary>
        <para>Returns the previous item&#39;s text.</para>
      </summary>
      <value>A string value that represents the previous item&#39;s text.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramItemCreatingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ItemCreating"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItemCreatingEventArgs.#ctor(System.Type,DevExpress.Diagram.Core.ItemUsage,DevExpress.XtraDiagram.DiagramItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramItemCreatingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="itemType">A System.Type object that represents the type of the item that is about to be created.</param>
      <param name="itemUsage">A DevExpress.Diagram.Core.ItemUsage enumeration value that specifies whether the item is a part of the diagram or toolbox preview.</param>
      <param name="item">A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object that represents the item that is about to be created.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemCreatingEventArgs.Item">
      <summary>
        <para>Gets or sets the item that is about to be created.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemCreatingEventArgs.ItemType">
      <summary>
        <para>Gets the type of the item that is about to be created.</para>
      </summary>
      <value>A System.Type object that represents the type of the item that is about to be created.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemCreatingEventArgs.ItemUsage">
      <summary>
        <para>Indicates whether the item is a part of the diagram or toolbox preview.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ItemUsage"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramItemDrawingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ItemDrawing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItemDrawingEventArgs.#ctor(DevExpress.XtraDiagram.DiagramItem,DevExpress.Diagram.Core.ItemTool,DevExpress.Diagram.Core.DiagramActionStage,DevExpress.Utils.PointFloat,DevExpress.Utils.PointFloat)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramItemDrawingEventArgs"/> class.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object representing the diagram item that is being drawn.</param>
      <param name="tool">A drawing tool.</param>
      <param name="stage">A <see cref="T:DevExpress.Diagram.Core.DiagramActionStage"/> enumeration value that indicates whether the drawing operation has just started, is continuing or has been finished or canceled.</param>
      <param name="startPosition">The starting point of the drawing operation on the canvas.</param>
      <param name="endPosition">The ending point of the drawing operation on the canvas.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemDrawingEventArgs.EndPosition">
      <summary>
        <para>Specifies the current ending point of the drawing operation on the canvas.</para>
      </summary>
      <value>The ending point of the drawing operation on the canvas.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemDrawingEventArgs.Item">
      <summary>
        <para>Returns the diagram item that is being drawn.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemDrawingEventArgs.Stage">
      <summary>
        <para>Indicates whether the drawing operation has just started, is continuing or has been finished or canceled.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.DiagramActionStage"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemDrawingEventArgs.StartPosition">
      <summary>
        <para>Indicates the point on the canvas where the end-user initiated the drawing operation by clicking and dragging with a drawing tool selected.</para>
      </summary>
      <value>A point on the canvas where the end-user initiated the drawing operation.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemDrawingEventArgs.Tool">
      <summary>
        <para>Returns the drawing tool that the end-user is using.</para>
      </summary>
      <value>An object representing the drawing tool.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramItemInitializingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ItemInitializing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItemInitializingEventArgs.#ctor(DevExpress.XtraDiagram.DiagramItem,DevExpress.Diagram.Core.ItemUsage)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramItemInitializingEventArgs"/> class.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object representing the diagram item that is being initialized.</param>
      <param name="itemUsage">A <see cref="T:DevExpress.Diagram.Core.ItemUsage"/> enumeration value that indicates whether the item is a part of the diagram or toolbox preview.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemInitializingEventArgs.Item">
      <summary>
        <para>Returns the diagram item that is being initialized.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemInitializingEventArgs.ItemUsage">
      <summary>
        <para>Indicates whether the item is a part of the diagram or toolbox preview.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ItemUsage"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramItemsChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ItemsChanged"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItemsChangedEventArgs.#ctor(DevExpress.XtraDiagram.DiagramItem,DevExpress.Diagram.Core.ItemsChangedAction)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramItemsChangedEventArgs"/> class.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object representing the item that is being added or removed.</param>
      <param name="action">An <see cref="T:DevExpress.Diagram.Core.ItemsChangedAction"/> enumeration value that indicates whether a diagram item is being added or removed.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsChangedEventArgs.Action">
      <summary>
        <para>Indicates whether the item is being added or removed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ItemsChangedAction"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsChangedEventArgs.Item">
      <summary>
        <para>Returns the item that is being added or removed from the diagram.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramItemsDeletingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ItemsDeleting"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItemsDeletingEventArgs.#ctor(System.Collections.Generic.IList{DevExpress.XtraDiagram.DiagramItem})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramItemsDeletingEventArgs"/> class.</para>
      </summary>
      <param name="items">A collection of <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> objects that are being deleted.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsDeletingEventArgs.Items">
      <summary>
        <para>Returns the collection of items that are being deleted.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> objects.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramItemsGeneratedEventArgs">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItemsGeneratedEventArgs.#ctor(System.Collections.ObjectModel.ReadOnlyCollection{DevExpress.XtraDiagram.DiagramItem},System.Collections.Generic.IEnumerable{DevExpress.Diagram.Core.IDiagramConnector},DevExpress.Diagram.Core.GenerationKind)">
      <summary>
        <para></para>
      </summary>
      <param name="generatedItems"></param>
      <param name="generatedConnectors"></param>
      <param name="generationKind"></param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsGeneratedEventArgs.GeneratedConnectors">
      <summary>
        <para>Returns the collection of generated connectors that represent connections between data objects.</para>
      </summary>
      <value>A collection of generated connectors.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsGeneratedEventArgs.GeneratedItems">
      <summary>
        <para>Returns the collection of generated items that represent data objects.</para>
      </summary>
      <value>A collection of generated items.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsGeneratedEventArgs.GenerationKind">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramItemsMovingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ItemsMoving"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItemsMovingEventArgs.#ctor(System.Collections.ObjectModel.ReadOnlyCollection{DevExpress.XtraDiagram.MovingItem},DevExpress.Diagram.Core.DiagramActionStage,DevExpress.Diagram.Core.ItemsActionSource,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramItemsMovingEventArgs"/> class.</para>
      </summary>
      <param name="items">A collection of DevExpress.XtraDiagram.MovingItem objects that store information about items that are being moved.</param>
      <param name="stage">A <see cref="T:DevExpress.Diagram.Core.DiagramActionStage"/> enumeration value that indicates whether the moving operation has just started, is continuing or has been finished or canceled.</param>
      <param name="actionSource">A <see cref="T:DevExpress.Diagram.Core.ItemsActionSource"/> enumeration value that indicates whether the moving operation is performed using drag-and-drop, the Properties Panel or by pressing key shortcuts.</param>
      <param name="allow">true, to allow performing the operation; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsMovingEventArgs.ActionSource">
      <summary>
        <para>Indicates whether the moving operation is performed using drag-and-drop, the Properties Panel or by pressing key shortcuts.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ItemsActionSource"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsMovingEventArgs.Allow">
      <summary>
        <para>Specifies whether to allow performing the operation.</para>
      </summary>
      <value>true, to allow performing the operation; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsMovingEventArgs.Items">
      <summary>
        <para>Returns the collection of data about items that are being moved.</para>
      </summary>
      <value>A collection of DevExpress.XtraDiagram.MovingItem objects that store information about items that are being moved.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsMovingEventArgs.Stage">
      <summary>
        <para>Indicates whether the moving operation has just started, is continuing or has been finished or canceled.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.DiagramActionStage"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramItemsPastingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ItemsPasting"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItemsPastingEventArgs.#ctor(System.Collections.ObjectModel.ReadOnlyCollection{DevExpress.XtraDiagram.AddingItem})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramItemsPastingEventArgs"/> class.</para>
      </summary>
      <param name="items">A collection of DevExpress.XtraDiagram.AddingItem objects that store information about items that are being pasted.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsPastingEventArgs.Items">
      <summary>
        <para>Returns the collection of data about items that are being pasted.</para>
      </summary>
      <value>A collection of DevExpress.XtraDiagram.AddingItem objects that store information about items that are being pasted.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramItemsResizingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ItemsResizing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItemsResizingEventArgs.#ctor(System.Collections.ObjectModel.ReadOnlyCollection{DevExpress.XtraDiagram.ResizingItem},DevExpress.Diagram.Core.DiagramActionStage,DevExpress.Diagram.Core.ItemsActionSource,DevExpress.Diagram.Core.ResizeMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramItemsResizingEventArgs"/> class.</para>
      </summary>
      <param name="items">A collection of DevExpress.XtraDiagram.ResizingItem objects that store information about items that are being resized.</param>
      <param name="stage">A <see cref="T:DevExpress.Diagram.Core.DiagramActionStage"/> enumeration value that indicates whether the resizing operation has just started, is continuing or has been finished or canceled.</param>
      <param name="actionSource">A <see cref="T:DevExpress.Diagram.Core.ItemsActionSource"/> enumeration value that indicates whether the resizing operation is performed using the selection handles or the Properties Panel.</param>
      <param name="mode">A DevExpress.Diagram.Core.ResizeMode enumeration value that indicates the direction of resizing.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsResizingEventArgs.ActionSource">
      <summary>
        <para>Indicates whether the resizing operation is performed using the selection handles or the Properties Panel.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ItemsActionSource"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsResizingEventArgs.Items">
      <summary>
        <para>Returns the collection of data about items that are being resized.</para>
      </summary>
      <value>A collection of DevExpress.XtraDiagram.ResizingItem objects that store information about items that are being resized.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsResizingEventArgs.Mode">
      <summary>
        <para>Indicates the direction of resizing.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.ResizeMode enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsResizingEventArgs.Stage">
      <summary>
        <para>Indicates whether the resizing operation has just started, is continuing or has been finished or canceled.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.DiagramActionStage"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramItemsRotatingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ItemsRotating"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramItemsRotatingEventArgs.#ctor(System.Collections.ObjectModel.ReadOnlyCollection{DevExpress.XtraDiagram.RotatingItem},DevExpress.Diagram.Core.DiagramActionStage,DevExpress.Diagram.Core.ItemsActionSource,System.Nullable{DevExpress.Utils.PointFloat})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramItemsRotatingEventArgs"/> class.</para>
      </summary>
      <param name="items">A collection of DevExpress.XtraDiagram.RotatingItem objects that store information about items that are being rotated.</param>
      <param name="stage">A <see cref="T:DevExpress.Diagram.Core.DiagramActionStage"/> enumeration value that indicates whether the rotating operation has just started, is continuing or has been finished or canceled.</param>
      <param name="actionSource">A <see cref="T:DevExpress.Diagram.Core.ItemsActionSource"/> enumeration value that indicates whether the rotating operation is performed using the rotation handle or the Properties Panel.</param>
      <param name="centerOfRotation">A <see cref="T:DevExpress.Utils.PointFloat"/> value that is the coordinates of the center of rotation.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsRotatingEventArgs.ActionSource">
      <summary>
        <para>Indicates whether the rotating operation is performed using the rotation handle or the Properties Panel.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ItemsActionSource"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsRotatingEventArgs.CenterOfRotation">
      <summary>
        <para>Returns the coordinates of the center of rotation.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.PointFloat"/> value that is the coordinates of the center of rotation.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsRotatingEventArgs.Items">
      <summary>
        <para>Returns the collection of data about items that are being rotated.</para>
      </summary>
      <value>A collection of DevExpress.XtraDiagram.RotatingItem objects that store information about items that are being rotated.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramItemsRotatingEventArgs.Stage">
      <summary>
        <para>Indicates whether the rotating operation has just started, is continuing or has been finished or canceled.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.DiagramActionStage"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramOrgChartController">
      <summary>
        <para>Allows you to generate relationship diagrams from a hierarchical data source.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramOrgChartController.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramOrgChartController"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramOrgChartController.#ctor(System.ComponentModel.IContainer)">
      <summary>
        <para>Initializes a new <see cref="T:DevExpress.XtraDiagram.DiagramOrgChartController"/> class instance with the specified container.</para>
      </summary>
      <param name="container">A <see cref="T:System.ComponentModel.IContainer"/> object that provides functionality for containers.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramOrgChartController.ChildrenPath">
      <summary>
        <para>Gets or sets the path to the property that stores a collection of the diagram item&#39;s subordinates. This is a dependnecy property.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the path to a property.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramOrgChartController.ChildrenSelector">
      <summary>
        <para>Gets or sets an object that chooses an item&#39;s subordinates based on custom logic. This is a dependency property.</para>
      </summary>
      <value>An IChildrenSelector implementation that chooses a diagram item&#39;s subordinates based on custom logic.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramOrgChartController.ExpandSubordinatesButtonMode">
      <summary>
        <para>Gets or sets the display mode of the expand/collapse buttons. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ExpandSubordinatesButtonMode"/> enumeration value that is the display mode of the expand/collapse buttons.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramOrgChartController.ExpansionDepth">
      <summary>
        <para>Gets or sets the number of hierarchy levels that expand when the diagram is generated. This is a dependency property.</para>
      </summary>
      <value>An integer value that is the number of expanded hierarchy levels. The default is -1.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramOrgChartController.GenerationDepth">
      <summary>
        <para>Gets or sets the number of hierarchy levels that are retrieved from the data source when the diagram is generated. This is a dependency property.</para>
      </summary>
      <value>An integer value that is the number of hierarchy levels retrieved from the data source. The default is -1.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramOrgChartController.LayoutKind">
      <summary>
        <para>Specifies the automatic layout to be applied to the generated diagram.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.DiagramLayoutKind enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramOrgChartController.ParentMember">
      <summary>
        <para>Specifies the name of the data field that identifies the parent of the data item.</para>
      </summary>
      <value>A System.String value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramOrgChartController.ParentMemberEmptyValue">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramPanAndZoomControl">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramPanAndZoomControl.#ctor">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramPanAndZoomControl.CanUpdateView">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramPanAndZoomControl.Diagram">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramPanAndZoomControl.DiagramPresenter">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramPanAndZoomControl.ZoomTrackBarControl">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramQueryConnectionPointsEventArgs">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramQueryConnectionPointsEventArgs.#ctor(DevExpress.Diagram.Core.IDiagramControl,DevExpress.XtraDiagram.DiagramConnector,DevExpress.Diagram.Core.ConnectorPointType,DevExpress.XtraDiagram.DiagramItem,System.Collections.ObjectModel.ReadOnlyCollection{DevExpress.Diagram.Core.ConnectionPoint},DevExpress.Diagram.Core.ConnectionElementState)">
      <summary>
        <para></para>
      </summary>
      <param name="diagram"></param>
      <param name="connector"></param>
      <param name="connectorPointType"></param>
      <param name="hoveredItem"></param>
      <param name="itemConnectionPointStates"></param>
      <param name="itemConnectionBorderState"></param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryConnectionPointsEventArgs.Connector">
      <summary>
        <para>Returns the connector whose point is being moved by the end-user.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramConnector"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryConnectionPointsEventArgs.ConnectorPointType">
      <summary>
        <para>Returns the type of the connection point that is being moved by the end-user.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ConnectorPointType"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryConnectionPointsEventArgs.HoveredItem">
      <summary>
        <para>Returns the item hovered with the connection point that is being moved by the end-user.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryConnectionPointsEventArgs.ItemConnectionBorderState">
      <summary>
        <para>Specifies whether to enable connection to an item and show a visual indication.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ConnectionElementState"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryConnectionPointsEventArgs.ItemConnectionPointStates">
      <summary>
        <para>Provides access to the collection of objects that represent connection points of a diagram item.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Diagram.Core.ConnectionPoint"/> objects.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryConnectionPointsEventArgs.OppositeItem">
      <summary>
        <para>Returns the diagram item to which the opposite connection point is glued.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryConnectionPointsEventArgs.OppositeItemPointIndex">
      <summary>
        <para>Returns the index of the opposite connection point.</para>
      </summary>
      <value>An integer value that is the index of the opposite connection point.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryConnectionPointsEventArgs.OppositePoint">
      <summary>
        <para>Returns the coordinates of the opposite connection point.</para>
      </summary>
      <value>A System.Windows.Point value that is the coordinates of the opposite connection point.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramQueryItemDrawActionEventArgs">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramQueryItemDrawActionEventArgs.#ctor(DevExpress.Diagram.Core.ItemTool,DevExpress.Utils.PointFloat,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramQueryItemDrawActionEventArgs"/> class.</para>
      </summary>
      <param name="tool">A DevExpress.Diagram.Core.ItemTool object that is the drawing tool that the end-user is attempting to use.</param>
      <param name="diagramPoint">A <see cref="T:DevExpress.Utils.PointFloat"/> value that is the current coordinates of the mouse pointer within the diagram control.</param>
      <param name="allow">true to allow the end-user to use the selected drawing tool; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryItemDrawActionEventArgs.Allow">
      <summary>
        <para>Specifies whether the end-user can use the selected drawing tool.</para>
      </summary>
      <value>true to allow the end-user to use the selected drawing tool; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryItemDrawActionEventArgs.DiagramPoint">
      <summary>
        <para>Returns the mouse pointer position within the diagram control.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.PointFloat"/> value that is the current coordinates of the mouse pointer within the diagram control.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryItemDrawActionEventArgs.Tool">
      <summary>
        <para>Returns the drawing tool that the end-user is attempting to use.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.ItemTool object.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramQueryItemEditActionEventArgs">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramQueryItemEditActionEventArgs.#ctor(DevExpress.XtraDiagram.DiagramItem,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="item"></param>
      <param name="allow"></param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryItemEditActionEventArgs.Allow">
      <summary>
        <para>Specifies whether to allow editing the item&#39;s text.</para>
      </summary>
      <value>true, to allow editing the item&#39;s text; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryItemEditActionEventArgs.Item">
      <summary>
        <para>Returns the item whose text the end-user is attempting to edit.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramQueryItemsActionEventArgs">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramQueryItemsActionEventArgs.#ctor(System.Collections.ObjectModel.ReadOnlyCollection{DevExpress.XtraDiagram.DiagramItem},DevExpress.Diagram.Core.ItemsActionKind,DevExpress.XtraDiagram.DiagramItem,System.Nullable{DevExpress.Utils.PointFloat},System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="items"></param>
      <param name="action"></param>
      <param name="highlightedItem"></param>
      <param name="highlightedItemPoint"></param>
      <param name="allow"></param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryItemsActionEventArgs.Action">
      <summary>
        <para>Indicates the kind of the action that raised the event.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ItemsActionKind"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryItemsActionEventArgs.Allow">
      <summary>
        <para>Specifies whether to allow performing the operation.</para>
      </summary>
      <value>true, to allow performing the operation; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryItemsActionEventArgs.HighlightedItem">
      <summary>
        <para>Returns the currently highlighted diagram item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryItemsActionEventArgs.HighlightedItemPoint">
      <summary>
        <para>Returns the current coordinates of the cursor.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.PointFloat"/> value that is the current coordinates of the cursor.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryItemsActionEventArgs.Items">
      <summary>
        <para>Returns the collection of diagram items affected by the action.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> objects.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramQueryItemSnappingEventArgs">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramQueryItemSnappingEventArgs.#ctor(DevExpress.XtraDiagram.DiagramItem,DevExpress.XtraDiagram.DiagramItem,System.Boolean)">
      <summary>
        <para></para>
      </summary>
      <param name="item"></param>
      <param name="snapTo"></param>
      <param name="allow"></param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryItemSnappingEventArgs.Allow">
      <summary>
        <para>Specifies whether to allow snapping of diagram items.</para>
      </summary>
      <value>true, to allow snapping of diagram items; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryItemSnappingEventArgs.Item">
      <summary>
        <para>Returns the item that is being dragged by the end-user.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramQueryItemSnappingEventArgs.SnapTo">
      <summary>
        <para>Returns the diagram item to which the item being dragged by the end-user can be snapped.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramItem"/> object.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramShape">
      <summary>
        <para>Displays a shape on the diagram canvas.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramShape.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramShape"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramShape.#ctor(DevExpress.Diagram.Core.ShapeDescription)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramShape"/> class with the specified settings.</para>
      </summary>
      <param name="shape">The shape kind. This object is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramShape.Shape"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramShape.#ctor(DevExpress.Diagram.Core.ShapeDescription,System.Drawing.RectangleF)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramShape"/> class with the specified settings.</para>
      </summary>
      <param name="shape">The shape kind. This object is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramShape.Shape"/> property.</param>
      <param name="bounds">A <see cref="T:System.Drawing.RectangleF"/> object that represents the item bounds.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramShape.#ctor(DevExpress.Diagram.Core.ShapeDescription,System.Drawing.RectangleF,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramShape"/> class with the specified settings.</para>
      </summary>
      <param name="shape">The shape kind. This object is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramShape.Shape"/> property.</param>
      <param name="bounds">A <see cref="T:System.Drawing.RectangleF"/> object that represents the shape bounds.</param>
      <param name="content">The text displayed within the shape.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramShape.#ctor(DevExpress.Diagram.Core.ShapeDescription,System.Single,System.Single,System.Single,System.Single)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramShape"/> class with the specified settings.</para>
      </summary>
      <param name="shape">The shape kind. This object is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramShape.Shape"/> property.</param>
      <param name="x">The horizontal coordinate of the shape&#39;s upper left corner.</param>
      <param name="y">The vertical coordinate of the shape&#39;s upper left corner.</param>
      <param name="width">The width of the shape.</param>
      <param name="height">The height of the shape.</param>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramShape.#ctor(DevExpress.Diagram.Core.ShapeDescription,System.Single,System.Single,System.Single,System.Single,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramShape"/> class with the specified settings.</para>
      </summary>
      <param name="shape">The shape kind. This object is used to initialize the <see cref="P:DevExpress.XtraDiagram.DiagramShape.Shape"/> property.</param>
      <param name="x">The horizontal coordinate of the shape&#39;s upper left corner.</param>
      <param name="y">The vertical coordinate of the shape&#39;s upper left corner.</param>
      <param name="width">The width of the shape.</param>
      <param name="height">The height of the shape.</param>
      <param name="content">The text displayed within the shape.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShape.CanChangeParameter">
      <summary>
        <para>Specifies whether end-users can transform the shape.</para>
      </summary>
      <value>true, to allow end-users to transform the shape; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShape.Content">
      <summary>
        <para>Gets or sets the text displayed by the shape.</para>
      </summary>
      <value>The text displayed by the diagram shape.</value>
    </member>
    <member name="E:DevExpress.XtraDiagram.DiagramShape.ContentChanged">
      <summary>
        <para>Occurs when the current diagram shape&#39;s content changes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShape.EditorBounds">
      <summary>
        <para>For internal use. Gets the text editor&#39;s bounds.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.RectangleF"/> structure.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShape.Parameters">
      <summary>
        <para>Specifies shape parameters.</para>
      </summary>
      <value>A System.Windows.Media.DoubleCollection object that represents shape parameters.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShape.Shape">
      <summary>
        <para>Gets or sets the shape kind.</para>
      </summary>
      <value>An object that specifies the shape kind.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShape.ShapeGeometry">
      <summary>
        <para>Gets an object that identifies the geometry of the shape.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.ShapeGeometry object.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramShowingOpenDialogEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ShowingOpenDialog"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramShowingOpenDialogEventArgs.#ctor(System.String,System.String,System.String,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramShowingOpenDialogEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="filter">The file filtering options available in the dialog. By default, Xml files (&#0042;.xml)|&#0042;.xml|All files (&#0042;.&#0042;)|&#0042;.&#0042;</param>
      <param name="title">A string value that is the title of the dialog.</param>
      <param name="initialDirectory">A string value that is the path to the directory displayed by the dialog.</param>
      <param name="documentSourceToOpen">The object from which to automatically load the diagram. It can be a string value specifying the file full path, a Uri object, a stream or an array of bytes.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShowingOpenDialogEventArgs.DocumentSourceToOpen">
      <summary>
        <para>Gets or sets the object from which to load the diagram skipping the dialog window. This object can be a full file path, Uri object, stream or array of bytes.</para>
      </summary>
      <value>The object from which to automatically load the diagram. It can be a string value specifying the file full path, a Uri object, a stream or an array of bytes.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShowingOpenDialogEventArgs.Filter">
      <summary>
        <para>Gets or sets the string that determines the file filtering options that appear in the dialog.</para>
      </summary>
      <value>The file filtering options available in the dialog. By default, Xml files (&#0042;.xml)|&#0042;.xml|All files (&#0042;.&#0042;)|&#0042;.&#0042;</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShowingOpenDialogEventArgs.InitialDirectory">
      <summary>
        <para>Gets or sets the path to the directory displayed by the dialog.</para>
      </summary>
      <value>A string value that is the path to the directory displayed by the dialog.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShowingOpenDialogEventArgs.Title">
      <summary>
        <para>Gets or sets the title of the dialog.</para>
      </summary>
      <value>A string value that is the title of the dialog. By default, Open File</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramShowingOpenImageDialogEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ShowingOpenImageDialog"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramShowingOpenImageDialogEventArgs.#ctor(System.String,System.String,System.String,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramShowingOpenImageDialogEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="filter">The file filtering options available in the dialog. By default, All pictures (&#0042;.jpg;&#0042;.jpeg;&#0042;.bmp;&#0042;.png;&#0042;.gif)|&#0042;.jpg;&#0042;.jpeg;&#0042;.bmp;&#0042;.png;&#0042;.gif|JPEG Fileinterchange Format (&#0042;.jpg;&#0042;.jpeg)|&#0042;.jpg;&#0042;.jpeg|Windows Bitmap (&#0042;.bmp)|&#0042;.bmp|Portable Network Graphics (&#0042;.png)|&#0042;.png|Graphics Interchange Format (&#0042;.gif)|&#0042;.gif</param>
      <param name="title">A string value that is the title of the dialog.</param>
      <param name="initialDirectory">A string value that is the path to the directory displayed by the dialog.</param>
      <param name="imageSourceToOpen">The object from which to automatically load the image. It can be a string value specifying the file full path, a Uri object, a stream or an array of bytes.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShowingOpenImageDialogEventArgs.Filter">
      <summary>
        <para>Gets or sets the string that determines the file filtering options that appear in the dialog.</para>
      </summary>
      <value>The file filtering options available in the dialog. By default, All pictures (.jpg;.jpeg;.bmp;.png;.gif)|.jpg;.jpeg;.bmp;.png;.gif|JPEG Fileinterchange Format (.jpg;.jpeg)|.jpg;.jpeg|Windows Bitmap (.bmp)|.bmp|Portable Network Graphics (.png)|.png|Graphics Interchange Format (.gif)|.gif</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShowingOpenImageDialogEventArgs.ImageSourceToOpen">
      <summary>
        <para>Gets or sets the object from which to load the image skipping the dialog window. This object can be a full file path, Uri object, stream or array of bytes.</para>
      </summary>
      <value>The object from which to load the image. It can be a string value specifying the file full path, a Uri object, a stream or an array of bytes.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShowingOpenImageDialogEventArgs.InitialDirectory">
      <summary>
        <para>Gets or sets the path to the directory displayed by the dialog.</para>
      </summary>
      <value>A string value that is the path to the directory displayed by the dialog.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShowingOpenImageDialogEventArgs.Title">
      <summary>
        <para>Gets or sets the title of the dialog.</para>
      </summary>
      <value>A string value that is the title of the dialog. By default, Insert Picture</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.DiagramShowingSaveDialogEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraDiagram.DiagramControl.ShowingSaveDialog"/> and <see cref="E:DevExpress.Xpf.Diagram.DiagramControl.ShowingSaveDialog"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.DiagramShowingSaveDialogEventArgs.#ctor(System.String,System.String,System.String,System.String,System.Object,System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.DiagramShowingSaveDialogEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="filter">The file filtering options available in the dialog. By default, Xml files (&#0042;.xml)|&#0042;.xml|All files (&#0042;.&#0042;)|&#0042;.&#0042;</param>
      <param name="title">A string value that is the title of the dialog.</param>
      <param name="initialDirectory">A string value that is the path to the directory displayed by the dialog.</param>
      <param name="defaultFileName">A string value that is the default name of the file to which to save the diagram.</param>
      <param name="documentSourceToSave">The object to which to automatically save the diagram. It can be a string value specifying the file full path, a Uri object, a stream or an array of bytes.</param>
      <param name="filterIndex">An integer value that specifies the index of the filter that is selected in the file dialog box by default.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShowingSaveDialogEventArgs.DefaultFileName">
      <summary>
        <para>Gets or sets the default name of the file to which to save the diagram.</para>
      </summary>
      <value>A string value that is the default name of the file to which to save the diagram.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShowingSaveDialogEventArgs.DocumentSourceToSave">
      <summary>
        <para>Gets or sets the object to which to save the diagram skipping the dialog window. This object can be a full file path, Uri object, stream or array of bytes.</para>
      </summary>
      <value>The object to which to save the diagram. It can be a string value specifying the file full path, a Uri object, a stream or an array of bytes.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShowingSaveDialogEventArgs.Filter">
      <summary>
        <para>Gets or sets the string that determines the file filtering options that appear in the dialog.</para>
      </summary>
      <value>The file filtering options available in the dialog. By default, Xml files (&#0042;.xml)|&#0042;.xml|All files (&#0042;.&#0042;)|&#0042;.&#0042;</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShowingSaveDialogEventArgs.FilterIndex">
      <summary>
        <para>Gets or sets which filtering option is selected by default.</para>
      </summary>
      <value>An integer value that determines which filtering option is selected by default.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShowingSaveDialogEventArgs.InitialDirectory">
      <summary>
        <para>Gets or sets the path to the directory displayed by the dialog.</para>
      </summary>
      <value>A string value that is the path to the directory displayed by the dialog.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.DiagramShowingSaveDialogEventArgs.Title">
      <summary>
        <para>Gets or sets the title of the dialog.</para>
      </summary>
      <value>A string value that is the title of the dialog. By default, Save File As</value>
    </member>
    <member name="N:DevExpress.XtraDiagram.Docking">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraDiagram.Docking.DockingMode">
      <summary>
        <para>Lists values that specify which Diagram Designer elements should be added to the diagram.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraDiagram.Docking.DockingMode.All">
      <summary>
        <para>Adds the Shapes Panel, Properties Panel and Pan and Zoom Panel.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraDiagram.Docking.DockingMode.PanZoom">
      <summary>
        <para>Adds the Pan and Zoom Panel.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraDiagram.Docking.DockingMode.ToolboxAndPropertyGrid">
      <summary>
        <para>Adds the Shapes Panel and Properties Panel.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraDiagram.Docking.PanAndZoomDockPanel">
      <summary>
        <para>Represents the dock panel for the Pan and Zoom Panel.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.Docking.PanAndZoomDockPanel.#ctor">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.Docking.PanAndZoomDockPanel.#ctor(DevExpress.XtraBars.Docking.DockingStyle,DevExpress.XtraBars.Docking.DockManager)">
      <summary>
        <para></para>
      </summary>
      <param name="dockStyle"></param>
      <param name="manager"></param>
    </member>
    <member name="P:DevExpress.XtraDiagram.Docking.PanAndZoomDockPanel.CanUpdateView">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Docking.PanAndZoomDockPanel.Diagram">
      <summary>
        <para>Gets or sets the diagram whose data is displayed by the <see cref="T:DevExpress.XtraDiagram.Docking.PanAndZoomDockPanel"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraDiagram.DiagramControl"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Docking.PanAndZoomDockPanel.PanZoom">
      <summary>
        <para>Gets the Pan and Zoom Panel contained in the dock panel.</para>
      </summary>
      <value></value>
    </member>
    <member name="N:DevExpress.XtraDiagram.Options">
      <summary>
        <para>Contains classes providing diagram-specific options.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior">
      <summary>
        <para>Contains options that control the DiagramControl&#39;s behavior.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.#ctor(DevExpress.XtraDiagram.DiagramControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior"/> class.</para>
      </summary>
      <param name="diagram">The owner of the created object.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.ActiveTool">
      <summary>
        <para>Specifies the currently selected diagram tool.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.DiagramTool"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.AllowDiagramProperties">
      <summary>
        <para>Specifies whether to display the &#39;Properties&#39; menu item in the context menu.</para>
      </summary>
      <value>true to display the &#39;Properties&#39; menu item in the context menu; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.AllowEmptySelection">
      <summary>
        <para>Specifies whether to allow selecting the root item.</para>
      </summary>
      <value>true, to allow selecting the root item; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.AllowPropertiesPanel">
      <summary>
        <para>Specifies whether to display the &#39;Properties&#39; menu item in the context menu.</para>
      </summary>
      <value>true to display the &#39;Properties&#39; menu item in the context menu; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies settings of the specified object to the current object.</para>
      </summary>
      <param name="other">The settings to be copied to the current object.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.BringIntoViewMargin">
      <summary>
        <para>Gets or sets the margin between diagram items and viewport bounds when calling the <see cref="M:DevExpress.XtraDiagram.DiagramControl.BringItemsIntoView(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem},DevExpress.Diagram.Core.BringIntoViewMode)"/> and <see cref="M:DevExpress.XtraDiagram.DiagramControl.BringSelectionIntoView(DevExpress.Diagram.Core.BringIntoViewMode)"/> methods.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value that represents the margin in pixels between diagram items and viewport bounds.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.ConnectorsSeparationMode">
      <summary>
        <para>Specifies whether to automatically split the overlapping right-angle connectors.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.DiagramConnectorsSeparationMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.ConnectorTool">
      <summary>
        <para>Specifies the tool that is used when the end-user selects the Connector tool in the Ribbon.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.DiagramTool"/> object that represents the connector tool.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.EllipseTool">
      <summary>
        <para>Specifies the tool that is used when the end-user selects the Ellipse tool in the Ribbon.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.DiagramTool"/> object that represents the ellipse tool.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.EnableProportionalResizing">
      <summary>
        <para>Gets or sets whether to maintain the aspect ratio when resizing diagram items.</para>
      </summary>
      <value>true to maintain the aspect ratio when resizing diagram items; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.FileDialogFilter">
      <summary>
        <para>Gets or sets the filter string that determines which files are displayed in the &#39;Open File&#39; and &#39;Save File As&#39; dialog boxes.</para>
      </summary>
      <value>A filter string that determines which files are displayed in the file dialog box.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.GlueToConnectionPointDistance">
      <summary>
        <para>Specifies the distance between the mouse pointer and a connection point at which the connector is glued to the point.</para>
      </summary>
      <value>A System.Single value that represents the distance in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.GlueToItemDistance">
      <summary>
        <para>Specifies the distance between the mouse pointer and a diagram item at which the connector is glued to the item.</para>
      </summary>
      <value>A System.Single value that represents the distance in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.HexagonTool">
      <summary>
        <para>Specifies the tool that is used when the end-user selects the Hexagon tool in the Ribbon.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.DiagramTool"/> object that represents the hexagon tool.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.MinDragDistance">
      <summary>
        <para>Specifies the number of pixels the cursor must move with the mouse down before dragging begins.</para>
      </summary>
      <value>A System.Double value that is the number of pixels the cursor must move with the mouse down before dragging begins.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.PanTool">
      <summary>
        <para>Gets or sets an object that is the <see cref="T:DevExpress.XtraDiagram.DiagramControl"/>&#39;s pan tool.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.DiagramTool"/> descendant that is the custom pan tool.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.PointerTool">
      <summary>
        <para>Specifies the tool that is used when the end-user selects the Pointer tool in the Ribbon.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.DiagramTool"/> object that represents the pointer tool.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.PointerToolDragMode">
      <summary>
        <para>Gets or sets whether dragging the pointer tool pans across the canvas or draws the selection rectangle. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.PointerToolDragMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.PropertiesPanelVisibility">
      <summary>
        <para>Gets or sets the Properties Panel display mode.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.PropertiesPanelVisibility"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.RectangleTool">
      <summary>
        <para>Specifies the tool that is used when the end-user selects the Rectangle tool in the Ribbon.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.DiagramTool"/> object that represents the rectangle tool.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.ResizingMode">
      <summary>
        <para>Gets or sets how diagram items are resized.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ResizingMode"/> enumeration value that specifies how diagram items are resized.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.RightTriangleTool">
      <summary>
        <para>Specifies the tool that is used when the end-user selects the Right Triangle tool in the Ribbon.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.DiagramTool"/> object that represents the triangle tool.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.ScrollMode">
      <summary>
        <para>Specifies the current scroll mode.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.DiagramScrollMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.SelectedStencils">
      <summary>
        <para>Specifies the selected shape groups.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.StencilCollection object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.SelectionMode">
      <summary>
        <para>Specifies the selection mode for diagram items.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.SelectionMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.SelectPartiallyCoveredItems">
      <summary>
        <para>Specifies whether to select items that are only partially covered with the selection rectangle.</para>
      </summary>
      <value>true, to select items that are only partially covered with the selection rectangle; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.ShowQuickShapes">
      <summary>
        <para>Gets or sets whether to show the Quick Shapes category in the Shapes Panel.</para>
      </summary>
      <value>true to show the Quick Shapes category in the Shapes Panel; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.ShowSelectionRectangle">
      <summary>
        <para>Gets or sets whether to show the selection rectangle.</para>
      </summary>
      <value>true to show the selection rectangle; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.SkinGallery">
      <summary>
        <para>Gets or sets whether the visibility and enabled state of the skin gallery in the Ribbon menu.</para>
      </summary>
      <value>A value that specifies the visibility and enabled state of the skin gallery in the Ribbon menu.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.SnapToGrid">
      <summary>
        <para>Gets or sets whether a diagram item can snap to grid lines during item movement and resizing operations in the Diagram Designer.</para>
      </summary>
      <value>true, if a diagram item can snap to grid lines; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.SnapToItems">
      <summary>
        <para>Gets or sets whether a diagram item can snap to alignment guides (snap lines) of other items during item movement and resizing operations in the Diagram Designer.</para>
      </summary>
      <value>true, if the snapping feature is enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.SnapToItemsDistance">
      <summary>
        <para>Gets or sets the minimum distance between alignment guides of a shape being dragged and another shape&#39;s alignment guides at which the &quot;snap-to-items&quot; feature is activated. If the dragged shape is dropped at this position, it will be aligned with the second shape.</para>
      </summary>
      <value>The snap distance in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.Stencils">
      <summary>
        <para>Provides access to the collection of available stencils.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.DiagramStencilCollection object that is the collection of available stencils.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.ToolboxVisibility">
      <summary>
        <para>Gets or sets the Shapes toolbox display mode.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ToolboxVisibility"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsBehavior.UseTabNavigation">
      <summary>
        <para>Gets or sets whether navigation through diagram items using the TAB key is enabled.</para>
      </summary>
      <value>Default or True, if TAB navigation through items is enabled; otherwise, False.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.Options.DiagramOptionsCircularLayout">
      <summary>
        <para>Contains circular layout settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.Options.DiagramOptionsCircularLayout.#ctor(DevExpress.XtraDiagram.DiagramControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsCircularLayout"/> class.</para>
      </summary>
      <param name="diagram">The owner of the options object created.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsCircularLayout.NodesSpacing">
      <summary>
        <para>Specifies the spacing between the diagram items.</para>
      </summary>
      <value>A System.Single value that represents the spacing between the diagram items in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsCircularLayout.Order">
      <summary>
        <para>Specifies how the circular layout algorithm arranges shapes.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Layout.CircularLayoutOrder"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsCircularLayout.StartAngle">
      <summary>
        <para>Specifies the start angle in degrees in the counterclockwise direction.</para>
      </summary>
      <value>A System.Single value that is the start angle in degrees in the counterclockwise direction.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.Options.DiagramOptionsConnector">
      <summary>
        <para>Contains options that control the diagram connectors.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.Options.DiagramOptionsConnector.#ctor(DevExpress.XtraDiagram.DiagramControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsConnector"/> class.</para>
      </summary>
      <param name="diagram">The owner of the options object created.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsConnector.LineJumpPlacement">
      <summary>
        <para>Specifies which connector lines display jumps in intersections.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.LineJumpPlacement"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsConnector.LineJumpSize">
      <summary>
        <para>Gets or sets the size of line jumps.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.SizeF"/> value that is the size of line jumps.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsConnector.LineJumpStyle">
      <summary>
        <para>Gets or sets the line jump style for connectors.</para>
      </summary>
      <value>A DevExpress.Diagram.Core.LineJumpDescription object.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.Options.DiagramOptionsExport">
      <summary>
        <para></para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.Options.DiagramOptionsExport.#ctor(DevExpress.XtraDiagram.DiagramControl)">
      <summary>
        <para></para>
      </summary>
      <param name="diagram"></param>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsExport.ExportDPI">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsExport.ExportMargin">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsExport.ExportScale">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsExport.PdfExportOptions">
      <summary>
        <para>Gets or sets the options that define how a document is exported to PDF format.</para>
      </summary>
      <value>A DevExpress.XtraPrinting.PdfExportOptions object that stores the settings.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsExport.PrintExportMode">
      <summary>
        <para>Specifies whether to preserve the diagram content placement relative to the document boundaries when printing or exporting the diagram.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.PrintExportMode"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.Options.DiagramOptionsMindMapTreeLayout">
      <summary>
        <para>Contains mind map tree layout settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.Options.DiagramOptionsMindMapTreeLayout.#ctor(DevExpress.XtraDiagram.DiagramControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsMindMapTreeLayout"/> class.</para>
      </summary>
      <param name="diagram">The owner of the options object created.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsMindMapTreeLayout.ConnectorsRouting">
      <summary>
        <para>Gets or sets whether applying the Mind-Map Tree layout affects connector routing.</para>
      </summary>
      <value>A TreeConnectorsRouting enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsMindMapTreeLayout.HierarchyAlignment">
      <summary>
        <para>Specifies the mind-map tree layout vertical alignment relative to the layout direction axis.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Alignment"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsMindMapTreeLayout.HierarchySpacing">
      <summary>
        <para>Specifies the distance between layers of hierarchy.</para>
      </summary>
      <value>A System.Single value that represents the distance in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsMindMapTreeLayout.NodeAlignment">
      <summary>
        <para>Specifies the mind-map tree layout horizontal alignment relative to the layout direction axis.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Alignment"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsMindMapTreeLayout.NodeSpacing">
      <summary>
        <para>Specifies the distance between shapes on the same level of hierarchy.</para>
      </summary>
      <value>A System.Single value that represents the distance in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsMindMapTreeLayout.Orientation">
      <summary>
        <para>Specifies the mind-map tree orientation.</para>
      </summary>
      <value>A System.Windows.Forms.Orientation enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsMindMapTreeLayout.RootSpacing">
      <summary>
        <para>Specifies the distance between the root shape and its immediate children.</para>
      </summary>
      <value>A System.Single value that is the distance in pixels between the root shape and its immediate children.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsMindMapTreeLayout.SplitMode">
      <summary>
        <para>Specifies which diagram items are realigned when performing automatic relayout.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsMindMapTreeLayout.SubTreeAlignment">
      <summary>
        <para>Gets or sets the alignment of the mind-map&#39;s subtrees.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Diagram.Core.Alignment"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsMindMapTreeLayout.SubTreeDefaultSplitMode">
      <summary>
        <para>Specifies the criteria for arranging the mind map branches.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Layout.SubTreeDefaultSplitMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsMindMapTreeLayout.SubTreeSelector">
      <summary>
        <para>Gets or sets the IMindMapSubTreeSelector object that allows you to customize the positioning of subtrees.</para>
      </summary>
      <value>An object implementing the DevExpress.Diagram.Core.Layout.IMindMapSubTreeSelector interface.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.Options.DiagramOptionsProtection">
      <summary>
        <para>Contains settings that allow you to restrict specific end-user actions.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.#ctor(DevExpress.XtraDiagram.DiagramControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsProtection"/> class.</para>
      </summary>
      <param name="diagram">The owner of the options object created.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowAddRemoveItems">
      <summary>
        <para>Gets or sets whether to allow the end-user to add and remove diagram items from the canvas.</para>
      </summary>
      <value>true to allow adding and removing items from the canvas; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowApplyAutomaticLayout">
      <summary>
        <para>Gets or sets whether to allow the end-user to apply the Automatic Layout.</para>
      </summary>
      <value>true to allow the end-user to apply automatic layout; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowApplyAutomaticLayoutForSubordinates">
      <summary>
        <para>Gets or sets whether to allow the end-user to apply the Automatic Layout.</para>
      </summary>
      <value>true, to allow the end-user to subordinates of an item; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowAttachDetachConnectors">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the connector&#39;s starting and ending items.</para>
      </summary>
      <value>true, to allow the end-user to change the connector&#39;s starting and ending items; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeCanvasSizeMode">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the canvas size mode in the Ribbon.</para>
      </summary>
      <value>true to allow the end-user to change the canvas size mode; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeConnectorsArrowSettings">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeConnectorsRoute">
      <summary>
        <para>Gets or sets whether to allow the end-user to manually reroute connectors.</para>
      </summary>
      <value>true, to allow the end-user to manually reroute connectors; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeConnectorsType">
      <summary>
        <para>Gets or sets whether to allow end-users to change the connector type in the Ribbon.</para>
      </summary>
      <value>true to allow end-users to change the connector type; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeContainerHeaderPadding">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the header&#39;s padding within the container&#39;s boundaries in the Ribbon.</para>
      </summary>
      <value>true to allow the end-user to change the container header&#39;s padding in the ribbon; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeContainerHeaderVisibility">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the container header&#39;s visibility.</para>
      </summary>
      <value>true to allow the end-user to change the container header&#39;s visibility; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeContainerPadding">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the container&#39;s padding in the Ribbon.</para>
      </summary>
      <value>true to allow the end-user to change the container&#39;s padding in the ribbon; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeContainerStyle">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the container style in the Ribbon.</para>
      </summary>
      <value>true to allow the end-user to change the container style in the ribbon; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeGridVisibility">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the visibility of grid lines on the Canvas in the Ribbon.</para>
      </summary>
      <value>true to allow the end-user to change the visibility of grid lines in the ribbon; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeImage">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the source image of an image item.</para>
      </summary>
      <value>true to allow the end-user to change the source image of an image item; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeImageStretchMode">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the stretch mode for an image item in the Ribbon.</para>
      </summary>
      <value>true, to allow the end-user to change the stretch mode for an image item; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeItemBackground">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the diagram item&#39;s background color in the Ribbon.</para>
      </summary>
      <value>true to allow the end-user to change the item&#39;s background color; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeItemStroke">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the diagram item&#39;s stroke color in the Ribbon.</para>
      </summary>
      <value>true to allow the end-user to change the item&#39;s stroke color; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeItemStyle">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the diagram item&#39;s style in the Ribbon.</para>
      </summary>
      <value>true to allow the end-user to change the item&#39;s style; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeItemZOrder">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the item&#39;s Z-order in the Ribbon.</para>
      </summary>
      <value>true to allow the end-user to change the item&#39;s Z-order; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeLineJumpSettings">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the line jump settings in the Page Setup Dialog Window.</para>
      </summary>
      <value>true, to allow the end-user to change the line jump settings; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangePageBreaksVisibility">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the visibility of page breaks in the Ribbon.</para>
      </summary>
      <value>true to allow the end-user to change the visibility of page breaks; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangePageParameters">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the page parameters in the Page Setup Dialog Window.</para>
      </summary>
      <value>true to allow the end-user to change the page parameters; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeRulerVisibility">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the visibility of grid rulers in the Ribbon.</para>
      </summary>
      <value>true to allow the end-user to change the visibility of grid rulers; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeShapesParameter">
      <summary>
        <para>Gets or sets whether to allow the end-user to transform shapes by dragging the transformation handles.</para>
      </summary>
      <value>true, to allow the end-user to transform shapes by dragging the transformation handles; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeTextFont">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the text font settings in the Ribbon.</para>
      </summary>
      <value>true to change the text font settings; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeTextForeground">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the text foreground in the Ribbon.</para>
      </summary>
      <value>true to allow the end-user to change the text foreground; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeTextHorizontalAlignment">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the text horizontal alignment in the Ribbon.</para>
      </summary>
      <value>true to allow the end-user to change the text horizontal alignment; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeTextVerticalAlignment">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the text vertical alignment in the Ribbon.</para>
      </summary>
      <value>true to allow the end-user to change the text vertical alignment; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowChangeTheme">
      <summary>
        <para>Gets or sets whether to allow end-users to select the diagram theme in the Ribbon.</para>
      </summary>
      <value>true to allow end-users to select the diagram theme in the Ribbon; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowCollapseContainers">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowCopyItems">
      <summary>
        <para>Gets or sets whether to allow end-users to copy diagram items.</para>
      </summary>
      <value>true to allow end-users to copy diagram items; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowEditItems">
      <summary>
        <para>Gets or sets whether to allow the end-user to enter the text editing mode.</para>
      </summary>
      <value>true to allow the end-user to enter the text editing mode; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowFlipImage">
      <summary>
        <para>Gets or sets whether to allow the end-user to apply the flip effect to an image item in the Ribbon.</para>
      </summary>
      <value>true to allow the end-user to apply the flip effect to an image item in the ribbon; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowMoveItems">
      <summary>
        <para>Gets or sets whether to allow the end-user to move diagram items by dragging with the mouse or pressing the arrow keys.</para>
      </summary>
      <value>true to allow the end-user to move diagram items by dragging with the mouse or pressing the arrow keys; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowResizeItems">
      <summary>
        <para>Gets or sets whether to allow the end-user to resize items by dragging selection handles.</para>
      </summary>
      <value>true to allow the end-user to resize items by dragging selection handles; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowRotateItems">
      <summary>
        <para>Gets or sets whether to allow the end-user to rotate items by dragging the rotation handle.</para>
      </summary>
      <value>true to allow the end-user to rotate items by dragging the rotation handle; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowUndoRedo">
      <summary>
        <para>Gets or sets whether to allow the end-user to perform the undo/redo operations.</para>
      </summary>
      <value>true to allow the end-user to perform the undo/redo operations; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.AllowZoom">
      <summary>
        <para>Gets or sets whether to allow the end-user to change the diagram zoom factor.</para>
      </summary>
      <value>true to allow the end-user to change the diagram zoom factor; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies all available property settings from the target <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsProtection"/> to this <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsProtection"/>.</para>
      </summary>
      <param name="other">A  object whose settings are to be copied.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsProtection.IsReadOnly">
      <summary>
        <para>Specifies whether to set all <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsProtection"/> properties to false by default.</para>
      </summary>
      <value>true, to disable all protection options by default; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.Options.DiagramOptionsSugiyamaLayout">
      <summary>
        <para>Contains Sugiyama (layered) layout settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.Options.DiagramOptionsSugiyamaLayout.#ctor(DevExpress.XtraDiagram.DiagramControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsSugiyamaLayout"/> class.</para>
      </summary>
      <param name="diagram">The owner of the options object created.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsSugiyamaLayout.ColumnsAlignment">
      <summary>
        <para>Gets or sets the alignment of the diagram depending on its direction.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Alignment"/> enumeration value that specifies the diagram alignment.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsSugiyamaLayout.ColumnSpacing">
      <summary>
        <para>Specifies the distance between shapes on the same level of hierarchy.</para>
      </summary>
      <value>A System.Single value that is the distance between shapes on the same level of hierarchy in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsSugiyamaLayout.ConnectorsRouting">
      <summary>
        <para>Gets or sets whether applying the Sugiyama layout affects connector routing.</para>
      </summary>
      <value>A SugiyamaConnectorsRouting enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsSugiyamaLayout.Direction">
      <summary>
        <para>Specifies the flow direction for the automatic layout.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Layout.LayoutDirection"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsSugiyamaLayout.LayersAlignment">
      <summary>
        <para>Gets or sets the alignment of the diagram depending on its direction.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Alignment"/> enumeration value that specifies the diagram alignment.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsSugiyamaLayout.LayerSpacing">
      <summary>
        <para>Specifies the distance between layers of hierarchy.</para>
      </summary>
      <value>A System.Single value that represents the distance in pixels.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.Options.DiagramOptionsTipOverTreeLayout">
      <summary>
        <para>Contains tip-over tree layout settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.Options.DiagramOptionsTipOverTreeLayout.#ctor(DevExpress.XtraDiagram.DiagramControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsTipOverTreeLayout"/> class.</para>
      </summary>
      <param name="diagram">The owner of the options object created.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTipOverTreeLayout.ConnectorsRouting">
      <summary>
        <para>Gets or sets whether applying the Tip-over tree layout affects connector routing.</para>
      </summary>
      <value>A TreeConnectorsRouting enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTipOverTreeLayout.Direction">
      <summary>
        <para>Specifies the tip-over tree layout direction.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Layout.TipOverDirection"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTipOverTreeLayout.HorizontalAlignment">
      <summary>
        <para>Gets or sets the horizontal alignment of the tip-over diagram.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Alignment"/> enumeration value that specifies the horizontal alignment of the diagram.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTipOverTreeLayout.Offset">
      <summary>
        <para>Gets or sets the offset used by the layout algorithm.</para>
      </summary>
      <value>A System.Single value that is the offset in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTipOverTreeLayout.OffsetMode">
      <summary>
        <para>Specifies whether to offset child shapes from the center or the edge of the parent shape.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Layout.TipOverOffsetMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTipOverTreeLayout.SplitMode">
      <summary>
        <para>Specifies whether to apply the layout algorithm only to items that are connected to at least one other item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTipOverTreeLayout.VerticalAlignment">
      <summary>
        <para>Gets or sets the vertical alignment of the tip-over diagram.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Alignment"/> enumeration value that specifies the vertical alignment of the diagram.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTipOverTreeLayout.VerticalSpacing">
      <summary>
        <para>Specifies the distance between layers of hierarchy.</para>
      </summary>
      <value>A System.Single value that represents the distance in pixels.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.Options.DiagramOptionsTreeLayout">
      <summary>
        <para>Contains tree layout settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.Options.DiagramOptionsTreeLayout.#ctor(DevExpress.XtraDiagram.DiagramControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsTreeLayout"/> class.</para>
      </summary>
      <param name="diagram">The owner of the options object created.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTreeLayout.Alignment">
      <summary>
        <para>Specifies the tree layout alignment.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Alignment"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTreeLayout.BreadthAlignment">
      <summary>
        <para>Gets or sets the tree diagram horizontal alignment relative to the layout direction axis.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Alignment"/> enumeration value that specifies the horizontal alignment of the tree diagram.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTreeLayout.ConnectorsRouting">
      <summary>
        <para>Gets or sets whether applying the Tree layout affects connector routing.</para>
      </summary>
      <value>A TreeConnectorsRouting enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTreeLayout.DepthAlignment">
      <summary>
        <para>Gets or sets the tree diagram vertical alignment relative to the layout direction axis.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Alignment"/> enumeration value that specifies the vertical alignment of the tree diagram.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTreeLayout.Direction">
      <summary>
        <para>Specifies the tree layout direction.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Layout.LayoutDirection"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTreeLayout.HorizontalSpacing">
      <summary>
        <para>Specifies the distance between shapes on the same level of hierarchy.</para>
      </summary>
      <value>A System.Single value that represents the distance in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTreeLayout.IsCompact">
      <summary>
        <para>Specifies whether the automatic tree layout arranges shapes minimizing the space between them.</para>
      </summary>
      <value>true to enable the compact tree layout; otherwise, false. By default, true.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTreeLayout.SplitMode">
      <summary>
        <para>Specifies which diagram items are realigned when performing automatic relayout.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.Layout.SplitToConnectedComponentsMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsTreeLayout.VerticalSpacing">
      <summary>
        <para>Specifies the distance between layers of hierarchy.</para>
      </summary>
      <value>A System.Single value that represents the distance in pixels.</value>
    </member>
    <member name="T:DevExpress.XtraDiagram.Options.DiagramOptionsView">
      <summary>
        <para>Contains the DiagramControl&#39;s view settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDiagram.Options.DiagramOptionsView.#ctor(DevExpress.XtraDiagram.DiagramControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDiagram.Options.DiagramOptionsView"/> class.</para>
      </summary>
      <param name="diagram">The owner of the options object created.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.AllowShapeShadows">
      <summary>
        <para>Gets or sets whether to show shadows for diagram items.</para>
      </summary>
      <value>true to show shadows for appointment; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraDiagram.Options.DiagramOptionsView.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies settings of the specified object to the current object.</para>
      </summary>
      <param name="other">The settings to be copied to the current object.</param>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.BringIntoViewMargin">
      <summary>
        <para>Gets or sets the margin between diagram items and viewport bounds when calling the <see cref="M:DevExpress.XtraDiagram.DiagramControl.BringItemsIntoView(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem},DevExpress.Diagram.Core.BringIntoViewMode)"/> and <see cref="M:DevExpress.XtraDiagram.DiagramControl.BringSelectionIntoView(DevExpress.Diagram.Core.BringIntoViewMode)"/> methods.</para>
      </summary>
      <value>A <see cref="T:System.Single"/> value that represents the margin in pixels between diagram items and viewport bounds.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.CanvasSizeMode">
      <summary>
        <para>Gets or sets whether the canvas size is automatically expanded/shrunk to fit the current shapes layout.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.CanvasSizeMode"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.FitToDrawingMargin">
      <summary>
        <para>Gets or sets the margin between diagram contents and page bounds when calling the <see cref="M:DevExpress.XtraDiagram.DiagramControl.FitToDrawing"/> and <see cref="M:DevExpress.XtraDiagram.DiagramControl.FitToItems(System.Collections.Generic.IEnumerable{DevExpress.XtraDiagram.DiagramItem})"/> methods.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.Padding"/> value that represents the margin between diagram contents and page bounds.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.GridSize">
      <summary>
        <para>Gets or sets the size of a grid&#39;s cell.</para>
      </summary>
      <value>The size of a grid&#39;s cell.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.Landscape">
      <summary>
        <para>Specifies whether to use the longer dimension of the page size as the page width.</para>
      </summary>
      <value>true, to use the longer edge of the document size as the page width (landscape orientation); otherwise, false (portrait orientation).</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.MaxZoomFactor">
      <summary>
        <para>Specifies the maximum diagram zoom factor.</para>
      </summary>
      <value>A System.Single value that represents the maximum diagram zoom factor.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.MeasureUnit">
      <summary>
        <para>Specifies the unit of measurement for the ruler displayed by the Canvas.</para>
      </summary>
      <value>A unit of measure.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.MinZoomFactor">
      <summary>
        <para>Specifies the minimum diagram zoom factor.</para>
      </summary>
      <value>A System.Single value that represents the minimum diagram zoom factor.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.PageMargin">
      <summary>
        <para>Gets or sets the amount of space between the page&#39;s borders and its contents.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.Padding"/> value that specifies the amount of space between the page&#39;s borders and its contents.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.PageSize">
      <summary>
        <para>Gets or sets the initial diagram canvas size. This property also specifies to what extent the canvas is expanded/shrunk in auto-size mode (see <see cref="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.CanvasSizeMode"/>).</para>
      </summary>
      <value>The page size.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.PaperKind">
      <summary>
        <para>Allows you to use one of the standard paper sizes as the page size.</para>
      </summary>
      <value>A System.Drawing.Printing.PaperKind enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.PropertiesPanelVisibility">
      <summary>
        <para>Gets or sets the Properties Panel display mode.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.PropertiesPanelVisibility"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.ScrollMargin">
      <summary>
        <para>Gets or sets the minimum diagram margin.</para>
      </summary>
      <value>The minimum diagram margin.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.ShowGrid">
      <summary>
        <para>Gets or sets whether to draw the grid, which helps you position shapes on the diagram canvas.</para>
      </summary>
      <value>true, to draw the grid; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.ShowPageBreaks">
      <summary>
        <para>Gets or sets whether to display page breaks.</para>
      </summary>
      <value>true, to display page breaks; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.ShowPanAndZoomPanel">
      <summary>
        <para>Specifies whether to show the Pan and Zoom Panel.</para>
      </summary>
      <value>true, to show the Pan and Zoom panel; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.ShowRulers">
      <summary>
        <para>Gets or sets whether the diagram rulers are visible.</para>
      </summary>
      <value>true, if the diagram rulers are visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.Theme">
      <summary>
        <para>Specifies the current theme.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.DiagramTheme"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.ToolboxVisibility">
      <summary>
        <para>Gets or sets the Shapes Panel display mode.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Diagram.Core.ToolboxVisibility"/> enumeration value.</value>
    </member>
    <member name="P:DevExpress.XtraDiagram.Options.DiagramOptionsView.ZoomFactor">
      <summary>
        <para>Specifies the diagram zoom factor.</para>
      </summary>
      <value>A System.Single value that represents the diagram zoom factor.</value>
    </member>
  </members>
</doc>