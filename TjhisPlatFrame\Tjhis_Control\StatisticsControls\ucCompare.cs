﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Tjhis.Controls.Properties;

namespace Tjhis.Controls.StatisticsControls
{
    public partial class ucCompare : RadiusUserControl
    {
        private const string CompareUnit = "%";

        private Color BackRed = Color.FromArgb(254, 238, 239);
        private Color BackGreen = Color.FromArgb(237, 250, 243);
        private Color ForeColorRed = Color.FromArgb(253, 94, 97);
        private Color ForeColorGreen = Color.FromArgb(78, 208, 135);
        /// <summary>
        /// 同比的值，为大于-1小于1的小数
        /// </summary>
        public decimal CompareValue
        {
            set
            {
                if(value == 0)
                {
                    IamgeCompare.Visible = false;
                }
                else
                {
                    IamgeCompare.Visible = true;
                    bool sign = value < 0 ? true : false;
                    if (sign)
                    {
                        this.RadiusColor = BackGreen;
                        IamgeCompare.Image = Resources.comparegreen;
                        IamgeCompare.BackColor = BackGreen;
                        this.LCompare.BackColor = BackGreen;
                        this.LCompare.ForeColor = ForeColorGreen;
                    }
                    else
                    {
                        this.RadiusColor = BackRed;
                        IamgeCompare.Image = Resources.comparered;
                        IamgeCompare.BackColor = BackRed;
                        this.LCompare.BackColor = BackRed;
                        this.LCompare.ForeColor = ForeColorRed;
                    }
                    
                }
                this.LCompare.Text = string.Concat(Math.Abs(ConvertToPercentage(value)), CompareUnit);

            }
        }
        /// <summary>
        /// 转换为百分比
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private decimal ConvertToPercentage(decimal value) => value * 100;

        public ucCompare()
        {
            InitializeComponent();
        }

        public override void Clear()
        {
            if(null != IamgeCompare.Image){
                IamgeCompare.Image.Dispose();
                IamgeCompare.Image = null;
            }
        }

    }
}
