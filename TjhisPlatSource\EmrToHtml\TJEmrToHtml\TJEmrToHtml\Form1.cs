﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using TJ.EMR.Utility;
using TJ.EMR.Business;
namespace TJEmrToHtml
{
    public partial class Form1 : Form
    {
        private string Tem_FilePath = Application.StartupPath + "\\FileTemp\\TempFile";
        private DataSet workPathDs;
        string[] args;
        string strArgs = string.Empty;
        string openPath = string.Empty;
        string patientId = string.Empty;
        string fileName = string.Empty;
        string fileSaveName = string.Empty;
        string isDown = string.Empty;
        string tem_FilePath = string.Empty;
        string mr_path = string.Empty;
        int result = -1;

        public Form1()
        {
            InitializeComponent();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            try
            {
                AddMsg("a");
                string selectsql = "select * from mr_work_path";
                this.workPathDs = DBSettingClass.GetDataSet(selectsql, "MR_WORK_PATH");
                AddMsgs("b");
                this.timer1.Enabled = true;
            }
            catch (Exception ex)
            {
                AddMsgs(ex.ToString());
            }
        }

        private void AddMsg(string msg)
        {
            this.label1.Text = msg;
            Application.DoEvents();
        }

        private void AddMsgs(string msg)
        {
            this.label2.Text = msg;
            Application.DoEvents();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            try
            {
                this.timer1.Enabled = false;
                if (File.Exists(Application.StartupPath + @"\args.txt"))
                {
                    StreamReader streamReader = new StreamReader(Application.StartupPath + "\\args.txt", Encoding.UTF8);
                    strArgs = streamReader.ReadToEnd();
                    streamReader.Close();
                    streamReader.Dispose();
                    args = strArgs.Split('&');
                    if (args.Length > 1)
                    {
                        openPath = args[0];
                        patientId = openPath.Split(';')[0];
                        fileName = openPath.Split(';')[1];
                        fileSaveName = args[1];
                        isDown = !string.IsNullOrEmpty(ConfigHelper.GetConfigString("IsDownFile")) ? ConfigHelper.GetConfigString("IsDownFile") : "0";
                        tem_FilePath = isDown == "1" ? this.Tem_FilePath : string.Empty;
                        mr_path = workPathDs.Tables[0].Rows.Count > 0 ? workPathDs.Tables[0].Rows[0]["MR_PATH"].ToString() : string.Empty;
                        result = -1;
                        if (isDown == "1")
                            result = TJ_EMR_Business.MRFile.GetMRFileByFileName(patientId, fileName, tem_FilePath, this.workPathDs);
                        else
                            tem_FilePath = mr_path + @"\" + GetPathbyPatientID(patientId) + @"\" + fileName;
                        this.AddMsg(patientId + @"\" + fileName + "开始下载...");
                        if (isDown == "0" && File.Exists(tem_FilePath))
                            this.TjemrPad1.FileOpen(tem_FilePath);
                        else if (result == 0)
                            this.TjemrPad1.FileOpen(tem_FilePath);
                        else
                        {
                            StreamWrite("Log.txt",patientId + fileName + "|病历文件下载失败：" + result.ToString());
                        }
                        this.TjemrPad1.ex_SetModeClear();
                        this.AddMsg(fileSaveName + "开始转化HTML文件...");
                        if (File.Exists(fileSaveName))
                            File.Delete(fileSaveName);
                        if (this.TjemrPad1.FileSaveAsHtml(fileSaveName))
                        {
                            this.AddMsgs(fileSaveName + "转化HTML文件成功...");
                            File.Delete(Application.StartupPath + "\\args.txt");
                        }
                    }
                    else
                        StreamWrite("Log.txt", strArgs);
                }
            }
            catch (Exception ex)
            {
                StreamWrite("Log.txt", ex.ToString());
            }
            finally
            {
                timer1.Enabled = true;
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="strValue"></param>
        private void StreamWrite(string fileName,string strValue)
        {
            if (!File.Exists(Application.StartupPath + "\\" + fileName))
            {
                FileStream fileStream = File.Create(Application.StartupPath + "\\" + fileName);
                fileStream.Close();
            }
            StreamWriter streamWriter = new StreamWriter(Application.StartupPath + "\\" + fileName, false, Encoding.UTF8);
            streamWriter.Write(strValue+" \r\n");
            streamWriter.Flush();
            streamWriter.Close();
            streamWriter.Dispose();
        }
        /// <summary>
        /// 截取patientid前8位生成病历文件路径
        /// </summary>
        private string GetPathbyPatientID(string PatientID)
        {
            char padLeftChar = '0';
            if (PatientID.Length < 2)
                PatientID = PatientID.PadLeft(2, padLeftChar);
            return PatientID.Substring(PatientID.Length - 2, 2) + @"\" + PatientID.Remove(PatientID.Length - 2).PadLeft(8, padLeftChar) + @"\";
        }
    }
}
