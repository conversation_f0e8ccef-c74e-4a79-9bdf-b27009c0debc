﻿//-----------------------------------------------------------------------
//  系统名称        : 基础类库
//  子系统名称      : 常量
//  功能概要        : 字符串常量
//  作  者          : 付军
//  创建时间        : 2016-10-28
//  版本            : 1.0.0.0
//-----------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Tjhis.Report.Custom.Srv
{
    /// <summary>
    /// 字符串常量定义
    /// </summary>
    public class ConstStr
    {
        /// <summary>
        /// 大括号: "{"
        /// </summary>
        public static readonly string BRACE_LEFT        = "{";
        
        
        /// <summary>
        /// 大括号: "}"
        /// </summary>
        public static readonly string BRACE_RIGHT       = "}";
        
        
        /// <summary>
        /// 反斜线: "\\"
        /// </summary>
        public static readonly string BACKSLASH         = "\\";
        
        
        /// <summary>
        /// 斜线: "/"
        /// </summary>
        public static readonly string SLASH             = "/";
        
        
        /// <summary>
        /// 点: "."
        /// </summary>
        public static readonly string POINT             = ".";
        
        
        /// <summary>
        /// 省略号: "..."
        /// </summary>
        public static readonly string SUSPENSION_POINTS = "...";
        
        
        /// <summary>
        /// 中划线: "-"
        /// </summary>
        public static readonly string LINEATION         = "-";
        
        
        /// <summary>
        /// 等号: "="
        /// </summary>
        public static readonly string EQUAL             = "=";
        
        
        /// <summary>
        /// 回车换行符: "\r\n"
        /// </summary>
        public static readonly string CRLF              = "\r\n";
        
        
        /// <summary>
        /// 跳格键: "\t"
        /// </summary>
        public static readonly string TAB               = "\t";
        
        
        /// <summary>
        /// 空白符: " "
        /// </summary>
        public static readonly string BLANK             = " ";
        
        
        /// <summary>
        /// 冒号: ":"
        /// </summary>
        public static readonly string COLON             = ":";
        
        
        /// <summary>
        /// 逗号: ","
        /// </summary>
        public static readonly string COMMA             = ",";
        
        
        /// <summary>
        /// 分号: ";"
        /// </summary>
        public static readonly string SEMICOLON         = ";";
        
        
        /// <summary>
        /// 单引号: "'"
        /// </summary>
        public static readonly string SINGLE_QUOTE      = "'";
        
        
        /// <summary>
        /// 双引号: "''"
        /// </summary>
        public static readonly string DOUBLE_QUOTE      = "''";
        
        
        /// <summary>
        /// 年字符串: "岁"
        /// </summary>
        public static readonly string YEAR              = "岁";
        
        
        /// <summary>
        /// 月字符串: "月"
        /// </summary>
        public static readonly string MONTH             = "月";
        
        
        /// <summary>
        /// 天字符串: "天"
        /// </summary>
        public static readonly string DAY               = "天";
    }
}
