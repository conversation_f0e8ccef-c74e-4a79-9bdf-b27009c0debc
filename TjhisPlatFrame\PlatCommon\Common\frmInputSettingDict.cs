﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Windows.Forms;
using DevExpress.XtraEditors;
//using PlatCommon.System;
using NM_Service.NMService;
namespace PlatCommon.Common
{
    public partial class frmInputSettingDict : PlatCommon.SysBase.ParentForm
    {
        public frmInputSettingDict()
        {
            InitializeComponent();
        }
        public string code { get; set; }
        public string name { get; set; }
        public string dtype { get; set; }
        public string GS_INPUTSETING;//输入法
        DataTable dt = new DataTable();

        private void sb_apply_Click(object sender, EventArgs e)
        {
            try
            {
                code = string.Empty;
                name = string.Empty;
                dtype = string.Empty;
                int[] selectedRows = gridView.GetSelectedRows();
                if (selectedRows.Length.Equals(1))
                {
                    code = gridView.GetRowCellDisplayText(selectedRows[0], "DIAGNOSIS_CODE");
                    name = gridView.GetRowCellDisplayText(selectedRows[0], "DIAGNOSIS_NAME");
                    dtype = gridView.GetRowCellDisplayText(selectedRows[0], "DIAG_INDICATOR");
                }
                else if (selectedRows.Length > 1)
                {
                    foreach (int i in selectedRows)
                    {
                        code += gridView.GetRowCellDisplayText(i, "DIAGNOSIS_CODE") + ",";
                        name += gridView.GetRowCellDisplayText(i, "DIAGNOSIS_NAME") + ",";
                        dtype += gridView.GetRowCellDisplayText(i, "DIAG_INDICATOR") + ",";
                    }
                    code = code.Substring(0, code.Length - 1);
                    name = name.Substring(0, name.Length - 1);
                    dtype = dtype.Substring(0, dtype.Length - 1);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch(Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }

        private void sb_clear_Click(object sender, EventArgs e)
        {
            code = string.Empty;
            name = string.Empty;
            dtype = string.Empty;
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void sb_cancel_Click(object sender, EventArgs e)
        {
              this.Close();
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                code = gridView.GetFocusedRowCellDisplayText("DIAGNOSIS_CODE");
                name = gridView.GetFocusedRowCellDisplayText("DIAGNOSIS_NAME");
                dtype = gridView.GetFocusedRowCellDisplayText("DIAG_INDICATOR");
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }

        private void gridView_CustomDrawRowIndicator(object sender, DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventArgs e)
        {
            if (e.Info.IsRowIndicator && e.RowHandle >= 0)
                e.Info.DisplayText = (e.RowHandle + 1).ToString();
        }

        private void frmInputSettingDict_Load(object sender, EventArgs e)
        {
            getDiagnosisDict();
        }
        private void getDiagnosisDict()
        {
            string sql = "select d.diagnosis_code, d.diagnosis_name, d.input_code, d.input_code_wb,d.diag_indicator from V_DIAGNOSIS_INPUT_DICT d";
            if (GS_INPUTSETING.IndexOf("诊断") >= 0)
            {
                sql = "select d.diagnosis_code, d.diagnosis_name, d.input_code, d.input_code_wb, d.diag_indicator from V_DIAGNOSIS_INPUT_DICT d";
            }
            else if (GS_INPUTSETING.IndexOf("科室")>=0)
            {
                sql = "select dept_code diagnosis_code ,dept_name diagnosis_name,input_code,input_code_wb,''diag_indicator  from dept_dict where clinic_attr in('0','1') and HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
            }
            else if (GS_INPUTSETING.Equals("工作人员字典"))
            {
                sql = "select user_name diagnosis_code,name diagnosis_name,input_code,input_code_wb,''diag_indicator from STAFF_DICT where HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
            }

            dt = new ServerPublicClient().GetDataBySql(sql).Tables[0];
            if (dt != null && dt.Rows.Count > 0)
                gridControl.DataSource = dt;
        }

        private void tE_search_EditValueChanging(object sender, DevExpress.XtraEditors.Controls.ChangingEventArgs e)
        {
            try
            {
                if (dt == null || dt.Rows.Count == 0)
                    getDiagnosisDict();

                if (dt != null && dt.Rows.Count > 0)
                {
                    DataTable tempDt = dt.Copy();
                    tempDt.DefaultView.RowFilter = " diagnosis_code like '%" + tE_search.Text.Trim() + "%' or  diagnosis_name like '%" + tE_search.Text.Trim() + "%' or input_code like '%" + tE_search.Text.Trim() + "%' or input_code_wb like '%" + tE_search.Text.Trim() + "%' ";
                    gridControl.DataSource = tempDt.DefaultView.ToTable();
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }

        private void gridView_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                gridView_DoubleClick(sender, e);
            }
            if (e.KeyCode == Keys.Escape)
            {
                Close();
            } 
        }

        private void tE_search_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                gridControl.Focus();
                gridView.FocusedColumn = DIAGNOSIS_NAME;
            }
        }

        private void frmInputSettingDict_Shown(object sender, EventArgs e)
        {
            tE_search.Focus();
        }

        private void frmInputSettingDict_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                gridControl.Focus();
            }
            if (e.KeyCode == Keys.Down || e.KeyCode == Keys.Up)
            {
                gridControl.Focus();
            }
        }
    }
}
