﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.OracleClient;
using System.Linq;
using System.Text;

namespace TJ.EMR.Utility
{
    public static class DBSettingClass
    {
        private static OracleTransaction transaction = null;
        private static OracleCommand command = null;
        private static OracleDataAdapter adapter = null;
        private static string ConnectionString = ConfigurationManager.ConnectionStrings["connectionString"].ConnectionString;
        private static OracleConnection connection = new OracleConnection(ConnectionString);
        /// <summary>
        /// 打开数据库连接
        /// </summary>
        private static void OpenConnection()
        {
            try
            {
                if (connection.State == ConnectionState.Closed)
                {
                    connection.Open();
                }
            }
            catch (OracleException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// 关闭数据库连接
        /// </summary>
        private static void CloseConnection()
        {
            if (connection.State == ConnectionState.Open)
            {
                connection.Close();
            }
        }

        /// <summary>
        /// 创建事务
        /// </summary>
        private static void BeginTransaction()
        {
            OpenConnection();
            transaction = connection.BeginTransaction();
        }

        /// <summary>
        ///提交事务
        /// </summary>
        private static void CommitTransaction()
        {
            if (transaction != null)
            {
                transaction.Commit();
            }
        }

        /// <summary>
        /// 回滚事务
        /// </summary>
        private static void RollbackTransaction()
        {
            if (transaction != null)
            {
                transaction.Rollback();
            }
        }

        /// <summary>
        /// 释放事务
        /// </summary>
        private static void DisposeTransaction()
        {
            CloseConnection();
            if (transaction != null)
            {
                transaction.Dispose();
            }
        }
        /// <summary>
        /// 获取DataTable
        /// </summary>
        /// <param name="sql">SQL查询语句</param>
        /// <param name="tableName">缺省的表名：默认为无名-Nameless</param>
        /// <returns>DataTable</returns>
        public static DataTable GetDataTable(string sql, string tableName = "Nameless")
        {
            DataSet ds = new DataSet();
            OpenConnection();
            try
            {
                adapter = new OracleDataAdapter(sql, connection);
                adapter.Fill(ds, tableName = string.IsNullOrEmpty(tableName) ? "Nameless" : tableName);
            }
            catch (OracleException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                CloseConnection();
            }
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取DataTable
        /// </summary>
        /// <param name="sql">SQL查询语句</param>
        /// <param name="tableName">缺省的表名：默认为无名-Nameless</param>
        /// <returns>DataTable</returns>
        public static DataSet GetDataSet(string sql, string tableName = "Nameless")
        {
            DataSet ds = new DataSet();
            OpenConnection();
            try
            {
                adapter = new OracleDataAdapter(sql, connection);
                adapter.Fill(ds, tableName = string.IsNullOrEmpty(tableName) ? "Nameless" : tableName);
            }
            catch (OracleException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                CloseConnection();
            }
            return ds;
        }
        /// <summary>
        /// 批量SQL执行
        /// </summary>
        /// <param name="list">SQL执行语句List集合</param>
        /// <returns>执行数量</returns>
        public static int ExecuteSql(List<string> list)
        {
            int num = 0;
            try
            {
                BeginTransaction();
                foreach (string sql in list)
                {
                    command = new OracleCommand(sql, connection,transaction);
                    num += command.ExecuteNonQuery();
                }
                CommitTransaction();
                return num;
            }
            catch (OracleException ex)
            {
                RollbackTransaction();
                throw ex;
            }
            catch (Exception ex)
            {
                RollbackTransaction();
                throw ex;
            }
            finally
            {
                DisposeTransaction();
            }
        }
    }
}
