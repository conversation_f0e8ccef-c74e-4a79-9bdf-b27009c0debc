﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Drawing;

namespace PlatCommon.Comm
{
    public class HISConvertor
    {
        public static Color GetColorByDeciStr(string decStr)
        {
            if (string.IsNullOrEmpty(decStr))
            {
                return Color.Black;
            }
            int n = 0;
            if (int.TryParse(decStr, out n) == false) return SystemColors.Control;

            // 转换为二进制
            decStr = Convert.ToString(n, 2);

            int[] colors = new int[3]{0, 0, 0};

            int counter = 0;
            while (decStr.Length >= 8 && counter < 3)
            {
                string colorStr = decStr.Substring(0, 8);
                colors[counter++] = Convert.ToInt32(colorStr, 2);

                decStr = decStr.Substring(8, decStr.Length - 8);
            }

            return Color.FromArgb(colors[0], colors[1], colors[2]);
        }
    }
}
