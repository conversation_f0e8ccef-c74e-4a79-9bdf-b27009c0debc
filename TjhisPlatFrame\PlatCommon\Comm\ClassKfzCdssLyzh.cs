﻿using Newtonsoft.Json.Linq;
using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Windows.Forms;

namespace PlatCommon.Comm
{
    public class ClassKfzCdssLyzh
    {
        #region 声明dll
        /// <summary>
        /// 启动知识库客户端
        /// </summary>
        /// <param name="cdssPath">客户端中01cdss.exe的绝对路径</param>
        [DllImport("01cdss.dll", EntryPoint = "OpenCDSSByPath", CallingConvention = CallingConvention.Cdecl)]
        private static extern void OpenCDSSByPath(string cdssPath);

        /// <summary>
        /// 注册程序信息
        /// </summary>
        /// <param name="systemType">系统类型，固定填写HIS</param>
        /// <param name="handleNum">当前窗口句柄号</param>
        [DllImport("01cdss.dll", EntryPoint = "SetReceiveHandle", CallingConvention = CallingConvention.Cdecl)]
        private static extern void SetReceiveHandle(string systemType, int handleNum);

        /// <summary>
        /// 注册密钥信息，ak和sk联系CDSS技术人员提供
        /// </summary>
        /// <param name="ak">配置到参数表里了，AK_CDSS</param>
        /// <param name="sk">配置到参数表里了，SK_CDSS</param>
        [DllImport("01cdss.dll", EntryPoint = "SetSecurityKeys", CallingConvention = CallingConvention.Cdecl)]
        private static extern void SetSecurityKeys(string ak, string sk);

        /// <summary>
        /// 注册用户基本信息
        /// </summary>
        /// <param name="userInfo">用户信息（医生姓名、医生工号、当前登录科室、医院名称）</param>
        [DllImport("01cdss.dll", EntryPoint = "UserLogin", CallingConvention = CallingConvention.Cdecl)]
        private static extern void UserLogin(string userInfo);

        /// <summary>
        /// 发送数据到CDSS
        /// </summary>
        /// <param name="message">当前业务数据（医嘱、申请单、诊断）</param>
        /// <param name="handleNum">当前窗口句柄号</param>
        /// <param name="trigger">11打开文书，12关闭文书，13保存病历/医嘱，14提交病历/医嘱</param>
        /// <returns></returns>
        [DllImport("01cdss.dll", EntryPoint = "SendMessageToCDSS", CallingConvention = CallingConvention.Cdecl)]
        private static extern int SendMessageToCDSS(string message, int handleNum, int trigger);

        /// <summary>
        /// CDSS回填数据
        /// </summary>
        /// <param name="msg">患者数据，符合规定格式的JSON字符串</param>
        /// <param name="handleNum">窗口句柄号</param>
        [DllImport("01cdss.dll", EntryPoint = "BroadcastMessageToCDSS", CallingConvention = CallingConvention.Cdecl)]
        private static extern void BroadcastMessageToCDSS(string msg, int handleNum);

        /// <summary>
        /// 关闭知识库客户端
        /// </summary>
        [DllImport("01cdss.dll", EntryPoint = "CloseCDSS", CallingConvention = CallingConvention.Cdecl)]
        private static extern void CloseCDSS();

        /// <summary>
        /// 人卫项目说明知识库
        /// </summary>
        /// <param name="message"></param>
        /// <returns></returns>
        [DllImport("01cdss.dll", EntryPoint = "SendMessageToRw", CallingConvention = CallingConvention.Cdecl)]
        private static extern int SendMessageToRw(string message);
        #endregion

        #region 私有方法
        /// <summary>
        /// 写日志
        /// </summary>
        /// <param name="str"></param>
        private static void WriteLog(string str)
        {
            string writeLog = PlatCommon.SysBase.SystemParm.GetParameterValue("CDSS_WRITE_LOG", "*", "*", "*", PlatCommon.SysBase.SystemParm.HisUnitCode);
            if (writeLog.Equals("1"))
            {
                try
                {
                    DateTime d = DateTime.Now;
                    string path = AppDomain.CurrentDomain.BaseDirectory + "CDSS/logs/";
                    string fileName = d.ToString("yyyy-MM-dd-HH") + ".txt";

                    if (!Directory.Exists(path))
                        Directory.CreateDirectory(path);

                    using (StreamWriter file = new StreamWriter(path + fileName, true))
                    {
                        if (string.IsNullOrEmpty(str))
                            file.WriteLine("");
                        else
                        {
                            file.WriteLine(d.ToString("MM-dd HH:mm:ss") + " ==>> " + str + "\r\n");
                        }
                    }
                }
                catch (Exception e)
                {
                    MessageBox.Show("writeLog方法异常" + e.Message);
                }
            }
        }

        /// <summary>
        /// 注册用户基本信息
        /// </summary>
        /// <returns></returns>
        private static void UserLogin()
        {
            //userInfo: 用户信息，值为JSON字符串
            //{
            //    "name": "医生/护士/药师姓名",
            //    "id": "医生/护士/药师ID",
            //    "department": "科室",
            //    "hospital": "医院名称"
            //}
            Dictionary<string, string> idc = new Dictionary<string, string>();
            idc["name"] = PlatCommon.SysBase.SystemParm.LoginUser.NAME;
            idc["id"] = PlatCommon.SysBase.SystemParm.LoginUser.ID;
            //idc["department"] = PlatCommon.SysBase.SystemParm.Deptname;
            idc["hospital"] = PlatCommon.SysBase.SystemParm.HospitalID;

            string userInfo = Newtonsoft.Json.JsonConvert.SerializeObject(idc);
            UserLogin(userInfo);
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 启动知识库客户端
        /// </summary>
        public static void OpenCdss()
        {
            //注册用户
            UserLogin();
            //打开客户端
            OpenCDSSByPath(AppDomain.CurrentDomain.BaseDirectory + @"CDSS\Application\");
        }

        /// <summary>
        /// 注册信息
        /// </summary>
        /// <param name="systemType">信息系统类型（HIS、EMR、LIS、RIS、OAMS手麻等）</param>
        /// <param name="handleNum">窗口句柄号</param>
        /// <returns></returns>
        public static int Regedit(string systemType, int handleNum)
        {
            //使用窗口句柄号注册程序信息，用于告知接收Windows消息（医嘱回填数据）的窗体
            SetReceiveHandle(systemType, handleNum);
            //注册密钥信息 ak和sk请联系灵医智惠CDSS技术人员提供
            //string ak = "NhV8G0zHN0tT7oQN5FRAsdEsoTE5uDe7", sk = "vxMDEOyQnLePSU5s1VfkE1zX7rLS1fsx";
            string ak = PlatCommon.SysBase.SystemParm.GetParameterValue("AK_CDSS", "*", "*", "*", SystemParm.HisUnitCode);
            string sk = PlatCommon.SysBase.SystemParm.GetParameterValue("SK_CDSS", "*", "*", "*", SystemParm.HisUnitCode);
            SetSecurityKeys(ak, sk);
            return 0;
        }

        /// <summary>
        /// 关闭知识库客户端
        /// </summary>
        public static void CloseCdss()
        {
            CloseCDSS();
        }

        /// <summary>
        /// 人卫项目说明知识库
        /// </summary>
        /// <param name="itemClass">项目类别 C检验 D检查</param>
        /// <param name="itemName">项目名称</param>
        /// <param name="itemCode">项目代码</param>
        /// <param name="err">返回错误详细</param>
        /// <returns></returns>
        public static int GetRwSm(string itemClass, string itemName, string itemCode, ref string err)
        {
            Dictionary<string, string> idc = new Dictionary<string, string>();

            //取对照
            string sql = @"
                    select t.insur_code
                      from insurance.tj_vs_other t
                     where t.interfacecode = 'knowledge'
                       and t.his_code = '" + itemCode + @"'
                       and t.his_name = '" + itemName + "' ";
            DataTable dt_rw = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
            if (dt_rw.Rows.Count <= 0)
            {
                err = "没有找到诊疗项目与人卫对照信息";
                return -1;
            }

            string rwid = dt_rw.Rows[0][0].ToString();
            idc["rwid"] = rwid;
            //if (itemClass.Equals("C"))
            //{
            //    idc["category"] = "clinicalexam";
            //}
            //if (itemClass.Equals("D"))
            //{
            //    idc["category"] = "examination";
            //}
            //string message = Newtonsoft.Json.JsonConvert.SerializeObject(idc);
            WriteLog("查询诊疗项目：" + itemCode + "名称：" + itemName);
            //int rtn = SendMessageToRw(message);

            Dictionary<string, string> idc1 = new Dictionary<string, string>();
            string ip = "************";
            string port = ":8080";
            string service = "inside";
            string appkey = "0F240E805B11B86B";//集成商 appkey 1
            string integratorSecurity = "56F463854B2B6C0310b1d1a2a4db427dbbea1ce417a8fc88";// 集成商秘钥 1
            string organSecurity = "8444D4DB2A69A1CA47f559b8430542849cb9abfb83607836";// 机构秘钥（线上试用版本机构秘钥为“”） 1 （公网联调下默认传空(‘’)，医院私有化内网部署下，集成商appkey和集成商秘钥默认不变，机构秘钥会根据以后授权文件一起给出。）
            idc1.Add("appkey", appkey);//集成商 appkey 1 "C7C430B3B5C55AFA";//
            idc1.Add("method", "detail"); // 方法名，此接口为：detail 1"kgbases";//
            idc1.Add("integratorSecurity", integratorSecurity); // 集成商秘钥 1 "6BD4E5185D76A600fade821985cb4e8cb6a60541ed2052b";//
            idc1.Add("organSecurity", organSecurity); // 机构秘钥（线上试用版本机构秘钥为“”） 1 （公网联调下默认传空(‘’)，医院私有化内网部署下，集成商appkey和集成商秘钥默认不变，机构秘钥会根据以后授权文件一起给出。） "32244509E0E4EC90a77d3a4d862e4efaa2ec05948db04a69";//
            DateTime timeStampStartTime = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            string timestamp = ((long)(new NM_Service.NMService.ServerPublicClient().GetSysDate().ToUniversalTime() - timeStampStartTime).TotalMilliseconds).ToString();
            idc1.Add("timestamp", timestamp);// 时间戳 1 "1629795995482";//
            string sign = "";// 签名，获取签名方式详见 2.2 章节 1

            idc1.Add("id", rwid);// 知识 id 1

            idc1.Add("word", ""); // 当匹配到用户传递的字段名称时，返回的详情页面定位到对应的字段位置展示。 0
            idc1 = idc1.OrderBy(f => f.Key).ToDictionary(f => f.Key, f => f.Value);
            string md5_string = "";
            foreach (var v in idc1)
            {
                if (!string.IsNullOrEmpty(v.Value))
                {
                    md5_string += v.Key + "=" + v.Value + "&";
                }
            }
            md5_string = md5_string.Substring(0, md5_string.Length - 1);
            md5_string += integratorSecurity + organSecurity + appkey;
            byte[] buffer = Encoding.Default.GetBytes(md5_string); //将字符串解析成字节数组，随便按照哪种解析格式都行
            MD5 md5 = MD5.Create();  //使用MD5这个抽象类的Creat()方法创建一个虚拟的MD5类的对象。
            byte[] bufferNew = md5.ComputeHash(buffer); //使用MD5实例的ComputerHash()方法处理字节数组。
            string strNew = null;
            for (int i = 0; i < bufferNew.Length; i++)
            {
                strNew += bufferNew[i].ToString("x2");  //对bufferNew字节数组中的每个元素进行十六进制转换然后拼接成strNew字符串
            }
            sign = strNew;
            string url = $"http://{ip + port}/{service}/api/rest?";
            idc1.Add("sign", sign);
            foreach (var v in idc1)
            {
                url += v.Key + "=" + v.Value + "&";
            }
            url = url.Substring(0, url.Length - 1);
            WriteLog("URL:" + url);
            System.Diagnostics.Process.Start("iexplore.exe", url);
            //using (var wc = new WebClient())
            //{
            //    Encoding enc = Encoding.GetEncoding("UTF-8");
            //    Byte[] pageData = wc.DownloadData(url);
            //    string re = enc.GetString(pageData);
            //    WriteLog("返参:" + re);
            //    //ClasKfzCdssShow ckcs = new ClasKfzCdssShow(re);
            //    //ckcs.Show();
                
            //}
            return 0;
        }

        /// <summary>
        /// 查看检查报告知识库
        /// </summary>
        /// <param name="examNo"></param>
        /// <param name="err"></param>
        /// <returns></returns>
        public static int GetExamReportKnowledge(string examNo, ref string err)
        {
            // message: 患者数据，必须符合《患者数据规范》
            // handleNum: 窗口句柄号
            // trigger=11: 触发方式为打开检查报告时自动触发
            int handleNum = 0;
            string message = "{\"xx\":\"xx\"}";
            //没写拼json算法
            //SendMessageToCDSS(message, handleNum, 11);

            return 0;
        }

        /// <summary>
        /// 门诊病历记录
        /// </summary>
        /// <param name="clinic_no"></param>
        /// <param name="visitDate"></param>
        /// <param name="visitNo"></param>
        /// <param name="type"></param>
        /// <param name="ds"></param>
        /// <param name="handle">窗口句柄号</param>
        /// <returns></returns>
        public static int Out_mr(string clinic_no, DateTime visitDate, int visitNo, int type, DataSet ds, int handle)
        {
            DataTable dt_mr;
            string sql = "";
            if (ds != null)
            {
                dt_mr = ds.Tables[0];
            }
            else
            {
                sql = " select * from (select * from v_outp_mr_cdss where doctor_no = '" + PlatCommon.SysBase.SystemParm.LoginUser.ID + "' ";
                if (!string.IsNullOrEmpty(clinic_no))
                {
                    sql += " and clinic_no = '" + clinic_no + "' ";
                }
                else
                {
                    sql += " and visit_date = to_date('" + visitDate.ToString("yyyy-MM-dd") + "','yyyy-mm-dd') and visit_no = " + visitNo + " ";
                }

                sql += ") where rownum = 1";
                dt_mr = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
            }
            if (dt_mr.Rows.Count > 0)
            {
                Dictionary<string, object> idc = new Dictionary<string, object>();
                idc["场景"] = "门诊记录";
                switch (type)
                {
                    case 13:
                        idc["trigger"] = "保存";
                        break;
                    case 14:
                        idc["trigger"] = "提交";
                        break;
                    default:
                        idc["trigger"] = "CDSS";
                        break;
                }

                //工作人员信息
                JArray staffArray = new JArray();
                JObject staffObject = new JObject();
                staffObject["医院"] = PlatCommon.SysBase.SystemParm.HospitalID;
                //staffObject["科室"] = PlatCommon.SysBase.SystemParm.Deptname;
                staffObject["姓名"] = PlatCommon.SysBase.SystemParm.LoginUser.NAME;
                staffObject["账号ID"] = PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;
                staffObject["角色"] = PlatCommon.SysBase.SystemParm.LoginUser.JOB;
                staffObject["职称"] = PlatCommon.SysBase.SystemParm.LoginUser.TITLE;
                staffArray.Add(staffObject);
                idc["工作人员信息"] = staffArray;

                //患者信息
                JArray patientArray = new JArray();
                JObject patientObject = new JObject();
                string clinic_type = dt_mr.Rows[0]["clinic_type"].ToString();
                if (clinic_type.IndexOf("急") >= 0)
                {
                    clinic_type = "急诊";
                }
                else if (clinic_type.IndexOf("体检") >= 0)
                {
                    clinic_type = "体检";
                }
                else
                {
                    clinic_type = "门诊";
                }
                patientObject["就诊类型"] = clinic_type; //"门诊/急诊/体检",   // [必填] 
                patientObject["就诊次数"] = dt_mr.Rows[0]["诊疗次数"].ToString();    // "1":初诊，大于1表示复诊
                patientObject["就诊ID"] = dt_mr.Rows[0]["rid"].ToString();    // [必填] 住院号 或者 门诊号
                patientObject["患者ID"] = dt_mr.Rows[0]["pid"].ToString(); // [必填] 患者ID
                patientObject["年龄"] = dt_mr.Rows[0]["年龄"].ToString(); // [必填] xx岁/xx月/xx天/xx小时
                patientObject["性别"] = dt_mr.Rows[0]["性别"].ToString(); //"性别": "男/女",             // [必填] 
                patientObject["出生日期"] = dt_mr.Rows[0]["出生日期"].ToString(); //"出生日期": "",              // YYYY-MM-DD
                patientObject["职业"] = dt_mr.Rows[0]["职业"].ToString();
                patientObject["出生地"] = dt_mr.Rows[0]["出生地"].ToString();
                patientObject["现住地"] = dt_mr.Rows[0]["现住地"].ToString(); //  "现住地": "",
                patientObject["费用类型"] = dt_mr.Rows[0]["费用类型"].ToString(); //"费用类型": "医保/商保/自费/公费/等", 
                patientObject["身高"] = dt_mr.Rows[0]["身高"].ToString();  //"身高": "180cm",
                patientObject["体重"] = dt_mr.Rows[0]["体重"].ToString(); //"体重": "70kg",
                patientObject["ABO血型"] = dt_mr.Rows[0]["ABO血型"].ToString(); //"ABO血型": "A/B/AB/O",
                patientObject["Rh血型"] = dt_mr.Rows[0]["Rh血型"].ToString(); //"Rh血型": "阴/阳",
                patientObject["婚姻状态"] = dt_mr.Rows[0]["婚姻状态"].ToString(); //"婚姻状态": "未婚/已婚/离异/丧偶",
                JArray specialPeopleArray = new JArray();
                specialPeopleArray.Add("");
                patientObject["特殊人群"] = specialPeopleArray; // "[\"\"]"; //"婚姻状态": "未婚/已婚/离异/丧偶",
                patientArray.Add(patientObject);
                idc["患者信息"] = patientArray;

                //门诊记录
                JArray outpMrArray = new JArray();
                JObject outpMrObject = new JObject();
                outpMrObject["科室"] = dt_mr.Rows[0]["门诊科室"].ToString(); //"门诊科室": "",
                outpMrObject["记录ID"] = dt_mr.Rows[0]["记录ID"].ToString(); //"记录ID": "",
                outpMrObject["记录内容"] = ""; //无结构化纯文本内容, 仅用于无法提供结构化数据的厂商使用
                outpMrObject["记录时间"] = ""; //"YYYY-MM-DD HH:MM:SS" 门诊记录书写时间
                outpMrObject["就诊时间"] = dt_mr.Rows[0]["门诊日期"].ToString(); //"YYYY-MM-DD HH:MM:SS" 患者就诊时间
                outpMrObject["怀孕状态"] = ""; //"未孕/已孕/已育"
                outpMrObject["主诉"] = dt_mr.Rows[0]["主诉"].ToString(); //"主诉": "",
                outpMrObject["现病史"] = dt_mr.Rows[0]["现病史"].ToString(); // "现病史": "",
                outpMrObject["既往史"] = dt_mr.Rows[0]["既往史"].ToString(); //"既往史": "",
                outpMrObject["个人史"] = dt_mr.Rows[0]["个人史"].ToString(); // "个人史": "",
                outpMrObject["月经史"] = dt_mr.Rows[0]["月经史"].ToString(); // "月经史": "",
                outpMrObject["婚育史"] = dt_mr.Rows[0]["婚育史"].ToString(); //"婚育史": "",
                outpMrObject["家族史"] = dt_mr.Rows[0]["家族史"].ToString(); //"家族史": "",
                outpMrObject["过敏史"] = dt_mr.Rows[0]["过敏史"].ToString(); //"过敏史": "",
                outpMrObject["接种史"] = dt_mr.Rows[0]["接种史"].ToString(); //"接种史": "",
                outpMrObject["体格检查"] = dt_mr.Rows[0]["体格检查"].ToString(); //"体格检查": "",
                outpMrObject["辅助检查"] = dt_mr.Rows[0]["辅助检查"].ToString(); //"辅助检查": "",
                outpMrObject["专科检查"] = dt_mr.Rows[0]["专科检查"].ToString(); //"专科检查": "",
                outpMrObject["嘱咐"] = ""; // 医生对患者建议的注意事项

                //诊断
                JArray diagArray = new JArray();
                JObject diagObject = new JObject();
                sql = "select * from outp_diagnosis a where a.clinic_no = '" + clinic_no + "' order by a.ordinal ";
                DataTable dt_diag = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
                if (dt_diag.Rows.Count > 0)
                {
                    for (int i = 0; i < dt_diag.Rows.Count; i++)
                    {
                        diagObject["诊断类型"] = "初步诊断";//暂时用的初步诊断  "初步诊断/补充诊断/确诊诊断/其他诊断",
                        diagObject["诊断项"] = dt_diag.Rows[i]["diagnosis_desc"].ToString();
                        diagObject["主要诊断"] = "是"; //"是/否"
                        diagArray.Add(diagObject);
                    }
                    outpMrObject["诊断"] = diagArray;
                }

                outpMrArray.Add(outpMrObject);
                idc["门诊记录"] = outpMrArray;

                int handleNum = handle;
                string message = Newtonsoft.Json.JsonConvert.SerializeObject(idc);
                SendMessageToCDSS(message, handleNum, type);
                WriteLog("门诊病历" + message);
            }
            else
            {
                WriteLog("门诊病历" + " 视图v_outp_mr_cdss中未查询到记录！" + sql);
            }
            return 0;
        }

        /// <summary>
        /// 门诊医嘱
        /// </summary>
        /// <param name="clinic_no"></param>
        /// <param name="ds"></param>
        /// <param name="type"></param>
        /// <param name="handle">当前窗口句柄号</param>
        /// <returns></returns>
        public static int outp_Orders(string clinic_no, DataSet ds, int type, int handle)
        {
            DataTable dt_order = null;
            DataTable dt_mr;
            string sql = "";
            if (ds != null)
            {
                dt_order = ds.Tables[0];
            }

            sql = @"
                select *
                  from (select *
                          from v_outp_mr_cdss
                         where doctor_no =
                               '" + PlatCommon.SysBase.SystemParm.LoginUser.ID + @"'
                           and clinic_no = '" + clinic_no + @"')
                 where rownum = 1 ";

            dt_mr = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];

            if (dt_order.Rows.Count > 0 && dt_mr.Rows.Count > 0)
            {
                Dictionary<string, object> idc = new Dictionary<string, object>();
                idc["场景"] = "医嘱";
                switch (type)
                {
                    case 13:
                        idc["trigger"] = "保存";
                        break;
                    case 14:
                        idc["trigger"] = "提交";
                        break;
                    default:
                        idc["trigger"] = "CDSS";
                        break;
                }

                //工作人员信息
                JArray staffArray = new JArray();
                JObject staffObject = new JObject();
                staffObject["医院"] = PlatCommon.SysBase.SystemParm.HospitalID;
                //staffObject["科室"] = PlatCommon.SysBase.SystemParm.Deptname;
                staffObject["姓名"] = PlatCommon.SysBase.SystemParm.LoginUser.NAME;
                staffObject["账号ID"] = PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;
                staffObject["角色"] = PlatCommon.SysBase.SystemParm.LoginUser.JOB;
                staffObject["职称"] = PlatCommon.SysBase.SystemParm.LoginUser.TITLE;
                staffArray.Add(staffObject);
                idc["工作人员信息"] = staffArray;

                //患者信息
                JArray patientArray = new JArray();
                JObject patientObject = new JObject();
                patientObject["就诊类型"] = dt_mr.Rows[0]["诊疗类型"].ToString(); //"门诊/急诊/体检",   // [必填] 
                patientObject["就诊次数"] = dt_mr.Rows[0]["诊疗次数"].ToString();    // "1":初诊，大于1表示复诊
                patientObject["就诊ID"] = dt_mr.Rows[0]["rid"].ToString();    // [必填] 住院号 或者 门诊号
                patientObject["患者ID"] = dt_mr.Rows[0]["pid"].ToString(); // [必填] 患者ID
                patientObject["年龄"] = dt_mr.Rows[0]["年龄"].ToString(); // [必填] xx岁/xx月/xx天/xx小时
                patientObject["性别"] = dt_mr.Rows[0]["性别"].ToString(); //"性别": "男/女",             // [必填] 
                patientObject["出生日期"] = dt_mr.Rows[0]["出生日期"].ToString(); //"出生日期": "",              // YYYY-MM-DD
                patientObject["职业"] = dt_mr.Rows[0]["职业"].ToString();
                patientObject["出生地"] = dt_mr.Rows[0]["出生地"].ToString();
                patientObject["现住地"] = dt_mr.Rows[0]["现住地"].ToString(); //  "现住地": "",
                patientObject["费用类型"] = dt_mr.Rows[0]["费用类型"].ToString(); //"费用类型": "医保/商保/自费/公费/等", 
                patientObject["身高"] = dt_mr.Rows[0]["身高"].ToString();  //"身高": "180cm",
                patientObject["体重"] = dt_mr.Rows[0]["体重"].ToString(); //"体重": "70kg",
                patientObject["ABO血型"] = dt_mr.Rows[0]["ABO血型"].ToString(); //"ABO血型": "A/B/AB/O",
                patientObject["Rh血型"] = dt_mr.Rows[0]["Rh血型"].ToString(); //"Rh血型": "阴/阳",
                patientObject["婚姻状态"] = dt_mr.Rows[0]["婚姻状态"].ToString(); //"婚姻状态": "未婚/已婚/离异/丧偶",
                patientArray.Add(patientObject);
                idc["患者信息"] = patientArray;

                //医嘱
                
                JObject orderPerformObject = new JObject();
                JArray orderArray = new JArray();
                for (int i = 0; i < dt_order.Rows.Count; i++)
                {
                    JObject orderObject = new JObject();
                    orderObject["处方ID"] = "";
                    orderObject["医嘱ID"] = dt_order.Rows[i]["医嘱ID"].ToString();
                    orderObject["医嘱分组"] = dt_order.Rows[i]["医嘱分组"].ToString();
                    orderObject["医嘱类型"] = dt_order.Rows[i]["医嘱类型"].ToString();
                    orderObject["医嘱项目"] = dt_order.Rows[i]["医嘱项目"].ToString();
                    orderObject["长期或临时"] = dt_order.Rows[i]["长期或临时"].ToString();
                    orderObject["医嘱状态"] = dt_order.Rows[i]["医嘱状态"].ToString();
                    orderObject["录入时间"] = dt_order.Rows[i]["录入时间"].ToString();
                    orderObject["审核时间"] = dt_order.Rows[i]["录入时间"].ToString();
                    orderObject["开始时间"] = dt_order.Rows[i]["开始时间"].ToString();
                    orderObject["结束时间"] = dt_order.Rows[i]["结束时间"].ToString();
                    orderObject["医嘱开立科室"] = dt_order.Rows[i]["医嘱开立科室"].ToString();
                    orderObject["医嘱开立者"] = dt_order.Rows[i]["医嘱开立者"].ToString();
                    orderObject["医嘱审核科室"] = dt_order.Rows[i]["医嘱开立科室"].ToString();
                    orderObject["医嘱审核者"] = dt_order.Rows[i]["医嘱开立者"].ToString();

                    //以下为用药医嘱独有字段
                    try
                    {
                        orderObject["用药天数"] = "";
                        orderObject["药物商品名"] = dt_order.Rows[i]["药物商品名"].ToString(); // 医嘱类型=用药时 [必填]
                        orderObject["药物通用名"] = dt_order.Rows[i]["药物通用名"].ToString();
                        orderObject["国药准字号"] = dt_order.Rows[i]["国药准字号"].ToString(); // 医嘱类型=用药时 [必填]
                        orderObject["药物编码"] = dt_order.Rows[i]["药物编码"].ToString();
                        orderObject["药物类别"] = dt_order.Rows[i]["药物类别"].ToString();
                        orderObject["药物剂型"] = dt_order.Rows[i]["药物剂型"].ToString(); // 医嘱类型=用药时 [必填]
                        orderObject["用药途径"] = dt_order.Rows[i]["用药途径"].ToString(); // 医嘱类型=用药时 [必填]
                        orderObject["使用频率"] = dt_order.Rows[i]["使用频率"].ToString(); // 医嘱类型=用药时 [必填]
                        orderObject["单次剂量"] = dt_order.Rows[i]["单次剂量"].ToString(); // 医嘱类型=用药时 [必填]
                        orderObject["总剂量"] = dt_order.Rows[i]["总剂量"].ToString(); // 医嘱类型=用药时 [必填]
                        orderObject["剂量单位"] = dt_order.Rows[i]["剂量单位"].ToString(); // 医嘱类型=用药时 [必填]
                        orderObject["需要皮试"] = ""; // 皮试医嘱有2种常见方式: 1)用药医嘱之外单独开一项皮试医嘱；2)开用药医嘱，标记"需要皮试"，护士做完皮试后填写"皮试结果"
                    }
                    catch
                    {
                        orderObject["药物商品名"] = "";
                        orderObject["药物通用名"] = "";
                        orderObject["国药准字号"] = "";
                        orderObject["药物编码"] = "";
                        orderObject["药物类别"] = "";
                        orderObject["药物剂型"] = "";
                        orderObject["用药途径"] = "";
                        orderObject["使用频率"] = "";
                        orderObject["单次剂量"] = "";
                        orderObject["总剂量"] = "";
                        orderObject["剂量单位"] = "";
                        orderObject["需要皮试"] = "";
                    }

                    //以下为护理医嘱、诊疗医嘱独有字段
                    orderObject["执行频率"] = "";
                    orderObject["护理级别"] = "";

                    // 以下为检查医嘱、检验医嘱独有字段
                    try
                    {
                        orderObject["报告ID"] = dt_order.Rows[i]["报告ID"].ToString();// 检验报告ID或者检查报告ID
                    }
                    catch
                    {
                        orderObject["报告ID"] = "";
                    }

                    //以下为手术医嘱独有字段
                    orderObject["麻醉方式"] = "";

                    // 以下为检查医嘱独有字段
                    try
                    {
                        orderObject["检查部位"] = dt_order.Rows[i]["检查部位"].ToString();
                    }
                    catch
                    {
                        orderObject["检查部位"] = "";
                    }

                    //诊断
                    //JObject diagObject = new JObject();
                    //JArray diagArray = new JArray();
                    //sql = "select * from outp_diagnosis a where a.clinic_no = '" + clinic_no + "' order by a.ordinal ";
                    //DataTable dt_diag = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
                    //if (dt_diag.Rows.Count > 0)
                    //{
                    //    for (int j = 0; j < dt_diag.Rows.Count; j++)
                    //    {
                    //        diagObject["诊断类型"] = "初步诊断";//暂时用的初步诊断  "初步诊断/补充诊断/确诊诊断/其他诊断",
                    //        diagObject["诊断项"] = dt_diag.Rows[j]["diagnosis_desc"].ToString();
                    //        diagObject["主要诊断"] = "是"; //"是/否"
                    //        diagArray.Add(diagObject);
                    //    }
                    //    orderObject["诊断"] = diagArray;
                    //}
                    orderArray.Add(orderObject);
                }
                for (int i = 0; i < dt_order.Rows.Count; i++)
                {
                    orderPerformObject["医嘱ID"] = dt_order.Rows[i]["医嘱ID"].ToString();
                    orderPerformObject["医嘱分组"] = dt_order.Rows[i]["医嘱分组"].ToString();
                    orderPerformObject["医嘱类型"] = dt_order.Rows[i]["医嘱类型"].ToString();
                    orderPerformObject["医嘱项目"] = dt_order.Rows[i]["医嘱项目"].ToString();
                    orderPerformObject["长期或临时"] = dt_order.Rows[i]["长期或临时"].ToString();
                    orderPerformObject["医嘱状态"] = dt_order.Rows[i]["医嘱状态"].ToString();
                    orderPerformObject["执行时间"] = dt_order.Rows[i]["开始时间"].ToString();
                    orderPerformObject["医嘱执行科室"] = dt_order.Rows[i]["医嘱开立科室"].ToString();
                    orderPerformObject["医嘱执行者"] = dt_order.Rows[i]["医嘱开立者"].ToString();
                    orderPerformObject["皮试结果"] = "";
                    orderArray.Add(orderPerformObject);
                }
                idc["医嘱"] = orderArray;

                int handleNum = handle;
                string message = Newtonsoft.Json.JsonConvert.SerializeObject(idc);
                SendMessageToCDSS(message, handleNum, type);
                WriteLog("门诊医嘱" + message);
            }
            else
            {
                WriteLog("门诊医嘱" + " 视图v_outp_mr_cdss中未查询到记录！" + sql);
            }
            return 0;
        }

        /// <summary>
        /// 住院记录
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="visit_id"></param>
        /// <param name="type"></param>
        /// <param name="ds"></param>
        /// <param name="handle">窗口句柄号</param>
        /// <returns></returns>
        public static int inp_mr(string patient_id, string visit_id, int type, DataSet ds, int handle)
        {
            DataTable dt_mr;
            string sql = "";
            if (ds != null)
            {
                dt_mr = ds.Tables[0];
            }
            else
            {
                sql = "select * from v_inp_mr_cdss where patient_id = '" + patient_id + "' and visit_id = " + visit_id + " ";
                dt_mr = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
            }
            if (dt_mr.Rows.Count > 0)
            {
                Dictionary<string, object> idc = new Dictionary<string, object>();
                idc["场景"] = "入院记录";
                switch (type)
                {
                    case 13:
                        idc["trigger"] = "保存";
                        break;
                    case 14:
                        idc["trigger"] = "提交";
                        break;
                    default:
                        idc["trigger"] = "CDSS";
                        break;
                }

                //工作人员信息
                JArray staffArray = new JArray();
                JObject staffObject = new JObject();
                staffObject["医院"] = PlatCommon.SysBase.SystemParm.HospitalID;
                //staffObject["科室"] = PlatCommon.SysBase.SystemParm.Deptname;
                staffObject["姓名"] = PlatCommon.SysBase.SystemParm.LoginUser.NAME;
                staffObject["账号ID"] = PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;
                staffObject["角色"] = PlatCommon.SysBase.SystemParm.LoginUser.JOB;
                staffObject["职称"] = PlatCommon.SysBase.SystemParm.LoginUser.TITLE;
                staffArray.Add(staffObject);
                idc["工作人员信息"] = staffArray;

                //患者信息
                JArray patientArray = new JArray();
                JObject patientObject = new JObject();
                patientObject["就诊类型"] = dt_mr.Rows[0]["诊疗类型"].ToString(); //"门诊/急诊/体检",   // [必填] 
                patientObject["就诊次数"] = dt_mr.Rows[0]["诊疗次数"].ToString();    // "1":初诊，大于1表示复诊
                patientObject["就诊ID"] = dt_mr.Rows[0]["rid"].ToString();    // [必填] 住院号 或者 门诊号
                patientObject["患者ID"] = dt_mr.Rows[0]["pid"].ToString(); // [必填] 患者ID
                patientObject["年龄"] = dt_mr.Rows[0]["年龄"].ToString(); // [必填] xx岁/xx月/xx天/xx小时
                patientObject["性别"] = dt_mr.Rows[0]["性别"].ToString(); //"性别": "男/女",             // [必填] 
                patientObject["出生日期"] = dt_mr.Rows[0]["出生日期"].ToString(); //"出生日期": "",              // YYYY-MM-DD
                patientObject["职业"] = dt_mr.Rows[0]["职业"].ToString();
                patientObject["出生地"] = dt_mr.Rows[0]["出生地"].ToString();
                patientObject["现住地"] = dt_mr.Rows[0]["现住地"].ToString(); //  "现住地": "",
                patientObject["费用类型"] = dt_mr.Rows[0]["费用类型"].ToString(); //"费用类型": "医保/商保/自费/公费/等", 
                patientObject["身高"] = dt_mr.Rows[0]["身高"].ToString();  //"身高": "180cm",
                patientObject["体重"] = dt_mr.Rows[0]["体重"].ToString(); //"体重": "70kg",
                patientObject["ABO血型"] = dt_mr.Rows[0]["ABO血型"].ToString(); //"ABO血型": "A/B/AB/O",
                patientObject["Rh血型"] = dt_mr.Rows[0]["Rh血型"].ToString(); //"Rh血型": "阴/阳",
                patientObject["婚姻状态"] = dt_mr.Rows[0]["婚姻状态"].ToString(); //"婚姻状态": "未婚/已婚/离异/丧偶",
                JArray specialPeopleArray = new JArray();
                specialPeopleArray.Add("");
                patientObject["特殊人群"] = specialPeopleArray; // "[\"\"]"; //"婚姻状态": "未婚/已婚/离异/丧偶",
                patientArray.Add(patientObject);
                idc["患者信息"] = patientArray;

                //入院记录
                JArray inpMrArray = new JArray();
                JObject inpMrObject = new JObject();
                inpMrObject["科室"] = dt_mr.Rows[0]["入院科室"].ToString(); //"门诊科室": "",
                inpMrObject["记录ID"] = ""; //入院记录ID
                inpMrObject["记录内容"] = ""; // 无结构化纯文本内容, 仅用于无法提供结构化数据的厂商使用
                inpMrObject["记录时间"] = ""; //"YYYY-MM-DD HH:MM:SS" 入院记录书写时间
                inpMrObject["入院时间"] = dt_mr.Rows[0]["入院日期"].ToString(); //"门诊日期": "YYYY-MM-DD HH:MM:SS",
                inpMrObject["怀孕状态"] = ""; //"未孕/已孕/已育"
                inpMrObject["主诉"] = dt_mr.Rows[0]["主诉"].ToString(); //"主诉": "",
                inpMrObject["现病史"] = dt_mr.Rows[0]["现病史"].ToString(); // "现病史": "",
                inpMrObject["既往史"] = dt_mr.Rows[0]["既往史"].ToString(); //"既往史": "",
                inpMrObject["个人史"] = dt_mr.Rows[0]["个人史"].ToString(); // "个人史": "",
                inpMrObject["月经史"] = dt_mr.Rows[0]["月经史"].ToString(); // "月经史": "",
                inpMrObject["婚育史"] = dt_mr.Rows[0]["婚育史"].ToString(); //"婚育史": "",
                inpMrObject["家族史"] = dt_mr.Rows[0]["家族史"].ToString(); //"家族史": "",
                inpMrObject["过敏史"] = dt_mr.Rows[0]["过敏史"].ToString(); //"过敏史": "",
                inpMrObject["接种史"] = dt_mr.Rows[0]["接种史"].ToString(); //"接种史": "",
                inpMrObject["体格检查"] = dt_mr.Rows[0]["体格检查"].ToString(); //"体格检查": "",
                inpMrObject["辅助检查"] = dt_mr.Rows[0]["辅助检查"].ToString(); //"辅助检查": "",
                inpMrObject["专科检查"] = dt_mr.Rows[0]["专科检查"].ToString(); //"专科检查": "",

                //诊断
                JObject diagObject = new JObject();
                JArray diagArray = new JArray();
                sql = "select * from diagnosis a where a.patient_id = '" + patient_id + "' and a.visit_id = " + visit_id + " ";
                DataTable dt_diag = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
                if (dt_diag.Rows.Count > 0)
                {
                    for (int i = 0; i < dt_diag.Rows.Count; i++)
                    {
                        if (i == 0)
                        {
                            diagObject["诊断类型"] = "初步诊断";//暂时用的初步诊断  "初步诊断/补充诊断/确诊诊断/其他诊断",
                        }
                        else if (i == 1)
                        {
                            diagObject["诊断类型"] = "确诊诊断";
                        }
                        else
                        {
                            diagObject["诊断类型"] = "其他诊断";
                        }
                        diagObject["诊断结果"] = dt_diag.Rows[i]["diagnosis_desc"].ToString();
                        diagObject["主要诊断"] = "是"; //"是/否"
                        diagArray.Add(diagObject);
                    }
                    inpMrObject["诊断"] = diagArray;
                }

                inpMrArray.Add(inpMrObject);
                idc["入院记录"] = inpMrArray;

                int handleNum = handle;
                string message = Newtonsoft.Json.JsonConvert.SerializeObject(idc);
                SendMessageToCDSS(message, handleNum, type);
                WriteLog("住院病历" + message);
            }
            else
            {
                WriteLog("住院病历" + " 视图v_inp_mr_cdss中未查询到记录！" + sql);
            }
            return 0;
        }

        /// <summary>
        /// 住院医嘱
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="visit_id"></param>
        /// <param name="ds"></param>
        /// <param name="type"></param>
        /// <param name="handle">窗口句柄号</param>
        /// <returns></returns>
        public static int inp_Orders(string patient_id, string visit_id, DataSet ds, int type, int handle)
        {
            DataTable dt_order = null;
            DataTable dt_mr;
            if (ds != null)
            {
                dt_order = ds.Tables[0];
            }

            string sql = "select * from v_inp_mr_cdss where patient_id = '" + patient_id + "' and visit_id = " + visit_id + " ";
            dt_mr = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];

            if (dt_order.Rows.Count > 0 && dt_mr.Rows.Count > 0)
            {
                Dictionary<string, object> idc = new Dictionary<string, object>();
                idc["场景"] = "医嘱";
                switch (type)
                {
                    case 13:
                        idc["trigger"] = "保存";
                        break;
                    case 14:
                        idc["trigger"] = "提交";
                        break;
                    default:
                        idc["trigger"] = "CDSS";
                        break;
                }

                //工作人员信息
                JArray staffArray = new JArray();
                JObject staffObject = new JObject();
                staffObject["医院"] = PlatCommon.SysBase.SystemParm.HospitalID;
                //staffObject["科室"] = PlatCommon.SysBase.SystemParm.Deptname;
                staffObject["姓名"] = PlatCommon.SysBase.SystemParm.LoginUser.NAME;
                staffObject["账号ID"] = PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;
                staffObject["角色"] = PlatCommon.SysBase.SystemParm.LoginUser.JOB;
                staffObject["职称"] = PlatCommon.SysBase.SystemParm.LoginUser.TITLE;
                staffArray.Add(staffObject);
                idc["工作人员信息"] = staffArray;

                //患者信息
                JArray patientArray = new JArray();
                JObject patientObject = new JObject();
                patientObject["就诊类型"] = dt_mr.Rows[0]["诊疗类型"].ToString(); //"门诊/急诊/体检",   // [必填] 
                patientObject["就诊次数"] = dt_mr.Rows[0]["诊疗次数"].ToString();    // "1":初诊，大于1表示复诊
                patientObject["就诊ID"] = dt_mr.Rows[0]["rid"].ToString();    // [必填] 住院号 或者 门诊号
                patientObject["患者ID"] = dt_mr.Rows[0]["pid"].ToString(); // [必填] 患者ID
                patientObject["年龄"] = dt_mr.Rows[0]["年龄"].ToString(); // [必填] xx岁/xx月/xx天/xx小时
                patientObject["性别"] = dt_mr.Rows[0]["性别"].ToString(); //"性别": "男/女",             // [必填] 
                patientObject["出生日期"] = dt_mr.Rows[0]["出生日期"].ToString(); //"出生日期": "",              // YYYY-MM-DD
                patientObject["职业"] = dt_mr.Rows[0]["职业"].ToString();
                patientObject["出生地"] = dt_mr.Rows[0]["出生地"].ToString();
                patientObject["现住地"] = dt_mr.Rows[0]["现住地"].ToString(); //  "现住地": "",
                patientObject["费用类型"] = dt_mr.Rows[0]["费用类型"].ToString(); //"费用类型": "医保/商保/自费/公费/等", 
                patientObject["身高"] = dt_mr.Rows[0]["身高"].ToString();  //"身高": "180cm",
                patientObject["体重"] = dt_mr.Rows[0]["体重"].ToString(); //"体重": "70kg",
                patientObject["ABO血型"] = dt_mr.Rows[0]["ABO血型"].ToString(); //"ABO血型": "A/B/AB/O",
                patientObject["Rh血型"] = dt_mr.Rows[0]["Rh血型"].ToString(); //"Rh血型": "阴/阳",
                patientObject["婚姻状态"] = dt_mr.Rows[0]["婚姻状态"].ToString(); //"婚姻状态": "未婚/已婚/离异/丧偶",
                patientArray.Add(patientObject);
                idc["患者信息"] = patientArray;

                //医嘱
                
                JArray orderArray = new JArray();
                for (int i = 0; i < dt_order.Rows.Count; i++)
                {
                    JObject orderObject = new JObject();
                    orderObject["医嘱ID"] = dt_order.Rows[i]["医嘱ID"].ToString();
                    orderObject["医嘱分组"] = dt_order.Rows[i]["医嘱分组"].ToString();
                    orderObject["医嘱类型"] = dt_order.Rows[i]["医嘱类型"].ToString();
                    orderObject["医嘱项目"] = dt_order.Rows[i]["医嘱项目"].ToString();
                    orderObject["长期或临时"] = dt_order.Rows[i]["长期或临时"].ToString();
                    orderObject["医嘱状态"] = dt_order.Rows[i]["医嘱状态"].ToString();
                    orderObject["录入时间"] = dt_order.Rows[i]["录入时间"].ToString();
                    orderObject["开始时间"] = dt_order.Rows[i]["开始时间"].ToString();
                    orderObject["结束时间"] = dt_order.Rows[i]["结束时间"].ToString();
                    orderObject["医嘱开立科室"] = dt_order.Rows[i]["医嘱开立科室"].ToString();
                    orderObject["医嘱开立者"] = dt_order.Rows[i]["医嘱开立者"].ToString();
                    orderObject["医嘱审核科室"] = dt_order.Rows[i]["医嘱开立科室"].ToString();
                    orderObject["医嘱审核者"] = dt_order.Rows[i]["医嘱开立者"].ToString();

                    //以下为用药医嘱独有字段
                    try
                    {
                        orderObject["药物商品名"] = dt_order.Rows[i]["药物商品名"].ToString(); // 医嘱类型=用药时 [必填]
                        orderObject["药物通用名"] = dt_order.Rows[i]["药物通用名"].ToString();
                        orderObject["国药准字号"] = dt_order.Rows[i]["国药准字号"].ToString(); // 医嘱类型=用药时 [必填]
                        orderObject["药物编码"] = dt_order.Rows[i]["药物编码"].ToString();
                        orderObject["药物类别"] = dt_order.Rows[i]["药物类别"].ToString();
                        orderObject["药物剂型"] = dt_order.Rows[i]["药物剂型"].ToString(); // 医嘱类型=用药时 [必填]
                        orderObject["用药途径"] = dt_order.Rows[i]["用药途径"].ToString(); // 医嘱类型=用药时 [必填]
                        orderObject["使用频率"] = dt_order.Rows[i]["使用频率"].ToString(); // 医嘱类型=用药时 [必填]
                        orderObject["单次剂量"] = dt_order.Rows[i]["单次剂量"].ToString(); // 医嘱类型=用药时 [必填]
                        orderObject["总剂量"] = dt_order.Rows[i]["总剂量"].ToString(); // 医嘱类型=用药时 [必填]
                        orderObject["剂量单位"] = dt_order.Rows[i]["剂量单位"].ToString(); // 医嘱类型=用药时 [必填]
                        orderObject["需要皮试"] = ""; // 皮试医嘱有2种常见方式: 1)用药医嘱之外单独开一项皮试医嘱；2)开用药医嘱，标记"需要皮试"，护士做完皮试后填写"皮试结果"
                    }
                    catch
                    {
                        orderObject["药物商品名"] = "";
                        orderObject["药物通用名"] = "";
                        orderObject["国药准字号"] = "";
                        orderObject["药物编码"] = "";
                        orderObject["药物类别"] = "";
                        orderObject["药物剂型"] = "";
                        orderObject["用药途径"] = "";
                        orderObject["使用频率"] = "";
                        orderObject["单次剂量"] = "";
                        orderObject["总剂量"] = "";
                        orderObject["剂量单位"] = "";
                        orderObject["需要皮试"] = "";
                    }

                    //以下为护理医嘱、诊疗医嘱独有字段
                    orderObject["执行频率"] = "";
                    orderObject["护理级别"] = "";

                    //以下为检查医嘱、检验医嘱独有字段
                    try
                    {
                        orderObject["报告ID"] = dt_order.Rows[i]["报告ID"].ToString();
                    }
                    catch
                    {
                        orderObject["报告ID"] = "";
                    }

                    //以下为手术医嘱独有字段
                    try
                    {
                        orderObject["麻醉方式"] = dt_order.Rows[i]["麻醉方式"].ToString();
                    }
                    catch
                    {
                        orderObject["麻醉方式"] = "";
                    }

                    // 以下为检查医嘱独有字段
                    try
                    {
                        orderObject["检查部位"] = dt_order.Rows[i]["检查部位"].ToString();
                    }
                    catch
                    {
                        orderObject["检查部位"] = "";
                    }

                    //诊断
                    //JObject diagObject = new JObject();
                    //JArray diagArray = new JArray();
                    //sql = "select * from diagnosis a where a.patient_id = '" + patient_id + "' and a.visit_id = " + visit_id + " ";
                    //DataTable dt_diag = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
                    //if (dt_diag.Rows.Count > 0)
                    //{
                    //    for (int j = 0; j < dt_diag.Rows.Count; j++)
                    //    {
                    //        if (j == 0)
                    //        {
                    //            diagObject["诊断类型"] = "初步诊断";//暂时用的初步诊断  "初步诊断/补充诊断/确诊诊断/其他诊断",
                    //        }
                    //        else if (j == 1)
                    //        {
                    //            diagObject["诊断类型"] = "确诊诊断";
                    //        }
                    //        else
                    //        {
                    //            diagObject["诊断类型"] = "其他诊断";
                    //        }
                    //        diagObject["诊断结果"] = dt_diag.Rows[j]["diagnosis_desc"].ToString();
                    //        diagObject["主要诊断"] = "是"; //"是/否"
                    //        diagArray.Add(diagObject);
                    //    }
                    //    orderObject["诊断"] = diagArray;
                    //}
                    orderArray.Add(orderObject);
                }
                JObject orderPerformObject = new JObject();
                for (int i = 0; i < dt_order.Rows.Count; i++)
                {
                    orderPerformObject["医嘱ID"] = dt_order.Rows[i]["医嘱ID"].ToString();
                    orderPerformObject["医嘱分组"] = dt_order.Rows[i]["医嘱分组"].ToString();
                    orderPerformObject["医嘱类型"] = dt_order.Rows[i]["医嘱类型"].ToString();
                    orderPerformObject["医嘱项目"] = dt_order.Rows[i]["医嘱项目"].ToString();
                    orderPerformObject["长期或临时"] = dt_order.Rows[i]["长期或临时"].ToString();
                    orderPerformObject["医嘱状态"] = dt_order.Rows[i]["医嘱状态"].ToString();
                    orderPerformObject["执行时间"] = dt_order.Rows[i]["开始时间"].ToString();
                    orderPerformObject["医嘱执行科室"] = dt_order.Rows[i]["医嘱开立科室"].ToString();
                    orderPerformObject["医嘱执行者"] = dt_order.Rows[i]["医嘱开立者"].ToString();
                    orderPerformObject["皮试结果"] = "";
                    orderArray.Add(orderPerformObject);
                }
                idc["医嘱"] = orderArray;

                int handleNum = handle;
                string message = Newtonsoft.Json.JsonConvert.SerializeObject(idc);
                SendMessageToCDSS(message, handleNum, type);
                WriteLog("住院医嘱" + message);
            }
            else
            {
                WriteLog("住院医嘱" + " 视图v_inp_mr_cdss中未查询到记录！" + sql);
            }
            return 0;
        }
        /// <summary>
        /// 关闭病历文档时自动触发
        /// </summary>
        /// <param name="handle"></param>
        /// <returns></returns>
        public static int closs_mr(int handle)
        {
            try
            {
                // message: ""
                // handleNum: 窗口句柄号
                // trigger=12: 触发方式为关闭病历文档时自动触发
                SendMessageToCDSS("", handle, 12); //当医生关闭病历窗口时后台自动触发，通过异步接口发送给CDSS程序。切换到已经存在的病历文档窗口或选项卡时，先执行trigger = 12告知当前窗口或选项卡已关闭，再执行trigger = 11发送新窗口或选项卡中的病历内容。
                WriteLog("关闭病历：handle" + handle);
                return 0;
            }
            catch(Exception ex)
            {
                WriteLog("关闭病历：" + ex.Message);
                return -1;
            }
        }

        /// <summary>
        /// 形成医嘱内容结构方法
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static int SetOrder(ref DataTable dt)
        {
            dt.Columns.Add("医嘱ID");
            dt.Columns.Add("医嘱分组");
            dt.Columns.Add("医嘱类型");
            dt.Columns.Add("医嘱项目");
            dt.Columns.Add("长期或临时");
            dt.Columns.Add("医嘱状态");
            dt.Columns.Add("录入时间");
            dt.Columns.Add("开始时间");
            dt.Columns.Add("结束时间");
            dt.Columns.Add("医嘱开立科室");
            dt.Columns.Add("医嘱开立者");

            //以下为用药医嘱独有字段
            dt.Columns.Add("药物商品名");
            dt.Columns.Add("药物通用名");
            dt.Columns.Add("国药准字号");
            dt.Columns.Add("药物编码");
            dt.Columns.Add("药物类别");
            dt.Columns.Add("药物剂型");
            dt.Columns.Add("用药途径");
            dt.Columns.Add("使用频率");
            dt.Columns.Add("单次剂量");
            dt.Columns.Add("总剂量");
            dt.Columns.Add("剂量单位");
            dt.Columns.Add("需要皮试");

            //以下为护理医嘱、诊疗医嘱独有字段
            dt.Columns.Add("执行频率");
            dt.Columns.Add("护理级别");

            //以下为检查医嘱、检验医嘱独有字段
            dt.Columns.Add("报告ID");

            //以下为手术医嘱独有字段
            dt.Columns.Add("麻醉方式");

            //以下为检查医嘱独有字段
            dt.Columns.Add("检查部位");

            //诊断
            dt.Columns.Add("诊断");

            dt.Columns.Add("医嘱执行科室");
            dt.Columns.Add("医嘱执行者");
            dt.Columns.Add("皮试结果");

            return 0;
        }
        #endregion
    }
}
