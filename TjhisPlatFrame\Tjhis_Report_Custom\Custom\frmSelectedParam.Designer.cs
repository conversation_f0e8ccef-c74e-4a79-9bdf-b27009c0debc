﻿namespace Tjhis.Report.Custom.Custom
{
    partial class frmSelectedParam
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmSelectedParam));
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gc_SerialNo = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemCheckEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.gc_PARAM_NAME = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_EDIT_TYPE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_CAPTION = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_DISPLAY_MEMBER = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_VALUE_MEMBER = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_SOURCE_TYPE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_NEXT_PARAM_NAME = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_DEFAULT_VALUE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_CONTROL_WIDTH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_DATE_FORMAT = new DevExpress.XtraGrid.Columns.GridColumn();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.btnCancel = new DevExpress.XtraBars.BarLargeButtonItem();
            this.btnOk = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl1
            // 
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 29);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemCheckEdit1});
            this.gridControl1.Size = new System.Drawing.Size(784, 492);
            this.gridControl1.TabIndex = 1;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gc_SerialNo,
            this.gc_PARAM_NAME,
            this.gc_EDIT_TYPE,
            this.gc_CAPTION,
            this.gc_DISPLAY_MEMBER,
            this.gc_VALUE_MEMBER,
            this.gc_SOURCE_TYPE,
            this.gc_NEXT_PARAM_NAME,
            this.gc_DEFAULT_VALUE,
            this.gc_CONTROL_WIDTH,
            this.gc_DATE_FORMAT});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsCustomization.AllowFilter = false;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.RowClick += new DevExpress.XtraGrid.Views.Grid.RowClickEventHandler(this.gridView1_RowClick);
            // 
            // gc_SerialNo
            // 
            this.gc_SerialNo.Caption = "选择";
            this.gc_SerialNo.ColumnEdit = this.repositoryItemCheckEdit1;
            this.gc_SerialNo.FieldName = "COL_SEL";
            this.gc_SerialNo.MinWidth = 10;
            this.gc_SerialNo.Name = "gc_SerialNo";
            this.gc_SerialNo.Visible = true;
            this.gc_SerialNo.VisibleIndex = 0;
            this.gc_SerialNo.Width = 35;
            // 
            // repositoryItemCheckEdit1
            // 
            this.repositoryItemCheckEdit1.AutoHeight = false;
            this.repositoryItemCheckEdit1.Name = "repositoryItemCheckEdit1";
            this.repositoryItemCheckEdit1.ValueChecked = "1";
            this.repositoryItemCheckEdit1.ValueUnchecked = "0";
            // 
            // gc_PARAM_NAME
            // 
            this.gc_PARAM_NAME.Caption = "参数";
            this.gc_PARAM_NAME.FieldName = "PARAM_NAME";
            this.gc_PARAM_NAME.MinWidth = 10;
            this.gc_PARAM_NAME.Name = "gc_PARAM_NAME";
            this.gc_PARAM_NAME.OptionsColumn.AllowEdit = false;
            this.gc_PARAM_NAME.Visible = true;
            this.gc_PARAM_NAME.VisibleIndex = 1;
            this.gc_PARAM_NAME.Width = 105;
            // 
            // gc_EDIT_TYPE
            // 
            this.gc_EDIT_TYPE.Caption = "参数类型";
            this.gc_EDIT_TYPE.FieldName = "EDIT_TYPE";
            this.gc_EDIT_TYPE.MinWidth = 76;
            this.gc_EDIT_TYPE.Name = "gc_EDIT_TYPE";
            this.gc_EDIT_TYPE.OptionsColumn.AllowEdit = false;
            this.gc_EDIT_TYPE.Visible = true;
            this.gc_EDIT_TYPE.VisibleIndex = 3;
            this.gc_EDIT_TYPE.Width = 76;
            // 
            // gc_CAPTION
            // 
            this.gc_CAPTION.Caption = "标题名称";
            this.gc_CAPTION.FieldName = "CAPTION";
            this.gc_CAPTION.MinWidth = 120;
            this.gc_CAPTION.Name = "gc_CAPTION";
            this.gc_CAPTION.OptionsColumn.AllowEdit = false;
            this.gc_CAPTION.Visible = true;
            this.gc_CAPTION.VisibleIndex = 2;
            this.gc_CAPTION.Width = 137;
            // 
            // gc_DISPLAY_MEMBER
            // 
            this.gc_DISPLAY_MEMBER.Caption = "显示列";
            this.gc_DISPLAY_MEMBER.FieldName = "DISPLAY_MEMBER";
            this.gc_DISPLAY_MEMBER.MinWidth = 100;
            this.gc_DISPLAY_MEMBER.Name = "gc_DISPLAY_MEMBER";
            this.gc_DISPLAY_MEMBER.OptionsColumn.AllowEdit = false;
            this.gc_DISPLAY_MEMBER.Visible = true;
            this.gc_DISPLAY_MEMBER.VisibleIndex = 4;
            this.gc_DISPLAY_MEMBER.Width = 100;
            // 
            // gc_VALUE_MEMBER
            // 
            this.gc_VALUE_MEMBER.Caption = "数据列";
            this.gc_VALUE_MEMBER.FieldName = "VALUE_MEMBER";
            this.gc_VALUE_MEMBER.MinWidth = 100;
            this.gc_VALUE_MEMBER.Name = "gc_VALUE_MEMBER";
            this.gc_VALUE_MEMBER.OptionsColumn.AllowEdit = false;
            this.gc_VALUE_MEMBER.Visible = true;
            this.gc_VALUE_MEMBER.VisibleIndex = 5;
            this.gc_VALUE_MEMBER.Width = 100;
            // 
            // gc_SOURCE_TYPE
            // 
            this.gc_SOURCE_TYPE.Caption = "数据源类型";
            this.gc_SOURCE_TYPE.FieldName = "SOURCE_TYPE";
            this.gc_SOURCE_TYPE.MinWidth = 100;
            this.gc_SOURCE_TYPE.Name = "gc_SOURCE_TYPE";
            this.gc_SOURCE_TYPE.OptionsColumn.AllowEdit = false;
            this.gc_SOURCE_TYPE.Visible = true;
            this.gc_SOURCE_TYPE.VisibleIndex = 6;
            this.gc_SOURCE_TYPE.Width = 100;
            // 
            // gc_NEXT_PARAM_NAME
            // 
            this.gc_NEXT_PARAM_NAME.Caption = "联动参数";
            this.gc_NEXT_PARAM_NAME.FieldName = "NEXT_PARAM_NAME";
            this.gc_NEXT_PARAM_NAME.MinWidth = 120;
            this.gc_NEXT_PARAM_NAME.Name = "gc_NEXT_PARAM_NAME";
            this.gc_NEXT_PARAM_NAME.OptionsColumn.AllowEdit = false;
            this.gc_NEXT_PARAM_NAME.Visible = true;
            this.gc_NEXT_PARAM_NAME.VisibleIndex = 7;
            this.gc_NEXT_PARAM_NAME.Width = 120;
            // 
            // gc_DEFAULT_VALUE
            // 
            this.gc_DEFAULT_VALUE.Caption = "默认值";
            this.gc_DEFAULT_VALUE.FieldName = "DEFAULT_VALUE";
            this.gc_DEFAULT_VALUE.MinWidth = 80;
            this.gc_DEFAULT_VALUE.Name = "gc_DEFAULT_VALUE";
            this.gc_DEFAULT_VALUE.OptionsColumn.AllowEdit = false;
            this.gc_DEFAULT_VALUE.Visible = true;
            this.gc_DEFAULT_VALUE.VisibleIndex = 8;
            this.gc_DEFAULT_VALUE.Width = 80;
            // 
            // gc_CONTROL_WIDTH
            // 
            this.gc_CONTROL_WIDTH.Caption = "控件大小";
            this.gc_CONTROL_WIDTH.FieldName = "CONTROL_WIDTH";
            this.gc_CONTROL_WIDTH.MinWidth = 60;
            this.gc_CONTROL_WIDTH.Name = "gc_CONTROL_WIDTH";
            this.gc_CONTROL_WIDTH.OptionsColumn.AllowEdit = false;
            this.gc_CONTROL_WIDTH.Visible = true;
            this.gc_CONTROL_WIDTH.VisibleIndex = 9;
            this.gc_CONTROL_WIDTH.Width = 60;
            // 
            // gc_DATE_FORMAT
            // 
            this.gc_DATE_FORMAT.Caption = "日期格式";
            this.gc_DATE_FORMAT.FieldName = "DATE_FORMAT";
            this.gc_DATE_FORMAT.MinWidth = 100;
            this.gc_DATE_FORMAT.Name = "gc_DATE_FORMAT";
            this.gc_DATE_FORMAT.Visible = true;
            this.gc_DATE_FORMAT.VisibleIndex = 10;
            this.gc_DATE_FORMAT.Width = 100;
            // 
            // barManager1
            // 
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1,
            this.bar2});
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.btnOk,
            this.btnCancel});
            this.barManager1.MainMenu = this.bar2;
            this.barManager1.MaxItemId = 2;
            // 
            // bar1
            // 
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.Text = "Tools";
            this.bar1.Visible = false;
            // 
            // bar2
            // 
            this.bar2.BarName = "Main menu";
            this.bar2.DockCol = 0;
            this.bar2.DockRow = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Bottom;
            this.bar2.FloatLocation = new System.Drawing.Point(191, 542);
            this.bar2.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.btnCancel),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnOk)});
            this.bar2.OptionsBar.DrawBorder = false;
            this.bar2.OptionsBar.DrawDragBorder = false;
            this.bar2.OptionsBar.MultiLine = true;
            this.bar2.OptionsBar.UseWholeRow = true;
            this.bar2.Text = "Main menu";
            // 
            // btnCancel
            // 
            this.btnCancel.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.btnCancel.Caption = "取消";
            this.btnCancel.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.btnCancel.Id = 1;
            this.btnCancel.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnCancel.ImageOptions.Image")));
            this.btnCancel.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnCancel.ImageOptions.LargeImage")));
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnCancel_ItemClick);
            // 
            // btnOk
            // 
            this.btnOk.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.btnOk.Caption = "确认";
            this.btnOk.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.btnOk.Id = 0;
            this.btnOk.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnOk.ImageOptions.Image")));
            this.btnOk.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnOk.ImageOptions.LargeImage")));
            this.btnOk.Name = "btnOk";
            this.btnOk.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnOk_ItemClick);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Manager = this.barManager1;
            this.barDockControlTop.Size = new System.Drawing.Size(784, 29);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 521);
            this.barDockControlBottom.Manager = this.barManager1;
            this.barDockControlBottom.Size = new System.Drawing.Size(784, 40);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 29);
            this.barDockControlLeft.Manager = this.barManager1;
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 492);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(784, 29);
            this.barDockControlRight.Manager = this.barManager1;
            this.barDockControlRight.Size = new System.Drawing.Size(0, 492);
            // 
            // frmSelectedParam
            // 
            this.ClientSize = new System.Drawing.Size(784, 561);
            this.Controls.Add(this.gridControl1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Name = "frmSelectedParam";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "参数字典";
            this.Load += new System.EventHandler(this.frmSelectedParam_Load);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gc_SerialNo;
        private DevExpress.XtraGrid.Columns.GridColumn gc_PARAM_NAME;
        private DevExpress.XtraGrid.Columns.GridColumn gc_EDIT_TYPE;
        private DevExpress.XtraGrid.Columns.GridColumn gc_CAPTION;
        private DevExpress.XtraGrid.Columns.GridColumn gc_DISPLAY_MEMBER;
        private DevExpress.XtraGrid.Columns.GridColumn gc_VALUE_MEMBER;
        private DevExpress.XtraGrid.Columns.GridColumn gc_SOURCE_TYPE;
        private DevExpress.XtraGrid.Columns.GridColumn gc_NEXT_PARAM_NAME;
        private DevExpress.XtraGrid.Columns.GridColumn gc_DEFAULT_VALUE;
        private DevExpress.XtraGrid.Columns.GridColumn gc_CONTROL_WIDTH;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit repositoryItemCheckEdit1;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraBars.BarLargeButtonItem btnCancel;
        private DevExpress.XtraBars.BarLargeButtonItem btnOk;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraGrid.Columns.GridColumn gc_DATE_FORMAT;
    }
}
