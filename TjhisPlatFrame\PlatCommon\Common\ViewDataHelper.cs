﻿using System;
using System.Data;
using System.IO;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using PlatCommon.Comm;
using ComboBox = System.Windows.Forms.ComboBox;


namespace PlatCommon.Common
{
    /// <summary>
    /// 数据改变事件托管
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    public delegate void DataChanged_Event(object sender, EventArgs e);

    /// <summary>
    /// 窗体数据帮助类
    /// </summary>
    public class ViewDataHelper
    {
        /// <summary>
        /// 清除界面控件值
        /// </summary>
        /// <param name="parentCtrl"></param>
        public static void ClearData(Control parentCtrl)
        {
            Label lbl = null;
            TextBox txtEdit = null;
            ComboBox cmb = null;
            //RadioButton rdo = null;
            DateTimePicker dtp = null;
            CheckBox chk = null;
            CheckedListBox chkList = null;
            DevExpress.XtraEditors.ButtonEdit txtButtonEdit = null;
            DevExpress.XtraEditors.TextEdit txtTextEdit = null;
            string colName = string.Empty;

            foreach (Control ctrl in parentCtrl.Controls)
            {
                // 标签控件
                lbl = ctrl as Label;
                if (lbl != null && lbl.Tag != null)
                {
                    lbl.Text = string.Empty;
                    continue;
                }
                // DevExpress.XtraEditors.ButtonEdit控件
                txtButtonEdit = ctrl as DevExpress.XtraEditors.ButtonEdit;
                if (txtButtonEdit != null && txtButtonEdit.Tag != null)
                {
                    txtButtonEdit.Text = string.Empty;
                    continue;
                }
                // DevExpress.XtraEditors.TextEdit控件
                txtTextEdit = ctrl as DevExpress.XtraEditors.TextEdit;
                if (txtTextEdit != null && txtTextEdit.Tag != null)
                {
                    txtTextEdit.Text = string.Empty;
                    continue;
                }
                // 文本框控件处理
                txtEdit = ctrl as TextBox;
                if (txtEdit != null)
                {
                    txtEdit.Text = string.Empty;
                    
                    continue;
                }

                // 日期控件
                dtp = ctrl as DateTimePicker;
                if (dtp != null)
                {
                    dtp.Checked = false;

                    continue;
                }

                // 下拉选择框
                cmb = ctrl as ComboBox;
                if (cmb != null)
                {
                    cmb.SelectedIndex = -1;

                    continue;
                }

                // 复选按钮
                chk = ctrl as CheckBox;
                if (chk != null)
                {
                    chk.Checked = false;

                    continue;
                }

                // 复选列表
                chkList = ctrl as CheckedListBox;
                if (chkList != null)
                {
                    for (int i = 0; i < chkList.Items.Count; i++)
                    {
                        chkList.SetItemChecked(i, false);
                    }

                    continue;
                }

                // GroupBox处理
                if (ctrl.Controls.Count > 0)
                {
                    ClearData(ctrl);
                }
            }
        }


        /// <summary>
        /// 显示数据
        /// </summary>
        /// <param name="parentCtrl"></param>
        /// <param name="dr"></param>
        public static void ShowData(Control parentCtrl, ref DataRow dr)
        {
            if (dr == null) return;

            TextBox txtEdit = null;
            ComboBox cmb = null;
            RadioButton rdo = null;
            DateTimePicker dtp = null;
            NumericUpDown nUpDown = null;
            Label lbl = null;
            CheckBox chk = null;
            DevExpress.XtraEditors.ButtonEdit txtButtonEdit = null;
            DevExpress.XtraEditors.TextEdit txtTextEdit = null;
            DevExpress.XtraEditors.CheckEdit xchkEdit = null;

            string colName = string.Empty;

            foreach (Control ctrl in parentCtrl.Controls)
            {
                // 标签控件处理
                lbl = ctrl as Label;
                if (lbl != null && Convert.ToString(lbl.Tag).Trim().Length > 0)
                {
                    colName = lbl.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName))
                    {
                        lbl.Text = Convert.ToString(dr[colName]);
                    }

                    continue;
                }
                // ButtonEdit
                txtButtonEdit = ctrl as DevExpress.XtraEditors.ButtonEdit;
                if (txtButtonEdit != null && Convert.ToString(txtButtonEdit.Tag).Trim().Length > 0)
                {
                    colName = txtButtonEdit.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName))
                    {
                        txtButtonEdit.Text = Convert.ToString(dr[colName]);
                    }

                    continue;
                }
                // TextEdit
                txtTextEdit = ctrl as DevExpress.XtraEditors.TextEdit;
                if (txtTextEdit != null && Convert.ToString(txtTextEdit.Tag).Trim().Length > 0)
                {
                    colName = txtTextEdit.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName))
                    {
                        txtTextEdit.Text = Convert.ToString(dr[colName]);
                    }

                    continue;
                }
                // 文本框控件处理
                txtEdit = ctrl as TextBox;
                if (txtEdit != null && Convert.ToString(txtEdit.Tag).Trim().Length > 0)
                {
                    colName = txtEdit.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName))
                    {
                        txtEdit.Text = Convert.ToString(dr[colName]);
                    }

                    continue;
                }

                // 日期控件
                dtp = ctrl as DateTimePicker;
                if (dtp != null && Convert.ToString(dtp.Tag).Trim().Length > 0)
                {
                    colName = dtp.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName) && dr[colName] != DBNull.Value)
                    {
                        dtp.Value = DateTime.Parse(dr[colName].ToString());
                        dtp.Checked = true;
                    }
                    else
                    {
                        dtp.Checked = false;
                    }

                    continue;
                }

                // 下拉选择框
                cmb = ctrl as ComboBox;
                if (cmb != null && Convert.ToString(cmb.Tag).Trim().Length > 0)
                {
                    colName = cmb.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName))
                    {
                        try
                        {
                            if (dr[colName] == DBNull.Value || string.IsNullOrEmpty(dr[colName].ToString()))
                            {
                                cmb.SelectedIndex = -1;
                            }
                            else
                            {
                                cmb.SelectedValue = Convert.ToString(dr[colName]);
                            }
                        }
                        catch
                        {
                            cmb.SelectedIndex = -1;
                        }
                    }

                    continue;
                }

                // 单选按钮
                rdo = ctrl as RadioButton;
                if (rdo != null && Convert.ToString(rdo.Tag).Trim().Length > 0)
                {
                    if (rdo.Tag.ToString().Split('|').Length < 2) continue;

                    colName = rdo.Tag.ToString().Split('|')[0].Trim();
                    string checkedValue = rdo.Tag.ToString().Split('|')[1].Trim();

                    if (dr.Table.Columns.Contains(colName))
                    {
                        rdo.Checked = Convert.ToString(dr[colName]).Equals(checkedValue);
                    }

                    continue;
                }

                // 复选按钮
                chk = ctrl as CheckBox;
                if (chk != null && Convert.ToString(chk.Tag).Trim().Length > 0)
                {
                    if (chk.Tag.ToString().Split('|').Length < 2) continue;

                    colName = chk.Tag.ToString().Split('|')[0].Trim();
                    string checkedValue = chk.Tag.ToString().Split('|')[1].Trim();

                    if (dr.Table.Columns.Contains(colName))
                    {
                        string colValue = Convert.ToString(dr[colName]).Trim();

                        if (string.IsNullOrEmpty(checkedValue) && 
                            colValue.Length > 0 && colValue.Equals("0") == false && colValue.Equals("-1") == false)
                        {
                            chk.Checked = true;
                            chk.Tag = colName + " | " + dr[colName].ToString().Trim();
                        }
                        else
                        {
                            chk.Checked = Convert.ToString(dr[colName]).Equals(checkedValue);
                        }
                    }

                    continue;
                }

                // 复选框
                xchkEdit = ctrl as DevExpress.XtraEditors.CheckEdit;
                if (xchkEdit != null)
                {
                    xchkEdit.Checked = false;
                    continue;
                }

                // 数值选择
                nUpDown = ctrl as NumericUpDown;
                if (nUpDown != null && Convert.ToString(nUpDown.Tag).Trim().Length > 0)
                {
                    colName = nUpDown.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName))
                    {
                        nUpDown.Value  = Converter.ToDeci(dr[colName]);
                    }

                    continue;
                }
                
                // GroupBox处理
                if (ctrl.Controls.Count > 0)
                {
                    ShowData(ctrl, ref dr);
                }
            }
        }




        /// <summary>
        /// 必填项检查
        /// </summary>
        /// <param name="ctrls"></param>
        /// <returns>出错的控件</returns>
        public static Control CheckRequired(Control[] ctrls)
        {
            TextBox txtEdit = null;
            ComboBox  cmb = null;
            string colName = string.Empty;

            for (int i = 0; i < ctrls.Length; i++ )
            {
                // 文本框控件处理
                txtEdit = ctrls[i] as TextBox;
                if (txtEdit != null)
                {
                    if (txtEdit.Text.Trim().Length == 0)
                    {
                        return ctrls[i];
                    }

                    continue;
                }

                // 下拉选择框
                cmb = ctrls[i] as ComboBox;
                if (cmb != null)
                {
                    if (cmb.SelectedIndex == -1)
                    {
                        return ctrls[i];
                    }

                    continue;
                }
            }

            return null;
        }


        /// <summary>
        /// 输入最大长度检测
        /// </summary>
        /// <param name="parentCtrl"></param>
        /// <param name="dt"></param>
        /// <returns>出错的控件</returns>
        public static Control CheckMaxLength(Control parentCtrl, DataTable dt)
        {
            TextBox txtEdit = null;
            string colName = string.Empty;

            foreach (Control ctrl in parentCtrl.Controls)
            {
                // 文本框控件处理
                txtEdit = ctrl as TextBox;
                if (txtEdit != null && Convert.ToString(txtEdit.Tag).Trim().Length > 0)
                {
                    colName = txtEdit.Tag.ToString();
                    
                    if (dt.Columns.Contains(colName))
                    {
                        if (dt.Columns[colName].MaxLength 
                            < System.Text.Encoding.Default.GetBytes(txtEdit.Text.TrimEnd()).Length)
                        {
                            return ctrl;
                        }
                    }

                    continue;
                }

                // GroupBox处理
                if (ctrl.Controls.Count > 0)
                {
                    CheckMaxLength(ctrl, dt);
                }
            }

            return null;
        }


        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="parentCtrl"></param>
        /// <param name="dr"></param>
        public static void SaveData(Control parentCtrl, ref DataRow dr)
        {
            if (dr == null) throw new Exception("空记录不能保存内容!"); ;

            TextBox txtEdit = null;
            ComboBox cmb = null;
            RadioButton rdo = null;
            DateTimePicker dtp = null;
            NumericUpDown nUpDown = null;
            CheckBox chk = null;

            string colName = string.Empty;

            foreach (Control ctrl in parentCtrl.Controls)
            {
                // 文本框控件处理
                txtEdit = ctrl as TextBox;
                if (txtEdit != null && Convert.ToString(txtEdit.Tag).Trim().Length > 0)
                {
                    colName = txtEdit.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName))
                    {
                        if (dr[colName].ToString().Equals(txtEdit.Text.TrimEnd()) == false)
                            dr[colName] = txtEdit.Text.TrimEnd();
                    }
                    continue;
                }

                // 日期控件
                dtp = ctrl as DateTimePicker;
                if (dtp != null && dtp.Checked == true && Convert.ToString(dtp.Tag).Trim().Length > 0)
                {
                    colName = dtp.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName))
                    {
                        if (dr[colName] == DBNull.Value || ((DateTime)(dr[colName])).Equals(dtp.Value) == false)
                            dr[colName] = dtp.Value;
                    }

                    continue;
                }

                // 下拉选择框
                cmb = ctrl as ComboBox;
                if (cmb != null && Convert.ToString(cmb.Tag).Trim().Length > 0)
                {
                    colName = cmb.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName))
                    {
                        if (cmb.SelectedIndex == -1)
                        {
                            if (dr[colName] != DBNull.Value)
                                dr[colName] = DBNull.Value;
                        }
                        else
                        {
                            if (dr[colName].ToString().Equals(cmb.SelectedValue.ToString()) == false)
                                dr[colName] = cmb.SelectedValue.ToString();
                        }
                    }

                    continue;
                }

                // 单选按钮
                rdo = ctrl as RadioButton;
                if (rdo != null && rdo.Checked && Convert.ToString(rdo.Tag).Trim().Length > 0)
                {
                    if (rdo.Tag.ToString().Split('|').Length < 2) continue;

                    colName = rdo.Tag.ToString().Split('|')[0].Trim();
                    string checkedValue = rdo.Tag.ToString().Split('|')[1].Trim();
                    
                    if (dr.Table.Columns.Contains(colName))
                    {
                        if (dr[colName].ToString().Equals(checkedValue) == false)
                            dr[colName] = checkedValue;
                    }

                    continue;
                }

                // 复选框
                chk = ctrl as CheckBox;
                if (chk != null)
                {
                    if (chk.Tag.ToString().Split('|').Length < 2) continue;

                    colName = chk.Tag.ToString().Split('|')[0].Trim();

                    string checkedValue = chk.Tag.ToString().Split('|')[1].Trim().ToUpper();
                    if (chk.Checked == false)
                    {
                        switch (checkedValue)
                        {
                            case "Y": checkedValue = "N"; break;
                            case "1": checkedValue = "0"; break;
                            case "0": checkedValue = "1"; break;
                            case "TRUE": checkedValue = "FALSE"; break;
                            case "FALSE": checkedValue = "TRUE"; break;
                            default: checkedValue = string.Empty; break;
                        }
                    }

                    if (dr.Table.Columns.Contains(colName))
                    {
                        if (dr[colName].ToString().Equals(checkedValue) == false)
                            dr[colName] = checkedValue;
                    }

                    continue;
                }

                // 数值选择
                nUpDown = ctrl as NumericUpDown;
                if (nUpDown != null && Convert.ToString(nUpDown.Tag).Trim().Length > 0)
                {
                    colName = nUpDown.Tag.ToString();
                    if (dr.Table.Columns.Contains(colName))
                    {
                        if (dr[colName].ToString().Equals(nUpDown.Value.ToString()) == false)
                            dr[colName] = nUpDown.Value.ToString();
                    }
                    continue;
                }

                // GroupBox处理
                if (ctrl.Controls.Count > 0)
                {
                    SaveData(ctrl, ref dr);
                }
            }
        }

        
        /// <summary>
        /// 将数据集中的数据保存到数据表中,并写日志
        /// </summary>
        /// <param name="drDest"></param>
        /// <param name="drSrc"></param>
        /// <returns></returns>
        public static bool SaveDataOneRow(ref DataRow drDest, ref DataRow drSrc)
        {
            if (drDest == null || drSrc == null) return false;

            // 字段赋值
            foreach (DataColumn dc in drSrc.Table.Columns)
            {
                if (drDest.Table.Columns.Contains(dc.ColumnName) && dc.ReadOnly == false)
                {
                    drDest[dc.ColumnName] = drSrc[dc.ColumnName];
                }
            }

            return true;
        }


        /// <summary>
        /// 控件变更事件注册
        /// </summary>
        /// <param name="parentCtrl"></param>
        /// <param name="dataChangedEvent"></param>
        public static void RegDataChangeEvent(Control parentCtrl, System.EventHandler dataChangedEvent)
        {
            TextBox txtEdit = null;
            ComboBox cmb = null;
            RadioButton rdo = null;
            DateTimePicker dtp = null;
            CheckBox chk = null;
            CheckedListBox chkList = null;

            string colName = string.Empty;

            foreach (Control ctrl in parentCtrl.Controls)
            {
                // 文本框控件处理
                txtEdit = ctrl as TextBox;
                if (txtEdit != null)
                {
                    txtEdit.TextChanged += dataChangedEvent;

                    continue;
                }

                // 日期控件
                dtp = ctrl as DateTimePicker;
                if (dtp != null)
                {
                    dtp.ValueChanged += dataChangedEvent;
                    continue;
                }

                // 下拉选择框
                cmb = ctrl as ComboBox;
                if (cmb != null)
                {
                    cmb.SelectedIndexChanged += dataChangedEvent;
                    
                    continue;
                }

                // 单选按钮
                rdo = ctrl as RadioButton;
                if (rdo != null)
                {
                    rdo.CheckedChanged += dataChangedEvent;

                    continue;
                }

                // 复选框
                chk = ctrl as CheckBox;
                if (chk != null)
                {
                    chk.CheckedChanged += dataChangedEvent;
                    continue;
                }

                // 复选框列表
                chkList = ctrl as CheckedListBox;
                if (chkList != null)
                {
                    chkList.SelectedValueChanged += dataChangedEvent;
                    continue;
                }
                
                // GroupBox处理
                if (ctrl.Controls.Count > 0)
                {
                    RegDataChangeEvent(ctrl, dataChangedEvent);
                }
            }
        }


        /// <summary>
        ///设置控件模式 
        /// </summary>
        /// <param name="parentCtrl"></param>
        /// <param name="viewMode"></param>
        public static void SetViewMode(Control parentCtrl, OperMode viewMode)
        {
            TextBox txtEdit = null;
            Label lbl = null;
            TreeView trv = null;
            Button btn = null;
            DataGridView dgv = null;
            CheckedListBox chl = null;

            bool blnReadOnly = (viewMode == OperMode.View || viewMode == OperMode.Del);

            foreach (Control ctrl in parentCtrl.Controls)
            {
                // 标签
                lbl = ctrl as Label;
                if (lbl != null)
                {
                    continue;
                }

                // 文本框控件处理
                txtEdit = ctrl as TextBox;
                if (txtEdit != null && txtEdit.ReadOnly == false)
                {
                    txtEdit.ReadOnly = blnReadOnly;
                    continue;
                }

                // 树
                trv = ctrl as TreeView;
                if (trv != null)
                {
                    trv.BeforeCheck += trv_BeforeCheck;
                    trv.Tag = (blnReadOnly ? "READONLY" : "");
                    continue;
                }

                // 复选框列表
                chl = ctrl as CheckedListBox;
                if (chl != null && blnReadOnly)
                {
                    chl.Tag = (blnReadOnly ? "READONLY" : "");
                    chl.ItemCheck += chl_ItemCheck;

                    continue;
                }

                // 按钮
                btn = ctrl as Button;
                if (btn != null)
                {
                    if (btn.Text.Contains("查看") || btn.Text.Contains("关闭"))
                    {
                        btn.Enabled = true;
                    }
                    else if (btn.Text.Contains("保存") || btn.Text.Contains("删除"))
                    {
                        //if (btn.Text.Contains("保存")) btn.Enabled = (viewMode == OperMode.Mod || viewMode == OperMode.Add);
                        if (btn.Text.Contains("保存")) btn.Enabled = false;
                        if (btn.Text.Contains("删除")) btn.Enabled = (viewMode == OperMode.Del);
                    }
                    else
                    {
                        btn.Enabled = !blnReadOnly;
                    }

                    continue;
                }

                // 表格
                dgv = ctrl as DataGridView;
                if (dgv != null)
                {
                    dgv.ReadOnly = (dgv.ReadOnly == false ? blnReadOnly : dgv.ReadOnly);
                    dgv.AllowUserToAddRows = (dgv.AllowUserToAddRows == true ? !blnReadOnly : dgv.AllowUserToAddRows);
                }

                // GroupBox处理
                if (ctrl.Controls.Count > 0)
                {
                    SetViewMode(ctrl, viewMode);
                    continue;
                }

                // 其它控件
                ctrl.Enabled = !blnReadOnly;
            }
        }


        /// <summary>
        /// 设置界面上的TextBox的属性
        /// </summary>
        /// <param name="parentCtrl"></param>
        public static void SetViewCtrlAttr(Control parentCtrl)
        { 
            TextBox txtEdit = null;

            foreach (Control ctrl in parentCtrl.Controls)
            {
                // 文本框控件处理
                txtEdit = ctrl as TextBox;
                if (txtEdit != null)
                {
                    if (txtEdit.Multiline == true)
                    {
                        txtEdit.AcceptsReturn = true;
                        txtEdit.AcceptsTab = false;
                        txtEdit.ScrollBars = ScrollBars.Both;
                    }

                    continue;
                }
                
                // GroupBox处理
                if (ctrl.Controls.Count > 0)
                {
                    SetViewCtrlAttr(ctrl);
                    continue;
                }
            }
        }

        /// <summary>
        /// 设置ToolBar状态
        /// </summary>
        /// <param name="tlb"></param>
        /// <param name="viewMode"></param>
        public static void SetToolBarStatus(ref ToolStrip tlb, OperMode viewMode)
        {
            bool blnSelectMode = (viewMode == OperMode.SELECT);

            for (int i = 0; i < tlb.Items.Count; i++)
            {
                ToolStripItem item = tlb.Items[i];

                // 分隔符
                if (item.Name.Contains("toolStripSeparator") && item.Name.Contains("1") == false)
                {
                    item.Visible = !blnSelectMode;
                    continue;
                }
                
                // 查询条件
                if (string.IsNullOrEmpty(item.Text))
                {
                    item.Visible = true;
                    continue;
                }

                // 查询按钮
                if (item.Text.Contains("查询") || item.Text.Contains("查看")) continue;

                // 选择按钮
                if (item.Text.Contains("选择"))
                {
                    item.Visible = blnSelectMode;
                    continue;
                }

                item.Visible = !blnSelectMode;
            }   
        }


        /// <summary>
        /// 为CheckBox中Tag进行赋值
        /// </summary>
        /// <param name="chk"></param>
        ///  <param name="val"></param>
        public static void SetCheckBoxTagValue(ref CheckBox chk, string val)
        {
            if (Converter.ToString(chk.Tag).Length == 0) return;

            string[] parts = chk.Tag.ToString().Split('|');
            if (parts.Length > 0)
            {
                chk.Tag = parts[0] + " | " + val;
            }
        }


        /// <summary>
        /// CheckedListBox 项目选择事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        static void chl_ItemCheck(object sender, ItemCheckEventArgs e)
        {
            CheckedListBox chl = sender as CheckedListBox;

            if (chl != null && Converter.ToString(chl.Tag).Trim().Length > 0)
            {
                e.NewValue = e.CurrentValue;
            }
        }


        /// <summary>
        /// 树勾选前事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        static void trv_BeforeCheck(object sender, TreeViewCancelEventArgs e)
        {
            TreeView trv = sender as TreeView;
            if (trv != null && Converter.ToString(trv.Tag).Trim().Length > 0)
            {
                e.Cancel = true;
            }
        }


        /// <summary>
        /// 把窗体作为控件加载
        /// </summary>
        /// <param name="ctrlParent"></param>
        /// <param name="frm"></param>
        /// <param name="style"></param>
        public static void LoadWindowAsControl(Control ctrlParent, Form frm, DockStyle style)
        {
            frm.WindowState = FormWindowState.Normal;
            frm.FormBorderStyle = FormBorderStyle.None;
            frm.ShowInTaskbar = false;
            frm.StartPosition = FormStartPosition.WindowsDefaultLocation;
            frm.TopLevel = false;
            //frm.Left = 0;
            //frm.Top = 0;
            //frm.Width = ctrlParent.Width;
            //frm.Height = ctrlParent.Height;
            //frm.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom;
            frm.Dock = style;
            frm.Visible = true;

            ctrlParent.Controls.Add(frm);
        }
    }
}

