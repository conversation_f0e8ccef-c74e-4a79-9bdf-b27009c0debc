<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RabbitMQ.Client</name>
    </assembly>
    <members>
        <member name="T:RabbitMQ.Client.AmqpTcpEndpoint">
            <summary>
            Represents a TCP-addressable AMQP peer: a host name and port number.
            </summary>
            <para>
            Some of the constructors take, as a convenience, a System.Uri
            instance representing an AMQP server address. The use of Uri
            here is not standardised - Uri is simply a convenient
            container for internet-address-like components. In particular,
            the Uri "Scheme" property is ignored: only the "Host" and
            "Port" properties are extracted.
            </para>
        </member>
        <member name="F:RabbitMQ.Client.AmqpTcpEndpoint.DefaultAmqpSslPort">
            <summary>
            Default Amqp ssl port.
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.AmqpTcpEndpoint.UseDefaultPort">
            <summary>
            Indicates that the default port for the protocol should be used.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.AmqpTcpEndpoint.#ctor(System.String,System.Int32,RabbitMQ.Client.SslOption)">
            <summary>
            Creates a new instance of the <see cref="T:RabbitMQ.Client.AmqpTcpEndpoint"/>.
            </summary>
            <param name="hostName">Hostname.</param>
            <param name="portOrMinusOne"> Port number. If the port number is -1, the default port number will be used.</param>
            <param name="ssl">Ssl option.</param>
        </member>
        <member name="M:RabbitMQ.Client.AmqpTcpEndpoint.#ctor(System.String,System.Int32)">
            <summary>
            Creates a new instance of the <see cref="T:RabbitMQ.Client.AmqpTcpEndpoint"/>.
            </summary>
            <param name="hostName">Hostname.</param>
            <param name="portOrMinusOne"> Port number. If the port number is -1, the default port number will be used.</param>
        </member>
        <member name="M:RabbitMQ.Client.AmqpTcpEndpoint.#ctor">
            <summary>
            Construct an AmqpTcpEndpoint with "localhost" as the hostname, and using the default port.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.AmqpTcpEndpoint.#ctor(System.Uri,RabbitMQ.Client.SslOption)">
            <summary>
            Creates a new instance of the <see cref="T:RabbitMQ.Client.AmqpTcpEndpoint"/> with the given Uri and ssl options.
            </summary>
            <remarks>
            Please see the class overview documentation for information about the Uri format in use.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.AmqpTcpEndpoint.#ctor(System.Uri)">
            <summary>
            Creates a new instance of the <see cref="T:RabbitMQ.Client.AmqpTcpEndpoint"/> with the given Uri.
            </summary>
            <remarks>
            Please see the class overview documentation for information about the Uri format in use.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.AmqpTcpEndpoint.Clone">
            <summary>
            Clones the endpoint.
            </summary>
            <returns>A copy with the same hostname, port, and TLS settings</returns>
        </member>
        <member name="M:RabbitMQ.Client.AmqpTcpEndpoint.CloneWithHostname(System.String)">
            <summary>
            Clones the endpoint using the provided hostname.
            </summary>
            <param name="hostname">Hostname to use</param>
            <returns>A copy with the provided hostname and port/TLS settings of this endpoint</returns>
        </member>
        <member name="P:RabbitMQ.Client.AmqpTcpEndpoint.HostName">
            <summary>
            Retrieve or set the hostname of this <see cref="T:RabbitMQ.Client.AmqpTcpEndpoint"/>.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.AmqpTcpEndpoint.Port">
             <summary>Retrieve or set the port number of this
            AmqpTcpEndpoint. A port number of -1 causes the default
            port number.</summary>
        </member>
        <member name="P:RabbitMQ.Client.AmqpTcpEndpoint.Protocol">
            <summary>
            Retrieve IProtocol of this <see cref="T:RabbitMQ.Client.AmqpTcpEndpoint"/>.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.AmqpTcpEndpoint.AddressFamily">
            <summary>
            Used to force the address family of the endpoint
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.AmqpTcpEndpoint.Ssl">
            <summary>
            Retrieve the SSL options for this AmqpTcpEndpoint. If not set, null is returned.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.AmqpTcpEndpoint.Parse(System.String)">
            <summary>
            Construct an instance from a protocol and an address in "hostname:port" format.
            </summary>
            <remarks>
            If the address string passed in contains ":", it is split
            into a hostname and a port-number part. Otherwise, the
            entire string is used as the hostname, and the port-number
            is set to -1 (meaning the default number for the protocol
            variant specified).
            Hostnames provided as IPv6 must appear in square brackets ([]).
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.AmqpTcpEndpoint.ParseMultiple(System.String)">
            <summary>
            Splits the passed-in string on ",", and passes the substrings to <see cref="M:RabbitMQ.Client.AmqpTcpEndpoint.Parse(System.String)"/>.
            </summary>
            <remarks>
            Accepts a string of the form "hostname:port,
            hostname:port, ...", where the ":port" pieces are
            optional, and returns a corresponding array of <see cref="T:RabbitMQ.Client.AmqpTcpEndpoint"/>s.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.AmqpTcpEndpoint.Equals(System.Object)">
            <summary>
            Compares this instance by value (protocol, hostname, port) against another instance.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.AmqpTcpEndpoint.GetHashCode">
            <summary>
            Implementation of hash code depending on protocol, hostname and port,
            to line up with the implementation of <see cref="M:RabbitMQ.Client.AmqpTcpEndpoint.Equals(System.Object)"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.AmqpTcpEndpoint.ToString">
            <summary>
            Returns a URI-like string of the form amqp-PROTOCOL://HOSTNAME:PORTNUMBER.
            </summary>
            <remarks>
            This method is intended mainly for debugging and logging use.
            </remarks>
        </member>
        <member name="T:RabbitMQ.Client.AmqpTimestamp">
            <summary>
            Structure holding an AMQP timestamp, a posix 64-bit time_t.</summary>
            <remarks>
            <para>
            When converting between an AmqpTimestamp and a System.DateTime,
            be aware of the effect of your local timezone. In particular,
            different versions of the .NET framework assume different
            defaults.
            </para>
            <para>
            We have chosen a signed 64-bit time_t here, since the AMQP
            specification through versions 0-9 is silent on whether
            timestamps are signed or unsigned.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.AmqpTimestamp.#ctor(System.Int64)">
            <summary>
            Construct an <see cref="T:RabbitMQ.Client.AmqpTimestamp"/>.
            </summary>
            <param name="unixTime">Unix time.</param>
        </member>
        <member name="P:RabbitMQ.Client.AmqpTimestamp.UnixTime">
            <summary>
            Unix time.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.AmqpTimestamp.ToString">
            <summary>
            Provides a debugger-friendly display.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.AmqpVersion">
            <summary>Represents a version of the AMQP specification.</summary>
            <remarks>
            <para>
            Vendor-specific variants of particular official specification
            versions exist: this class simply represents the AMQP
            specification version, and does not try to represent
            information about any custom variations involved.
            </para>
            <para>
            AMQP version 0-8 peers sometimes advertise themselves as
            version 8-0: for this reason, this class's constructor
            special-cases 8-0, rewriting it at construction time to be 0-8 instead.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.AmqpVersion.#ctor(System.Int32,System.Int32)">
            <summary>
            Construct an <see cref="T:RabbitMQ.Client.AmqpVersion"/> from major and minor version numbers.
            </summary>
            <remarks>
            Converts major=8 and minor=0 into major=0 and minor=8. Please see the class comment.
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.AmqpVersion.Major">
            <summary>
            The AMQP specification major version number.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.AmqpVersion.Minor">
            <summary>
            The AMQP specification minor version number.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.AmqpVersion.Equals(System.Object)">
            <summary>
            Implement value-equality comparison.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.AmqpVersion.GetHashCode">
            <summary>
            Implement hashing as for value-equality.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.AmqpVersion.ToString">
            <summary>
            Format appropriately for display.
            </summary>
            <remarks>
            The specification currently uses "MAJOR-MINOR" as a display format.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.AsyncDefaultBasicConsumer.#ctor">
            <summary>
            Creates a new instance of an <see cref="T:RabbitMQ.Client.DefaultBasicConsumer"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.AsyncDefaultBasicConsumer.#ctor(RabbitMQ.Client.IModel)">
            <summary>
            Constructor which sets the Model property to the given value.
            </summary>
            <param name="model">Common AMQP model.</param>
        </member>
        <member name="P:RabbitMQ.Client.AsyncDefaultBasicConsumer.ConsumerTag">
            <summary>
            Retrieve the consumer tag this consumer is registered as; to be used when discussing this consumer
            with the server, for instance with <see cref="M:RabbitMQ.Client.IModel.BasicCancel(System.String)"/>.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.AsyncDefaultBasicConsumer.IsRunning">
            <summary>
            Returns true while the consumer is registered and expecting deliveries from the broker.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.AsyncDefaultBasicConsumer.ShutdownReason">
            <summary>
            If our <see cref="T:RabbitMQ.Client.IModel"/> shuts down, this property will contain a description of the reason for the
            shutdown. Otherwise it will contain null. See <see cref="T:RabbitMQ.Client.ShutdownEventArgs"/>.
            </summary>
        </member>
        <member name="E:RabbitMQ.Client.AsyncDefaultBasicConsumer.ConsumerCancelled">
            <summary>
            Signalled when the consumer gets cancelled.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.AsyncDefaultBasicConsumer.Model">
            <summary>
            Retrieve the <see cref="T:RabbitMQ.Client.IModel"/> this consumer is associated with,
             for use in acknowledging received messages, for instance.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.AsyncDefaultBasicConsumer.HandleBasicCancel(System.String)">
            <summary>
             Called when the consumer is cancelled for reasons other than by a basicCancel:
             e.g. the queue has been deleted (either by this channel or  by any other channel).
             See <see cref="M:RabbitMQ.Client.AsyncDefaultBasicConsumer.HandleBasicCancelOk(System.String)"/> for notification of consumer cancellation due to basicCancel
            </summary>
            <param name="consumerTag">Consumer tag this consumer is registered.</param>
        </member>
        <member name="M:RabbitMQ.Client.AsyncDefaultBasicConsumer.HandleBasicCancelOk(System.String)">
            <summary>
            Called upon successful deregistration of the consumer from the broker.
            </summary>
            <param name="consumerTag">Consumer tag this consumer is registered.</param>
        </member>
        <member name="M:RabbitMQ.Client.AsyncDefaultBasicConsumer.HandleBasicConsumeOk(System.String)">
            <summary>
            Called upon successful registration of the consumer with the broker.
            </summary>
            <param name="consumerTag">Consumer tag this consumer is registered.</param>
        </member>
        <member name="M:RabbitMQ.Client.AsyncDefaultBasicConsumer.HandleBasicDeliver(System.String,System.UInt64,System.Boolean,System.String,System.String,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>
            Called each time a message arrives for this consumer.
            </summary>
            <remarks>
            Does nothing with the passed in information.
            Note that in particular, some delivered messages may require acknowledgement via <see cref="M:RabbitMQ.Client.IModel.BasicAck(System.UInt64,System.Boolean)"/>.
            The implementation of this method in this class does NOT acknowledge such messages.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.AsyncDefaultBasicConsumer.HandleModelShutdown(System.Object,RabbitMQ.Client.ShutdownEventArgs)">
            <summary>
             Called when the model shuts down.
             </summary>
             <param name="model"> Common AMQP model.</param>
            <param name="reason"> Information about the reason why a particular model, session, or connection was destroyed.</param>
        </member>
        <member name="M:RabbitMQ.Client.AsyncDefaultBasicConsumer.OnCancel">
            <summary>
            Default implementation - overridable in subclasses.</summary>
            <remarks>
            This default implementation simply sets the <see cref="P:RabbitMQ.Client.AsyncDefaultBasicConsumer.IsRunning"/> 
            property to false, and takes no further action.
            </remarks>
        </member>
        <member name="T:RabbitMQ.Client.AuthMechanism">
            <summary>
            A pluggable authentication mechanism.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.AuthMechanism.handleChallenge(System.Byte[],RabbitMQ.Client.IConnectionFactory)">
            <summary>
            Handle one round of challenge-response.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.AuthMechanismFactory.Name">
            <summary>
            The name of the authentication mechanism, as negotiated on the wire.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.AuthMechanismFactory.GetInstance">
            <summary>
            Return a new authentication mechanism implementation.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.BasicGetResult">
            <summary>Represents Basic.GetOk responses from the server.</summary>
            <remarks>
            Basic.Get either returns an instance of this class, or null if a Basic.GetEmpty was received.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.BasicGetResult.#ctor(System.UInt64,System.Boolean,System.String,System.String,System.UInt32,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>
            Sets the new instance's properties from the arguments passed in.
            </summary>
            <param name="deliveryTag">Delivery tag for the message.</param>
            <param name="redelivered">Redelivered flag for the message</param>
            <param name="exchange">The exchange this message was published to.</param>
            <param name="routingKey">Routing key with which the message was published.</param>
            <param name="messageCount">The number of messages pending on the queue, excluding the message being delivered.</param>
            <param name="basicProperties">The Basic-class content header properties for the message.</param>
            <param name="body"></param>
        </member>
        <member name="P:RabbitMQ.Client.BasicGetResult.BasicProperties">
            <summary>
            Retrieves the Basic-class content header properties for this message.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.BasicGetResult.Body">
            <summary>
            Retrieves the body of this message.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.BasicGetResult.DeliveryTag">
            <summary>
            Retrieve the delivery tag for this message. See also <see cref="M:RabbitMQ.Client.IModel.BasicAck(System.UInt64,System.Boolean)"/>.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.BasicGetResult.Exchange">
            <summary>
            Retrieve the exchange this message was published to.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.BasicGetResult.MessageCount">
            <summary>
            Retrieve the number of messages pending on the queue, excluding the message being delivered.
            </summary>
            <remarks>
            Note that this figure is indicative, not reliable, and can
            change arbitrarily as messages are added to the queue and removed by other clients.
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.BasicGetResult.Redelivered">
            <summary>
            Retrieve the redelivered flag for this message.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.BasicGetResult.RoutingKey">
            <summary>
            Retrieve the routing key with which this message was published.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.BinaryTableValue">
             <summary>Wrapper for a byte[]. May appear as values read from
            and written to AMQP field tables.</summary>
             <remarks>
             <para>
             The sole reason for the existence of this class is to permit
             encoding of byte[] as 'x' in AMQP field tables, an extension
             to the specification that is part of the tentative JMS mapping
             implemented by QPid.
             </para>
             <para>
             Instances of this object may be found as values held in
             IDictionary instances returned from
             RabbitMQ.Client.Impl.WireFormatting.ReadTable, e.g. as part of
             IBasicProperties.Headers tables. Likewise, instances may be
             set as values in an IDictionary table to be encoded by
             RabbitMQ.Client.Impl.WireFormatting.WriteTable.
             </para>
             <para>
             When an instance of this class is encoded/decoded, the type
             tag 'x' is used in the on-the-wire representation. The AMQP
             standard type tag 'S' is decoded to a raw byte[], and a raw
             byte[] is encoded as 'S'. Instances of System.String are
             converted to a UTF-8 binary representation, and then encoded
             using tag 'S'. In order to force the use of tag 'x', instances
             of this class must be used.
             </para>
             </remarks>
        </member>
        <member name="M:RabbitMQ.Client.BinaryTableValue.#ctor">
            <summary>
            Creates a new instance of the <see cref="T:RabbitMQ.Client.BinaryTableValue"/> with null for its Bytes property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.BinaryTableValue.#ctor(System.Byte[])">
            <summary>
            Creates a new instance of the <see cref="T:RabbitMQ.Client.BinaryTableValue"/>.
            </summary>
            <param name="bytes">The wrapped byte array, as decoded or as to be encoded.</param>
        </member>
        <member name="P:RabbitMQ.Client.BinaryTableValue.Bytes">
            <summary>
            The wrapped byte array, as decoded or as to be encoded.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.ConnectionFactory">
             <summary>Main entry point to the RabbitMQ .NET AMQP client
            API. Constructs <see cref="T:RabbitMQ.Client.IConnection"/> instances.</summary>
             <remarks>
             <para>
             A simple example of connecting to a broker:
             </para>
             <example><code>
                 ConnectionFactory factory = new ConnectionFactory();
                 //
                 // The next six lines are optional:
                 factory.UserName = ConnectionFactory.DefaultUser;
                 factory.Password = ConnectionFactory.DefaultPass;
                 factory.VirtualHost = ConnectionFactory.DefaultVHost;
                 factory.HostName = hostName;
                 factory.Port     = AmqpTcpEndpoint.UseDefaultPort;
                 //
                 IConnection conn = factory.CreateConnection();
                 //
                 IModel ch = conn.CreateModel();
                 //
                 // ... use ch's IModel methods ...
                 //
                 ch.Close(Constants.ReplySuccess, "Closing the channel");
                 conn.Close(Constants.ReplySuccess, "Closing the connection");
             </code></example>
             <para>
            The same example, written more compactly with AMQP URIs:
             </para>
             <example><code>
                 ConnectionFactory factory = new ConnectionFactory();
                 factory.SetUri("amqp://localhost");
                 IConnection conn = factory.CreateConnection();
                 ...
             </code></example>
             <para>
             Please see also the API overview and tutorial in the User Guide.
             </para>
             <para>
            Note that the Uri property takes a string representation of an
            AMQP URI.  Omitted URI parts will take default values.  The
            host part of the URI cannot be omitted and URIs of the form
            "amqp://foo/" (note the trailling slash) also represent the
            default virtual host.  The latter issue means that virtual
            hosts with an empty name are not addressable. </para></remarks>
        </member>
        <member name="F:RabbitMQ.Client.ConnectionFactory.DefaultChannelMax">
            <summary>
            Default value for the desired maximum channel number, with zero meaning unlimited (value: 0).
            </summary>
            <remarks>PLEASE KEEP THIS MATCHING THE DOC ABOVE.</remarks>
        </member>
        <member name="F:RabbitMQ.Client.ConnectionFactory.DefaultConnectionTimeout">
            <summary>
            Default value for connection attempt timeout, in milliseconds.
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.ConnectionFactory.DefaultFrameMax">
            <summary>
            Default value for the desired maximum frame size, with zero meaning unlimited (value: 0).
            </summary>
            <remarks>PLEASE KEEP THIS MATCHING THE DOC ABOVE.</remarks>
        </member>
        <member name="F:RabbitMQ.Client.ConnectionFactory.DefaultHeartbeat">
            <summary>
            Default value for desired heartbeat interval, in seconds, with zero meaning none (value: 60).
            </summary>
            <remarks>PLEASE KEEP THIS MATCHING THE DOC ABOVE.</remarks>
        </member>
        <member name="F:RabbitMQ.Client.ConnectionFactory.DefaultPass">
            <summary>
            Default password (value: "guest").
            </summary>
            <remarks>PLEASE KEEP THIS MATCHING THE DOC ABOVE.</remarks>
        </member>
        <member name="F:RabbitMQ.Client.ConnectionFactory.DefaultUser">
            <summary>
            Default user name (value: "guest").
            </summary>
            <remarks>PLEASE KEEP THIS MATCHING THE DOC ABOVE.</remarks>
        </member>
        <member name="F:RabbitMQ.Client.ConnectionFactory.DefaultVHost">
            <summary>
            Default virtual host (value: "/").
            </summary>
            <remarks> PLEASE KEEP THIS MATCHING THE DOC ABOVE.</remarks>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.DefaultAmqpUriSslProtocols">
            <summary>
            The default AMQP URI SSL protocols.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.AmqpUriSslProtocols">
            <summary>
            The AMQP URI SSL protocols.
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.ConnectionFactory.DefaultAuthMechanisms">
            <summary>
             Default SASL auth mechanisms to use.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.AuthMechanisms">
            <summary>
             SASL auth mechanisms to use.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.AutomaticRecoveryEnabled">
            <summary>
            Set to false to disable automatic connection recovery.
            Defaults to true.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.DispatchConsumersAsync">
            <summary>
            Set to true will enable a asynchronous consumer dispatcher which is compatible with <see cref="T:RabbitMQ.Client.IAsyncBasicConsumer"/>.
            Defaults to false.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.HostName">
            <summary>The host to connect to.</summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.NetworkRecoveryInterval">
            <summary>
            Amount of time client will wait for before re-trying  to recover connection.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.HandshakeContinuationTimeout">
            <summary>
            Amount of time protocol handshake operations are allowed to take before
            timing out.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.ContinuationTimeout">
            <summary>
            Amount of time protocol  operations (e.g. <code>queue.declare</code>) are allowed to take before
            timing out.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.EndpointResolverFactory">
            <summary>
            Factory function for creating the <see cref="T:RabbitMQ.Client.IEndpointResolver"/>
            used to generate a list of endpoints for the ConnectionFactory
            to try in order.
            The default value creates an instance of the <see cref="T:RabbitMQ.Client.DefaultEndpointResolver"/>
            using the list of endpoints passed in. The DefaultEndpointResolver shuffles the
            provided list each time it is requested.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.Port">
            <summary>
            The port to connect on. <see cref="F:RabbitMQ.Client.AmqpTcpEndpoint.UseDefaultPort"/>
             indicates the default for the protocol should be used.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.Protocol">
            <summary>
            Protocol used, only AMQP 0-9-1 is supported in modern versions.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.RequestedConnectionTimeout">
            <summary>
            Timeout setting for connection attempts (in milliseconds).
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.SocketReadTimeout">
            <summary>
            Timeout setting for socket read operations (in milliseconds).
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.SocketWriteTimeout">
            <summary>
            Timeout setting for socket write operations (in milliseconds).
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.Ssl">
            <summary>
            Ssl options setting.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.TopologyRecoveryEnabled">
            <summary>
            Set to false to make automatic connection recovery not recover topology (exchanges, queues, bindings, etc).
            Defaults to true.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.TaskScheduler">
            <summary>
            Task scheduler connections created by this factory will use when
            dispatching consumer operations, such as message deliveries.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.ConnectionFactory.#ctor">
            <summary>
            Construct a fresh instance, with all fields set to their respective defaults.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.Endpoint">
            <summary>
            Connection endpoint.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.ClientProperties">
            <summary>
            Dictionary of client properties to be sent to the server.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.Password">
            <summary>
            Password to use when authenticating to the server.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.RequestedChannelMax">
            <summary>
            Maximum channel number to ask for.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.RequestedFrameMax">
            <summary>
            Frame-max parameter to ask for (in bytes).
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.RequestedHeartbeat">
            <summary>
            Heartbeat timeout to use when negotiating with the server (in seconds).
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.UseBackgroundThreadsForIO">
            <summary>
            When set to true, background thread will be used for the I/O loop.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.UserName">
            <summary>
            Username to use when authenticating to the server.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.VirtualHost">
            <summary>
            Virtual host to access during this connection.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ConnectionFactory.Uri">
            <summary>
            The uri to use for the connection.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.ConnectionFactory.AuthMechanismFactory(System.Collections.Generic.IList{System.String})">
            <summary>
            Given a list of mechanism names supported by the server, select a preferred mechanism,
             or null if we have none in common.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.ConnectionFactory.CreateConnection">
            <summary>
            Create a connection to one of the endpoints provided by the IEndpointResolver
            returned by the EndpointResolverFactory. By default the configured
            hostname and port are used.
            </summary>
            <exception cref="T:RabbitMQ.Client.Exceptions.BrokerUnreachableException">
            When the configured hostname was not reachable.
            </exception>
        </member>
        <member name="M:RabbitMQ.Client.ConnectionFactory.CreateConnection(System.String)">
            <summary>
            Create a connection to one of the endpoints provided by the IEndpointResolver
            returned by the EndpointResolverFactory. By default the configured
            hostname and port are used.
            </summary>
            <param name="clientProvidedName">
            Application-specific connection name, will be displayed in the management UI
            if RabbitMQ server supports it. This value doesn't have to be unique and cannot
            be used as a connection identifier, e.g. in HTTP API requests.
            This value is supposed to be human-readable.
            </param>
            <exception cref="T:RabbitMQ.Client.Exceptions.BrokerUnreachableException">
            When the configured hostname was not reachable.
            </exception>
        </member>
        <member name="M:RabbitMQ.Client.ConnectionFactory.CreateConnection(System.Collections.Generic.IList{System.String})">
            <summary>
            Create a connection using a list of hostnames using the configured port.
            By default each hostname is tried in a random order until a successful connection is
            found or the list is exhausted using the DefaultEndpointResolver.
            The selection behaviour can be overriden by configuring the EndpointResolverFactory.
            </summary>
            <param name="hostnames">
            List of hostnames to use for the initial
            connection and recovery.
            </param>
            <returns>Open connection</returns>
            <exception cref="T:RabbitMQ.Client.Exceptions.BrokerUnreachableException">
            When no hostname was reachable.
            </exception>
        </member>
        <member name="M:RabbitMQ.Client.ConnectionFactory.CreateConnection(System.Collections.Generic.IList{System.String},System.String)">
            <summary>
            Create a connection using a list of hostnames using the configured port.
            By default each endpoint is tried in a random order until a successful connection is
            found or the list is exhausted.
            The selection behaviour can be overriden by configuring the EndpointResolverFactory.
            </summary>
            <param name="hostnames">
            List of hostnames to use for the initial
            connection and recovery.
            </param>
            <param name="clientProvidedName">
            Application-specific connection name, will be displayed in the management UI
            if RabbitMQ server supports it. This value doesn't have to be unique and cannot
            be used as a connection identifier, e.g. in HTTP API requests.
            This value is supposed to be human-readable.
            </param>
            <returns>Open connection</returns>
            <exception cref="T:RabbitMQ.Client.Exceptions.BrokerUnreachableException">
            When no hostname was reachable.
            </exception>
        </member>
        <member name="M:RabbitMQ.Client.ConnectionFactory.CreateConnection(System.Collections.Generic.IList{RabbitMQ.Client.AmqpTcpEndpoint})">
            <summary>
            Create a connection using a list of endpoints. By default each endpoint will be tried
            in a random order until a successful connection is found or the list is exhausted.
            The selection behaviour can be overriden by configuring the EndpointResolverFactory.
            </summary>
            <param name="endpoints">
            List of endpoints to use for the initial
            connection and recovery.
            </param>
            <returns>Open connection</returns>
            <exception cref="T:RabbitMQ.Client.Exceptions.BrokerUnreachableException">
            When no hostname was reachable.
            </exception>
        </member>
        <member name="M:RabbitMQ.Client.ConnectionFactory.CreateConnection(RabbitMQ.Client.IEndpointResolver,System.String)">
            <summary>
            Create a connection using an IEndpointResolver.
            </summary>
            <param name="endpointResolver">
            The endpointResolver that returns the endpoints to use for the connection attempt.
            </param>
            <param name="clientProvidedName">
            Application-specific connection name, will be displayed in the management UI
            if RabbitMQ server supports it. This value doesn't have to be unique and cannot
            be used as a connection identifier, e.g. in HTTP API requests.
            This value is supposed to be human-readable.
            </param>
            <returns>Open connection</returns>
            <exception cref="T:RabbitMQ.Client.Exceptions.BrokerUnreachableException">
            When no hostname was reachable.
            </exception>
        </member>
        <member name="M:RabbitMQ.Client.ConnectionFactory.UriDecode(System.String)">
            <summary>
             Unescape a string, protecting '+'.
             </summary>
        </member>
        <member name="F:RabbitMQ.Client.ConnectionFactoryBase.SocketFactory">
            <summary>
            Set custom socket options by providing a SocketFactory.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.ConnectionFactoryBase.DefaultSocketFactory(System.Net.Sockets.AddressFamily)">
            <summary>
            Creates a new instance of the <see cref="T:System.Net.Sockets.TcpClient"/>.
            </summary>
            <param name="addressFamily">Specifies the addressing scheme.</param>
            <returns>New instance of a <see cref="T:System.Net.Sockets.TcpClient"/>.</returns>
        </member>
        <member name="T:RabbitMQ.Client.DefaultBasicConsumer">
            <summary>
            Useful default/base implementation of <see cref="T:RabbitMQ.Client.IBasicConsumer"/>.
            Subclass and override <see cref="M:RabbitMQ.Client.DefaultBasicConsumer.HandleBasicDeliver(System.String,System.UInt64,System.Boolean,System.String,System.String,RabbitMQ.Client.IBasicProperties,System.Byte[])"/> in application code.
            </summary>
            <remarks>
            Note that the "Handle*" methods run in the connection's thread!
            Consider using <see cref="T:RabbitMQ.Client.Events.EventingBasicConsumer"/>,  which exposes
            events that can be subscribed to consumer messages.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.DefaultBasicConsumer.#ctor">
            <summary>
            Creates a new instance of an <see cref="T:RabbitMQ.Client.DefaultBasicConsumer"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.DefaultBasicConsumer.#ctor(RabbitMQ.Client.IModel)">
            <summary>
            Constructor which sets the Model property to the given value.
            </summary>
            <param name="model">Common AMQP model.</param>
        </member>
        <member name="P:RabbitMQ.Client.DefaultBasicConsumer.ConsumerTag">
            <summary>
            Retrieve the consumer tag this consumer is registered as; to be used when discussing this consumer
            with the server, for instance with <see cref="M:RabbitMQ.Client.IModel.BasicCancel(System.String)"/>.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.DefaultBasicConsumer.IsRunning">
            <summary>
            Returns true while the consumer is registered and expecting deliveries from the broker.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.DefaultBasicConsumer.ShutdownReason">
            <summary>
            If our <see cref="T:RabbitMQ.Client.IModel"/> shuts down, this property will contain a description of the reason for the
            shutdown. Otherwise it will contain null. See <see cref="T:RabbitMQ.Client.ShutdownEventArgs"/>.
            </summary>
        </member>
        <member name="E:RabbitMQ.Client.DefaultBasicConsumer.ConsumerCancelled">
            <summary>
            Signalled when the consumer gets cancelled.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.DefaultBasicConsumer.Model">
            <summary>
            Retrieve the <see cref="T:RabbitMQ.Client.IModel"/> this consumer is associated with,
             for use in acknowledging received messages, for instance.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.DefaultBasicConsumer.HandleBasicCancel(System.String)">
            <summary>
             Called when the consumer is cancelled for reasons other than by a basicCancel:
             e.g. the queue has been deleted (either by this channel or  by any other channel).
             See <see cref="M:RabbitMQ.Client.DefaultBasicConsumer.HandleBasicCancelOk(System.String)"/> for notification of consumer cancellation due to basicCancel
            </summary>
            <param name="consumerTag">Consumer tag this consumer is registered.</param>
        </member>
        <member name="M:RabbitMQ.Client.DefaultBasicConsumer.HandleBasicCancelOk(System.String)">
            <summary>
            Called upon successful deregistration of the consumer from the broker.
            </summary>
            <param name="consumerTag">Consumer tag this consumer is registered.</param>
        </member>
        <member name="M:RabbitMQ.Client.DefaultBasicConsumer.HandleBasicConsumeOk(System.String)">
            <summary>
            Called upon successful registration of the consumer with the broker.
            </summary>
            <param name="consumerTag">Consumer tag this consumer is registered.</param>
        </member>
        <member name="M:RabbitMQ.Client.DefaultBasicConsumer.HandleBasicDeliver(System.String,System.UInt64,System.Boolean,System.String,System.String,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>
            Called each time a message arrives for this consumer.
            </summary>
            <remarks>
            Does nothing with the passed in information.
            Note that in particular, some delivered messages may require acknowledgement via <see cref="M:RabbitMQ.Client.IModel.BasicAck(System.UInt64,System.Boolean)"/>.
            The implementation of this method in this class does NOT acknowledge such messages.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.DefaultBasicConsumer.HandleModelShutdown(System.Object,RabbitMQ.Client.ShutdownEventArgs)">
            <summary>
             Called when the model shuts down.
             </summary>
             <param name="model"> Common AMQP model.</param>
            <param name="reason"> Information about the reason why a particular model, session, or connection was destroyed.</param>
        </member>
        <member name="M:RabbitMQ.Client.DefaultBasicConsumer.OnCancel">
            <summary>
            Default implementation - overridable in subclasses.</summary>
            <remarks>
            This default implementation simply sets the <see cref="P:RabbitMQ.Client.DefaultBasicConsumer.IsRunning"/> 
            property to false, and takes no further action.
            </remarks>
        </member>
        <member name="T:RabbitMQ.Client.ExchangeType">
            <summary>
            Convenience class providing compile-time names for standard exchange types.
            </summary>
            <remarks>
            Use the static members of this class as values for the
            "exchangeType" arguments for IModel methods such as
            ExchangeDeclare. The broker may be extended with additional
            exchange types that do not appear in this class.
            </remarks>
        </member>
        <member name="F:RabbitMQ.Client.ExchangeType.Direct">
            <summary>
            Exchange type used for AMQP direct exchanges.
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.ExchangeType.Fanout">
            <summary>
            Exchange type used for AMQP fanout exchanges.
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.ExchangeType.Headers">
            <summary>
            Exchange type used for AMQP headers exchanges.
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.ExchangeType.Topic">
            <summary>
            Exchange type used for AMQP topic exchanges.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.ExchangeType.All">
            <summary>
            Retrieve a collection containing all standard exchange types.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.ExternalMechanism.handleChallenge(System.Byte[],RabbitMQ.Client.IConnectionFactory)">
            <summary>
            Handle one round of challenge-response.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ExternalMechanismFactory.Name">
            <summary>
            The name of the authentication mechanism, as negotiated on the wire.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.ExternalMechanismFactory.GetInstance">
            <summary>
            Return a new authentication mechanism implementation.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Headers">
            <summary>
            Convenience class providing compile-time names for standard headers.
            </summary>
            <remarks>
            Use the static members of this class as headers for the
            arguments for Queue and Exchange declaration or Consumer creation. 
            The broker may be extended with additional
            headers that do not appear in this class.
            </remarks>
        </member>
        <member name="F:RabbitMQ.Client.Headers.XMaxPriority">
            <summary>
            x-max-priority header
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Headers.XMaxLength">
            <summary>
            x-max-length header
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Headers.XMaxLengthInBytes">
            <summary>
            x-max-length-bytes header
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Headers.XDeadLetterExchange">
            <summary>
            x-dead-letter-exchange header
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Headers.XDeadLetterRoutingKey">
            <summary>
            x-dead-letter-routing-key header
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Headers.XMessageTTL">
            <summary>
            x-message-ttl header
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Headers.XExpires">
            <summary>
            x-expires header
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Headers.AlternateExchange">
            <summary>
            alternate-exchange header
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Headers.XPriority">
            <summary>
            x-priority header
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Headers.XQueueMode">
            <summary>
            x-queue-mode header.
            Available modes: "default" and "lazy"
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Headers.XQueueType">
            <summary>
            x-queue-type header.
            Available types: "quorum" and "classic"(default)
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Headers.XQuorumInitialGroupSize">
            <summary>
            x-quorum-initial-group-size header.
            Use to control the number of quorum queue members
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Headers.XSingleActiveConsumer">
            <summary>
            x-single-active-consumer header.
            Available modes: true and false(default).
            Allows to have only one consumer at a time consuming from a queue
            and to fail over to another registered consumer in case the active one is cancelled or dies
             </summary>
        </member>
        <member name="F:RabbitMQ.Client.Headers.XOverflow">
            <summary>
            x-overflow header.
            Available strategies: "reject-publish" and "drop-head"(default).
            Allows to configure strategy when <see cref="F:RabbitMQ.Client.Headers.XMaxLength"/> or <see cref="F:RabbitMQ.Client.Headers.XMaxLengthInBytes"/> hits limits
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IAsyncBasicConsumer.Model">
            <summary>
            Retrieve the <see cref="T:RabbitMQ.Client.IModel"/> this consumer is associated with,
             for use in acknowledging received messages, for instance.
            </summary>
        </member>
        <member name="E:RabbitMQ.Client.IAsyncBasicConsumer.ConsumerCancelled">
            <summary>
            Signalled when the consumer gets cancelled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IAsyncBasicConsumer.HandleBasicCancel(System.String)">
            <summary>
             Called when the consumer is cancelled for reasons other than by a basicCancel:
             e.g. the queue has been deleted (either by this channel or  by any other channel).
             See <see cref="M:RabbitMQ.Client.IAsyncBasicConsumer.HandleBasicCancelOk(System.String)"/> for notification of consumer cancellation due to basicCancel
            </summary>
            <param name="consumerTag">Consumer tag this consumer is registered.</param>
        </member>
        <member name="M:RabbitMQ.Client.IAsyncBasicConsumer.HandleBasicCancelOk(System.String)">
            <summary>
            Called upon successful deregistration of the consumer from the broker.
            </summary>
            <param name="consumerTag">Consumer tag this consumer is registered.</param>
        </member>
        <member name="M:RabbitMQ.Client.IAsyncBasicConsumer.HandleBasicConsumeOk(System.String)">
            <summary>
            Called upon successful registration of the consumer with the broker.
            </summary>
            <param name="consumerTag">Consumer tag this consumer is registered.</param>
        </member>
        <member name="M:RabbitMQ.Client.IAsyncBasicConsumer.HandleBasicDeliver(System.String,System.UInt64,System.Boolean,System.String,System.String,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>
            Called each time a message arrives for this consumer.
            </summary>
            <remarks>
            Does nothing with the passed in information.
            Note that in particular, some delivered messages may require acknowledgement via <see cref="M:RabbitMQ.Client.IModel.BasicAck(System.UInt64,System.Boolean)"/>.
            The implementation of this method in this class does NOT acknowledge such messages.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IAsyncBasicConsumer.HandleModelShutdown(System.Object,RabbitMQ.Client.ShutdownEventArgs)">
            <summary>
             Called when the model shuts down.
             </summary>
             <param name="model"> Common AMQP model.</param>
            <param name="reason"> Information about the reason why a particular model, session, or connection was destroyed.</param>
        </member>
        <member name="T:RabbitMQ.Client.IBasicConsumer">
             <summary>Consumer interface. Used to
            receive messages from a queue by subscription.</summary>
             <remarks>
             <para>
             See IModel.BasicConsume, IModel.BasicCancel.
             </para>
             <para>
             Note that the "Handle*" methods run in the connection's
             thread! Consider using QueueingBasicConsumer, which uses a
             SharedQueue instance to safely pass received messages across
             to user threads.
             </para>
             </remarks>
        </member>
        <member name="P:RabbitMQ.Client.IBasicConsumer.Model">
            <summary>
            Retrieve the <see cref="T:RabbitMQ.Client.IModel"/> this consumer is associated with,
             for use in acknowledging received messages, for instance.
            </summary>
        </member>
        <member name="E:RabbitMQ.Client.IBasicConsumer.ConsumerCancelled">
            <summary>
            Signalled when the consumer gets cancelled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicConsumer.HandleBasicCancel(System.String)">
            <summary>
             Called when the consumer is cancelled for reasons other than by a basicCancel:
             e.g. the queue has been deleted (either by this channel or  by any other channel).
             See <see cref="M:RabbitMQ.Client.IBasicConsumer.HandleBasicCancelOk(System.String)"/> for notification of consumer cancellation due to basicCancel
            </summary>
            <param name="consumerTag">Consumer tag this consumer is registered.</param>
        </member>
        <member name="M:RabbitMQ.Client.IBasicConsumer.HandleBasicCancelOk(System.String)">
            <summary>
            Called upon successful deregistration of the consumer from the broker.
            </summary>
            <param name="consumerTag">Consumer tag this consumer is registered.</param>
        </member>
        <member name="M:RabbitMQ.Client.IBasicConsumer.HandleBasicConsumeOk(System.String)">
            <summary>
            Called upon successful registration of the consumer with the broker.
            </summary>
            <param name="consumerTag">Consumer tag this consumer is registered.</param>
        </member>
        <member name="M:RabbitMQ.Client.IBasicConsumer.HandleBasicDeliver(System.String,System.UInt64,System.Boolean,System.String,System.String,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>
            Called each time a message arrives for this consumer.
            </summary>
            <remarks>
            Does nothing with the passed in information.
            Note that in particular, some delivered messages may require acknowledgement via <see cref="M:RabbitMQ.Client.IModel.BasicAck(System.UInt64,System.Boolean)"/>.
            The implementation of this method in this class does NOT acknowledge such messages.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IBasicConsumer.HandleModelShutdown(System.Object,RabbitMQ.Client.ShutdownEventArgs)">
            <summary>
             Called when the model shuts down.
             </summary>
             <param name="model"> Common AMQP model.</param>
            <param name="reason"> Information about the reason why a particular model, session, or connection was destroyed.</param>
        </member>
        <member name="T:RabbitMQ.Client.IBasicProperties">
            <summary>Common AMQP Basic content-class headers interface,
            spanning the union of the functionality offered by versions
            0-8, 0-8qpid, 0-9 and 0-9-1 of AMQP.</summary>
            <remarks>
            <para>
            The specification code generator provides
            protocol-version-specific implementations of this interface. To
            obtain an implementation of this interface in a
            protocol-version-neutral way, use <see cref="M:RabbitMQ.Client.IModel.CreateBasicProperties"/>.
            </para>
            <para>
            Each property is readable, writable and clearable: a cleared
            property will not be transmitted over the wire. Properties on a
            fresh instance are clear by default.
            </para>
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.IBasicProperties.AppId">
            <summary>
            Application Id.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IBasicProperties.ClusterId">
            <summary>
            Intra-cluster routing identifier (cluster id is deprecated in AMQP 0-9-1).
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IBasicProperties.ContentEncoding">
            <summary>
            MIME content encoding.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IBasicProperties.ContentType">
            <summary>
            MIME content type.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IBasicProperties.CorrelationId">
            <summary>
            Application correlation identifier.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IBasicProperties.DeliveryMode">
            <summary>
            Non-persistent (1) or persistent (2).
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IBasicProperties.Expiration">
            <summary>
            Message expiration specification.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IBasicProperties.Headers">
            <summary>
            Message header field table. Is of type <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IBasicProperties.MessageId">
            <summary>
            Application message Id.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IBasicProperties.Persistent">
            <summary>
            Sets <see cref="P:RabbitMQ.Client.IBasicProperties.DeliveryMode"/> to either persistent (2) or non-persistent (1).
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IBasicProperties.Priority">
            <summary>
            Message priority, 0 to 9.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IBasicProperties.ReplyTo">
            <summary>
            Destination to reply to.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IBasicProperties.ReplyToAddress">
            <summary>
            Convenience property; parses <see cref="P:RabbitMQ.Client.IBasicProperties.ReplyTo"/> property using <see cref="M:RabbitMQ.Client.PublicationAddress.Parse(System.String)"/>,
            and serializes it using <see cref="M:RabbitMQ.Client.PublicationAddress.ToString"/>.
            Returns null if <see cref="P:RabbitMQ.Client.IBasicProperties.ReplyTo"/> property cannot be parsed by <see cref="M:RabbitMQ.Client.PublicationAddress.Parse(System.String)"/>.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IBasicProperties.Timestamp">
            <summary>
            Message timestamp.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IBasicProperties.Type">
            <summary>
            Message type name.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IBasicProperties.UserId">
            <summary>
            User Id.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.ClearAppId">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IBasicProperties.AppId"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.ClearClusterId">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IBasicProperties.ClusterId"/> property (cluster id is deprecated in AMQP 0-9-1).
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.ClearContentEncoding">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IBasicProperties.ContentEncoding"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.ClearContentType">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IBasicProperties.ContentType"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.ClearCorrelationId">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IBasicProperties.CorrelationId"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.ClearDeliveryMode">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IBasicProperties.DeliveryMode"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.ClearExpiration">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IBasicProperties.Expiration"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.ClearHeaders">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IBasicProperties.Headers"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.ClearMessageId">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IBasicProperties.MessageId"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.ClearPriority">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IBasicProperties.Priority"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.ClearReplyTo">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IBasicProperties.ReplyTo"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.ClearTimestamp">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IBasicProperties.Timestamp"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.ClearType">
            <summary>
            Clear the Type property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.ClearUserId">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IBasicProperties.UserId"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.IsAppIdPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IBasicProperties.AppId"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.IsClusterIdPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IBasicProperties.ClusterId"/> property is present (cluster id is deprecated in AMQP 0-9-1).
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.IsContentEncodingPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IBasicProperties.ContentEncoding"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.IsContentTypePresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IBasicProperties.ContentType"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.IsCorrelationIdPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IBasicProperties.CorrelationId"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.IsDeliveryModePresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IBasicProperties.DeliveryMode"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.IsExpirationPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IBasicProperties.Expiration"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.IsHeadersPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IBasicProperties.Headers"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.IsMessageIdPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IBasicProperties.MessageId"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.IsPriorityPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IBasicProperties.Priority"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.IsReplyToPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IBasicProperties.ReplyTo"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.IsTimestampPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IBasicProperties.Timestamp"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.IsTypePresent">
            <summary>
            Returns true if the Type property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.IsUserIdPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IBasicProperties.UserId"/> UserId property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IBasicProperties.SetPersistent(System.Boolean)">
            <summary>Sets <see cref="P:RabbitMQ.Client.IBasicProperties.DeliveryMode"/> to either persistent (2) or non-persistent (1).</summary>
            <remarks>
            <para>
            The numbers 1 and 2 for delivery mode are "magic" in that
            they appear in the AMQP 0-8 and 0-9 specifications as part
            of the definition of the DeliveryMode Basic-class property,
            without being defined as named constants.
            </para>
            <para>
            Calling this method causes <see cref="P:RabbitMQ.Client.IBasicProperties.DeliveryMode"/> to take on a  value.
            In order to reset <see cref="P:RabbitMQ.Client.IBasicProperties.DeliveryMode"/> to the default empty condition, call <see cref="M:RabbitMQ.Client.IBasicProperties.ClearDeliveryMode"/> .
            </para>
            </remarks>
        </member>
        <member name="T:RabbitMQ.Client.IConnection">
            <summary>
            Main interface to an AMQP connection.
            </summary>
            <remarks>
            <para>
            Instances of <see cref="T:RabbitMQ.Client.IConnection"/> are used to create fresh
            sessions/channels. The <see cref="T:RabbitMQ.Client.ConnectionFactory"/> class is used to
            construct <see cref="T:RabbitMQ.Client.IConnection"/> instances.
             Please see the documentation for ConnectionFactory for an example of usage.
             Alternatively, an API tutorial can be found in the User Guide.
            </para>
            <para>
            Extends the <see cref="T:System.IDisposable"/> interface, so that the "using"
            statement can be used to scope the lifetime of a channel when
            appropriate.
            </para>
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.IConnection.AutoClose">
            <summary>
            If true, will close the whole connection as soon as there are no channels open on it;
            if false, manual connection closure will be required.
            </summary>
            <remarks>
            DON'T set AutoClose to true before opening the first
            channel, because the connection will be immediately closed if you do!
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.IConnection.ChannelMax">
            <summary>
            The maximum channel number this connection supports (0 if unlimited).
            Usable channel numbers range from 1 to this number, inclusive.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnection.ClientProperties">
            <summary>
            A copy of the client properties that has been sent to the server.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnection.CloseReason">
            <summary>
            Returns null if the connection is still in a state
            where it can be used, or the cause of its closure otherwise.
            </summary>
            <remarks>
            <para>
            Applications should use the ConnectionShutdown event to
            avoid race conditions. The scenario to avoid is checking
            <see cref="P:RabbitMQ.Client.IConnection.CloseReason"/>, seeing it is null (meaning the <see cref="T:RabbitMQ.Client.IConnection"/>
            was available for use at the time of the check), and
            interpreting this mistakenly as a guarantee that the
            <see cref="T:RabbitMQ.Client.IConnection"/> will remain usable for a time. Instead, the
            operation of interest should simply be attempted: if the
            <see cref="T:RabbitMQ.Client.IConnection"/> is not in a usable state, an exception will be
            thrown (most likely <see cref="T:RabbitMQ.Client.Exceptions.OperationInterruptedException"/>, but may
            vary depending on the particular operation being attempted).
            </para>
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.IConnection.Endpoint">
            <summary>
            Retrieve the endpoint this connection is connected to.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnection.FrameMax">
            <summary>
            The maximum frame size this connection supports (0 if unlimited).
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnection.Heartbeat">
            <summary>
            The current heartbeat setting for this connection (0 for disabled), in seconds.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnection.IsOpen">
            <summary>
            Returns true if the connection is still in a state where it can be used.
            Identical to checking if <see cref="P:RabbitMQ.Client.IConnection.CloseReason"/> equal null.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnection.KnownHosts">
            <summary>
            Returns the known hosts that came back from the
            broker in the connection.open-ok method at connection
            startup time. Null until the connection is completely open and ready for use.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnection.Protocol">
            <summary>
            The <see cref="T:RabbitMQ.Client.IProtocol"/> this connection is using to communicate with its peer.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnection.ServerProperties">
            <summary>
            A dictionary of the server properties sent by the server while establishing the connection.
            This typically includes the product name and version of the server.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnection.ShutdownReport">
            <summary>
            Returns the list of <see cref="T:RabbitMQ.Client.ShutdownReportEntry"/> objects that contain information
            about any errors reported while closing the connection in the order they appeared
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnection.ClientProvidedName">
            <summary>
            Application-specific connection name, will be displayed in the management UI
            if RabbitMQ server supports it. This value doesn't have to be unique and cannot
            be used as a connection identifier, e.g. in HTTP API requests.
            This value is supposed to be human-readable.
            </summary>
        </member>
        <member name="E:RabbitMQ.Client.IConnection.CallbackException">
            <summary>
            Signalled when an exception occurs in a callback invoked by the connection.
            </summary>
            <remarks>
            This event is signalled when a ConnectionShutdown handler
            throws an exception. If, in future, more events appear on
            <see cref="T:RabbitMQ.Client.IConnection"/>, then this event will be signalled whenever one
            of those event handlers throws an exception, as well.
            </remarks>
        </member>
        <member name="E:RabbitMQ.Client.IConnection.ConnectionShutdown">
            <summary>
            Raised when the connection is destroyed.
            </summary>
            <remarks>
            If the connection is already destroyed at the time an
            event handler is added to this event, the event handler
            will be fired immediately.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IConnection.Abort">
             <summary>
             Abort this connection and all its channels.
             </summary>
             <remarks>
             Note that all active channels, sessions, and models will be closed if this method is called.
             In comparison to normal <see cref="M:RabbitMQ.Client.IConnection.Close"/> method, <see cref="M:RabbitMQ.Client.IConnection.Abort"/> will not throw
             <see cref="T:System.IO.IOException"/> during closing connection.
            This method waits infinitely for the in-progress close operation to complete.
             </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IConnection.Abort(System.UInt16,System.String)">
            <summary>
            Abort this connection and all its channels.
            </summary>
            <remarks>
            The method behaves in the same way as <see cref="M:RabbitMQ.Client.IConnection.Abort"/>, with the only
            difference that the connection is closed with the given connection close code and message.
            <para>
            The close code (See under "Reply Codes" in the AMQP 0-9-1 specification)
            </para>
            <para>
            A message indicating the reason for closing the connection
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IConnection.Abort(System.Int32)">
            <summary>
            Abort this connection and all its channels and wait with a
            timeout for all the in-progress close operations to complete.
            </summary>
            <remarks>
            This method, behaves in a similar way as method <see cref="M:RabbitMQ.Client.IConnection.Abort"/> with the
            only difference that it explictly specifies a timeout given
            for all the in-progress close operations to complete.
            If timeout is reached and the close operations haven't finished, then socket is forced to close.
            <para>
            The timeout value is in milliseconds.
            To wait infinitely for the close operations to complete use <see cref="F:System.Threading.Timeout.Infinite"/>.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IConnection.Abort(System.UInt16,System.String,System.Int32)">
            <summary>
            Abort this connection and all its channels and wait with a
            timeout for all the in-progress close operations to complete.
            </summary>
            <remarks>
            The method behaves in the same way as <see cref="M:RabbitMQ.Client.IConnection.Abort(System.Int32)"/>, with the only
            difference that the connection is closed with the given connection close code and message.
            <para>
            The close code (See under "Reply Codes" in the AMQP 0-9-1 specification).
            </para>
            <para>
            A message indicating the reason for closing the connection.
            </para>
            <para>
            Operation timeout in milliseconds.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IConnection.Close">
            <summary>
            Close this connection and all its channels.
            </summary>
            <remarks>
            Note that all active channels, sessions, and models will be
            closed if this method is called. It will wait for the in-progress
            close operation to complete. This method will not return to the caller
            until the shutdown is complete. If the connection is already closed
            (or closing), then this method will do nothing.
            It can also throw <see cref="T:System.IO.IOException"/> when socket was closed unexpectedly.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IConnection.Close(System.UInt16,System.String)">
            <summary>
            Close this connection and all its channels.
            </summary>
            <remarks>
            The method behaves in the same way as <see cref="M:RabbitMQ.Client.IConnection.Close"/>, with the only
            difference that the connection is closed with the given connection close code and message.
            <para>
            The close code (See under "Reply Codes" in the AMQP specification).
            </para>
            <para>
            A message indicating the reason for closing the connection.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IConnection.Close(System.Int32)">
            <summary>
            Close this connection and all its channels
            and wait with a timeout for all the in-progress close operations to complete.
            </summary>
            <remarks>
            Note that all active channels, sessions, and models will be
            closed if this method is called. It will wait for the in-progress
            close operation to complete with a timeout. If the connection is
            already closed (or closing), then this method will do nothing.
            It can also throw <see cref="T:System.IO.IOException"/> when socket was closed unexpectedly.
            If timeout is reached and the close operations haven't finished, then socket is forced to close.
            <para>
            The timeout value is in milliseconds.
            To wait infinitely for the close operations to complete use <see cref="F:System.Threading.Timeout.Infinite"/>.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IConnection.Close(System.UInt16,System.String,System.Int32)">
            <summary>
            Close this connection and all its channels
            and wait with a timeout for all the in-progress close operations to complete.
            </summary>
            <remarks>
            The method behaves in the same way as <see cref="M:RabbitMQ.Client.IConnection.Close(System.Int32)"/>, with the only
            difference that the connection is closed with the given connection close code and message.
            <para>
            The close code (See under "Reply Codes" in the AMQP 0-9-1 specification).
            </para>
            <para>
            A message indicating the reason for closing the connection.
            </para>
            <para>
            Operation timeout in milliseconds.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IConnection.CreateModel">
            <summary>
            Create and return a fresh channel, session, and model.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IConnection.HandleConnectionBlocked(System.String)">
            <summary>
            Handle incoming Connection.Blocked methods.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IConnection.HandleConnectionUnblocked">
            <summary>
            Handle incoming Connection.Unblocked methods.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnectionFactory.ClientProperties">
            <summary>
            Dictionary of client properties to be sent to the server.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnectionFactory.Password">
            <summary>
            Password to use when authenticating to the server.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnectionFactory.RequestedChannelMax">
            <summary>
            Maximum channel number to ask for.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnectionFactory.RequestedFrameMax">
            <summary>
            Frame-max parameter to ask for (in bytes).
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnectionFactory.RequestedHeartbeat">
            <summary>
            Heartbeat setting to request (in seconds).
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnectionFactory.UseBackgroundThreadsForIO">
            <summary>
            When set to true, background threads will be used for I/O and heartbeats.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnectionFactory.UserName">
            <summary>
            Username to use when authenticating to the server.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnectionFactory.VirtualHost">
            <summary>
            Virtual host to access during this connection.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnectionFactory.Uri">
            <summary>
            Sets or gets the AMQP Uri to be used for connections.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IConnectionFactory.AuthMechanismFactory(System.Collections.Generic.IList{System.String})">
            <summary>
            Given a list of mechanism names supported by the server, select a preferred mechanism,
            or null if we have none in common.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IConnectionFactory.CreateConnection">
            <summary>
            Create a connection to the specified endpoint.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IConnectionFactory.CreateConnection(System.String)">
            <summary>
            Create a connection to the specified endpoint.
            </summary>
            <param name="clientProvidedName">
            Application-specific connection name, will be displayed in the management UI
            if RabbitMQ server supports it. This value doesn't have to be unique and cannot
            be used as a connection identifier, e.g. in HTTP API requests.
            This value is supposed to be human-readable.
            </param>
            <returns></returns>
        </member>
        <member name="M:RabbitMQ.Client.IConnectionFactory.CreateConnection(System.Collections.Generic.IList{System.String})">
            <summary>
            Connects to the first reachable hostname from the list.
            </summary>
            <param name="hostnames">List of host names to use</param>
            <returns></returns>
        </member>
        <member name="M:RabbitMQ.Client.IConnectionFactory.CreateConnection(System.Collections.Generic.IList{System.String},System.String)">
            <summary>
            Connects to the first reachable hostname from the list.
            </summary>
            <param name="hostnames">List of host names to use</param>
            <param name="clientProvidedName">
            Application-specific connection name, will be displayed in the management UI
            if RabbitMQ server supports it. This value doesn't have to be unique and cannot
            be used as a connection identifier, e.g. in HTTP API requests.
            This value is supposed to be human-readable.
            </param>
            <returns></returns>
        </member>
        <member name="M:RabbitMQ.Client.IConnectionFactory.CreateConnection(System.Collections.Generic.IList{RabbitMQ.Client.AmqpTcpEndpoint})">
            <summary>
            Create a connection using a list of endpoints.
            The selection behaviour can be overriden by configuring the EndpointResolverFactory.
            </summary>
            <param name="endpoints">
            List of endpoints to use for the initial
            connection and recovery.
            </param>
            <returns>Open connection</returns>
            <exception cref="T:RabbitMQ.Client.Exceptions.BrokerUnreachableException">
            When no hostname was reachable.
            </exception>
        </member>
        <member name="P:RabbitMQ.Client.IConnectionFactory.TaskScheduler">
             <summary>
             Advanced option.
            
             What task scheduler should consumer dispatcher use.
             </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnectionFactory.HandshakeContinuationTimeout">
            <summary>
            Amount of time protocol handshake operations are allowed to take before
            timing out.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IConnectionFactory.ContinuationTimeout">
            <summary>
            Amount of time protocol  operations (e.g. <code>queue.declare</code>) are allowed to take before
            timing out.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.IContentHeader">
            <summary>
            A decoded AMQP content header frame.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IContentHeader.ProtocolClassId">
            <summary>
            Retrieve the AMQP class ID of this content header.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IContentHeader.ProtocolClassName">
            <summary>
            Retrieve the AMQP class name of this content header.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IEndpointResolver.All">
            <summary>
            Return all AmqpTcpEndpoints in the order they should be tried.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.IMethod">
            <summary>
            A decoded AMQP method frame.
            </summary>
            <remarks>
            <para>
            AMQP methods can be RPC requests, RPC responses, exceptions
            (ChannelClose, ConnectionClose), or one-way asynchronous
            messages. Currently this information is not recorded in their
            type or interface: it is implicit in the way the method is
            used, and the way it is defined in the AMQP specification. A
            future revision of the RabbitMQ .NET client library may extend
            the IMethod interface to represent this information
            explicitly.
            </para>
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.IMethod.ProtocolClassId">
            <summary>
            Retrieves the class ID number of this method, as defined in the AMQP specification XML.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IMethod.ProtocolMethodId">
            <summary>
            Retrieves the method ID number of this method, as defined in the AMQP specification XML.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IMethod.ProtocolMethodName">
            <summary>
            Retrieves the name of this method - for debugging use.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.IModel">
            <summary>
            Common AMQP model, spanning the union of the
            functionality offered by versions 0-8, 0-8qpid, 0-9 and 0-9-1 of AMQP.
            </summary>
            <remarks>
            Extends the <see cref="T:System.IDisposable"/> interface, so that the "using"
            statement can be used to scope the lifetime of a channel when appropriate.
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.IModel.ChannelNumber">
            <summary>
            Channel number, unique per connections.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IModel.CloseReason">
            <summary>
            Returns null if the session is still in a state where it can be used,
            or the cause of its closure otherwise.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IModel.DefaultConsumer">
             <summary>Signalled when an unexpected message is delivered
            
             Under certain circumstances it is possible for a channel to receive a
             message delivery which does not match any consumer which is currently
             set up via basicConsume(). This will occur after the following sequence
             of events:
            
             ctag = basicConsume(queue, consumer); // i.e. with explicit acks
             // some deliveries take place but are not acked
             basicCancel(ctag);
             basicRecover(false);
            
             Since requeue is specified to be false in the basicRecover, the spec
             states that the message must be redelivered to "the original recipient"
             - i.e. the same channel / consumer-tag. But the consumer is no longer
             active.
            
             In these circumstances, you can register a default consumer to handle
             such deliveries. If no default consumer is registered an
             InvalidOperationException will be thrown when such a delivery arrives.
            
             Most people will not need to use this.</summary>
        </member>
        <member name="P:RabbitMQ.Client.IModel.IsClosed">
            <summary>
            Returns true if the model is no longer in a state where it can be used.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IModel.IsOpen">
            <summary>
            Returns true if the model is still in a state where it can be used.
            Identical to checking if <see cref="P:RabbitMQ.Client.IModel.CloseReason"/> equals null.</summary>
        </member>
        <member name="P:RabbitMQ.Client.IModel.NextPublishSeqNo">
            <summary>
            When in confirm mode, return the sequence number of the next message to be published.
            </summary>
        </member>
        <member name="E:RabbitMQ.Client.IModel.BasicAcks">
            <summary>
            Signalled when a Basic.Ack command arrives from the broker.
            </summary>
        </member>
        <member name="E:RabbitMQ.Client.IModel.BasicNacks">
            <summary>
            Signalled when a Basic.Nack command arrives from the broker.
            </summary>
        </member>
        <member name="E:RabbitMQ.Client.IModel.BasicRecoverOk">
            <summary>
            All messages received before this fires that haven't been ack'ed will be redelivered.
            All messages received afterwards won't be.
            </summary>
            <remarks>
            Handlers for this event are invoked by the connection thread.
            It is sometimes useful to allow that thread to know that a recover-ok
            has been received, rather than the thread that invoked <see cref="M:RabbitMQ.Client.IModel.BasicRecover(System.Boolean)"/>.
            </remarks>
        </member>
        <member name="E:RabbitMQ.Client.IModel.BasicReturn">
            <summary>
            Signalled when a Basic.Return command arrives from the broker.
            </summary>
        </member>
        <member name="E:RabbitMQ.Client.IModel.CallbackException">
             <summary>
             Signalled when an exception occurs in a callback invoked by the model.
            
             Examples of cases where this event will be signalled
             include exceptions thrown in <see cref="T:RabbitMQ.Client.IBasicConsumer"/> methods, or
             exceptions thrown in <see cref="E:RabbitMQ.Client.IModel.ModelShutdown"/> delegates etc.
             </summary>
        </member>
        <member name="E:RabbitMQ.Client.IModel.ModelShutdown">
            <summary>
            Notifies the destruction of the model.
            </summary>
            <remarks>
            If the model is already destroyed at the time an event
            handler is added to this event, the event handler will be fired immediately.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.Abort">
            <summary>
            Abort this session.
            </summary>
            <remarks>
            If the session is already closed (or closing), then this
            method does nothing but wait for the in-progress close
            operation to complete. This method will not return to the
            caller until the shutdown is complete.
            In comparison to normal <see cref="M:RabbitMQ.Client.IModel.Close"/> method, <see cref="M:RabbitMQ.Client.IModel.Abort"/> will not throw
            <see cref="T:RabbitMQ.Client.Exceptions.AlreadyClosedException"/> or <see cref="T:System.IO.IOException"/> or any other <see cref="T:System.Exception"/> during closing model.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.Abort(System.UInt16,System.String)">
            <summary>
            Abort this session.
            </summary>
            <remarks>
            The method behaves in the same way as <see cref="M:RabbitMQ.Client.IModel.Abort"/>, with the only
            difference that the model is closed with the given model close code and message.
            <para>
            The close code (See under "Reply Codes" in the AMQP specification)
            </para>
            <para>
            A message indicating the reason for closing the model
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.BasicAck(System.UInt64,System.Boolean)">
            <summary>
            Acknowledge one or more delivered message(s).
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.BasicCancel(System.String)">
            <summary>
            Delete a Basic content-class consumer.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.BasicConsume(System.String,System.Boolean,System.String,System.Boolean,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object},RabbitMQ.Client.IBasicConsumer)">
            <summary>Start a Basic content-class consumer.</summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.BasicGet(System.String,System.Boolean)">
            <summary>
            Retrieve an individual message, if
            one is available; returns null if the server answers that
            no messages are currently available. See also <see cref="M:RabbitMQ.Client.IModel.BasicAck(System.UInt64,System.Boolean)"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.BasicNack(System.UInt64,System.Boolean,System.Boolean)">
            <summary>Reject one or more delivered message(s).</summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.BasicPublish(System.String,System.String,System.Boolean,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>
            Publishes a message.
            </summary>
            <remarks>
              <para>
                Routing key must be shorter than 255 bytes.
              </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.BasicQos(System.UInt32,System.UInt16,System.Boolean)">
            <summary>
            Configures QoS parameters of the Basic content-class.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.BasicRecover(System.Boolean)">
            <summary>
            Indicates that a consumer has recovered.
            Deprecated. Should not be used.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.BasicRecoverAsync(System.Boolean)">
            <summary>
            Indicates that a consumer has recovered.
            Deprecated. Should not be used.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.BasicReject(System.UInt64,System.Boolean)">
            <summary> Reject a delivered message.</summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.Close">
            <summary>Close this session.</summary>
            <remarks>
            If the session is already closed (or closing), then this
            method does nothing but wait for the in-progress close
            operation to complete. This method will not return to the
            caller until the shutdown is complete.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.Close(System.UInt16,System.String)">
            <summary>Close this session.</summary>
            <remarks>
            The method behaves in the same way as Close(), with the only
            difference that the model is closed with the given model
            close code and message.
            <para>
            The close code (See under "Reply Codes" in the AMQP specification)
            </para>
            <para>
            A message indicating the reason for closing the model
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.ConfirmSelect">
            <summary>
            Enable publisher acknowledgements.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.CreateBasicPublishBatch">
            <summary>
             Creates a BasicPublishBatch instance
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.CreateBasicProperties">
            <summary>
            Construct a completely empty content header for use with the Basic content class.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.ExchangeBind(System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Bind an exchange to an exchange.
            </summary>
            <remarks>
              <para>
                Routing key must be shorter than 255 bytes.
              </para>
            </remarks>        
        </member>
        <member name="M:RabbitMQ.Client.IModel.ExchangeBindNoWait(System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Like ExchangeBind but sets nowait to true.
            </summary>
            <remarks>
              <para>
                Routing key must be shorter than 255 bytes.
              </para>
            </remarks>        
        </member>
        <member name="M:RabbitMQ.Client.IModel.ExchangeDeclare(System.String,System.String,System.Boolean,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>Declare an exchange.</summary>
            <remarks>
            The exchange is declared non-passive and non-internal.
            The "nowait" option is not exercised.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.ExchangeDeclareNoWait(System.String,System.String,System.Boolean,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Same as ExchangeDeclare but sets nowait to true and returns void (as there
            will be no response from the server).
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.ExchangeDeclarePassive(System.String)">
            <summary>
            Do a passive exchange declaration.
            </summary>
            <remarks>
            This method performs a "passive declare" on an exchange,
            which verifies whether .
            It will do nothing if the exchange already exists and result
            in a channel-level protocol exception (channel closure) if not.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.ExchangeDelete(System.String,System.Boolean)">
            <summary>
            Delete an exchange.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.ExchangeDeleteNoWait(System.String,System.Boolean)">
            <summary>
            Like ExchangeDelete but sets nowait to true.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.ExchangeUnbind(System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Unbind an exchange from an exchange.
            </summary>
            <remarks>
            Routing key must be shorter than 255 bytes.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.ExchangeUnbindNoWait(System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Like ExchangeUnbind but sets nowait to true.
            </summary>
            <remarks>
              <para>
                Routing key must be shorter than 255 bytes.
              </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.QueueBind(System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Bind a queue to an exchange.
            </summary>
            <remarks>
              <para>
                Routing key must be shorter than 255 bytes.
              </para>
            </remarks>        
        </member>
        <member name="M:RabbitMQ.Client.IModel.QueueBindNoWait(System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>Same as QueueBind but sets nowait parameter to true.</summary>
            <remarks>
              <para>
                Routing key must be shorter than 255 bytes.
              </para>
            </remarks>        
        </member>
        <member name="M:RabbitMQ.Client.IModel.QueueDeclare(System.String,System.Boolean,System.Boolean,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary> Declare a queue.</summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.QueueDeclareNoWait(System.String,System.Boolean,System.Boolean,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Same as QueueDeclare but sets nowait to true and returns void (as there
            will be no response from the server).
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.QueueDeclarePassive(System.String)">
             <summary>Declare a queue passively.</summary>
             <remarks>
            The queue is declared passive, non-durable,
            non-exclusive, and non-autodelete, with no arguments.
            The queue is declared passively; i.e. only check if it exists.
             </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.MessageCount(System.String)">
            <summary>
            Returns the number of messages in a queue ready to be delivered
            to consumers. This method assumes the queue exists. If it doesn't,
            an exception will be closed with an exception.
            </summary>
            <param name="queue">The name of the queue</param>
        </member>
        <member name="M:RabbitMQ.Client.IModel.ConsumerCount(System.String)">
            <summary>
            Returns the number of consumers on a queue.
            This method assumes the queue exists. If it doesn't,
            an exception will be closed with an exception.
            </summary>
            <param name="queue">The name of the queue</param>
        </member>
        <member name="M:RabbitMQ.Client.IModel.QueueDelete(System.String,System.Boolean,System.Boolean)">
             <summary>
             Delete a queue.
             </summary>
             <remarks>
            Returns the number of messages purged during queue deletion.
             <code>uint.MaxValue</code>.
             </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.QueueDeleteNoWait(System.String,System.Boolean,System.Boolean)">
             <summary>
            Same as QueueDelete but sets nowait parameter to true
            and returns void (as there will be no response from the server)
             </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.QueuePurge(System.String)">
            <summary>
            Purge a queue of messages.
            </summary>
            <remarks>
            Returns the number of messages purged.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.QueueUnbind(System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Unbind a queue from an exchange.
            </summary>
            <remarks>
              <para>
                Routing key must be shorter than 255 bytes.
              </para>
            </remarks>        
        </member>
        <member name="M:RabbitMQ.Client.IModel.TxCommit">
            <summary>
            Commit this session's active TX transaction.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.TxRollback">
            <summary>
            Roll back this session's active TX transaction.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.TxSelect">
            <summary>
            Enable TX mode for this session.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModel.WaitForConfirms">
            <summary>Wait until all published messages have been confirmed.
            </summary>
            <remarks>
            Waits until all messages published since the last call have
            been either ack'd or nack'd by the broker.  Returns whether
            all the messages were ack'd (and none were nack'd). Note,
            throws an exception when called on a non-Confirm channel.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.WaitForConfirms(System.TimeSpan)">
             <summary>
             Wait until all published messages have been confirmed.
             </summary>
             <returns>True if no nacks were received within the timeout, otherwise false.</returns>
             <param name="timeout">How long to wait (at most) before returning
            whether or not any nacks were returned.
             </param>
             <remarks>
             Waits until all messages published since the last call have
             been either ack'd or nack'd by the broker.  Returns whether
             all the messages were ack'd (and none were nack'd). Note,
             throws an exception when called on a non-Confirm channel.
             </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.WaitForConfirms(System.TimeSpan,System.Boolean@)">
            <summary>
            Wait until all published messages have been confirmed.
            </summary>
            <returns>True if no nacks were received within the timeout, otherwise false.</returns>
            <param name="timeout">How long to wait (at most) before returning
            whether or not any nacks were returned.
            </param>
            <param name="timedOut">True if the method returned because
            the timeout elapsed, not because all messages were ack'd or at least one nack'd.
            </param>
            <remarks>
            Waits until all messages published since the last call have
            been either ack'd or nack'd by the broker.  Returns whether
            all the messages were ack'd (and none were nack'd). Note,
            throws an exception when called on a non-Confirm channel.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.WaitForConfirmsOrDie">
            <summary>
            Wait until all published messages have been confirmed.
            </summary>
            <remarks>
            Waits until all messages published since the last call have
            been ack'd by the broker.  If a nack is received, throws an
            OperationInterrupedException exception immediately.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModel.WaitForConfirmsOrDie(System.TimeSpan)">
            <summary>
            Wait until all published messages have been confirmed.
            </summary>
            <remarks>
            Waits until all messages published since the last call have
            been ack'd by the broker.  If a nack is received or the timeout
            elapses, throws an OperationInterrupedException exception immediately.
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.IModel.ContinuationTimeout">
            <summary>
            Amount of time protocol  operations (e.g. <code>queue.declare</code>) are allowed to take before
            timing out.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.BasicConsume(RabbitMQ.Client.IModel,RabbitMQ.Client.IBasicConsumer,System.String,System.Boolean,System.String,System.Boolean,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>Start a Basic content-class consumer.</summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.BasicConsume(RabbitMQ.Client.IModel,System.String,System.Boolean,RabbitMQ.Client.IBasicConsumer)">
            <summary>Start a Basic content-class consumer.</summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.BasicConsume(RabbitMQ.Client.IModel,System.String,System.Boolean,System.String,RabbitMQ.Client.IBasicConsumer)">
            <summary>Start a Basic content-class consumer.</summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.BasicConsume(RabbitMQ.Client.IModel,System.String,System.Boolean,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},RabbitMQ.Client.IBasicConsumer)">
            <summary>Start a Basic content-class consumer.</summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.BasicPublish(RabbitMQ.Client.IModel,RabbitMQ.Client.PublicationAddress,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>
            (Extension method) Convenience overload of BasicPublish.
            </summary>
            <remarks>
            The publication occurs with mandatory=false and immediate=false.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.BasicPublish(RabbitMQ.Client.IModel,System.String,System.String,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>
            (Extension method) Convenience overload of BasicPublish.
            </summary>
            <remarks>
            The publication occurs with mandatory=false
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.BasicPublish(RabbitMQ.Client.IModel,System.String,System.String,System.Boolean,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>
            (Spec method) Convenience overload of BasicPublish.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.QueueDeclare(RabbitMQ.Client.IModel,System.String,System.Boolean,System.Boolean,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            (Spec method) Declare a queue.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.ExchangeBind(RabbitMQ.Client.IModel,System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            (Extension method) Bind an exchange to an exchange.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.ExchangeBindNoWait(RabbitMQ.Client.IModel,System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            (Extension method) Like exchange bind but sets nowait to true. 
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.ExchangeDeclare(RabbitMQ.Client.IModel,System.String,System.String,System.Boolean,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            (Spec method) Declare an exchange.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.ExchangeDeclareNoWait(RabbitMQ.Client.IModel,System.String,System.String,System.Boolean,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            (Extension method) Like ExchangeDeclare but sets nowait to true. 
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.ExchangeUnbind(RabbitMQ.Client.IModel,System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            (Spec method) Unbinds an exchange.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.ExchangeDelete(RabbitMQ.Client.IModel,System.String,System.Boolean)">
            <summary>
            (Spec method) Deletes an exchange.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.ExchangeDeleteNoWait(RabbitMQ.Client.IModel,System.String,System.Boolean)">
            <summary>
            (Extension method) Like ExchangeDelete but sets nowait to true.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.QueueBind(RabbitMQ.Client.IModel,System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            (Spec method) Binds a queue.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.QueueDelete(RabbitMQ.Client.IModel,System.String,System.Boolean,System.Boolean)">
            <summary>
            (Spec method) Deletes a queue.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.QueueDeleteNoWait(RabbitMQ.Client.IModel,System.String,System.Boolean,System.Boolean)">
            <summary>
            (Extension method) Like QueueDelete but sets nowait to true.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IModelExensions.QueueUnbind(RabbitMQ.Client.IModel,System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            (Spec method) Unbinds a queue.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.IProtocol">
            <summary>
            Object describing various overarching parameters
            associated with a particular AMQP protocol variant.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IProtocol.ApiName">
            <summary>
            Retrieve the protocol's API name, used for printing,
            configuration properties, IDE integration, Protocols.cs etc.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IProtocol.DefaultPort">
            <summary>
            Retrieve the protocol's default TCP port.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IProtocol.MajorVersion">
            <summary>
            Retrieve the protocol's major version number.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IProtocol.MinorVersion">
            <summary>
            Retrieve the protocol's minor version number.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IProtocol.Revision">
            <summary>
            Retrieve the protocol's revision (if specified).
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IProtocol.CreateConnection(RabbitMQ.Client.IConnectionFactory,System.Boolean,RabbitMQ.Client.Impl.IFrameHandler)">
            <summary>
            Construct a connection from a given set of parameters,
            a frame handler, and no automatic recovery.
            The "insist" parameter is passed on to the AMQP connection.open method.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IProtocol.CreateConnection(RabbitMQ.Client.ConnectionFactory,RabbitMQ.Client.Impl.IFrameHandler,System.Boolean)">
            <summary>
            Construct a connection from a given set of parameters,
            a frame handler, and automatic recovery settings.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IProtocol.CreateConnection(RabbitMQ.Client.IConnectionFactory,System.Boolean,RabbitMQ.Client.Impl.IFrameHandler,System.String)">
            <summary>
            Construct a connection from a given set of parameters,
            a frame handler, a client-provided name, and no automatic recovery.
            The "insist" parameter is passed on to the AMQP connection.open method.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IProtocol.CreateConnection(RabbitMQ.Client.ConnectionFactory,RabbitMQ.Client.Impl.IFrameHandler,System.Boolean,System.String)">
            <summary>
            Construct a connection from a given set of parameters,
            a frame handler, a client-provided name, and automatic recovery settings.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IProtocol.CreateModel(RabbitMQ.Client.Impl.ISession)">
            <summary>
            Construct a protocol model atop a given session.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.IQueueingBasicConsumer">
             <summary>
             A <see cref="T:RabbitMQ.Client.IBasicConsumer"/> implementation that
             uses a <see cref="T:RabbitMQ.Util.SharedQueue"/> to buffer incoming deliveries.
             </summary>
            <remarks>
            <para>
             This interface is provided to make creation of test doubles
             for <see cref="T:RabbitMQ.Client.QueueingBasicConsumer" /> easier.
            </para>
            </remarks>
        </member>
        <member name="T:RabbitMQ.Client.IRecoverable">
            <summary>
            A marker interface for entities that are recoverable (currently connection or channel).
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.IStreamProperties">
             <summary>
             Common AMQP Stream content-class headers interface,
            spanning the union of the functionality offered by versions 0-8, 0-8qpid, 0-9 and 0-9-1 of AMQP.
             </summary>
             <remarks>
             <para>
             The specification code generator provides
             protocol-version-specific implementations of this interface. To
             obtain an implementation of this interface in a
             protocol-version-neutral way, use IModel.CreateStreamProperties().
             </para>
             <para>
             Each property is readable, writable and clearable: a cleared
             property will not be transmitted over the wire. Properties on a fresh instance are clear by default.
             </para>
             </remarks>
        </member>
        <member name="P:RabbitMQ.Client.IStreamProperties.ContentEncoding">
            <summary>
            MIME content encoding.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IStreamProperties.ContentType">
            <summary>
            MIME content type.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IStreamProperties.Headers">
            <summary>
            Message header field table.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IStreamProperties.Priority">
            <summary>
            Message priority, 0 to 9.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.IStreamProperties.Timestamp">
            <summary>
            Message timestamp.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IStreamProperties.ClearContentEncoding">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IStreamProperties.ContentEncoding"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IStreamProperties.ClearContentType">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IStreamProperties.ContentType"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IStreamProperties.ClearHeaders">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IStreamProperties.Headers"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IStreamProperties.ClearPriority">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IStreamProperties.Priority"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IStreamProperties.ClearTimestamp">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.IStreamProperties.Timestamp"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IStreamProperties.IsContentEncodingPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IStreamProperties.ContentEncoding"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IStreamProperties.IsContentTypePresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IStreamProperties.ContentType"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IStreamProperties.IsHeadersPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IStreamProperties.Headers"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IStreamProperties.IsPriorityPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IStreamProperties.Priority"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.IStreamProperties.IsTimestampPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.IStreamProperties.Timestamp"/> property is present.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.ITcpClient">
            <summary>
            Wrapper interface for standard TCP-client. Provides socket for socket frame handler class.
            </summary>
            <remarks>Contains all methods that are currenty in use in rabbitmq client.</remarks>
        </member>
        <member name="T:RabbitMQ.Client.NetworkConnection">
            <summary>
            Common interface for network (TCP/IP) connection classes.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.NetworkConnection.LocalPort">
            <summary>
            Local port.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.NetworkConnection.RemotePort">
            <summary>
            Remote port.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.PlainMechanismFactory.Name">
            <summary>
            The name of the authentication mechanism, as negotiated on the wire.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.PlainMechanismFactory.GetInstance">
            <summary>
            Return a new authentication mechanism implementation.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Protocols">
            <summary>
             Provides access to the supported <see cref="T:RabbitMQ.Client.IProtocol"/> implementations.
             </summary>
        </member>
        <member name="P:RabbitMQ.Client.Protocols.AMQP_0_9_1">
            <summary>
             Protocol version 0-9-1 as modified by Pivotal.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Protocols.DefaultProtocol">
            <summary>
             Retrieve the current default protocol variant (currently AMQP_0_9_1).
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.PublicationAddress">
            <summary>
            Container for an exchange name, exchange type and
            routing key, usable as the target address of a message to be published.
            </summary>
            <remarks>
            <para>
            The syntax used for the external representation of instances
            of this class is compatible with QPid's "Reply-To" field
            pseudo-URI format. The pseudo-URI format is
            (exchange-type)://(exchange-name)/(routing-key), where
            exchange-type is one of the permitted exchange type names (see
            class ExchangeType), exchange-name must be present but may be
            empty, and routing-key must be present but may be empty.
            </para>
            <para>
            The syntax is as it is solely for compatibility with QPid's
            existing usage of the ReplyTo field; the AMQP specifications
            0-8 and 0-9 do not define the format of the field, and do not
            define any format for the triple (exchange name, exchange
            type, routing key) that could be used instead.
            </para>
            </remarks>
        </member>
        <member name="F:RabbitMQ.Client.PublicationAddress.PSEUDO_URI_PARSER">
            <summary>
            Regular expression used to extract the exchange-type,
            exchange-name and routing-key from a string.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.PublicationAddress.#ctor(System.String,System.String,System.String)">
            <summary>
             Creates a new instance of the <see cref="T:RabbitMQ.Client.PublicationAddress"/>.
            </summary>
            <param name="exchangeType">Exchange type.</param>
            <param name="exchangeName">Exchange name.</param>
            <param name="routingKey">Routing key.</param>
        </member>
        <member name="P:RabbitMQ.Client.PublicationAddress.ExchangeName">
            <summary>
            Retrieve the exchange name.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.PublicationAddress.ExchangeType">
            <summary>
            Retrieve the exchange type string.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.PublicationAddress.RoutingKey">
             <summary>
            Retrieve the routing key.
             </summary>
        </member>
        <member name="M:RabbitMQ.Client.PublicationAddress.Parse(System.String)">
            <summary>
            Parse a <see cref="T:RabbitMQ.Client.PublicationAddress"/> out of the given string,
             using the <see cref="F:RabbitMQ.Client.PublicationAddress.PSEUDO_URI_PARSER"/> regex.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.PublicationAddress.ToString">
            <summary>
            Reconstruct the "uri" from its constituents.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.QueueDeclareOk">
            <summary>
            Represents Queue info.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.QueueDeclareOk.#ctor(System.String,System.UInt32,System.UInt32)">
            <summary>
            Creates a new instance of the <see cref="T:RabbitMQ.Client.QueueDeclareOk"/>.
            </summary>
            <param name="queueName">Queue name.</param>
            <param name="messageCount">Message count.</param>
            <param name="consumerCount">Consumer count.</param>
        </member>
        <member name="P:RabbitMQ.Client.QueueDeclareOk.ConsumerCount">
            <summary>
            Consumer count.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.QueueDeclareOk.MessageCount">
            <summary>
            Message count.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.QueueDeclareOk.QueueName">
            <summary>
            Queue name.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.QueueingBasicConsumer">
             <summary>
             A <see cref="T:RabbitMQ.Client.IBasicConsumer"/> implementation that
             uses a <see cref="T:RabbitMQ.Util.SharedQueue"/> to buffer incoming deliveries.
             </summary>
             <remarks>
             <para>
             Received messages are placed in the SharedQueue as instances
             of <see cref="T:RabbitMQ.Client.Events.BasicDeliverEventArgs"/>.
             </para>
             <para>
             Note that messages taken from the SharedQueue may need
             acknowledging with <see cref="M:RabbitMQ.Client.IModel.BasicAck(System.UInt64,System.Boolean)"/>.
             </para>
             <para>
             When the consumer is closed, through BasicCancel or through
             the shutdown of the underlying <see cref="T:RabbitMQ.Client.IModel"/> or <see cref="T:RabbitMQ.Client.IConnection"/>,
              the  <see cref="M:RabbitMQ.Util.SharedQueue`1.Close"/> method is called, which causes any
             Enqueue() operations, and Dequeue() operations when the queue
             is empty, to throw EndOfStreamException (see the comment for <see cref="M:RabbitMQ.Util.SharedQueue`1.Close"/>).
             </para>
             <para>
             The following is a simple example of the usage of this class:
             </para>
             <example><code>
             IModel channel = ...;
             QueueingBasicConsumer consumer = new QueueingBasicConsumer(channel);
             channel.BasicConsume(queueName, null, consumer);
            
             // At this point, messages will be being asynchronously delivered,
             // and will be queueing up in consumer.Queue.
            
             while (true) {
                 try {
                     BasicDeliverEventArgs e = (BasicDeliverEventArgs) consumer.Queue.Dequeue();
                     // ... handle the delivery ...
                     channel.BasicAck(e.DeliveryTag, false);
                 } catch (EndOfStreamException ex) {
                     // The consumer was cancelled, the model closed, or the
                     // connection went away.
                     break;
                 }
             }
             </code></example>
             </remarks>
        </member>
        <member name="M:RabbitMQ.Client.QueueingBasicConsumer.#ctor">
            <summary>
            Creates a fresh <see cref="T:RabbitMQ.Client.QueueingBasicConsumer"/>,
             initialising the <see cref="P:RabbitMQ.Client.DefaultBasicConsumer.Model"/> property to null
             and the <see cref="P:RabbitMQ.Client.QueueingBasicConsumer.Queue"/> property to a fresh <see cref="T:RabbitMQ.Util.SharedQueue"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.QueueingBasicConsumer.#ctor(RabbitMQ.Client.IModel)">
            <summary>
            Creates a fresh <see cref="T:RabbitMQ.Client.QueueingBasicConsumer"/>, with <see cref="P:RabbitMQ.Client.DefaultBasicConsumer.Model"/>
             set to the argument, and <see cref="P:RabbitMQ.Client.QueueingBasicConsumer.Queue"/> set to a fresh <see cref="T:RabbitMQ.Util.SharedQueue"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.QueueingBasicConsumer.#ctor(RabbitMQ.Client.IModel,RabbitMQ.Util.SharedQueue{RabbitMQ.Client.Events.BasicDeliverEventArgs})">
            <summary>
            Creates a fresh <see cref="T:RabbitMQ.Client.QueueingBasicConsumer"/>,
             initialising the <see cref="P:RabbitMQ.Client.DefaultBasicConsumer.Model"/>
             and <see cref="P:RabbitMQ.Client.QueueingBasicConsumer.Queue"/> properties to the given values.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.QueueingBasicConsumer.Queue">
            <summary>
            Retrieves the <see cref="T:RabbitMQ.Util.SharedQueue"/> that messages arrive on.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.QueueingBasicConsumer.HandleBasicDeliver(System.String,System.UInt64,System.Boolean,System.String,System.String,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>
            Overrides <see cref="T:RabbitMQ.Client.DefaultBasicConsumer"/>'s  <see cref="M:RabbitMQ.Client.QueueingBasicConsumer.HandleBasicDeliver(System.String,System.UInt64,System.Boolean,System.String,System.String,RabbitMQ.Client.IBasicProperties,System.Byte[])"/> implementation,
             building a <see cref="T:RabbitMQ.Client.Events.BasicDeliverEventArgs"/> instance and placing it in the Queue.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.QueueingBasicConsumer.OnCancel">
            <summary>
            Overrides <see cref="T:RabbitMQ.Client.DefaultBasicConsumer"/>'s OnCancel implementation,
             extending it to call the Close() method of the <see cref="T:RabbitMQ.Util.SharedQueue"/>.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.ShutdownEventArgs">
            <summary>
            Information about the reason why a particular model, session, or connection was destroyed.
            </summary>
            <remarks>
            The <see cref="P:RabbitMQ.Client.ShutdownEventArgs.ClassId"/> and <see cref="P:RabbitMQ.Client.ShutdownEventArgs.Initiator"/> properties should be used to determine the originator of the shutdown event.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.ShutdownEventArgs.#ctor(RabbitMQ.Client.ShutdownInitiator,System.UInt16,System.String,System.Object)">
            <summary>
            Construct a <see cref="T:RabbitMQ.Client.ShutdownEventArgs"/> with the given parameters and
             0 for <see cref="P:RabbitMQ.Client.ShutdownEventArgs.ClassId"/> and <see cref="P:RabbitMQ.Client.ShutdownEventArgs.MethodId"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.ShutdownEventArgs.#ctor(RabbitMQ.Client.ShutdownInitiator,System.UInt16,System.String,System.UInt16,System.UInt16,System.Object)">
            <summary>
            Construct a <see cref="T:RabbitMQ.Client.ShutdownEventArgs"/> with the given parameters.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ShutdownEventArgs.Cause">
            <summary>
            Object causing the shutdown, or null if none.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ShutdownEventArgs.ClassId">
            <summary>
            AMQP content-class ID, or 0 if none.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ShutdownEventArgs.Initiator">
            <summary>
            Returns the source of the shutdown event: either the application, the library, or the remote peer.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ShutdownEventArgs.MethodId">
            <summary>
            AMQP method ID within a content-class, or 0 if none.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ShutdownEventArgs.ReplyCode">
            <summary>
            One of the standardised AMQP reason codes. See RabbitMQ.Client.Framing.*.Constants.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ShutdownEventArgs.ReplyText">
            <summary>
            Informative human-readable reason text.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.ShutdownEventArgs.ToString">
            <summary>
            Override ToString to be useful for debugging.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.ShutdownInitiator">
            <summary>
             Describes the source of a shutdown event.
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.ShutdownInitiator.Application">
            <summary>
             The shutdown event originated from the application using the RabbitMQ .NET client library.
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.ShutdownInitiator.Library">
            <summary>
             The shutdown event originated from the RabbitMQ .NET client library itself.
            </summary>
            <remarks>
             Shutdowns with this ShutdownInitiator code may appear if,
             for example, an internal error is detected by the client,
             or if the server sends a syntactically invalid
             frame. Another potential use is on IConnection AutoClose.
            </remarks>
        </member>
        <member name="F:RabbitMQ.Client.ShutdownInitiator.Peer">
            <summary>
             The shutdown event originated from the remote AMQP peer.
            </summary>
            <remarks>
             A valid received connection.close or channel.close event
             will manifest as a shutdown with this ShutdownInitiator.
            </remarks>
        </member>
        <member name="T:RabbitMQ.Client.ShutdownReportEntry">
            <summary>
            Single entry object in the shutdown report that encapsulates description
            of the error which occured during shutdown.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ShutdownReportEntry.Description">
            <summary>
            Description provided in the error.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.ShutdownReportEntry.Exception">
            <summary>
            <see cref="P:RabbitMQ.Client.ShutdownReportEntry.Exception"/> object that occured during shutdown, or null if unspecified.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.SslHelper">
            <summary>
            Represents an <see cref="T:RabbitMQ.Client.SslHelper"/> which does the actual heavy lifting to set up an SSL connection,
             using the config options in an <see cref="T:RabbitMQ.Client.SslOption"/> to make things cleaner.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.SslHelper.TcpUpgrade(System.IO.Stream,RabbitMQ.Client.SslOption)">
            <summary>
            Upgrade a Tcp stream to an Ssl stream using the SSL options provided.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.SslOption">
            <summary>
            Represents a configurable SSL option, used in setting up an SSL connection.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.SslOption.#ctor(System.String,System.String,System.Boolean)">
            <summary>
            Constructs an SslOption specifying both the server cannonical name and the client's certificate path.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.SslOption.#ctor">
            <summary>
            Constructs an <see cref="T:RabbitMQ.Client.SslOption"/> with no parameters set.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.SslOption.AcceptablePolicyErrors">
            <summary>
            Retrieve or set the set of ssl policy errors that are deemed acceptable.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.SslOption.CertPassphrase">
            <summary>
            Retrieve or set the path to client certificate.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.SslOption.CertPath">
            <summary>
            Retrieve or set the path to client certificate.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.SslOption.CertificateSelectionCallback">
            <summary>
            An optional client specified SSL certificate selection callback.  If this is not specified,
            the first valid certificate found will be used.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.SslOption.CertificateValidationCallback">
            <summary>
            An optional client specified SSL certificate validation callback.  If this is not specified,
            the default callback will be used in conjunction with the <see cref="P:RabbitMQ.Client.SslOption.AcceptablePolicyErrors"/> property to
            determine if the remote server certificate is valid.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.SslOption.Certs">
            <summary>
            Retrieve or set the X509CertificateCollection containing the client certificate.
            If no collection is set, the client will attempt to load one from the specified <see cref="P:RabbitMQ.Client.SslOption.CertPath"/>.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.SslOption.Enabled">
            <summary>
            Flag specifying if Ssl should indeed be used.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.SslOption.ServerName">
            <summary>
            Retrieve or set server's Canonical Name.
            This MUST match the CN on the Certificate else the SSL connection will fail.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.SslOption.Version">
            <summary>
            Retrieve or set the Ssl protocol version.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.BasicMessageBuilder">
            <summary>
            Framework for constructing various types of AMQP. Basic-class application messages.
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Content.BasicMessageBuilder.DefaultAccumulatorSize">
            <summary>
            By default, new instances of BasicMessageBuilder and its subclasses will have this much initial buffer space.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BasicMessageBuilder.#ctor(RabbitMQ.Client.IModel)">
            <summary>
            Construct an instance ready for writing.
            </summary>
            <remarks>
            The <see cref="F:RabbitMQ.Client.Content.BasicMessageBuilder.DefaultAccumulatorSize"/> is used for the initial accumulator buffer size.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Content.BasicMessageBuilder.#ctor(RabbitMQ.Client.IModel,System.Int32)">
            <summary>
            Construct an instance ready for writing.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Content.BasicMessageBuilder.Properties">
            <summary>
            Retrieve the <see cref="T:RabbitMQ.Client.IBasicProperties"/> associated with this instance.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Content.BasicMessageBuilder.Writer">
            <summary>
            Retrieve this instance's <see cref="T:RabbitMQ.Util.NetworkBinaryWriter"/> writing to BodyStream.
            </summary>
            <remarks>
            If no <see cref="T:RabbitMQ.Util.NetworkBinaryWriter"/> instance exists, one is created,
            pointing at the beginning of the accumulator. If one
            already exists, the existing instance is returned. The
            instance is not reset.
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.Content.BasicMessageBuilder.BodyStream">
            <summary>
            Retrieve the <see cref="T:System.IO.Stream"/> being used to construct the message body.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Content.BasicMessageBuilder.Headers">
            <summary>
            Retrieves the dictionary that will be used to construct the message header table.
            It is of type <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BasicMessageBuilder.GetContentBody">
            <summary>
            Finish and retrieve the content body for transmission.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BasicMessageBuilder.GetContentHeader">
            <summary>
            Returns the default MIME content type for messages this instance constructs,
            or null if none is available or relevant.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BasicMessageBuilder.GetDefaultContentType">
            <summary>
            Returns the default MIME content type for messages this instance constructs,
            or null if none is available or relevant.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BasicMessageBuilder.RawWrite(System.Byte)">
            <summary>
            Write a single byte into the message body, without encoding or interpretation.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BasicMessageBuilder.RawWrite(System.Byte[])">
            <summary>
            Write a byte array into the message body, without encoding or interpretation.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BasicMessageBuilder.RawWrite(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Write a section of a byte array into the message body, without encoding or interpretation.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.BasicMessageReader">
            <summary>
            Framework for analyzing various types of AMQP Basic-class application messages.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BasicMessageReader.#ctor(RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>
            Construct an instance ready for reading.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Content.BasicMessageReader.Properties">
            <summary>
            Retrieve the <see cref="T:RabbitMQ.Client.IBasicProperties"/> associated with this instance.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Content.BasicMessageReader.Reader">
            <summary>
            Retrieve this instance's NetworkBinaryReader reading from <see cref="P:RabbitMQ.Client.Content.BasicMessageReader.BodyBytes"/>.
            </summary>
            <remarks>
            If no NetworkBinaryReader instance exists, one is created,
            pointing at the beginning of the body. If one already
            exists, the existing instance is returned. The instance is
            not reset.
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.Content.BasicMessageReader.BodyBytes">
            <summary>
            Retrieve the message body, as a byte array.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Content.BasicMessageReader.BodyStream">
            <summary>
            Retrieve the <see cref="T:System.IO.Stream"/> being used to read from the message body.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Content.BasicMessageReader.Headers">
            <summary>
            Retrieves the content header properties of the message being read. Is of type <seealso cref="T:System.Collections.Generic.IDictionary`2"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BasicMessageReader.RawRead">
            <summary>
            Read a single byte from the body stream, without encoding or interpretation.
            Returns -1 for end-of-stream.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BasicMessageReader.RawRead(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Read bytes from the body stream into a section of
            an existing byte array, without encoding or
            interpretation. Returns the number of bytes read from the
            body and written into the target array, which may be less
            than the number requested if the end-of-stream is reached.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.BytesMessageBuilder">
            <summary>
            Constructs AMQP Basic-class messages binary-compatible with QPid's "BytesMessage" wire encoding.
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Content.BytesMessageBuilder.MimeType">
            <summary>
            MIME type associated with QPid BytesMessages.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageBuilder.#ctor(RabbitMQ.Client.IModel)">
            <summary>
            Construct an instance for writing. See <see cref="T:RabbitMQ.Client.Content.BasicMessageBuilder"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageBuilder.#ctor(RabbitMQ.Client.IModel,System.Int32)">
            <summary>
            Construct an instance for writing. See <see cref="T:RabbitMQ.Client.Content.BasicMessageBuilder"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageBuilder.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Write a section of a byte array into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageBuilder.WriteByte(System.Byte)">
            <summary>
            Writes a <see cref="T:System.Byte"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageBuilder.WriteBytes(System.Byte[])">
            <summary>
            Write a <see cref="T:System.Byte"/> array into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageBuilder.WriteChar(System.Char)">
            <summary>
            Writes a <see cref="T:System.Char"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageBuilder.WriteDouble(System.Double)">
            <summary>
            Writes a <see cref="T:System.Double"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageBuilder.WriteInt16(System.Int16)">
            <summary>
            Writes a <see cref="T:System.Int16"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageBuilder.WriteInt32(System.Int32)">
            <summary>
            Writes an <see cref="T:System.Int32"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageBuilder.WriteInt64(System.Int64)">
            <summary>
            Writes a <see cref="T:System.Int64"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageBuilder.WriteSingle(System.Single)">
            <summary>
            Writes a <see cref="T:System.Single"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageBuilder.WriteString(System.String)">
            <summary>
            Writes a <see cref="T:System.String"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageBuilder.GetDefaultContentType">
            <summary>
            Returns the default MIME content type for messages this instance constructs,
            or null if none is available or relevant.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.BytesMessageReader">
            <summary>
            Analyzes AMQP Basic-class messages binary-compatible with QPid's "BytesMessage" wire encoding.
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Content.BytesMessageReader.MimeType">
            <summary>
            MIME type associated with QPid BytesMessages.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageReader.#ctor(RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>
            Construct an instance for reading. See <see cref="T:RabbitMQ.Client.Content.BasicMessageReader"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageReader.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a given number ("count") of bytes from the message body,
            placing them into "target", starting at "offset".
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageReader.ReadByte">
            <summary>
            Reads a <see cref="T:System.Byte"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageReader.ReadBytes(System.Int32)">
            <summary>
            Reads a given number of bytes from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageReader.ReadChar">
            <summary>
            Reads a <see cref="T:System.Char"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageReader.ReadDouble">
            <summary>
            Reads a <see cref="T:System.Double"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageReader.ReadInt16">
            <summary>
            Reads a <see cref="T:System.Int16"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageReader.ReadInt32">
            <summary>
            Reads an <see cref="T:System.Int32"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageReader.ReadInt64">
            <summary>
            Reads a <see cref="T:System.Int64"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageReader.ReadSingle">
            <summary>
            Reads a <see cref="T:System.Single"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.BytesMessageReader.ReadString">
            <summary>
            Reads a <see cref="T:System.String"/> from the message body.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.BytesWireFormatting">
            <summary>
            Internal support class for use in reading and
            writing information binary-compatible with QPid's "BytesMessage" wire encoding.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.IBytesMessageBuilder">
            <summary>
            Interface for constructing messages binary-compatible with QPid's "BytesMessage" wire encoding.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageBuilder.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Write a section of a byte array into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageBuilder.WriteByte(System.Byte)">
            <summary>
            Writes a <see cref="T:System.Byte"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageBuilder.WriteBytes(System.Byte[])">
            <summary>
            Write a <see cref="T:System.Byte"/> array into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageBuilder.WriteChar(System.Char)">
            <summary>
            Writes a <see cref="T:System.Char"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageBuilder.WriteDouble(System.Double)">
            <summary>
            Writes a <see cref="T:System.Double"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageBuilder.WriteInt16(System.Int16)">
            <summary>
            Writes a <see cref="T:System.Int16"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageBuilder.WriteInt32(System.Int32)">
            <summary>
            Writes an <see cref="T:System.Int32"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageBuilder.WriteInt64(System.Int64)">
            <summary>
            Writes a <see cref="T:System.Int64"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageBuilder.WriteSingle(System.Single)">
            <summary>
            Writes a <see cref="T:System.Single"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageBuilder.WriteString(System.String)">
            <summary>
            Writes a <see cref="T:System.String"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.IBytesMessageReader">
            <summary>
            Analyzes messages binary-compatible with QPid's "BytesMessage" wire encoding.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageReader.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a given number ("count") of bytes from the message body,
            placing them into "target", starting at "offset".
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageReader.ReadByte">
            <summary>
            Reads a <see cref="T:System.Byte"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageReader.ReadBytes(System.Int32)">
            <summary>
            Reads a given number of bytes from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageReader.ReadChar">
            <summary>
            Reads a <see cref="T:System.Char"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageReader.ReadDouble">
            <summary>
            Reads a <see cref="T:System.Double"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageReader.ReadInt16">
            <summary>
            Reads a <see cref="T:System.Int16"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageReader.ReadInt32">
            <summary>
            Reads an <see cref="T:System.Int32"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageReader.ReadInt64">
            <summary>
            Reads a <see cref="T:System.Int64"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageReader.ReadSingle">
            <summary>
            Reads a <see cref="T:System.Single"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IBytesMessageReader.ReadString">
            <summary>
            Reads a <see cref="T:System.String"/> from the message body.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.IMapMessageBuilder">
            <summary>
            Interface for constructing messages binary-compatible with QPid's "MapMessage" wire encoding.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Content.IMapMessageBuilder.Body">
            <summary>
            Retrieves the dictionary that will be written into the body of the message.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.IMapMessageReader">
            <summary>
             Analyzes messages binary-compatible with QPid's "MapMessage" wire encoding.
             </summary>
        </member>
        <member name="P:RabbitMQ.Client.Content.IMapMessageReader.Body">
            <summary>
             Parses the message body into an <see cref="T:System.Collections.Generic.IDictionary`2"/> instance.
             </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.IMessageBuilder">
            <summary>
            Interface for constructing application messages.
            </summary>
            <remarks>
            Subinterfaces provide specialized data-writing methods. This
            base interface deals with the lowest common denominator:
            bytes, with no special encodings for higher-level objects.
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.Content.IMessageBuilder.BodyStream">
            <summary>
            Retrieve the <see cref="T:System.IO.Stream"/> being used to construct the message body.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Content.IMessageBuilder.Headers">
            <summary>
            Retrieves the dictionary that will be used to construct the message header table.
            It is of type <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IMessageBuilder.GetContentBody">
            <summary>
            Finish and retrieve the content body for transmission.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IMessageBuilder.GetContentHeader">
            <summary>
            Finish and retrieve the content header for transmission.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IMessageBuilder.GetDefaultContentType">
            <summary>
            Returns the default MIME content type for messages this instance constructs,
            or null if none is available or relevant.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IMessageBuilder.RawWrite(System.Byte)">
            <summary>
            Write a single byte into the message body, without encoding or interpretation.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IMessageBuilder.RawWrite(System.Byte[])">
            <summary>
            Write a byte array into the message body, without encoding or interpretation.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IMessageBuilder.RawWrite(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Write a section of a byte array into the message body, without encoding or interpretation.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.IMessageReader">
            <summary>
            Interface for analyzing application messages.
            </summary>
            <remarks>
            Subinterfaces provide specialized data-reading methods. This
            base interface deals with the lowest common denominator:
            bytes, with no special encodings for higher-level objects.
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.Content.IMessageReader.BodyBytes">
            <summary>
            Retrieve the message body, as a byte array.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Content.IMessageReader.BodyStream">
            <summary>
            Retrieve the <see cref="T:System.IO.Stream"/> being used to read from the message body.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Content.IMessageReader.Headers">
            <summary>
            Retrieves the content header properties of the message being read. Is of type <seealso cref="T:System.Collections.Generic.IDictionary`2"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IMessageReader.RawRead">
            <summary>
            Read a single byte from the body stream, without encoding or interpretation.
            Returns -1 for end-of-stream.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IMessageReader.RawRead(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Read bytes from the body stream into a section of
            an existing byte array, without encoding or
            interpretation. Returns the number of bytes read from the
            body and written into the target array, which may be less
            than the number requested if the end-of-stream is reached.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.IStreamMessageBuilder">
            <summary>
            Interface for constructing messages binary-compatible with QPid's "StreamMessage" wire encoding.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageBuilder.WriteBool(System.Boolean)">
            <summary>
            Writes a <see cref="T:System.Boolean"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageBuilder.WriteByte(System.Byte)">
            <summary>
            Writes a <see cref="T:System.Byte"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageBuilder.WriteBytes(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes a section of a byte array into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageBuilder.WriteBytes(System.Byte[])">
            <summary>
            Writes a <see cref="T:System.Byte"/> array into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageBuilder.WriteChar(System.Char)">
            <summary>
            Writes a <see cref="T:System.Char"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageBuilder.WriteDouble(System.Double)">
            <summary>
            Writes a <see cref="T:System.Double"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageBuilder.WriteInt16(System.Int16)">
            <summary>
            Writes a <see cref="T:System.Int16"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageBuilder.WriteInt32(System.Int32)">
            <summary>
            Writes an <see cref="T:System.Int32"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageBuilder.WriteInt64(System.Int64)">
            <summary>
            Writes a <see cref="T:System.Int64"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageBuilder.WriteObject(System.Object)">
            <summary>
            Writes an <see cref="T:System.Object"/> value into the message body being assembled.
            </summary>
            <remarks>
            The only permitted types are bool, int, short, byte, char,
            long, float, double, byte[] and string.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageBuilder.WriteObjects(System.Object[])">
            <summary>
            Writes objects using WriteObject(), one after the other. No length indicator is written.
            See also <see cref="M:RabbitMQ.Client.Content.IStreamMessageReader.ReadObjects"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageBuilder.WriteSingle(System.Single)">
            <summary>
            Writes a <see cref="T:System.Single"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageBuilder.WriteString(System.String)">
            <summary>
            Writes a <see cref="T:System.Single"/>string value into the message body being assembled.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.IStreamMessageReader">
            <summary>
            Analyzes messages binary-compatible with QPid's "StreamMessage" wire encoding.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageReader.ReadBool">
            <summary>
            Reads a <see cref="T:System.Boolean"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageReader.ReadByte">
            <summary>
            Reads a <see cref="T:System.Byte"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageReader.ReadBytes">
            <summary>
            Reads a <see cref="T:System.Byte"/> array from the message body.
            The body contains information about the size of the array to read.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageReader.ReadChar">
            <summary>
            Reads a <see cref="T:System.Char"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageReader.ReadDouble">
            <summary>
            Reads a <see cref="T:System.Double"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageReader.ReadInt16">
            <summary>
            Reads a <see cref="T:System.Int16"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageReader.ReadInt32">
            <summary>
            Reads an <see cref="T:System.Int32"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageReader.ReadInt64">
            <summary>
            Reads a <see cref="T:System.Int64"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageReader.ReadObject">
            <summary>
            Reads an <see cref="T:System.Object"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageReader.ReadObjects">
            <summary>
            Reads <see cref="T:System.Object"/> array from the message body until the end-of-stream is reached.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageReader.ReadSingle">
            <summary>
            Reads a <see cref="T:System.Single"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.IStreamMessageReader.ReadString">
            <summary>
            Reads a <see cref="T:System.String"/> from the message body.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.MapMessageBuilder">
            <summary>
            Constructs AMQP Basic-class messages binary-compatible with QPid's "MapMessage" wire encoding.
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Content.MapMessageBuilder.MimeType">
            <summary>
            MIME type associated with QPid MapMessages.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.MapMessageBuilder.#ctor(RabbitMQ.Client.IModel)">
            <summary>
            Construct an instance for writing. See <see cref="T:RabbitMQ.Client.Content.BasicMessageBuilder"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.MapMessageBuilder.#ctor(RabbitMQ.Client.IModel,System.Int32)">
            <summary>
            Construct an instance for writing. See <see cref="T:RabbitMQ.Client.Content.BasicMessageBuilder"/>.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Content.MapMessageBuilder.Body">
            <summary>
            Retrieves the dictionary that will be written into the body of the message.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.MapMessageBuilder.GetContentBody">
            <summary>
            Finish and retrieve the content body for transmission.
            </summary>
            <remarks>
            Calling this message clears Body to null. Subsequent calls will fault.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Content.MapMessageBuilder.GetDefaultContentType">
            <summary>
            Returns the default MIME content type for messages this instance constructs,
            or null if none is available or relevant.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.MapMessageReader">
            <summary>
            Analyzes AMQP Basic-class messages binary-compatible with QPid's "MapMessage" wire encoding.
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Content.MapMessageReader.MimeType">
            <summary>
            MIME type associated with QPid MapMessages.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.MapMessageReader.#ctor(RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>
            Construct an instance for reading. See <see cref="T:RabbitMQ.Client.Content.BasicMessageReader"/>.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Content.MapMessageReader.Body">
            <summary>
             Parses the message body into an <see cref="T:System.Collections.Generic.IDictionary`2"/> instance.
             </summary>
             <exception cref="T:RabbitMQ.Client.ProtocolViolationException"/>.
        </member>
        <member name="T:RabbitMQ.Client.Content.MapWireFormatting">
            <summary>
            Internal support class for use in reading and
            writing information binary-compatible with QPid's "MapMessage" wire encoding.
            </summary>
            <exception cref="T:RabbitMQ.Client.ProtocolViolationException"/>
        </member>
        <member name="T:RabbitMQ.Client.Content.PrimitiveParser">
            <summary>
                Utility class for extracting typed values from strings.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.PrimitiveParser.CreateProtocolViolationException(System.String,System.Object)">
            <summary>
            Creates the protocol violation exception.
            </summary>
            <param name="targetType">Type of the target.</param>
            <param name="source">The source.</param>
            <returns>Instance of the <see cref="T:RabbitMQ.Client.ProtocolViolationException" />.</returns>
        </member>
        <member name="M:RabbitMQ.Client.Content.PrimitiveParser.ParseBool(System.String)">
            <summary>
            Attempt to parse a string representation of a <see cref="T:System.Boolean" />.
            </summary>
            <exception cref="T:RabbitMQ.Client.ProtocolViolationException" />
        </member>
        <member name="M:RabbitMQ.Client.Content.PrimitiveParser.ParseByte(System.String)">
            <summary>
            Attempt to parse a string representation of a <see cref="T:System.Byte" />.
            </summary>
            <exception cref="T:RabbitMQ.Client.ProtocolViolationException" />
        </member>
        <member name="M:RabbitMQ.Client.Content.PrimitiveParser.ParseDouble(System.String)">
            <summary>
            Attempt to parse a string representation of a <see cref="T:System.Double" />.
            </summary>
            <exception cref="T:RabbitMQ.Client.ProtocolViolationException" />
        </member>
        <member name="M:RabbitMQ.Client.Content.PrimitiveParser.ParseFloat(System.String)">
            <summary>
            Attempt to parse a string representation of a <see cref="T:System.Single" />.
            </summary>
            <exception cref="T:RabbitMQ.Client.ProtocolViolationException" />
        </member>
        <member name="M:RabbitMQ.Client.Content.PrimitiveParser.ParseInt(System.String)">
            <summary>
            Attempt to parse a string representation of an <see cref="T:System.Int32" />.
            </summary>
            <exception cref="T:RabbitMQ.Client.ProtocolViolationException" />
        </member>
        <member name="M:RabbitMQ.Client.Content.PrimitiveParser.ParseLong(System.String)">
            <summary>
            Attempt to parse a string representation of a <see cref="T:System.Int64" />.
            </summary>
            <exception cref="T:RabbitMQ.Client.ProtocolViolationException" />
        </member>
        <member name="M:RabbitMQ.Client.Content.PrimitiveParser.ParseShort(System.String)">
            <summary>
            Attempt to parse a string representation of a <see cref="T:System.Int16" />.
            </summary>
            <exception cref="T:RabbitMQ.Client.ProtocolViolationException" />
        </member>
        <member name="T:RabbitMQ.Client.Content.StreamMessageBuilder">
            <summary>
            Constructs AMQP Basic-class messages binary-compatible with QPid's "StreamMessage" wire encoding.
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Content.StreamMessageBuilder.MimeType">
            <summary>
            MIME type associated with QPid StreamMessages.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageBuilder.#ctor(RabbitMQ.Client.IModel)">
            <summary>
            Construct an instance for writing. See <see cref="T:RabbitMQ.Client.Content.BasicMessageBuilder"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageBuilder.#ctor(RabbitMQ.Client.IModel,System.Int32)">
            <summary>
            Construct an instance for writing. See <see cref="T:RabbitMQ.Client.Content.BasicMessageBuilder"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageBuilder.GetDefaultContentType">
            <summary>
            Returns the default MIME content type for messages this instance constructs,
            or null if none is available or relevant.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageBuilder.WriteBool(System.Boolean)">
            <summary>
            Writes a <see cref="T:System.Boolean"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageBuilder.WriteByte(System.Byte)">
            <summary>
            Writes a <see cref="T:System.Byte"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageBuilder.WriteBytes(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes a section of a byte array into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageBuilder.WriteBytes(System.Byte[])">
            <summary>
            Writes a <see cref="T:System.Byte"/> array into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageBuilder.WriteChar(System.Char)">
            <summary>
            Writes a <see cref="T:System.Char"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageBuilder.WriteDouble(System.Double)">
            <summary>
            Writes a <see cref="T:System.Double"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageBuilder.WriteInt16(System.Int16)">
            <summary>
            Writes a <see cref="T:System.Int16"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageBuilder.WriteInt32(System.Int32)">
            <summary>
            Writes an <see cref="T:System.Int32"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageBuilder.WriteInt64(System.Int64)">
            <summary>
            Writes a <see cref="T:System.Int64"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageBuilder.WriteObject(System.Object)">
            <summary>
            Writes an <see cref="T:System.Object"/> value into the message body being assembled.
            </summary>
            <remarks>
            The only permitted types are bool, int, short, byte, char,
            long, float, double, byte[] and string.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageBuilder.WriteObjects(System.Object[])">
            <summary>
            Writes objects using WriteObject(), one after the other. No length indicator is written.
            See also <see cref="M:RabbitMQ.Client.Content.IStreamMessageReader.ReadObjects"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageBuilder.WriteSingle(System.Single)">
            <summary>
            Writes a <see cref="T:System.Single"/> value into the message body being assembled.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageBuilder.WriteString(System.String)">
            <summary>
            Writes a <see cref="T:System.Single"/>string value into the message body being assembled.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.StreamMessageReader">
            <summary>
            Analyzes AMQP Basic-class messages binary-compatible with QPid's "StreamMessage" wire encoding.
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Content.StreamMessageReader.MimeType">
            <summary>
            MIME type associated with QPid StreamMessages.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageReader.#ctor(RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>
            Construct an instance for reading. See <see cref="T:RabbitMQ.Client.Content.BasicMessageReader"/>.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageReader.ReadBool">
            <summary>
            Reads a <see cref="T:System.Boolean"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageReader.ReadByte">
            <summary>
            Reads a <see cref="T:System.Byte"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageReader.ReadBytes">
            <summary>
            Reads a <see cref="T:System.Byte"/> array from the message body.
            The body contains information about the size of the array to read.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageReader.ReadChar">
            <summary>
            Reads a <see cref="T:System.Char"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageReader.ReadDouble">
            <summary>
            Reads a <see cref="T:System.Double"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageReader.ReadInt16">
            <summary>
            Reads a <see cref="T:System.Int16"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageReader.ReadInt32">
            <summary>
            Reads an <see cref="T:System.Int32"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageReader.ReadInt64">
            <summary>
            Reads a <see cref="T:System.Int64"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageReader.ReadObject">
            <summary>
            Reads an <see cref="T:System.Object"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageReader.ReadObjects">
            <summary>
            Reads <see cref="T:System.Object"/> array from the message body until the end-of-stream is reached.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageReader.ReadSingle">
            <summary>
            Reads a <see cref="T:System.Single"/> from the message body.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamMessageReader.ReadString">
            <summary>
            Reads a <see cref="T:System.String"/> from the message body.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.StreamWireFormattingTag">
            <summary>
            Tags used in parsing and generating StreamWireFormatting message bodies.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Content.StreamWireFormatting">
            <summary>
            Internal support class for use in reading and
            writing information binary-compatible with QPid's "StreamMessage" wire encoding.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamWireFormatting.ReadNonnullObject(System.String,RabbitMQ.Util.NetworkBinaryReader)">
            <exception cref="T:RabbitMQ.Client.ProtocolViolationException"/>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamWireFormatting.ReadObject(RabbitMQ.Util.NetworkBinaryReader)">
            <exception cref="T:System.IO.EndOfStreamException"/>
            <exception cref="T:RabbitMQ.Client.ProtocolViolationException"/>
        </member>
        <member name="M:RabbitMQ.Client.Content.StreamWireFormatting.WriteObject(RabbitMQ.Util.NetworkBinaryWriter,System.Object)">
            <exception cref="T:RabbitMQ.Client.ProtocolViolationException"/>
        </member>
        <member name="M:RabbitMQ.Client.Events.AsyncEventingBasicConsumer.#ctor(RabbitMQ.Client.IModel)">
            <summary>Constructor which sets the Model property to the
            given value.</summary>
        </member>
        <member name="E:RabbitMQ.Client.Events.AsyncEventingBasicConsumer.Received">
            <summary>Event fired on HandleBasicDeliver.</summary>
        </member>
        <member name="E:RabbitMQ.Client.Events.AsyncEventingBasicConsumer.Registered">
            <summary>Event fired on HandleBasicConsumeOk.</summary>
        </member>
        <member name="E:RabbitMQ.Client.Events.AsyncEventingBasicConsumer.Shutdown">
            <summary>Event fired on HandleModelShutdown.</summary>
        </member>
        <member name="E:RabbitMQ.Client.Events.AsyncEventingBasicConsumer.Unregistered">
            <summary>Event fired on HandleBasicCancelOk.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Events.AsyncEventingBasicConsumer.HandleBasicCancelOk(System.String)">
            <summary>Fires the Unregistered event.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Events.AsyncEventingBasicConsumer.HandleBasicConsumeOk(System.String)">
            <summary>Fires the Registered event.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Events.AsyncEventingBasicConsumer.HandleBasicDeliver(System.String,System.UInt64,System.Boolean,System.String,System.String,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>Fires the Received event.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Events.AsyncEventingBasicConsumer.HandleModelShutdown(System.Object,RabbitMQ.Client.ShutdownEventArgs)">
            <summary>Fires the Shutdown event.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Events.BasicAckEventArgs">
            <summary>Contains all the information about a message acknowledged
            from an AMQP broker within the Basic content-class.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicAckEventArgs.DeliveryTag">
            <summary>The sequence number of the acknowledged message, or
            the closed upper bound of acknowledged messages if multiple
            is true.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicAckEventArgs.Multiple">
            <summary>Whether this acknoledgement applies to one message
            or multiple messages.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Events.BasicDeliverEventArgs">
            <summary>Contains all the information about a message delivered
            from an AMQP broker within the Basic content-class.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Events.BasicDeliverEventArgs.#ctor">
            <summary>Default constructor.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Events.BasicDeliverEventArgs.#ctor(System.String,System.UInt64,System.Boolean,System.String,System.String,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>Constructor that fills the event's properties from
            its arguments.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicDeliverEventArgs.BasicProperties">
            <summary>The content header of the message.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicDeliverEventArgs.Body">
            <summary>The message body.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicDeliverEventArgs.ConsumerTag">
            <summary>The consumer tag of the consumer that the message
            was delivered to.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicDeliverEventArgs.DeliveryTag">
            <summary>The delivery tag for this delivery. See
            IModel.BasicAck.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicDeliverEventArgs.Exchange">
            <summary>The exchange the message was originally published
            to.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicDeliverEventArgs.Redelivered">
            <summary>The AMQP "redelivered" flag.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicDeliverEventArgs.RoutingKey">
            <summary>The routing key used when the message was
            originally published.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Events.BasicNackEventArgs">
            <summary>Contains all the information about a message nack'd
            from an AMQP broker within the Basic content-class.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicNackEventArgs.DeliveryTag">
            <summary>The sequence number of the nack'd message, or the
            closed upper bound of nack'd messages if multiple is
            true.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicNackEventArgs.Multiple">
            <summary>Whether this nack applies to one message or
            multiple messages.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicNackEventArgs.Requeue">
            <summary>Ignore</summary>
            <remarks>Clients should ignore this field.</remarks>
        </member>
        <member name="T:RabbitMQ.Client.Events.BasicReturnEventArgs">
            <summary>Contains all the information about a message returned
            from an AMQP broker within the Basic content-class.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicReturnEventArgs.BasicProperties">
            <summary>The content header of the message.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicReturnEventArgs.Body">
            <summary>The message body.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicReturnEventArgs.Exchange">
            <summary>The exchange the returned message was originally
            published to.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicReturnEventArgs.ReplyCode">
            <summary>The AMQP reason code for the return. See
            RabbitMQ.Client.Framing.*.Constants.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicReturnEventArgs.ReplyText">
            <summary>Human-readable text from the broker describing the
            reason for the return.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BasicReturnEventArgs.RoutingKey">
            <summary>The routing key used when the message was
            originally published.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Events.BaseExceptionEventArgs.#ctor(System.Exception)">
            <summary>Wrap an exception thrown by a callback.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BaseExceptionEventArgs.Detail">
            <summary>Access helpful information about the context in
            which the wrapped exception was thrown.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.BaseExceptionEventArgs.Exception">
            <summary>Access the wrapped exception.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Events.CallbackExceptionEventArgs">
            <summary>Describes an exception that was thrown during the
            library's invocation of an application-supplied callback
            handler.</summary>
            <remarks>
            <para>
             When an exception is thrown from a callback registered with
             part of the RabbitMQ .NET client library, it is caught,
             packaged into a CallbackExceptionEventArgs, and passed through
             the appropriate IModel's or IConnection's CallbackException
             event handlers. If an exception is thrown in a
             CallbackException handler, it is silently swallowed, as
             CallbackException is the last chance to handle these kinds of
             exception.
            </para>
            <para>
             Code constructing CallbackExceptionEventArgs instances will
             usually place helpful information about the context of the
             call in the IDictionary available through the Detail property.
            </para>
            </remarks>
        </member>
        <member name="T:RabbitMQ.Client.Events.ConnectionBlockedEventArgs">
            <summary>
            Event relating to connection being blocked.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.ConnectionBlockedEventArgs.Reason">
            <summary>
            Access the reason why connection is blocked.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Events.ConsumerEventArgs">
            <summary>Event relating to a successful consumer registration
            or cancellation.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Events.ConsumerEventArgs.#ctor(System.String)">
            <summary>Construct an event containing the consumer-tag of
            the consumer the event relates to.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.ConsumerEventArgs.ConsumerTag">
            <summary>Access the consumer-tag of the consumer the event
            relates to.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Events.ConsumerTagChangedAfterRecoveryEventArgs.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:RabbitMQ.Client.Events.ConsumerTagChangedAfterRecoveryEventArgs"/> class.
            </summary>
            <param name="tagBefore">The tag before.</param>
            <param name="tagAfter">The tag after.</param>
        </member>
        <member name="P:RabbitMQ.Client.Events.ConsumerTagChangedAfterRecoveryEventArgs.TagBefore">
            <summary>
            Gets the tag before.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.ConsumerTagChangedAfterRecoveryEventArgs.TagAfter">
            <summary>
            Gets the tag after.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Events.EventingBasicConsumer">
            <summary>Experimental class exposing an IBasicConsumer's
            methods as separate events.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Events.EventingBasicConsumer.#ctor(RabbitMQ.Client.IModel)">
            <summary>Constructor which sets the Model property to the
            given value.</summary>
        </member>
        <member name="E:RabbitMQ.Client.Events.EventingBasicConsumer.Received">
            <summary>Event fired on HandleBasicDeliver.</summary>
        </member>
        <member name="E:RabbitMQ.Client.Events.EventingBasicConsumer.Registered">
            <summary>Event fired on HandleBasicConsumeOk.</summary>
        </member>
        <member name="E:RabbitMQ.Client.Events.EventingBasicConsumer.Shutdown">
            <summary>Event fired on HandleModelShutdown.</summary>
        </member>
        <member name="E:RabbitMQ.Client.Events.EventingBasicConsumer.Unregistered">
            <summary>Event fired on HandleBasicCancelOk.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Events.EventingBasicConsumer.HandleBasicCancelOk(System.String)">
            <summary>Fires the Unregistered event.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Events.EventingBasicConsumer.HandleBasicConsumeOk(System.String)">
            <summary>Fires the Registered event.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Events.EventingBasicConsumer.HandleBasicDeliver(System.String,System.UInt64,System.Boolean,System.String,System.String,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>Fires the Received event.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Events.EventingBasicConsumer.HandleModelShutdown(System.Object,RabbitMQ.Client.ShutdownEventArgs)">
            <summary>Fires the Shutdown event.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Events.FlowControlEventArgs">
            <summary>
            Event relating to flow control.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.FlowControlEventArgs.Active">
            <summary>
            Access the flow control setting.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Events.QueueNameChangedAfterRecoveryEventArgs.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:RabbitMQ.Client.Events.QueueNameChangedAfterRecoveryEventArgs"/> class.
            </summary>
            <param name="nameBefore">The name before.</param>
            <param name="nameAfter">The name after.</param>
        </member>
        <member name="P:RabbitMQ.Client.Events.QueueNameChangedAfterRecoveryEventArgs.NameBefore">
            <summary>
            Gets the name before.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Events.QueueNameChangedAfterRecoveryEventArgs.NameAfter">
            <summary>
            Gets the name after.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Events.RecoveryExceptionEventArgs">
            <summary>
            Describes an exception that was thrown during
            automatic connection recovery performed by the library.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Exceptions.AlreadyClosedException">
            <summary>Thrown when the application tries to make use of a
            session or connection that has already been shut
            down.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Exceptions.AlreadyClosedException.#ctor(RabbitMQ.Client.ShutdownEventArgs)">
            <summary>Construct an instance containing the given
            shutdown reason.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Exceptions.AuthenticationFailureException">
            <summary> Thrown when the cause is  an
            authentication failure. </summary>
        </member>
        <member name="T:RabbitMQ.Client.Exceptions.BrokerUnreachableException">
            <summary>Thrown when no connection could be opened during a
            ConnectionFactory.CreateConnection attempt.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Exceptions.BrokerUnreachableException.#ctor(System.Exception)">
            <summary>Construct a BrokerUnreachableException. The inner exception is
            an AggregateException holding the errors from multiple connection attempts.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Exceptions.ChannelAllocationException">
            <summary> Thrown when a SessionManager cannot allocate a new
            channel number, or the requested channel number is already in
            use. </summary>
        </member>
        <member name="M:RabbitMQ.Client.Exceptions.ChannelAllocationException.#ctor">
            <summary>
            Indicates that there are no more free channels.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Exceptions.ChannelAllocationException.#ctor(System.Int32)">
            <summary>
            Indicates that the specified channel is in use
            </summary>
            <param name="channel">The requested channel number</param>
        </member>
        <member name="P:RabbitMQ.Client.Exceptions.ChannelAllocationException.Channel">
            <summary>Retrieves the channel number concerned; will
            return -1 in the case where "no more free channels" is
            being signaled, or a non-negative integer when "channel is
            in use" is being signaled.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Exceptions.ConnectFailureException">
            <summary>Thrown when a connection to the broker fails</summary>
        </member>
        <member name="T:RabbitMQ.Client.Exceptions.OperationInterruptedException">
            <summary>
            Thrown when a session is destroyed during an RPC call to a
            broker. For example, if a TCP connection dropping causes the
            destruction of a session in the middle of a QueueDeclare
            operation, an OperationInterruptedException will be thrown to
            the caller of IModel.QueueDeclare.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Exceptions.OperationInterruptedException.#ctor(RabbitMQ.Client.ShutdownEventArgs)">
            <summary>Construct an OperationInterruptedException with
            the passed-in explanation, if any.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Exceptions.OperationInterruptedException.#ctor(RabbitMQ.Client.ShutdownEventArgs,System.String)">
            <summary>Construct an OperationInterruptedException with
            the passed-in explanation and prefix, if any.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Exceptions.OperationInterruptedException.ShutdownReason">
            <summary>Retrieves the explanation for the shutdown. May
            return null if no explanation is available.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Exceptions.PacketNotRecognizedException">
            <summary>Thrown to indicate that the peer didn't understand
            the packet received from the client. Peer sent default message
            describing protocol version it is using and transport parameters.
            </summary>
            <remarks>
            The peer's {'A','M','Q','P',txHi,txLo,major,minor} packet is
            decoded into instances of this class.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Exceptions.PacketNotRecognizedException.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>Fills the new instance's properties with the values passed in.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Exceptions.PacketNotRecognizedException.ServerMajor">
            <summary>The peer's AMQP specification major version.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Exceptions.PacketNotRecognizedException.ServerMinor">
            <summary>The peer's AMQP specification minor version.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Exceptions.PacketNotRecognizedException.TransportHigh">
            <summary>The peer's high transport byte.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Exceptions.PacketNotRecognizedException.TransportLow">
            <summary>The peer's low transport byte.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Exceptions.PossibleAuthenticationFailureException">
            <summary> Thrown when the likely cause is  an
            authentication failure. </summary>
        </member>
        <member name="T:RabbitMQ.Client.Exceptions.ProtocolVersionMismatchException">
            <summary>Thrown to indicate that the peer does not support the
            wire protocol version we requested immediately after opening
            the TCP socket.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Exceptions.ProtocolVersionMismatchException.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>Fills the new instance's properties with the values passed in.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Exceptions.ProtocolVersionMismatchException.ClientMajor">
            <summary>The client's AMQP specification major version.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Exceptions.ProtocolVersionMismatchException.ClientMinor">
            <summary>The client's AMQP specification minor version.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Exceptions.ProtocolVersionMismatchException.ServerMajor">
            <summary>The peer's AMQP specification major version.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Exceptions.ProtocolVersionMismatchException.ServerMinor">
            <summary>The peer's AMQP specification minor version.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Exceptions.RabbitMQClientException.#ctor">
            <summary>Initializes a new instance of the <see cref="T:RabbitMQ.Client.Exceptions.RabbitMQClientException" /> class.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Exceptions.RabbitMQClientException.#ctor(System.String)">
            <summary>Initializes a new instance of the <see cref="T:RabbitMQ.Client.Exceptions.RabbitMQClientException" /> class with a specified error message.</summary>
            <param name="message">The message that describes the error. </param>
        </member>
        <member name="M:RabbitMQ.Client.Exceptions.RabbitMQClientException.#ctor(System.String,System.Exception)">
            <summary>Initializes a new instance of the <see cref="T:RabbitMQ.Client.Exceptions.RabbitMQClientException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
            <param name="message">The error message that explains the reason for the exception. </param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (Nothing in Visual Basic) if no inner exception is specified. </param>
        </member>
        <member name="T:RabbitMQ.Client.Exceptions.UnexpectedMethodException">
            <summary>
            Thrown when the model receives an RPC reply that it wasn't expecting.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Exceptions.UnexpectedMethodException.Method">
            <summary>The unexpected reply method.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Exceptions.UnsupportedMethodException">
            <summary>
            Thrown when the model receives an RPC request it cannot satisfy.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Exceptions.UnsupportedMethodException.MethodName">
            <summary>The name of the RPC request that could not be sent.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Exceptions.UnsupportedMethodFieldException">
            <summary>Thrown when the model cannot transmit a method field
            because the version of the protocol the model is implementing
            does not contain a definition for the field in
            question.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Exceptions.UnsupportedMethodFieldException.FieldName">
            <summary>The name of the unsupported field.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Exceptions.UnsupportedMethodFieldException.MethodName">
            <summary>The name of the method involved.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Exceptions.WireFormattingException">
            <summary> Thrown when the wire-formatting code cannot encode a
            particular .NET value to AMQP protocol format.  </summary>
        </member>
        <member name="M:RabbitMQ.Client.Exceptions.WireFormattingException.#ctor(System.String)">
            <summary>Construct a WireFormattingException with no
            particular offender (i.e. null)</summary>
        </member>
        <member name="M:RabbitMQ.Client.Exceptions.WireFormattingException.#ctor(System.String,System.Object)">
            <summary>Construct a WireFormattingException with the given
            offender</summary>
        </member>
        <member name="P:RabbitMQ.Client.Exceptions.WireFormattingException.Offender">
            <summary>Object which this exception is complaining about;
            may be null if no particular offender exists</summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.BasicProperties.AppId">
            <summary>
            Application Id.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.BasicProperties.ClusterId">
            <summary>
            Intra-cluster routing identifier (cluster id is deprecated in AMQP 0-9-1).
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.BasicProperties.ContentEncoding">
            <summary>
            MIME content encoding.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.BasicProperties.ContentType">
            <summary>
            MIME content type.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.BasicProperties.CorrelationId">
            <summary>
            Application correlation identifier.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.BasicProperties.DeliveryMode">
            <summary>
            Non-persistent (1) or persistent (2).
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.BasicProperties.Expiration">
            <summary>
            Message expiration specification.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.BasicProperties.Headers">
            <summary>
            Message header field table. Is of type <see cref="T:System.Collections.Generic.IDictionary`2" />.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.BasicProperties.MessageId">
            <summary>
            Application message Id.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.BasicProperties.Persistent">
            <summary>
            Sets <see cref="P:RabbitMQ.Client.Impl.BasicProperties.DeliveryMode"/> to either persistent (2) or non-persistent (1).
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.BasicProperties.Priority">
            <summary>
            Message priority, 0 to 9.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.BasicProperties.ReplyTo">
            <summary>
            Destination to reply to.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.BasicProperties.ReplyToAddress">
            <summary>
            Convenience property; parses <see cref="P:RabbitMQ.Client.Impl.BasicProperties.ReplyTo"/> property using <see cref="M:RabbitMQ.Client.PublicationAddress.Parse(System.String)"/>,
            and serializes it using <see cref="M:RabbitMQ.Client.PublicationAddress.ToString"/>.
            Returns null if <see cref="P:RabbitMQ.Client.Impl.BasicProperties.ReplyTo"/> property cannot be parsed by <see cref="M:RabbitMQ.Client.PublicationAddress.Parse(System.String)"/>.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.BasicProperties.Timestamp">
            <summary>
            Message timestamp.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.BasicProperties.Type">
            <summary>
            Message type name.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.BasicProperties.UserId">
            <summary>
            User Id.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.ClearAppId">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.AppId"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.ClearClusterId">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.ClusterId"/> property (cluster id is deprecated in AMQP 0-9-1).
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.ClearContentEncoding">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.ContentEncoding"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.ClearContentType">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.ContentType"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.ClearCorrelationId">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.CorrelationId"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.ClearDeliveryMode">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.DeliveryMode"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.ClearExpiration">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.Expiration"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.ClearHeaders">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.Headers"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.ClearMessageId">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.MessageId"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.ClearPriority">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.Priority"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.ClearReplyTo">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.ReplyTo"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.ClearTimestamp">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.Timestamp"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.ClearType">
            <summary>
            Clear the Type property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.ClearUserId">
            <summary>
            Clear the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.UserId"/> property.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.IsAppIdPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.AppId"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.IsClusterIdPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.ClusterId"/> property is present (cluster id is deprecated in AMQP 0-9-1).
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.IsContentEncodingPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.ContentEncoding"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.IsContentTypePresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.ContentType"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.IsCorrelationIdPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.CorrelationId"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.IsDeliveryModePresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.DeliveryMode"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.IsExpirationPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.Expiration"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.IsHeadersPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.Headers"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.IsMessageIdPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.MessageId"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.IsPriorityPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.Priority"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.IsReplyToPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.ReplyTo"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.IsTimestampPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.Timestamp"/> property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.IsTypePresent">
            <summary>
            Returns true if the Type property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.IsUserIdPresent">
            <summary>
            Returns true if the <see cref="P:RabbitMQ.Client.Impl.BasicProperties.UserId"/> UserId property is present.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.BasicProperties.SetPersistent(System.Boolean)">
            <summary>Sets <see cref="P:RabbitMQ.Client.Impl.BasicProperties.DeliveryMode"/> to either persistent (2) or non-persistent (1).</summary>
            <remarks>
            <para>
            The numbers 1 and 2 for delivery mode are "magic" in that
            they appear in the AMQP 0-8 and 0-9 specifications as part
            of the definition of the DeliveryMode Basic-class property,
            without being defined as named constants.
            </para>
            <para>
            Calling this method causes <see cref="P:RabbitMQ.Client.Impl.BasicProperties.DeliveryMode"/> to take on a  value.
            In order to reset <see cref="P:RabbitMQ.Client.Impl.BasicProperties.DeliveryMode"/> to the default empty condition, call <see cref="M:RabbitMQ.Client.Impl.BasicProperties.ClearDeliveryMode"/> .
            </para>
            </remarks>
        </member>
        <member name="T:RabbitMQ.Client.Impl.ChannelErrorException">
            <summary> Thrown when the server sends a frame along a channel
            that we do not currently have a Session entry in our
            SessionManager for. </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.ChannelErrorException.Channel">
            <summary>The channel number concerned.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.ContentHeaderBase.ProtocolClassId">
            <summary>
             Retrieve the AMQP class ID of this content header.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.ContentHeaderBase.ProtocolClassName">
            <summary>
             Retrieve the AMQP class name of this content header.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.ContentHeaderBase.ReadFrom(RabbitMQ.Util.NetworkBinaryReader)">
            <summary>
             Fill this instance from the given byte buffer stream.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.ContentHeaderPropertyReader.ReadTable">
            <returns>A type of <seealso cref="T:System.Collections.Generic.IDictionary`2"/>.</returns>
        </member>
        <member name="M:RabbitMQ.Client.Impl.ExtensionMethods.RandomItem``1(System.Collections.Generic.IList{``0})">
            <summary>
            Returns a random item from the list.
            </summary>
            <typeparam name="T">Element item type</typeparam>
            <param name="list">Input list</param>
            <returns></returns>
        </member>
        <member name="T:RabbitMQ.Client.Impl.HardProtocolException">
            <summary>Subclass of ProtocolException representing problems
            requiring a connection.close.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.IFrameHandler.ReadTimeout">
            <summary>Socket read timeout, in milliseconds. Zero signals "infinity".</summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.IFrameHandler.WriteTimeout">
            <summary>Socket write timeout, in milliseconds. Zero signals "infinity".</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFrameHandler.ReadFrame">
            <summary>Read a frame from the underlying
            transport. Returns null if the read operation timed out
            (see Timeout property).</summary>
        </member>
        <member name="T:RabbitMQ.Client.Impl.IFullModel">
            <summary>Not part of the public API. Extension of IModel to
            include utilities and connection-setup routines needed by the
            implementation side.</summary>
            
            <remarks>This interface is used by the API autogeneration
            process. The AMQP XML specifications are read by the spec
            compilation tool, and after the basic method interface and
            implementation classes are generated, this interface is
            scanned, and a spec-version-specific implementation is
            autogenerated. Annotations are used on certain methods, return
            types, and parameters, to customise the details of the
            autogeneration process.</remarks>
            
            <see cref="T:RabbitMQ.Client.Impl.ModelBase"/>
            <see cref="T:RabbitMQ.Client.Framing.Impl.Model"/>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.ConnectionTuneOk(System.UInt16,System.UInt32,System.UInt16)">
            <summary>Sends a Connection.TuneOk. Used during connection
            initialisation.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleBasicAck(System.UInt64,System.Boolean)">
            <summary>Handle incoming Basic.Ack methods. Signals a
            BasicAckEvent.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleBasicCancelOk(System.String)">
            <summary>Handle incoming Basic.CancelOk methods.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleBasicConsumeOk(System.String)">
            <summary>Handle incoming Basic.ConsumeOk methods.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleBasicDeliver(System.String,System.UInt64,System.Boolean,System.String,System.String,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>Handle incoming Basic.Deliver methods. Dispatches
            to waiting consumers.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleBasicGetEmpty">
            <summary>Handle incoming Basic.GetEmpty methods. Routes the
            information to a waiting Basic.Get continuation.</summary>
            <remarks>
             Note that the clusterId field is ignored, as in the
             specification it notes that it is "deprecated pending
             review".
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleBasicGetOk(System.UInt64,System.Boolean,System.String,System.String,System.UInt32,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>Handle incoming Basic.GetOk methods. Routes the
            information to a waiting Basic.Get continuation.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleBasicNack(System.UInt64,System.Boolean,System.Boolean)">
            <summary>Handle incoming Basic.Nack methods. Signals a
            BasicNackEvent.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleBasicRecoverOk">
            <summary>Handle incoming Basic.RecoverOk methods
            received in reply to Basic.Recover.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleBasicReturn(System.UInt16,System.String,System.String,System.String,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>Handle incoming Basic.Return methods. Signals a
            BasicReturnEvent.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleChannelClose(System.UInt16,System.String,System.UInt16,System.UInt16)">
            <summary>Handle an incoming Channel.Close. Shuts down the
            session and model.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleChannelCloseOk">
            <summary>Handle an incoming Channel.CloseOk.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleChannelFlow(System.Boolean)">
            <summary>Handle incoming Channel.Flow methods. Either
            stops or resumes sending the methods that have content.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleConnectionBlocked(System.String)">
            <summary>Handle an incoming Connection.Blocked.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleConnectionClose(System.UInt16,System.String,System.UInt16,System.UInt16)">
            <summary>Handle an incoming Connection.Close. Shuts down the
            connection and all sessions and models.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleConnectionOpenOk(System.String)">
            <summary>Handle an incoming Connection.OpenOk.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleConnectionSecure(System.Byte[])">
            <summary>Handle incoming Connection.Secure
            methods.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleConnectionStart(System.Byte,System.Byte,System.Collections.Generic.IDictionary{System.String,System.Object},System.Byte[],System.Byte[])">
            <summary>Handle an incoming Connection.Start. Used during
            connection initialisation.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleConnectionTune(System.UInt16,System.UInt32,System.UInt16)">
            <summary>Handle incoming Connection.Tune
            methods.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleConnectionUnblocked">
            <summary>Handle an incominga Connection.Unblocked.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel.HandleQueueDeclareOk(System.String,System.UInt32,System.UInt32)">
            <summary>Handle incoming Queue.DeclareOk methods. Routes the
            information to a waiting Queue.DeclareOk continuation.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_BasicCancel(System.String,System.Boolean)">
            <summary>Used to send a Basic.Cancel method. The public
            consume API calls this while also managing internal
            datastructures.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_BasicConsume(System.String,System.String,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>Used to send a Basic.Consume method. The public
            consume API calls this while also managing internal
            datastructures.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_BasicGet(System.String,System.Boolean)">
            <summary>Used to send a Basic.Get. Basic.Get is a special
            case, since it can result in a Basic.GetOk or a
            Basic.GetEmpty, so this level of manual control is
            required.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_BasicPublish(System.String,System.String,System.Boolean,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>Used to send a Basic.Publish method. Called by the
            public publish method after potential null-reference issues
            have been rectified.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_ChannelClose(System.UInt16,System.String,System.UInt16,System.UInt16)">
            <summary>Used to send a Channel.Close. Called during
            session shutdown.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_ChannelCloseOk">
            <summary>Used to send a Channel.CloseOk. Called during
            session shutdown.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_ChannelFlowOk(System.Boolean)">
            <summary>Used to send a Channel.FlowOk. Confirms that
            Channel.Flow from the broker was processed.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_ChannelOpen(System.String)">
            <summary>Used to send a Channel.Open. Called during session
            initialisation.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_ConfirmSelect(System.Boolean)">
            <summary>Used to send a Confirm.Select method. The public
            confirm API calls this while also managing internal
            datastructures.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_ConnectionClose(System.UInt16,System.String,System.UInt16,System.UInt16)">
            <summary>Used to send a Connection.Close. Called during
            connection shutdown.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_ConnectionCloseOk">
            <summary>Used to send a Connection.CloseOk. Called during
            connection shutdown.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_ConnectionOpen(System.String,System.String,System.Boolean)">
            <summary>Used to send a Connection.Open. Called during
            connection startup.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_ConnectionSecureOk(System.Byte[])">
            <summary>Used to send a Connection.SecureOk. Again, this is
            special, like Basic.Get.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_ConnectionStartOk(System.Collections.Generic.IDictionary{System.String,System.Object},System.String,System.Byte[],System.String)">
            <summary>Used to send a Connection.StartOk. This is
            special, like Basic.Get.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_ExchangeBind(System.String,System.String,System.String,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>Used to send a Exchange.Bind method. Called by the
            public bind method.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_ExchangeDeclare(System.String,System.String,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>Used to send a Exchange.Declare method. Called by the
            public declare method.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_ExchangeDelete(System.String,System.Boolean,System.Boolean)">
            <summary>Used to send a Exchange.Delete method. Called by the
            public delete method.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_ExchangeUnbind(System.String,System.String,System.String,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>Used to send a Exchange.Unbind method. Called by the
            public unbind method.
            </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_QueueBind(System.String,System.String,System.String,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>Used to send a Queue.Bind method. Called by the
            public bind method.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_QueueDeclare(System.String,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>Used to send a Queue.Declare method. Called by the
            public declare method.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_QueueDelete(System.String,System.Boolean,System.Boolean,System.Boolean)">
            <summary>Used to send a Queue.Delete method. Called by the
            public delete method.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.IFullModel._Private_QueuePurge(System.String,System.Boolean)">
            <summary>Used to send a Queue.Purge method. Called by the
            public purge method.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Impl.ConnectionTuneDetails">
            <summary>Essential information from an incoming Connection.Tune
            method.</summary>
        </member>
        <member name="F:RabbitMQ.Client.Impl.ConnectionTuneDetails.m_channelMax">
            <summary>The peer's suggested channel-max parameter.</summary>
        </member>
        <member name="F:RabbitMQ.Client.Impl.ConnectionTuneDetails.m_frameMax">
            <summary>The peer's suggested frame-max parameter.</summary>
        </member>
        <member name="F:RabbitMQ.Client.Impl.ConnectionTuneDetails.m_heartbeat">
            <summary>The peer's suggested heartbeat parameter.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.ISession.ChannelNumber">
            <summary>
            Gets the channel number.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.ISession.CloseReason">
            <summary>
            Gets the close reason.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.ISession.CommandReceived">
            <summary>
             Single recipient - no need for multiple handlers to be informed of arriving commands.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.ISession.Connection">
            <summary>
            Gets the connection.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.ISession.IsOpen">
            <summary>
            Gets a value indicating whether this session is open.
            </summary>
        </member>
        <member name="E:RabbitMQ.Client.Impl.ISession.SessionShutdown">
            <summary>
             Multicast session shutdown event.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Impl.MainSession">
            <summary>Small ISession implementation used only for channel 0.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.MainSession.SetSessionClosing(System.Boolean)">
            <summary> Set channel 0 as quiescing </summary>
            <remarks>
             Method should be idempotent. Cannot use base.Close
             method call because that would prevent us from
             sending/receiving Close/CloseOk commands
            </remarks>
        </member>
        <member name="T:RabbitMQ.Client.Impl.MalformedFrameException">
            <summary>Thrown when frame parsing code detects an error in the
            wire-protocol encoding of a frame.</summary>
            <remarks>
            For example, potential MalformedFrameException conditions
            include frames too short, frames missing their end marker, and
            invalid protocol negotiation headers.
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.Impl.MethodBase.ProtocolClassId">
            <summary>
            Retrieves the class ID number of this method, as defined in the AMQP specification XML.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.MethodBase.ProtocolMethodId">
            <summary>
            Retrieves the method ID number of this method, as defined in the AMQP specification XML.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.MethodBase.ProtocolMethodName">
            <summary>
            Retrieves the name of this method - for debugging use.
            </summary>
        </member>
        <member name="F:RabbitMQ.Client.Impl.ModelBase.m_connectionStartCell">
            <summary>Only used to kick-start a connection open
            sequence. See <see cref="M:RabbitMQ.Client.Framing.Impl.Connection.Open(System.Boolean)"/> </summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.ModelBase.OnModelShutdown(RabbitMQ.Client.ShutdownEventArgs)">
            <summary>Broadcasts notification of the final shutdown of the model.</summary>
            <remarks>
            <para>
            Do not call anywhere other than at the end of OnSessionShutdown.
            </para>
            <para>
            Must not be called when m_closeReason == null, because
            otherwise there's a window when a new continuation could be
            being enqueued at the same time as we're broadcasting the
            shutdown event. See the definition of Enqueue() above.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Impl.ModelBase.HandleConnectionTune(System.UInt16,System.UInt32,System.UInt16)">
            <summary>Handle incoming Connection.Tune
            methods.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Impl.ProtocolException">
            <summary> Instances of subclasses of subclasses
            HardProtocolException and SoftProtocolException are thrown in
            situations when we detect a problem with the connection-,
            channel- or wire-level parts of the AMQP protocol. </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.ProtocolException.ReplyCode">
            <summary>Retrieve the reply code to use in a
            connection/channel close method.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.ProtocolException.ShutdownReason">
            <summary>Retrieve the shutdown details to use in a
            connection/channel close method. Defaults to using
            ShutdownInitiator.Library, and this.ReplyCode and
            this.Message as the reply code and text,
            respectively.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Impl.QuiescingSession">
            <summary>Small ISession implementation used during channel quiescing.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Impl.RpcContinuationQueue">
            <summary>Manages a queue of waiting AMQP RPC requests.</summary>
            <remarks>
            <para>
             Currently, pipelining of requests is forbidden by this
             implementation. The AMQP 0-8 and 0-9 specifications themselves
             forbid pipelining, but only by the skin of their teeth and
             under a somewhat generous reading.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Impl.RpcContinuationQueue.Enqueue(RabbitMQ.Client.Impl.IRpcContinuation)">
            <summary>Enqueue a continuation, marking a pending RPC.</summary>
            <remarks>
            <para>
             Continuations are retrieved in FIFO order by calling Next().
            </para>
            <para>
             In the current implementation, only one continuation can
             be queued up at once. Calls to Enqueue() when a
             continuation is already enqueued will result in
             NotSupportedException being thrown.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Impl.RpcContinuationQueue.HandleModelShutdown(RabbitMQ.Client.ShutdownEventArgs)">
            <summary>Interrupt all waiting continuations.</summary>
            <remarks>
            <para>
             There's just the one potential waiter in the current
             implementation.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Impl.RpcContinuationQueue.Next">
            <summary>Retrieve the next waiting continuation.</summary>
            <remarks>
            <para>
             It is an error to call this method when there are no
             waiting continuations. In the current implementation, if
             this happens, null will be returned (which will usually
             result in an immediate NullPointerException in the
             caller). Correct code will always arrange for a
             continuation to have been Enqueue()d before calling this
             method.
            </para>
            </remarks>
        </member>
        <member name="T:RabbitMQ.Client.Impl.Session">
            <summary>Normal ISession implementation used during normal channel operation.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.SessionManager.AutoCloseConnection">
            <summary>Called from CheckAutoClose, in a separate thread,
            when we decide to close the connection.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.SessionManager.CheckAutoClose">
            <summary>If m_autoClose and there are no active sessions
            remaining, Close()s the connection with reason code
            200.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.SessionManager.Swap(System.Int32,RabbitMQ.Client.Impl.ISession)">
            <summary>Replace an active session slot with a new ISession
            implementation. Used during channel quiescing.</summary>
            <remarks>
             Make sure you pass in a channelNumber that's currently in
             use, as if the slot is unused, you'll get a null pointer
             exception.
            </remarks>
        </member>
        <member name="T:RabbitMQ.Client.Impl.SoftProtocolException">
            <summary>Subclass of ProtocolException representing problems
            requiring a channel.close.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Impl.SyntaxError">
            <summary> Thrown when our peer sends a frame that contains
            illegal values for one or more fields. </summary>
        </member>
        <member name="T:RabbitMQ.Client.Impl.UnexpectedFrameException">
            <summary>
            Thrown when the connection receives a frame that it wasn't expecting.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.Impl.UnknownClassOrMethodException">
            <summary>
            Thrown when the protocol handlers detect an unknown class
            number or method number.
            </summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.UnknownClassOrMethodException.ClassId">
            <summary>The AMQP content-class ID.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Impl.UnknownClassOrMethodException.MethodId">
            <summary>The AMQP method ID within the content-class, or 0 if none.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Impl.WireFormatting.ReadTable(RabbitMQ.Util.NetworkBinaryReader)">
            <summary>Reads an AMQP "table" definition from the reader.</summary>
            <remarks>
             Supports the AMQP 0-8/0-9 standard entry types S, I, D, T
             and F, as well as the QPid-0-8 specific b, d, f, l, s, t,
             x and V types and the AMQP 0-9-1 A type.
            </remarks>
             <returns>A <seealso cref="T:System.Collections.Generic.IDictionary`2"/>.</returns>
        </member>
        <member name="M:RabbitMQ.Client.Impl.WireFormatting.WriteTable(RabbitMQ.Util.NetworkBinaryWriter,System.Collections.IDictionary)">
            <summary>Writes an AMQP "table" to the writer.</summary>
            <remarks>
            <para>
             In this method, we assume that the stream that backs our
             NetworkBinaryWriter is a positionable stream - which it is
             currently (see Frame.m_accumulator, Frame.GetWriter and
             Command.Transmit).
            </para>
            <para>
             Supports the AMQP 0-8/0-9 standard entry types S, I, D, T
             and F, as well as the QPid-0-8 specific b, d, f, l, s, t
             x and V types and the AMQP 0-9-1 A type.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Impl.WireFormatting.WriteTable(RabbitMQ.Util.NetworkBinaryWriter,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>Writes an AMQP "table" to the writer.</summary>
            <remarks>
            <para>
             In this method, we assume that the stream that backs our
             NetworkBinaryWriter is a positionable stream - which it is
             currently (see Frame.m_accumulator, Frame.GetWriter and
             Command.Transmit).
            </para>
            <para>
             Supports the AMQP 0-8/0-9 standard entry types S, I, D, T
             and F, as well as the QPid-0-8 specific b, d, f, l, s, t
             x and V types and the AMQP 0-9-1 A type.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.AutorecoveringConnection.Abort">
            <summary>API-side invocation of connection abort.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.AutorecoveringConnection.Abort(System.UInt16,System.String)">
            <summary>API-side invocation of connection abort.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.AutorecoveringConnection.Abort(System.Int32)">
            <summary>API-side invocation of connection abort with timeout.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.AutorecoveringConnection.Abort(System.UInt16,System.String,System.Int32)">
            <summary>API-side invocation of connection abort with timeout.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.AutorecoveringConnection.Close">
            <summary>API-side invocation of connection.close.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.AutorecoveringConnection.Close(System.UInt16,System.String)">
            <summary>API-side invocation of connection.close.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.AutorecoveringConnection.Close(System.Int32)">
            <summary>API-side invocation of connection.close with timeout.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.AutorecoveringConnection.Close(System.UInt16,System.String,System.Int32)">
            <summary>API-side invocation of connection.close with timeout.</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Impl.Connection.m_heartbeatFrame">
            <summary>Heartbeat frame for transmission. Reusable across connections.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Framing.Impl.Connection.Protocol">
            <summary>Another overload of a Protocol property, useful
            for exposing a tighter type.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Framing.Impl.Connection.RabbitMQ#Client#IConnection#Protocol">
            <summary>Explicit implementation of IConnection.Protocol.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.Connection.Close(RabbitMQ.Client.ShutdownEventArgs,System.Boolean,System.Int32)">
            <summary>Try to close connection in a graceful way</summary>
            <remarks>
            <para>
            Shutdown reason contains code and text assigned when closing the connection,
            as well as the information about what initiated the close
            </para>
            <para>
            Abort flag, if true, signals to close the ongoing connection immediately
            and do not report any errors if it was already closed.
            </para>
            <para>
            Timeout determines how much time internal close operations should be given
            to complete. Negative or Timeout.Infinite value mean infinity.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.Connection.ClosingLoop">
            <remarks>
             Loop only used while quiescing. Use only to cleanly close connection
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.Connection.HandleDomainUnload(System.Object,System.EventArgs)">
            <remarks>
            We need to close the socket, otherwise attempting to unload the domain
            could cause a CannotUnloadAppDomainException
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.Connection.OnShutdown">
            <summary>Broadcasts notification of the final shutdown of the connection.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.Connection.QuiesceChannel(RabbitMQ.Client.Impl.SoftProtocolException)">
            <summary>
             Sets the channel named in the SoftProtocolException into
             "quiescing mode", where we issue a channel.close and
             ignore everything except for subsequent channel.close
             messages and the channel.close-ok reply that should
             eventually arrive.
            </summary>
            <remarks>
            <para>
             Since a well-behaved peer will not wait indefinitely before
             issuing the close-ok, we don't bother with a timeout here;
             compare this to the case of a connection.close-ok, where a
             timeout is necessary.
            </para>
            <para>
             We need to send the close method and politely wait for a
             reply before marking the channel as available for reuse.
            </para>
            <para>
             As soon as SoftProtocolException is detected, we should stop
             servicing ordinary application work, and should concentrate
             on bringing down the channel as quickly and gracefully as
             possible. The way this is done, as per the close-protocol,
             is to signal closure up the stack *before* sending the
             channel.close, by invoking ISession.Close. Once the upper
             layers have been signalled, we are free to do what we need
             to do to clean up and shut down the channel.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.Connection.TerminateMainloop">
            <remarks>
             May be called more than once. Should therefore be idempotent.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.Connection.Abort">
            <summary>API-side invocation of connection abort.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.Connection.Abort(System.UInt16,System.String)">
            <summary>API-side invocation of connection abort.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.Connection.Abort(System.Int32)">
            <summary>API-side invocation of connection abort with timeout.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.Connection.Abort(System.UInt16,System.String,System.Int32)">
            <summary>API-side invocation of connection abort with timeout.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.Connection.Close">
            <summary>API-side invocation of connection.close.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.Connection.Close(System.UInt16,System.String)">
            <summary>API-side invocation of connection.close.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.Connection.Close(System.Int32)">
            <summary>API-side invocation of connection.close with timeout.</summary>
        </member>
        <member name="M:RabbitMQ.Client.Framing.Impl.Connection.Close(System.UInt16,System.String,System.Int32)">
            <summary>API-side invocation of connection.close with timeout.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ConnectionStart">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ConnectionStartOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ConnectionSecure">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ConnectionSecureOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ConnectionTune">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ConnectionTuneOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ConnectionOpen">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ConnectionOpenOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ConnectionClose">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ConnectionCloseOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ConnectionBlocked">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ConnectionUnblocked">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ChannelOpen">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ChannelOpenOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ChannelFlow">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ChannelFlowOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ChannelClose">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ChannelCloseOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ExchangeDeclare">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ExchangeDeclareOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ExchangeDelete">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ExchangeDeleteOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ExchangeBind">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ExchangeBindOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ExchangeUnbind">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ExchangeUnbindOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.QueueDeclare">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.QueueDeclareOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.QueueBind">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.QueueBindOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.QueueUnbind">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.QueueUnbindOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.QueuePurge">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.QueuePurgeOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.QueueDelete">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.QueueDeleteOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicQos">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicQosOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicConsume">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicConsumeOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicCancel">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicCancelOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicPublish">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicReturn">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicDeliver">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicGet">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicGetOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicGetEmpty">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicAck">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicReject">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicRecoverAsync">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicRecover">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicRecoverOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.BasicNack">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.TxSelect">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.TxSelectOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.TxCommit">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.TxCommitOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.TxRollback">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.TxRollbackOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ConfirmSelect">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.Impl.ConfirmSelectOk">
            <summary>Autogenerated type. Private implementation class - do not use directly.</summary>
        </member>
        <member name="P:RabbitMQ.Client.Framing.Protocol.MajorVersion">
            <summary>Protocol major version (= 0)</summary>
        </member>
        <member name="P:RabbitMQ.Client.Framing.Protocol.MinorVersion">
            <summary>Protocol minor version (= 9)</summary>
        </member>
        <member name="P:RabbitMQ.Client.Framing.Protocol.Revision">
            <summary>Protocol revision (= 1)</summary>
        </member>
        <member name="P:RabbitMQ.Client.Framing.Protocol.ApiName">
            <summary>Protocol API name (= :AMQP_0_9_1)</summary>
        </member>
        <member name="P:RabbitMQ.Client.Framing.Protocol.DefaultPort">
            <summary>Default TCP port (= 5672)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.FrameMethod">
            <summary>(= 1)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.FrameHeader">
            <summary>(= 2)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.FrameBody">
            <summary>(= 3)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.FrameHeartbeat">
            <summary>(= 8)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.FrameMinSize">
            <summary>(= 4096)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.FrameEnd">
            <summary>(= 206)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.ReplySuccess">
            <summary>(= 200)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.ContentTooLarge">
            <summary>(= 311)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.NoConsumers">
            <summary>(= 313)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.ConnectionForced">
            <summary>(= 320)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.InvalidPath">
            <summary>(= 402)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.AccessRefused">
            <summary>(= 403)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.NotFound">
            <summary>(= 404)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.ResourceLocked">
            <summary>(= 405)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.PreconditionFailed">
            <summary>(= 406)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.FrameError">
            <summary>(= 501)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.SyntaxError">
            <summary>(= 502)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.CommandInvalid">
            <summary>(= 503)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.ChannelError">
            <summary>(= 504)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.UnexpectedFrame">
            <summary>(= 505)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.ResourceError">
            <summary>(= 506)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.NotAllowed">
            <summary>(= 530)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.NotImplemented">
            <summary>(= 540)</summary>
        </member>
        <member name="F:RabbitMQ.Client.Framing.Constants.InternalError">
            <summary>(= 541)</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IConnectionStart">
            <summary>Autogenerated type. AMQP specification method "connection.start".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IConnectionStartOk">
            <summary>Autogenerated type. AMQP specification method "connection.start-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IConnectionSecure">
            <summary>Autogenerated type. AMQP specification method "connection.secure".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IConnectionSecureOk">
            <summary>Autogenerated type. AMQP specification method "connection.secure-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IConnectionTune">
            <summary>Autogenerated type. AMQP specification method "connection.tune".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IConnectionTuneOk">
            <summary>Autogenerated type. AMQP specification method "connection.tune-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IConnectionOpen">
            <summary>Autogenerated type. AMQP specification method "connection.open".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IConnectionOpenOk">
            <summary>Autogenerated type. AMQP specification method "connection.open-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IConnectionClose">
            <summary>Autogenerated type. AMQP specification method "connection.close".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IConnectionCloseOk">
            <summary>Autogenerated type. AMQP specification method "connection.close-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IConnectionBlocked">
            <summary>Autogenerated type. AMQP specification method "connection.blocked".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IConnectionUnblocked">
            <summary>Autogenerated type. AMQP specification method "connection.unblocked".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IChannelOpen">
            <summary>Autogenerated type. AMQP specification method "channel.open".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IChannelOpenOk">
            <summary>Autogenerated type. AMQP specification method "channel.open-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IChannelFlow">
            <summary>Autogenerated type. AMQP specification method "channel.flow".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IChannelFlowOk">
            <summary>Autogenerated type. AMQP specification method "channel.flow-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IChannelClose">
            <summary>Autogenerated type. AMQP specification method "channel.close".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IChannelCloseOk">
            <summary>Autogenerated type. AMQP specification method "channel.close-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IExchangeDeclare">
            <summary>Autogenerated type. AMQP specification method "exchange.declare".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IExchangeDeclareOk">
            <summary>Autogenerated type. AMQP specification method "exchange.declare-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IExchangeDelete">
            <summary>Autogenerated type. AMQP specification method "exchange.delete".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IExchangeDeleteOk">
            <summary>Autogenerated type. AMQP specification method "exchange.delete-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IExchangeBind">
            <summary>Autogenerated type. AMQP specification method "exchange.bind".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IExchangeBindOk">
            <summary>Autogenerated type. AMQP specification method "exchange.bind-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IExchangeUnbind">
            <summary>Autogenerated type. AMQP specification method "exchange.unbind".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IExchangeUnbindOk">
            <summary>Autogenerated type. AMQP specification method "exchange.unbind-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IQueueDeclare">
            <summary>Autogenerated type. AMQP specification method "queue.declare".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IQueueDeclareOk">
            <summary>Autogenerated type. AMQP specification method "queue.declare-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IQueueBind">
            <summary>Autogenerated type. AMQP specification method "queue.bind".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IQueueBindOk">
            <summary>Autogenerated type. AMQP specification method "queue.bind-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IQueueUnbind">
            <summary>Autogenerated type. AMQP specification method "queue.unbind".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IQueueUnbindOk">
            <summary>Autogenerated type. AMQP specification method "queue.unbind-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IQueuePurge">
            <summary>Autogenerated type. AMQP specification method "queue.purge".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IQueuePurgeOk">
            <summary>Autogenerated type. AMQP specification method "queue.purge-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IQueueDelete">
            <summary>Autogenerated type. AMQP specification method "queue.delete".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IQueueDeleteOk">
            <summary>Autogenerated type. AMQP specification method "queue.delete-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicQos">
            <summary>Autogenerated type. AMQP specification method "basic.qos".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicQosOk">
            <summary>Autogenerated type. AMQP specification method "basic.qos-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicConsume">
            <summary>Autogenerated type. AMQP specification method "basic.consume".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicConsumeOk">
            <summary>Autogenerated type. AMQP specification method "basic.consume-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicCancel">
            <summary>Autogenerated type. AMQP specification method "basic.cancel".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicCancelOk">
            <summary>Autogenerated type. AMQP specification method "basic.cancel-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicPublish">
            <summary>Autogenerated type. AMQP specification method "basic.publish".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicReturn">
            <summary>Autogenerated type. AMQP specification method "basic.return".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicDeliver">
            <summary>Autogenerated type. AMQP specification method "basic.deliver".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicGet">
            <summary>Autogenerated type. AMQP specification method "basic.get".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicGetOk">
            <summary>Autogenerated type. AMQP specification method "basic.get-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicGetEmpty">
            <summary>Autogenerated type. AMQP specification method "basic.get-empty".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicAck">
            <summary>Autogenerated type. AMQP specification method "basic.ack".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicReject">
            <summary>Autogenerated type. AMQP specification method "basic.reject".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicRecoverAsync">
            <summary>Autogenerated type. AMQP specification method "basic.recover-async".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicRecover">
            <summary>Autogenerated type. AMQP specification method "basic.recover".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicRecoverOk">
            <summary>Autogenerated type. AMQP specification method "basic.recover-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IBasicNack">
            <summary>Autogenerated type. AMQP specification method "basic.nack".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.ITxSelect">
            <summary>Autogenerated type. AMQP specification method "tx.select".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.ITxSelectOk">
            <summary>Autogenerated type. AMQP specification method "tx.select-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.ITxCommit">
            <summary>Autogenerated type. AMQP specification method "tx.commit".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.ITxCommitOk">
            <summary>Autogenerated type. AMQP specification method "tx.commit-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.ITxRollback">
            <summary>Autogenerated type. AMQP specification method "tx.rollback".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.ITxRollbackOk">
            <summary>Autogenerated type. AMQP specification method "tx.rollback-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IConfirmSelect">
            <summary>Autogenerated type. AMQP specification method "confirm.select".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.IConfirmSelectOk">
            <summary>Autogenerated type. AMQP specification method "confirm.select-ok".</summary>
        </member>
        <member name="T:RabbitMQ.Client.Framing.BasicProperties">
            <summary>Autogenerated type. AMQP specification content header properties for content class "basic"</summary>
        </member>
        <member name="T:RabbitMQ.Client.Apigen.Attributes.AmqpApigenAttribute">
            <summary>Base class for attributes for controlling the API
            autogeneration process.</summary>
        </member>
        <member name="F:RabbitMQ.Client.Apigen.Attributes.AmqpApigenAttribute.m_namespaceName">
            <summary>The specification namespace (i.e. version) that
            this attribute applies to, or null for all specification
            versions.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Apigen.Attributes.AmqpMethodDoNotImplementAttribute">
            <summary>Causes the API generator to ignore the attributed method.</summary>
            
            <remarks>Mostly used to declare convenience overloads of
            various AMQP methods in the IModel interface. Also used
            to omit an autogenerated implementation of a method which
            is not required for one protocol version. The API
            autogeneration process should of course not attempt to produce
            an implementation of the convenience methods, as they will be
            implemented by hand with sensible defaults, delegating to the
            autogenerated variant of the method concerned.</remarks>
        </member>
        <member name="T:RabbitMQ.Client.Apigen.Attributes.AmqpAsynchronousHandlerAttribute">
            <summary>Causes the API generator to generate asynchronous
            receive code for the attributed method.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Apigen.Attributes.AmqpUnsupportedAttribute">
            <summary>Causes the API generator to generate
            exception-throwing code for, instead of simply ignoring, the
            attributed method.</summary>
            
            <see cref="T:RabbitMQ.Client.Apigen.Attributes.AmqpMethodDoNotImplementAttribute"/>
        </member>
        <member name="T:RabbitMQ.Client.Apigen.Attributes.AmqpFieldMappingAttribute">
            <summary>Informs the API generator which AMQP method field to
            use for either a parameter in a request, or for a simple result
            in a reply.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Apigen.Attributes.AmqpMethodMappingAttribute">
            <summary>Informs the API generator which AMQP method to use for
            either a request (if applied to an IModel method) or a reply
            (if applied to an IModel method result).</summary>
        </member>
        <member name="T:RabbitMQ.Client.Apigen.Attributes.AmqpNowaitArgumentAttribute">
            <summary>This attribute, if placed on a parameter in an IModel
            method, causes it to be interpreted as a "nowait" parameter for
            the purposes of autogenerated RPC reply continuation management
            and control.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Apigen.Attributes.AmqpContentHeaderFactoryAttribute">
            <summary>This attribute, if placed on a method in IModel,
            causes the method to be interpreted as a factory method
            producing a protocol-specific implementation of a common
            content header interface.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Apigen.Attributes.AmqpContentHeaderMappingAttribute">
            <summary>This attribute, if placed on a parameter in a
            content-carrying IModel method, causes it to be sent as part of
            the content header frame.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Apigen.Attributes.AmqpContentBodyMappingAttribute">
            <summary>This attribute, if placed on a parameter in a
            content-carrying IModel method, causes it to be sent as part of
            the content body frame.</summary>
        </member>
        <member name="T:RabbitMQ.Client.Apigen.Attributes.AmqpForceOneWayAttribute">
            <summary>This attribute, placed on an IModel method, causes
            what would normally be an RPC, sent with ModelRpc, to be sent
            as if it were oneway, with ModelSend. The assumption that this
            is for a custom continuation (e.g. for BasicConsume/BasicCancel
            etc.)</summary>
        </member>
        <member name="T:RabbitMQ.Client.TcpClientAdapter">
            <summary>
            Simple wrapper around TcpClient.
            </summary>
        </member>
        <member name="T:RabbitMQ.Client.MessagePatterns.ISubscription">
            <summary>Manages a subscription to a queue.</summary>
            <remarks>
            <para>
             This interface is provided to make creation of test doubles
             for <see cref="T:RabbitMQ.Client.MessagePatterns.Subscription" /> easier.
            </para>
            </remarks>
        </member>
        <member name="T:RabbitMQ.Client.MessagePatterns.SimpleRpcClient">
            <summary>Implements a simple RPC client.</summary>
            <remarks>
            <para>
             This class sends requests that can be processed by remote
             SimpleRpcServer instances.
            </para>
            <para>
             The basic pattern for accessing a remote service is to
             determine the exchange name and routing key needed for
             submissions of service requests, and to construct a
             SimpleRpcClient instance using that address. Once constructed,
             the various Call() and Cast() overloads can be used to send
             requests and receive the corresponding replies.
            </para>
            <example><code>
            	string queueName = "ServiceRequestQueue"; // See also Subscription ctors
            	using (IConnection conn = new ConnectionFactory()
            	                                .CreateConnection(serverAddress)) {
            	    using (IModel ch = conn.CreateModel()) {
            	        SimpleRpcClient client =
            	            new SimpleRpcClient(ch, queueName);
            	        client.TimeoutMilliseconds = 5000; // optional
            
            	        /// ... make use of the various Call() overloads
            	    }
            	}
            </code></example>
            <para>
             Instances of this class declare a queue, so it is the user's
             responsibility to ensure that the exchange concerned exists
             (using IModel.ExchangeDeclare) before invoking Call() or
             Cast().
            </para>
            <para>
             This class implements only a few basic RPC message formats -
             to extend it with support for more formats, either subclass,
             or transcode the messages before transmission using the
             built-in byte[] format.
            </para>
            </remarks>
            <see cref="T:RabbitMQ.Client.MessagePatterns.SimpleRpcServer"/>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.#ctor(RabbitMQ.Client.IModel)">
            <summary>Construct an instance with no configured
            Address. The Address property must be set before Call() or
            Cast() are called.</summary>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.#ctor(RabbitMQ.Client.IModel,System.String)">
            <summary>Construct an instance that will deliver to the
            default exchange (""), with routing key equal to the passed
            in queueName, thereby delivering directly to a named queue
            on the AMQP server.</summary>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.#ctor(RabbitMQ.Client.IModel,System.String,System.String,System.String)">
            <summary>Construct an instance that will deliver to the
            named and typed exchange, with the given routing
            key.</summary>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.#ctor(RabbitMQ.Client.IModel,RabbitMQ.Client.PublicationAddress)">
            <summary>Construct an instance that will deliver to the
            given address.</summary>
        </member>
        <member name="E:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.Disconnected">
            <summary>This event is fired whenever Call() detects the
            disconnection of the underlying Subscription while waiting
            for a reply from the service.</summary>
            <remarks>
             See also OnDisconnected(). Note that the sending of a
             request may result in OperationInterruptedException before
             the request is even sent.
            </remarks>
        </member>
        <member name="E:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.TimedOut">
            <summary>This event is fired whenever Call() decides that a
            timeout has occurred while waiting for a reply from the
            service.</summary>
            <remarks>
             See also OnTimedOut().
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.Address">
            <summary>Retrieve or modify the address that will be used
            for the next Call() or Cast().</summary>
            <remarks>
             This address represents the service, i.e. the destination
             service requests should be published to. It can be changed
             at any time before a Call() or Cast() request is sent -
             the value at the time of the call is used by Call() and
             Cast().
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.Model">
            <summary>Retrieve the IModel this instance uses to communicate.</summary>
        </member>
        <member name="P:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.Subscription">
            <summary>Retrieve the Subscription that is used to receive
            RPC replies corresponding to Call() RPC requests. May be
            null.</summary>
            <remarks>
            <para>
             Upon construction, this property will be null. It is
             initialised by the protected virtual method
             EnsureSubscription upon the first call to Call(). Calls to
             Cast() do not initialise the subscription, since no
             replies are expected or possible when using Cast().
            </para>
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.TimeoutMilliseconds">
            <summary>Retrieve or modify the timeout (in milliseconds)
            that will be used for the next Call().</summary>
            <remarks>
            <para>
             This property defaults to
             System.Threading.Timeout.Infinite (i.e. -1). If it is set
             to any other value, Call() will only wait for the
             specified amount of time before returning indicating a
             timeout.
            </para>
            <para>
             See also TimedOut event and OnTimedOut().
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.Call(System.Object[])">
            <summary>Sends a "jms/stream-message"-encoded RPC request,
            and expects an RPC reply in the same format.</summary>
            <remarks>
            <para>
             The arguments passed in must be of types that are
             representable as JMS StreamMessage values, and so must the
             results returned from the service in its reply message.
            </para>
            <para>
             Calls OnTimedOut() and OnDisconnected() when a timeout or
             disconnection, respectively, is detected when waiting for
             our reply.
            </para>
            <para>
             Returns null if the request timed out or if we were
             disconnected before a reply arrived.
            </para>
            <para>
             The reply message, if any, is acknowledged to the AMQP
             server via Subscription.Ack().
            </para>
            </remarks>
            <see cref="T:RabbitMQ.Client.Content.IStreamMessageBuilder"/>
            <see cref="T:RabbitMQ.Client.Content.IStreamMessageReader"/>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.Call(System.Byte[])">
            <summary>Sends a simple byte[] message, without any custom
            headers or properties.</summary>
            <remarks>
            <para>
             Delegates directly to Call(IBasicProperties, byte[]), and
             discards the properties of the received reply, returning
             only the body of the reply.
            </para>
            <para>
             Calls OnTimedOut() and OnDisconnected() when a timeout or
             disconnection, respectively, is detected when waiting for
             our reply.
            </para>
            <para>
             Returns null if the request timed out or if we were
             disconnected before a reply arrived.
            </para>
            <para>
             The reply message, if any, is acknowledged to the AMQP
             server via Subscription.Ack().
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.Call(RabbitMQ.Client.IBasicProperties,System.Byte[],RabbitMQ.Client.IBasicProperties@)">
            <summary>Sends a byte[] message and IBasicProperties
            header, returning both the body and headers of the received
            reply.</summary>
            <remarks>
            <para>
             Sets the "replyProperties" outbound parameter to the
             properties of the received reply, and returns the byte[]
             body of the reply.
            </para>
            <para>
             Calls OnTimedOut() and OnDisconnected() when a timeout or
             disconnection, respectively, is detected when waiting for
             our reply.
            </para>
            <para>
             Both sets "replyProperties" to null and returns null when
             either the request timed out or we were disconnected
             before a reply arrived.
            </para>
            <para>
             The reply message, if any, is acknowledged to the AMQP
             server via Subscription.Ack().
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.Call(RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>Sends a byte[]/IBasicProperties RPC request,
            returning full information about the delivered reply as a
            BasicDeliverEventArgs.</summary>
            <remarks>
            <para>
             This is the most general/lowest-level Call()-style method
             on SimpleRpcClient. It sets CorrelationId and ReplyTo on
             the request message's headers before transmitting the
             request to the service via the AMQP server. If the reply's
             CorrelationId does not match the request's CorrelationId,
             ProtocolViolationException will be thrown.
            </para>
            <para>
             Calls OnTimedOut() and OnDisconnected() when a timeout or
             disconnection, respectively, is detected when waiting for
             our reply.
            </para>
            <para>
             Returns null if the request timed out or if we were
             disconnected before a reply arrived.
            </para>
            <para>
             The reply message, if any, is acknowledged to the AMQP
             server via Subscription.Ack().
            </para>
            </remarks>
            <see cref="T:RabbitMQ.Client.ProtocolViolationException"/>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.Cast(RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>Sends an asynchronous/one-way message to the
            service.</summary>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.Close">
            <summary>Close the reply subscription associated with this instance, if any.</summary>
            <remarks>
             Simply delegates to calling Subscription.Close(). Clears
             the Subscription property, so that subsequent Call()s, if
             any, will re-initialize it to a fresh Subscription
             instance.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.OnDisconnected">
            <summary>Signals that the Subscription we use for receiving
            our RPC replies was disconnected while we were
            waiting.</summary>
            <remarks>
             Fires the Disconnected event.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.OnTimedOut">
            <summary>Signals that the configured timeout fired while
            waiting for an RPC reply.</summary>
            <remarks>
             Fires the TimedOut event.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.System#IDisposable#Dispose">
            <summary>Implement the IDisposable interface, permitting
            SimpleRpcClient instances to be used in using
            statements.</summary>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.EnsureSubscription">
            <summary>Should initialise m_subscription to be non-null
            and usable for fetching RPC replies from the service
            through the AMQP server.</summary>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcClient.RetrieveReply(System.String)">
            <summary>Retrieves the reply for the request with the given
            correlation ID from our internal Subscription.</summary>
            <remarks>
             Currently requires replies to arrive in the same order as
             the requests were sent out. Subclasses may override this
             to provide more sophisticated behaviour.
            </remarks>
        </member>
        <member name="T:RabbitMQ.Client.MessagePatterns.SimpleRpcServer">
            <summary>Implements a simple RPC service, responding to
            requests received via a Subscription.</summary>
            <remarks>
            <para>
             This class interprets requests such as those sent by instances
             of SimpleRpcClient.
            </para>
            <para>
             The basic pattern for implementing a service is to subclass
             SimpleRpcServer, overriding HandleCall and HandleCast as
             appropriate, and then to create a Subscription object for
             receiving requests from clients, and start an instance of the
             SimpleRpcServer subclass with the Subscription.
            </para>
            <example><code>
            	string queueName = "ServiceRequestQueue"; // See also Subscription ctors
            	using (IConnection conn = new ConnectionFactory()
            	                                .CreateConnection(serverAddress)) {
            	    using (IModel ch = conn.CreateModel()) {
            	        Subscription sub = new Subscription(ch, queueName);
            	        new MySimpleRpcServerSubclass(sub).MainLoop();
            	    }
            	}
            </code></example>
            <para>
             Note that this class itself does not declare any resources
             (exchanges, queues or bindings). The Subscription we use for
             receiving RPC requests should have already declared all the
             resources we need. See the Subscription constructors and the
             Subscription.Bind method.
            </para>
            <para>
             If you are implementing a service that responds to
             "jms/stream-message"-formatted requests (as implemented by
             RabbitMQ.Client.Content.IStreamMessageReader), override
             HandleStreamMessageCall. Otherwise, override HandleSimpleCall
             or HandleCall as appropriate. Asynchronous, one-way requests
             are dealt with by HandleCast etc.
            </para>
            <para>
             Every time a request is successfully received and processed
             within the server's MainLoop, the request message is Ack()ed
             using Subscription.Ack before the next request is
             retrieved. This causes the Subscription object to take care of
             acknowledging receipt and processing of the request message.
            </para>
            <para>
             If transactional service is enabled, via SetTransactional(),
             then after every successful ProcessRequest, IModel.TxCommit is
             called. Making use of transactional service has effects on all
             parts of the application that share an IModel instance,
             completely changing the style of interaction with the AMQP
             server. For this reason, it is initially disabled, and must be
             explicitly enabled with a call to SetTransactional(). Please
             see the documentation for SetTransactional() for details.
            </para>
            <para>
             To stop a running RPC server, call Close(). This will in turn
             Close() the Subscription, which will cause MainLoop() to
             return to its caller.
            </para>
            <para>
             Unless overridden, ProcessRequest examines properties in the
             request content header, and uses them to dispatch to one of
             the Handle[...]() methods. See the documentation for
             ProcessRequest and each Handle[...] method for details.
            </para>
            </remarks>
            <see cref="T:RabbitMQ.Client.MessagePatterns.SimpleRpcClient"/>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcServer.#ctor(RabbitMQ.Client.MessagePatterns.Subscription)">
            <summary>Create, but do not start, an instance that will
            receive requests via the given Subscription.</summary>
            <remarks>
            <para>
             The instance is initially in non-transactional mode. See
             SetTransactional().
            </para>
            <para>
             Call MainLoop() to start the request-processing loop.
            </para>
            </remarks>
        </member>
        <member name="P:RabbitMQ.Client.MessagePatterns.SimpleRpcServer.Transactional">
            <summary>Returns true if we are in "transactional" mode, or
            false if we are not.</summary>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcServer.Close">
            <summary>Shut down the server, causing MainLoop() to return
            to its caller.</summary>
            <remarks>
             Acts by calling Close() on the server's Subscription object.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcServer.HandleCall(System.Boolean,RabbitMQ.Client.IBasicProperties,System.Byte[],RabbitMQ.Client.IBasicProperties@)">
            <summary>Called by ProcessRequest(), this is the most
            general method that handles RPC-style requests.</summary>
            <remarks>
            <para>
             This method should map requestProperties and body to
             replyProperties and the returned byte array.
            </para>
            <para>
             The default implementation checks
             requestProperties.ContentType, and if it is
             "jms/stream-message" (i.e. the current value of
             StreamMessageBuilder.MimeType), parses it using
             StreamMessageReader and delegates to
             HandleStreamMessageCall before encoding and returning the
             reply. If the ContentType is any other value, the request
             is passed to HandleSimpleCall instead.
            </para>
            <para>
             The isRedelivered flag is true when the server knows for
             sure that it has tried to send this request previously
             (although not necessarily to this application). It is not
             a reliable indicator of previous receipt, however - the
             only claim it makes is that a delivery attempt was made,
             not that the attempt succeeded. Be careful if you choose
             to use the isRedelivered flag.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcServer.HandleCast(System.Boolean,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>Called by ProcessRequest(), this is the most
            general method that handles asynchronous, one-way
            requests.</summary>
            <remarks>
            <para>
             The default implementation checks
             requestProperties.ContentType, and if it is
             "jms/stream-message" (i.e. the current value of
             StreamMessageBuilder.MimeType), parses it using
             StreamMessageReader and delegates to
             HandleStreamMessageCall, passing in null as the
             replyWriter parameter to indicate that no reply is desired
             or possible. If the ContentType is any other value, the
             request is passed to HandleSimpleCast instead.
            </para>
            <para>
             The isRedelivered flag is true when the server knows for
             sure that it has tried to send this request previously
             (although not necessarily to this application). It is not
             a reliable indicator of previous receipt, however - the
             only claim it makes is that a delivery attempt was made,
             not that the attempt succeeded. Be careful if you choose
             to use the isRedelivered flag.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcServer.HandleSimpleCall(System.Boolean,RabbitMQ.Client.IBasicProperties,System.Byte[],RabbitMQ.Client.IBasicProperties@)">
            <summary>Called by the default HandleCall() implementation
            as a fallback.</summary>
            <remarks>
             If the MIME ContentType of the request did not match any
             of the types specially recognised
             (e.g. "jms/stream-message"), this method is called instead
             with the raw bytes of the request. It should fill in
             replyProperties (or set it to null) and return a byte
             array to send back to the remote caller as a reply
             message.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcServer.HandleSimpleCast(System.Boolean,RabbitMQ.Client.IBasicProperties,System.Byte[])">
            <summary>Called by the default HandleCast() implementation
            as a fallback.</summary>
            <remarks>
             If the MIME ContentType of the request did not match any
             of the types specially recognised
             (e.g. "jms/stream-message"), this method is called instead
             with the raw bytes of the request.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcServer.HandleStreamMessageCall(RabbitMQ.Client.Content.IStreamMessageBuilder,System.Boolean,RabbitMQ.Client.IBasicProperties,System.Object[])">
            <summary>Called by HandleCall and HandleCast when a
            "jms/stream-message" request is received.</summary>
            <remarks>
            <para>
             The args array contains the values decoded by HandleCall
             or HandleCast.
            </para>
            <para>
             The replyWriter parameter will be null if we were called
             from HandleCast, in which case a reply is not expected or
             possible, or non-null if we were called from
             HandleCall. Use the methods of replyWriter in this case to
             assemble your reply, which will be sent back to the remote
             caller.
            </para>
            <para>
             This default implementation does nothing, which
             effectively sends back an empty reply to any and all
             remote callers.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcServer.MainLoop">
            <summary>Enters the main loop of the RPC service.</summary>
            <remarks>
            <para>
             Retrieves requests repeatedly from the service's
             subscription. Each request is passed to
             ProcessRequest. Once ProcessRequest returns, the request
             is acknowledged via Subscription.Ack(). If transactional
             mode is enabled, TxCommit is then called. Finally, the
             loop begins again.
            </para>
            <para>
             Runs until the subscription ends, which happens either as
             a result of disconnection, or of a call to Close().
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcServer.ProcessRequest(RabbitMQ.Client.Events.BasicDeliverEventArgs)">
            <summary>Process a single request received from our
            subscription.</summary>
            <remarks>
            <para>
             If the request's properties contain a non-null, non-empty
             CorrelationId string (see IBasicProperties), it is assumed
             to be a two-way call, requiring a response. The ReplyTo
             header property is used as the reply address (via
             PublicationAddress.Parse, unless that fails, in which case it
             is treated as a simple queue name), and the request is
             passed to HandleCall().
            </para>
            <para>
             If the CorrelationId is absent or empty, the request is
             treated as one-way asynchronous event, and is passed to
             HandleCast().
            </para>
            <para>
             Usually, overriding HandleCall(), HandleCast(), or one of
             their delegates is sufficient to implement a service, but
             in some cases overriding ProcessRequest() is
             required. Overriding ProcessRequest() gives the
             opportunity to implement schemes for detecting interaction
             patterns other than simple request/response or one-way
             communication.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcServer.SetTransactional">
            <summary>Enables transactional mode.</summary>
            <remarks>
            <para>
             Once enabled, transactional mode is not only enabled for
             all users of the underlying IModel instance, but cannot be
             disabled without shutting down the entire IModel (which
             involves shutting down all the services depending on it,
             and should not be undertaken lightly).
            </para>
            <para>
             This method calls IModel.TxSelect, every time it is
             called. (TxSelect is idempotent, so this is harmless.)
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.SimpleRpcServer.System#IDisposable#Dispose">
            <summary>Implement the IDisposable interface, permitting
            SimpleRpcServer instances to be used in using
            statements.</summary>
        </member>
        <member name="T:RabbitMQ.Client.MessagePatterns.Subscription">
            <summary>Manages a subscription to a queue.</summary>
            <remarks>
            <para>
             This convenience class abstracts away from much of the detail
             involved in receiving messages from a queue.
            </para>
            <para>
             Once created, the Subscription consumes from a queue (using a
             EventingBasicConsumer). Received deliveries can be retrieved
             by calling Next(), or by using the Subscription as an
             IEnumerator in, for example, a foreach loop.
            </para>
            <para>
             Note that if the "autoAck" option is enabled (which it is by
             default), then received deliveries are automatically acked
             within the server before they are even transmitted across the
             network to us. Calling Ack() on received events will always do
             the right thing: if "autoAck" is enabled, nothing is done on an
             Ack() call, and if "autoAck" is disabled, IModel.BasicAck() is
             called with the correct parameters.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.Subscription.#ctor(RabbitMQ.Client.IModel,System.String)">
            <summary>Creates a new Subscription in "autoAck" mode,
            consuming from a named queue.</summary>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.Subscription.#ctor(RabbitMQ.Client.IModel,System.String,System.Boolean)">
            <summary>Creates a new Subscription, with full control over
            both "autoAck" mode and the name of the queue.</summary>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.Subscription.#ctor(RabbitMQ.Client.IModel,System.String,System.Boolean,System.String)">
            <summary>Creates a new Subscription, with full control over
            both "autoAck" mode, the name of the queue, and the consumer tag.</summary>
        </member>
        <member name="P:RabbitMQ.Client.MessagePatterns.Subscription.Consumer">
            <summary>Retrieve the IBasicConsumer that is receiving the
            messages from the server for us. Normally, you will not
            need to access this property - use Next() and friends
            instead.</summary>
        </member>
        <member name="P:RabbitMQ.Client.MessagePatterns.Subscription.ConsumerTag">
            <summary>Retrieve the consumer-tag that this subscription
            is using. Will usually be a server-generated
            name.</summary>
        </member>
        <member name="P:RabbitMQ.Client.MessagePatterns.Subscription.LatestEvent">
            <summary>Returns the most recent value returned by Next(),
            or null when either no values have been retrieved yet, the
            end of the subscription has been reached, or the most
            recent value has already been Ack()ed. See also the
            documentation for Ack().</summary>
        </member>
        <member name="P:RabbitMQ.Client.MessagePatterns.Subscription.Model">
            <summary>Retrieve the IModel our subscription is carried by.</summary>
        </member>
        <member name="P:RabbitMQ.Client.MessagePatterns.Subscription.AutoAck">
            <summary>Returns true if we are in "autoAck" mode, where
            calls to Ack() will be no-ops, and where the server acks
            messages before they are delivered to us. Returns false if
            we are in a mode where calls to Ack() are required, and
            where such calls will actually send an acknowledgement
            message across the network to the server.</summary>
        </member>
        <member name="P:RabbitMQ.Client.MessagePatterns.Subscription.QueueName">
            <summary>Retrieve the queue name we have subscribed to.</summary>
        </member>
        <member name="P:RabbitMQ.Client.MessagePatterns.Subscription.System#Collections#IEnumerator#Current">
            <summary>Implementation of the IEnumerator interface, for
            permitting Subscription to be used in foreach
            loops.</summary>
            <remarks>
            <para>
             As per the IEnumerator interface definition, throws
             InvalidOperationException if LatestEvent is null.
            </para>
            <para>
             Does not acknowledge any deliveries at all. Ack() must be
             called explicitly on received deliveries.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.Subscription.Ack">
            <summary>If LatestEvent is non-null, passes it to
            Ack(BasicDeliverEventArgs). Causes LatestEvent to become
            null.</summary>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.Subscription.Ack(RabbitMQ.Client.Events.BasicDeliverEventArgs)">
            <summary>If we are not in "autoAck" mode, calls
            IModel.BasicAck with the delivery-tag from <paramref name="evt"/>;
            otherwise, sends nothing to the server. if <paramref name="evt"/> is the same as LatestEvent
            by pointer comparison, sets LatestEvent to null.
            </summary>
            <remarks>
            Passing an event that did not originate with this Subscription's
             channel, will lead to unpredictable behaviour
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.Subscription.Close">
            <summary>Closes this Subscription, cancelling the consumer
            record in the server.</summary>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.Subscription.Nack(System.Boolean)">
            <summary>If LatestEvent is non-null, passes it to
            Nack(BasicDeliverEventArgs, false, requeue). Causes LatestEvent to become
            null.</summary>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.Subscription.Nack(System.Boolean,System.Boolean)">
            <summary>If LatestEvent is non-null, passes it to
            Nack(BasicDeliverEventArgs, multiple, requeue). Causes LatestEvent to become
            null.</summary>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.Subscription.Nack(RabbitMQ.Client.Events.BasicDeliverEventArgs,System.Boolean,System.Boolean)">
            <summary>If we are not in "autoAck" mode, calls
            IModel.BasicNack with the delivery-tag from <paramref name="evt"/>;
            otherwise, sends nothing to the server. if <paramref name="evt"/> is the same as LatestEvent
            by pointer comparison, sets LatestEvent to null.
            </summary>
            <remarks>
            Passing an event that did not originate with this Subscription's
             channel, will lead to unpredictable behaviour
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.Subscription.Next">
            <summary>Retrieves the next incoming delivery in our
            subscription queue.</summary>
            <remarks>
            <para>
             Returns null when the end of the stream is reached and on
             every subsequent call. End-of-stream can arise through the
             action of the Subscription.Close() method, or through the
             closure of the IModel or its underlying IConnection.
            </para>
            <para>
             Updates LatestEvent to the value returned.
            </para>
            <para>
             Does not acknowledge any deliveries at all (but in "autoAck"
             mode, the server will have auto-acknowledged each event
             before it is even sent across the wire to us).
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.Subscription.Next(System.Int32,RabbitMQ.Client.Events.BasicDeliverEventArgs@)">
            <summary>Retrieves the next incoming delivery in our
            subscription queue, or times out after a specified number
            of milliseconds.</summary>
            <remarks>
            <para>
             Returns false only if the timeout expires before either a
             delivery appears or the end-of-stream is reached. If false
             is returned, the out parameter "result" is set to null,
             but LatestEvent is not updated.
            </para>
            <para>
             Returns true to indicate a delivery or the end-of-stream.
            </para>
            <para>
             If a delivery is already waiting in the queue, or one
             arrives before the timeout expires, it is removed from the
             queue and placed in the "result" out parameter. If the
             end-of-stream is detected before the timeout expires,
             "result" is set to null.
            </para>
            <para>
             Whenever this method returns true, it updates LatestEvent
             to the value placed in "result" before returning.
            </para>
            <para>
             End-of-stream can arise through the action of the
             Subscription.Close() method, or through the closure of the
             IModel or its underlying IConnection.
            </para>
            <para>
             This method does not acknowledge any deliveries at all
             (but in "autoAck" mode, the server will have
             auto-acknowledged each event before it is even sent across
             the wire to us).
            </para>
            <para>
             A timeout of -1 (i.e. System.Threading.Timeout.Infinite)
             will be interpreted as a command to wait for an
             indefinitely long period of time for an item or the end of
             the stream to become available. Usage of such a timeout is
             equivalent to calling Next() with no arguments (modulo
             predictable method signature differences).
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.Subscription.System#IDisposable#Dispose">
            <summary>Implementation of the IDisposable interface,
            permitting Subscription to be used in using
            statements. Simply calls Close().</summary>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.Subscription.System#Collections#IEnumerable#GetEnumerator">
            <summary>Implementation of the IEnumerable interface, for
            permitting Subscription to be used in foreach
            loops.</summary>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.Subscription.System#Collections#IEnumerator#MoveNext">
            <summary>Implementation of the IEnumerator interface, for
            permitting Subscription to be used in foreach
            loops.</summary>
            <remarks>
            <para>
             Does not acknowledge any deliveries at all. Ack() must be
             called explicitly on received deliveries.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Client.MessagePatterns.Subscription.System#Collections#IEnumerator#Reset">
            <summary>Dummy implementation of the IEnumerator interface,
            for permitting Subscription to be used in foreach loops;
            Reset()ting a Subscription doesn't make sense, so this
            method always throws InvalidOperationException.</summary>
        </member>
        <member name="T:RabbitMQ.Util.BlockingCell">
            <summary>A thread-safe single-assignment reference cell.</summary>
            <remarks>
            A fresh BlockingCell holds no value (is empty). Any thread
            reading the Value property when the cell is empty will block
            until a value is made available by some other thread. The Value
            property can only be set once - on the first call, the
            BlockingCell is considered full, and made immutable. Further
            attempts to set Value result in a thrown
            InvalidOperationException.
            </remarks>
        </member>
        <member name="P:RabbitMQ.Util.BlockingCell.Value">
            <summary>Retrieve the cell's value, blocking if none exists
            at present, or supply a value to an empty cell, thereby
            filling it.</summary>
             <exception cref="T:System.InvalidOperationException" />
        </member>
        <member name="M:RabbitMQ.Util.BlockingCell.validatedTimeout(System.Int32)">
            <summary>Return valid timeout value</summary>
            <remarks>If value of the parameter is less then zero, return 0
            to mean infinity</remarks>
        </member>
        <member name="M:RabbitMQ.Util.BlockingCell.GetValue(System.TimeSpan)">
            <summary>Retrieve the cell's value, waiting for the given
            timeout if no value is immediately available.</summary>
            <remarks>
            <para>
             If a value is present in the cell at the time the call is
             made, the call will return immediately. Otherwise, the
             calling thread blocks until either a value appears, or
             operation times out.
            </para>
            <para>
             If no value was available before the timeout, an exception
             is thrown.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Util.BlockingCell.GetValue(System.Int32)">
            <summary>Retrieve the cell's value, waiting for the given
            timeout if no value is immediately available.</summary>
            <remarks>
            <para>
             If a value is present in the cell at the time the call is
             made, the call will return immediately. Otherwise, the
             calling thread blocks until either a value appears, or
             operation times out.
            </para>
            <para>
             If no value was available before the timeout, an exception
             is thrown.
            </para>
            </remarks>
        </member>
        <member name="T:RabbitMQ.Util.DebugUtil">
            <summary>Miscellaneous debugging and development utilities.</summary>
            <remarks>
            Not part of the public API.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Util.DebugUtil.Dump(System.Byte[])">
            <summary>Print a hex dump of the supplied bytes to stdout.</summary>
        </member>
        <member name="M:RabbitMQ.Util.DebugUtil.Dump(System.Byte[],System.IO.TextWriter)">
            <summary>Print a hex dump of the supplied bytes to the supplied TextWriter.</summary>
        </member>
        <member name="M:RabbitMQ.Util.DebugUtil.DumpKeyValue(System.String,System.Object,System.IO.TextWriter,System.Int32)">
            <summary>Prints an indented key/value pair; used by DumpProperties()</summary>
            <remarks>Recurses into the value using DumpProperties().</remarks>
        </member>
        <member name="M:RabbitMQ.Util.DebugUtil.DumpProperties(System.Object,System.IO.TextWriter,System.Int32)">
            <summary>Dump properties of objects to the supplied writer.</summary>
        </member>
        <member name="T:RabbitMQ.Util.EitherAlternative">
            <summary>Used internally by class Either.</summary>
        </member>
        <member name="T:RabbitMQ.Util.Either">
            <summary>Models the disjoint union of two alternatives, a
            "left" alternative and a "right" alternative.</summary>
            <remarks>Borrowed from ML, Haskell etc.</remarks>
        </member>
        <member name="M:RabbitMQ.Util.Either.#ctor(RabbitMQ.Util.EitherAlternative,System.Object)">
            <summary>Private constructor. Use the static methods Left, Right instead.</summary>
        </member>
        <member name="P:RabbitMQ.Util.Either.Alternative">
            <summary>Retrieve the alternative represented by this instance.</summary>
        </member>
        <member name="P:RabbitMQ.Util.Either.Value">
            <summary>Retrieve the value carried by this instance.</summary>
        </member>
        <member name="M:RabbitMQ.Util.Either.Left(System.Object)">
            <summary>Constructs an Either instance representing a Left alternative.</summary>
        </member>
        <member name="M:RabbitMQ.Util.Either.Right(System.Object)">
            <summary>Constructs an Either instance representing a Right alternative.</summary>
        </member>
        <member name="T:RabbitMQ.Util.IntAllocator">
            A class for allocating integer IDs in a given range.
        </member>
        <member name="M:RabbitMQ.Util.IntAllocator.#ctor(System.Int32,System.Int32)">
            A class representing a list of inclusive intervals
            Creates an IntAllocator allocating integer IDs within the inclusive range [start, end]
        </member>
        <member name="M:RabbitMQ.Util.IntAllocator.Allocate">
            Allocate a fresh integer from the range, or return -1 if no more integers
            are available. This operation is guaranteed to run in O(1)
        </member>
        <member name="M:RabbitMQ.Util.IntAllocator.Free(System.Int32)">
             Make the provided integer available for allocation again. This operation
             runs in amortized O(sqrt(range size)) time: About every sqrt(range size)
             operations  will take O(range_size + number of intervals) to complete and
             the rest run in constant time.
            
             No error checking is performed, so if you double Free or Free an integer
             that was not originally Allocated the results are undefined. Sorry.
        </member>
        <member name="T:RabbitMQ.Util.NetworkBinaryReader">
            <summary>
            Subclass of BinaryReader that reads integers etc in correct network order.
            </summary>
            <remarks>
            <para>
            Kludge to compensate for .NET's broken little-endian-only BinaryReader.
            Relies on BinaryReader always being little-endian.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryReader.#ctor(System.IO.Stream)">
            <summary>
            Construct a NetworkBinaryReader over the given input stream.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryReader.#ctor(System.IO.Stream,System.Text.Encoding)">
            <summary>
            Construct a NetworkBinaryReader over the given input
            stream, reading strings using the given encoding.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryReader.TemporaryBinaryReader(System.Byte[])">
            <summary>Helper method for constructing a temporary
            BinaryReader over a byte[].</summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryReader.ReadDouble">
            <summary>
            Override BinaryReader's method for network-order.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryReader.ReadInt16">
            <summary>
            Override BinaryReader's method for network-order.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryReader.ReadInt32">
            <summary>
            Override BinaryReader's method for network-order.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryReader.ReadInt64">
            <summary>
            Override BinaryReader's method for network-order.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryReader.ReadSingle">
            <summary>
            Override BinaryReader's method for network-order.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryReader.ReadUInt16">
            <summary>
            Override BinaryReader's method for network-order.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryReader.ReadUInt32">
            <summary>
            Override BinaryReader's method for network-order.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryReader.ReadUInt64">
            <summary>
            Override BinaryReader's method for network-order.
            </summary>
        </member>
        <member name="T:RabbitMQ.Util.NetworkBinaryWriter">
             <summary>
             Subclass of BinaryWriter that writes integers etc in correct network order.
             </summary>
            
             <remarks>
             <p>
             Kludge to compensate for .NET's broken little-endian-only BinaryWriter.
             </p><p>
             See also NetworkBinaryReader.
             </p>
             </remarks>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryWriter.#ctor(System.IO.Stream)">
            <summary>
            Construct a NetworkBinaryWriter over the given input stream.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryWriter.#ctor(System.IO.Stream,System.Text.Encoding)">
            <summary>
            Construct a NetworkBinaryWriter over the given input
            stream, reading strings using the given encoding.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryWriter.TemporaryBinaryWriter(System.Int32)">
            <summary>Helper method for constructing a temporary
            BinaryWriter streaming into a fresh MemoryStream
            provisioned with the given initialSize.</summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryWriter.TemporaryContents(System.IO.BinaryWriter)">
            <summary>Helper method for extracting the byte[] contents
            of a BinaryWriter over a MemoryStream, such as constructed
            by TemporaryBinaryWriter.</summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryWriter.Write(System.Int16)">
            <summary>
            Override BinaryWriter's method for network-order.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryWriter.Write(System.UInt16)">
            <summary>
            Override BinaryWriter's method for network-order.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryWriter.Write(System.Int32)">
            <summary>
            Override BinaryWriter's method for network-order.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryWriter.Write(System.UInt32)">
            <summary>
            Override BinaryWriter's method for network-order.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryWriter.Write(System.Int64)">
            <summary>
            Override BinaryWriter's method for network-order.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryWriter.Write(System.UInt64)">
            <summary>
            Override BinaryWriter's method for network-order.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryWriter.Write(System.Single)">
            <summary>
            Override BinaryWriter's method for network-order.
            </summary>
        </member>
        <member name="M:RabbitMQ.Util.NetworkBinaryWriter.Write(System.Double)">
            <summary>
            Override BinaryWriter's method for network-order.
            </summary>
        </member>
        <member name="T:RabbitMQ.Util.SharedQueue">
            <summary>A thread-safe shared queue implementation.</summary>
        </member>
        <member name="T:RabbitMQ.Util.SharedQueue`1">
            <summary>A thread-safe shared queue implementation.</summary>
        </member>
        <member name="F:RabbitMQ.Util.SharedQueue`1.m_isOpen">
            <summary>Flag holding our current status.</summary>
        </member>
        <member name="F:RabbitMQ.Util.SharedQueue`1.m_queue">
            <summary>The shared queue.</summary>
            <remarks>
            Subclasses must ensure appropriate locking discipline when
            accessing this field. See the implementation of Enqueue,
            Dequeue.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Util.SharedQueue`1.Close">
            <summary>Close the queue. Causes all further Enqueue()
            operations to throw EndOfStreamException, and all pending
            or subsequent Dequeue() operations to throw an
            EndOfStreamException once the queue is empty.</summary>
        </member>
        <member name="M:RabbitMQ.Util.SharedQueue`1.Dequeue">
            <summary>Retrieve the first item from the queue, or block if none available</summary>
            <remarks>
            Callers of Dequeue() will block if no items are available
            until some other thread calls Enqueue() or the queue is
            closed. In the latter case this method will throw
            EndOfStreamException.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Util.SharedQueue`1.Dequeue(System.Int32,`0@)">
            <summary>Retrieve the first item from the queue, or return
            nothing if no items are available after the given
            timeout</summary>
            <remarks>
            <para>
             If one or more items are present on the queue at the time
             the call is made, the call will return
             immediately. Otherwise, the calling thread blocks until
             either an item appears on the queue, or
             millisecondsTimeout milliseconds have elapsed.
            </para>
            <para>
             Returns true in the case that an item was available before
             the timeout, in which case the out parameter "result" is
             set to the item itself.
            </para>
            <para>
             If no items were available before the timeout, returns
             false, and sets "result" to null.
            </para>
            <para>
             A timeout of -1 (i.e. System.Threading.Timeout.Infinite)
             will be interpreted as a command to wait for an
             indefinitely long period of time for an item to become
             available. Usage of such a timeout is equivalent to
             calling Dequeue() with no arguments. See also the MSDN
             documentation for
             System.Threading.Monitor.Wait(object,int).
            </para>
            <para>
             If no items are present and the queue is in a closed
             state, or if at any time while waiting the queue
             transitions to a closed state (by a call to Close()), this
             method will throw EndOfStreamException.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Util.SharedQueue`1.DequeueNoWait(`0)">
            <summary>Retrieve the first item from the queue, or return
            defaultValue immediately if no items are
            available</summary>
            <remarks>
            <para>
             If one or more objects are present in the queue at the
             time of the call, the first item is removed from the queue
             and returned. Otherwise, the defaultValue that was passed
             in is returned immediately. This defaultValue may be null,
             or in cases where null is part of the range of the queue,
             may be some other sentinel object. The difference between
             DequeueNoWait() and Dequeue() is that DequeueNoWait() will
             not block when no items are available in the queue,
             whereas Dequeue() will.
            </para>
            <para>
             If at the time of call the queue is empty and in a
             closed state (following a call to Close()), then this
             method will throw EndOfStreamException.
            </para>
            </remarks>
        </member>
        <member name="M:RabbitMQ.Util.SharedQueue`1.Enqueue(`0)">
            <summary>Place an item at the end of the queue.</summary>
            <remarks>
            If there is a thread waiting for an item to arrive, the
            waiting thread will be woken, and the newly Enqueued item
            will be passed to it. If the queue is closed on entry to
            this method, EndOfStreamException will be thrown.
            </remarks>
        </member>
        <member name="M:RabbitMQ.Util.SharedQueue`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>Implementation of the IEnumerable interface, for
            permitting SharedQueue to be used in foreach
            loops.</summary>
        </member>
        <member name="M:RabbitMQ.Util.SharedQueue`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
            <summary>Implementation of the IEnumerable interface, for
            permitting SharedQueue to be used in foreach
            loops.</summary>
        </member>
        <member name="M:RabbitMQ.Util.SharedQueue`1.EnsureIsOpen">
            <summary>Call only when the lock on m_queue is held.</summary>
             <exception cref="T:System.IO.EndOfStreamException" />
        </member>
        <member name="T:RabbitMQ.Util.SharedQueueEnumerator`1">
            <summary>Implementation of the IEnumerator interface, for
            permitting SharedQueue to be used in foreach loops.</summary>
        </member>
        <member name="M:RabbitMQ.Util.SharedQueueEnumerator`1.#ctor(RabbitMQ.Util.SharedQueue{`0})">
            <summary>Construct an enumerator for the given
            SharedQueue.</summary>
        </member>
        <member name="M:RabbitMQ.Util.SharedQueueEnumerator`1.System#Collections#IEnumerator#Reset">
            <summary>Reset()ting a SharedQueue doesn't make sense, so
            this method always throws
            InvalidOperationException.</summary>
        </member>
    </members>
</doc>
