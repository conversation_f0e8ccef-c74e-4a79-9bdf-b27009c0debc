﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using NM_Service.NMService;

namespace PlatCommonForm
{
    public partial class FrmBillitemQueryForRcpt : Form
    {
        public FrmBillitemQueryForRcpt()
        {
            InitializeComponent();
        }

        private void FrmBillitemQueryForRcpt_Load(object sender, EventArgs e)
        {
            p1.Dock = DockStyle.Fill;
            p2.Dock = DockStyle.Fill;
            p3.Dock = DockStyle.Fill;
        }



        #region SQL
        /// <summary>
        /// 
        /// </summary>
        /// <param name="rcptno"></param>
        /// <returns></returns>
        private DataTable GetInpBillDetailByRcptNo(string rcptno)
        {
            string sql = "SELECT I.ITEM_CLASS,I.ITEM_NAME,I.ITEM_SPEC,I.UNITS,I.AMOUNT,I.COSTS,I.CHARGES,I.BILLING_DATE_TIME,I.OPERATOR_NO,I.ORDERED_BY,I.PERFORMED_BY,I.PATIENT_ID,I.VISIT_ID,I.ITEM_CODE,I.ITEM_NO,I.ITEM_PRICE,I.DISCHARGE_TAKING_INDICATOR  ,I.RCPT_NO FROM INP_BILL_DETAIL I WHERE I.RCPT_NO='" + rcptno + "'";
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            return dt; 
        }
        #endregion
    }
}
