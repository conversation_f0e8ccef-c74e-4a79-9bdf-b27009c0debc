﻿namespace PlatCommonForm
{
    partial class frmSelectPatientId
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridPATIENT_ID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.INP_NO = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridNAME = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridSEX = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridIDENTITY = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl1
            // 
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(814, 177);
            this.gridControl1.TabIndex = 0;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridPATIENT_ID,
            this.INP_NO,
            this.gridNAME,
            this.gridSEX,
            this.gridIDENTITY});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.DoubleClick += new System.EventHandler(this.gridView1_DoubleClick);
            // 
            // gridPATIENT_ID
            // 
            this.gridPATIENT_ID.AppearanceHeader.Options.UseTextOptions = true;
            this.gridPATIENT_ID.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridPATIENT_ID.Caption = "ID号";
            this.gridPATIENT_ID.FieldName = "PATIENT_ID";
            this.gridPATIENT_ID.Name = "gridPATIENT_ID";
            this.gridPATIENT_ID.OptionsColumn.AllowEdit = false;
            this.gridPATIENT_ID.OptionsColumn.AllowFocus = false;
            this.gridPATIENT_ID.Visible = true;
            this.gridPATIENT_ID.VisibleIndex = 0;
            // 
            // INP_NO
            // 
            this.INP_NO.AppearanceHeader.Options.UseTextOptions = true;
            this.INP_NO.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.INP_NO.Caption = "住院号";
            this.INP_NO.FieldName = "INP_NO";
            this.INP_NO.Name = "INP_NO";
            this.INP_NO.OptionsColumn.AllowEdit = false;
            this.INP_NO.OptionsColumn.AllowFocus = false;
            this.INP_NO.Visible = true;
            this.INP_NO.VisibleIndex = 4;
            // 
            // gridNAME
            // 
            this.gridNAME.AppearanceHeader.Options.UseTextOptions = true;
            this.gridNAME.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridNAME.Caption = "姓名";
            this.gridNAME.FieldName = "NAME";
            this.gridNAME.Name = "gridNAME";
            this.gridNAME.OptionsColumn.AllowEdit = false;
            this.gridNAME.OptionsColumn.AllowFocus = false;
            this.gridNAME.Visible = true;
            this.gridNAME.VisibleIndex = 1;
            // 
            // gridSEX
            // 
            this.gridSEX.AppearanceHeader.Options.UseTextOptions = true;
            this.gridSEX.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridSEX.Caption = "性别";
            this.gridSEX.FieldName = "SEX";
            this.gridSEX.Name = "gridSEX";
            this.gridSEX.OptionsColumn.AllowEdit = false;
            this.gridSEX.OptionsColumn.AllowFocus = false;
            this.gridSEX.Visible = true;
            this.gridSEX.VisibleIndex = 2;
            // 
            // gridIDENTITY
            // 
            this.gridIDENTITY.AppearanceHeader.Options.UseTextOptions = true;
            this.gridIDENTITY.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridIDENTITY.Caption = "身份";
            this.gridIDENTITY.FieldName = "IDENTITY";
            this.gridIDENTITY.Name = "gridIDENTITY";
            this.gridIDENTITY.OptionsColumn.AllowEdit = false;
            this.gridIDENTITY.OptionsColumn.AllowFocus = false;
            this.gridIDENTITY.Visible = true;
            this.gridIDENTITY.VisibleIndex = 3;
            // 
            // frmSelectPatientId
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(814, 177);
            this.Controls.Add(this.gridControl1);
            this.Name = "frmSelectPatientId";
            this.Text = "选择ID号";
            this.Load += new System.EventHandler(this.frmSelectPatientId_Load);
            this.KeyDown += new System.Windows.Forms.KeyEventHandler(this.frmSelectPatientId_KeyDown);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridPATIENT_ID;
        private DevExpress.XtraGrid.Columns.GridColumn INP_NO;
        private DevExpress.XtraGrid.Columns.GridColumn gridNAME;
        private DevExpress.XtraGrid.Columns.GridColumn gridSEX;
        private DevExpress.XtraGrid.Columns.GridColumn gridIDENTITY;
    }
}