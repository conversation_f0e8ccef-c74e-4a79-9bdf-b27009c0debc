﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using System.ComponentModel;
namespace Model
{

    /// <summary>
    ///应用程序菜单字典
    /// </summary>

    [DataContract]
    public class SEC_MENUS_DICT : NotificationObject
    {

        /// <summary>
        /// 应用程序代码(applications.application)
        /// </summary>		
        private string _application_code;
        [DataMember]
        public string APPLICATION_CODE
        {
            get { return _application_code; }
            set
            {
                if (_application_code != value)
                {
                    _application_code = value;
                    this.RaisePropertyChanged("APPLICATION_CODE");
                }
            }
        }
        /// <summary>
        /// 菜单标识
        /// </summary>		
        private string _menu_name;
        [DataMember]
        public string MENU_NAME
        {
            get { return _menu_name; }
            set
            {
                if (_menu_name != value)
                {
                    _menu_name = value;
                    this.RaisePropertyChanged("MENU_NAME");
                }
            }
        }
        /// <summary>
        /// 菜单所属窗体类名(区分在哪个窗体上的菜单,要加上命名空间。)
        /// </summary>		
        private string _form_menu;
        [DataMember]
        public string FORM_MENU
        {
            get { return _form_menu; }
            set
            {
                if (_form_menu != value)
                {
                    _form_menu = value;
                    this.RaisePropertyChanged("FORM_MENU");
                }
            }
        }
        /// <summary>
        /// 菜单所属控件(如果是窗体上面的主菜单，该字段为空，如果是窗体上控件右手键菜单，该字段保存窗口上控件名称)
        /// </summary>		
        private string _form_control;
        [DataMember]
        public string FORM_CONTROL
        {
            get { return _form_control; }
            set
            {
                if (_form_control != value)
                {
                    _form_control = value;
                    this.RaisePropertyChanged("FORM_CONTROL");
                }
            }
        }
        /// <summary>
        /// 序号	
        /// </summary>		
        private decimal _serial_no;
        [DataMember]
        public decimal SERIAL_NO
        {
            get { return _serial_no; }
            set
            {
                if (_serial_no != value)
                {
                    _serial_no = value;
                    this.RaisePropertyChanged("SERIAL_NO");
                }
            }
        }
        /// <summary>
        /// 菜单中文描述
        /// </summary>		
        private string _menu_text;
        [DataMember]
        public string MENU_TEXT
        {
            get { return _menu_text; }
            set
            {
                if (_menu_text != value)
                {
                    _menu_text = value;
                    this.RaisePropertyChanged("MENU_TEXT");
                }
            }
        }
        /// <summary>
        /// 菜单提示信息
        /// </summary>		
        private string _tool_tips;
        [DataMember]
        public string TOOL_TIPS
        {
            get { return _tool_tips; }
            set
            {
                if (_tool_tips != value)
                {
                    _tool_tips = value;
                    this.RaisePropertyChanged("TOOL_TIPS");
                }
            }
        }
        /// <summary>
        /// 上级菜单标识
        /// </summary>		
        private string _supper_menu;
        [DataMember]
        public string SUPPER_MENU
        {
            get { return _supper_menu; }
            set
            {
                if (_supper_menu != value)
                {
                    _supper_menu = value;
                    this.RaisePropertyChanged("SUPPER_MENU");
                }
            }
        }
        /// <summary>
        /// 打开的窗体
        /// </summary>		
        private string _open_form;
        [DataMember]
        public string OPEN_FORM
        {
            get { return _open_form; }
            set
            {
                if (_open_form != value)
                {
                    _open_form = value;
                    this.RaisePropertyChanged("OPEN_FORM");
                }
            }
        }
        /// <summary>
        /// 打开窗体是否有参数
        /// </summary>		
        private decimal _open_param;
        [DataMember]
        public decimal OPEN_PARAM
        {
            get { return _open_param; }
            set
            {
                if (_open_param != value)
                {
                    _open_param = value;
                    this.RaisePropertyChanged("OPEN_PARAM");
                }
            }
        }
        /// <summary>
        /// 是否可见(1-可见，0-不可见)
        /// </summary>		
        private string _menu_visible;
        [DataMember]
        public string MENU_VISIBLE
        {
            get { return _menu_visible; }
            set
            {
                if (_menu_visible != value)
                {
                    _menu_visible = value;
                    this.RaisePropertyChanged("MENU_VISIBLE");
                }
            }
        }
        /// <summary>
        /// 图标风格(1-显示大图标，2-显示小图标)
        /// </summary>		
        private string _icon_style;
        [DataMember]
        public string ICON_STYLE
        {
            get { return _icon_style; }
            set
            {
                if (_icon_style != value)
                {
                    _icon_style = value;
                    this.RaisePropertyChanged("ICON_STYLE");
                }
            }
        }
        /// <summary>
        /// 大图标(保存本地图标文件的路径)
        /// </summary>		
        private string _large_icon;
        [DataMember]
        public string LARGE_ICON
        {
            get { return _large_icon; }
            set
            {
                if (_large_icon != value)
                {
                    _large_icon = value;
                    this.RaisePropertyChanged("LARGE_ICON");
                }
            }
        }
        /// <summary>
        /// 小图标(保存本地图标文件的路径)
        /// </summary>		
        private string _small_icon;
        [DataMember]
        public string SMALL_ICON
        {
            get { return _small_icon; }
            set
            {
                if (_small_icon != value)
                {
                    _small_icon = value;
                    this.RaisePropertyChanged("SMALL_ICON");
                }
            }
        }
        /// <summary>
        /// 菜单分组标识
        /// </summary>		
        private string _menu_group;
        [DataMember]
        public string MENU_GROUP
        {
            get { return _menu_group; }
            set
            {
                if (_menu_group != value)
                {
                    _menu_group = value;
                    this.RaisePropertyChanged("MENU_GROUP");
                }
            }
        }
        /// <summary>
        /// 备注
        /// </summary>		
        private string _menu_memos;
        [DataMember]
        public string MENU_MEMOS
        {
            get { return _menu_memos; }
            set
            {
                if (_menu_memos != value)
                {
                    _menu_memos = value;
                    this.RaisePropertyChanged("MENU_MEMOS");
                }
            }
        }
        /// <summary>
        /// 打开窗体所在文件
        /// </summary>		
        private string _open_file_name;
        [DataMember]
        public string OPEN_FILE_NAME
        {
            get { return _open_file_name; }
            set
            {
                if (_open_file_name != value)
                {
                    _open_file_name = value;
                    this.RaisePropertyChanged("OPEN_FILE_NAME");
                }
            }
        }

    }
}