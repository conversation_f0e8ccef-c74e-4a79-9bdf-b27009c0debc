﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


using System.Runtime.InteropServices;



namespace Utility
{
    public class FileSrv
    {
        #region 变量声明
        [DllImport("fsrv.dll")]
        public static extern int put_file(string host_addr, string local_file, string remote_file, int option);

        [DllImport("fsrv.dll")]
        public static extern int get_file(string host_addr, string remote_file, string local_file, int option);


        #endregion 



        #region 文件传输
        /// <summary>
        /// 放置文件
        /// </summary>
        public void PutFile()
        {
            //int a = put_file("*************", "C:\\mr2\\07\\00020081\\010004", "C:\\templet\\temp\\222", 1);
        }
        /// <summary>
        /// 提取文件
        /// </summary>
        public int GetFile(string ServerPath, string LocalPath)
        {
            return get_file(ConfigHelper.GetConfigString("fileSrvIP").ToString(), ConfigHelper.GetConfigString("filePathSrv").ToString() + "\\" + ServerPath + "\\" + LocalPath + ".emt", ConfigHelper.GetConfigString("filePathClient").ToString() + "\\" + LocalPath + ".emt", 1);
        }
        #endregion

    }
}
