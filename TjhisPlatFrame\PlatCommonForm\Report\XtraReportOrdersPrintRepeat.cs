﻿using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using System.Data;
using DevExpress.XtraPrinting;
using PlatCommon.SysBase;

namespace PlatCommonForm.Report
{
    public partial class XtraReportOrdersPrintRepeat : DevExpress.XtraReports.UI.XtraReport
    {
        int curpage;
        int irepeat;
        string head_print;
        String head_title;

        public XtraReportOrdersPrintRepeat()
        {
            InitializeComponent();
        }
        public XtraReportOrdersPrintRepeat(DataSet ds, DataSet pat, int temp_curpage, int repeat, string is_head_print)
        {
            curpage = temp_curpage;
            irepeat = repeat;
            head_print = is_head_print;
            InitializeComponent();
            SetPrintDate(ds, pat);            
        }

        public void SetPrintDate(DataSet ds, DataSet pat)
        {
            //add kangjia 20170413
            this.hospital_name.Text = PlatCommon.SysBase.SystemParm.HospitalID;
            //读取参数
            string canshu = PlatCommon.SysBase.SystemParm.GetParaValue("E_SIGNATURE", SystemParm.AppName, "*", "*", "0");
            if (PlatCommon.SysBase.SystemParm.HospitalID.Equals("桓仁满族自治县人民医院"))
            {
                //dhl桓仁，偶尔出现电子签名打的是机打字体，未测试出来先这么写死了
                canshu = "1";
            }
            if (canshu == "1")
            {
                string caused = PlatCommon.SysBase.SystemParm.GetParaValue("CA_USED", SystemParm.AppName, "*", "*", "0");
                //add kangjia 打印电子签名
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                if (PlatCommon.SysBase.SystemParm.HospitalID.Equals("桓仁满族自治县人民医院"))
                {
                    //dhl桓仁，偶尔出现电子签名打的是机打字体，未测试出来先这么写死了
                    caused = "1";
                }
                string sql = "";
                DataSet dsc = null;
                DataRow drc = null;
                this.xrPictureBox1.Visible = true;
                this.xrPictureBox2.Visible = true;
                this.xrPictureDoctor.Visible = true;
                this.xrPictureStopDoctor.Visible = true;
                System.Text.Encoding encoding = System.Text.Encoding.GetEncoding("GBK");
                for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
                {
                    string pic = ds.Tables[0].Rows[i]["NURSE1"].ToString();
                    if (!string.IsNullOrEmpty(pic))
                    {
                        if ("1".Equals(caused))//辽宁CA
                            sql = "select CA_IMG  AS NURSE1 from hisinterface.lnca_interface t where t.his_id = '" + pic + "'";
                        else
                            sql = "select BJCA_IMG AS NURSE1 from comm.bjca_interface WHERE HIS_ID='" + pic + "'";
                        dsc = spc.GetDataBySql(sql);
                        if (dsc.Tables[0].Rows.Count > 0)
                        {
                            drc = (DataRow)dsc.Tables[0].Rows[0];
                            byte[] byteImg = (byte[])drc["NURSE1"];
                            System.IO.MemoryStream ms = new System.IO.MemoryStream(byteImg);
                            System.Drawing.Image img = System.Drawing.Image.FromStream(ms);
                            ds.Tables[0].Rows[i]["NURSE2"] = img;
                        }
                        else
                        {
                            Bitmap b = new Bitmap(45, 30);
                            Font f = new Font("Arial", 9F);
                            Graphics g = Graphics.FromImage(b);
                            SolidBrush whiteBrush = new SolidBrush(Color.White);
                            SolidBrush BlackBrush = new SolidBrush(Color.Black);
                            RectangleF canvas = new RectangleF(0, 8, 45, 30);
                            g.FillRectangle(whiteBrush, new RectangleF(0, 0, 45, 30));
                            g.DrawString(ds.Tables[0].Rows[i]["NURSE"].ToString(), f, BlackBrush, canvas);
                            System.IO.MemoryStream ms = new System.IO.MemoryStream();
                            b.Save(ms, System.Drawing.Imaging.ImageFormat.Jpeg);
                            System.Drawing.Image img = System.Drawing.Image.FromStream(ms);
                            ds.Tables[0].Rows[i]["NURSE2"] = img;
                        }
                    }
                    pic = ds.Tables[0].Rows[i]["STOP_NURSE1"].ToString();
                    if (!string.IsNullOrEmpty(pic))
                    {
                        if ("1".Equals(caused))//辽宁CA
                            sql = "select CA_IMG  AS STOP_NURSE1 from hisinterface.lnca_interface t where t.his_id = '" + pic + "'";
                        else
                            sql = "select BJCA_IMG as STOP_NURSE1 from comm.bjca_interface WHERE HIS_ID='" + pic + "'";

                        dsc = spc.GetDataBySql(sql);
                        if (dsc.Tables[0].Rows.Count > 0)
                        {
                            drc = (DataRow)dsc.Tables[0].Rows[0];
                            byte[] byteImg = (byte[])drc["STOP_NURSE1"];
                            System.IO.MemoryStream ms = new System.IO.MemoryStream(byteImg);
                            System.Drawing.Image img = System.Drawing.Image.FromStream(ms);
                            ds.Tables[0].Rows[i]["STOP_NURSE2"] = img;
                        }
                        else
                        {
                            Bitmap b = new Bitmap(45, 30);
                            Font f = new Font("Arial", 9F);
                            Graphics g = Graphics.FromImage(b);
                            SolidBrush whiteBrush = new SolidBrush(Color.White);
                            SolidBrush BlackBrush = new SolidBrush(Color.Black);
                            RectangleF canvas = new RectangleF(0, 8, 45, 30);
                            g.FillRectangle(whiteBrush, new RectangleF(0, 0, 45, 30));
                            g.DrawString(ds.Tables[0].Rows[i]["STOP_NURSE"].ToString(), f, BlackBrush, canvas);
                            System.IO.MemoryStream ms = new System.IO.MemoryStream();
                            b.Save(ms, System.Drawing.Imaging.ImageFormat.Jpeg);
                            System.Drawing.Image img = System.Drawing.Image.FromStream(ms);
                            ds.Tables[0].Rows[i]["STOP_NURSE2"] = img;
                        }
                    }
                    //开单医生
                    pic = ds.Tables[0].Rows[i]["DOCTOR_CODE"].ToString();
                    if (!string.IsNullOrEmpty(pic))
                    {
                        //sql = "select BJCA_IMG AS NURSE1 from comm.bjca_interface WHERE HIS_ID='" + pic + "'";
                        if ("1".Equals(caused))//辽宁CA
                            sql = "select CA_IMG  AS NURSE1 from hisinterface.lnca_interface t where t.his_id = '" + pic + "'";
                        else
                            sql = "select BJCA_IMG AS NURSE1 from comm.bjca_interface WHERE HIS_ID='" + pic + "'";
                        dsc = spc.GetDataBySql(sql);
                        if (dsc.Tables[0].Rows.Count > 0)
                        {
                            drc = (DataRow)dsc.Tables[0].Rows[0];
                            byte[] byteImg = (byte[])drc["NURSE1"];
                            System.IO.MemoryStream ms = new System.IO.MemoryStream(byteImg);
                            System.Drawing.Image img = System.Drawing.Image.FromStream(ms);
                            ds.Tables[0].Rows[i]["doctor_pic"] = img;
                        }
                        else
                        {
                            Bitmap b = new Bitmap(45, 30);
                            Font f = new Font("Arial", 9F);
                            Graphics g = Graphics.FromImage(b);
                            SolidBrush whiteBrush = new SolidBrush(Color.White);
                            SolidBrush BlackBrush = new SolidBrush(Color.Black);
                            RectangleF canvas = new RectangleF(0, 8, 45, 30);
                            g.FillRectangle(whiteBrush, new RectangleF(0, 0, 45, 30));
                            g.DrawString(ds.Tables[0].Rows[i]["DOCTOR"].ToString(), f, BlackBrush, canvas);
                            System.IO.MemoryStream ms = new System.IO.MemoryStream();
                            b.Save(ms, System.Drawing.Imaging.ImageFormat.Jpeg);
                            System.Drawing.Image img = System.Drawing.Image.FromStream(ms);
                            ds.Tables[0].Rows[i]["doctor_pic"] = img;
                        }
                    }
                    //停止医生
                    pic = ds.Tables[0].Rows[i]["STOP_DOCTOR_CODE"].ToString();
                    if (!string.IsNullOrEmpty(pic))
                    {
                        if ("1".Equals(caused))//辽宁CA
                            sql = "select CA_IMG  AS NURSE1 from hisinterface.lnca_interface t where t.his_id = '" + pic + "'";
                        else
                            sql = "select BJCA_IMG AS NURSE1 from comm.bjca_interface WHERE HIS_ID='" + pic + "'";
                        dsc = spc.GetDataBySql(sql);
                        if (dsc.Tables[0].Rows.Count > 0)
                        {
                            drc = (DataRow)dsc.Tables[0].Rows[0];
                            byte[] byteImg = (byte[])drc["NURSE1"];
                            System.IO.MemoryStream ms = new System.IO.MemoryStream(byteImg);
                            System.Drawing.Image img = System.Drawing.Image.FromStream(ms);
                            ds.Tables[0].Rows[i]["nurse_pic"] = img;
                        }
                        else
                        {
                            Bitmap b = new Bitmap(45, 30);
                            Font f = new Font("Arial", 9F);
                            Graphics g = Graphics.FromImage(b);
                            SolidBrush whiteBrush = new SolidBrush(Color.White);
                            SolidBrush BlackBrush = new SolidBrush(Color.Black);
                            RectangleF canvas = new RectangleF(0, 8, 45, 30);
                            g.FillRectangle(whiteBrush, new RectangleF(0, 0, 45, 30));
                            g.DrawString(ds.Tables[0].Rows[i]["STOP_DOCTOR"].ToString(), f, BlackBrush, canvas);
                            System.IO.MemoryStream ms = new System.IO.MemoryStream();
                            b.Save(ms, System.Drawing.Imaging.ImageFormat.Jpeg);
                            System.Drawing.Image img = System.Drawing.Image.FromStream(ms);
                            ds.Tables[0].Rows[i]["nurse_pic"] = img;
                        }
                    }
                }
                this.xrPictureBox1.Sizing = ImageSizeMode.ZoomImage;//设置电子签名尺寸
                this.xrPictureBox2.Sizing = ImageSizeMode.ZoomImage;//设置电子签名尺寸
                this.xrPictureDoctor.Sizing = ImageSizeMode.ZoomImage;
                this.xrPictureStopDoctor.Sizing = ImageSizeMode.ZoomImage;
            }
            //add kangjia 20170413
            this.DataSource = ds;
            if (ds.Tables[0].Rows.Count > 0)
            {
                this.date.DataBindings.Add("Text", DataSource, "DATE");
                this.time.DataBindings.Add("Text", DataSource, "TIME");
                this.order_text.DataBindings.Add("Text", DataSource, "ORDER_TEXT");
                this.joint_line.DataBindings.Add("Text", DataSource, "JOINT_LINE");
                this.doctor.DataBindings.Add("Text", DataSource, "DOCTOR");
                this.nurse.DataBindings.Add("Text", DataSource, "NURSE");
                this.stop_date.DataBindings.Add("Text", DataSource, "STOP_DATE");
                this.stop_time.DataBindings.Add("Text", DataSource, "STOP_TIME");
                this.stop_doctor.DataBindings.Add("Text", DataSource, "STOP_DOCTOR");
                this.stop_nurse.DataBindings.Add("Text", DataSource, "STOP_NURSE");
                this.rowNum.DataBindings.Add("Text", DataSource, "ROWNUM");
                this.xrTableCell1.DataBindings.Add("Text", DataSource, "DOSAGE_DOSAGE_UNITS");
                this.xrTableCell2.DataBindings.Add("Text", DataSource, "FREQUENCY");
                this.xrTableCell3.DataBindings.Add("Text", DataSource, "administration");
                this.xrPictureBox1.DataBindings.Add("Image", DataSource, "NURSE2");
                this.xrPictureBox2.DataBindings.Add("Image", DataSource, "STOP_NURSE2");
                this.xrPictureDoctor.DataBindings.Add("Image", DataSource, "DOCTOR_PIC");
                this.xrPictureStopDoctor.DataBindings.Add("Image", DataSource, "NURSE_PIC");
            }
            if (pat.Tables[0].Rows.Count > 0)
            {
                this.name.Text = pat.Tables[0].Rows[0]["NAME"].ToString();
                this.dept.Text = pat.Tables[0].Rows[0]["DEPT_NAME"].ToString();
                this.bed_no.Text = pat.Tables[0].Rows[0]["BED_LABEL"].ToString();
                this.inp_no.Text = pat.Tables[0].Rows[0]["INP_NO"].ToString();
                this.id.Text = pat.Tables[0].Rows[0]["PATIENT_ID"].ToString();
                this.t_page.Text = curpage.ToString();
            }
            hospital_name.Text = PlatCommon.SysBase.SystemParm.HospitalID;

            if (head_print.Equals("0")) //不显示表格
            {
                this.t_page.Visible = false;
                this.name.Visible = false;
                this.t_name.Visible = false;
                this.dept.Visible = false;
                this.t_dept.Visible = false;
                this.bed_no.Visible = false;
                this.t_bed_no.Visible = false;
                this.inp_no.Visible = false;
                this.t_inp_no.Visible = false;
                this.id.Visible = false;
                this.t_id.Visible = false;

                hospital_name.Text = "";
                orders_type.Text = "";
                t_start.Visible = false;
                t_stop.Visible = false;
                t_date.Visible = false;
                t_time.Visible = false;
                t_doctor.Visible = false;
                t_nurse.Visible = false;
                t_order_text.Visible = false;
                t_stopdate.Visible = false;
                t_stoptime.Visible = false;
                t_stopdoctor.Visible = false;
                t_stopnurse.Visible = false;
                this.date.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.time.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.order_text.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.joint_line.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.xrTableCell1.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.xrTableCell2.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.xrTableCell3.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.doctor.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.nurse.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.stop_date.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.stop_time.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.stop_doctor.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.stop_nurse.Borders = DevExpress.XtraPrinting.BorderSide.None;

            }
            if (head_print.Equals("1")) //显示表格
            {
                this.t_page.Visible = true;
                this.name.Visible = true;
                this.t_name.Visible = true;
                this.dept.Visible = true;
                this.t_dept.Visible = true;
                this.bed_no.Visible = true;
                this.t_bed_no.Visible = true;
                this.inp_no.Visible = true;
                this.t_inp_no.Visible = true;
                this.id.Visible = true;
                this.t_id.Visible = true;

                hospital_name.Visible = true;
                //orders_type.Visible = true;
                t_start.Visible = true;
                t_stop.Visible = true;
                t_date.Visible = true;
                t_time.Visible = true;
                t_doctor.Visible = true;
                t_nurse.Visible = true;
                t_order_text.Visible = true;
                t_stopdate.Visible = true;
                t_stoptime.Visible = true;
                t_stopdoctor.Visible = true;
                t_stopnurse.Visible = true;
                this.xrTableCell1.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Bottom)));
                this.xrTableCell2.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Bottom)));
                this.xrTableCell3.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Bottom)));
                this.date.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
                this.time.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
                this.order_text.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
                this.joint_line.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Bottom)));
                this.doctor.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
                this.nurse.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
            | DevExpress.XtraPrinting.BorderSide.Bottom)));

                if (irepeat == 1)
                {
                    this.stop_date.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
                | DevExpress.XtraPrinting.BorderSide.Bottom)));
                    this.stop_time.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
                | DevExpress.XtraPrinting.BorderSide.Bottom)));
                    this.stop_doctor.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
                | DevExpress.XtraPrinting.BorderSide.Bottom)));
                    this.stop_nurse.Borders = DevExpress.XtraPrinting.BorderSide.All;
                }
            }
        }
    }
}
