﻿/*-----------------------------------------------------------------------
 * 类名称    ：frminputsetting
 * 类描述    ：输入法窗体,适用于gridview适用于某些条件下不能使用searchlookupedit的情况需传进来GS_INPUTSETING
 * 创建人    ： 杨红宇yhy
 * 创建时间  ：2016/7/1 13:23:46
 * 修改人    ：杨红宇yhy
 * 修改时间  ：2016/7/13 13:00:00
 * 修改备注  ：医生站医嘱也用到此窗体
 * 版本      ：1.3
 * ---------------------------------------------------------------------- */


using System;
using System.Data;
using System.Windows.Forms;
using PlatCommon.SysBase;
using NM_Service.NMService;
namespace PlatCommonForm
{
    public partial class frminputsetting : ParentForm
    {
        public frminputsetting()
        {
            InitializeComponent();
        }
        private string rowFilter = "";

        public string GS_INPUTSETING;//输入法
        public string GS_ITEM_CLASS;
        public string GS_ITEM_NAME;
        public string GS_ITEM_CODE;
        public string GS_ITEM_SPEC;
        public string GS_PRICE;
        public string GS_UNITS;
        public string GS_PERFORMED_BY;
        //public string GS_PERFORMED_BY_CODE;
        public string GS_AAA;
        public string GS_BBB;
        public string GS_DISP; //摆药药局 格式为'A','B'
        public string GS_QUANTITY;//库存数量
        public string GS_CHARGE_TYPE;
        public string GS_GJBM;//国家编码
        public string GS_GJMC;//国家名称
        public string GS_SJBM;//国家编码
        public string GS_SJMC;//国家名称
        DataTable dtPriceList;
        private void frminputsetting_Load(object sender, EventArgs e)
        {
            //MessageBox.Show(GS_ITEM_CLASS);
            if (!string.IsNullOrEmpty(GS_INPUTSETING))
            {
                this.Text = GS_INPUTSETING;
            }
            LoadPriceList();
            //gridControl2.Focus();

            //searchLookUpEdit1.ShowPopup();
        }
        public void LoadPriceList()
        {
            using (ServerPublicClient client = new ServerPublicClient())
            {
                //取医保接口名，同时使用此变量标记是否为医保
                string chargeType = string.IsNullOrEmpty(GS_CHARGE_TYPE) ? "" : GS_CHARGE_TYPE;
                string sqlstr = @" select a.interfacecode from insurance.tj_interface_vs_charge a where a.chargetype = '" + chargeType + "' ";
                DataTable dt = client.GetDataBySql(sqlstr).Tables[0];
                string interfaceCode = (dt != null && dt.Rows.Count > 0) ? dt.Rows[0]["interfacecode"].ToString() : "";

                switch (GS_INPUTSETING)
                {
                    case "其它价表":
                        if (!string.IsNullOrEmpty(interfaceCode))
                        {
                            sqlstr = @"
                            select A.ITEM_CLASS,
                                   A.ITEM_CODE,
                                   A.ITEM_NAME,
                                   A.ITEM_SPEC,
                                   A.UNITS,
                                   A.PRICE,
                                   A.PREFER_PRICE AAA,
                                   A.FOREIGNER_PRICE BBB,
                                   A.PERFORMED_BY,
                                   A.INPUT_CODE,
                                   A.INPUT_CODE_WB,
                                   '' BASIC_DRUG_FLAG,
                                   '' insur_level,
                                   0 as quantity,
                                   '' as xzsyfw, 
                                   A.GJBM,
                                   A.GJMC,
                                   A.GJBM SJBM,
                                   A.GJMC SJMC,
A.INSUR_LEVEL,
A.GJYBYPMLBZ
                              from input_nodrug_list a,insurance.tj_vs_price b
                             where A.HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' and B.INTERFACECODE='"+ interfaceCode + "' and  A.ITEM_CLASS=B.ITEM_CLASS AND A.ITEM_CODE=B.ITEM_CODE AND A.ITEM_SPEC=B.ITEM_SPEC ";
                        }
                        else
                        {
                            sqlstr = @"
                            select ITEM_CLASS,
                                   ITEM_CODE,
                                   ITEM_NAME,
                                   ITEM_SPEC,
                                   UNITS,
                                   PRICE,
                                   PREFER_PRICE AAA,
                                   FOREIGNER_PRICE BBB,
                                   PERFORMED_BY,
                                   INPUT_CODE,
                                   INPUT_CODE_WB,
                                   '' BASIC_DRUG_FLAG,
                                   0 as quantity,
                                   '' as xzsyfw, 
                                   GJBM,
                                   GJMC,
                                   GJBM SJBM,
                                   GJMC SJMC,
INSUR_LEVEL,
GJYBYPMLBZ
                              from input_nodrug_list
                             where HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' ";
                        }
                            
                        break;
                    case "全部价表":
                        if (!string.IsNullOrEmpty(interfaceCode))
                        {
                            sqlstr = @"
                           select A.ITEM_CLASS,
                                   A.ITEM_CODE,
                                   A.ITEM_NAME,
                                   A.ITEM_SPEC,
                                   A.UNITS,
                                   A.PRICE,
                                   A.PREFER_PRICE AAA,
                                   A.FOREIGNER_PRICE BBB,
                                   A.PERFORMED_BY,
                                   A.INPUT_CODE,
                                   A.INPUT_CODE_WB,
                                   '' BASIC_DRUG_FLAG,
                                   0 as quantity,
                                   '' as xzsyfw, 
                                   A.GJBM,
                                   A.GJMC,
                                   A.GJBM SJBM,
                                   A.GJMC SJMC,
"" INSUR_LEVEL,
"" GJYBYPMLBZ
                              from INPUT_ALLPRICE_LIST a,insurance.tj_vs_price b
                             where A.HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' and B.INTERFACECODE='" + interfaceCode + "' and A.ITEM_CLASS=B.ITEM_CLASS AND A.ITEM_CODE=B.ITEM_CODE AND A.ITEM_SPEC=B.ITEM_SPEC ";
                        }
                        else
                        {
                            sqlstr = @"
                            select ITEM_CLASS,
                                   ITEM_CODE,
                                   ITEM_NAME,
                                   ITEM_SPEC,
                                   UNITS,
                                   PRICE,
                                   PREFER_PRICE AAA,
                                   FOREIGNER_PRICE BBB,
                                   PERFORMED_BY,
                                   INPUT_CODE,
                                   INPUT_CODE_WB,
                                   '' BASIC_DRUG_FLAG,
                                   0 as quantity,
                                   '' as xzsyfw, 
                                   GJBM,
                                   GJMC,
                                   GJBM SJBM,
                                   GJMC SJMC,
"" INSUR_LEVEL,
"" GJYBYPMLBZ
                              from INPUT_ALLPRICE_LIST
                             where HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' ";
                        }
                        break;
                    case "诊疗项目":
                        if (!string.IsNullOrEmpty(interfaceCode))
                        {
                            sqlstr = @"
                            select ITEM_CLASS,
                                   ITEM_CODE,
                                   ITEM_NAME,
                                   ITEM_CLASS ITEM_SPEC,
                                   '' UNITS,
                                   '' PRICE,
                                   '' AAA,
                                   '' BBB,
                                   '' PERFORMED_BY,
                                   INPUT_CODE,
                                   INPUT_CODE_WB,
                                   '' BASIC_DRUG_FLAG,
                                   0 as quantity,
                                   '' as xzsyfw, 
                                   GJBM,
                                   GJMC,
                                   GJBM SJBM,
                                   GJMC SJMC,
"" INSUR_LEVEL,
"" GJYBYPMLBZ
                              from V_CLINIC_NAME_DICT
                             where HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + @"' and (ITEM_CLASS,ITEM_CODE) in (select a.clinic_item_class,a.clinic_item_code
  from clinic_vs_charge a, tj_vs_price t
 where t.interfacecode = '" + interfaceCode + @"'
   and t.item_class = a.charge_item_class
   and t.item_code = a.charge_item_code
   and t.item_spec = a.charge_item_spec)";
                        }
                        else
                        {
                            sqlstr = @"
                            select ITEM_CLASS,
                                   ITEM_CODE,
                                   ITEM_NAME,
                                   ITEM_CLASS ITEM_SPEC,
                                   '' UNITS,
                                   '' PRICE,
                                   '' AAA,
                                   '' BBB,
                                   '' PERFORMED_BY,
                                   INPUT_CODE,
                                   INPUT_CODE_WB,
                                   '' BASIC_DRUG_FLAG,
                                   0 as quantity,
                                   '' as xzsyfw, 
                                   GJBM,
                                   GJMC,
                                   GJBM SJBM,
                                   GJMC SJMC,
"" INSUR_LEVEL,
"" GJYBYPMLBZ
                              from V_CLINIC_NAME_DICT
                             where HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' ";
                        }
                          
                        break;
                    case "检验项目":
                        if (!string.IsNullOrEmpty(interfaceCode))
                        {
                            sqlstr = @"
                            select ITEM_CLASS,
                                   ITEM_CODE,
                                   ITEM_NAME,
                                   ITEM_CLASS ITEM_SPEC,
                                   '' UNITS,
                                   '' PRICE,
                                   EXPAND1 AAA,
                                   EXPAND2 BBB,
                                   EXPAND3 PERFORMED_BY,
                                   INPUT_CODE,
                                   INPUT_CODE_WB,
                                   '' BASIC_DRUG_FLAG,
                                   0 as quantity,
                                   '' as xzsyfw, 
                                   '' GJBM,
                                   '' GJMC,
                                   '' SJBM,
                                   '' SJMC,
"" INSUR_LEVEL,
"" GJYBYPMLBZ 
                              from v_clab_name_dict
                             where HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + @"' and (ITEM_CLASS,ITEM_CODE) in (select a.clinic_item_class,a.clinic_item_code
  from clinic_vs_charge a, tj_vs_price t
 where t.interfacecode = '" + interfaceCode + @"'
   and t.item_class = a.charge_item_class
   and t.item_code = a.charge_item_code
   and t.item_spec = a.charge_item_spec) ";
                        }
                        else
                        {
                            sqlstr = @"
                            select ITEM_CLASS,
                                   ITEM_CODE,
                                   ITEM_NAME,
                                   ITEM_CLASS ITEM_SPEC,
                                   '' UNITS,
                                   '' PRICE,
                                   EXPAND1 AAA,
                                   EXPAND2 BBB,
                                   EXPAND3 PERFORMED_BY,
                                   INPUT_CODE,
                                   INPUT_CODE_WB,
                                   '' BASIC_DRUG_FLAG,
                                   0 as quantity,
                                   '' as xzsyfw, 
                                   '' GJBM,
                                   '' GJMC,
                                   '' SJBM,
                                   '' SJMC,
"" INSUR_LEVEL,
"" GJYBYPMLBZ
                              from v_clab_name_dict
                             where HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' ";
                        }
                            
                        break;
                    case "套餐检验项目":
                        if (!string.IsNullOrEmpty(interfaceCode))
                        {
                            sqlstr = @"
                            select ITEM_CLASS,
                                   ITEM_CODE,
                                   ITEM_NAME,
                                   ITEM_CLASS ITEM_SPEC,
                                   '' UNITS,
                                   '' PRICE,
                                   EXPAND1 AAA,
                                   EXPAND2 BBB,
                                   EXPAND3 PERFORMED_BY,
                                   INPUT_CODE,
                                   INPUT_CODE_WB,
                                   '' BASIC_DRUG_FLAG,
                                   0 as quantity,
                                   '' as xzsyfw , 
                                   '' GJBM,
                                   '' GJMC,
                                   '' SJBM,
                                   '' SJMC,
"" INSUR_LEVEL,
"" GJYBYPMLBZ
                              from clinic_item_dict
                             where HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + @"'
                               and(CLINIC_ITEM_DICT.ITEM_CLASS = 'C')
                               and(CLINIC_ITEM_DICT.ITEM_STATUS is null OR CLINIC_ITEM_DICT.ITEM_STATUS = '0')
                               and CLINIC_ITEM_DICT.EXPAND1 is not null
                               and CLINIC_ITEM_DICT.EXPAND2 is not null
                               and CLINIC_ITEM_DICT.EXPAND3 is not null  and (CLINIC_ITEM_DICT.ITEM_CLASS,CLINIC_ITEM_DICT.ITEM_CODE) in (select a.clinic_item_class,a.clinic_item_code
  from clinic_vs_charge a, tj_vs_price t
 where t.interfacecode = '" + interfaceCode + @"'
   and t.item_class = a.charge_item_class
   and t.item_code = a.charge_item_code
   and t.item_spec = a.charge_item_spec) ";
                        }
                        else
                        {
                            sqlstr = @"
                            select ITEM_CLASS,
                                   ITEM_CODE,
                                   ITEM_NAME,
                                   ITEM_CLASS ITEM_SPEC,
                                   '' UNITS,
                                   '' PRICE,
                                   EXPAND1 AAA,
                                   EXPAND2 BBB,
                                   EXPAND3 PERFORMED_BY,
                                   INPUT_CODE,
                                   INPUT_CODE_WB,
                                   '' BASIC_DRUG_FLAG,
                                   0 as quantity,
                                   '' as xzsyfw , 
                                   '' GJBM,
                                   '' GJMC,
                                   '' SJBM,
                                   '' SJMC,
"" INSUR_LEVEL,
"" GJYBYPMLBZ
                              from clinic_item_dict
                             where HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + @"'
                               and(CLINIC_ITEM_DICT.ITEM_CLASS = 'C')
                               and(CLINIC_ITEM_DICT.ITEM_STATUS is null OR CLINIC_ITEM_DICT.ITEM_STATUS = '0')
                               and CLINIC_ITEM_DICT.EXPAND1 is not null
                               and CLINIC_ITEM_DICT.EXPAND2 is not null
                               and CLINIC_ITEM_DICT.EXPAND3 is not null ";
                        }
                          
                        break;
                    case "诊疗药品":
                        if (!string.IsNullOrEmpty(interfaceCode))
                        {
                            sqlstr = @"
                                select aa.drug_indicator ITEM_CLASS,
                                       aa.drug_code ITEM_CODE,
                                       aa.drug_name ITEM_NAME,
                                       aa.drug_spec ITEM_SPEC,
                                       aa.package_units UNITS,
                                       aa.retail_price PRICE,
                                       aa.dose_per_unit AAA,
                                       aa.dose_units BBB,
                                       storage as PERFORMED_BY,
                                       storage_name AS PERFORMED_NAME,
                                       storage,
                                       aa.INPUT_CODE,
                                       aa.INPUT_CODE_WB,
                                       decode(BASIC_DRUG_FLAG, 1, '基本药物', 2, '非基本药物', BASIC_DRUG_FLAG) BASIC_DRUG_FLAG,
                                       aa.quantity,
                                       decode(a.insur_level, '1', '甲', '2', '乙', '3', '丙', insur_level) insur_level,
                                       '' as xzsyfw, 
                                   aa.GJBM,
                                   aa.GJMC,
                                   aa.GJBM SJBM,
                                   aa.GJMC SJMC,
"" GJYBYPMLBZ
                                  from INPUT_DRUG_LISTS aa
                                  , insurance.tj_vs_price a
                                 where aa.quantity > 0
                                   and aa.HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' and ( a.interfacecode = '" + interfaceCode + @"' and aa.drug_code = a.ITEM_CODE and aa.drug_spec = a.item_spec and aa.package_units = a.units) ";
                        }
                        else
                        {
                            sqlstr = @"
                                select drug_indicator ITEM_CLASS,
                                       drug_code ITEM_CODE,
                                       drug_name ITEM_NAME,
                                       drug_spec ITEM_SPEC,
                                       package_units UNITS,
                                       retail_price PRICE,
                                       dose_per_unit AAA,
                                       dose_units BBB,
                                       storage as PERFORMED_BY,
                                       storage_name AS PERFORMED_NAME,
                                       storage,
                                       INPUT_CODE,
                                       INPUT_CODE_WB,
                                       decode(BASIC_DRUG_FLAG, 1, '基本药物', 2, '非基本药物', BASIC_DRUG_FLAG) BASIC_DRUG_FLAG,
                                       quantity,
                                       '' as xzsyfw, 
                                   GJBM,
                                   GJMC,
                                   GJBM SJBM,
                                   GJMC SJMC,
"" INSUR_LEVEL,
"" GJYBYPMLBZ
                                  from INPUT_DRUG_LISTS aa
                                 where quantity > 0
                                   and HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' ";
                        }
                        break;
                    case "诊疗药品_合并":
                        if (!string.IsNullOrEmpty(interfaceCode))
                        {
                            sqlstr = @"
                                select aa.drug_indicator ITEM_CLASS,
                                       aa.drug_code ITEM_CODE,
                                       aa.drug_name ITEM_NAME,
                                       aa.drug_spec ITEM_SPEC,
                                       aa.package_units UNITS,
                                       aa.retail_price PRICE,
                                       aa.dose_per_unit AAA,
                                       aa.dose_units BBB,
                                       storage as PERFORMED_BY,
                                       storage_name AS PERFORMED_NAME,
                                       storage,
                                       aa.INPUT_CODE,
                                       aa.INPUT_CODE_WB,
                                       decode(BASIC_DRUG_FLAG, 1, '基本药物', 2, '非基本药物', BASIC_DRUG_FLAG) BASIC_DRUG_FLAG,
                                       aa.quantity,
                                       decode(a.insur_level, '1', '甲', '2', '乙', '3', '丙', insur_level) insur_level,
                                       (select m.out_bz from insurance.drugs_dict_bxyb m where m.out_ypbm = a.insur_code and m.out_xzsyfw is not null and rownum = 1) as xzsyfw,
                                   aa.GJBM,
                                   aa.GJMC,
                                   aa.GJBM SJBM,
                                   aa.GJMC SJMC,
"" GJYBYPMLBZ
                                  from INPUT_DRUG_LISTS_SUM aa
                                  , insurance.tj_vs_price a
                                    
                                 where aa.quantity > 0
                                   and aa.HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' and  (a.interfacecode = '" + interfaceCode + @"' and aa.drug_code = a.ITEM_CODE and aa.drug_spec = a.item_spec and aa.package_units = a.units)";
                        }
                        else
                        {
                            sqlstr = @"
                                select drug_indicator ITEM_CLASS,
                                       drug_code ITEM_CODE,
                                       drug_name ITEM_NAME,
                                       drug_spec ITEM_SPEC,
                                       package_units UNITS,
                                       retail_price PRICE,
                                       dose_per_unit AAA,
                                       dose_units BBB,
                                       storage as PERFORMED_BY,
                                       storage_name AS PERFORMED_NAME,
                                       storage,
                                       INPUT_CODE,
                                       INPUT_CODE_WB,
                                       decode(BASIC_DRUG_FLAG, 1, '基本药物', 2, '非基本药物', BASIC_DRUG_FLAG) BASIC_DRUG_FLAG,
                                       quantity,
                                       '' as xzsyfw,
                                   GJBM,
                                   GJMC,
                                   GJBM SJBM,
                                   GJMC SJMC,
"" INSUR_LEVEL,
"" GJYBYPMLBZ
                                  from INPUT_DRUG_LISTS_SUM aa
                                 where quantity > 0
                                   and HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' ";
                        }
                        break;
                    // 其它输入法也继续字段别名保持一致就可以
                    default:
                        sqlstr = @"
                            select ITEM_CLASS,
                                   ITEM_CODE,
                                   ITEM_NAME,
                                   ITEM_SPEC,
                                   UNITS,
                                   PRICE,
                                   PREFER_PRICE AAA,
                                   FOREIGNER_PRICE BBB,
                                   PERFORMED_BY,
                                   INPUT_CODE,
                                   INPUT_CODE_WB,
                                   '' BASIC_DRUG_FLAG,
                                   0 as quantity,
                                   '' as xzsyfw,
                                   GJBM,
                                   GJMC,
                                   GJBM SJBM,
                                   GJMC SJMC,
INSUR_LEVEL,
GJYBYPMLBZ  
                              from input_nodrug_list
                             where HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' ";
                        break;
                }

                DataSet ds = client.GetDataBySql(sqlstr);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    dtPriceList = ds.Tables[0];
                    if (string.IsNullOrEmpty(GS_INPUTSETING) || GS_INPUTSETING.Equals("其它价表"))
                    {
                        if (GS_INPUTSETING.Equals("其它价表"))
                        {
                            this.gridView3.Columns["INSUR_LEVEL"].Visible = true;
                            this.gridView3.Columns["GJYBYPMLBZ"].Visible = true;
                        }
                        
                        if (string.IsNullOrEmpty(GS_ITEM_CLASS) || GS_ITEM_CLASS.Length == 0)
                        {
                            rowFilter = "ITEM_CLASS NOT IN ('A','B')";
                            dtPriceList.DefaultView.RowFilter = "ITEM_CLASS NOT IN ('A','B')";
                        }
                        else if (GS_ITEM_CLASS.Equals("INPBILL"))
                        {
                            //住院计价单
                            rowFilter = "ITEM_CLASS NOT IN ('A','B')";
                            dtPriceList.DefaultView.RowFilter = "ITEM_CLASS NOT IN ('A','B')";
                        }
                        else if (GS_ITEM_CLASS.Equals("EXAMAT"))
                        {
                            //检查确认
                            rowFilter = "ITEM_CLASS NOT IN ('A','B','C')";
                            dtPriceList.DefaultView.RowFilter = "ITEM_CLASS NOT IN ('A','B')";
                        }
                        else
                        {
                            rowFilter = "ITEM_CLASS  IN ('" + GS_ITEM_CLASS + "')";
                            dtPriceList.DefaultView.RowFilter = "ITEM_CLASS  IN ('" + GS_ITEM_CLASS + "')";
                        }
                    }
                    dtPriceList.DefaultView.Sort = "INPUT_CODE";
                }
                ds.Dispose();

                gridControl2.DataSource = dtPriceList;

                if (GS_INPUTSETING.Equals("诊疗项目") || GS_INPUTSETING.Equals("检验项目"))
                {
                    this.gridView3.Columns["ITEM_SPEC"].Caption = "类别";
                    this.gridView3.Columns["UNITS"].Visible = false;
                    this.gridView3.Columns["PRICE"].Visible = false;
                    //this.gridView3.Columns["PERFORMED_BY"].Visible = false;
                }
                if (GS_INPUTSETING.Equals("诊疗药品") || GS_INPUTSETING.Equals("诊疗药品_合并"))
                {
                    this.gridView3.Columns["PERFORMED_NAME"].Caption = "库房";
                    this.gridView3.Columns["PERFORMED_NAME"].Visible = true;
                    this.gridView3.Columns["BASIC_DRUG_FLAG"].Visible = true;
                    this.gridView3.Columns["QUANTITY"].Visible = true;
                    this.gridView3.Columns["BASIC_DRUG_FLAG"].VisibleIndex = 6;
                    this.gridView3.Columns["QUANTITY"].VisibleIndex = 7;
                    if (!string.IsNullOrEmpty(interfaceCode))
                    {
                        this.gridView3.Columns["INSUR_LEVEL"].Visible = true;
                        this.gridView3.Columns["XZSYFW"].Visible = true;
                    }
                    
                    if (!string.IsNullOrEmpty(GS_DISP))
                    {
                        rowFilter = "STORAGE  IN (" + GS_DISP + ")";
                        dtPriceList.DefaultView.RowFilter = "STORAGE  IN (" + GS_DISP + ")";
                    }
                }
                else
                {
                    //this.gridView3.Columns["PERFORMED_BY"].Visible = false;
                }

            }
        }

        private void gridView3_MouseDown(object sender, MouseEventArgs e)
        {

        }

        private void gridView3_DoubleClick(object sender, EventArgs e)
        {
            DataRow row = this.gridView3.GetDataRow(this.gridView3.FocusedRowHandle);
            if (row != null)
            {
                GS_ITEM_NAME = row["ITEM_NAME"].ToString();
                GS_ITEM_CODE = row["ITEM_CODE"].ToString();
                GS_ITEM_SPEC = row["ITEM_SPEC"].ToString();
                GS_ITEM_CLASS = row["ITEM_CLASS"].ToString();
                GS_PRICE = row["PRICE"].ToString();
                GS_UNITS = row["UNITS"].ToString();
                GS_PERFORMED_BY = row["PERFORMED_BY"].ToString();
                GS_AAA = row["AAA"].ToString();
                GS_BBB = row["BBB"].ToString();

                GS_GJBM = row["GJBM"].ToString();
                GS_GJMC = row["GJMC"].ToString();
                GS_SJBM = row["SJBM"].ToString();
                GS_SJMC = row["SJMC"].ToString();
            }
            this.Close();
        }
        //20160802 康佳 添加
        private void textEdit1_EditValueChanged(object sender, EventArgs e)
        {
            this.gridView3.FindFilterText = this.textEdit1.Text.Trim();
        }

        private void frminputsetting_Shown(object sender, EventArgs e)
        {
            this.textEdit1.Focus();
        }

        private void frminputsetting_KeyDown(object sender, KeyEventArgs e)
        {

        }

        private void gridView3_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                gridView3_DoubleClick(sender, e);
            }
            if (e.KeyCode == Keys.Escape)
            {
                Close();
            }
        }

        private void textEdit1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                gridControl2.Focus();
                gridView3.Focus();
            }
        }
        //20160802 康佳 添加

    }
}
