﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace Tjhis.Controls.StatisticsControls
{
    public partial class ucDeptInfo : RadiusUserControl
    {
        public DeptInfoData deptInfoData { get; set; }
        public ucDeptInfo()
        {
            InitializeComponent();
        }
        protected override void SetContentSize()
        {
            this.mainPanel.Width = this.Width;
            this.mainPanel.Height = this.Height - TopBannerHeight;
        }
        

        public override void InitData()
        {
            if(null != deptInfoData)
            {
                this.LPatSum.Text = GetLabelHtmlText(deptInfoData.Pat.Sum, PatUnit);
                this.LPatCliniced.Text = GetLabelHtmlText(deptInfoData.Pat.Cliniced, PatUnit);
                this.LPatWait.Text = GetLabelHtmlText(deptInfoData.Pat.Wait, PatUnit);
                SetucCompareValue(this.ucComparePatSum, deptInfoData.Pat.CompareSum);
                SetucCompareValue(this.ucComparePatCliniced,deptInfoData.Pat.CompareCliniced);
                SetucCompareValue(this.ucComparePatWait, deptInfoData.Pat.CompareWait);

                this.LIncomeSum.Text = GetLabelHtmlText(deptInfoData.Income.Sum, CNYUnit);
                this.LAvgIncome.Text = GetLabelHtmlText(deptInfoData.Income.Avg, CNYUnit);
                this.LAvgIncomePrescSum.Text = GetLabelHtmlText(deptInfoData.Income.AvgPresc, CNYUnit);
                this.LAvgIncomeExamAndLab.Text = GetLabelHtmlText(deptInfoData.Income.AvgExamAndLab, CNYUnit);
                this.LAvgIncomeTreat.Text = GetLabelHtmlText(deptInfoData.Income.AvgTreat, CNYUnit);
                this.LAvgIncomeOther.Text = GetLabelHtmlText(deptInfoData.Income.AvgOther, CNYUnit);

                this.ucCompareIncomeSum.CompareValue = deptInfoData.Income.CompareSum;
                this.ucCompareAvgIncome.CompareValue = deptInfoData.Income.CompareAvg;
                this.ucCompareAvgIncomePresc.CompareValue = deptInfoData.Income.CompareAvgPresc;
                this.ucCompareAvgIncomeExamAndLab.CompareValue = deptInfoData.Income.CompareAvgExamAndLab;
                this.ucCompareAvgIncomeTreat.CompareValue = deptInfoData.Income.CompareAvgTreat;
                this.ucCompareAvgIncomeOther.CompareValue = deptInfoData.Income.CompareAvgOther;

            }
        }

        

        public class DeptInfoData
        {
            public static DeptInfoData CreateTestData()
            {
                DeptInfoData data = new DeptInfoData();
                data.Pat.Sum = 1000;
                data.Pat.Wait = 200;
                data.Pat.Cliniced = 800;
                data.Pat.CompareCliniced = 12.2m;
                data.Pat.CompareSum = 0.9m;
                data.Pat.CompareWait = 2.1m;

                data.Income.Sum = 100000;
                data.Income.Avg = 1000;
                data.Income.AvgPresc = 300;
                data.Income.AvgTreat = 200;
                data.Income.AvgExamAndLab = 300;
                data.Income.AvgOther = 100;
                data.Income.CompareAvg = 0.2m;
                data.Income.CompareAvgExamAndLab = 0.3m;
                data.Income.CompareAvgOther = 0.5m;
                data.Income.CompareAvgPresc = 0.1m;
                data.Income.CompareAvgTreat = 0.2m;

                return data;

            }
            public DeptInfoData()
            {
                Pat = new Pat();
                Income = new Income();
            }
            /// <summary>
            /// 人数
            /// </summary>
            public Pat Pat { get; set; }
            /// <summary>
            /// 费用收入
            /// </summary>
            public Income Income { get; set; }
        }
        public class Pat
        {
            /// <summary>
            /// 门诊总人数
            /// </summary>
            public int Sum { get; set; }
            /// <summary>
            /// 门诊总人数同比
            /// </summary>
            public decimal CompareSum { get; set; }
            /// <summary>
            /// 未接诊人数
            /// </summary>
            public int Wait { get => wait; set => wait = value; }
            /// <summary>
            /// 已接诊人数
            /// </summary>
            public int Cliniced { get => cliniced; set => cliniced = value; }
            /// <summary>
            /// 已接诊人数同比
            /// </summary>
            public decimal CompareCliniced { get => compareCliniced; set => compareCliniced = value; }
            /// <summary>
            /// 未接诊人数同比
            /// </summary>
            public decimal CompareWait { get => compareWait; set => compareWait = value; }

            private decimal compareWait;
            private decimal compareCliniced;
            private int cliniced;
            private int wait;
        }
        public class Income
        {
            public decimal Sum { get; set; }
            public decimal CompareSum { get; set; }
            public decimal Avg { get; set; }
            public decimal CompareAvg { get; set; }
            public decimal AvgPresc { get; set; }
            public decimal CompareAvgPresc { get; set; }
            public decimal AvgExamAndLab { get; set; }
            public decimal CompareAvgExamAndLab { get; set; }
            public decimal AvgTreat { get; set; }
            public decimal CompareAvgTreat { get; set; }
            public decimal AvgOther { get; set; }
            public decimal CompareAvgOther { get; set; }


        }
    }
}
