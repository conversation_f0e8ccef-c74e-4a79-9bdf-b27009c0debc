﻿using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using System.Data;

namespace PlatCommonForm
{
    public partial class prepayReport : DevExpress.XtraReports.UI.XtraReport
    {
        public prepayReport()
        {
            InitializeComponent();

        }
        public prepayReport(DataSet dtrcpt)
        {
            InitializeComponent();
            SetPrint(dtrcpt);
        }
        public void SetPrint(DataSet dtrcpt)
       {
           this.hospital_name.Text = "";
           this.transact_date.Text = "";
           this.rcpt_no.Text = "";
           this.patient_id.Text = "";
           this.dept_admission_to.Text = "";
           this.name.Text = "";
           this.pay_way.Text = "";
           this.inp_no.Text = "";
           this.upper_money.Text = "";
           this.amount.Text = "";
           this.operator_no.Text = "";
           if (dtrcpt.Tables[0].Rows.Count > 0)
           {
                string sqla = "select EHEALTH_CODE from pat_master_index where patient_id = '"+ dtrcpt.Tables[0].Rows[0]["patient_id"].ToString() + "'";
                DataSet ds_info = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sqla);
                xrBarCode_EHEALTH_CODE.Text = ds_info.Tables[0].Rows[0]["EHEALTH_CODE"].ToString();
                this.hospital_name.Text = dtrcpt.Tables[0].Rows[0]["hospitalname"].ToString();
               this.transact_date.Text = dtrcpt.Tables[0].Rows[0]["transact_date"].ToString();
               this.rcpt_no.Text = dtrcpt.Tables[0].Rows[0]["rcpt_no"].ToString();
               this.patient_id.Text = dtrcpt.Tables[0].Rows[0]["patient_id"].ToString();
               this.dept_admission_to.Text = dtrcpt.Tables[0].Rows[0]["deptname"].ToString();
               this.name.Text = dtrcpt.Tables[0].Rows[0]["name"].ToString();
               this.pay_way.Text = dtrcpt.Tables[0].Rows[0]["pay_way"].ToString();
               this.inp_no.Text = dtrcpt.Tables[0].Rows[0]["inp_no"].ToString();
               this.amount.Text = dtrcpt.Tables[0].Rows[0]["amount"].ToString();
               string gt_charges = dtrcpt.Tables[0].Rows[0]["amount"].ToString();
               if (string.IsNullOrEmpty(gt_charges))
               { gt_charges = "0"; }
               decimal gs_charges = Math.Round(Convert.ToDecimal(gt_charges), 4);
               if (gs_charges >= 0)
               {
                   this.upper_money.Text = CmycurD(gs_charges);
        
                }
               else
               {
                   this.upper_money.Text = "负" + CmycurD(Convert.ToDecimal(Math.Abs(gs_charges).ToString()));
               }
               this.operator_no.Text = dtrcpt.Tables[0].Rows[0]["operator_no"].ToString();
           }


       }

        /// <summary>
        /// 大小写转换
        /// </summary>    
        private string DaXie(string money)
        {
            //将小写金额转换成大写金额
            double MyNumber = Convert.ToDouble(money);
            String[] MyScale = { "分", "角", "元", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿", "拾", "佰", "仟", "兆", "拾", "佰", "仟" };
            String[] MyBase = { "零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖" };
            String
             M = "";
            bool
             isPoint = false;
            if (money.IndexOf(".") != -1)
            {
                money =
                 money.Remove(money.IndexOf("."), 1);
                isPoint = true;
            }
            for (int
             i =
             money.Length;
             i > 0;
             i--)
            {
                int MyData = Convert.ToInt16(money[money.Length -
                 i].ToString());
                M += MyBase[MyData];
                if (isPoint == true)
                {
                    M += MyScale[i - 1];
                }
                else
                {
                    M += MyScale[i + 1];
                }
            }
            return
             M;
        }
        /// <summary> 
        /// 转换人民币大小金额 
        /// </summary> 
        /// <param name="num">金额</param> 
        /// <returns>返回大写形式</returns> 
        public static string CmycurD(decimal num)
        {
            string str1 = "零壹贰叁肆伍陆柒捌玖";            //0-9所对应的汉字 
            string str2 = "万仟佰拾亿仟佰拾万仟佰拾元角分"; //数字位所对应的汉字 
            string str3 = "";    //从原num值中取出的值 
            string str4 = "";    //数字的字符串形式 
            string str5 = "";  //人民币大写金额形式 
            int i;    //循环变量 
            int j;    //num的值乘以100的字符串长度 
            string ch1 = "";    //数字的汉语读法 
            string ch2 = "";    //数字位的汉字读法 
            int nzero = 0;  //用来计算连续的零值是几个 
            int temp;            //从原num值中取出的值 

            num = Math.Round(Math.Abs(num), 2);    //将num取绝对值并四舍五入取2位小数 
            str4 = ((long)(num * 100)).ToString();        //将num乘100并转换成字符串形式 
            j = str4.Length;      //找出最高位 
            if (j > 15) { return "溢出"; }
            str2 = str2.Substring(15 - j);   //取出对应位数的str2的值。如：200.55,j为5所以str2=佰拾元角分 

            //循环取出每一位需要转换的值 
            for (i = 0; i < j; i++)
            {
                str3 = str4.Substring(i, 1);          //取出需转换的某一位的值 
                temp = Convert.ToInt32(str3);      //转换为数字 
                if (i != (j - 3) && i != (j - 7) && i != (j - 11) && i != (j - 15))
                {
                    //当所取位数不为元、万、亿、万亿上的数字时 
                    if (str3 == "0")
                    {
                        ch1 = "";
                        ch2 = "";
                        nzero = nzero + 1;
                    }
                    else
                    {
                        if (str3 != "0" && nzero != 0)
                        {
                            ch1 = "零" + str1.Substring(temp * 1, 1);
                            ch2 = str2.Substring(i, 1);
                            nzero = 0;
                        }
                        else
                        {
                            ch1 = str1.Substring(temp * 1, 1);
                            ch2 = str2.Substring(i, 1);
                            nzero = 0;
                        }
                    }
                }
                else
                {
                    //该位是万亿，亿，万，元位等关键位 
                    if (str3 != "0" && nzero != 0)
                    {
                        ch1 = "零" + str1.Substring(temp * 1, 1);
                        ch2 = str2.Substring(i, 1);
                        nzero = 0;
                    }
                    else
                    {
                        if (str3 != "0" && nzero == 0)
                        {
                            ch1 = str1.Substring(temp * 1, 1);
                            ch2 = str2.Substring(i, 1);
                            nzero = 0;
                        }
                        else
                        {
                            if (str3 == "0" && nzero >= 3)
                            {
                                ch1 = "";
                                ch2 = "";
                                nzero = nzero + 1;
                            }
                            else
                            {
                                if (j >= 11)
                                {
                                    ch1 = "";
                                    nzero = nzero + 1;
                                }
                                else
                                {
                                    ch1 = "";
                                    ch2 = str2.Substring(i, 1);
                                    nzero = nzero + 1;
                                }
                            }
                        }
                    }
                }
                if (i == (j - 11) || i == (j - 3))
                {
                    //如果该位是亿位或元位，则必须写上 
                    ch2 = str2.Substring(i, 1);
                }
                str5 = str5 + ch1 + ch2;

                if (i == j - 1 && str3 == "0")
                {
                    //最后一位（分）为0时，加上“整” 
                    str5 = str5 + '整';
                }
            }
            if (num == 0)
            {
                str5 = "零元整";
            }
            return str5;
        }

    }
}
