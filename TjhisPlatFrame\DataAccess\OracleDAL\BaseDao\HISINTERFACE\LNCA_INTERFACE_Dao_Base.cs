﻿/*-----------------------------------------------------------------------
 * 类名称    ：LNCA_INTERFACE_Dao_Base
 * 类描述    ：
 * 创建人    ：梁吉lions
 * 创建时间  ：2017/5/6 13:16:22
 * 修改人    ：
 * 修改时间  ：
 * 修改备注  ：
 * 版本      ：
 * ----------------------------------------------------------------------
 */
using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using Utility;
using Utility.OracleODP;
using Oracle.ManagedDataAccess.Client;

namespace OracleDAL
{

    /// <summary>
    /// 辽宁CA接口 数据库操作类
    /// </summary>

    public class LNCA_INTERFACE_Dao_Base
    {
        #region   Method
        public bool Exists(string LNCA_ID, OracleBaseClass db)
        {
            #region  init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from hisinterface.LNCA_INTERFACE");
            strSql.Append(" where ");
            strSql.Append(" LNCA_ID = :LNCA_ID ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":LNCA_ID", OracleDbType.Varchar2, 100);
            p.Value = LNCA_ID;
            parameters.Add(p);

            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    int cmdresult;
                    cmdresult = int.Parse(ds.Tables[0].Rows[0][0].ToString());
                    if (cmdresult <= 0)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                    return false;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.LNCA_INTERFACE model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into hisinterface.LNCA_INTERFACE(");
            strSql.Append("LNCA_ID,LNCA_INFO,HIS_ID,CA_IMG,CA_SN,CA_IMG_STR");
            strSql.Append(") values (");
            strSql.Append(":LNCA_ID,:LNCA_INFO,:HIS_ID,:CA_IMG,:CA_SN,:CA_IMG_STR");
            strSql.Append(") ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":LNCA_ID", OracleDbType.Varchar2, 100);
            p.Value = model.LNCA_ID;
            parameters.Add(p);

            p = new OracleParameter(":LNCA_INFO", OracleDbType.Clob, 4000);
            p.Value = model.LNCA_INFO;
            parameters.Add(p);

            p = new OracleParameter(":HIS_ID", OracleDbType.Varchar2, 20);
            p.Value = model.HIS_ID;
            parameters.Add(p);

            p = new OracleParameter(":CA_IMG", OracleDbType.Blob);
            p.Value = model.CA_IMG;
            parameters.Add(p);

            p = new OracleParameter(":CA_SN", OracleDbType.Varchar2, 100);
            p.Value = model.CA_SN;
            parameters.Add(p);

            p = new OracleParameter(":CA_IMG_STR", OracleDbType.Clob, 4000);
            p.Value = model.CA_IMG_STR;
            parameters.Add(p);
            #endregion
            try
            {

                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.LNCA_INTERFACE model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update hisinterface.LNCA_INTERFACE set ");

            strSql.Append(" LNCA_ID = :LNCA_ID , ");
            strSql.Append(" LNCA_INFO = :LNCA_INFO , ");
            strSql.Append(" HIS_ID = :HIS_ID , ");
            strSql.Append(" CA_IMG = :CA_IMG , ");
            strSql.Append(" CA_SN = :CA_SN , ");
            strSql.Append(" CA_IMG_STR = :CA_IMG_STR  ");
            strSql.Append(" where LNCA_ID=:LNCA_ID  ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":LNCA_ID", OracleDbType.Varchar2, 100);
            p.Value = model.LNCA_ID;
            parameters.Add(p);

            p = new OracleParameter(":LNCA_INFO", OracleDbType.Clob, 4000);
            p.Value = model.LNCA_INFO;
            parameters.Add(p);

            p = new OracleParameter(":HIS_ID", OracleDbType.Varchar2, 20);
            p.Value = model.HIS_ID;
            parameters.Add(p);

            p = new OracleParameter(":CA_IMG", OracleDbType.Blob);
            p.Value = model.CA_IMG;
            parameters.Add(p);

            p = new OracleParameter(":CA_SN", OracleDbType.Varchar2, 100);
            p.Value = model.CA_SN;
            parameters.Add(p);

            p = new OracleParameter(":CA_IMG_STR", OracleDbType.Clob, 4000);
            p.Value = model.CA_IMG_STR;
            parameters.Add(p);
            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool UpdateByHisID(Model.LNCA_INTERFACE model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update hisinterface.LNCA_INTERFACE set ");

            strSql.Append(" LNCA_ID = :LNCA_ID , ");
            strSql.Append(" LNCA_INFO = :LNCA_INFO , ");
            strSql.Append(" HIS_ID = :HIS_ID , ");
            strSql.Append(" CA_IMG = :CA_IMG , ");
            strSql.Append(" CA_SN = :CA_SN , ");
            strSql.Append(" CA_IMG_STR = :CA_IMG_STR  ");
            strSql.Append(" where HIS_ID=:HIS_ID  ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":LNCA_ID", OracleDbType.Varchar2, 100);
            p.Value = model.LNCA_ID;
            parameters.Add(p);

            p = new OracleParameter(":LNCA_INFO", OracleDbType.Clob, 4000);
            p.Value = model.LNCA_INFO;
            parameters.Add(p);

            p = new OracleParameter(":HIS_ID", OracleDbType.Varchar2, 20);
            p.Value = model.HIS_ID;
            parameters.Add(p);

            p = new OracleParameter(":CA_IMG", OracleDbType.Blob);
            p.Value = model.CA_IMG;
            parameters.Add(p);

            p = new OracleParameter(":CA_SN", OracleDbType.Varchar2, 100);
            p.Value = model.CA_SN;
            parameters.Add(p);

            p = new OracleParameter(":CA_IMG_STR", OracleDbType.Clob, 4000);
            p.Value = model.CA_IMG_STR;
            parameters.Add(p);
            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string LNCA_ID, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from hisinterface.LNCA_INTERFACE ");
            strSql.Append(" where LNCA_ID=:LNCA_ID ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":LNCA_ID", OracleDbType.Varchar2, 100);
            p.Value = LNCA_ID;
            parameters.Add(p);

            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }



        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.LNCA_INTERFACE GetModel(string LNCA_ID, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select LNCA_ID, LNCA_INFO, HIS_ID, CA_IMG, CA_SN, CA_IMG_STR  ");
            strSql.Append("  from hisinterface.LNCA_INTERFACE ");
            strSql.Append(" where LNCA_ID=:LNCA_ID ");
            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":LNCA_ID", OracleDbType.Varchar2, 100);
            p.Value = LNCA_ID;
            parameters.Add(p);
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;
                    Model.LNCA_INTERFACE model = new Model.LNCA_INTERFACE();

                    if (cmdresult > 0)
                    {
                        model = CopyToModel(ds.Tables[0].Rows[0]);
                        return model;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM hisinterface.LNCA_INTERFACE ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得几行数据
        /// </summary>
        public DataSet GetList(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            #region 初始化参数
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM hisinterface.LNCA_INTERFACE T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }
            if (strWhere.Trim() != "")
            {
                strSql.Append("  and " + strWhere);
            }
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.LNCA_INTERFACE> GetObservableCollection(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM hisinterface.LNCA_INTERFACE ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.LNCA_INTERFACE> list = new System.Collections.ObjectModel.ObservableCollection<Model.LNCA_INTERFACE>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.LNCA_INTERFACE model = new Model.LNCA_INTERFACE();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表 
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.LNCA_INTERFACE> GetObservableCollection(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM LNCA_INTERFACE T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }

            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.LNCA_INTERFACE> list = new System.Collections.ObjectModel.ObservableCollection<Model.LNCA_INTERFACE>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.LNCA_INTERFACE model = new Model.LNCA_INTERFACE();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion   Method
        #region
        /// <summary>
        /// 
        /// </summary>
        protected Model.LNCA_INTERFACE CopyToModel(DataRow dRow)
        {
            Model.LNCA_INTERFACE model1 = new Model.LNCA_INTERFACE();

            if (dRow["LNCA_ID"] != null && dRow["LNCA_ID"].ToString() != "")
            {
                model1.LNCA_ID = dRow["LNCA_ID"].ToString();
            }

            if (dRow["HIS_ID"] != null && dRow["HIS_ID"].ToString() != "")
            {
                model1.HIS_ID = dRow["HIS_ID"].ToString();
            }

            if (dRow["CA_IMG"] != null && dRow["CA_IMG"].ToString() != "")
            {
                model1.CA_IMG = (byte[])dRow["CA_IMG"];
            }

            if (dRow["CA_SN"] != null && dRow["CA_SN"].ToString() != "")
            {
                model1.CA_SN = dRow["CA_SN"].ToString();
            }

            return model1;
        }
        #endregion

    }
}

