﻿using DevExpress.XtraEditors;
using PlatCommon.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace TJHisPlat
{
    /// <summary>
    /// 医生坐诊列表选择
    /// </summary>
    public partial class FrmSelectOutpDoctorS : PlatCommon.SysBase.ParentForm
    {
        public DataTable gs_dtOutpDs; //诊室列表
        public string Doctor_QueueName; //选择的诊室
        public FrmSelectOutpDoctorS()
        {
            InitializeComponent();
          
        }
        //初始化
        public void Init()
        {
            gridControl2.DataSource = gs_dtOutpDs;
        }

        /// <summary>
        /// [确定]按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void simpleButton1_Click(object sender, EventArgs e)
        {
            if (gridView2.RowCount > 0)
            {
                DataRow DRow = this.gridView2.GetDataRow(this.gridView2.FocusedRowHandle);
                Doctor_QueueName = DRow["QUEUE_NAME"].ToString();
            }
            this.Close();
            
        }
        /// <summary>
        /// 双击列表
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gridControl2_DoubleClick(object sender, EventArgs e)
        {
            if (gridView2.RowCount > 0)
            {
                DataRowView drv = gridView2.GetFocusedRow() as DataRowView;
                if (drv == null)
                {
                    XtraMessageBox.Show("请选择护工作组！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                if (string.IsNullOrEmpty(drv.Row["CLINIC_DEPT"].ToString()))
                {
                    XtraMessageBox.Show("请选择工作组！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                Doctor_QueueName = drv.Row["QUEUE_NAME"].ToString();
            }
            this.Close();
        }

        private void FrmSelectOutpDoctorS_Load(object sender, EventArgs e)
        {
            Init();
        }
    }
}
