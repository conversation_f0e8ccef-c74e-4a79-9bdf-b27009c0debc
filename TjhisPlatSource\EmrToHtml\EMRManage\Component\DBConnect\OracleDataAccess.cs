﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Data.OracleClient;
using System.Configuration;
using System.Collections;
using System.Data.Common;

namespace Utility
{
    class OracleDataAccess : IDataAccess
    {
        private string connectionString = ConfigurationManager.ConnectionStrings["connectionString"].ConnectionString;
        public OracleDataAccess(bool isEntcrypt)
        {
            if (isEntcrypt)
            {
                connectionString = EnDecrypt.TJDecrypt(connectionString);
            }
        }

        #region IDataAccess 成员

        public System.Data.DataTable GetDataTable(string sql)
        {
            DataTable table = new DataTable();
            using (OracleConnection oraConn = new OracleConnection(connectionString))
            {
                OracleDataAdapter adapter = new OracleDataAdapter(sql, oraConn);
                adapter.Fill(table);
            }
            return table;
        }

        public System.Data.DataTable GetDataTable(System.Data.Common.DbCommand dbCommand)
        {
            DataTable table = new DataTable();
            using (OracleConnection oraConn = new OracleConnection(connectionString))
            {
                OracleCommand command = dbCommand as OracleCommand;
                if (command != null)
                {
                    command.Connection = oraConn;
                    OracleDataAdapter adapter = new OracleDataAdapter(command);
                    adapter.Fill(table);
                }
            }
            return table;
        }

        public bool ExecuteNonQuery(string sql)
        {
            int effectRows = 0;
            string exceptionMsg = "";
            using (OracleConnection oraConn = new OracleConnection(connectionString))
            {
                try
                {
                    oraConn.Open();
                    OracleCommand command = new OracleCommand(sql, oraConn);
                    effectRows = command.ExecuteNonQuery();
                }
                catch (OracleException ex)
                {
                    exceptionMsg = ex.Message;
                }
                if (oraConn.State != ConnectionState.Closed)
                    oraConn.Close();
            }
            if (exceptionMsg != "")
                throw new Exception(exceptionMsg + "\r\n错误源语句：" + sql + "\r\n");
            return effectRows > 0;
        }

        public bool ExecuteNonQuery(System.Data.Common.DbCommand dbCommand)
        {
            int effectRows = 0;
            using (OracleConnection oraConn = new OracleConnection(connectionString))
            {
                OracleCommand command = dbCommand as OracleCommand;
                if (command != null)
                {
                    command.Connection = oraConn;
                    effectRows = command.ExecuteNonQuery();
                }
            }
            return effectRows > 0;
        }

        public object ExecuteScalar(string sql)
        {
            object result;
            using (OracleConnection oraConn = new OracleConnection(connectionString))
            {
                OracleCommand command = new OracleCommand(sql, oraConn);
                result = command.ExecuteScalar();
            }
            return result;
        }

        public object ExecuteScalar(System.Data.Common.DbCommand dbCommand)
        {
            object result=null;
            using (OracleConnection oraConn = new OracleConnection(connectionString))
            {
                OracleCommand command =dbCommand as OracleCommand;
                if (command != null)
                {
                    command.Connection = oraConn;
                    result = command.ExecuteScalar();
                }
            }
            return result;
        }

        public bool ExecuteTransaction(List<string> sql)
        {
            bool result = false;
            using (OracleConnection oraConn = new OracleConnection(connectionString))
            {
                using (OracleCommand command = new OracleCommand())
                {
                    OracleTransaction oraTrans = oraConn.BeginTransaction();
                    command.Connection = oraConn;
                    command.CommandType = CommandType.Text;
                    try
                    {
                        for (int i = 0; i < sql.Count; i++)
                        {
                            command.CommandText = sql[i];
                            command.ExecuteNonQuery();
                        }
                        oraTrans.Commit();
                        result = true;
                    }
                    catch (Exception)
                    {
                        oraTrans.Rollback();
                        result = false;
                    }
                    finally
                    {
                        oraTrans.Dispose();
                    }
                }
            }
            return result;
        }

        public bool ExecuteTransaction(List<System.Data.Common.DbCommand> dbCommands)
        {
            bool result = false;
            using (OracleConnection oraConn = new OracleConnection(connectionString))
            {
                OracleTransaction oraTrans = oraConn.BeginTransaction();
                try
                {
                    for (int i = 0; i < dbCommands.Count; i++)
                    {
                        OracleCommand command = dbCommands[i] as OracleCommand;
                        if (command == null)
                            throw new Exception();
                        command.Connection = oraConn;
                        command.ExecuteNonQuery();
                    }
                    oraTrans.Commit();
                    result = true;
                }
                catch (Exception)
                {
                    oraTrans.Rollback();
                    result = false;
                }
                finally
                {
                    oraTrans.Dispose();
                }
            }
            return result;
        }

        public bool ExecuteTransaction(ArrayList sqlCommands,CustomCommandType type)
        {
            if (type == CustomCommandType.Text)
            {
                List<string> sqlList = new List<string>();
                for (int i = 0; i < sqlCommands.Count; i++)
                {
                    sqlList.Add(sqlCommands[i].ToString());
                }
                return ExecuteTransaction(sqlList);
            }
            else if (type == CustomCommandType.DBCommand)
            {
                List<System.Data.Common.DbCommand> cmdList = new List<System.Data.Common.DbCommand>();
                for (int i = 0; i < sqlCommands.Count; i++)
                {
                    cmdList.Add(sqlCommands[i] as System.Data.Common.DbCommand);
                }
                return ExecuteTransaction(cmdList);
            }
            return false;
        }

        public System.Data.Common.DbDataReader GetDataReader(string sql)
        {
            OracleDataReader reader = null;
            using (OracleConnection oraConn = new OracleConnection(connectionString))
            {
                OracleCommand command = new OracleCommand(sql, oraConn);
                reader = command.ExecuteReader();
            }
            return reader;
        }

        public System.Data.Common.DbDataReader GetDataReader(System.Data.Common.DbCommand dbCommand)
        {
            OracleDataReader reader = null;
            using (OracleConnection oraConn = new OracleConnection(connectionString))
            {
                OracleCommand command = dbCommand as OracleCommand;
                if (command != null)
                {
                    command.Connection = oraConn;
                    reader = command.ExecuteReader();
                }
            }
            return reader;
        }

        public System.Data.Common.DbConnection GetDbConnection()
        {
            OracleConnection oraConn = new OracleConnection(connectionString);
            return oraConn as DbConnection;
        }

        #endregion
    }
}
