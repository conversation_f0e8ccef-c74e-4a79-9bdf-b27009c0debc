﻿namespace PlatCommonForm.Report
{
    partial class SecondHomesAttachedPage
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.xrLabel60 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ICUSYSB_SFFSXGGR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel62 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ICUSYSB_JSSJ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ICUSYSB_KSSJ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel58 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel59 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ICUQJSYSBMC = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel55 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel54 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SFFSRGQDTCK = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel52 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel50 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_APPCCHE_SFYC = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel53 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel47 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_APPCCHE_SFSW = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel51 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_APACCHEPF = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel49 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel41 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel38 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SF_APACCHEPF = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel45 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel36 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SYDNGZRS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel39 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel40 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_DNG_SFFSGLHT = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel42 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel43 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel44 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_DNG_SFZCR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel46 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SFFSLZDNGXGMNXTGR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel48 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SFSYLZDNGK = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel37 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel32 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel35 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SFFSSYZXJMGXGXLGR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel34 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ZXJMLZG_SFZCR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel33 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel30 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel29 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SFFSGLHT = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel31 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel28 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SYZXJMZGZRS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel26 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel25 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SFSYZXJMZG = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel27 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel24 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SFFSHXJXGFYGR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel22 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel21 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SYHXJXTGCT = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel19 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel20 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_SYHXJZRS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel18 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel17 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ICU_SFSYHXJ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel16 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel15 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_CFJGSJ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel14 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel13 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ICU_FYQCFQK = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel11 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ICU_HZSFSW = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel10 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel9 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ICU_ZCRZSFSJH = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel12 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ICU_LX = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ICU_RZCS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ICU_TCSJ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ICU_JRSJ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel23 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.HeightF = 4.166667F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // TopMargin
            // 
            this.TopMargin.HeightF = 31F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // BottomMargin
            // 
            this.BottomMargin.HeightF = 6F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // PageHeader
            // 
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel60,
            this.xrL_ICUSYSB_SFFSXGGR,
            this.xrLabel62,
            this.xrL_ICUSYSB_JSSJ,
            this.xrL_ICUSYSB_KSSJ,
            this.xrLabel58,
            this.xrLabel59,
            this.xrL_ICUQJSYSBMC,
            this.xrLabel55,
            this.xrLabel54,
            this.xrL_SFFSRGQDTCK,
            this.xrLabel52,
            this.xrLabel50,
            this.xrL_APPCCHE_SFYC,
            this.xrLabel53,
            this.xrLabel47,
            this.xrL_APPCCHE_SFSW,
            this.xrLabel51,
            this.xrL_APACCHEPF,
            this.xrLabel49,
            this.xrLabel41,
            this.xrLabel38,
            this.xrL_SF_APACCHEPF,
            this.xrLabel45,
            this.xrLabel36,
            this.xrL_SYDNGZRS,
            this.xrLabel39,
            this.xrLabel40,
            this.xrL_DNG_SFFSGLHT,
            this.xrLabel42,
            this.xrLabel43,
            this.xrLabel44,
            this.xrL_DNG_SFZCR,
            this.xrLabel46,
            this.xrL_SFFSLZDNGXGMNXTGR,
            this.xrLabel48,
            this.xrL_SFSYLZDNGK,
            this.xrLabel37,
            this.xrLabel32,
            this.xrLabel35,
            this.xrL_SFFSSYZXJMGXGXLGR,
            this.xrLabel34,
            this.xrL_ZXJMLZG_SFZCR,
            this.xrLabel33,
            this.xrLabel30,
            this.xrLabel29,
            this.xrL_SFFSGLHT,
            this.xrLabel31,
            this.xrLabel28,
            this.xrL_SYZXJMZGZRS,
            this.xrLabel26,
            this.xrLabel25,
            this.xrL_SFSYZXJMZG,
            this.xrLabel27,
            this.xrLabel24,
            this.xrL_SFFSHXJXGFYGR,
            this.xrLabel22,
            this.xrLabel21,
            this.xrL_SYHXJXTGCT,
            this.xrLabel19,
            this.xrLabel20,
            this.xrL_SYHXJZRS,
            this.xrLabel18,
            this.xrLabel17,
            this.xrL_ICU_SFSYHXJ,
            this.xrLabel16,
            this.xrLabel15,
            this.xrL_CFJGSJ,
            this.xrLabel14,
            this.xrLabel13,
            this.xrL_ICU_FYQCFQK,
            this.xrLabel11,
            this.xrL_ICU_HZSFSW,
            this.xrLabel10,
            this.xrLabel8,
            this.xrLabel9,
            this.xrL_ICU_ZCRZSFSJH,
            this.xrLabel6,
            this.xrLabel12,
            this.xrL_ICU_LX,
            this.xrLabel4,
            this.xrLabel7,
            this.xrL_ICU_RZCS,
            this.xrLabel5,
            this.xrLabel1,
            this.xrL_ICU_TCSJ,
            this.xrL_ICU_JRSJ,
            this.xrLabel23,
            this.xrLabel3,
            this.xrLabel2});
            this.PageHeader.HeightF = 653.125F;
            this.PageHeader.Name = "PageHeader";
            // 
            // xrLabel60
            // 
            this.xrLabel60.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel60.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel60.LocationFloat = new DevExpress.Utils.PointFloat(267.1667F, 599.2082F);
            this.xrLabel60.Name = "xrLabel60";
            this.xrLabel60.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel60.SizeF = new System.Drawing.SizeF(78.58325F, 25.99994F);
            this.xrLabel60.StylePriority.UseBorders = false;
            this.xrLabel60.StylePriority.UseFont = false;
            this.xrLabel60.StylePriority.UsePadding = false;
            this.xrLabel60.StylePriority.UseTextAlignment = false;
            this.xrLabel60.Text = "1.是 2.否";
            this.xrLabel60.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel60.WordWrap = false;
            // 
            // xrL_ICUSYSB_SFFSXGGR
            // 
            this.xrL_ICUSYSB_SFFSXGGR.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ICUSYSB_SFFSXGGR.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ICUSYSB_SFFSXGGR.LocationFloat = new DevExpress.Utils.PointFloat(239.1667F, 599.2082F);
            this.xrL_ICUSYSB_SFFSXGGR.Name = "xrL_ICUSYSB_SFFSXGGR";
            this.xrL_ICUSYSB_SFFSXGGR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ICUSYSB_SFFSXGGR.SizeF = new System.Drawing.SizeF(28F, 25.99994F);
            this.xrL_ICUSYSB_SFFSXGGR.StylePriority.UseBorders = false;
            this.xrL_ICUSYSB_SFFSXGGR.StylePriority.UseFont = false;
            this.xrL_ICUSYSB_SFFSXGGR.StylePriority.UsePadding = false;
            this.xrL_ICUSYSB_SFFSXGGR.StylePriority.UseTextAlignment = false;
            this.xrL_ICUSYSB_SFFSXGGR.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ICUSYSB_SFFSXGGR.WordWrap = false;
            // 
            // xrLabel62
            // 
            this.xrLabel62.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel62.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel62.LocationFloat = new DevExpress.Utils.PointFloat(1.416731F, 599.2082F);
            this.xrLabel62.Name = "xrLabel62";
            this.xrLabel62.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel62.SizeF = new System.Drawing.SizeF(237.75F, 25.99994F);
            this.xrLabel62.StylePriority.UseBorders = false;
            this.xrLabel62.StylePriority.UseFont = false;
            this.xrLabel62.StylePriority.UsePadding = false;
            this.xrLabel62.StylePriority.UseTextAlignment = false;
            this.xrLabel62.Text = "    使用期间是否发生相关感染:";
            this.xrLabel62.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel62.WordWrap = false;
            // 
            // xrL_ICUSYSB_JSSJ
            // 
            this.xrL_ICUSYSB_JSSJ.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_ICUSYSB_JSSJ.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ICUSYSB_JSSJ.LocationFloat = new DevExpress.Utils.PointFloat(360.6248F, 573.2084F);
            this.xrL_ICUSYSB_JSSJ.Name = "xrL_ICUSYSB_JSSJ";
            this.xrL_ICUSYSB_JSSJ.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_ICUSYSB_JSSJ.SizeF = new System.Drawing.SizeF(160.3333F, 25.99994F);
            this.xrL_ICUSYSB_JSSJ.StylePriority.UseBorders = false;
            this.xrL_ICUSYSB_JSSJ.StylePriority.UseFont = false;
            this.xrL_ICUSYSB_JSSJ.StylePriority.UsePadding = false;
            this.xrL_ICUSYSB_JSSJ.StylePriority.UseTextAlignment = false;
            this.xrL_ICUSYSB_JSSJ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrL_ICUSYSB_JSSJ.WordWrap = false;
            // 
            // xrL_ICUSYSB_KSSJ
            // 
            this.xrL_ICUSYSB_KSSJ.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_ICUSYSB_KSSJ.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ICUSYSB_KSSJ.LocationFloat = new DevExpress.Utils.PointFloat(105.6667F, 573.2084F);
            this.xrL_ICUSYSB_KSSJ.Name = "xrL_ICUSYSB_KSSJ";
            this.xrL_ICUSYSB_KSSJ.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_ICUSYSB_KSSJ.SizeF = new System.Drawing.SizeF(160.3333F, 25.99994F);
            this.xrL_ICUSYSB_KSSJ.StylePriority.UseBorders = false;
            this.xrL_ICUSYSB_KSSJ.StylePriority.UseFont = false;
            this.xrL_ICUSYSB_KSSJ.StylePriority.UsePadding = false;
            this.xrL_ICUSYSB_KSSJ.StylePriority.UseTextAlignment = false;
            this.xrL_ICUSYSB_KSSJ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrL_ICUSYSB_KSSJ.WordWrap = false;
            // 
            // xrLabel58
            // 
            this.xrLabel58.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel58.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel58.LocationFloat = new DevExpress.Utils.PointFloat(294.5416F, 573.2084F);
            this.xrLabel58.Name = "xrLabel58";
            this.xrLabel58.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel58.SizeF = new System.Drawing.SizeF(66.08334F, 25.99994F);
            this.xrLabel58.StylePriority.UseBorders = false;
            this.xrLabel58.StylePriority.UseFont = false;
            this.xrLabel58.StylePriority.UsePadding = false;
            this.xrLabel58.StylePriority.UseTextAlignment = false;
            this.xrLabel58.Text = "结束时间:";
            this.xrLabel58.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel58.WordWrap = false;
            // 
            // xrLabel59
            // 
            this.xrLabel59.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel59.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel59.LocationFloat = new DevExpress.Utils.PointFloat(0F, 573.2083F);
            this.xrLabel59.Name = "xrLabel59";
            this.xrLabel59.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel59.SizeF = new System.Drawing.SizeF(105.6667F, 25.99994F);
            this.xrLabel59.StylePriority.UseBorders = false;
            this.xrLabel59.StylePriority.UseFont = false;
            this.xrLabel59.StylePriority.UsePadding = false;
            this.xrLabel59.StylePriority.UseTextAlignment = false;
            this.xrLabel59.Text = "    开始时间:";
            this.xrLabel59.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel59.WordWrap = false;
            // 
            // xrL_ICUQJSYSBMC
            // 
            this.xrL_ICUQJSYSBMC.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_ICUQJSYSBMC.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ICUQJSYSBMC.LocationFloat = new DevExpress.Utils.PointFloat(163.1249F, 547.2085F);
            this.xrL_ICUQJSYSBMC.Name = "xrL_ICUQJSYSBMC";
            this.xrL_ICUQJSYSBMC.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ICUQJSYSBMC.SizeF = new System.Drawing.SizeF(429.9166F, 26F);
            this.xrL_ICUQJSYSBMC.StylePriority.UseBorders = false;
            this.xrL_ICUQJSYSBMC.StylePriority.UseFont = false;
            this.xrL_ICUQJSYSBMC.StylePriority.UsePadding = false;
            this.xrL_ICUQJSYSBMC.StylePriority.UseTextAlignment = false;
            this.xrL_ICUQJSYSBMC.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ICUQJSYSBMC.WordWrap = false;
            // 
            // xrLabel55
            // 
            this.xrLabel55.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel55.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel55.LocationFloat = new DevExpress.Utils.PointFloat(0F, 547.2084F);
            this.xrLabel55.Name = "xrLabel55";
            this.xrLabel55.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel55.SizeF = new System.Drawing.SizeF(163.1249F, 25.99988F);
            this.xrLabel55.StylePriority.UseBorders = false;
            this.xrLabel55.StylePriority.UseFont = false;
            this.xrLabel55.StylePriority.UsePadding = false;
            this.xrLabel55.StylePriority.UseTextAlignment = false;
            this.xrLabel55.Text = "    ICU期间使用设备名称:";
            this.xrLabel55.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel55.WordWrap = false;
            // 
            // xrLabel54
            // 
            this.xrLabel54.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel54.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel54.LocationFloat = new DevExpress.Utils.PointFloat(191.125F, 521.2086F);
            this.xrLabel54.Name = "xrLabel54";
            this.xrLabel54.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel54.SizeF = new System.Drawing.SizeF(78.58324F, 25.99994F);
            this.xrLabel54.StylePriority.UseBorders = false;
            this.xrLabel54.StylePriority.UseFont = false;
            this.xrLabel54.StylePriority.UsePadding = false;
            this.xrLabel54.StylePriority.UseTextAlignment = false;
            this.xrLabel54.Text = "1.是 2.否";
            this.xrLabel54.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel54.WordWrap = false;
            // 
            // xrL_SFFSRGQDTCK
            // 
            this.xrL_SFFSRGQDTCK.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SFFSRGQDTCK.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SFFSRGQDTCK.LocationFloat = new DevExpress.Utils.PointFloat(163.125F, 521.2086F);
            this.xrL_SFFSRGQDTCK.Name = "xrL_SFFSRGQDTCK";
            this.xrL_SFFSRGQDTCK.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SFFSRGQDTCK.SizeF = new System.Drawing.SizeF(27.99994F, 25.99994F);
            this.xrL_SFFSRGQDTCK.StylePriority.UseBorders = false;
            this.xrL_SFFSRGQDTCK.StylePriority.UseFont = false;
            this.xrL_SFFSRGQDTCK.StylePriority.UsePadding = false;
            this.xrL_SFFSRGQDTCK.StylePriority.UseTextAlignment = false;
            this.xrL_SFFSRGQDTCK.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SFFSRGQDTCK.WordWrap = false;
            // 
            // xrLabel52
            // 
            this.xrLabel52.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel52.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel52.LocationFloat = new DevExpress.Utils.PointFloat(0F, 521.2085F);
            this.xrLabel52.Name = "xrLabel52";
            this.xrLabel52.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel52.SizeF = new System.Drawing.SizeF(163.1249F, 25.99988F);
            this.xrLabel52.StylePriority.UseBorders = false;
            this.xrLabel52.StylePriority.UseFont = false;
            this.xrLabel52.StylePriority.UsePadding = false;
            this.xrLabel52.StylePriority.UseTextAlignment = false;
            this.xrLabel52.Text = "    是否发生人工气道脱出:";
            this.xrLabel52.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel52.WordWrap = false;
            // 
            // xrLabel50
            // 
            this.xrLabel50.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel50.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel50.LocationFloat = new DevExpress.Utils.PointFloat(412.7917F, 495.2086F);
            this.xrLabel50.Name = "xrLabel50";
            this.xrLabel50.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel50.SizeF = new System.Drawing.SizeF(71.37503F, 26F);
            this.xrLabel50.StylePriority.UseBorders = false;
            this.xrLabel50.StylePriority.UseFont = false;
            this.xrLabel50.StylePriority.UsePadding = false;
            this.xrLabel50.StylePriority.UseTextAlignment = false;
            this.xrLabel50.Text = "是否压疮 :";
            this.xrLabel50.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel50.WordWrap = false;
            // 
            // xrL_APPCCHE_SFYC
            // 
            this.xrL_APPCCHE_SFYC.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_APPCCHE_SFYC.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_APPCCHE_SFYC.LocationFloat = new DevExpress.Utils.PointFloat(486.4583F, 495.2086F);
            this.xrL_APPCCHE_SFYC.Name = "xrL_APPCCHE_SFYC";
            this.xrL_APPCCHE_SFYC.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_APPCCHE_SFYC.SizeF = new System.Drawing.SizeF(27.99994F, 26F);
            this.xrL_APPCCHE_SFYC.StylePriority.UseBorders = false;
            this.xrL_APPCCHE_SFYC.StylePriority.UseFont = false;
            this.xrL_APPCCHE_SFYC.StylePriority.UsePadding = false;
            this.xrL_APPCCHE_SFYC.StylePriority.UseTextAlignment = false;
            this.xrL_APPCCHE_SFYC.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_APPCCHE_SFYC.WordWrap = false;
            // 
            // xrLabel53
            // 
            this.xrLabel53.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel53.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel53.LocationFloat = new DevExpress.Utils.PointFloat(514.4583F, 495.2086F);
            this.xrLabel53.Name = "xrLabel53";
            this.xrLabel53.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel53.SizeF = new System.Drawing.SizeF(78.58325F, 26F);
            this.xrLabel53.StylePriority.UseBorders = false;
            this.xrLabel53.StylePriority.UseFont = false;
            this.xrLabel53.StylePriority.UsePadding = false;
            this.xrLabel53.StylePriority.UseTextAlignment = false;
            this.xrLabel53.Text = "1.是 2.否";
            this.xrLabel53.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel53.WordWrap = false;
            // 
            // xrLabel47
            // 
            this.xrLabel47.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel47.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel47.LocationFloat = new DevExpress.Utils.PointFloat(245.2083F, 495.2086F);
            this.xrLabel47.Name = "xrLabel47";
            this.xrLabel47.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel47.SizeF = new System.Drawing.SizeF(71.37505F, 26F);
            this.xrLabel47.StylePriority.UseBorders = false;
            this.xrLabel47.StylePriority.UseFont = false;
            this.xrLabel47.StylePriority.UsePadding = false;
            this.xrLabel47.StylePriority.UseTextAlignment = false;
            this.xrLabel47.Text = "是否死亡:";
            this.xrLabel47.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel47.WordWrap = false;
            // 
            // xrL_APPCCHE_SFSW
            // 
            this.xrL_APPCCHE_SFSW.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_APPCCHE_SFSW.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_APPCCHE_SFSW.LocationFloat = new DevExpress.Utils.PointFloat(315.5835F, 495.2086F);
            this.xrL_APPCCHE_SFSW.Name = "xrL_APPCCHE_SFSW";
            this.xrL_APPCCHE_SFSW.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_APPCCHE_SFSW.SizeF = new System.Drawing.SizeF(27.99994F, 26F);
            this.xrL_APPCCHE_SFSW.StylePriority.UseBorders = false;
            this.xrL_APPCCHE_SFSW.StylePriority.UseFont = false;
            this.xrL_APPCCHE_SFSW.StylePriority.UsePadding = false;
            this.xrL_APPCCHE_SFSW.StylePriority.UseTextAlignment = false;
            this.xrL_APPCCHE_SFSW.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_APPCCHE_SFSW.WordWrap = false;
            // 
            // xrLabel51
            // 
            this.xrLabel51.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel51.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel51.LocationFloat = new DevExpress.Utils.PointFloat(343.5835F, 495.2085F);
            this.xrLabel51.Name = "xrLabel51";
            this.xrLabel51.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel51.SizeF = new System.Drawing.SizeF(69.20822F, 26F);
            this.xrLabel51.StylePriority.UseBorders = false;
            this.xrLabel51.StylePriority.UseFont = false;
            this.xrLabel51.StylePriority.UsePadding = false;
            this.xrLabel51.StylePriority.UseTextAlignment = false;
            this.xrLabel51.Text = "1.是 2.否";
            this.xrLabel51.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel51.WordWrap = false;
            // 
            // xrL_APACCHEPF
            // 
            this.xrL_APACCHEPF.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_APACCHEPF.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_APACCHEPF.LocationFloat = new DevExpress.Utils.PointFloat(163.1249F, 495.2086F);
            this.xrL_APACCHEPF.Name = "xrL_APACCHEPF";
            this.xrL_APACCHEPF.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_APACCHEPF.SizeF = new System.Drawing.SizeF(53.16675F, 25.99994F);
            this.xrL_APACCHEPF.StylePriority.UseBorders = false;
            this.xrL_APACCHEPF.StylePriority.UseFont = false;
            this.xrL_APACCHEPF.StylePriority.UsePadding = false;
            this.xrL_APACCHEPF.StylePriority.UseTextAlignment = false;
            this.xrL_APACCHEPF.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_APACCHEPF.WordWrap = false;
            // 
            // xrLabel49
            // 
            this.xrLabel49.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel49.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel49.LocationFloat = new DevExpress.Utils.PointFloat(216.2916F, 495.2086F);
            this.xrLabel49.Name = "xrLabel49";
            this.xrLabel49.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel49.SizeF = new System.Drawing.SizeF(26.8333F, 25.99994F);
            this.xrLabel49.StylePriority.UseBorders = false;
            this.xrLabel49.StylePriority.UseFont = false;
            this.xrLabel49.StylePriority.UsePadding = false;
            this.xrLabel49.StylePriority.UseTextAlignment = false;
            this.xrLabel49.Text = "分";
            this.xrLabel49.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel49.WordWrap = false;
            // 
            // xrLabel41
            // 
            this.xrLabel41.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel41.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel41.LocationFloat = new DevExpress.Utils.PointFloat(1.416731F, 495.2085F);
            this.xrLabel41.Name = "xrLabel41";
            this.xrLabel41.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel41.SizeF = new System.Drawing.SizeF(161.7081F, 26F);
            this.xrLabel41.StylePriority.UseBorders = false;
            this.xrLabel41.StylePriority.UseFont = false;
            this.xrLabel41.StylePriority.UsePadding = false;
            this.xrLabel41.StylePriority.UseTextAlignment = false;
            this.xrLabel41.Text = "        APACCHEII评分:";
            this.xrLabel41.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel41.WordWrap = false;
            // 
            // xrLabel38
            // 
            this.xrLabel38.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel38.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel38.LocationFloat = new DevExpress.Utils.PointFloat(0F, 469.2085F);
            this.xrLabel38.Name = "xrLabel38";
            this.xrLabel38.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel38.SizeF = new System.Drawing.SizeF(163.1249F, 26F);
            this.xrLabel38.StylePriority.UseBorders = false;
            this.xrLabel38.StylePriority.UseFont = false;
            this.xrLabel38.StylePriority.UsePadding = false;
            this.xrLabel38.StylePriority.UseTextAlignment = false;
            this.xrLabel38.Text = "    是否APACCHEII评分:";
            this.xrLabel38.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel38.WordWrap = false;
            // 
            // xrL_SF_APACCHEPF
            // 
            this.xrL_SF_APACCHEPF.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SF_APACCHEPF.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SF_APACCHEPF.LocationFloat = new DevExpress.Utils.PointFloat(163.1249F, 469.2085F);
            this.xrL_SF_APACCHEPF.Name = "xrL_SF_APACCHEPF";
            this.xrL_SF_APACCHEPF.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SF_APACCHEPF.SizeF = new System.Drawing.SizeF(27.99994F, 26F);
            this.xrL_SF_APACCHEPF.StylePriority.UseBorders = false;
            this.xrL_SF_APACCHEPF.StylePriority.UseFont = false;
            this.xrL_SF_APACCHEPF.StylePriority.UsePadding = false;
            this.xrL_SF_APACCHEPF.StylePriority.UseTextAlignment = false;
            this.xrL_SF_APACCHEPF.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SF_APACCHEPF.WordWrap = false;
            // 
            // xrLabel45
            // 
            this.xrLabel45.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel45.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel45.LocationFloat = new DevExpress.Utils.PointFloat(191.125F, 469.2085F);
            this.xrLabel45.Name = "xrLabel45";
            this.xrLabel45.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel45.SizeF = new System.Drawing.SizeF(78.58325F, 26F);
            this.xrLabel45.StylePriority.UseBorders = false;
            this.xrLabel45.StylePriority.UseFont = false;
            this.xrLabel45.StylePriority.UsePadding = false;
            this.xrLabel45.StylePriority.UseTextAlignment = false;
            this.xrLabel45.Text = "1.是 2.否";
            this.xrLabel45.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel45.WordWrap = false;
            // 
            // xrLabel36
            // 
            this.xrLabel36.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel36.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel36.LocationFloat = new DevExpress.Utils.PointFloat(1.416731F, 417.2084F);
            this.xrLabel36.Name = "xrLabel36";
            this.xrLabel36.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel36.SizeF = new System.Drawing.SizeF(203.4583F, 25.99991F);
            this.xrLabel36.StylePriority.UseBorders = false;
            this.xrLabel36.StylePriority.UseFont = false;
            this.xrLabel36.StylePriority.UsePadding = false;
            this.xrLabel36.StylePriority.UseTextAlignment = false;
            this.xrLabel36.Text = "        使用导尿管总日数:";
            this.xrLabel36.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel36.WordWrap = false;
            // 
            // xrL_SYDNGZRS
            // 
            this.xrL_SYDNGZRS.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_SYDNGZRS.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SYDNGZRS.LocationFloat = new DevExpress.Utils.PointFloat(204.875F, 417.2086F);
            this.xrL_SYDNGZRS.Name = "xrL_SYDNGZRS";
            this.xrL_SYDNGZRS.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SYDNGZRS.SizeF = new System.Drawing.SizeF(36.83324F, 25.99991F);
            this.xrL_SYDNGZRS.StylePriority.UseBorders = false;
            this.xrL_SYDNGZRS.StylePriority.UseFont = false;
            this.xrL_SYDNGZRS.StylePriority.UsePadding = false;
            this.xrL_SYDNGZRS.StylePriority.UseTextAlignment = false;
            this.xrL_SYDNGZRS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SYDNGZRS.WordWrap = false;
            // 
            // xrLabel39
            // 
            this.xrLabel39.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel39.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel39.LocationFloat = new DevExpress.Utils.PointFloat(245.2083F, 417.2086F);
            this.xrLabel39.Name = "xrLabel39";
            this.xrLabel39.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel39.SizeF = new System.Drawing.SizeF(36.83328F, 25.99994F);
            this.xrLabel39.StylePriority.UseBorders = false;
            this.xrLabel39.StylePriority.UseFont = false;
            this.xrLabel39.StylePriority.UsePadding = false;
            this.xrLabel39.StylePriority.UseTextAlignment = false;
            this.xrLabel39.Text = "天";
            this.xrLabel39.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel39.WordWrap = false;
            // 
            // xrLabel40
            // 
            this.xrLabel40.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel40.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel40.LocationFloat = new DevExpress.Utils.PointFloat(282.0416F, 417.2086F);
            this.xrLabel40.Name = "xrLabel40";
            this.xrLabel40.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel40.SizeF = new System.Drawing.SizeF(119.2917F, 25.99997F);
            this.xrLabel40.StylePriority.UseBorders = false;
            this.xrLabel40.StylePriority.UseFont = false;
            this.xrLabel40.StylePriority.UsePadding = false;
            this.xrLabel40.StylePriority.UseTextAlignment = false;
            this.xrLabel40.Text = "是否发生管路滑脱:";
            this.xrLabel40.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel40.WordWrap = false;
            // 
            // xrL_DNG_SFFSGLHT
            // 
            this.xrL_DNG_SFFSGLHT.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_DNG_SFFSGLHT.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_DNG_SFFSGLHT.LocationFloat = new DevExpress.Utils.PointFloat(403.4583F, 417.2086F);
            this.xrL_DNG_SFFSGLHT.Name = "xrL_DNG_SFFSGLHT";
            this.xrL_DNG_SFFSGLHT.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_DNG_SFFSGLHT.SizeF = new System.Drawing.SizeF(27.99994F, 26F);
            this.xrL_DNG_SFFSGLHT.StylePriority.UseBorders = false;
            this.xrL_DNG_SFFSGLHT.StylePriority.UseFont = false;
            this.xrL_DNG_SFFSGLHT.StylePriority.UsePadding = false;
            this.xrL_DNG_SFFSGLHT.StylePriority.UseTextAlignment = false;
            this.xrL_DNG_SFFSGLHT.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_DNG_SFFSGLHT.WordWrap = false;
            // 
            // xrLabel42
            // 
            this.xrLabel42.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel42.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel42.LocationFloat = new DevExpress.Utils.PointFloat(431.4583F, 417.2086F);
            this.xrLabel42.Name = "xrLabel42";
            this.xrLabel42.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel42.SizeF = new System.Drawing.SizeF(78.58325F, 26F);
            this.xrLabel42.StylePriority.UseBorders = false;
            this.xrLabel42.StylePriority.UseFont = false;
            this.xrLabel42.StylePriority.UsePadding = false;
            this.xrLabel42.StylePriority.UseTextAlignment = false;
            this.xrLabel42.Text = "1.是 2.否";
            this.xrLabel42.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel42.WordWrap = false;
            // 
            // xrLabel43
            // 
            this.xrLabel43.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel43.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel43.LocationFloat = new DevExpress.Utils.PointFloat(1.416731F, 443.2083F);
            this.xrLabel43.Name = "xrLabel43";
            this.xrLabel43.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel43.SizeF = new System.Drawing.SizeF(135F, 25.99994F);
            this.xrLabel43.StylePriority.UseBorders = false;
            this.xrLabel43.StylePriority.UseFont = false;
            this.xrLabel43.StylePriority.UsePadding = false;
            this.xrLabel43.StylePriority.UseTextAlignment = false;
            this.xrLabel43.Text = "        是否再插入:";
            this.xrLabel43.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel43.WordWrap = false;
            // 
            // xrLabel44
            // 
            this.xrLabel44.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel44.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel44.LocationFloat = new DevExpress.Utils.PointFloat(165.9584F, 443.2086F);
            this.xrLabel44.Name = "xrLabel44";
            this.xrLabel44.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel44.SizeF = new System.Drawing.SizeF(78.58325F, 25.99997F);
            this.xrLabel44.StylePriority.UseBorders = false;
            this.xrLabel44.StylePriority.UseFont = false;
            this.xrLabel44.StylePriority.UsePadding = false;
            this.xrLabel44.StylePriority.UseTextAlignment = false;
            this.xrLabel44.Text = "1.是 2.否";
            this.xrLabel44.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel44.WordWrap = false;
            // 
            // xrL_DNG_SFZCR
            // 
            this.xrL_DNG_SFZCR.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_DNG_SFZCR.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_DNG_SFZCR.LocationFloat = new DevExpress.Utils.PointFloat(137.9584F, 443.2086F);
            this.xrL_DNG_SFZCR.Name = "xrL_DNG_SFZCR";
            this.xrL_DNG_SFZCR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_DNG_SFZCR.SizeF = new System.Drawing.SizeF(27.99994F, 25.99997F);
            this.xrL_DNG_SFZCR.StylePriority.UseBorders = false;
            this.xrL_DNG_SFZCR.StylePriority.UseFont = false;
            this.xrL_DNG_SFZCR.StylePriority.UsePadding = false;
            this.xrL_DNG_SFZCR.StylePriority.UseTextAlignment = false;
            this.xrL_DNG_SFZCR.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_DNG_SFZCR.WordWrap = false;
            // 
            // xrLabel46
            // 
            this.xrLabel46.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel46.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel46.LocationFloat = new DevExpress.Utils.PointFloat(244.5416F, 443.2086F);
            this.xrLabel46.Name = "xrLabel46";
            this.xrLabel46.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel46.SizeF = new System.Drawing.SizeF(237.75F, 25.99994F);
            this.xrLabel46.StylePriority.UseBorders = false;
            this.xrLabel46.StylePriority.UseFont = false;
            this.xrLabel46.StylePriority.UsePadding = false;
            this.xrLabel46.StylePriority.UseTextAlignment = false;
            this.xrLabel46.Text = "是否发生留置导尿管相关泌尿系统感染:";
            this.xrLabel46.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel46.WordWrap = false;
            // 
            // xrL_SFFSLZDNGXGMNXTGR
            // 
            this.xrL_SFFSLZDNGXGMNXTGR.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SFFSLZDNGXGMNXTGR.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SFFSLZDNGXGMNXTGR.LocationFloat = new DevExpress.Utils.PointFloat(482.2916F, 443.2086F);
            this.xrL_SFFSLZDNGXGMNXTGR.Name = "xrL_SFFSLZDNGXGMNXTGR";
            this.xrL_SFFSLZDNGXGMNXTGR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SFFSLZDNGXGMNXTGR.SizeF = new System.Drawing.SizeF(28F, 25.99997F);
            this.xrL_SFFSLZDNGXGMNXTGR.StylePriority.UseBorders = false;
            this.xrL_SFFSLZDNGXGMNXTGR.StylePriority.UseFont = false;
            this.xrL_SFFSLZDNGXGMNXTGR.StylePriority.UsePadding = false;
            this.xrL_SFFSLZDNGXGMNXTGR.StylePriority.UseTextAlignment = false;
            this.xrL_SFFSLZDNGXGMNXTGR.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SFFSLZDNGXGMNXTGR.WordWrap = false;
            // 
            // xrLabel48
            // 
            this.xrLabel48.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel48.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel48.LocationFloat = new DevExpress.Utils.PointFloat(510.2916F, 443.2086F);
            this.xrLabel48.Name = "xrLabel48";
            this.xrLabel48.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel48.SizeF = new System.Drawing.SizeF(78.58325F, 25.99997F);
            this.xrLabel48.StylePriority.UseBorders = false;
            this.xrLabel48.StylePriority.UseFont = false;
            this.xrLabel48.StylePriority.UsePadding = false;
            this.xrLabel48.StylePriority.UseTextAlignment = false;
            this.xrLabel48.Text = "1.是 2.否";
            this.xrLabel48.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel48.WordWrap = false;
            // 
            // xrL_SFSYLZDNGK
            // 
            this.xrL_SFSYLZDNGK.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SFSYLZDNGK.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SFSYLZDNGK.LocationFloat = new DevExpress.Utils.PointFloat(164.5417F, 391.2084F);
            this.xrL_SFSYLZDNGK.Name = "xrL_SFSYLZDNGK";
            this.xrL_SFSYLZDNGK.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SFSYLZDNGK.SizeF = new System.Drawing.SizeF(27.99994F, 25.99997F);
            this.xrL_SFSYLZDNGK.StylePriority.UseBorders = false;
            this.xrL_SFSYLZDNGK.StylePriority.UseFont = false;
            this.xrL_SFSYLZDNGK.StylePriority.UsePadding = false;
            this.xrL_SFSYLZDNGK.StylePriority.UseTextAlignment = false;
            this.xrL_SFSYLZDNGK.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SFSYLZDNGK.WordWrap = false;
            // 
            // xrLabel37
            // 
            this.xrLabel37.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel37.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel37.LocationFloat = new DevExpress.Utils.PointFloat(192.5417F, 391.2084F);
            this.xrLabel37.Name = "xrLabel37";
            this.xrLabel37.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel37.SizeF = new System.Drawing.SizeF(78.58324F, 25.99997F);
            this.xrLabel37.StylePriority.UseBorders = false;
            this.xrLabel37.StylePriority.UseFont = false;
            this.xrLabel37.StylePriority.UsePadding = false;
            this.xrLabel37.StylePriority.UseTextAlignment = false;
            this.xrLabel37.Text = "1.是 2.否";
            this.xrLabel37.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel37.WordWrap = false;
            // 
            // xrLabel32
            // 
            this.xrLabel32.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel32.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel32.LocationFloat = new DevExpress.Utils.PointFloat(1.416731F, 391.2084F);
            this.xrLabel32.Name = "xrLabel32";
            this.xrLabel32.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel32.SizeF = new System.Drawing.SizeF(163.1249F, 25.99997F);
            this.xrLabel32.StylePriority.UseBorders = false;
            this.xrLabel32.StylePriority.UseFont = false;
            this.xrLabel32.StylePriority.UsePadding = false;
            this.xrLabel32.StylePriority.UseTextAlignment = false;
            this.xrLabel32.Text = "    是否使用留置导尿管:";
            this.xrLabel32.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel32.WordWrap = false;
            // 
            // xrLabel35
            // 
            this.xrLabel35.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel35.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel35.LocationFloat = new DevExpress.Utils.PointFloat(508.8749F, 365.2084F);
            this.xrLabel35.Name = "xrLabel35";
            this.xrLabel35.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel35.SizeF = new System.Drawing.SizeF(78.58325F, 26F);
            this.xrLabel35.StylePriority.UseBorders = false;
            this.xrLabel35.StylePriority.UseFont = false;
            this.xrLabel35.StylePriority.UsePadding = false;
            this.xrLabel35.StylePriority.UseTextAlignment = false;
            this.xrLabel35.Text = "1.是 2.否";
            this.xrLabel35.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel35.WordWrap = false;
            // 
            // xrL_SFFSSYZXJMGXGXLGR
            // 
            this.xrL_SFFSSYZXJMGXGXLGR.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SFFSSYZXJMGXGXLGR.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SFFSSYZXJMGXGXLGR.LocationFloat = new DevExpress.Utils.PointFloat(480.8749F, 365.2084F);
            this.xrL_SFFSSYZXJMGXGXLGR.Name = "xrL_SFFSSYZXJMGXGXLGR";
            this.xrL_SFFSSYZXJMGXGXLGR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SFFSSYZXJMGXGXLGR.SizeF = new System.Drawing.SizeF(28F, 26F);
            this.xrL_SFFSSYZXJMGXGXLGR.StylePriority.UseBorders = false;
            this.xrL_SFFSSYZXJMGXGXLGR.StylePriority.UseFont = false;
            this.xrL_SFFSSYZXJMGXGXLGR.StylePriority.UsePadding = false;
            this.xrL_SFFSSYZXJMGXGXLGR.StylePriority.UseTextAlignment = false;
            this.xrL_SFFSSYZXJMGXGXLGR.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SFFSSYZXJMGXGXLGR.WordWrap = false;
            // 
            // xrLabel34
            // 
            this.xrLabel34.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel34.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel34.LocationFloat = new DevExpress.Utils.PointFloat(243.1249F, 365.2084F);
            this.xrLabel34.Name = "xrLabel34";
            this.xrLabel34.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel34.SizeF = new System.Drawing.SizeF(237.75F, 25.99997F);
            this.xrLabel34.StylePriority.UseBorders = false;
            this.xrLabel34.StylePriority.UseFont = false;
            this.xrLabel34.StylePriority.UsePadding = false;
            this.xrLabel34.StylePriority.UseTextAlignment = false;
            this.xrLabel34.Text = "是否发生使用中心静脉置管相关血流感染:";
            this.xrLabel34.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel34.WordWrap = false;
            // 
            // xrL_ZXJMLZG_SFZCR
            // 
            this.xrL_ZXJMLZG_SFZCR.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ZXJMLZG_SFZCR.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ZXJMLZG_SFZCR.LocationFloat = new DevExpress.Utils.PointFloat(136.5417F, 365.2084F);
            this.xrL_ZXJMLZG_SFZCR.Name = "xrL_ZXJMLZG_SFZCR";
            this.xrL_ZXJMLZG_SFZCR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ZXJMLZG_SFZCR.SizeF = new System.Drawing.SizeF(27.99994F, 26F);
            this.xrL_ZXJMLZG_SFZCR.StylePriority.UseBorders = false;
            this.xrL_ZXJMLZG_SFZCR.StylePriority.UseFont = false;
            this.xrL_ZXJMLZG_SFZCR.StylePriority.UsePadding = false;
            this.xrL_ZXJMLZG_SFZCR.StylePriority.UseTextAlignment = false;
            this.xrL_ZXJMLZG_SFZCR.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ZXJMLZG_SFZCR.WordWrap = false;
            // 
            // xrLabel33
            // 
            this.xrLabel33.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel33.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel33.LocationFloat = new DevExpress.Utils.PointFloat(164.5417F, 365.2084F);
            this.xrLabel33.Name = "xrLabel33";
            this.xrLabel33.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel33.SizeF = new System.Drawing.SizeF(78.58325F, 26F);
            this.xrLabel33.StylePriority.UseBorders = false;
            this.xrLabel33.StylePriority.UseFont = false;
            this.xrLabel33.StylePriority.UsePadding = false;
            this.xrLabel33.StylePriority.UseTextAlignment = false;
            this.xrLabel33.Text = "1.是 2.否";
            this.xrLabel33.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel33.WordWrap = false;
            // 
            // xrLabel30
            // 
            this.xrLabel30.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel30.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel30.LocationFloat = new DevExpress.Utils.PointFloat(0F, 365.2083F);
            this.xrLabel30.Name = "xrLabel30";
            this.xrLabel30.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel30.SizeF = new System.Drawing.SizeF(135F, 25.99991F);
            this.xrLabel30.StylePriority.UseBorders = false;
            this.xrLabel30.StylePriority.UseFont = false;
            this.xrLabel30.StylePriority.UsePadding = false;
            this.xrLabel30.StylePriority.UseTextAlignment = false;
            this.xrLabel30.Text = "        是否再插入:";
            this.xrLabel30.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel30.WordWrap = false;
            // 
            // xrLabel29
            // 
            this.xrLabel29.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel29.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel29.LocationFloat = new DevExpress.Utils.PointFloat(430.7916F, 339.2085F);
            this.xrLabel29.Name = "xrLabel29";
            this.xrLabel29.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel29.SizeF = new System.Drawing.SizeF(78.58325F, 26F);
            this.xrLabel29.StylePriority.UseBorders = false;
            this.xrLabel29.StylePriority.UseFont = false;
            this.xrLabel29.StylePriority.UsePadding = false;
            this.xrLabel29.StylePriority.UseTextAlignment = false;
            this.xrLabel29.Text = "1.是 2.否";
            this.xrLabel29.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel29.WordWrap = false;
            // 
            // xrL_SFFSGLHT
            // 
            this.xrL_SFFSGLHT.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SFFSGLHT.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SFFSGLHT.LocationFloat = new DevExpress.Utils.PointFloat(402.7916F, 339.2085F);
            this.xrL_SFFSGLHT.Name = "xrL_SFFSGLHT";
            this.xrL_SFFSGLHT.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SFFSGLHT.SizeF = new System.Drawing.SizeF(27.99994F, 26F);
            this.xrL_SFFSGLHT.StylePriority.UseBorders = false;
            this.xrL_SFFSGLHT.StylePriority.UseFont = false;
            this.xrL_SFFSGLHT.StylePriority.UsePadding = false;
            this.xrL_SFFSGLHT.StylePriority.UseTextAlignment = false;
            this.xrL_SFFSGLHT.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SFFSGLHT.WordWrap = false;
            // 
            // xrLabel31
            // 
            this.xrLabel31.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel31.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel31.LocationFloat = new DevExpress.Utils.PointFloat(281.3749F, 339.2085F);
            this.xrLabel31.Name = "xrLabel31";
            this.xrLabel31.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel31.SizeF = new System.Drawing.SizeF(119.2917F, 25.99997F);
            this.xrLabel31.StylePriority.UseBorders = false;
            this.xrLabel31.StylePriority.UseFont = false;
            this.xrLabel31.StylePriority.UsePadding = false;
            this.xrLabel31.StylePriority.UseTextAlignment = false;
            this.xrLabel31.Text = "是否发生管路滑脱:";
            this.xrLabel31.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel31.WordWrap = false;
            // 
            // xrLabel28
            // 
            this.xrLabel28.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel28.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel28.LocationFloat = new DevExpress.Utils.PointFloat(244.5416F, 339.2085F);
            this.xrLabel28.Name = "xrLabel28";
            this.xrLabel28.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel28.SizeF = new System.Drawing.SizeF(36.83328F, 25.99994F);
            this.xrLabel28.StylePriority.UseBorders = false;
            this.xrLabel28.StylePriority.UseFont = false;
            this.xrLabel28.StylePriority.UsePadding = false;
            this.xrLabel28.StylePriority.UseTextAlignment = false;
            this.xrLabel28.Text = "天";
            this.xrLabel28.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel28.WordWrap = false;
            // 
            // xrL_SYZXJMZGZRS
            // 
            this.xrL_SYZXJMZGZRS.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_SYZXJMZGZRS.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SYZXJMZGZRS.LocationFloat = new DevExpress.Utils.PointFloat(203.4583F, 339.2085F);
            this.xrL_SYZXJMZGZRS.Name = "xrL_SYZXJMZGZRS";
            this.xrL_SYZXJMZGZRS.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SYZXJMZGZRS.SizeF = new System.Drawing.SizeF(38.24995F, 25.99991F);
            this.xrL_SYZXJMZGZRS.StylePriority.UseBorders = false;
            this.xrL_SYZXJMZGZRS.StylePriority.UseFont = false;
            this.xrL_SYZXJMZGZRS.StylePriority.UsePadding = false;
            this.xrL_SYZXJMZGZRS.StylePriority.UseTextAlignment = false;
            this.xrL_SYZXJMZGZRS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SYZXJMZGZRS.WordWrap = false;
            // 
            // xrLabel26
            // 
            this.xrLabel26.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel26.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel26.LocationFloat = new DevExpress.Utils.PointFloat(0F, 339.2083F);
            this.xrLabel26.Name = "xrLabel26";
            this.xrLabel26.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel26.SizeF = new System.Drawing.SizeF(203.4583F, 25.99994F);
            this.xrLabel26.StylePriority.UseBorders = false;
            this.xrLabel26.StylePriority.UseFont = false;
            this.xrLabel26.StylePriority.UsePadding = false;
            this.xrLabel26.StylePriority.UseTextAlignment = false;
            this.xrLabel26.Text = "        使用中心静脉置管总日数:";
            this.xrLabel26.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel26.WordWrap = false;
            // 
            // xrLabel25
            // 
            this.xrLabel25.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel25.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel25.LocationFloat = new DevExpress.Utils.PointFloat(7.947286E-06F, 313.2083F);
            this.xrLabel25.Name = "xrLabel25";
            this.xrLabel25.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel25.SizeF = new System.Drawing.SizeF(163.1249F, 25.99997F);
            this.xrLabel25.StylePriority.UseBorders = false;
            this.xrLabel25.StylePriority.UseFont = false;
            this.xrLabel25.StylePriority.UsePadding = false;
            this.xrLabel25.StylePriority.UseTextAlignment = false;
            this.xrLabel25.Text = "    是否使用中心静脉置管:";
            this.xrLabel25.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel25.WordWrap = false;
            // 
            // xrL_SFSYZXJMZG
            // 
            this.xrL_SFSYZXJMZG.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SFSYZXJMZG.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SFSYZXJMZG.LocationFloat = new DevExpress.Utils.PointFloat(164.5417F, 313.2085F);
            this.xrL_SFSYZXJMZG.Name = "xrL_SFSYZXJMZG";
            this.xrL_SFSYZXJMZG.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SFSYZXJMZG.SizeF = new System.Drawing.SizeF(27.99994F, 25.99997F);
            this.xrL_SFSYZXJMZG.StylePriority.UseBorders = false;
            this.xrL_SFSYZXJMZG.StylePriority.UseFont = false;
            this.xrL_SFSYZXJMZG.StylePriority.UsePadding = false;
            this.xrL_SFSYZXJMZG.StylePriority.UseTextAlignment = false;
            this.xrL_SFSYZXJMZG.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SFSYZXJMZG.WordWrap = false;
            // 
            // xrLabel27
            // 
            this.xrLabel27.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel27.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel27.LocationFloat = new DevExpress.Utils.PointFloat(192.5417F, 313.2085F);
            this.xrLabel27.Name = "xrLabel27";
            this.xrLabel27.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel27.SizeF = new System.Drawing.SizeF(78.58325F, 25.99997F);
            this.xrLabel27.StylePriority.UseBorders = false;
            this.xrLabel27.StylePriority.UseFont = false;
            this.xrLabel27.StylePriority.UsePadding = false;
            this.xrLabel27.StylePriority.UseTextAlignment = false;
            this.xrLabel27.Text = "1.是 2.否";
            this.xrLabel27.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel27.WordWrap = false;
            // 
            // xrLabel24
            // 
            this.xrLabel24.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel24.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel24.LocationFloat = new DevExpress.Utils.PointFloat(284.875F, 287.2084F);
            this.xrLabel24.Name = "xrLabel24";
            this.xrLabel24.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel24.SizeF = new System.Drawing.SizeF(75.74991F, 25.99994F);
            this.xrLabel24.StylePriority.UseBorders = false;
            this.xrLabel24.StylePriority.UseFont = false;
            this.xrLabel24.StylePriority.UsePadding = false;
            this.xrLabel24.StylePriority.UseTextAlignment = false;
            this.xrLabel24.Text = "1.是 2.否";
            this.xrLabel24.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel24.WordWrap = false;
            // 
            // xrL_SFFSHXJXGFYGR
            // 
            this.xrL_SFFSHXJXGFYGR.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_SFFSHXJXGFYGR.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SFFSHXJXGFYGR.LocationFloat = new DevExpress.Utils.PointFloat(256.8749F, 287.2085F);
            this.xrL_SFFSHXJXGFYGR.Name = "xrL_SFFSHXJXGFYGR";
            this.xrL_SFFSHXJXGFYGR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SFFSHXJXGFYGR.SizeF = new System.Drawing.SizeF(25.16656F, 25.99991F);
            this.xrL_SFFSHXJXGFYGR.StylePriority.UseBorders = false;
            this.xrL_SFFSHXJXGFYGR.StylePriority.UseFont = false;
            this.xrL_SFFSHXJXGFYGR.StylePriority.UsePadding = false;
            this.xrL_SFFSHXJXGFYGR.StylePriority.UseTextAlignment = false;
            this.xrL_SFFSHXJXGFYGR.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SFFSHXJXGFYGR.WordWrap = false;
            // 
            // xrLabel22
            // 
            this.xrLabel22.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel22.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel22.LocationFloat = new DevExpress.Utils.PointFloat(0F, 287.2083F);
            this.xrLabel22.Name = "xrLabel22";
            this.xrLabel22.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel22.SizeF = new System.Drawing.SizeF(256.8749F, 26F);
            this.xrLabel22.StylePriority.UseBorders = false;
            this.xrLabel22.StylePriority.UseFont = false;
            this.xrLabel22.StylePriority.UsePadding = false;
            this.xrLabel22.StylePriority.UseTextAlignment = false;
            this.xrLabel22.Text = "       是否发生使用呼吸机相关肺炎感染:";
            this.xrLabel22.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel22.WordWrap = false;
            // 
            // xrLabel21
            // 
            this.xrLabel21.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel21.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel21.LocationFloat = new DevExpress.Utils.PointFloat(380.8751F, 261.2083F);
            this.xrLabel21.Name = "xrLabel21";
            this.xrLabel21.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel21.SizeF = new System.Drawing.SizeF(78.58322F, 25.99997F);
            this.xrLabel21.StylePriority.UseBorders = false;
            this.xrLabel21.StylePriority.UseFont = false;
            this.xrLabel21.StylePriority.UsePadding = false;
            this.xrLabel21.StylePriority.UseTextAlignment = false;
            this.xrLabel21.Text = "天";
            this.xrLabel21.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel21.WordWrap = false;
            // 
            // xrL_SYHXJXTGCT
            // 
            this.xrL_SYHXJXTGCT.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_SYHXJXTGCT.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SYHXJXTGCT.LocationFloat = new DevExpress.Utils.PointFloat(327.7083F, 261.2083F);
            this.xrL_SYHXJXTGCT.Name = "xrL_SYHXJXTGCT";
            this.xrL_SYHXJXTGCT.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SYHXJXTGCT.SizeF = new System.Drawing.SizeF(53.16675F, 25.99994F);
            this.xrL_SYHXJXTGCT.StylePriority.UseBorders = false;
            this.xrL_SYHXJXTGCT.StylePriority.UseFont = false;
            this.xrL_SYHXJXTGCT.StylePriority.UsePadding = false;
            this.xrL_SYHXJXTGCT.StylePriority.UseTextAlignment = false;
            this.xrL_SYHXJXTGCT.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SYHXJXTGCT.WordWrap = false;
            // 
            // xrLabel19
            // 
            this.xrLabel19.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel19.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel19.LocationFloat = new DevExpress.Utils.PointFloat(0F, 261.2083F);
            this.xrLabel19.Name = "xrLabel19";
            this.xrLabel19.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel19.SizeF = new System.Drawing.SizeF(327.7083F, 26F);
            this.xrLabel19.StylePriority.UseBorders = false;
            this.xrLabel19.StylePriority.UseFont = false;
            this.xrLabel19.StylePriority.UsePadding = false;
            this.xrLabel19.StylePriority.UseTextAlignment = false;
            this.xrLabel19.Text = "       使用呼吸机下抬高床头≧ 30度日数（每天2次）:";
            this.xrLabel19.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel19.WordWrap = false;
            // 
            // xrLabel20
            // 
            this.xrLabel20.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel20.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel20.LocationFloat = new DevExpress.Utils.PointFloat(220.5417F, 235.2084F);
            this.xrLabel20.Name = "xrLabel20";
            this.xrLabel20.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel20.SizeF = new System.Drawing.SizeF(78.58325F, 25.99997F);
            this.xrLabel20.StylePriority.UseBorders = false;
            this.xrLabel20.StylePriority.UseFont = false;
            this.xrLabel20.StylePriority.UsePadding = false;
            this.xrLabel20.StylePriority.UseTextAlignment = false;
            this.xrLabel20.Text = "天";
            this.xrLabel20.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel20.WordWrap = false;
            // 
            // xrL_SYHXJZRS
            // 
            this.xrL_SYHXJZRS.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_SYHXJZRS.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_SYHXJZRS.LocationFloat = new DevExpress.Utils.PointFloat(163.1249F, 235.2084F);
            this.xrL_SYHXJZRS.Name = "xrL_SYHXJZRS";
            this.xrL_SYHXJZRS.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_SYHXJZRS.SizeF = new System.Drawing.SizeF(56.20842F, 25.99992F);
            this.xrL_SYHXJZRS.StylePriority.UseBorders = false;
            this.xrL_SYHXJZRS.StylePriority.UseFont = false;
            this.xrL_SYHXJZRS.StylePriority.UsePadding = false;
            this.xrL_SYHXJZRS.StylePriority.UseTextAlignment = false;
            this.xrL_SYHXJZRS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_SYHXJZRS.WordWrap = false;
            // 
            // xrLabel18
            // 
            this.xrLabel18.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel18.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel18.LocationFloat = new DevExpress.Utils.PointFloat(0F, 235.2083F);
            this.xrLabel18.Name = "xrLabel18";
            this.xrLabel18.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel18.SizeF = new System.Drawing.SizeF(163.1249F, 26F);
            this.xrLabel18.StylePriority.UseBorders = false;
            this.xrLabel18.StylePriority.UseFont = false;
            this.xrLabel18.StylePriority.UsePadding = false;
            this.xrLabel18.StylePriority.UseTextAlignment = false;
            this.xrLabel18.Text = "       使用呼吸机总日数:";
            this.xrLabel18.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel18.WordWrap = false;
            // 
            // xrLabel17
            // 
            this.xrLabel17.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel17.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel17.LocationFloat = new DevExpress.Utils.PointFloat(163.125F, 209.2084F);
            this.xrLabel17.Name = "xrLabel17";
            this.xrLabel17.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel17.SizeF = new System.Drawing.SizeF(78.58327F, 25.99997F);
            this.xrLabel17.StylePriority.UseBorders = false;
            this.xrLabel17.StylePriority.UseFont = false;
            this.xrLabel17.StylePriority.UsePadding = false;
            this.xrLabel17.StylePriority.UseTextAlignment = false;
            this.xrLabel17.Text = "1.是 2.否";
            this.xrLabel17.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel17.WordWrap = false;
            // 
            // xrL_ICU_SFSYHXJ
            // 
            this.xrL_ICU_SFSYHXJ.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ICU_SFSYHXJ.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ICU_SFSYHXJ.LocationFloat = new DevExpress.Utils.PointFloat(135.125F, 209.2084F);
            this.xrL_ICU_SFSYHXJ.Name = "xrL_ICU_SFSYHXJ";
            this.xrL_ICU_SFSYHXJ.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ICU_SFSYHXJ.SizeF = new System.Drawing.SizeF(27.99994F, 25.99997F);
            this.xrL_ICU_SFSYHXJ.StylePriority.UseBorders = false;
            this.xrL_ICU_SFSYHXJ.StylePriority.UseFont = false;
            this.xrL_ICU_SFSYHXJ.StylePriority.UsePadding = false;
            this.xrL_ICU_SFSYHXJ.StylePriority.UseTextAlignment = false;
            this.xrL_ICU_SFSYHXJ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ICU_SFSYHXJ.WordWrap = false;
            // 
            // xrLabel16
            // 
            this.xrLabel16.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel16.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel16.LocationFloat = new DevExpress.Utils.PointFloat(7.947286E-06F, 209.2083F);
            this.xrLabel16.Name = "xrLabel16";
            this.xrLabel16.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel16.SizeF = new System.Drawing.SizeF(135F, 25.99998F);
            this.xrLabel16.StylePriority.UseBorders = false;
            this.xrLabel16.StylePriority.UseFont = false;
            this.xrLabel16.StylePriority.UsePadding = false;
            this.xrLabel16.StylePriority.UseTextAlignment = false;
            this.xrLabel16.Text = "    是否使用呼吸机:";
            this.xrLabel16.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel16.WordWrap = false;
            // 
            // xrLabel15
            // 
            this.xrLabel15.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel15.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel15.LocationFloat = new DevExpress.Utils.PointFloat(431.4583F, 183.2083F);
            this.xrLabel15.Name = "xrLabel15";
            this.xrLabel15.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel15.SizeF = new System.Drawing.SizeF(267.1248F, 25.99995F);
            this.xrLabel15.StylePriority.UseBorders = false;
            this.xrLabel15.StylePriority.UseFont = false;
            this.xrLabel15.StylePriority.UsePadding = false;
            this.xrLabel15.StylePriority.UseTextAlignment = false;
            this.xrLabel15.Text = "1. 24小时内 2. 24-48小时 3. >48小时 ";
            this.xrLabel15.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel15.WordWrap = false;
            // 
            // xrL_CFJGSJ
            // 
            this.xrL_CFJGSJ.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_CFJGSJ.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_CFJGSJ.LocationFloat = new DevExpress.Utils.PointFloat(403.4583F, 183.2084F);
            this.xrL_CFJGSJ.Name = "xrL_CFJGSJ";
            this.xrL_CFJGSJ.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_CFJGSJ.SizeF = new System.Drawing.SizeF(27.99997F, 25.99997F);
            this.xrL_CFJGSJ.StylePriority.UseBorders = false;
            this.xrL_CFJGSJ.StylePriority.UseFont = false;
            this.xrL_CFJGSJ.StylePriority.UsePadding = false;
            this.xrL_CFJGSJ.StylePriority.UseTextAlignment = false;
            this.xrL_CFJGSJ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_CFJGSJ.WordWrap = false;
            // 
            // xrLabel14
            // 
            this.xrLabel14.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel14.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel14.LocationFloat = new DevExpress.Utils.PointFloat(302.375F, 183.2084F);
            this.xrLabel14.Name = "xrLabel14";
            this.xrLabel14.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel14.SizeF = new System.Drawing.SizeF(98.95834F, 25.99997F);
            this.xrLabel14.StylePriority.UseBorders = false;
            this.xrLabel14.StylePriority.UseFont = false;
            this.xrLabel14.StylePriority.UsePadding = false;
            this.xrLabel14.StylePriority.UseTextAlignment = false;
            this.xrLabel14.Text = " 重返间隔时间:";
            this.xrLabel14.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel14.WordWrap = false;
            // 
            // xrLabel13
            // 
            this.xrLabel13.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel13.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel13.LocationFloat = new DevExpress.Utils.PointFloat(220.5417F, 183.2083F);
            this.xrLabel13.Name = "xrLabel13";
            this.xrLabel13.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel13.SizeF = new System.Drawing.SizeF(78.58327F, 25.99997F);
            this.xrLabel13.StylePriority.UseBorders = false;
            this.xrLabel13.StylePriority.UseFont = false;
            this.xrLabel13.StylePriority.UsePadding = false;
            this.xrLabel13.StylePriority.UseTextAlignment = false;
            this.xrLabel13.Text = "1.是 2.否";
            this.xrLabel13.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel13.WordWrap = false;
            // 
            // xrL_ICU_FYQCFQK
            // 
            this.xrL_ICU_FYQCFQK.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ICU_FYQCFQK.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ICU_FYQCFQK.LocationFloat = new DevExpress.Utils.PointFloat(192.5417F, 183.2084F);
            this.xrL_ICU_FYQCFQK.Name = "xrL_ICU_FYQCFQK";
            this.xrL_ICU_FYQCFQK.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ICU_FYQCFQK.SizeF = new System.Drawing.SizeF(27.99995F, 25.99995F);
            this.xrL_ICU_FYQCFQK.StylePriority.UseBorders = false;
            this.xrL_ICU_FYQCFQK.StylePriority.UseFont = false;
            this.xrL_ICU_FYQCFQK.StylePriority.UsePadding = false;
            this.xrL_ICU_FYQCFQK.StylePriority.UseTextAlignment = false;
            this.xrL_ICU_FYQCFQK.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ICU_FYQCFQK.WordWrap = false;
            // 
            // xrLabel11
            // 
            this.xrLabel11.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel11.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel11.LocationFloat = new DevExpress.Utils.PointFloat(7.947286E-06F, 183.2083F);
            this.xrLabel11.Name = "xrLabel11";
            this.xrLabel11.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel11.SizeF = new System.Drawing.SizeF(189F, 25.99997F);
            this.xrLabel11.StylePriority.UseBorders = false;
            this.xrLabel11.StylePriority.UseFont = false;
            this.xrLabel11.StylePriority.UsePadding = false;
            this.xrLabel11.StylePriority.UseTextAlignment = false;
            this.xrLabel11.Text = "    非预期24/48小时重返情况:";
            this.xrLabel11.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel11.WordWrap = false;
            // 
            // xrL_ICU_HZSFSW
            // 
            this.xrL_ICU_HZSFSW.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ICU_HZSFSW.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ICU_HZSFSW.LocationFloat = new DevExpress.Utils.PointFloat(431.4583F, 157.2084F);
            this.xrL_ICU_HZSFSW.Name = "xrL_ICU_HZSFSW";
            this.xrL_ICU_HZSFSW.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ICU_HZSFSW.SizeF = new System.Drawing.SizeF(27.99997F, 25.99997F);
            this.xrL_ICU_HZSFSW.StylePriority.UseBorders = false;
            this.xrL_ICU_HZSFSW.StylePriority.UseFont = false;
            this.xrL_ICU_HZSFSW.StylePriority.UsePadding = false;
            this.xrL_ICU_HZSFSW.StylePriority.UseTextAlignment = false;
            this.xrL_ICU_HZSFSW.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ICU_HZSFSW.WordWrap = false;
            // 
            // xrLabel10
            // 
            this.xrLabel10.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel10.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel10.LocationFloat = new DevExpress.Utils.PointFloat(459.4583F, 157.2083F);
            this.xrLabel10.Name = "xrLabel10";
            this.xrLabel10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel10.SizeF = new System.Drawing.SizeF(75.45822F, 25.99997F);
            this.xrLabel10.StylePriority.UseBorders = false;
            this.xrLabel10.StylePriority.UseFont = false;
            this.xrLabel10.StylePriority.UsePadding = false;
            this.xrLabel10.StylePriority.UseTextAlignment = false;
            this.xrLabel10.Text = "1.是 2.否";
            this.xrLabel10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel10.WordWrap = false;
            // 
            // xrLabel8
            // 
            this.xrLabel8.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel8.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel8.LocationFloat = new DevExpress.Utils.PointFloat(282.0416F, 157.2084F);
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel8.SizeF = new System.Drawing.SizeF(149.4167F, 25.99998F);
            this.xrLabel8.StylePriority.UseBorders = false;
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UsePadding = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            this.xrLabel8.Text = "    患者是否在ICU死亡:";
            this.xrLabel8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel8.WordWrap = false;
            // 
            // xrLabel9
            // 
            this.xrLabel9.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel9.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel9.LocationFloat = new DevExpress.Utils.PointFloat(203.4583F, 157.2083F);
            this.xrLabel9.Name = "xrLabel9";
            this.xrLabel9.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel9.SizeF = new System.Drawing.SizeF(78.58328F, 25.99997F);
            this.xrLabel9.StylePriority.UseBorders = false;
            this.xrLabel9.StylePriority.UseFont = false;
            this.xrLabel9.StylePriority.UsePadding = false;
            this.xrLabel9.StylePriority.UseTextAlignment = false;
            this.xrLabel9.Text = "1.是 2.否";
            this.xrLabel9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel9.WordWrap = false;
            // 
            // xrL_ICU_ZCRZSFSJH
            // 
            this.xrL_ICU_ZCRZSFSJH.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ICU_ZCRZSFSJH.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ICU_ZCRZSFSJH.LocationFloat = new DevExpress.Utils.PointFloat(175.4583F, 157.2084F);
            this.xrL_ICU_ZCRZSFSJH.Name = "xrL_ICU_ZCRZSFSJH";
            this.xrL_ICU_ZCRZSFSJH.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ICU_ZCRZSFSJH.SizeF = new System.Drawing.SizeF(27.99997F, 25.99997F);
            this.xrL_ICU_ZCRZSFSJH.StylePriority.UseBorders = false;
            this.xrL_ICU_ZCRZSFSJH.StylePriority.UseFont = false;
            this.xrL_ICU_ZCRZSFSJH.StylePriority.UsePadding = false;
            this.xrL_ICU_ZCRZSFSJH.StylePriority.UseTextAlignment = false;
            this.xrL_ICU_ZCRZSFSJH.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ICU_ZCRZSFSJH.WordWrap = false;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel6.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel6.LocationFloat = new DevExpress.Utils.PointFloat(7.947286E-06F, 157.2083F);
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel6.SizeF = new System.Drawing.SizeF(175.4583F, 25.99998F);
            this.xrLabel6.StylePriority.UseBorders = false;
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UsePadding = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            this.xrLabel6.Text = "    再次入住是否属计划入住:";
            this.xrLabel6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel6.WordWrap = false;
            // 
            // xrLabel12
            // 
            this.xrLabel12.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel12.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel12.LocationFloat = new DevExpress.Utils.PointFloat(135F, 131.2084F);
            this.xrLabel12.Name = "xrLabel12";
            this.xrLabel12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel12.SizeF = new System.Drawing.SizeF(612.9999F, 25.99998F);
            this.xrLabel12.StylePriority.UseBorders = false;
            this.xrLabel12.StylePriority.UseFont = false;
            this.xrLabel12.StylePriority.UsePadding = false;
            this.xrLabel12.StylePriority.UseTextAlignment = false;
            this.xrLabel12.Text = "1.EICU急诊  2. CCU 心内  3. SICU 外科  4.NICU 神经  5.RICU 呼吸 6. PICU 儿科 7. 综合 ICU 8. 其他";
            this.xrLabel12.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel12.WordWrap = false;
            // 
            // xrL_ICU_LX
            // 
            this.xrL_ICU_LX.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrL_ICU_LX.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ICU_LX.LocationFloat = new DevExpress.Utils.PointFloat(107F, 131.2084F);
            this.xrL_ICU_LX.Name = "xrL_ICU_LX";
            this.xrL_ICU_LX.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 100F);
            this.xrL_ICU_LX.SizeF = new System.Drawing.SizeF(28F, 25.99998F);
            this.xrL_ICU_LX.StylePriority.UseBorders = false;
            this.xrL_ICU_LX.StylePriority.UseFont = false;
            this.xrL_ICU_LX.StylePriority.UsePadding = false;
            this.xrL_ICU_LX.StylePriority.UseTextAlignment = false;
            this.xrL_ICU_LX.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrL_ICU_LX.WordWrap = false;
            // 
            // xrLabel4
            // 
            this.xrLabel4.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel4.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel4.LocationFloat = new DevExpress.Utils.PointFloat(0F, 131.2084F);
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel4.SizeF = new System.Drawing.SizeF(105.6667F, 25.99998F);
            this.xrLabel4.StylePriority.UseBorders = false;
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UsePadding = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            this.xrLabel4.Text = "    ICU类型:";
            this.xrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel4.WordWrap = false;
            // 
            // xrLabel7
            // 
            this.xrLabel7.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel7.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel7.LocationFloat = new DevExpress.Utils.PointFloat(596.1668F, 105.2084F);
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel7.SizeF = new System.Drawing.SizeF(58.79172F, 25.99998F);
            this.xrLabel7.StylePriority.UseBorders = false;
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UsePadding = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            this.xrLabel7.Text = "次入住";
            this.xrLabel7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel7.WordWrap = false;
            // 
            // xrL_ICU_RZCS
            // 
            this.xrL_ICU_RZCS.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_ICU_RZCS.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ICU_RZCS.LocationFloat = new DevExpress.Utils.PointFloat(542.9168F, 105.2084F);
            this.xrL_ICU_RZCS.Name = "xrL_ICU_RZCS";
            this.xrL_ICU_RZCS.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_ICU_RZCS.SizeF = new System.Drawing.SizeF(53.25F, 25.99999F);
            this.xrL_ICU_RZCS.StylePriority.UseBorders = false;
            this.xrL_ICU_RZCS.StylePriority.UseFont = false;
            this.xrL_ICU_RZCS.StylePriority.UsePadding = false;
            this.xrL_ICU_RZCS.StylePriority.UseTextAlignment = false;
            this.xrL_ICU_RZCS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrL_ICU_RZCS.WordWrap = false;
            // 
            // xrLabel5
            // 
            this.xrLabel5.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel5.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel5.LocationFloat = new DevExpress.Utils.PointFloat(503.9168F, 105.2084F);
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel5.SizeF = new System.Drawing.SizeF(39F, 25.99999F);
            this.xrLabel5.StylePriority.UseBorders = false;
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UsePadding = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            this.xrLabel5.Text = "第";
            this.xrLabel5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.xrLabel5.WordWrap = false;
            // 
            // xrLabel1
            // 
            this.xrLabel1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel1.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(277.5002F, 105.2084F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(66.08333F, 25.99999F);
            this.xrLabel1.StylePriority.UseBorders = false;
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UsePadding = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "退出时间:";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel1.WordWrap = false;
            // 
            // xrL_ICU_TCSJ
            // 
            this.xrL_ICU_TCSJ.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_ICU_TCSJ.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ICU_TCSJ.LocationFloat = new DevExpress.Utils.PointFloat(343.5835F, 105.2084F);
            this.xrL_ICU_TCSJ.Name = "xrL_ICU_TCSJ";
            this.xrL_ICU_TCSJ.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_ICU_TCSJ.SizeF = new System.Drawing.SizeF(160.3333F, 25.99999F);
            this.xrL_ICU_TCSJ.StylePriority.UseBorders = false;
            this.xrL_ICU_TCSJ.StylePriority.UseFont = false;
            this.xrL_ICU_TCSJ.StylePriority.UsePadding = false;
            this.xrL_ICU_TCSJ.StylePriority.UseTextAlignment = false;
            this.xrL_ICU_TCSJ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrL_ICU_TCSJ.WordWrap = false;
            // 
            // xrL_ICU_JRSJ
            // 
            this.xrL_ICU_JRSJ.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrL_ICU_JRSJ.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrL_ICU_JRSJ.LocationFloat = new DevExpress.Utils.PointFloat(105.6667F, 105.2084F);
            this.xrL_ICU_JRSJ.Name = "xrL_ICU_JRSJ";
            this.xrL_ICU_JRSJ.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrL_ICU_JRSJ.SizeF = new System.Drawing.SizeF(160.3333F, 25.99999F);
            this.xrL_ICU_JRSJ.StylePriority.UseBorders = false;
            this.xrL_ICU_JRSJ.StylePriority.UseFont = false;
            this.xrL_ICU_JRSJ.StylePriority.UsePadding = false;
            this.xrL_ICU_JRSJ.StylePriority.UseTextAlignment = false;
            this.xrL_ICU_JRSJ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrL_ICU_JRSJ.WordWrap = false;
            // 
            // xrLabel23
            // 
            this.xrLabel23.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel23.Font = new System.Drawing.Font("黑体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel23.LocationFloat = new DevExpress.Utils.PointFloat(0F, 105.2083F);
            this.xrLabel23.Name = "xrLabel23";
            this.xrLabel23.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel23.SizeF = new System.Drawing.SizeF(105.6667F, 25.99999F);
            this.xrLabel23.StylePriority.UseBorders = false;
            this.xrLabel23.StylePriority.UseFont = false;
            this.xrLabel23.StylePriority.UsePadding = false;
            this.xrLabel23.StylePriority.UseTextAlignment = false;
            this.xrLabel23.Text = "    进入时间:";
            this.xrLabel23.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel23.WordWrap = false;
            // 
            // xrLabel3
            // 
            this.xrLabel3.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel3.Font = new System.Drawing.Font("黑体", 10F, System.Drawing.FontStyle.Bold);
            this.xrLabel3.LocationFloat = new DevExpress.Utils.PointFloat(0F, 68.58333F);
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 8, 0, 100F);
            this.xrLabel3.SizeF = new System.Drawing.SizeF(219.3333F, 25.99998F);
            this.xrLabel3.StylePriority.UseBorders = false;
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UsePadding = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            this.xrLabel3.Text = "二、ICU转出前填写";
            this.xrLabel3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrLabel3.WordWrap = false;
            // 
            // xrLabel2
            // 
            this.xrLabel2.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel2.Font = new System.Drawing.Font("黑体", 16F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(0F, 23.95833F);
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(757F, 29F);
            this.xrLabel2.StylePriority.UseBorders = false;
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            this.xrLabel2.Text = "住院病案首页附页 (二)";
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // SecondHomesAttachedPage
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageHeader});
            this.Margins = new System.Drawing.Printing.Margins(43, 26, 31, 6);
            this.PageHeight = 1169;
            this.PageWidth = 827;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.Version = "17.2";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRLabel xrL_ICU_JRSJ;
        private DevExpress.XtraReports.UI.XRLabel xrLabel23;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel xrL_ICU_RZCS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel xrL_ICU_TCSJ;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.XRLabel xrLabel12;
        private DevExpress.XtraReports.UI.XRLabel xrL_ICU_LX;
        private DevExpress.XtraReports.UI.XRLabel xrLabel9;
        private DevExpress.XtraReports.UI.XRLabel xrL_ICU_ZCRZSFSJH;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLabel xrLabel14;
        private DevExpress.XtraReports.UI.XRLabel xrLabel13;
        private DevExpress.XtraReports.UI.XRLabel xrL_ICU_FYQCFQK;
        private DevExpress.XtraReports.UI.XRLabel xrLabel11;
        private DevExpress.XtraReports.UI.XRLabel xrL_ICU_HZSFSW;
        private DevExpress.XtraReports.UI.XRLabel xrLabel10;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel xrLabel15;
        private DevExpress.XtraReports.UI.XRLabel xrL_CFJGSJ;
        private DevExpress.XtraReports.UI.XRLabel xrLabel17;
        private DevExpress.XtraReports.UI.XRLabel xrL_ICU_SFSYHXJ;
        private DevExpress.XtraReports.UI.XRLabel xrLabel16;
        private DevExpress.XtraReports.UI.XRLabel xrLabel18;
        private DevExpress.XtraReports.UI.XRLabel xrLabel19;
        private DevExpress.XtraReports.UI.XRLabel xrLabel20;
        private DevExpress.XtraReports.UI.XRLabel xrL_SYHXJZRS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel21;
        private DevExpress.XtraReports.UI.XRLabel xrL_SYHXJXTGCT;
        private DevExpress.XtraReports.UI.XRLabel xrLabel22;
        private DevExpress.XtraReports.UI.XRLabel xrLabel24;
        private DevExpress.XtraReports.UI.XRLabel xrL_SFFSHXJXGFYGR;
        private DevExpress.XtraReports.UI.XRLabel xrLabel30;
        private DevExpress.XtraReports.UI.XRLabel xrLabel29;
        private DevExpress.XtraReports.UI.XRLabel xrL_SFFSGLHT;
        private DevExpress.XtraReports.UI.XRLabel xrLabel31;
        private DevExpress.XtraReports.UI.XRLabel xrLabel28;
        private DevExpress.XtraReports.UI.XRLabel xrL_SYZXJMZGZRS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel26;
        private DevExpress.XtraReports.UI.XRLabel xrLabel25;
        private DevExpress.XtraReports.UI.XRLabel xrL_SFSYZXJMZG;
        private DevExpress.XtraReports.UI.XRLabel xrLabel27;
        private DevExpress.XtraReports.UI.XRLabel xrL_ICUQJSYSBMC;
        private DevExpress.XtraReports.UI.XRLabel xrLabel55;
        private DevExpress.XtraReports.UI.XRLabel xrLabel54;
        private DevExpress.XtraReports.UI.XRLabel xrL_SFFSRGQDTCK;
        private DevExpress.XtraReports.UI.XRLabel xrLabel52;
        private DevExpress.XtraReports.UI.XRLabel xrLabel50;
        private DevExpress.XtraReports.UI.XRLabel xrL_APPCCHE_SFYC;
        private DevExpress.XtraReports.UI.XRLabel xrLabel53;
        private DevExpress.XtraReports.UI.XRLabel xrLabel47;
        private DevExpress.XtraReports.UI.XRLabel xrL_APPCCHE_SFSW;
        private DevExpress.XtraReports.UI.XRLabel xrLabel51;
        private DevExpress.XtraReports.UI.XRLabel xrL_APACCHEPF;
        private DevExpress.XtraReports.UI.XRLabel xrLabel49;
        private DevExpress.XtraReports.UI.XRLabel xrLabel41;
        private DevExpress.XtraReports.UI.XRLabel xrLabel38;
        private DevExpress.XtraReports.UI.XRLabel xrL_SF_APACCHEPF;
        private DevExpress.XtraReports.UI.XRLabel xrLabel45;
        private DevExpress.XtraReports.UI.XRLabel xrLabel36;
        private DevExpress.XtraReports.UI.XRLabel xrL_SYDNGZRS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel39;
        private DevExpress.XtraReports.UI.XRLabel xrLabel40;
        private DevExpress.XtraReports.UI.XRLabel xrL_DNG_SFFSGLHT;
        private DevExpress.XtraReports.UI.XRLabel xrLabel42;
        private DevExpress.XtraReports.UI.XRLabel xrLabel43;
        private DevExpress.XtraReports.UI.XRLabel xrLabel44;
        private DevExpress.XtraReports.UI.XRLabel xrL_DNG_SFZCR;
        private DevExpress.XtraReports.UI.XRLabel xrLabel46;
        private DevExpress.XtraReports.UI.XRLabel xrL_SFFSLZDNGXGMNXTGR;
        private DevExpress.XtraReports.UI.XRLabel xrLabel48;
        private DevExpress.XtraReports.UI.XRLabel xrL_SFSYLZDNGK;
        private DevExpress.XtraReports.UI.XRLabel xrLabel37;
        private DevExpress.XtraReports.UI.XRLabel xrLabel32;
        private DevExpress.XtraReports.UI.XRLabel xrLabel35;
        private DevExpress.XtraReports.UI.XRLabel xrL_SFFSSYZXJMGXGXLGR;
        private DevExpress.XtraReports.UI.XRLabel xrLabel34;
        private DevExpress.XtraReports.UI.XRLabel xrL_ZXJMLZG_SFZCR;
        private DevExpress.XtraReports.UI.XRLabel xrLabel33;
        private DevExpress.XtraReports.UI.XRLabel xrLabel60;
        private DevExpress.XtraReports.UI.XRLabel xrL_ICUSYSB_SFFSXGGR;
        private DevExpress.XtraReports.UI.XRLabel xrLabel62;
        private DevExpress.XtraReports.UI.XRLabel xrL_ICUSYSB_JSSJ;
        private DevExpress.XtraReports.UI.XRLabel xrL_ICUSYSB_KSSJ;
        private DevExpress.XtraReports.UI.XRLabel xrLabel58;
        private DevExpress.XtraReports.UI.XRLabel xrLabel59;
    }
}
