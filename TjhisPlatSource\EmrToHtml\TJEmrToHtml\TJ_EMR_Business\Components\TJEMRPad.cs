﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Collections;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using TJ.EMR.Utility;
using System.Xml;
using System.Text.RegularExpressions;
using System.Runtime.InteropServices;
using System.IO;

namespace TJ.EMR.Business.Components
{
    public partial class TJEMRPad : UserControl
    {
        public TJEMRPad()
        {
            InitializeComponent();
            emrEditorControl1.InitializeDocument();
            emrEditorControl1.CaretMoved += emrEditorControl1_CaretMoved;
            emrEditorControl1.CheckStatusChanged += new TJTextEditor.ElementClickEventHandler(emrEditorControl1_CheckStatusChanged);
            emrEditorControl1.SetHospitalName("濮阳市油田总医院");
            emrEditorControl1.SetHeaderLineHeight(5.0F);
            emrEditorControl1.SetFooterLineHeight(7.0F);
            List<string> fontName = emrEditorControl1.GetFontNameList();
            foreach (string item in fontName)
            {
                tscmbFontName.Items.Add(item);
            }
            
            tscmbFontName.SelectedIndex = fontName.IndexOf("宋体");
            string[] fontSize = emrEditorControl1.GetFontSizeList();
            foreach (string item in fontSize)
            {
                tscmbFontSize.Items.Add(item);
            }
            tscmbFontSize.SelectedIndex = Array.IndexOf(fontSize, "五号");
            //emrEditorControl1.SetSingleClickSelectedMode(true);//单选元素  默认勾选 单击选择
            emrEditorControl1.LineSpaceMultiple(1.5F);
        }

        public void emrEditorControl1_CheckStatusChanged(object sender, TJTextEditor.ElementClickEventArgs e)
        {
            if (CheckStatusChangedEvent != null)
                CheckStatusChangedEvent(sender, e);
        }

        void emrEditorControl1_CaretMoved(object sender, EventArgs e)
        {
            tscmbFontSize.SelectedIndexChanged -= tscmbFontSize_SelectedIndexChanged;
            tscmbFontName.SelectedIndexChanged -= tscmbFontName_SelectedIndexChanged;
            tscmbFontSize.Text = emrEditorControl1.GetFontSizeNameBySize(float.Parse(emrEditorControl1.SelectionFontSize())); ;
            tscmbFontName.Text = emrEditorControl1.SelectionFontName();
            tscmbFontSize.SelectedIndexChanged += tscmbFontSize_SelectedIndexChanged;
            tscmbFontName.SelectedIndexChanged += tscmbFontName_SelectedIndexChanged;
            //string fontName = emrEditorControl1.SelectionFontName();
            //string fontSize = emrEditorControl1.SelectionFontSize();
            //if (!string.IsNullOrEmpty(fontName))
            //    tscmbFontName.Text = fontName;
            //if (!string.IsNullOrEmpty(fontSize))f
            //{
            //    tscmbFontSize.Text = emrEditorControl1.GetFontSizeNameBySize(float.Parse(fontSize));
            //}
            //tsbtnFontBold.Checked = emrEditorControl1.FontIsBold();
            //tsbtnFontItalic.Checked = emrEditorControl1.FontIsItalic();
            //tsbtnUnderLine.Checked = emrEditorControl1.FontIsUnderLine();
            //tsbtnAlignCenter.Checked = emrEditorControl1.LineAlignIsCenter();
            //tsbtnAlignLeft.Checked = emrEditorControl1.LineAlignIsLeft();
            //tsbtnAlignRight.Checked = emrEditorControl1.LineAlignIsRight();
            //tsbtnAlignJustify.Checked = emrEditorControl1.LineAlignIsJustify();
            //tsbtnDisperse.Checked = emrEditorControl1.LineAlignIsDisperse();
        }

        #region 变量初始化
        private string Path { get; set; }
        private string FileName { get; set; }
        public event EventHandler OnLoadSchema;
        public delegate string onDbClick(string mr_code); //双击委托
        public event onDbClick OnDbClickEvent = null;
        public event EventHandler OnClickRightMouse;
        public event EventHandler OnSumbitClick;
        public delegate void clickEvent(); //单击委托
        public event clickEvent OnClickEvent = null;
        public delegate void SelectKeyWordDelegate();
        public event SelectKeyWordDelegate SelectKeyWordEvent = null;
        public delegate void CheckStatusChangedEventDelegate();
        public TJTextEditor.ElementClickEventHandler CheckStatusChangedEvent=null;
        public long DocShowMode;
        public event EventHandler onPrintAllPageClick;
        public event EventHandler onPrintSelectedClick;
        #endregion

        #region 基方法 1

        #region  结构图

        public TreeNode GetSchemaTree()
        {
            return emrEditorControl1.GetSchemaTree();
        }


        /// <summary>
        /// 另存文件
        /// 2012-07-09 wangsx修改 屏蔽axEMRPad301.SetDocumentMode(1);
        /// </summary>
        /// <param name="strFile">文件名</param>
        /// <param name="nFileType">0病历文件、1模板文件、2关键词、3平面语法文件、4XML文件、5Text文件、12加密的Text文件、13把编辑器当前内容追加到指定的加密Text文件、14XML大纲文件。 7 HTML文件  输出显示的样式和病历一样 (2007.05.1 扩充)  16  基于GB2312 的平面XML 文件输出(2007.08.3 扩充)</param>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool FileSaveAsAllType(string strFile, short nFileType)
        {           
            //return emrEditorControl1.FileSaveAs(strFile, nFileType, 0);
            return emrEditorControl1.FileSaveAs(strFile, nFileType);
        }
        #endregion

        #region mark
        public ArrayList GetReversalInfo()
        {
            return emrEditorControl1.GetRevisalInfo();
        }

        /// <summary>
        /// 文档是否包含指定名称的书签，true包含，false不包含
        /// </summary>
        /// <param name="bookMarkName">书签名称</param>
        /// <returns></returns>
        public bool ContainBookMark(string bookMarkName)
        {
            return emrEditorControl1.ContainBookMark(bookMarkName);
        }
        public void ClearReversalInfo()
        {
            emrEditorControl1.ClearReversalInfo();
        }

        /// <summary>
        /// 创建书签
        /// </summary>
        /// <param name="bookMarkName"></param>
        public bool CreateBookMark(string bookMarkName)
        {
            try
            {
                return emrEditorControl1.CreateBookMark(bookMarkName);
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        /// <summary>
        /// 定位书签
        /// </summary>
        /// <param name="bookMarkName"></param>
        public void LocateBookMark(string bookMarkName)
        {
            emrEditorControl1.LocateBookMark(bookMarkName);
        }
         /// <summary>
        /// 获取文档书签列表
        /// </summary>
        /// <returns></returns>
        public ArrayList GetBookMarks()
        {
            return emrEditorControl1.GetBookMarks();
        }

         /// <summary>
        /// 删除书签
        /// </summary>
        /// <param name="bookMarkName"></param>
        public void DeleteBookMark(string bookMarkName)
        {
            emrEditorControl1.DeleteBookMark(bookMarkName);
        }
        #endregion

        #region  2.2 文件操作


        /// <summary>
        /// 新建文件
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool FileNew()
        {
            return emrEditorControl1.FileNew();
        }

        /// <summary>
        /// 打开文件
        /// </summary>
        /// <param name="strFile">指定打开的文档名，应当包含全路径名。例如“C:\住院病历.emr”</param>
        /// <returns></returns>
        /// <remarks>对于编辑器生成的文件不一定需要扩展名，编辑器会识别文件的类型如病历文件、模板
        ///文件、关键词等，对于打开外部的文件，如图象，文本文件等，需要指定扩展名，否则编辑电子病历编辑器控件V3.0开发手册 
        ///器对不识别的文件类型自动按照文本文件处理，对与二进制的文件可能会出现处理错误。特
        ///别指出，打开病历平面语法文件一定要使用emp为扩展名，编辑器会自动处理语法，转换为
        ///病历文件格式，否则会按照文本文件处理。此功能根据控件注册版本的不同，会禁止使用此
        ///功能。</remarks>
        public bool FileOpen(string strFile)
        {
            return emrEditorControl1.FileOpen(strFile, false);
        }
        /// <summary>
        /// 另存文件
        /// 2012-07-09 wangsx修改 屏蔽emrEditorControl1.SetDocumentMode(1);
        /// </summary>
        /// <param name="strFile">文件名</param>
        /// <param name="nFileType">0病历文件、1模板文件、2关键词、3平面语法文件、4 XML文件、5 Text文件、12 加密的Text文件、13把编辑器当前内容追加到指定的加密Text文件、14XML大纲文件。 7 HTML文件  输出显示的样式和病历一样 (2007.05.1 扩充)  16  基于GB2312 的平面XML 文件输出(2007.08.3 扩充)</param>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool FileSaveAs(string strFile, short nFileType)
        {
            //emrEditorControl1.SetDocumentMode(1);
            if (nFileType == 4)  // 4XML文件    16  基于GB2312 的平面XML  统一采用GB2312的XML格式，解决保存XML到oracle报错的问题（没有内容的选择项）
                nFileType = 16;
            return emrEditorControl1.FileSaveAs(strFile, nFileType);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="strFile"></param>
        /// <param name="nFileType"></param>
        /// <returns></returns>
        public bool FileSaveAsHtml(string strFile)
        {
            System.Xml.XmlDocument myDoc = new System.Xml.XmlDocument();
            emrEditorControl1.Document.ToHTMLDocument(myDoc);
            myDoc.Save(strFile);
            if (File.Exists(strFile))
            {
                string text = File.ReadAllText(strFile, Encoding.UTF8);
                text = text.Replace("nbsp", "ensp");
                File.WriteAllText(strFile, text, Encoding.UTF8);
            }
            return true;
        }
        /// <summary>
        /// 保存文件
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool FileSave()
        {
            emrEditorControl1.SetDocumentMode(1);
            return emrEditorControl1.FileSave();
        }

        /// <summary>
        /// 插入指定文件
        /// </summary>
        /// <param name="strFile">指定插入的文档名，应当包含全路径名</param>
        /// <returns></returns>
        /// <remarks>插入操作支持关键词、文本文件和图像文件。</remarks>
        public bool FileInsert(string strFile)
        {
            return emrEditorControl1.FileInsert(strFile);
        }

        /// <summary>
        /// 返回当前文件名称
        /// </summary>
        /// <returns></returns>后
        /// <remarks>返回当前打开的文档名称，如果是新建的文档返回空串</remarks>
        public string GetFileName()
        {
            return emrEditorControl1.GetFileName();
        }

                /// <summary>
        /// 按页输出图片
        /// </summary>
        /// <returns></returns>
        public List<Bitmap> GetPreviewBmp()
        {
            return emrEditorControl1.GetPreviewBmp();
        }

        #endregion

        #region 2.3 打印操作

        /// <summary>
        /// 打印通用函数
        /// </summary>
        /// <param name="nPrintType">0全文打印方式，会弹出一个打印对话框、1选择打印，只打印选中的部分，2区域打印、打印选择的行范围内容。</param>
        /// <returns></returns>
        /// <remarks>病历续打使用打印类型为2的方式进行，事先先选定要打印的行范围。</remarks>
        public bool Print(short nPrintType)
        {
            if (nPrintType == 1)
                emrEditorControl1.SetEnabledSelectionPrint(true);
            else if (nPrintType == 2)
                emrEditorControl1.SetEnabledSelectionAreaPrint(true);
            else if (nPrintType == 4)
                emrEditorControl1.SetEnabledJumpPrint(true);
            bool isPrint = emrEditorControl1.Print(nPrintType);
            emrEditorControl1.SetEnabledSelectionPrint(false);
            emrEditorControl1.SetEnabledSelectionAreaPrint(false);
            emrEditorControl1.SetEnabledJumpPrint(false);
            return isPrint;
        }

        /// <summary>
        /// 设置页面
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool PageSetup()
        {
            return emrEditorControl1.PageSetup();
        }

        /// <summary>
        /// 设置页面序号
        /// </summary>
        /// <remarks>对于有些特殊的病历文档可能需要自定义起始页的页码,同时可设置首页是否显示页眉和页脚。 </remarks>
        public void FileSetPageNumber()
        {
            emrEditorControl1.FileSetPageNumber();
        }

        #endregion

        #region 2.4 编辑操作接口

        /// <summary>
        /// 撤消
        /// </summary>
        /// <remarks></remarks>
        public void EditUnDo()
        {
            emrEditorControl1.EditUnDo();
        }

        /// <summary>
        /// 重新显示撤消的内容
        /// </summary>
        /// <remarks></remarks>
        public void EditReDo()
        {
            emrEditorControl1.EditReDo();
        }

        /// <summary>
        /// 剪切
        /// </summary>
        /// <remarks></remarks>
        public void EditCut()
        {
            emrEditorControl1.EditCut();
        }

        /// <summary>
        /// 设置复制层次号
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool SetCopyLevel()
        {
            return true;// emrEditorControl1.SetCopyLevel();
        }

        /// <summary>
        /// 设置文档默认字体
        /// </summary>
        /// <param name="fontName"></param>
        /// <returns></returns>
        public bool SetDefaultFontName(string fontName)
        {
            return emrEditorControl1.SetDefaultFontName(fontName);
        }

        /// <summary>
        /// 设置文档默认字体大小
        /// </summary>
        /// <param name="size"></param>
        /// <returns></returns>
        public bool SetDefaultFontSize(string size)
        {
            return emrEditorControl1.SetDefaultFontSize(size);
        }
        public bool SetDefaultFont(string fontName, string fontSize)
        {
            return emrEditorControl1.SetDefaultFont(fontName, fontSize);
        }
        /// <summary>
        /// 复制
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool EditCopy()
        {
            return emrEditorControl1.EditCopy();
        }
        /// <summary>
        /// 粘贴
        /// </summary>
        /// <returns></returns>
        /// <remarks>编辑器按照编辑器文件格式的内容优先的原则，如果剪贴板有编辑器文件格式的内容，则粘贴此内容，否则粘贴文本或图像内容。如果剪贴板为空，则返回FALSE。</remarks>
        public bool EditPaste()
        {
            return emrEditorControl1.EditPaste();
        }
        /// <summary>
        /// 粘贴时只粘贴病历文件、图像 5
        /// </summary>
        public bool EditPasteForWrite()
        {
                return emrEditorControl1.EditPasteForWrite();
        }
        /// <summary>
        /// 粘贴外部文本
        /// </summary>
        /// <returns></returns>
        public bool EditPasteExternal()
        {
            //粘贴参数设置
            return emrEditorControl1.EditPasteExternal();
         
            //  return emrEditorControl1.EditPaste();
        }

        /// <summary>
        /// 全选选中中当前文档
        /// </summary>
        /// <remarks></remarks>
        public void EditSelectAll()
        {
            emrEditorControl1.EditSelectAll();
        }

        /// <summary>
        /// 弹出标准的查找界面
        /// </summary>
        /// <remarks></remarks>
        public void EditFind()
        {
            emrEditorControl1.EditFind();
        }

        /// <summary>
        /// 弹出标准的替换界面 ,从光标处向下查找
        /// </summary>
        /// <remarks></remarks>
        public void EditReplace()
        {
            emrEditorControl1.EditReplace();
        }


        /// <summary>
        /// 判断是否可以撤消
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool EditCanUnDo()
        {
            return emrEditorControl1.EditCanUnDo();
        }

        /// <summary>
        /// 判断是否可以重做
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool EditCanReDo()
        {
            return emrEditorControl1.EditCanReDo();
        }

        /// <summary>
        /// 判断是否可以复制
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool EditCanCopy()
        {
            return emrEditorControl1.EditCanCopy();
        }

        /// <summary>
        /// 判断是否可以粘贴
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool EditCanPaste()
        {
            return emrEditorControl1.EditCanPaste();
        }

        /// <summary>
        /// 清空编辑器剪贴板
        /// </summary>
        /// <remarks>对于一些操作后不允许用户粘贴剪贴板中的内容的，则需要执行此功能。 </remarks>
        public void CleanClipboard()
        {
            emrEditorControl1.CleanClipboard();
        }

        /// <summary>
        /// 清空编辑缓冲区
        /// </summary>
        /// <remarks>对于一些操作后不允许用户撤消的，则需要执行此功能。 </remarks>
        public void CleanUndoBuffer()
        {
            emrEditorControl1.CleanUndoBuffer();
        }

        /// <summary>
        /// 删除  相当于按下Delete键
        /// </summary>
        /// <remarks></remarks>
        public void EditDelete()
        {
            emrEditorControl1.EditDelete();
        }

        /// <summary>
        /// 打开关键词字典
        /// </summary>
        /// <remarks></remarks>
        public void EditQueryKeyword()
        {
            //emrEditorControl1.EditQueryKeyword();
        }

        /// <summary>
        /// 自动排版
        /// </summary>
        /// <returns></returns>
        /// <remarks>自动排版功能根据文档设置的段连接标志符进行操作，范围为当前是可编辑的行</remarks>
        //public bool EditAutoRange()
        //{
        //    return emrEditorControl1.EditAutoRange();
        //}

        /// <summary>
        /// 清除未使用的元素
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool EditClearUnuseField()
        {
            return emrEditorControl1.EditClearUnuseField();
        }

        /// <summary>
        /// 清除元素的语法
        /// </summary>
        /// <returns></returns>
        /// <remarks>清除的对象是，没有处理的关键词元素和未处理的注释元素。 </remarks>
        //public bool EditClearSymtax()
        //{
        //    //return emrEditorControl1.EditClearSymtax();
        //}

        /// <summary>
        /// 设置行只读
        /// </summary>
        /// <returns></returns>
        /// <remarks>把当前选中的行设置为只读状态，如果文档已经是只读状态，此功能失效。</remarks>
        public bool EditLineReadOnly()
        {
            return emrEditorControl1.EditLineReadOnly();
        }

        /// <summary>
        /// 设置行可编辑
        /// </summary>
        /// <returns></returns>
        /// <remarks>把当前选中的行设置为可编辑状态，如果文档已经是编辑状态，此功能失效。 </remarks>
        public bool EditLineEditMode()
        {
            return emrEditorControl1.EditLineEditMode();
        }

        /// <summary>
        /// 编辑图像
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool EditImage()
        {
            return emrEditorControl1.EditImage();
        }


        #endregion

        #region 2.5 工具条操作

        /// <summary>
        /// 显示或隐藏工具条
        /// </summary>
        /// <remarks></remarks>
        public void ViewToolbar()
        {
            //emrEditorControl1.ViewToolbar();
            toolStripbar.Visible = !toolStripbar.Visible;
        }
        /// <summary>
        /// 增加函数控制工具条是否显示
        /// </summary>
        public void ue_ViewToolbar()
        {
            //this.ViewToolbar();
            //if (UniversalBoolFunction("", "", 4, 0))//判断当前工具条状态
            //{
            //    this.ViewToolbar();
            //    colorComboBox1.Visible = false;
            //    btncha.Visible = false;
            //}
            //else
            //{
            //    colorComboBox1.Visible = false;
            //    btncha.Visible = false;
            //}
            toolStripbar.Visible = !toolStripbar.Visible;
        }

        /// <summary>
        /// 显示或隐藏状态条
        /// </summary>
        /// <remarks></remarks>
        public void ViewStatusbar()
        {
            // emrEditorControl1.ViewStatusbar();
            toolStripbar.Visible = !toolStripbar.Visible;
        }

        /// <summary>
        /// 显示或隐藏行号
        /// </summary>
        /// <remarks></remarks>
        public void ViewLineIndex()
        {
            emrEditorControl1.ShowLineNumber();
        }
        #endregion

        #region 2.6 格式函数
        /// <summary>
        ///设置黑体
        /// </summary>
        public void FontBold() { emrEditorControl1.FontBold(); }
        /// <summary>
        ///设置斜体
        /// </summary>
        public void FontItalic() { emrEditorControl1.FontItalic(); }
        /// <summary>
        ///设置下划线
        /// </summary>
        public void FontUnderline() { emrEditorControl1.FontUnderline(); }
        /// <summary>
        ///设置上角标///
        ///</summary>
        public void FontSubscript() { emrEditorControl1.FontSubscript(); }
        /// <summary>
        ///设置下脚标///
        ///</summary>
        public void FontSuperscript() { emrEditorControl1.FontSuperscript(); }
        /// <summary>
        ///设置单倍行距/// 
        ///</summary>
        public bool LineSpaceSingle()
        {
            return emrEditorControl1.LineSpaceSingle();
        }

        /// <summary>
        /// 
        ///</summary>
        public bool LineAlignJustify()
        {
            return emrEditorControl1.LineAlignJustify();
        }

        public bool LineAlignDisperse()
        {
            return emrEditorControl1.LineAlignDisperse();
        }

        public void FontBackColor()
        {
             emrEditorControl1.FontBackColor();
        }

        public void FontForeColor()
        {
             emrEditorControl1.FontForeColor();
        }
        /// <summary>
        /// 设置字体
        /// </summary>
        /// <param name="fontName"></param>
        public void SelectionFontName(string fontName)
        {
            emrEditorControl1.SelectionFontName(fontName);
        }
        /// <summary>
        /// 设置字号
        /// </summary>
        /// <param name="fontSize"></param>
        public void SelectionFontSize(string fontSize)
        {
            emrEditorControl1.SelectionFontSize(fontSize);
        }
        /// <summary>
        ///设置1.5倍行距///
        ///</summary>
        public bool LineSpaceOnehalf()
        {
            return emrEditorControl1.LineSpaceOnehalf();
        }
        /// <summary>
        ///设置左对齐/// 
        ///</summary>
        public void LineAlignLeft() { emrEditorControl1.LineAlignLeft(); }
        /// <summary>
        ///设置右对齐///
        ///</summary>
        public void LineAlignRight() { emrEditorControl1.LineAlignRight(); }
        ///设置中对齐///
        ///</summary>
        public void LineAlignCenter() { emrEditorControl1.LineAlignCenter(); }
        /// <summary>
        ///设置段起始连接符/// 
        ///</summary>
        public void ParagraphBegin() { emrEditorControl1.ParagraphBegin(); }
        /// <summary>
        ///设置段结束连接符///
        ///</summary>
        public void ParagraphEnd() { emrEditorControl1.ParagraphEnd(); }
        /// <summary>
        ///段落的格式设置/// 
        ///</summary>
        public void ParagraphFormat() { emrEditorControl1.ParagraphFormat(); }
        #endregion

        #region 2.7表格函数

        /// <summary>
        ///表格插入对话框
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public void TableInsert()
        {
            emrEditorControl1.InsertTable();// TableInsert();
        }
        /// <summary>
        ///在列的左边插入新列
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool TableInsertColLeft()
        {
            return emrEditorControl1.TableInsertColumn(true); //TableInsertColLeft();
        }
        /// <summary>
        ///在列的右边插入新列
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool TableInsertColRight()
        {
            return emrEditorControl1.TableInsertColumn(false); //TableInsertColRight();
        }
        /// <summary>
        ///在行上边插入新行
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool TableInsertRowTop()
        {
            return emrEditorControl1.TableInsertRow(true);// TableInsertRowTop();
        }
        /// <summary>
        ///在行下边插入新行
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool TableInsertRowBottom()
        {
            return emrEditorControl1.TableInsertRow(false);// TableInsertRowBottom();
        }
        /// <summary>
        ///删除表格
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public void TableDelete()
        {
            emrEditorControl1.DeleteTable();// TableDelete();
        }
        /// <summary>
        ///删除表格选中的列
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool TableDeleteCol()
        {
            return emrEditorControl1.TableDeleteSelectedColumn();// TableDeleteCol();
        }
        /// <summary>
        ///删除表格选中的行
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool TableDeleteRow()
        {
            return emrEditorControl1.TableDeleteSelectedRow();// TableDeleteRow();
        }
        /// <summary>
        /// 删除空白行
        /// </summary>
        /// <returns></returns>
        public bool TableDeleteBlankRow()
        {
            return emrEditorControl1.TableDeleteBlankRow();
        }
        /// <summary>
        /// 删除当前行
        /// </summary>
        /// <returns></returns>
        /// <summary>
        public void DeleteCurrentParagraph()
        {
             emrEditorControl1.DeleteCurrentParagraph();
        }
        /// <summary>
        /// 删除选中行
        /// </summary>
        /// <returns></returns>
        /// <summary>
        public void DeleteSelectedParagraph()
        {
            emrEditorControl1.ForceDeleteSelectedParagraph();
        }
        ///选中当前的表格
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool TableSelect()
        {
            return emrEditorControl1.TableSelect();
        }
        /// <summary>
        ///选中当前的表格列
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool TableSelectCol()
        {
            return emrEditorControl1.TableSelectCurrentColumn();//.TableSelectCol();
        }
        /// <summary>
        ///选中当前的表格行
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool TableSelectRow()
        {
            return emrEditorControl1.TableSelectCurrentRow();// TableSelectRow();
        }
        /// <summary>
        ///合并单元格
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool TableMergeCell()
        {
            return emrEditorControl1.MergeTableCell();// TableMergeCell();
        }
        /// <summary>
        ///拆分单元格
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public bool TableSplitCell()
        {
            return emrEditorControl1.SplitTableCell();// TableSplitCell();
        }
        /// <summary>
        ///显示表格属性对话框
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public void TableProp()
        {
            emrEditorControl1.TableProperty();// TableProp();
        }
        /// <summary>
        ///显示单元格属性对话框
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public void TableCellProp()
        {
             emrEditorControl1.TableCellProperty();// TableCellProp();
        }
        /// <summary>
        /// 是否允许当前行进行换行
        /// </summary>
        /// <param name="fix">true 允许换行 false 为不允许换行</param>
        public void TableCurrentRowFixHeight(bool fix)
        {
            emrEditorControl1.TableCurrentRowFixHeight(fix);//.TableCellFixHeight(true);
        }
        /// <summary>
        /// 设置当前单元格是否允许换行
        /// </summary>
        /// <param name="fix">true 允许换行 false 为不允许换行</param>
        public void TableCurrentCellFixHeight(bool fix)
        {
            emrEditorControl1.TableCurrentCellFixHeight(fix);//.TableCellFixHeight(true);
        }
        /// <summary>
        /// 设置表格行是否允许换行
        /// </summary>
        /// <param name="fix">true 允许换行  false 不允许换行</param>
        public void SetTableFixHeight(bool fix)
        {
            emrEditorControl1.SetTableFixHeight(fix);
        }
        /// <summary>
        ///设置表格单元格只读属性
        /// </summary>
        /// <param name="readOnly"></param>
        public void TableCellReadOnly(bool readOnly)
        {
            emrEditorControl1.SetTableCellReadOnly(readOnly);
        }
        /// <summary>
        ///显示表格列属性对话框
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public void TableColProp()
        {
             emrEditorControl1.TableColumnProperty();// TableColProp();
        }
        /// <summary>
        ///显示表格的隐含边框
        /// </summary>
        /// <returns></returns>
        /// <remarks>对于没有边框的表格，可显示灰色的边框，打印时不打印出边框。 </remarks>
        public void TableHideBorder(bool fix)
        {
            emrEditorControl1.SetTableBorderVisible(fix);// TableHideBorder();
        }

        #endregion

        #region 2.8 文档状态函数

        /// <summary>
        /// 设置文档的状态
        /// </summary>
        /// <param name="nMode">0为设计模式，1编辑状态，2只读状态，3清洁显示状态。</param>
        /// <returns></returns>
        /// <remarks>清洁显示状态是一种全文只读状态，它删除了文档中经修订删除掉的文字，同时重新排
        ///版，不显示修改痕迹的线条。三种状态通过调用此函数就可以相互切换，清洁打印不再需要
        ///单独开辟一个窗口处理了</remarks>
        public bool SetDocumentMode(short nMode)
        {
            return emrEditorControl1.SetDocumentMode(nMode);
        }

        public void SetDocumentModifyStatus(bool isModify)
        {
            emrEditorControl1.SetDocumentModifyStatus(isModify);
        }

        /// <summary>
        /// 返回文档的状态 ,只读状态返回2，清洁显示状态返回3，其它状态返回1。
        /// </summary>
        /// <returns></returns>
        /// <remarks>如果是只读状态返回2，清洁显示状态返回3，其它状态返回1。 </remarks>
        public long GetDocumentMode()
        {
            return emrEditorControl1.GetDocumentMode();
        }

        /// <summary>
        /// 显示文档的修订记录
        /// </summary>
        /// <remarks></remarks>
        public void ShowRevisionHistory()
        {
            emrEditorControl1.ShowRevisionHistory(true);
        }

        #endregion

        #region 2.9编程函数

         /// <summary>
        /// 设置当前单选多选元素是否启用单击选择模式
        /// </summary>
        /// <param name="value"></param>
        //public void SetSingleClickSelectedMode(bool value) 
        //{
        //   // emrEditorControl1.SetSingleClickSelectedMode(value);
        //}

        /// <summary>
        ///插入分页符
        /// </summary>
        /// <remarks></remarks>
        public void InsertPageBreaker()
        {
            emrEditorControl1.InsertPageBreaker();
        }
        /// <summary>
        /// 强制编辑器重画
        /// </summary>
        /// <param name="bRedraw">用于设置是否重画，目前始终默认为重画，忽略TRUE和FALSE。</param>

        public void SetPadRedraw(bool bRedraw)
        {
            emrEditorControl1.SetPadRedraw(bRedraw);
        }
        /// <summary>
        /// 插入新空白行
        /// </summary>
        /// <param name="bAferCurLine">用于设置新行与当前行的相对位置，TRUE在当前行的下面，FALSE在当前行的上面</param>
        /// <returns></returns>
        public void InsertLine(bool bAferCurLine)
        {
            emrEditorControl1.InsertLine(bAferCurLine);
        }
        /// <summary>
        /// 删除指定范围的行
        /// </summary>
        /// <param name="nStartLine">用于设置删除行的起始位置。 </param>
        /// <param name="nEndLine">用于设置删除行的结束位置。</param>
        /// <returns></returns>
        public bool DeleteLines(int nStartLine, int nEndLine)
        {
            return emrEditorControl1.DeleteLines(nStartLine, nEndLine);
        }
        /// <summary>
        /// 返回当前的光标位置
        /// </summary>
        /// <param name="nBaseLineIndex">保存返回的基行号。第一个基行的序号是0,如果返回-1,则说明此函数返回的所有值都无效</param>
        /// <param name="nCellIndex">保存返回的单元需要,如果当前光标不在表格内,则返回-1,表格第一个单元格的序号是0。</param>
        /// <param name="nLineIndex">保存返回的当前行的行号从0开始</param>
        /// <param name="nFieldElemIndex">保存返回的当前元素序号从0开始。</param>
        /// <param name="nCharPos">保存返回的当前字符位置从0开始。 </param>
        /// <param name="bCursorBegin">如果当前为选择状态，则发生作用，TRUE则返回选择区域的起始位置，FALSE则返回选择区域的结束位置，如果不是选择状态，TRUE和FALSE都返回当前的光标位置。</param>
        /// <returns></returns>
        ///  <remarks>获取当前的光标位置，如果取选择区域的位置，则要调用此函数两次，赋予参数bCursorBegin不同的值。 </remarks>
        public bool GetCurCursorPos(ref int nBaseLineIndex, ref int nCellIndex, ref int nLineIndex, ref int nFieldElemIndex, ref int nCharPos, bool bCursorBegin)
        {
            return emrEditorControl1.GetCurCursorPos(ref nBaseLineIndex, ref nCellIndex, ref nLineIndex, ref nFieldElemIndex, ref nCharPos, bCursorBegin);
        }
        /// <summary>
        /// 设置选择范围
        /// </summary>
        /// <param name="BaseLineIndex1">起始基行号。第一个基行的序号是0,如果为-1,则说明起始位置为文档末尾，系统将自动计算出位置，忽略 CellIndex1，LineIndex1，FieldElemIndex1，CharPos1 参数。 </param>
        /// <param name="CellIndex1"> 起始单元号,如果不在表格内,则赋值-1,否则需要赋值单元格的序号。</param>
        /// <param name="LineIndex1">起始行号。</param>
        /// <param name="FieldElemIndex1">起始元素号。</param>
        /// <param name="CharPos1">起始字符位置。 </param>
        /// <param name="BaseLineIndex2">结束基行号。第一个基行的序号是0,如果为-1,则说明结束位置为文档末尾，系统将自动计算出位置，忽略 CellIndex2，LineIndex2，FieldElemIndex2，CharPos2 参数。 </param>
        /// <param name="CellIndex2">结束单元号,如果不在表格内,则赋值-1,否则需要赋值单元格的序号。 </param>
        /// <param name="LineIndex2">结束行号。</param>
        /// <param name="FieldElemIndex2">结束元素号。 </param>
        /// <param name="CharPos2">结束字符位置。</param>
        /// <returns></returns>
        public bool SetSel(int BaseLineIndex1, int CellIndex1, int LineIndex1, int FieldElemIndex1, int CharPos1, int BaseLineIndex2, int CellIndex2, int LineIndex2, int FieldElemIndex2, int CharPos2)
        {
            return emrEditorControl1.SetSel(BaseLineIndex1, CellIndex1, LineIndex1, FieldElemIndex1, CharPos1, BaseLineIndex2, CellIndex2, LineIndex2, FieldElemIndex2, CharPos2);
        }
        /// <summary>
        ///返回基行的行数
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public int GetBaseLineCount()
        {
            return emrEditorControl1.GetBaseLineCount();
        }
        /// <summary>
        ///返回当前行容器的行数
        /// </summary>
        /// <returns></returns>
        /// <remarks>如果当前行是在表格单元中，则返回此表格单元的行数，如果是在基行中，则返回文档的行数，这时的功能和 GetBaseLineCount相同。 </remarks>
        public int GetLineCount()
        {
            return emrEditorControl1.GetLineCount();
        }
        /// <summary>
        ///返回当前元素的字符数
        /// </summary>
        /// <returns></returns>
        /// <remarks></remarks>
        public int GetCurFieldCharCount()
        {
            return emrEditorControl1.GetCurFieldCharCount();
        }
        /// <summary>
        /// 插入新域
        /// </summary>
        /// <param name="strName">用于设置元素的名称，以后内容取值和XML输出需要使用到</param>
        /// <param name="strContent">元素的内容</param>
        /// <param name="nLayerNo">元素的层次号，指定在XML输出时的层次，序号从1开始，1为最顶层</param>
        /// <param name="nFieldType">元素的类型。0 普通文字、1 宏、2 固定文本标题、3 标题定位符</param>
        /// <param name="nPosition">元素的插入位置,如果>0则插入在当前元素的后面， 否则插入到当前元素的前面</param>
        /// <returns></returns>
        public bool InsertField(string strName, string strContent, short nLayerNo, short nFieldType, short nPosition)
        {
            return emrEditorControl1.InsertField(strName, strContent, nLayerNo, nFieldType, nPosition);
        }
        /// <summary>
        /// 返回指定元素的文本内容
        /// </summary>
        /// <param name="nBaseLineIndex">元素所在基行的行号</param>
        /// <param name="nCellIndex">元素所在表格单元的序号，如果不在表格单元中，此参数应设置为-1</param>
        /// <param name="nLineIndex">所在行的行号，此行号相对与所在的容器</param>
        /// <param name="nFieldIndex">元素的序号</param>
        /// <param name="bSelected">TRUE返回此元素选中部分的文本，FALSE返回此元素的所有文本</param>
        /// <returns></returns>
        /// <remarks>如果 nBaseLineIndex、 nCellIndex、 nLineIndex、 nFieldIndex 均为-1， 则返回当前元素的内容</remarks>
        public string GetFieldText(int nBaseLineIndex, int nCellIndex, int nLineIndex, int nFieldIndex, bool bSelected)
        {
            return emrEditorControl1.GetFieldText(nBaseLineIndex, nCellIndex, nLineIndex, nFieldIndex, bSelected);
        }

        /// <summary>
        /// 更新文档中和页眉中的元素属性值
        /// </summary>
        /// <param name="attrTypeQuery">属性类别.0:元素类型,1：ID，2：Code，3：Name，4：SysCode，5：CustomCode，6：Text,7:checkstate,8:checkvalue,9:CaculateResultElementID,10:复选框组值,逗号分隔</param>
        /// <param name="attrTypeUpdate">属性类别.0:元素类型,1：ID，2：Code，3：Name，4：SysCode，5：CustomCode，6：Text,7:checkstate,8:checkvalue,9:CaculateResultElementID,10:复选框组值,逗号分隔</param>
        /// <param name="queryAttrValue">查找的属性值</param>
        /// <param name="updateAttrValue">要更新的内容值</param>
        /// <param name="setFieldReadOnly">更新元素之后是否设置元素为只读</param>
        /// <returns>true替换成功，false替换失败，参数ht含有null的值</returns>
        public bool SetFieldAttributeValue(int attrTypeQuery, int attrTypeUpdate, string queryAttrValue, string updateAttrValue, bool setFieldReadOnly)
        {
            return emrEditorControl1.SetFieldAttributeValue(attrTypeQuery, attrTypeUpdate, queryAttrValue, updateAttrValue, setFieldReadOnly);

        }
        /// <summary>
        /// 设置指定元素的文本内容
        /// </summary>
        /// <param name="nBaseLineIndex">元素所在基行的行号</param>
        /// <param name="nCellIndex">元素所在表格单元的序号，如果不在表格单元中，此参数应设置为-1</param>
        /// <param name="nLineIndex">所在行的行号，此行号相对与所在的容器</param>
        /// <param name="nFieldIndex"> 元素的序号</param>
        /// <param name="strText">需要设置的内容</param>
        /// <returns></returns>
        ///<remarks>如果 nBaseLineIndex、 nCellIndex、 nLineIndex、 nFieldIndex 均为-1， 则返回当前元素的内容</remarks>
        public bool SetFieldText(int nBaseLineIndex, int nCellIndex, int nLineIndex, int nFieldIndex, string strText)
        {
            return emrEditorControl1.SetFieldText(nBaseLineIndex, nCellIndex, nLineIndex, nFieldIndex, strText);
        }
        public void SetFieldText(string fieldName, string value, int beginLine, int endLine)
        { emrEditorControl1.SetFieldsTextByName(fieldName, value, beginLine, endLine); }
        /// <summary>
        /// 查找元素
        /// </summary>
        /// <param name="strFindText">元素名称. 扩充如果元素名称后用％结尾,如“姓%” ，则查找以“姓”为前缀的元素</param>
        /// <param name="nLayerNo">元素的层次好，如果为-1，则查找所有层次的元素</param>
        /// <param name="nFindType">元素类型，1元素、2表格、3为表格或元素。 如果需要按定位符属性查找，则在上述值上+9,例如：查找元素有定位符属性的，则赋值为10，查找表格有定位符属性的，则赋值为11。</param>
        /// <param name="bFromBegin">TRUE从起始位置开始查找，FALSE从当前位置开始查找</param>
        /// <returns></returns>
        public bool FindField(string strFindText, short nLayerNo, short nFindType, bool bFromBegin)
        {
            return emrEditorControl1.FindField(strFindText, nLayerNo, nFindType, bFromBegin);
        }
        /// <summary>
        /// 查找元素
        /// </summary>
        /// <param name="strFindText">元素名称. 扩充如果元素名称后用％结尾,如“姓%” ，则查找以“姓”为前缀的元素</param>
        /// <param name="nLayerNo">元素的层次好，如果为-1，则查找所有层次的元素</param>
        /// <param name="nFindType">元素类型，1元素、2表格、3为表格或元素。 如果需要按定位符属性查找，则在上述值上+9,例如：查找元素有定位符属性的，则赋值为10，查找表格有定位符属性的，则赋值为11。</param>
        /// <param name="bFromBegin">TRUE从起始位置开始查找，FALSE从当前位置开始查找</param>
        /// <returns></returns>
        public bool FindFieldByCode(string strFindText, short nLayerNo, short nFindType, bool bFromBegin)
        {
            return emrEditorControl1.FindFieldByCode(strFindText, nLayerNo, nFindType, bFromBegin);
        }


        #region 元素操作
        /// <summary>
        /// 查找元素
        /// </summary>
        /// <param name="attrType">属性类别.1：ID，2：Code，3：Name，4：SysCode，5：CustomCode，6：Text</param>
        /// <param name="findText">查找的元素属性包含的内容</param>
        /// <returns>查找到的元素索引，如果是-1则未找到，如果是>-1则找到</returns>
        public bool FindField(int attrType, string findText)
        {
            return emrEditorControl1.FindField(attrType, findText) > -1;
        }
             /// <summary>
        /// 查找元素
        /// </summary>
        /// <param name="attrType">属性类别.1：ID，2：Code，3：Name，4：SysCode，5：CustomCode，6：Text</param>
        /// <param name="findText">查找的元素属性包含的内容</param>
        /// <param name="findType">查找类型，0：元素，1：表格</param>
        /// <returns>查找到的元素索引，如果是-1则未找到，如果是>-1则找到</returns>
        public bool FindField(int attrType, string findText, int findType)
        {
            return emrEditorControl1.FindField(attrType, findText,findType) > -1;
        }
        /// <summary>
        /// 查找元素
        /// </summary>
        /// <param name="attrType">属性类别.1：ID，2：Code，3：Name，4：SysCode，5：CustomCode，6：Text</param>
        /// <param name="findText">查找的元素属性包含的内容</param>
        /// <param name="setCursor">是否移动光标</param>
        /// <param name="fromBegin">是否从文档开头查找 true 为文档开头进行查找，false 为光标当前位置进行查找</param>
        /// <returns>查找到的元素索引，如果是-1则未找到，如果是>-1则找到</returns>
        public bool FindField(int attrType, string findText, bool setCursor, bool fromBegin)
        {
            return emrEditorControl1.FindField(attrType, findText, setCursor, fromBegin) > -1;
        }
        /// <summary>
        /// 查找元素
        /// </summary>
        /// <param name="attrType">属性类别.1：ID，2：Code，3：Name，4：SysCode，5：CustomCode，6：Text</param>
        /// <param name="findText">查找的元素属性包含的内容</param>
        /// <param name="setCursor">是否移动光标 true 为移动 false 为不移动 </param>
        /// <returns>查找到的元素索引，如果是-1则未找到，如果是>-1则找到</returns>
        public bool FindField(int attrType, string findText, bool setCursor)
        {
            return emrEditorControl1.FindField(attrType, findText, setCursor)>-1;
        }
        /// <summary>
        /// 查找元素
        /// </summary>
        /// <param name="attrType">属性类别.1：ID，2：Code，3：Name，4：SysCode，5：CustomCode，6：Text</param>
        /// <param name="findText">查找的元素属性包含的内容</param>
        /// <param name="findType">查找类型，0：元素，1：表格</param>
        /// <param name="setCursor">是否移动光标</param>
        /// <returns>查找到的元素索引，如果是-1则未找到，如果是>-1则找到</returns>
        public bool FindField(int attrType, string findText, int findType, bool setCursor)
        {
            return emrEditorControl1.FindField(attrType, findText, findType, setCursor)>-1;
        }
        /// <summary>
        /// 查找元素
        /// </summary>
        /// <param name="attrType">属性类别.1：ID，2：Code，3：Name，4：SysCode，5：CustomCode，6：Text</param>
        /// <param name="findText">查找的元素属性包含的内容</param>
        /// <param name="findType">查找类型，0：元素，1：表格</param>
        /// <param name="setCursor">是否移动光标</param>
        /// <param name="startIndex">查找范围的开始位置</param>
        /// <param name="endIndex">查找范围的结束位置</param>
        /// <returns>查找到的元素索引，如果是-1则未找到，如果是>-1则找到</returns>
        public bool FindField(int attrType, string findText, int findType, bool setCursor, int startIndex, int endIndex)
        {
            return emrEditorControl1.FindField(attrType, findText, findType, setCursor, startIndex, endIndex) > -1;
        }
         /// <summary>
        /// 返回当前选中元素
        /// </summary>
        /// <returns></returns>
        public object GetCurrentField()
        {
            return emrEditorControl1.GetCurrentField();
        }
        /// <summary>
        /// 删除当前选中元素
        /// </summary>
        public void DeleteField()
        {
            emrEditorControl1.DeleteField();
        }
        /// <summary>
        /// 获取选中元素指定属性的值
        /// </summary>
        /// <param name="attrType">属性类别.1：ID，2：Code，3：Name，4：SysCode，5：CustomCode，6：Text,7:checkstate,8:checkvalue</param>
        /// <returns></returns>
        public string GetFieldAttributeValue(int attrType)
        {
            return emrEditorControl1.GetFieldAttributeValue(attrType);
        }
        /// <summary>
        /// 设置元素指定属性的值
        /// </summary>
        /// <param name="attrType">属性类别.1：ID，2：Code，3：Name，4：SysCode，5：CustomCode，6：Text,7:checkstate</param>
        /// <param name="attrName">值</param>
        public void SetFieldAttributeValue(int attrType, string attrName)
        {
            emrEditorControl1.SetFieldAttributeValue(attrType,attrName);
        }
        public void SetFieldAttributeValue(int attrType, int attrTypeUpdate, Hashtable ht, bool setFieldReadOnly)
        {
            emrEditorControl1.SetFieldAttributeValue(attrType,attrTypeUpdate,ht,setFieldReadOnly);
        }

        /// <summary>
        /// 替换指定元素名称的元素值
        /// </summary>
        /// <param name="fieldName"></param>
        /// <param name="value"></param>
        /// <param name="beginLine">起始行</param>
        /// <param name="endLine">结束行</param>
        public void SetFieldsTextByName(string fieldName, string value, int beginLine, int endLine)
        {
            emrEditorControl1.SetFieldsTextByName(fieldName,value,beginLine,endLine);
        }

        /// <summary>
        /// 更新文档中的元素属性值
        /// </summary>
        /// <param name="attrTypeQuery">属性类别.0:元素类型,1：ID，2：Code，3：Name，4：SysCode，5：CustomCode，6：Text,7:checkstate,8:checkvalue,9:CaculateResultElementID,10:复选框组值,逗号分隔</param>
        /// <param name="attrTypeUpdate">属性类别.0:元素类型,1：ID，2：Code，3：Name，4：SysCode，5：CustomCode，6：Text,7:checkstate,8:checkvalue,9:CaculateResultElementID,10:复选框组值,逗号分隔</param>
        /// <param name="ht">属性键值对</param>
        /// <param name="setFieldReadOnly">更新元素之后是否设置元素为只读</param>
        /// <param name="startIndex">替换元素的起始位置</param>
        /// <param name="endIndex">替换元素的结束位置</param>
        /// <returns>true替换成功，false替换失败，参数ht含有null的值</returns>
        public void SetFieldAttributeValue(int attrType, int attrTypeUpdate, Hashtable ht, bool setFieldReadOnly, int startIndex, int endIndex)
        {
            emrEditorControl1.SetFieldAttributeValue(attrType, attrTypeUpdate, ht,setFieldReadOnly,startIndex,endIndex);
        }
        /// <summary>
        /// 更新文档中的元素属性值
        /// </summary>
        /// <param name="attrTypeQuery">属性类别.0:元素类型,1：ID，2：Code，3：Name，4：SysCode，5：CustomCode，6：Text,7:checkstate,8:checkvalue,9:CaculateResultElementID,10:复选框组值,逗号分隔</param>
        /// <param name="attrTypeUpdate">属性类别.0:元素类型,1：ID，2：Code，3：Name，4：SysCode，5：CustomCode，6：Text,7:checkstate,8:checkvalue,9:CaculateResultElementID,10:复选框组值,逗号分隔</param>
        /// <param name="ht">属性键值对</param>
        /// <param name="setFieldReadOnly">更新元素之后是否设置元素为只读</param>
        /// <param name="startIndex">替换元素的起始位置</param>
        /// <param name="endIndex">替换元素的结束位置</param>
        /// <returns>true替换成功，false替换失败，参数ht含有null的值</returns>
        public void SetFieldAttributeValue(int attrType, int attrTypeUpdate, string strCode, string strValue, bool setFieldReadOnly, int startIndex, int endIndex)
        {
            Hashtable ht = new Hashtable();
            ht.Add(strCode, strValue);
            emrEditorControl1.SetFieldAttributeValue(attrType, attrTypeUpdate, ht, setFieldReadOnly, startIndex, endIndex);
        }

        public void SetFieldsTextByCode(string strCode, string strValue, int startIndex, int endIndex)
        {
            emrEditorControl1.SetFieldsTextByCode(strCode, strValue, startIndex, endIndex);
        }
        /// <summary>
        /// 根据元素名称设置元素的值
        /// </summary>
        /// <param name="code"></param>
        /// <param name="includHead"></param>
        /// <returns></returns>
        public void UpdateFieldText(string name, string value, bool onlyCanEdit)
        {
            emrEditorControl1.UpdateFieldText(name,value,onlyCanEdit);
        }
        /// <summary>
        /// 根据元素编码设置元素的值
        /// </summary>
        /// <param name="code"> 元素编码</param>
        /// <param name="includHead"></param>
        /// <returns></returns>
        public void UpdateFieldTextByCode(string code, string value, bool onlyCanEdit)
        {
            emrEditorControl1.UpdateFieldTextByCode(code, value, onlyCanEdit);
        }
        /// <summary>
        /// 删除指定ID的元素
        /// </summary>
        /// <param name="fieldName"></param>
        /// <param name="beginLine"></param>
        /// <param name="endLine"></param>
        public void DeleteFieldByName(string fieldName, int beginLine, int endLine)
        {
            emrEditorControl1.DeleteFieldByName(fieldName, beginLine, endLine);
        }
#endregion 
         /// <summary>
        /// 范围内查找元素
        /// </summary>
        public bool FindFieldForCourse(string fieldName, string layerNo, string fieldType, string beginLine, string endLine)
        {
            return emrEditorControl1.FindFieldForCourse(fieldName, layerNo, fieldType, beginLine, endLine);
        }
        /// <summary>
        /// 设置宏
        /// </summary>
        /// <param name="strName">宏名称</param>
        /// <param name="strValue">宏的值</param>
        /// <returns></returns>
        public bool SetMicroField(string strName, string strValue)
        {
            return emrEditorControl1.SetMicroField(strName, strValue);
        }
        /// <summary>
        /// 替换宏
        /// </summary>
        /// <param name="bOnlyCanEdit">TRUE只处理可编辑状态下的宏，及宏所在的行不是只读状态。FALSE处理文档所有的宏。 </param>
        /// <returns></returns>
        public bool UpdateMicroField(bool bOnlyCanEdit)
        {
            return emrEditorControl1.UpdateMicroField(bOnlyCanEdit);
        }
        /// <summary>
        /// 替换元素内容
        /// </summary>
        /// <param name="nBaseLineIndex">元素所在基行的行号</param>
        /// <param name="nCellIndex">元素所在表格单元的序号，如果不在表格单元中，此参数应设置为-1。</param>
        /// <param name="nLineIndex">所在行的行号，此行号相对与所在的容器</param>
        /// <param name="nFieldIndex">元素的序号</param>
        /// <param name="strOld">被替换的内容</param>
        /// <param name="strNew">替换的内容</param>
        /// <returns></returns>
        /// <remarks>如果 nBaseLineIndex、nCellIndex、nLineIndex、nFieldIndex均为-1，则操作的对象是当前元素</remarks>
        public bool ReplaceFieldText(int nBaseLineIndex, int nCellIndex, int nLineIndex, int nFieldIndex, string strOld, string strNew)
        {
            return emrEditorControl1.ReplaceFieldText(nBaseLineIndex, nCellIndex, nLineIndex, nFieldIndex, strOld, strNew);
        }
        /// <summary>
        /// 设置修订版本
        /// </summary>
        /// <param name="nRevisialID">修订号，-1,则修订号由系统自动生成,>0，则为手工设置修订号,如果此号已经存在,则忽略这个设置,将返回-1。 </param>
        /// <param name="nAuthorization">权限等级，0为普通权限，这里的数值需>=0，等级的值决定了修改和删除的痕迹线条的数目</param>
        /// <param name="strUserName">当前操作的用户名</param>
        /// <param name="strUserCode">当前操作的用户代码</param>
        /// <param name="strExtent">需要登记的扩展信息。当nRevisialID>0 时,内容格式为“修订时间;终端名</param>
        /// <returns></returns>
        /// <remarks>本函数用于设置修订的痕迹，系统默认取出文档的最后一次修订信息，因此如果需要改变修订，需要在用户能操作前就设定，同一次修订痕迹的信息相同。</remarks>
        public int SetRevisalInfo(int nRevisialID, short nAuthorization, string strUserName, string strUserCode, string strExtent)
        {
            return emrEditorControl1.SetRevisalInfo(nRevisialID, nAuthorization, strUserName, strUserCode, strExtent);
        }

        /// <summary>
        /// 设置修订版本
        /// </summary>
        /// <param name="nRevisialID">修订号，-1,则修订号由系统自动生成,>0，则为手工设置修订号,如果此号已经存在,则忽略这个设置,将返回-1。 </param>
        /// <param name="nAuthorization">权限等级，0为普通权限，这里的数值需>=0，等级的值决定了修改和删除的痕迹线条的数目</param>
        /// <param name="strUserName">当前操作的用户名</param>
        /// <param name="strUserCode">当前操作的用户代码</param>
        /// <param name="strExtent">需要登记的扩展信息。当nRevisialID>0 时,内容格式为“修订时间;终端名</param>
        /// <returns></returns>
        /// <remarks>本函数用于设置修订的痕迹，系统默认取出文档的最后一次修订信息，因此如果需要改变修订，需要在用户能操作前就设定，同一次修订痕迹的信息相同。</remarks>
        public void ChangeUserLevel(int nAuthorization, string strUserName, string strUserCode)
        {
             emrEditorControl1.ChangeUserLevel( nAuthorization, strUserName, strUserCode);
        }
        /// <summary>
        /// 设置基行选中范围
        /// </summary>
        /// <param name="nStartIndex">开始行号，从0开始。</param>
        /// <param name="nEndIndex">结束行号，最大为基行总数-1。</param>
        /// <returns></returns>
        public bool SelectBaseLineRange(int nStartIndex, int nEndIndex)
        {
            return emrEditorControl1.SelectBaseLineRange(nStartIndex, nEndIndex);
        }
        public void ParagraphReadOnly(bool ReadOnly)
        {
            emrEditorControl1.ParagraphReadOnly(ReadOnly);
        }
        /// <summary>
        /// 获取对象的数目
        /// </summary>
        /// <param name="nBaseLineIndex">基行的行号</param>
        /// <param name="nCellIndex">表格单元的序号，如果不是表格单元，则设置为-1。</param>
        /// <param name="nLineIndex">行号</param>
        /// <param name="nType">统计的类别。0 指定行的元素个数、1 如果当前行是表格则返回表格的单元数，否则
        ///返回-1、2 如果当前行是表格则返回表格的行数，否则返回-1、3 如果当前行是表格则返回表格
        ///的列数，否则返回-1、4如果当前行是表格则返回表格的单元包含的行数，否则返回-1 </param>
        /// <returns>成功返回数量>=0，失败返回-1。 </returns>
        public int GetObjectCount(int nBaseLineIndex, int nCellIndex, int nLineIndex, int nType)
        {
            return emrEditorControl1.GetObjectCount(nBaseLineIndex, nCellIndex, nLineIndex, nType);
        }
        /// <summary>
        /// 获取光标位置索引
        /// </summary>
        /// <param name="nType">[in] 索引的类别。0 指定当前基行的索引、1当前表格单元索引、2 当前行索引、3 当前元索引。 </param>
        /// <returns></returns>
        public int GetCurObjectIndex(int nType)
        {
            return emrEditorControl1.GetCurObjectIndex(nType);
        }
        #endregion
        #endregion
        #region 基方法 2
        #region 2.10
        public bool UniversalBoolFunction(string str1, string str2, int n1, int n2)
        {
            return emrEditorControl1.UniversalBoolFunction(str1, str2, n1, n2);
        }
        /// <summary>
        /// 文档是否修改
        /// </summary>
        /// <returns></returns>
        public bool ex_IsDocAlter()
        {
            return emrEditorControl1.ex_IsDocAlter();
        }
        /// <summary>
        /// 在当前光标位置插入字符串 
        /// </summary>
        /// <param name="str">需要插入的字符串</param>
        /// <returns></returns>
        public bool ex_InsertContent(string strContent)
        {
            return emrEditorControl1.ex_InsertContent(strContent);
        }
        /// <summary>
        /// 判断当前光标位置的行是否只读 
        /// </summary>
        /// <returns></returns>
        public bool ex_CanReadLine()
        {
            return emrEditorControl1.ex_CanReadLine();
        }
        /// <summary>
        /// 在当前位置插入回车符 
        /// </summary>
        /// <returns></returns>
        public bool ex_InsertEnterChar()
        {
            return emrEditorControl1.ex_InsertEnterChar();
        }
        /// <summary>
        /// 判断当前的文档是否为空 
        /// </summary>
        /// <returns></returns>
        public bool ex_CanNull()
        {
            return emrEditorControl1.ex_CanNull();
        }
        /// <summary>
        /// 模拟发送键盘消息 
        /// </summary>
        ///  /// <param name="Iskey">true 只是键盘操作，不含选择操作</param>
        /// <param name="nVK">键盘值。 1  VK_UP， 2  VK_LEFT， 3  VK_DOWN， 4  VK_RIGHT， 5  VK_PRIOR，6  VK_NEXT，7  VK_HOME，8  VK_END，9  VK_DELETE，10  VK_RETURN，11 VK_BACK 。 </param>
        /// <returns></returns>
        public bool ex_SendKeyboardMessage(bool Iskey, int nVK)
        {
            return emrEditorControl1.ex_SendKeyboardMessage(Iskey, nVK);
        }
        /// <summary>
        ///  设置是否起用修订机制 ,修订机制的设置影响粘贴操作,控件默认起用修订机制
        /// </summary>
        /// <param name="nRead"></param>
        /// <returns></returns>
        public bool ex_CanAlter(bool nRead)
        {
            return emrEditorControl1.ex_CanAlter(nRead);
        }
        /// <summary>
        /// 返回当前修订机制状态 
        /// </summary>
        /// <returns></returns>
        public bool ex_GetCanAlter()
        {
            return emrEditorControl1.ex_GetCanAlter();
        }
        /// <summary>
        /// 设置表格只读
        /// </summary>
        /// <param name="nRead">true 只读 false 编辑</param>
        /// <returns></returns>
        public bool ex_CanReadTable(bool nRead)
        {
            return emrEditorControl1.ex_CanReadTable(nRead);
        }
        /// <summary>
        /// 文本替换 
        /// </summary>
        /// <param name="strOld">原文本</param>
        /// <param name="strNew">替换文本。注意不能包含回车、换行符 </param>
        /// <param name="bPos">ture 从文档开始替换 false 从当前光标位置开始替换</param>
        /// <returns></returns>
        public bool ex_Replace(string strOld, string strNew, bool bPos)
        {
            return emrEditorControl1.Replace(strOld, strNew, bPos);
        }
        /// <summary>
        /// 插入文件  当前位置追加
        /// </summary>
        /// <param name="strFileName"></param>
        public bool ex_InsertFile(string strFileName)
        {
            return emrEditorControl1.FileInsert(strFileName);
        }

        /// <summary>
        /// 将当前行置顶 
        /// </summary>
        public void ex_CurLineTop()
        {
            emrEditorControl1.ex_CurLineTop();
        }
        /// <summary>
        /// 删除当前元素
        /// </summary>
        public void ex_DelField()
        {
            emrEditorControl1.ex_DelField();
        }
        /// <summary>
        /// 元素属性
        /// </summary>
        /// <returns></returns>
        public void ex_FiledAttribute()
        {
            emrEditorControl1.ex_FiledAttribute();
        }
        /// <summary>
        /// 查找文本
        /// </summary>
        /// <param name="str">字符串</param>
        /// <param name="pos">》0重头开始找</param>
        /// <returns></returns>
        public bool ex_FindText(string str, int pos)
        {
            return emrEditorControl1.FindText(str, pos);
        }
        /// <summary>
        ///设置当前控件获得焦点
        /// </summary>
        /// <remarks></remarks>
        public void ex_SetOnFocus()
        {
            emrEditorControl1.ex_SetOnFocus();
        }
        /// <summary>
        /// 设置当前复制操作是复制带语法的还是普通文本
        /// 0是带语法复制，>0复制普通平面文字。
        /// </summary>
        public void ex_SetCopyType(int type)
        {
            emrEditorControl1.ex_SetCopyType(type);
        }

        /// <summary>
        /// 查找当前文档中是否有包含指定类型的元素
        /// </summary>
        /// <param name="strFieldType"></param>
        /// <param name="bFindCurPos"></param>
        /// <param name="CursorPos">>0把光标置到选中的元素，否则不改变当前光标位置。 </param>
        /// <returns></returns>
        public bool ex_FindField(string strFieldType, bool bFindCurPos, int nCursorPos)
        {
            return emrEditorControl1.ex_FindField(strFieldType, bFindCurPos, nCursorPos);
        }

        ///// <summary>
        ///// 克隆表格的最后一行
        ///// </summary>
        ///// <param name="bCursorPos"></param>
        ///// <returns></returns>
        //public bool ex_CloneTableLastLine(bool bCursorPos)
        //{
        //    return emrEditorControl1.ex_CloneTableLastLine(bCursorPos);
        //}
        ///// <summary>
        ///// 克隆行
        ///// </summary>
        ///// <returns></returns>
        //public bool ex_CloneLine()
        //{
        //    return emrEditorControl1.ex_CloneLine();
        //}

        /// <summary>
        /// 设置表格固定行高
        /// </summary>
        /// <param name="nLineNum">行号，从“0”开始</param>
        /// <param name="nHeight">bAutoHeight=fasle 是有效</param>
        /// <param name="bAutoHeight">true 自动伸缩 false 固定行高</param>
        /// <returns></returns>
        public bool ex_SetTableLineHeight(int nLineNum, int nHeight, bool bAutoHeight)
        {
            return emrEditorControl1.ex_SetTableLineHeight(nLineNum, nHeight, bAutoHeight);
        }
        /// <summary>
        /// 设置字体颜色
        /// </summary>
        /// <param name="R">The R.</param>
        /// <param name="G">The G.</param>
        /// <param name="B">The B.</param>
        /// <remarks></remarks>
        public void ex_SetColor(int R, int G, int B)
        {
            emrEditorControl1.SetColor(R, G, B);
        }
        /// <summary>
        /// 获取指定的文档是否是电子病历文档
        /// </summary>
        /// <param name="strFilePath"> 文件名，含绝对路径名如“c:\tempmr” </param>
        /// <returns></returns>
        public bool ex_IsEmrFile(string strFilePath)
        {
            return true; //emrEditorControl1.IsEmrFile(strFilePath);
        }
        /// <summary>
        /// 插入图像，并可设置是否保存真实图像数据 
        /// </summary>
        /// <param name="strFilePath">文件名</param>
        /// <param name="bSaveInter">true 保存病历时将把图像保存到病历中 false保存病历时仅保存图像的文件名</param>
        /// <returns></returns>
        public bool ex_InsertImage(string strFilePath, bool bSaveInter)
        {
            return emrEditorControl1.ex_InsertImage(strFilePath, bSaveInter);
        }
        /// <summary>
        /// 通过接口插入 Plugin 插件
        /// </summary>
        /// <param name="strDllName">插件DLL文件名</param>
        /// <param name="strParma">插件参数,可为空 </param>
        /// <returns></returns>
        public bool ex_InsertPlugin(string strDllName, string strParma)
        {
            return emrEditorControl1.ex_InsertPlugin(strDllName, strParma);
        }
        /// <summary>
        /// 设置是否打印装订线
        /// </summary>
        /// <param name="nType">true 是，false 否</param>
        public void ex_CanGutter(bool nType)
        {
            emrEditorControl1.ex_CanGutter(nType);
        }
        /// <summary>
        /// 判断光标是否在一页的首行上
        /// </summary>
        /// <returns></returns>
        public bool ex_IsFirstLine()
        {
            return emrEditorControl1.ex_IsFirstLine();
        }
        /// <summary>
        /// 多媒体
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public bool ex_InsertMultimedia(string path)
        {
            return emrEditorControl1.ex_InsertMultimedia(path);
        }
        /// <summary>
        /// 设置装订线类型
        /// </summary>
        /// <param name="strTyoe">类型 0.1.2.3.4</param>
        /// <param name="nWidth">宽度 1-9</param>
        /// <returns></returns>
        public bool ex_SetGutter(int strTyoe, int nWidth)
        {
            return emrEditorControl1.ex_SetGutter(strTyoe, nWidth);
        }
        /// <summary>
        /// 设置固定文本整洁显示是否可见
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        public bool ex_CanVisible(bool ntype)
        {
            return emrEditorControl1.ex_CanVisible(ntype);
        }
        /// <summary>
        /// 通过行保护页眉页脚变化的值
        /// </summary>
        /// <param name="strFieldName">元素名</param>
        /// <param name="strFieldValue">元素值</param>
        public void ex_SetLineHeadOrFoot(string strFieldName, string strFieldValue)
        {
            emrEditorControl1.ex_SetLineHeadOrFoot(strFieldName, strFieldValue);
        }
        /// <summary>
        /// 登记文档页眉需要变化值的元素
        /// </summary>
        /// <param name="strFieldName">元素名</param>
        /// <param name="bReg">true登记元素 false 注销元素</param>
        public void ex_CanRegHeadOrFoot(string strFieldName, bool bReg)
        {
            emrEditorControl1.ex_CanRegHeadOrFoot(strFieldName, bReg);
        }
        /// <summary>
        /// 清空预设宏的值
        /// </summary>
        public void ex_SetLineHead()
        {
            emrEditorControl1.ex_SetLineHead();
        }
        /// <summary>
        /// 设置元素的属性
        /// </summary>
        /// <param name="AttributeStr">元素的格式字符串</param>
        public void SetElementAttribute(string AttributeStr, string createType, int elementType)
        {
            emrEditorControl1.SetElementAttribute(AttributeStr, createType, elementType);
        }
        #endregion

        #region 2.11
        public string UniversalStringFunction(string str1, string str2, string str3, string str4, string str5, int n1)
        {
            return emrEditorControl1.UniversalStringFunction(str1, str2, str3, str4, str5, n1.ToString());
        }
        /// <summary>
        /// 设置元素的名称 
        /// </summary>
        /// <param name="baseLineNum">基行行号,如果设置当前元素的名称设置为“-1” </param>
        /// <param name="cellNum">表格单元号,如果不是表格单元设置为“-1” ,设置当前元素的名称设置为“-1”</param>
        /// <param name="lineNum">行号，如果设置当前元素的名称设置为“-1” 。 </param>
        /// <param name="fieldNum">元素号，如果设置当前元素的名称设置为“-1” 。</param>
        /// <param name="fieldName">元素名称。</param>
        /// <returns></returns>
        public bool ex_SetFieldName(string baseLineNum, string cellNum, string lineNum, string fieldNum, string fieldName)
        {
            return emrEditorControl1.ex_SetFieldName(baseLineNum, cellNum, lineNum, fieldNum, fieldName);
        }
        /// <summary>
        /// 获取元素的名称 
        /// </summary>
        /// <param name="baseLineNum">基行行号,如果设置当前元素的名称设置为“-1” </param>
        /// <param name="cellNum">表格单元号,如果不是表格单元设置为“-1” ,设置当前元素的名称设置为“-1”</param>
        /// <param name="lineNum">行号，如果设置当前元素的名称设置为“-1” 。 </param>
        /// <param name="fieldNum">元素号，如果设置当前元素的名称设置为“-1” 。</param>
        /// <returns></returns>
        public string ex_GetFieldName(string baseLineNum, string cellNum, string lineNum, string fieldNum)
        {
            return emrEditorControl1.ex_GetFieldName(baseLineNum, cellNum, lineNum, fieldNum);
        }
        /// <summary>
        /// 设置文档的名称 
        /// </summary>
        /// <param name="docName">文档的名称</param>
        /// <returns></returns>
        public void ex_SetDocName(string docName)
        {
            emrEditorControl1.SetFileName(docName);//.SetDocName(docName);
        }
        /// <summary>
        /// 获取文档的名称 
        /// </summary>
        /// <returns></returns>
        public string ex_GetDocName()
        {
            return emrEditorControl1.GetFileName();//.GetDocName();
        }
        /// <summary>
        /// 修订信息
        /// </summary>
        /// <param name="bDoc">true 整个文档，false 选中部分</param>
        /// <returns></returns>
        public string ex_GetRevisionInfo(bool bDoc)
        {
            return emrEditorControl1.ex_GetRevisionInfo(bDoc);
        }
        /// <summary>
        /// 获取选中部分的平面文字
        /// </summary>
        /// <returns></returns>
        public string ex_GetSelectContent()
        {
            return emrEditorControl1.ex_GetSelectContent();
        }
        /// <summary>
        /// 获取当前的修订片段号
        /// </summary>
        /// <returns></returns>
        public string ex_GetRevisionNum()
        {
            return emrEditorControl1.ex_GetRevisionNum();
        }
        /// <summary>
        /// 获取当前元素的类型名称
        /// </summary>
        /// <returns></returns>
        public string ex_GetFieldTypeName()
        {
            return emrEditorControl1.ex_GetFieldTypeName();
        }
        /// <summary>
        /// 获取当前元素的数据类型
        /// </summary>
        /// <returns></returns>
        public string ex_GetFieldDataType()
        {
            return emrEditorControl1.ex_GetFieldDataType();
        }
        /// <summary>
        /// 获取当前元素的text
        /// </summary>
        /// <returns></returns>
        public string ex_GetFieldText()
        {
            return emrEditorControl1.ex_GetFieldText();
        }
        /// <summary>
        /// 获取当前元素的value
        /// </summary>
        /// <returns></returns>
        public string ex_GetFieldValue()
        {
            return emrEditorControl1.ex_GetFieldValue();
        }
        public void ex_SetFieldValue(string code)
        {
            emrEditorControl1.ex_SetFieldValue(code);
        }
        /// <summary>
        /// 获取元素的层次 
        /// </summary>
        /// <param name="baseLineNum">基行行号,如果设置当前元素的名称设置为“-1” </param>
        /// <param name="cellNum">表格单元号,如果不是表格单元设置为“-1” ,设置当前元素的名称设置为“-1”</param>
        /// <param name="lineNum">行号，如果设置当前元素的名称设置为“-1” 。 </param>
        /// <param name="fieldNum">元素号，如果设置当前元素的名称设置为“-1” 。</param>

        /// <returns></returns>
        public string ex_GetFieldLevel(string baseLineNum, string cellNum, string lineNum, string fieldNum)
        {
            return emrEditorControl1.ex_GetFieldLevel(baseLineNum, cellNum, lineNum, fieldNum);
        }
        /// <summary>
        /// 获取元素层次号
        /// </summary>
        /// <param name="baseLineNum"></param>
        /// <param name="cellNum"></param>
        /// <param name="lineNum"></param>
        /// <param name="fieldNum"></param>
        /// <returns></returns>
        //public string ex_GetFieldLevel(string baseLineNum, string cellNum, string lineNum, string fieldNum)
        //{
        //    return UniversalStringFunction(baseLineNum,  cellNum,  lineNum,  fieldNum, "", 26);
        //}
        /// <summary>
        /// 在指定范围内查找指定的元素
        /// </summary>
        /// <param name="fieldName">元素名</param>
        /// <param name="LevelNum">元素层次号，若为-1，查找所有层次的元素</param>
        /// <param name="fieldType">元素类型，1 元素，2 表格，3 表格或元素</param>
        /// <param name="beginLineNum">开始基行号</param>
        /// <param name="endLineNum">结束基行号</param>
        /// <returns>true 找到</returns>
        public bool ex_GetField(string fieldName, string LevelNum, string fieldType, string beginLineNum, string endLineNum)
        {
            return emrEditorControl1.ex_GetField(fieldName, LevelNum, fieldType, beginLineNum, endLineNum);
        }
        /// <summary>
        /// 获取修订信息
        /// </summary>
        /// <param name="revisionNum"></param>
        /// <returns></returns>
        public string ex_GetRevisionInfo(string revisionNum)
        {
            return emrEditorControl1.ex_GetRevisionInfo(revisionNum);
        }
        /// <summary>
        /// 获取已登记文档页眉变动元素的名称列表
        /// </summary>
        /// <returns></returns>
        public string[] ex_GetHeadFieldName()
        {
            return emrEditorControl1.ex_GetHeadFieldName();
        }
        #endregion

        #region 2.13 创建元素
        /// <summary>
        /// 文档样式
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        public bool ex_InsertShadedType(int ntype)
        {
            return emrEditorControl1.ex_InsertShadedType(ntype);
        }
        /// <summary>
        /// 插入页码
        /// </summary>
        /// <returns></returns>
        public bool ex_InsertPageNumber()
        {
            return emrEditorControl1.ex_InsertPageNumber();
        }
        /// <summary>
        /// 页眉页脚行高
        /// </summary>
        /// <returns></returns>
        public bool ex_SetHeaderFooterHeight(int heightValue)
        {
            return emrEditorControl1.ex_SetHeaderFooterHeight(heightValue);
        }
        /// <summary>
        /// 编辑页眉
        /// </summary>
        /// <returns></returns>
        public bool ex_EditPageHeader()
        {
           return emrEditorControl1.ex_EditPageHeader();
            //XmlDocument doc = new XmlDocument();
            //emrEditorControl1.GetXmlDocument(doc);
            //using (FrmEditHeader frm = new FrmEditHeader(doc))
            //{
            //    if (frm.ShowDialog() == DialogResult.OK)
            //    {
            //        emrEditorControl1.re.RefreshModifyHeader(frm.HeaderXmlString);
            //    }
            //}
           //return true;
        }
        /// <summary>
        /// 删除页眉
        /// </summary>
        /// <returns></returns>
        public bool ex_DeletePageHeader()
        {
            return emrEditorControl1.ex_DeletePageHeader();
        }
        /// <summary>
        /// 编辑页脚
        /// </summary>
        /// <returns></returns>
        public bool ex_EditPageFooter()
        {
            return emrEditorControl1.ex_EditPageFooter();
        }
        /// <summary>
        /// 删除页脚
        /// </summary>
        /// <returns></returns>
        public bool ex_DeletePageFooter()
        {
            return emrEditorControl1.ex_DeletePageFooter();
        }
        /// <summary>
        /// 编辑正文
        /// </summary>
        /// <returns></returns>
        public bool ex_EditPageContent()
        {
            return emrEditorControl1.ex_EditPageContent();
        }
        /// <summary>
        /// 创建横线
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        public bool ex_CreateHorizontalLine()
        {
            return emrEditorControl1.ex_CreateHorizontalLine();
        }
        /// <summary>
        /// 创建普通文本
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        public bool ex_CreateText()
        {
            return emrEditorControl1.ex_CreateText();
        }
        /// <summary>
        /// 创建单选元素
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        public bool ex_CreateRadio()
        {
            return emrEditorControl1.ex_CreateRadio();
        }
        public bool ex_CreateCheckBoxNew()
        {
            return emrEditorControl1.ex_CreateCheckBoxNew();
        }
        ///// <summary>
        ///// 创建多选元素
        ///// </summary>
        ///// <param name="ntype"></param>
        ///// <returns></returns>
        public bool ex_CreateCheckBox()
        {
            return emrEditorControl1.ex_CreateCheckBox();
        }
        /// <summary>
        ///有无选择元素
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        public bool ex_CreateBoolBox()
        {
            return emrEditorControl1.ex_CreateBoolBox();
        }
        /// <summary>
        ///录入提示元素
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        public bool ex_CreateTextBox()
        {
            return emrEditorControl1.ex_CreateTextBox();
        }
        /// <summary>
        ///格式录入元素
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        //public bool ex_CreateFormartElement()
        //{
        //    return emrEditorControl1.ex_CreateFormartElement();
        //}
        public bool ex_CreateFormatNumber()
        {
            return emrEditorControl1.ex_CreateFormatNumber();
        }
        public bool ex_CreateFormatDateTime()
        {
            return emrEditorControl1.ex_CreateFormatDateTime();
        }
        /// 脚本元素
        /// </summary>
        /// <returns></returns>
        public bool ex_CreateScriptElement()
        {
            return emrEditorControl1.ex_CreateScriptElement();
        }
        /// <summary>
        ///宏元素
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        public bool ex_CreateMicroFieldBox()
        {
            return emrEditorControl1.ex_CreateMicroFieldBox();
        }
        /// <summary>
        ///关键字元素
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        public bool ex_CreateKeywordBox()
        {
            return emrEditorControl1.ex_CreateKeywordBox();
        }
        /// <summary>
        ///固定标题元素
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        public bool ex_CreateFixTitleBox()
        {
            return emrEditorControl1.ex_CreateFixTitleBox();
        }
        /// <summary>
        ///月经史公式
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        public bool ex_CreateMenstrualHistory()
        {
            return emrEditorControl1.ex_CreateMenstrualHistory();
        }
        /// <summary>
        /// 插入牙位图
        /// </summary>
        /// <returns></returns>
        public bool ex_CreateToothFormula()
        {
            return emrEditorControl1.ex_CreateToothFormula();
        }
        /// <summary>
        ///标题定位符元素
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        public bool ex_CreateTitlePosBox()
        {
            return emrEditorControl1.ex_CreateTitlePosBox();
        }
        /// <summary>
        ///图像元素
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        public bool ex_CreateImageBox()
        {
            return emrEditorControl1.ex_CreateImageBox();
        }
        /// <summary>
        ///Plugin元素
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        //public bool ex_CreatePluginBox()
        //{
        //    return emrEditorControl1.ex_CreatePluginBox();
        //}
        /// <summary>
        ///多媒体元素
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        public bool ex_CreateFlashBox()
        {
            return emrEditorControl1.ex_CreateMediaPlayer();//.ex_CreateFlashBox();
        }
        /// <summary>
        ///脚本元素
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        //public bool ex_CreateScriptBox()
        //{
        //    return emrEditorControl1.ex_CreateScriptBox();
        //}
        /// <summary>
        ///设置行置顶,2012-07-04王守信增加
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        public bool ex_LineTotop()
        {
            return emrEditorControl1.ex_LineTotop();
        }

        #endregion
        #region 2.14 通用字符串扩展接口函数

        public string UniversalStringFunction(string str1, string str2, string str3, string str4, string str5, string str6)
        {
            return emrEditorControl1.UniversalStringFunction(str1, str2, str3, str4, str5, str6);
        }
        /// <summary>
        ///获取选中部分文字
        /// </summary>
        /// <param name="ntype"></param>
        /// <returns></returns>
        public string u_getselect_text()
        {
            return emrEditorControl1.u_getselect_text();
        }
        #endregion
        #endregion

        #region 扩展方法

        /// <summary>
        /// 复制到剪切板
        /// </summary>
        public void u_EditCopyToClipboard()
        {
            Clipboard.SetDataObject(ex_GetSelectContent());
        }

        /// <summary>
        /// 颜色对话框
        /// </summary>
        public void u_ColorDialog()
        {
            ColorDialog colorDialog = new ColorDialog();
            colorDialog.AllowFullOpen = true;
            colorDialog.FullOpen = true;
            colorDialog.ShowHelp = true;
            //colorDialog.Color = Color.Black;//初始化当前文本框中的字体颜色，当用户在ColorDialog对话框中点击"取消"按钮file://恢复原来的值
            colorDialog.ShowDialog();
            ex_SetColor(colorDialog.Color.R, colorDialog.Color.G, colorDialog.Color.B);
        }

        /// <summary>
        /// 页面设置对话框
        /// </summary>
        /// <remarks></remarks>
        public void u_PrintSetDialog()
        {
            PageSetupDialog printDialog = new PageSetupDialog();
            printDialog.Document = new System.Drawing.Printing.PrintDocument();
            if (printDialog.ShowDialog() != DialogResult.Cancel)
            {

            }
        }

        /// <summary>
        /// 特殊符号对话框
        /// </summary>
        public void u_CharacterDialog()
        {
            CharacterDialog frm = new CharacterDialog();
            if (frm.ShowDialog() == DialogResult.Yes)
            {
                ex_InsertContent(frm.SelectText);
                this.btncha.Enabled = false;
                this.btncha.Enabled = true;
            }
            else
            {
                this.btncha.Enabled = false;
                this.btncha.Enabled = true;
            }
        }

        /// <summary>
        /// 光标定位到开始行
        /// </summary>
        public void u_SetCursorPosBegin()
        {
            SetSel(0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
        }

        /// <summary>
        /// 光标定位到最后行
        /// </summary>
        public void u_SetCursorPosEnd()
        {
            SetSel(-1, 0, 0, 0, 0, -1, 0, 0, 0, 0);
        }

        /// <summary>
        /// 删除当前行
        /// </summary>
        /// <returns></returns>
        public bool u_DelSelectLine()
        {
            //DeleteLines
            int _beginlineindex = 0;
            int _cellindex = 0;
            int _lineindex = 0;
            int _fieldelemindex = 0;
            int _charpos = 0;

            bool flag = GetCurCursorPos(ref _beginlineindex, ref _cellindex, ref _lineindex, ref _fieldelemindex, ref _charpos, true);
            int _endlineindex = 0;
            flag = GetCurCursorPos(ref _endlineindex, ref _cellindex, ref _lineindex, ref _fieldelemindex, ref _charpos, false);

            return DeleteLines(_beginlineindex, _endlineindex);
        }
        /// <summary>
        /// 2012-07-03王守信添加光标定位,如当前为选中区域则定位到选中区域后，如当前非选中，则不变
        /// </summary>
        /// <returns></returns>
        public bool u_setsel_cursorend()
        {
            //u_setsel_cursorend
            int _begin_Baselineindex = 0;
            int _cellindex = 0;
            int _lineindex = 0;
            int _fieldelemindex = 0;
            int _charpos = 0;

            int _end_Baselineindex = 0;
            int _endcellindex = 0;
            int _endlineindex = 0;
            int _endfieldelemindex = 0;
            int _endcharpos = 0;

            bool flag = GetCurCursorPos(ref _begin_Baselineindex, ref _cellindex, ref _lineindex, ref _fieldelemindex, ref _charpos, true);//开头
            flag = GetCurCursorPos(ref _end_Baselineindex, ref _endcellindex, ref _endlineindex, ref _endfieldelemindex, ref _endcharpos, false);//结尾
            if ((_begin_Baselineindex == _end_Baselineindex) && (_cellindex == _endcellindex) && (_lineindex == _endlineindex) && (_fieldelemindex == _endfieldelemindex) && (_charpos == _endcharpos))//判断是否是选中已区域
            {
                return true;
            }
            else
            {
                return SetSel(_end_Baselineindex, _endcellindex, _endlineindex, _endfieldelemindex, _endcharpos, _end_Baselineindex, _endcellindex, _endlineindex, _endfieldelemindex, _endcharpos);
            }
        }

    /// <summary>
        /// 取当前光标位置
        /// </summary>
        /// <param name="cursorBegin">如果当前为选择状态，则发生作用，TRUE则返回选择区域的起始位置，FALSE则返回选择区域的结束位置，如果不是选择状态，TRUE和FALSE都返回当前的光标位置。</param>
        /// <returns></returns>
        public int GetCurCursorPos(bool cursorBegin)
        {
            return emrEditorControl1.GetCurCursorPos(cursorBegin);
        }
        /// <summary>
        /// 设置选择范围
        /// </summary>
        /// <param name="beginIndex">选择开始位置</param>
        /// <param name="endIndex">选择结束位置</param>
        /// <returns></returns>
        public bool SetSelected(int beginIndex, int endIndex)
        {
            return emrEditorControl1.SetSelected(beginIndex, endIndex);
        }
        /// <summary>
        /// 设置病程片段可编辑，其他区域不可编辑
        /// </summary>
        /// <param name="namePreFix">病程片段标识符的前缀</param>
        /// <param name="name">需要编辑的病程片段的标识符的完整名称</param>
        public bool SetCourseEdit(string namePreFix, string name)
        {
            return emrEditorControl1.SetCourseEdit(namePreFix,name);
        }
        /// <summary>
        /// 设定当前选中的关键词的文本内容
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        public bool u_SetSelectFieldName(string text)
        {
            return ex_SetFieldName("-1", "-1", "-1", "-1", text);
        }

        /// <summary>
        /// 读取当前光标位置处理所在表格的第几行
        /// </summary>
        /// <returns></returns>
        public int u_ReturnTableLine()
        {
            int _lineindex = 0;
            GetCurCursorPos(ref _lineindex, ref _lineindex, ref _lineindex, ref _lineindex, ref _lineindex, true);
            return _lineindex;
        }

       
        /// <summary>
        /// 插入多媒体
        /// </summary>
        public void u_InsertMultimedia()
        {
            OpenFileDialog openfile = new OpenFileDialog();
            //openfile.Filter = "图像文件|*.bmp;*.jpg;*.png";
            openfile.Title = "选择多媒体";
            //openfile.DefaultExt = "emt";
            //openfile.InitialDirectory = "C:\\mr2";
            if (openfile.ShowDialog() == DialogResult.OK)
            {
                ex_InsertMultimedia(openfile.FileName);
            }
        }

        #endregion

        #region 右键菜单
        private void 剪切CtrlVToolStripMenuItem_Click(object sender, EventArgs e)
        {
            EditCut();
        }

        private void 复制CtrlCToolStripMenuItem_Click(object sender, EventArgs e)
        {
            EditCopy();
        }

        private void 粘贴CtrlVToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //UniversalBoolFunction("", "", 11, 1);
            EditPaste();
        }

        /// <summary>
        /// 菜单状态
        /// </summary>
        private void MenuState(bool bvalue)
        {
            剪切CtrlVToolStripMenuItem.Enabled = bvalue;
            复制CtrlCToolStripMenuItem.Enabled = bvalue;
        }

        private void 整洁病例ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ex_SetModeClear();
        }

        private void 编辑病例ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ex_SetModeEdit();
        }

        #endregion

        #region 事件

        void emrEditorControl1_DblClick(object sender, System.EventArgs e)
        {
            if (OnDbClickEvent != null)
            {
                if (ex_GetFieldTypeName() == "text-fix") //双击固定文本，不可删除的
                {

                    string mr_code = ex_GetFieldValue();// UniversalStringFunction("-1", "-1", "-1", "-1", "", 12);
                    if (mr_code != null && mr_code.Trim() != "")
                    {
                        ex_DelField(); //删除当前按钮
                        OnDbClickEvent(mr_code);
                    }
                }
            }
        }

        private void colorComboBox1_ColorChanged(object sender, ColorChangeArgs e)
        {
            ex_SetColor(e.color.R, e.color.G, e.color.B);
        }

        private void emrEditorControl1_SelectKeyword(object sender, EventArgs e)
        {
            if (SelectKeyWordEvent != null)
                SelectKeyWordEvent();
        }
        private void btncha_Click(object sender, EventArgs e)
        {
            u_CharacterDialog();
        }

        private void contextMenuStrip1_Opening(object sender, CancelEventArgs e)
        {
            //复制 剪切
            if (ex_GetSelectContent().Length > 0)
                MenuState(true);
            else
                MenuState(false);

            //粘贴
            if (EditCanPaste())
                粘贴CtrlVToolStripMenuItem.Enabled = true;
            else
                粘贴CtrlVToolStripMenuItem.Enabled = false;
        }

        #endregion

        #region 私有方法
        /// <summary>
        /// 若添加结构元素触发结构控件
        /// </summary>
        public void ReflashXmlSchema()
        {
            //ex_CreateText();
            //ex_SetFieldName("-1", "-1", "-1", "-1", "[schema]fdasfd");
            //ReflashXmlSchema();
            string strlike = "schema_";
            string name = ex_GetFieldName("-1", "-1", "-1", "-1");
            if (name.IndexOf(strlike.ToLower()) > -1 || name.IndexOf(strlike.ToUpper()) > -1)
            {

                if (OnLoadSchema != null)
                {
                    OnLoadSchema(this, EventArgs.Empty);
                }
            }
        }

        #endregion

        #region 新方法
        /// <summary>
        /// 时间比较提示
        /// </summary>
        public void ex_SetModeClear()
        {
            long showMode = GetDocumentMode();
            if (showMode != 3)
            {
                DocShowMode = GetDocumentMode();
                SetDocumentMode(3);
            }
            else
            {
                SetDocumentMode((short)DocShowMode);
            }
        }
        public void ex_SetModeReadOnly()
        {
            SetDocumentMode(2);
        }
        public void ex_SetModeEdit()
        {
            SetDocumentMode(1);
        }
        /// <summary>
        /// 设置为设计模式
        /// </summary>
        public void ex_SetModeDesign()
        {
            SetDocumentMode(0);
        }
        /// <summary>
        /// 光标定位到文档结尾位置后，开始新段落
        /// </summary>
        public void ex_SetCursorPosDocEnd()
        {
            u_SetCursorPosEnd();
            EditLineEditMode();
            ex_SendKeyboardMessage(true, 8);
            //ex_InsertEnterChar();
        }
        /// <summary>
        /// 按元素定位查看，锁定所有行
        /// </summary>
        /// <param name="path">xml节点路径，格式"a-1/b-1"</param>
        public void u_SetLineView(string path)
        {
            try
            {
                ex_SetModeReadOnly();
                char levStr1 = '/';//一级匹配符
                char levStr2 = '-';//二级匹配符
                string strlike = "schema_";//元素匹配符
                int nBaseLineIndex = 0, nCellIndex = 0, nLineIndex = 0, nFieldElemIndex = 0, nCharPos = 0;
                int nBaseLineIndex2 = 0, nCellIndex2 = 0, nLineIndex2 = 0, nFieldElemIndex2 = 0, nCharPos2 = 0;
                u_SetCursorPosBegin();
                //循环的三个条件 1、名称，2 、层次 3、序列号
                string[] nodes = path.Split(levStr1);
                //遍历层次
                for (int i = 0; i < nodes.Length; i++)
                {
                    string node = nodes[i].Split(levStr2)[0];
                    int index = Convert.ToInt32(nodes[i].Split(levStr2)[1]);
                    //遍历同层个数
                    for (int j = 0; j < index; j++)
                    {
                        if (FindField(strlike.ToLower() + node, -1, 1, false) || FindField(strlike.ToUpper() + node, -1, 1, false))
                        {
                            //获取元素开始与结束位置
                            GetCurCursorPos(ref nBaseLineIndex, ref nCellIndex, ref nLineIndex, ref nFieldElemIndex, ref nCharPos, true);
                            GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                            //选中元素
                            SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2 + 1, nCharPos2);
                            //移动光标
                            ex_SendKeyboardMessage(true, 4);
                        }
                        if (FindField(strlike.ToLower() + "%", -1, 1, false) || FindField(strlike.ToUpper() + "%", -1, 1, false))
                        {
                            GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                            //漏选该控件
                            //ex_SendKeyboardMessage(true, 7);
                            //选中元素
                            SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2 + 1, nCharPos2);
                            //移动光标
                            ex_SendKeyboardMessage(true, 2);
                        }
                        else
                        {
                            //ex_SetCursorPosDocEnd();
                            GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);

                            //选中元素
                            SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2 + 1, nCharPos2);
                            //移动光标
                            ex_SendKeyboardMessage(true, 2);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("xml格式错误");
            }
        }
        /// <summary>
        /// 删除当前元素
        /// </summary>
        /// <returns></returns>
        public bool u_DelField()
        {//2.11.10 向上查找
            ex_DelField();
            return false;
        }
        /// <summary>
        /// 按元素定位编辑，锁定其他所有行
        /// </summary>
        /// <param name="path">xml节点路径，格式"a-1/b-1"</param>
        public void u_SetLineEdit(string path, ref int beginLine, ref int endLine)
        {
            try
            {
                ////可编辑模式
                ex_SetModeEdit();
                //全选
                EditSelectAll();
                //行只读
                EditLineReadOnly();
                char levStr1 = '/';//一级匹配符
                char levStr2 = '-';//二级匹配符
                string strlike = "schema_bc_";//元素匹配符
                int nBaseLineIndex = 0, nCellIndex = 0, nLineIndex = 0, nFieldElemIndex = 0, nCharPos = 0;
                int nBaseLineIndex2 = 0, nCellIndex2 = 0, nLineIndex2 = 0, nFieldElemIndex2 = 0, nCharPos2 = 0;
                u_SetCursorPosBegin();
                //循环的三个条件 1、名称，2 、层次 3、序列号
                string[] nodes = path.Split(levStr1);
                //遍历层次
                for (int i = 0; i < nodes.Length; i++)
                {
                    string node = nodes[i].Split(levStr2)[0];
                    int index = Convert.ToInt32(nodes[i].Split(levStr2)[1]);
                    //遍历同层个数
                    for (int j = 0; j < index; j++)
                    {
                        if (FindField(strlike.ToLower() + node, -1, 1, false) || FindField(strlike.ToUpper() + node, -1, 1, false))
                        {
                            //移动光标End 病程头末尾
                            ex_SendKeyboardMessage(true, 8);
                            //移动至下一行
                            ex_SendKeyboardMessage(true, 3);
                            GetCurCursorPos(ref nBaseLineIndex, ref nCellIndex, ref nLineIndex, ref nFieldElemIndex, ref nCharPos, true);
                            beginLine = nBaseLineIndex;
                            GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                        }
                        if (FindField(strlike.ToLower() + "%", -1, 1, false) || FindField(strlike.ToUpper() + "%", -1, 1, false))
                        {
                            //移动光标Home 病程头开始
                            ex_SendKeyboardMessage(true, 7);
                            //移动至上一行
                            ex_SendKeyboardMessage(true, 1);
                            GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                            //选中元素
                            SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2 + 1, nCharPos2);
                            endLine = nBaseLineIndex2;
                            EditLineEditMode();
                        }
                        else
                        {
                            ex_SetCursorPosDocEnd();
                            GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                            endLine = nBaseLineIndex2;
                            //选中元素
                            SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2 + 1, nCharPos2);
                            //移动光标
                            EditLineEditMode();
                        }
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("xml格式错误");
            }
        }
        /// <summary>
        /// 按元素定位，查找行，王守信2012-07-04增加
        /// </summary>
        /// <param name="path">xml节点路径，格式"a-1/b-1"</param>
        public int u_FindEditLine(string tagstring, ref int beginLine, ref int endLine)
        {
            return 0;
            //    try
            //    {
            //        //可编辑模式
            //        ex_SetModeEdit();
            //        //全选
            //        EditSelectAll();
            //        //行只读
            //        EditLineReadOnly();

            //        char levStr1 = '/';//一级匹配符
            //        char levStr2 = '-';//二级匹配符
            //        string strlike = "schema_bc_";//元素匹配符

            //        int nBaseLineIndex = 0, nCellIndex = 0, nLineIndex = 0, nFieldElemIndex = 0, nCharPos = 0;
            //        int nBaseLineIndex2 = 0, nCellIndex2 = 0, nLineIndex2 = 0, nFieldElemIndex2 = 0, nCharPos2 = 0;

            //        u_SetCursorPosBegin();

            //        //循环的三个条件 1、名称，2 、层次 3、序列号
            //        string[] nodes = path.Split(levStr1);
            //        //遍历层次
            //        for (int i = 0; i < nodes.Length; i++)
            //        {
            //            string node = nodes[i].Split(levStr2)[0];
            //            int index = Convert.ToInt32(nodes[i].Split(levStr2)[1]);
            //            //遍历同层个数
            //            for (int j = 0; j < index; j++)
            //            {
            //                if (FindField(strlike.ToLower() + node, -1, 1, false) || FindField(strlike.ToUpper() + node, -1, 1, false))
            //                {
            //                    //移动光标End 病程头末尾
            //                    ex_SendKeyboardMessage(true, 8);
            //                    //移动至下一行
            //                    ex_SendKeyboardMessage(true, 3);
            //                    GetCurCursorPos(ref nBaseLineIndex, ref nCellIndex, ref nLineIndex, ref nFieldElemIndex, ref nCharPos, true);
            //                    beginLine = nBaseLineIndex;
            //                    GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
            //                }
            //                if (FindField(strlike.ToLower() + "%", -1, 1, false) || FindField(strlike.ToUpper() + "%", -1, 1, false))
            //                {
            //                    //移动光标Home 病程头开始
            //                    ex_SendKeyboardMessage(true, 7);
            //                    //移动至上一行
            //                    ex_SendKeyboardMessage(true, 1);
            //                    GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);

            //                    //选中元素
            //                    SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2 + 1, nCharPos2);
            //                    endLine = nBaseLineIndex2;
            //                    EditLineEditMode();
            //                }
            //                else
            //                {
            //                    ex_SetCursorPosDocEnd();
            //                    GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
            //                    endLine = nBaseLineIndex2;
            //                    //选中元素
            //                    SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2 + 1, nCharPos2);
            //                    //移动光标
            //                    EditLineEditMode();
            //                }
            //            }
            //        }
            //    }
            //    catch (Exception e)
            //    {
            //        MessageBox.Show("xml格式错误");
            //    }
        }

        /// <summary>
        /// 定位元素末尾
        /// </summary>
        /// <param name="path"></param>
        public void u_GetNextSchema(string path)
        {
            try
            {
                char levStr1 = '/';//一级匹配符
                char levStr2 = '-';//二级匹配符
                string strlike = "schema_";//元素匹配符
                u_SetCursorPosBegin();
                //循环的三个条件 1、名称，2 、层次 3、序列号
                string[] nodes = path.Split(levStr1);
                //遍历层次
                for (int i = 0; i < nodes.Length; i++)
                {
                    string node = nodes[i].Split(levStr2)[0];
                    int index = Convert.ToInt32(nodes[i].Split(levStr2)[1]);
                    //遍历同层个数
                    for (int j = 0; j < index; j++)
                    {
                        if (FindField(strlike.ToLower() + node, -1, 1, false) || FindField(strlike.ToUpper() + node, -1, 1, false))
                        {//节点
                            //移动光标End 病程头末尾
                            ex_SendKeyboardMessage(true, 8);
                            //移动至下一行
                            ex_SendKeyboardMessage(true, 3);
                        }
                        if (FindField(strlike.ToLower() + "%", -1, 1, false) || FindField(strlike.ToUpper() + "%", -1, 1, false))
                        {
                            //移动光标Home 病程头开始
                            ex_SendKeyboardMessage(true, 7);
                            EditLineEditMode();
                            ex_InsertEnterChar();
                            ex_SendKeyboardMessage(true, 1);
                        }
                        else
                        {
                            ex_SetCursorPosDocEnd();
                            ex_InsertEnterChar();
                        }
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("xml格式错误");
            }
        }

        /// <summary>
        /// 根据光标位置选中元素
        /// </summary>
        public void ex_SelectCurrentElement()
        {
            int nBaseLineIndex = 0, nCellIndex = 0, nLineIndex = 0, nFieldElemIndex = 0, nCharPos = 0;
            int nBaseLineIndex2 = 0, nCellIndex2 = 0, nLineIndex2 = 0, nFieldElemIndex2 = 0, nCharPos2 = 0;
            //获取元素开始与结束位置
            GetCurCursorPos(ref nBaseLineIndex, ref nCellIndex, ref nLineIndex, ref nFieldElemIndex, ref nCharPos, true);
            GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
            //选中元素
            SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2 + 1, nCharPos2);
            //ex_SendKeyboardMessage(true, 4);
        }

        /// <summary>
        /// 按元素定位，并获取元素内容
        /// </summary>
        /// <param name="path">xml节点路径，格式"a-1/b-1"</param>
        public string u_GetEleContent(string path)
        {
            try
            {
                //char levStr1 = '/';//一级匹配符
                //char levStr2 = '-';//二级匹配符
                string strlike = "SCHEMA_";//元素匹配
                int nBaseLineIndex = 0, nCellIndex = 0, nLineIndex = 0, nFieldElemIndex = 0, nCharPos = 0;
                int nBaseLineIndex2 = 0, nCellIndex2 = 0, nLineIndex2 = 0, nFieldElemIndex2 = 0, nCharPos2 = 0;
                u_SetCursorPosBegin();
                if (FindField(strlike.ToUpper() + path, -1, 1, false))
                {
                    GetCurCursorPos(ref nBaseLineIndex, ref nCellIndex, ref nLineIndex, ref nFieldElemIndex, ref nCharPos, false);
                    GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                    //移动光标right
                    ex_SendKeyboardMessage(true, 4);
                    ex_SendKeyboardMessage(true, 4);
                    if (FindField(strlike.ToUpper() + "%", -1, 1, false))
                    {
                        GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                        //选中元素
                        SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2, nCharPos2);
                    }
                    else
                    {
                        ex_SetCursorPosDocEnd();
                        GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                        //选中元素
                        SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2, nCharPos2);
                    }
                }
                else
                    return null;
            }
            catch (Exception e)
            {
                MessageBox.Show("xml格式错误");
            }
            return ex_GetSelectContent();
        }
        /// <summary>
        /// 按元素定位，并获取元素内容
        /// </summary>
        /// <param name="path">xml节点路径，格式"a-1/b-1"</param>
        public string u_GetEleContentForCourse(string path)
        {
            try
            {
                //char levStr1 = '/';//一级匹配符
                //char levStr2 = '-';//二级匹配符
                string strlike = "SCHEMA_";//元素匹配
                int nBaseLineIndex = 0, nCellIndex = 0, nLineIndex = 0, nFieldElemIndex = 0, nCharPos = 0;
                int nBaseLineIndex2 = 0, nCellIndex2 = 0, nLineIndex2 = 0, nFieldElemIndex2 = 0, nCharPos2 = 0;
                //u_SetCursorPosBegin();
                if (FindField(strlike.ToUpper() + path, -1, 1, false))
                {
                    GetCurCursorPos(ref nBaseLineIndex, ref nCellIndex, ref nLineIndex, ref nFieldElemIndex, ref nCharPos, false);
                    GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                    //移动光标right
                    ex_SendKeyboardMessage(true, 4);
                    ex_SendKeyboardMessage(true, 4);
                    if (FindField(strlike.ToUpper() + "%", -1, 1, false))
                    {
                        GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                        //选中元素
                        SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2, nCharPos2);
                    }
                    else
                    {
                        ex_SetCursorPosDocEnd();
                        GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                        //选中元素
                        SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2, nCharPos2);
                    }
                }
                else
                    return null;

            }
            catch (Exception e)
            {
                MessageBox.Show("xml格式错误");
            }

            return ex_GetSelectContent();
        }
        /// <summary>
        /// 按病程定位，并获取元素内容 (病程签名时使用) lidw 2013-01-10
        /// </summary>
        public string u_GetCourseContent(string fileNo)
        {
            try
            {
                string strlike = "schema_bc_";//元素匹配
                int nBaseLineIndex = 0, nCellIndex = 0, nLineIndex = 0, nFieldElemIndex = 0, nCharPos = 0;
                int nBaseLineIndex2 = 0, nCellIndex2 = 0, nLineIndex2 = 0, nFieldElemIndex2 = 0, nCharPos2 = 0;
                u_SetCursorPosBegin();
                if (FindField(strlike.ToUpper() + fileNo, -1, 1, false) || FindField(strlike.ToLower() + fileNo, -1, 1, false))
                {
                    GetCurCursorPos(ref nBaseLineIndex, ref nCellIndex, ref nLineIndex, ref nFieldElemIndex, ref nCharPos, false);
                    GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                    //移动光标right
                    ex_SendKeyboardMessage(true, 4);
                    ex_SendKeyboardMessage(true, 4);
                    if (FindField(strlike.ToUpper() + "%", -1, 1, false) || FindField(strlike.ToLower() + "%", -1, 1, false))
                    {
                        GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                        //选中元素
                        SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2, nCharPos2);
                    }
                    else
                    {
                        ex_SetCursorPosDocEnd();
                        //SetSel(-1, -1, 0, 0, 0, -1, -1, 0, 0, 0);
                        GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                        //选中元素
                        SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2, nCharPos2);
                    }
                }
                else
                    return null;

            }
            catch (Exception e)
            {
                MessageBox.Show("xml格式错误");
            }

            return ex_GetSelectContent();
        }

        /// <summary>
        /// 入院记录定位，并获取内容
        /// </summary>
        public string u_GetAdmitContent(string fileNo)
        {
            try
            {
                string strlike = "姓名%";//元素匹配
                int nBaseLineIndex = 0, nCellIndex = 0, nLineIndex = 0, nFieldElemIndex = 0, nCharPos = 0;
                int nBaseLineIndex2 = 0, nCellIndex2 = 0, nLineIndex2 = 0, nFieldElemIndex2 = 0, nCharPos2 = 0;
                u_SetCursorPosBegin();
                if (FindField(strlike.ToUpper(), -1, 1, false) || FindField(strlike.ToLower(), -1, 1, false))
                {
                    GetCurCursorPos(ref nBaseLineIndex, ref nCellIndex, ref nLineIndex, ref nFieldElemIndex, ref nCharPos, false);
                    GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                    //移动光标right
                    ex_SendKeyboardMessage(true, 4);
                    ex_SendKeyboardMessage(true, 4);
                    if (FindField(strlike.ToUpper() + "%", -1, 1, false) || FindField(strlike.ToLower() + "%", -1, 1, false))
                    {
                        GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                        //选中元素
                        SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2, nCharPos2);
                    }
                    else
                    {
                        ex_SetCursorPosDocEnd();
                        //SetSel(-1, -1, 0, 0, 0, -1, -1, 0, 0, 0);
                        GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                        //选中元素
                        SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2, nCharPos2);
                    }
                }
                else
                    return null;
            }
            catch (Exception e)
            {
                MessageBox.Show("xml格式错误");
            }
            return ex_GetSelectContent();
        }


        /// <summary>
        /// 按Schema定位(结束靠层次关系得到)，并选中元素内容
        /// </summary>
        /// <param name="path">xml节点路径，格式"a-1/b-1"</param>
        public void u_SelectedBySchema(string path)
        {
            try
            {
                string strlike = "schema_";//元素匹配
                int nBaseLineIndex = 0, nCellIndex = 0, nLineIndex = 0, nFieldElemIndex = 0, nCharPos = 0;
                int nBaseLineIndex2 = 0, nCellIndex2 = 0, nLineIndex2 = 0, nFieldElemIndex2 = 0, nCharPos2 = 0;
                u_SetCursorPosBegin();
                string level = "";
                if (FindField(strlike.ToLower() + path, -1, 1, false) || FindField(strlike.ToUpper() + path, -1, 1, false))
                {
                    //获取元素的层次
                    level = ex_GetFieldLevel("-1", "-1", "-1", "-1");
                    GetCurCursorPos(ref nBaseLineIndex, ref nCellIndex, ref nLineIndex, ref nFieldElemIndex, ref nCharPos, true);
                    GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                    ex_SendKeyboardMessage(true, 4); //  4.right-->  3.Down
                    ex_SendKeyboardMessage(true, 4);
                    bool ifEnd = true;
                    while (ifEnd)
                    {
                        if (FindField(strlike.ToLower() + "%", -1, 1, false) || FindField(strlike.ToUpper() + "%", -1, 1, false))
                        {
                            string level2 = ex_GetFieldLevel("-1", "-1", "-1", "-1");
                            if (level2.Equals(level))
                            {
                                ifEnd = false;
                                GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                                //选中元素
                                SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2, nCharPos2);
                            }
                            else
                                ex_SendKeyboardMessage(true, 4); //  4.right-->  3.Down
                        }
                        else
                        {
                            ex_SetCursorPosDocEnd();
                            GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                            //选中元素
                            SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2, nCharPos2);
                            ifEnd = false;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("xml格式错误");
            }
        }

        /// <summary>
        /// 按元素定位选中
        /// </summary>
        /// <param name="path">xml节点路径，格式"a-1/b-1"</param>
        public void u_SetLineSelect(string node)
        {
            try
            {
                //可编辑模式
                ex_SetModeEdit();
                #region 测试
                //全选
                //EditSelectAll();
                //行只读
                //EditLineReadOnly();
                #endregion
                string lev1 = string.Empty;//上一个元素层级
                string lev2 = string.Empty;//下一个元素层级
                string strlike = "schema_";//元素匹配符
                int nBaseLineIndex = 0, nCellIndex = 0, nLineIndex = 0, nFieldElemIndex = 0, nCharPos = 0;
                int nBaseLineIndex2 = 0, nCellIndex2 = 0, nLineIndex2 = 0, nFieldElemIndex2 = 0, nCharPos2 = 0;
                u_SetCursorPosBegin();
                #region 查找该元素
                bool lb_ret = FindField(strlike + node, -1, 1, true);
                string aa = ex_GetFieldTypeName();
                //获取当前元素的类型名称
                //获取元素的层次
                lev1 = ex_GetFieldLevel("-1", "-1", "-1", "-1");
                GetCurCursorPos(ref nBaseLineIndex, ref nCellIndex, ref nLineIndex, ref nFieldElemIndex, ref nCharPos, true);
                GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                //选中元素
                SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2 + 1, nCharPos2);
                ex_SendKeyboardMessage(true, 4);
                #endregion
                if (lb_ret)
                {//有结构
                    while (lb_ret)
                    {
                        //查找下一结点
                        if (FindField(strlike.ToLower() + "%", -1, 1, false) || FindField(strlike.ToUpper() + "%", -1, 1, false))
                        {
                            //aa = ex_GetFieldTypeName();
                            //获取元素的层次
                            lev2 = ex_GetFieldLevel("-1", "-1", "-1", "-1");
                            GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                            //选中元素
                            SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2 + 1, nCharPos2);
                            if (string.Equals(lev1, lev2))
                            {
                                //ex_SendKeyboardMessage(true,7);
                                //选中元素
                                SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2 + 1, nCharPos2);
                                DeleteLines(nBaseLineIndex, nBaseLineIndex2 - 1);
                                //EditLineEditMode();
                                lb_ret = false;
                            }
                            else
                            {
                                ex_SendKeyboardMessage(true, 4);
                                lb_ret = true;
                            }
                        }
                        else
                        {
                            lb_ret = false;
                            ex_SetCursorPosDocEnd();
                            GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
                            //选中元素
                            SetSel(nBaseLineIndex, nCellIndex, nLineIndex, nFieldElemIndex, nCharPos, nBaseLineIndex2, nCellIndex2, nLineIndex2, nFieldElemIndex2 + 1, nCharPos2);
                            //移动光标
                            EditLineEditMode();
                        }
                    }
                }
                u_SetCursorPosBegin();
            }
            catch (Exception e)
            {
                MessageBox.Show("xml格式错误");
            }
        }

        //public void SetRightMenuSaveVisible(bool jud)
        //{
        //    emrEditorControl1.SetRightMenuSaveVisible(jud);
        //}
        //public void SetRightMenuCompleteVisible(bool jud)
        //{
        //    emrEditorControl1.SetRightMenuCompleteVisible(jud);
        //}

        public bool ex_CreateNewHead()
        {
            return emrEditorControl1.ex_CreateNewHead();
        }
        #endregion
       #region 病历打印方法集合
         /// <summary>
        /// 打印预览
        /// </summary>
        public void PrintPreview()
        {
            emrEditorControl1.PrintPreview();
        }
        /// <summary>
        /// 打印文档
        /// </summary>
        /// <param name="preview">是否弹出预览</param>
        /// <param name="printerName">打印机名称</param>
        public void PrintAll(bool preview, string printerName)
        {
            emrEditorControl1.PrintAll(preview,printerName);
        }
        /// <summary>
        /// 按页打印
        /// </summary>
        /// <param name="startpage">开始页</param>
        /// <param name="endpage">结束页</param>
        /// <returns></returns>
        public bool PrintPage(int startpage, int endpage)
        {
            return emrEditorControl1.PrintPage(startpage, endpage);
        }

        /// <summary>
        /// 打印当前页
        /// </summary>
        /// <returns></returns>
        public bool PrintCurentPage()
        {
            return emrEditorControl1.PrintCurentPage();
        }
        /// <summary>
        /// 打印奇数页
        /// </summary>
        /// <param name="printerName"></param>
        /// <returns></returns>
        public bool PrintOddNumberPage(string printerName)
        {
            return emrEditorControl1.PrintOddNumberPage(printerName);
        }
        /// <summary>
        /// 
        /// </summary>
        /// 打印
        /// <param name="printerName"></param>
        /// <param name="startPage"></param>
        /// <param name="endPage"></param>
        /// <returns></returns>
        public bool PrintOddNumberPage(string printerName, int startPage, int endPage)
        {
            return emrEditorControl1.PrintOddNumberPage(printerName,startPage,endPage);
        }
        /// <summary>
        /// 打印偶数页
        /// </summary>
        /// <param name="printerName"></param>
        /// <returns></returns>
        public bool PrintEvenNumberPage(string printerName)
        {
            return emrEditorControl1.PrintEvenNumberPage(printerName);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="printerName"></param>
        /// <param name="startPage"></param>
        /// <param name="endPage"></param>
        /// <returns></returns>
        public bool PrintEvenNumberPage(string printerName, int startPage, int endPage)
        {
            return emrEditorControl1.PrintEvenNumberPage(printerName,startPage,endPage);
        }
        /// <summary>
        /// 设置是否启用选择文本打印
        /// </summary>
        /// <param name="enabled"></param>
        public void SetEnabledSelectionPrint(bool enabled)
        {
            emrEditorControl1.SetEnabledSelectionPrint(enabled);
        }
        /// <summary>
        /// 打印选中内容
        /// </summary>
        /// <param name="isPreview">是否预览</param>
        /// <param name="printerName">打印机名称</param>
        /// <returns></returns>
        public bool PrintSelection(bool isPreview, string printerName)
        {
           return emrEditorControl1.PrintSelection(isPreview,printerName);
        }
        /// <summary>
        /// 设置是否启用选择区域打印
        /// </summary>
        /// <param name="enabled"></param>
        public void SetEnabledSelectionAreaPrint(bool enabled)
        {
            emrEditorControl1.SetEnabledSelectionAreaPrint(enabled);
        }
        /// <summary>
        /// 开始区域打印模式
        /// </summary>
        /// <param name="isPreview">是否需要预览</param>
        /// <param name="printerName">打印机名称</param>
        /// <returns></returns>
        public bool PrintSelectArea(bool isPreview, string printerName)
        {
            return emrEditorControl1.PrintSelectArea(isPreview,printerName);
        }
        /// <summary>
        /// 设置是否启用续打
        /// </summary>
        /// <param name="enabled"></param>
        public void SetEnabledJumpPrint(bool enabled)
        {
            emrEditorControl1.SetEnabledJumpPrint(enabled);
        }
       /// <summary>
        /// 续打
        /// </summary>
        /// <param name="isPreview">是否预览</param>
        /// <param name="printerName">打印机名称</param>
        /// <returns></returns>
        public bool PrintJump(bool isPreview, string printerName)
        {
          return emrEditorControl1.PrintJump(isPreview,printerName);
        }
        /// <summary>
        /// 设置双面打印
        /// </summary>
        /// <param name="bSytle">0默认，1单面，2水平双面，3垂直双面</param>
        /// <returns></returns>
        public bool SetDuplexPrint(int bSytle)
        {
            return emrEditorControl1.SetDuplexPrint(bSytle);
        }
        /// <summary>
        ///  获取当前是否双面打印
        /// </summary>
        /// <returns>true双面打印，false单面打印或者打印机默认值</returns>
        public bool GetCanDuplexPrint()
        {
            return emrEditorControl1.GetCanDuplexPrint();
        }
        /// <summary>
        /// 判断选择打印是否打印页眉页脚
        /// </summary>
        /// <returns></returns>
        public bool CanPrintHeadOrFoot()
        {
           return emrEditorControl1.CanPrintHeadOrFoot();
        }
        /// <summary>
        /// 设置选择打印是否打印页眉页脚
        /// </summary>
        /// <returns></returns>
        public void SetPrintHeadOrFoot(bool bType)
        {
            emrEditorControl1.SetPrintHeadOrFoot(bType);
        }
#endregion
        public void SetPageSizeCustom(string type)
        {
            emrEditorControl1.SetPageSizeCustom(type);
        }
        private void emrEditorControl1_ClickEvent(object sender, EventArgs e)
        {
            if (OnClickEvent != null)
                OnClickEvent();
        }

        private void 选中打印ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Print(1);
            //if (onPrintSelectedClick != null)
            //{
            //    onPrintSelectedClick(sender, e);
            //}
            //else
            //{
            //    Print(1);
            //}
        }
      #region 设置默认打印机

        [DllImport("winspool.drv")]
        private static extern bool SetDefaultPrinter(string Name);
        /// <summary>
        /// 设置默认打印机
        /// </summary>
        /// <param name="printername"></param>
        public void SetCustomPrinter(string printername)
        {
            SetDefaultPrinter(printername);
        }
        /// <summary>
        /// 获取当前默认打印机
        /// </summary>
        /// <returns></returns>
        public string GetDefaultPrinter()
        {
            System.Drawing.Printing.PrintDocument pdoc = new System.Drawing.Printing.PrintDocument();
            return pdoc.PrinterSettings.PrinterName;
        }
        #endregion
        /// <summary>
        /// 设置菜单是否显示
        /// </summary>
        /// <param name="as_bool"></param>
        public void MenuItemVisible(Boolean as_bool)
        {
            复制文本ToolStripMenuItem.Visible = as_bool;
            粘贴外部文本ToolStripMenuItem.Visible = as_bool;
            复制文本ToolStripMenuItem.Enabled = as_bool;
            粘贴外部文本ToolStripMenuItem.Enabled = as_bool;
            toolStripSeparator1.Visible = as_bool;
            //if (GetDocumentMode() == 1)//当可编辑时才能删除
            //{
            //    删除元素toolStripMenuItem.Visible = true;
            //}
            //else
            //{
            //    删除元素toolStripMenuItem.Visible = false;
            //}
            //if (UniversalStringFunction("-1", "-1", "-1", "-1", "", "32") == "TRUE")//判断行是否只读，返回true只读否则返回“”
            //{
            //    删除元素toolStripMenuItem.Visible = false;
            //}
            //else
            //{
            //    删除元素toolStripMenuItem.Visible = true;
            //}
        }
        /// <summary>
        /// 显示菜单
        /// </summary>
        /// <param name="visible"></param>
        public void MenuVisible(bool visible)
        {
            if (visible)
            {
                contextMenuStrip1.Visible = true;
                剪切CtrlVToolStripMenuItem.Visible = true;
                复制CtrlCToolStripMenuItem.Visible = true;
                粘贴CtrlVToolStripMenuItem.Visible = true;
                toolStripSeparator1.Visible = true;
                复制文本ToolStripMenuItem.Visible = true;
                粘贴外部文本ToolStripMenuItem.Visible = true;
                toolStripSeparator2.Visible = true;
                选中打印ToolStripMenuItem.Visible = true;
                整洁病例ToolStripMenuItem.Visible = true;
                替换元素ToolStripMenuItem.Visible = true;
                编辑病例ToolStripMenuItem.Visible = true;
                撤销CtrlZToolStripMenuItem.Visible = true;
                恢复CtrlRToolStripMenuItem.Visible = true;

                //保存CtrlSToolStripMenuItem.Visible = true;
                //完成CtrlDToolStripMenuItem.Visible = true;
                打印整页ToolStripMenuItem.Visible = true;
            }
            else
            {
                contextMenuStrip1.Visible = false;
                剪切CtrlVToolStripMenuItem.Visible = false;
                复制CtrlCToolStripMenuItem.Visible = false;
                粘贴CtrlVToolStripMenuItem.Visible = false;
                toolStripSeparator1.Visible = false;
                复制文本ToolStripMenuItem.Visible = false;
                粘贴外部文本ToolStripMenuItem.Visible = false;
                toolStripSeparator2.Visible = false;
                选中打印ToolStripMenuItem.Visible = false;
                整洁病例ToolStripMenuItem.Visible = false;
                替换元素ToolStripMenuItem.Visible = false;
                编辑病例ToolStripMenuItem.Visible = false;
                撤销CtrlZToolStripMenuItem.Visible = false;
                恢复CtrlRToolStripMenuItem.Visible = false;
                //保存CtrlSToolStripMenuItem.Visible = false;
                //完成CtrlDToolStripMenuItem.Visible = false;
                打印整页ToolStripMenuItem.Visible = false;
            }
        }
        /// <summary>
        /// 显示菜单
        /// </summary>
        /// <param name="visible"></param>
        public void MenuVisibleAll(bool visible)
        {
            if (visible)
            {
                contextMenuStrip1.Visible = true;
                剪切CtrlVToolStripMenuItem.Visible = true;
                复制CtrlCToolStripMenuItem.Visible = true;
                粘贴CtrlVToolStripMenuItem.Visible = true;
                toolStripSeparator1.Visible = true;
                复制文本ToolStripMenuItem.Visible = true;
                粘贴外部文本ToolStripMenuItem.Visible = true;
                toolStripSeparator2.Visible = true;
                选中打印ToolStripMenuItem.Visible = true;
                整洁病例ToolStripMenuItem.Visible = true;
                编辑病例ToolStripMenuItem.Visible = true;
                删除元素toolStripMenuItem.Visible = true;
                替换元素ToolStripMenuItem.Visible = true;
                复制为文本toolStripMenuItem.Visible = true;
                元素属性ToolStripMenuItem.Visible = true;
                撤销CtrlZToolStripMenuItem.Visible = true;
                恢复CtrlRToolStripMenuItem.Visible = true;
                //保存CtrlSToolStripMenuItem.Visible = true;
                //完成CtrlDToolStripMenuItem.Visible = true;
                打印整页ToolStripMenuItem.Visible = true;
            }
            else
            {
                contextMenuStrip1.Visible = false;
                剪切CtrlVToolStripMenuItem.Visible = false;
                复制CtrlCToolStripMenuItem.Visible = false;
                粘贴CtrlVToolStripMenuItem.Visible = false;
                toolStripSeparator1.Visible = false;
                复制文本ToolStripMenuItem.Visible = true;
                粘贴外部文本ToolStripMenuItem.Visible = false;
                toolStripSeparator2.Visible = false;
                选中打印ToolStripMenuItem.Visible = false;
                整洁病例ToolStripMenuItem.Visible = false;
                编辑病例ToolStripMenuItem.Visible = false;
                删除元素toolStripMenuItem.Visible = false;
                复制为文本toolStripMenuItem.Visible = false;
                元素属性ToolStripMenuItem.Visible = false;
                替换元素ToolStripMenuItem.Visible = false;
                撤销CtrlZToolStripMenuItem.Visible = false;
                恢复CtrlRToolStripMenuItem.Visible = false;
                //保存CtrlSToolStripMenuItem.Visible = false;
                //完成CtrlDToolStripMenuItem.Visible = false;
                打印整页ToolStripMenuItem.Visible = false;
            }
        }
        public void MenuBarVisible(bool visible)
        {
            toolStripbar.Visible = visible;
        }

        private void 删除元素toolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (GetDocumentMode() == 1)//当可编辑时才能删除
            {
                if (emrEditorControl1.UniversalStringFunction("-1", "-1", "-1", "-1", "", "32") == "TRUE")//判断行是否只读，返回true只读否则返回“”
                {
                    MessageBox.Show("元素不允许删除！", "友情提示");
                    return;
                }
                else
                {
                    emrEditorControl1.UniversalBoolFunction("", "", 36, 0);
                }
            }
        }

        private void 替换元素ToolStripMenuItem_Click(object sender, EventArgs e)
        {
          
            if (GetDocumentMode() == 1)//当可编辑时才能替换
            {
                if (emrEditorControl1.UniversalStringFunction("-1", "-1", "-1", "-1", "", "32") == "TRUE")//判断行是否只读，返回true只读否则返回“”
                {
                    MessageBox.Show("元素不允许替换！", "友情提示");
                    return;
                }
                else
                {
                    //SetFieldAttributeValue();
                    //UpdateFieldText(ex_GetFieldName("-1", "-1", "-1", "-1"), ex_GetFieldText(), true);
                   UpdateFieldTextByCode(ex_GetFieldValue(), ex_GetFieldText(),true);
                    
                }
            }
        }

        private void 元素属性ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ex_FiledAttribute();
            ReflashXmlSchema();
        }

        private void 复制文本ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ex_SetCopyType(1);
            EditCopy();//拷贝到剪切板EditCopy()
            ex_SetCopyType(0);
        }

        private void 复制为文本toolStripMenuItem_Click(object sender, EventArgs e)
        {
            ex_SetCopyType(1);
            EditCopy();
            ex_SetCopyType(0);
        }

        private void 粘贴为文本toolStripMenuItem_Click(object sender, EventArgs e)
        {
            //UniversalBoolFunction("", "", 11, 1);
            EditPasteExternal();
            // UniversalBoolFunction("", "", 11, 5);
        }
        private void 粘贴外部文本ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //UniversalBoolFunction("", "", 11, 2);//设置内容为纯文本
            EditPaste();//粘贴
            //UniversalBoolFunction("", "", 11, 5);//设置内容带格式
        }

        private void label1_Click(object sender, EventArgs e)
        {
            label1.Visible = false;
        }

        private void 保存CtrlSToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (OnClickRightMouse != null)
            {
                OnClickRightMouse(sender, e);
            }
        }

        private void 完成CtrlDToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (OnSumbitClick != null)
            {
                OnSumbitClick(sender, e);
            }
        }

        private void 撤销CtrlZToolStripMenuItem_Click(object sender, EventArgs e)
        {
            EditUnDo();
        }

        private void 恢复CtrlRToolStripMenuItem_Click(object sender, EventArgs e)
        {
            EditReDo();
        }

        private void 打印整页ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (onPrintAllPageClick != null)
                onPrintAllPageClick(sender, e);
            else
                Print(0);
        }

        public int ex_GetLineHeight()
        {
            return emrEditorControl1.GetLineHeight();
        }
        public bool ex_SetLineHeight()
        {
            return emrEditorControl1.SetLineHeight();
        }
        public void ex_SetCopyFlag(bool isCopy)
        {
            emrEditorControl1.SetCopyFlag(isCopy);
        }
        public void ShowPageHeaderLine(bool isVisible)
        {
            emrEditorControl1.ShowPageHeaderLine(isVisible);
        }
        public void ShowPageFooterLine(bool isVisible)
        {
            emrEditorControl1.ShowPageFooterLine(isVisible);
        }
        public void ex_InsertPageCount()
        {
            emrEditorControl1.ex_InsertPageCount();
        }
        //--------------工具条
        private void tsbtnFontBold_Click(object sender, EventArgs e)
        {
            emrEditorControl1.FontBold();
        }

        private void tsbtnFontItalic_Click(object sender, EventArgs e)
        {
            emrEditorControl1.FontItalic();
        }

        private void tsbtnUnderLine_Click(object sender, EventArgs e)
        {
            emrEditorControl1.FontUnderline();
        }

        private void tsbtnAlignLeft_Click(object sender, EventArgs e)
        {
            emrEditorControl1.LineAlignLeft();
        }

        private void tsbtnAlignCenter_Click(object sender, EventArgs e)
        {
            emrEditorControl1.LineAlignCenter();
        }

        private void tsbtnAlignRight_Click(object sender, EventArgs e)
        {
            emrEditorControl1.LineAlignRight();
        }

        private void tsbtnAlignJustify_Click(object sender, EventArgs e)
        {
            emrEditorControl1.LineAlignJustify();
        }

        private void tsbtnDisperse_Click(object sender, EventArgs e)
        {
            emrEditorControl1.LineAlignDisperse();
        }

        private void tsbtnDefaultLineSpacing_Click(object sender, EventArgs e)
        {

            emrEditorControl1.LineSpaceParagraph(1F);
        }

        private void tsbtnOneAndHalfLineSpacing_Click(object sender, EventArgs e)
        {
            emrEditorControl1.LineSpaceParagraph(1.5F);
        }

        private void tsbtnFontSup_Click(object sender, EventArgs e)
        {
            emrEditorControl1.FontSuperscript();
        }

        private void tsbtnFontSub_Click(object sender, EventArgs e)
        {
            emrEditorControl1.FontSubscript();
        }

        private void tsbtnFontColor_Click(object sender, EventArgs e)
        {
            emrEditorControl1.FontForeColor();
        }
        private void btncha_Click_1(object sender, EventArgs e)
        {

        }
        private void tscmbFontName_SelectedIndexChanged(object sender, EventArgs e)
        {
            emrEditorControl1.SelectionFontName(tscmbFontName.Text);
        }

        private void tscmbFontSize_SelectedIndexChanged(object sender, EventArgs e)
        {
            emrEditorControl1.SelectionFontSize(tscmbFontSize.Text);
        }


        private void SelectionFontSizeNew(string fontsize)
        {
            emrEditorControl1.SelectionFontSize(fontsize);
        }

        private void emrEditorControl1_SaveEMR(object sender, EventArgs e)
        {
            if (OnClickRightMouse != null)
            {
                OnClickRightMouse(sender, e);
            }
        }

        private void emrEditorControl1_CompleteEMR(object sender, EventArgs e)
        {
            if (OnSumbitClick != null)
            {
                OnSumbitClick(sender, e);
            }
        }

        private void tsbtnBgColor_Click(object sender, EventArgs e)
        {
            emrEditorControl1.FontBackColor();
        }
        public bool SetCourseEdit(string namePreFix, string name, ref int beginLine, ref int endLine)
        {
            int nBaseLineIndex = 0, nCellIndex = 0, nLineIndex = 0, nFieldElemIndex = 0, nCharPos = 0;
            int nBaseLineIndex2 = 0, nCellIndex2 = 0, nLineIndex2 = 0, nFieldElemIndex2 = 0, nCharPos2 = 0;
            FindField(name.ToLower(), -1, 1, true);
            GetCurCursorPos(ref nBaseLineIndex, ref nCellIndex, ref nLineIndex, ref nFieldElemIndex, ref nCharPos, true);
            beginLine = nBaseLineIndex;
            FindField(namePreFix.ToLower() + "%", -1, 1, false);
            GetCurCursorPos(ref nBaseLineIndex2, ref nCellIndex2, ref nLineIndex2, ref nFieldElemIndex2, ref nCharPos2, false);
            endLine = nBaseLineIndex2 - 1;
            return emrEditorControl1.SetCourseEdit(namePreFix, name); ;
        }
        /// <summary>
        /// 删除病程片段
        /// </summary>
        /// <param name="namePreFix">病程片段标识符的前缀</param>
        /// <param name="name">需要删除的病程片段的标识符的完整名称</param>
        public bool DeleteCourse(string namePreFix, string name)
        {
            return emrEditorControl1.DeleteCourse(namePreFix, name);
        }
        /// <summary>
        /// 移动病程顺序，把movename的病程段移动到targetName病程段的前面。如果targetName为空，则移动到文档末尾
        /// </summary>
        /// <param name="namePreFix">病程片段标识符的前缀</param>
        /// <param name="moveName">需要移动的病程片段的标识符的完整名称</param>
        /// <param name="targetName">目标位置标识符完整名称，如果未空，则移动到文档末尾</param>
        /// <returns></returns>
        public bool EditCourseOrder(string namePreFix, string moveName, string targetName)
        {
            return emrEditorControl1.EditCourseOrder(namePreFix, moveName, targetName);
        }

        private void tscmbLineSpace_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (tscmbLineSpace.Text == "1")
            { emrEditorControl1.LineSpaceParagraph(1F); }
            else if (tscmbLineSpace.Text == "1.5")
            { emrEditorControl1.LineSpaceParagraph(1.5F); }
            if (tscmbLineSpace.Text == "2")
            { emrEditorControl1.LineSpaceParagraph(2F); }
            if (tscmbLineSpace.Text == "2.5")
            { emrEditorControl1.LineSpaceParagraph(2.5F); }
            if (tscmbLineSpace.Text == "3")
            { emrEditorControl1.LineSpaceParagraph(3F); }
            if (tscmbLineSpace.Text == "4")
            { emrEditorControl1.LineSpaceParagraph(4F); }
        }

        private void 插入牙位图ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ex_CreateToothFormula();
        }

        private void tsbtnTwoLineSpacing_Click(object sender, EventArgs e)
        {
            emrEditorControl1.LineSpaceParagraph(2F);
        }

        private void tsbtnBoldUnderLine_Click(object sender, EventArgs e)
        {
            emrEditorControl1.FontBoldUnderLine();
        }
    }
}
