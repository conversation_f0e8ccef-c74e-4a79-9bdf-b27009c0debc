<?xml version="1.0"?>
<doc>
    <assembly>
        <name>PlatCommon</name>
    </assembly>
    <members>
        <member name="T:PlatCommon.Base01.Cs01Base64">
            <summary>
            Base64与文件的转换
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Base64.Base64ToFile(System.String,System.String)">
            <summary>        
            base64转文件     
            </summary> 
            <param name="strBase64">Base64串</param>  
            <param name="strFileName">保存的文件名</param>
            <returns>成功</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Base64.Base64ToImage(System.String,System.String,System.Drawing.Imaging.ImageFormat)">
            <summary>        
            base64 转 Image       
            </summary> 
            <param name="strBase64">Base64串</param>  
            <param name="strFileName">保存的文件名</param>  
            <param name="imgFormat">图片格式</param>  
        </member>
        <member name="M:PlatCommon.Base01.Cs01Base64.ImageToBase64(System.String,System.Drawing.Imaging.ImageFormat)">
            <summary>        
            Image 转成 base64        
            </summary>        
            <param name="strFileFullName"></param>  
            <param name="imgFormat">图片格式</param>  
        </member>
        <member name="M:PlatCommon.Base01.Cs01Base64.FileToBase64(System.String)">
            <summary>        
            文件转成 base64        
            </summary>        
            <param name="strFileFullName"></param>  
        </member>
        <member name="M:PlatCommon.Base01.Cs01Base64.Base64ToStream(System.String,System.IO.MemoryStream@)">
            <summary>        
            base64 转 数据流       
            </summary> 
            <param name="strBase64">Base64串</param>  
            <param name="memStream">数据流</param>  
        </member>
        <member name="M:PlatCommon.Base01.Cs01Base64.StreamToBase64(System.IO.MemoryStream)">
            <summary>
             数据流 转base64 
            </summary>
            <param name="memStream">数据流</param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Base01.Cs01DtExtend">
            <summary>
            静态数据类全局函数集
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.IsNumericType(System.Object)">
            <summary>
            判断对象是否是数值类型
            </summary>
            <param name="o">对象</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.IsNumericType(System.Type)">
            <summary>
            判断类型是否是数值类型
            </summary>
            <param name="type">类型</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.ToList``1(System.Data.DataTable)">
            <summary>
            DataTable To List的扩展方法
            </summary>
            <typeparam name="T"></typeparam>
            <param name="dtData">数据表</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.MergeColumn(System.Data.DataTable,System.String)">
            <summary>
            合并DataTable的一列为\r\n分割的字符串
            </summary>
            <param name="dtData">数据表</param>
            <param name="strColName">列名</param>
            <returns>DataTable</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.IsSameDataRow(System.Data.DataRow,System.Data.DataRow,System.Int32[])">
            <summary>
            比较DataRow是否有相同的列
            </summary>
            <param name="dr1">DataRow1</param>
            <param name="dr2">DataRow2</param>
            <param name="cols">列的整数数组</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.IsSameDataRow(System.Data.DataRow,System.Data.DataRow,System.Collections.Generic.Dictionary{System.Int32,System.Int32})">
            <summary>
            比较DataRow是否有相同的列
            </summary>
            <param name="dr1">DataRow1</param>
            <param name="dr2">DataRow2</param>
            <param name="dicCols">Dictionary源列，目标列序号</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.IsSameDataRow(System.Data.DataRow,System.Data.DataRow,System.String[])">
            <summary>
            比较DataRow是否有相同的列
            </summary>
            <param name="dr1">DataRow</param>
            <param name="dr2">DataRow</param>
            <param name="strArrCols">列名的字符串数组</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.IsSameDataRow(System.Data.DataRowView,System.Data.DataRowView,System.String[])">
            <summary>
            比较DataRowView是否有相同的列
            </summary>
            <param name="dr1">DataRowView</param>
            <param name="dr2">DataRowView</param>
            <param name="strArrCols">列名的字符串数组</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.CopyDataRow(System.Data.DataRow,System.Data.DataRow)">
            <summary>
            复制DataRow列的值, 相同列名进行拷贝
            </summary>
            <param name="dr1">待复制DataRow</param>
            <param name="dr2">将复制DataRow</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.CopyDataRow(System.Data.DataRow,System.Data.DataRow,System.Int32[])">
            <summary>
            复制DataRow列的值
            </summary>
            <param name="dr1">待复制DataRow</param>
            <param name="dr2">将复制DataRow</param>
            <param name="iArrCols">列的整数数组, 空数组则要求dr1包含dr2的全部列</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.CopyDataRow(System.Data.DataRow,System.Data.DataRow,System.Collections.Generic.Dictionary{System.Int32,System.Int32})">
            <summary>
            复制DataRow列的值
            </summary>
            <param name="dr1">待复制的DataRow</param>
            <param name="dr2">复制到的DataRow</param>
            <param name="dicCols">Dictionary源列，目标列号</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.CopyDataRow(System.Data.DataRow,System.Data.DataRow,System.String[])">
            <summary>
            复制DataRow列的值
            </summary>
            <param name="dr1">待复制DataRow</param>
            <param name="dr2">将复制DataRow</param>
            <param name="strArrColNames">列的名称数组, 空数组则是复制相同名称列</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.FindRow(System.Data.DataTable,System.String,System.Object)">
            <summary>
            查找字段为指定值的数据行
            </summary>
            <param name="dataTable">DataTable</param>
            <param name="strFieldName">字段</param>
            <param name="objValue">查找值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.GetExistedRow(System.Data.DataTable,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            返回存在数据的行号
            </summary>
            <param name="dataTable">dataTable</param>
            <param name="dicFields">列值字典</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.FindEmptyFieldRow(System.Data.DataTable,System.String)">
            <summary>
            查找字段为空的数据行
            </summary>
            <param name="dtData">DataTable</param>
            <param name="strFieldName">字段</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.DeleteEmptyFieldRows(System.Data.DataTable,System.String)">
            <summary>
            删除字段不能为空的数据行
            </summary>
            <param name="dtData">DataTable</param>
            <param name="strFieldName">字段</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.DeleteEmptyFieldsRows(System.Data.DataTable,System.String[])">
            <summary>
            删除字段不能为空的数据行
            </summary>
            <param name="dtData">DataTable</param>
            <param name="strArrFieldNames">字段集</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.FindEmptyFieldsRows(System.Data.DataTable,System.String[])">
            <summary>
            查找字段为空的数据行
            </summary>
            <param name="dtData">DataTable</param>
            <param name="strArrFieldNames">字段集</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.IsRepeated(System.Data.DataTable,System.String)">
            <summary>
            判断是否有重复数据，返回重复的行号对
            </summary>
            <param name="dtData">dtData</param>
            <param name="strFieldName">列名</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.IsRepeated(System.Data.DataTable,System.String[])">
            <summary>
            判断是否有重复数据，返回重复的行号对
            </summary>
            <param name="dtData">dtData</param>
            <param name="strArrFieldNames">列名数组</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.DataTableToList``1(System.Data.DataTable)">
            <summary>
            DataTable转化为List
            </summary>
            <typeparam name="T">数据类</typeparam>
            <param name="dtData">DataTable</param>
            <returns>List</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.ToListDataTable``1(System.Data.DataTable)">
            <summary>
            DataTable转化为List数据类型一致的数据, 方法是修改字段的数据类型
            </summary>
            <typeparam name="T">数据类</typeparam>
            <param name="dtData">DataTable</param>
            <returns>List</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.ToListDataTable2``1(System.Data.DataTable)">
            <summary>
            DataTable转化为List数据类型一致的数据, 方法是添加新类型的字段，将数据转换到新的字段，删除原有字段，修改新字段的名称为原字段名称
            </summary>
            <typeparam name="T">数据类</typeparam>
            <param name="dataTable">dataTable</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.GetDataTableModifiedFirtRowInfo(System.Data.DataTable)">
            <summary>
            读取DataTable中第一行修改的数据
            </summary>
            <param name="dataTable">DataTable</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.IsDataRowChanged(System.Data.DataRow)">
            <summary>
            判断是否是变化的行
            </summary>
            <param name="dataRow"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.DataTableColumnLandscape(System.Data.DataTable,System.String[],System.String,System.String)">
            <summary>
            将DataTable的某列横向展开，将值列填充到横向对应列中
            </summary>
            <param name="dt">DataTable</param>
            <param name="strArrColumns">输出列表，空数组为全部</param>
            <param name="landscapeColumn">横向列名</param>
            <param name="valueColumn">值列名</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.DataTableColumnLandscape(System.Data.DataTable,System.Int32[],System.Int32,System.Int32)">
            <summary>
            将DataTable的某列横向展开，将值列填充到横向对应列中
            </summary>
            <param name="dt">DataTable</param>
            <param name="commonColumns">输出列表，空数组为全部</param>
            <param name="landscapeColumn">横向列</param>
            <param name="valueColumn">值列</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.DataTableSetCaptions(System.Data.DataTable,System.String)">
            <summary>
            设定DataTable各列的Caption
            </summary>
            <param name="dataTable">dataTable</param>
            <param name="strCaptions">列头</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.CreateDataTable``1(``0)">
            <summary>
            根据数据类创建DataTable对象
            </summary>
            <typeparam name="T">泛型数据类</typeparam>
            <param name="t">数据类对象</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.GetDynamicClassByDataRow(System.Data.DataRow)">
            <summary>
            使用dynamic根据DataRow,由列名自动添加属性并赋值
            </summary>
            <param name="dataRow">dataRow</param>
            <returns></returns> 
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.CreatNewClassByDataRow(System.Data.DataRow)">
            <summary>
            使用反射 动态创建类，将DataTable的列名动态添加为该类的属性，并给属性赋值
            </summary>
            <param name="dataRow"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.DataRowToEntity``1(System.Data.DataRow)">
            <summary>
            将行数据转成对应的实体(属性转换)
            </summary>
            <typeparam name="T">实体</typeparam>
            <param name="dataRow">DataRow</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.ToEntity``1(System.Data.DataRow)">
            <summary>
            将行数据转成对应的实体(属性转换
            </summary>
            <typeparam name="T">实体</typeparam>
            <param name="dataRow">dataRow</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.DataRowToEntityFields``1(System.Data.DataRow)">
            <summary>
            将行数据转成对应的实体(字段转换)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="dataRow">DataRow</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.ListToDataTable``1(System.Collections.Generic.List{``0})">
            <summary>
            转换List对象为DataTable对象
            </summary>
            <param name="listData">List</param>
            <returns>DataTable对象</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.DataTableDelete(System.Data.DataTable)">
            <summary>
            将DataTable各行置为删除状态
            </summary>
            <param name="dtData">DataTable</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.GetDataTableChangedCount(System.Data.DataTable)">
            <summary>
            获取DataTable变化的行数
            </summary>
            <param name="dtData">DataTable</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.DataTableCopy(System.Data.DataTable,System.Data.DataTable,System.String[])">
            <summary>
            将DataTable指定的列数据复制到dtTarget中， 不复制删除行和行状态。
            </summary>
            <param name="dtSource">源DataTable</param>
            <param name="dtTarget">目的DataTable</param>
            <param name="columns">列名数组</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.DataTableToToEdit(System.Data.DataTable,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            去掉DataTable中的MaxLength限制
            </summary>
            <param name="dataTable">DataTable</param>
            <param name="dCaption">字典：字段==>显示名称</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.RemoveLongerItem(System.Collections.Generic.Dictionary{System.String,System.Int32},System.Int32)">
            <summary>
            字典中移除长度大于某值的记录
            </summary>
            <param name="dic">字典</param>
            <param name="n">长度值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.StartsWithCode(System.Collections.Generic.Dictionary{System.String,System.Int32},System.String)">
            <summary>
            
            </summary>
            <param name="dic"></param>
            <param name="strCode"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.Swap``1(``0@,``0@)">
            <summary>
            交换变量的值
            </summary>
            <typeparam name="T">数据泛类型</typeparam>
            <param name="lhs">左交换数</param>
            <param name="rhs">右交换数</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.SwapIfGreater``1(``0@,``0@)">
            <summary>
            当右交换数大于左交换数时，交换变量的值
            </summary>
            <typeparam name="T">数据泛类型</typeparam>
            <param name="lhs">左交换数</param>
            <param name="rhs">右交换数</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.SwapIfLess``1(``0@,``0@)">
            <summary>
            当右交换数小于左交换数时，交换变量的值
            </summary>
            <typeparam name="T">数据泛类型</typeparam>
            <param name="lhs">左交换数</param>
            <param name="rhs">右交换数</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.StringToList(System.String,System.String[])">
            <summary>
            将字符串分割成字符串List
            </summary>
            <param name="strSource">源字符串</param>
            <param name="strArrSplit">分割字符串数组</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.StringToList(System.String,System.String)">
            <summary>
            将字符串分割成字符串
            </summary>
            <param name="strSource">源字符串</param>
            <param name="strSplit">分割字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.GetDisplayName(System.Reflection.PropertyInfo)">
            <summary>
            获取属性DisplayName
            </summary>
            <param name="pInfo">属性</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.Clone``1(``0)">
            <summary>
            对象的复制
            </summary>
            <typeparam name="T">数据泛类型</typeparam>
            <param name="RealObject">数据实例</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.CloneEx``1(``0)">
            <summary>
            复制对象
            </summary>
            <typeparam name="T">数据泛类型</typeparam>
            <param name="data">数据实例</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.TableSort(System.Data.DataTable,System.String)">
            <summary>
            将DataTable按列排序
            <param name="dtSourceTable">排序前DataTable</param>
            <param name="strSortFields">排序字段strFieldNames</param>
            <returns>排序后DataTable</returns>
            </summary> 
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.TableGroup(System.Data.DataTable,System.String)">
            <summary>
            将DataTable分组
            <param name="dtSourceTable">要分组的DataTable</param>
            <param name="strGroupFields">分组字段strGroupFields</param>
            <returns>分组后DataTable</returns>
            </summary> 
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.ChangeToCross(System.Data.DataTable,System.String,System.String,System.String)">
            <summary>
            将DataTable转为交叉表
            </summary>
            <param name="dtSourceTable">交叉表源</param>
            <param name="strFixFields">固定字段</param>
            <param name="strCrossField">可变字段</param>
            <param name="strCrossValueField">可变值字段</param>
            <returns>转为交叉表后的DataTable</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.TableGroupCalcute(System.Data.DataTable,System.String,System.String,System.String,System.String)">
            <summary>
            将DataTable分组计算
            <param name="dtSourceTable">分组计算源表</param>
            <param name="strGroupFields">分组字段</param>
            <param name="strCountFields">计数字段</param>
            <param name="strSumFields">求和字段</param>
            <param name="strAvgFields">平均值字段</param>
            <returns>分组计算后的DataTable</returns>
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01DtExtend.TableNUp(System.Data.DataTable,System.Int32)">
            <summary>
            表分栏
            </summary>
            <param name="dtSourceTable">源表</param>
            <param name="iNUpCount">分栏数</param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Base01.Cs01ExcelHelper">
            <summary>
            DataSet与EXCEL转换
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01ExcelHelper.DataSetToExcel(System.String,System.Data.DataSet)">
            <summary>
            将DataSet数据保存为EXCEL文件
            </summary>
            <param name="strFileName">将要保存的文件路径</param>
            <param name="dsSource">将要保存的DataSet数据表可多页面</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01ExcelHelper.ExcelToDataSet(System.String,System.String)">
            <summary>
            将EXCLE文件读取到DataSet表中。支持多工作表读取
            </summary>
            <param name="strFileName">要读取的文件路径</param>
            <param name="strSheetName">要读取的文件路径</param>
            <returns>返回DataSet表</returns>
        </member>
        <member name="T:PlatCommon.Base01.Cs01FileHelper">
            <summary>
            目录及文件操作助手
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FileHelper.#ctor">
            <summary>
            默认构造函数
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FileHelper.GetCurrDllPath">
            <summary>
            获取当前动态库所在路径(非网络路径，即不含 file:\)
            </summary>
            <returns>当前动态库所在路径</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FileHelper.CopyDirectory(System.IO.DirectoryInfo,System.IO.DirectoryInfo)">
            <summary>
            拷贝目录
            </summary>
            <param name="dirInfoOld">源目录</param>
            <param name="dirInfoNew">目标目录, 如果不存在,则创建</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FileHelper.CopyDirectory_NoSub(System.String,System.String)">
            <summary>
            拷贝目录, 不拷贝下一级目录
            </summary>
            <param name="dirSrc">源目录</param>
            <param name="dirDest">目标目录, 如果不存在,则创建</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FileHelper.CopyDirectory(System.String,System.String)">
            <summary>
            拷贝目录
            </summary>
            <param name="oldDir">源目录</param>
            <param name="newDir">目标目录, 如果不存在,则创建</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FileHelper.GetDirFiles(System.String)">
            <summary>
            获取目录下的所有文件
            </summary>
            <param name="path">目录</param>
            <returns>文件集合</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FileHelper.GetDirFiles(System.String,System.Collections.ArrayList@)">
            <summary>
            获取目录下的所有文件
            </summary>
            <param name="path">目录</param>
            <param name="files">文件集合</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FileHelper.DeleteFile(System.String)">
            <summary>
            删除文件
            </summary>
            <param name="path">文件</param>
            <remarks>对于只读文件会先去掉只读属性, 再删除</remarks>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FileHelper.CopyFile(System.String,System.String)">
            <summary>
            尽力Copy文件, 忽略错误
            </summary>
            <param name="srcFile">源文件</param>
            <param name="destFile">目标文件</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FileHelper.GetFullFileName(System.String,System.String)">
            <summary>
            获取文件的完整路径名称
            </summary>
            <param name="currentPath">当前目录</param>
            <param name="fileName">文件名</param>
            <returns>文件的绝对路径</returns>
            <remarks>把..\...\fileName转换成绝对路径</remarks>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FileHelper.GetFileContent(System.String)">
            <summary>
            获取文件内容
            </summary>
            <param name="fileName">文件名</param>
            <returns>文件内容字符串</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FileHelper.CreateFileFromStream(System.String,System.IO.Stream)">
            <summary>
            从数据流中创建文件
            </summary>
            <param name="fileName">创建的文件名</param>
            <param name="s"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FileHelper.WriteFile(System.Byte[],System.String)">
            <summary>
            将传入的byte[]写入到指定文件中
            </summary>
            <param name="data"></param>
            <param name="fileName">指定的文件名</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FileHelper.ReadFile(System.String)">
            <summary>
            从指定的文件中读取数据到byte[]中
            </summary>
            <param name="fileName">指定的文件名</param>
            <returns>读取到的byte[]</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FileHelper.ReadFileTemp(System.String)">
            <summary>
            从指定的临时文件中读取数据到byte[]中
            </summary>
            <param name="fileName">指定的临时文件名</param>
            <returns>读取到的byte[]</returns>
        </member>
        <member name="T:PlatCommon.Base01.MyLogicType">
            <summary>
            逻辑运算枚举
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.MyLogicType.And">
            <summary>
            逻辑与
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.MyLogicType.Or">
            <summary>
            逻辑或
            </summary>
        </member>
        <member name="T:PlatCommon.Base01.MyOperatorName">
            <summary>
            运算枚举
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.MyOperatorName.Equals">
            <summary>
            等于
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.MyOperatorName.NotEquals">
            <summary>
            不等于
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.MyOperatorName.Contain">
            <summary>
            包含
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.MyOperatorName.NotContain">
            <summary>
            不包含
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.MyOperatorName.GreaterThan">
            <summary>
            大于
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.MyOperatorName.NotGreaterThan">
            <summary>
            不大于
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.MyOperatorName.LessThan">
            <summary>
            小于
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.MyOperatorName.NotLessThan">
            <summary>
            不小于
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.MyOperatorName.Like">
            <summary>
            匹配
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.MyOperatorName.NotLike">
            <summary>
            不匹配
            </summary>
        </member>
        <member name="T:PlatCommon.Base01.MyFilterCondition">
            <summary>
            运算条件行
            </summary>
        </member>
        <member name="P:PlatCommon.Base01.MyFilterCondition.LogicType">
            <summary>
            运算类型
            </summary>
        </member>
        <member name="P:PlatCommon.Base01.MyFilterCondition.FieldName">
            <summary>
            字段名
            </summary>
        </member>
        <member name="P:PlatCommon.Base01.MyFilterCondition.OpName">
            <summary>
            操作名
            </summary>
        </member>
        <member name="P:PlatCommon.Base01.MyFilterCondition.fieldValue">
            <summary>
            字段值
            </summary>
        </member>
        <member name="P:PlatCommon.Base01.MyFilterCondition.FilterConditions">
            <summary>
            过滤条件
            </summary>
        </member>
        <member name="T:PlatCommon.Base01.Cs01FilterList`1">
            <summary>
            过滤用List类 用于过滤数据与分组汇总
            </summary>
        </member>
        <member name="P:PlatCommon.Base01.Cs01FilterList`1.FilterConditions">
            <summary>
            过滤条件List集合, 支持()And(Or)复杂查询， 
            目前接口：只支持两级条件， 条件只支持增加，如果需要改变，请重新设置FilterConditions。
            条件过滤并不限级数，超过两级，请自建条件集合。
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FilterList`1.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01FilterList`1.Data">
            <summary>
            对象数据List
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FilterList`1.IsFilterData(`0)">
            <summary>
            判断数据类实例是否符合过滤条件
            </summary>
            <param name="t"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FilterList`1.IsFilterDataEx(`0,System.Collections.Generic.List{PlatCommon.Base01.MyFilterCondition})">
            <summary>
            
            </summary>
            <param name="t"></param>
            <param name="FilterConditions"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FilterList`1.AddCondition(PlatCommon.Base01.MyLogicType)">
            <summary>
            增加一个子条件集合， 返回条件号, 增加子条件是用此返回行号
            </summary>
            <param name="logicType"></param>
            <returns>条件行号</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FilterList`1.AddCondition(System.String,PlatCommon.Base01.MyOperatorName,System.String,PlatCommon.Base01.MyLogicType)">
            <summary>
            增加过滤条件行，logicType第一行也需要， 计算时如果第一行logicType为Add，会与true运算，logicType为Or， 会与false运算
            </summary>
            <param name="strFieldName">字段名称 string</param>
            <param name="OpName">操作符 枚举MyOperatorName</param>
            <param name="strFieldValue">字段值 string</param>
            <param name="logicType">条件行之间的逻辑运算符 枚举MyLogicType</param>
            <returns>条件行号</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FilterList`1.AddCondition(System.Int32,System.String,PlatCommon.Base01.MyOperatorName,System.String,PlatCommon.Base01.MyLogicType)">
            <summary>
            增加过滤条件行子集合条件行(目前只支持一级子集合)， 增加子集合时，应先调用AddCondition(MyLogicType logicType)获得行号index
            </summary>
            <param name="iRowNo">行号</param>
            <param name="strFieldName">字段名称 string</param>
            <param name="OpName">操作符 枚举MyOperatorName</param>
            <param name="strFieldValue">字段值 string</param>
            <param name="logicType">条件行之间的逻辑运算符 枚举MyLogicType</param>
            <returns>条件行号</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FilterList`1.GetFilterData">
            <summary>
            获取符合过滤条件的记录
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FilterList`1.myCompare(`0,`0)">
            <summary>
            比较两个对象是否相同
            </summary>
            <param name="t1"></param>
            <param name="t2"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FilterList`1.GetGroupData(System.Collections.Generic.List{`0},System.String[],System.String[])">
            <summary>
            按列分组汇总，生成新的数据, 如果没有指定(null), 则使用类实例的Data
            </summary>
            <param name="Data">待分组数据List</param>
            <param name="strArrFields">分组字段列表 string[]</param>
            <param name="strArrSumFields">统计字段列表 string[]</param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Base01.Cs01FtpClientParameter">
            <summary>
            FTP客户端参数
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01FtpClientParameter.Host">
            <summary>
            主机名
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01FtpClientParameter.UserId">
            <summary>
            用户名
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01FtpClientParameter.Password">
            <summary>
            用户密码
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01FtpClientParameter.Port">
            <summary>
            端口号
            </summary>
        </member>
        <member name="T:PlatCommon.Base01.Cs01FtpClient">
            <summary>
            FTP客户端
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.#ctor(System.String,System.String,System.String,System.Int32)">
            <summary>
            构造函数
            </summary>
            <param name="strHost">主机名称</param>
            <param name="strUserId">用户名</param>
            <param name="strPassword">密码</param>
            <param name="iPort">端口号</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.getDataTableValue(System.Data.DataTable,PlatCommon.Base01.Cs01FtpClientParameter)">
            <summary>
            从数据表中读参数
            </summary>
            <param name="dtParameter">参数数据表</param>
            <param name="p">参数对象</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.#ctor(System.Data.DataTable)">
            <summary>
            构造函数
            </summary>
            <param name="dtParameter">参数数据表</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.getMyHost(System.String)">
            <summary>
            生成主机串
            </summary>
            <param name="strFtpAddr">FTP服务器地址</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.#ctor(System.String,System.String,System.String,System.Int32,System.Net.IWebProxy,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            创建FTP工具
            </summary>
            <param name="strHost">主机名称</param>
            <param name="strUserId">用户名</param>
            <param name="strPassword">密码</param>
            <param name="iPort">端口</param>
            <param name="enableSsl">允许Ssl</param>
            <param name="proxy">代理</param>
            <param name="useBinary">允许二进制</param>
            <param name="usePassive">允许被动模式</param>
        </member>
        <member name="F:PlatCommon.Base01.Cs01FtpClient._strUserId">
            <summary>
            主机
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01FtpClient.enableSsl">
            <summary>
            代理
            </summary>
        </member>
        <member name="P:PlatCommon.Base01.Cs01FtpClient.EnableSsl">
            <summary>
            EnableSsl 
            </summary>
        </member>
        <member name="P:PlatCommon.Base01.Cs01FtpClient.UsePassive">
            <summary>
            被动模式
            </summary>
        </member>
        <member name="P:PlatCommon.Base01.Cs01FtpClient.UseBinary">
            <summary>
            二进制方式
            </summary>
        </member>
        <member name="P:PlatCommon.Base01.Cs01FtpClient.RemotePath">
            <summary>
            远端路径
            <para>
                返回FTP服务器上的当前路径(可以是 / 或 /a/../ 的形式)
            </para>
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.CanConnected">
            <summary>
            连接判断参数是否设置正确或者FTP服务器是否启用
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.CreateRequest(System.String,System.String)">
            <summary>
            创建一个FTP请求
            </summary>
            <param name="url">请求地址</param>
            <param name="method">请求方法</param>
            <returns>FTP请求</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.Upload(System.IO.FileInfo,System.String)">
            <summary>
            把文件上传到FTP服务器的RemotePath下
            </summary>
            <param name="localFile">本地文件信息</param>
            <param name="remoteFileName">要保存到的文件名称包含扩展名</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.Upload(System.String,System.String)">
            <summary>
            把文件内容上传到FTP服务器的RemotePath下的文件中
            </summary>
            <param name="fileText">文件内容</param>
            <param name="remoteFileName">要保存到的文件名称包含扩展名</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.Download(System.String,System.String)">
            <summary>
            从当前目录下下载文件
            如果本地文件存在,则从本地文件结束的位置开始下载.
            </summary>
            <param name="strServerFileName">服务器上的文件名称</param>
            <param name="strLocalFileName">本地文件名称</param>
            <returns>返回一个值,指示是否下载成功</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.Rename(System.String,System.String)">
            <summary>
            文件更名
            </summary>
            <param name="strOldFileName">原文件名</param>
            <param name="strNewFileName">新文件名</param>
            <returns>返回一个值,指示更名是否成功</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.GetFileList">
            <summary>
            获取当前目录下文件列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.GetFileDetails">
            <summary>
            获取详细列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.DeleteFile(System.String)">
            <summary>
            删除FTP服务器上的文件
            </summary>
            <param name="strFileName">文件名称</param>
            <returns>返回一个值,指示是否删除成功</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.MakeDirectory(System.String)">
            <summary>
            在当前目录下创建文件夹
            </summary>
            <param name="strDirName">文件夹名称</param>
            <returns>返回一个值,指示是否创建成功</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.DeleteDirectory(System.String)">
            <summary>
            删除文件夹（只能是空文件夹）
            </summary>
            <param name="strDirName">文件夹名称</param>
            <returns>返回一个值,指示是否删除成功</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.DeletetTreeDirectory(System.String)">
            <summary>
            删除文件夹（包括文件与子文件夹）
            </summary>
            <param name="strDirName">文件夹名称</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.GetFileSize(System.String)">
            <summary>
            获取文件大小
            </summary>
            <param name="strFileName">文件名称</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.Append(System.IO.FileInfo,System.String)">
            <summary>
            给FTP服务器上的文件追加内容
            </summary>
            <param name="localFile">本地文件</param>
            <param name="strRemoteFileName">FTP服务器上的文件</param>
            <returns>返回一个值,指示是否追加成功</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.Append(System.IO.Stream,System.String)">
            <summary>
            给FTP服务器上的文件追加内容
            </summary>
            <param name="stream">数据流(可通过设置偏移来实现从特定位置开始上传)</param>
            <param name="strRemoteFileName">FTP服务器上的文件</param>
            <returns>返回一个值,指示是否追加成功</returns>
        </member>
        <member name="P:PlatCommon.Base01.Cs01FtpClient.CurrentDirectory">
            <summary>
            获取FTP服务器上的当前路径
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.CheckFileExist(System.String)">
            <summary>
            检查文件是否存在
            </summary>
            <param name="strFileName">要检查的文件名</param>
            <returns>返回一个值,指示要检查的文件是否存在</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.RemoteFtpDirExists(System.String)">
            <summary>
            判断ftp上的文件目录是否存在
            </summary>
            <param name="path">文件目录</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient._DirectoryExist(System.String)">
            <summary>
            判断文件夹是否存在
            </summary>
            <param name="strRemotePath"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.ParseDirectoryName(System.String)">
            <summary>
            解析文件夹名称
            </summary>
            <param name="line">FTP数据行</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01FtpClient.ParseFileName(System.String)">
            <summary>
            解析文件名称
            </summary>
            <param name="line">FTP数据行</param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Base01.Cs01Functions">
            <summary>
            静态基本类全局函数
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.CInt``1(``0)">
            <summary>
            强制转换为Int
            </summary>
            <typeparam name="T"></typeparam>
            <param name="t"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.CLng``1(``0)">
            <summary>
            强制转换为Long
            </summary>
            <typeparam name="T"></typeparam>
            <param name="t"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.CDecimal``1(``0)">
            <summary>
            强制转换为Decimal
            </summary>
            <typeparam name="T"></typeparam>
            <param name="t"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.CDbl``1(``0)">
            <summary>
            强制转换为Double
            </summary>
            <typeparam name="T"></typeparam>
            <param name="t"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.CDate``1(``0)">
            <summary>
            强制转换为DateTime
            </summary>
            <typeparam name="T"></typeparam>
            <param name="t"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.CBool``1(``0)">
            <summary>
            强制转换为Boolean
            </summary>
            <typeparam name="T"></typeparam>
            <param name="t"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.CStr``1(``0)">
            <summary>
            强制转换为String
            </summary>
            <typeparam name="T"></typeparam>
            <param name="t"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.CStr``1(``0,System.Int32)">
            <summary>
            强制转换为指定长度的String
            </summary>
            <typeparam name="T"></typeparam>
            <param name="t"></param>
            <param name="iLen"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.CStrEx``1(``0)">
            <summary>
            
            </summary>
            <typeparam name="T"></typeparam>
            <param name="t"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.CStrEx``1(``0,System.Int32)">
            <summary>
            
            </summary>
            <typeparam name="T"></typeparam>
            <param name="t"></param>
            <param name="len"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.GetNvarcharLength(System.String)">
            <summary>
            取字符串长度
            </summary>
            <param name="strSource"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.GetVarcharLengthStr(System.String,System.Int32)">
            <summary>
            返回指定长度的串
            </summary>
            <param name="strSource">源串</param>
            <param name="iLen">长度</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.Trim(System.String)">
            <summary>
            去掉字符串中的空格
            </summary>
            <param name="strSource">源串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.ParseInt(System.String)">
            <summary>
            数字串转换为Int
            </summary>
            <param name="strSource">源串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.IsAllNumber(System.String)">
            <summary>
            判断一个输入字符串是否完全是数字，null和空字符串会返回false
            </summary>
            <param name="strSource">输入字符串</param>
            <returns>true表示合法</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.IsAscIIString(System.String)">
            <summary>
            判断字符串是否是ASCII码字符串
            </summary>
            <param name="strSource">字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.IsMail(System.String)">
            <summary>
            判断是否是Email
            </summary>
            <param name="strSource">字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.IsMobile(System.String)">
            <summary>
            判断是否是手机号
            </summary>
            <param name="strSource">输入字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.IsMobile(System.Int64)">
            <summary>
            判断是否是手机号
            </summary>
            <param name="mobile">输入数字</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.IsVariableString(System.String)">
            <summary>
            判断字符串是否是申明变量要求的字符串
            </summary>
            <param name="strSource">输入字符串</param>
            <returns>true:是， false：否</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.Random(System.Int32)">
            <summary>
            随机生成长度为N的字符串，每个字符都随机生成
            </summary>
            <param name="n">长度</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.RandomInt(System.Int32)">
            <summary>
            随机生成长度为N的字符串，随机一个整数串
            </summary>
            <param name="iLength">长度</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.SqlDateStr(System.DateTime)">
            <summary>
            将DateTime转为Oracle SQL中的时间串
            </summary>
            <param name="dtmDatetime">DateTime时间</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.SqlDateStr(System.Object)">
            <summary>
            将DateTime转为Oracle SQL中的时间串
            </summary>
            <param name="dateObj">对象</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.DateStr(System.DateTime)">
            <summary>
            格式化Date
            </summary>
            <param name="dtmDatetime">DateTime日期时间</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.DateTimeStr(System.DateTime)">
            <summary>
            格式化DateTime
            </summary>
            <param name="dtmDatetime">DateTime日期时间</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.Now">
            <summary>
            当前时间串yyyy-MM-dd HH:mm:ss
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.Today">
            <summary>
            当前日期串yyyy-MM-dd
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.GetTimeStamp(System.DateTime)">
            <summary>
            返回时间戳
            </summary>
            <param name="d">DateTime日期时间</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.GetUnixTimeStamp(System.DateTime)">
            <summary>
            Unix时间戳
            </summary>
            <param name="d">DateTime日期时间</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.BigIntToTimestamp(System.Int64)">
            <summary>
            
            </summary>
            <param name="i64">64位整数</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.IntToTime(System.Int32)">
            <summary>
            整数转换为时间
            </summary>
            <param name="totalSeconds">总秒数</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.TimeOf">
            <summary>
            当前时间转为整数
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.TimeOf(System.DateTime)">
            <summary>
            指定时间转为整数
            </summary>
            <param name="t">DateTime日期时间</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.OracleTime">
            <summary>
            当前时间转换Oracle时间(去掉毫秒)
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.OracleTime(System.DateTime)">
            <summary>
            指定时间转换Oracle时间(去掉毫秒)
            </summary>
            <param name="d">DateTime日期时间</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.StringToDict(System.String,System.String,System.String)">
            <summary>
            字符串转换为字典
            </summary>
            <param name="strSource">字符串</param>
            <param name="strRowDeli">行分隔串</param>
            <param name="strColDeli">列分隔串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.StringToDict(System.String,System.Char,System.Char)">
            <summary>
            字符串转换为字典
            </summary>
            <param name="strSource">字符串</param>
            <param name="cRowDeli">行分隔符</param>
            <param name="cColDeli">列分隔符</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.StringToDictInt(System.String,System.Char,System.Char)">
            <summary>
            字符串转换为字典
            </summary>
            <param name="strSource">字符串</param>
            <param name="cRowDeli">行分隔符</param>
            <param name="cColDeli">列分隔符</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.GetDeditCredit">
            <summary>
            取借贷字典
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.DictionaryToString``2(System.Collections.Generic.Dictionary{``0,``1},System.String,System.String)">
            <summary>
            字典转换为字符串
            </summary>
            <typeparam name="TKey"></typeparam>
            <typeparam name="TValue"></typeparam>
            <param name="dic">字典</param>
            <param name="strRowDeli">行分隔串</param>
            <param name="strColDeli">列分隔串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.DictionaryToString(System.Collections.IDictionary,System.String,System.String)">
            <summary>
            字典转换为字符串
            </summary>
            <param name="dic">字典</param>
            <param name="strRowDeli">行分隔串</param>
            <param name="strColDeli">列分隔串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.ConvertCodeString(System.String,System.String,System.String)">
            <summary>
            各种字符串之间的转换
            </summary>
            <param name="strSource">源串</param>
            <param name="charsetFrom">源字符集</param>
            <param name="charsetTo">目的字符集</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.Utf8ToGBK(System.String)">
            <summary>
            Utf8转换为GBK
            </summary>
            <param name="utf8">Utf8串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.GetHostName">
            <summary>
            获得本机电脑名称
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.GetIP">
            <summary>
            获得本机电脑IP
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.GetLocalMac">
            <summary>
            获取本机Mac地址
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.RepeatString(System.String,System.Int32)">
            <summary>
            字符串重复
            </summary>
            <param name="strSource">源字符串</param>
            <param name="iNum">重复次数</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.StringToIntArray(System.String)">
            <summary>
            逗号分隔的数字字符串转换为整数数组
            </summary>
            <param name="strInts">逗号分隔的数字字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.SqlEncode(System.String)">
            <summary>
            SQL串格式化
            </summary>
            <param name="strSQL"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.ReadFileBinary(System.String)">
            <summary>
            读文件到byte[]
            </summary>
            <param name="strFileFullName">全路径文件名</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.PraseDataSourceString(System.String)">
            <summary>
            分析数据库联接字符串
            </summary>
            <param name="strValue">字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.SortedKeyValue(System.String,System.Char,System.Char)">
            <summary>
            字符串转为表
            </summary>
            <param name="strSource">源串</param>
            <param name="cColumn">列分隔符</param>
            <param name="cRow">行分隔符</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.SortedKeyValue(System.Collections.Generic.SortedDictionary{System.String,System.String},System.Char,System.Char)">
            <summary>
            DIC转为字符串
            </summary>
            <param name="d"></param>
            <param name="cColumn">列分隔符</param>
            <param name="cRow">行分隔符</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.SortedKeyValue(System.Collections.Generic.Dictionary{System.String,System.String},System.Char,System.Char)">
            <summary>
            
            </summary>
            <param name="d"></param>
            <param name="colchar"></param>
            <param name="rowchar"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.JsonString(System.String)">
            <summary>
            Json串编码
            </summary>
            <param name="strKeyVal">值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.EncodeJsonValue(System.String)">
            <summary>
            Json串编码转义
            </summary>
            <param name="strSource">源串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.HtmlDecode(System.String)">
            <summary>
            Html串转义
            </summary>
            <param name="strSource">源串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.StringToStringArray(System.String)">
            <summary>
            字符串按逗号转字符串数组
            </summary>
            <param name="strSource">源串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.InSQLStringDecimal(System.String,System.Char)">
            <summary>
            
            </summary>
            <param name="idsstr"></param>
            <param name="deli"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.GetIdsTable(System.String)">
            <summary>
            
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.GetIdsTable(System.String,System.Char)">
            <summary>
            
            </summary>
            <param name="ids"></param>
            <param name="deli"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.GetIdsTable(System.String[])">
            <summary>
            
            </summary>
            <param name="idarr"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.HexStringToBytes(System.String)">
            <summary>
            16进制字符串转化为字节数组
            </summary>
            <param name="hexstr"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.HexStringToBytes(System.String,System.Char)">
            <summary>
            
            </summary>
            <param name="hexstr"></param>
            <param name="seperator"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.BytesToHexString(System.Byte[])">
            <summary>
            
            </summary>
            <param name="bits"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.BytesToHexString(System.Byte[],System.Char)">
            <summary>
            
            </summary>
            <param name="bits"></param>
            <param name="seperator"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.StringToHexString(System.String,System.String)">
            <summary>
            从汉字转换到16进制
            </summary>
            <param name="strSource">源串</param>
            <param name="charset">字符集</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.StringToHexString(System.String,System.Text.Encoding)">
            <summary>
            字符串转化为16进制字符串
            </summary>
            <param name="strSource">源串</param>
            <param name="encode">字符编码</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.HexStringToString(System.String,System.Text.Encoding)">
            <summary>
            16进制字符串转化为字符串
            </summary>
            <param name="strHexSource">16进制源串</param>
            <param name="encode">字符编码</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.HexStringToString(System.String,System.String)">
            <summary>
            16进制字符串转化为字符串
            </summary>
            <param name="strHexSource">16进制源串</param>
            <param name="charset">字符集</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Functions.ToMoneyStr``1(``0)">
            <summary>
            金额小写转中文大写。
            整数支持到万亿；小数部分支持到分(超过两位将进行Banker舍入法处理)
            </summary>
            <param name="value">需要转换的双精度浮点数</param>
            <returns>转换后的字符串</returns>
        </member>
        <member name="T:PlatCommon.Base01.Struct2Bytes">
            <summary>
            结构体与byte数组互转
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Struct2Bytes.StructToBytes(System.Object)">
            <summary>
            结构体转byte数组
            </summary>
            <param name="structObj">要转换的结构体</param>
            <returns>转换后的byte数组</returns>
        </member>
        <member name="M:PlatCommon.Base01.Struct2Bytes.BytesToStruct(System.Byte[],System.Type)">
            <summary>
            byte数组转结构体
            </summary>
            <param name="bytes">byte数组</param>
            <param name="type">结构体类型</param>
            <returns>转换后的结构体</returns>
        </member>
        <member name="M:PlatCommon.Base01.Struct2Bytes.BytesToStruct``1(System.Byte[])">
            <summary>
            byte数组转结构体
            </summary>
            <typeparam name="T">结构体类型</typeparam>
            <param name="bytes">byte数组</param>
            <returns>转换后的结构体</returns>
        </member>
        <member name="T:PlatCommon.Base01.Cs01IniFile">
            <summary>
            IniFile 的摘要说明。
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01IniFile.VALUE_MAX_LEN">
            <summary>
            Ini文件中值的最大长度
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01IniFile._configFile">
            <summary>
            配置文件
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01IniFile.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01IniFile.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="strFileName">文件名</param>
        </member>
        <member name="P:PlatCommon.Base01.Cs01IniFile.FileName">
            <summary>
            Ini文件名
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01IniFile.GetPrivateProfileInt(System.String,System.String,System.Int32,System.String)">
            <summary>
            获取Ini文件中的数字值
            </summary>
            <param name="lpAppName">段名</param>
            <param name="lpKeyName">键名</param>
            <param name="nDefault">默认值</param>
            <param name="lpFileName">Ini文件</param>
            <returns>Ini文件中设置的值</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01IniFile.GetPrivateProfileString(System.String,System.String,System.String,System.Text.StringBuilder,System.Int32,System.String)">
            <summary>
            获取Ini文件中的字符串
            </summary>
            <param name="lpAppName">段名</param>
            <param name="lpKeyName">键名</param>
            <param name="lpDefault">默认值</param>
            <param name="lpReturnedString">存放返回值的缓冲区</param>
            <param name="nSize">缓冲区大小</param>
            <param name="lpFileName">Ini文件</param>
            <returns>Ini文件中设置的值</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01IniFile.WritePrivateProfileString(System.String,System.String,System.String,System.String)">
            <summary>
            设置Ini文件中的值
            </summary>
            <param name="lpAppName">段名</param>
            <param name="lpKeyName">键名</param>
            <param name="lpString">值</param>
            <param name="lpFileName">Ini文件</param>
            <returns>TRUE: 成功; FALSE: 失败</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01IniFile.ReadInt(System.String,System.String,System.Int32)">
            <summary>
            读Ini文件中的Int值
            </summary>
            <param name="strSection">段名</param>
            <param name="strKey">键名</param>
            <param name="iDefValue">默认值</param>
            <returns>Ini文件中设置的值</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01IniFile.ReadString(System.String,System.String,System.String)">
            <summary>
            读Ini文件中的字符串值
            </summary>
            <param name="strSection">段名</param>
            <param name="strKey">键名</param>
            <param name="strDefValue">默认值</param>
            <returns>Ini文件中设置的值</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01IniFile.ReadStringByFile(System.String,System.String,System.String,System.String)">
            <summary>
            读Ini文件中的字符串值
            </summary>
            <param name="strSection">段名</param>
            <param name="strKey">键名</param>
            <param name="strDefValue">默认值</param>
            <param name="strFileName">文件名</param>
            <returns>Ini文件中设置的值</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01IniFile.WriteString(System.String,System.String,System.String)">
            <summary>
            写Ini文件
            </summary>
            <param name="strSection">段名</param>
            <param name="strKey">键名</param>
            <param name="strVal">要写入的字符串</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01IniFile.DelKey(System.String,System.String)">
            <summary>
            删除键
            </summary>
            <param name="strSection">段名</param>
            <param name="strKey">键名</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01IniFile.DelSection(System.String)">
            <summary>
            删除段
            </summary>
            <param name="strSection">段名</param>
        </member>
        <member name="T:PlatCommon.Base01.Cs01MoneryToChinese">
            <summary>
            金额数字转汉字大写
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01MoneryToChinese.ToChinese``1(``0)">
            <summary>
            转换程序
            </summary>
            <typeparam name="T">类型</typeparam>
            <param name="value">值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01MoneryToChinese.ConvertXaioshu(System.String)">
            <summary>
            小数部分转换
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01MoneryToChinese.FindNextNotZero(System.String,System.Int32)">
            <summary>
            找到第一个非零数字的位置，计数位置是从右到左依次增大,起始位为1， 查找是从左往右找
            </summary>
            <param name="value"></param>
            <param name="startIndex"></param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Base01.Cs01Pdf">
            <summary>
            PDF类
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Pdf.PrintPdfFile(System.String,System.String)">
            <summary>
            打印PDF文件
            </summary>
            <param name="strFileName">PDF文件名</param>
            <param name="strPrinterName">打印机</param>
        </member>
        <member name="T:PlatCommon.Base01.Cs01PhoneticWubi">
            <summary>
            生成字符串的拼音码和五笔码
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01PhoneticWubi.PhoneticCode(System.String)">
            <summary>
            生成字符串的拼音码
            </summary>
            <param name="input">输入串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01PhoneticWubi.PhoneticCode(System.String,System.Int32)">
            <summary>
            生成不超过固定长度的字符串拼音码
            </summary>
            <param name="input"></param>
            <param name="len"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01PhoneticWubi.WubiCode(System.String)">
            <summary>
            生成字符串的五笔码
            </summary>
            <param name="input">输入串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01PhoneticWubi.WubiCode(System.String,System.Int32)">
            <summary>
            生成不超过固定长度的字符串五笔码
            </summary>
            <param name="input">输入串</param>
            <param name="len">长度</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01PhoneticWubi.GetStrPinyin(System.String)">
            <summary>汉字转换成全拼的拼音</summary>
            <param name="strChstr">汉字字符串</param>
            <returns>转换后的拼音字符串</returns> 
        </member>
        <member name="T:PlatCommon.Base01.Cs01Security">
            <summary>
            加密解密类
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Security.MD5Encrypt(System.String)">
            <summary>
            MD5加密
            </summary>
            <param name="strSource">原文</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Security.DESEncrypt(System.String,System.String,System.String,System.Security.Cryptography.CipherMode,System.Int32)">
            <summary>
            DES加密
            </summary>
            <param name="strSource">原文</param>
            <param name="strKey">key</param>
            <param name="IV">偏移量,使用ECB模式这里留空</param>
            <param name="Mode">CipherMode加密模式，Java 默认的是ECB模式，PKCS5 padding；C#默认的是CBC模式，PKCS7 padding </param>
            <param name="iPad">填充形式，0- PKCS7，1- ANSIX923，2-ISO10126，3-None，4-Zeros </param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Security.DESDecrypt(System.String,System.String,System.String,System.Security.Cryptography.CipherMode,System.Int32)">
            <summary>
            DES解密
            </summary>
            <param name="strEnMessage">密文</param>
            <param name="strKey">key</param>
            <param name="IV">偏移量,使用ECB模式这里留空</param>
            <param name="Mode">CipherMode加密模式，可以设为CipherMode.ECB</param>
            <param name="iPad">填充形式，0- PKCS7，1- ANSIX923，2-ISO10126，3-None，4-Zeros </param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Security.HexStringToByteArray(System.String)">
            <summary>
            Hex转为Byte
            </summary>
            <param name="strSource"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Security.ByteArrayToHexString(System.Byte[])">
            <summary>
            Byte转换为Hex
            </summary>
            <param name="bArrData"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Security.SHA1(System.String)">
            <summary>
            取字符串哈希值
            </summary>
            <param name="strSource"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Security.GetRandonKey(System.Int32)">
            <summary>
            产生n位包含数字与大小字母的随机字符串
            </summary>
            <param name="n"></param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Base01.Cs01AES">
            <summary>
            AES加密类
            </summary>
        </member>
        <member name="T:PlatCommon.Base01.Cs01AES.His00AESBits">
            <summary>
            
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01AES.His00AESBits.BITS128">
            <summary>
            16位
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01AES.His00AESBits.BITS192">
            <summary>
            24位
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01AES.His00AESBits.BITS256">
            <summary>
            32位
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01AES.#ctor">
            <summary>
            
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01AES.#ctor(System.String,PlatCommon.Base01.Cs01AES.His00AESBits)">
            <summary>
            Initialize new AESEncryptor.
            </summary>
            <param name="password">The password to use for encryption/decryption.</param>
            <param name="encryptionBits">Encryption bits (128,192,256).</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01AES.#ctor(System.String,PlatCommon.Base01.Cs01AES.His00AESBits,System.Byte[])">
            <summary>
            Initialize new AESEncryptor.
            </summary>
            <param name="password">The password to use for encryption/decryption.</param>
            <param name="encryptionBits">Encryption bits (128,192,256).</param>
            <param name="salt">Salt bytes. Bytes length must be 15.</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01AES.iEncrypt(System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            
            </summary>
            <param name="data"></param>
            <param name="key"></param>
            <param name="iV"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01AES.Encrypt(System.String)">
            <summary>
            Encrypt string with AES algorith.
            </summary>
            <param name="data">String to encrypt.</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01AES.Encrypt(System.Byte[])">
            <summary>
            Encrypt byte array with AES algorithm.
            </summary>
            <param name="data">Bytes to encrypt.</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01AES.Decrypt(System.String)">
            <summary>
            Decrypt string with AES algorithm.
            </summary>
            <param name="data">Encrypted string.</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01AES.Decrypt(System.Byte[])">
            <summary>
            Decrypt byte array with AES algorithm.
            </summary>
            <param name="data">Encrypted byte array.</param>
        </member>
        <member name="P:PlatCommon.Base01.Cs01AES.Password">
            <summary>
            Encryption/Decryption password.
            </summary>
        </member>
        <member name="P:PlatCommon.Base01.Cs01AES.EncryptionBits">
            <summary>
            Encryption/Decryption bits.
            </summary>
        </member>
        <member name="P:PlatCommon.Base01.Cs01AES.Salt">
            <summary>
            Salt bytes (bytes length must be 15).
            </summary>
        </member>
        <member name="T:PlatCommon.Base01.Cs01RSA">
            <summary>
            RSA加密类
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01RSA.#ctor(System.String,System.String)">
            <summary>
            RSA加密
            </summary>
            <param name="strPrivateKey">私钥匙</param>
            <param name="strPublicKey">公钥匙</param>
        </member>
        <member name="M:PlatCommon.Base01.Cs01RSA.Decrypt(System.String)">
            <summary>
            解密
            </summary>
            <param name="cipherText"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01RSA.Encrypt(System.String)">
            <summary>
            加密
            </summary>
            <param name="text"></param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Base01.Cs01ShowInfo">
            <summary>
            显示信息类
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01ShowInfo.GetFriendlyInfo(System.String)">
            <summary>
            取字段信息
            </summary>
            <param name="strFieldName">字段名称</param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Base01.Cs01StringExtend">
            <summary>
            扩展字符串类
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01StringExtend.StringToUnicode(System.String)">
            <summary>
            字符串转UNICODE代码
            </summary>
            <param name="strSource">源字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01StringExtend.UnicodeToString(System.String)">
            <summary>  
            Unicode字符串转为正常字符串  
            </summary>  
            <param name="strSource">源Unicode字符串</param>  
            <returns></returns>  
        </member>
        <member name="T:PlatCommon.Base01.Cs01Win32API">
            <summary>
            功能描述：WIN32 API接口
            </summary>	
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.MSG_USER">
            <summary>
            用户自定义消息
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.MSG_EXIT_APP">
            <summary>
            退出应用程序
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.MSG_PATIENT">
            <summary>
            病人消息
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.WM_COPYDATA">
            <summary>
            
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.WM_LBUTTONDOWN">
            <summary>
            Window消息
            </summary> 
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.SW_HIDE">
            <summary>
            隐藏
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.SW_SHOWNORMAL">
            <summary>
            正常
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.SW_SHOWMINIMIZED">
            <summary>
            最小化
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.SW_SHOWMAXIMIZED">
            <summary>
            最大化
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.SW_SHOWNOACTIVATE">
            <summary>
            SHOWNOACTIVATE
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.SW_RESTORE">
            <summary>
            RESTORE
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.SW_SHOWDEFAULT">
            <summary>
            默认
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.EM_GETLINECOUNT">
            <summary>
            获取文本框总行数
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.EM_GETSEL">
            <summary>
            获取编辑框一段选定内容的起点与终点字符的个数
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.EM_LINEINDEX">
            <summary>
            每一行第一字符在全文中的字符序号
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.EM_LINEFROMCHAR">
            <summary>
            获取光标所在行数
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.EM_GETLINE">
            <summary>
            获取一行的内容
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.SendMessageA(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            将指定的消息发送到一个或多个窗口,此函数为指定的窗口调用窗口过程，直到窗口过程处理完消息后才返回
            </summary>
            <param name="hwnd">目标窗口句柄</param>
            <param name="wMsg">被发送的消息</param>
            <param name="wParam">第一个消息参数</param>
            <param name="lParam">第二个消息参数</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.SendMessage(System.Int32,System.Int32,System.Int32,System.Text.StringBuilder)">
            <summary>
            将指定的消息发送到一个或多个窗口,此函数为指定的窗口调用窗口过程，直到窗口过程处理完消息后才返回
            </summary>
            <param name="hwnd">目标窗口句柄</param>
            <param name="wMsg">被发送的消息</param>
            <param name="wParam">第一个消息参数</param>
            <param name="lParam">第二个消息参数</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.SendMessage(System.Int32,System.Int32,System.Int32,PlatCommon.Base01.Cs01Win32API.COPYDATASTRUCT@)">
            <summary>
            
            </summary>
            <param name="hwnd"></param>
            <param name="msg"></param>
            <param name="wParam"></param>
            <param name="IParam"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.PostMessage(System.IntPtr,System.Int32,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="hWnd"></param>
            <param name="Msg"></param>
            <param name="wParam"></param>
            <param name="lParam"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.PostMessage(System.IntPtr,System.Int32,System.Int32,PlatCommon.Base01.Cs01Win32API.COPYDATASTRUCT@)">
            <summary>
            
            </summary>
            <param name="hWnd"></param>
            <param name="Msg"></param>
            <param name="wParam"></param>
            <param name="lParam"></param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Base01.Cs01Win32API.SystemTime">
            <summary>
            日期时间
            </summary> 
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.SystemTime.year">
            <summary>
            年
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.SystemTime.month">
            <summary>
            月
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.SystemTime.dayofweek">
            <summary>
            星期几
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.SystemTime.day">
            <summary>
            日
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.SystemTime.hour">
            <summary>
            小时
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.SystemTime.minute">
            <summary>
            分
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.SystemTime.second">
            <summary>
            秒
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.SystemTime.milliseconds">
            <summary>
            毫秒
            </summary>
        </member>
        <member name="T:PlatCommon.Base01.Cs01Win32API.LASTINPUTINFO">
            <summary>
            最后输入信息
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.LASTINPUTINFO.cbSize">
            <summary>
            
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.LASTINPUTINFO.dwTime">
            <summary>
            
            </summary>
        </member>
        <member name="T:PlatCommon.Base01.Cs01Win32API.COPYDATASTRUCT">
            <summary>
            使用COPYDATASTRUCT来传递字符串
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.COPYDATASTRUCT.dwData">
            <summary>
            
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.COPYDATASTRUCT.cbData">
            <summary>
            
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.Cs01Win32API.COPYDATASTRUCT.lpData">
            <summary>
            
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.Beep(System.Int32,System.Int32)">
            <summary>
            让主板蜂鸣器持续发声
            </summary>
            <param name="dwFreq">频率的高低，越大越高</param>
            <param name="dwDuration">响的时间,单位为毫秒</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.SetSystemTime(PlatCommon.Base01.Cs01Win32API.SystemTime)">
            <summary>
            设定系统时间,默认设置的为UTC时间，比北京时间少了8个小时
            </summary>
            <param name="st">指向一个SYSTEMTIME数据结构.它接收当前系统的日期和时间</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.GetLastInputInfo(PlatCommon.Base01.Cs01Win32API.LASTINPUTINFO@)">
            <summary>
            获取空闲时间
            </summary>
            <param name="plii"></param>
            <returns></returns> 
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.GetLastInputTime">
            <summary>
            获取最后操作时间
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.FindWindow(System.String,System.String)">
            <summary>
            检索处理顶级窗口的类名和窗口名称匹配指定的字符串
            </summary>
            <param name="lpClassName">类名</param>
            <param name="lpWindowName">窗口名</param>
            <returns>如果函数执行成功，则返回值是拥有指定窗口类名或窗口名的窗口的句柄。如果函数执行失败，则返回值为 NULL </returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.ShowWindow(System.IntPtr,System.Int32)">
            <summary>
            显示窗体
            </summary>
            <param name="hwnd"></param>
            <param name="nCmdShow"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.ShowWindowAsync(System.IntPtr,System.Int32)">
            <summary>
            设置由不同线程产生的窗口的显示状态
            </summary>
            <param name="hWnd">窗口句柄</param>
            <param name="nCmdShow">指定窗口如何显示</param>
            <returns>如果函数原来可见，返回值为非零；如果函数原来被隐藏，返回值为零。</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.SetForegroundWindow(System.IntPtr)">
            <summary>
            将创建指定窗口的线程设置到前台，并且激活该窗口
            </summary>
            <param name="hWnd">将要设置前台的窗口句柄</param>
            <returns>如果窗口设入了前台，返回值为非零；如果窗口未被设入前台，返回值为零。</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.IsIconic(System.IntPtr)">
            <summary>
            确定窗口是否是最小化（图标化）
            </summary>
            <param name="hWnd">窗口的句柄 </param>
            <returns>如果窗口未最小化，返回值为零；如果窗口已最小化，返回值为非零</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.IsZoomed(System.IntPtr)">
            <summary>
            确定窗口是否是最大化
            </summary>
            <param name="hWnd">窗口的句柄</param>
            <returns>如果窗口己最大化，则返回值为非零；如果窗口未最大化，则返回值为零</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.GetForegroundWindow">
            <summary>
            获取一个前台窗口的句柄
            </summary>
            <returns>返回值是一个前台窗口的句柄。在某些情况下，如一个窗口失去激活时，前台窗口可以是NULL</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.GetWindowThreadProcessId(System.IntPtr,System.IntPtr)">
            <summary>
            找出某个窗口的创建者（线程或进程），返回创建者的标志符
            </summary>
            <param name="hWnd">被查找窗口的句柄.</param>
            <param name="ProcessId">进程号的存放地址（变量地址）</param>
            <returns>返回线程号，注意，lpdwProcessId 是存放进程号的变量。返回值是线程号，lpdwProcessId 是进程号存放处</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.ClientToScreen(System.IntPtr,System.Drawing.Point@)">
            <summary>
            该函数将指定点，或者矩形的用户坐标转换成屏幕坐标
            </summary>
            <param name="hWnd">用户区域用于转换的窗口句柄</param>
            <param name="lp">指向一个含有要转换的用户坐标的结构的指针，如果函数调用成功，新屏幕坐标复制到此结构</param>
            <returns>如果函数调用成功，返回值为非零值，否则为零</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.MessageBoxTimeout(System.IntPtr,System.String,System.String,System.Int32,System.Int32,System.Int32)">
            <summary>
            实现定时消息，功能类似于MessageBox。如果用户不回应，能定时关闭消息框。函数由user32.dll导出，windows2000及以下没有此函数
            </summary>
            <param name="hwnd">消息框的拥有窗口</param>
            <param name="txt">消息框的内容</param>
            <param name="caption">消息框的标题</param>
            <param name="wtype">指定一个决定对话框的内容和行为的位标志集。取值参照函数MessageBox的参数uType</param>
            <param name="wlange">函数扩展，一般取0</param>
            <param name="dwtimeout">消息框延迟关闭时间，单位：毫秒</param>
            <returns>参照函数MessageBox的返回值。如果超时，即用户未操作，消息框自动关闭，返回32000。</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.SetCursorPos(System.Int32,System.Int32)">
            <summary>
            把光标移到屏幕的指定位置。该光标是共享资源，仅当该光标在一个窗口的客户区域内时它才能移动该光标。
            </summary>
            <param name="x">指定光标的新的X坐标，以屏幕坐标表示</param>
            <param name="y">指定光标的新的Y坐标，以屏幕坐标表示</param>
            <returns>如果成功，返回非零值；如果失败，返回值是零，若想获得更多错误信息，请调用GetLastError函数</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.SetParent(System.IntPtr,System.IntPtr)">
            <summary>
            
            </summary>
            <param name="hWndChild"></param>
            <param name="hWndNewParent"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.GetPrivateProfileInt(System.String,System.String,System.Int32,System.String)">
            <summary>
            获取Ini文件中的数字值
            </summary>
            <param name="lpAppName">段名</param>
            <param name="lpKeyName">键名</param>
            <param name="nDefault">默认值</param>
            <param name="lpFileName">Ini文件</param>
            <returns>Ini文件中设置的值</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.GetPrivateProfileString(System.String,System.String,System.String,System.Text.StringBuilder,System.Int32,System.String)">
            <summary>
            获取Ini文件中的字符串
            </summary>
            <param name="lpAppName">段名</param>
            <param name="lpKeyName">键名</param>
            <param name="lpDefault">默认值</param>
            <param name="lpReturnedString">存放返回值的缓冲区</param>
            <param name="nSize">缓冲区大小</param>
            <param name="lpFileName">Ini文件</param>
            <returns>Ini文件中设置的值</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.WritePrivateProfileString(System.String,System.String,System.String,System.String)">
            <summary>
            设置Ini文件中的值
            </summary>
            <param name="lpAppName">段名</param>
            <param name="lpKeyName">键名</param>
            <param name="lpString">值</param>
            <param name="lpFileName">Ini文件</param>
            <returns>TRUE: 成功; FALSE: 失败</returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.SetDefaultPrinter(System.String)">
            <summary>
            调用win api将指定名称的打印机设置为默认打印机
            </summary>
            <param name="Name"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base01.Cs01Win32API.ClearMemory">
            <summary>
            释放内存
            </summary>
        </member>
        <member name="T:PlatCommon.Base01.GCCLass">
            <summary>
            通用静态类
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.GCCLass.Message">
            <summary>
            执行过程中需要传递的消息
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CStr(System.Object)">
            <summary>
            转换为字符
            </summary>
            <param name="obj">object</param>
            <returns>string</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CStrOrDefault(System.Object,System.String)">
            <summary>
            转换为字符
            </summary>
            <param name="obj">object</param>
            <param name="defValue">string</param>
            <returns>string</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CTrim(System.Object)">
            <summary>
            转换为字符串并删除前后空格
            </summary>
            <param name="obj">object</param>
            <returns>string</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CTrim(System.String)">
            <summary>
            null转化为转换为String.Empty，并删除前后空格
            </summary>
            <param name="strVal">string</param>
            <returns>string</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CInt(System.Object)">
            <summary>
            转换为整数
            </summary>
            <param name="obj">object</param>
            <returns>int</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CInt(System.String)">
            <summary>
            转换为整数
            </summary>
            <param name="strVal">string</param>
            <returns>int</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CDbl(System.Object)">
            <summary>
            转换为double
            </summary>
            <param name="obj">object</param>
            <returns>double</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CDbl(System.String)">
            <summary>
            转换为double
            </summary>
            <param name="strVal">string</param>
            <returns>int</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CDecimal(System.Object)">
            <summary>
            转换为decimal
            </summary>
            <param name="obj">object</param>
            <returns>decimal</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CDecimal(System.String)">
            <summary>
            转换为decimal
            </summary>
            <param name="strVal">string</param>
            <returns>int</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.Now">
            <summary>
            取精确到秒的当前时间
            </summary>
            <returns>DateTime</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CDateTime(System.Object)">
            <summary>
            转换为DateTime
            </summary>
            <param name="obj">object</param>
            <returns>为空时转为DateTime(0,1,1)</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CDateTimeOrDefaultNow(System.Object)">
            <summary>
            转换为DateTime
            </summary>
            <param name="obj">object</param>
            <returns>为空时转为DateTime.Now</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CDateTimeOrDefault(System.String,System.DateTime)">
            <summary>
            转换为DateTime
            </summary>
            <param name="str">string</param>
            <param name="defValue">DateTime</param>
            <returns>为空时转为defValue</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CDateTimeString(System.Object)">
            <summary>
            转化为日期字符串
            </summary>
            <param name="obj">object</param>
            <returns>格式: yyyy-MM-dd HH:mm:ss</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CDateString(System.Object)">
            <summary>
            转化为日期字符串
            </summary>
            <param name="obj">object</param>
            <returns>格式: yyyy-MM-dd</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CDateTimeString(System.Object,System.String)">
            <summary>
            转换为日期字符符，要求提供格式
            </summary>
            <param name="obj">object</param>
            <param name="fmt">格式字符串</param>
            <returns>string</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.CBool(System.Object)">
            <summary>
            转换为布尔值
            </summary>
            <param name="obj">object</param>
            <returns>bool</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.StringToDate(System.String)">
            <summary>
            字符串转换为日期
            </summary>
            <param name="timestr">格式：yyyy-MM-dd HH:mm:ss, 分隔符可以是任意，只占一个字符</param>
            <returns>DateTime</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.StringToDateEx(System.String)">
            <summary>
            字符串转换为日期
            </summary>
            <param name="timestr">格式：yyyyMMddHHmmss，没有分隔符</param>
            <returns>DateTime</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.EscapeRowFilter(System.String)">
            <summary>
            DataView.RowFilter字符串的转义处理
            </summary>
            <param name="strVal">string</param>
            <returns>转义处理后的字符串</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.GetAscIILength(System.String)">
            <summary>
            返回字符串的字节长度，对于AscII字符串长度为1，其它字符长度为2
            </summary>
            <param name="s">输入字符串</param>
            <returns>返回字符串的字节长度</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.GetAscIILength(System.String,System.Int32)">
            <summary>
            返回指定字节长度的字符串，对于AscII字符串长度为1，其它字符长度为2
            </summary>
            <param name="s">输入字符串</param>
            <param name="len">字节长度</param>
            <returns>返回指定字节长度的字符串</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.GetAscIILength(System.String,System.Int32,System.Int32@)">
            <summary>
            返回字节长度的字符串，对于AscII字符串长度为1，其它字符长度为2
            </summary>
            <param name="s">输入字符串</param>
            <param name="len">字节长度</param>
            <param name="length">返回字字符串的字节长度</param>
            <returns>返回字节长度的字符串</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.GetAscIIAlignText(System.String,System.Int32,System.Int32)">
            <summary>
            根据对齐方式，返回固定字节长度的字符串，对于AscII字符串长度为1，其它字符长度为2
            </summary>
            <param name="s">输入字符串</param>
            <param name="len">字节长度</param>
            <param name="align">对齐方式：1左对齐，2右对齐，3居中对齐</param>
            <returns>返回用空格填空的对应对齐方式固定长度的字符串</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.ReplaceAscIILength(System.String,System.Int32,System.Int32,System.String)">
            <summary>
            字段移去字节长度，遇开始位置和结束位置是中文的一个字节时，移掉一个中文，加入一个英文空格
            </summary>
            <param name="str">待替换字符串</param>
            <param name="startIndex">起始字节位置</param>
            <param name="byteLen">替换字节长度</param>
            <param name="replaceStr">替换字符串</param>
            <returns>替换后的字符串</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.RepeatStr(System.String,System.Int32)">
            <summary>
            获取重复字符串
            </summary>
            <param name="str">待重复的字符串</param>
            <param name="count">重复次数</param>
            <returns>字符串值</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.RepeatStr(System.Char,System.Int32)">
            <summary>
            获取重复字符串
            </summary>
            <param name="c">待重复的字符</param>
            <param name="count">重复次数</param>
            <returns>字符串值</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.RepeatStr(System.String,System.String,System.Int32)">
            <summary>
            获取重复字符串
            </summary>
            <param name="str">待重复的字符串</param>
            <param name="deli">中间连接字符串</param>
            <param name="count">重复次数</param>
            <returns>字符串值</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.IsCode128(System.String)">
            <summary>
            判断是否是128码字符，如果不是，可以读取Message信息
            </summary>
            <param name="s">string</param>
            <returns>true是，false否</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.IsNumeric(System.String)">
            <summary>
            判断是否是数据，判断方法:字符串转换为数字后，可逆转换为原来的字符串
            </summary>
            <param name="s">string</param>
            <returns>bool</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.FreeObject``1(``0@)">
            <summary>
            释放当前对象，变量置为null
            </summary>
            <typeparam name="T">泛型</typeparam>
            <param name="obj">泛型对象</param>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.StringToDict(System.String,System.String,System.String)">
            <summary>
            字符串转换为字典
            </summary>
            <param name="str">string</param>
            <param name="rowdeli">键值对分割字符串</param>
            <param name="coldeli">键值分割字符串</param>
            <returns>Dictionary&lt;string, string&gt;</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.StringToDict(System.String,System.Char,System.Char)">
            <summary>
            字符串转换为字典
            </summary>
            <param name="str">string</param>
            <param name="rowdeli">键值对分割符</param>
            <param name="coldeli">键值分割符</param>
            <returns>Dictionary&lt;string, string&gt;</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.StringToDictInt(System.String,System.Char,System.Char)">
            <summary>
            字符串转换为字典
            </summary>
            <param name="str">string</param>
            <param name="rowdeli">键值对分割符</param>
            <param name="coldeli">键值分割符</param>
            <returns>Dictionary&lt;string, int&gt;</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.DictionaryToString``2(System.Collections.Generic.Dictionary{``0,``1},System.String,System.String)">
            <summary>
            字典转换为字符串
            </summary>
            <typeparam name="TKey">键类</typeparam>
            <typeparam name="TValue">键类</typeparam>
            <param name="dic">Dictionary&lt;TKey, TValue&gt;</param>
            <param name="rowdeli">键值对分割字符串</param>
            <param name="coldeli">键值分割字符串</param>
            <returns>string</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.StringToList(System.String,System.Char)">
            <summary>
            字符串转换为List(string)
            </summary>
            <param name="str">string</param>
            <param name="deli">分割符</param>
            <returns>List&lt;string&gt;</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.StringToList(System.String,System.String)">
            <summary>
            字符串转换为List(string)
            </summary>
            <param name="str">string</param>
            <param name="deli">分割符</param>
            <returns>List&lt;string&gt;</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.StringToListInt(System.String,System.Char)">
            <summary>
            字符串转换为List(int)
            </summary>
            <param name="str">string</param>
            <param name="deli">分割符</param>
            <returns>List&lt;int&gt;</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.StringToListInt(System.String,System.String)">
            <summary>
            字符串转换为List(string)
            </summary>
            <param name="str">string</param>
            <param name="deli">分割符</param>
            <returns>List&lt;int&gt;</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.DictionaryToString(System.Collections.IDictionary,System.String,System.String)">
            <summary>
            字典IDictionary转换为字符串
            </summary>
            <param name="dic">IDictionary</param>
            <param name="rowdeli">键值对分割字符串</param>
            <param name="coldeli">键值分割字符串</param>
            <returns>string</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.GetDataType(System.Object)">
            <summary>
            获取对象的数据类型
            </summary>
            <param name="o">任意对象</param>
            <returns>EnumDataType</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.ChangeType(System.Object,System.Type)">
            <summary>
            值的数据类型转换
            </summary>
            <param name="val">待转换数据</param>
            <param name="type">Type</param>
            <returns>type对应的数据值</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.Round(System.Double,System.Int32)">
            <summary>
            double类型中国式的四舍五入
            </summary>
            <param name="v">数值</param>
            <param name="digits">小数位数</param>
            <returns>中国式的四舍五入的值</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCCLass.Round(System.Decimal,System.Int32)">
            <summary>
            decimal类型中国式的四舍五入
            </summary>
            <param name="v">数值</param>
            <param name="decimals">小数位数</param>
            <returns>中国式的四舍五入的值</returns>
        </member>
        <member name="T:PlatCommon.Base01.EnumDataType">
            <summary>
            枚举数据类型， 数字0，日期1和字符串2
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.EnumDataType.Number">
            <summary>
            数值
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.EnumDataType.Date">
            <summary>
            日期
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.EnumDataType.String">
            <summary>
            字符串
            </summary>
        </member>
        <member name="T:PlatCommon.Base01.IndexType">
            <summary>
            索引类型结构
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.IndexType.index">
            <summary>
            索引下标
            </summary>
        </member>
        <member name="F:PlatCommon.Base01.IndexType.type">
            <summary>
            数据类型
            </summary>
        </member>
        <member name="T:PlatCommon.Base01.GCClassExtention">
            <summary>
            静态扩展类
            </summary>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.CStr(System.Collections.Generic.Dictionary{System.String,System.String},System.String)">
            <summary>
            取键和值为字符串的字典的值，不存在时返回为空字符串
            </summary>
            <param name="dic">Dictionary&lt;string, string&gt;</param>
            <param name="key">键值</param>
            <returns>string</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.CValue``1(System.Collections.Generic.Dictionary{System.String,``0},System.String)">
            <summary>
            取键为字符串的字典的值，不存在时返回为default(T)
            </summary>
            <typeparam name="T">泛型</typeparam>
            <param name="dic">Dictionary&lt;string, T&gt;</param>
            <param name="key">键值</param>
            <returns>T</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.CDateStr(System.DateTime)">
            <summary>
            取DateTime的字符串，DateTime.MinValue为""
            </summary>
            <param name="date">DateTime</param>
            <returns>string</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.CDateTimeStr(System.DateTime)">
            <summary>
            取DateTime的字符串，DateTime.MinValue为""
            </summary>
            <param name="time">DateTime</param>
            <returns>string</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.CStr(System.Object)">
            <summary>
            取对象的字符串，null对象为""
            </summary>
            <param name="obj">Object</param>
            <returns>string</returns>
            <example>string str = obj.CStr();</example>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.CInt(System.Object)">
            <summary>
            取对象的int值，null对象为
            </summary>
            <param name="obj">Object</param>
            <returns>int</returns>
            <example>int val = obj.CInt();</example>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.CDouble(System.Object)">
            <summary>
            取对象的Double值，null对象为0
            </summary>
            <param name="obj">Object</param>
            <returns>double</returns>
            <example>double val = obj.CDouble();</example>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.CDecimal(System.Object)">
            <summary>
            取对象的Decimal值，null对象为0
            </summary>
            <param name="obj">Object</param>
            <returns>decimal</returns>
            <example>decimal val = obj.CDecimal();</example>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.CDateTime(System.Object)">
            <summary>
            取对象的DateTime值，非日期型时为DateTime.MinValue
            </summary>
            <param name="obj">Object</param>
            <returns>decimal</returns>
            <example>DateTime time = obj.CDateTime();</example>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.CInt(System.Data.DataRow,System.String)">
            <summary>
            取DataTable中Cell值的int值，非整数时为0
            </summary>
            <param name="dr">DataRow</param>
            <param name="fieldname">string</param>
            <returns>int</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.CInt(System.Data.DataRow,System.Int32)">
            <summary>
            取DataTable中Cell值的int值，非整数时为0
            </summary>
            <param name="dr">DataRow</param>
            <param name="index">index</param>
            <returns>int</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.CDouble(System.Data.DataRow,System.String)">
            <summary>
            取DataTable中Cell值的double值，非整数时为0
            </summary>
            <param name="dr">DataRow</param>
            <param name="fieldname">string</param>
            <returns>double</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.CDecimal(System.Data.DataRow,System.String)">
            <summary>
            取DataTable中Cell值的double值，非整数时为0
            </summary>
            <param name="dr">DataRow</param>
            <param name="fieldname">string</param>
            <returns>decimal</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.CDouble(System.Data.DataRow,System.Int32)">
            <summary>
            取DataTable中Cell值的double值，非整数时为0
            </summary>
            <param name="dr">DataRow</param>
            <param name="index">int</param>
            <returns>double</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.ToEntity``1(System.Data.DataRow)">
            <summary>
            将行数据转成对应的实体(属性转换)
            </summary>
            <typeparam name="T">泛型</typeparam>
            <param name="dataRow">DataRow</param>
            <returns>T</returns>
            <example>string sql = $"select * from CPR.MR_TEMPLET_VERIFY_FLOW where templet_id={GCSQL.SqlString(templetId)}";<br/>MODEL_MR_TEMPLET_VERIFY_FLOW item = OracleSrv.ItemLoad&lt;MODEL_MR_TEMPLET_VERIFY_FLOW&gt;(sql);;</example>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.ToEntityFields``1(System.Data.DataRow)">
            <summary>
            将行数据转成对应的实体(字段转换)
            </summary>
            <typeparam name="T">泛型</typeparam>
            <param name="dataRow">DataRow</param>
            <returns>T</returns>
        </member>
        <member name="M:PlatCommon.Base01.GCClassExtention.SetEntityToDataRow``1(System.Data.DataRow,``0)">
            <summary>
            将实体数据写入DataRow
            </summary>
            <typeparam name="T">泛型</typeparam>
            <param name="dataRow">DataRow</param>
            <param name="obj">实体对象</param>
            <returns>写入的列数</returns>
        </member>
        <member name="T:PlatCommon.Base02.Cs02BarCodeHelper">
            <summary>
            条码类
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02BarCodeHelper.GenBarCode128(System.String,System.Drawing.Size,System.Boolean)">
            <summary>
            生成128条码
            </summary>
            <param name="strBarCodeTxt">条码内容</param>
            <param name="size">大小</param>
            <param name="bShowText">是否显示文本</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02BarCodeHelper.GenBarCode39(System.String,System.Drawing.Size,System.Boolean)">
            <summary>
            生成39条码
            </summary>
            <param name="strBarCodeTxt">条码内容</param>
            <param name="size">大小</param>
            <param name="bShowText">是否显示文本</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02BarCodeHelper.GenBarCode39Extend(System.String,System.Drawing.Size,System.Boolean)">
            <summary>
            生成39条码扩展
            </summary>
            <param name="strBarCodeTxt">条码内容</param>
            <param name="size">大小</param>
            <param name="bShowText">是否显示文本</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02BarCodeHelper.GenBarCode93(System.String,System.Drawing.Size,System.Boolean)">
            <summary>
            生成93条码
            </summary>
            <param name="strBarCodeTxt">条码内容</param>
            <param name="size">大小</param>
            <param name="bShowText">是否显示文本</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02BarCodeHelper.GenBarCode93Extend(System.String,System.Drawing.Size,System.Boolean)">
            <summary>
            生成93条码Extend
            </summary>
            <param name="strBarCodeTxt">条码内容</param>
            <param name="size">大小</param>
            <param name="bShowText">是否显示文本</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02BarCodeHelper.GenBarEAN8(System.String,System.Drawing.Size,System.Boolean)">
            <summary>
            生成EAN8
            </summary>
            <param name="strBarCodeTxt">条码内容</param>
            <param name="size">大小</param>
            <param name="bShowText">是否显示文本</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02BarCodeHelper.GenBarEAN13(System.String,System.Drawing.Size,System.Boolean)">
            <summary>
            生成EAN13
            </summary>
            <param name="strBarCodeTxt">条码内容</param>
            <param name="size">大小</param>
            <param name="bShowText">是否显示文本</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02BarCodeHelper.GenBarEAN128(System.String,System.Drawing.Size,System.Boolean)">
            <summary>
            生成EAN128
            </summary>
            <param name="strBarCodeTxt">条码内容</param>
            <param name="size">大小</param>
            <param name="bShowText">是否显示文本</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02BarCodeHelper.GenBarCodeQR(System.String,System.Drawing.Size,System.Boolean)">
            <summary>
            生成QR二维码
            </summary>
            <param name="strBarCodeTxt">条码内容</param>
            <param name="size">大小</param>
            <param name="bShowText">是否显示文本</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02BarCodeHelper.GenBarCodePDF417(System.String,System.Drawing.Size,System.Boolean)">
            <summary>
            生成PDF417二维码
            </summary>
            <param name="strBarCodeTxt">条码内容</param>
            <param name="size">大小</param>
            <param name="bShowText">是否显示文本</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02BarCodeHelper.GenBarCode11(System.String,System.Drawing.Size,System.Boolean)">
            <summary>
            生成Code11二维码
            </summary>
            <param name="strBarCodeTxt">条码内容</param>
            <param name="size">大小</param>
            <param name="bShowText">是否显示文本</param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Base02.Cs02Controls">
            <summary>
            公共控件扩展
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.GetFormFromDll(System.String,System.String)">
            <summary>
            从Dll中，打开Form
            </summary>
            <param name="strDllName">DLL文件名</param>
            <param name="strTypeName">Form名，带命名空间的全名，如：Tjhis.HisComm.His06FrmAbout </param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetDateEditToDateTime(DevExpress.XtraEditors.DateEdit)">
            <summary>
            将DateEdit设置为DateTime控件模式
            </summary>
            <param name="edit">编辑控件</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.ExportToExcel(DevExpress.XtraGrid.GridControl,System.String)">
            <summary>
            另保存Excel文件
            </summary>
            <param name="GridControl">数据源(GridControl)</param>
            <param name="strFileName">文件名</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.ExportToExcel(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            另保存Excel文件
            </summary>
            <param name="gv">数据源(GridView)</param>
            <param name="strFileName">文件名</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetColumnFilter(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.String)">
            <summary>
            设置某列过滤
            </summary>
            <param name="gv">目标gv</param>
            <param name="strFieldName">过滤字段</param>
            <param name="strSearchText">过滤值</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetAllColumnFilter(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            设置GridView全列包含过滤
            </summary>
            <param name="gv">目标gv</param>
            <param name="strSearchText">过滤值</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.ClearFilterColumns(DevExpress.XtraGrid.Views.Grid.GridView)">
            <summary>
            清除GridView过滤列
            </summary>
            <param name="gv"></param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.ClearColumnSortOrder(DevExpress.XtraGrid.Views.Grid.GridView)">
            <summary>
            清除GridView各列的排序
            </summary>
            <param name="gv"></param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.GetEmptyRow(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            返回空白列数据的行号
            </summary>
            <param name="gridView">GridView</param>
            <param name="strFieldName">列名</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.GetEmptyRow(DevExpress.XtraGrid.Views.Grid.GridView,System.String[])">
            <summary>
            返回空白列数据的行号
            </summary>
            <param name="gridView">GridView</param>
            <param name="strArrFieldNames">列名数组</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.GetExistedRow(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.String)">
            <summary>
            返回存在数据的行号
            </summary>
            <param name="gridView">GridView</param>
            <param name="strFieldName">列名</param>
            <param name="strFieldValue">值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.GetRepeatedRow(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            判断是否有重复数据，返回重复的行号对
            </summary>
            <param name="gridView">GridView</param>
            <param name="strFieldName">列名</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.GetRepeatedRow(DevExpress.XtraGrid.Views.Grid.GridView,System.String[])">
            <summary>
            判断是否有重复数据，返回重复的行号对
            </summary>
            <param name="gridView">GridView</param>
            <param name="strArrFieldName">列名数组</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.GetGridViewColumnsCaption(DevExpress.XtraGrid.Views.Grid.GridView)">
            <summary>
            读取GridView各列的标题字符串（格式:FieldName=标题[,FieldName=标题])
            </summary>
            <param name="gridView">GridView</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetGridViewColumnsCaption(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            设定GridView各列标题（格式:FieldName=标题[,FieldName=标题])
            </summary>
            <param name="gridView">GridView</param>
            <param name="strFieldsCaption">fieldsCaption</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.GetGridViewColumnsWidth(DevExpress.XtraGrid.Views.Grid.GridView)">
            <summary>
            读取GridView各列宽度的表示字符串（格式:FieldName=Width[,FieldName=Width])
            </summary>
            <param name="gridView">GridView</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetGridViewColumnsWidth(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            设定GridView各列宽度（格式:FieldName=Width[,FieldName=Width])
            </summary>
            <param name="gridView">GridView</param>
            <param name="strFieldsWidth">GridView</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetTreeListProperty(DevExpress.XtraTreeList.TreeList)">
            <summary>
            设置TreeList的属性
            </summary>
            <param name="treelist">树形控件</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetEnterKeyDownAsTab(DevExpress.XtraGrid.Views.Grid.GridView)">
            <summary>
            GridView回车是TAB
            </summary>
            <param name="gridView">目标GridView</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.GetScreenPoint(System.Windows.Forms.Control)">
            <summary>
            获取页面中控件的绝对坐标
            </summary>
            <param name="ctrl"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetGridViewEditState(DevExpress.XtraGrid.Views.Grid.GridView,System.Int32)">
            <summary>
            设定GridView编辑状态
            </summary>
            <param name="gridView">GridView</param>
            <param name="iWidth">标志位宽度</param>
        </member>
        <member name="F:PlatCommon.Base02.Cs02Controls.waitDialog">
            <summary>
            等待窗口：waitDialog 字段与方法作者：曹顺想 2019/01/21
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetWaitDialogCaption(System.String,System.String)">
            <summary>
            显示进度窗口
            </summary>
            <param name="strCaption">说明文本</param>
            <param name="strTitle">窗口标题</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetWaitDialogPosition(System.Int32,System.Int32)">
            <summary>
            设置等待窗口相对于屏幕中心的位置
            </summary>
            <param name="x">x坐标的偏移量</param>
            <param name="y">y坐标的偏移量</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.HideWaitDialog">
            <summary>
            隐藏进度窗口
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetDateTimeColumnFormat(DevExpress.XtraGrid.Columns.GridColumn,System.String)">
            <summary>
            设置GridView日期型列的显示字符串
            </summary>
            <param name="gc">GridColumn</param>
            <param name="strFormatString">格式化串</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetDateTimeColumnFormat(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.String)">
            <summary>
            设置GridView日期型列的显示字符串
            </summary>
            <param name="gv">GridView</param>
            <param name="strFieldName">字段名</param>
            <param name="strFormatString">格式化串</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetSearchAllColumn(System.Windows.Forms.Control,DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit)">
            <summary>
            设置GridLookUpEdit全列搜索属性。 此方法在GridView中的下拉GridLookUpEdit控件中存在问题，有待解决。
            </summary>
            <param name="control"></param>
            <param name="edit">RepositoryItemGridLookUpEdit</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.BulidFilterCriteria(System.String,System.String)">
            <summary>
            gridView单项过滤方法， 是gridView1.ActiveFilterCriteria的参数
            </summary>
            <param name="strFieldName">过滤字段</param>
            <param name="strFieldValue">过滤值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.BulidFilterCriteria(System.String[],System.String[],DevExpress.Data.Filtering.GroupOperatorType)">
            <summary>
            gridView多项过滤方法。 是gridView1.ActiveFilterCriteria的参数
            </summary>
            <param name="strArrFieldNames">过滤字段数组</param>
            <param name="strArrFieldValues">过滤值数组</param>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetColumnFilter(DevExpress.XtraGrid.Columns.GridColumn,System.String)">
            <summary>
            设置某列过滤
            </summary>
            <param name="gridColumn">数据列</param>
            <param name="strSearchText">过滤值</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetGridLookUpEditDataSource``1(DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit,System.Collections.Generic.List{``0},System.String,System.String,System.Int32,System.Int32,System.Boolean)">
            <summary>
            为GridLookUpEdit设置数据源（RepositoryItemGridLookUpEdit）
            </summary>
            <param name="gle">RepositoryItemGridLookUpEdit</param>
            <param name="listData">数据源</param>
            <param name="valueMember">实际列名</param>
            <param name="displayMember">显示列名</param>
            <param name="valueMemberLength">实际列名宽度</param>
            <param name="displayMemberLength">显示列名宽度</param>
            <param name="bAllowEdit">是否允许编辑</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetGridLookUpEditDataSource(DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit,System.Data.DataTable,System.String,System.String,System.Int32,System.Int32,System.Boolean)">
            <summary>
            为GridLookUpEdit设置数据源（RepositoryItemGridLookUpEdit）
            </summary>
            <param name="gle">RepositoryItemGridLookUpEdit</param>
            <param name="dataTable">数据源</param>
            <param name="valueMember">实际列名</param>
            <param name="displayMember">显示列名</param>
            <param name="valueMemberLength">实际列名宽度</param>
            <param name="displayMemberLength">显示列名宽度</param>
            <param name="bAllowEdit">是否允许编辑</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetRepositoryItemGridLookUpEdit``1(DevExpress.XtraGrid.Columns.GridColumn,System.Collections.Generic.List{``0},System.String,System.String,System.Int32,System.Int32,System.Boolean)">
            <summary>
            为某列创建GridLookUpEdit及设置数据源（RepositoryItemGridLookUpEdit）
            </summary>
            <param name="gridColumn">GridView的列</param>
            <param name="listData">数据源</param>
            <param name="valueMember">实际列名</param>
            <param name="displayMember">显示列名</param>
            <param name="valueMemberLength">实际列名宽度</param>
            <param name="displayMemberLength">显示列名宽度</param>
            <param name="bAllowEdit">是否允许编辑</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetRepositoryItemGridLookUpEdit(DevExpress.XtraGrid.Columns.GridColumn,System.Data.DataTable,System.String,System.String,System.Int32,System.Int32,System.Boolean)">
            <summary>
            为某列创建GridLookUpEdit及设置数据源（RepositoryItemGridLookUpEdit）
            </summary>
            <param name="gridColumn">GridView的列</param>
            <param name="dataTable">数据源</param>
            <param name="valueMember">实际列名</param>
            <param name="displayMember">显示列名</param>
            <param name="valueMemberLength">实际列名宽度</param>
            <param name="displayMemberLength">显示列名宽度</param>
            <param name="bAllowEdit">允许编辑</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.GetRepositoryItemGridLookUpEdit``1(System.Collections.Generic.List{``0},System.String,System.String,System.Boolean)">
            <summary>
            创建GridLookUpEdit及设置数据源（RepositoryItemGridLookUpEdit）
            </summary>
            <param name="listData">数据源</param>
            <param name="valueMember">实际列名</param>
            <param name="displayMember">显示列名</param>
            <param name="bAllowEdit">是否允许编辑</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.GetRepositoryItemGridLookUpEdit``1(System.Collections.Generic.List{``0},System.Boolean,System.String,System.Boolean)">
            <summary>
            创建GridLookUpEdit及设置数据源（RepositoryItemGridLookUpEdit）
            </summary>
            <param name="listData">数据源</param>
            <param name="isShowHeader">显示表头</param>
            <param name="valueDisplayMember">唯一列名</param>
            <param name="bAllowEdit">是否允许编辑</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.CustomDrawRowIndicator(System.Object,DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventArgs)">
            <summary>
            设置显示行号
            </summary>
            <param name="sender">object</param>
            <param name="e">RowIndicatorCustomDrawEventArgs</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.RowCellStyle(System.Object,DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs)">
            <summary>
            设置焦点行背景蓝色，前景白色， 不可编辑列设置为灰色
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.EnterKeyDownAsTab(System.Object,System.Windows.Forms.KeyEventArgs)">
            <summary>
            GridView回车
            </summary>
        </member>
        <member name="T:PlatCommon.Base02.Cs02Controls.UCCheckInfoInput">
            <summary>
            支票信息输入类
            </summary>
        </member>
        <member name="F:PlatCommon.Base02.Cs02Controls.UCCheckInfoInput.txtBank">
            <summary>
            银行输入
            </summary>
        </member>
        <member name="F:PlatCommon.Base02.Cs02Controls.UCCheckInfoInput.txtCheckNo">
            <summary>
            支票号输入
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.UCCheckInfoInput.#ctor">
            <summary>
            支票信息输入
            </summary>
        </member>
        <member name="T:PlatCommon.Base02.Cs02Controls.UCGuarantorInput">
            <summary>
            担保人输入类
            </summary>
        </member>
        <member name="F:PlatCommon.Base02.Cs02Controls.UCGuarantorInput.txtGuarantor">
            <summary>
            担保人
            </summary>
        </member>
        <member name="F:PlatCommon.Base02.Cs02Controls.UCGuarantorInput.txtGuarantorID">
            <summary>
            担保人ID
            </summary>
        </member>
        <member name="F:PlatCommon.Base02.Cs02Controls.UCGuarantorInput.txtGuarantorUnit">
            <summary>
            担保人单位
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.UCGuarantorInput.#ctor">
            <summary>
            担保信息
            </summary>
        </member>
        <member name="T:PlatCommon.Base02.Cs02Controls.UCSingleInfoInput">
            <summary>
            单行信息输入类
            </summary>
        </member>
        <member name="F:PlatCommon.Base02.Cs02Controls.UCSingleInfoInput.txtInput">
            <summary>
            文本输入控件
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.UCSingleInfoInput.#ctor(System.String,System.Int32,System.Boolean)">
            <summary>
            构造函数
            </summary>
            <param name="strInputTitle">输入的说明</param>
            <param name="iMaxLength">最大长度</param>
            <param name="bPassWord">密码标志</param>
        </member>
        <member name="T:PlatCommon.Base02.Cs02Controls.UCDoubleInfoInput">
            <summary>
            两行信息输入类
            </summary>
        </member>
        <member name="F:PlatCommon.Base02.Cs02Controls.UCDoubleInfoInput.txtInput1">
            <summary>
            信息输入1
            </summary>
        </member>
        <member name="F:PlatCommon.Base02.Cs02Controls.UCDoubleInfoInput.txtInput2">
            <summary>
            信息输入2
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.UCDoubleInfoInput.#ctor(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            构造函数
            </summary>
            <param name="strInputTitle1">输入的说明1</param>
            <param name="strInputTitle2">输入的说明2</param>
            <param name="iMaxLength1">最大长度1</param>
            <param name="iMaxLength2">最大长度2</param>
        </member>
        <member name="T:PlatCommon.Base02.Cs02Controls.UCTriInfoInput">
            <summary>
            三行信息输入类
            </summary>
        </member>
        <member name="F:PlatCommon.Base02.Cs02Controls.UCTriInfoInput.txtInput1">
            <summary>
            信息输入1
            </summary>
        </member>
        <member name="F:PlatCommon.Base02.Cs02Controls.UCTriInfoInput.txtInput2">
            <summary>
            信息输入2
            </summary>
        </member>
        <member name="F:PlatCommon.Base02.Cs02Controls.UCTriInfoInput.txtInput3">
            <summary>
            信息输入3
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.UCTriInfoInput.#ctor(System.String,System.String,System.String,System.Int32,System.Int32,System.Int32)">
            <summary>
            构造函数
            </summary>
            <param name="strInputTitle1">输入的说明1</param>
            <param name="strInputTitle2">输入的说明2</param>
            <param name="strInputTitle3">输入的说明3</param>
            <param name="iMaxLength1">最大长度1</param>
            <param name="iMaxLength2">最大长度2</param>
            <param name="iMaxLength3">最大长度3</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.OpenWait">
            <summary>
            打开等待提示
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.OpenWait(System.String,System.String)">
            <summary>
            打开等待提示
            </summary>
            <param name="strShowMsg"></param>
            <param name="strTitle"></param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.CloseWait">
            <summary>
            关闭等待提示
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SendMessage(System.IntPtr,System.Int32,System.Int32,System.IntPtr)">
            <summary>
            发送消息
            </summary>
            <param name="hwnd"></param>
            <param name="wMsg"></param>
            <param name="wParam"></param>
            <param name="lParam"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02Controls.SetRedraw(System.IntPtr,System.Boolean)">
            <summary>
            控件重画
            </summary>
            <param name="hwnd"></param>
            <param name="bRedraw"></param>
        </member>
        <member name="T:PlatCommon.Base02.Cs02DataSetHelper">
            <summary>
            DataSet操作类
            </summary>	
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.HasRecord(System.Data.DataSet)">
            <summary>
            数据集是否具有记录
            </summary>
            <param name="dsData">数据集</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.ConvertRowToCol(System.Data.DataTable,System.String,System.String)">
            <summary>
            将行转换为列
            </summary>
            <param name="strColName">列名称</param>
            <param name="strColValue">列值</param>
            <param name="dtSrc">源DataTable</param>
            <returns>转换后的Datatable</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.GetDataSetFromFile(System.String)">
            <summary>
            XML文件，转成DATASET 
            </summary>
            <param name="strXmlFile">要转换的XML文件的文件名</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.DataSetToXmlFile(System.Data.DataSet,System.String)">
            <summary>
            将DataSet 保存到文件中, 如果目录不存在，创建目录
            </summary>
            <param name="dsData">要保存的DataSet</param>
            <param name="strXmlFile">指定文件名</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.DataSetToXmlFile(System.Data.DataSet)">
            <summary>
            将DataSet 保存到文件中
            </summary>
            <param name="dsData">要保存的DataSet</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.IsInColumns(System.String,System.Data.DataColumnCollection)">
            <summary>
            判断是否包含特定名称的列
            </summary>
            <param name="strCloName">特定名称</param>
            <param name="dcc">DataColumnCollection</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.GetMaxValue(System.Data.DataSet,System.String,System.String,System.Int32)">
            <summary>
            根据过滤条件得到数据集中指定列的最大值
            </summary>
            <param name="dsData">数据集</param>
            <param name="strFilter">过滤条件</param>
            <param name="strColName">指定列</param>
            <param name="iNullVaue">找不到时的缺省值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.GetDictDSFromStr(System.String)">
            <summary>
            从字符串中生成DataSet, 字符串以,进行分隔 0:不划,1:划
            </summary>
            <param name="strSource">字符串</param>
            <returns>生成的DataSet</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.CreateDict">
            <summary>
            创建字典类dataset
            </summary>
            <returns>创建的DataSet</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.AddDictItem(System.Data.DataSet@,System.String,System.String,System.Int32)">
            <summary>
            往字典类dataset中添加一条数据
            </summary>
            <param name="dsData">数据集</param>
            <param name="strItemName">项目名称</param>
            <param name="strItemCode">项目代码</param>
            <param name="iShowOrder">显示序号</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.CopyDataRow(System.Data.DataRow@,System.Data.DataRow@)">
            <summary>
            复制DataRow
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.GetDistinctDataTable(System.Data.DataTable,System.String[])">
            <summary>
            获取DataTable中指定列不重复的记录
            </summary>
            <param name="dtSource">需传入的数据源DataTable</param>
            <param name="strArrColumns">例：columns = { "DEPT_CODE", "DEPT_NAME" };  //DataTable中不重复的科室代码，科室名称</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.CheckDistinctDataTable(System.Data.DataTable,System.String[])">
            <summary>
            检查DataTable中指定列是否有重复记录
            </summary>
            <param name="dtSource">需检查的DataTable</param>
            <param name="strArrColumns">指定检查列</param>
            <returns>True 有重复记录，False 无重复记录</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.HasRecord(System.Data.DataTable)">
            <summary>
            数据集是否具有记录
            </summary>
            <param name="dtData">数据表</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.GetMaxValue(System.Data.DataSet,System.String,System.String,System.String)">
            <summary>
            获取列最大值
            </summary>
            <param name="dsData">数据集</param>
            <param name="strFilter">过滤串</param>
            <param name="strColName">列名</param>
            <param name="strNullValue">Null值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.GetMinValue(System.Data.DataSet,System.String,System.String,System.String)">
            <summary>
            获取最小值
            </summary>
            <param name="dsData">数据集</param>
            <param name="strFilter">过滤串</param>
            <param name="strColName">列名</param>
            <param name="strNullValue">Null值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.GetValue(System.Data.DataSet,System.String,System.String,System.String@)">
            <summary>
            获取值
            </summary>
            <param name="dsData">数据集</param>
            <param name="strFilter">过滤串</param>
            <param name="strColName">列名</param>
            <param name="strResult">返回查找值</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.CreateDict(System.String,System.Boolean)">
            <summary>
            创建DataSet
            </summary>
            <param name="strValList">值列表,  ","分隔</param>
            <param name="bNameIsInt">是否数字</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.CreateDict(System.String[0:,0:])">
            <summary>
            生成字典DataSet
            </summary>
            <param name="dictItems"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.AddNewRow(System.Data.DataTable,System.String[],System.String[])">
            <summary>
            添加新行
            </summary>
            <param name="dtData">数据表</param>
            <param name="strArrColNames">列名数组</param>
            <param name="strArrValues">值数组</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.AddNewRow(System.Data.DataSet,System.String[],System.String[])">
            <summary>
            添加新行
            </summary>
            <param name="dsData">数据集</param>
            <param name="strArrColNames">列名数组</param>
            <param name="strArrValues">值数组</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.GetItems(System.Data.DataTable,System.String)">
            <summary>
            获取某张表中某个字段值的列表
            </summary>
            <param name="dtData">数据表</param>
            <param name="strColName">列名</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.GetItems(System.Data.DataTable,System.String,System.String)">
            <summary>
            获取某张表中某个字段值的列表
            </summary>
            <param name="dtData">数据表</param>
            <param name="strColName">列名</param>
            <param name="strFilter">过滤串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.GetNameList(System.Data.DataSet,System.String,System.String,System.String)">
            <summary>
            获取名称字符串, 多值之间用","分隔
            </summary>
            <param name="dsData">数据集</param>
            <param name="strCodeList">名称字符串</param>
            <param name="strColName">名称列</param>
            <param name="strColCode">代码列</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.ConvertDataTableToClass``1(System.Data.DataTable)">
            <summary>
            表转换为实体对象
            </summary>
            <typeparam name="T"></typeparam>
            <param name="dtData">数据表</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.GetItem``1(System.Data.DataRow)">
            <summary>
            数据行转换为实体对象
            </summary>
            <typeparam name="T"></typeparam>
            <param name="drData">数据行</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.DeleteEmptyRow(System.Data.DataTable)">
            <summary>
            删除DataTable中的空白行
            </summary>
            <param name="dtData">数据表</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.DeleteEmptyRow(System.Data.DataTable,System.String[])">
            <summary>
            删除DataTable中的空白行
            </summary>
            <param name="dtData">数据表</param>
            <param name="strArrIgnoreColumns">忽略的列数组</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.ToDataTable(System.Data.DataRow[])">
            <summary>
            把行转换为表
            </summary>
            <param name="drArrRows">行数组</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.GetDataTable_DistinctRecord(System.Data.DataTable,System.String[])">
            <summary>
            去重复数据
            </summary>
            <param name="dtSource">源数据表</param>
            <param name="strArrColumns"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.UpdateTableByKey(System.Data.DataSet,System.Data.DataSet)">
            <summary>
            合并数据表，不比较数据，只比较主键, 要求有主键, 无修改操作, 只有增删
            </summary>
            <param name="dsDest">目标表</param>
            <param name="dsSrc">原表</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.UpdateTableByKey(System.Data.DataSet,System.Data.DataSet,System.String[])">
            <summary>
            合并数据表，不比较数据，只比较主键, 要求有主键, 无修改操作, 只有增删
            </summary>
            <param name="dsDest">目标表</param>
            <param name="dsSrc">原表</param>
            <param name="strArrKeyCols">主键列数组</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.MergeTable(System.Data.DataTable,System.Data.DataTable)">
            <summary>
            合并数据表，不比较数据，只比较主键, 要求有主键, 无修改操作, 只有增删
            </summary>
            <param name="dtDest">目标表</param>
            <param name="dtSrc">原表</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.MergeDataSet(System.Data.DataSet@,System.Data.DataSet,System.String[])">
            <summary>
            合并DataSet
            </summary>
            <param name="dsDest">目标数据集</param>
            <param name="dsSrc">源数据集</param>
            <param name="strArrPKCol">主键列数组</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.MergeDataSetEx(System.Data.DataTable@,System.Data.DataTable,System.Data.DataColumn[])">
            <summary>
            合并DataSet
            </summary>
            <param name="dtDest">目标数据表</param>
            <param name="dtSrc">源数据表</param>
            <param name="colPrimaryKeys">主键列</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.MergeDataSetPart(System.Data.DataTable@,System.Data.DataTable,System.Data.DataColumn[],System.String)">
            <summary>
            合并DataSet
            </summary>
            <param name="dtDest">目标数据表</param>
            <param name="dtSrc">源数据表</param>
            <param name="colPrimaryKeys">主键列</param>
            <param name="strFilter">过滤串</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DataSetHelper.MergeDataSetEx(System.Data.DataTable@,System.Data.DataTable,System.Data.DataColumn[],System.String)">
            <summary>
            合并DataSet
            </summary>
            <param name="dtDest">目标数据表</param>
            <param name="dtSrc">源数据表</param>
            <param name="colPrimaryKeys">主键列</param>
            <param name="strDateFieldName">修改日期列</param>
        </member>
        <member name="T:PlatCommon.Base02.Cs02DateTimeHelper">
            <summary>
            DateTime操作类
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.GetFirstDateOfWeek(System.DateTime)">
            <summary>
            计算本周起始日期（礼拜一的日期）
            </summary>
            <param name="someDate">该周中任意一天</param>
            <returns>返回礼拜一日期，后面的具体时、分、秒和传入值相等</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.GetLastDateOfWeek(System.DateTime)">
            <summary>
            计算本周结束日期（礼拜日的日期）
            </summary>
            <param name="someDate">该周中任意一天</param>
            <returns>返回礼拜日日期，后面的具体时、分、秒和传入值相等</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.GetFirstDateOfMonth(System.DateTime)">
            <summary>
            返回当月第一天
            </summary>
            <param name="someDate"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.GetLastDateOfMonth(System.DateTime)">
            <summary>
            返回当月最后一天
            </summary>
            <param name="someDate"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.Parse(System.Object)">
            <summary>
            对象转换为DateTime?
            </summary>
            <param name="objDate"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.DateTime_Null">
            <summary>
            获取DateTime的Null值
            </summary>
            <remarks>假定一个"1-1-1 12:00:00"值作为Null值</remarks>
            <returns>DateTime</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.DateTime_IsNull(System.DateTime@)">
            <summary>
            判断DateTime是否为Null
            </summary>
            <param name="dt">DateTime</param>
            <returns>TRUE: 是为Null; FALSE: 不为Null</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.DateTimeFromString(System.String)">
            <summary>
            把字符串转换成为日期
            </summary>
            <param name="strDateTime">日期字符串</param>
            <returns>日期</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.GetDateTime(System.String)">
            <summary>
            将字符串转成日期
            </summary>
            <param name="strDate"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.GetDateTime(System.Object)">
            <summary>
            将对象转换为日期
            </summary>
            <param name="objValue"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.GetDateTime(System.DateTime,System.String)">
            <summary>
            获取时间
            </summary>
            <param name="dtDay">指定日期</param>
            <param name="timeStr">时分秒字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.GetDateTime(System.String@)">
            <summary>
            从一个字符串中尽量提取日期时间字符串
            </summary>
            <remarks>
             1: 年月日时分秒可以被任意非数字字符分隔
             2: 年月日时分秒可以不用任何分隔符分隔,提取时按年四位数整数, 其它都有两位整数
             3: 1与2两种情况的结合体也可以识别, 如 200012-2 20:12-43, 可以正确识别
            </remarks>
            <param name="dtSrc">日期时间字符串</param>
            <returns>TRUE: 成功; FALSE: 失败</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.GetDateTimeShort(System.String)">
            <summary>
            把一个表示日期的字符串转换成短日期格式
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.GetDateTimeLong(System.String)">
            <summary>
            把一个表示日期的字符串转换成长日期格式
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.GetShortTime(System.String)">
            <summary>
            获取短时间格式
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.GetMD(System.String)">
            <summary>
            获取月日(如8.2)
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.GetYMD(System.String)">
            <summary>
            获取年月日, 如2007年12月6日
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.GetRomaNum(System.Int32)">
            <summary>
            获取数字的罗马表示
            </summary>
            <param name="num"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.IsTime(System.String@)">
            <summary>
            检查一个字符串是不是合法的时间格式
            如果是: 把输入字符串转换成标准的日期格式HH:MM:SS
            </summary>
            <param name="timeStr">表示时间的字符</param>
            <returns>TRUE: 是; FALSE: 不是</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.IsNumber(System.String)">
            <summary>
            判断一个字符串是不是正整数
            </summary>
            <remarks>仅包含数字</remarks>
            <param name="text">待验证的字符串</param>
            <returns>TRUE: 符合要求; FALSE: 不符合要求</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.IsDateTime(System.String)">
            <summary>
            判断字符串是不是日期时间,如果是转换成标准形式 YYYY-MM-DD HH:mm:ss
            </summary>
            <param name="dtStr"></param>
            <returns>TRUE: 是; FALSE: 否</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02DateTimeHelper.SetSystemTime(System.DateTime)">
            <summary>
            设置系统日期时间
            </summary>
            <param name="dtNew"></param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Base02.Cs02ExtendDev">
            <summary>
            DEV GridControl扩展
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetGridTitle(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            设置Grid的标题
            <param name="gridView">目标gridView</param>
            <param name="strTitle">标题</param>
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetGridFooter(DevExpress.XtraGrid.Views.Grid.GridView)">
            <summary>
            设置汇总行的字体和颜色
            <param name="gridView">目标gridView</param>
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetGridGroupRow(DevExpress.XtraGrid.Views.Grid.GridView)">
            <summary>
            设置分组行字体和颜色
            <param name="gridView">目标gridView</param>
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetGridMultiSelect(DevExpress.XtraGrid.Views.Grid.GridView)">
             <summary>
            设置为行多选
             <param name="gridView">目标gridView</param>
             </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetGridStatus(DevExpress.XtraGrid.Views.Grid.GridView)">
            <summary>
            设置Grid的显示状态时的属性值
            </summary>
            <param name="gridView">目标gridView</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetGridStatusEdit(DevExpress.XtraGrid.Views.Grid.GridView)">
            <summary>
            设置Grid的编辑状态时的属性值
            </summary>
            <param name="gridView">目标gridView</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetSelectRowColor(DevExpress.XtraGrid.Views.Grid.GridView,System.Boolean)">
            <summary>
            设置Grid的选择行背景
            </summary>
            <param name="gridView">目标gridView</param>
            <param name="bFlag">是否设置</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.GridView_CustomDrawCell(System.Object,DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs)">
            <summary>
            设置选择行背景蓝色，前景白色
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.ShowRowIndicator(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.Int32)">
            <summary>
            设置显示行号
            </summary>
            <param name="gridView">目标gridView</param>
            <param name="strCaption">显示标题，缺省为“序号”</param>
            <param name="iWidth">显示宽度，缺省为60</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.DrawRowIndicator(System.Object,DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventArgs)">
            <summary>
            行号指示
            </summary>
            <param name="sender">object</param>
            <param name="e">RowIndicatorCustomDrawEventArgs</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetRowColorChange(DevExpress.XtraGrid.Views.Grid.GridView,System.Boolean)">
            <summary>
            设置奇数行背景色不同
            </summary>
            <param name="gridView">目标gridView</param>
            <param name="bChange">是否显示不同</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetFocusedRowColor(DevExpress.XtraGrid.Views.Grid.GridView)">
            <summary>
            设置当前行的背景色，前景色
            </summary>
            <param name="gridView">目标gridView</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetSelectedRowColor(DevExpress.XtraGrid.Views.Grid.GridView)">
            <summary>
            设置选中行的背景色，前景色
            </summary>
            <param name="gridView">目标gridView</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetLoopUpEdit(DevExpress.XtraEditors.LookUpEdit,System.Data.DataTable,System.String,System.String,System.Boolean,System.String)">
            <summary>
            设置下拉选择框的数据
            <param name="lookUpEdit">下拉选择框</param>
            <param name="dtSource">显示数据源</param>
            <param name="strDisplay">显示字段</param>
            <param name="strValue">值字段</param>
            <param name="bShowAll">是否有全部</param>
            <param name="strNullText">Null显示</param>
            </summary> 
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetLoopUpEdit(DevExpress.XtraEditors.LookUpEdit,System.Collections.Generic.Dictionary{System.String,System.String},System.Boolean,System.String)">
            <summary>
            设置下拉选择框的数据
            <param name="lookUpEdit">下拉选择框</param>
            <param name="dictData">显示数据源</param>
            <param name="bShowAll">是否有全部</param>
            <param name="strNullText">Null显示</param>
            </summary> 
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetSearchLookUp(DevExpress.XtraEditors.SearchLookUpEdit,System.Data.DataTable,System.String,System.String,System.Boolean,System.String)">
            <summary>
            设置查找下拉输入，无列头定义
            </summary>
            <param name="searchLookUpEdit">查找下拉控件</param>
            <param name="dtData">数据源</param>
            <param name="strValueColumn">值列</param>
            <param name="strDisColumn">显示列</param>
            <param name="bShowAll">是否有全部</param>
            <param name="strNullText">Null显示</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetSearchLookUp(DevExpress.XtraEditors.SearchLookUpEdit,System.Data.DataTable,System.String,System.String,System.Data.DataTable,System.Boolean,System.String)">
            <summary>
            设置查找下拉输入，有列头定义
            </summary>
            <param name="searchLookUpEdit">查找下拉控件</param>
            <param name="dtData">数据源</param>
            <param name="strValueColumn">值列</param>
            <param name="strDisColumn">显示列</param>
            <param name="dtColFormat">列头定义表</param>
            <param name="bShowAll">是否有全部</param>
            <param name="strNullText">Null显示</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetColumnForeColor(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.String,System.Drawing.Color,System.Boolean)">
            <summary>
            设置GridView列的字体颜色
            </summary>
            <param name="gridView">控件</param>
            <param name="strExpression">表达式</param>
            <param name="strColumnName">列名</param>
            <param name="color">颜色</param>
            <param name="bWholeRow">整行</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetColumnBackColor(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.String,System.Drawing.Color,System.Boolean)">
            <summary>
            设置GridView列的背景颜色
            </summary>
            <param name="gridView">控件</param>
            <param name="strExpression">表达式</param>
            <param name="strColumnName">列名</param>
            <param name="color">颜色</param>
            <param name="bWholeRow">整行</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetColumnMaxLength(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.Int32)">
            <summary>
            设置列最大输入长度
            </summary>
            <param name="gridView">控件</param>
            <param name="strColumnName">列名</param>
            <param name="iMaxLength">最大长度</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetColumnEditCheckBox(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.Object,System.Object)">
            <summary>
            设置列为CheckBox输入框
            </summary>
            <param name="gridView">GridView控件</param>
            <param name="strColumnName">列名</param>
            <param name="objCheckedValue">选中的值</param>
            <param name="objUnCheckedValue">非选中的值</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetColumnEditDateTime(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            设置列为日期时间编辑
            </summary>
            <param name="gridView">控件</param>
            <param name="strColumnName">列名</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetColumnEditDate(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            设置列为日期编辑
            </summary>
            <param name="gridView">控件</param>
            <param name="strColumnName">列名</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetColumnEditNumberic(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.Int32,System.Int32)">
            <summary>
            设置列数字输入
            </summary>
            <param name="gridView">控件</param>
            <param name="strColumnName">列名</param>
            <param name="iPrecision">小数位数</param>
            <param name="iLength">整数位数</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetColumnEditLookUp(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.Data.DataTable,System.String,System.String,System.Int32,System.Boolean,System.String)">
            <summary>
            设置列为下拉选择
            </summary>
            <param name="gridView">控件</param>
            <param name="strFieldName">列名</param>
            <param name="dtData">数据源</param>
            <param name="strValueColumn">数据源列--值</param>
            <param name="strDisColumn">数据源列--显示</param>
            <param name="iWidth">显示宽度</param>
            <param name="bShowAll">是否有全部</param>
            <param name="strNullText">Null显示</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetLoopUpEdit(DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit,System.Data.DataTable,System.String,System.String,System.Boolean,System.String)">
            <summary>
            设置下拉选择框的数据
            <param name="lookUpEdit">下拉选择框</param>
            <param name="dtSource">显示数据源</param>
            <param name="strDisplay">显示字段</param>
            <param name="strValue">值字段</param>
            <param name="bShowAll">是否有全部</param>
            <param name="strNullText">Null显示</param>
            </summary> 
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetColumnEditLookUp(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.Int32,System.Boolean,System.String)">
            <summary>
            设置列为下拉选择
            </summary>
            <param name="gridView">控件</param>
            <param name="strFieldName">列名</param>
            <param name="dictData">数据源</param>
            <param name="iWidth">显示宽度</param>
            <param name="bShowAll">是否有全部</param>
            <param name="strNullText">Null显示</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetLoopUpEdit(DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit,System.Collections.Generic.Dictionary{System.String,System.String},System.Boolean,System.String)">
            <summary>
            设置下拉选择框的数据
            <param name="lookUpEdit">下拉选择框</param>
            <param name="dictData">显示数据源</param>
            <param name="bShowAll">是否有全部</param>
            <param name="strNullText">Null显示</param>
            </summary> 
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetColumnEditSearchLookUp(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.Data.DataTable,System.Data.DataTable,System.String,System.String,System.Int32,System.Boolean,System.String)">
            <summary>
            设置列为查找下拉选择
            </summary>
            <param name="gridView">控件</param>
            <param name="strFieldName">列名</param>
            <param name="dtData">数据源</param>
            <param name="dtColFormat">数据源列定义</param>
            <param name="strValueColumn">数据源列--值</param>
            <param name="strDisColumn">数据源列--显示</param>
            <param name="iWidth">显示宽度</param>
            <param name="bShowAll">是否有全部</param>
            <param name="strNullText">Null显示</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetSearchLookUp(DevExpress.XtraEditors.Repository.RepositoryItemSearchLookUpEdit,System.Data.DataTable,System.String,System.String,System.Data.DataTable,System.Boolean,System.String)">
            <summary>
            设置Grid中查找下拉输入，有列头定义
            </summary>
            <param name="searchLookUpEdit"></param>
            <param name="dtData"></param>
            <param name="strValueColumn"></param>
            <param name="strDisColumn"></param>
            <param name="dtColFormat"></param>
            <param name="bShowAll"></param>
            <param name="strNullText">Null显示</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetsearchLookUpEditColumn(System.Object,System.EventArgs,System.Data.DataTable)">
            <summary>
            设置弹出的标题
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
            <param name="dtColFormat"></param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.ExistField(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            查找是否存在列
            </summary>
            <param name="gridView">目标gridView</param>
            <param name="strFieldName">列名</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.GetFieldMaxValue(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            取列的最大值
            </summary>
            <param name="gridView">控件</param>
            <param name="strColumnName">列名</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetEnterKeyDownAsTab(DevExpress.XtraGrid.Views.Grid.GridView)">
            <summary>
            GridView回车是TAB
            </summary>
            <param name="gridView">目标GridView</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.EnterKeyDownAsTab(System.Object,System.Windows.Forms.KeyEventArgs)">
            <summary>
            GridView回车
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SetGridCustom(DevExpress.XtraGrid.Views.Grid.GridView,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            设置GRID的定制属性
            </summary>
            <param name="gridView">控件</param>
            <param name="bSort">允许排序</param>
            <param name="bFilter">允许过滤</param>
            <param name="bMoving">允许列移动</param>
            <param name="bResizing">允许拖动列宽</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.AddCalcuteColumn(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.String)">
            <summary>
            加入计算列,显示在最后
            </summary>
            <param name="gridView">控件</param>
            <param name="strColumnName">列名</param>
            <param name="strExpression">计算表达式</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.AddCalcuteColumn(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.String,System.Int32)">
            <summary>
            加入计算列，指定位置
            </summary>
            <param name="gridView">控件</param>
            <param name="strColumnName">列名</param>
            <param name="strExpression">计算表达式</param>
            <param name="iShowIndex">显示位置</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.AddCalcuteColumn(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.String,DevExpress.Data.UnboundColumnType)">
            <summary>
            加入计算列
            </summary>
            <param name="gridView">控件</param>
            <param name="strColumnName">列名</param>
            <param name="strExpression">计算表达式</param>
            <param name="ColType">数据类型</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.AddCalcuteColumn(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.String,System.Int32,DevExpress.Data.UnboundColumnType)">
            <summary>
            加入计算列
            </summary>
            <param name="gridView">控件</param>
            <param name="strColumnName">列名</param>
            <param name="strExpression">计算表达式</param>
            <param name="iShowIndex">显示位置</param>
            <param name="ColType">数据类型</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.GetColFormatDataTable">
            <summary>
            取列头设置表
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SelectData(System.Object,DevExpress.XtraEditors.Controls.ClosedEventArgs)">
            <summary>
            选择数据
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.SelectData(System.Object,DevExpress.XtraEditors.Controls.ClosedEventArgs,System.Data.DataTable)">
            <summary>
            选择数据
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
            <param name="dtShow"></param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.GetEmptyRow(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.Boolean)">
            <summary>
            返回空白列数据的行号
            </summary>
            <param name="gridView">GridView</param>
            <param name="strFieldName">列名</param>
            <param name="bShowMsg">是否显示信息</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.GetEmptyRow(DevExpress.XtraGrid.Views.Grid.GridView,System.String[],System.Boolean)">
            <summary>
            返回空白列数据的行号
            </summary>
            <param name="gridView">GridView</param>
            <param name="strArrFieldNames">列名数组</param>
            <param name="bShowMsg">是否显示信息</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.GetExistedRow(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.String)">
            <summary>
            返回存在数据的行号
            </summary>
            <param name="gridView">GridView</param>
            <param name="strFieldName">列名</param>
            <param name="strFieldValue">值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.GetExistedRow(DevExpress.XtraGrid.Views.Grid.GridView,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            返回存在数据的行号
            </summary>
            <param name="gridView">GridView</param>
            <param name="dicFields">列值字典</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.GetZeroRow(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.Boolean)">
            <summary>
            返回数据为零的行号
            </summary>
            <param name="gridView">GridView</param>
            <param name="strFieldName">列名</param>
            <param name="bShowMsg">是否显示信息</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.GetNotPositiveRow(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.Boolean)">
            <summary>
            返回数据为非正数的行号
            </summary>
            <param name="gridView">GridView</param>
            <param name="strFieldName">列名</param>
            <param name="bShowMsg">是否显示信息</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.GetRepeatedRow(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.Boolean)">
            <summary>
            判断是否有重复数据，返回重复的行号对
            </summary>
            <param name="gridView">GridView</param>
            <param name="strFieldName">列名</param>
            <param name="bShowMsg">是否显示信息</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.GetRepeatedRow(DevExpress.XtraGrid.Views.Grid.GridView,System.String[],System.Boolean)">
            <summary>
            判断是否有重复数据，返回重复的行号对
            </summary>
            <param name="gridView">GridView</param>
            <param name="strArrFieldNames">列名数组</param>
            <param name="bShowMsg">是否显示信息</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.GridControlPrint(DevExpress.XtraGrid.GridControl,System.String,System.String,System.Boolean)">
            <summary>
            打印GridControl
            </summary>
            <param name="gcControl">打印控件</param>
            <param name="strTitle">标题</param>
            <param name="strCondition">查询条件</param>
            <param name="bAutoFooter">是否打印页脚</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendDev.PrintCreateHeaderArea(DevExpress.XtraPrinting.CreateAreaEventArgs,System.String,System.String)">
            <summary>
            打印标题区的设置
            </summary>
            <param name="e"></param>
            <param name="strTitle"></param>
            <param name="strCondition"></param>
        </member>
        <member name="T:PlatCommon.Base02.Cs02ExtendTable">
            <summary>
            DataTable扩展
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendTable.TableSort(System.Data.DataTable,System.String)">
            <summary>
            将DataTable按列排序
            <param name="dtSourceTable">排序前DataTable</param>
            <param name="strSortFields">排序字段strFieldNames</param>
            <returns>排序后DataTable</returns>
            </summary> 
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendTable.TableGroup(System.Data.DataTable,System.String)">
            <summary>
            将DataTable分组
            <param name="dtSourceTable">要分组的DataTable</param>
            <param name="strGroupFields">分组字段strGroupFields</param>
            <returns>分组后DataTable</returns>
            </summary> 
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendTable.ChangeToCross(System.Data.DataTable,System.String,System.String,System.String)">
            <summary>
            将DataTable转为交叉表
            </summary>
            <param name="dtSourceTable">交叉表源</param>
            <param name="strFixFields">固定字段</param>
            <param name="strCrossField">可变字段</param>
            <param name="strCrossValueField">可变值字段</param>
            <returns>转为交叉表后的DataTable</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02ExtendTable.TableGroupCalcute(System.Data.DataTable,System.String,System.String,System.String,System.String)">
            <summary>
            将DataTable分组计算
            <param name="dtSourceTable">分组计算源表</param>
            <param name="strGroupFields">分组字段</param>
            <param name="strCountFields">计数字段</param>
            <param name="strSumFields">求和字段</param>
            <param name="strAvgFields">平均值字段</param>
            <returns>分组计算后的DataTable</returns>
            </summary>
        </member>
        <member name="T:PlatCommon.Base02.Cs02HttpClient">
            <summary>
            Http客户端
            </summary>
        </member>
        <member name="P:PlatCommon.Base02.Cs02HttpClient._strUrl">
            <summary>
            请求的url地址
            </summary>
        </member>
        <member name="P:PlatCommon.Base02.Cs02HttpClient._strMethod">
            <summary>
            请求的方法:GET,POST,PUT,DELETE
            </summary>
        </member>
        <member name="P:PlatCommon.Base02.Cs02HttpClient._strContentType">
            <summary>
            格式类型
            </summary>
        </member>
        <member name="P:PlatCommon.Base02.Cs02HttpClient._strPostData">
            <summary>
            传送的数据
            </summary>
        </member>
        <member name="P:PlatCommon.Base02.Cs02HttpClient._encoding">
            <summary>
            编码方式
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpClient.#ctor(System.String)">
            <summary>
            初始化
            <param name="strUrl">Url地址</param>
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpClient.#ctor(System.String,System.String)">
            <summary>
            初始化
            </summary>
            <param name="strUrl">Url地址</param>
            <param name="strContentType">格式类型</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpClient.#ctor(System.String,System.String,System.String)">
            <summary>
            初始化
            </summary>
            <param name="strUrl">Url地址</param>
            <param name="strMethod">执行方法</param>
            <param name="strContentType">格式类型</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpClient.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            初始化
            </summary>
            <param name="strUrl">Url地址</param>
            <param name="strMethod">执行方法</param>
            <param name="strContentType">格式类型</param>
            <param name="strPostData">POST数据</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpClient.#ctor(System.String,System.String,System.String,System.String,System.Text.Encoding)">
            <summary>
            初始化
            </summary>
            <param name="strUrl">Url地址</param>
            <param name="strMethod">执行方法</param>
            <param name="strContentType">格式类型</param>
            <param name="strPostData">POST数据</param>
            <param name="encoding">编码方式</param>
            
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpClient.GetResponse">
            <summary>
            执行HTTP调用
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpClient.GetResponse(System.String)">
            <summary>
            执行HTTP调用
            </summary>
            <param name="strParams">参数</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpClient.CheckUrl(System.String)">
            <summary>
            检查URL状态
            </summary>
            <param name="strParams">参数</param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Base02.Cs02HttpHelper">
            <summary>
            HTTP类
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpHelper.GetHttpResponse(System.String,System.String@,System.String@,System.Int32)">
            <summary>
            Get方式的HTTP请求
            </summary>
            <param name="strUrl">Url地址</param>
            <param name="strReturn">返回值</param>
            <param name="strErrMsg">错误信息</param>
            <param name="iTimeout">超时(毫秒)</param>
            <returns>0:成功，其它:错误</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpHelper.PostHttpResponse(System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.String@,System.String@,System.Int32)">
            <summary>
            POST方式的HTTP请求
            </summary>
            <param name="strUrl">Url地址</param>
            <param name="strContentType">编码类型</param>
            <param name="dicParameters">参数表string, string</param>
            <param name="strReturn">返回值</param>
            <param name="strErrMsg">错误信息</param>
            <param name="iTimeout">超时(毫秒)</param>
            <returns>0:成功，其它:错误</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpHelper.PostHttpResponse(System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.String@,System.String@,System.Net.CookieCollection,System.Int32)">
            <summary>
            POST方式的HTTP请求
            </summary>
            <param name="strUrl">Url地址</param>
            <param name="strContentType">编码类型</param>
            <param name="dicParameters">参数表string, string</param>
            <param name="strReturn">返回值</param>
            <param name="strErrMsg">错误信息</param>
            <param name="cookies">cookies</param>
            <param name="iTimeout">超时</param>
            <returns>0:成功，其它:错误</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpHelper.PostHttpResponse(System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.String@,System.String@,System.String,System.Int32)">
            <summary>
            POST方式的HTTP请求
            </summary>
            <param name="strUrl">Url地址</param>
            <param name="strContentType">编码类型</param>
            <param name="dicParameters">参数表string, string</param>
            <param name="strReturn">返回值</param>
            <param name="strErrMsg">错误信息</param>
            <param name="iTimeout">超时(毫秒)</param>
            <param name="strUserAgent">代理</param>
            <returns>0:成功，其它:错误</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpHelper.PostHttpResponse(System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.String@,System.String@,System.String,System.Net.CookieCollection,System.Int32)">
            <summary>
            POST方式的HTTP请求
            </summary>
            <param name="strUrl">Url地址</param>
            <param name="strContentType">编码类型</param>
            <param name="dicParameters">参数表string, string</param>
            <param name="strReturn">返回值</param>
            <param name="strErrMsg">错误信息</param>
            <param name="iTimeout">超时(毫秒)</param>
            <param name="strUserAgent">代理</param>
            <param name="cookies">cookies</param>
            <returns>0:成功，其它:错误</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpHelper.DownloadFile(System.String,System.String)">
            <summary>
             Http下载文件
             </summary>
             <param name="strUrl">下载文件地址</param>
             <param name="strFileName">保存文件全路径</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpHelper.UploadFile(System.String,System.String)">
            <summary>
            Http上传文件
            </summary>
            <param name="strUrl">上传文件地址</param>
            <param name="strFileFullName">上传文件全路径</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpHelper.HttpPost1(System.String,System.String,System.String)">
            <summary>
            POST 
            </summary>
            <param name="strUrl">Url地址</param>
            <param name="strContentType">Url地址</param>
            <param name="strBody">XML内容</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpHelper.PostCallUrlForTxt(System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            POST方式调用
            </summary>
            <param name="strUrl">调用地址</param>
            <param name="strContentType">application/json;application/xml;application/x-www-form-urlencoded</param>
            <param name="strCallMethod">调用方法</param>
            <param name="strPostData">POST参数</param>
            <param name="bShowErrMsg">显示错误信息</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpHelper.GetCallUrlForTxt(System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            Get方式调用
            </summary>
            <param name="strUrl">调用地址</param>
            <param name="strContentType">application/json;application/xml;application/x-www-form-urlencoded</param>
            <param name="strCallMethod">调用方法</param>
            <param name="strGetParam">GET参数</param>
            <param name="bShowErrMsg">显示错误信息</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpHelper.IDictConvertStr(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            IDictionary转换string
            </summary>
            <param name="dicParams"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02HttpHelper.GetCallUrlForTxt(System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String},System.Boolean)">
            <summary>
            Get方式调用
            </summary>
            <param name="strUrl">调用地址</param>
            <param name="strContentType">application/json;application/xml;application/x-www-form-urlencoded</param>
            <param name="strCallMethod">调用方法</param>
            <param name="dicParam">GET参数字典</param>
            <param name="bShowErrMsg">显示错误信息</param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Base02.Cs02JsonHelper">
            <summary>
            Json类
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02JsonHelper.JsonSerializer``1(``0)">
            <summary>
            Json序列化
            </summary>
            <typeparam name="T">对象类型</typeparam>
            <param name="obj">数据对象</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02JsonHelper.DataTableToJson(System.Data.DataTable)">
            <summary>
            将 DataTable 序列化成 json 字符串
            </summary>
            <param name="dtData">数据表</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02JsonHelper.ObjectToJson(System.Object)">
            <summary>
            将对象序列化成 json 字符串
            </summary>
            <param name="obj">数据对象</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02JsonHelper.JsonDeserialize``1(System.String)">
            <summary>
            Json反序列化
            </summary>
            <typeparam name="T">对象类型</typeparam>
            <param name="strJson">Json字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02JsonHelper.JsonToObject(System.String)">
            <summary>
            将 json 字符串反序列化成对象
            </summary>
            <param name="strJson">Json字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02JsonHelper.JsonToObject``1(System.String)">
            <summary>
            将 json 字符串反序列化成对象
            </summary>
            <typeparam name="T">对象类型</typeparam>
            <param name="strJson">Json字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02JsonHelper.JsonToDataTable(System.String,System.Boolean)">
            <summary>
            Json字符串转换为 DataTable,一层数据
            </summary>
            <param name="strJson">Json字符串</param>
            <param name="bColConstant">Json列是否确定</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02JsonHelper.JsonToDataTableMult(System.String,System.String,System.Boolean)">
            <summary>
            Json字符串转换为 DataTable,二层数据
            </summary>
            <param name="strJson">Json字符串</param>
            <param name="strDetailColumn">明细项的名称</param>
            <param name="bColConstant">Json列是否确定</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02JsonHelper.DicToJson(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            将字典转为JSON串
            </summary>
            <param name="dicData">数据字典</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02JsonHelper.JsonToDic(System.String)">
            <summary>
            将JSON串转为字典
            <param name="strJson">Json字符串</param>
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02JsonHelper.JsonToJobject(System.String)">
            <summary>
            将JSON串转为Jobject
            </summary>
            <param name="strJson">Json字符串</param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Base02.Cs02MessageBox">
            <summary>
            显示信息类
            </summary>
        </member>
        <member name="F:PlatCommon.Base02.Cs02MessageBox._toolTip">
            <summary>
            DEV 的提示信息控件
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02MessageBox.ShowInfo(System.String,System.String)">
            <summary>
            显示信息
            </summary>
            <param name="strInfoMsg">信息内容</param>
            <param name="strTitle">信息标题</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02MessageBox.ShowError(System.String,System.String)">
            <summary>
            显示错误
            </summary>
            <param name="strErrorMsg">错误内容</param>
            <param name="strTitle">错误标题</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02MessageBox.ShowWarning(System.String,System.String)">
            <summary>
            显示警告
            </summary>
            <param name="strErrorMsg">警告内容</param>
            <param name="strTitle">警告标题</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02MessageBox.ShowYesNo(System.String,System.String,System.Int32)">
            <summary>
            显示问题
            </summary>
            <param name="strErrorMsg">提示内容</param>
            <param name="strTitle">标题</param>
            <param name="iDefaultButton">缺省按钮</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02MessageBox.ShowYesNoCancel(System.String,System.String,System.Int32)">
            <summary>
            显示问题
            </summary>
            <param name="strErrorMsg">提示内容</param>
            <param name="strTitle">标题</param>
            <param name="iDefaultButton">缺省按钮</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02MessageBox.ShowAutoClose(System.String,System.Int32,System.String)">
            <summary>
            自动关闭的提示框
            </summary>
            <param name="strErrorMsg">信息</param>
            <param name="strTitle">标题</param>
            <param name="iDelay">延迟秒数</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02MessageBox.ShowToolTip(System.String,System.Int32)">
            <summary>
            显示提示信息
            </summary>
            <param name="strContent">提示内容</param>
            <param name="iDelay">显示时间</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02MessageBox.ShowToolTip(System.String,System.String,System.Int32)">
            <summary>
            显示提示信息
            </summary>
            <param name="strTitle">提示标题</param>
            <param name="strContent">提示内容</param>
            <param name="iDelay">显示时间</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02MessageBox.CloseWait">
            <summary>
            关闭等待提示
            </summary>
        </member>
        <member name="T:PlatCommon.Base02.Cs02MyEval">
            <summary>
            动态编译执行字符串形式表达式类
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02MyEval.Eval(System.String)">
            <summary>
            CodeDom动态编译执行表达式
            </summary>
            <param name="expression">表达式字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02MyEval.EvalSql(System.String)">
            <summary>
            利用Dataset的Express表达式，但仅仅支持sql表达式
            </summary>
            <param name="expression">SQL表达式</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02MyEval.EvalBoolean(System.String)">
            <summary>
            CodeDom动态编译执行判断逻辑的表达式,返回判断结果
            </summary>
            <param name="expression">判断表达式</param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Base02.Cs02NotificationObject">
            <summary>
            监听对象，实现数据更新广播，及时刷新显示
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02NotificationObject.RaisePropertyChanged``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            监听对象，实现数据更新广播，及时刷新显示
            </summary>
            <typeparam name="T"></typeparam>
            <param name="action"></param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02NotificationObject.RaisePropertyChanged(System.String)">
            <summary>
            
            </summary>
            <param name="propertyName"></param>
        </member>
        <member name="E:PlatCommon.Base02.Cs02NotificationObject.PropertyChanged">
            <summary>
            
            </summary>
        </member>
        <member name="T:PlatCommon.Base02.Cs02StringHelper">
            <summary>
            字符串处理类
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.#ctor">
            <summary>
            本对象不能实例化
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetSplitChar(System.String)">
            <summary>
            获取字符串中存在的分隔符, 支持',', ':', ';'三种, 包括全角
            </summary>
            <param name="strString">传入的字符串</param>
            <returns>如果存在分隔符 , 返回分隔符; 如果不存在, 返回null</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetParameterValue(System.String,System.String)">
            <summary>
            获取值对
            </summary>
            <remarks>示例: Data Source = orcl, user id = system, pwd = xiou</remarks>
            <param name="strString">源字符串</param>
            <param name="strParaName">参数名称, 不区分大小写</param>
            <returns>若不存在指定名称的参数则返回空, 否则返回相应的值(大写)</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.SetParameterValue(System.String,System.String,System.String)">
            <summary>
            设置参数的值
            </summary>
            <remarks>示例: Data Source = orcl, user id = system, pwd = xiou</remarks>
            <param name="strString">源字符串</param>
            <param name="strParaName">参数名称, 不区分大小写</param>
            <param name="strParaValueNew">新参数值</param>
            <returns>替换参数值后返回的字符串</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetRomaNum(System.Int32)">
            <summary>
            获取数字的罗马表示
            </summary>
            <param name="num">数字</param>
            <returns>罗马字母表示的数字</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.SqlConvert(System.String)">
            <summary>
            字符串前后加单引号, 字符串内部单引号改成双引号
            </summary>
            <param name="strText">源文本</param>
            <returns>转换后的文本</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.Convert(System.String)">
            <summary>
            字符串内部单引号改成双引号
            </summary>
            <param name="strText">源文本</param>
            <returns>转换后的文本</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetSqlDate(System.DateTime)">
            <summary>
            获取SQL的日期表达
            </summary>
            <param name="dtmDate">时间</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetOraDate(System.DateTime)">
            <summary>
            获取Oracle的日期表达
            </summary>
            <param name="dtmDate">时间</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetOraDateShort(System.DateTime)">
            <summary>
            获取Oracle的日期表达（短日期格式）
            </summary>
            <param name="dtmDate">时间</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.CStr(System.Object)">
            <summary>
            转换为字符
            </summary>
            <param name="obj">object</param>
            <returns>string</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetOraDate(System.String)">
            <summary>
            Oracle的日期表达
            </summary>
            <param name="dtmDate">时间</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.Repeat(System.String,System.Int32)">
            <summary>
            重复生成字符串
            </summary>
            <param name="strSource">被重复的字符串</param>
            <param name="iTimes">重复次数</param>
            <returns>重复生成的字符串</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.Right(System.String,System.Int32)">
            <summary>
            从字符串右边取字符
            </summary>
            <param name="strSource">要取值的字符串</param>
            <param name="iLength">要取出的字符的个数，如果length大于字符串的长度，则取整个字符串</param>
            <returns>取得的字符串</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.AppendString(System.String,System.String,System.String)">
            <summary>
            拼接字符串
            </summary>
            <param name="strSource">基串</param>
            <param name="strNewStr">要被追加在基串后面的字符串</param>
            <param name="strSplit">加在两个字符串之间的间隔符。如果基串为空的话，则忽略flag</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.UpperFirstChar(System.String)">
            <summary>
            首字母大写
            </summary>
            <param name="strSource">要转换的字符穿表达式</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.LowerFirstChar(System.String)">
            <summary>
            首字母小写
            </summary>
            <param name="strSource">要转换的字符穿表达式</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.IsInt(System.String)">
            <summary>
            检测字符串是否可转换成int型
            </summary>
            <param name="strSource">传入的字符串</param>
            <returns>true:可以转换，false:不可以转换</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.IsFloat(System.String)">
            <summary>
            检测字符串是否可转换成float型
            </summary>
            <param name="strSource">传入的字符串</param>
            <returns>true:可以转换，false:不可以转换</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.IsDouble(System.String)">
            <summary>
            检测字符串是否可转换成double型
            </summary>
            <param name="strSource">传入的字符串</param>
            <returns>true:可以转换，false:不可以转换</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.IsLong(System.String)">
            <summary>
            检测字符串是否可转换成long型
            </summary>
            <param name="strSource">传入的字符串</param>
            <returns>true:可以转换，false:不可以转换</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.IsIPAdress(System.String)">
            <summary>
            检测字符串是否可转换成IPv4地址
            </summary>
            <param name="strSource">传入的字符串</param>
            <returns>true:可以转换，false:不可以转换</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.IsNumeric(System.String)">
            <summary>
            检测字符串是否可转换成int型并是否在某个值范围内
            </summary>
            <param name="strSource">要转换的字符串</param>
            <returns>true:可以转换，false:不可以转换</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.IsNumeric(System.String,System.Double,System.Double,System.Boolean)">
            <summary>
            检测字符串是否可转换成int型并是否在某个值范围内
            </summary>
            <param name="strSource">要转换的字符串</param>
            <param name="minValue">允许的最小值，不限的话可用int.MinValue表示</param>
            <param name="maxValue">允许的最大值，不限的话可用int.maxValue表示</param>
            <param name="AllowEmpty">是否允许空字符串。空字符串不收最大和最小值的约束</param>
            <returns>true:可以转换，false:不可以转换</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.IsDate(System.String)">
            <summary>
            检测字符串是否可转换成DateTime型
            </summary>
            <param name="strSource">要转换的字符串</param>
            <returns>true:可以转换，false:不可以转换</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.IsNum(System.String)">
            <summary>
            判断一个字符串是不是正整数
            </summary>
            <remarks>仅包含数字</remarks>
            <param name="strSource">待验证的字符串</param>
            <returns>TRUE: 符合要求; FALSE: 不符合要求</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.IsNumber(System.String)">
            <summary>
            用正则表达式检查是否是数字
            </summary>
            <param name="strNumber">字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.IsTime(System.String)">
            <summary>
            检查一个字符串是不是合法的时间格式,如果是: 把输入字符串转换成标准的日期格式HH:MM:SS
            </summary>
            <param name="strSource">表示时间的字符</param>
            <returns>TRUE: 是; FALSE: 不是</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.IsTime(System.String,System.String@)">
            <summary>
            检查一个字符串是不是合法的时间格式
            如果是: 把输入字符串转换成标准的日期格式HH:MM:SS
            </summary>
            <param name="strSource">表示时间的字符</param>
            <param name="formatedStr">格式化后的字符串</param>
            <returns>TRUE: 是; FALSE: 不是</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.IsNullOrEmpty(System.Object)">
            <summary>
            判断一个对象是否为空
            </summary>
            <param name="objSrc">要判断的对象</param>
            <returns>TRUE: 为空; FALSE: 非空</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.IsNullOrEmpty(System.Object,System.String)">
            <summary>
            判断一个对象是否为空
            </summary>
            <param name="objSrc">要判断的对象</param>
            <param name="emptyValue">空值</param>
            <returns>TRUE: 为空; FALSE: 非空</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.FormatDecimal(System.String,System.String)">
            <summary>
            格式化数字
            </summary>
            <param name="strValue">值</param>
            <param name="strFormat">格式</param>
            <returns>以指定格式显示值</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.FormatNumber(System.Double,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            格式化数字
            </summary>
            <param name="dValue">要格式化的数字</param>
            <param name="precision">精度，即取小数点后几位</param>
            <param name="groupDigits">是否使用逗号分割符</param>
            <param name="round">是否四舍五入</param>
            <param name="null0">无效的值是否显示为0，为否的话返回空字符串</param>
            <returns>格式化后的字符串</returns> 
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.FormatNumber(System.String,System.Int32,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            格式化数字
            </summary>
            <param name="strValue">要格式话的数字</param>
            <param name="precision">精度，即取小数点后几位</param>
            <param name="groupDigits">是否使用逗号分割符</param>
            <param name="round">是否四舍五入</param>
            <param name="null0">无效的值是否显示为0，为否的话返回空字符串</param>
            <returns>格式化后的字符串</returns> 
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.CompareString(System.String,System.String)">
            <summary>
            比较字符穿大小
            </summary>
            <param name="str1">字符串1</param>
            <param name="str2">字符串2</param>
            <remarks>逐字母比较, 以ascii码为依据，短的比长的小。返回值-1/0/1分别表示小于/等于/大于</remarks>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.LikeCompare(System.String,System.String)">
            <summary>
            支持通配符的字符串比较
            </summary>
            <param name="strPattern">字符串模板, 如"hou*"</param>
            <param name="strInput">要比较的字符串</param>
            <returns>是否匹配</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.SplitStrByCommChar(System.String)">
            <summary>
            用通用分隔符(,，;；)分隔字符串, 去除空串
            </summary>
            <param name="strSource">字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.ChangeIntArrayToString(System.Int32[])">
            <summary>
            int型数组转换为字符串
            </summary>
            <param name="iArrData">int型数组</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.ChangeStringToIntArray(System.String)">
            <summary>
            字符串转换为int型数组
            </summary>
            <param name="strSource">字符串</param>
            <returns>int型数组</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetChineseSpell(System.String)">
            <summary>
            获取汉字拼音的第一个字母
            </summary>
            <param name="strText">汉字字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetChineseSpell(System.String[])">
            <summary>
            根据一组汉字的获取他们的一组第一个字母拼音
            </summary>
            <param name="strText">汉字字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.getSpell(System.String)">
            <summary>
            获取一个汉字的拼音首字母
            </summary>
            <param name="strText">汉字字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetPYString(System.String)">
            <summary>
            汉字转拼音缩写
            </summary>
            <param name="strText">汉字字符串</param>
            <returns>拼音首字母字符串</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetPYChar(System.String)">
            <summary>
            取单个字符的拼音声母
            </summary>
            <param name="strText">要转换的单个汉字</param>
            <returns>拼音声母</returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.NumberToChnText(System.String)">
            <summary>
            把数字转换为汉字
            </summary>
            <param name="strNumString">数字字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.StringToUnicode(System.String)">
            <summary>  
            字符串转为UniCode码字符串  
            </summary>  
            <param name="strSource">字符串</param>  
            <returns>UniCode码字符串</returns>  
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.UnicodeToString(System.String)">
            <summary>  
            Unicode字符串转为正常字符串  
            </summary>  
            <param name="strSource">UniCode码字符串</param>  
            <returns>字符串</returns>  
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetSqlInFilter(System.String,System.String[])">
            <summary>
            获取SQL的IN 条件 例：ITEM_ID IN ('1', '2')
            </summary>
            <param name="strColName">列名</param>
            <param name="strArrValue">值数组</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetSqlInFilter(System.String,System.Collections.ArrayList)">
            <summary>
            获取SQL的IN 条件 例：ITEM_ID IN ('1', '2')
            </summary>
            <param name="colName"></param>
            <param name="valList"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetSqlTextInsert(System.String,System.Collections.Hashtable)">
            <summary>
            生成插入SQL语句
            </summary>
            <param name="strTableName">表名</param>
            <param name="hasItems">参数表</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetSqlTextUpdate(System.String,System.Collections.Hashtable,System.String)">
            <summary>
            生成Update的SQL语句
            </summary>
            <param name="strTableName">表名</param>
            <param name="hasItems">参数表</param>
            <param name="strFilter">条件</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetSqlTextEqual(System.Collections.Hashtable)">
            <summary>
            生成条件SQL语句
            </summary>
            <param name="hasItems">参数表</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetSqlInDay(System.String,System.DateTime)">
            <summary>
            获取SQL查询的在某一天内条件
            </summary>
            <param name="strColName">列名</param>
            <param name="dtmDate">时间</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02StringHelper.GetNullValue(System.String,System.String)">
            <summary>
            获取空值
            </summary>
            <param name="str"></param>
            <param name="nullValue"></param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Base02.Cs02TxtHelper">
            <summary>
            文本文件类
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02TxtHelper.Txt2DataTable(System.String,System.Char,System.Boolean)">
            <summary>
            文本文件转为DataTable
            </summary>
            <param name="strFileName">TXT文件名</param>
            <param name="cSplit">分隔字符</param>
            <param name="bHasTitle">是否有标题行</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02TxtHelper.WriteTxtLog(System.String,System.String,System.String,System.String)">
            <summary>
            写日志
            </summary>
            <param name="strLogFile">日志文件名</param>
            <param name="strFunction">执行功能</param>
            <param name="strInput">输入参数</param>
            <param name="strOutput">输出参数</param>
        </member>
        <member name="T:PlatCommon.Base02.Cs02XMLSetting">
            <summary>
            系统设置XML文件类
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.GetDocument(System.String)">
            <summary>
            加载XML文件
            </summary>
            <param name="strXmlFileName">XML文件名</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.ReadKey(System.String,System.String,System.String,System.String)">
            <summary>
            读取某一节(Section)的某一个值(Key)
            </summary>
            <param name="strXmlFileName">XML文件名</param>
            <param name="strSection">Section</param>
            <param name="strKey">Key</param>
            <param name="strDefalutValue">缺省值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.WriteKey(System.String,System.String,System.String,System.String)">
            <summary>
            设置某一节(Section)的某一个值(Key)的值
            </summary>
            <param name="strXmlFileName">XML文件名</param>
            <param name="strSection">Section</param>
            <param name="strKey">Key</param>
            <param name="strValue">值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.ReadSection(System.String,System.String)">
            <summary>
            读取某一节(Section)的所有值(Key)
            </summary>
            <param name="strXmlFileName">XML文件名</param>
            <param name="strSection">Section</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.WriteSection(System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            设置某一节(Section)的所有值(Key)
            </summary>
            <param name="strXmlFileName">XML文件名</param>
            <param name="strSection">Section</param>
            <param name="keyDictionary">数据字典</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.ReadXmlToDataTable(System.String)">
            <summary>
            读取XML到DataTable
            </summary>
            <param name="strXmlFileName">XML文件名</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.ReadXMLKeyFromStr(System.String,System.String,System.String,System.String)">
            <summary>
            读取Xml串某一节(Section)的某一个值(Key)
            </summary>
            <param name="strSourceXML">源XML串</param>
            <param name="strSection">Section</param>
            <param name="strKey">Key</param>
            <param name="strDefalutValue">缺省值</param>
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.ReadSectionFromStr(System.String,System.String)">
            <summary>
            读取Xml串某一节(Section)的所有值(Key)
            </summary>
            <param name="strSourceXML">源XML串</param>
            <param name="strSection">节</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.DataTableToXml(System.Data.DataTable)">
            <summary> 
            将DataTable对象转换成XML字符串 
            </summary> 
            <param name="dtSource">DataTable对象</param> 
            <returns>XML字符串</returns> 
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.DataSetToXml(System.Data.DataSet,System.Int32)">
            <summary> 
            将DataSet对象中指定的Table转换成XML字符串 
            </summary> 
            <param name="dsSource">DataSet对象</param> 
            <param name="iTableIndex">DataSet对象中的Table索引</param> 
            <returns>XML字符串</returns> 
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.DataViewToXml(System.Data.DataView)">
            <summary> 
            将DataView对象转换成XML字符串 
            </summary> 
            <param name="dvSource">DataView对象</param> 
            <returns>XML字符串</returns> 
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.DataTableToXmlFile(System.Data.DataTable,System.String)">
            <summary> 
            将DataTable对象数据保存为XML文件 
            </summary> 
            <param name="dtSource">DataSet</param> 
            <param name="strFilePath">XML文件路径</param> 
            <returns>bool值</returns> 
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.DataSetToXmlFile(System.Data.DataSet,System.Int32,System.String)">
            <summary> 
            将DataSet对象中指定的Table转换成XML文件 
            </summary> 
            <param name="dsSource">DataSet对象</param> 
            <param name="iTableIndex">DataSet对象中的Table索引</param> 
            <param name="strFilePath">xml文件路径</param> 
            <returns>bool]值</returns> 
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.DataViewToXmlFile(System.Data.DataView,System.String)">
            <summary> 
            将DataView对象转换成XML文件 
            </summary> 
            <param name="dvSource">DataView对象</param> 
            <param name="strFilePath">xml文件路径</param> 
            <returns>bool]值</returns> 
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.XmlToDataSet(System.String)">
            <summary> 
            将Xml串转换成DataSet对象 
            </summary> 
            <param name="strXML">Xml内容字符串</param> 
            <returns>DataSet对象</returns> 
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.XmlToDataTable(System.String)">
            <summary> 
             将Xml字符串转换成DataTable对象 
             </summary> 
             <param name="strXML">Xml字符串</param> 
             <returns>DataTable对象</returns> 
        </member>
        <member name="M:PlatCommon.Base02.Cs02XMLSetting.XmlFileToDataSet(System.String)">
            <summary> 
            读取Xml串信息,并转换成DataSet对象 
            </summary> 
            <param name="strFilePath">Xml文件地址</param> 
            <returns>DataSet对象</returns> 
        </member>
        <member name="T:PlatCommon.Base02.DataTableHelper">
            <summary>
            DataTable帮助类
            </summary>	
        </member>
        <member name="M:PlatCommon.Base02.DataTableHelper.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:PlatCommon.Base02.DataTableHelper.HasChanged(System.Data.DataTable)">
            <summary>
            判断数据表数据是否发生变化
            </summary>        
        </member>
        <member name="T:PlatCommon.Common.AutoSettingIDProvider">
            <summary>
            自动ID处理器
            </summary>
        </member>
        <member name="T:PlatCommon.Common.AutoSettingIDProvider.AutoSettingIDInfo">
            <summary>
            自动ID信息
            </summary>
        </member>
        <member name="F:PlatCommon.Common.AutoSettingIDProvider.LOG_FILE_NAME">
            <summary>
            日志文件名
            </summary>
        </member>
        <member name="M:PlatCommon.Common.AutoSettingIDProvider.GetId(System.String,System.String,System.String@,System.Int32@,Utility.OracleODP.OracleBaseClass,System.Collections.Hashtable,System.Boolean)">
            <summary>
            读取ID或序号，不回写更新ID
            </summary>
            <param name="typeName">类型名称</param>
            <param name="hisUnitCode">医院编码</param>
            <param name="sCurrentIDValue">输出类型，格式化后的当前值</param>
            <param name="iCurrentIDValue">输出类型，当前值整数值</param>
            <param name="db">默认参数：用于控制事务的db，可为null</param>
            <param name="hasParams">可选参数：用于生成SQL语句结果前缀的参数，可为空</param>
            <param name="sqlFirst">可选参数：true为SQL语句结果前置， false为固定前缀前置，默认为true</param>
            <returns>true成功，false失败</returns>
        </member>
        <member name="M:PlatCommon.Common.AutoSettingIDProvider.GetUpdateIdSql(System.String,System.String,System.String@,System.Int32@,System.String@,System.Int32@,System.String@,Utility.OracleODP.OracleBaseClass,System.Collections.Hashtable,System.Boolean)">
            <summary>
            得到更新Id值的sql语句，方法会返回update语句用于更新id值，一般用于调用者在保存前执行更新Id的SQL语句
            </summary>
            <param name="typeName">类型名称</param>
            <param name="hisUnitCode">医院编码</param>
            <param name="sCurrentIDValue">输出：格式化后的当前ID值</param>
            <param name="iCurrentIDValue">输出：当前ID值整数变量</param>
            <param name="sNextIDValue">输出：执行更新操作后的计算出的格式化后的新值</param>
            <param name="iNextIDValue">输出：执行更新操作后的计算出的新值</param>
            <param name="updateSql">输出：更新自动ID表的语句，用于调用者执行更新自动ID表</param>
            <param name="db">默认参数：用于控制事务的db，可为null</param>
            <param name="hasParams">可选参数：用于生成SQL语句结果前缀的参数，可为空</param>
            <param name="sqlFirst">可选参数：true为SQL语句结果前置， false为固定前缀前置，默认为true</param>
            <returns>true成功，false失败</returns>
        </member>
        <member name="M:PlatCommon.Common.AutoSettingIDProvider.UpdateId(System.String,System.String,System.String@,System.Int32@,System.String@,System.Int32@,Utility.OracleODP.OracleBaseClass,System.Collections.Hashtable,System.Boolean)">
            <summary>
            回写Id，该方法会计算新值自动更新Id，保存到AUTO_SETTING_ID表中，一般用于保存前更新Id
            </summary>
            <param name="typeName">类型名称</param>
            <param name="hisUnitCode">医院编码</param>
            <param name="iCurrentIDValue">输出类型：当前值</param>
            <param name="sCurrentIDValue">输出类型：格式化后的当前ID值</param>
            <param name="sNexIDValue">输出类型：执行更新操作后的计算出的格式化后的新值</param>
            <param name="iNextIDValue">输出类型：执行更新操作后的计算出的新值</param>
            <param name="db">用于控制事务的db，必选项</param>
            <param name="hasParams">可选参数：用于生成SQL语句结果前缀的参数，可为空</param>
            <param name="sqlFirst">可选参数：true为SQL语句结果前置， false为固定前缀前置，默认为true</param>
            <returns>true成功，false失败</returns>
        </member>
        <member name="M:PlatCommon.Common.AutoSettingIDProvider.getAutoSettingIDInfo(System.String,System.String,PlatCommon.Common.AutoSettingIDProvider.AutoSettingIDInfo@,Utility.OracleODP.OracleBaseClass)">
            <summary>
            查询AUTO_SETTING_ID表获得指定类型名称的自动ID的信息(不回写ID)
            </summary>
            <param name="typeName">类型名称</param>
            <param name="hisUnitCode">医院编码</param>
            <param name="idInfo">输出类型参数 自动ID信息</param>
            <param name="db">用于事务控制的db，可选项</param>
            <returns>true成功，false失败</returns>
        </member>
        <member name="M:PlatCommon.Common.AutoSettingIDProvider.formatID(PlatCommon.Common.AutoSettingIDProvider.AutoSettingIDInfo,System.Collections.Hashtable,System.Boolean,System.String@)">
            <summary>
            获得根据规则格式化后的ID值
            </summary>
            <param name="idInfo">自动ID信息</param>
            <param name="sIDValue">输出类型参数，符合格式的ID值字符串</param>
            <returns>true成功，false失败</returns>
        </member>
        <member name="M:PlatCommon.Common.AutoSettingIDProvider.getNumberValueDBFieldValue(System.Object)">
            <summary>
            获得数据库字段的数值类型的值
            </summary>
            <param name="dbValue">DataRow读取出来的数据库字段值</param>
            <returns>true成功，false失败</returns>
        </member>
        <member name="M:PlatCommon.Common.AutoSettingIDProvider.getStringValueDBFiledValue(System.Object)">
            <summary>
            获得数据库字段的文本字符类型的值
            </summary>
            <param name="dbValue">DataRow读取出来的数据库字段值</param>
            <returns>true成功，false失败</returns>
        </member>
        <member name="M:PlatCommon.Common.AutoSettingIDProvider.calculationNewID(System.Int32,System.Int32,System.Int32,System.Int32@)">
            <summary>
            计算新值
            </summary>
            <param name="iIDValue">原值</param>
            <param name="valueLength">变量长度范围</param>
            <param name="upgrade">是否允许升位</param>
            <param name="iNewIDValue">输出类型，新值</param>
            <returns>true成功，false失败</returns>
        </member>
        <member name="M:PlatCommon.Common.AutoSettingIDProvider.getUpdateIdSqlStatement(System.String,System.String,System.Int32,System.String@,System.Int32@,System.String@,Utility.OracleODP.OracleBaseClass,System.Collections.Hashtable,System.Boolean)">
            <summary>
            得到更新id值的sql语句，只需要提供当前值，方法会返回计算新id后的update语句
            </summary>
            <param name="typeName">类型名称</param>
            <param name="hisUnitCode">医院编码</param>
            <param name="iCurrentIDValue">当前值数字</param>        
            <param name="iNewIDValue">输出类型：计算后的新值</param>
            <param name="updateSql">输出值：更新自动ID表的update语句</param>
            <param name="db">用于控制事务的db</param>
            <returns>true成功，false失败</returns>
        </member>
        <member name="M:PlatCommon.Common.AutoSettingIDProvider.writeIDValue(System.String,System.String,System.Int32,System.String@,System.Int32@,System.Collections.Hashtable,Utility.OracleODP.OracleBaseClass)">
            <summary>
            回写ID，只需要提供当前值，方法会自动计算+1，保存到AUTO_SETTING_ID表中
            </summary>
            <param name="typeName">类型名称</param>
            <param name="iCurrentIDValue">整数类型的当前值</param>
            <param name="hisUnitCode">医院编码</param>
            <param name="db">用于事务控制的db</param>
            <returns>true成功；false失败</returns>
        </member>
        <member name="M:PlatCommon.Common.AutoSettingIDProvider.getSqlTextByParam(System.String,System.Collections.Hashtable)">
            <summary>
            获取SQL语句
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.AutoSettingIDProvider.getErrorMsg(System.Exception,System.Reflection.MethodBase)">
            <summary>
            获得异常的内容
            </summary>
            <param name="exception">异常</param>
            <param name="methodBase">方法的信息</param>
            <returns>自定义格式的错误信息</returns>
        </member>
        <member name="M:PlatCommon.Common.AutoSettingIDProvider.getErrorMsg(System.String,System.Reflection.MethodBase)">
            <summary>
            获得异常的内容
            </summary>
            <param name="message">错误字符串</param>
            <param name="methodBase">方法的信息</param>
            <returns>自定义格式的错误信息</returns>
        </member>
        <member name="P:PlatCommon.Common.ClassInsurVsMessage.insur_code">
            <summary>
            医保编码
            </summary>
        </member>
        <member name="P:PlatCommon.Common.ClassInsurVsMessage.insur_scale">
            <summary>
            医保比例
            </summary>
        </member>
        <member name="P:PlatCommon.Common.ClassInsurVsMessage.insur_level">
            <summary>
            医保等级
            </summary>
        </member>
        <member name="P:PlatCommon.Common.ClassInsurVsMessage.insur_name">
            <summary>
            医保名称
            </summary>
        </member>
        <member name="P:PlatCommon.Common.ClassInsurVsMessage.insur_memo">
            <summary>
            医保备注说明
            </summary>
        </member>
        <member name="P:PlatCommon.Common.ClassInsurVsMessage.insur_class">
            <summary>
            医保类别
            </summary>
        </member>
        <member name="P:PlatCommon.Common.ClassInsurVsMessage.insur_xzbs">
            <summary>
            医保限制标识
            </summary>
        </member>
        <member name="P:PlatCommon.Common.ClassInsurVsMessage.insur_xzsm">
            <summary>
            医保限制说明
            </summary>
        </member>
        <member name="M:PlatCommon.Common.ClosedLoopBusiness.CreateDataTable(System.String)">
            <summary>
            创建DataTable方法
            </summary>
            <param name="type">类型：1医嘱</param>
            <returns>DataTable表结构</returns>
        </member>
        <member name="M:PlatCommon.Common.ClosedLoopBusiness.DoBusiness(System.String,System.Data.DataTable)">
            <summary>
            业务入口方法
            </summary>
            <param name="type">类型：1医嘱</param>
            <param name="dt"></param>
        </member>
        <member name="M:PlatCommon.Common.ClosedLoopBusiness.Init">
            <summary>
            初始化方法
            </summary>
            <returns> </returns>
        </member>
        <member name="M:PlatCommon.Common.ClosedLoopBusiness.PostXml(System.String)">
            <summary>
            推送xml方法
            </summary>
            <param name="content">xml字符串</param>
        </member>
        <member name="M:PlatCommon.Common.ClosedLoopBusiness.OrdersClosedLoop">
            <summary>
            医嘱闭环入口方法
            </summary>
        </member>
        <member name="M:PlatCommon.Common.ClosedLoopBusiness.OrdersClosedLoop(System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            医嘱闭环实体方法
            </summary>
            <param name="actionType">闭环节点 M1医嘱开立 M2转抄 M3校对 M4签名等等，参考平台提供的闭环字典</param>
            <param name="patientId"></param>
            <param name="visitId"></param>
            <param name="orderNo"></param>
            <param name="orderSubNo"></param>
            <param name="orderClass"></param>
            <param name="orderStatus"></param>
        </member>
        <member name="T:PlatCommon.Common.Converter">
            <summary>
            功能描述：对象安全转换类  
            </summary>
        </member>
        <member name="M:PlatCommon.Common.Converter.ToString(System.Object)">
            <summary>
            把对象转换为字符串
            </summary>
            <param name="obj">要被转换的对象</param>
            <returns>字符串</returns>
        </member>
        <member name="M:PlatCommon.Common.Converter.ToInt(System.Object)">
            <summary>
            把对象转换为Int
            </summary>
            <param name="obj">要被转换的对象</param>
            <returns>数字</returns>
        </member>
        <member name="M:PlatCommon.Common.Converter.ToInt(System.Object,System.Int32)">
            <summary>
            把对象转换为Int
            </summary>
            <param name="obj">要被转换的对象</param>
            <param name="defValue">缺省值</param>
            <returns>数字</returns>
        </member>
        <member name="M:PlatCommon.Common.Converter.ToDeci(System.Object)">
            <summary>
            把对象转换为Deci
            </summary>
            <param name="obj">要被转换的对象</param>
            <returns>数字</returns>
        </member>
        <member name="M:PlatCommon.Common.Converter.ToMoneyStr(System.Double)">
            <summary>
            金额小写转中文大写。
            整数支持到万亿；小数部分支持到分(超过两位将进行Banker舍入法处理)
            </summary>
            <param name="Num">需要转换的双精度浮点数</param>
            <returns>转换后的字符串</returns>
        </member>
        <member name="T:PlatCommon.Common.Result1`1">
            <summary>
            公共泛型类
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:PlatCommon.Common.Result1`1.#ctor(`0)">
            <summary>
            正常返回参数使用
            </summary>
            <param name="t">object类型</param>
        </member>
        <member name="M:PlatCommon.Common.Result1`1.#ctor(System.Exception)">
            <summary>
            出现异常时使用
            </summary>
            <param name="ex"></param>
        </member>
        <member name="P:PlatCommon.Common.Result1`1.HasError">
            <summary>
            是否存在错误
            </summary>
        </member>
        <member name="P:PlatCommon.Common.Result1`1.ErrMsg">
            <summary>
            错误输出语句
            </summary>
        </member>
        <member name="P:PlatCommon.Common.Result1`1.MsgTip">
            <summary>
            提示信息
            </summary>
        </member>
        <member name="P:PlatCommon.Common.Result1`1.Data">
            <summary>
            数据集
            </summary>
        </member>
        <member name="F:PlatCommon.Common.database.Area_Type.strShengCode">
            <summary>
            省代码
            </summary>
        </member>
        <member name="F:PlatCommon.Common.database.Area_Type.strShengName">
            <summary>
            省名称
            </summary>
        </member>
        <member name="F:PlatCommon.Common.database.Area_Type.strCityCode">
            <summary>
            市代码
            </summary>
        </member>
        <member name="F:PlatCommon.Common.database.Area_Type.strCityName">
            <summary>
            市名称
            </summary>
        </member>
        <member name="F:PlatCommon.Common.database.Area_Type.strAreaCode">
            <summary>
            县代码
            </summary>
        </member>
        <member name="F:PlatCommon.Common.database.Area_Type.strAreaName">
            <summary>
            县名称
            </summary>
        </member>
        <member name="M:PlatCommon.Common.database.connDB">
            <summary>
            返回打开连接的OleDbConnect对象实例
            </summary>
            <param name="strconn"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.database.getDS(System.String)">
            <summary>
            根据指定的sql语句，返回相应的dataset对象实例
            </summary>
            <param name="strSQL"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.database.getBreakAreaCode(System.String)">
            <summary>
            分割地区代码，分割成省、市、县
            </summary>
            <param name="strCode"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.database.saveSql(System.String)">
            <summary>
            执行指定的sql语句
            成功返回true,否则返回false
            </summary>
            <param name="strSQL"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.database.saveSql(System.String,System.Data.OleDb.OleDbTransaction,System.Data.OleDb.OleDbConnection)">
            <summary>
            带事物的sql语句执行操作,操作失败会自动回滚
            </summary>
            <param name="strSQL"></param>
            <param name="tranObj"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.database.isSFZH(System.String)">
            <summary>
            验证身份证号
            </summary>
            <param name="strID"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.database.fGetICDDesc(System.String)">
            <summary>
            根据diagnonsis_code值返回字典标准名称
            </summary>
            <param name="strDescCode"></param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Common.DataGridViewMode">
            <summary>
            DataGridView的显示模式
            </summary>
        </member>
        <member name="T:PlatCommon.Common.DataGridViewHelper">
            <summary>
            DataGridView 助手
            </summary>
        </member>
        <member name="M:PlatCommon.Common.DataGridViewHelper.SetGridMode(System.Windows.Forms.DataGridView,PlatCommon.Common.DataGridViewMode)">
            <summary>
            设置DataGridView的属性
            </summary>
            <param name="dgv"></param>
            <param name="viewMode"></param>
        </member>
        <member name="M:PlatCommon.Common.DataGridViewHelper.GetConfigSchema">
            <summary>
            生成配置文件结构
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataGridViewHelper.SaveColumnConfig(System.Data.DataSet,System.String)">
            <summary>
            保存列的配置文件
            </summary>
            <param name="dsColConfig"></param>
            <param name="dgvId"></param>
        </member>
        <member name="M:PlatCommon.Common.DataGridViewHelper.SaveColumnConfig(System.Windows.Forms.DataGridView,System.String)">
            <summary>
            保存列宽
            </summary>
            <param name="dgv"></param>
            <param name="dgvId"></param>
        </member>
        <member name="M:PlatCommon.Common.DataGridViewHelper.GetColumnConfig(System.String)">
            <summary>
            获取DataGridView的列配置信息
            </summary>
            <param name="dgvId"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataGridViewHelper.GetColumnConfig(System.Data.DataSet,System.String)">
            <summary>
            DataGridView的列配置信息, 从数据源与配置文件中获取
            </summary>
            <param name="dsSrc"></param>
            <param name="dgvId"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataGridViewHelper.AddColByConfig(System.Windows.Forms.DataGridView,System.Data.DataSet)">
            <summary>
            通过配置信息生成DataGridView的列
            </summary>
            <param name="dgv"></param>
            <param name="ds"></param>
        </member>
        <member name="M:PlatCommon.Common.DataGridViewHelper.AddColByConfig(System.Windows.Forms.DataGridView,System.String)">
            <summary>
            通过配置信息生成DataGridView的列
            </summary>
            <param name="dgv"></param>
            <param name="ds"></param>
        </member>
        <member name="M:PlatCommon.Common.DataGridViewHelper.ResetShowOrder(System.Windows.Forms.DataGridView@,System.Data.DataSet@,System.String)">
            <summary>
            更新现有排序
            </summary>
            <param name="dgv"></param>
            <param name="ds"></param>
            <param name="colNameOrder"></param>
        </member>
        <member name="M:PlatCommon.Common.DataGridViewHelper.MoveUpRow(System.Windows.Forms.DataGridView@,System.Data.DataSet@,System.String)">
            <summary>
            向上移动
            </summary>
            <param name="dgv"></param>
            <param name="ds"></param>
            <param name="colNameOrder"></param>
        </member>
        <member name="M:PlatCommon.Common.DataGridViewHelper.MoveDownRow(System.Windows.Forms.DataGridView@,System.Data.DataSet@,System.String)">
            <summary>
            向下移动
            </summary>
            <param name="dgv"></param>
            <param name="ds"></param>
            <param name="colNameOrder"></param>
        </member>
        <member name="M:PlatCommon.Common.DataGridViewHelper.SetCurrRow(System.Windows.Forms.DataGridView@,System.String,System.String)">
            <summary>
            定位当前行
            </summary>
            <param name="dgv"></param>
            <param name="keyColName"></param>
            <param name="keyValue"></param>
        </member>
        <member name="T:PlatCommon.Common.DataSetHelper">
            <summary>
            DataSet操作类
            </summary>	
        </member>
        <member name="P:PlatCommon.Common.DataSetHelper.IsConnected">
            <summary>
            是否与数据库连接
            </summary>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.HasRecord(System.Data.DataSet)">
            <summary>
            数据集是否具有记录
            </summary>
            <param name="dsData">数据集</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.ConvertRowToCol(System.Data.DataTable,System.String,System.String)">
            <summary>
            将行转换为列
            </summary>
            <param name="strColName">列名称</param>
            <param name="strColValue">列值</param>
            <param name="dtSrc">源DataTable</param>
            <returns>转换后的Datatable</returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.GetDataSetFromFile(System.String)">
            <summary>
            XML文件，转成DATASET 
            </summary>
            <param name="xmlFile">要转换的XML文件的文件名</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.DataSetToXmlFile(System.Data.DataSet,System.String)">
            <summary>
            将DataSet 保存到文件中, 如果目录不存在，创建目录
            </summary>
            <param name="ds">要保存的DataSet</param>
            <param name="xmlFile">指定文件名</param>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.DataSetToXmlFile(System.Data.DataSet)">
            <summary>
            将DataSet 保存到文件中
            </summary>
            <param name="ds">要保存的DataSet</param>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.IsInColumns(System.String,System.Data.DataColumnCollection)">
            <summary>
            判断是否包含特定名称的列
            </summary>
            <param name="name">特定名称</param>
            <param name="dcc">DataColumnCollection</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.GetMaxValue(System.Data.DataSet,System.String,System.String,System.Int32)">
            <summary>
            根据过滤条件得到数据集中指定列的最大值
            </summary>
            <param name="ds">数据集</param>
            <param name="filter">过滤条件</param>
            <param name="colName">指定列</param>
            <param name="nullVaue">找不到时的缺省值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.CreateDict">
            <summary>
            创建字典类dataset
            </summary>
            <returns>创建的DataSet</returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.AddDictItem(System.Data.DataSet@,System.String,System.String,System.Int32)">
            <summary>
            往字典类dataset中添加一条数据
            </summary>
            <param name="dsData">数据集</param>
            <param name="strItemName">项目名称</param>
            <param name="strItemCode">项目代码</param>
            <param name="iShowOrder">显示序号</param>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.CopyDataRow(System.Data.DataRow@,System.Data.DataRow@)">
            <summary>
            复制DataRow
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.GetDistinctDataTable(System.Data.DataTable,System.String[])">
            <summary>
            获取DataTable中指定列不重复的记录
            </summary>
            <param name="dtSource">需传入的数据源DataTable</param>
            <param name="strArrColumns">例：columns = { "DEPT_CODE", "DEPT_NAME" };  //DataTable中不重复的科室代码，科室名称</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.CheckDistinctDataTable(System.Data.DataTable,System.String[])">
            <summary>
            检查DataTable中指定列是否有重复记录
            </summary>
            <param name="dtSource">需检查的DataTable</param>
            <param name="strArrColumns">指定检查列</param>
            <returns>True 有重复记录，False 无重复记录</returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.HasRecord(System.Data.DataTable)">
            <summary>
            数据集是否具有记录
            </summary>
            <param name="dtData">数据表</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.GetMaxValue(System.Data.DataSet,System.String,System.String,System.String)">
            <summary>
            获取列最大值
            </summary>
            <param name="dsData">数据集</param>
            <param name="strFilter">过滤串</param>
            <param name="strColName">列名</param>
            <param name="strNullValue">Null值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.GetMinValue(System.Data.DataSet,System.String,System.String,System.String)">
            <summary>
            获取最小值
            </summary>
            <param name="dsData">数据集</param>
            <param name="strFilter">过滤串</param>
            <param name="strColName">列名</param>
            <param name="strNullValue">Null值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.GetValue(System.Data.DataSet,System.String,System.String,System.String@)">
            <summary>
            获取值
            </summary>
            <param name="dsData">数据集</param>
            <param name="strFilter">过滤串</param>
            <param name="strColName">列名</param>
            <param name="strResult">返回查找值</param>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.CreateDict(System.String[0:,0:])">
            <summary>
            生成字典DataSet
            </summary>
            <param name="dictItems"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.AddNewRow(System.Data.DataTable,System.String[],System.String[])">
            <summary>
            添加新行
            </summary>
            <param name="dtData">数据表</param>
            <param name="strArrColNames">列名数组</param>
            <param name="strArrValues">值数组</param>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.AddNewRow(System.Data.DataSet,System.String[],System.String[])">
            <summary>
            添加新行
            </summary>
            <param name="dsData">数据集</param>
            <param name="strArrColNames">列名数组</param>
            <param name="strArrValues">值数组</param>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.GetItems(System.Data.DataTable,System.String,System.String)">
            <summary>
            获取某张表中某个字段值的列表
            </summary>
            <param name="dtData">数据表</param>
            <param name="strColName">列名</param>
            <param name="strFilter">过滤串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.GetNameList(System.Data.DataSet,System.String,System.String,System.String)">
            <summary>
            获取名称字符串, 多值之间用","分隔
            </summary>
            <param name="dsData">数据集</param>
            <param name="strCodeList">名称字符串</param>
            <param name="strColName">名称列</param>
            <param name="strColCode">代码列</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.ConvertDataTableToClass``1(System.Data.DataTable)">
            <summary>
            表转换为实体对象
            </summary>
            <typeparam name="T"></typeparam>
            <param name="dtData">数据表</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.GetItem``1(System.Data.DataRow)">
            <summary>
            数据行转换为实体对象
            </summary>
            <typeparam name="T"></typeparam>
            <param name="drData">数据行</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.DeleteEmptyRow(System.Data.DataTable)">
            <summary>
            删除DataTable中的空白行
            </summary>
            <param name="dtData">数据表</param>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.DeleteEmptyRow(System.Data.DataTable,System.String[])">
            <summary>
            删除DataTable中的空白行
            </summary>
            <param name="dtData">数据表</param>
            <param name="strArrIgnoreColumns">忽略的列数组</param>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.ToDataTable(System.Data.DataRow[])">
            <summary>
            把行转换为表
            </summary>
            <param name="drArrRows">行数组</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.GetDataTable_DistinctRecord(System.Data.DataTable,System.String[])">
            <summary>
            去重复数据
            </summary>
            <param name="dtSource">源数据表</param>
            <param name="strArrColumns"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.UpdateTableByKey(System.Data.DataSet,System.Data.DataSet)">
            <summary>
            合并数据表，不比较数据，只比较主键, 要求有主键, 无修改操作, 只有增删
            </summary>
            <param name="dsDest">目标表</param>
            <param name="dsSrc">原表</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.UpdateTableByKey(System.Data.DataSet,System.Data.DataSet,System.String[])">
            <summary>
            合并数据表，不比较数据，只比较主键, 要求有主键, 无修改操作, 只有增删
            </summary>
            <param name="dsDest">目标表</param>
            <param name="dsSrc">原表</param>
            <param name="keyCols">主键列数组</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.GetDictDSFromStr(System.String)">
            <summary>
            从字符串中生成DataSet, 字符串以,进行分隔 0:不划,1:划
            </summary>
            <param name="str">字符串</param>
            <returns>生成的DataSet</returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.CreateDict(System.String,System.Boolean)">
            <summary>
            创建只有一列的DataSet
            </summary>
            <param name="colName">列名</param>
            <param name="valList">值列表,  ","分隔</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.MergeTable(System.Data.DataTable,System.Data.DataTable)">
            <summary>
            合并数据表，不比较数据，只比较主键, 要求有主键, 无修改操作, 只有增删
            </summary>
            <param name="dtDest">目标表</param>
            <param name="dtSrc">原表</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.MergeDataSet(System.Data.DataSet@,System.Data.DataSet,System.String[])">
            <summary>
            合并DataSet
            </summary>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.MergeDataSetEx(System.Data.DataTable@,System.Data.DataTable,System.Data.DataColumn[])">
            <summary>
            合并DataSet
            </summary>
            <param name="dtDest">目标数据表</param>
            <param name="dtSrc">源数据表</param>
            <param name="colPrimaryKeys">主键列</param>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.MergeDataSetPart(System.Data.DataTable@,System.Data.DataTable,System.Data.DataColumn[],System.String)">
            <summary>
            合并DataSet
            </summary>
            <param name="dtDest">目标数据表</param>
            <param name="dtSrc">源数据表</param>
            <param name="colPrimaryKeys">主键列</param>
            <param name="strFilter">过滤串</param>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.GetItems(System.Data.DataTable,System.String)">
            <summary>
            获取某张表中某个字段值的列表
            </summary>
            <param name="ds"></param>
            <param name="colName"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.GetSQLData(System.String,System.Collections.Hashtable)">
            <summary>
            通过sql文件获取数据源
            </summary>
            <param name="templetName"></param>
            <param name="hasParam"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.GetSQL(System.String,System.Collections.Hashtable)">
            <summary>
            通过sql文件获取SQL语句
            </summary>
            <param name="templetName"></param>
            <param name="hasParam"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DataSetHelper.getParams(System.Data.DataTable)">
            <summary>
            获取参数
            </summary>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Common.DateTimeHelper">
            <summary>
            DateTime操作类
            </summary>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.GetFirstDateOfWeek(System.DateTime)">
            <summary>
            计算本周起始日期（礼拜一的日期）
            </summary>
            <param name="someDate">该周中任意一天</param>
            <returns>返回礼拜一日期，后面的具体时、分、秒和传入值相等</returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.GetLastDateOfWeek(System.DateTime)">
            <summary>
            计算本周结束日期（礼拜日的日期）
            </summary>
            <param name="someDate">该周中任意一天</param>
            <returns>返回礼拜日日期，后面的具体时、分、秒和传入值相等</returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.GetFirstDateOfMonth(System.DateTime)">
            <summary>
            返回当月第一天
            </summary>
            <param name="someDate"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.GetLastDateOfMonth(System.DateTime)">
            <summary>
            返回当月最后一天
            </summary>
            <param name="someDate"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.Parse(System.Object)">
            <summary>
            对象转换为DateTime?
            </summary>
            <param name="objDate"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.DateTime_Null">
            <summary>
            获取DateTime的Null值
            </summary>
            <remarks>假定一个"1-1-1 12:00:00"值作为Null值</remarks>
            <returns>DateTime</returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.DateTime_IsNull(System.DateTime@)">
            <summary>
            判断DateTime是否为Null
            </summary>
            <param name="dt">DateTime</param>
            <returns>TRUE: 是为Null; FALSE: 不为Null</returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.DateTimeFromString(System.String)">
            <summary>
            把字符串转换成为日期
            </summary>
            <param name="strDateTime">日期字符串</param>
            <returns>日期</returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.GetDateTime(System.String)">
            <summary>
            将字符串转成日期
            </summary>
            <param name="strDate"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.GetDateTime(System.Object)">
            <summary>
            将对象转换为日期
            </summary>
            <param name="objValue"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.GetDateTime(System.DateTime,System.String)">
            <summary>
            获取时间
            </summary>
            <param name="dtDay">指定日期</param>
            <param name="timeStr">时分秒字符串</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.GetDateTime(System.String@)">
            <summary>
            从一个字符串中尽量提取日期时间字符串
            </summary>
            <remarks>
             1: 年月日时分秒可以被任意非数字字符分隔
             2: 年月日时分秒可以不用任何分隔符分隔,提取时按年四位数整数, 其它都有两位整数
             3: 1与2两种情况的结合体也可以识别, 如 200012-2 20:12-43, 可以正确识别
            </remarks>
            <param name="dtSrc">日期时间字符串</param>
            <returns>TRUE: 成功; FALSE: 失败</returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.GetDateTimeShort(System.String)">
            <summary>
            把一个表示日期的字符串转换成短日期格式
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.GetDateTimeLong(System.String)">
            <summary>
            把一个表示日期的字符串转换成长日期格式
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.GetShortTime(System.String)">
            <summary>
            获取短时间格式
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.GetMD(System.String)">
            <summary>
            获取月日(如8.2)
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.GetYMD(System.String)">
            <summary>
            获取年月日, 如2007年12月6日
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.GetRomaNum(System.Int32)">
            <summary>
            获取数字的罗马表示
            </summary>
            <param name="num"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.IsTime(System.String@)">
            <summary>
            检查一个字符串是不是合法的时间格式
            如果是: 把输入字符串转换成标准的日期格式HH:MM:SS
            </summary>
            <param name="timeStr">表示时间的字符</param>
            <returns>TRUE: 是; FALSE: 不是</returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.IsNumber(System.String)">
            <summary>
            判断一个字符串是不是正整数
            </summary>
            <remarks>仅包含数字</remarks>
            <param name="text">待验证的字符串</param>
            <returns>TRUE: 符合要求; FALSE: 不符合要求</returns>
        </member>
        <member name="M:PlatCommon.Common.DateTimeHelper.IsDateTime(System.String)">
            <summary>
            判断字符串是不是日期时间,如果是转换成标准形式 YYYY-MM-DD HH:mm:ss
            </summary>
            <param name="dtStr"></param>
            <returns>TRUE: 是; FALSE: 否</returns>
        </member>
        <member name="F:PlatCommon.Common.frmInputSettingDict.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:PlatCommon.Common.frmInputSettingDict.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:PlatCommon.Common.frmInputSettingDict.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:PlatCommon.Common.frminputsettingoutp.Bclear_Click(System.Object,System.EventArgs)">
            <summary>
            清除
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:PlatCommon.Common.frminputsettingoutp.Backspace_Click(System.Object,System.EventArgs)">
            <summary>
            
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:PlatCommon.Common.frminputsettingoutp.gridView3_ColumnWidthChanged(System.Object,DevExpress.XtraGrid.Views.Base.ColumnEventArgs)">
            <summary>
            保存列宽
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:PlatCommon.Common.frminputsettingoutp.gridView3_ColumnPositionChanged(System.Object,System.EventArgs)">
            <summary>
            保存列位置
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="F:PlatCommon.Common.frminputsettingoutp.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:PlatCommon.Common.frminputsettingoutp.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:PlatCommon.Common.frminputsettingoutp.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:PlatCommon.Common.FrmInsuranceAdult.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:PlatCommon.Common.FrmInsuranceAdult.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:PlatCommon.Common.FrmInsuranceAdult.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:PlatCommon.Common.frmSelectWard.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:PlatCommon.Common.frmSelectWard.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:PlatCommon.Common.frmSelectWard.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:PlatCommon.Common.IDHelper">
            <summary>
            自动ID帮助类
            </summary>
        </member>
        <member name="M:PlatCommon.Common.IDHelper.GetId(System.String,System.String,System.String@,System.Int32@,Utility.OracleODP.OracleBaseClass,System.Collections.Hashtable,System.Boolean)">
            <summary>
            读取ID或序号 不回写更新ID
            </summary>
            <param name="typeName">类型名称</param>
            <param name="hisUnitCode">医院编码</param>
            <param name="sCurrentIDValue">输出类型：格式化后的当前值</param>
            <param name="iCurrentIDValue">输出类型：当前值整数值</param>
            <param name="db">默认参数：用于控制事务的db，可为null</param>
            <param name="hasParams">可选参数：用于生成SQL语句结果前缀的参数，可为空</param>
            <param name="sqlFirst">可选参数：true为SQL语句结果前置， false为固定前缀前置，默认为true</param>
            <returns>true成功，false失败</returns>
        </member>
        <member name="M:PlatCommon.Common.IDHelper.GetUpdateIdSql(System.String,System.String,System.String@,System.Int32@,System.String@,System.Int32@,System.String@,Utility.OracleODP.OracleBaseClass,System.Collections.Hashtable,System.Boolean)">
            <summary>
            得到更新Id值的sql语句，方法会返回update语句用于更新id值，一般用于调用者在保存前执行更新Id的SQL语句,由调用者更新Id
            </summary>
            <param name="typeName">类型名称</param>
            <param name="hisUnitCode">医院编码</param>
            <param name="sCurrentIDValue">输出：格式化后的当前ID值</param>
            <param name="iCurrentIDValue">输出：当前ID值整数变量</param>
            <param name="sNextIDValue">输出类型：执行更新操作后的计算出的格式化后的新值</param>
            <param name="iNextIDValue">输出类型：执行更新操作后的计算出的新值</param>
            <param name="updateSql">输出类型：更新自动ID表的语句，用于调用者执行更新自动ID表</param>
            <param name="db">用于控制事务的db，必选项</param>
            <param name="hasParams">可选参数：用于生成SQL语句结果前缀的参数，可为空</param>
            <param name="sqlFirst">可选参数：true为SQL语句结果前置， false为固定前缀前置，默认为true</param>
            <returns>true成功，false失败</returns>
        </member>
        <member name="M:PlatCommon.Common.IDHelper.UpdateId(System.String,System.String,System.String@,System.Int32@,System.String@,System.Int32@,Utility.OracleODP.OracleBaseClass,System.Collections.Hashtable,System.Boolean)">
            <summary>
            回写Id，该方法会计算新值自动更新Id，保存到AUTO_SETTING_ID表中，一般用于保存前更新Id
            </summary>
            <param name="typeName">类型名称</param>
            <param name="hisUnitCode">医院编码</param>
            <param name="iCurrentIDValue">输出类型：当前值</param>
            <param name="sCurrentIDValue">输出类型：格式化后的当前ID值</param>
            <param name="sNexIDValue">输出类型：执行更新操作后的计算出的格式化后的新值</param>
            <param name="iNextIDValue">输出类型：执行更新操作后的计算出的新值</param>
            <param name="db">用于控制事务的db，必选项</param>
            <param name="hasParams">可选参数：用于生成SQL语句结果前缀的参数，可为空</param>
            <param name="sqlFirst">可选参数：true为SQL语句结果前置， false为固定前缀前置，默认为true</param>
            <returns>true成功，false失败</returns>
        </member>
        <member name="M:PlatCommon.Common.IExamAppoints.Environment">
            <summary>
            检查预约环境
            </summary>
            <returns>ture--环境通过；false--环境不通过</returns>
        </member>
        <member name="M:PlatCommon.Common.IExamAppoints.OpenAppoints(System.String)">
            <summary>
            打开预约
            </summary>
            <param name="parameterList">多个参数相连</param>
        </member>
        <member name="M:PlatCommon.Common.IExamAppoints.IeOpen(System.String,System.String)">
            <summary>
            打开浏览器
            </summary>
            <param name="url">URL地址</param>
            <param name="app">浏览器名称</param>
        </member>
        <member name="M:PlatCommon.Common.IExamAppoints.Appoints(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            预约申请原型方法
            </summary>
            <param name="functionFlag">功能标识(R--查询;W--预约)</param>
            <param name="examClass">检查大类别名称</param>
            <param name="examSubClass">检查子类别名称</param>
            <param name="patientId">病人ID</param>
            <param name="visitId">住院次数</param>
            <param name="operation">操作人</param>
            <param name="reqDept">申请科室代码</param>
            <param name="doctorUser">申请医生</param>
            <param name="examNo">申请号</param>
            <param name="examItemCode">申请项目代码</param>
            <param name="patientSource">病人来源</param>
        </member>
        <member name="T:PlatCommon.Common.ListViewHelper">
            <summary>
            ListView操作类
            </summary>
        </member>
        <member name="M:PlatCommon.Common.ListViewHelper.DataBind(System.Windows.Forms.ListView,System.Data.DataView,System.String,System.String)">
            <summary>
            设置DataGridView的属性
            </summary>
            <param name="lstV"></param>
            <param name="dvSrc"></param>
            <param name="displayMember"></param>
            <param name="valueMember"></param>
        </member>
        <member name="T:PlatCommon.Common.MessageHelper">
            <summary>
            功能描述：扩展显示消息功能
            
            </summary>	
        </member>
        <member name="F:PlatCommon.Common.MessageHelper.MainWidow">
            <summary>
            
            </summary>
        </member>
        <member name="M:PlatCommon.Common.MessageHelper.#cctor">
            <summary>
            信息弹窗
            </summary>
        </member>
        <member name="M:PlatCommon.Common.MessageHelper.ShowInformation(System.Windows.Forms.Form,System.String,System.String)">
            <summary>
            消息提醒
            </summary>
            <param name="forms"></param>
            <param name="caption"></param>
            <param name="text"></param>
        </member>
        <member name="M:PlatCommon.Common.MessageHelper.ShowInformation(System.Windows.Forms.Form,System.String)">
            <summary>
            消息提示
            </summary>
            <param name="forms"></param>
            <param name="text"></param>
        </member>
        <member name="M:PlatCommon.Common.MessageHelper.ShowInformation(System.String)">
            <summary>
            消息提示
            </summary>
            <param name="text"></param>
        </member>
        <member name="M:PlatCommon.Common.MessageHelper.ShowSuccess(System.Windows.Forms.Form,System.String,System.String)">
            <summary>
            保存成功
            </summary>
            <param name="forms"></param>
            <param name="caption"></param>
            <param name="text"></param>
        </member>
        <member name="M:PlatCommon.Common.MessageHelper.ShowSuccess(System.Windows.Forms.Form,System.String)">
            <summary>
            保存成功
            </summary>
            <param name="forms"></param>
            <param name="text"></param>
        </member>
        <member name="M:PlatCommon.Common.MessageHelper.ShowSuccess(System.String)">
            <summary>
            保存成功
            </summary>
            <param name="text"></param>
        </member>
        <member name="M:PlatCommon.Common.MessageHelper.ShowError(System.Windows.Forms.Form,System.String,System.String)">
            <summary>
            错误
            </summary>
            <param name="forms"></param>
            <param name="caption"></param>
            <param name="text"></param>
        </member>
        <member name="M:PlatCommon.Common.MessageHelper.ShowError(System.Windows.Forms.Form,System.String)">
            <summary>
            错误
            </summary>
            <param name="forms"></param>
            <param name="text"></param>
        </member>
        <member name="M:PlatCommon.Common.MessageHelper.ShowError(System.String)">
            <summary>
            错误
            </summary>
            <param name="forms"></param>
            <param name="text"></param>
        </member>
        <member name="M:PlatCommon.Common.MessageHelper.ShowWaring(System.Windows.Forms.Form,System.String,System.String)">
            <summary>
            警告
            </summary>
            <param name="forms"></param>
            <param name="caption"></param>
            <param name="text"></param>
        </member>
        <member name="M:PlatCommon.Common.MessageHelper.ShowWaring(System.Windows.Forms.Form,System.String)">
            <summary>
            警告
            </summary>
            <param name="forms"></param>
            <param name="text"></param>
        </member>
        <member name="M:PlatCommon.Common.MessageHelper.ShowWaring(System.String)">
            <summary>
            警告
            </summary>
            <param name="text"></param>
        </member>
        <member name="M:PlatCommon.Common.Pass4.MDC_SetPatient(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Int32,System.Int32,System.Int32,System.String,System.Int32,System.Int32)">
            <summary>
            患者信息
            </summary>
            <param name="pcPatCode"></param>
            <param name="pcInHospNo"></param>
            <param name="pcVisitCode"></param>
            <param name="pcName"></param>
            <param name="pcSex"></param>
            <param name="pcBirthday"></param>
            <param name="pcHeightCM"></param>
            <param name="pcWeighKG"></param>
            <param name="pcDeptCode"></param>
            <param name="pcDeptName"></param>
            <param name="pcDoctorCode"></param>
            <param name="pcDoctorName"></param>
            <param name="piPatStatus"></param>
            <param name="piIsLactation"></param>
            <param name="piIsPregnancy"></param>
            <param name="pcPregStartDate"></param>
            <param name="piHepDamageDegree"></param>
            <param name="piRenDamageDegree"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.Pass4.MDC_AddMedCond(System.String,System.String,System.String,System.String)">
            <summary>
            诊断信息
            </summary>
            <param name="pcIndex"></param>
            <param name="pcDiseaseCode"></param>
            <param name="pcDiseaseName"></param>
            <param name="pcRecipNo"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.Pass4.MDC_AddScreenDrug(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            用药
            </summary>
            <param name="pcIndex"></param>
            <param name="pcDiseaseCode"></param>
            <param name="pcDiseaseName"></param>
            <param name="pcRecipNo"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.Pass4.MDC_DoCheck">
            <summary>
            审查函数
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.Pass4.MDC_GetWarningCode(System.String)">
            <summary>
            药品医嘱警示级别
            </summary>
            <param name="pcIndex"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.Pass4.MDC_ShowWarningHint(System.String)">
            <summary>
            一条药品医嘱的审查结果提示窗口
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.Pass4.MDC_DoSetDrug(System.String,System.String)">
            <summary>
            查询药品函数
            </summary>
            <param name="pcDrugUniqueCode"></param>
            <param name="pcDrugName"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.Pass4.MDC_DoRefDrug">
            <summary>
            药品信息查询函数
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.Pass4.MDC_Quit">
            <summary>
            PASS V4退出函数
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetPrepayment(System.String,System.String,System.String,System.Decimal@,System.Decimal@,System.Decimal@,System.Decimal@,System.Decimal@)">
            <summary>
            // 参  数: 
            	reference	decimal	ldprepayment   		// 预交金总额
            	reference	decimal	ldapprovemoney 		// 透支额度
            	reference	decimal	ldcharges      		// 未结算费用
            	reference	decimal	ldprepaybalance		// 预交金余额(根据参数设置, 可能包含透支额度)
            	reference	decimal	ldusemoney     		// 可用预交金总额(根据参数设置, 可能包含透支额度)
            返  回: integer
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.UnCheckPrepayment(System.String,System.String)">
            <summary>
            不判断预交金的人
            </summary>
            <returns></returns>
        </member>
        <!-- 对于成员“M:PlatCommon.Common.PublicFunction.SetHighQualityStock(Utility.OracleODP.OracleBaseClass,System.Collections.Generic.Dictionary{System.String,System.String},System.String,System.String,System.String,System.String,System.String,System.String,System.Boolean)”忽略有格式错误的 XML 注释 -->
        <member name="M:PlatCommon.Common.PublicFunction.GetImportNo(Utility.OracleODP.OracleBaseClass,System.String,System.String@)">
            <summary>
            得到某一库房的新入库单号
            </summary>
            <param name="idc">待更新数据表</param>
            <param name="as_storage">库房编码</param>
            <param name="as_importno">新单据号</param>
            <returns>成功时返回空否则返回错误</returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetExportNo(Utility.OracleODP.OracleBaseClass,System.String,System.String@)">
            <summary>
            得到某一库房的新入库单号
            </summary>
            <param name="idc">待更新数据表</param>
            <param name="as_storage">库房编码</param>
            <param name="as_importno">新单据号</param>
            <returns>成功时返回空否则返回错误</returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetOrderStatusColor(System.String,System.Boolean,System.DateTime,System.DateTime,System.DateTime,System.Boolean)">
            <summary>
            获取医嘱状态颜色
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetSystemAuthorization(System.String,System.String,System.String,System.String,System.String,System.String@,System.Int32@)">
            <summary>
            判断系统授权
            </summary>
            <param name="as_app_code">系统代码</param>
            <param name="as_app_name">系统代码</param>
            <param name="as_app_version">系统版本</param>
            <param name="encryptCode">系统授权码</param>
            <param name="msg">返回消息</param>
            <param name="as_day">返回剩余可使用天数</param>
            <returns>true可用</returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetSystemAuthorization(System.String,System.String,System.String,System.String,System.String@,System.Int32@)">
            <summary>
            判断系统授权
            </summary>
            <param name="as_app_code">系统代码</param>
            <param name="as_app_name">系统代码</param>
            <param name="as_app_version">系统版本</param>
            <param name="encryptCode">系统授权码</param>
            <param name="msg">返回消息</param>
            <param name="as_day">返回剩余可使用天数</param>
            <returns>true可用</returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.DecryptNew(System.String)">
            <summary>
            解密字符串
            </summary>
            <param name="Source">要解密的字符串</param>
            <param name="Key">密钥</param>
            <returns>解密后的字符串</returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetAge(System.String,System.String,System.Boolean)">
            <summary>
            取年龄
            </summary>
            <param name="patientID">病人ID</param>
            <param name="visitID">住院次数</param>
            <param name="dischargeFlag">是否出院标识</param>
            <returns>返回年龄字符串</returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetWarcodeVsDept(System.String)">
            <summary>
            根据医生站编码获取护理单元编码列表，用逗号间隔
            2016-07-05 by lxm
            </summary>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetDrugIndicatorDict">
            <summary>
            
            </summary>
            <returns>返回药品类别字典</returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetDeptVsWarCode(System.String)">
            <summary>
            根据根据护理单元取医生科室
            2016-07-05 by lxm
            </summary>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetWarcodelist(System.String)">
            <summary>
            获取护理人员的护理单元编码列表，用逗号间隔
            2016-03-22 by 梁吉
            </summary>
            <param name="userName">登录用户名</param>
            <returns>护理单元编码列表</returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.gf_login_logout(System.String,System.String,System.String)">
            <summary>
            描述:保存操作员登录和登出日志,并实时更新机位在线状态 yhy
            </summary>
            <param name="as_application">应用程序名称  </param>
            <param name="as_user_name">程序用户名</param>
            <param name="as_in_out_flag">登录登出标志 in：登录；out：登出</param>
            <returns> (none)</returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.Getdiscount(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Decimal,System.Decimal,System.Decimal@,System.String@)">
            <summary>
            打折公共函数
            </summary>
            <param name="ldec_discount_price">打折金额(减免金额)</param>
            <param name="ls_discount_demo">打折备注</param>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.DownloadFile(System.String,System.String)">
            <summary>
            下载文件
            </summary>
            <param name="downLoadUrl">文件的url路径</param>
            <param name="saveFullName">需要保存在本地的路径(包含文件名)</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetOrderStatusColor_NEINTER(System.String,System.Boolean,System.Boolean)">
            <summary>
            东北国际医嘱状态显示
            </summary>
            <param name="statusCode"></param>
            <param name="blnLongTerm"></param>
            <param name="blnForeColor"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetOrderStatusColor(System.String,System.Boolean,System.Boolean)">
            <summary>
            获取医嘱状态颜色
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetLocalHostName">
            <summary>
            获得本机电脑名称
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetLocalMac">
            <summary>
            获取本机Mac地址
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetDrugLimitClass(System.String,System.String,System.String)">
            <summary>
            取药品限制标志
            </summary>
            <param name="drug_code">处方代码</param>
            <param name="drug_spec">处方规格</param>
            <param name="drug_indicator">分类</param>
            <returns>限制标志 空 1，2，3</returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.IsInClinicPath(System.String,System.String,System.Int32)">
            <summary>
             //判断是否在路径里 by lions
            </summary>
            <param name="pat_id"></param>
            <param name="visit_id"></param>
            <param name="status">路径状态 0 在路径里 1正常结束路径 2退出路径</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetCAPABILITY(System.String,System.String)">
            <summary>
            获取用户权限
            </summary>
            <param name="appcode">应用模板编码</param>
            <param name="dbuser">用户名</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetInputSetting(System.String,System.String,System.String,System.Data.DataTable@,System.String)">
            <summary>
            取输入法数据 by lions 2018-12-16
            </summary>
            <param name="GS_INPUTSETING">输入法类型</param>
            <param name="GS_STORAGES">药房编码</param>
            <param name="GS_CONDITIONS">可能的条件</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetFormInDll(System.String,System.String,System.String[])">
            <summary>
            从动态库中获取窗体
            </summary>
            <param name="dllName">动态库的名称</param>
            <param name="typeName">类名</param>
            <returns>窗体类</returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.ExistsAndInsertPara(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            判断参数是否存在，不存在则新增
            </summary>
            <param name="appCode">程序代码</param>
            <param name="paraName">参数名称</param>
            <param name="deptCode">科室代码</param>
            <param name="empNO">员工编码</param>
            <param name="defaltValue">默认值</param>
            <param name="paramScope">取值范围</param>
            <param name="explanation">说明</param>
            <param name="hisUnitCode">医院编码</param>
            <returns>成功是返回true否则false</returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetPrescNo">
            <summary>
            获取处方序列号 by lions
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetPatientIdForIdNo(System.String@,System.String@,System.String)">
            <summary>
            根据身份证返回patient_id 和 visit_id
            </summary>
            <param name="patient_id"></param>
            <param name="visit_id"></param>
            <param name="id_no">身份证号 如果此参数不为空则直接根据此参数提取，否则读取卡机</param>
            <returns>true成功</returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetSequeceFromAuto(System.String,System.String,System.String@,Utility.OracleODP.OracleBaseClass)">
            <summary>
            从设置表中查询序列号由前缀+序列号当前值（序列号当前值不足位数的，前面补0）
            </summary>
            <param name="typename">类型名</param>
            <param name="hisunitcode">医院编码</param>
            <param name="sequece">返回序列号</param>
            <returns>获取成功失败 true false</returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetIDNoFromAuto(System.Int32,System.Int32,System.String,System.String@,Utility.OracleODP.OracleBaseClass)">
            <summary>
            根据序号取id值
            </summary>
            <param name="serialno"></param>
            <param name="type">0,ID序列；1，INP序列</param>
            <param name="hisunitcode"></param>
            <param name="sequece"></param>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetUserImage(System.String,System.String)">
            <summary>
            取用户对应图片
            </summary>
            <param name="userName"></param>
            <param name="hisUnitcode"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetDeptNameByCode(System.String,System.String)">
            <summary>
            取科室名称
            </summary>
            <param name="deptCode"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicFunction.GetInterfaceConfigDict(System.String,System.String@)">
            <summary>
            获取url接口配置字典表方法
            </summary>
            <param name="config_code"></param>
            <param name="config_path"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicInsurVsByChargeType.CheckInsurMessage(System.String,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="ChargeType"></param>
            <param name="item_code"></param>
            <param name="as_item_name"></param>
            <param name="insurance_type"></param>
            <param name="message"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicInsurVsByChargeType.GetInsurCodeIcd9(System.String,System.String,System.String@,System.String@)">
            <summary>
            本地医保码
            </summary>
            <param name="ChargeType"></param>
            <param name="item_code"></param>
            <param name="insur_code"></param>
            <param name="insur_name"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.PublicInsurVsByChargeType.GetInsurVsPrice(System.String,System.String,System.String,System.String,System.String,System.String,PlatCommon.Common.ClassInsurVsMessage,System.String)">
            <summary>
            获取医保字典信息
            </summary>
            <param name="ChargeType">费别</param>
            <param name="item_code">编码</param>
            <param name="as_item_name">名称</param>
            <param name="as_item_spec">规格</param>
            <param name="as_unit">单位</param>
            <param name="flag">使用规格标志</param>
            <param name="message">医保信息类</param>
            <returns>有对照 则返回1 无对照 则返回-1 非医保费别 返回0</returns>
        </member>
        <member name="M:PlatCommon.Common.PublicInsurVsByChargeType.GetInsurInfo(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,PlatCommon.Common.ClassInsurVsMessage@,System.String@)">
            <summary>
            
            </summary>
            <param name="ChargeType">费别</param>
            <param name="patient_id">住院 patient_id，门诊 CLINIC_NO</param>
            <param name="visit_id">住院 visit_id，门诊 空</param>
            <param name="item_code">编码</param>
            <param name="as_item_name">名称</param>
            <param name="as_item_spec">规格</param>
            <param name="as_unit">单位</param>
            <param name="flag">使用规格标志</param>
            <param name="message">医保信息类</param>
            <param name="insur_adult">返回审批标志</param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Common.Result`1">
            <summary>
            公共类,扩展泛型返回附属信息
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="P:PlatCommon.Common.Result`1.Data">
            <summary>
            类型数据
            </summary>
        </member>
        <member name="P:PlatCommon.Common.Result`1.ErrObj">
            <summary>
            返回的错误信息
            </summary>
        </member>
        <member name="P:PlatCommon.Common.Result`1.HasError">
            <summary>
            是否报错
            </summary>
        </member>
        <member name="P:PlatCommon.Common.Result`1.MsgTip">
            <summary>
            返回提示信息
            </summary>
        </member>
        <member name="M:PlatCommon.Common.Result`1.Clear">
            <summary>
            清除此类状态
            </summary>
        </member>
        <member name="T:PlatCommon.Common.commFunct">
            <summary>
            公共函数类
            合理用药外部函数定义
            函数多为静态函数或方法
            </summary>
        </member>
        <member name="M:PlatCommon.Common.commFunct.PassQueryOrCheck">
            <summary>
            药物信息查询、浮动窗体、用药研究、警告及审查功能
            (合理用药,暂时不使用---未完)
            </summary>
        </member>
        <member name="T:PlatCommon.Common.DefineValue">
            <summary>
            定义一些实例变量信息类
            </summary>
        </member>
        <member name="F:PlatCommon.Common.DefineValue.isVisitid">
            <summary>
            住院次数
            </summary>
        </member>
        <member name="F:PlatCommon.Common.DefineValue.isWardCode">
            <summary>
            所在病房代码
            </summary>
        </member>
        <member name="F:PlatCommon.Common.DefineValue.isPatientID">
            <summary>
            住院ID号
            </summary>
        </member>
        <member name="F:PlatCommon.Common.DefineValue.isOrderDoc">
            <summary>
            经治医生
            </summary>
        </member>
        <member name="F:PlatCommon.Common.DefineValue.isUserDoc">
            <summary>
            医生代码
            </summary>
        </member>
        <member name="F:PlatCommon.Common.DefineValue.isCharge">
            <summary>
            费别(自费、医保)
            </summary>
        </member>
        <member name="F:PlatCommon.Common.DefineValue.isPriceInd">
            <summary>
            享受优惠价格标志
            </summary>
        </member>
        <member name="F:PlatCommon.Common.DefineValue.isPriceNum">
            <summary>
            收费系数分子
            </summary>
        </member>
        <member name="F:PlatCommon.Common.DefineValue.isPriceDenom">
            <summary>
            收费系数分母
            </summary>
        </member>
        <member name="F:PlatCommon.Common.DefineValue.isSpecInd">
            <summary>
            适用特殊收费项目标志
            </summary>
        </member>
        <member name="F:PlatCommon.Common.DefineValue.ideBlance">
            <summary>
            预交金余额
            </summary>
        </member>
        <member name="F:PlatCommon.Common.DefineValue.iDSOrder">
            <summary>
            毁方用DataSet对象
            </summary>
        </member>
        <member name="M:PlatCommon.Common.RMBCapitalization.ConvertIntToUppercaseAmount(System.String)">
            <summary>
            转换整数为大写金额
            最高精度为垓，保留小数点后4位，实际精度为亿兆已经足够了，理论上精度无限制，如下所示：
            序号:...30.29.28.27.26.25.24  23.22.21.20.19.18  17.16.15.14.13  12.11.10.9   8 7.6.5.4  . 3.2.1.0
            单位:...垓兆亿萬仟佰拾        京亿萬仟佰拾       兆萬仟佰拾      亿仟佰拾     萬仟佰拾元 . 角分厘毫
            数值:...1000000               000000             00000           0000         00000      . 0000
            下面列出网上搜索到的数词单位：
            元、十、百、千、万、亿、兆、京、垓、秭、穰、沟、涧、正、载、极
            </summary>
            <param name="capValue">整数值</param>
            <returns>返回大写金额</returns>
        </member>
        <member name="M:PlatCommon.Common.RMBCapitalization.ConvertDecToUppercaseAmount(System.String,System.Boolean)">
            <summary>
            转换小数为大写金额
            </summary>
            <param name="capValue">小数值</param>
            <param name="addZero">是否增加零位</param>
            <returns>返回大写金额</returns>
        </member>
        <member name="M:PlatCommon.Common.RMBCapitalization.RMBAmount(System.Double)">
            <summary>
            人民币大写金额
            </summary>
            <param name="value">人民币数字金额值</param>
            <returns>返回人民币大写金额</returns>
        </member>
        <member name="F:PlatCommon.Common.SpellAndWbConfig._Reader">
            <summary>
            XML文件读取实例
            </summary>
        </member>
        <member name="F:PlatCommon.Common.SpellAndWbConfig._StrXmlData">
            <summary>
            XML文件中数据
            </summary>
        </member>
        <member name="F:PlatCommon.Common.SpellAndWbConfig._WBCodeStation">
            <summary>
            记录XML中五笔码开始位置
            </summary>
        </member>
        <member name="F:PlatCommon.Common.SpellAndWbConfig._OutStation">
            <summary>
            记录XML中结束位置
            </summary>
        </member>
        <member name="M:PlatCommon.Common.SpellAndWbConfig.SetData">
            <summary>
            初始化XMLREADER
            </summary>
        </member>
        <member name="M:PlatCommon.Common.SpellAndWbConfig.GetXmlData">
            <summary>
            读取XML文件中数据
            </summary>
            <returns>返回String[]</returns>
        </member>
        <member name="M:PlatCommon.Common.SpellAndWbConfig.SearchWord(System.String,System.Int32,System.Int32)">
            <summary>
            查找汉字
            </summary>
            <param name="strName">汉字</param>
            <param name="start">搜索的开始位置</param>
            <param name="end">搜索的结束位置</param>
            <returns>汉语反义成字符串，该字符串只包含大写的英文字母</returns>
        </member>
        <member name="M:PlatCommon.Common.SpellAndWbConfig.GetSpellCode(System.String)">
            <summary>
            获得汉语的拼音码
            </summary>
            <param name="strName">汉字</param>
            <returns>汉语拼音码,该字符串只包含大写的英文字母</returns>
        </member>
        <member name="M:PlatCommon.Common.SpellAndWbConfig.GetWBCode(System.String)">
            <summary>
            获得汉语的五笔码
            </summary>
            <param name="strName">汉字</param>
            <returns>汉语五笔码,该字符串只包含大写的英文字母</returns>
        </member>
        <member name="M:PlatCommon.Common.TreatmentRoom.GetTreatmentRoomAdd(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.String}@)">
            <summary>
            插入治疗表函数 
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.TreatmentRoom.GetTreatmentRoomDel(System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.String}@)">
            <summary>
            删除治疗表函数
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.ucc2_his21_prepayment.uf_settle_used(System.String,System.String,System.Data.DataSet,System.String,System.Decimal,System.String,System.Boolean,System.Data.DataSet@,System.String)">
            <summary>
            描述:  结算使用预交金时，预交金记录处理
            参数: 
                [value] datastore ads_pre 预交金使用情况的数据存储
                [value] string as_settle_no 结算号
                [value] deciaml adc_payment_amount 自付金额
                [value] string  as_operator_no 操作员编号
                [value] boolean ab_refund 多于预交金是否直接退还病人
                ref DataSet lds_pre 返回给结算界面的DataSet 用于统一保存
                string file 文件名,改为外部传入例如结算传入的是string file = @"Config\\ibilling.xml";
            </summary>
        </member>
        <member name="M:PlatCommon.Common.ucc2_his21_prepayment.uf_get_pre_rcpt_no(System.String@)">
            <summary>
            描述:  改造的取参数表收据号,原来取INI的屏蔽;
            </summary> 
        </member>
        <member name="M:PlatCommon.Common.ucc2_his21_prepayment.uf_set_settle(System.String,System.String,System.String,System.Decimal@,System.Data.DataSet@,System.String,System.Boolean,System.String)">
            <summary>
            描述: 使用预交金结算时，预交金表的处理
            参数: 
                [value]  string as_rcpt_no 使用的预交金收据号
                [value]  dec adc_settle_amount  结算金额
            </summary>
        </member>
        <member name="M:PlatCommon.Common.ucc2_his21_prepayment.uf_set_next_rcptno(System.Int32,System.String)">
            <summary>
             描述: 根据传入的收据数量，直接设置收据号，适用于批量写收据的情况
            参数:  [value] integer ai_number 已经使用过的收据数量
            </summary>
        </member>
        <member name="M:PlatCommon.Common.ucc2_his21_prepayment.uf_get_currentno(System.String@,System.String,System.String,System.Int32@,System.Int32@)">
            <summary>
            功    能:根据用户提取当前为“使用”状态的发票号码
            参数:[out] (string)as_fp_no[in]  (string)as_user       用户编号 [in]  (int)   ai_flag       发票类型，对应flag字段
            返 回 值: (string)  0 发票已经用完; -1 取下一个发票号码信息失败  1 获得下一个发票号码   
            </summary> 
        </member>
        <member name="M:PlatCommon.Common.ucc2_his21_prepayment.uf_get_next(System.String@,System.String,System.String,System.Int32)">
            <summary>
            功    能:根据用户提取当前为“使用”状态的发票号码
            参数:[out] (string)as_fp_no[in]  (string)as_user       用户编号 [in]  (int)   ai_flag       发票类型，对应flag字段
            返 回 值: (string)  0 发票已经用完; -1 取下一个发票号码信息失败  1 获得下一个发票号码  [in]  (int)   ai_number     取发票量  
            </summary> 
        </member>
        <member name="M:PlatCommon.Common.ucc2_his21_prepayment.uf_get_overdrawn(System.String,System.String,System.Decimal@,System.Decimal@)">
            <summary>
            取预交金余额(同时根据配置：是否加上透支额)和未结费用金额
            </summary>
            <param name="patientID">病人ID</param>
            <param name="visitID">住院次数</param>
            <param name="adc_amount">余额</param>
            <param name="adc_charges">未结算的实收费用和</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.ucc2_his21_prepayment.uf_inp_getbalance(System.String,System.String)">
            <summary>
            取纯预交金总额
            </summary>
            <param name="patientID">病人ID</param>
            <param name="visitID">住院次数</param>
            <returns>总额</returns>
        </member>
        <member name="M:PlatCommon.Common.ucc2_his21_prepayment.GetOverDraft(System.String,System.String,System.Decimal@)">
            <summary>
            根据配置的策略,取透支金额
            </summary>
            <param name="patientid">病人ID</param>
            <param name="visitid">住院次数</param>
            <param name="draftMoney">返回金额</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.ucc2_his21_prepayment.uf_set_refund_by_balance(System.Collections.Generic.Dictionary{System.String,System.String},System.String,System.String,System.Decimal,System.String)">
            <summary>
            预交金交易余额退款
            </summary>
            <param name="idc">保存数据使用</param>
            <param name="as_rcpt_no">原始交款预交金号</param>
            <param name="as_operator">操作员号</param>
            <param name="adc_amount">退款金额</param>
            <param name="as_payway">退款支付方式</param>
            <returns>成功返回true否则返回false</returns>
        </member>
        <member name="M:PlatCommon.Common.ucc2_his21_prepayment.uf_set_rcpt_refund(System.String,System.String,System.String,System.Data.DataSet@,System.String)">
            <summary>
            结算后的退费，预交金的处理
            </summary>
            <param name="as_old_rcptno">要退的结算主记录的收据号</param>
            <param name="as_new_rcptno">新写入退结算主记录的收据号</param>
        </member>
        <member name="M:PlatCommon.Common.ucc2_his21_prepayment.uf_set_nextno(System.String,System.String,System.Int32,System.Int32,System.Int32,System.Collections.Generic.Dictionary{System.String,System.String}@)">
            <summary>
            更新发票号
            </summary>
        </member>
        <member name="T:PlatCommon.Common.UdpMessage">
            <summary>
            UDP消息
            </summary>
        </member>
        <member name="T:PlatCommon.Common.DataChanged_Event">
            <summary>
            数据改变事件托管
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:PlatCommon.Common.ViewDataHelper">
            <summary>
            窗体数据帮助类
            </summary>
        </member>
        <member name="M:PlatCommon.Common.ViewDataHelper.ClearData(System.Windows.Forms.Control)">
            <summary>
            清除界面控件值
            </summary>
            <param name="parentCtrl"></param>
        </member>
        <member name="M:PlatCommon.Common.ViewDataHelper.ShowData(System.Windows.Forms.Control,System.Data.DataRow@)">
            <summary>
            显示数据
            </summary>
            <param name="parentCtrl"></param>
            <param name="dr"></param>
        </member>
        <member name="M:PlatCommon.Common.ViewDataHelper.CheckRequired(System.Windows.Forms.Control[])">
            <summary>
            必填项检查
            </summary>
            <param name="ctrls"></param>
            <returns>出错的控件</returns>
        </member>
        <member name="M:PlatCommon.Common.ViewDataHelper.CheckMaxLength(System.Windows.Forms.Control,System.Data.DataTable)">
            <summary>
            输入最大长度检测
            </summary>
            <param name="parentCtrl"></param>
            <param name="dt"></param>
            <returns>出错的控件</returns>
        </member>
        <member name="M:PlatCommon.Common.ViewDataHelper.SaveData(System.Windows.Forms.Control,System.Data.DataRow@)">
            <summary>
            保存数据
            </summary>
            <param name="parentCtrl"></param>
            <param name="dr"></param>
        </member>
        <member name="M:PlatCommon.Common.ViewDataHelper.SaveDataOneRow(System.Data.DataRow@,System.Data.DataRow@)">
            <summary>
            将数据集中的数据保存到数据表中,并写日志
            </summary>
            <param name="drDest"></param>
            <param name="drSrc"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.ViewDataHelper.RegDataChangeEvent(System.Windows.Forms.Control,System.EventHandler)">
            <summary>
            控件变更事件注册
            </summary>
            <param name="parentCtrl"></param>
            <param name="dataChangedEvent"></param>
        </member>
        <member name="M:PlatCommon.Common.ViewDataHelper.SetViewMode(System.Windows.Forms.Control,PlatCommon.Comm.OperMode)">
             <summary>
            设置控件模式 
             </summary>
             <param name="parentCtrl"></param>
             <param name="viewMode"></param>
        </member>
        <member name="M:PlatCommon.Common.ViewDataHelper.SetViewCtrlAttr(System.Windows.Forms.Control)">
            <summary>
            设置界面上的TextBox的属性
            </summary>
            <param name="parentCtrl"></param>
        </member>
        <member name="M:PlatCommon.Common.ViewDataHelper.SetToolBarStatus(System.Windows.Forms.ToolStrip@,PlatCommon.Comm.OperMode)">
            <summary>
            设置ToolBar状态
            </summary>
            <param name="tlb"></param>
            <param name="viewMode"></param>
        </member>
        <member name="M:PlatCommon.Common.ViewDataHelper.SetCheckBoxTagValue(System.Windows.Forms.CheckBox@,System.String)">
            <summary>
            为CheckBox中Tag进行赋值
            </summary>
            <param name="chk"></param>
             <param name="val"></param>
        </member>
        <member name="M:PlatCommon.Common.ViewDataHelper.chl_ItemCheck(System.Object,System.Windows.Forms.ItemCheckEventArgs)">
            <summary>
            CheckedListBox 项目选择事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:PlatCommon.Common.ViewDataHelper.trv_BeforeCheck(System.Object,System.Windows.Forms.TreeViewCancelEventArgs)">
            <summary>
            树勾选前事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:PlatCommon.Common.ViewDataHelper.LoadWindowAsControl(System.Windows.Forms.Control,System.Windows.Forms.Form,System.Windows.Forms.DockStyle)">
            <summary>
            把窗体作为控件加载
            </summary>
            <param name="ctrlParent"></param>
            <param name="frm"></param>
            <param name="style"></param>
        </member>
        <member name="F:PlatCommon.Common.w_his33_input_pwd.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:PlatCommon.Common.w_his33_input_pwd.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:PlatCommon.Common.w_his33_input_pwd.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:PlatCommon.Common.XtraGridViewHelper">
            <summary>
            DataGridView 助手
            </summary>
        </member>
        <member name="M:PlatCommon.Common.XtraGridViewHelper.GetConfigSchema">
            <summary>
            生成配置文件结构
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraGridViewHelper.SaveColumnConfig(System.Data.DataSet,System.String)">
            <summary>
            保存列的配置文件
            </summary>
            <param name="dsColConfig"></param>
            <param name="dgvId"></param>
        </member>
        <member name="M:PlatCommon.Common.XtraGridViewHelper.SaveColumnConfig(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            保存列宽
            </summary>
        </member>
        <member name="M:PlatCommon.Common.XtraGridViewHelper.GetColumnConfig(System.String)">
            <summary>
            获取DataGridView的列配置信息
            </summary>
            <param name="dgvId"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraGridViewHelper.GetColumnConfig(System.Data.DataSet,System.String)">
            <summary>
            DataGridView的列配置信息, 从数据源与配置文件中获取
            </summary>
            <param name="dsSrc"></param>
            <param name="dgvId"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraGridViewHelper.AddColByConfig(DevExpress.XtraGrid.Views.Grid.GridView,System.Data.DataSet)">
            <summary>
            通过配置信息生成DataGridView的列
            </summary>
        </member>
        <member name="M:PlatCommon.Common.XtraGridViewHelper.AddColByConfig(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            通过配置信息生成DataGridView的列
            </summary>
            <param name="dgv"></param>
            <param name="dgvId"></param>
        </member>
        <member name="M:PlatCommon.Common.XtraGridViewHelper.GetDisplayData(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            获取界面展现的值
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraGridViewHelper.GetDisplayData_OneRowOneTable(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            获取界面展现的值
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraGridViewHelper.ResetShowOrder(DevExpress.XtraGrid.Views.Grid.GridView@,System.Data.DataSet@,System.String)">
            <summary>
            更新现有排序
            </summary>
            <param name="gv">DEV GridView引用</param>
            <param name="ds">DataSet引用</param>
            <param name="colNameOrder">排序列</param>
        </member>
        <member name="M:PlatCommon.Common.XtraGridViewHelper.MoveUpRow(DevExpress.XtraGrid.Views.Grid.GridView@,System.Data.DataSet@,System.String)">
            <summary>
            向上移动
            </summary>
            <param name="gv">DEV GridView引用</param>
            <param name="ds">DataSet引用</param>
            <param name="colNameOrder">排序列</param>
        </member>
        <member name="M:PlatCommon.Common.XtraGridViewHelper.MoveDownRow(DevExpress.XtraGrid.Views.Grid.GridView@,System.Data.DataSet@,System.String)">
            <summary>
            向下移动
            </summary>
            <param name="gv">DEV GridView引用</param>
            <param name="ds">DataSet引用</param>
            <param name="colNameOrder">排序列</param>
        </member>
        <member name="M:PlatCommon.Common.XtraGridViewHelper.HandleInvalidRowException(DevExpress.XtraGrid.Views.Grid.GridView,DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs@)">
            <summary>
            行异常处理
            </summary>
            <param name="xgrv"></param>
            <param name="e"></param>
        </member>
        <member name="M:PlatCommon.Common.XtraGridViewHelper.FocusRow(DevExpress.XtraGrid.Views.Grid.GridView,System.Collections.Hashtable)">
            <summary>
            进行定位
            </summary>
        </member>
        <member name="T:PlatCommon.Common.XtraReportHelper">
            <summary>
            XtraReportHelper 助手
            </summary>
        </member>
        <member name="F:PlatCommon.Common.XtraReportHelper.PrinterName">
            <summary>
            打印机
            </summary>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.LoadTemplet(DevExpress.XtraPrinting.Preview.DocumentViewer@,DevExpress.XtraReports.UI.XtraReport@,System.String,System.String)">
            <summary>
            加载模板
            </summary>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.Print(System.String,System.Data.DataSet,System.String)">
            <summary>
            增加数据库取打印设计 20230727 liul
            </summary>
            <param name="templetFileName">模板名称</param>
            <param name="ds">数据集</param>
            <param name="appCode">应用名称</param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.Print(System.String,System.Data.DataSet,System.Collections.Generic.List{DevExpress.XtraReports.Parameters.Parameter},System.String)">
            <summary>
            增加数据库取打印传参设计 20230727 liul
            </summary>
            <param name="templetFileName"></param>
            <param name="ds"></param>
            <param name="listParameters"></param>
            <param name="appCode"></param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.Print(System.String,System.Data.DataSet,System.Boolean,System.String)">
            <summary>
            打印
            </summary>
            <param name="templetFileName">报表名称</param>
            <param name="ds">数据集</param>
            <param name="blnPreview">是否预览，True是，False否</param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.Print(System.String,System.Data.DataSet,System.Boolean,System.Boolean,System.String)">
            <summary>
            打印
            </summary>
            <param name="templetFileName">报表名称</param>
            <param name="ds">数据集</param>
            <param name="blnPreview">是否预览，True是，False否</param>
            <param name="thread"></param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.Print(System.String,System.Data.DataSet,System.Collections.Generic.List{DevExpress.XtraReports.Parameters.Parameter},System.Boolean,System.String)">
            <summary>
            打印(带参数)
            </summary>
            <param name="templetFileName">报表名称</param>
            <param name="ds">数据集</param>
            <param name="listParameters">参数列表</param>
            <param name="blnPreview">是否预览，True是，False否</param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.Print(System.String,System.Data.DataSet,System.Boolean,System.Boolean,System.Collections.Generic.List{DevExpress.XtraReports.Parameters.Parameter},System.String)">
            <summary>
            打印
            </summary>
            <param name="templetFileName"></param>
            <param name="ds"></param>
            <param name="blnPreview"></param>
            <param name="thread"></param>
            <param name="listParameters"></param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.Print(System.String,System.Data.DataSet,System.Boolean,System.String,System.String)">
            <summary>
            打印，传打印机名称参数
            </summary>
            <param name="templetFileName">报表名称</param>
            <param name="ds">数据集</param>
            <param name="blnPreview">是否预览，True是，False否</param>
            <param name="appCode">模块代码</param>
            <param name="printerName">打印机名称</param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.Print(System.String,System.Data.DataSet,System.Boolean,System.Boolean,System.String,System.String)">
            <summary>
            打印，传打印机名称参数
            </summary>
            <param name="templetFileName">报表名称</param>
            <param name="ds">数据集</param>
            <param name="blnPreview">是否预览，True是，False否</param>
            <param name="thread">线程</param>
            <param name="appCode">模块代码</param>
            <param name="printerName">打印机名称</param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.Print(System.String,System.Data.DataSet,System.Collections.Generic.List{DevExpress.XtraReports.Parameters.Parameter},System.Boolean,System.String,System.String)">
            <summary>
            打印，传打印机名称参数
            </summary>
            <param name="templetFileName">报表名称</param>
            <param name="ds">数据集</param>
            <param name="listParameters">参数集合</param>
            <param name="blnPreview">是否预览，True是，False否</param>
            <param name="appCode">模块代码</param>
            <param name="printerName">打印机名称</param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.Print(System.String,System.Data.DataSet,System.Boolean,System.Boolean,System.Collections.Generic.List{DevExpress.XtraReports.Parameters.Parameter},System.String,System.String)">
            <summary>
            打印，传打印机名称参数
            </summary>
            <param name="templetFileName">报表名称</param>
            <param name="ds">数据集</param>
            <param name="blnPreview">是否预览，True是，False否</param>
            <param name="thread">线程</param>
            <param name="listParameters">参数集合</param>
            <param name="appCode">模块代码</param>
            <param name="printerName">打印机名称</param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.WriteSchemaData(System.String,DevExpress.XtraReports.UI.XtraReport@,System.Collections.Hashtable,System.String)">
            <summary>
            把架构数据写入\Schema目录
            </summary>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetPrintData(DevExpress.XtraReports.UI.XtraReport@,System.Collections.Hashtable)">
            <summary>
            生成打印数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetPrintData(System.String,System.Collections.Hashtable,System.String)">
            <summary>
            生成打印数据
            </summary>
            <param name="templetName"></param>
            <param name="hasParam"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetPrintData(System.String,System.Collections.Hashtable,System.String,System.Boolean,System.String[])">
            <summary>
            生成打印数据
            </summary>
            <param name="templetName"></param>
            <param name="hasParam"></param> 
            /// <param name="isSql"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetPrintData(System.String,System.Collections.Hashtable,System.String,System.Boolean)">
            <summary>
            生成打印数据
            </summary>
            <param name="templetName"></param>
            <param name="hasParam"></param> 
            /// <param name="isSql"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetPrintData(System.String,System.Collections.Hashtable,System.Int32,System.String)">
            <summary>
            生成打印数据
            </summary>
            <param name="templetName"></param>
            <param name="hasParam"></param>
            <param name="reportNo"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetPrintData_DataBase(System.String,System.Collections.Hashtable,System.String)">
            <summary>
            生成打印数据 - 取数据库语句
            </summary>
            <param name="templetName"></param>
            <param name="hasParam"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetTempleteFileNameFull(System.String,System.String)">
            <summary>
            获取完整的模板文件名称
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetTemplateFileNameFull(System.String)">
            <summary>
            获取完整的模板文件名称
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetConfigSchema">
            <summary>
            生成配置文件结构
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetColumnConfig(System.String)">
            <summary>
            获取DataGridView的列配置信息
            </summary>
            <param name="dgvId"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.AddColByConfig(DevExpress.XtraGrid.Views.Grid.GridView,System.Data.DataSet)">
            <summary>
            通过配置信息生成DataGridView的列
            </summary>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.AddColByConfig(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            通过配置信息生成DataGridView的列
            </summary>
            <param name="dgv"></param>
            <param name="dgvId"></param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.SaveColumnConfig(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            保存列宽
            </summary>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.SaveColumnConfig(System.Data.DataSet,System.String)">
            <summary>
            保存列的配置文件
            </summary>
            <param name="dsColConfig"></param>
            <param name="dgvId"></param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetSql(DevExpress.XtraReports.UI.XtraReport@,System.Collections.Hashtable)">
            <summary>
            获取模板的SQL语句
            </summary>
            <param name="mReport"></param>
            <param name="hasParam"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetSql(System.String,System.Collections.Hashtable,System.String)">
            <summary>
            获取模板的SQL语句
            </summary>
            <param name="templetName"></param>
            <param name="hasParam"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetSql_DataBase(System.String,System.Collections.Hashtable,System.String,System.Boolean)">
            <summary>
            获取模板的SQL语句 取数据库 
            </summary>
            <param name="templetName"></param>
            <param name="hasParam"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetSql_Tree(System.String,System.Collections.Hashtable,System.String)">
            <summary>
            获取模板的SQL语句
            </summary>
            <param name="templetName"></param>
            <param name="hasParam"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetData(DevExpress.XtraReports.UI.XtraReport@,System.Collections.Hashtable)">
            <summary>
            获取报表数据
            </summary>
            <param name="mReport"></param>
            <param name="hasParam"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetData(System.String,System.Collections.Hashtable,System.String)">
            <summary>
            获取报表数据
            </summary>
            <param name="templetName"></param>
            <param name="hasParam"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetData_DataBase(System.String,System.Collections.Hashtable,System.String)">
            <summary>
            获取报表数据 - 取数据库
            </summary>
            <param name="templetName"></param>
            <param name="hasParam"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetData(System.String,System.Collections.Hashtable,System.String,System.Boolean)">
            <summary>
            获取报表数据
            </summary>
            <param name="templetName"></param>
            <param name="hasParam"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetDataStruct(System.String)">
            <summary>
            获取报表结构（无数据）
            </summary>
            <param name="templetName">报表模板名称</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetData_Tree(System.String,System.Collections.Hashtable,System.String)">
            <summary>
            获取报表数据
            </summary>
            <param name="templetName"></param>
            <param name="hasParam"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.WriteSchemaData(System.String,System.Data.DataSet)">
            <summary>
            把架构数据写入\Schema目录
            </summary>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.CreatePrintData(System.Data.DataTable[],System.Collections.Hashtable)">
            <summary>
            生成打印数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.CreatePrintData(System.Data.DataSet,System.Collections.Hashtable)">
            <summary> 
            2022-04-20
            </summary>
            <param name="_DS">传入dataset带数据</param>
            <param name="hasParam"> 变量 例如 统计日期 打印人 等信息的传递 </param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetSqlText(System.String,System.Collections.Hashtable)">
            <summary>
            获取SQL语句
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.ShowReportDesign(System.String,System.String)">
            <summary>
            报表设计
            </summary>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.ShowGridViewDesign(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            加载 GridView 风格配置 
            </summary>
            <param name="gvOrders"></param>
            <param name="item_class"></param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetfileName(System.String,System.String@)">
            <summary>
            新增GridView保存列属性方法  列款 排序 显示隐藏 保存列的配置文件
            </summary>
            <param name="dsColConfig"></param>
            <param name="dgvId"></param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.AddColByTable(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            通过配置信息生成DataGridView的列
            </summary>
            <param name="dgv"></param>
            <param name="dgvId"></param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.AddColByTable(DevExpress.XtraGrid.Views.Grid.GridView,System.Data.DataSet)">
            <summary>
            通过配置信息生成DataGridView的列 新增从表取值
            </summary>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.SaveGridViewDesign(DevExpress.XtraGrid.Views.Grid.GridView,System.String)">
            <summary>
            新增通用保存gridview风格到数据库和提取风格从数据库
            </summary>
            <param name="gvOrders"></param>
            <param name="item_class"></param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.SaveColumnToTable(DevExpress.XtraGrid.Views.Grid.GridView,System.String,System.String@)">
            <summary>
            新增GridView保存列属性方法  列款 排序 显示隐藏
            </summary>
            <param name="dgv"></param>
            <param name="dgvId"></param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.SaveColumnToTable(System.Data.DataSet,System.String,System.String@)">
            <summary>
            新增GridView保存列属性方法  列款 排序 显示隐藏 保存列的配置文件
            </summary>
            <param name="dsColConfig"></param>
            <param name="dgvId"></param>
        </member>
        <member name="M:PlatCommon.Common.XtraReportHelper.GetRepxStreamTemplate(System.String,System.String,System.String)">
            <summary>
            得到数据库中保存的报表模板格式的内存流
            </summary>
            <param name="reportHelper"></param>
            <param name="AppName">应用名称</param>
            <param name="ReportName">报表模板名称</param>
            <param name="HisUnitCode">院区代码</param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Comm.CallBackArgs">
            <summary>
            后台任务回调参数
            </summary>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.OpenCDSSByPath(System.String)">
            <summary>
            启动知识库客户端
            </summary>
            <param name="cdssPath">客户端中01cdss.exe的绝对路径</param>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.SetReceiveHandle(System.String,System.Int32)">
            <summary>
            注册程序信息
            </summary>
            <param name="systemType">系统类型，固定填写HIS</param>
            <param name="handleNum">当前窗口句柄号</param>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.SetSecurityKeys(System.String,System.String)">
            <summary>
            注册密钥信息，ak和sk联系CDSS技术人员提供
            </summary>
            <param name="ak">配置到参数表里了，AK_CDSS</param>
            <param name="sk">配置到参数表里了，SK_CDSS</param>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.UserLogin(System.String)">
            <summary>
            注册用户基本信息
            </summary>
            <param name="userInfo">用户信息（医生姓名、医生工号、当前登录科室、医院名称）</param>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.SendMessageToCDSS(System.String,System.Int32,System.Int32)">
            <summary>
            发送数据到CDSS
            </summary>
            <param name="message">当前业务数据（医嘱、申请单、诊断）</param>
            <param name="handleNum">当前窗口句柄号</param>
            <param name="trigger">11打开文书，12关闭文书，13保存病历/医嘱，14提交病历/医嘱</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.BroadcastMessageToCDSS(System.String,System.Int32)">
            <summary>
            CDSS回填数据
            </summary>
            <param name="msg">患者数据，符合规定格式的JSON字符串</param>
            <param name="handleNum">窗口句柄号</param>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.CloseCDSS">
            <summary>
            关闭知识库客户端
            </summary>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.SendMessageToRw(System.String)">
            <summary>
            人卫项目说明知识库
            </summary>
            <param name="message"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.WriteLog(System.String)">
            <summary>
            写日志
            </summary>
            <param name="str"></param>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.UserLogin">
            <summary>
            注册用户基本信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.OpenCdss">
            <summary>
            启动知识库客户端
            </summary>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.Regedit(System.String,System.Int32)">
            <summary>
            注册信息
            </summary>
            <param name="systemType">信息系统类型（HIS、EMR、LIS、RIS、OAMS手麻等）</param>
            <param name="handleNum">窗口句柄号</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.CloseCdss">
            <summary>
            关闭知识库客户端
            </summary>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.GetRwSm(System.String,System.String,System.String,System.String@)">
            <summary>
            人卫项目说明知识库
            </summary>
            <param name="itemClass">项目类别 C检验 D检查</param>
            <param name="itemName">项目名称</param>
            <param name="itemCode">项目代码</param>
            <param name="err">返回错误详细</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.GetExamReportKnowledge(System.String,System.String@)">
            <summary>
            查看检查报告知识库
            </summary>
            <param name="examNo"></param>
            <param name="err"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.Out_mr(System.String,System.DateTime,System.Int32,System.Int32,System.Data.DataSet,System.Int32)">
            <summary>
            门诊病历记录
            </summary>
            <param name="clinic_no"></param>
            <param name="visitDate"></param>
            <param name="visitNo"></param>
            <param name="type"></param>
            <param name="ds"></param>
            <param name="handle">窗口句柄号</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.outp_Orders(System.String,System.Data.DataSet,System.Int32,System.Int32)">
            <summary>
            门诊医嘱
            </summary>
            <param name="clinic_no"></param>
            <param name="ds"></param>
            <param name="type"></param>
            <param name="handle">当前窗口句柄号</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.inp_mr(System.String,System.String,System.Int32,System.Data.DataSet,System.Int32)">
            <summary>
            住院记录
            </summary>
            <param name="patient_id"></param>
            <param name="visit_id"></param>
            <param name="type"></param>
            <param name="ds"></param>
            <param name="handle">窗口句柄号</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.inp_Orders(System.String,System.String,System.Data.DataSet,System.Int32,System.Int32)">
            <summary>
            住院医嘱
            </summary>
            <param name="patient_id"></param>
            <param name="visit_id"></param>
            <param name="ds"></param>
            <param name="type"></param>
            <param name="handle">窗口句柄号</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.closs_mr(System.Int32)">
            <summary>
            关闭病历文档时自动触发
            </summary>
            <param name="handle"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.ClassKfzCdssLyzh.SetOrder(System.Data.DataTable@)">
            <summary>
            形成医嘱内容结构方法
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.class_obilling_public.DaXie(System.String)">
            <summary>
            大小写转换
            </summary>    
        </member>
        <member name="M:PlatCommon.Comm.class_obilling_public.CmycurD(System.Decimal)">
            <summary> 
            转换人民币大小金额 
            </summary> 
            <param name="num">金额</param> 
            <returns>返回大写形式</returns> 
        </member>
        <member name="M:PlatCommon.Comm.class_obilling_public.CmycurD(System.String)">
            <summary> 
            一个重载，将字符串先转换成数字在调用CmycurD(decimal num) 
            </summary> 
            <param name="num">用户输入的金额，字符串形式未转成decimal</param> 
            <returns></returns> 
        </member>
        <member name="M:PlatCommon.Comm.class_public_setprint.print_load_printname(System.String)">
            <summary>
            加载打印机列表
            </summary>
        </member>
        <member name="T:PlatCommon.Comm.ComConst">
            <summary>
            ComConst 的摘要说明。
            </summary>
        </member>
        <member name="T:PlatCommon.Comm.ComConst.STR">
            <summary>
            字符串
            </summary>
        </member>
        <member name="T:PlatCommon.Comm.ComConst.FMT_DATE">
            <summary>
            日期格式
            </summary>
        </member>
        <member name="T:PlatCommon.Comm.ComConst.VAL">
            <summary>
            常用数字
            </summary>
        </member>
        <member name="T:PlatCommon.Comm.ComConst.FMT_MONEY">
            <summary>
            货币格式
            </summary>
        </member>
        <member name="T:PlatCommon.Comm.DataType">
            <summary>
            ComFunctions 的摘要说明。
            </summary>
        </member>
        <member name="M:PlatCommon.Comm.DataType.DateTime_Null">
            <summary>
            获取DateTime的Null值
            </summary>
            <remarks>假定一个"1-1-1 12:00:00"值作为Null值</remarks>
            <returns>DateTime</returns>
        </member>
        <member name="M:PlatCommon.Comm.DataType.DateTime_IsNull(System.DateTime@)">
            <summary>
            判断DateTime是否为Null
            </summary>
            <param name="dt">DateTime</param>
            <returns>TRUE: 是为Null; FALSE: 不为Null</returns>
        </member>
        <member name="M:PlatCommon.Comm.DataType.DateTimeFromString(System.String)">
            <summary>
            把字符串转换成为日期
            </summary>
            <param name="strDateTime">日期字符串</param>
            <returns>日期</returns>
        </member>
        <member name="M:PlatCommon.Comm.DataType.GetDateTime(System.String@)">
            <summary>
            从一个字符串中尽量提取日期时间字符串
            </summary>
            <remarks>
             1: 年月日时分秒可以被任意非数字字符分隔
             2: 年月日时分秒可以不用任何分隔符分隔,提取时按年四位数整数, 其它都有两位整数
             3: 1与2两种情况的结合体也可以识别, 如 200012-2 20:12-43, 可以正确识别
            </remarks>
            <param name="dtSrc">日期时间字符串</param>
            <returns>TRUE: 成功; FALSE: 失败</returns>
        </member>
        <member name="M:PlatCommon.Comm.DataType.GetDateTimeShort(System.String)">
            <summary>
            把一个表示日期的字符串转换成短日期格式
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.DataType.GetDateTimeLong(System.String)">
            <summary>
            把一个表示日期的字符串转换成长日期格式
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.DataType.GetShortTime(System.String)">
            <summary>
            获取短时间格式
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.DataType.GetMD(System.String)">
            <summary>
            获取月日(如8.2)
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.DataType.GetYMD(System.String)">
            <summary>
            获取年月日, 如2007年12月6日
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.DataType.GetRomaNum(System.Int32)">
            <summary>
            获取数字的罗马表示
            </summary>
            <param name="num"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.DataType.IsNumber(System.String)">
            <summary>
            判断一个字符串是不是正整数
            </summary>
            <remarks>仅包含数字</remarks>
            <param name="text">待验证的字符串</param>
            <returns>TRUE: 符合要求; FALSE: 不符合要求</returns>
        </member>
        <member name="M:PlatCommon.Comm.DataType.IsPositive(System.String)">
            <summary>
            检查某个数是不是正数(包括0)
            </summary>
            <param name="text">要检查的字符串</param>
            <returns>TRUE: 成功; FALSE: 失败</returns>
        </member>
        <member name="M:PlatCommon.Comm.DataType.IsTime(System.String@)">
            <summary>
            检查一个字符串是不是合法的时间格式
            如果是: 把输入字符串转换成标准的日期格式HH:MM:SS
            </summary>
            <param name="timeStr">表示时间的字符</param>
            <returns>TRUE: 是; FALSE: 不是</returns>
        </member>
        <member name="M:PlatCommon.Comm.DataType.IsDateTime(System.String)">
            <summary>
            判断字符串是不是日期时间,如果是转换成标准形式 YYYY-MM-DD HH:mm:ss
            </summary>
            <param name="dtStr"></param>
            <returns>TRUE: 是; FALSE: 否</returns>
        </member>
        <member name="M:PlatCommon.Comm.DataType.IsShortDate(System.String)">
            <summary>
            检查一个短日期是否是正确的日期格式
            </summary>
            <remarks>YYYY-MM-DD或YYYYMMDD</remarks>
            <param name="dtStr"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.GetInternetConStatus.GetNetConStatus(System.String)">
             <summary>
             判断网络的连接状态
             </summary>
             <returns>
             网络状态(1-->未联网;2-->采用调治解调器上网;3-->采用网卡上网)
            </returns>
        </member>
        <member name="M:PlatCommon.Comm.GetInternetConStatus.PingNetAddress(System.String)">
            <summary>
            ping 具体的网址看能否ping通
            </summary>
            <param name="strNetAdd"></param>
            <returns></returns>
        </member>
        <!-- 对于成员“M:PlatCommon.Comm.GfHis13GetFirstLetter.GetSpellCode(System.String)”忽略有格式错误的 XML 注释 -->
        <!-- 对于成员“M:PlatCommon.Comm.GfHis13GetFirstLetter.GetCharSpellCode(System.String)”忽略有格式错误的 XML 注释 -->
        <member name="M:PlatCommon.Comm.GfHis13GetFirstLetter.CutString(System.String,System.Int32)">
            <summary>
            截取字符长度
            </summary>
            <param name="str">被截取的字符串</param>
            <param name="len">所截取的长度</param>
            <returns>子字符串</returns>
        </member>
        <member name="T:PlatCommon.Comm.IME">
            <summary>
            输液法控制
            </summary>
        </member>
        <member name="M:PlatCommon.Comm.IME.SetIme(System.String)">
            <summary>
            设定当前Ime, 使用方法Ime.SetImeName("中文 (简体) - 拼音加加"); 
            </summary>
            <param name="ImeName"></param>
        </member>
        <member name="M:PlatCommon.Comm.IME.GetImes">
            <summary>
            获得所有的Ime列表 
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.IME.ChangeControlIme(System.IntPtr)">
            <summary>
            输入法全角半角转换
            </summary>
            <param name="h"></param>
        </member>
        <member name="M:PlatCommon.Comm.NR_Code.GetClinicDate(System.String@,System.String@)">
            <summary>
             获取挂号当天日期
            </summary>
            <param name="age"></param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.Comm.TreeViewHelper">
            <summary>
            TreeView操作
            </summary>
        </member>
        <member name="M:PlatCommon.Comm.TreeViewHelper.#ctor">
            <summary>
            默认构造函数
            </summary>
        </member>
        <member name="M:PlatCommon.Comm.TreeViewHelper.NodeMoveUp(System.Windows.Forms.TreeView@,System.Windows.Forms.TreeNode@)">
            <summary>
            向上移动节点
            </summary>
            <param name="trv">TreeView控件</param>
            <param name="node">指定节点</param>
            <returns>TRUE: 成功; FALSE: 失败</returns>
        </member>
        <member name="M:PlatCommon.Comm.TreeViewHelper.NodeMoveDown(System.Windows.Forms.TreeView@,System.Windows.Forms.TreeNode@)">
            <summary>
            向下移动节点
            </summary>
            <param name="trv">TreeView控件</param>
            <param name="node">指定节点</param>
            <returns>TRUE: 成功; FALSE: 失败</returns>
        </member>
        <member name="M:PlatCommon.Comm.TreeViewHelper.ClearSubNodes(System.Windows.Forms.TreeView@,System.Windows.Forms.TreeNode)">
            <summary>
            清除所有节点
            </summary>
            <param name="trv">TreeView控件</param>
            <param name="parentNode">指定节点</param>
        </member>
        <member name="M:PlatCommon.Comm.TreeViewHelper.ClearChecked(System.Windows.Forms.TreeView@,System.Windows.Forms.TreeNode)">
            <summary>
            清除所有节点的Check状态
            </summary>
            <param name="trv">TreeView控件</param>
            <param name="parentNode">指定节点</param>
        </member>
        <member name="M:PlatCommon.Comm.TreeViewHelper.CheckedSpread(System.Windows.Forms.TreeView@,System.Windows.Forms.TreeNode)">
            <summary>
            节点Checked状波及上下级节点
            </summary>
            <param name="trv">TreeView控件</param>
            <param name="checkedNode">指定节点</param>
        </member>
        <member name="M:PlatCommon.Comm.TreeViewHelper.DiGuiCheckTreeNode(System.Windows.Forms.TreeNode)">
            <summary>
            LB20120109,选中一个节点的所有子节点
            </summary>
            <param name="parentNode"></param>
        </member>
        <member name="M:PlatCommon.Comm.TreeViewHelper.LoadTreeNodes(System.Windows.Forms.TreeView@,System.Data.DataSet,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            加载树节点
            </summary>
            <param name="trv">树控件</param>
            <param name="dsData">数据</param>
            <param name="tableName">表名</param>
            <param name="colNodeCode">节点代码字段名称</param>
            <param name="colNodeParentCode">父节点代码字段名称</param>
            <param name="colText">节点名称显示哪个字段的值</param>
            <param name="colTag">节点备注显示哪个字段的值</param>
            <param name="colOrder">节点排序字段</param>
            <param name="colIconFile">图标字段</param>
            <param name="iconPath">图标文件所在路径</param>
            <returns>TRUE: 加载成功; FALSE: 加载失败</returns>
        </member>
        <member name="M:PlatCommon.Comm.TreeViewHelper.LoadTreeNodes(System.Windows.Forms.TreeView@,System.Data.DataSet,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            加载树节点
            </summary>
            <param name="trv">树控件</param>
            <param name="dsData">数据</param>
            <param name="colNodeCode">节点代码字段名称</param>
            <param name="colNodeParentCode">父节点代码字段名称</param>
            <param name="colText">节点名称显示哪个字段的值</param>
            <param name="colTag">节点备注显示哪个字段的值</param>
            <param name="colOrder">节点排序字段</param>
            <param name="colIconFile">图标字段</param>
            <param name="iconPath">图标文件所在路径</param>
            <returns>TRUE: 加载成功; FALSE: 加载失败</returns>
        </member>
        <member name="M:PlatCommon.Comm.TreeViewHelper.LoadTreeNodes(System.Windows.Forms.TreeView@,System.Data.DataSet,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            加载树节点(二级菜单)
            </summary>
            <param name="trv">树控件</param>
            <param name="dsData">数据</param>
            <param name="tableName">表名</param>
            <param name="strNodeCode">一级菜单项编码</param>
            <param name="colNodeCode">节点代码字段名称</param>
            <param name="colNodeParentCode">父节点代码字段名称</param>
            <param name="colText">节点名称显示哪个字段的值</param>
            <param name="colTag">节点备注显示哪个字段的值</param>
            <param name="colOrder">节点排序字段</param>
            <param name="colIconFile">图标字段</param>
            <param name="iconPath">图标文件所在路径</param>
            <returns>TRUE: 加载成功; FALSE: 加载失败</returns>
        </member>
        <member name="M:PlatCommon.Comm.TreeViewHelper.LoadTreeSubNodes(System.Windows.Forms.TreeNode,System.String,System.Data.DataSet,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            加载树节点
            </summary>
            <param name="nodeParent">父节点</param>
            <param name="parentCode">父节点代码</param>
            <param name="dsData">数据</param>
            <param name="tableName">表名</param>
            <param name="colNodeCode">节点代码字段名称</param>
            <param name="colNodeParentCode">父节点代码字段名称</param>
            <param name="colText">节点名称显示哪个字段的值</param>
            <param name="colTag">节点备注显示哪个字段的值</param>
            <param name="colOrder">节点排序字段</param>
            <param name="colIconFile">图标字段</param>
            <param name="iconPath">图标文件所在路径</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.TreeViewHelper.LoadTreeNodes2(System.Windows.Forms.TreeView@,System.Data.DataSet,System.String,System.String,System.Int32,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            加载树节点
            </summary>
            <param name="trv">树控件</param>
            <param name="dsData">数据</param>
            <param name="tableName">表名</param>
            <param name="colNodeCode">节点代码字段名称</param>
            <param name="codeLevelLen">每一层代码的长度</param>
            <param name="colText">节点名称显示哪个字段的值</param>
            <param name="colTag">节点备注显示哪个字段的值</param>
            <param name="colOrder">节点排序字段</param>
            <param name="colIconFile">图标字段</param>
            <param name="iconPath">图标文件所在路径</param>
            <returns>TRUE: 加载成功; FALSE: 加载失败</returns>
        </member>
        <member name="M:PlatCommon.Comm.TreeViewHelper.LoadTreeSubNodes2(System.Windows.Forms.TreeNode,System.String,System.Data.DataSet,System.String,System.String,System.Int32,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            加载树节点
            </summary>
            <param name="nodeParent">父节点</param>
            <param name="parentCode">父节点代码</param>
            <param name="dsData">数据</param>
            <param name="tableName">表名</param>
            <param name="colNodeCode">节点代码字段名称</param>
            <param name="codeLevelLen">每一层代码的长度</param>
            <param name="colText">节点名称显示哪个字段的值</param>
            <param name="colTag">节点备注显示哪个字段的值</param>
            <param name="colOrder">节点排序字段</param>
            <param name="colIconFile">图标字段</param>
            <param name="iconPath">图标文件所在路径</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.TreeViewHelper.getDllPath">
            <summary>
            获取当前dll的路径
            </summary>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.Comm.TreeViewHelper.getIconFile(System.String,System.String)">
            <summary>
            获取图标文件的绝对路径
            </summary>
            <param name="iconPath">图标文件所有目录 (相对于当前dll)</param>
            <param name="iconFile">图标文件</param>
            <returns>图标文件的绝对路径</returns>
        </member>
        <member name="M:PlatCommon.Comm.Utility.GetXmlAttr(System.String,System.String,System.String,System.String)">
            <summary>
            获取xml节点属性
            </summary>
            <param name="section"></param>
            <param name="attribute"></param>
            <returns></returns>
        </member>
        <member name="T:PlatCommon.SysBase.ClassValueCopier">
            <summary>
            类属性/字段的值复制工具
            </summary>
        </member>
        <member name="M:PlatCommon.SysBase.ClassValueCopier.Copy(System.Object,System.Object)">
            <summary>
            复制
            </summary>
            <param name="destination">目标</param>
            <param name="source">来源</param>
            <returns>成功复制的值个数</returns>
        </member>
        <member name="M:PlatCommon.SysBase.ClassValueCopier.Copy(System.Object,System.Object,System.Type)">
            <summary>
            复制
            </summary>
            <param name="destination">目标</param>
            <param name="source">来源</param>
            <param name="type">复制的属性字段模板</param>
            <returns>成功复制的值个数</returns>
        </member>
        <member name="M:PlatCommon.SysBase.ClassValueCopier.Copy(System.Object,System.Object,System.Type,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            复制
            </summary>
            <param name="destination">目标</param>
            <param name="source">来源</param>
            <param name="type">复制的属性字段模板</param>
            <param name="excludeName">排除下列名称的属性不要复制</param>
            <returns>成功复制的值个数</returns>
        </member>
        <member name="M:PlatCommon.SysBase.ClassValueCopier2.GetPropertyNames(System.Object)">
            <summary>
            获取实体类的属性名称
            </summary>
            <param name="source">实体类</param>
            <returns>属性名称列表</returns>
        </member>
        <member name="M:PlatCommon.SysBase.ClassValueCopier2.GetPropertyNames(System.Type)">
            <summary>
            获取类类型的属性名称（按声明顺序）
            </summary>
            <param name="source">类类型</param>
            <returns>属性名称列表</returns>
        </member>
        <member name="M:PlatCommon.SysBase.ClassValueCopier2.GetPropertyNames(System.Type,System.Boolean)">
            <summary>
            获取类类型的属性名称
            </summary>
            <param name="source">类类型</param>
            <param name="declarationOrder">是否按声明顺序排序</param>
            <returns>属性名称列表</returns>
        </member>
        <member name="M:PlatCommon.SysBase.ClassValueCopier2.CopyValueFrom(System.Object,System.Object)">
            <summary>
            从源对象赋值到当前对象
            </summary>
            <param name="destination">当前对象</param>
            <param name="source">源对象</param>
            <returns>成功复制的值个数</returns>
        </member>
        <member name="M:PlatCommon.SysBase.ClassValueCopier2.CopyValueFrom(System.Object,System.Object,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            从源对象赋值到当前对象
            </summary>
            <param name="destination">当前对象</param>
            <param name="source">源对象</param>
            <param name="excludeName">排除下列名称的属性不要复制</param>
            <returns>成功复制的值个数</returns>
        </member>
        <member name="M:PlatCommon.SysBase.ClassValueCopier2.CopyValueTo(System.Object,System.Object)">
            <summary>
            从当前对象赋值到目标对象
            </summary>
            <param name="source">当前对象</param>
            <param name="destination">目标对象</param>
            <returns>成功复制的值个数</returns>
        </member>
        <member name="M:PlatCommon.SysBase.ClassValueCopier2.CopyValueTo(System.Object,System.Object,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            从当前对象赋值到目标对象
            </summary>
            <param name="source">当前对象</param>
            <param name="destination">目标对象</param>
            <param name="excludeName">排除下列名称的属性不要复制</param>
            <returns>成功复制的值个数</returns>
        </member>
        <member name="M:PlatCommon.SysBase.CommonFuntion.BitmapToIcon(System.Drawing.Bitmap,System.Boolean,System.Int32,System.Int32)">
            <summary>
            转换icon图片
            </summary>
            <param name="obm"></param>
            <param name="preserve"></param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.SysBase.DoctorParameter.JudgeParam(System.Data.DataTable,System.String)">
            <summary>
            判断参数是否存在
            </summary>
            <param name="paraName">参数名称</param>
            <returns>true--存在;false--不存在</returns>
        </member>
        <member name="M:PlatCommon.SysBase.DoctorParameter.JudgeParamBaseinfo(System.Data.DataTable,System.String)">
            <summary>
            判断参数说明是否存在
            </summary>
            <param name="dtBaseinfo">参数说明表</param>
            <param name="parameterName">参数名称</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.SysBase.DoctorParameter.InserParameter(System.Data.DataTable,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            插入参数表
            </summary>
            <param name="dtParameter">参数表</param>
            <param name="appName">应用名</param>
            <param name="deptCode">适用科室</param>
            <param name="empNo">适用人员</param>
            <param name="parameterName">参数名称</param>
            <param name="parameterValue">参数值</param>
        </member>
        <member name="M:PlatCommon.SysBase.DoctorParameter.InsertBaseinfo(System.Data.DataTable,System.String,System.Int32,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            插入参数说明表
            </summary>
            <param name="dtBaseinfo">参数说明表</param>
            <param name="appName">应用名</param>
            <param name="parameterNo">参数序号</param>
            <param name="parameterName">参数名称</param>
            <param name="parainitValue">参数初始值</param>
            <param name="parameterScope">取值范围</param>
            <param name="explanation">说明</param>
            <param name="parameterClass">参数分类</param>
        </member>
        <member name="T:PlatCommon.SysBase.ParentForm">
            <summary>
            父窗体（继承自XtraForm）
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.AppCode">
            <summary>
            应用编码
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.AppName">
            <summary>
            应用名称
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.DeptCode">
            <summary>
            当前科室编码
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.DeptName">
            <summary>
            科室名称
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.DeptList">
            <summary>
            科室列表
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.WardCode">
            <summary>
            当前病区编码
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.WardName">
            <summary>
            病区名称
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.WardList">
            <summary>
            病区列表
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.PatientInfo">
            <summary>
            当前患者信息
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.Capability">
            <summary>
            当前APP用户权限（app_grants.Capability)
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.ClinicClass">
            <summary>
            核算组使用方式(0-诊疗组核算;1-核算组核算),参数 CLINIC_CLASS设置
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.NewBornOut">
            <summary>
            母亲和新生儿是否一起出科，1是，0否，参数 NEW_BORN_OUT设置
            </summary>        
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.NurseRole">
            <summary>
            护理单元护士角色(1-普通护士、2-护士长、3-大科护士长、4-护理部人员)，表NR_DICT_NURSE_ROLE设置
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.IsAllDept">
            <summary>
            是否全病区(护理管理）
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.DutyRightDept">
            <summary>
            护士权限科室（护理管理、门诊护士站）
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.ReportDesignRoles">
            <summary>
            护理管理报表设计角色设定(1-护理部,2-大护士长,3-护士长,4-护士。例：1,2,3),参数REPORT_DESIGN_ROLE设置
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.NurAdmDutyRight">
            <summary>
            人员所有授权的职务,表 NURADM_DUTY_RIGHT设置
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.Clinical_Pathway">
            <summary>
            临床路径启用标志 (0-不启用,默认；1-启用)
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.ParentForm.QueueName">
            <summary>
            医生出诊诊室名称，即队列名称,表OUTP_DOCTOR_SCHEDULE
            </summary>
        </member>
        <member name="T:PlatCommon.SysBase.SystemParmBak">
            <summary>
            系统参数(备份版本）
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParmBak.Gs_Bjca_Enabled">
            <summary>
            北京CA是否可用
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParmBak.Gs_Time_Snap">
            <summary>
            是否启用时间戳
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParmBak.Warcode_list">
            <summary>
            护理单元编码列表，逗号间隔
            </summary>
            //住院护理单元数据集
        </member>
        <member name="P:PlatCommon.SysBase.SystemParmBak.Database_ip">
            <summary>
            数据库IP
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParmBak.Database_user">
            <summary>
            数据库用户名
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParmBak.Database_pass">
            <summary>
            数据库密码
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParmBak.Database_dbname">
            <summary>
            数据库名称
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParmBak.ConnectionString">
            <summary>
            获取业务系统数据库连接字符串
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParmBak.CLINICAL_PATHWAY">
            <summary>
            是否启用临床路径0不启用，1启用
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParmBak.CA_PIN">
            <summary>
            获取业务系统数据库连接字符串
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParmBak.NurAdmDutyRight">
            <summary>
            人员所有授权的职务
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParmBak.ReportDesignRoles">
            <summary>
            报表设计角色
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParmBak.NURSE_ROLE">
            <summary>
            报表设计角色
            </summary>
        </member>
        <member name="M:PlatCommon.SysBase.SystemParmBak.GetParameterValue(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            取系统参数
            </summary>
            <param name="paraName">参数名称</param>
            <param name="appName">应用程序名称</param>
            <param name="deptCode">科室代码</param>
            <param name="userName">用户代码</param>
            <param name="defaltValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.SysBase.SystemParmBak.GetParaValue(System.String,System.String,System.String,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            取系统参数
            </summary>
            <param name="paraName">参数名称</param>
            <param name="appName">应用程序名称</param>
            <param name="deptCode">科室代码</param>
            <param name="userName">用户代码</param>
            <param name="defaltValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.SysBase.SystemParmBak.GetParaValue(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            取系统参数
            </summary>
            <param name="paraName">参数名称</param>
            <param name="appName">应用程序名称</param>
            <param name="deptCode">科室代码</param>
            <param name="userName">用户代码</param>
            <param name="defaltValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.SysBase.SystemParmBak.GetButtonRight(System.String,System.String)">
            <summary>
            取用户按钮权限
            </summary>
            <param name="appName">模块名称</param>
            <param name="userName">用户登录名称</param>
            <returns>权限列表</returns>
        </member>
        <member name="M:PlatCommon.SysBase.SystemParmBak.GetDockFormInDll(System.String,System.String,System.String)">
            <summary>
            从动态库中获取窗体
            </summary>
            <param name="dllName">动态库的名称</param>
            <param name="typeName">类名</param>
            <returns>窗体类</returns>
        </member>
        <member name="T:PlatCommon.SysBase.SystemParm">
            <summary>
            系统全局参数
            </summary>
        </member>
        <member name="F:PlatCommon.SysBase.SystemParm.version">
            <summary>
            系统版本号,从hospital_config中获取
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.Database_ip">
            <summary>
            数据库IP
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.Database_user">
            <summary>
            数据库用户名
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.Database_pass">
            <summary>
            数据库密码
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.Database_dbname">
            <summary>
            数据库名称
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.ConnectionString">
            <summary>
            获取业务系统数据库连接字符串
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.HisUnitCode">
            <summary>
            医院编码
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.HospitalID">
            <summary>
            医院名称
            </summary>        
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.Hospitalhospital_label">
            <summary>
            医院别名 区分同名医院的不同分院 
            </summary>        
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.Dayin_print_url">
            <summary>
            化验单样本采集窗体打印机
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.IsPass">
            <summary>
            是否启用合理用药
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.PassEnabled">
            <summary>
            合理用药是否可用
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.Gs_Bjca_Enabled">
            <summary>
            北京CA是否可用
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.Gs_Time_Snap">
            <summary>
            是否启用时间戳
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.UdpClass">
            <summary>
            消息类
            </summary>        
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.LoginUser">
            <summary>
            登录用户
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.Lis_print_url">
            <summary>
            Lis打印图像地址
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.Pacs_print_url">
            <summary>
            PACS打印图像地址
            </summary>
        </member>
        <member name="P:PlatCommon.SysBase.SystemParm.CA_PIN">
            <summary>
            用户key密码
            </summary>
        </member>
        <member name="M:PlatCommon.SysBase.SystemParm.GetParameterValue(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            取系统参数
            </summary>
            <param name="paraName">参数名称</param>
            <param name="appName">应用程序名称</param>
            <param name="deptCode">科室代码</param>
            <param name="userName">用户代码</param>
            <param name="defaltValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.SysBase.SystemParm.GetParaValue(System.String,System.String,System.String,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            取系统参数
            </summary>
            <param name="paraName">参数名称</param>
            <param name="appName">应用程序名称</param>
            <param name="deptCode">科室代码</param>
            <param name="userName">用户代码</param>
            <param name="defaltValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:PlatCommon.SysBase.SystemParm.GetParaValue(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            取系统参数
            </summary>
            <param name="paraName">参数名称</param>
            <param name="appName">应用程序名称</param>
            <param name="deptCode">科室代码</param>
            <param name="userName">用户代码</param>
            <param name="defaltValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:PlatPublic.Common.ImageHelper.ImageToBytes(System.String,System.Drawing.Imaging.ImageFormat)">
            <summary>
            图片转换byte数组
            </summary>
            <param name="imageFile"></param>
            <param name="fmat">指定图片保存格式</param>
            <returns></returns>
        </member>
        <member name="M:PlatPublic.Common.ImageHelper.GetThumbnail(System.Drawing.Bitmap,System.Int32,System.Int32)">
            <summary>
            取新尺寸大小的图片
            </summary>
            <param name="b">原图</param>
            <param name="destHeight">新高</param>
            <param name="destWidth">新宽</param>
            <returns></returns>
        </member>
        <member name="M:PlatPublic.Common.ImageHelper.BitmapToIcon(System.Drawing.Bitmap,System.Boolean,System.Int32,System.Int32)">
            <summary>
            转换icon图片
            </summary>
            <param name="obm"></param>
            <param name="preserve"></param>
            <param name="ICON_W"></param>
            <param name="ICON_H"></param>
            <returns></returns>
        </member>
        <member name="M:PlatPublic.Common.ImageHelper.CreateImage(System.Byte[])">
            <summary>
            从字节转换为图像
            </summary>
            <param name="bitmapData">图像的字节数组</param>
            <returns>图象</returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Imp.MDC_GetTaskStatus(System.String,System.String,System.String,System.String,System.Int32)">
            <summary>
            根据返回值的通过1和不通过值0来进行his系统不同业务逻辑的处理
            </summary>
            <param name="pcPatCode">表示病人ID，与参数pcPatCode唯一确定一个病人，此参数不能为空。要求与MDC_SetPatient函数传入的pcPatCode参数值完全一致</param>
            <param name="pcInHospNo">表示病人门诊号或住院号，此参数不能为空。要求与MDC_SetPatient函数传入的pcInHospNo参数值完全一致</param>
            <param name="pcVisitCode">表示病人就诊次数或住院次数，与参数pcPatCode唯一确定一个病人，如果HIS系统没有此信息，则可传入"1"。要求与MDC_SetPatient函数传入的pcVisitCode参数值完全一致</param>
            <param name="pcRecipNo">A.	门诊传空字符串。 B.住院传医嘱唯一码，与MDC_AddScreenDrug函数传入pcindex参数值完全一致。 注意：只有在提交医嘱，对审核没有问题的医嘱准予提交，对于有问题的医嘱返回修改，则才循环此方法并此参数传入医嘱唯一码，否则传入空即可 </param>
            <param name="piTaskType">整型，表示病人类型：1表示住院病人（默认），2表示门诊病人。</param>
            <returns>药师干预结果： A.门诊 1-通过，0-不能通过 B.住院 1-通过，0-不能通过，-1-待定 </returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Imp.MDC_GetTaskDetail(System.String,System.String,System.String,System.String,System.Int32)">
             <summary>
             根据json返回值操作
             </summary>
             <param name="pcPatCode">表示病人ID，与参数pcPatCode唯一确定一个病人，此参数不能为空。要求与MDC_SetPatient函数传入的pcPatCode参数值完全一致</param>
             <param name="pcInHospNo">表示病人门诊号或住院号，此参数不能为空。要求与MDC_SetPatient函数传入的pcInHospNo参数值完全一致</param>
             <param name="pcVisitCode">表示病人就诊次数或住院次数，与参数pcPatCode唯一确定一个病人，如果HIS系统没有此信息，则可传入"1"。要求与MDC_SetPatient函数传入的pcVisitCode参数值完全一致</param>
             <param name="pcRecipNo">A.	门诊传空字符串。 B.住院传医嘱唯一码，与MDC_AddScreenDrug函数传入pcindex参数值完全一致。 注意：只有在提交医嘱，对审核没有问题的医嘱准予提交，对于有问题的医嘱返回修改，则才循环此方法并此参数传入医嘱唯一码，否则传入空即可 </param>
             <param name="piTaskType">整型，表示病人类型：1表示住院病人（默认），2表示门诊病人。</param>
             <returns>object对象，如下例所示："Status":9,"OverTime":180,"PassTime":0,"Paused":0,"Reason":"","MHisCode":0,"CaseID":"0_2_BRBM22-05_MZH22-05_MZH22-05","TaskId":"201808280954468213","StatusNo":null,"Mobile":"","TelePhone":"","Urgent":-1,"NeedApply":0,"NeedAutoIntervene":0,"NeedCounterSign":2,"WorkerId":"4","WorkerName":"药师1","StatusDesc":"医生双签后药师复核通过","Type":2,"OrderNum":null,"NewUseReason":null}
             Status：任务状态。
            -15 含拦截问题，医生需修改
               -14 医生修改后，存在药师关注问题
               -13 含药师关注问题，提请药师审核
               -12 医生修改后提请药师审核
               -11 自动干预系统预判不通过
               -10 医生修改后重点关注提请药师审核
               -9 重点关注提请药师审核
               -8 医生双签后药师复核不通过
               -7 医生修改后系统预判不通过
               -6 医生已填写理由并双签
               -5 医生提请药师审核
               -4 默认状态
               -3 医生修改后药师再次不通过
               -2 药师首次审核不通过
               -1 系统预判不通过
               0 系统预判通过
               1 药师首次审核通过
               2 医生双签后自动通过
               3 医生修改后药师审核通过
               4 自动通过-已关闭系统干预功能
               5 自动通过-该科室未分配审方药师
               6 超时通过-药师还未获取
               7 自动通过-无审方药师在线
               8 自动通过-该科室审方药师不在线
               9 医生双签后药师复核通过
              10 系统自动干预后通过
              11 医生修改后系统预判通过
              12 自动通过-药师离开或退出
              13 超时通过-医生修改后，药师未审核
              14 超时通过-医生双签后，药师未复核
              15 超时通过-药师还未审核
              16 自动通过-医生修改后，已关闭干预功能
              17 自动通过-医生双签后，已关闭干预功能
              18 自动通过-医生修改后，药师离开或退出
              19 自动通过-医生双签后，药师离开或退出
              20 系统预判问题经药师确认后通过
              21 自动通过-未达到药师监测标准
              22 自动通过-不进行药师审方 
             </returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Transfer.MeiKang_Load(System.String,System.String,System.String)">
            <summary>
            美康接口初始化
            </summary>
            <param name="pcCheckMode">住院类型</param>
            <param name="pcHisCode">医院名称(汉字)</param>
            <param name="pcDoctorCode">医生编码</param>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Transfer.MDC_SetPatient(System.String,System.String)">
            <summary>
            病人基本信息函数
            </summary>
            <param name="pcPatCode">病人ID</param>
            <param name="pcInHospNo">病人门诊号或住院号</param>
            <param name="pcVisitCode">病人就诊次数或住院次数</param>
            <param name="pcName">病人姓名</param>
            <param name="pcSex">病人性别</param>
            <param name="pcBirthday">病人出生日期</param>
            <param name="pcHeightCM">病人身高</param>
            <param name="pcWeighKG">体重</param>
            <param name="pcDeptCode">科室编码</param>
            <param name="pcDeptName">科室名称</param>
            <param name="pcDoctorCode">医生编码</param>
            <param name="pcDoctorName">医生名称</param>
            <param name="piPatStatus">病人状态1表示住院病人（默认），2表示门诊病人，3表示急诊病人。用于采集病人的类型</param>
            <param name="piIsLactation">哺乳状态 取值： -1-无法获取哺乳状态（默认）;0-不是;1-是</param>
            <param name="piIsPregnancy">妊娠状态 取值： -1-无法获取妊娠状态（默认）;0-不是;1-是</param>
            <param name="pcPregStartDate">妊娠开始日期</param>
            <param name="piHepDamageDegree">肝损害程度 取值： -1-不确定（默认）；0-无肝损害；1-肝功能不全；2-轻度肝损害；3-中度肝损害；4-重度肝损害</param>
            <param name="piRenDamageDegree">肾损害程度 取值： -1-不确定（默认）；0-无肾损害；1-肾功能不全；2-轻度肾损害；3-中度肾损害；4-重度肾损害</param>
            <returns></returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Transfer.MDC_AddAller(System.String,System.String)">
            <summary>
            传入病人过敏史函数
            </summary>
            <param name="pcPatCode"></param>
            <param name="pcVisitCode"></param>
            <returns></returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Transfer.MDC_AddMedCond(System.String,System.String)">
            <summary>
            传入病人诊断信息函数
            </summary>
            <param name="pcIndex">诊断序号</param>
            <param name="pcDiseaseCode">诊断唯一码</param>
            <param name="pcDiseaseName">诊断名称</param>
            <param name="pcRecipNo">处方号</param>
            <returns></returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Transfer.MDC_AddMedCondIn(System.String,System.String)">
            <summary>
            传入病人入院诊断信息函数 住院
            </summary>
            <param name="pcIndex">诊断序号</param>
            <param name="pcDiseaseCode">诊断唯一码</param>
            <param name="pcDiseaseName">诊断名称</param>
            <param name="pcRecipNo">处方号</param>
            <returns></returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Transfer.MDC_DoSetDrug(System.String,System.String)">
            <summary>
            传入查询药品函数
            </summary>
            <param name="pcDrugUniqueCode">药品唯一码</param>
            <param name="pcDrugName">药品名称</param>
            <returns></returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Transfer.MDC_DoRefDrug(System.Int32)">
            <summary>
            药品信息查询函数
            </summary>
            <param name="piQueryType">11-药品说明书,51-简要信息(浮动窗口)</param>
            <returns></returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Transfer.MDC_AddScreenDrug(System.String,System.Int32,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            传入病人药品记录信息
            </summary>
            <param name="pcIndex">主键</param>
            <param name="piOrderNo">医嘱编号</param>
            <param name="pcDrugUniqueCode">药品代码</param>
            <param name="pcDrugName">药品名称</param>
            <param name="pcDosePerTime">每次使用剂量的数字部分</param>
            <param name="pcDoseUnit">每次服用剂量单位</param>
            <param name="pcFrequency">频次</param>
            <param name="pcRouteCode">给药途径编码</param>
            <param name="pcRouteName">给药途径名称</param>
            <param name="pcStartTime">开立医嘱日期 yyyy-mm-dd hh:mm:ss</param>
            <param name="pcEndTime">停嘱日期 yyyy-mm-dd hh:mm:ss</param>
            <param name="pcExecuteTime">执行医嘱时间 yyyy-mm-dd hh:mm:ss</param>
            <param name="pcGroupTag">成组医嘱标记，如果此参数值相同，则表示是配制在一起用</param>
            <param name="pcIsTempDrug">长期医嘱还是临时医嘱 0长期 1临时。由于临时医嘱不审重复用药，所以门诊临时处方应传’0’，以免漏审重复用药。</param>
            <param name="pcOrderType">医嘱类别，0在用(默认) 1已作废 2已停嘱 3出院带药 4取药医嘱 除了0其它都不参与审核</param>
            <param name="pcDeptCode">开嘱科室编码</param>
            <param name="pcDeptName">开嘱科室名称</param>
            <param name="pcDoctorCode">开嘱医生编码</param>
            <param name="pcDoctorName">开嘱医生姓名</param>
            <param name="pcRecipNo">处方号，门诊处方处方专用，住院传空</param>
            <param name="pcNum">药品开出数量，门诊处方审查专用，住院传空</param>
            <param name="pcNumUnit">药品开出数量单位，门诊处方审查专用，住院传空</param>
            <param name="pcPurpose">用药目的，1可能预防 2可能治疗 3预防 4治疗 5预防+治疗, 默认值为0</param>
            <param name="pcOprCode">手术编号，如果对应多手术，用'，'隔开，表示该药为该编号对应的手术用药</param>
            <param name="pcMediTime">手术用药时机类型 0非手术用药 1术前0.5h以内用药 2术前0.5-2h内 3术前大于2h用药 4术中用药 5术后用药</param>
            <param name="pcRemark">医嘱备注信息</param>
            <returns></returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Transfer.MDC_AddJsonInfo(System.String)">
            <summary>
            调用病人补充信息
            </summary>
            <param name="pcJson"></param>
            <returns></returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Transfer.MDC_DoCheck(System.Int32,System.Int32)">
            <summary>
            审查函数
            </summary>
            <param name="piShowMode"></param>
            <param name="piIsSave"></param>
            <returns></returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Transfer.MDC_GetWarningCode(System.String)">
            <summary>
            获取药品医嘱警示级别
            </summary>
            <param name="pcIndex">正常传主键获取单独药品警示结果，传空的话获取所有药品的最大警示结果</param>
            <returns></returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Transfer.DoCheck(System.Boolean)">
            <summary>
            封装的审核函数
            </summary>
            <returns>0正常, -1非正常</returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Transfer.MDC_Quit">
            <summary>
            PASS退出函数
            </summary>
            <returns>1成功 0失败</returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Transfer.MDC_GetResultDetail(System.String)">
            <summary>
            审查结果详细信息函数
            </summary>
            <param name="pcIndex"></param>
            <returns></returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Transfer.MDC_GetTaskStatus(System.String,System.String,System.String,System.String,System.Int32)">
            <summary>
            获取药师干预系统结果
            </summary>
            <param name="pcPatCode"></param>
            <param name="pcInHospNo"></param>
            <param name="pcVisitCode"></param>
            <param name="pcRecipNo"></param>
            <param name="piTaskType"></param>
            <returns>1通过 0不通过</returns>
        </member>
        <member name="M:PlatPublic.Common.MeiKang_Transfer.MDC_GetTaskDetail(System.String,System.String,System.String,System.String,System.Int32)">
            <summary>
            获取药师干预系统明细
            </summary>
            <param name="pcPatCode"></param>
            <param name="pcInHospNo"></param>
            <param name="pcVisitCode"></param>
            <param name="pcRecipNo"></param>
            <param name="piTaskType"></param>
            <returns>详细json串</returns>
        </member>
    </members>
</doc>
