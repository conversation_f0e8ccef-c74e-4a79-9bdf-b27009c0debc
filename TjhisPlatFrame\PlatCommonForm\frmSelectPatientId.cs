﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using PlatCommon.SysBase;
using NM_Service.NMService;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.WinExplorer;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors;

namespace PlatCommonForm
{
    public partial class frmSelectPatientId : PlatCommon.SysBase.ParentForm
    {
        public frmSelectPatientId()
        {
            InitializeComponent();
        }
        public string gs_patientid { get; set; }
        public string gs_Name { get; set; }
        public string gs_ID_NO { get; set; }
        DataTable dtPmi = new DataTable();

        private void frmSelectPatientId_Load(object sender, EventArgs e)
        {
            string sqlstr = "select * from pat_master_index where id_no = '"+ gs_ID_NO + "'";
            using (ServerPublicClient client = new ServerPublicClient())
            {
                DataSet ds = client.GetDataBySql(sqlstr);
                if (ds != null)
                {
                    dtPmi = ds.Tables[0];
                }
                ds.Dispose();
                gridControl1.DataSource = dtPmi;
            }
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            DataRow row = this.gridView1.GetDataRow(this.gridView1.FocusedRowHandle);
            if (row != null)
            {
                gs_patientid = row["PATIENT_ID"].ToString();
                string tname = row["NAME"].ToString();
                if(!string.IsNullOrEmpty(gs_Name))
                {
                    if(!gs_Name.Equals(tname))
                    {
                        if (DialogResult.No == XtraMessageBox.Show("身份证姓名'" + gs_Name + "'与选择姓名‘"+ tname + "’ 不符，是否继续？", "提示信息", MessageBoxButtons.YesNo, MessageBoxIcon.Question))
                        {
                            return ;
                        }
                    }
                }
                Close();
            }
        }

        private void frmSelectPatientId_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                gridView1_DoubleClick(sender, e);
            }
            if (e.KeyCode == Keys.Escape)
            {
                Close();
            }
        }
    }
}
