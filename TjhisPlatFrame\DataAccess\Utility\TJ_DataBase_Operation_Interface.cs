﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using Oracle.ManagedDataAccess.Client;

namespace Utility
{
    /// <summary>
    /// 数据库操作标准
    /// </summary>
    public interface TJ_DataBase_Operation_Interface
    {
        /// <summary>打开数据库
        /// 打开数据库
        /// </summary>
        /// <returns>true or false</returns>
        bool OpenDB();
         /// <summary>关闭数据库
        /// 关闭数据库
        /// </summary>
        /// <returns>true or false</returns>
        bool CloseDB();
         /// <summary>查询数据
        /// 查询数据
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <param name="Parameters">SQL对应参数列表</param>
        /// <returns>数据读取器</returns>
        OracleDataReader SelectData(String sqlString, List<OracleParameter> Parameters);
          /// <summary>查询数据
        /// 查询数据
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <returns>数据库读取器</returns>
        OracleDataReader SelectData(String sqlString);
          /// <summary>获取数据集
        /// 获取数据集
        /// 查询SQL存在datatable的DisplayExpression属性
        /// 查询SQL存在datatable的ExtendedProperties属性"SQL"关键字对应的值;
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <returns>数据集，默认tablename为“table”</returns>
        DataSet SelectDataSet(String sqlString);
         /// <summary>获取数据集
        /// 获取数据集
        /// 查询SQL存在datatable的DisplayExpression属性
        /// 查询SQL存在datatable的ExtendedProperties属性"SQL"关键字对应的值;
        /// 查询SQL的参数存在datatable的ExtendedProperties属性"Parameters"关键字对应的值 类型为;List<OracleParameter> Parameters
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <param name="Parameters">参数列表</param>
        /// <returns>数据集，默认tablename为“table”</returns>
        DataSet SelectDataSet(String sqlString, List<OracleParameter> Parameters);
          /// <summary>获取数据集
        /// 获取数据集
        /// 查询SQL存在datatable的DisplayExpression属性
        /// 查询SQL存在datatable的ExtendedProperties属性"SQL"关键字对应的值;
        /// </summary>
        /// <param name="sqlStrings">SQL语句数组</param>
        /// <param name="TableNames">表名数组（建议使用全称）</param>
        /// <returns>数据集</returns>
        DataSet SelectDataSet(String[] sqlStrings, String[] TableNames);
         /// <summary>获取数据集
        /// 获取数据集
        /// 查询SQL存在datatable的DisplayExpression属性
        /// 查询SQL存在datatable的ExtendedProperties属性"SQL"关键字对应的值;
        /// 查询SQL的参数存在datatable的ExtendedProperties属性"Parameters"关键字对应的值 类型为;List<OracleParameter> Parameters
        /// </summary>
        /// <param name="sqlStrings">SQL语句数组</param>
        /// <param name="TableNames">表名数组（建议使用全称）</param>
        /// <param name="Parameters_Array">参数列表数组</param>
        /// <returns></returns>
        DataSet SelectDataSet(String[] sqlStrings, String[] TableNames, List<OracleParameter>[] Parameters_Array);
         /// <summary>执行一个事务 
        /// 执行一个事务 默认共享锁 IsolationLevel.ReadCommitted
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <returns>执行事务影响行数</returns>
        int ExecuteATransaction(String sqlString);
          /// <summary>执行一个事物
        /// 执行一个事物
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <param name="Parameters">参数列表</param>
        /// <returns>执行事务影响行数</returns>
        int ExecuteATransaction(String sqlString, List<OracleParameter> Parameters);
          /// <summary>执行一个事务
        /// 执行一个事务
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <param name="_IsolationLevel">数据库锁级别</param>
        /// <returns>执行事务影响行数</returns>
        int ExecuteATransaction(String sqlString, IsolationLevel _IsolationLevel);
          /// <summary>执行一个事务
        /// 执行一个事务
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <param name="Parameters">参数列表</param>
        /// <param name="_IsolationLevel">数据库锁类型</param>
        /// <returns>执行事务影响行数</returns>
        int ExecuteATransaction(String sqlString, List<OracleParameter> Parameters, IsolationLevel _IsolationLevel);
         /// <summary>开始事务，初始化事务
        /// 开始事务，初始化事务 默认锁类型：IsolationLevel.ReadCommitted
        /// </summary>
         /// <returns>true or false</returns>
        bool BeginTransaction();
         /// <summary>开始事务，初始化事务
        /// 开始事务，初始化事务
        /// </summary>
        /// <param name="_IsolationLevel">数据库事务锁类型</param>
        /// <returns>true or false</returns>
        bool BeginTransaction(IsolationLevel _IsolationLevel);
         /// <summary>提交事务
        /// 提交事务
        /// </summary>
        /// <returns>true or false</returns>
        bool CommitTransaction();
          /// <summary>回滚事务
        /// 回滚事务
        /// </summary>
        /// <returns>true or false</returns>
        bool RollbackTransaction();
         /// <summary> 执行事务
        /// 执行事务
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <returns>true or false</returns>
        bool ExecuteTransaction(String sqlString);
         /// <summary>执行事务
        /// 执行事务
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <param name="Parameters">参数列表</param>
        /// <returns>true or false</returns>
        bool ExecuteTransaction(String sqlString, List<OracleParameter> Parameters);
          /// <summary>数据更新
        /// 数据更新
        /// 优先提取ExtendedProperties属性中的SQL与Parameters键
        /// 然后是DisplayExpression，此时已默认为字符串SQL,不会再添加数据库参数
        /// 最后是= "select *from " + dt.TableName;
        /// </summary>
        /// <param name="dt">数据表入参DataTable，必须符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在，以及tablename是否存在数据库查询上的意义</param>
        /// <returns>成功更新的行数</returns>
        int UpData_A_Data(DataTable dt);
         /// <summary>数据更新
        /// 数据更新
        /// </summary>
        /// <param name="dt">数据表</param>
        /// <param name="sqlString">SQL语句</param>
        /// <returns>成功更新的行数</returns>
        int UpData_A_Data(DataTable dt, String sqlString);
         /// <summary>更新数据
        /// 更新数据 
        /// 优先提取ExtendedProperties属性中的SQL与Parameters键
        /// 然后是DisplayExpression，此时已默认为字符串SQL,不会再添加数据库参数
        /// 最后是= "select *from " + dt.TableName;
        /// </summary>
        /// <param name="ds">数据集包含的DataTable，必须符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在，以及tablename是否存在数据库查询上的意义</param>
        /// <returns>成功更新的行数</returns>
        int UpData_A_Data(DataSet ds);
    }
}
