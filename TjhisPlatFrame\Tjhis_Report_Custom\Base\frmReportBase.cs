﻿using DevExpress.Utils.Menu;
using DevExpress.XtraEditors;
using DevExpress.XtraPrinting;
using DevExpress.XtraPrinting.Preview;
using DevExpress.XtraReports.UI;
using DevExpress.XtraReports.UserDesigner;
using PlatCommon.Base02;
using PlatCommon.Comm;
using PlatCommon.Common;
using PlatCommon.SysBase;
using System;
using System.Collections;
using System.Data;
using System.IO;
using System.Windows.Forms;
using Tjhis.Report.Custom.Common;

namespace Tjhis.Report.Custom.Base
{
    public partial class frmReportBase : ParentForm
    {
        #region 变量定义
        protected string TempleteFile = string.Empty;                                   // 模板名称   
        protected string TempleteSql = string.Empty;                                    //sql字符串
        protected DocumentViewer DocViewer = null;

        protected TreeView TrvNavigator = null;                                         // 树型导行
        protected DataSet dsTree = null;                                                // 树数据集

        protected DropDownButton BtnExport = null;

        public XtraReport mReport = null;
        public string deptCode = string.Empty;
        public bool IsShowFunctionButton { get; set; } = true;                       // 窗口是否显示工功能按钮，用于预览和正式打印
        #endregion


        public frmReportBase()
        {
            InitializeComponent();
        }


        /// <summary>
        /// 窗体加载
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void ReportBaseFrm_Load(object sender, EventArgs e)
        {
            if (System.ComponentModel.LicenseManager.UsageMode != System.ComponentModel.LicenseUsageMode.Designtime)
            {
                timer1.Enabled = true;
            }
        }


        /// <summary>
        /// 时钟事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void timer1_Tick(object sender, EventArgs e)
        {
            try
            {
                timer1.Enabled = false;

                if (DocViewer != null)
                {
                    DocViewer.ShowPageMargins = false;
                }
                // 加载模板
                //mReport = new XtraReport();
                //XtraReportHelper.LoadTemplet(ref DocViewer, ref mReport, TempleteFile);

                // 加载树
                if (TrvNavigator != null)
                {
                    RefreshTree();
                    TrvNavigator.AfterSelect += trv_AfterSelect;
                }

                // 绑定事件
                if (BtnExport != null)
                {
                    BtnExport.DropDownControl = CreateExportPopupMenu();
                    BtnExport.Click += btnExport_Click;
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }


        /// <summary>
        /// 树节点选择事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void trv_AfterSelect(object sender, TreeViewEventArgs e)
        {
            try
            {
                if (TrvNavigator.SelectedNode == null) return;

                //判断护理记录类别
                switch (TrvNavigator.SelectedNode.Text)   //判断护理记录单类型设置模板名称 - swl
                {
                    case "重症监护记录单":
                        TempleteFile = "重症监护记录单";
                        break;
                    case "新生儿重症监护记录单":
                        TempleteFile = "新生儿重症监护记录单";
                        break;
                    case "护理记录单":
                        TempleteFile = "护理记录单";
                        break;
                    case "产科护理记录单":
                        TempleteFile = "产科护理记录单";
                        break;
                    case "产后记录":
                        TempleteFile = "产后记录";
                        break;
                    case "母乳喂养及护理记录单":
                        TempleteFile = "母乳喂养及护理记录单";
                        break;
                    case "儿科护理记录单":
                        TempleteFile = "儿科护理记录单";
                        break;
                    case "产时护理记录单":
                        TempleteFile = "产时护理记录单";
                        break;
                    default:
                        break;
                }
                btnSearch_Click(null, null);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }


        /// <summary>
        /// 查询数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                this.Cursor = Cursors.WaitCursor;
                XtraReportHelper.LoadTemplet(ref DocViewer, ref mReport, TempleteFile, Const.customAppCode);
                mReport.DataSource = XtraReportHelper.GetPrintData(TempleteFile, getParams(), Const.customAppCode);
                mReport.CreateDocument();
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }
        protected void btnSearch_Sql_Click(object sender, EventArgs e)
        {
            try
            {
                if(mReport != null)
                {
                    mReport.Dispose();
                    mReport = null;
                }
                this.Cursor = Cursors.WaitCursor;
                XtraReportHelper.LoadTemplet(ref DocViewer, ref mReport, TempleteFile, Const.customAppCode);
                mReport.DataSource = XtraReportHelper.GetPrintData(TempleteSql, getParams(), Const.customAppCode, true);
                mReport.CreateDocument();
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                System.GC.Collect();
            }
        }

        
        /// <summary>
        /// 按钮[打印]
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                mReport.PrinterName = PrinterConfigFrm.GetPrinterName(TempleteFile);
                mReport.PrintDialog();//.ShowPageSetupDialog();
                //mReport.Print();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }


        /// <summary>
        /// 按钮[导出]
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                ExportFile();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }


        protected void btnExport_Xls_Click(object sender, EventArgs e)
        {
            try
            {
                SaveFileDialog sfd = new SaveFileDialog();
                //sfd.Filter = "Excel（*.xlsx）|*.xlsx |Excel 97-2003（*.xls）|*.xls"; // 设定文件类型
                sfd.Filter = "Excel（*.xlsx）|*.xlsx"; // 设定文件类型
                sfd.FilterIndex = 1;
                sfd.RestoreDirectory = true;
                sfd.DefaultExt = "xlsx";
                string defFileName = TempleteFile + "_" + DateTime.Now.ToString("yyyyMMddHHmmss");
                sfd.FileName = defFileName;
                
                
                if (sfd.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                {
                    return;
                }

                // 导出文件                    
                if (sfd.FileName.ToLower().Trim().EndsWith(".xlsx"))
                {
                    XlsxExportOptions xlsxOptions = mReport.ExportOptions.Xlsx;

                    xlsxOptions.ShowGridLines = false;
                    //xlsxOptions.TextExportMode = TextExportMode.Value;

                    mReport.ExportToXlsx(sfd.FileName);
                }
                else
                {
                    XlsExportOptions xlsOptions = mReport.ExportOptions.Xls;
                    xlsOptions.ShowGridLines = false;

                    mReport.ExportToXls(sfd.FileName);
                }

                XtraMessageBox.Show("导出成功!");
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }


        protected void btnExport_Pdf_Click(object sender, EventArgs e)
        {
            try
            {
                if(mReport.DataSource == null)
                {
                    XtraMessageBox.Show("没有数据可导出！","提示");
                    return;
                }
                SaveFileDialog sfd = new SaveFileDialog();
                sfd.Filter = "PDF文件（*.pdf）|*.pdf ";  // 设置文件类型
                sfd.FilterIndex = 1;
                sfd.RestoreDirectory = true;
                sfd.DefaultExt = "pdf";
                string defFileName = TempleteFile + "_" + DateTime.Now.ToString("yyyyMMddHHmmss");
                sfd.FileName = defFileName;
                
                
                if (sfd.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                {
                    return;
                }

                // 导出文修的
                PdfExportOptions pdfOptions = mReport.ExportOptions.Pdf;

                // Set PDF-specific export options.
                pdfOptions.Compressed = true;
                pdfOptions.ImageQuality = PdfJpegImageQuality.Low;
                //pdfOptions.NeverEmbeddedFonts = "Tahoma;Courier New";
                //pdfOptions.DocumentOptions.Application = "Test Application";
                //pdfOptions.DocumentOptions.Author = "DX Documentation Team";
                //pdfOptions.DocumentOptions.Keywords = "XtraReports, XtraPrinting";
                //pdfOptions.DocumentOptions.Subject = "Test Subject";
                pdfOptions.DocumentOptions.Title = TempleteFile;

                // Export the report to PDF.
                mReport.ExportToPdf(sfd.FileName);
                  
                XtraMessageBox.Show("导出成功!");
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }


        private void OnExportItem_Click(object sender, EventArgs e)
        {
            try
            {
                bool blnExportDone = false;

                DXMenuItem item = sender as DXMenuItem;

                SaveFileDialog sfd = new SaveFileDialog();
                sfd.FilterIndex = 1;
                sfd.RestoreDirectory = true;

                // 导出Excel
                if (item.Caption.Contains("XLS"))
                {
                    // 获取文件名
                    sfd.Filter = "Excel（*.xlsx）|*.xlsx |Excel 97-2003（*.xls）|*.xls";
                    if (sfd.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                    {
                        return;
                    }

                    // 导出文件                    
                    if (sfd.FileName.ToLower().Trim().EndsWith(".xlsx"))
                    {
                        XlsxExportOptions xlsxOptions = mReport.ExportOptions.Xlsx;

                        xlsxOptions.ShowGridLines = false;
                        //xlsxOptions.TextExportMode = TextExportMode.Value;

                        mReport.ExportToXlsx(sfd.FileName);
                    }
                    else
                    {
                        XlsExportOptions xlsOptions = mReport.ExportOptions.Xls;
                        xlsOptions.ShowGridLines = false;

                        mReport.ExportToXls(sfd.FileName);
                    }

                    blnExportDone = true;
                }

                // 导出PDF
                if (item.Caption.Contains("PDF"))
                {
                    // 获取文件名
                    sfd.Filter = "PDF文件（*.pdf）|*.pdf ";
                    if (sfd.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                    {
                        return;
                    }

                    // 导出文修的
                    PdfExportOptions pdfOptions = mReport.ExportOptions.Pdf;

                    // Set PDF-specific export options.
                    pdfOptions.Compressed = true;
                    pdfOptions.ImageQuality = PdfJpegImageQuality.Low;
                    //pdfOptions.NeverEmbeddedFonts = "Tahoma;Courier New";
                    //pdfOptions.DocumentOptions.Application = "Test Application";
                    //pdfOptions.DocumentOptions.Author = "DX Documentation Team";
                    //pdfOptions.DocumentOptions.Keywords = "XtraReports, XtraPrinting";
                    //pdfOptions.DocumentOptions.Subject = "Test Subject";
                    pdfOptions.DocumentOptions.Title = TempleteFile;
                    
                    // Export the report to PDF.
                    mReport.ExportToPdf(sfd.FileName);
                    blnExportDone = true;
                }

                // 导出Word
                if (item.Caption.Contains("Word"))
                {
                    //// 获取文件名
                    //sfd.Filter = "Word（*.docx）|*.docx |Word 97-2003（*.doc）|*.doc";
                    //if (sfd.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                    //{
                    //    return;
                    //}

                    //// 导出文修的
                    //mReport.ExportToRtf(sfd.FileName);
                    //blnExportDone = true;
                }

                if (blnExportDone)
                {
                    XtraMessageBox.Show("导出成功!");
                }
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }


        /// <summary>
        /// 按钮[报表设计]
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void btnReportDesign_Click(object sender, EventArgs e)
        {
            try
            {
                // 进行查询
                //btnSearch_Click(sender, e);

                // 把数据集保存到Schema子目录中
                XtraReportHelper.WriteSchemaData(TempleteFile, ref mReport, getParams(), Const.customAppCode);
                //XtraMessageBox.Show(TempleteFile + "数据集保存成功!");

                // 打开模板设计器
                XRDesignFormEx frm = new XRDesignFormEx();
                frm.FileName = XtraReportHelper.GetTempleteFileNameFull(TempleteFile, Const.customAppCode);

                if (File.Exists(frm.FileName))
                {
                    frm.OpenReport(frm.FileName);
                }

                frm.ShowDialog();

                // 重新加载模板
                this.Cursor = Cursors.WaitCursor;
                XtraReportHelper.LoadTemplet(ref DocViewer, ref mReport, TempleteFile, Const.customAppCode);
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }
        protected void btnReportDesign_Sql_Click(object sender, EventArgs e)
        {
            try
            {
                // 进行查询
                //btnSearch_Click(sender, e);

                // 把数据集保存到Schema子目录中
                XtraReportHelper.WriteSchemaData(TempleteFile, TempleteSql, ref mReport, getParams(), Const.customAppCode);
                //XtraMessageBox.Show(TempleteFile + "数据集保存成功!");

                // 打开模板设计器
                XRDesignFormEx frm = new XRDesignFormEx();
                frm.FileName = XtraReportHelper.GetTempleteFileNameFull(TempleteFile, Const.customAppCode);

                if (File.Exists(frm.FileName))
                {
                    frm.OpenReport(frm.FileName);
                }

                frm.ShowDialog();
                frm.Dispose();

                // 重新加载模板
                this.Cursor = Cursors.WaitCursor;
                XtraReportHelper.LoadTemplet(ref DocViewer, ref mReport, TempleteFile, Const.customAppCode);
                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }


        /// <summary>
        /// 窗体按F12时,导出数据到本地文件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void ReportSampleFrm_KeyUp(object sender, KeyEventArgs e)
        {
            try
            {
                if (e.Control == false) return;

                this.Cursor = Cursors.WaitCursor;

                // 把数据集保存到Schema子目录中
                if (e.KeyCode == Keys.F12)
                {
                    XtraReportHelper.WriteSchemaData(TempleteFile, ref mReport, getParams(), Const.customAppCode);

                    //XtraMessageBox.Show(TempleteFile + "数据集保存成功!");
                }

                // 打开模板设计器
                if (e.KeyCode == Keys.F10)
                {
                    XRDesignFormEx frm = new XRDesignFormEx();
                    frm.FileName = XtraReportHelper.GetTempleteFileNameFull(TempleteFile, Const.customAppCode);

                    if (File.Exists(frm.FileName))
                    {
                        frm.OpenReport(frm.FileName);
                    }

                    frm.ShowDialog();
                }

                // 重新加载模板
                if (e.KeyCode == Keys.F5)
                {
                    XtraReportHelper.LoadTemplet(ref DocViewer, ref mReport, TempleteFile, Const.customAppCode);
                }
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }


        /// <summary>
        /// 按钮[关闭]
        /// </summary>
        protected void btnClose_Click(object sender, EventArgs e)
        {
            try
            {
                this.Close();      
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }


        /// <summary>
        /// 显示比例
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void seZoom_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                SpinEdit seZoom = sender as SpinEdit;
                if (seZoom == null) return;

                this.DocViewer.Zoom = (float)(seZoom.Value / 100);
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }


        #region 共通函数
        /// <summary>
        /// 获取参数
        /// </summary>
        /// <returns></returns>
        protected virtual Hashtable getParams()
        {
            return null;
        }


        /// <summary>
        /// 获取基础参数
        /// </summary>
        /// <returns></returns>
        protected Hashtable getBaseParams()
        {
            Hashtable hasParam = new Hashtable();

            bool blnHasValue = false;

            if (TrvNavigator != null && TrvNavigator.SelectedNode != null && TrvNavigator.SelectedNode.Tag != null)
            {
                string filter = "NODE_TAG = " + Cs02StringHelper.SqlConvert(TrvNavigator.SelectedNode.Tag.ToString());
                DataRow[] drFind = dsTree.Tables[0].Select(filter);

                if (drFind.Length > 0)
                {
                    DataRow dr = drFind[0];

                    hasParam.Add("NODE_CODE", dr["NODE_CODE"].ToString());
                    hasParam.Add("NODE_CODE_PARENT", dr["NODE_CODE_PARENT"].ToString());
                    hasParam.Add("NODE_TITLE", dr["NODE_TITLE"].ToString());
                    hasParam.Add("NODE_TAG", dr["NODE_TAG"].ToString());

                    AddHashParam(ref dr, ref hasParam);

                    blnHasValue = true;
                }
            }

            if (blnHasValue == false)
            {
                hasParam.Add("NODE_CODE", "");
                hasParam.Add("NODE_CODE_PARENT", "");
                hasParam.Add("NODE_TITLE", "");
                hasParam.Add("NODE_TAG", "");

                if (dsTree != null)
                {
                    AddHashParam(dsTree.Tables[0], ref hasParam);
                }
            }

            return hasParam;
        }


        private void AddHashParam(ref DataRow dr, ref Hashtable hasParam)
        {
            if (dr == null) return;

            bool blnExit = false;

            foreach (DataColumn dc in dr.Table.Columns)
            {
                blnExit = false;
                foreach (DictionaryEntry de in hasParam)
                {
                    if (de.Key.ToString().ToUpper().Trim().Equals(dc.ColumnName.Trim().ToUpper()))
                    {
                        blnExit = true;
                        break;
                    }
                }

                if (blnExit) continue;

                hasParam.Add(dc.ColumnName, dr[dc.ColumnName]);
            }
        }


        private void AddHashParam(DataTable dataTable, ref Hashtable hasParam)
        {
            if (dataTable == null) return;

            bool blnExit = false;

            foreach (DataColumn dc in dataTable.Columns)
            {
                blnExit = false;
                foreach (DictionaryEntry de in hasParam)
                {
                    if (de.Key.ToString().ToUpper().Trim().Equals(dc.ColumnName.Trim().ToUpper()))
                    {
                        blnExit = true;
                        break;
                    }
                }

                if (blnExit) continue;

                hasParam.Add(dc.ColumnName, "");
            }
        }

        private DXPopupMenu CreateExportPopupMenu()
        {
            DXPopupMenu menu = new DXPopupMenu();
            menu.Items.Add(new DXMenuItem("XLS File", OnExportItem_Click));
            //menu.Items.Add(new DXMenuItem("Word File", OnItemClick));
            menu.Items.Add(new DXMenuItem("PDF File", OnExportItem_Click));
            
            return menu;
        }

        
        private void ExportFile()
        {
            try
            {

                SaveFileDialog sfd = new SaveFileDialog();
                sfd.FilterIndex = 1;
                sfd.RestoreDirectory = true;

                // 导出Excel
                if (true)
                {
                    // 获取文件名
                    sfd.Filter = "Excel（*.xlsx）|*.xlsx |Excel 97-2003（*.xls）|*.xls";
                    if (sfd.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                    {
                        return;
                    }

                    // 导出文修的
                    //XlsExportOptions xlsOptions = mReport.ExportOptions.Xls;

                    //xlsOptions.ShowGridLines = false;
                    //xlsOptions.TextExportMode = TextExportMode.Value;

                    if (sfd.DefaultExt.Contains("xlsx"))
                    {
                        mReport.ExportToXlsx(sfd.FileName);
                    }
                    else
                    {
                        mReport.ExportToXls(sfd.FileName);
                    }

                    return;
                }
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }


        /// <summary>
        /// 加载数
        /// </summary>
        public void RefreshTree()
        { 
            // 获取SQL语句
            if (TrvNavigator == null) return;

            // 获取数据
            dsTree = XtraReportHelper.GetData_Tree(TempleteFile, getParams(), Const.customAppCode);

            // 加载树
            TreeViewHelper.LoadTreeNodes(ref TrvNavigator, dsTree, "NODE_CODE", "NODE_CODE_PARENT", "NODE_TITLE", "NODE_TAG", "COL_ORDER", "", "");

            if (TrvNavigator.Nodes.Count > 0)
            {
                TrvNavigator.Nodes[0].ExpandAll();
            }
        }
        #endregion
    }
}
