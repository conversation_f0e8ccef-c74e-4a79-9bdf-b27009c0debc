﻿namespace PlatCommonForm
{
    partial class frmPrepQueryPat
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmPrepQueryPat));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.cb_refresh = new DevExpress.XtraBars.BarLargeButtonItem();
            this.cb_save = new DevExpress.XtraBars.BarLargeButtonItem();
            this.cb_commit = new DevExpress.XtraBars.BarLargeButtonItem();
            this.cb_copy = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barLargeButtonItem3 = new DevExpress.XtraBars.BarLargeButtonItem();
            this.cb_close = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.cb_childorder = new DevExpress.XtraBars.BarLargeButtonItem();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.layoutControl1 = new DevExpress.XtraLayout.LayoutControl();
            this.textEdit18 = new DevExpress.XtraEditors.TextEdit();
            this.comboBoxEdit1 = new DevExpress.XtraEditors.ComboBoxEdit();
            this.textEdit16 = new DevExpress.XtraEditors.TextEdit();
            this.searchLookUpEdit3 = new DevExpress.XtraEditors.SearchLookUpEdit();
            this.textEdit10 = new DevExpress.XtraEditors.TextEdit();
            this.textEdit9 = new DevExpress.XtraEditors.TextEdit();
            this.textEdit6 = new DevExpress.XtraEditors.TextEdit();
            this.searchLookUpEdit2 = new DevExpress.XtraEditors.SearchLookUpEdit();
            this.textEdit2 = new DevExpress.XtraEditors.TextEdit();
            this.textEdit5 = new DevExpress.XtraEditors.TextEdit();
            this.textEdit8 = new DevExpress.XtraEditors.TextEdit();
            this.textEdit11 = new DevExpress.XtraEditors.TextEdit();
            this.textEdit14 = new DevExpress.XtraEditors.TextEdit();
            this.searchLookUpEdit1 = new DevExpress.XtraEditors.SearchLookUpEdit();
            this.textEdit7 = new DevExpress.XtraEditors.TextEdit();
            this.comboBoxEdit2 = new DevExpress.XtraEditors.ComboBoxEdit();
            this.textEdit1 = new DevExpress.XtraEditors.TextEdit();
            this.layoutControlItem2 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem7 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem8 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem10 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem14 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem15 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem13 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem17 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem9 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem19 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem3 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem4 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem16 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem5 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem6 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem11 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem18 = new DevExpress.XtraLayout.LayoutControlItem();
            this.searchLookUpEdit3View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.UNIT_CODE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.UNIT_NAME = new DevExpress.XtraGrid.Columns.GridColumn();
            this.INPUT_CODE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.searchLookUpEdit2View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.OUT_BZSBM = new DevExpress.XtraGrid.Columns.GridColumn();
            this.OUT_BZMC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.OUT_PJZJM1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.searchLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.OUT_JBBM = new DevExpress.XtraGrid.Columns.GridColumn();
            this.OUT_JBMC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.OUT_PJZJM = new DevExpress.XtraGrid.Columns.GridColumn();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.layoutControl2 = new DevExpress.XtraLayout.LayoutControl();
            this.txtName = new DevExpress.XtraEditors.TextEdit();
            this.txtInTime = new DevExpress.XtraEditors.TextEdit();
            this.txtInDept = new DevExpress.XtraEditors.TextEdit();
            this.txtSex = new DevExpress.XtraEditors.TextEdit();
            this.txtPatientid = new DevExpress.XtraEditors.TextEdit();
            this.txtInpNo = new DevExpress.XtraEditors.TextEdit();
            this.txtVisitID = new DevExpress.XtraEditors.TextEdit();
            this.txtChargeType = new DevExpress.XtraEditors.TextEdit();
            this.layoutControlItem12 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem20 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem21 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem22 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem23 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem24 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem25 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem27 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem26 = new DevExpress.XtraLayout.LayoutControlItem();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.gcClinic = new DevExpress.XtraGrid.GridControl();
            this.gvRcpt = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcRCPT_NO = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcTRANSACT_DATE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcAMOUNT = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcPAY_WAY = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcPATIENT_ID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcTRANSACT_TYPE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcBANK = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcCHECK_NO = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcBANK_AUOUNT_NO = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcOPERATOR_NO = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcREFUNDED_RCPT_NO = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcUSED_FLAG = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcBANK_TRANS_DATE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.txtsum = new DevExpress.XtraEditors.TextEdit();
            this.txtyue = new DevExpress.XtraEditors.TextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).BeginInit();
            this.layoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit18.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit16.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.searchLookUpEdit3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit10.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit9.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit6.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.searchLookUpEdit2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit5.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit8.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit11.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit14.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.searchLookUpEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit7.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem18)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.searchLookUpEdit3View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.searchLookUpEdit2View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.searchLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl2)).BeginInit();
            this.layoutControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInTime.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInDept.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSex.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPatientid.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInpNo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVisitID.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtChargeType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem21)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem22)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem23)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem24)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem25)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem27)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem26)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcClinic)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvRcpt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtsum.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtyue.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar2});
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.cb_childorder,
            this.cb_refresh,
            this.cb_copy,
            this.cb_commit,
            this.cb_save,
            this.cb_close,
            this.barLargeButtonItem3});
            this.barManager1.MainMenu = this.bar2;
            this.barManager1.MaxItemId = 18;
            // 
            // bar2
            // 
            this.bar2.BarName = "Main menu";
            this.bar2.DockCol = 0;
            this.bar2.DockRow = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar2.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.cb_refresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.cb_save),
            new DevExpress.XtraBars.LinkPersistInfo(this.cb_commit),
            new DevExpress.XtraBars.LinkPersistInfo(this.cb_copy),
            new DevExpress.XtraBars.LinkPersistInfo(this.barLargeButtonItem3),
            new DevExpress.XtraBars.LinkPersistInfo(this.cb_close)});
            this.bar2.OptionsBar.AllowQuickCustomization = false;
            this.bar2.OptionsBar.DrawBorder = false;
            this.bar2.OptionsBar.MultiLine = true;
            this.bar2.OptionsBar.UseWholeRow = true;
            this.bar2.Text = "Main menu";
            // 
            // cb_refresh
            // 
            this.cb_refresh.Caption = "提取";
            this.cb_refresh.Glyph = ((System.Drawing.Image)(resources.GetObject("cb_refresh.Glyph")));
            this.cb_refresh.Id = 5;
            this.cb_refresh.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("cb_refresh.LargeGlyph")));
            this.cb_refresh.Name = "cb_refresh";
            this.cb_refresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.cb_refresh_ItemClick);
            // 
            // cb_save
            // 
            this.cb_save.Caption = "打印";
            this.cb_save.Glyph = ((System.Drawing.Image)(resources.GetObject("cb_save.Glyph")));
            this.cb_save.Id = 10;
            this.cb_save.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("cb_save.LargeGlyph")));
            this.cb_save.Name = "cb_save";
            this.cb_save.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.cb_save_ItemClick);
            // 
            // cb_commit
            // 
            this.cb_commit.Caption = "身份证";
            this.cb_commit.Glyph = ((System.Drawing.Image)(resources.GetObject("cb_commit.Glyph")));
            this.cb_commit.Id = 9;
            this.cb_commit.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("cb_commit.LargeGlyph")));
            this.cb_commit.Name = "cb_commit";
            // 
            // cb_copy
            // 
            this.cb_copy.Caption = "清屏";
            this.cb_copy.Glyph = ((System.Drawing.Image)(resources.GetObject("cb_copy.Glyph")));
            this.cb_copy.Id = 6;
            this.cb_copy.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("cb_copy.LargeGlyph")));
            this.cb_copy.Name = "cb_copy";
            this.cb_copy.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.cb_copy_ItemClick);
            // 
            // barLargeButtonItem3
            // 
            this.barLargeButtonItem3.Caption = "上次住院";
            this.barLargeButtonItem3.Glyph = ((System.Drawing.Image)(resources.GetObject("barLargeButtonItem3.Glyph")));
            this.barLargeButtonItem3.Id = 17;
            this.barLargeButtonItem3.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barLargeButtonItem3.LargeGlyph")));
            this.barLargeButtonItem3.Name = "barLargeButtonItem3";
            this.barLargeButtonItem3.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barLargeButtonItem3.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barLargeButtonItem3_ItemClick);
            // 
            // cb_close
            // 
            this.cb_close.Caption = "关闭";
            this.cb_close.Glyph = ((System.Drawing.Image)(resources.GetObject("cb_close.Glyph")));
            this.cb_close.Id = 11;
            this.cb_close.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("cb_close.LargeGlyph")));
            this.cb_close.Name = "cb_close";
            this.cb_close.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.cb_close_ItemClick);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Margin = new System.Windows.Forms.Padding(3, 5, 3, 5);
            this.barDockControlTop.Size = new System.Drawing.Size(1046, 57);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 546);
            this.barDockControlBottom.Margin = new System.Windows.Forms.Padding(3, 5, 3, 5);
            this.barDockControlBottom.Size = new System.Drawing.Size(1046, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 57);
            this.barDockControlLeft.Margin = new System.Windows.Forms.Padding(3, 5, 3, 5);
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 489);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1046, 57);
            this.barDockControlRight.Margin = new System.Windows.Forms.Padding(3, 5, 3, 5);
            this.barDockControlRight.Size = new System.Drawing.Size(0, 489);
            // 
            // cb_childorder
            // 
            this.cb_childorder.Caption = "子医嘱";
            this.cb_childorder.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.cb_childorder.Glyph = ((System.Drawing.Image)(resources.GetObject("cb_childorder.Glyph")));
            this.cb_childorder.Id = 3;
            this.cb_childorder.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("cb_childorder.LargeGlyph")));
            this.cb_childorder.Name = "cb_childorder";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.layoutControl1);
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(200, 100);
            this.groupControl1.TabIndex = 0;
            // 
            // layoutControl1
            // 
            this.layoutControl1.Controls.Add(this.textEdit18);
            this.layoutControl1.Controls.Add(this.comboBoxEdit1);
            this.layoutControl1.Controls.Add(this.textEdit16);
            this.layoutControl1.Controls.Add(this.searchLookUpEdit3);
            this.layoutControl1.Controls.Add(this.textEdit10);
            this.layoutControl1.Controls.Add(this.textEdit9);
            this.layoutControl1.Controls.Add(this.textEdit6);
            this.layoutControl1.Controls.Add(this.searchLookUpEdit2);
            this.layoutControl1.Controls.Add(this.textEdit2);
            this.layoutControl1.Controls.Add(this.textEdit5);
            this.layoutControl1.Controls.Add(this.textEdit8);
            this.layoutControl1.Controls.Add(this.textEdit11);
            this.layoutControl1.Controls.Add(this.textEdit14);
            this.layoutControl1.Controls.Add(this.searchLookUpEdit1);
            this.layoutControl1.Controls.Add(this.textEdit7);
            this.layoutControl1.Controls.Add(this.comboBoxEdit2);
            this.layoutControl1.Controls.Add(this.textEdit1);
            this.layoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl1.HiddenItems.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem2});
            this.layoutControl1.Location = new System.Drawing.Point(2, 21);
            this.layoutControl1.Name = "layoutControl1";
            this.layoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(686, 48, 873, 697);
            this.layoutControl1.Root = this.layoutControlGroup1;
            this.layoutControl1.Size = new System.Drawing.Size(196, 77);
            this.layoutControl1.TabIndex = 1;
            this.layoutControl1.Text = "layoutControl1";
            // 
            // textEdit18
            // 
            this.textEdit18.Location = new System.Drawing.Point(170, 108);
            this.textEdit18.Name = "textEdit18";
            this.textEdit18.Size = new System.Drawing.Size(262, 20);
            this.textEdit18.StyleController = this.layoutControl1;
            this.textEdit18.TabIndex = 4;
            // 
            // comboBoxEdit1
            // 
            this.comboBoxEdit1.Location = new System.Drawing.Point(382, 84);
            this.comboBoxEdit1.Name = "comboBoxEdit1";
            this.comboBoxEdit1.Size = new System.Drawing.Size(50, 20);
            this.comboBoxEdit1.StyleController = this.layoutControl1;
            this.comboBoxEdit1.TabIndex = 5;
            // 
            // textEdit16
            // 
            this.textEdit16.Location = new System.Drawing.Point(64, 108);
            this.textEdit16.Name = "textEdit16";
            this.textEdit16.Size = new System.Drawing.Size(50, 20);
            this.textEdit16.StyleController = this.layoutControl1;
            this.textEdit16.TabIndex = 6;
            // 
            // searchLookUpEdit3
            // 
            this.searchLookUpEdit3.Location = new System.Drawing.Point(382, 60);
            this.searchLookUpEdit3.Name = "searchLookUpEdit3";
            this.searchLookUpEdit3.Size = new System.Drawing.Size(50, 20);
            this.searchLookUpEdit3.StyleController = this.layoutControl1;
            this.searchLookUpEdit3.TabIndex = 7;
            // 
            // textEdit10
            // 
            this.textEdit10.Location = new System.Drawing.Point(370, 36);
            this.textEdit10.Name = "textEdit10";
            this.textEdit10.Size = new System.Drawing.Size(62, 20);
            this.textEdit10.StyleController = this.layoutControl1;
            this.textEdit10.TabIndex = 8;
            // 
            // textEdit9
            // 
            this.textEdit9.Location = new System.Drawing.Point(259, 36);
            this.textEdit9.Name = "textEdit9";
            this.textEdit9.Size = new System.Drawing.Size(55, 20);
            this.textEdit9.StyleController = this.layoutControl1;
            this.textEdit9.TabIndex = 9;
            // 
            // textEdit6
            // 
            this.textEdit6.Location = new System.Drawing.Point(64, 36);
            this.textEdit6.Name = "textEdit6";
            this.textEdit6.Size = new System.Drawing.Size(139, 20);
            this.textEdit6.StyleController = this.layoutControl1;
            this.textEdit6.TabIndex = 10;
            // 
            // searchLookUpEdit2
            // 
            this.searchLookUpEdit2.Location = new System.Drawing.Point(276, 84);
            this.searchLookUpEdit2.Name = "searchLookUpEdit2";
            this.searchLookUpEdit2.Size = new System.Drawing.Size(50, 20);
            this.searchLookUpEdit2.StyleController = this.layoutControl1;
            this.searchLookUpEdit2.TabIndex = 11;
            // 
            // textEdit2
            // 
            this.textEdit2.Location = new System.Drawing.Point(64, 12);
            this.textEdit2.Name = "textEdit2";
            this.textEdit2.Size = new System.Drawing.Size(50, 20);
            this.textEdit2.StyleController = this.layoutControl1;
            this.textEdit2.TabIndex = 12;
            // 
            // textEdit5
            // 
            this.textEdit5.Location = new System.Drawing.Point(170, 12);
            this.textEdit5.Name = "textEdit5";
            this.textEdit5.Size = new System.Drawing.Size(50, 20);
            this.textEdit5.StyleController = this.layoutControl1;
            this.textEdit5.TabIndex = 13;
            // 
            // textEdit8
            // 
            this.textEdit8.Location = new System.Drawing.Point(276, 12);
            this.textEdit8.Name = "textEdit8";
            this.textEdit8.Size = new System.Drawing.Size(50, 20);
            this.textEdit8.StyleController = this.layoutControl1;
            this.textEdit8.TabIndex = 14;
            // 
            // textEdit11
            // 
            this.textEdit11.Location = new System.Drawing.Point(382, 12);
            this.textEdit11.Name = "textEdit11";
            this.textEdit11.Size = new System.Drawing.Size(50, 20);
            this.textEdit11.StyleController = this.layoutControl1;
            this.textEdit11.TabIndex = 15;
            // 
            // textEdit14
            // 
            this.textEdit14.Location = new System.Drawing.Point(170, 84);
            this.textEdit14.Name = "textEdit14";
            this.textEdit14.Size = new System.Drawing.Size(50, 20);
            this.textEdit14.StyleController = this.layoutControl1;
            this.textEdit14.TabIndex = 16;
            // 
            // searchLookUpEdit1
            // 
            this.searchLookUpEdit1.Location = new System.Drawing.Point(64, 84);
            this.searchLookUpEdit1.Name = "searchLookUpEdit1";
            this.searchLookUpEdit1.Size = new System.Drawing.Size(50, 20);
            this.searchLookUpEdit1.StyleController = this.layoutControl1;
            this.searchLookUpEdit1.TabIndex = 17;
            // 
            // textEdit7
            // 
            this.textEdit7.Location = new System.Drawing.Point(276, 60);
            this.textEdit7.Name = "textEdit7";
            this.textEdit7.Size = new System.Drawing.Size(50, 20);
            this.textEdit7.StyleController = this.layoutControl1;
            this.textEdit7.TabIndex = 18;
            // 
            // comboBoxEdit2
            // 
            this.comboBoxEdit2.Location = new System.Drawing.Point(170, 60);
            this.comboBoxEdit2.Name = "comboBoxEdit2";
            this.comboBoxEdit2.Size = new System.Drawing.Size(50, 20);
            this.comboBoxEdit2.StyleController = this.layoutControl1;
            this.comboBoxEdit2.TabIndex = 19;
            // 
            // textEdit1
            // 
            this.textEdit1.Location = new System.Drawing.Point(64, 60);
            this.textEdit1.Name = "textEdit1";
            this.textEdit1.Size = new System.Drawing.Size(50, 20);
            this.textEdit1.StyleController = this.layoutControl1;
            this.textEdit1.TabIndex = 20;
            // 
            // layoutControlItem2
            // 
            this.layoutControlItem2.Location = new System.Drawing.Point(0, 24);
            this.layoutControlItem2.Name = "layoutControlItem2";
            this.layoutControlItem2.Size = new System.Drawing.Size(774, 218);
            this.layoutControlItem2.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem2.TextVisible = false;
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem7,
            this.layoutControlItem8,
            this.layoutControlItem10,
            this.layoutControlItem14,
            this.layoutControlItem15,
            this.layoutControlItem13,
            this.layoutControlItem17,
            this.layoutControlItem9,
            this.layoutControlItem1,
            this.layoutControlItem19,
            this.layoutControlItem3,
            this.layoutControlItem4,
            this.layoutControlItem16,
            this.layoutControlItem5,
            this.layoutControlItem6,
            this.layoutControlItem11,
            this.layoutControlItem18});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "Root";
            this.layoutControlGroup1.OptionsItemText.TextToControlDistance = 4;
            this.layoutControlGroup1.Size = new System.Drawing.Size(444, 140);
            this.layoutControlGroup1.TextVisible = false;
            // 
            // layoutControlItem7
            // 
            this.layoutControlItem7.Control = this.textEdit11;
            this.layoutControlItem7.Location = new System.Drawing.Point(318, 0);
            this.layoutControlItem7.Name = "layoutControlItem7";
            this.layoutControlItem7.Size = new System.Drawing.Size(106, 24);
            this.layoutControlItem7.Text = "费别";
            this.layoutControlItem7.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem8
            // 
            this.layoutControlItem8.Control = this.textEdit8;
            this.layoutControlItem8.Location = new System.Drawing.Point(212, 0);
            this.layoutControlItem8.Name = "layoutControlItem8";
            this.layoutControlItem8.Size = new System.Drawing.Size(106, 24);
            this.layoutControlItem8.Text = "性别";
            this.layoutControlItem8.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem10
            // 
            this.layoutControlItem10.Control = this.textEdit2;
            this.layoutControlItem10.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem10.Name = "layoutControlItem10";
            this.layoutControlItem10.Size = new System.Drawing.Size(106, 24);
            this.layoutControlItem10.Text = "病人ID";
            this.layoutControlItem10.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem14
            // 
            this.layoutControlItem14.Control = this.textEdit9;
            this.layoutControlItem14.Location = new System.Drawing.Point(195, 24);
            this.layoutControlItem14.Name = "layoutControlItem14";
            this.layoutControlItem14.Size = new System.Drawing.Size(111, 24);
            this.layoutControlItem14.Text = "诊别";
            this.layoutControlItem14.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem15
            // 
            this.layoutControlItem15.Control = this.textEdit10;
            this.layoutControlItem15.Location = new System.Drawing.Point(306, 24);
            this.layoutControlItem15.Name = "layoutControlItem15";
            this.layoutControlItem15.Size = new System.Drawing.Size(118, 24);
            this.layoutControlItem15.Text = "其他费用";
            this.layoutControlItem15.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem13
            // 
            this.layoutControlItem13.Control = this.textEdit6;
            this.layoutControlItem13.Location = new System.Drawing.Point(0, 24);
            this.layoutControlItem13.Name = "layoutControlItem13";
            this.layoutControlItem13.Size = new System.Drawing.Size(195, 24);
            this.layoutControlItem13.Text = "年龄";
            this.layoutControlItem13.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem17
            // 
            this.layoutControlItem17.Control = this.textEdit16;
            this.layoutControlItem17.Location = new System.Drawing.Point(0, 96);
            this.layoutControlItem17.Name = "layoutControlItem17";
            this.layoutControlItem17.Size = new System.Drawing.Size(106, 24);
            this.layoutControlItem17.Text = "门诊号别";
            this.layoutControlItem17.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem9
            // 
            this.layoutControlItem9.Control = this.textEdit5;
            this.layoutControlItem9.Location = new System.Drawing.Point(106, 0);
            this.layoutControlItem9.Name = "layoutControlItem9";
            this.layoutControlItem9.Size = new System.Drawing.Size(106, 24);
            this.layoutControlItem9.Text = "姓名";
            this.layoutControlItem9.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.textEdit1;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 48);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(106, 24);
            this.layoutControlItem1.Text = "医保号";
            this.layoutControlItem1.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem19
            // 
            this.layoutControlItem19.Control = this.textEdit18;
            this.layoutControlItem19.Location = new System.Drawing.Point(106, 96);
            this.layoutControlItem19.Name = "layoutControlItem19";
            this.layoutControlItem19.Size = new System.Drawing.Size(318, 24);
            this.layoutControlItem19.Text = "诊疗卡号";
            this.layoutControlItem19.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem3
            // 
            this.layoutControlItem3.Control = this.comboBoxEdit2;
            this.layoutControlItem3.CustomizationFormText = "医保类别";
            this.layoutControlItem3.Location = new System.Drawing.Point(106, 48);
            this.layoutControlItem3.Name = "layoutControlItem3";
            this.layoutControlItem3.Size = new System.Drawing.Size(106, 24);
            this.layoutControlItem3.Text = "医保类型";
            this.layoutControlItem3.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem4
            // 
            this.layoutControlItem4.Control = this.textEdit7;
            this.layoutControlItem4.Location = new System.Drawing.Point(212, 48);
            this.layoutControlItem4.Name = "layoutControlItem4";
            this.layoutControlItem4.Size = new System.Drawing.Size(106, 24);
            this.layoutControlItem4.Text = "账户余额";
            this.layoutControlItem4.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem16
            // 
            this.layoutControlItem16.Control = this.searchLookUpEdit3;
            this.layoutControlItem16.Location = new System.Drawing.Point(318, 48);
            this.layoutControlItem16.Name = "layoutControlItem16";
            this.layoutControlItem16.Size = new System.Drawing.Size(106, 24);
            this.layoutControlItem16.Text = "合同单位";
            this.layoutControlItem16.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem5
            // 
            this.layoutControlItem5.Control = this.searchLookUpEdit1;
            this.layoutControlItem5.Location = new System.Drawing.Point(0, 72);
            this.layoutControlItem5.Name = "layoutControlItem5";
            this.layoutControlItem5.Size = new System.Drawing.Size(106, 24);
            this.layoutControlItem5.Text = "疾病编码";
            this.layoutControlItem5.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem6
            // 
            this.layoutControlItem6.Control = this.textEdit14;
            this.layoutControlItem6.Location = new System.Drawing.Point(106, 72);
            this.layoutControlItem6.Name = "layoutControlItem6";
            this.layoutControlItem6.Size = new System.Drawing.Size(106, 24);
            this.layoutControlItem6.Text = "疾病名称";
            this.layoutControlItem6.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem11
            // 
            this.layoutControlItem11.Control = this.searchLookUpEdit2;
            this.layoutControlItem11.Location = new System.Drawing.Point(212, 72);
            this.layoutControlItem11.Name = "layoutControlItem11";
            this.layoutControlItem11.Size = new System.Drawing.Size(106, 24);
            this.layoutControlItem11.Text = "识别码";
            this.layoutControlItem11.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem18
            // 
            this.layoutControlItem18.Control = this.comboBoxEdit1;
            this.layoutControlItem18.Location = new System.Drawing.Point(318, 72);
            this.layoutControlItem18.Name = "layoutControlItem18";
            this.layoutControlItem18.Size = new System.Drawing.Size(106, 24);
            this.layoutControlItem18.Text = "VIP";
            this.layoutControlItem18.TextSize = new System.Drawing.Size(48, 14);
            // 
            // searchLookUpEdit3View
            // 
            this.searchLookUpEdit3View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.UNIT_CODE,
            this.UNIT_NAME,
            this.INPUT_CODE});
            this.searchLookUpEdit3View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.searchLookUpEdit3View.Name = "searchLookUpEdit3View";
            this.searchLookUpEdit3View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.searchLookUpEdit3View.OptionsView.ShowGroupPanel = false;
            // 
            // UNIT_CODE
            // 
            this.UNIT_CODE.Caption = "单位代码";
            this.UNIT_CODE.FieldName = "UNIT_CODE";
            this.UNIT_CODE.Name = "UNIT_CODE";
            this.UNIT_CODE.Visible = true;
            this.UNIT_CODE.VisibleIndex = 0;
            // 
            // UNIT_NAME
            // 
            this.UNIT_NAME.Caption = "单位名称";
            this.UNIT_NAME.FieldName = "UNIT_NAME";
            this.UNIT_NAME.Name = "UNIT_NAME";
            this.UNIT_NAME.Visible = true;
            this.UNIT_NAME.VisibleIndex = 1;
            // 
            // INPUT_CODE
            // 
            this.INPUT_CODE.Caption = "拼音助记码";
            this.INPUT_CODE.FieldName = "INPUT_CODE";
            this.INPUT_CODE.Name = "INPUT_CODE";
            this.INPUT_CODE.Visible = true;
            this.INPUT_CODE.VisibleIndex = 2;
            // 
            // searchLookUpEdit2View
            // 
            this.searchLookUpEdit2View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.OUT_BZSBM,
            this.OUT_BZMC,
            this.OUT_PJZJM1});
            this.searchLookUpEdit2View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.searchLookUpEdit2View.Name = "searchLookUpEdit2View";
            this.searchLookUpEdit2View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.searchLookUpEdit2View.OptionsView.ShowGroupPanel = false;
            // 
            // OUT_BZSBM
            // 
            this.OUT_BZSBM.Caption = "病种识别码";
            this.OUT_BZSBM.FieldName = "OUT_BZSBM";
            this.OUT_BZSBM.Name = "OUT_BZSBM";
            this.OUT_BZSBM.Visible = true;
            this.OUT_BZSBM.VisibleIndex = 0;
            // 
            // OUT_BZMC
            // 
            this.OUT_BZMC.Caption = "病种名称";
            this.OUT_BZMC.FieldName = "OUT_BZMC";
            this.OUT_BZMC.Name = "OUT_BZMC";
            this.OUT_BZMC.Visible = true;
            this.OUT_BZMC.VisibleIndex = 1;
            // 
            // OUT_PJZJM1
            // 
            this.OUT_PJZJM1.Caption = "拼音助记码";
            this.OUT_PJZJM1.FieldName = "OUT_PJZJM";
            this.OUT_PJZJM1.Name = "OUT_PJZJM1";
            this.OUT_PJZJM1.Visible = true;
            this.OUT_PJZJM1.VisibleIndex = 2;
            // 
            // searchLookUpEdit1View
            // 
            this.searchLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.OUT_JBBM,
            this.OUT_JBMC,
            this.OUT_PJZJM});
            this.searchLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.searchLookUpEdit1View.Name = "searchLookUpEdit1View";
            this.searchLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.searchLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            // 
            // OUT_JBBM
            // 
            this.OUT_JBBM.Caption = "病种识别码";
            this.OUT_JBBM.FieldName = "OUT_JBBM";
            this.OUT_JBBM.Name = "OUT_JBBM";
            this.OUT_JBBM.Visible = true;
            this.OUT_JBBM.VisibleIndex = 0;
            // 
            // OUT_JBMC
            // 
            this.OUT_JBMC.Caption = "病种名称";
            this.OUT_JBMC.FieldName = "OUT_JBMC";
            this.OUT_JBMC.Name = "OUT_JBMC";
            this.OUT_JBMC.Visible = true;
            this.OUT_JBMC.VisibleIndex = 1;
            // 
            // OUT_PJZJM
            // 
            this.OUT_PJZJM.Caption = "拼音助记码";
            this.OUT_PJZJM.FieldName = "OUT_PJZJM";
            this.OUT_PJZJM.Name = "OUT_PJZJM";
            this.OUT_PJZJM.Visible = true;
            this.OUT_PJZJM.VisibleIndex = 2;
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.layoutControl2);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl2.Location = new System.Drawing.Point(0, 57);
            this.groupControl2.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(1046, 79);
            this.groupControl2.TabIndex = 6;
            // 
            // layoutControl2
            // 
            this.layoutControl2.Controls.Add(this.txtName);
            this.layoutControl2.Controls.Add(this.txtInTime);
            this.layoutControl2.Controls.Add(this.txtInDept);
            this.layoutControl2.Controls.Add(this.txtSex);
            this.layoutControl2.Controls.Add(this.txtPatientid);
            this.layoutControl2.Controls.Add(this.txtInpNo);
            this.layoutControl2.Controls.Add(this.txtVisitID);
            this.layoutControl2.Controls.Add(this.txtChargeType);
            this.layoutControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl2.HiddenItems.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem12});
            this.layoutControl2.Location = new System.Drawing.Point(2, 21);
            this.layoutControl2.Name = "layoutControl2";
            this.layoutControl2.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(686, 48, 873, 697);
            this.layoutControl2.Root = this.layoutControlGroup2;
            this.layoutControl2.Size = new System.Drawing.Size(1042, 56);
            this.layoutControl2.TabIndex = 1;
            this.layoutControl2.Text = "layoutControl2";
            // 
            // txtName
            // 
            this.txtName.Enabled = false;
            this.txtName.Location = new System.Drawing.Point(57, 29);
            this.txtName.MenuManager = this.barManager1;
            this.txtName.Name = "txtName";
            this.txtName.Size = new System.Drawing.Size(182, 20);
            this.txtName.StyleController = this.layoutControl2;
            this.txtName.TabIndex = 16;
            // 
            // txtInTime
            // 
            this.txtInTime.Enabled = false;
            this.txtInTime.Location = new System.Drawing.Point(804, 29);
            this.txtInTime.Name = "txtInTime";
            this.txtInTime.Size = new System.Drawing.Size(233, 20);
            this.txtInTime.StyleController = this.layoutControl2;
            this.txtInTime.TabIndex = 15;
            // 
            // txtInDept
            // 
            this.txtInDept.Enabled = false;
            this.txtInDept.Location = new System.Drawing.Point(533, 29);
            this.txtInDept.Name = "txtInDept";
            this.txtInDept.Size = new System.Drawing.Size(215, 20);
            this.txtInDept.StyleController = this.layoutControl2;
            this.txtInDept.TabIndex = 13;
            // 
            // txtSex
            // 
            this.txtSex.Enabled = false;
            this.txtSex.Location = new System.Drawing.Point(295, 29);
            this.txtSex.Name = "txtSex";
            this.txtSex.Size = new System.Drawing.Size(182, 20);
            this.txtSex.StyleController = this.layoutControl2;
            this.txtSex.TabIndex = 11;
            // 
            // txtPatientid
            // 
            this.txtPatientid.Location = new System.Drawing.Point(57, 5);
            this.txtPatientid.Name = "txtPatientid";
            this.txtPatientid.Size = new System.Drawing.Size(182, 20);
            this.txtPatientid.StyleController = this.layoutControl2;
            this.txtPatientid.TabIndex = 1;
            this.txtPatientid.KeyDown += new System.Windows.Forms.KeyEventHandler(this.txtPatientid_KeyDown);
            // 
            // txtInpNo
            // 
            this.txtInpNo.Location = new System.Drawing.Point(295, 5);
            this.txtInpNo.Name = "txtInpNo";
            this.txtInpNo.Size = new System.Drawing.Size(182, 20);
            this.txtInpNo.StyleController = this.layoutControl2;
            this.txtInpNo.TabIndex = 3;
            this.txtInpNo.KeyDown += new System.Windows.Forms.KeyEventHandler(this.txtPatientid_KeyDown);
            // 
            // txtVisitID
            // 
            this.txtVisitID.Enabled = false;
            this.txtVisitID.Location = new System.Drawing.Point(533, 5);
            this.txtVisitID.Name = "txtVisitID";
            this.txtVisitID.Size = new System.Drawing.Size(215, 20);
            this.txtVisitID.StyleController = this.layoutControl2;
            this.txtVisitID.TabIndex = 5;
            // 
            // txtChargeType
            // 
            this.txtChargeType.Enabled = false;
            this.txtChargeType.Location = new System.Drawing.Point(804, 5);
            this.txtChargeType.Name = "txtChargeType";
            this.txtChargeType.Size = new System.Drawing.Size(233, 20);
            this.txtChargeType.StyleController = this.layoutControl2;
            this.txtChargeType.TabIndex = 7;
            // 
            // layoutControlItem12
            // 
            this.layoutControlItem12.Location = new System.Drawing.Point(0, 24);
            this.layoutControlItem12.Name = "layoutControlItem2";
            this.layoutControlItem12.Size = new System.Drawing.Size(774, 218);
            this.layoutControlItem12.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem12.TextVisible = false;
            // 
            // layoutControlGroup2
            // 
            this.layoutControlGroup2.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.layoutControlGroup2.GroupBordersVisible = false;
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem20,
            this.layoutControlItem21,
            this.layoutControlItem22,
            this.layoutControlItem23,
            this.layoutControlItem24,
            this.layoutControlItem25,
            this.layoutControlItem27,
            this.layoutControlItem26});
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup2.Name = "Root";
            this.layoutControlGroup2.OptionsItemText.TextToControlDistance = 4;
            this.layoutControlGroup2.Padding = new DevExpress.XtraLayout.Utils.Padding(3, 3, 3, 3);
            this.layoutControlGroup2.Size = new System.Drawing.Size(1042, 56);
            this.layoutControlGroup2.TextVisible = false;
            // 
            // layoutControlItem20
            // 
            this.layoutControlItem20.Control = this.txtChargeType;
            this.layoutControlItem20.Location = new System.Drawing.Point(747, 0);
            this.layoutControlItem20.Name = "layoutControlItem7";
            this.layoutControlItem20.Size = new System.Drawing.Size(289, 24);
            this.layoutControlItem20.Text = "费别";
            this.layoutControlItem20.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem21
            // 
            this.layoutControlItem21.Control = this.txtVisitID;
            this.layoutControlItem21.Location = new System.Drawing.Point(476, 0);
            this.layoutControlItem21.Name = "layoutControlItem8";
            this.layoutControlItem21.Size = new System.Drawing.Size(271, 24);
            this.layoutControlItem21.Text = "住院次数";
            this.layoutControlItem21.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem22
            // 
            this.layoutControlItem22.Control = this.txtPatientid;
            this.layoutControlItem22.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem22.Name = "layoutControlItem10";
            this.layoutControlItem22.Size = new System.Drawing.Size(238, 24);
            this.layoutControlItem22.Text = "病人ID";
            this.layoutControlItem22.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem23
            // 
            this.layoutControlItem23.Control = this.txtInDept;
            this.layoutControlItem23.Location = new System.Drawing.Point(476, 24);
            this.layoutControlItem23.Name = "layoutControlItem14";
            this.layoutControlItem23.Size = new System.Drawing.Size(271, 26);
            this.layoutControlItem23.Text = "入院科室";
            this.layoutControlItem23.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem24
            // 
            this.layoutControlItem24.Control = this.txtInTime;
            this.layoutControlItem24.Location = new System.Drawing.Point(747, 24);
            this.layoutControlItem24.Name = "layoutControlItem15";
            this.layoutControlItem24.Size = new System.Drawing.Size(289, 26);
            this.layoutControlItem24.Text = "入院时间";
            this.layoutControlItem24.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem25
            // 
            this.layoutControlItem25.Control = this.txtSex;
            this.layoutControlItem25.Location = new System.Drawing.Point(238, 24);
            this.layoutControlItem25.Name = "layoutControlItem13";
            this.layoutControlItem25.Size = new System.Drawing.Size(238, 26);
            this.layoutControlItem25.Text = "性别";
            this.layoutControlItem25.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem27
            // 
            this.layoutControlItem27.Control = this.txtInpNo;
            this.layoutControlItem27.Location = new System.Drawing.Point(238, 0);
            this.layoutControlItem27.Name = "layoutControlItem9";
            this.layoutControlItem27.Size = new System.Drawing.Size(238, 24);
            this.layoutControlItem27.Text = "住院号";
            this.layoutControlItem27.TextSize = new System.Drawing.Size(48, 14);
            // 
            // layoutControlItem26
            // 
            this.layoutControlItem26.Control = this.txtName;
            this.layoutControlItem26.Location = new System.Drawing.Point(0, 24);
            this.layoutControlItem26.Name = "layoutControlItem26";
            this.layoutControlItem26.Size = new System.Drawing.Size(238, 26);
            this.layoutControlItem26.Text = "姓名";
            this.layoutControlItem26.TextSize = new System.Drawing.Size(48, 14);
            // 
            // panelControl1
            // 
            this.panelControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panelControl1.Controls.Add(this.gcClinic);
            this.panelControl1.Location = new System.Drawing.Point(1, 140);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(1045, 371);
            this.panelControl1.TabIndex = 7;
            // 
            // gcClinic
            // 
            this.gcClinic.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcClinic.EmbeddedNavigator.Margin = new System.Windows.Forms.Padding(3, 5, 3, 5);
            this.gcClinic.Location = new System.Drawing.Point(2, 2);
            this.gcClinic.MainView = this.gvRcpt;
            this.gcClinic.Margin = new System.Windows.Forms.Padding(3, 5, 3, 5);
            this.gcClinic.MenuManager = this.barManager1;
            this.gcClinic.Name = "gcClinic";
            this.gcClinic.Size = new System.Drawing.Size(1041, 367);
            this.gcClinic.TabIndex = 7;
            this.gcClinic.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvRcpt});
            // 
            // gvRcpt
            // 
            this.gvRcpt.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcRCPT_NO,
            this.gcTRANSACT_DATE,
            this.gcAMOUNT,
            this.gcPAY_WAY,
            this.gcPATIENT_ID,
            this.gcTRANSACT_TYPE,
            this.gcBANK,
            this.gcCHECK_NO,
            this.gcBANK_AUOUNT_NO,
            this.gcOPERATOR_NO,
            this.gcREFUNDED_RCPT_NO,
            this.gcUSED_FLAG,
            this.gcBANK_TRANS_DATE});
            this.gvRcpt.GridControl = this.gcClinic;
            this.gvRcpt.Name = "gvRcpt";
            this.gvRcpt.OptionsView.ColumnAutoWidth = false;
            this.gvRcpt.OptionsView.ShowGroupPanel = false;
            // 
            // gcRCPT_NO
            // 
            this.gcRCPT_NO.AppearanceHeader.Options.UseTextOptions = true;
            this.gcRCPT_NO.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gcRCPT_NO.Caption = "收据号";
            this.gcRCPT_NO.FieldName = "RCPT_NO";
            this.gcRCPT_NO.MaxWidth = 120;
            this.gcRCPT_NO.MinWidth = 120;
            this.gcRCPT_NO.Name = "gcRCPT_NO";
            this.gcRCPT_NO.OptionsColumn.AllowEdit = false;
            this.gcRCPT_NO.OptionsColumn.AllowFocus = false;
            this.gcRCPT_NO.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.gcRCPT_NO.OptionsColumn.AllowIncrementalSearch = false;
            this.gcRCPT_NO.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcRCPT_NO.OptionsColumn.ReadOnly = true;
            this.gcRCPT_NO.Visible = true;
            this.gcRCPT_NO.VisibleIndex = 0;
            this.gcRCPT_NO.Width = 120;
            // 
            // gcTRANSACT_DATE
            // 
            this.gcTRANSACT_DATE.AppearanceHeader.Options.UseTextOptions = true;
            this.gcTRANSACT_DATE.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gcTRANSACT_DATE.Caption = "日期及时间";
            this.gcTRANSACT_DATE.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.gcTRANSACT_DATE.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gcTRANSACT_DATE.FieldName = "TRANSACT_DATE";
            this.gcTRANSACT_DATE.MaxWidth = 120;
            this.gcTRANSACT_DATE.MinWidth = 120;
            this.gcTRANSACT_DATE.Name = "gcTRANSACT_DATE";
            this.gcTRANSACT_DATE.OptionsColumn.AllowEdit = false;
            this.gcTRANSACT_DATE.OptionsColumn.AllowFocus = false;
            this.gcTRANSACT_DATE.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.gcTRANSACT_DATE.OptionsColumn.AllowIncrementalSearch = false;
            this.gcTRANSACT_DATE.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcTRANSACT_DATE.OptionsColumn.ReadOnly = true;
            this.gcTRANSACT_DATE.Visible = true;
            this.gcTRANSACT_DATE.VisibleIndex = 1;
            this.gcTRANSACT_DATE.Width = 120;
            // 
            // gcAMOUNT
            // 
            this.gcAMOUNT.AppearanceHeader.Options.UseTextOptions = true;
            this.gcAMOUNT.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gcAMOUNT.Caption = "金额";
            this.gcAMOUNT.FieldName = "AMOUNT";
            this.gcAMOUNT.MaxWidth = 120;
            this.gcAMOUNT.MinWidth = 120;
            this.gcAMOUNT.Name = "gcAMOUNT";
            this.gcAMOUNT.OptionsColumn.AllowEdit = false;
            this.gcAMOUNT.OptionsColumn.AllowFocus = false;
            this.gcAMOUNT.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.gcAMOUNT.OptionsColumn.AllowIncrementalSearch = false;
            this.gcAMOUNT.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcAMOUNT.OptionsColumn.ReadOnly = true;
            this.gcAMOUNT.Visible = true;
            this.gcAMOUNT.VisibleIndex = 2;
            this.gcAMOUNT.Width = 120;
            // 
            // gcPAY_WAY
            // 
            this.gcPAY_WAY.AppearanceHeader.Options.UseTextOptions = true;
            this.gcPAY_WAY.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gcPAY_WAY.Caption = "支付方式";
            this.gcPAY_WAY.FieldName = "PAY_WAY";
            this.gcPAY_WAY.MaxWidth = 120;
            this.gcPAY_WAY.MinWidth = 120;
            this.gcPAY_WAY.Name = "gcPAY_WAY";
            this.gcPAY_WAY.OptionsColumn.AllowEdit = false;
            this.gcPAY_WAY.OptionsColumn.AllowFocus = false;
            this.gcPAY_WAY.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.gcPAY_WAY.OptionsColumn.AllowIncrementalSearch = false;
            this.gcPAY_WAY.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcPAY_WAY.OptionsColumn.ReadOnly = true;
            this.gcPAY_WAY.Visible = true;
            this.gcPAY_WAY.VisibleIndex = 3;
            this.gcPAY_WAY.Width = 120;
            // 
            // gcPATIENT_ID
            // 
            this.gcPATIENT_ID.AppearanceHeader.Options.UseTextOptions = true;
            this.gcPATIENT_ID.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gcPATIENT_ID.Caption = "病人ID";
            this.gcPATIENT_ID.FieldName = "PATIENT_ID";
            this.gcPATIENT_ID.MaxWidth = 120;
            this.gcPATIENT_ID.MinWidth = 120;
            this.gcPATIENT_ID.Name = "gcPATIENT_ID";
            this.gcPATIENT_ID.OptionsColumn.AllowEdit = false;
            this.gcPATIENT_ID.OptionsColumn.AllowFocus = false;
            this.gcPATIENT_ID.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.gcPATIENT_ID.OptionsColumn.AllowIncrementalSearch = false;
            this.gcPATIENT_ID.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcPATIENT_ID.OptionsColumn.ReadOnly = true;
            this.gcPATIENT_ID.Width = 120;
            // 
            // gcTRANSACT_TYPE
            // 
            this.gcTRANSACT_TYPE.AppearanceHeader.Options.UseTextOptions = true;
            this.gcTRANSACT_TYPE.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gcTRANSACT_TYPE.Caption = "类型";
            this.gcTRANSACT_TYPE.FieldName = "TRANSACT_TYPE";
            this.gcTRANSACT_TYPE.MaxWidth = 120;
            this.gcTRANSACT_TYPE.MinWidth = 120;
            this.gcTRANSACT_TYPE.Name = "gcTRANSACT_TYPE";
            this.gcTRANSACT_TYPE.OptionsColumn.AllowEdit = false;
            this.gcTRANSACT_TYPE.OptionsColumn.AllowFocus = false;
            this.gcTRANSACT_TYPE.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.gcTRANSACT_TYPE.OptionsColumn.AllowIncrementalSearch = false;
            this.gcTRANSACT_TYPE.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcTRANSACT_TYPE.OptionsColumn.ReadOnly = true;
            this.gcTRANSACT_TYPE.Visible = true;
            this.gcTRANSACT_TYPE.VisibleIndex = 4;
            this.gcTRANSACT_TYPE.Width = 120;
            // 
            // gcBANK
            // 
            this.gcBANK.AppearanceHeader.Options.UseTextOptions = true;
            this.gcBANK.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gcBANK.Caption = "开户银行";
            this.gcBANK.FieldName = "BANK";
            this.gcBANK.MaxWidth = 120;
            this.gcBANK.MinWidth = 120;
            this.gcBANK.Name = "gcBANK";
            this.gcBANK.OptionsColumn.AllowEdit = false;
            this.gcBANK.OptionsColumn.AllowFocus = false;
            this.gcBANK.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.gcBANK.OptionsColumn.AllowIncrementalSearch = false;
            this.gcBANK.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcBANK.OptionsColumn.ReadOnly = true;
            this.gcBANK.Visible = true;
            this.gcBANK.VisibleIndex = 5;
            this.gcBANK.Width = 120;
            // 
            // gcCHECK_NO
            // 
            this.gcCHECK_NO.AppearanceHeader.Options.UseTextOptions = true;
            this.gcCHECK_NO.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gcCHECK_NO.Caption = "支票号";
            this.gcCHECK_NO.FieldName = "CHECK_NO";
            this.gcCHECK_NO.MaxWidth = 120;
            this.gcCHECK_NO.MinWidth = 120;
            this.gcCHECK_NO.Name = "gcCHECK_NO";
            this.gcCHECK_NO.OptionsColumn.AllowEdit = false;
            this.gcCHECK_NO.OptionsColumn.AllowFocus = false;
            this.gcCHECK_NO.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.gcCHECK_NO.OptionsColumn.AllowIncrementalSearch = false;
            this.gcCHECK_NO.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcCHECK_NO.OptionsColumn.ReadOnly = true;
            this.gcCHECK_NO.Visible = true;
            this.gcCHECK_NO.VisibleIndex = 6;
            this.gcCHECK_NO.Width = 120;
            // 
            // gcBANK_AUOUNT_NO
            // 
            this.gcBANK_AUOUNT_NO.AppearanceHeader.Options.UseTextOptions = true;
            this.gcBANK_AUOUNT_NO.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gcBANK_AUOUNT_NO.Caption = "银行帐号";
            this.gcBANK_AUOUNT_NO.FieldName = "BANK_AUOUNT_NO";
            this.gcBANK_AUOUNT_NO.MaxWidth = 120;
            this.gcBANK_AUOUNT_NO.MinWidth = 120;
            this.gcBANK_AUOUNT_NO.Name = "gcBANK_AUOUNT_NO";
            this.gcBANK_AUOUNT_NO.OptionsColumn.AllowEdit = false;
            this.gcBANK_AUOUNT_NO.OptionsColumn.AllowFocus = false;
            this.gcBANK_AUOUNT_NO.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.gcBANK_AUOUNT_NO.OptionsColumn.AllowIncrementalSearch = false;
            this.gcBANK_AUOUNT_NO.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcBANK_AUOUNT_NO.OptionsColumn.ReadOnly = true;
            this.gcBANK_AUOUNT_NO.Visible = true;
            this.gcBANK_AUOUNT_NO.VisibleIndex = 7;
            this.gcBANK_AUOUNT_NO.Width = 120;
            // 
            // gcOPERATOR_NO
            // 
            this.gcOPERATOR_NO.AppearanceHeader.Options.UseTextOptions = true;
            this.gcOPERATOR_NO.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gcOPERATOR_NO.Caption = "收款员";
            this.gcOPERATOR_NO.FieldName = "NAME";
            this.gcOPERATOR_NO.MaxWidth = 120;
            this.gcOPERATOR_NO.MinWidth = 120;
            this.gcOPERATOR_NO.Name = "gcOPERATOR_NO";
            this.gcOPERATOR_NO.OptionsColumn.AllowEdit = false;
            this.gcOPERATOR_NO.OptionsColumn.AllowFocus = false;
            this.gcOPERATOR_NO.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.gcOPERATOR_NO.OptionsColumn.AllowIncrementalSearch = false;
            this.gcOPERATOR_NO.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcOPERATOR_NO.OptionsColumn.ReadOnly = true;
            this.gcOPERATOR_NO.Visible = true;
            this.gcOPERATOR_NO.VisibleIndex = 8;
            this.gcOPERATOR_NO.Width = 120;
            // 
            // gcREFUNDED_RCPT_NO
            // 
            this.gcREFUNDED_RCPT_NO.AppearanceHeader.Options.UseTextOptions = true;
            this.gcREFUNDED_RCPT_NO.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gcREFUNDED_RCPT_NO.Caption = "退款号";
            this.gcREFUNDED_RCPT_NO.FieldName = "REFUNDED_RCPT_NO";
            this.gcREFUNDED_RCPT_NO.MaxWidth = 120;
            this.gcREFUNDED_RCPT_NO.MinWidth = 120;
            this.gcREFUNDED_RCPT_NO.Name = "gcREFUNDED_RCPT_NO";
            this.gcREFUNDED_RCPT_NO.OptionsColumn.AllowEdit = false;
            this.gcREFUNDED_RCPT_NO.OptionsColumn.AllowFocus = false;
            this.gcREFUNDED_RCPT_NO.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.gcREFUNDED_RCPT_NO.OptionsColumn.AllowIncrementalSearch = false;
            this.gcREFUNDED_RCPT_NO.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcREFUNDED_RCPT_NO.OptionsColumn.ReadOnly = true;
            this.gcREFUNDED_RCPT_NO.Visible = true;
            this.gcREFUNDED_RCPT_NO.VisibleIndex = 9;
            this.gcREFUNDED_RCPT_NO.Width = 120;
            // 
            // gcUSED_FLAG
            // 
            this.gcUSED_FLAG.AppearanceHeader.Options.UseTextOptions = true;
            this.gcUSED_FLAG.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gcUSED_FLAG.Caption = "标志";
            this.gcUSED_FLAG.FieldName = "USED_FLAG";
            this.gcUSED_FLAG.MaxWidth = 120;
            this.gcUSED_FLAG.MinWidth = 120;
            this.gcUSED_FLAG.Name = "gcUSED_FLAG";
            this.gcUSED_FLAG.OptionsColumn.AllowEdit = false;
            this.gcUSED_FLAG.OptionsColumn.AllowFocus = false;
            this.gcUSED_FLAG.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.gcUSED_FLAG.OptionsColumn.AllowIncrementalSearch = false;
            this.gcUSED_FLAG.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcUSED_FLAG.OptionsColumn.ReadOnly = true;
            this.gcUSED_FLAG.Width = 120;
            // 
            // gcBANK_TRANS_DATE
            // 
            this.gcBANK_TRANS_DATE.AppearanceHeader.Options.UseTextOptions = true;
            this.gcBANK_TRANS_DATE.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gcBANK_TRANS_DATE.Caption = "银行交易时间";
            this.gcBANK_TRANS_DATE.FieldName = "BANK_TRANS_DATE";
            this.gcBANK_TRANS_DATE.MaxWidth = 120;
            this.gcBANK_TRANS_DATE.MinWidth = 120;
            this.gcBANK_TRANS_DATE.Name = "gcBANK_TRANS_DATE";
            this.gcBANK_TRANS_DATE.OptionsColumn.AllowEdit = false;
            this.gcBANK_TRANS_DATE.OptionsColumn.AllowFocus = false;
            this.gcBANK_TRANS_DATE.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.gcBANK_TRANS_DATE.OptionsColumn.AllowIncrementalSearch = false;
            this.gcBANK_TRANS_DATE.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcBANK_TRANS_DATE.OptionsColumn.ReadOnly = true;
            this.gcBANK_TRANS_DATE.Visible = true;
            this.gcBANK_TRANS_DATE.VisibleIndex = 10;
            this.gcBANK_TRANS_DATE.Width = 120;
            // 
            // labelControl1
            // 
            this.labelControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.labelControl1.Appearance.ForeColor = System.Drawing.Color.Blue;
            this.labelControl1.Location = new System.Drawing.Point(10, 522);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(36, 14);
            this.labelControl1.TabIndex = 8;
            this.labelControl1.Text = "总额：";
            // 
            // labelControl2
            // 
            this.labelControl2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.labelControl2.Appearance.ForeColor = System.Drawing.Color.Blue;
            this.labelControl2.Location = new System.Drawing.Point(197, 522);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(36, 14);
            this.labelControl2.TabIndex = 8;
            this.labelControl2.Text = "余额：";
            // 
            // txtsum
            // 
            this.txtsum.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.txtsum.Enabled = false;
            this.txtsum.Location = new System.Drawing.Point(56, 519);
            this.txtsum.MenuManager = this.barManager1;
            this.txtsum.Name = "txtsum";
            this.txtsum.Properties.Appearance.ForeColor = System.Drawing.Color.Blue;
            this.txtsum.Properties.Appearance.Options.UseForeColor = true;
            this.txtsum.Size = new System.Drawing.Size(136, 20);
            this.txtsum.TabIndex = 9;
            // 
            // txtyue
            // 
            this.txtyue.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.txtyue.Enabled = false;
            this.txtyue.Location = new System.Drawing.Point(242, 519);
            this.txtyue.Name = "txtyue";
            this.txtyue.Properties.Appearance.ForeColor = System.Drawing.Color.Blue;
            this.txtyue.Properties.Appearance.Options.UseForeColor = true;
            this.txtyue.Size = new System.Drawing.Size(136, 20);
            this.txtyue.TabIndex = 9;
            // 
            // frmPrepQueryPat
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1046, 546);
            this.Controls.Add(this.txtyue);
            this.Controls.Add(this.txtsum);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.panelControl1);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Margin = new System.Windows.Forms.Padding(4);
            this.Name = "frmPrepQueryPat";
            this.Text = "查询病人预交金";
            this.Load += new System.EventHandler(this.frmPrepQueryPat_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).EndInit();
            this.layoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.textEdit18.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit16.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.searchLookUpEdit3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit10.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit9.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit6.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.searchLookUpEdit2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit5.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit8.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit11.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit14.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.searchLookUpEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit7.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEdit2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.searchLookUpEdit3View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.searchLookUpEdit2View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.searchLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl2)).EndInit();
            this.layoutControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInTime.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInDept.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSex.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPatientid.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInpNo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVisitID.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtChargeType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem21)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem22)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem23)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem24)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem25)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem27)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem26)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcClinic)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvRcpt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtsum.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtyue.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraBars.BarLargeButtonItem cb_refresh;
        private DevExpress.XtraBars.BarLargeButtonItem cb_save;
        private DevExpress.XtraBars.BarLargeButtonItem cb_commit;
        private DevExpress.XtraBars.BarLargeButtonItem cb_copy;
        private DevExpress.XtraBars.BarLargeButtonItem barLargeButtonItem3;
        private DevExpress.XtraBars.BarLargeButtonItem cb_close;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarLargeButtonItem cb_childorder;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraLayout.LayoutControl layoutControl2;
        private DevExpress.XtraEditors.TextEdit txtName;
        private DevExpress.XtraEditors.TextEdit txtInTime;
        private DevExpress.XtraEditors.TextEdit txtInDept;
        private DevExpress.XtraEditors.TextEdit txtSex;
        private DevExpress.XtraEditors.TextEdit txtPatientid;
        private DevExpress.XtraEditors.TextEdit txtInpNo;
        private DevExpress.XtraEditors.TextEdit txtVisitID;
        private DevExpress.XtraEditors.TextEdit txtChargeType;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem12;
        private DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem20;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem21;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem22;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem23;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem24;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem25;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem27;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem26;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraLayout.LayoutControl layoutControl1;
        private DevExpress.XtraEditors.TextEdit textEdit18;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEdit1;
        private DevExpress.XtraEditors.TextEdit textEdit16;
        private DevExpress.XtraEditors.SearchLookUpEdit searchLookUpEdit3;
        private DevExpress.XtraGrid.Views.Grid.GridView searchLookUpEdit3View;
        private DevExpress.XtraGrid.Columns.GridColumn UNIT_CODE;
        private DevExpress.XtraGrid.Columns.GridColumn UNIT_NAME;
        private DevExpress.XtraGrid.Columns.GridColumn INPUT_CODE;
        private DevExpress.XtraEditors.TextEdit textEdit10;
        private DevExpress.XtraEditors.TextEdit textEdit9;
        private DevExpress.XtraEditors.TextEdit textEdit6;
        private DevExpress.XtraEditors.SearchLookUpEdit searchLookUpEdit2;
        private DevExpress.XtraGrid.Views.Grid.GridView searchLookUpEdit2View;
        private DevExpress.XtraGrid.Columns.GridColumn OUT_BZSBM;
        private DevExpress.XtraGrid.Columns.GridColumn OUT_BZMC;
        private DevExpress.XtraGrid.Columns.GridColumn OUT_PJZJM1;
        private DevExpress.XtraEditors.TextEdit textEdit2;
        private DevExpress.XtraEditors.TextEdit textEdit5;
        private DevExpress.XtraEditors.TextEdit textEdit8;
        private DevExpress.XtraEditors.TextEdit textEdit11;
        private DevExpress.XtraEditors.TextEdit textEdit14;
        private DevExpress.XtraEditors.SearchLookUpEdit searchLookUpEdit1;
        private DevExpress.XtraGrid.Views.Grid.GridView searchLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn OUT_JBBM;
        private DevExpress.XtraGrid.Columns.GridColumn OUT_JBMC;
        private DevExpress.XtraGrid.Columns.GridColumn OUT_PJZJM;
        private DevExpress.XtraEditors.TextEdit textEdit7;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEdit2;
        private DevExpress.XtraEditors.TextEdit textEdit1;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem2;
        private DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem7;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem8;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem10;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem14;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem15;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem13;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem17;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem9;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem19;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem3;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem4;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem16;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem5;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem6;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem11;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem18;
        private DevExpress.XtraEditors.TextEdit txtyue;
        private DevExpress.XtraEditors.TextEdit txtsum;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraGrid.GridControl gcClinic;
        private DevExpress.XtraGrid.Views.Grid.GridView gvRcpt;
        private DevExpress.XtraGrid.Columns.GridColumn gcRCPT_NO;
        private DevExpress.XtraGrid.Columns.GridColumn gcTRANSACT_DATE;
        private DevExpress.XtraGrid.Columns.GridColumn gcAMOUNT;
        private DevExpress.XtraGrid.Columns.GridColumn gcPAY_WAY;
        private DevExpress.XtraGrid.Columns.GridColumn gcPATIENT_ID;
        private DevExpress.XtraGrid.Columns.GridColumn gcTRANSACT_TYPE;
        private DevExpress.XtraGrid.Columns.GridColumn gcBANK;
        private DevExpress.XtraGrid.Columns.GridColumn gcCHECK_NO;
        private DevExpress.XtraGrid.Columns.GridColumn gcBANK_AUOUNT_NO;
        private DevExpress.XtraGrid.Columns.GridColumn gcOPERATOR_NO;
        private DevExpress.XtraGrid.Columns.GridColumn gcREFUNDED_RCPT_NO;
        private DevExpress.XtraGrid.Columns.GridColumn gcUSED_FLAG;
        private DevExpress.XtraGrid.Columns.GridColumn gcBANK_TRANS_DATE;

    }
}