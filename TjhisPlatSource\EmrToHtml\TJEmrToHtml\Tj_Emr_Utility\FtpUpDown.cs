﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Net;

namespace TJ.EMR.Utility
{
    public class FtpUpDown
    {
        string ftpServerIP;
        string ftpUserID;
        string ftpPassword;
        FtpWebRequest reqFTP;
        private void Connect(String path)//连接ftp
        {
            // 根据uri创建FtpWebRequest对象
            reqFTP = (FtpWebRequest)FtpWebRequest.Create(new Uri(path));
            // 指定数据传输类型
            reqFTP.UseBinary = true;
            // ftp用户名和密码
            reqFTP.Credentials = new NetworkCredential(ftpUserID, ftpPassword);
        }
        public FtpUpDown(string ftpServerIP, string ftpUserID, string ftpPassword)
        {
            this.ftpServerIP = ftpServerIP;
            this.ftpUserID = ftpUserID;
            this.ftpPassword = ftpPassword;
        }

        #region 上传
        /// <summary>
        /// 上传文件到FTP服务器
        /// </summary>
        /// <param name="filename">本地文件完全路径</param>
        /// <returns>0 成功，-1本地无此文件，-2上传失败</returns>
        public int Upload(string RemoteFile, string LocalFile)
        {
            if (File.Exists(LocalFile))
            {
                FileInfo fileInf = new FileInfo(LocalFile);
                if (RemoteFile.Split('/').Length > 1)
                {
                    CreateFtpListDirectory(RemoteFile.Substring(0, RemoteFile.LastIndexOf('/')));
                }

                string uri = "ftp://" + ftpServerIP + "/" + RemoteFile;
                Connect(uri);//连接   

                // 默认为true，连接不会被关闭
                // 在一个命令之后被执行
                reqFTP.KeepAlive = false;
                // 指定执行什么命令
                reqFTP.Method = WebRequestMethods.Ftp.UploadFile;
                // 上传文件时通知服务器文件的大小
                reqFTP.ContentLength = fileInf.Length;
                // 缓冲大小设置为kb 
                int buffLength = 2048;
                byte[] buff = new byte[buffLength];
                int contentLen;
                // 打开一个文件流(System.IO.FileStream) 去读上传的文件
                FileStream fs = fileInf.OpenRead();
                try
                {
                    // 把上传的文件写入流
                    Stream strm = reqFTP.GetRequestStream();
                    // 每次读文件流的kb
                    contentLen = fs.Read(buff, 0, buffLength);
                    // 流内容没有结束
                    while (contentLen != 0)
                    {
                        // 把内容从file stream 写入upload stream 
                        strm.Write(buff, 0, contentLen);
                        contentLen = fs.Read(buff, 0, buffLength);
                    }
                    // 关闭两个流
                    strm.Close();
                    fs.Close();
                    return 0;
                }

                catch (Exception ex)
                {
                    return -2;
                }
            }
            else
            {
                return -1;
            }
        }
        #endregion

        #region 下载
        /// <summary>
        /// 从FTP服务器下载文件
        /// </summary>
        /// <param name="filename">服务器文件完全路径</param>
        /// <param name="filename">本地文件完全路径</param>
        /// <returns>0 成功，-1本地无此文件，-2上传失败</returns>
        public int Download(string RemoteFile, string LocalFile)////上面的代码实现了从ftp服务器下载文件的功能
        {
            try
            {
                string url = "ftp://" + ftpServerIP + "/" + RemoteFile;
                Connect(url);//连接  
                reqFTP.Method = WebRequestMethods.Ftp.DownloadFile;
                FtpWebResponse response = (FtpWebResponse)reqFTP.GetResponse();
                Stream ftpStream = response.GetResponseStream();
                long cl = response.ContentLength;
                int bufferSize = 2048;
                int readCount;
                byte[] buffer = new byte[bufferSize];
                readCount = ftpStream.Read(buffer, 0, bufferSize);
                FileStream outputStream = new FileStream(LocalFile, FileMode.Create);
                while (readCount > 0)
                {
                    outputStream.Write(buffer, 0, readCount);
                    readCount = ftpStream.Read(buffer, 0, bufferSize);
                }
                ftpStream.Close();
                outputStream.Close();
                response.Close();
                return 0;
            }
            catch (Exception ex)
            {
                return -1;
            }
        }
        #endregion

        #region 创建目录
        /// <summary>
        /// 创建文件夹:不实现级联创建
        /// 返回：0成功，-1失败
        /// </summary>
        /// <param name="dirName">目标目录名，相对路径：如/FTF</param>
        /// <returns></returns>
        public int CreateFtpDirectory(string dirName)
        {

            string uri = "ftp://" + ftpServerIP + "/" + dirName;
            Connect(uri);//连接      
            reqFTP.Method = WebRequestMethods.Ftp.MakeDirectory;

            try
            {
                FtpWebResponse FtpResponse = (FtpWebResponse)reqFTP.GetResponse();
                if (FtpResponse.StatusCode == FtpStatusCode.PathnameCreated)
                {
                    FtpResponse.Close();
                    return 0;
                }
                else
                {
                    FtpResponse.Close();
                    return -1;
                }
            }
            catch (WebException e)
            {
                FtpWebResponse FtpResponse = (FtpWebResponse)e.Response;
                if (FtpResponse.StatusCode == FtpStatusCode.ActionNotTakenFileUnavailable)
                {
                    //文件已存在，返回True
                    FtpResponse.Close();
                    return 0;
                }
                else
                {
                    FtpResponse.Close();
                    return -1;
                }
            }
        }

        /// <summary>
        /// 创建文件夹:实现级联创建
        /// 返回：true成功，false失败
        /// </summary>
        /// <param name="AimUriStr">目标目录名，相对路径：如/FTF/FTF1/FTF2/FTF3</param>
        /// <returns></returns>
        public int CreateFtpListDirectory(string AimUriStr)
        {
            string[] AimUriArray = AimUriStr.TrimStart('/').Split('/');
            string AimUriCache = string.Empty;
            for (int i = 0; i < AimUriArray.Length; i++)
            {
                AimUriCache += "/" + AimUriArray[i];
                if (CreateFtpDirectory(AimUriCache) == 0)
                {
                    continue;
                }
                else
                {
                    if (CreateFtpDirectory(AimUriCache) == 0)
                    {
                        continue;
                    }
                    else
                    {
                        return -1;
                    }
                }
            }

            return 0;
        }
        #endregion

        #region 删除文件夹/文件
        /// <summary>
        /// 删除文件夹,不实现级联删除
        /// 返回：0成功，-1失败
        /// </summary>
        /// <param name="dirName">目标目录，相对路径：如/FTF/FTFDEL</param>
        /// <returns></returns>
        public int DeleteFtpDirectory(string dirName)
        {
            string uri = "ftp://" + ftpServerIP + "/" + dirName;
            Connect(uri);//连接      
            reqFTP.Method = WebRequestMethods.Ftp.RemoveDirectory;
            try
            {
                FtpWebResponse FtpResponse = (FtpWebResponse)reqFTP.GetResponse();
                if (FtpResponse.StatusCode == FtpStatusCode.FileActionOK)
                {
                    FtpResponse.Close();
                    return 0;
                }
                else
                {
                    FtpResponse.Close();
                    return -1;
                }
            }
            catch (WebException e)
            {
                FtpWebResponse FtpResponse = (FtpWebResponse)e.Response;
                //如果返回信息表示文件不可操作或不存在，表明文件夹已经被删除
                if (FtpResponse.StatusCode == FtpStatusCode.ActionNotTakenFileUnavailable)
                {
                    FtpResponse.Close();
                    return 0;
                }
                else
                {
                    //返回其他错误：可能出现问题
                    FtpResponse.Close();
                    return -1;
                }
            }
        }

        /// <summary>
        /// 删除文件夹，实现级联删除
        /// 返回：true成功，false失败
        /// </summary>
        /// <param name="AimUriStr">目标目录，相对路径：如/FTF/FTFDEL</param>
        /// <returns></returns>
        public int DeleteFtpListDirectory(string AimUriStr)
        {
            string[] DirectoryDetailList = GetFilesDetailList(AimUriStr);
            foreach (string ListDetail in DirectoryDetailList)
            {
                if (ListDetail.EndsWith("|D"))
                {
                    //删除文件夹内容
                    if (GetFilesDetailList(AimUriStr + "/" + ListDetail.Split('|')[0]).Length == 0)
                    {
                        if (DeleteFtpDirectory(AimUriStr + "/" + ListDetail.Split('|')[0]) == -1)
                        {
                            return -1;
                        }
                    }
                    else
                    {
                        if (DeleteFtpListDirectory(AimUriStr + "/" + ListDetail.Split('|')[0]) == -1)
                        {
                            return -1;
                        }
                    }
                }
                if (ListDetail.EndsWith("|F"))
                {
                    //删除文件
                    if (DeleteFileName(AimUriStr + "/" + ListDetail.Split('|')[0]) == -1)
                    {
                        return -1;
                    }
                }
            }
            //删除当前文件夹
            if (DeleteFtpDirectory(AimUriStr) == -1)
            {
                return -1;
            }

            return 0;
        }

        /// <summary>
        /// 删除文件
        /// 返回：0成功，-1失败
        /// </summary>
        /// <param name="fileName">目标目录名，相对路径：如/FTF/abc.emt/param>
        /// <returns></returns>
        public int DeleteFileName(string fileName)
        {
            try
            {
                FileInfo fileInf = new FileInfo(fileName);
                string uri = "ftp://" + ftpServerIP + "/" + fileInf.Name;
                Connect(uri);//连接         
                // 默认为true，连接不会被关闭
                // 在一个命令之后被执行
                reqFTP.KeepAlive = false;
                // 指定执行什么命令
                reqFTP.Method = WebRequestMethods.Ftp.DeleteFile;
                FtpWebResponse response = (FtpWebResponse)reqFTP.GetResponse();
                response.Close();
                return 0;
            }
            catch (Exception ex)
            {
                return -1;
            }
        }
        #endregion

        #region 获取文件夹内文件和文件夹列表信息
        //都调用这个
        private string[] GetFileList(string path, string WRMethods)//上面的代码示例了如何从ftp服务器上获得文件列表
        {
            string[] downloadFiles;
            StringBuilder result = new StringBuilder();
            try
            {
                Connect(path);
                reqFTP.Method = WRMethods;
                WebResponse response = reqFTP.GetResponse();
                StreamReader reader = new StreamReader(response.GetResponseStream(), System.Text.Encoding.UTF8);//中文文件名
                string line = reader.ReadLine();
                while (line != null)
                {
                    result.Append(line);
                    result.Append("\n");
                    line = reader.ReadLine();
                }
                // to remove the trailing '\n'
                result.Remove(result.ToString().LastIndexOf('\n'), 1);
                reader.Close();
                response.Close();
                return result.ToString().Split('\n');
            }
            catch (Exception ex)
            {
                downloadFiles = null;
                return downloadFiles;
            }
        }

        public string[] GetFileList(string path)//上面的代码示例了如何从ftp服务器上获得文件列表
        {
            return GetFileList("ftp://" + ftpServerIP + "/" + path, WebRequestMethods.Ftp.ListDirectory);
        }

        public string[] GetFileList()//上面的代码示例了如何从ftp服务器上获得文件列表
        {
            return GetFileList("ftp://" + ftpServerIP + "/", WebRequestMethods.Ftp.ListDirectory);
        }

        //获得文件大小
        public long GetFileSize(string filename)
        {

            long fileSize = 0;
            try
            {
                FileInfo fileInf = new FileInfo(filename);
                string uri = "ftp://" + ftpServerIP + "/" + fileInf.Name;
                Connect(uri);//连接      
                reqFTP.Method = WebRequestMethods.Ftp.GetFileSize;
                FtpWebResponse response = (FtpWebResponse)reqFTP.GetResponse();
                fileSize = response.ContentLength;
                response.Close();
            }
            catch (Exception ex)
            {
                
            }
            return fileSize;
        }

        //获得文件明晰
        public string[] GetFilesDetailList()
        {
            return GetFileList("ftp://" + ftpServerIP + "/", WebRequestMethods.Ftp.ListDirectoryDetails);
        }

        //获得文件明晰
        public string[] GetFilesDetailList(string path)
        {
            return GetFileList("ftp://" + ftpServerIP + "/" + path, WebRequestMethods.Ftp.ListDirectoryDetails);
        }
        #endregion

        #region 文件重命名
        public int Rename(string currentFilename, string newFilename)
        {
            try
            {
                FileInfo fileInf = new FileInfo(currentFilename);
                string uri = "ftp://" + ftpServerIP + "/" + fileInf.Name;
                Connect(uri);//连接
                reqFTP.Method = WebRequestMethods.Ftp.Rename;
                reqFTP.RenameTo = newFilename;

                FtpWebResponse response = (FtpWebResponse)reqFTP.GetResponse();
                response.Close();
                return 0;
            }
            catch (Exception ex)
            {
               return -1;
            }
        }
        #endregion
    }
}
