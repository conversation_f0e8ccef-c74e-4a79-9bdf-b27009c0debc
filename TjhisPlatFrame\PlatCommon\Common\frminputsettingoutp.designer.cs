﻿namespace PlatCommon.Common
{
    partial class frminputsettingoutp
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frminputsettingoutp));
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.gridControl2 = new DevExpress.XtraGrid.GridControl();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gITEM_NAME = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridBATCH_NO = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gITEM_SPEC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gUNITS = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridQUANTITY = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDEPT_CODE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.deptItemLookUpEdit3 = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.gPRICE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gITEM_CODE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.AAA = new DevExpress.XtraGrid.Columns.GridColumn();
            this.BBB = new DevExpress.XtraGrid.Columns.GridColumn();
            this.DRUG_FORM = new DevExpress.XtraGrid.Columns.GridColumn();
            this.INPUT_CODE_WB = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gdITEM_CLASS = new DevExpress.XtraGrid.Columns.GridColumn();
            this.XZSYFW = new DevExpress.XtraGrid.Columns.GridColumn();
            this.SUPPLY_INDICATOR = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemLookUpEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.repositoryItemSearchLookUpEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemSearchLookUpEdit();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemGridLookUpEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.repositoryItemLookUpEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.repositoryItemMemoExEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoExEdit();
            this.repositoryItemSearchLookUpEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemSearchLookUpEdit();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ItemCheckEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.textEdit1 = new DevExpress.XtraEditors.TextEdit();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.B0 = new DevExpress.XtraEditors.SimpleButton();
            this.Bclear = new DevExpress.XtraEditors.SimpleButton();
            this.Backspace = new DevExpress.XtraEditors.SimpleButton();
            this.Bspace = new DevExpress.XtraEditors.SimpleButton();
            this.B9 = new DevExpress.XtraEditors.SimpleButton();
            this.B8 = new DevExpress.XtraEditors.SimpleButton();
            this.B7 = new DevExpress.XtraEditors.SimpleButton();
            this.B6 = new DevExpress.XtraEditors.SimpleButton();
            this.B5 = new DevExpress.XtraEditors.SimpleButton();
            this.B4 = new DevExpress.XtraEditors.SimpleButton();
            this.B3 = new DevExpress.XtraEditors.SimpleButton();
            this.X = new DevExpress.XtraEditors.SimpleButton();
            this.C = new DevExpress.XtraEditors.SimpleButton();
            this.V = new DevExpress.XtraEditors.SimpleButton();
            this.B = new DevExpress.XtraEditors.SimpleButton();
            this.N = new DevExpress.XtraEditors.SimpleButton();
            this.M = new DevExpress.XtraEditors.SimpleButton();
            this.B2 = new DevExpress.XtraEditors.SimpleButton();
            this.Z = new DevExpress.XtraEditors.SimpleButton();
            this.L = new DevExpress.XtraEditors.SimpleButton();
            this.B1 = new DevExpress.XtraEditors.SimpleButton();
            this.S = new DevExpress.XtraEditors.SimpleButton();
            this.D = new DevExpress.XtraEditors.SimpleButton();
            this.F = new DevExpress.XtraEditors.SimpleButton();
            this.G = new DevExpress.XtraEditors.SimpleButton();
            this.H = new DevExpress.XtraEditors.SimpleButton();
            this.J = new DevExpress.XtraEditors.SimpleButton();
            this.K = new DevExpress.XtraEditors.SimpleButton();
            this.A = new DevExpress.XtraEditors.SimpleButton();
            this.O = new DevExpress.XtraEditors.SimpleButton();
            this.P = new DevExpress.XtraEditors.SimpleButton();
            this.W = new DevExpress.XtraEditors.SimpleButton();
            this.E = new DevExpress.XtraEditors.SimpleButton();
            this.R = new DevExpress.XtraEditors.SimpleButton();
            this.T = new DevExpress.XtraEditors.SimpleButton();
            this.Y = new DevExpress.XtraEditors.SimpleButton();
            this.U = new DevExpress.XtraEditors.SimpleButton();
            this.I = new DevExpress.XtraEditors.SimpleButton();
            this.Q = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.deptItemLookUpEdit3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemLookUpEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSearchLookUpEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemLookUpEdit2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoExEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSearchLookUpEdit2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemCheckEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            this.SuspendLayout();
            // 
            // panelControl2
            // 
            this.panelControl2.Controls.Add(this.gridControl2);
            this.panelControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelControl2.Location = new System.Drawing.Point(0, 119);
            this.panelControl2.Name = "panelControl2";
            this.panelControl2.Size = new System.Drawing.Size(958, 325);
            this.panelControl2.TabIndex = 1;
            // 
            // gridControl2
            // 
            this.gridControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl2.Location = new System.Drawing.Point(2, 2);
            this.gridControl2.MainView = this.gridView3;
            this.gridControl2.Name = "gridControl2";
            this.gridControl2.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemLookUpEdit1,
            this.repositoryItemSearchLookUpEdit1,
            this.repositoryItemGridLookUpEdit2,
            this.repositoryItemLookUpEdit2,
            this.repositoryItemMemoExEdit1,
            this.repositoryItemSearchLookUpEdit2,
            this.ItemCheckEdit1,
            this.deptItemLookUpEdit3});
            this.gridControl2.Size = new System.Drawing.Size(954, 321);
            this.gridControl2.TabIndex = 5;
            this.gridControl2.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView3,
            this.gridView2});
            // 
            // gridView3
            // 
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gITEM_NAME,
            this.gridBATCH_NO,
            this.gITEM_SPEC,
            this.gUNITS,
            this.gridQUANTITY,
            this.colDEPT_CODE,
            this.gPRICE,
            this.gridColumn1,
            this.gITEM_CODE,
            this.AAA,
            this.BBB,
            this.DRUG_FORM,
            this.INPUT_CODE_WB,
            this.gdITEM_CLASS,
            this.XZSYFW,
            this.SUPPLY_INDICATOR,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18});
            this.gridView3.GridControl = this.gridControl2;
            this.gridView3.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.None, null, null, "")});
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsView.ShowGroupPanel = false;
            this.gridView3.Tag = "frminputsettingoutp";
            this.gridView3.KeyDown += new System.Windows.Forms.KeyEventHandler(this.gridView3_KeyDown);
            this.gridView3.MouseDown += new System.Windows.Forms.MouseEventHandler(this.gridView3_MouseDown);
            this.gridView3.DoubleClick += new System.EventHandler(this.gridView3_DoubleClick);
            // 
            // gITEM_NAME
            // 
            this.gITEM_NAME.AppearanceCell.Options.UseTextOptions = true;
            this.gITEM_NAME.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gITEM_NAME.AppearanceHeader.Options.UseTextOptions = true;
            this.gITEM_NAME.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gITEM_NAME.Caption = "名称";
            this.gITEM_NAME.FieldName = "ITEM_NAME";
            this.gITEM_NAME.Name = "gITEM_NAME";
            this.gITEM_NAME.OptionsColumn.AllowEdit = false;
            this.gITEM_NAME.Visible = true;
            this.gITEM_NAME.VisibleIndex = 0;
            this.gITEM_NAME.Width = 133;
            // 
            // gridBATCH_NO
            // 
            this.gridBATCH_NO.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBATCH_NO.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBATCH_NO.Caption = "批次";
            this.gridBATCH_NO.FieldName = "BATCH_NO";
            this.gridBATCH_NO.Name = "gridBATCH_NO";
            this.gridBATCH_NO.OptionsColumn.AllowEdit = false;
            this.gridBATCH_NO.OptionsColumn.AllowFocus = false;
            this.gridBATCH_NO.Visible = true;
            this.gridBATCH_NO.VisibleIndex = 1;
            this.gridBATCH_NO.Width = 74;
            // 
            // gITEM_SPEC
            // 
            this.gITEM_SPEC.AppearanceHeader.Options.UseTextOptions = true;
            this.gITEM_SPEC.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gITEM_SPEC.Caption = "规格";
            this.gITEM_SPEC.FieldName = "ITEM_SPEC";
            this.gITEM_SPEC.Name = "gITEM_SPEC";
            this.gITEM_SPEC.OptionsColumn.AllowEdit = false;
            this.gITEM_SPEC.OptionsColumn.AllowFocus = false;
            this.gITEM_SPEC.Visible = true;
            this.gITEM_SPEC.VisibleIndex = 2;
            this.gITEM_SPEC.Width = 152;
            // 
            // gUNITS
            // 
            this.gUNITS.AppearanceHeader.Options.UseTextOptions = true;
            this.gUNITS.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gUNITS.Caption = "单位";
            this.gUNITS.FieldName = "UNITS";
            this.gUNITS.Name = "gUNITS";
            this.gUNITS.OptionsColumn.AllowEdit = false;
            this.gUNITS.Visible = true;
            this.gUNITS.VisibleIndex = 3;
            this.gUNITS.Width = 31;
            // 
            // gridQUANTITY
            // 
            this.gridQUANTITY.AppearanceHeader.Options.UseTextOptions = true;
            this.gridQUANTITY.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridQUANTITY.Caption = "库存";
            this.gridQUANTITY.FieldName = "QUANTITY";
            this.gridQUANTITY.Name = "gridQUANTITY";
            this.gridQUANTITY.OptionsColumn.AllowEdit = false;
            this.gridQUANTITY.OptionsColumn.AllowFocus = false;
            this.gridQUANTITY.Visible = true;
            this.gridQUANTITY.VisibleIndex = 4;
            this.gridQUANTITY.Width = 39;
            // 
            // colDEPT_CODE
            // 
            this.colDEPT_CODE.AppearanceHeader.Options.UseTextOptions = true;
            this.colDEPT_CODE.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colDEPT_CODE.Caption = "科室代码";
            this.colDEPT_CODE.ColumnEdit = this.deptItemLookUpEdit3;
            this.colDEPT_CODE.FieldName = "PERFORMED_BY";
            this.colDEPT_CODE.Name = "colDEPT_CODE";
            this.colDEPT_CODE.OptionsColumn.AllowEdit = false;
            this.colDEPT_CODE.OptionsColumn.AllowFocus = false;
            this.colDEPT_CODE.Visible = true;
            this.colDEPT_CODE.VisibleIndex = 5;
            this.colDEPT_CODE.Width = 81;
            // 
            // deptItemLookUpEdit3
            // 
            this.deptItemLookUpEdit3.AutoHeight = false;
            this.deptItemLookUpEdit3.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.deptItemLookUpEdit3.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("DEPT_NAME", "科室名称"),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("DEPT_CODE", "科室代码")});
            this.deptItemLookUpEdit3.Name = "deptItemLookUpEdit3";
            // 
            // gPRICE
            // 
            this.gPRICE.AppearanceHeader.Options.UseTextOptions = true;
            this.gPRICE.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gPRICE.Caption = "价格";
            this.gPRICE.FieldName = "PRICE";
            this.gPRICE.Name = "gPRICE";
            this.gPRICE.OptionsColumn.AllowEdit = false;
            this.gPRICE.Visible = true;
            this.gPRICE.VisibleIndex = 6;
            this.gPRICE.Width = 35;
            // 
            // gridColumn1
            // 
            this.gridColumn1.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn1.Caption = "拼音码";
            this.gridColumn1.FieldName = "INPUT_CODE";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowEdit = false;
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 7;
            this.gridColumn1.Width = 47;
            // 
            // gITEM_CODE
            // 
            this.gITEM_CODE.AppearanceHeader.Options.UseTextOptions = true;
            this.gITEM_CODE.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gITEM_CODE.Caption = "代码";
            this.gITEM_CODE.FieldName = "ITEM_CODE";
            this.gITEM_CODE.Name = "gITEM_CODE";
            this.gITEM_CODE.OptionsColumn.AllowEdit = false;
            this.gITEM_CODE.Visible = true;
            this.gITEM_CODE.VisibleIndex = 8;
            this.gITEM_CODE.Width = 53;
            // 
            // AAA
            // 
            this.AAA.AppearanceHeader.Options.UseTextOptions = true;
            this.AAA.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.AAA.Caption = "剂量";
            this.AAA.FieldName = "AAA";
            this.AAA.Name = "AAA";
            this.AAA.OptionsColumn.AllowEdit = false;
            this.AAA.Visible = true;
            this.AAA.VisibleIndex = 9;
            this.AAA.Width = 57;
            // 
            // BBB
            // 
            this.BBB.AppearanceHeader.Options.UseTextOptions = true;
            this.BBB.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.BBB.Caption = "剂量单位";
            this.BBB.FieldName = "BBB";
            this.BBB.Name = "BBB";
            this.BBB.OptionsColumn.AllowEdit = false;
            this.BBB.Visible = true;
            this.BBB.VisibleIndex = 10;
            this.BBB.Width = 57;
            // 
            // DRUG_FORM
            // 
            this.DRUG_FORM.AppearanceHeader.Options.UseTextOptions = true;
            this.DRUG_FORM.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.DRUG_FORM.Caption = "剂型";
            this.DRUG_FORM.FieldName = "DRUG_FORM";
            this.DRUG_FORM.Name = "DRUG_FORM";
            this.DRUG_FORM.OptionsColumn.AllowEdit = false;
            this.DRUG_FORM.Visible = true;
            this.DRUG_FORM.VisibleIndex = 11;
            this.DRUG_FORM.Width = 57;
            // 
            // INPUT_CODE_WB
            // 
            this.INPUT_CODE_WB.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            this.INPUT_CODE_WB.AppearanceCell.Options.UseBackColor = true;
            this.INPUT_CODE_WB.AppearanceHeader.Options.UseTextOptions = true;
            this.INPUT_CODE_WB.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.INPUT_CODE_WB.Caption = "五笔码";
            this.INPUT_CODE_WB.FieldName = "INPUT_CODE_WB";
            this.INPUT_CODE_WB.Name = "INPUT_CODE_WB";
            this.INPUT_CODE_WB.OptionsColumn.AllowEdit = false;
            this.INPUT_CODE_WB.Width = 211;
            // 
            // gdITEM_CLASS
            // 
            this.gdITEM_CLASS.AppearanceHeader.Options.UseTextOptions = true;
            this.gdITEM_CLASS.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gdITEM_CLASS.Caption = "类别";
            this.gdITEM_CLASS.FieldName = "ITEM_CLASS";
            this.gdITEM_CLASS.Name = "gdITEM_CLASS";
            // 
            // XZSYFW
            // 
            this.XZSYFW.AppearanceHeader.Options.UseTextOptions = true;
            this.XZSYFW.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.XZSYFW.Caption = "限制用药";
            this.XZSYFW.FieldName = "XZSYFW";
            this.XZSYFW.Name = "XZSYFW";
            this.XZSYFW.OptionsColumn.AllowEdit = false;
            this.XZSYFW.OptionsColumn.AllowFocus = false;
            this.XZSYFW.Visible = true;
            this.XZSYFW.VisibleIndex = 12;
            this.XZSYFW.Width = 120;
            // 
            // SUPPLY_INDICATOR
            // 
            this.SUPPLY_INDICATOR.AppearanceHeader.Options.UseTextOptions = true;
            this.SUPPLY_INDICATOR.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.SUPPLY_INDICATOR.Caption = "供应标志";
            this.SUPPLY_INDICATOR.FieldName = "SUPPLY_INDICATOR";
            this.SUPPLY_INDICATOR.Name = "SUPPLY_INDICATOR";
            this.SUPPLY_INDICATOR.OptionsColumn.AllowEdit = false;
            this.SUPPLY_INDICATOR.Visible = true;
            this.SUPPLY_INDICATOR.VisibleIndex = 13;
            this.SUPPLY_INDICATOR.Width = 57;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "国家编码";
            this.gridColumn15.FieldName = "GJBM";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 14;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "国家名称";
            this.gridColumn16.FieldName = "GJMC";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 15;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "省级编码";
            this.gridColumn17.FieldName = "SJBM";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 16;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "省级名称";
            this.gridColumn18.FieldName = "SJMC";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 17;
            // 
            // repositoryItemLookUpEdit1
            // 
            this.repositoryItemLookUpEdit1.AutoHeight = false;
            this.repositoryItemLookUpEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemLookUpEdit1.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("FieldName", "FieldName"),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("FieldValue", "Name5")});
            this.repositoryItemLookUpEdit1.Name = "repositoryItemLookUpEdit1";
            // 
            // repositoryItemSearchLookUpEdit1
            // 
            this.repositoryItemSearchLookUpEdit1.AutoHeight = false;
            this.repositoryItemSearchLookUpEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemSearchLookUpEdit1.Name = "repositoryItemSearchLookUpEdit1";
            this.repositoryItemSearchLookUpEdit1.PopupFormMinSize = new System.Drawing.Size(800, 800);
            this.repositoryItemSearchLookUpEdit1.PopupView = this.gridView5;
            // 
            // gridView5
            // 
            this.gridView5.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn6,
            this.gridColumn5,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10});
            this.gridView5.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn2
            // 
            this.gridColumn2.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn2.Caption = "名称";
            this.gridColumn2.FieldName = "EX_NAME";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 0;
            this.gridColumn2.Width = 100;
            // 
            // gridColumn3
            // 
            this.gridColumn3.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn3.Caption = "代码";
            this.gridColumn3.FieldName = "EX_CODE";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 1;
            this.gridColumn3.Width = 50;
            // 
            // gridColumn4
            // 
            this.gridColumn4.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn4.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn4.Caption = "包装规格";
            this.gridColumn4.FieldName = "PACKAGE_SPEC";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 2;
            this.gridColumn4.Width = 31;
            // 
            // gridColumn6
            // 
            this.gridColumn6.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn6.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn6.Caption = "包装单位";
            this.gridColumn6.FieldName = "PACKAGE_SPEC";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            this.gridColumn6.Width = 31;
            // 
            // gridColumn5
            // 
            this.gridColumn5.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn5.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn5.Caption = "基本规格";
            this.gridColumn5.FieldName = "EX_SPEC";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 3;
            this.gridColumn5.Width = 31;
            // 
            // gridColumn7
            // 
            this.gridColumn7.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn7.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn7.Caption = "基本单位";
            this.gridColumn7.FieldName = "UNITS";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 4;
            this.gridColumn7.Width = 31;
            // 
            // gridColumn8
            // 
            this.gridColumn8.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn8.Caption = "输入码";
            this.gridColumn8.FieldName = "INPUT_CODE";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 6;
            this.gridColumn8.Width = 46;
            // 
            // gridColumn9
            // 
            this.gridColumn9.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn9.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn9.Caption = "五笔";
            this.gridColumn9.FieldName = "INPUT_CODE_WB";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 7;
            this.gridColumn9.Width = 25;
            // 
            // gridColumn10
            // 
            this.gridColumn10.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn10.Caption = "库房代码";
            this.gridColumn10.FieldName = "STORAGE_CODE";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Width = 39;
            // 
            // repositoryItemGridLookUpEdit2
            // 
            this.repositoryItemGridLookUpEdit2.AutoHeight = false;
            this.repositoryItemGridLookUpEdit2.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemGridLookUpEdit2.Name = "repositoryItemGridLookUpEdit2";
            this.repositoryItemGridLookUpEdit2.PopupView = this.repositoryItemGridLookUpEdit1View;
            // 
            // repositoryItemGridLookUpEdit1View
            // 
            this.repositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit1View.Name = "repositoryItemGridLookUpEdit1View";
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            // 
            // repositoryItemLookUpEdit2
            // 
            this.repositoryItemLookUpEdit2.AutoHeight = false;
            this.repositoryItemLookUpEdit2.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemLookUpEdit2.Name = "repositoryItemLookUpEdit2";
            // 
            // repositoryItemMemoExEdit1
            // 
            this.repositoryItemMemoExEdit1.AutoHeight = false;
            this.repositoryItemMemoExEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemMemoExEdit1.Name = "repositoryItemMemoExEdit1";
            this.repositoryItemMemoExEdit1.ShowIcon = false;
            // 
            // repositoryItemSearchLookUpEdit2
            // 
            this.repositoryItemSearchLookUpEdit2.AutoHeight = false;
            this.repositoryItemSearchLookUpEdit2.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemSearchLookUpEdit2.Name = "repositoryItemSearchLookUpEdit2";
            this.repositoryItemSearchLookUpEdit2.PopupView = this.gridView4;
            // 
            // gridView4
            // 
            this.gridView4.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14});
            this.gridView4.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "科室名称";
            this.gridColumn11.FieldName = "DEPT_NAME";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 0;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "科室编码";
            this.gridColumn12.FieldName = "DEPT_CODE";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 1;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "拼音码";
            this.gridColumn13.FieldName = "INPUT_CODE";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 2;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "五笔码";
            this.gridColumn14.FieldName = "INPUT_CODE_WB";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 3;
            // 
            // ItemCheckEdit1
            // 
            this.ItemCheckEdit1.AutoHeight = false;
            this.ItemCheckEdit1.Name = "ItemCheckEdit1";
            this.ItemCheckEdit1.ValueChecked = "1";
            this.ItemCheckEdit1.ValueUnchecked = "0";
            // 
            // gridView2
            // 
            this.gridView2.GridControl = this.gridControl2;
            this.gridView2.Name = "gridView2";
            // 
            // textEdit1
            // 
            this.textEdit1.Location = new System.Drawing.Point(2, 5);
            this.textEdit1.Name = "textEdit1";
            this.textEdit1.Size = new System.Drawing.Size(327, 20);
            this.textEdit1.TabIndex = 6;
            this.textEdit1.EditValueChanged += new System.EventHandler(this.textEdit1_EditValueChanged);
            this.textEdit1.KeyDown += new System.Windows.Forms.KeyEventHandler(this.textEdit1_KeyDown);
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.B0);
            this.panelControl1.Controls.Add(this.Bclear);
            this.panelControl1.Controls.Add(this.Backspace);
            this.panelControl1.Controls.Add(this.Bspace);
            this.panelControl1.Controls.Add(this.B9);
            this.panelControl1.Controls.Add(this.B8);
            this.panelControl1.Controls.Add(this.B7);
            this.panelControl1.Controls.Add(this.B6);
            this.panelControl1.Controls.Add(this.B5);
            this.panelControl1.Controls.Add(this.B4);
            this.panelControl1.Controls.Add(this.B3);
            this.panelControl1.Controls.Add(this.X);
            this.panelControl1.Controls.Add(this.C);
            this.panelControl1.Controls.Add(this.V);
            this.panelControl1.Controls.Add(this.B);
            this.panelControl1.Controls.Add(this.N);
            this.panelControl1.Controls.Add(this.M);
            this.panelControl1.Controls.Add(this.B2);
            this.panelControl1.Controls.Add(this.Z);
            this.panelControl1.Controls.Add(this.L);
            this.panelControl1.Controls.Add(this.B1);
            this.panelControl1.Controls.Add(this.S);
            this.panelControl1.Controls.Add(this.D);
            this.panelControl1.Controls.Add(this.F);
            this.panelControl1.Controls.Add(this.G);
            this.panelControl1.Controls.Add(this.H);
            this.panelControl1.Controls.Add(this.J);
            this.panelControl1.Controls.Add(this.K);
            this.panelControl1.Controls.Add(this.A);
            this.panelControl1.Controls.Add(this.O);
            this.panelControl1.Controls.Add(this.P);
            this.panelControl1.Controls.Add(this.W);
            this.panelControl1.Controls.Add(this.E);
            this.panelControl1.Controls.Add(this.R);
            this.panelControl1.Controls.Add(this.T);
            this.panelControl1.Controls.Add(this.Y);
            this.panelControl1.Controls.Add(this.U);
            this.panelControl1.Controls.Add(this.I);
            this.panelControl1.Controls.Add(this.Q);
            this.panelControl1.Controls.Add(this.textEdit1);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControl1.Location = new System.Drawing.Point(0, 0);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(958, 119);
            this.panelControl1.TabIndex = 7;
            this.panelControl1.Paint += new System.Windows.Forms.PaintEventHandler(this.panelControl1_Paint);
            // 
            // B0
            // 
            this.B0.Location = new System.Drawing.Point(409, 92);
            this.B0.Name = "B0";
            this.B0.Size = new System.Drawing.Size(38, 23);
            this.B0.TabIndex = 45;
            this.B0.Text = "0";
            this.B0.Click += new System.EventHandler(this.B0_Click);
            // 
            // Bclear
            // 
            this.Bclear.Location = new System.Drawing.Point(335, 4);
            this.Bclear.Name = "Bclear";
            this.Bclear.Size = new System.Drawing.Size(97, 23);
            this.Bclear.TabIndex = 44;
            this.Bclear.Text = "清除";
            this.Bclear.Click += new System.EventHandler(this.Bclear_Click);
            // 
            // Backspace
            // 
            this.Backspace.Location = new System.Drawing.Point(321, 92);
            this.Backspace.Name = "Backspace";
            this.Backspace.Size = new System.Drawing.Size(82, 23);
            this.Backspace.TabIndex = 43;
            this.Backspace.Text = "Backspace";
            this.Backspace.Click += new System.EventHandler(this.Backspace_Click);
            // 
            // Bspace
            // 
            this.Bspace.Location = new System.Drawing.Point(409, 64);
            this.Bspace.Name = "Bspace";
            this.Bspace.Size = new System.Drawing.Size(38, 23);
            this.Bspace.TabIndex = 42;
            this.Bspace.Text = "空格";
            this.Bspace.Click += new System.EventHandler(this.Bspace_Click);
            // 
            // B9
            // 
            this.B9.Location = new System.Drawing.Point(541, 92);
            this.B9.Name = "B9";
            this.B9.Size = new System.Drawing.Size(38, 23);
            this.B9.TabIndex = 41;
            this.B9.Text = "9";
            this.B9.Click += new System.EventHandler(this.B9_Click);
            // 
            // B8
            // 
            this.B8.Location = new System.Drawing.Point(497, 92);
            this.B8.Name = "B8";
            this.B8.Size = new System.Drawing.Size(38, 23);
            this.B8.TabIndex = 40;
            this.B8.Text = "8";
            this.B8.Click += new System.EventHandler(this.B8_Click);
            // 
            // B7
            // 
            this.B7.Location = new System.Drawing.Point(453, 92);
            this.B7.Name = "B7";
            this.B7.Size = new System.Drawing.Size(38, 23);
            this.B7.TabIndex = 39;
            this.B7.Text = "7";
            this.B7.Click += new System.EventHandler(this.B7_Click);
            // 
            // B6
            // 
            this.B6.Location = new System.Drawing.Point(541, 64);
            this.B6.Name = "B6";
            this.B6.Size = new System.Drawing.Size(38, 23);
            this.B6.TabIndex = 38;
            this.B6.Text = "6";
            this.B6.Click += new System.EventHandler(this.B6_Click);
            // 
            // B5
            // 
            this.B5.Location = new System.Drawing.Point(497, 63);
            this.B5.Name = "B5";
            this.B5.Size = new System.Drawing.Size(38, 23);
            this.B5.TabIndex = 37;
            this.B5.Text = "5";
            this.B5.Click += new System.EventHandler(this.B5_Click);
            // 
            // B4
            // 
            this.B4.Location = new System.Drawing.Point(453, 64);
            this.B4.Name = "B4";
            this.B4.Size = new System.Drawing.Size(38, 23);
            this.B4.TabIndex = 36;
            this.B4.Text = "4";
            this.B4.Click += new System.EventHandler(this.B4_Click);
            // 
            // B3
            // 
            this.B3.Location = new System.Drawing.Point(541, 33);
            this.B3.Name = "B3";
            this.B3.Size = new System.Drawing.Size(38, 23);
            this.B3.TabIndex = 35;
            this.B3.Text = "3";
            this.B3.Click += new System.EventHandler(this.B3_Click);
            // 
            // X
            // 
            this.X.Location = new System.Drawing.Point(57, 92);
            this.X.Name = "X";
            this.X.Size = new System.Drawing.Size(38, 23);
            this.X.TabIndex = 34;
            this.X.Text = "X";
            this.X.Click += new System.EventHandler(this.X_Click);
            // 
            // C
            // 
            this.C.Location = new System.Drawing.Point(101, 92);
            this.C.Name = "C";
            this.C.Size = new System.Drawing.Size(38, 23);
            this.C.TabIndex = 33;
            this.C.Text = "C";
            this.C.Click += new System.EventHandler(this.C_Click);
            // 
            // V
            // 
            this.V.Location = new System.Drawing.Point(145, 92);
            this.V.Name = "V";
            this.V.Size = new System.Drawing.Size(38, 23);
            this.V.TabIndex = 32;
            this.V.Text = "V";
            this.V.Click += new System.EventHandler(this.V_Click);
            // 
            // B
            // 
            this.B.Location = new System.Drawing.Point(189, 92);
            this.B.Name = "B";
            this.B.Size = new System.Drawing.Size(38, 23);
            this.B.TabIndex = 31;
            this.B.Text = "B";
            this.B.Click += new System.EventHandler(this.B_Click);
            // 
            // N
            // 
            this.N.Location = new System.Drawing.Point(233, 92);
            this.N.Name = "N";
            this.N.Size = new System.Drawing.Size(38, 23);
            this.N.TabIndex = 30;
            this.N.Text = "N";
            this.N.Click += new System.EventHandler(this.N_Click);
            // 
            // M
            // 
            this.M.Location = new System.Drawing.Point(277, 92);
            this.M.Name = "M";
            this.M.Size = new System.Drawing.Size(38, 23);
            this.M.TabIndex = 29;
            this.M.Text = "M";
            this.M.Click += new System.EventHandler(this.M_Click);
            // 
            // B2
            // 
            this.B2.Location = new System.Drawing.Point(497, 32);
            this.B2.Name = "B2";
            this.B2.Size = new System.Drawing.Size(38, 23);
            this.B2.TabIndex = 28;
            this.B2.Text = "2";
            this.B2.Click += new System.EventHandler(this.B2_Click);
            // 
            // Z
            // 
            this.Z.Location = new System.Drawing.Point(13, 92);
            this.Z.Name = "Z";
            this.Z.Size = new System.Drawing.Size(38, 23);
            this.Z.TabIndex = 27;
            this.Z.Text = "Z";
            this.Z.Click += new System.EventHandler(this.Z_Click);
            // 
            // L
            // 
            this.L.Location = new System.Drawing.Point(365, 64);
            this.L.Name = "L";
            this.L.Size = new System.Drawing.Size(38, 23);
            this.L.TabIndex = 26;
            this.L.Text = "L";
            this.L.Click += new System.EventHandler(this.L_Click);
            // 
            // B1
            // 
            this.B1.Location = new System.Drawing.Point(453, 33);
            this.B1.Name = "B1";
            this.B1.Size = new System.Drawing.Size(38, 23);
            this.B1.TabIndex = 25;
            this.B1.Text = "1";
            this.B1.Click += new System.EventHandler(this.B1_Click);
            // 
            // S
            // 
            this.S.Location = new System.Drawing.Point(57, 63);
            this.S.Name = "S";
            this.S.Size = new System.Drawing.Size(38, 23);
            this.S.TabIndex = 24;
            this.S.Text = "S";
            this.S.Click += new System.EventHandler(this.S_Click);
            // 
            // D
            // 
            this.D.Location = new System.Drawing.Point(101, 63);
            this.D.Name = "D";
            this.D.Size = new System.Drawing.Size(38, 23);
            this.D.TabIndex = 23;
            this.D.Text = "D";
            this.D.Click += new System.EventHandler(this.D_Click);
            // 
            // F
            // 
            this.F.Location = new System.Drawing.Point(145, 63);
            this.F.Name = "F";
            this.F.Size = new System.Drawing.Size(38, 23);
            this.F.TabIndex = 22;
            this.F.Text = "F";
            this.F.Click += new System.EventHandler(this.F_Click);
            // 
            // G
            // 
            this.G.Location = new System.Drawing.Point(189, 63);
            this.G.Name = "G";
            this.G.Size = new System.Drawing.Size(38, 23);
            this.G.TabIndex = 21;
            this.G.Text = "G";
            this.G.Click += new System.EventHandler(this.G_Click);
            // 
            // H
            // 
            this.H.Location = new System.Drawing.Point(233, 63);
            this.H.Name = "H";
            this.H.Size = new System.Drawing.Size(38, 23);
            this.H.TabIndex = 20;
            this.H.Text = "H";
            this.H.Click += new System.EventHandler(this.H_Click);
            // 
            // J
            // 
            this.J.Location = new System.Drawing.Point(277, 63);
            this.J.Name = "J";
            this.J.Size = new System.Drawing.Size(38, 23);
            this.J.TabIndex = 19;
            this.J.Text = "J";
            this.J.Click += new System.EventHandler(this.J_Click);
            // 
            // K
            // 
            this.K.Location = new System.Drawing.Point(321, 63);
            this.K.Name = "K";
            this.K.Size = new System.Drawing.Size(38, 23);
            this.K.TabIndex = 18;
            this.K.Text = "K";
            this.K.Click += new System.EventHandler(this.K_Click);
            // 
            // A
            // 
            this.A.Location = new System.Drawing.Point(13, 63);
            this.A.Name = "A";
            this.A.Size = new System.Drawing.Size(38, 23);
            this.A.TabIndex = 17;
            this.A.Text = "A";
            this.A.Click += new System.EventHandler(this.A_Click);
            // 
            // O
            // 
            this.O.Location = new System.Drawing.Point(365, 33);
            this.O.Name = "O";
            this.O.Size = new System.Drawing.Size(38, 23);
            this.O.TabIndex = 16;
            this.O.Text = "O";
            this.O.Click += new System.EventHandler(this.O_Click);
            // 
            // P
            // 
            this.P.Location = new System.Drawing.Point(409, 33);
            this.P.Name = "P";
            this.P.Size = new System.Drawing.Size(38, 23);
            this.P.TabIndex = 15;
            this.P.Text = "P";
            this.P.Click += new System.EventHandler(this.P_Click);
            // 
            // W
            // 
            this.W.Location = new System.Drawing.Point(57, 32);
            this.W.Name = "W";
            this.W.Size = new System.Drawing.Size(38, 23);
            this.W.TabIndex = 14;
            this.W.Text = "W";
            this.W.Click += new System.EventHandler(this.W_Click);
            // 
            // E
            // 
            this.E.Location = new System.Drawing.Point(101, 32);
            this.E.Name = "E";
            this.E.Size = new System.Drawing.Size(38, 23);
            this.E.TabIndex = 13;
            this.E.Text = "E";
            this.E.Click += new System.EventHandler(this.E_Click);
            // 
            // R
            // 
            this.R.Location = new System.Drawing.Point(145, 32);
            this.R.Name = "R";
            this.R.Size = new System.Drawing.Size(38, 23);
            this.R.TabIndex = 12;
            this.R.Text = "R";
            this.R.Click += new System.EventHandler(this.R_Click);
            // 
            // T
            // 
            this.T.Location = new System.Drawing.Point(189, 32);
            this.T.Name = "T";
            this.T.Size = new System.Drawing.Size(38, 23);
            this.T.TabIndex = 11;
            this.T.Text = "T";
            this.T.Click += new System.EventHandler(this.T_Click);
            // 
            // Y
            // 
            this.Y.Location = new System.Drawing.Point(233, 32);
            this.Y.Name = "Y";
            this.Y.Size = new System.Drawing.Size(38, 23);
            this.Y.TabIndex = 10;
            this.Y.Text = "Y";
            this.Y.Click += new System.EventHandler(this.Y_Click);
            // 
            // U
            // 
            this.U.Location = new System.Drawing.Point(277, 32);
            this.U.Name = "U";
            this.U.Size = new System.Drawing.Size(38, 23);
            this.U.TabIndex = 9;
            this.U.Text = "U";
            this.U.Click += new System.EventHandler(this.U_Click);
            // 
            // I
            // 
            this.I.Location = new System.Drawing.Point(321, 32);
            this.I.Name = "I";
            this.I.Size = new System.Drawing.Size(38, 23);
            this.I.TabIndex = 8;
            this.I.Text = "I";
            this.I.Click += new System.EventHandler(this.I_Click);
            // 
            // Q
            // 
            this.Q.Location = new System.Drawing.Point(13, 32);
            this.Q.Name = "Q";
            this.Q.Size = new System.Drawing.Size(38, 23);
            this.Q.TabIndex = 7;
            this.Q.Text = "Q";
            this.Q.Click += new System.EventHandler(this.Q_Click);
            // 
            // frminputsettingoutp
            // 
            this.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(239)))));
            this.Appearance.Options.UseBackColor = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(958, 444);
            this.Controls.Add(this.panelControl2);
            this.Controls.Add(this.panelControl1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "frminputsettingoutp";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "双击选择项目";
            this.Load += new System.EventHandler(this.frminputsetting_Load);
            this.Shown += new System.EventHandler(this.frminputsetting_Shown);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frminputsettingoutp_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.deptItemLookUpEdit3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemLookUpEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSearchLookUpEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemLookUpEdit2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoExEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSearchLookUpEdit2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemCheckEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraGrid.GridControl gridControl2;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gITEM_NAME;
        private DevExpress.XtraGrid.Columns.GridColumn gITEM_CODE;
        private DevExpress.XtraGrid.Columns.GridColumn gITEM_SPEC;
        private DevExpress.XtraGrid.Columns.GridColumn gUNITS;
        private DevExpress.XtraGrid.Columns.GridColumn gPRICE;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn INPUT_CODE_WB;
        private DevExpress.XtraGrid.Columns.GridColumn colDEPT_CODE;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repositoryItemLookUpEdit1;
        private DevExpress.XtraEditors.Repository.RepositoryItemSearchLookUpEdit repositoryItemSearchLookUpEdit1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repositoryItemGridLookUpEdit2;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit1View;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repositoryItemLookUpEdit2;
        private DevExpress.XtraEditors.Repository.RepositoryItemMemoExEdit repositoryItemMemoExEdit1;
        private DevExpress.XtraEditors.Repository.RepositoryItemSearchLookUpEdit repositoryItemSearchLookUpEdit2;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit ItemCheckEdit1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gdITEM_CLASS;
        private DevExpress.XtraGrid.Columns.GridColumn AAA;
        private DevExpress.XtraGrid.Columns.GridColumn BBB;
        private DevExpress.XtraEditors.TextEdit textEdit1;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit deptItemLookUpEdit3;
        private DevExpress.XtraGrid.Columns.GridColumn gridBATCH_NO;
        private DevExpress.XtraGrid.Columns.GridColumn gridQUANTITY;
        private DevExpress.XtraEditors.SimpleButton Q;
        private DevExpress.XtraEditors.SimpleButton W;
        private DevExpress.XtraEditors.SimpleButton E;
        private DevExpress.XtraEditors.SimpleButton R;
        private DevExpress.XtraEditors.SimpleButton T;
        private DevExpress.XtraEditors.SimpleButton Y;
        private DevExpress.XtraEditors.SimpleButton U;
        private DevExpress.XtraEditors.SimpleButton I;
        private DevExpress.XtraEditors.SimpleButton B3;
        private DevExpress.XtraEditors.SimpleButton X;
        private DevExpress.XtraEditors.SimpleButton C;
        private DevExpress.XtraEditors.SimpleButton V;
        private DevExpress.XtraEditors.SimpleButton B;
        private DevExpress.XtraEditors.SimpleButton N;
        private DevExpress.XtraEditors.SimpleButton M;
        private DevExpress.XtraEditors.SimpleButton B2;
        private DevExpress.XtraEditors.SimpleButton Z;
        private DevExpress.XtraEditors.SimpleButton L;
        private DevExpress.XtraEditors.SimpleButton B1;
        private DevExpress.XtraEditors.SimpleButton S;
        private DevExpress.XtraEditors.SimpleButton D;
        private DevExpress.XtraEditors.SimpleButton F;
        private DevExpress.XtraEditors.SimpleButton G;
        private DevExpress.XtraEditors.SimpleButton H;
        private DevExpress.XtraEditors.SimpleButton J;
        private DevExpress.XtraEditors.SimpleButton K;
        private DevExpress.XtraEditors.SimpleButton A;
        private DevExpress.XtraEditors.SimpleButton O;
        private DevExpress.XtraEditors.SimpleButton P;
        private DevExpress.XtraEditors.SimpleButton B0;
        private DevExpress.XtraEditors.SimpleButton Bclear;
        private DevExpress.XtraEditors.SimpleButton Backspace;
        private DevExpress.XtraEditors.SimpleButton Bspace;
        private DevExpress.XtraEditors.SimpleButton B9;
        private DevExpress.XtraEditors.SimpleButton B8;
        private DevExpress.XtraEditors.SimpleButton B7;
        private DevExpress.XtraEditors.SimpleButton B6;
        private DevExpress.XtraEditors.SimpleButton B5;
        private DevExpress.XtraEditors.SimpleButton B4;
        private DevExpress.XtraGrid.Columns.GridColumn DRUG_FORM;
        private DevExpress.XtraGrid.Columns.GridColumn SUPPLY_INDICATOR;
        private DevExpress.XtraGrid.Columns.GridColumn XZSYFW;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
    }
}