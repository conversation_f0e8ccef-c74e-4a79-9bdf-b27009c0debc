﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using System.Threading;

namespace TJEmrToHtml
{
    static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                ThreadExceptionHandler handler = new ThreadExceptionHandler();
                Application.ThreadException += new ThreadExceptionEventHandler(handler.Application_ThreadException);
                AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(handler.CurrentDomain_UnhandledException);
                Environment.CurrentDirectory = System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetAssembly(typeof(Program)).CodeBase).Replace("file:\\", "");

                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new Form1());
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
        }
        internal class ThreadExceptionHandler
        {
            public void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
            {
                try
                {
                    if (MessageBox.Show(e.Exception.Message.ToString() + " 详细请看错误日志") == DialogResult.OK)
                    {
                        MessageBox.Show(e.Exception.Message);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                    Application.Exit();
                }
            }

            public void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
            {
                try
                {
                    Exception exception = e.ExceptionObject as Exception;
                    if (exception == null)
                    {
                        return;
                    }
                    MessageBox.Show(exception.Message);
                    AppDomain.Unload(AppDomain.CurrentDomain);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }
            }
        }
    }
}
