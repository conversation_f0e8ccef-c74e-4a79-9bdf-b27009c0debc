﻿//-----------------------------------------------------------------------
//  系统名称        : 基础类库
//  子系统名称      : DataTable 帮助类
//  功能概要        : DataSet操作类
//  作  者          : YinZhiwei
//  创建时间        : 2023-8-1
//  版本            : 1.0.0.0
//-----------------------------------------------------------------------
using System;
using System.Data;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Xml;
using System.Text.RegularExpressions;
using System.Reflection;

namespace PlatCommon.Base02
{
    /// <summary>
    /// DataTable帮助类
    /// </summary>	
    public class DataTableHelper
    {
        /// <summary>
		/// 构造函数
		/// </summary>
        private DataTableHelper()
		{
        }

        /// <summary>
        /// 判断数据表数据是否发生变化
        /// </summary>        
        public static bool HasChanged(DataTable dataTable)
        {
            bool result = false;
            if (dataTable == null)
            {
                return false;
            }
            foreach (DataRow dr in dataTable.Rows)
            {
                if (dr.RowState != DataRowState.Unchanged)
                {
                    result = true;
                    break;
                }
            }
            return result;
        }
 
    }
}
