﻿/*-----------------------------------------------------------------------
 * 类名称    ：Interface1
 * 类描述    ：
 * 创建人    ：梁吉lions
 * 创建时间  ：2016/9/18 10:22:50
 * 修改人    ：
 * 修改时间  ：
 * 修改备注  ：
 * 版本      ：
 * ----------------------------------------------------------------------
 */
using System;
using System.Data;
using System.ServiceModel;
namespace INMService
{
    /// <summary>
    /// 接口层BJCA_INTERFACE
    /// </summary>
    [ServiceContract]
    public interface IBJCA_INTERFACE
    {
        #region  成员方法
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        [OperationContract]
        bool Exists_BJCA_INTERFACE(string BJCA_ID);
        /// <summary>
        /// 增加一条数据
        /// </summary>
        [OperationContract]
        bool Add_BJCA_INTERFACE(Model.BJCA_INTERFACE model);
        /// <summary>
        /// 更新一条数据
        /// </summary>
        [OperationContract]
        bool Update_BJCA_INTERFACE(Model.BJCA_INTERFACE model);
        /// <summary>
        /// 删除数据
        /// </summary>
        [OperationContract]
        bool Delete_BJCA_INTERFACE(string BJCA_ID,string HIS_ID);
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        [OperationContract]
        Model.BJCA_INTERFACE GetModel_BJCA_INTERFACE(string BJCA_ID);
        /// <summary>
        /// 获得数据列表
        /// </summary>
        [OperationContract]
        DataSet GetList_All_BJCA_INTERFACE(string strWhere);
        /// <summary>
        /// 获得前几行数据
        /// </summary>
        [OperationContract]
        DataSet GetList_BJCA_INTERFACE(int startIndex, int endIndex, string strWhere, string filedOrder);
        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.BJCA_INTERFACE> GetObservableCollection_All_BJCA_INTERFACE(string strWhere);
        /// <summary>
        /// 获得ObservableCollection根据分页获得数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.BJCA_INTERFACE> GetObservableCollection_BJCA_INTERFACE(int startIndex, int endIndex, string strWhere, string filedOrder);
        #endregion  成员方法
    }
}