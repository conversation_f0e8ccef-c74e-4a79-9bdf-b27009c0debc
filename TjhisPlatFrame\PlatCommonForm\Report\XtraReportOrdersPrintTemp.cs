﻿using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using System.Data;
using DevExpress.XtraPrinting;
using PlatCommon.SysBase;

namespace PlatCommonForm.Report
{
    public partial class XtraReportOrdersPrintTemp : DevExpress.XtraReports.UI.XtraReport
    {
        int curpage;
        int irepeat;
        string head_print;

        public XtraReportOrdersPrintTemp()
        {
            InitializeComponent();
        }
        public XtraReportOrdersPrintTemp(DataSet ds, DataSet pat, int temp_curpage, int repeat, string is_head_print)
        {
            curpage = temp_curpage;
            irepeat = repeat;
            head_print = is_head_print;
            InitializeComponent();
            SetPrintDate(ds, pat);
        }

        public void SetPrintDate(DataSet ds, DataSet pat)
        {
            //读取参数
            string canshu = PlatCommon.SysBase.SystemParm.GetParaValue("E_SIGNATURE", SystemParm.AppName, "*", "*", "0");
            if (PlatCommon.SysBase.SystemParm.HospitalID.Equals("桓仁满族自治县人民医院"))
            {
                //dhl桓仁，偶尔出现电子签名打的是机打字体，未测试出来先这么写死了
                canshu = "1";
            }
            if (canshu == "1")
            {
                string caused = PlatCommon.SysBase.SystemParm.GetParaValue("CA_USED", SystemParm.AppName, "*", "*", "0");
                if (PlatCommon.SysBase.SystemParm.HospitalID.Equals("桓仁满族自治县人民医院"))
                {
                    //dhl桓仁，偶尔出现电子签名打的是机打字体，未测试出来先这么写死了
                    caused = "1";
                }
                //add kangjia 打印电子签名
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                string sql = "";
                DataSet dsc = null;
                DataRow drc = null;
                this.xrPictureBox1.Visible = true;
                this.xrPictureBox2.Visible = true;
                for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
                {
                    string pic = ds.Tables[0].Rows[i]["NURSE3"].ToString();
                    if (!string.IsNullOrEmpty(pic))
                    {
                        if ("1".Equals(caused))//辽宁CA
                            sql = "select CA_IMG  AS NURSE3 from hisinterface.lnca_interface t where t.his_id = '" + pic + "'";
                        else
                            sql = "select BJCA_IMG AS NURSE3 from comm.bjca_interface WHERE HIS_ID='" + pic + "'";
                        dsc = spc.GetDataBySql(sql);
                        if (dsc.Tables[0].Rows.Count > 0)
                        {
                            drc = (DataRow)dsc.Tables[0].Rows[0];
                            byte[] byteImg = (byte[])drc["NURSE3"];
                            System.IO.MemoryStream ms = new System.IO.MemoryStream(byteImg);
                            System.Drawing.Image img = System.Drawing.Image.FromStream(ms);
                            ds.Tables[0].Rows[i]["nurse_pic1"] = img;
                        }
                    }
                    pic = ds.Tables[0].Rows[i]["NURSE4"].ToString();
                    if (!string.IsNullOrEmpty(pic))
                    {
                        if ("1".Equals(caused))//辽宁CA
                            sql = "select CA_IMG  AS NURSE4 from hisinterface.lnca_interface t where t.his_id = '" + pic + "'";
                        else
                            sql = "select BJCA_IMG AS NURSE4 from comm.bjca_interface WHERE HIS_ID='" + pic + "'";
                        dsc = spc.GetDataBySql(sql);
                        if (dsc.Tables[0].Rows.Count > 0)
                        {
                            drc = (DataRow)dsc.Tables[0].Rows[0];
                            byte[] byteImg = (byte[])drc["NURSE4"];
                            System.IO.MemoryStream ms = new System.IO.MemoryStream(byteImg);
                            System.Drawing.Image img = System.Drawing.Image.FromStream(ms);
                            ds.Tables[0].Rows[i]["nurse_pic2"] = img;
                        }
                    }

                    pic = ds.Tables[0].Rows[i]["DOCTOR_CODE"].ToString();
                    if (!string.IsNullOrEmpty(pic))
                    {
                        if ("1".Equals(caused))//辽宁CA
                            sql = "select CA_IMG  AS DOCTORPIC from hisinterface.lnca_interface t where t.his_id = '" + pic + "'";
                        else
                            sql = "select BJCA_IMG AS DOCTORPIC from comm.bjca_interface WHERE HIS_ID='" + pic + "'";
                        dsc = spc.GetDataBySql(sql);
                        if (dsc.Tables[0].Rows.Count > 0)
                        {
                            drc = (DataRow)dsc.Tables[0].Rows[0];
                            byte[] byteImg = (byte[])drc["DOCTORPIC"];
                            System.IO.MemoryStream ms = new System.IO.MemoryStream(byteImg);
                            System.Drawing.Image img = System.Drawing.Image.FromStream(ms);
                            ds.Tables[0].Rows[i]["DOCTOR_PIC"] = img;
                        }
                    }

                    Bitmap[] bmps = new Bitmap[2];
                    if (ds.Tables[0].Rows[i]["nurse_pic1"] != DBNull.Value)
                    {
                        bmps[0] = (Bitmap)ds.Tables[0].Rows[i]["nurse_pic1"];
                    }
                    if (ds.Tables[0].Rows[i]["nurse_pic2"] != DBNull.Value)
                    {
                        bmps[1] = (Bitmap)ds.Tables[0].Rows[i]["nurse_pic2"];
                    }

                    ds.Tables[0].Rows[i]["nurse_pic"] = MergerImg(bmps);
                }

                this.xrPictureBox1.Sizing = ImageSizeMode.ZoomImage;//设置电子签名尺寸
                this.xrPictureBox2.Sizing = ImageSizeMode.ZoomImage;
            }
            //add kangjia 打印电子签名
            this.DataSource = ds;
            if (ds.Tables[0].Rows.Count > 0)
            {
                this.date.DataBindings.Add("Text", DataSource, "DATE");
                this.time.DataBindings.Add("Text", DataSource, "TIME");
                this.order_text.DataBindings.Add("Text", DataSource, "ORDER_TEXT");
                this.joint_line.DataBindings.Add("Text", DataSource, "JOINT_LINE");
                this.doctor.DataBindings.Add("Text", DataSource, "DOCTOR");
                this.verify_datetime.DataBindings.Add("Text", DataSource, "PERFORM_SCHEDULE");
                this.nurse.DataBindings.Add("Text", DataSource, "EXE_NURSE_NAME");
                this.rowNum.DataBindings.Add("Text", DataSource, "ROWNUM");
                this.xrTableCell1.DataBindings.Add("Text", DataSource, "administration");
                this.xrTableCell2.DataBindings.Add("Text", DataSource, "DOSAGE_DOSAGE_UNITS");
                this.xrTableCell3.DataBindings.Add("Text", DataSource, "FREQUENCY");
                this.xrPictureBox1.DataBindings.Add("Image", DataSource, "nurse_pic");
                this.xrPictureBox2.DataBindings.Add("Image", DataSource, "DOCTOR_PIC");
            }
            if (pat.Tables[0].Rows.Count > 0)
            {
                this.name.Text = pat.Tables[0].Rows[0]["NAME"].ToString();
                this.dept.Text = pat.Tables[0].Rows[0]["DEPT_NAME"].ToString();
                this.bed_no.Text = pat.Tables[0].Rows[0]["BED_LABEL"].ToString();
                this.inp_no.Text = pat.Tables[0].Rows[0]["INP_NO"].ToString();
                this.id.Text = pat.Tables[0].Rows[0]["PATIENT_ID"].ToString();
                this.t_page.Text = curpage.ToString();
            }
            hospital_name.Text = PlatCommon.SysBase.SystemParm.HospitalID;

            if (head_print.Equals("0")) //不显示表格
            {
                this.t_page.Visible = false;
                this.name.Visible = false;
                this.t_name.Visible = false;
                this.dept.Visible = false;
                this.t_dept.Visible = false;
                this.bed_no.Visible = false;
                this.t_bed_no.Visible = false;
                this.inp_no.Visible = false;
                this.t_inp_no.Visible = false;
                this.id.Visible = false;
                this.t_id.Visible = false;

                hospital_name.Text = "";
                orders_type.Text = "";
                t_date.Visible = false;
                t_time.Visible = false;
                t_doctor.Visible = false;
                t_verify_datetime.Visible = false;
                t_order_text.Visible = false;
                t_nurse.Visible = false;
                this.date.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.time.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.order_text.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.joint_line.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.xrTableCell1.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.xrTableCell2.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.xrTableCell3.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.doctor.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.verify_datetime.Borders = DevExpress.XtraPrinting.BorderSide.None;
                this.nurse.Borders = DevExpress.XtraPrinting.BorderSide.None;

            }
            if (head_print.Equals("1")) //显示表格
            {
                this.t_page.Visible = true;
                this.name.Visible = true;
                this.t_name.Visible = true;
                this.dept.Visible = true;
                this.t_dept.Visible = true;
                this.bed_no.Visible = true;
                this.t_bed_no.Visible = true;
                this.inp_no.Visible = true;
                this.t_inp_no.Visible = true;
                this.id.Visible = true;
                this.t_id.Visible = true;

                hospital_name.Visible = true;
                //orders_type.Visible = true;
                t_date.Visible = true;
                t_time.Visible = true;
                t_doctor.Visible = true;
                t_verify_datetime.Visible = true;
                t_order_text.Visible = true;
                t_nurse.Visible = true;
                this.xrTableCell1.Borders = ((DevExpress.XtraPrinting.BorderSide)(( DevExpress.XtraPrinting.BorderSide.Bottom)));
                this.xrTableCell2.Borders = ((DevExpress.XtraPrinting.BorderSide)(( DevExpress.XtraPrinting.BorderSide.Bottom)));
                this.xrTableCell3.Borders = ((DevExpress.XtraPrinting.BorderSide)(( DevExpress.XtraPrinting.BorderSide.Bottom)));
                this.date.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left )
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
                this.time.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left )
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
                this.order_text.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left )
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
                this.joint_line.Borders = ((DevExpress.XtraPrinting.BorderSide)(( DevExpress.XtraPrinting.BorderSide.Bottom)));
                this.doctor.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left )
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
                this.verify_datetime.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left )
            | DevExpress.XtraPrinting.BorderSide.Bottom)));

                this.nurse.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left )
            | DevExpress.XtraPrinting.BorderSide.Bottom | DevExpress.XtraPrinting.BorderSide.Right)));
                   
            }
        }

        /// <summary>
        /// 合并图片
        /// </summary>
        /// <param name="maps"></param>
        /// <returns></returns>
        private Bitmap MergerImg(params Bitmap[] maps)
        {
            if (maps.Length == 0)
            {
                throw new Exception("图片数不能够为0");
            }
            //创建要显示的图片对象,根据参数的个数设置宽度
            int width = 0;
            int height = 0;
            bool bl=false;
            for (int i = 0; i < maps.Length; i++)
            {
                if (maps[i] == null)
                {
                    continue;
                }
                bl=true;
                width = width + maps[i].Width;
                height = height + maps[i].Height;
            }
            if (bl == false) return null;
            Bitmap backgroudImg = new Bitmap(width, height);
            Graphics g = Graphics.FromImage(backgroudImg);
            //清除画布,背景设置为白色
            g.Clear(System.Drawing.Color.White);
            int beginx = 0;
            int beginy = 0;
            for (int j = 0; j < maps.Length; j++)
            {
                if (maps[j] == null)
                {
                    continue;
                }
                g.DrawImage(maps[j], beginx, beginy, maps[j].Width, maps[j].Height);
                beginy = beginy + maps[j].Height;
            }
            g.Dispose();
            return backgroudImg;
        }
    }
}
