﻿using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Nodes;
using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Tjhis.Report.Custom.Srv;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmCreateSQL : ParentForm
    {
           srvT srv = new srvT();

        Dictionary<string,FrmColumnView> tablesList;//存放打开的表的小窗体
        public string reportId { get; set; }    //报表ID

        public delegate void DelegateUseSQL(string sqlStr);
        public event DelegateUseSQL UseSqlEvent;

        List<Point> emptyPoint = new List<Point>();//存放空白限制位置
        public frmCreateSQL()
        {
            InitializeComponent();
        }

        private void frmCreateSQL_Load(object sender, EventArgs e)
        {
            lupOraUsers.Properties.DataSource = srv.GetUsers().Tables[0];

            InitTreeView(treeTables);

            treeTables.DataSource = srv.GetUserTables("").Tables[0];

            tablesList = new Dictionary<string, FrmColumnView>();

            InitTreeList();
            
        }

        /// <summary>
        /// 初始化树控件
        /// </summary>
        /// <param name="treeView"></param>
        public static void InitTreeView(DevExpress.XtraTreeList.TreeList treeView)
        {
            treeView.OptionsView.ShowColumns = false;
            treeView.OptionsView.ShowIndicator = false;
            treeView.OptionsView.ShowVertLines = false;
            treeView.OptionsView.ShowHorzLines = false;
            treeView.OptionsBehavior.Editable = false;
            treeView.OptionsSelection.EnableAppearanceFocusedCell = false;
            treeView.KeyFieldName = "TABLE_NAME";
            treeView.ParentFieldName = "OWNER";
        }
        
        TreeListNode selectNode;
        TreeListNode fromNode;
        TreeListNode whereNode;
        TreeListNode orderNode;
        TreeListNode groupNode;
        TreeListNode unionNode;//暂时没有思路，闲置中。。。。。。
        /// <summary>
        /// 初始化查询节点
        /// </summary>
        private void InitTreeList()
        {
            selectNode = treeList1.Nodes.Add();
            selectNode.SetValue(treeList1.Columns[treeListColumn3.Name],"SELECT");
            selectNode.Nodes.Add("","","", "<按这里添加字段>", "","","","","column");

            fromNode = treeList1.AppendNode(null, null);
            fromNode.SetValue(treeList1.Columns[treeListColumn3.Name], "FROM");
            fromNode.Nodes.Add("", "", "", "<按这里添加表>", "", "", "", "", "table");

            whereNode = treeList1.AppendNode(null, null);
            whereNode.SetValue(treeList1.Columns[treeListColumn3.Name], "WHERE");
            whereNode.Nodes.Add("", "", "", "<按这里添加条件>", "", "", "", "", "where");

            groupNode = treeList1.AppendNode(null, null);
            groupNode.SetValue(treeList1.Columns[treeListColumn3.Name], "GROUP BY");
            groupNode.Nodes.Add("", "", "", "<按这里添加分组>", "", "", "", "", "group");

            orderNode = treeList1.AppendNode(null, null);
            orderNode.SetValue(treeList1.Columns[treeListColumn3.Name], "ORDER BY");
            orderNode.Nodes.Add("", "", "", "<按这里添加排序>", "", "", "", "", "order");

            treeList1.ExpandAll();
        }
        /// <summary>
        /// 打开表字段的小窗体
        /// </summary>
        /// <param name="tableName"></param>
        private void openView(string tableName)
        {
            tableName = tableName.ToUpper();
            //如果已存在则跳过
            if (tablesList.ContainsKey(tableName))
            {
                tablesList[tableName].TopMost = true;
                tablesList[tableName].Focus();
                return;
            }
            
            FrmColumnView cv = new FrmColumnView(tableName);
            cv.TopLevel = false;
            
            if (emptyPoint.Count == 0)
                cv.Location = new Point(cv.Location.X + (tablesList.Count * cv.Width)+(tablesList.Count * 2), 2);
            else
            {
                cv.Location = emptyPoint[0];
                emptyPoint.Remove(emptyPoint[0]);
            }
            panel_tableList.Controls.Add(cv);
            cv.Visible = true;
            cv.FormClosed += Cv_FormClosed;
            cv.ColumnChangeEvent += Cv_ColumnChangeEvent;
            tablesList.Add(tableName, cv);

            //添加成功后在form节点中添加表节点
            TreeListNode sNode = fromNode.Nodes.ToList().Find(n => n.GetValue(treeListColumn6.Name).Equals(tableName));
            if (sNode == null)
            {
                TreeListNode addNode = fromNode.Nodes.Add("", "", "", tableName, "", "", "", "", "tables");

                treeList1.BeginUpdate();
                int prevNodeIndex = treeList1.GetNodeIndex(addNode.PrevNode);
                treeList1.SetNodeIndex(addNode, prevNodeIndex);
                treeList1.EndUpdate();

                if (addNode.PrevNode != null)
                {
                    addNode.PrevNode.SetValue(treeListColumn9.Name, ",");
                }
            }
            else
            {
                if (sNode.PrevNode != null && sNode.NextNode.NextNode == null)
                {
                    sNode.PrevNode.SetValue(treeListColumn9.Name, "");
                }
                fromNode.Nodes.Remove(sNode);
            }
        }
        /// <summary>
        /// 表字段选择状态更改后，与之对应的节点做删减或增加动作
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Cv_ColumnChangeEvent(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            DataRow dr = ((DevExpress.XtraGrid.Views.Grid.GridView)sender).GetDataRow(e.RowHandle);

            List<TreeListNode> sNode = selectNode.Nodes.ToList().FindAll(n => n.GetValue(treeListColumn6.Name).Equals(dr["table_name"] + "." + dr["column_name"]));
            
            if (e.Value.Equals("1"))//如果是选中状态
            {
                if(sNode.Count == 0)//如果节点中不存在则添加
                {
                    TreeListNode addNode = selectNode.Nodes.Add("", "", "[<func>]", dr["table_name"]+"."+dr["column_name"], "", "[<别名>]", "", "", "columns");
                    
                    ChangeNodeIndex(addNode.PrevNode, addNode);

                    if (addNode.PrevNode != null)
                    {
                        addNode.PrevNode.SetValue(treeListColumn9.Name, ",");
                    }
                }
            }
            else//取消选中
            {
                if (sNode.Count > 0) //如果节点中存在则删除
                {
                    foreach(TreeListNode node in sNode)
                    {
                        if (node.PrevNode != null && node.NextNode.NextNode == null)
                        {
                            node.PrevNode.SetValue(treeListColumn9.Name, ""); //如果有上一节点则去结尾逗号
                        }
                        selectNode.Nodes.Remove(node);
                    }
                }
            }
        }

        /// <summary>
        /// 表字段小窗体关闭事件--移除对应的表和字段节点
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Cv_FormClosed(object sender, FormClosedEventArgs e)
        {
            emptyPoint.Add(((FrmColumnView)sender).Location);
            tablesList.Remove(((FrmColumnView)sender).Text);

            //移除关闭表所关联的所有字段
            foreach(DataRow dr in ((FrmColumnView)sender).dtCols.Rows)
            {
                List<TreeListNode> filed = selectNode.Nodes.ToList().FindAll(n => n.GetValue(treeListColumn6.Name).Equals(dr["table_name"] + "." + dr["column_name"]));//字段节点
                RemoveColumns(filed);
                filed = whereNode.Nodes.ToList().FindAll(n => n.GetValue(treeListColumn6.Name).Equals(dr["table_name"] + "." + dr["column_name"]));//条件节点
                RemoveColumns(filed);
                filed = groupNode.Nodes.ToList().FindAll(n => n.GetValue(treeListColumn6.Name).Equals(dr["table_name"] + "." + dr["column_name"]));//分组节点
                RemoveColumns(filed);
                filed = orderNode.Nodes.ToList().FindAll(n => n.GetValue(treeListColumn6.Name).Equals(dr["table_name"] + "." + dr["column_name"]));//排序节点
                RemoveColumns(filed);
            }
            //如果已经添加表，移除
            TreeListNode table = fromNode.Nodes.ToList().Find(n => n.GetValue(treeListColumn6.Name).Equals(((FrmColumnView)sender).Text));
            if (table != null)
            {
                if (table.PrevNode != null && table.NextNode.NextNode == null)
                {
                    table.PrevNode.SetValue(treeListColumn9.Name, "");
                }
                selectNode.Nodes.Remove(table);
            }
            //如果打开的表数量为零，则清除空位置列表从新开始
            if (tablesList.Count == 0)
            {
                emptyPoint.Clear();
            }
        }
        /// <summary>
        /// 移除节点
        /// </summary>
        /// <param name="filed">节点List</param>
        private void RemoveColumns(List<TreeListNode> filed)
        {
            if (filed != null)
            {
                foreach (TreeListNode fNode in filed)
                {
                    object flag = fNode.GetValue(treeListColumn11.Name);
                    if (fNode.PrevNode != null)
                    {
                        flag = fNode.PrevNode.GetValue(treeListColumn11.Name);
                        if (flag != null && flag.ToString().Equals("wheres_"))
                        {
                            fNode.PrevNode.Remove();
                        }
                    }
                    if (fNode.PrevNode != null && fNode.NextNode.NextNode == null)
                    {
                        fNode.PrevNode.SetValue(treeListColumn9.Name, "");
                    }

                    flag = fNode.GetValue(treeListColumn11.Name);
                    if (flag != null && flag.ToString().Equals("columns"))
                    {
                        string frmKey = fNode.GetValue(treeListColumn6.Name).ToString();
                        string[] splitStr = frmKey.Split('.');
                        if (tablesList.ContainsKey(splitStr[0]))
                        {
                            tablesList[splitStr[0]].SetFiledStatus(splitStr[1], "");
                        }
                    }
                    
                    selectNode.Nodes.Remove(fNode);
                }
            }
        }
        /// <summary>
        /// 用户下拉框值更改后重新绑定表数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lupOraUsers_EditValueChanged(object sender, EventArgs e)
        {
            treeTables.DataSource = srv.GetUserTables(lupOraUsers.EditValue.ToString()).Tables[0];
        }
        /// <summary>
        /// 表数据节点双击
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void treeTables_DoubleClick(object sender, EventArgs e)
        {
            DataRow dr = treeTables.GetFocusedDataRow();
            openView(dr["TABLE_NAME"].ToString());
        }
        /// <summary>
        /// 查询节点开始编辑
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void treeList1_ShowingEditor(object sender, CancelEventArgs e)
        {
            TreeListNode node = treeList1.FocusedNode;
            object flag = node.GetValue(treeList1.Columns[treeListColumn11.Name]);
            object flag1 = node.GetValue(treeList1.Columns[treeListColumn3.Name]);
            //禁止点击添加的节点编辑
            if (flag != null && (flag.Equals("table") || flag.Equals("column") || flag.Equals("where") || flag.Equals("order") || flag.Equals("group")))
            {
                e.Cancel = true;
            }
            //禁止主节点编辑
            if (flag1 != null && (flag1.Equals("SELECT") || flag1.Equals("FROM") || flag1.Equals("WHERE") || flag1.Equals("ORDER BY") || flag1.Equals("GROUP BY")))
            {
                e.Cancel = true;
            }
        }
        
        private frmSelectedFiled OpenSelectFiledForm(List<DataTable> lSource, bool isShowList = false, string result = "")
        {
            frmSelectedFiled fsf = new frmSelectedFiled();
            fsf.DataSource = lSource;
            fsf.result = result;
            fsf.showList = true;
            fsf.StartPosition = FormStartPosition.Manual;
            fsf.Location = MousePosition;
            return fsf;
        }
        /// <summary>
        /// 打开选择器
        /// </summary>
        /// <param name="lSource">选择器数据源</param>
        /// <param name="dialogResult">选择器窗体结果</param>
        /// <param name="result">选择器选中值</param>
        /// <param name="isShowList">true显示列表，false显示编辑</param>
        /// <param name="resultStr">编辑默认显示值</param>
        /// <returns></returns>
        private frmSelectedFiled OpenSelectFiledForm(List<DataTable> lSource, out DialogResult dialogResult, out string result, bool isShowList = false, string resultStr = "")
        {
            frmSelectedFiled fsf = new frmSelectedFiled();
            fsf.DataSource = lSource;
            fsf.showList = true;
            fsf.result = resultStr;
            fsf.panelPoint = MousePosition;
            dialogResult = fsf.ShowDialog();
            result = fsf.result;
            return fsf;
        }
        /// <summary>
        /// 鼠标双击事件 -做添加动作
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void treeList1_MouseClick(object sender, MouseEventArgs e)
        {
            bool isTrue = e.Clicks == 2;
            if (isTrue)
            {
                try
                {
                    TreeListNode node = treeList1.GetNodeAt(e.Location);
                    object flag = node.GetValue(treeList1.Columns[treeListColumn11.Name]);

                    DialogResult dResult;
                    string result;

                    bool isAddCol = (treeList1.FocusedColumn == treeListColumn6);
                    if (flag == null) return;
                    if (flag.Equals("column") && isAddCol)//添加字段
                    {
                        OpenSelectFiledForm(GetAllFiled(), out dResult, out result, true);
                        if (dResult == DialogResult.OK)
                        {
                            AddFiled(result);
                        }
                    }

                    if (flag.Equals("table") && isAddCol)//添加表
                    {
                        DataTable dt = (treeTables.DataSource as DataTable).Copy();
                        dt.Columns["table_name"].ColumnName = "COLUMN_NAME";
                        List<DataTable> l = new List<DataTable>();
                        l.Add(dt);
                        OpenSelectFiledForm(l, out dResult, out result, true);
                        if (dResult == DialogResult.OK)
                        {
                            openView(result);
                        }
                    }

                    if (flag.Equals("where") && isAddCol)//添加条件
                    {
                        AddCondition();
                    }

                    if (flag.Equals("group") && isAddCol)//添加分组
                    {
                        OpenSelectFiledForm(GetAllFiled(), out dResult, out result, true);
                        if (dResult == DialogResult.OK)
                        {
                            AddGroup(result);
                        }
                    }

                    if (flag.Equals("order") && isAddCol)//添加排序
                    {
                        OpenSelectFiledForm(GetAllFiled(), out dResult, out result, true);
                        if (dResult == DialogResult.OK)
                        {
                            AddFiledSort(result, "DESC");
                        }
                    }
                    if (flag != null && (flag.Equals("tables") || flag.Equals("columns") || flag.Equals("wheres") || flag.Equals("orders") || flag.Equals("groups")))
                    {
                        if (treeList1.FocusedColumn == treeListColumn5 || treeList1.FocusedColumn == treeListColumn6 || 
                            treeList1.FocusedColumn == treeListColumn7 || treeList1.FocusedColumn == treeListColumn8 || 
                            treeList1.FocusedColumn == treeListColumn9)
                            ShowSelectedFrom();
                    }
                }
                catch
                {

                }
            }
        }
        /// <summary>
        /// 添加排序字段
        /// </summary>
        /// <param name="result">字段名称</param>
        /// <param name="sort">排序方式 asc desc</param>
        private void AddFiledSort(string result,string sort)
        {
            TreeListNode addNode = orderNode.Nodes.Add("", "", "", result, sort, "", "", "", "orders");

            ChangeNodeIndex(addNode.PrevNode, addNode);

            if (addNode.PrevNode != null)
            {
                addNode.PrevNode.SetValue(treeListColumn9.Name, ",");
            }
        }
        /// <summary>
        /// 添加分组字段
        /// </summary>
        /// <param name="result">字段名称</param>
        private void AddGroup(string result)
        {
            TreeListNode addNode = groupNode.Nodes.Add("", "", "", result, "", "", "", "", "groups");

            ChangeNodeIndex(addNode.PrevNode, addNode);

            if (addNode.PrevNode != null)
            {
                addNode.PrevNode.SetValue(treeListColumn9.Name, ",");
            }
        }
        /// <summary>
        /// 添加空条件
        /// </summary>
        private void AddCondition()
        {
            TreeListNode addNode = whereNode.Nodes.Add("", "", "", "[<---->]", "=", "[<---->]", "", "", "wheres");
            ChangeNodeIndex(addNode.PrevNode, addNode);

            if (addNode.PrevNode != null)
            {
                TreeListNode WhNode = whereNode.Nodes.Add("", "", "", "AND", "", "", "", "", "wheres_");

                ChangeNodeIndex(addNode, WhNode);
            }
        }
        /// <summary>
        /// 添加带参条件
        /// </summary>
        /// <param name="condOne">参数①</param>
        /// <param name="expStr">表达式</param>
        /// <param name="condTwo">参数②</param>
        /// <param name="isAddAnd">是否添加 And 节点</param>
        private void AddCondition(string condOne, string expStr, string condTwo ,bool isAddAnd = false)
        {
            TreeListNode addNode = whereNode.Nodes.Add("", "", "", condOne, expStr, condTwo, "", "", "wheres");
            ChangeNodeIndex(addNode.PrevNode, addNode);

            if(isAddAnd)
                if (addNode.PrevNode != null)
                {
                    TreeListNode WhNode = whereNode.Nodes.Add("", "", "", "AND", "", "", "", "", "wheres_");

                    ChangeNodeIndex(addNode, WhNode);
                }
        }
        /// <summary>
        /// 添加 and 或 or 节点
        /// </summary>
        /// <param name="str"> and 或 or</param>
        private void AddAndOr(string str)
        {
            TreeListNode WhNode = whereNode.Nodes.Add("", "", "", str, "", "", "", "", "wheres_");

            ChangeNodeIndex(WhNode.PrevNode, WhNode);
        }
        /// <summary>
        /// 添加查询字段
        /// </summary>
        /// <param name="result">字段名称</param>
        private void AddFiled(string result)
        {
            string[] tableFiled = result.Split('.');
            if (tablesList.ContainsKey(tableFiled[0]))
            {
                tablesList[tableFiled[0]].SetFiledStatus(tableFiled[1], "1");
            }
            TreeListNode addNode = selectNode.Nodes.Add("", "", "[<func>]", result, "", "[<别名>]", "", "", "columns");

            ChangeNodeIndex(addNode.PrevNode, addNode);

            if (addNode.PrevNode != null)
            {
                addNode.PrevNode.SetValue(treeListColumn9.Name, ",");
            }
        }

        /// <summary>
        /// 鼠标右键弹出菜单
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void treeList1_MouseClick1(object sender, MouseEventArgs e)
        {
            if(e.Button == MouseButtons.Right)
            {
                TreeListHitInfo setSelectNode = treeList1.CalcHitInfo(e.Location);

                if (setSelectNode.Node != null && setSelectNode.Column != null)
                {
                    object flag = setSelectNode.Node.GetValue(treeList1.Columns[treeListColumn11.Name]);
                    object flag1 = setSelectNode.Node.GetValue(treeList1.Columns[treeListColumn3.Name]);

                    setSelectNode.Node.Selected = true;
                    treeList1.FocusedColumn = setSelectNode.Column;

                    if (flag != null && (flag.Equals("tables") || flag.Equals("columns") || flag.Equals("wheres") || flag.Equals("wheres_") || flag.Equals("orders") || flag.Equals("groups")))
                    {
                        if(treeList1.FocusedColumn == treeListColumn6)
                            popupMenu1.ShowPopup(Cursor.Position);
                    }
                    //禁止主节点编辑
                    if (flag1 != null && (flag1.Equals("SELECT") || flag1.Equals("FROM") || flag1.Equals("WHERE") || flag1.Equals("ORDER BY") || flag1.Equals("GROUP BY")))
                    {
                        if (treeList1.FocusedColumn == treeListColumn3)
                            popupMenu1.ShowPopup(Cursor.Position);
                    }
                }
            }
        }
            /// <summary>
            /// 更改节点位置
            /// </summary>
            /// <param name="indexNode">目标节点</param>
            /// <param name="addNode">需移动的节点</param>
            private void ChangeNodeIndex(TreeListNode indexNode, TreeListNode addNode)
        {
            treeList1.BeginUpdate();
            int prevNodeIndex = treeList1.GetNodeIndex(indexNode);
            treeList1.SetNodeIndex(addNode, prevNodeIndex);
            treeList1.EndUpdate();
        }

        /// <summary>
        /// 获取打开的表的所有字段
        /// </summary>
        /// <returns></returns>
        private List<DataTable> GetAllFiled()
        {
            List<DataTable> dtList = new List<DataTable>();
            foreach (string key in tablesList.Keys)
            {
                dtList.Add(tablesList[key].dtCols);
            }
            return dtList;
        }
        /// <summary>
        /// buttonEdit点击事件  --做打开选择器动作
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void repositoryItemButtonEdit1_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            
            
        }
        private void ShowSelectedFrom()
        {
            List<DataTable> dtList = GetAllFiled();
            bool showListPage = false;

            string content = treeList1.GetFocusedRowCellValue(treeList1.FocusedColumn).ToString();
            TreeListNode focusedNode = treeList1.FocusedNode;

            if (focusedNode != null)
            {
                content = focusedNode.GetValue(treeList1.FocusedColumn).ToString();

                object flag = focusedNode.GetValue(treeList1.Columns[treeListColumn11.Name]);
                // columns  tables   wheres  wheres_  orders
                if (flag.Equals("columns"))
                {
                    if (treeList1.FocusedColumn == treeListColumn5)
                    {
                        dtList.Clear();
                        dtList.Add(srvT.CreateDict("SUM;MAX;MIN;AVG;COUNT;[<func>]"));
                        showListPage = true;
                    }
                    if (treeList1.FocusedColumn == treeListColumn8)
                    {
                        //dtList.Add(srvT.CreateDict("SUM;MAX;MIN;AVG;COUNT;[<func>]"));
                    }
                }
                else if (flag.Equals("tables"))
                {
                    dtList.Clear();
                    DataTable dt = (treeTables.DataSource as DataTable).Copy();
                    dt.Columns["table_name"].ColumnName = "COLUMN_NAME";
                    dtList.Add(dt);
                }
                else if (flag.Equals("wheres"))
                {
                    if (treeList1.FocusedColumn == treeListColumn7)
                    {
                        dtList.Clear();
                        dtList.Add(srvT.CreateDict("=;<>;<;<=;>;>=;LIKE;NOT LIKE;IN;NOT IN;IS NULL;IS NOT NULL;BETWEEN;NOT BETWEEN;IN;NOT IN"));
                        showListPage = true;
                    }
                    if (treeList1.FocusedColumn == treeListColumn5 || treeList1.FocusedColumn == treeListColumn9)
                    {
                        dtList.Clear();
                        dtList.Add(srvT.CreateDict("(;); "));
                        showListPage = true;
                    }
                }
                else if (flag.Equals("wheres_"))
                {
                    dtList.Clear();
                    if (treeList1.FocusedColumn == treeListColumn6)
                    {
                        dtList.Add(srvT.CreateDict("AND;OR"));
                        showListPage = true;
                    }
                }
                else if (flag.Equals("orders"))
                {
                    if (treeList1.FocusedColumn == treeListColumn7)
                    {
                        string value = focusedNode.GetValue(treeListColumn7).ToString();
                        if (value.Equals("ASC"))
                        {
                            focusedNode.SetValue(treeListColumn7, "DESC");
                        }
                        else
                        {
                            focusedNode.SetValue(treeListColumn7, "ASC");
                        }
                        return;
                    }
                }
            }

            DialogResult result;
            string strResult;
            OpenSelectFiledForm(dtList, out result, out strResult, showListPage, content);
            if (result == DialogResult.OK)
            {
                if (focusedNode.GetValue(treeList1.Columns[treeListColumn11.Name]).Equals("tables"))
                {
                    string frmKey = focusedNode.GetValue(treeListColumn6.Name).ToString();
                    if (tablesList.ContainsKey(frmKey))
                    {
                        tablesList[frmKey].Close();
                        tablesList.Remove(frmKey);
                    }
                    openView(strResult);
                }
                else
                    treeList1.SetFocusedRowCellValue(treeList1.FocusedColumn, strResult);
            }
        }
        /// <summary>
        /// 双击触发buttonEdit事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void repositoryItemButtonEdit1_DoubleClick(object sender, EventArgs e)
        {
            //repositoryItemButtonEdit1_ButtonClick(null, null);
        }

        /// <summary>
        /// 自定义单元格编辑器  --暂时用处不大
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void treeList1_CustomNodeCellEdit(object sender, DevExpress.XtraTreeList.GetCustomNodeCellEditEventArgs e)
        {
            if (e.Node.GetValue(treeListColumn11.Name) != null && e.Node.GetValue(treeListColumn11.Name).Equals("columns") )
            {
                if(e.Column == treeListColumn5)
                {
                    //e.RepositoryItem = new RepositoryItemCheckEdit();
                }
                else if (e.Column == treeListColumn7)
                {
                    
                }
            }

            TreeListNode node = treeList1.FocusedNode;
            object flag = node.GetValue(treeList1.Columns[treeListColumn11.Name]);
            object flag1 = node.GetValue(treeList1.Columns[treeListColumn3.Name]);

            if ((flag != null && (flag.Equals("table") || flag.Equals("column") || flag.Equals("where") || flag.Equals("order"))) ||(flag1 != null && (flag1.Equals("SELECT") || flag1.Equals("FROM") || flag1.Equals("WHERE") || flag1.Equals("ORDER BY"))))
            {
                e.RepositoryItem = new RepositoryItemTextEdit();
            }
        }

        /// <summary>
        /// 自定义单元格样式
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void treeList1_CustomDrawNodeCell(object sender, DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs e)
        {

            TreeListNode node = e.Node;
            if (e.CellValue != null && e.CellValue.ToString().Contains("[<"))
            {
                e.Appearance.ForeColor = Color.Gray;
            }
            else
            {
                //e.Appearance.ForeColor = Color.Black;
            }


            if (e.Node.GetValue(treeListColumn11.Name) != null && e.Node.GetValue(treeListColumn11.Name).Equals("columns"))
            {
                if (e.Column == treeListColumn5)
                {
                    //e.RepositoryItem = new RepositoryItemCheckEdit();
                }
                else if (e.Column == treeListColumn7)
                {
                    e.Appearance.ForeColor = Color.Blue;
                }
            }

            object flag = node.GetValue(treeList1.Columns[treeListColumn11.Name]);
            if (flag == null) return;
            if (flag.Equals("table") || flag.Equals("column") || flag.Equals("where") || flag.Equals("order") || flag.Equals("group"))
            {
                if (e.Column == treeListColumn6)
                {
                    e.Appearance.ForeColor = Color.Gray;
                }
            }
        }

        /// <summary>
        /// 根据查询节点数，生成Sql
        /// </summary>
        /// <returns></returns>
        private string GetSql()
        {
            if(fromNode.Nodes.Count == 1) { return ""; }

            StringBuilder sql = null;
            bool isGroup = false;
            if (fromNode.Nodes.Count > 1)
            {
                sql = new StringBuilder("SELECT \n");
                if(selectNode.Nodes.Count > 1)
                {
                    for (int i = 0; i < selectNode.Nodes.Count - 1; i++)
                    {
                        TreeListNode node = selectNode.Nodes[i];
                        string func = node.GetValue(treeListColumn5.Name).ToString();
                        string filed = node.GetValue(treeListColumn6.Name).ToString();
                        string nameAs = node.GetValue(treeListColumn8.Name).ToString();
                        string d = node.GetValue(treeListColumn9.Name).ToString();
                        if (!isGroup)
                        {
                            isGroup = (!func.Contains("[<") || func.Contains("[<GP") )&& selectNode.Nodes.Count > 1;
                        }
                        sql.Append("\t" + (func.Contains("[<") ? filed  : func + "(" + filed + ") ") + (nameAs.Contains("[<") ? "" : " AS " + nameAs + " " ) + d + "\n");
                    }
                }
                else
                {
                    sql.Append("\t * \n");
                }
                sql.Append("FROM \n");
                for (int i = 0; i < fromNode.Nodes.Count - 1; i++)
                {
                    TreeListNode node = fromNode.Nodes[i];
                    string func = node.GetValue(treeListColumn5.Name).ToString();
                    string table = node.GetValue(treeListColumn6.Name).ToString();
                    string nameAs = node.GetValue(treeListColumn8.Name).ToString();
                    string d = node.GetValue(treeListColumn9.Name).ToString();

                    sql.Append("\t" + table + " " + d + "\n");
                }
            }
            if (sql != null && whereNode.Nodes.Count > 1)
            {
                sql.Append("WHERE \n");
                for (int i = 0; i < whereNode.Nodes.Count - 1; i++)
                {
                    TreeListNode node = whereNode.Nodes[i];
                    string a = node.GetValue(treeListColumn5.Name).ToString();
                    string b = node.GetValue(treeListColumn7.Name).ToString();
                    string wh1 = node.GetValue(treeListColumn6.Name).ToString();
                    string wh2 = node.GetValue(treeListColumn8.Name).ToString();
                    string d = node.GetValue(treeListColumn9.Name).ToString();
                    if (b.Contains("IS NULL") || b.Contains("IS NOT NULL"))
                        wh2 = "";
                    else
                        wh2 = (wh2.Contains("[<") ? "''" : wh2) ;
                    sql.Append("\t" + a +" " + (wh1.Contains("[<") ? "''" : wh1) + " " + b + " " + wh2 + " " + d + "\n");
                }
            }
            if (sql != null && groupNode.Nodes.Count > 1)//(isGroup)
            {
                sql.Append("GROUP BY \n");
                if (groupNode.Nodes.Count > 1)
                {
                    for (int i = 0; i < groupNode.Nodes.Count - 1; i++)
                    {
                        TreeListNode node = groupNode.Nodes[i];
                        string func = node.GetValue(treeListColumn5.Name).ToString();
                        string filed = node.GetValue(treeListColumn6.Name).ToString();
                        string nameAs = node.GetValue(treeListColumn8.Name).ToString();
                        string d = node.GetValue(treeListColumn9.Name).ToString();
                        
                        sql.Append("\t" +  filed + d + "\n");
                    }
                }
            }
            if (sql != null && orderNode.Nodes.Count > 1)
            {
                sql.Append("ORDER BY \n");
                for (int i = 0; i < orderNode.Nodes.Count - 1; i++)
                {
                    TreeListNode node = orderNode.Nodes[i];
                    string type = node.GetValue(treeListColumn7.Name).ToString();
                    string fild = node.GetValue(treeListColumn6.Name).ToString();
                    string wh2 = node.GetValue(treeListColumn8.Name).ToString();
                    string d = node.GetValue(treeListColumn9.Name).ToString();

                    sql.Append("\t" + fild +" " +type + d + "\n");
                }
            }
            return sql.ToString();
        }
        /// <summary>
        /// 确认按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOk_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Clipboard.SetText(GetSql());   //复制到剪贴板
            XtraMessageBox.Show(GetSql(),"Sql:");//显示生成的Sql
        }

        private void btnCancel_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
        /// <summary>
        /// 右键菜单删除节点
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tsm_deleteNode_Click(object sender, EventArgs e)
        {
            try
            {
                TreeListNode node = treeList1.FocusedNode;
                object flag = node.GetValue(treeList1.Columns[treeListColumn11.Name]);

                if (treeList1.FocusedColumn == treeListColumn3)
                {
                    flag = node.GetValue(treeList1.Columns[treeListColumn3.Name]);
                    List<TreeListNode> removeNode = null;
                    if (flag.Equals("SELECT"))
                    {
                        removeNode = selectNode.Nodes.ToList();
                    }
                    else if(flag.Equals("FROM"))
                    {
                        //removeNode = selectNode.Nodes.ToList();
                        foreach(FrmColumnView fcv in tablesList.Values.ToList())
                        {
                            fcv.Close();
                        }
                        tablesList.Clear();
                        return;
                    }
                    else if (flag.Equals("WHERE"))
                    {
                        removeNode = whereNode.Nodes.ToList();
                    }
                    else if (flag.Equals("GROUP BY"))
                    {
                        removeNode = groupNode.Nodes.ToList();
                    }
                    else if (flag.Equals("ORDER BY"))
                    {
                        removeNode = orderNode.Nodes.ToList();
                    }

                    removeNode.Remove(removeNode[removeNode.Count-1]);
                    RemoveColumns(removeNode);
                }
                if (treeList1.FocusedColumn == treeListColumn6)
                {
                    if (flag == null) return;
                    if (flag.Equals("columns") || flag.Equals("tables") || flag.Equals("orders") || flag.Equals("groups"))
                    {

                        if (flag.Equals("tables"))
                        {
                            string frmKey = node.GetValue(treeListColumn6.Name).ToString();
                            if (tablesList.ContainsKey(frmKey))
                            {
                                tablesList[frmKey].Close();
                                tablesList.Remove(frmKey);
                            }
                        }
                        if (flag.Equals("columns"))
                        {
                            string frmKey = node.GetValue(treeListColumn6.Name).ToString();

                            List<TreeListNode> filed = selectNode.Nodes.ToList().FindAll(n => n.GetValue(treeListColumn6.Name).Equals(frmKey));
                            if (filed != null && filed.Count == 1)
                            {
                                string[] splitStr = frmKey.Split('.');
                                if (tablesList.ContainsKey(splitStr[0]))
                                {
                                    tablesList[splitStr[0]].SetFiledStatus(splitStr[1], "");
                                }
                            }
                        }

                        if (node.PrevNode != null && node.NextNode.NextNode == null)
                        {
                            node.PrevNode.SetValue(treeListColumn9.Name, "");
                        }
                        node.Remove();
                    }

                    if (flag.Equals("wheres") || flag.Equals("wheres_"))
                    {
                        if (node.PrevNode != null)
                        {
                            node.PrevNode.Remove();
                        }
                        else
                        {
                            if (node.NextNode.GetValue(treeListColumn11.Name).Equals("wheres_"))
                            {
                                node.NextNode.Remove();
                            }
                        }

                        node.Remove();
                    }
                }
            }
            catch
            {

            }
        }

        /// <summary>
        /// 查询测试按钮，打开测试查询的窗体
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barBtnQueryTest_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            string sql = GetSql();
            frmSQLQueryTest ft = new frmSQLQueryTest();
            ft.UseSqlEvent += Ft_UseSqlEvent; ;
            ft.sql = sql;

            ft.ShowDialog();
        }

        private void Ft_UseSqlEvent(string sqlStr)
        {
            if (UseSqlEvent != null) 
                UseSqlEvent(sqlStr);
        }

        private void contextMenuStrip1_Opening(object sender, CancelEventArgs e)
        {
            TreeListNode getNode = treeList1.FocusedNode;
            bool isCancel = false;
            if(getNode != null)
            {
                if(treeList1.FocusedColumn != treeListColumn6 && treeList1.FocusedColumn != treeListColumn3)
                {
                    isCancel = true;
                }
                else
                {
                    isCancel = false;
                }
            }
            else
            {
                isCancel = true;
            }
            e.Cancel = isCancel;
        }

        private void barBtnDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            tsm_deleteNode_Click(null, null);
        }

        private void btnUseSQL_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Ft_UseSqlEvent(GetSql());
        }

        bool isMultiSelect;
        private void lupOraUsers_Properties_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            if(e.Button.Kind == DevExpress.XtraEditors.Controls.ButtonPredefines.OK)
            {
                if (isMultiSelect)
                    isMultiSelect = false;
                else
                    isMultiSelect = true;

                lupOraUsers.Properties.PopupView.OptionsSelection.MultiSelect = isMultiSelect;
                //lupOraUsers.Properties.PopupView.OptionsSelection.
            }
        }
    }
}
