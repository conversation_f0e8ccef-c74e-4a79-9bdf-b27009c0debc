﻿/*********************************************
* 文 件 名：Cs01DtExtend
* 类 名 称：Cs01DtExtend
* 功能说明：DataTable扩展类
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：吴新才
* 创建时间：2018-05-22 15:38:26
* 版 本 号：1.0.0.1
* 修改时间：2020-02-18 15:38:26
* 修 改 人：刘成刚
* CLR 版本：4.0.30319.42000
/*********************************************/

using Microsoft.CSharp;
using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;

namespace PlatCommon.Base01
{
 
    /// <summary>
    /// 静态数据类全局函数集
    /// </summary>
    public static class Cs01DtExtend
    {
        /// <summary>
        /// 判断对象是否是数值类型
        /// </summary>
        /// <param name="o">对象</param>
        /// <returns></returns>
        public static bool IsNumericType(this object o)
        {
            switch (Type.GetTypeCode(o.GetType()))
            {
                case TypeCode.Byte:
                case TypeCode.SByte:
                case TypeCode.UInt16:
                case TypeCode.UInt32:
                case TypeCode.UInt64:
                case TypeCode.Int16:
                case TypeCode.Int32:
                case TypeCode.Int64:
                case TypeCode.Decimal:
                case TypeCode.Double:
                case TypeCode.Single:
                    return true;
                default:
                    return false;
            }
        }

        /// <summary>
        /// 判断类型是否是数值类型
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns></returns>
        public static bool IsNumericType(this Type type)
        {
            switch (Type.GetTypeCode(type))
            {
                case TypeCode.Byte:
                case TypeCode.SByte:
                case TypeCode.UInt16:
                case TypeCode.UInt32:
                case TypeCode.UInt64:
                case TypeCode.Int16:
                case TypeCode.Int32:
                case TypeCode.Int64:
                case TypeCode.Decimal:
                case TypeCode.Double:
                case TypeCode.Single:
                    return true;
                default:
                    return false;
            }
        }

        /// <summary>
        /// DataTable To List的扩展方法
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dtData">数据表</param>
        /// <returns></returns>
        public static List<T> ToList<T>(this DataTable dtData) where T : new()
        {
            return DataTableToList<T>(dtData);
        }

        /// <summary>
        /// 合并DataTable的一列为\r\n分割的字符串
        /// </summary>
        /// <param name="dtData">数据表</param>
        /// <param name="strColName">列名</param>
        /// <returns>DataTable</returns>
        public static DataTable MergeColumn(DataTable dtData, string strColName)
        {
            DataTable dt = dtData;
            if (dt.Rows.Count <= 1) return dt;

            DataTable dtResult = new DataTable();
            DataColumn dc;
            int iColNo = 0; //合并列的序号
            List<string> list = new List<string>(); //其它列
            Dictionary<int, int> dic = new Dictionary<int, int>();
            DataColumnCollection dcc = dt.Columns;
            int iColCount = dcc.Count;
            int dt2Index = 0;
            for (int i = 0; i < iColCount; i++)
            {
                string strColumnName = dcc[i].ColumnName;
                if (!strColName.Equals(strColumnName))
                {
                    list.Add(strColumnName);
                    dc = new DataColumn(strColumnName, dcc[i].DataType);
                    dtResult.Columns.Add(dc);
                    dic.Add(i, dt2Index);
                    dt2Index++;
                }
                else
                {
                    iColNo = i;
                }
            }
            dc = new DataColumn(strColName, Type.GetType("System.String"));
            dtResult.Columns.Add(dc);

            DataRow[] drArrRows = dt.Select("", string.Join(",", list));
            int iRowNo = 0;
            int iPrevRowNo = -1;
            int iRowCount = drArrRows.Length;
            DataRow dr2 = dtResult.NewRow();
            bool isNewRow = true;
            list.Clear();
            int[] cols = dic.Keys.ToArray();
            for (iRowNo = 0; iRowNo < iRowCount; iRowNo++)
            {
                iPrevRowNo = iRowNo - 1;
                if (iPrevRowNo < 0)
                {
                    isNewRow = true;
                }
                else
                {
                    isNewRow = !IsSameDataRow(drArrRows[iPrevRowNo], drArrRows[iRowNo], cols);
                }
                if (isNewRow)
                {
                    if (iPrevRowNo >= 0)
                    {
                        dr2[iColCount - 1] = string.Join(" \r\n", list);
                    }
                    list.Clear();
                    list.Add(drArrRows[iRowNo][iColNo].ToString());
                    dr2 = dtResult.NewRow();
                    CopyDataRow(drArrRows[iRowNo], dr2, dic);
                    dtResult.Rows.Add(dr2);
                }
                else
                {
                    list.Add(drArrRows[iRowNo][iColNo].ToString());
                }
            }
            return dtResult;
        }

        /// <summary>
        /// 比较DataRow是否有相同的列
        /// </summary>
        /// <param name="dr1">DataRow1</param>
        /// <param name="dr2">DataRow2</param>
        /// <param name="cols">列的整数数组</param>
        /// <returns></returns>
        public static bool IsSameDataRow(DataRow dr1, DataRow dr2, int[] cols)
        {
            bool isok = true;
            foreach (int i in cols)
            {
                if (!dr1[i].ToString().Equals(dr2[i].ToString()))
                {
                    isok = false;
                    break;
                }
            }
            return isok;
        }

        /// <summary>
        /// 比较DataRow是否有相同的列
        /// </summary>
        /// <param name="dr1">DataRow1</param>
        /// <param name="dr2">DataRow2</param>
        /// <param name="dicCols">Dictionary源列，目标列序号</param>
        /// <returns></returns>
        public static bool IsSameDataRow(DataRow dr1, DataRow dr2, Dictionary<int, int> dicCols)
        {
            bool isok = true;
            foreach (KeyValuePair<int, int> kv in dicCols)
            {
                if (dr1[kv.Key].ToString() != (dr2[kv.Value].ToString()))
                {
                    isok = false;
                    break;
                }
            }
            return isok;
        }

        /// <summary>
        /// 比较DataRow是否有相同的列
        /// </summary>
        /// <param name="dr1">DataRow</param>
        /// <param name="dr2">DataRow</param>
        /// <param name="strArrCols">列名的字符串数组</param>
        /// <returns></returns>
        public static bool IsSameDataRow(DataRow dr1, DataRow dr2, string[] strArrCols)
        {
            bool bIsOk = true;
            foreach (string strColumn in strArrCols)
            {
                if (dr1[strColumn].ToString() != dr2[strColumn].ToString())
                {
                    bIsOk = false;
                    break;
                }
            }
            return bIsOk;
        }

        /// <summary>
        /// 比较DataRowView是否有相同的列
        /// </summary>
        /// <param name="dr1">DataRowView</param>
        /// <param name="dr2">DataRowView</param>
        /// <param name="strArrCols">列名的字符串数组</param>
        /// <returns></returns>
        public static bool IsSameDataRow(DataRowView dr1, DataRowView dr2, string[] strArrCols)
        {
            bool isok = true;
            foreach (string strColumn in strArrCols)
            {
                if (dr1[strColumn].ToString() != dr2[strColumn].ToString())
                {
                    isok = false;
                    break;
                }
            }
            return isok;
        }

        /// <summary>
        /// 复制DataRow列的值, 相同列名进行拷贝
        /// </summary>
        /// <param name="dr1">待复制DataRow</param>
        /// <param name="dr2">将复制DataRow</param>
        /// <returns></returns>
        public static bool CopyDataRow(DataRow dr1, DataRow dr2)
        {
            DataColumnCollection dcc = dr1.Table.Columns;
            Dictionary<string, int> d = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            for (int i = 0, n = dcc.Count; i < n; i++)
            {
                d.Add(dcc[i].ColumnName, i);
            }

            dcc = dr2.Table.Columns;
            for (int i = 0, n = dcc.Count; i < n; i++)
            {
                string fieldName = dcc[i].ColumnName;
                if (d.ContainsKey(fieldName))
                {
                    dr2[i] = dr1[d[fieldName]];
                }
            }
            return true;
        }

        /// <summary>
        /// 复制DataRow列的值
        /// </summary>
        /// <param name="dr1">待复制DataRow</param>
        /// <param name="dr2">将复制DataRow</param>
        /// <param name="iArrCols">列的整数数组, 空数组则要求dr1包含dr2的全部列</param>
        /// <returns></returns>
        public static bool CopyDataRow(DataRow dr1, DataRow dr2, int[] iArrCols)
        {
            if (iArrCols.Length == 0)
            {
                for (int i = 0, n = dr2.ItemArray.Length; i < n; i++)
                {
                    dr2[i] = dr1[i];
                }
            }
            else
            {
                foreach (int i in iArrCols)
                {
                    dr2[i] = dr1[i];
                }
            }
            return true;
        }

        /// <summary>
        /// 复制DataRow列的值
        /// </summary>
        /// <param name="dr1">待复制的DataRow</param>
        /// <param name="dr2">复制到的DataRow</param>
        /// <param name="dicCols">Dictionary源列，目标列号</param>
        /// <returns></returns>
        public static bool CopyDataRow(DataRow dr1, DataRow dr2, Dictionary<int, int> dicCols)
        {
            foreach (KeyValuePair<int, int> kv in dicCols)
            {
                dr2[kv.Value] = dr1[kv.Key];
            }
            return true;
        }

        /// <summary>
        /// 复制DataRow列的值
        /// </summary>
        /// <param name="dr1">待复制DataRow</param>
        /// <param name="dr2">将复制DataRow</param>
        /// <param name="strArrColNames">列的名称数组, 空数组则是复制相同名称列</param>
        /// <returns></returns>
        public static bool CopyDataRow(DataRow dr1, DataRow dr2, string[] strArrColNames)
        {
            if (strArrColNames == null || strArrColNames.Length == 0)
            {
                return CopyDataRow(dr1, dr2);
            }
            foreach (string fieldName in strArrColNames)
            {
                dr2[fieldName] = dr1[fieldName];
            }
            return true;
        }

        /// <summary>
        /// 查找字段为指定值的数据行
        /// </summary>
        /// <param name="dataTable">DataTable</param>
        /// <param name="strFieldName">字段</param>
        /// <param name="objValue">查找值</param>
        /// <returns></returns>
        public static int FindRow(DataTable dataTable, string strFieldName, object objValue)
        {
            int iFindRow = -1;
            for (int i = 0; i < dataTable.Rows.Count; i++)
            {
                if (dataTable.Rows[i].RowState == DataRowState.Deleted) continue;
                object oCurrentValue = dataTable.Rows[i][strFieldName];
                if (oCurrentValue.ToString() == objValue.ToString())
                {
                    iFindRow = i;
                    break;
                }

            }
            return iFindRow;
        }

        /// <summary>
        /// 返回存在数据的行号
        /// </summary>
        /// <param name="dataTable">dataTable</param>
        /// <param name="dicFields">列值字典</param>
        /// <returns></returns>
        public static int GetExistedRow(DataTable dataTable, Dictionary<string, object> dicFields)
        {
            //检查列存在
            foreach (var item in dicFields)
            {
                if (!dataTable.Columns.Contains(item.Key))
                {
                    return -1;
                }
            }

            int iRowHandle = -1;
            Boolean bFind = true;
            //循环处理每一行
            for (int i = 0; i < dataTable.Rows.Count; i++)
            {
                bFind = true;
                if (dataTable.Rows[i].RowState == DataRowState.Deleted) continue;

                //循环处理指定的列
                foreach (var item in dicFields)
                {
                    string strValue = dataTable.Rows[i][item.Key]?.ToString();
                    if (strValue != item.Value.ToString())
                    {
                        bFind = false;
                        break;
                    }
                }

                //找到了
                if (bFind)
                {
                    iRowHandle = i;
                    break;
                }
            }
            return iRowHandle;
        }

        /// <summary>
        /// 查找字段为空的数据行
        /// </summary>
        /// <param name="dtData">DataTable</param>
        /// <param name="strFieldName">字段</param>
        /// <returns></returns>
        public static int FindEmptyFieldRow(DataTable dtData, string strFieldName)
        {
            int iFindRow = -1;
            for (int i = 0; i < dtData.Rows.Count; i++)
            {
                if (dtData.Rows[i].RowState == DataRowState.Deleted) continue;
                object o = dtData.Rows[i][strFieldName];
                if (o == DBNull.Value || Cs01Functions.CStr(o) == "")
                {
                    iFindRow = i;
                    break;
                }
            }
            return iFindRow;
        }

        /// <summary>
        /// 删除字段不能为空的数据行
        /// </summary>
        /// <param name="dtData">DataTable</param>
        /// <param name="strFieldName">字段</param>
        /// <returns></returns>
        public static int DeleteEmptyFieldRows(DataTable dtData, string strFieldName)
        {
            int iCount = 0;
            int iRowCount = dtData.Rows.Count;
            for (int i = iRowCount - 1; i >= 0; i--)
            {
                DataRow dr = dtData.Rows[i];
                if (dr.RowState == DataRowState.Deleted) continue;

                object o = dr[strFieldName];
                if (o == DBNull.Value || Cs01Functions.CStr(o) == "")
                {
                    dr.Delete();
                    iCount++;
                }
            }
            return iCount;
        }

        /// <summary>
        /// 删除字段不能为空的数据行
        /// </summary>
        /// <param name="dtData">DataTable</param>
        /// <param name="strArrFieldNames">字段集</param>
        /// <returns></returns>
        public static int DeleteEmptyFieldsRows(DataTable dtData, string[] strArrFieldNames)
        {
            int iCount = 0;
            int iRowCount = dtData.Rows.Count;
            for (int i = iRowCount - 1; i >= 0; i--)
            {
                DataRow dr = dtData.Rows[i];
                if (dr.RowState == DataRowState.Deleted) continue;

                bool needDelete = false;
                foreach (string strFieldName in strArrFieldNames)
                {
                    object o = dr[strFieldName];
                    if (o == DBNull.Value || Cs01Functions.CStr(o) == "")
                    {
                        needDelete = true;
                        iCount++;
                        break;
                    }
                }
                if (needDelete)
                {
                    dr.Delete();
                }
            }
            return iCount;
        }

        /// <summary>
        /// 查找字段为空的数据行
        /// </summary>
        /// <param name="dtData">DataTable</param>
        /// <param name="strArrFieldNames">字段集</param>
        /// <returns></returns>
        public static int FindEmptyFieldsRows(DataTable dtData, string[] strArrFieldNames)
        {
            int iFindRow = -1;
            for (int i = 0; i < dtData.Rows.Count; i++)
            {
                DataRow dr = dtData.Rows[i];
                if (dr.RowState == DataRowState.Deleted) continue;

                bool bFind = false;
                foreach (string strFieldName in strArrFieldNames)
                {
                    object o = dr[strFieldName];
                    if (o == DBNull.Value || Cs01Functions.CStr(o) == "")
                    {
                        bFind = true;
                        break;
                    }
                }

                if (bFind)
                {
                    iFindRow = i;
                    break;
                }
            }
            return iFindRow;
        }

        /// <summary>
        /// 判断是否有重复数据，返回重复的行号对
        /// </summary>
        /// <param name="dtData">dtData</param>
        /// <param name="strFieldName">列名</param>
        /// <returns></returns>
        public static int[] IsRepeated(DataTable dtData, string strFieldName)
        {
            int count = dtData.Rows.Count;
            int[] rows = new int[2] { -1, -1 };
            Dictionary<string, int> dic = new Dictionary<string, int>();
            for (int i = 0; i < count; i++)
            {
                if (dtData.Rows[i].RowState == DataRowState.Deleted) continue;

                string name = dtData.Rows[i][strFieldName].ToString();
                if (dic.ContainsKey(name))
                {
                    rows[1] = i + 1;
                    rows[0] = dic[name];
                    break;
                }
                else
                {
                    dic[name] = i + 1;
                }
            }
            return rows;
        }

        /// <summary>
        /// 判断是否有重复数据，返回重复的行号对
        /// </summary>
        /// <param name="dtData">dtData</param>
        /// <param name="strArrFieldNames">列名数组</param>
        /// <returns></returns>
        public static int[] IsRepeated(DataTable dtData, string[] strArrFieldNames)
        {
            int iRowCount = dtData.Rows.Count;
            int[] iArrRows = new int[2] { -1, -1 };
            Dictionary<string, int> dic = new Dictionary<string, int>();
            int iArrLen = strArrFieldNames.Length;
            for (int i = 0; i < iRowCount; i++)
            {
                int k;
                DataRow dr = dtData.Rows[i];
                if (dr.RowState == DataRowState.Deleted) continue;

                string key = dr[strArrFieldNames[0]].ToString();
                for (k = 1; k < iArrLen; k++)
                {
                    key += "." + dr[strArrFieldNames[k]].ToString();
                }
                if (dic.ContainsKey(key))
                {
                    iArrRows[1] = i + 1;
                    iArrRows[0] = dic[key];
                    break;
                }
                else
                {
                    dic[key] = i + 1;
                }
            }
            return iArrRows;
        }

        /// <summary>
        /// DataTable转化为List
        /// </summary>
        /// <typeparam name="T">数据类</typeparam>
        /// <param name="dtData">DataTable</param>
        /// <returns>List</returns>
        public static List<T> DataTableToList<T>(DataTable dtData) where T : new()
        {
            T data = new T();
            Type type = data.GetType(); //获取类型
            List<T> list = new List<T>();
            DataColumnCollection cols = dtData.Columns;
            IndexType it;

            //创建对照字典
            Dictionary<string, IndexType> dic = new Dictionary<string, IndexType>();
            foreach (PropertyInfo pinfo in type.GetProperties())
            {
                string fieldname = pinfo.Name;
                int i = cols.IndexOf(fieldname);
                if (i >= 0)
                {
                    it.index = i;
                    if (!pinfo.PropertyType.Equals(cols[i].DataType))
                    {
                        it.type = 1;
                    }
                    else
                    {
                        it.type = 0;
                    }
                    dic.Add(fieldname, it);
                }
            }

            foreach (DataRow dr in dtData.Rows)
            {
                data = new T();
                Type targetType;
                foreach (string fieldName in dic.Keys)
                {
                    PropertyInfo pinfo = type.GetProperty(fieldName);
                    if (pinfo == null) continue;


                    it = dic[fieldName];
                    int i = it.index;
                    object value;
                    if (dr[i] != DBNull.Value)
                    {
                        if (it.type == 0)
                        {
                            pinfo.SetValue(data, dr[i], null);
                        }
                        else
                        {
                            targetType = pinfo.PropertyType;
                            if (targetType.IsGenericType && (targetType.GetGenericTypeDefinition() == typeof(Nullable<>)))
                            {
                                targetType = targetType.GetGenericArguments()[0];
                            }
                            value = Convert.ChangeType(dr[i], targetType);
                            pinfo.SetValue(data, value, null);
                        }
                    }
                }
                list.Add(data);
            }
            return list;
        }

        /// <summary>
        /// DataTable转化为List数据类型一致的数据, 方法是修改字段的数据类型
        /// </summary>
        /// <typeparam name="T">数据类</typeparam>
        /// <param name="dtData">DataTable</param>
        /// <returns>List</returns>
        public static DataTable ToListDataTable<T>(DataTable dtData) where T : new()
        {
            T data = new T();
            Type type = data.GetType(); //获取类型
            DataColumnCollection cols = dtData.Columns;

            Dictionary<int, Type> dic = new Dictionary<int, Type>();
            foreach (PropertyInfo pinfo in type.GetProperties())
            {
                string fieldName = pinfo.Name;
                int i = cols.IndexOf(fieldName);
                if (i >= 0)
                {
                    if (!pinfo.PropertyType.Equals(cols[i].DataType))
                    {
                        dic.Add(i, pinfo.PropertyType);
                    }
                }
            }
            foreach (KeyValuePair<int, Type> kv in dic)
            {
                cols[kv.Key].DataType = kv.Value;
            }
            dtData.AcceptChanges();
            return dtData;
        }

        /// <summary>
        /// DataTable转化为List数据类型一致的数据, 方法是添加新类型的字段，将数据转换到新的字段，删除原有字段，修改新字段的名称为原字段名称
        /// </summary>
        /// <typeparam name="T">数据类</typeparam>
        /// <param name="dataTable">dataTable</param>
        /// <returns></returns>
        public static DataTable ToListDataTable2<T>(DataTable dataTable) where T : new()
        {
            T data = new T();
            Type type = data.GetType(); //获取类型
            DataColumnCollection cols = dataTable.Columns;
            IndexType it;

            //创建需要转换数据类型的字典double, float
            Dictionary<string, IndexType> dic = new Dictionary<string, IndexType>();
            foreach (PropertyInfo pinfo in type.GetProperties())
            {
                string fieldName = pinfo.Name;
                int i = cols.IndexOf(fieldName);
                if (i >= 0)
                {
                    if (!pinfo.PropertyType.Equals(cols[i].DataType))
                    {
                        cols.Add($"new__{fieldName}", pinfo.PropertyType);
                        it.index = i;
                        it.type = cols.Count - 1;
                        dic.Add(fieldName, it);
                    }
                }
            }
            foreach (DataRow dr in dataTable.Rows)
            {
                foreach (string fieldName in dic.Keys)
                {
                    it = dic[fieldName];
                    int i = it.index;
                    int j = it.type;
                    if (dr[i] != DBNull.Value)
                    {
                        dr[j] = Convert.ChangeType(dr[i], cols[j].DataType);
                    }
                }
            }

            foreach (string fieldName in dic.Keys)
            {
                int i = cols.IndexOf(fieldName);
                cols.RemoveAt(i);
                string newField = $"new__{fieldName}";
                cols[newField].ColumnName = fieldName;
            }
            dataTable.AcceptChanges();
            return dataTable;
        }

        /// <summary>
        /// 读取DataTable中第一行修改的数据
        /// </summary>
        /// <param name="dataTable">DataTable</param>
        /// <returns></returns>
        public static List<string> GetDataTableModifiedFirtRowInfo(DataTable dataTable)
        {
            //string: Row={Row}, FieldName={FieldName}, DataType={DataType}, VALUE={VALUE}, ORIGINAL={ORIGINAL}
            List<string> list = new List<string>();
            DataColumnCollection dcc = dataTable.Columns;
            int n = dcc.Count;
            bool isok = false;
            foreach (DataRow dr in dataTable.Rows)
            {
                isok = false;
                if (dr.RowState == DataRowState.Modified)
                {
                    string s1, s2;
                    for (int i = 0; i < n; i++)
                    {
                        if (dr[i, DataRowVersion.Original] == DBNull.Value)
                        {
                            if (dr[i] != DBNull.Value)
                            {
                                isok = true;
                                list.Add($"Row={i}, FieldName={dcc[i].ColumnName}, DataType={dcc[i].DataType.Name}, VALUE={dr[i].ToString()}, ORIGINAL=DBNull.Value");
                            }
                            continue;
                        }
                        if (dr[i] == DBNull.Value)
                        {
                            isok = true;
                            list.Add($"Row={i}, FieldName={dcc[i].ColumnName}, DataType={dcc[i].DataType.Name}, VALUE=DBNull.Value, ORIGINAL={dr[i, DataRowVersion.Original].ToString()}");
                        }
                        else
                        {
                            s1 = dr[i].ToString();
                            s2 = dr[i, DataRowVersion.Original].ToString();
                            if (s1 != s2)
                            {
                                isok = true;
                                list.Add($"Row={i}, FieldName={dcc[i].ColumnName}, DataType={dcc[i].DataType.Name}, VALUE={s1}, ORIGINAL={s2}");
                            }
                        }
                    }
                }
                if (isok) break;
            }
            return list;
        }

        /// <summary>
        /// 判断是否是变化的行
        /// </summary>
        /// <param name="dataRow"></param>
        /// <returns></returns>
        public static bool IsDataRowChanged(DataRow dataRow)
        {
            DataRowState state = dataRow.RowState;
            if (state == DataRowState.Added ) return true;
            if (state == DataRowState.Deleted) return true;
            if (state != DataRowState.Modified) return false;

            //Modified状态，要进行一步比较
            DataColumnCollection dcc = dataRow.Table.Columns;
            int n = dcc.Count;
            bool isok = false;
            for (int i = 0; i < n; i++)
            {
                if (dataRow[i, DataRowVersion.Original] == DBNull.Value)
                {
                    if (dataRow[i] != DBNull.Value)
                    {
                        isok = true;
                        break;
                    }
                }
                else
                {
                    if (dataRow[i] == DBNull.Value)
                    {
                        isok = true;
                        break;
                    }
                    else
                    {
                        if (dataRow[i].ToString() != dataRow[i, DataRowVersion.Original].ToString())
                        {
                            isok = true;
                            break;
                        }
                    }
                }
            }
            return isok;
        }

        /// <summary>
        /// 将DataTable的某列横向展开，将值列填充到横向对应列中
        /// </summary>
        /// <param name="dt">DataTable</param>
        /// <param name="strArrColumns">输出列表，空数组为全部</param>
        /// <param name="landscapeColumn">横向列名</param>
        /// <param name="valueColumn">值列名</param>
        /// <returns></returns>
        public static DataTable DataTableColumnLandscape(DataTable dt, string[] strArrColumns, string landscapeColumn, string valueColumn)
        {
            DataColumnCollection dcc = dt.Columns;
            int landscapeCol = dcc.IndexOf(landscapeColumn);
            if (landscapeCol < 0)
            {
                throw new Exception($"字段名{landscapeColumn}不存在。");
            }
            int valueCol = dcc.IndexOf(valueColumn);
            if (valueCol < 0)
            {
                throw new Exception($"字段名{valueColumn}不存在。");
            }

            int[] commonColumns = null;
            if (strArrColumns != null)
            {
                int n = strArrColumns.Length;
                if (n > 0)
                {
                    commonColumns = new int[n];
                    for (int i = 0; i < n; i++)
                    {
                        int index = dcc.IndexOf(strArrColumns[i]);
                        if (index < 0)
                        {
                            throw new Exception($"字段名{strArrColumns[i]}不存在。");
                        }
                        commonColumns[i] = index;
                    }
                }
            }
            return DataTableColumnLandscape(dt, commonColumns, landscapeCol, valueCol);
        }

        /// <summary>
        /// 将DataTable的某列横向展开，将值列填充到横向对应列中
        /// </summary>
        /// <param name="dt">DataTable</param>
        /// <param name="commonColumns">输出列表，空数组为全部</param>
        /// <param name="landscapeColumn">横向列</param>
        /// <param name="valueColumn">值列</param>
        /// <returns></returns>
        public static DataTable DataTableColumnLandscape(DataTable dt, int[] commonColumns, int landscapeColumn, int valueColumn)
        {
            List<string> commonColumnsList = new List<string>(); //除值列以外的显示列
            if (commonColumns == null)
            {
                List<int> list = new List<int>();
                for (int i = 0, n = dt.Columns.Count; i < n; i++)
                {
                    if (i != landscapeColumn && i != valueColumn)
                    {
                        list.Add(i);
                    }
                }
                commonColumns = list.ToArray();
            }
            SortedDictionary<string, int> dic = new SortedDictionary<string, int>(); //字段名，新数据表的数据列索引号
            string key = "";
            foreach (DataRow dr in dt.Rows)
            {
                key = dr[landscapeColumn].ToString();
                if (string.IsNullOrEmpty(key)) key = "_";
                if (!dic.ContainsKey(key)) dic[key] = 1;
            }
            //创建新的结构
            DataTable dt2 = new DataTable();
            DataColumn dc;
            string colname = "";
            foreach (int i in commonColumns)
            {
                dc = new DataColumn();
                colname = dt.Columns[i].ColumnName;
                commonColumnsList.Add(colname);
                dc.ColumnName = colname;
                dc.DataType = dt.Columns[i].DataType;
                dt2.Columns.Add(dc);
            }
            commonColumnsList.Add(dt.Columns[landscapeColumn].ColumnName);

            int n1 = commonColumns.Length;
            string[] lsColumns = dic.Keys.ToArray();
            for (int i = 0, n = lsColumns.Length; i < n; i++)
            {
                dc = new DataColumn();
                dc.ColumnName = "f_" + lsColumns[i];
                dc.DataType = dt.Columns[valueColumn].DataType; //值列的类型
                dic[lsColumns[i]] = i + n1;
                dt2.Columns.Add(dc);
            }
            //数据排序
            string sortString = string.Join(",", commonColumnsList.ToArray());
            DataRow[] drs = dt.Select("", sortString);

            //填充新的数据表
            bool isNewRow = true;
            int colIndex = 0;
            DataRow dr2 = dt2.NewRow();
            for (int i = 0, n = drs.Length; i < n; i++)
            {
                if (i == 0) { isNewRow = true; } else { isNewRow = !IsSameDataRow(drs[i], drs[i - 1], commonColumns); }
                if (isNewRow)
                {
                    dr2 = dt2.NewRow();
                    CopyDataRow(drs[i], dr2, commonColumns);
                    dt2.Rows.Add(dr2);
                }
                colname = drs[i][landscapeColumn].ToString();
                if (string.IsNullOrEmpty(colname)) colname = "_";
                colIndex = dic[colname];
                dr2[colIndex] = drs[i][valueColumn];
            }
            return dt2;
        }

        /// <summary>
        /// 设定DataTable各列的Caption
        /// </summary>
        /// <param name="dataTable">dataTable</param>
        /// <param name="strCaptions">列头</param>
        /// <returns></returns>
        public static int DataTableSetCaptions(DataTable dataTable, string strCaptions)
        {
            Dictionary<string, string> d = Cs01Functions.StringToDict(strCaptions, ',', '=');
            DataColumnCollection cols = dataTable.Columns;
            int count = 0;
            foreach (DataColumn col in cols)
            {
                string fieldName = col.ColumnName;
                if (d.ContainsKey(fieldName))
                {
                    col.Caption = d[fieldName];
                    count++;
                }
            }
            return count;
        }

        /// <summary>
        /// 根据数据类创建DataTable对象
        /// </summary>
        /// <typeparam name="T">泛型数据类</typeparam>
        /// <param name="t">数据类对象</param>
        /// <returns></returns>
        public static DataTable CreateDataTable<T>(T t) where T : new()
        {
            DataTable dataTable = new DataTable("DataTable");
            PropertyInfo[] pInfos = t.GetType().GetProperties();
            DataColumnCollection cols = dataTable.Columns;
            foreach (PropertyInfo pInfo in pInfos)
            {
                string fieldName = pInfo.Name;
                DataColumn col = new DataColumn();
                col.ColumnName = fieldName;
                col.DataType = pInfo.PropertyType;
                string displayName = GetDisplayName(pInfo);
                if (!string.IsNullOrEmpty(displayName))
                {
                    col.Caption = displayName;
                }
                cols.Add(col);

            }
            return dataTable;
        }

        /// <summary>
        /// 使用dynamic根据DataRow,由列名自动添加属性并赋值
        /// </summary>
        /// <param name="dataRow">dataRow</param>
        /// <returns></returns> 
        public static ICollection<KeyValuePair<string, object>> GetDynamicClassByDataRow(DataRow dataRow)
        {
            KeyValuePair<string, object> kv;
            dynamic obj = new System.Dynamic.ExpandoObject();
            ICollection<KeyValuePair<string, object>> d = obj as ICollection<KeyValuePair<string, object>>;
            //创建属性，并赋值。
            foreach (DataColumn dc in dataRow.Table.Columns)
            {
                string key = dc.ColumnName;
                kv = new KeyValuePair<string, object>(key, dataRow[key]);
                d.Add(kv);
            }
            return d;
        }

        /// <summary>
        /// 使用反射 动态创建类，将DataTable的列名动态添加为该类的属性，并给属性赋值
        /// </summary>
        /// <param name="dataRow"></param>
        /// <returns></returns>
        public static Object CreatNewClassByDataRow(DataRow dataRow)
        {
            //创建编译器实例。
            CSharpCodeProvider provider = new CSharpCodeProvider();
            //设置编译参数。
            CompilerParameters paras = new CompilerParameters();
            paras.GenerateExecutable = false;
            paras.GenerateInMemory = true;

            //创建动态代码。
            StringBuilder classSource = new StringBuilder();
            classSource.Append("public class MyDynamicClass \n");
            classSource.Append("{\n");
            //创建属性。
            DataColumnCollection dcc = dataRow.Table.Columns;
            foreach (DataColumn dc in dcc)
            {
                classSource.Append($"\t{dc.DataType.Name} {dc.ColumnName};");
            }
            classSource.Append("}");
            //System.Diagnostics.Debug.WriteLine(classSource.ToString());

            //编译代码。
            CompilerResults result = provider.CompileAssemblyFromSource(paras, classSource.ToString());
            //获取编译后的程序集。
            Assembly assembly = result.CompiledAssembly;
            object obclass = assembly.CreateInstance("MyDynamicClass");
            //赋值
            Type type = obclass.GetType();
            foreach (DataColumn dc in dcc)
            {
                PropertyInfo _Property = type.GetProperty(dc.ColumnName);
                if (_Property != null && _Property.CanRead)
                {
                    _Property.SetValue(obclass, dataRow[dc.ColumnName], null);
                }
            }
            return obclass;
        }

        /// <summary>
        /// 将行数据转成对应的实体(属性转换)
        /// </summary>
        /// <typeparam name="T">实体</typeparam>
        /// <param name="dataRow">DataRow</param>
        /// <returns></returns>
        public static T DataRowToEntity<T>(DataRow dataRow) where T : class, new()
        {
            if (dataRow == null)
            {
                return default(T);
            }
            T entity = new T();
            PropertyInfo[] pInfos = entity.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
            foreach (PropertyInfo pinfo in pInfos)
            {
                DataColumnCollection cols = dataRow.Table.Columns;
                string fieldName = pinfo.Name;
                int i = cols.IndexOf(fieldName);
                object value;
                if (i >= 0 && dataRow[i] != DBNull.Value)
                {
                    value = dataRow[i];
                    Type type1 = pinfo.PropertyType;
                    //   type2 = pinfo.PropertyType;
                    if (type1.IsValueType && type1 != cols[i].DataType)
                    {
                        Type type2;
                        if (type1.IsGenericType && (type1.GetGenericTypeDefinition() == typeof(Nullable<>)))
                        {
                            type2 = type1.GetGenericArguments()[0];
                        }
                        else
                        {
                            type2 = pinfo.PropertyType;
                        }
                        value = Convert.ChangeType(value, type2);
                    }
                    pinfo.SetValue(entity, value, null);
                }
            }
            return entity;
        }

        /// <summary>
        /// 将行数据转成对应的实体(属性转换
        /// </summary>
        /// <typeparam name="T">实体</typeparam>
        /// <param name="dataRow">dataRow</param>
        /// <returns></returns>
        public static T ToEntity<T>(this DataRow dataRow) where T : class, new()
        {
            return DataRowToEntity<T>(dataRow);
        }

        /// <summary>
        /// 将行数据转成对应的实体(字段转换)
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dataRow">DataRow</param>
        /// <returns></returns>
        public static T DataRowToEntityFields<T>(DataRow dataRow) where T : class, new()
        {
            if (dataRow == null)
            {
                return default(T);
            }
            T entity = new T();
            FieldInfo[] fInfos = entity.GetType().GetFields();
            foreach (FieldInfo finfo in fInfos)
            {
                DataColumnCollection cols = dataRow.Table.Columns;
                string fieldName = finfo.Name;
                int i = cols.IndexOf(fieldName);
                object value;
                Type type2;
                if (i >= 0 && dataRow[i] != DBNull.Value)
                {
                    value = dataRow[i];
                    type2 = finfo.FieldType;
                    if (type2.IsValueType && type2 != cols[i].DataType)
                    {
                        value = Convert.ChangeType(value, type2);
                    }
                    finfo.SetValue(entity, value);
                }
            }
            return entity;
        }

        /// <summary>
        /// 转换List对象为DataTable对象
        /// </summary>
        /// <param name="listData">List</param>
        /// <returns>DataTable对象</returns>
        public static DataTable ListToDataTable<T>(List<T> listData) where T : new()
        {
            if (listData.Count == 0)
            {
                throw new Exception("ListToDataTable: no data!");
            }
            Type type = typeof(T);
            T t = listData[0];
            DataTable dt = CreateDataTable<T>(t);
            DataColumnCollection cols = dt.Columns;
            PropertyInfo[] pInfos = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            IndexType it;

            //创建对照字典
            Dictionary<string, IndexType> dic = new Dictionary<string, IndexType>();
            foreach (PropertyInfo pinfo in pInfos)
            {
                Type type2;
                string fieldName = pinfo.Name;
                int i = cols.IndexOf(fieldName);
                if (i >= 0)
                {
                    it.index = i;
                    type2 = pinfo.PropertyType;
                    if (!type2.Equals(cols[i].DataType))
                    {
                        it.type = 1;
                    }
                    else
                    {
                        it.type = 0;
                    }
                    dic.Add(fieldName, it);
                }
            }
            //填充数据
            dt.BeginLoadData();
            foreach (T obj in listData)
            {
                int i = 0;
                object value;
                DataRow dr = dt.NewRow();
                foreach (string fieldName in dic.Keys)
                {
                    it = dic[fieldName];
                    i = it.index;
                    value = type.GetProperty(fieldName).GetValue(obj, null);
                    if (value != null)
                    {
                        if (it.type == 1)
                        {
                            value = Convert.ChangeType(value, cols[i].DataType);
                        }
                        dr[i] = value;
                    }
                }
                dt.Rows.Add(dr);
            }
            dt.EndLoadData();
            //设定状态
            dt.AcceptChanges();
            return dt;
        }

        /// <summary>
        /// 将DataTable各行置为删除状态
        /// </summary>
        /// <param name="dtData">DataTable</param>
        /// <returns></returns>
        public static bool DataTableDelete(DataTable dtData)
        {
            DataRowCollection drc = dtData.Rows;
            for (int i = 0, n = drc.Count; i < n; i++)
            {
                DataRow dr = drc[i];
                if (dr.RowState != DataRowState.Deleted)
                {
                    dr.Delete();
                }
            }
            return true;
        }

        /// <summary>
        /// 获取DataTable变化的行数
        /// </summary>
        /// <param name="dtData">DataTable</param>
        /// <returns></returns>
        public static int GetDataTableChangedCount(DataTable dtData)
        {
            int count = 0;
            if (dtData == null) return 0;
            foreach (DataRow dr in dtData.Rows)
            {
                if (dr.RowState != DataRowState.Unchanged)
                {
                    count++;
                }
            }
            return count;
        }

        /// <summary>
        /// 将DataTable指定的列数据复制到dtTarget中， 不复制删除行和行状态。
        /// </summary>
        /// <param name="dtSource">源DataTable</param>
        /// <param name="dtTarget">目的DataTable</param>
        /// <param name="columns">列名数组</param>
        /// <returns></returns>
        public static bool DataTableCopy(DataTable dtSource, DataTable dtTarget, string[] columns)
        {
            Dictionary<int, int> dic = new Dictionary<int, int>();
            int n = columns.Length;
            DataColumnCollection cols1 = dtSource.Columns;
            DataColumnCollection cols2 = dtTarget.Columns;
            bool isSameDataType = true;
            for (int i = 0; i < n; i++)
            {
                string fieldName = columns[i];
                int key = cols1.IndexOf(fieldName);
                int value = cols2.IndexOf(fieldName);
                if (key < 0) throw new Exception($"DataTableCopy：字段{fieldName}不在原DataTable中。");
                if (value < 0) throw new Exception($"DataTableCopy：字段{fieldName}不在目标DataTable中。");
                dic.Add(key, value);
                if (!cols1[key].DataType.Equals(cols1[value].DataType))
                {
                    isSameDataType = false;
                }
            }
            DataRowCollection drc = dtSource.Rows;
            foreach (DataRow dr in drc)
            {
                DataRow r2 = dtTarget.NewRow();
                if (dr.RowState != DataRowState.Deleted)
                {
                    foreach (KeyValuePair<int, int> kv in dic)
                    {
                        if (isSameDataType)
                        {
                            r2[kv.Value] = dr[kv.Key];
                        }
                        else
                        {
                            r2[kv.Value] = Convert.ChangeType(dr[kv.Key], cols2[kv.Value].DataType);
                        }
                    }
                }
                dtTarget.Rows.Add(r2);
            }
            return true;
        }

        /// <summary>
        /// 去掉DataTable中的MaxLength限制
        /// </summary>
        /// <param name="dataTable">DataTable</param>
        /// <param name="dCaption">字典：字段==>显示名称</param>
        /// <returns></returns>
        public static DataTable DataTableToToEdit(DataTable dataTable, Dictionary<string, string> dCaption = null)
        {
            DataColumnCollection dcc = dataTable.Columns;
            bool isCaption = (dCaption != null);
            bool isString = true;
            foreach (DataColumn dc in dcc)
            {
                string stype = dc.DataType.ToString();
                string fieldName;
                isString = true;
                switch (stype)
                {
                    case "String":
                        break;
                    case "Char":
                        isString = false;
                        break;
                    case "Boolean":
                        isString = false;
                        break;
                    case "Byte":
                        isString = false;
                        break;
                    case "DateTime":
                        isString = false;
                        break;
                    case "Decimal":
                        isString = false;
                        break;
                    case "Double":
                        isString = false;
                        break;
                    case "Guid":
                        isString = false;
                        break;
                    case "Int16":
                        isString = false;
                        break;
                    case "Int32":
                        isString = false;
                        break;
                    case "Int64":
                        isString = false;
                        break;
                    case "SByte":
                        isString = false;
                        break;
                    case "Single":
                        isString = false;
                        break;
                    case "TimeSpan":
                        isString = false;
                        break;
                    case "UInt16":
                        isString = false;
                        break;
                    case "UInt32":
                        isString = false;
                        break;
                    case "UInt64":
                        isString = false;
                        break;
                    case "Byte[]":
                        isString = false;
                        break;
                    default:
                        break;
                }
                if (isString)
                {
                    dc.MaxLength = -1;
                }
                if (isCaption)
                {
                    fieldName = dc.ColumnName;
                    if (dCaption.ContainsKey(fieldName))
                    {
                        dc.Caption = dCaption[fieldName];
                    }
                }
            }
            return dataTable;
        }

        /// <summary>
        /// 字典中移除长度大于某值的记录
        /// </summary>
        /// <param name="dic">字典</param>
        /// <param name="n">长度值</param>
        /// <returns></returns>
        public static bool RemoveLongerItem(Dictionary<string, int> dic, int n)
        {
            string[] ks = dic.Keys.ToArray<string>();
            foreach (string key in ks)
            {
                if (dic[key] >= n) dic.Remove(key);
            }
            return true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dic"></param>
        /// <param name="strCode"></param>
        /// <returns></returns>
        public static string StartsWithCode(Dictionary<string, int> dic, string strCode)
        {
            string code = "";
            foreach (KeyValuePair<string, int> kv in dic)
            {
                if (strCode.StartsWith(kv.Key))
                {
                    if (kv.Key.Length > code.Length)
                    {
                        code = kv.Key;
                    }
                }
            }
            return code;
        }

        /// <summary>
        /// 交换变量的值
        /// </summary>
        /// <typeparam name="T">数据泛类型</typeparam>
        /// <param name="lhs">左交换数</param>
        /// <param name="rhs">右交换数</param>
        public static void Swap<T>(ref T lhs, ref T rhs)
        {
            T temp;
            temp = lhs;
            lhs = rhs;
            rhs = temp;
        }

        /// <summary>
        /// 当右交换数大于左交换数时，交换变量的值
        /// </summary>
        /// <typeparam name="T">数据泛类型</typeparam>
        /// <param name="lhs">左交换数</param>
        /// <param name="rhs">右交换数</param>
        public static void SwapIfGreater<T>(ref T lhs, ref T rhs) where T : System.IComparable<T>
        {
            T temp;
            if (lhs.CompareTo(rhs) > 0)
            {
                temp = lhs;
                lhs = rhs;
                rhs = temp;
            }
        }

        /// <summary>
        /// 当右交换数小于左交换数时，交换变量的值
        /// </summary>
        /// <typeparam name="T">数据泛类型</typeparam>
        /// <param name="lhs">左交换数</param>
        /// <param name="rhs">右交换数</param>
        public static void SwapIfLess<T>(ref T lhs, ref T rhs) where T : System.IComparable<T>
        {
            T temp;
            if (lhs.CompareTo(rhs) < 0)
            {
                temp = lhs;
                lhs = rhs;
                rhs = temp;
            }
        }

        /// <summary>
        /// 将字符串分割成字符串List
        /// </summary>
        /// <param name="strSource">源字符串</param>
        /// <param name="strArrSplit">分割字符串数组</param>
        /// <returns></returns>
        public static List<string> StringToList(string strSource, string[] strArrSplit)
        {
            Dictionary<string, int> dic = new Dictionary<string, int>();
            string[] strs = strSource.Split(strArrSplit, StringSplitOptions.RemoveEmptyEntries);
            foreach (string s in strs)
            {
                string key = s.Trim();
                if (!string.IsNullOrEmpty(key))
                {
                    if (!dic.ContainsKey(key)) dic[key] = 1; else dic[key] = dic[key] + 1;
                }
            }
            return dic.Keys.ToList();
        }

        /// <summary>
        /// 将字符串分割成字符串
        /// </summary>
        /// <param name="strSource">源字符串</param>
        /// <param name="strSplit">分割字符串</param>
        /// <returns></returns>
        public static List<string> StringToList(string strSource, string strSplit)
        {
            string[] cs = new string[] { strSplit };
            return StringToList(strSource, cs);
        }

        /// <summary>
        /// 获取属性DisplayName
        /// </summary>
        /// <param name="pInfo">属性</param>
        /// <returns></returns>
        public static string GetDisplayName(PropertyInfo pInfo)
        {
            Attribute[] attributes = (Attribute[])pInfo.GetCustomAttributes(typeof(DisplayNameAttribute), false);
            if (attributes.Length > 0)
            {
                DisplayNameAttribute displayName = (DisplayNameAttribute)attributes[0];
                return displayName.DisplayName;
            }
            return "";
        }

        /// <summary>
        /// 对象的复制
        /// </summary>
        /// <typeparam name="T">数据泛类型</typeparam>
        /// <param name="RealObject">数据实例</param>
        /// <returns></returns>
        public static T Clone<T>(T RealObject)
        {
            using (Stream objectStream = new MemoryStream())
            {
                /*利用 System.Runtime.Serialization序列化与反序列化完成引用对象的复制*/
                IFormatter formatter = new BinaryFormatter();
                formatter.Serialize(objectStream, RealObject);
                objectStream.Seek(0, SeekOrigin.Begin);
                return (T)formatter.Deserialize(objectStream);
            }
        }

        /// <summary>
        /// 复制对象
        /// </summary>
        /// <typeparam name="T">数据泛类型</typeparam>
        /// <param name="data">数据实例</param>
        /// <returns></returns>
        public static T CloneEx<T>(T data) where T : new()
        {
            if (data == null)
            {
                throw new NullReferenceException("CloneEx: data is null ");
            }
            Type type = data.GetType();
            PropertyInfo[] pInfos = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            T obj = new T();
            Type objType = typeof(T);
            foreach (PropertyInfo pinfo in pInfos)
            {
                string pname = pinfo.Name;
                PropertyInfo p = objType.GetProperty(pname);
                if (p != null)
                {
                    p.SetValue(obj, pinfo.GetValue(data, null), null);
                }
            }
            return obj;
        }

        /// <summary>
        /// 将DataTable按列排序
        /// <param name="dtSourceTable">排序前DataTable</param>
        /// <param name="strSortFields">排序字段strFieldNames</param>
        /// <returns>排序后DataTable</returns>
        /// </summary> 
        public static DataTable TableSort(DataTable dtSourceTable, string strSortFields)
        {
            //传入的参数检查
            if (dtSourceTable.Rows.Count == 0)
                return dtSourceTable;

            if (strSortFields.Length < 1)
                return dtSourceTable;

            //复制表结构
            DataTable dtResultTable = dtSourceTable.Clone();
            //排序
            DataRow[] sortRows = dtSourceTable.Select(null, strSortFields);
            dtResultTable = sortRows.CopyToDataTable();
            return dtResultTable;
        }

        /// <summary>
        /// 将DataTable分组
        /// <param name="dtSourceTable">要分组的DataTable</param>
        /// <param name="strGroupFields">分组字段strGroupFields</param>
        /// <returns>分组后DataTable</returns>
        /// </summary> 
        public static DataTable TableGroup(DataTable dtSourceTable, string strGroupFields)
        {
            //传入的参数检查
            if (dtSourceTable.Rows.Count == 0)
                return dtSourceTable;

            if (strGroupFields.Length < 1)
                return dtSourceTable;

            //分解分组的字段
            string[] strArrGroupField = strGroupFields.Split(',');

            //生成结果表的结构
            DataTable dtResultTable = new DataTable();
            DataColumn dcResultColumn;
            for (int i = 0; i < dtSourceTable.Columns.Count; i++)
            {
                if (Array.IndexOf(strArrGroupField, dtSourceTable.Columns[i].ColumnName) >= 0)
                {
                    dcResultColumn = new DataColumn(dtSourceTable.Columns[i].ColumnName, dtSourceTable.Columns[i].DataType);
                    dtResultTable.Columns.Add(dcResultColumn);
                }
            }

            //旧行的数据初始化
            string[] strArrFieldValue = strGroupFields.Split(',');
            for (int i = 0; i < strArrFieldValue.Length; i++)
            {
                strArrFieldValue[i] = "";
            }

            //对源表进行排序，用排序表进行数据处理
            DataTable dtSortTable = TableSort(dtSourceTable, strGroupFields);

            DataRow drSortRow, drResultRow;
            Boolean bIsNewRow = false;
            for (int i = 0; i < dtSortTable.Rows.Count; i++)
            {
                drSortRow = dtSortTable.Rows[i];

                //判断是不是新行
                bIsNewRow = false;
                for (int j = 0; j < strArrGroupField.Length; j++)
                {
                    if (drSortRow[strArrGroupField[j]]?.ToString() != strArrFieldValue[j])
                    {
                        bIsNewRow = true;
                        break;
                    }
                }

                //结果中加入新行
                if (bIsNewRow)
                {
                    drResultRow = dtResultTable.NewRow();
                    for (int j = 0; j < strArrGroupField.Length; j++)
                    {
                        drResultRow[strArrGroupField[j]] = drSortRow[strArrGroupField[j]];
                        strArrFieldValue[j] = drSortRow[strArrGroupField[j]]?.ToString();
                    }
                    dtResultTable.Rows.Add(drResultRow);
                }
            }
            return dtResultTable;
        }

        /// <summary>
        /// 将DataTable转为交叉表
        /// </summary>
        /// <param name="dtSourceTable">交叉表源</param>
        /// <param name="strFixFields">固定字段</param>
        /// <param name="strCrossField">可变字段</param>
        /// <param name="strCrossValueField">可变值字段</param>
        /// <returns>转为交叉表后的DataTable</returns>
        public static DataTable ChangeToCross(DataTable dtSourceTable, string strFixFields, string strCrossField, string strCrossValueField)
        {
            //传入的参数检查
            if (dtSourceTable.Rows.Count == 0)
                return dtSourceTable;

            if (strFixFields.Length < 1)
                return dtSourceTable;

            if (strCrossField.Length < 1)
                return dtSourceTable;

            if (strCrossValueField.Length < 1)
                return dtSourceTable;

            DataTable dtResultTable = new DataTable();
            DataColumn dcSourceColumn, dcResultColumn;
            //先处理固定的列，除了传入的交叉列以外，其它的都是固定列
            for (int i = 0; i < dtSourceTable.Columns.Count; i++)
            {
                dcSourceColumn = dtSourceTable.Columns[i];
                if (strFixFields.IndexOf(dtSourceTable.Columns[i].ColumnName) >= 0)
                {
                    dcResultColumn = new DataColumn(dcSourceColumn.ColumnName, dcSourceColumn.DataType);
                    dtResultTable.Columns.Add(dcResultColumn);
                }
            }

            //处理交叉列：根据交叉列的列名先进行分组，再加将分组结果，作为列加入结果表中
            DataTable dtGroupCross = TableGroup(dtSourceTable, strCrossField);
            for (int i = 0; i < dtGroupCross.Rows.Count; i++)
            {
                dcResultColumn = new DataColumn();
                dcResultColumn.ColumnName = dtGroupCross.Rows[i][strCrossField]?.ToString();
                dcResultColumn.DataType = typeof(decimal);
                dtResultTable.Columns.Add(dcResultColumn);
            }
            //加入合计列
            dcResultColumn = new DataColumn();
            dcResultColumn.ColumnName = "合计";
            dcResultColumn.DataType = typeof(decimal);
            dtResultTable.Columns.Add(dcResultColumn);

            //分解固定的字段
            string[] strArrFixField = strFixFields.Split(',');
            //旧行的数据初始化
            string[] strArrFieldValue = strFixFields.Split(',');
            for (int i = 0; i < strArrFieldValue.Length; i++)
            {
                strArrFieldValue[i] = "";
            }

            //源数据，进行排序
            DataTable dtSortTable = TableSort(dtSourceTable, strFixFields);

            DataRow drSortRow = null;
            DataRow drResultRow = null;
            Boolean bIsNewRow = false;
            decimal dRowSum = 0;

            for (int i = 0; i < dtSortTable.Rows.Count; i++)
            {
                drSortRow = dtSortTable.Rows[i];

                //判断是不是新行
                bIsNewRow = false;
                for (int j = 0; j < strArrFixField.Length; j++)
                {
                    if (drSortRow[strArrFixField[j]]?.ToString() != strArrFieldValue[j])
                    {
                        bIsNewRow = true;
                        break;
                    }
                }

                //是新行
                if (bIsNewRow)
                {
                    //是新行且不是第一行，加入以前生成的行,
                    if (i > 0)
                    {
                        //计算合计列
                        drResultRow["合计"] = dRowSum;
                        dtResultTable.Rows.Add(drResultRow);
                        dRowSum = 0;
                    }
                    //再生成一新行，赋固定字段的值
                    drResultRow = dtResultTable.NewRow();

                    for (int j = 0; j < strArrFixField.Length; j++)
                    {
                        drResultRow[strArrFixField[j]] = drSortRow[strArrFixField[j]];
                        strArrFieldValue[j] = drSortRow[strArrFixField[j]]?.ToString();
                    }
                }
                //交叉列赋值
                drResultRow[drSortRow[strCrossField].ToString()] = drSortRow[strCrossValueField];
                if (drSortRow[strCrossValueField] != DBNull.Value)
                    dRowSum += Math.Round(Cs01Functions.CDecimal(drSortRow[strCrossValueField]), 4, MidpointRounding.AwayFromZero);

                //加入最后一行，
                if (i == (dtSortTable.Rows.Count - 1))
                {
                    drResultRow["合计"] = dRowSum;
                    dtResultTable.Rows.Add(drResultRow);
                }
            }

            return dtResultTable;
        }

        /// <summary>
        /// 将DataTable分组计算
        /// <param name="dtSourceTable">分组计算源表</param>
        /// <param name="strGroupFields">分组字段</param>
        /// <param name="strCountFields">计数字段</param>
        /// <param name="strSumFields">求和字段</param>
        /// <param name="strAvgFields">平均值字段</param>
        /// <returns>分组计算后的DataTable</returns>
        /// </summary>
        public static DataTable TableGroupCalcute(DataTable dtSourceTable, string strGroupFields, string strCountFields, string strSumFields, string strAvgFields)
        {
            //传入的参数检查
            if (dtSourceTable.Rows.Count == 0)
                return dtSourceTable;

            if (strGroupFields.Length < 1)
                return dtSourceTable;

            //分解分组字段数组
            string[] strArrGroupField = strGroupFields.Split(',');
            //分组字段值数组
            string[] strArrGroupValue = new string[strArrGroupField.Length];

            //分解计数字段数组
            Boolean bHasCountField = false;
            string[] strArrCountField = null;
            if (strCountFields.Length > 0)
            {
                bHasCountField = true;
                strCountFields.Split(',');
            }

            //分解求和字段数组
            Boolean bHasSumField = false;
            string[] strArrSumField = null;
            decimal[] iArrSumValue = null;
            if (strSumFields.Length > 0)
            {
                bHasSumField = true;
                strArrSumField = strSumFields.Split(',');
                iArrSumValue = new decimal[strArrSumField.Length];
            }

            //分解平均字段数组
            Boolean bHasAvgField = false;
            string[] strArrAvgField = null;
            decimal[] iArrAvgValue = null;
            if (strAvgFields.Length > 0)
            {
                bHasAvgField = true;
                strArrAvgField = strAvgFields.Split(',');
                iArrAvgValue = new decimal[strArrAvgField.Length];
            }

            DataTable dtResultTable = new DataTable();
            DataColumn dcSourceColumn, dcResultColumn;

            //结果表的列处理
            for (int i = 0; i < dtSourceTable.Columns.Count; i++)
            {
                dcSourceColumn = dtSourceTable.Columns[i];
                //处理分组的列
                if (strGroupFields.IndexOf(dtSourceTable.Columns[i].ColumnName) >= 0)
                {
                    dcResultColumn = new DataColumn(dcSourceColumn.ColumnName, dcSourceColumn.DataType);
                    dtResultTable.Columns.Add(dcResultColumn);
                }

                //处理计数的列
                if (bHasCountField)
                {
                    if (strCountFields.IndexOf(dtSourceTable.Columns[i].ColumnName) >= 0)
                    {
                        dcResultColumn = new DataColumn(dcSourceColumn.ColumnName + "_COUNT", typeof(int));
                        dtResultTable.Columns.Add(dcResultColumn);
                    }
                }

                //处理求和的列
                if (bHasSumField)
                {
                    if (strSumFields.IndexOf(dtSourceTable.Columns[i].ColumnName) >= 0)
                    {
                        dcResultColumn = new DataColumn(dcSourceColumn.ColumnName + "_SUM", typeof(decimal));
                        dtResultTable.Columns.Add(dcResultColumn);
                    }
                }

                //处理平均值的列
                if (bHasAvgField)
                {
                    if (strAvgFields.IndexOf(dtSourceTable.Columns[i].ColumnName) >= 0)
                    {
                        dcResultColumn = new DataColumn(dcSourceColumn.ColumnName + "_AVG", typeof(decimal));
                        dtResultTable.Columns.Add(dcResultColumn);
                    }
                }
            }

            //源数据，进行排序
            DataTable dtSortTable = TableSort(dtSourceTable, strGroupFields);

            DataRow drSortRow = null;
            DataRow drResultRow = null;
            Boolean bIsNewRow = false;
            int iRowCount = 0;

            for (int i = 0; i < dtSortTable.Rows.Count; i++)
            {
                drSortRow = dtSortTable.Rows[i];

                //判断是不是新行
                bIsNewRow = false;
                for (int j = 0; j < strArrGroupValue.Length; j++)
                {
                    if (drSortRow[strArrGroupField[j]]?.ToString() != strArrGroupValue[j])
                    {
                        bIsNewRow = true;
                        break;
                    }
                }

                //是新行
                if (bIsNewRow)
                {
                    //是新行且不是第一行，加入以前生成的行,
                    if (i > 0)
                    {
                        //设置计数的列
                        if (bHasCountField)
                        {
                            for (int j = 0; j < strArrCountField.Length; j++)
                            {
                                drResultRow[strArrCountField[j] + "_COUNT"] = iRowCount;
                            }
                        }

                        //处理求和的列
                        if (bHasSumField)
                        {
                            for (int j = 0; j < strArrSumField.Length; j++)
                            {
                                drResultRow[strArrSumField[j] + "_SUM"] = iArrSumValue[j];
                                iArrSumValue[j] = 0;
                            }
                        }

                        //处理平均值的列
                        if (bHasAvgField)
                        {
                            for (int j = 0; j < strArrAvgField.Length; j++)
                            {
                                drResultRow[strArrAvgField[j] + "_AVG"] = Math.Round(iArrAvgValue[j] / iRowCount, 4, MidpointRounding.AwayFromZero);
                                iArrAvgValue[j] = 0;
                            }
                        }

                        //结果中加入行
                        dtResultTable.Rows.Add(drResultRow);

                        //行计数置0
                        iRowCount = 0;
                    }

                    //再生成一新行，赋分组字段的值
                    drResultRow = dtResultTable.NewRow();

                    for (int j = 0; j < strArrGroupField.Length; j++)
                    {
                        drResultRow[strArrGroupField[j]] = drSortRow[strArrGroupField[j]];
                        strArrGroupValue[j] = drSortRow[strArrGroupField[j]]?.ToString();
                    }
                }

                iRowCount++;

                //进行求和计算
                if (bHasSumField)
                {
                    for (int j = 0; j < strArrSumField.Length; j++)
                    {
                        if (drSortRow[strArrSumField[j]] == DBNull.Value) continue;
                        iArrSumValue[j] += Math.Round(Cs01Functions.CDecimal(drSortRow[strArrSumField[j]]), 4, MidpointRounding.AwayFromZero);
                    }
                }

                //进行平均值计算
                if (bHasAvgField)
                {
                    for (int j = 0; j < strArrAvgField.Length; j++)
                    {
                        if (drSortRow[strArrSumField[j]] == DBNull.Value) continue;
                        iArrAvgValue[j] += Math.Round(Cs01Functions.CDecimal(drSortRow[strArrAvgField[j]]), 4, MidpointRounding.AwayFromZero);
                    }
                }

                //最后一行
                if (i == (dtSortTable.Rows.Count - 1))
                {
                    //设置计数的列
                    if (bHasCountField)
                    {
                        for (int j = 0; j < strArrCountField.Length; j++)
                        {
                            drResultRow[strArrCountField[j] + "_COUNT"] = iRowCount;
                        }
                    }

                    //处理求和的列
                    if (bHasSumField)
                    {
                        for (int j = 0; j < strArrSumField.Length; j++)
                        {
                            drResultRow[strArrSumField[j] + "_SUM"] = iArrSumValue[j];
                        }
                    }

                    //处理平均值的列
                    if (bHasAvgField)
                    {
                        for (int j = 0; j < strArrAvgField.Length; j++)
                        {
                            drResultRow[strArrAvgField[j] + "_AVG"] = Math.Round(iArrAvgValue[j] / iRowCount, 4, MidpointRounding.AwayFromZero);
                        }
                    }
                    //加入最后一行，
                    dtResultTable.Rows.Add(drResultRow);
                }
            }

            return dtResultTable;
        }

        /// <summary>
        /// 表分栏
        /// </summary>
        /// <param name="dtSourceTable">源表</param>
        /// <param name="iNUpCount">分栏数</param>
        /// <returns></returns>
        public static DataTable TableNUp(DataTable dtSourceTable, int iNUpCount)
        {
            if (iNUpCount <= 1)
                return dtSourceTable;

            if (dtSourceTable.Rows.Count < 1)
                return dtSourceTable;

            //根据源表，生成列
            DataTable dtResult = new DataTable();
            for (int i = 0; i < iNUpCount; i++)
            {
                DataTable dtTmp = dtSourceTable.Clone();
                for (int k = 0; k < dtTmp.Columns.Count; k++)
                {
                    dtTmp.Columns[k].ColumnName += i.ToString();
                }

                dtResult.Merge(dtTmp);
            }

            int iNewTableRows = 0;
            int iCurrentCol = 0;
            DataRow drInsert = null;
            for (int i = 0; i < dtSourceTable.Rows.Count; i++)
            {
                //取余，得当前列号
                iCurrentCol = i % iNUpCount;

                if (i >= iNewTableRows * iNUpCount)
                {
                    if (drInsert != null)
                        dtResult.Rows.Add(drInsert);

                    drInsert = dtResult.NewRow();
                    iNewTableRows++;
                }

                for (int k = 0; k < dtSourceTable.Columns.Count; k++)
                {
                    string strColName = dtSourceTable.Columns[k].ColumnName + iCurrentCol;
                    drInsert[strColName] = dtSourceTable.Rows[i][k];
                }

                //最后一次
                if ((i == dtSourceTable.Rows.Count - 1) && drInsert != null)
                    dtResult.Rows.Add(drInsert);
            }

            return dtResult;
        }
        
    }

}
