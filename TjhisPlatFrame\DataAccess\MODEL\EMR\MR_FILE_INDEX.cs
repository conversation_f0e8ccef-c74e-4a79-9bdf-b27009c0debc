﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;

namespace Model.EMR
{
    public class MR_FILE_INDEX
    {
        #region Model
        private string _patient_id;
        private int _visit_id;
        private int _file_no;
        private string _file_name;
        private string _topic;
        private string _creator_name;
        private string _creator_id;
        private DateTime? _create_date_time;
        private DateTime _last_modify_date_time;
        private DateTime? _file_modify_date_time;
        private string _flag;
        private string _file_attr;
        private string _print_flag;
        private string _mr_code;
        private string _data_model_code;
        private string _parent_name;
        private string _parent_id;
        private string _file_flag;
        private DateTime? _modify_date_time;
        private string _epr_index;
        private DateTime? _super_date_time;
        private string _super_id;
        private string _super_name;
        private DateTime? _sign_paper_datetime;
        private int _file_order;
        private DateTime? _parent_sign_paper_datetime;
        private DateTime? _super_sign_paper_datetime;
        private string _file_creator_grade = "0";
        private DateTime? _hand_sign_paper_datetime;
        private string _evaluate_pass_falg;
        private int? _patient_sign_flag = 0;
        private DateTime? _patient_sign_datetime;
        private string _app_no;
        private int _signature_no;
        private int _patient_signature_no;
        private string _study_doctor_id;
        private string _study_doctor_name;
        private string _take_flag;
        private string _first_sign_dept_code;
        private string _diag_write_indicator;
        private int    _sing_flag;
        private DateTime? _CREATE_DISPLAY_DATE_TIME;
        private string _create_dept_code;
        /// <summary>
        /// 病人ID
        /// </summary>
        public string PATIENT_ID
        {
            set { _patient_id = value; }
            get { return _patient_id; }
        }
        /// <summary>
        /// 住院标识
        /// </summary>
        
        public int VISIT_ID
        {
            set { _visit_id = value; }
            get { return _visit_id; }
        }
        /// <summary>
        /// 文件序号
        /// </summary>
        public int FILE_NO
        {
            set { _file_no = value; }
            get { return _file_no; }
        }
        /// <summary>
        /// 文件名
        /// </summary>
        public string FILE_NAME
        {
            set { _file_name = value; }
            get { return _file_name; }
        }
        /// <summary>
        /// 主题
        /// </summary>
        public string TOPIC
        {
            set { _topic = value; }
            get { return _topic; }
        }
        /// <summary>
        /// 创建者姓名
        /// </summary>
        public string CREATOR_NAME
        {
            set { _creator_name = value; }
            get { return _creator_name; }
        }
        /// <summary>
        /// 创建者ID
        /// </summary>
        public string CREATOR_ID
        {
            set { _creator_id = value; }
            get { return _creator_id; }
        }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CREATE_DATE_TIME
        {
            set { _create_date_time = value; }
            get { return _create_date_time; }
        }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LAST_MODIFY_DATE_TIME
        {
            set { _last_modify_date_time = value; }
            get { return _last_modify_date_time; }
        }
        /// <summary>
        /// 
        /// </summary>
        public DateTime? FILE_MODIFY_DATE_TIME
        {
            set { _file_modify_date_time = value; }
            get { return _file_modify_date_time; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string FLAG
        {
            set { _flag = value; }
            get { return _flag; }
        }
        /// <summary>
        /// 病历类别:A住院志/B病程记录/C会诊记录/T知情文件/R其它记录/H体征库/S症状库/Z知识库
        /// </summary>
        public string FILE_ATTR
        {
            set { _file_attr = value; }
            get { return _file_attr; }
        }
        /// <summary>
        /// 打印标志，用于病程续打
        /// </summary>
        public string PRINT_FLAG
        {
            set { _print_flag = value; }
            get { return _print_flag; }
        }
        /// <summary>
        /// 病历模板编码
        /// </summary>
        public string MR_CODE
        {
            set { _mr_code = value; }
            get { return _mr_code; }
        }
        /// <summary>
        /// 病历模板数据集编码
        /// </summary>
        public string DATA_MODEL_CODE
        {
            set { _data_model_code = value; }
            get { return _data_model_code; }
        }
        /// <summary>
        /// 上级医生签名姓名
        /// </summary>
        public string PARENT_NAME
        {
            set { _parent_name = value; }
            get { return _parent_name; }
        }
        /// <summary>
        /// 上级医生签名帐号
        /// </summary>
        public string PARENT_ID
        {
            set { _parent_id = value; }
            get { return _parent_id; }
        }
        /// <summary>
        /// 病历签名等级T－未签名 1－经治签名 2－上级签名 3－主任签名
        /// </summary>
        public string FILE_FLAG
        {
            set { _file_flag = value; }
            get { return _file_flag; }
        }
        /// <summary>
        /// 上级医生签名的时间(系统时间，不显示在病历中)
        /// </summary>
        public DateTime? MODIFY_DATE_TIME
        {
            set { _modify_date_time = value; }
            get { return _modify_date_time; }
        }
        /// <summary>
        /// 电子病历索引标志
        /// </summary>
        public string EPR_INDEX
        {
            set { _epr_index = value; }
            get { return _epr_index; }
        }
        /// <summary>
        /// 主任医生签名的时间(系统时间，不显示在病历中)
        /// </summary>
        public DateTime? SUPER_DATE_TIME
        {
            set { _super_date_time = value; }
            get { return _super_date_time; }
        }
        /// <summary>
        /// 主任医生签名帐号
        /// </summary>
        public string SUPER_ID
        {
            set { _super_id = value; }
            get { return _super_id; }
        }
        /// <summary>
        /// 主任医生签名姓名
        /// </summary>
        public string SUPER_NAME
        {
            set { _super_name = value; }
            get { return _super_name; }
        }
        /// <summary>
        /// 经治医生签名的时间(显示在病历中的时间)
        /// </summary>
        public DateTime? SIGN_PAPER_DATETIME
        {
            set { _sign_paper_datetime = value; }
            get { return _sign_paper_datetime; }
        }

        /// <summary>
        /// 病程病历排列序号
        /// </summary>
        public int FILE_ORDER
        {
            set { _file_order = value; }
            get { return _file_order; }
        }
        /// <summary>
        /// 上级医生签名的时间(显示在病历中的时间)
        /// </summary>
        public DateTime? PARENT_SIGN_PAPER_DATETIME
        {
            set { _parent_sign_paper_datetime = value; }
            get { return _parent_sign_paper_datetime; }
        }
        /// <summary>
        /// 主任医生签名的时间(显示在病历中的时间)
        /// </summary>
        public DateTime? SUPER_SIGN_PAPER_DATETIME
        {
            set { _super_sign_paper_datetime = value; }
            get { return _super_sign_paper_datetime; }
        }
        /// <summary>
        /// 病程片段创建等级,默认是0,经治医生为1,上级医生2,主任医生3
        /// </summary>
        public string FILE_CREATOR_GRADE
        {
            set { _file_creator_grade = value; }
            get { return _file_creator_grade; }
        }
        /// <summary>
        /// 
        /// </summary>
        public DateTime? HAND_SIGN_PAPER_DATETIME
        {
            set { _hand_sign_paper_datetime = value; }
            get { return _hand_sign_paper_datetime; }
        }
        /// <summary>
        /// 审核通过标志,1通过
        /// </summary>
        public string EVALUATE_PASS_FALG
        {
            set { _evaluate_pass_falg = value; }
            get { return _evaluate_pass_falg; }
        }
        /// <summary>
        /// 患者及家属签名标志
        /// </summary>
        public int? PATIENT_SIGN_FLAG
        {
            set { _patient_sign_flag = value; }
            get { return _patient_sign_flag; }
        }
        /// <summary>
        /// 患者及家属签名时间
        /// </summary>
        public DateTime? PATIENT_SIGN_DATETIME
        {
            set { _patient_sign_datetime = value; }
            get { return _patient_sign_datetime; }
        }
        /// <summary>
        /// 申请单序号,包括:会诊记录\检查检验记录编号
        /// </summary>
        public string APP_NO
        {
            set { _app_no = value; }
            get { return _app_no; }
        }

        /// <summary>
        /// 签章序号
        /// </summary>
        public int SIGNATURE_NO
        {
            set { _signature_no = value; }
            get { return _signature_no; }
        }

        /// <summary>
        /// 病人签章序号
        /// </summary>
        public int PATIENT_SIGNATURE_NO
        {
            set { _patient_signature_no = value; }
            get { return _patient_signature_no; }
        }
        
        /// <summary>
        /// 实习医生id
        /// </summary>
        public string STUDY_DOCTOR_ID
        { 
            set { _study_doctor_id = value; }
            get { return _study_doctor_id; }
        }
        /// <summary>
        /// 实习医生姓名
        /// </summary>
        public string STUDY_DOCTOR_NAME 
        {
            set { _study_doctor_name = value; }
            get { return _study_doctor_name; }
        }

        /// <summary>
        /// 领取标志0未申领，1已申领
        /// </summary>
        public string TAKE_FLAG 
        {
            set { _take_flag = value; }
            get { return _take_flag; }
        }
        /// <summary>
        /// 第一次签名时病人所在科室
        /// </summary>
        public string FIRST_SIGN_DEPT_CODE 
        {
            set { _first_sign_dept_code = value; }
            get { return _first_sign_dept_code; }
        }
        /// <summary>
        /// 诊断书写方式(取值：同参数MR_ADMISSION_NOTE_TCM的取值内容列表相同)
        /// </summary>
        public string DIAG_WRITE_INDICATOR 
        {
            set { _diag_write_indicator = value; }
            get { return _diag_write_indicator; }
        }
        /// <summary>
        /// 住院志初次的诊断模式
        /// </summary>
        public int SING_FLAG 
        {
            set { _sing_flag = value; }
            get { return _sing_flag; }
        }
            
        /// <summary>
        /// 显示在病历上的创建时间(显示在病历中的时间)
        /// </summary>
        public DateTime? CREATE_DISPLAY_DATE_TIME
        {
            set { _CREATE_DISPLAY_DATE_TIME = value; }
            get { return _CREATE_DISPLAY_DATE_TIME; }
        }

        public string CREATE_DEPT_CODE
        {
            set { _create_dept_code = value; }
            get { return _create_dept_code; }
        }
        #endregion Model
    }
}
