﻿//**********************************************
//说明:组合菜单主窗体
//计算机名称：LINDP
//创建日期：2017/4/5 9:48:11
//作者：林大鹏
//版本号：V1.00
//**********************************************
//最后修改日期：2024/3/29 11:48:11
//修改人：YinZhiwei
//版本号：V6.6.4
//**********************************************

using DevExpress.XtraEditors;
using DevExpress.XtraSplashScreen;
using DevExpress.XtraTabbedMdi;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Columns;
using DevExpress.XtraTreeList.Nodes;
using PlatCommon.SysBase;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using NM_Service.NMService;
using Model;
using System.Diagnostics;
using System.Media;
using PlatCommon.Comm;
using System.Threading;
using System.IO;
using DevExpress.XtraBars;
using DevExpress.XtraEditors.Repository;
using DevExpress.LookAndFeel;
using DevExpress.Skins;
using DevExpress.Utils.Svg;
using DevExpress.XtraBars.Navigation;
using DevExpress.Utils;


namespace PlatCommonForm
{
    /// <summary>
    /// 主框架窗体
    /// </summary>
    public partial class FrmNewMain : XtraForm
    {
        #region 公共变量        
        /// <summary>
        /// 应用编码
        /// </summary>
        public string AppCode = string.Empty;
        /// <summary>
        /// 应用名称
        /// </summary>
        public string AppName = string.Empty;
        /// <summary>
        /// 授权码
        /// </summary>
        public string EncryptCode = string.Empty;
        /// <summary>
        /// 当前科室编码
        /// </summary>
        public string DeptCode = string.Empty;
        /// <summary>
        /// 科室名称
        /// </summary>
        public string DeptName = string.Empty;
        /// <summary>
        /// 当前病区编码
        /// </summary>
        public string WardCode = string.Empty;
        /// <summary>
        /// 病区名称
        /// </summary>
        public string WardName = string.Empty;
        /// <summary>
        /// 当前应用科室列表
        /// </summary>
        public string DeptList = string.Empty;
        /// <summary>
        /// 当前应用病区列表
        /// </summary>
        public string WardList = string.Empty;

        /// <summary>
        /// 临床路径启用标志 (0-不启用,默认；1-启用)
        /// </summary>
        public string Clinical_Pathway = "0";

        /// <summary>
        /// 用户权限（app_grants.Capability)
        /// </summary>
        public string Capability;

        /// <summary>
        /// 核算组使用方式(0-诊疗组核算;1-核算组核算),参数 CLINIC_CLASS设置
        /// </summary>
        public string ClinicClass;

        /// <summary>
        /// 母亲和新生儿是否一起出科，1是，0否，参数 NEW_BORN_OUT设置
        /// </summary>        
        public string NewBornOut;

        /// <summary>
        /// 护理单元护士角色(1-普通护士、2-护士长、3-大科护士长、4-护理部人员)，表NR_DICT_NURSE_ROLE设置
        /// </summary>
        public string NurseRole;

        /// <summary>
        /// 是否全病区(护理管理）
        /// </summary>
        public bool IsAllDept = false;
        /// <summary>
        /// 护士权限科室（护理管理、门诊护士站）
        /// </summary>
        public DataTable DutyRightDept;
        /// <summary>
        /// 护理管理报表设计角色设定(1-护理部,2-大护士长,3-护士长,4-护士。例：1,2,3),参数REPORT_DESIGN_ROLE设置
        /// </summary>
        public string ReportDesignRoles;

        /// <summary>
        /// 人员所有授权的职务,表 NURADM_DUTY_RIGHT设置
        /// </summary>
        public DataTable NurAdmDutyRight;
        /// <summary>
        /// 医生出诊诊室名称，即队列名称,表OUTP_DOCTOR_SCHEDULE
        /// </summary>
        public string QueueName;

        #endregion

        #region Field
        private string default_menu_path = "Images/Menu/Svg/默认菜单.svg";  //默认菜单图标    

        /// <summary>
        /// 框架主题
        /// </summary>
        private string skinName = "亮银主题"; //框架主题

        /// <summary>
        /// svg图片集合 key，图片名称
        /// </summary>
        private Dictionary<string, SvgImage> svgImages = new Dictionary<string, SvgImage>();
        private Dictionary<string, SvgImage> menuSvgImages = new Dictionary<string, SvgImage>();
        #endregion

        #region 构造函数
        public FrmNewMain()
        {
            Rectangle current = Screen.GetWorkingArea(this);
            this.MaximizedBounds = new Rectangle(0, 0, current.Width, current.Height);
            this.WindowState = FormWindowState.Maximized;
            InitializeComponent();
            if (PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME.ToUpper().Equals("TJSYSTEM"))
            {
                barBtnSystemSetting.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
            }
            else
            {
                barBtnSystemSetting.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }
            // 界面字号
            string FontUsed = PlatCommon.SysBase.SystemParm.GetParameterValue("FONT_USED", "*", "*", "*", SystemParm.HisUnitCode);
            if (FontUsed.Equals("1"))
            {
                this.barfont.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
                string strFont = Utility.ConfigHelper.GetConfigByXml("FontWeight.xml", "parameter", "FontWeight").ToString(); ;
                if (strFont == "12")
                {
                    barfont.EditValue = "特大号字体";
                }
                else if (strFont == "11")
                {
                    barfont.EditValue = "大号字体";
                }
                else if (strFont == "9")
                {
                    barfont.EditValue = "标准字体";
                }
                else
                {
                    barfont.EditValue = "中号字体";
                }
            }
        }
        #endregion

        #region 窗体事件
        /// <summary>
        /// 加载事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmNewMain_Load(object sender, EventArgs e)
        {
            //1、初始化参数
            PlatCommon.Common.PublicFunction.InitSystemPara(this.AppCode);
            lblHospital.Text = SystemParm.HospitalID + "[" + this.AppName + "]" + "[" + this.DeptName + "]";
            lblLoginName.Text = SystemParm.LoginUser.NAME;
            lblDate.Text = Convert.ToDateTime(PlatCommon.Common.PublicFunction.GetSysDate()).ToString("yyyy年M月d日 dddd");
            lblDept.Text = this.DeptName;
            SetFormTitleIcon();
            SetExpendIcon();

            //2、获取本地配置风格 如果不为空则取本地配置的皮肤风格
            GetSkins();
            try
            {
                skinName = Utility.ConfigHelper.GetConfiguration("skinName");//获取选择的皮肤风格
                barEditItem_skin.EditValue = skinName;
                UseSelectSkin(skinName);
            }
            catch (Exception)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("设置皮肤出错，请和系统管理人员联系", "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            //3、判断授权                                 
            string msg = "";
            int iDay = 0;
            string strName;
            bool rev = PlatCommon.Common.PublicFunction.GetSystemAuthorization(SystemParm.HisUnitCode, AppCode, AppName, "6.6", EncryptCode, ref msg, ref iDay);
            if (rev)
            {
                if (!string.IsNullOrEmpty(msg))
                {
                    strName = AppName + "【" + iDay.ToString() + "天后到期】";
                }
                else
                {
                    strName = AppName;
                }
            }
            else
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(msg, "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            this.Text = strName + "【" + DeptName + "】";

            //4、药库、药房管理系统默认显示“工作提醒”界面(每个模块打开时默认开启的界面可以统一进行处理 TODO )         
            if (AppCode == "PHSTOCK" || AppCode == "STOCKMGR")
            {
                ParentForm frm = GetFormInDll("Tjhis.Phstock.Station.dll", "Tjhis.Phstock.Station.Phstock.DrugStorage.FrmMessage");
                frm.StartPosition = FormStartPosition.CenterParent;
                frm.WindowState = FormWindowState.Maximized;
                frm.Text = "工作提醒";
                frm.DeptCode = this.DeptCode;
                frm.WardCode = this.WardCode;
                frm.AppCode = this.AppCode;
                frm.DeptList = this.DeptList;
                frm.WardList = this.WardList;
                frm.WardName = this.WardName;
                frm.DeptName = this.DeptName;
                frm.IsMdiContainer = false;
                frm.MdiParent = this;
                frm.Show();
            }
            SplashScreenManager.CloseForm(false);

            //5、获取当前登录用户的医保医师编码
            string doc_insur_code = new NM_Service.NMService.ServerPublicClient().GetSingleValue("select t.insur_doc from staff_dict t where t.user_name ='" + PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME + "'");
            barStaticItemUser.Caption = PlatCommon.SysBase.SystemParm.LoginUser.NAME + "(" + doc_insur_code + ")";
            barStaticItemUserBottom.Caption = barStaticItemUser.Caption;
            //6、获取客户机IP
            string ip = PlatCommon.Common.PublicFunction.GetLoacalIP();
            if (!string.IsNullOrEmpty(ip))
            {
                this.bsitemIPaddress.Caption = ip;
            }
            //7、 获取临床路径启用标识
            this.Clinical_Pathway = PlatCommon.SysBase.SystemParm.GetParameterValue("CLINICAL_PATHWAY", AppCode, "*", "*", SystemParm.HisUnitCode);
            this.barStaticItem_hospital.Caption = SystemParm.HospitalID;

            //8、验证各个模块的特殊权限、加载各模块特殊的参数和变量 Yzw20240401
            if(!CheckAppData(this.AppCode))
            {
                return;
            }
            //9、加载菜单
            DataTable dataMenu = InitMenuData(AppCode, strName,PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME); //获取菜单
            if (dataMenu != null && dataMenu.Rows.Count > 0)
            {
                InitTopMenu(dataMenu);
                InitLeftMenu(dataMenu);
            }

            //10、启动灵医智慧知识库客户端
            string cdss_switch = PlatCommon.SysBase.SystemParm.GetParameterValue("CDSS_FLAG", this.AppCode, "*", "*", SystemParm.HisUnitCode);
            if (cdss_switch.Equals("1"))
            {
                PlatCommon.Comm.ClassKfzCdssLyzh.OpenCdss();
            }
            barStaticItem6.Caption = this.DeptName; //科室名称显示

            //11、打开各个模块的默认窗口
            string default_form = PlatCommon.SysBase.SystemParm.GetParameterValue("DEFAULT_FORM", this.AppCode, "*", "*", SystemParm.HisUnitCode);
            if (!string.IsNullOrEmpty(default_form))
            {
                foreach (DataRow menuItem in dataMenu.Rows)
                {
                    string display = menuItem["DISPLAY_TEXT"].ToString();
                    if (default_form.Equals(display))
                    {
                        OpenForm(menuItem);
                        break;
                    }
                }
            }

            //定时任务新增刷新授权
            string authorizefreq = PlatCommon.SysBase.SystemParm.GetParameterValue("AUTHORIZE_FREQ", "*", "*", "*", SystemParm.HisUnitCode);
            if (string.IsNullOrEmpty(authorizefreq)) authorizefreq = "10";
            int li_freq = 600000;
            try
            {
                li_freq = int.Parse(authorizefreq) * 60000;
            }
            catch
            {

            }
            timer1.Enabled = true;
            timer1.Interval = li_freq; //默认10分钟
            timer1.Start();

        }

        /// <summary>
        /// FormClosing 事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmNewMain_FormClosing(object sender, FormClosingEventArgs e)
        {
            DialogResult ok = XtraMessageBox.Show("是否退出程序？", "提示", MessageBoxButtons.YesNo);
            if (ok == DialogResult.No)
            {
                e.Cancel = true;
            }

            //1、关闭知识库客户端
            string cdss_switch = PlatCommon.SysBase.SystemParm.GetParameterValue("CDSS_FLAG", this.AppCode, "*", "*", SystemParm.HisUnitCode);
            if (cdss_switch.Equals("1"))
            {
                PlatCommon.Comm.ClassKfzCdssLyzh.CloseCdss();
            }

            //2、 住院医生站、临床路径和电子病历关闭提醒消息Yzw20230411
            if (AppCode.Equals("EMRINP") || AppCode.Equals("DOCTWS") || AppCode.Equals("CP"))
            {
                try
                {
                    foreach (Form frm in Application.OpenForms)
                    {
                        if (frm.Name == "FrmFloatingForm")
                        {
                            frm.Dispose();
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    XtraMessageBox.Show(ex.Message);
                }
            }

            
        }

        /// <summary>
        /// FormClosed 事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmNewMain_FormClosed(object sender, FormClosedEventArgs e)
        {

            try
            {
                //1、保存操作员登录和登出日志,并更新在线状态
                PlatCommon.Common.PublicFunction.gf_login_logout(this.AppName, PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME, "logout");
                //2、关闭时关闭院感程序  20180123 张鹏
                Process[] processes = Process.GetProcesses();
                foreach (Process p in processes)
                {
                    if (p.ProcessName == "RawTcpClientDEMO")
                    {
                        p.Kill();
                    }
                }

                //关闭所有窗体，释放资源
                CloseAllWindows();

                //3、恢复选择模块窗体状态
                //if (Application.OpenForms.Count < 2)
                //{
                //    if (Application.OpenForms[0].Name.Equals("frmLayoutView"))
                //    {
                //        Application.OpenForms[0].WindowState = FormWindowState.Maximized;
                //    }
                //}
                //备注：根据20240604会议中大家提出的在项目上反馈问题（在关闭所有窗体后，选择模块的界面偶发的不恢复导致程序假死问题）上面的代码修改为下面代码
                if (Application.OpenForms.Count > 0)
                {
                    bool isSelect = false;
                    for (int i = 0;i< Application.OpenForms.Count;i++)
                    {
                        if (Application.OpenForms[i].Name.Equals("frmLayoutView"))
                        {
                            isSelect = true;
                            Application.OpenForms[i].WindowState = FormWindowState.Maximized;
                            Application.OpenForms[i].Show();
                            break;
                        }
                    }
                    if (!isSelect)
                    {
                        System.Environment.Exit(0);
                    }
                }
            }
            catch
            {
            }
            GC.Collect();
        }

        #endregion

        #region 主题皮肤设置
        /// <summary>
        /// 获取主题皮肤
        /// </summary>
        private void GetSkins()
        {
            RepositoryItemComboBox comboBox = barEditItem_skin.Edit as RepositoryItemComboBox;
            DevExpress.UserSkins.BonusSkins.Register();
            foreach (DevExpress.Skins.SkinContainer cnt in DevExpress.Skins.SkinManager.Default.Skins)
            {
                switch (cnt.SkinName)
                {
                    case "DevExpress Style":
                        comboBox.Items.Add("亮银主题");
                        break;
                    case "Office 2010 Blue":
                        comboBox.Items.Add("淡蓝主题");
                        break;
                    case "The Bezier":
                        comboBox.Items.Add("樱花粉主题");
                        comboBox.Items.Add("森林绿主题");
                        break;
                }
            }
        }
        /// <summary>
        /// 应用主题皮肤
        /// </summary>
        /// <param name="skinName">皮肤名称</param>
        /// <param name="isActiveWindow">是否激活窗体</param>
        private void UseSelectSkin(string skinName, bool isActiveWindow = true)
        {
            this.SuspendLayout();
            switch (skinName)
            {
                case "亮银主题":
                    if (isActiveWindow)
                    {
                        DevExpress.LookAndFeel.UserLookAndFeel.Default.SkinName = "DevExpress Style";
                    }
                    palMenu.BackColor = Color.FromArgb(65, 124, 255);
                    tablePanel_mediumMenu.BackColor = Color.FromArgb(235, 236, 239);
                    labelControl_expend.BackColor = Color.FromArgb(235, 236, 239);
                    break;
                case "淡蓝主题":
                    if (isActiveWindow)
                    {
                        DevExpress.LookAndFeel.UserLookAndFeel.Default.SkinName = "Office 2010 Blue";
                    }
                    palMenu.BackColor = Color.FromArgb(65, 124, 255);
                    tablePanel_mediumMenu.BackColor = Color.FromArgb(207, 221, 238);
                    labelControl_expend.BackColor = Color.FromArgb(207, 221, 238);
                    break;
                case "樱花粉主题":
                    if (isActiveWindow)
                    {
                        SetPink();
                    }
                    palMenu.BackColor = Color.FromArgb(240, 120, 176);
                    tablePanel_mediumMenu.BackColor = Color.FromArgb(255, 242, 248);
                    labelControl_expend.BackColor = Color.FromArgb(255, 242, 248);
                    break;
                case "森林绿主题":
                    if (isActiveWindow)
                    {
                        SetGreen();
                    }
                    palMenu.BackColor = Color.FromArgb(92, 178, 132);
                    tablePanel_mediumMenu.BackColor = Color.FromArgb(216, 240, 227);
                    labelControl_expend.BackColor = Color.FromArgb(216, 240, 227);
                    break;
                default:
                    if (isActiveWindow)
                    {
                        DevExpress.LookAndFeel.UserLookAndFeel.Default.SkinName = "DevExpress Style";
                    }
                    palMenu.BackColor = Color.FromArgb(65, 124, 255);
                    tablePanel_mediumMenu.BackColor = Color.FromArgb(235, 236, 239);
                    labelControl_expend.BackColor = Color.FromArgb(235, 236, 239);
                    break;
            }
            SetTileBarItemColor(skinName);
            if (!isActiveWindow)
            {
                this.barEditItem_skin.EditValue = skinName;
            }
            this.ResumeLayout(false);
        }
        /// <summary>
        /// 森林绿主题设置
        /// </summary>
        private void SetGreen()
        {
            DevExpress.LookAndFeel.UserLookAndFeel.Default.SetSkinStyle(DevExpress.LookAndFeel.SkinSvgPalette.Bezier.VSLight);

            //obtain a vector skin
            var commonSkin = CommonSkins.GetSkin(LookAndFeel);
            //create a new palette
            SvgPalette svgPalette = commonSkin.SvgPalettes[Skin.DefaultSkinPaletteName].CustomPalette;
            svgPalette.Colors.Clear();

            //set up palette colors
            svgPalette.Colors.Add(new SvgColor("Paint", Color.FromArgb(248, 248, 248)));
            svgPalette.Colors.Add(new SvgColor("Paint High", Color.FromArgb(255, 255, 255)));
            svgPalette.Colors.Add(new SvgColor("Paint Shadow", Color.FromArgb(216, 240, 227)));
            svgPalette.Colors.Add(new SvgColor("Paint Deep Shadow", Color.FromArgb(230, 230, 230)));

            svgPalette.Colors.Add(new SvgColor("Brush", Color.FromArgb(72, 70, 68)));
            svgPalette.Colors.Add(new SvgColor("Brush Light", Color.FromArgb(119, 119, 119)));
            svgPalette.Colors.Add(new SvgColor("Brush High", Color.FromArgb(72, 70, 68)));
            svgPalette.Colors.Add(new SvgColor("Brush Major", Color.FromArgb(156, 216, 185)));
            svgPalette.Colors.Add(new SvgColor("Brush Minor", Color.FromArgb(156, 216, 185)));

            svgPalette.Colors.Add(new SvgColor("Accent Paint", Color.FromArgb(156, 216, 185)));
            svgPalette.Colors.Add(new SvgColor("Accent Paint Dark", Color.FromArgb(227, 244, 235)));
            svgPalette.Colors.Add(new SvgColor("Accent Paint Light", Color.FromArgb(227, 244, 235)));
            svgPalette.Colors.Add(new SvgColor("Accent Paint Lighter", Color.FromArgb(227, 244, 235)));

            svgPalette.Colors.Add(new SvgColor("Accent Brush", Color.FromArgb(71, 71, 71)));
            svgPalette.Colors.Add(new SvgColor("Accent Brush Light", Color.FromArgb(146, 208, 80)));

            svgPalette.Colors.Add(new SvgColor("Key Paint", Color.FromArgb(216, 240, 227)));
            svgPalette.Colors.Add(new SvgColor("Key Brush", Color.FromArgb(72, 70, 68)));
            svgPalette.Colors.Add(new SvgColor("Key Brush Light", Color.FromArgb(150, 150, 150)));

            svgPalette.Colors.Add(new SvgColor("Red", Color.FromArgb(237, 61, 59)));
            svgPalette.Colors.Add(new SvgColor("Green", Color.FromArgb(48, 144, 72)));
            svgPalette.Colors.Add(new SvgColor("Blue", Color.FromArgb(30, 139, 205)));
            svgPalette.Colors.Add(new SvgColor("Yellow", Color.FromArgb(251, 152, 59)));
            svgPalette.Colors.Add(new SvgColor("Black", Color.FromArgb(87, 87, 85)));
            svgPalette.Colors.Add(new SvgColor("Gray", Color.FromArgb(169, 168, 168)));
            svgPalette.Colors.Add(new SvgColor("White", Color.FromArgb(255, 255, 255)));
            //commonSkin.SvgPalettes.GetName(commonSkin);
            commonSkin.SvgPalettes["Normal"].CustomPalette = svgPalette;
            commonSkin.SvgPalettes["Disabled"].CustomPalette = svgPalette;
            commonSkin.SvgPalettes[Skin.DefaultSkinPaletteName].CustomPalette = svgPalette;
            LookAndFeelHelper.ForceDefaultLookAndFeelChanged();
        }
        /// <summary>
        /// 樱花粉主题设置
        /// </summary>
        private void SetPink()
        {
            DevExpress.LookAndFeel.UserLookAndFeel.Default.SetSkinStyle(DevExpress.LookAndFeel.SkinSvgPalette.Bezier.VSLight);

            //obtain a vector skin
            var commonSkin = CommonSkins.GetSkin(LookAndFeel);
            //create a new palette
            SvgPalette svgPalette = commonSkin.SvgPalettes[Skin.DefaultSkinPaletteName].CustomPalette;
            svgPalette.Colors.Clear();
            //set up palette colors
            svgPalette.Colors.Add(new SvgColor("Paint", Color.FromArgb(248, 248, 248)));                    // 窗体背景色
            svgPalette.Colors.Add(new SvgColor("Paint High", Color.FromArgb(255, 255, 255)));              // 控件背景色
            svgPalette.Colors.Add(new SvgColor("Paint Shadow", Color.FromArgb(255, 242, 248)));            // 控件header背景色
            svgPalette.Colors.Add(new SvgColor("Paint Deep Shadow", Color.FromArgb(230, 230, 230)));       // 

            svgPalette.Colors.Add(new SvgColor("Brush", Color.FromArgb(72, 70, 68)));                      //header 文字颜色
            svgPalette.Colors.Add(new SvgColor("Brush Light", Color.FromArgb(119, 119, 119)));             //
            svgPalette.Colors.Add(new SvgColor("Brush High", Color.FromArgb(72, 70, 68)));                 //
            svgPalette.Colors.Add(new SvgColor("Brush Major", Color.FromArgb(255, 196, 223)));             //控件边框颜色
            svgPalette.Colors.Add(new SvgColor("Brush Minor", Color.FromArgb(255, 196, 223)));             //控件内部线框颜色

            svgPalette.Colors.Add(new SvgColor("Accent Paint", Color.FromArgb(255, 196, 223)));            // titlebar 背景色
            svgPalette.Colors.Add(new SvgColor("Accent Paint Dark", Color.FromArgb(255, 242, 248)));            // 最小化按钮hover时背景色
            svgPalette.Colors.Add(new SvgColor("Accent Paint Light", Color.FromArgb(255, 232, 243)));      // 选中行背景色
            svgPalette.Colors.Add(new SvgColor("Accent Paint Lighter", Color.FromArgb(255, 232, 243)));    // hover背景色

            svgPalette.Colors.Add(new SvgColor("Accent Brush", Color.FromArgb(71, 71, 71)));               // title文字颜色
            svgPalette.Colors.Add(new SvgColor("Accent Brush Light", Color.FromArgb(255, 242, 248)));

            svgPalette.Colors.Add(new SvgColor("Key Paint", Color.FromArgb(255, 242, 248)));
            svgPalette.Colors.Add(new SvgColor("Key Brush", Color.FromArgb(72, 70, 68)));
            svgPalette.Colors.Add(new SvgColor("Key Brush Light", Color.FromArgb(150, 150, 150)));

            svgPalette.Colors.Add(new SvgColor("Red", Color.FromArgb(237, 61, 59)));
            svgPalette.Colors.Add(new SvgColor("Green", Color.FromArgb(48, 144, 72)));
            svgPalette.Colors.Add(new SvgColor("Blue", Color.FromArgb(30, 139, 205)));
            svgPalette.Colors.Add(new SvgColor("Yellow", Color.FromArgb(251, 152, 59)));
            svgPalette.Colors.Add(new SvgColor("Black", Color.FromArgb(87, 87, 85)));
            svgPalette.Colors.Add(new SvgColor("Gray", Color.FromArgb(169, 168, 168)));
            svgPalette.Colors.Add(new SvgColor("White", Color.FromArgb(255, 255, 255)));
            LookAndFeelHelper.ForceDefaultLookAndFeelChanged();
        }

        private void SetTileBarItemColor(string skinName)
        {
            this.SuspendLayout();
            if (this.tileBarGroup2.Items != null && this.tileBarGroup2.Items.Count > 0)
            {
                foreach (TileItem item in this.tileBarGroup2.Items)
                {
                    switch (skinName)
                    {
                        case "亮银主题":
                            item.AppearanceItem.Normal.BackColor = Color.FromArgb(235, 236, 239);
                            item.AppearanceItem.Normal.ForeColor = SystemColors.ControlText;
                            break;

                        case "淡蓝主题":
                            item.AppearanceItem.Normal.BackColor = Color.FromArgb(207, 221, 238);
                            item.AppearanceItem.Normal.ForeColor = SystemColors.ControlText;
                            break;

                        case "樱花粉主题":
                            item.AppearanceItem.Normal.BackColor = Color.FromArgb(255, 242, 248);
                            item.AppearanceItem.Normal.ForeColor = SystemColors.ControlText;
                            break;

                        case "森林绿主题":
                            item.AppearanceItem.Normal.BackColor = Color.FromArgb(216, 240, 227);
                            item.AppearanceItem.Normal.ForeColor = SystemColors.ControlText;
                            break;

                        default:
                            item.AppearanceItem.Normal.BackColor = Color.FromArgb(235, 236, 239);
                            item.AppearanceItem.Normal.ForeColor = SystemColors.ControlText;
                            break;
                    }
                }
            }
            this.ResumeLayout(false);
        }
        /// <summary>
        /// 切换皮肤事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void comboBox_skin_SelectedIndexChanged(object sender, EventArgs e)
        {
            ComboBoxEdit comboBox = sender as ComboBoxEdit;
            string skinName = comboBox.Text;
            Utility.ConfigHelper.SetConfiguration("skinName", skinName);//保存选择的皮肤风格到本地配置文件

            UseSelectSkin(skinName);

            FormCollection fc = Application.OpenForms;
            foreach (Form f in fc)
            {
                if (f != null && f != this && f is FrmNewMain fnm)
                {
                    fnm.UseSelectSkin(skinName, false);
                }
            }
        }

        volatile bool hasGetItemData = false;
        private void skinBarSubItem1_GetItemData(object sender, EventArgs e)
        {
            if (!hasGetItemData)
            {
                hasGetItemData = true;
                BarSubItem barSubItem = (BarSubItem)sender;
                BarItemLinkCollection barItemLinks = barSubItem.ItemLinks;
                addSkinItemClick(barItemLinks);
            }
        }
        private void addSkinItemClick(BarItemLinkCollection collection)
        {
            foreach (BarItemLink itemLink in collection)
            {
                if (itemLink.Item is BarSubItem)
                {
                    addSkinItemClick(((BarSubItem)itemLink.Item).ItemLinks);
                }
                else
                {
                    itemLink.Item.ItemClick += changeSkin;
                }
            }
        }
        void changeSkin(object sender, ItemClickEventArgs e)
        {
            string skinName = e.Item.Caption;
            try
            {
                Utility.ConfigHelper.SetConfiguration("skinName", skinName);//获取选择的皮肤风格保存到本地config配置文件中lxm20230914
            }
            catch
            {
            }
        }
        #endregion

        /// <summary>
        /// 根据模块加载菜单
        /// </summary>        
        public DataTable InitMenuData(string appCode, string appName, string userID)
        {
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            // 1、查询模块菜单脚本
            string sql = string.Format(@"SELECT DISTINCT T1.APP_CODE APPLICATION_CODE,T1.NODE_CODE MENU_NAME,'MAINWINDOW' FORM_MENU,T1.FORM_CONTROL,T1.PARENT_NODE_CODE,T1.HAS_TOOLBAR_ITEM,T1.ICON_PRESS,
                                         T1.SERIAL_NO,T1.NODE_TITLE MENU_TEXT,'' TOOL_TIPS,T1.PARENT_NODE_CODE SUPPER_MENU,T1.FILE_NAME OPEN_FILE_NAME,T1.DISPLAY_TEXT DISPLAY_TEXT,
                                         T1.FORM_ID OPEN_FORM,T1.WIN_OPEN_MODE OPEN_PARAM,T1.STATUS MENU_VISIBLE,T1.NODE_TYPE NODE_TYPE,T1.WIN_PARAMETER WIN_PARAMETER,
                                         to_number(T1.ICON_STYLE) ICON_STYLE,T1.LARGE_ICON,T1.ICON_FILE SMALL_ICON,T1.MENU_GROUP,T1.MEMO MENU_MEMOS,T1.HIS_UNIT_CODE   FROM MR_APP_ENTRY T1 
                                         LEFT JOIN MR_RIGHT_VS_UI_OBJECT T2 ON T1.NODE_CODE = T2.UI_OBJECT_ID AND T1.HIS_UNIT_CODE = T2.HIS_UNIT_CODE 
                                         LEFT JOIN MR_ROLE_RIGHT T3 ON T2.RIGHT_ID = T3.RIGHT_ID AND T2.HIS_UNIT_CODE = T3.HIS_UNIT_CODE 
                                         LEFT JOIN MR_USER_ROLE T4 ON T3.ROLE_CODE = T4.ROLE_CODE 
                                         LEFT JOIN STAFF_DICT T5 ON UPPER(T5.USER_NAME) = UPPER(T4.DB_USER)
                                         WHERE T5.USER_NAME = '{0}' AND T2.ENABLE = 1 AND T1.STATUS = 1 AND T1.FORM_CONTROL IS NULL AND T1.APP_CODE = '{1}'
                                            AND T1.HIS_UNIT_CODE = '{2}' ", userID, appCode, SystemParm.HisUnitCode);
            // 如果是TJSYSTEM 用户，直接获取HIS系统管理的菜单
            if ("TJSYSTEM".Equals(userID.ToUpper()))
            {
                appCode = "SYSTEMMGR";
                appName = "HIS系统管理";
                sql = @"SELECT DISTINCT T1.APP_CODE APPLICATION_CODE,T1.NODE_CODE MENU_NAME,'MAINWINDOW' FORM_MENU,T1.FORM_CONTROL,T1.PARENT_NODE_CODE,T1.HAS_TOOLBAR_ITEM,T1.ICON_PRESS,
                             T1.SERIAL_NO,T1.NODE_TITLE MENU_TEXT,'' TOOL_TIPS,T1.PARENT_NODE_CODE SUPPER_MENU,T1.FILE_NAME OPEN_FILE_NAME,T1.DISPLAY_TEXT DISPLAY_TEXT,
                             T1.FORM_ID OPEN_FORM,T1.WIN_OPEN_MODE OPEN_PARAM,T1.STATUS MENU_VISIBLE,T1.NODE_TYPE NODE_TYPE,T1.WIN_PARAMETER WIN_PARAMETER,
                             to_number(T1.ICON_STYLE) ICON_STYLE,T1.LARGE_ICON,T1.ICON_FILE SMALL_ICON,T1.MENU_GROUP,T1.MEMO MENU_MEMOS  FROM MR_APP_ENTRY T1 where  T1.APP_CODE = '" + appCode + "' and   T1.STATUS = 1";
            }
            sql += " ORDER BY T1.PARENT_NODE_CODE, T1.SERIAL_NO";
            DataTable dataMenu = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            return dataMenu;
        }

        /// <summary>
        /// 验证各应用的特殊数据,加载初始数据
        /// </summary>
        /// <param name="appCode">应用编码</param>
        /// <returns></returns>
        private Boolean CheckAppData(string appCode)
        {
            try
            {
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();

                // 1、住院护士站、护理病历、留观护理系统
                if ("NURSWS".Equals(appCode.ToUpper()) || "NURINPREC".Equals(appCode.ToUpper()) || "NUROB".Equals(appCode.ToUpper()))
                {
                    // (1)权限验证，同时对this.Capability赋值
                    string sql1 = @" select  APPLICATION , USER_ID,CAPABILITY,DEMO  from  app_grants
                                where  user_id =  '" + PlatCommon.SysBase.SystemParm.LoginUser.ID + "' and application = '" + appCode + "' ";
                    DataSet dsAppGrants = spc.GetDataBySql(sql1, "app_grants", false);
                    if (!PlatCommon.Common.DataSetHelper.HasRecord(dsAppGrants))
                    {
                        XtraMessageBox.Show("您无权使用本系统！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return false;
                    }
                    else
                    {
                        this.Capability = dsAppGrants.Tables[0].Rows[0]["CAPABILITY"].ToString();
                    }
                    // (2)核算组设置参数
                    this.ClinicClass = PlatCommon.SysBase.SystemParm.GetParameterValue("CLINIC_CLASS", appCode, this.WardCode, "*", SystemParm.HisUnitCode);
                    // (3)母亲和新生儿一起出科参数
                    this.NewBornOut = SystemParm.GetParameterValue(appCode, "NEW_BORN_OUT", this.WardCode, "*", SystemParm.HisUnitCode);

                    // (4)护理单元护士角色
                    string sql2 = "SELECT NURSE_ROLE FROM NR_DICT_NURSE_ROLE WHERE WARD_CODE = '" + this.WardCode + "'  AND NURSE_CODE = '" + SystemParm.LoginUser.USER_NAME + "' ";
                    string roles = spc.ExecuteScalarStr(sql2);
                    if (string.IsNullOrEmpty(roles))
                    {
                        this.NurseRole = "1";
                    }
                    else
                    {
                        this.NurseRole = roles;
                    }
                }
                //2、 护理管理系统
                else if ("NURADM".Equals(appCode.ToUpper()))
                {
                    // （1）获取有权限的护理单元
                    string sql1 = string.Format(@"SELECT (select SERIAL_NO from nuradm_ward_info iw where iw.ward_code = i.dept_code) SERIAL_NO,n.*, i.DEPT_NAME WARD_NAME, i.INPUT_CODE
                                          FROM NURADM_DUTY_RIGHT n
                                          INNER JOIN v_nuradm_union_his_dept i
                                            ON n.WARD_CODE = i.DEPT_CODE
                                         WHERE n.NURSE_CODE = '{0}' AND NURSING_DUTY <> '4'  
                                         ORDER BY SERIAL_NO", PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME);
                    DataSet ds = spc.GetDataBySql(sql1, "NURADM_DUTY_RIGHT", false);

                    this.DutyRightDept = ds.Tables[0];

                    //（2）获取系统报表设计权限并给SystemParm.Capability赋值
                    this.Capability = "1"; //初始设置为1
                    this.ReportDesignRoles = PlatCommon.SysBase.SystemParm.GetParameterValue("REPORT_DESIGN_ROLE", this.AppCode, "*", "*", SystemParm.HisUnitCode);
                    //（3）获取人员所有授权的职务
                    string sql2 = string.Format("SELECT DISTINCT NURSING_DUTY FROM NURADM.NURADM_DUTY_RIGHT WHERE NURSE_CODE = '{0}' ", PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME);
                    this.NurAdmDutyRight = spc.GetDataBySql(sql2, "NURADM_DUTY_RIGHT", false).Tables[0];
                    DataTable dtDutyRole = this.NurAdmDutyRight;
                    int hasRole = 0;
                    if (dtDutyRole != null && dtDutyRole.Rows.Count > 0)
                    {
                        if (!string.IsNullOrEmpty(this.ReportDesignRoles))
                        {
                            string strs = this.ReportDesignRoles;
                            foreach (DataRow dr in dtDutyRole.Rows)
                            {
                                if (strs.IndexOf(dr[0].ToString()) > -1)
                                {
                                    hasRole = 1;
                                    break;
                                }
                            }
                        }
                    }
                    //if (hasRole == 1)
                    //{
                    //    this.Capability = "9"; //管理权限
                    //}
                }
                //3、门诊护士站
                else if ("OUTPNURSE".Equals(appCode.ToUpper()))
                {
                    string sql1 = @"select a.*, b.dept_name from STAFF_VS_GROUP a,dept_dict b
                                where a.group_code = b.dept_code
                                and a.group_class IN('门诊护士')
                                and emp_no = '" + SystemParm.LoginUser.EMP_NO + "'";
                    DataSet ds = spc.GetDataBySql(sql1, "STAFF_VS_GROUP", false);
                    if (PlatCommon.Common.DataSetHelper.HasRecord(ds))
                    {
                        this.DutyRightDept = ds.Tables[0];
                    }
                }
                //4、住院医生工作站、临床路径系统
                if ("DOCTWS".Equals(appCode.ToUpper()) || "CP".Equals(appCode.ToUpper()))
                {
                    //显示病历消息
                    ParentForm form = GetFormInDll("Tjhis.EmrInp.Station.dll", "Tjhis.EmrInp.Message.FrmFloatingForm");
                    if (form != null)
                    {
                        form.TopMost = true;
                        form.Show();
                    }
                }
                //5、 住院电子病历（住院电子病历和住院医生站共用患者列表，初始化数据一致）
                else if ("EMRINP".Equals(appCode.ToUpper()))
                {
                    //显示病历消息
                    ParentForm form = GetFormInDll("Tjhis.EmrInp.Station.dll", "Tjhis.EmrInp.Message.FrmFloatingForm");
                    if (form != null)
                    {
                        form.TopMost = true;
                        form.Show();
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
                return false;
            }
        }

        #region 加载菜单和菜单点击事件        
        /// <summary>
        /// 加载顶部菜单
        /// </summary>
        /// <param name="dataMenu">菜单集合</param>
        public void InitTopMenu(DataTable dataMenu)
        {
            string filter = "PARENT_NODE_CODE = 'parent'";
            DataRow[] parentRow = dataMenu.Select(filter);
            BarSubItem menuItem = null;
            DataRow drParent = null;
            // 一级菜单
            for (int i = 0; i < parentRow.Length; i++)
            {
                drParent = parentRow[i];
                if (i == 0)
                {
                    menuItem = new BarSubItem(barManager2, drParent["MENU_TEXT"].ToString());
                    menuItem.Tag = drParent;
                    topMainMenu.ItemLinks.Add(menuItem);
                    GetSubMenuItem(menuItem, dataMenu);
                }
                else
                {
                    menuItem = new BarSubItem(barManager2, drParent["MENU_TEXT"].ToString());
                    menuItem.Tag = drParent;
                    topMainMenu.ItemLinks.Add(menuItem, true);
                    GetSubMenuItem(menuItem, dataMenu);
                }
            }
        }
        /// <summary>
        /// 加载左侧快捷菜单
        /// </summary>
        /// <param name="dataMenu">菜单集合</param>
        public void InitLeftMenu(DataTable dataMenu)
        {
            string filter = "HAS_TOOLBAR_ITEM > 0 ";
            string order = "HAS_TOOLBAR_ITEM ASC";
            DataRow[] menuItem = dataMenu.Select(filter, order);

            tileBar1.ItemSize = 60;
            GetLeftMenu(menuItem, tileBarGroup2, TileBarItemSize.Medium);
        }
        /// <summary>
        /// 加载子菜单
        /// </summary>
        /// <param name="parentMenu">主菜单</param>
        /// <param name="dataMenuTable">菜单集合</param>
        /// <returns></returns>
        private bool GetSubMenuItem(BarSubItem parentMenu, DataTable dataMenuTable)
        {
            bool hasSubItem = false;
            DataRow parantRow = parentMenu.Tag as DataRow;
            var subMenuRows = dataMenuTable.AsEnumerable()
            .Where(p => p.Field<string>("PARENT_NODE_CODE") == parantRow["MENU_NAME"].ToString());
            if (subMenuRows != null && subMenuRows.Count() > 0)
            {
                hasSubItem = true;
                BarSubItem subMenuItem = null;
                foreach (DataRow subMenuRow in subMenuRows)
                {
                    subMenuItem = new BarSubItem(barManager2, subMenuRow["MENU_TEXT"].ToString());
                    subMenuItem.Tag = subMenuRow;
                    parentMenu.AddItem(subMenuItem);

                    bool hasMenuItem = GetSubMenuItem(subMenuItem, dataMenuTable);
                    if (!hasMenuItem)
                    {
                        subMenuItem.AllowDrawArrow = DevExpress.Utils.DefaultBoolean.False;
                        subMenuItem.ItemClick += SubMenuItem_ItemClick;
                    }
                }
            }
            return hasSubItem;
        }
        private void GetLeftMenu(DataRow[] menuItem, TileBarGroup tileBarGroup, TileBarItemSize itemSize)
        {
            TileBarItem element = null;
            SvgBitmap svgBitmap;
            foreach (DataRow drMenu in menuItem)
            {
                if ("parent".Equals(drMenu["PARENT_NODE_CODE"].ToString().ToLower()))
                {
                    continue;//跳过组节点
                }
                element = new TileBarItem();
                element.ItemSize = itemSize;

                switch (skinName)
                {
                    case "亮银主题":
                        element.AppearanceItem.Normal.BackColor = Color.FromArgb(235, 236, 239);
                        tablePanel_mediumMenu.BackColor = Color.FromArgb(235, 236, 239);
                        labelControl_expend.BackColor = Color.FromArgb(235, 236, 239);
                        break;

                    case "淡蓝主题":
                        element.AppearanceItem.Normal.BackColor = Color.FromArgb(207, 221, 238);
                        tablePanel_mediumMenu.BackColor = Color.FromArgb(207, 221, 238);
                        labelControl_expend.BackColor = Color.FromArgb(207, 221, 238);
                        break;

                    case "樱花粉主题":
                        element.AppearanceItem.Normal.BackColor = Color.FromArgb(255, 242, 248);
                        tablePanel_mediumMenu.BackColor = Color.FromArgb(255, 242, 248);
                        labelControl_expend.BackColor = Color.FromArgb(255, 242, 248);
                        break;

                    case "森林绿主题":
                        element.AppearanceItem.Normal.BackColor = Color.FromArgb(216, 240, 227);
                        tablePanel_mediumMenu.BackColor = Color.FromArgb(216, 240, 227);
                        labelControl_expend.BackColor = Color.FromArgb(216, 240, 227);
                        break;

                    default:
                        element.AppearanceItem.Normal.BackColor = Color.FromArgb(235, 236, 239);
                        tablePanel_mediumMenu.BackColor = Color.FromArgb(235, 236, 239);
                        labelControl_expend.BackColor = Color.FromArgb(235, 236, 239);
                        break;
                }
                element.AppearanceItem.Normal.ForeColor = SystemColors.ControlText;
                string svgKey = drMenu["MENU_NAME"].ToString();

                //菜单
                if (tileBarGroup != null)
                {
                    tileBarGroup.Items.Add(element);
                }
                string menuName = drMenu["DISPLAY_TEXT"].ToString();
                SuperToolTip sTooltip2 = new SuperToolTip();
                // Create an object that initializes tooltip regions.
                SuperToolTipSetupArgs args = new SuperToolTipSetupArgs();
                //args.Title.Text = "Edit Popup Menu";
                args.Contents.Text = menuName;
                //args.Contents.ImageOptions.Image = resImage;
                sTooltip2.Setup(args);
                element.SuperTip = sTooltip2;

                if (itemSize == TileBarItemSize.Wide)
                {
                    element.ImageToTextAlignment = TileControlImageToTextAlignment.Left;
                }
                else
                {
                    element.ImageToTextAlignment = TileControlImageToTextAlignment.Top;
                }

                TileItemElement itemElement = new TileItemElement();
                itemElement.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.TopCenter;
                itemElement.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.TileControlImageToTextAlignment.Top;
                itemElement.Text = menuName;
                itemElement.TextAlignment = TileItemContentAlignment.BottomCenter;
                element.Padding = new Padding(6);
                element.AppearanceItem.Normal.TextOptions.WordWrap = WordWrap.NoWrap;
                element.Tag = drMenu;
                element.Elements.Add(itemElement);

                if (!string.IsNullOrEmpty(drMenu["SMALL_ICON"].ToString()) && System.IO.File.Exists(drMenu["SMALL_ICON"].ToString()))
                {
                    if (drMenu["SMALL_ICON"].ToString().ToLower().IndexOf(".svg") >= 0)
                    {
                        if (!menuSvgImages.ContainsKey(svgKey))
                        {
                            svgBitmap = DevExpress.Utils.Svg.SvgBitmap.FromFile(drMenu["SMALL_ICON"].ToString());
                            menuSvgImages.Add(svgKey, svgBitmap.SvgImage);
                        }
                        itemElement.ImageOptions.SvgImage = menuSvgImages[svgKey];
                        itemElement.ImageOptions.SvgImageSize = new Size(24, 24);
                    }
                    else
                    {
                        itemElement.Image = Image.FromFile(drMenu["SMALL_ICON"].ToString());
                    }
                }
                else
                {
                    if (!menuSvgImages.ContainsKey(default_menu_path))
                    {
                        svgBitmap = DevExpress.Utils.Svg.SvgBitmap.FromFile(default_menu_path);
                        menuSvgImages.Add(default_menu_path, svgBitmap.SvgImage);
                    }
                    itemElement.ImageOptions.SvgImage = menuSvgImages[default_menu_path];
                    itemElement.ImageOptions.SvgImageSize = new Size(24, 24);

                }
                element.ItemClick += Element_ItemClick;
            }
        }
        private void SubMenuItem_ItemClick(object sender, ItemClickEventArgs e)
        {
            BarSubItem barSubItem = e.Item as BarSubItem;
            if (barSubItem == null) return;
            DataRow drMenu = barSubItem.Tag as DataRow;
            if (drMenu == null) return;
            (sender as BarManager).CloseMenus();

            OpenForm(drMenu);
        }

        private void Element_ItemClick(object sender, TileItemEventArgs e)
        {
            TileBarItem tileBarItem = e.Item as TileBarItem;
            if (tileBarItem == null) return;

            DataRow drMenu = tileBarItem.Tag as DataRow;
            if (drMenu == null)
            {
                XtraMessageBox.Show("没有发现当前菜单窗体！");
                return;
            }
            OpenForm(drMenu);
        }
        #endregion

        /// <summary>
        /// winform窗体
        /// </summary>
        /// <param name="dllName"></param>
        /// <param name="typeName"></param>
        /// <param name="treeListNode"></param>
        /// <returns></returns>
        public Form GetFormInDll(string dllName, string typeName, TreeListNode treeListNode)
        {
            try
            {
                object[] parameters = new object[2];
                parameters[0] = treeListNode.GetValue("WIN_PARAMETER").ToString();
                parameters[1] = treeListNode.GetValue("MENU_TEXT").ToString();
                Environment.CurrentDirectory = Application.StartupPath;
                Assembly asmAssembly = Assembly.LoadFrom(dllName);
                Type typeToLoad = asmAssembly.GetType(typeName);
                object GenericInstance = Activator.CreateInstance(typeToLoad, parameters);
                Form formToLoad = (Form)(GenericInstance);

                return formToLoad;
            }
            catch (Exception ex)
            {
                string msg = "DLL名称: " + dllName + " 类名:" + typeName + " 没被发现! ";
                MessageBox.Show(msg);
                return null;
                //throw new Exception(msg + ex.Message);
            }
        }

        /// <summary>
        /// 从动态库中获取窗体
        /// </summary>
        /// <param name="dllName">动态库的名称</param>
        /// <param name="typeName">类名</param>
        /// <returns>窗体类</returns>
        public ParentForm GetFormInDll(string dllName, string typeName, string[] paras = null)
        {
            try
            {
                Environment.CurrentDirectory = Application.StartupPath; //g 116
                Assembly asmAssembly = Assembly.LoadFrom(dllName);
                Type typeToLoad = asmAssembly.GetType(typeName);
                object GenericInstance;

                GenericInstance = Activator.CreateInstance(typeToLoad, paras);

                ParentForm formToLoad;
                formToLoad = (ParentForm)(GenericInstance);
                return formToLoad;
            }
            catch (Exception ex)
            {
                string msg = "DLL名称: " + dllName + " 类名:" + typeName + " 没被发现! ";
                throw new Exception(msg + ex.Message);
            }
        }

        /// <summary>
        /// 从动态库中获取窗体
        /// </summary>
        /// <param name="dllName">动态库的名称</param>
        /// <param name="typeName">类名</param>
        /// <returns>窗体类</returns>
        public Form GetFormWindowInDll(string dllName, string typeName, string[] paras = null, string errorMessage = "")
        {
            try
            {
                Environment.CurrentDirectory = Application.StartupPath; //g 116
                Assembly asmAssembly = Assembly.LoadFrom(dllName);
                Type typeToLoad = asmAssembly.GetType(typeName);
                object GenericInstance;

                GenericInstance = Activator.CreateInstance(typeToLoad, paras);

                Form formToLoad;
                formToLoad = (Form)(GenericInstance);
                return formToLoad;
            }
            catch (Exception ex)
            {
                string msg = string.Empty;
                if (!string.IsNullOrEmpty(errorMessage))
                {
                    msg = "未检测到【" + errorMessage + "】！\r\n";
                }
                msg += "DLL名称: " + dllName + " 类名:" + typeName + " 没被发现! ";
                MessageBox.Show(msg, "提示信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return null;
            }
        }
        #region 打开和关闭窗体的函数
        /// <summary>
        /// 打开窗体
        /// </summary>        
        public void OpenForm(DataRow drMenu)
        {
            if (drMenu == null) return;
            XtraMdiTabPage pg = null;
            string controlName = drMenu["DISPLAY_TEXT"].ToString();
            pg = xtraTabbedMdiManager1.Pages.FirstOrDefault(p => p.Text.ToString().Split(' ')[0].ToString().Equals(controlName));

            if (pg != null)
            {
                xtraTabbedMdiManager1.SelectedPage = pg;
            }
            else
            {
                string[] paras = null;
                string para = drMenu["WIN_PARAMETER"].ToString();//窗体参数
                if (!string.IsNullOrEmpty(para))
                {
                    paras = new string[1];
                    paras[0] = para;
                }
                Form formWin = GetFormWindowInDll(drMenu["OPEN_FILE_NAME"].ToString(), drMenu["OPEN_FORM"].ToString(), paras, controlName);
                if (formWin == null) return;

                ParentForm frm = formWin as ParentForm;
                if (frm == null)
                {
                    //外部窗体
                    frm.ShowDialog();
                    return;
                }

                frm.Text = drMenu["DISPLAY_TEXT"].ToString();
                //窗体变量赋值
                SetParentFormProperty(frm);
                //模态窗体显示（弹窗） 参数5 
                if ("5".Equals(drMenu["OPEN_PARAM"].ToString()))
                {
                    frm.ShowDialog();
                    return;
                }
                frm.IsMdiContainer = false;
                frm.MdiParent = this;
                //图标
                string SMALL_ICON = drMenu["SMALL_ICON"].ToString();
                if (SMALL_ICON.ToString().ToLower().IndexOf(".svg") >= 0)
                {
                    Image svgImage = DevExpress.Utils.Svg.SvgBitmap.FromFile(SMALL_ICON).Render(null, 1);
                    imagefrmchild.AddImage(svgImage);
                }
                else if (!string.IsNullOrEmpty(SMALL_ICON))
                {
                    imagefrmchild.AddImage(Image.FromFile(@SMALL_ICON));
                }
                xtraTabbedMdiManager1.Pages[xtraTabbedMdiManager1.Pages.Count - 1].ImageIndex = imagefrmchild.Images.Count - 1;
                xtraTabbedMdiManager1.Pages[xtraTabbedMdiManager1.Pages.Count - 1].Tooltip = drMenu["APPLICATION_CODE"].ToString();
                frm.Show();
            }
        }
        /// <summary>
        /// 打开窗体-旧版本树形结构打开方式
        /// </summary>
        /// <param name="treeListNode">树形节点</param>
        public void OpenForm(TreeListNode treeListNode)
        {
            if (treeListNode == null) return;
            XtraMdiTabPage pg = null;
            pg = xtraTabbedMdiManager1.Pages.FirstOrDefault(p => p.Text.ToString().Split(' ')[0].ToString().Equals(treeListNode.GetValue("DISPLAY_TEXT").ToString()));
            if (pg != null)
            {
                xtraTabbedMdiManager1.SelectedPage = pg;
            }
            else
            {
                //OPEN_PARAM打开方式是2  WIN_PARAMETER字段有值表示调用电子病历窗体，存的是电子病历的node_code OPEN_PARAM
                if ("2".Equals(treeListNode.GetValue("OPEN_PARAM").ToString()) && !string.IsNullOrEmpty(treeListNode.GetValue("WIN_PARAMETER").ToString()))
                {
                    Form frm = GetFormInDll(treeListNode.GetValue("OPEN_FILE_NAME").ToString(), treeListNode.GetValue("OPEN_FORM").ToString(), treeListNode);
                    if (frm == null) return;
                    frm.StartPosition = FormStartPosition.CenterParent;
                    frm.WindowState = FormWindowState.Maximized;
                    frm.Text = treeListNode.GetValue("DISPLAY_TEXT") != null ? treeListNode.GetValue("DISPLAY_TEXT").ToString() : treeListNode.GetValue("NODE_TITLE").ToString();
                    frm.IsMdiContainer = false;
                    frm.MdiParent = this;

                    string SMALL_ICON = treeListNode.GetValue("SMALL_ICON").ToString();
                    if (!string.IsNullOrEmpty(SMALL_ICON))
                    {
                        imagefrmchild.AddImage(Image.FromFile(@SMALL_ICON));
                    }
                    xtraTabbedMdiManager1.Pages[xtraTabbedMdiManager1.Pages.Count - 1].ImageIndex = imagefrmchild.Images.Count - 1;
                    xtraTabbedMdiManager1.Pages[xtraTabbedMdiManager1.Pages.Count - 1].Tooltip = treeListNode.GetValue("APPLICATION_CODE").ToString();
                    frm.Show();
                }
                else if ("2".Equals(treeListNode.GetValue("OPEN_PARAM").ToString()) && string.IsNullOrEmpty(treeListNode.GetValue("WIN_PARAMETER").ToString()))
                {
                    Form frm = GetFormInDll(treeListNode.GetValue("OPEN_FILE_NAME").ToString(), treeListNode.GetValue("OPEN_FORM").ToString());
                    frm.StartPosition = FormStartPosition.CenterParent;
                    frm.WindowState = FormWindowState.Maximized;
                    frm.Text = treeListNode.GetValue("DISPLAY_TEXT") != null ? treeListNode.GetValue("DISPLAY_TEXT").ToString() : treeListNode.GetValue("NODE_TITLE").ToString();
                    frm.IsMdiContainer = false;
                    frm.MdiParent = this;
                    string SMALL_ICON = treeListNode.GetValue("SMALL_ICON").ToString();
                    if (!string.IsNullOrEmpty(SMALL_ICON))
                    {
                        imagefrmchild.AddImage(Image.FromFile(@SMALL_ICON));
                    }
                    xtraTabbedMdiManager1.Pages[xtraTabbedMdiManager1.Pages.Count - 1].ImageIndex = imagefrmchild.Images.Count - 1;
                    xtraTabbedMdiManager1.Pages[xtraTabbedMdiManager1.Pages.Count - 1].Tooltip = treeListNode.GetValue("APPLICATION_CODE").ToString();
                    frm.Show();
                }
                else
                {
                    string[] paras = null;
                    string para = treeListNode.GetValue("WIN_PARAMETER").ToString();//窗体参数
                    if (!string.IsNullOrEmpty(para))
                    {
                        paras = new string[1];
                        paras[0] = para;
                    }
                    ParentForm frm = GetFormInDll(treeListNode.GetValue("OPEN_FILE_NAME").ToString(), treeListNode.GetValue("OPEN_FORM").ToString(), paras);
                    frm.Text = treeListNode.GetValue("DISPLAY_TEXT").ToString();
                    //窗体变量赋值
                    SetParentFormProperty(frm);

                    //by LiangJi 增加窗体模式显示 参数5 2022/02/13
                    if (treeListNode.GetValue("OPEN_FORM").ToString().Contains(".SelectWardFrm") || treeListNode.GetValue("OPEN_FORM").ToString().Contains("FrmOutpWorkStation") || "5".Equals(treeListNode.GetValue("OPEN_PARAM").ToString()))
                    {
                        frm.ShowDialog();
                        return;
                    }
                    frm.IsMdiContainer = false;
                    frm.MdiParent = this;

                    string SMALL_ICON = treeListNode.GetValue("SMALL_ICON").ToString();
                    if (SMALL_ICON.ToString().ToLower().IndexOf(".svg") >= 0)
                    {
                        Image svgImage = DevExpress.Utils.Svg.SvgBitmap.FromFile(SMALL_ICON).Render(null, 1);
                        imagefrmchild.AddImage(svgImage);
                    }
                    else if (!string.IsNullOrEmpty(SMALL_ICON))
                    {
                        imagefrmchild.AddImage(Image.FromFile(@SMALL_ICON));
                    }
                    xtraTabbedMdiManager1.Pages[xtraTabbedMdiManager1.Pages.Count - 1].ImageIndex = imagefrmchild.Images.Count - 1;
                    xtraTabbedMdiManager1.Pages[xtraTabbedMdiManager1.Pages.Count - 1].Tooltip = treeListNode.GetValue("APPLICATION_CODE").ToString();
                    frm.Show();
                }
            }
        }
        /// <summary>
        /// 打开窗体
        /// </summary>
        /// <param name="frm">继承自ParentForm的子窗体</param>
        /// <param name="iconFile">图标文件</param>
        public void openForm(PlatCommon.SysBase.ParentForm frm, string iconFile)
        {
            try
            {
                FrmNewMain frmMain=this;
                if (frm.Tag != null && !string.IsNullOrWhiteSpace(frm.Tag.ToString()) && frm.Tag != this)
                {
                    frmMain =(FrmNewMain) frm.Tag;
                }
                XtraMdiTabPage pg = null;

                pg = frmMain.xtraTabbedMdiManager1.Pages.FirstOrDefault(p => p.Text.ToString().Split(' ')[0].ToString().Equals(frm.Text));
                if (pg != null)
                    frmMain.xtraTabbedMdiManager1.SelectedPage = pg;
                else
                {
                    frm.MdiParent = frmMain;
                    if (string.IsNullOrEmpty(iconFile)) iconFile = @"Images/Menu/Small/26.png";
                    if (File.Exists(iconFile)) imagefrmchild.AddImage(Image.FromFile(iconFile));
                    frmMain.xtraTabbedMdiManager1.Pages[frmMain.xtraTabbedMdiManager1.Pages.Count - 1].ImageIndex = frmMain.imagefrmchild.Images.Count - 1;
                    frm.Show();
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 打开窗体
        /// </summary>
        /// <param name="frm">继承自ParentForm的子窗体</param>
        /// <param name="iconFile">图标文件</param>
        /// <param name="appCode">应用程序码</param>
        public void openForm(PlatCommon.SysBase.ParentForm frm, string iconFile, string appCode)
        {
            try
            {
                FrmNewMain frmMain = this;
                if (frm.Tag != null && frm.Tag != this)
                {
                    frmMain = (FrmNewMain)frm.Tag;
                }
                //判断此窗体是否已经打开
                XtraMdiTabPage pg = null;
                pg = frmMain.xtraTabbedMdiManager1.Pages.FirstOrDefault(p => p.Text.ToString().Split(' ')[0].ToString().Equals(frm.Text));
                if (pg != null)
                {
                    frmMain.xtraTabbedMdiManager1.SelectedPage = pg;
                }
                else
                {
                    frm.MdiParent = frmMain;
                    if (string.IsNullOrEmpty(iconFile)) iconFile = @"Images/Menu/Small/26.png";
                    if (File.Exists(iconFile)) imagefrmchild.AddImage(Image.FromFile(iconFile));
                    frmMain.xtraTabbedMdiManager1.Pages[frmMain.xtraTabbedMdiManager1.Pages.Count - 1].ImageIndex = frmMain.imagefrmchild.Images.Count - 1;
                    frmMain.AppCode = appCode; //将传递来的应用编码赋给框架窗体
                    frm.AppCode = appCode; //将传递来的应用编码赋给父窗体
                    frm.Show();
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }
        /// <summary>
        /// 重新打开窗体
        /// </summary>
        /// <param name="frm">继承自ParentForm的子窗体</param>
        /// <param name="iconFile">图标文件</param>
        public void openNewForm(PlatCommon.SysBase.ParentForm frm, string iconFile)
        {
            try
            {
                FrmNewMain frmMain = this;
                if (frm.Tag != null && frm.Tag != this)
                {
                    frmMain = (FrmNewMain)frm.Tag;
                }
                //判断此窗体是否已经打开
                XtraMdiTabPage pg = null;
                pg = frmMain.xtraTabbedMdiManager1.Pages.FirstOrDefault(p => p.Text.ToString().Split(' ')[0].ToString().Equals(frm.Text));
                if (pg != null)
                {
                    pg.MdiChild.Close(); //关闭已经打开的窗体
                }
                frm.MdiParent = frmMain;
                if (string.IsNullOrEmpty(iconFile)) iconFile = @"Images/Menu/Small/26.png";
                if (File.Exists(iconFile)) imagefrmchild.AddImage(Image.FromFile(iconFile));
                frmMain.xtraTabbedMdiManager1.Pages[frmMain.xtraTabbedMdiManager1.Pages.Count - 1].ImageIndex = frmMain.imagefrmchild.Images.Count - 1;
                frm.Show();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 关闭所有窗体Tab页
        /// </summary>        
        public void CloseAllWindows()
        {
            if (xtraTabbedMdiManager1.Pages.Count > 0)
            {
                for (int i = xtraTabbedMdiManager1.Pages.Count - 1; i >= 0; i--)
                {
                    Form form = this.xtraTabbedMdiManager1.Pages[i].MdiChild;
                    form.Close();
                }
            }

            xtraTabbedMdiManager1.MdiParent = null;
            xtraTabbedMdiManager1.Dispose();
        }
        #endregion
        /// <summary>
        /// 窗体变量赋值
        /// </summary>
        /// <param name="frm">继承ParentForm的子窗体</param>
        public void SetParentFormProperty(ParentForm frm)
        {
            frm.AppCode = this.AppCode;
            frm.AppName = this.AppName;
            frm.DeptCode = this.DeptCode;
            frm.DeptName = this.DeptName;
            frm.WardCode = this.WardCode;
            frm.WardName = this.WardName;
            frm.DeptList = this.DeptList;
            frm.WardList = this.WardList;
            frm.Capability = this.Capability;
            frm.ClinicClass = this.ClinicClass;
            frm.NewBornOut = this.NewBornOut;
            frm.NurseRole = this.NurseRole;
            frm.IsAllDept = this.IsAllDept;
            frm.DutyRightDept = this.DutyRightDept;
            frm.ReportDesignRoles = this.ReportDesignRoles;
            frm.NurAdmDutyRight = this.NurAdmDutyRight;
            frm.Clinical_Pathway = this.Clinical_Pathway;
            frm.QueueName = this.QueueName;
        }
        /// <summary>
        /// 启动院感程序，只有住院医生站、住院护士站  20180122 张鹏
        /// </summary>
        private void SetYG(string appname, string strParameter)
        {
            try
            {
                Process[] processes = Process.GetProcesses();
                foreach (Process p in processes)
                {
                    if (p.ProcessName == "RawTcpClientDEMO")
                    {
                        p.Kill();
                    }
                }
                string raw = PlatCommon.SysBase.SystemParm.GetParameterValue("SETRAW", "*", "*", "*", SystemParm.HisUnitCode);
                if (raw.Equals("1"))
                {
                    if (appname == "DOCTWS" || appname == "NURSWS")
                    {
                        Process myprocess = new Process();
                        ProcessStartInfo startInfo = new ProcessStartInfo(Application.StartupPath + @"\YG\RawTcpClientDEMO.exe", strParameter);
                        myprocess.StartInfo = startInfo;
                        myprocess.StartInfo.UseShellExecute = false;
                        myprocess.Start();
                    }
                }
            }
            catch
            {
            }
        }
        /// <summary>
        /// 设置图标
        /// </summary>
        private void SetFormTitleIcon()
        {
            SvgBitmap svgBitmap = null;
            foreach (Control ctrl in tableLayoutPanel1.Controls)
            {
                if (ctrl is LabelControl && ctrl.Tag != null)
                {
                    string tagValue = ctrl.Tag.ToString();
                    if (string.IsNullOrEmpty(tagValue)) continue;
                    if (!svgImages.ContainsKey(tagValue))
                    {
                        svgBitmap = DevExpress.Utils.Svg.SvgBitmap.FromFile(@"Images\System\Tiltle\" + tagValue);
                        svgImages.Add(tagValue, svgBitmap.SvgImage);
                    }
                    (ctrl as LabelControl).ImageOptions.SvgImage = svgImages[tagValue];
                }
            }
        }
        /// <summary>
        /// 设置展开图标
        /// </summary>
        private void SetExpendIcon()
        {
            SvgBitmap svgBitmap = null;

            string picName = "展开图标.svg";
            if (!svgImages.ContainsKey(picName))
            {
                svgBitmap = DevExpress.Utils.Svg.SvgBitmap.FromFile(@"Images\Menu\Svg\" + picName);
                svgImages.Add(picName, svgBitmap.SvgImage);
            }
            this.labelControl_expend.ImageOptions.SvgImage = svgImages[picName];
        }

        /// <summary>
        /// 消息动态触发打开菜单树状节点上指定的应用窗口
        /// </summary>
        /// <param name="frmTitle"></param>
        /// <param name="dllName"></param>
        /// <param name="typeName"></param>
        /// <param name="paras"></param>
        public void ShowCtrlInFrame(string frmTitle, string dllName, string typeName, params object[] paras)
        {
            // 获取对应打开类的【有权限】TreeList节点，找到并调用
            if (string.IsNullOrEmpty(typeName))
                return;

            foreach (BarItemLink itemLink in topMainMenu.ItemLinks)
            {
                BarSubItem ownerItem = itemLink.Item as BarSubItem;
                if (ownerItem.Tag != null)
                {
                    DataRow subDataRow = null;
                    foreach (BarSubItem subItem in ownerItem.Links)
                    {
                        subDataRow = subItem.Tag as DataRow;
                        if (subDataRow != null)
                        {
                            if (subDataRow["FORM_ID"].ToString().ToLower() == typeName)
                            {
                                OpenForm(subDataRow);
                            }
                        }
                    }
                }
            }
        }
        /// <summary>
        /// 切换Tab页事件
        /// </summary>        
        private void xtraTabbedMdiManager1_SelectedPageChanged(object sender, EventArgs e)
        {
        }
        #region 底部按钮点击事件
        /// <summary>
        /// [关于简介]按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barLargeButtonItem2_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
        }
        /// <summary>
        /// [程序帮助]按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barLargeButtonItem3_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
        }
        /// <summary>
        /// [北京天健科技集团]按钮
        /// </summary>
        private void barButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            System.Diagnostics.Process.Start("https://www.medvision.com.cn/");
        }
        /// <summary>
        /// [法律法规]按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barButtonItem2_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            FrmReadLaw frl = new FrmReadLaw();
            frl.ShowDialog();
        }
        /// <summary>
        /// 切换字体
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barfont_EditValueChanged(object sender, EventArgs e)
        {
            string strFont = barfont.EditValue?.ToString();
            if (strFont == "特大号字体")
            {
                DevExpress.XtraEditors.WindowsFormsSettings.DefaultFont = new System.Drawing.Font("Tahoma", 12);
                DevExpress.XtraEditors.WindowsFormsSettings.DefaultMenuFont = new System.Drawing.Font("Tahoma", 12);
                DevExpress.XtraEditors.WindowsFormsSettings.DefaultPrintFont = new System.Drawing.Font("Tahoma", 12);
                Utility.ConfigHelper.SetConfigByXml("FontWeight.xml", "parameter", "FontWeight", "12");
            }
            else if (strFont == "大号字体")
            {
                DevExpress.XtraEditors.WindowsFormsSettings.DefaultFont = new System.Drawing.Font("Tahoma", 11);
                DevExpress.XtraEditors.WindowsFormsSettings.DefaultMenuFont = new System.Drawing.Font("Tahoma", 11);
                DevExpress.XtraEditors.WindowsFormsSettings.DefaultPrintFont = new System.Drawing.Font("Tahoma", 11);
                Utility.ConfigHelper.SetConfigByXml("FontWeight.xml", "parameter", "FontWeight", "11");
            }
            else if (strFont == "中号字体")
            {
                DevExpress.XtraEditors.WindowsFormsSettings.DefaultFont = new System.Drawing.Font("Tahoma", 10);
                DevExpress.XtraEditors.WindowsFormsSettings.DefaultMenuFont = new System.Drawing.Font("Tahoma", 10);
                DevExpress.XtraEditors.WindowsFormsSettings.DefaultPrintFont = new System.Drawing.Font("Tahoma", 10);
                Utility.ConfigHelper.SetConfigByXml("FontWeight.xml", "parameter", "FontWeight", "10");
            }
            else if (strFont == "标准字体")
            {
                DevExpress.XtraEditors.WindowsFormsSettings.DefaultFont = new System.Drawing.Font("Tahoma", 9);
                DevExpress.XtraEditors.WindowsFormsSettings.DefaultMenuFont = new System.Drawing.Font("Tahoma", 9);
                DevExpress.XtraEditors.WindowsFormsSettings.DefaultPrintFont = new System.Drawing.Font("Tahoma", 9);
                Utility.ConfigHelper.SetConfigByXml("FontWeight.xml", "parameter", "FontWeight", "9");
            }

            FormCollection fc = Application.OpenForms;
            foreach (Form f in fc)
            {
                if (f != null && f != this && f is FrmNewMain fnm)
                {
                    fnm.SetFontCombo(strFont);
                }
            }
        }
        //设置下拉框字体
        private void SetFontCombo(string fontname)
        {
            this.barfont.EditValueChanged -= this.barfont_EditValueChanged;
            this.barfont.EditValue = fontname;
            this.barfont.EditValueChanged += this.barfont_EditValueChanged;
        }
        /// <summary>
        /// [权限管理]按钮(存疑）
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barBtnSystemSetting_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            ////WIN_PARAMETER字段有值表示调用电子病历窗体，存的是电子病历的node_code
            //TreeList temp_list = new TreeList();
            //temp_list.Columns.AddField("WIN_PARAMETER");
            //temp_list.Columns.AddField("MENU_TEXT");
            //TreeListNode node = temp_list.Nodes.Add(new object[] { "EMR15", "系统权限设置" });
            ////node.SetValue("WIN_PARAMETER", "EMR01");
            ////node.SetValue("MENU_TEXT", "住院病历-权限管理");
            ////DockContent frm = GetDockContentFormInDll("TJ_EMR_Business.dll", "TJ.EMR.Business.MainForm", node);
            ////frm.ShowDialog();
            XtraMessageBox.Show("请在系统管理模块中进行权限管理!", "系统提示");
        }
        /// <summary>
        /// [修改密码]按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barLargeButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            ParentForm frm = GetFormInDll("Tjhis.SysManager.Station.dll", "Tjhis.SysManager.Station.View.FrmModifyPass");
            frm.ShowDialog();
        }
        #endregion

        /// <summary>
        /// 退出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lblClose_Click(object sender, EventArgs e)
        {
            this.timer1.Stop();
            this.timer1.Tick -= new System.EventHandler(this.timer1_Tick);
            this.Close();
        }
        /// <summary>
        /// 最小化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lblMaxSize_Click(object sender, EventArgs e)
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                this.WindowState = FormWindowState.Minimized;
            }
            else
            {
                Rectangle current = Screen.GetWorkingArea(this);
                this.MaximizedBounds = new Rectangle(0, 0, current.Width, current.Height);

                this.WindowState = FormWindowState.Maximized;
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            //3、判断授权                                 
            string msg = "";
            int iDay = 0;
            string strName;
            bool rev = PlatCommon.Common.PublicFunction.GetSystemAuthorization(SystemParm.HisUnitCode, AppCode, AppName, "6.6", EncryptCode, ref msg, ref iDay);
            if (rev)
            {
                if (!string.IsNullOrEmpty(msg))
                {
                    strName = AppName + "【" + iDay.ToString() + "天后到期】";
                }
                else
                {
                    strName = AppName;
                }
            }
            else
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(msg + "系统将会强制关闭,请联系信息科！", "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Environment.Exit(0);
                return;
            }
        }
    }
}
