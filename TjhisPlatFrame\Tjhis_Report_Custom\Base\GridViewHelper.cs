﻿using System;
using System.Data;
using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;

namespace Tjhis.Report.Custom.Base
{
    public class GridViewHelper
    {
        public static void InitGridViewColumns(GridView view,DataTable source)
        {
            view.FormatRules.Clear();
            foreach (DataRow configRow in source.Rows)
            {

                string fieldName = configRow["COLUMN_FIELD"].ToString().ToUpper();
                string ifGroup = configRow["IF_GROUP"].ToString();
                string groupIndex = configRow["GROUP_INDEX"].ToString();
                string ifSummary = configRow["IF_SUMMARY"].ToString();
                string summaryType = configRow["SUMMARY_TYPE"].ToString();
                string summaryDisplayFormat = configRow["SUMMARY_DISPLAY"].ToString();
                string ifUnbound = configRow["IF_UNBOUND"].ToString();
                string expression = configRow["EXPRESSION"].ToString();
                string unboundType = configRow["UNBOUND_TYPE"].ToString();

                GridColumn column = new GridColumn();
                column.Caption = configRow["COLUMN_NAME"].ToString();
                column.Name = "COL_" + configRow["SERIAL_NO"].ToString().ToUpper();
                column.Visible = true;
                column.FieldName = string.IsNullOrEmpty(fieldName) ? column.Name : fieldName;
                column.Width = int.Parse(configRow["COLUMN_WIDTH"].ToString());
                column.OptionsColumn.AllowEdit = false;

                string strFormatString = configRow["FORMAT_STRING"].ToString();
                if (!string.IsNullOrEmpty(strFormatString))
                {
                    FormatType strFormatType = (FormatType)Enum.Parse(typeof(FormatType), configRow["FORMAT_TYPE"].ToString());
                    column.DisplayFormat.FormatString = strFormatString;
                    column.DisplayFormat.FormatType = strFormatType;
                }

                if (source.Columns.Contains("VISIBLE"))
                {
                    string visibleStr = configRow["VISIBLE"].ToString();
                    if (!string.IsNullOrEmpty(visibleStr) && visibleStr.Equals("1"))
                    {
                        column.Visible = false;
                    }
                }
                if (source.Columns.Contains("IS_FORMATRULE"))
                {
                    string isFormatRule = configRow["IS_FORMATRULE"].ToString();
                    if(!string.IsNullOrEmpty(isFormatRule) && isFormatRule.Equals("1"))
                    {
                        CreateFormatRule(view, column, configRow["RULE_EXPRESSION"].ToString(), configRow["PREDEFINED_NAME"].ToString());
                    }
                }

                if ("1".Equals(ifGroup))
                {
                    //column.GroupFormat.
                    //column.GroupIndex = int.Parse("0");
                }

                if ("1".Equals(ifSummary))
                {
                    //column.SummaryItem.SummaryType = (DevExpress.Data.SummaryItemType)Enum.Parse(typeof(DevExpress.Data.SummaryItemType), summaryType);
                    DevExpress.Data.SummaryItemType summaryType1 = (DevExpress.Data.SummaryItemType)Enum.Parse(typeof(DevExpress.Data.SummaryItemType), summaryType);
                    column.Summary.AddRange(new GridSummaryItem[] { new GridColumnSummaryItem(summaryType1, column.FieldName, summaryDisplayFormat) });
                }

                if ("1".Equals(ifUnbound))
                {
                    column.FieldName = column.Name;
                    column.UnboundExpression = expression;
                    if (!string.IsNullOrEmpty(unboundType))
                        column.UnboundType = (DevExpress.Data.UnboundColumnType)Enum.Parse(typeof(DevExpress.Data.UnboundColumnType), unboundType);

                }
                
                view.Columns.Add(column);
            }
            view.RefreshData();
            view.GridControl.Refresh();
        }

        static void CreateFormatRule(GridView gv,GridColumn gc,string expressionStr,string predefinedName = "Red Text, Bold Text")
        {

            GridFormatRule formatRule = new GridFormatRule();
            FormatConditionRuleValue r = new FormatConditionRuleValue();
            r.Condition = FormatCondition.Expression;
            r.Expression = expressionStr;
            r.PredefinedName = predefinedName.Trim();// "Red Text";
            formatRule.Rule = r;
            formatRule.Column = gc;
            
            gv.FormatRules.Add(formatRule);
        }
    }
}
