﻿using System.Data;
using System.Windows.Forms;
using Tjhis.Report.Custom.Common;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmLocalDocReport : frmStatisticalQueryDoc
    {
        public string tempName { get; set; }

        DataSet reportDict;
        DataSet reportParam;
        DataSet reportConfig;

        public frmLocalDocReport()
        {
            InitializeComponent();
        }
        public frmLocalDocReport(string reportName) : this()
        {
            this.tempName = reportName;
        }

        //自定义报表字典-NURADM_REPORT_STATISTICS_DICT   自定义报表相关参数-NURADM_REPORT_VS_PARAM  自定义报表相关列配置-NURADM_REPORT_CONFIG
        protected override void TempletInfo_load()
        {
            if (this.Tag != null)
                tempName = this.Tag.ToString();
            else
                this.Tag = tempName;

            DataSet ds = new DataSet();

            string path = $@"{Application.StartupPath}\reports\{Const.customAppCode}\{tempName}.xml";

            ds.ReadXml(path);

            if (ds.Tables.Count <= 0) return;

            if (!ds.Tables.Contains("NURADM_REPORT_STATISTICS_DICT"))
                return;

            reportDict = new DataSet();
            reportDict.Tables.Add(ds.Tables["NURADM_REPORT_STATISTICS_DICT"].Copy());

            if (ds.Tables.Contains("NURADM_REPORT_CONFIG"))
            {
                reportConfig = new DataSet();
                reportConfig.Tables.Add(ds.Tables["NURADM_REPORT_CONFIG"].Copy());
            }

            if (ds.Tables.Contains("NURADM_REPORT_VS_PARAM"))
            {
                reportParam = new DataSet();
                reportParam.Tables.Add(ds.Tables["NURADM_REPORT_VS_PARAM"].Copy());
            }

            drTemplet = reportDict.Tables[0].Rows[0];
            this.TempleteFile = drTemplet["dict_name"].ToString() + "_" + drTemplet["dict_id"].ToString();
            this.TempleteSql = drTemplet["templet_sql"].ToString();

            this.Text = drTemplet["dict_name"].ToString();
        }

        protected override DataSet GetReportParamNew(string strAppName, string reportID)
        {
            return reportParam;
        }
        
        protected override DataRow GetTempletNew()
        {
            return reportDict?.Tables?[0]?.Rows?[0];
        }

    }
}
