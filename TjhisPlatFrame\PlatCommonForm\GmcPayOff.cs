﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace PlatCommonForm
{
    public class GmcPayOff
    {
        /// <summary>
        /// 无GMC界面接口
        /// </summary>
        /// <param name="request"></param>
        /// <param name="response"></param>
        /// <returns></returns>
        [DllImport("C:\\gmc\\PosInf.DLL", EntryPoint = "bankall")]
        public static extern int bankall(string request, StringBuilder response);

        NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
        Dictionary<string, string> dict = new Dictionary<string, string>();
        public int Gmc_BankAll(string tjym, string _ApplicationType, string _TransactionType, string _Amount, string _OriginalTransactionDate,
                                ref string _OriginalTransactionReference, ref string _OriginalVoucher, string _PatientId, string _VisitId,
                                ref string _CardNumber, ref string _SYS_DATE, string _StaffId,ref string _TransactionDate ,
                                ref string _ErrorText, Boolean _bPWCA)
        {
            LogFile log = new LogFile("Gmc_BankAll");
            try
            {
                string Int_Request = string.Empty;
                StringBuilder Out_Response = new StringBuilder(1024);

                string PayBarCode = string.Empty; ////增值信息 
                if (_ApplicationType.Equals("01") && _TransactionType.Equals("00")) ////应用类型  00银行卡(营销联盟、银联钱包)   （01POS通微信支付宝）
                {
                    if (string.IsNullOrEmpty(tjym))
                    {
                        //FrmGmcBarCode FrmCode = new FrmGmcBarCode();
                        //FrmCode.ShowDialog();
                        //PayBarCode = FrmCode.OutValue;
                    }
                    else
                    {
                        PayBarCode = tjym;
                    }
                    if (string.IsNullOrEmpty(PayBarCode))
                    {
                        _ErrorText = "增值信息，交易码为空";
                        log.Debug("Gmc_BankAll  ", _ErrorText);
                        return -1;
                    }
                }

                if (string.IsNullOrEmpty(_StaffId))
                {
                    _StaffId = "AAAA";
                }
                string PosMachine = string.Empty;  ////POS机号
                if (_StaffId.Length > 8)
                {
                    PosMachine = _StaffId.Substring(0, 8).ToString();
                }
                else
                {
                    PosMachine = _StaffId.PadRight(8, '0').ToString();
                }

                string PosStaff = string.Empty; ///POS员工号
                if (_StaffId.Length > 8)
                {
                    PosStaff = _StaffId.Substring(0, 8).ToString();
                }
                else
                {
                    PosStaff = _StaffId.PadRight(8, '0').ToString();
                }

                //_TransactionType //交易类型
                decimal damount = 0;
                damount = Math.Round(decimal.Parse(_Amount) * 100);
                string Amount = damount.ToString().PadLeft(12, '0');
                //string Amount = Math.Round(decimal.Parse(_Amount) * 100, 2).ToString().Replace(".", "").PadLeft(12, '0');
                //string Amount = Math.Round(decimal.Parse(_Amount), 2).ToString().Replace(".", "").PadLeft(12, '0');
                string Empty = ""; ////空字符串 

                //string _OriginalTransactionDate = ""; ////原交易日期    退货时用,其它交易为空
                //string _OriginalTransactionReference = "" ; //// 原交易参考号  退货时用,其它交易为空
                //string _OriginalVoucher = ""; //// 原凭证号  撤销时用 ，其它交易为空

                if (!string.IsNullOrEmpty(_OriginalTransactionDate))
                {
                    _OriginalTransactionDate = DateTime.Parse(_OriginalTransactionDate).ToString("yyyyMMdd");
                }

                string LrcCheck = "   "; ////LRC校验  3位随机数字
                switch (_TransactionType)
                {
                    case "00": //消费
                        Int_Request = _ApplicationType + PosMachine + PosStaff + _TransactionType + Amount +
                                      Empty.PadLeft(8, ' ').ToString() + Empty.PadLeft(12, ' ').ToString() +
                                      Empty.PadLeft(6, ' ').ToString() + LrcCheck + PayBarCode.PadRight(50, ' ').ToString();
                        break;
                    case "01": //撤销
                        Int_Request = _ApplicationType + PosMachine + PosStaff + _TransactionType + Amount +
                                     Empty.PadLeft(8, ' ').ToString() + Empty.PadLeft(12, ' ').ToString() +
                                     _OriginalVoucher.PadLeft(6, ' ').ToString() + LrcCheck + PayBarCode.PadRight(50, ' ').ToString();
                        break;
                    case "02": //退货
                        Int_Request = _ApplicationType + PosMachine + PosStaff + _TransactionType + Amount +
                                     _OriginalTransactionDate.PadLeft(8, ' ').ToString() + _OriginalTransactionReference.PadLeft(12, ' ').ToString() +
                                     Empty.PadLeft(6, ' ').ToString() + LrcCheck + PayBarCode.PadRight(50, ' ').ToString();
                        break;
                    case "03": //查余额
                    case "04": //重打印
                    case "05": //签到
                    case "06": //结算
                        Int_Request = _ApplicationType + PosMachine + PosStaff + _TransactionType + Amount +
                         Empty.PadLeft(8, ' ').ToString() + Empty.PadLeft(12, ' ').ToString() +
                         Empty.PadLeft(6, ' ').ToString() + LrcCheck + PayBarCode.PadRight(50, ' ').ToString();
                        break;
                    case "07": //重打结算单
                        Int_Request = _ApplicationType + PosMachine + PosStaff + _TransactionType + Amount +
                                      Empty.PadLeft(8, ' ').ToString() + Empty.PadLeft(12, ' ').ToString() +
                                      Empty.PadLeft(6, ' ').ToString() + LrcCheck + PayBarCode.PadRight(50, ' ').ToString();
                        break;
                    default:
                        Int_Request = "Int_Request入参有问题";
                        break;
                }

                log.Info("Int_Request  ", Int_Request.ToString());
                int num = bankall(Int_Request, Out_Response);
                log.Info("Out_Response  ", Out_Response.ToString());

                //Out_Response.Append ("00WXZF134561535505233730  000029************交易成功                                89821017999833277008901000001082916074807032166655W      0829   ************");

                string ReturnCode = string.Empty; ////返回码 
                string BankCode = string.Empty; ////银行行号
                //string CardNumber = string.Empty; //卡号
                string VoucherNo = string.Empty;//凭证号
                string RealBuckleAmount = string.Empty;//实扣金额
                string ErrorDescription = string.Empty;//错误说明 
                string MerchantNumber = string.Empty;//商户号
                string TerminalNumber = string.Empty;//终端号
                string BatchNumber = string.Empty;//批次号
                string TransactionDate = string.Empty;//交易日期
                string TransactionTime = string.Empty;//交易时间
                string TradeReferenceNumber = string.Empty;//交易参考号
                string AuthorizationNumber = string.Empty;//授权号
                string LiquidationDate = string.Empty;//清算日期
                string LRCCheck = string.Empty;//LRC校验
                string OriginalTransactionAmount = string.Empty;//原交易金额
                string ThirdPartySubsidyAmount = string.Empty;//第三方补贴金额
                string ProfitMarginMerchant = string.Empty;//商户让利金额
                string IntegralDeductionAmount = string.Empty;//积分抵扣金额
                string SpareField = string.Empty;//备用字段

                byte[] btyeArray = System.Text.Encoding.Default.GetBytes(Out_Response.ToString());
                byte[] btyeNew = new byte[btyeArray.Length];
                int len = Out_Response.ToString().Length;
                List<byte> list = new List<byte>();
                for (int i = 0; i < btyeArray.Length; i++)
                {
                    list.Add(btyeArray[i]);
                    switch (i)
                    {
                        case 1:
                            btyeNew = list.ToArray();
                            ReturnCode = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); ////返回码 
                            list.Clear();
                            if (ReturnCode != "00")
                            {
                                _ErrorText = Out_Response.ToString().Trim();
                                return -1;
                            }
                            break;
                        case 5:
                            btyeNew = list.ToArray();
                            BankCode = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 25:
                            btyeNew = list.ToArray();
                            _CardNumber = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 31:
                            btyeNew = list.ToArray();
                            VoucherNo = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 43:
                            btyeNew = list.ToArray();
                            RealBuckleAmount = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 83:
                            btyeNew = list.ToArray();
                            ErrorDescription = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 98:
                            btyeNew = list.ToArray();
                            MerchantNumber = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 106:
                            btyeNew = list.ToArray();
                            TerminalNumber = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 112:
                            btyeNew = list.ToArray();
                            BatchNumber = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 116:
                            btyeNew = list.ToArray();
                            TransactionDate = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 122:
                            btyeNew = list.ToArray();
                            TransactionTime = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 134:
                            btyeNew = list.ToArray();
                            TradeReferenceNumber = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 140:
                            btyeNew = list.ToArray();
                            AuthorizationNumber = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 144:
                            btyeNew = list.ToArray();
                            LiquidationDate = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 147:
                            btyeNew = list.ToArray();
                            LRCCheck = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 159:
                            btyeNew = list.ToArray();
                            OriginalTransactionAmount = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 171:
                            btyeNew = list.ToArray();
                            ThirdPartySubsidyAmount = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 183:
                            btyeNew = list.ToArray();
                            ProfitMarginMerchant = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 195:
                            btyeNew = list.ToArray();
                            IntegralDeductionAmount = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        case 245:
                            btyeNew = list.ToArray();
                            SpareField = System.Text.Encoding.Default.GetString(btyeNew).ToString().Trim(); //// 
                            list.Clear();
                            break;
                        default:
                            break;
                    }
                }
                if (_TransactionType.Equals("00") || _TransactionType.Equals("01") || _TransactionType.Equals("02"))
                {
                    TransactionDate = spc.GetSysDateTime().ToString().Substring(0, 4) + "-" + TransactionDate.ToString().Substring(0, 2) + "-" + TransactionDate.ToString().Substring(2, 2);
                
                if (!string.IsNullOrEmpty(TransactionDate))
                {
                    TransactionDate = "to_date('" + TransactionDate + "','yyyy-mm-dd hh24:mi:ss')";
                    _TransactionDate = TransactionDate;
                }
                else
                {
                    TransactionDate = "";
                    _TransactionDate = "null";
                }
                }
                _SYS_DATE = spc.GetSysDateTime().ToString();
               // string 
                if (!string.IsNullOrEmpty(_SYS_DATE))
                {
                    _SYS_DATE = "to_date('" + _SYS_DATE + "','yyyy-mm-dd hh24:mi:ss')";
                }
                else
                {
                    _SYS_DATE = "";
                }

                _OriginalTransactionReference = TradeReferenceNumber; //// 原交易参考号  退货时用,其它交易为空
                _OriginalVoucher = VoucherNo; //// 原凭证号  撤销时用 ，其它交易为空

                if (_bPWCA)
                {
                    RealBuckleAmount = (decimal.Parse(RealBuckleAmount) / 100).ToString();
                    if (_TransactionType == "01" || _TransactionType == "02")
                    {
                        RealBuckleAmount = "-" + RealBuckleAmount;
                    }
                    string strPWCA = "INSERT INTO INSURANCE.SETTLE_MASTER_PWCA ( ";
                    strPWCA += "PATIENT_ID,VISIT_ID,AMOUNT,TRADE_DATE,COMMISSION_CHARGE, ";
                    strPWCA += "OPCODE,SYS_DATE,POS_CKH,POS_PZH,POS_NUM, ";
                    strPWCA += "BANK_NO,BANKCARD_NO,MERCHANT_NUMBER,TERMINAL_NUMBER,BATCH_NUMBER, ";
                    strPWCA += "AUTHORIZATION_NUMBER,DATE_OF_LIQUIDATION,IN_STR,OUT_STR,IP_ADDRESS, ";
                    strPWCA += "HOSTNAME,DATA_SOURCE,PAY_CHANNEL,PAY_CODE ";
                    strPWCA += " ) VALUES ( ";
                    strPWCA += "'" + _PatientId + "','" + _VisitId + "','" + RealBuckleAmount + "'," + TransactionDate + ",'0',";
                    strPWCA += "'" + _StaffId + "'," + _SYS_DATE + ",'" + TradeReferenceNumber + "','" + VoucherNo + "','" + _StaffId + "',";
                    strPWCA += "'" + BankCode + "','" + _CardNumber + "','" + MerchantNumber + "','" + TerminalNumber + "','" + BatchNumber + "',";
                    strPWCA += "'" + AuthorizationNumber + "','" + LiquidationDate + "','" + Int_Request + "','','" + "IP" + "',";
                    strPWCA += "'" + "主机地址" + "','" + "His" + "','" + _ApplicationType + "','" + PayBarCode + "'";
                    strPWCA += " ) ";

                    dict.Add(strPWCA, "strPWCA"); ////_ApplicationType
                    string result = new NM_Service.NMService.PatientInDeptClient().SaveTable(dict);
                    if (!string.IsNullOrEmpty(result))
                    {
                        _ErrorText = "SETTLE_MASTER_PWCA表插入错误" + result + " SQL语句 " + strPWCA;
                        log.Error("Gmc_BankAll ", _ErrorText);
                    }

                }
                if(_TransactionType.Equals("06"))
                {
                    Dictionary<string, string> adict = new Dictionary<string, string>();
                    string sqla = "update INSURANCE.SETTLE_MASTER_PWCA  set POS_ACCT='1'  set where  terminal_number='" + TerminalNumber + "'";
                    adict.Add(sqla, "sqla");
                    string result = new NM_Service.NMService.PatientInDeptClient().SaveTable(adict);
                    if (!string.IsNullOrEmpty(result))
                    {
                        _ErrorText = "更新SETTLE_MASTER_PWCA表错误" + result + " SQL语句 " + sqla;
                        log.Error("Gmc_BankAll ", _ErrorText);
                    }
                }
                return 0;
            }
            catch (Exception ex)
            {
                log.Debug("Gmc_BankAll  ", ex.Message);
                throw new Exception(ex.Message);
            }

        }
    }
}
