﻿using System;
using System.Data;
using System.Text;
using System.Collections.Generic;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Microsoft.Practices.EnterpriseLibrary.Data.Oracle;
using System.Data.Common;
using System.Xml;
using System.Runtime.Serialization;
using System.Diagnostics;
using Model;
using System.Configuration;
using System.IO;
using System.Collections;
using Utility.DbInfo;
using System.Data.OracleClient;
using System.ServiceModel;
using System.ServiceModel.Channels;
using Utility.YangFan;
namespace OracleDAL
{
    /// <summary>
    /// 数据访问类MR_FILE_INDEX。
    /// </summary>
    public class MR_FILE_INDEXDAL
    {
        DbInfo dbinfo = new DbInfo();
        public MR_FILE_INDEXDAL()
        { }
        #region 新建病例
        /// <summary>
        /// 是否有病程记录，若有返回病程文件名
        /// </summary>
        /// <param name="PATIENT_ID">The PATIENT_ID.</param>
        /// <returns></returns>
        public DataTable GetAddInfo(string PATIENT_ID, int Visit_ID)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select nvl(max(file_no),0)+1 as fileno from MR_FILE_INDEX");
            strSql.Append(" where patient_id='" + PATIENT_ID + "' and VISIT_ID='" + Visit_ID + "' ");
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                DataTable dt = new DataTable("MR_FILE_INDEX");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetAddInfo", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 是否有病程记录，若有返回病程文件名
        /// </summary>
        /// <param name="PATIENT_ID">The PATIENT_ID.</param>
        /// <returns></returns>
        public DataTable IsCourseOfDis(string PATIENT_ID, int Visit_ID)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select max(FILE_NAME),max(FILE_NO) as no from MR_FILE_INDEX ");
            strSql.AppendFormat(" where patient_id='{0}'  and FILE_ATTR='B' and VISIT_ID={1} ", PATIENT_ID, Visit_ID);
            try
            {
                DataTable dt = new DataTable("MR_FILE_INDEX");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "IsCourseOfDis", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(CommandType.Text, strSql.ToString());
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 生成子集回退记录
        /// add by wgj 2014-07-09 for QC:3576
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        public bool CreateBackDetail(DataSet ds)
        {
            string sql = "insert into MR_RECALL_DETAIL (PATIENT_ID,VISIT_ID,RECALL_NO,CLASS_CODE,FILE_NO,FILE_STATUS,DETAIL_REMARK) values（'";
            sql += ds.Tables[0].Rows[0]["PATIENT_ID"].ToString() + "',";
            sql += ds.Tables[0].Rows[0]["VISIT_ID"].ToString() + ",";
            sql += ds.Tables[0].Rows[0]["RECALL_NO"].ToString() + ",'";
            sql += ds.Tables[0].Rows[0]["CLASS_CODE"].ToString() + "',";
            sql += ds.Tables[0].Rows[0]["FILE_NO"].ToString() + ",";
            sql += ds.Tables[0].Rows[0]["FILE_STATUS"].ToString() + ",'";
            sql += ds.Tables[0].Rows[0]["DETAIL_REMARK"].ToString() + "')";
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "CreateBackDetail", sql.ToString(), endpoint.Address);
                }
                return dbinfo.ExecuteSql(sql);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        #endregion

        #region 病程树
        #region  成员方法
        #region 追加文件
        /****************************************
          * 函数名称：FileAdd
          * 功能说明：追加文件内容
          * 参     数：Path:文件路径,strings:内容
          * 调用示列：
          *            string Path = Server.MapPath("Default2.aspx");     
          *            string Strings = "新追加内容";
          *            EC.FileObj.FileAdd(Path, Strings);
         *****************************************/
        /// <summary>
        /// 追加文件
        /// </summary>
        /// <param name="Path">文件路径</param>
        /// <param name="strings">内容</param>
        public static void FileAdd(string Path, string strings)
        {
            if (!System.IO.File.Exists(Path))
            {
                System.IO.FileStream f = System.IO.File.Create(Path);
            }
            using (StreamWriter sw = new StreamWriter(Path, true))
            {
                sw.Write(strings);
                sw.Flush();
                sw.Close();
            }
        }
        #endregion
        /// <summary>
        /// 门诊类别病例列表
        /// </summary>
        /// <param name="PATIENT_ID"></param>
        /// <param name="VISIT_DATE"></param>
        /// <param name="VISIT_NO"></param>
        /// <returns></returns>
        public DataSet GetEMRTreeO(string PATIENT_ID, DateTime VISIT_DATE, int VISIT_NO)
        {
            DataSet ds = new DataSet();
            Database db = DatabaseFactory.CreateDatabase();
            //文件类型
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select file_name,file_attr,TOPIC,to_char(CREATE_DATE_TIME,'yyyy-MM-dd') as CREATE_DATE_TIME,(select NAME from MR_FILE_FLAG_DICT where MEDREC.OUTP_MR_FILE_INDEX.FILE_FLAG=CODE) as FILE_FLAG_NAME ,FILE_FLAG,CREATOR_NAME as CREATOR_NAME,file_no  from MEDREC.OUTP_MR_FILE_INDEX ");
            strSql.Append("where patient_id='{0}' and VISIT_DATE=to_date('{1}','YYYY-MM-DD HH24:MI:SS') and VISIT_NO={2}  order by file_no");
            string sql = string.Format(strSql.ToString(), PATIENT_ID, VISIT_DATE.ToString("yyyy-MM-dd HH:mm:ss"), VISIT_NO.ToString());
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(sql);
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetEMRTreeO", strSql.ToString(), endpoint.Address);
                }
                db.LoadDataSet(cmd, ds, "OUTP");
                strSql.Remove(0, strSql.Length);
                return ds;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获取time line 列表
        /// </summary>
        /// <param name="PATIENT_ID"></param>
        /// <returns></returns>
        public DataTable GetIRecordTree(string PATIENT_ID, string type)
        {
            DataTable dt = null;
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            DbCommand cmd;
            try
            {
                if (string.Equals(type, "2"))
                {
                    strSql.Append("select ADMISSION_DATE_TIME as CREATE_DATE_TIME ,VISIT_ID,DISCHARGE_DATE_TIME as leaveHospital_date_time  from PAT_VISIT where patient_id='" + PATIENT_ID + "'  order by ADMISSION_DATE_TIME desc ");
                    cmd = db.GetSqlStringCommand(strSql.ToString());
                    dt = new DataTable("Iall");
                    if (TrackCOMM.Track_Indicator)
                    {
                        OperationContext context = OperationContext.Current;
                        MessageProperties properties = context.IncomingMessageProperties;
                        RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                        TrackEntity entity = new TrackEntity();
                        entity.Add("MR_FILE_INDEXDAL", "GetIRecordTree", strSql.ToString(), endpoint.Address);
                    }
                    DataSet ds = db.ExecuteDataSet(cmd);
                    if (ds != null)
                    {
                        dt = ds.Tables[0];
                    }
                }
                if (string.Equals(type, "3"))
                {
                    strSql.Append("select VISIT_NO,VISIT_DATE,clinic_label from outpadm.CLINIC_MASTER where patient_id='" + PATIENT_ID + "' order by VISIT_DATE desc ,Visit_No desc ");
                    cmd = db.GetSqlStringCommand(strSql.ToString());
                    dt = new DataTable("Oall");
                    if (TrackCOMM.Track_Indicator)
                    {
                        OperationContext context = OperationContext.Current;
                        MessageProperties properties = context.IncomingMessageProperties;
                        RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                        TrackEntity entity = new TrackEntity();
                        entity.Add("MR_FILE_INDEXDAL", "GetIRecordTree", strSql.ToString(), endpoint.Address);
                    }
                    DataSet ds = db.ExecuteDataSet(cmd);
                    if (ds != null)
                    {
                        dt = ds.Tables[0];
                    }
                }
                if (string.Equals(type, "5"))
                {
                    strSql.Append("select ADMISSION_DATE_TIME as CREATE_DATE_TIME ,OB_VISIT_NO,DISCHARGE_DATE_TIME as leaveHospital_date_time  from EMERGOB.OB_PAT_VISIT where patient_id='" + PATIENT_ID + "'  order by ADMISSION_DATE_TIME desc ");
                    cmd = db.GetSqlStringCommand(strSql.ToString());
                    dt = new DataTable("OBall");
                    if (TrackCOMM.Track_Indicator)
                    {
                        OperationContext context = OperationContext.Current;
                        MessageProperties properties = context.IncomingMessageProperties;
                        RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                        TrackEntity entity = new TrackEntity();
                        entity.Add("MR_FILE_INDEXDAL", "GetIRecordTree", strSql.ToString(), endpoint.Address);
                    }
                    DataSet ds = db.ExecuteDataSet(cmd);
                    if (ds != null)
                    {
                        dt = ds.Tables[0];
                    }
                }
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion  成员方法
        #endregion

        #region mr_file_index
        public DataTable GetMR_FILE_INDEX(string PATIENT_ID, int VISIT_ID)
        {
            Database db = DatabaseFactory.CreateDatabase();
            //住院志 --A
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * from  MR_FILE_INDEX where patient_id='" + PATIENT_ID + "'");
            if (VISIT_ID != -1)
                strSql.Append(" and VISIT_ID=" + VISIT_ID + " ");
            strSql.Append(" order by last_modify_date_time desc,file_no");
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                DataTable dt = new DataTable("mr_file_index");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetMR_FILE_INDEX", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        public DataSet GetMr_File_Xml(string patient_id, int visit_id)
        {
            ArrayList alResult = new ArrayList();
            Database db = DatabaseFactory.CreateDatabase();
            string strSql = string.Format(@"select patient_id,visit_id,file_no,t.file_xml.getclobval() file_xml from mr_file_xml t
                                            where patient_id='{0}' and visit_id={1}
                                            order by file_no desc", patient_id, visit_id);
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetMr_File_Xml", strSql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, strSql);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 取病历的数据集
        /// </summary>
        /// <param name="patientid"></param>
        /// <param name="visitid"></param>
        /// <param name="fileno"></param>
        /// <returns></returns>
        public DataTable GetMrFileDataSetCode(string patientid, int visitid, int fileno)
        {
            string sql = @"select * from mr_file_index b 
                            left join mr_item_index a on a.mr_code=b.mr_code
                             where b.patient_id='" + patientid + "' and b.visit_id=" + visitid + " and b.file_no=" + fileno + "";
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetMrFileDataSetCode", sql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, sql).Tables[0];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        #endregion

        #region LAB_TEST_ITEMS
        /// <summary>
        /// 取检验子项表信息
        /// </summary>
        /// <param name="PATIENT_ID"></param>
        /// <returns></returns>
        public DataTable GetLAB_TEST_ITEMS(string PATIENT_ID)
        {
            DataSet ds = new DataSet();
            DataTable dt = null;
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * from LAB_TEST_ITEMS where TEST_NO in (select TEST_NO from LAB_TEST_MASTER");
            strSql.Append(" where patient_id='" + PATIENT_ID + "')");
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetLAB_TEST_ITEMS", strSql.ToString(), endpoint.Address);
                }
                ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 检验报告
        /// </summary>
        /// <param name="EXAM_NO"></param>
        /// <returns></returns>
        public DataTable GetLAB_RESULT(string TEST_NO)
        {
            Database db = DatabaseFactory.CreateDatabase();
            //住院志 --A
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT TEST_NO,REPORT_ITEM_NAME,RESULT,ABNORMAL_INDICATOR,UNITS,(CASE v_lab_result.ABNORMAL_INDICATOR WHEN 'H' THEN '↑' WHEN 'L' THEN '↓' ELSE '' END) ABNORMAL_INDICATOR_VIEW FROM v_lab_result ");
            strSql.Append(" where TEST_NO='" + TEST_NO + "' ");
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                DataTable dt = new DataTable("v_lab_result");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetLAB_RESULT", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        public DataTable GetLabTestmaster(string testno)
        {
            string sql = @"select * from lab_test_master where test_no='" + testno + "' Order By REQUESTED_DATE_TIME";
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetLabTestmaster", sql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(sql).Tables[0];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        public DataTable GetLAB_TEST_MASTER(string PATIENT_ID, int VISIT_ID)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select lm.*,ed.exam_result_status_name,(select ITEM_NAME from lab_test_items a where a.test_no =lm.test_no and a.item_no=1) ITEM_NAME from  LAB_TEST_MASTER lm left join exam_result_status_dict ed on lm.result_status=ed.exam_result_status_code ");
            try
            {
                strSql.Append(" where patient_id='" + PATIENT_ID + "' and decode(VISIT_ID,null,0,'',0,visit_id)=" + VISIT_ID + " Order by REQUESTED_DATE_TIME");
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                DataTable dt = new DataTable("EXAM_MASTER");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetLAB_TEST_MASTER", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="PATIENT_ID"></param>
        /// <param name="VISIT_ID"></param>
        /// <returns></returns>
        public DataTable GetLAB_TEST_MASTER(string PATIENT_ID, int VISIT_ID, DateTime visitdate)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select lm.*,ed.exam_result_status_name,(select ITEM_NAME from lab_test_items a where a.test_no =lm.test_no and a.item_no=1) ITEM_NAME from  LAB_TEST_MASTER lm left join exam_result_status_dict ed on lm.result_status=ed.exam_result_status_code ");
            if (VISIT_ID == 0)
                strSql.AppendFormat(" where patient_id='{0}' and (visit_id={1} or visit_id is null) and lm.REQUESTED_DATE_TIME>=to_date('{2}','yyyy-MM-dd')", PATIENT_ID, VISIT_ID, visitdate.ToShortDateString());
            else
                strSql.Append(" where patient_id='" + PATIENT_ID + "' and VISIT_ID=" + VISIT_ID + " Order By REQUESTED_DATE_TIME");
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                DataTable dt = new DataTable("EXAM_MASTER");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetLAB_TEST_MASTER", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 病历列表树的加载
        /// </summary>
        /// <param name="patientID"></param>
        /// <param name="visitID"></param>
        /// <param name="visitDate"></param>
        /// <returns></returns>
        public DataTable GetLabTestList(string PATIENT_ID, int VISIT_ID, DateTime visitdate)
        {
            string where = "";
            if (VISIT_ID < 1)
                where = "  patient_id='" + PATIENT_ID + "' and visit_id=" + VISIT_ID + " and lm.requested_date_time>=to_date('" + visitdate.ToShortDateString() + "','yyyy-MM-dd') ";
            else
                where = "  patient_id='" + PATIENT_ID + "' and visit_id=" + VISIT_ID + " ";
            string sql = string.Format(@" select * from ( select lm.test_no,lm.result_status,lm.requested_date_time,lm.ORDERING_PROVIDER,
                                        (select to_char(wm_concat(ITEM_NAME)) from lab_test_items where test_no = lm.test_no)  ITEM_NAME,lm.RESULTS_RPT_DATE_TIME
                                        from  LAB_TEST_MASTER lm
                                        where lm.result_status=4 and {0}
                                          union 
                                          select lm.test_no,'0' result_status,lm.requested_date_time,lm.ORDERING_PROVIDER,
                                          (select to_char(wm_concat(ITEM_NAME)) from lab_test_items where test_no = lm.test_no)  ITEM_NAME,lm.RESULTS_RPT_DATE_TIME 
                                        from  LAB_TEST_MASTER lm
                                        where {0})
                                       Order by REQUESTED_DATE_TIME", where);

            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetLabTestList", sql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(CommandType.Text, sql);
                return ds.Tables[0];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        #endregion

        #region EXAM_MASTER
        /// <summary>
        /// 取检查子项表信息
        /// </summary>
        /// <param name="PATIENT_ID"></param>
        /// <returns></returns>
        public DataTable GetEXAM_ITEMS(string PATIENT_ID)
        {
            Database db = DatabaseFactory.CreateDatabase();
            //住院志 --A
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select EXAM_NO,EXAM_ITEM from EXAM_ITEMS where EXAM_NO in (select EXAM_NO from EXAM_MASTER ");
            strSql.Append(" where patient_id='" + PATIENT_ID + "')");
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                DataTable dt = new DataTable("EXAM_ITEMS");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetEXAM_ITEMS", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 患者本次住院的检查数据
        /// </summary>
        /// <param name="PATIENT_ID"></param>
        /// <param name="VISIT_ID"></param>
        /// <returns></returns>
        public DataTable GetEXAM_MASTER(string PATIENT_ID, int VISIT_ID)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select em.*,ed.exam_result_status_name from  EXAM_MASTER em left join exam_result_status_dict ed on em.result_status=ed.exam_result_status_code ");
            strSql.AppendFormat(" where patient_id='{0}' and VISIT_ID={1} Order By Req_date_time ", PATIENT_ID, VISIT_ID);
            try
            {
                DataTable dt = new DataTable("EXAM_MASTER");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetEXAM_MASTER", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(CommandType.Text, strSql.ToString());
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 患者本次留观的检查数据
        /// </summary>
        /// <param name="PATIENT_ID"></param>
        /// <param name="OB_VISIT_NO"></param>
        /// <returns></returns>
        public DataTable GetOB_EXAM_MASTER(string PATIENT_ID, string OB_VISIT_NO)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select em.*,ed.exam_result_status_name from EXAM_MASTER em left join exam_result_status_dict ed on em.result_status=ed.exam_result_status_code ");
            strSql.AppendFormat(" where patient_id='{0}' and OB_VISIT_NO='{1}' Order By Req_date_time ", PATIENT_ID, OB_VISIT_NO);
            try
            {
                DataTable dt = new DataTable("EXAM_MASTER");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetOB_EXAM_MASTER", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(CommandType.Text, strSql.ToString());
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 门诊检查
        /// </summary>
        /// <param name="PATIENT_ID"></param>
        /// <param name="VISIT_ID"></param>
        /// <param name="visitdate"></param>
        /// <returns></returns>
        public DataTable GetEXAM_MASTER(string PATIENT_ID, int VISIT_ID, DateTime visitdate)
        {
            Database db = DatabaseFactory.CreateDatabase();
            //住院志 --A
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select em.*,ed.exam_result_status_name from  EXAM_MASTER em left join exam_result_status_dict ed on em.result_status=ed.exam_result_status_code ");
            if (VISIT_ID == 0)
                strSql.AppendFormat(" where patient_id='{0}' and (visit_id='{1}' or visit_id is null) and REQ_DATE_TIME>=to_date('{2}','yyyy-MM-dd') Order By Req_Date_Time", PATIENT_ID, VISIT_ID, visitdate.ToShortDateString());
            else
                strSql.AppendFormat(" where patient_id='{0}' and VISIT_ID={1} Order By Req_Date_Time", PATIENT_ID, VISIT_ID);
            try
            {
                DataTable dt = new DataTable("EXAM_MASTER");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetEXAM_MASTER", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(CommandType.Text, strSql.ToString());
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        //冀飞涛 2012/4/24 【检查】 begin
        /// <summary>
        /// 取检查类别
        /// </summary>
        ///<param name="EXAM_CLASS_NAME">类别名称</param>
        /// <returns></returns>
        public DataTable GetEXAM_CLASS(string EXAM_CLASS_NAME)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * from  comm.EXAM_CLASS_DICT ");
            strSql.Append(" where EXAM_CLASS_NAME='" + EXAM_CLASS_NAME + "' ");
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                DataTable dt = new DataTable("EXAM_CLASS_DICT");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetEXAM_CLASS", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        public DataTable GetEXAM_MASTER(string EXAM_NO)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" select NAME,PATIENT_ID,SEX,DATE_OF_BIRTH,");
            strSql.Append(" (select DEPT_NAME from comm.dept_dict where DEPT_CODE = PERFORMED_BY) PERFORMED_BY, ");
            strSql.Append(" (select DEPT_NAME from comm.dept_dict where DEPT_CODE = REQ_DEPT) REQ_DEPT, ");
            strSql.Append(" PHYS_SIGN,CLIN_SYMP,CLIN_DIAG,RELEVANT_LAB_TEST,PATIENT_LOCAL_ID,NOTICE,REQ_PHYSICIAN,");
            strSql.Append(" EXAM_NO,REQ_DATE_TIME from EXAM_MASTER ");
            strSql.Append(" where EXAM_NO='" + EXAM_NO + "' Order By Req_Date_Time ");
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                DataTable dt = new DataTable("EXAM_MASTER");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetEXAM_MASTER", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 取医嘱信息
        /// </summary>
        ///<param name="EXAM_NO">检查号</param>
        /// <returns></returns>
        public DataTable GetEXAM_APPOINTS(string EXAM_NO)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" select NAME,PATIENT_ID,SEX,DATE_OF_BIRTH, ");
            strSql.Append(" (select DEPT_NAME from comm.dept_dict where DEPT_CODE = PERFORMED_BY) PERFORMED_BY, ");
            strSql.Append(" (select DEPT_NAME from comm.dept_dict where DEPT_CODE = REQ_DEPT) REQ_DEPT, ");
            strSql.Append(" PHYS_SIGN,CLIN_SYMP,CLIN_DIAG,RELEVANT_LAB_TEST,PATIENT_LOCAL_ID,NOTICE,REQ_PHYSICIAN, ");
            strSql.Append(" EXAM_NO,REQ_DATE_TIME ");
            strSql.Append(" from Exam_Appoints ");
            strSql.Append(" where EXAM_NO= '" + EXAM_NO + "' ");
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                DataTable dt = new DataTable("EXAM_APPOINTS");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetEXAM_APPOINTS", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        //冀飞涛 2012/4/24 【检查】 end
        public DataTable GetExamAppoints(string patientid, int visitid, DateTime visitdate)
        {
            string dateWhere = "";
            if (visitid < 1)
                dateWhere = " and req_date_time > to_date('" + visitdate.ToShortDateString() + "', 'yyyy-MM-dd')";
            else
                dateWhere = "";
            //            string sql = @"SELECT distinct V.EXAM_NO,V.EXAM_CLASS, to_char(WM_CONCAT(C.EXAM_ITEM)) EXAM_ITEM,V.req_date_time,V.REQ_PHYSICIAN,V.RESULT_STATUS
            //                             FROM (SELECT patient_id, visit_id, req_date_time, EXAM_NO, EXAM_CLASS,REQ_PHYSICIAN,'0' as RESULT_STATUS
            //                                          FROM EXAM_APPOINTS C 
            //                                    UNION
            //                                   SELECT patient_id, visit_id, req_date_time, EXAM_NO, EXAM_CLASS,REQ_PHYSICIAN,decode(RESULT_STATUS,'2','4',RESULT_STATUS) RESULT_STATUS
            //                                           FROM EXAM_MASTER B 
            //                                   ) V
            //                            JOIN EXAM_ITEMS C ON V.EXAM_NO = C.EXAM_NO
            //                             where patient_id = '" + patientid + "'  and visit_id = " + visitid + " " + dateWhere +
            //                             " GROUP BY V.EXAM_NO, V.EXAM_CLASS,V.req_date_time,V.REQ_PHYSICIAN,V.RESULT_STATUS ORDER BY V.EXAM_NO, V.EXAM_CLASS" +
            //                             " , Req_Date_Time";
            string sql = string.Format(@"
select distinct EXAM_NO,EXAM_CLASS, Item_Name EXAM_ITEM,req_date_time,REQ_PHYSICIAN,RESULT_STATUS
  from (select EXAM_NO,
               EXAM_CLASS,
               to_char(REQ_DATE_TIME, 'yyyy-MM-dd') req_date_time,
               (select to_char(wm_concat(EXAM_ITEM))
                  from EXAM_ITEMS
                 where EXAM_NO = a.EXAM_NO) Item_Name,
                 REQ_PHYSICIAN,
              '0' RESULT_STATUS
          from exam_appoints a
         where patient_id ='{0}'
           and visit_id ={1}  and exam_no not in (select exam_no from exam_master where patient_id='{0}' and visit_id={1} )
           union all
        select EXAM_NO,
               EXAM_CLASS,
               to_char(REQ_DATE_TIME, 'yyyy-MM-dd') req_date_time,
               (select to_char(wm_concat(EXAM_ITEM))
                  from EXAM_ITEMS
                 where EXAM_NO = EXAM_MASTER.EXAM_NO) Item_Name,
                 REQ_PHYSICIAN,
              '0' RESULT_STATUS
          from EXAM_MASTER
         where patient_id = '{0}'
           and visit_id ={1} 
        union all
        select EXAM_NO,
               EXAM_CLASS,
               to_char(REQ_DATE_TIME, 'yyyy-MM-dd') req_date_time,
               (select to_char(wm_concat(EXAM_ITEM))
                  from EXAM_ITEMS
                 where EXAM_NO = EXAM_MASTER.EXAM_NO) Item_Name,
                 REQ_PHYSICIAN,
              '4' RESULT_STATUS
          from EXAM_MASTER
         where patient_id = '{0}'
           and visit_id ={1} 
           and (RESULT_STATUS = '4' or RESULT_STATUS = '2'))
 order by EXAM_NO, EXAM_CLASS, RESULT_STATUS", patientid, visitid + " " + dateWhere);
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetEXAM_APPOINTS", sql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, sql).Tables[0];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        /// <summary>
        /// 检查报告
        /// </summary>
        /// <param name="EXAM_NO"></param>
        /// <returns></returns>
        public DataTable GetEXAM_REPORT(string EXAM_NO)
        {
            Database db = DatabaseFactory.CreateDatabase();
            //住院志 --A
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT EXAM_NO,DESCRIPTION,RECOMMENDATION,IMPRESSION,EXAM_PARA,IS_ABNORMAL FROM EXAM_REPORT");
            strSql.Append(" where EXAM_NO='" + EXAM_NO + "' ");
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                DataTable dt = new DataTable("EXAM_REPORT");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "GetEXAM_APPOINTS", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 取住院检查项目
        /// </summary>
        /// <param name="patientID"></param>
        /// <param name="visitID"></param>
        /// <returns></returns>
        public DataTable GetExamMasterItems(string patientID, int visitID)
        {
            string sql = @"select em.exam_no,ei.exam_item, ed.exam_result_status_name,em.report_date_time,em.req_date_time,er.description,er.impression,er.recommendation,em.patient_id,em.name,em.sex,em.REQ_PHYSICIAN,em.req_dept,em.PERFORMED_BY,em.EXAM_CLASS,em.exam_sub_class
                          from V_EXAM_MASTER em
                          left join exam_result_status_dict ed  on em.result_status = ed.exam_result_status_code
                          left join EXAM_REPORT er on em.exam_no=er.exam_no
                          join EXAM_ITEMS ei on ei.exam_no=em.exam_no
                          where patient_id='" + patientID + "' and decode(visit_id,null,0,'',0,visit_id)=" + visitID + "" + " Order By Req_Date_Time";
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(CommandType.Text, sql);
                if (ds == null)
                    return null;
                return ds.Tables[0];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        #endregion

        #region EMR_ITEM_CLASS
        public DataTable GetEMR_ITEM_CLASS(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select CODE,NAME,ICO_NAME,INP_OR_OUTP,STATUS,EDIT_MODE,EMR_ROLE_TYPE ");
            strSql.Append(" FROM MR_ITEM_CLASS  ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append("order by serial_no");
            Database db = DatabaseFactory.CreateDatabase();
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            DataTable dt = db.ExecuteDataSet(CommandType.Text, strSql.ToString()).Tables[0];
            return dt;
        }
        #endregion

        #region 数据集维护
        /// <summary>
        /// 获取病历类别与数据集关系
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetMR_Class_VS_DS(string strWhere)
        {
            string tableName = "cpr.Data_Set";
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT c.name,c.code,a.ds_name,a.ds_code,a.version,a.ds_class,a.ds_publisher,a.key_word,a.ds_language,a.ds_summary,a.is_group ");
            strSql.Append(" FROM data_set a ");
            strSql.Append(" RIGHT JOIN cpr.mr_item_class_vs_ds b ON a.ds_code = b.ds_code ");
            strSql.Append(" RIGHT JOIN cpr.mr_item_class c ON b.mr_item_class_code = c.code ");
            try
            {
                Utility.DbInfo.DbInfo dbInfo = new Utility.DbInfo.DbInfo();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                return dbInfo.GetData(strSql.ToString(), tableName);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion

        #region Orders
        public DataTable GetOrders(string PATIENT_ID, int VISIT_ID)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  TO_NCHAR(PATIENT_ID) ,  VISIT_ID, ORDER_NO, ORDER_SUB_NO , REPEAT_INDICATOR,b.order_class_name");
            strSql.Append(" ORDER_CLASS , TO_NCHAR(ORDER_TEXT)  ORDER_TEXT , TO_NCHAR(ORDER_CODE) ORDER_CODE,");
            strSql.Append("DOSAGE , TO_NCHAR(DOSAGE_UNITS) DOSAGE_UNITS , TO_NCHAR(ADMINISTRATION) ADMINISTRATION,DURATION , TO_NCHAR(DURATION_UNITS) DURATION_UNITS , START_DATE_TIME , STOP_DATE_TIME , TO_NCHAR(FREQUENCY) FREQUENCY ,");
            strSql.Append("FREQ_COUNTER , FREQ_INTERVAL , TO_NCHAR(FREQ_INTERVAL_UNIT) FREQ_INTERVAL_UNIT , TO_NCHAR(FREQ_DETAIL) FREQ_DETAIL  ,TO_NCHAR(PERFORM_SCHEDULE) PERFORM_SCHEDULE , TO_NCHAR(PERFORM_RESULT) PERFORM_RESULT,TO_NCHAR(ORDERING_DEPT) ORDERING_DEPT , TO_NCHAR(DOCTOR) DOCTOR , TO_NCHAR(STOP_DOCTOR) STOP_DOCTOR ,");
            strSql.Append("TO_NCHAR(NURSE) NURSE , TO_NCHAR(STOP_NURSE) STOP_NURSE ,  ENTER_DATE_TIME ,  STOP_ORDER_DATE_TIME,  ORDER_STATUS ,  DRUG_BILLING_ATTR ,  BILLING_ATTR ,  LAST_PERFORM_DATE_TIME,  ");
            strSql.Append("LAST_ACCTING_DATE_TIME ,  CURRENT_PRESC_NO ,  TO_NCHAR(DOCTOR_USER) DOCTOR_USER ,  VERIFY_DATA_TIME ,  ORDER_PRINT_INDICATOR ,  PROCESSING_DATE_TIME,  TO_NCHAR(PROCESSING_NURSE) PROCESSING_NURSE , ");
            strSql.Append("TO_NCHAR(STOP_PROCESSING_NURSE) STOP_PROCESSING_NURSE ,  STOP_PROCESSING_DATE_TIME,  CANCEL_DATE_TIME,  TO_NCHAR(CANCEL_DOCTOR) CANCEL_DOCTOR ,  DEGREE  ,  TO_NCHAR(APP_NO) APP_NO ,  IS_ADJUST ,  CONVERSION_DATE_TIME,  TO_NCHAR(CONTINUE_ORDER_NO) CONTINUE_ORDER_NO  , ");
            strSql.Append("TO_NCHAR(STOP_FLAG) STOP_FLAG   ,  ADAPT_SYMPTOM_INDICATE ,  TO_NCHAR(EVALUATE_PASS_FALG) EVALUATE_PASS_FALG ,  TO_NCHAR(COLLATE_NURSE) COLLATE_NURSE ,  COLLATE_DATE_TIME ,WRITEOFF from  ORDERS a left join order_class_dict b on a.order_class=b.order_class_code ");
            strSql.Append(" where patient_id='" + PATIENT_ID + "' and VISIT_ID=" + VISIT_ID + " ");
            strSql.Append(" order by ORDER_NO,ORDER_SUB_NO");
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
            DataTable dt = new DataTable("ORDERS");
            DataSet ds = db.ExecuteDataSet(cmd);
            if (ds != null)
                dt = ds.Tables[0];
            return dt;
        }

        /// <summary>
        /// 取医嘱列表
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetOrdersList(string strWhere)
        {
            string strSql = "select * from ORDADM.ORDERS ";
            if (strWhere.Trim() != "")
            {
                strSql = strSql + " where " + strWhere;
            }
            strSql += " order by ORDER_NO,ORDER_SUB_NO";
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            return dbinfo.GetData(strSql, "ORDERS");
        }

        /// <summary>
        /// 取医嘱费用表
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetOrdersCostsList(string strWhere)
        {
            string strSql = "select * from ORDERS_COSTS ";
            if (strWhere.Trim() != "")
            {
                strSql = strSql + " where " + strWhere;
            }
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                return dbinfo.GetData(strSql, "ORDERS_COSTS");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 更新医嘱表及医嘱费用表
        /// </summary>
        /// <param name="dsOrders"></param>
        /// <returns></returns>
        public bool UpdateOrdersList(DataSet dsOrders, DataSet dsCosts)
        {
            object[] arrDataSet = { dsOrders, dsCosts };
            return dbinfo.SaveData(null, arrDataSet);
        }

        /// <summary>
        /// 取最大医嘱号
        /// </summary>
        /// <returns></returns>
        public string Get_MaxOrdersNo(string PATIENT_ID, int VISIT_ID)
        {
            string strSql = "select max(ORDER_NO) from ORDADM.ORDERS where patient_id='" + PATIENT_ID + "' and VISIT_ID=" + VISIT_ID + " ";
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                return dbinfo.GetValueBySql(strSql);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion

        #region 处方DOCT_DRUG_PRESC_MASTER
        /// <summary>
        /// 处方
        /// </summary>
        /// <param name="PATIENT_ID"></param>
        /// <param name="VISIT_ID"></param>
        /// <returns></returns>
        public DataTable GetPRESC_MASTER(string PATIENT_ID, int VISIT_ID)
        {
            Database db = DatabaseFactory.CreateDatabase();
            //住院志 --A
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"select dm.presc_date,dm.presc_no,dm.dispensary,dm.patient_id,dm.visit_id,dm.name,dm.name_phonetic,dm.identity,dm.charge_type,dm.unit_in_contract,
                            decode(dm.presc_type,0,'西药','中药') presc_type,dm.presc_attr,dm.presc_source,dm.discharge_taking_indicator,dm.binding_presc_title,dm.repetition,
                            dm.count_per_repetition,dm.costs,dm.payments,dm.ordered_by,dm.prescribed_by,dm.entered_by,dm.presc_status,dm.dispensing_provider,dm.usage,
                            dm.decoction,dm.doctor_user,dm.newly_print,dm.verify_by,dm.verified_datetime,dm.diagnosis_name,dm.duty_doctor,dm.evaluate_pass_falg,dd.drug_name || ' ' || dd.drug_spec DRUG_NAME  
                            from DOCT_DRUG_PRESC_MASTER dm
                            left join DOCT_DRUG_PRESC_DETAIL dd on dm.presc_date=dd.presc_date and dm.presc_no=dd.presc_no ");
            strSql.Append(" where dm.patient_id='" + PATIENT_ID + "' and dm.VISIT_ID=" + VISIT_ID + "  ");
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                DataTable dt = new DataTable("DOCT_DRUG_PRESC_MASTER");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 门诊处方
        /// </summary>
        /// <param name="PATIENT_ID"></param>
        /// <param name="VISIT_ID"></param>
        /// <returns></returns>
        public DataTable GetOutPRESC_MASTER(string VISIT_DATE, int VISIT_NO)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"select 0 ISCHECKED,dm.presc_no,dm.costs,dm.drug_spec,dm.amount,dm.dosage,dm.dosage_units,dm.frequency,
                            decode(dm.item_class,'A','西药','中药') presc_type,dm.drug_name || ' ' || dm.drug_spec DRUG_NAME  
                            from OUTPDOCT.outp_presc dm");
            strSql.Append(" where VISIT_DATE=to_date('" + VISIT_DATE + "','yyyy-MM-dd') and VISIT_NO=" + VISIT_NO + "  ");
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                DataTable dt = new DataTable("OUTP_PRESC");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 本次开药
        /// </summary>
        /// <param name="PRESC_DATE"></param>
        /// <param name="PRESC_NO"></param>
        /// <returns></returns>
        public DataTable GetPRESC_MASTER_DETAIL(DateTime PRESC_DATE, int PRESC_NO)
        {
            Database db = DatabaseFactory.CreateDatabase();
            //住院志 --A
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * from  DOCT_DRUG_PRESC_DETAIL ");
            strSql.Append(" where PRESC_DATE=:PRESC_DATE and PRESC_NO=:PRESC_NO  ");
            DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
            db.AddInParameter(cmd, "PRESC_DATE", DbType.DateTime, PRESC_DATE);
            db.AddInParameter(cmd, "PRESC_NO", DbType.Int32, PRESC_NO);
            try
            {
                DataTable dt = new DataTable("DOCT_DRUG_PRESC_DETAIL");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 取住院处方的主表数据
        /// </summary>
        /// <param name="patientID"></param>
        /// <param name="visitID"></param>
        /// <returns></returns>
        public DataTable GetPrescMaster(string patientID, int visitID)
        {
            string sql = "select * from DOCT_DRUG_PRESC_MASTER where patient_id='" + patientID + "' and visit_id=" + visitID + "";
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, sql).Tables[0];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        #endregion

        #region 会诊
        /// <summary>
        /// 病历书写界面。查询病人的会诊信息
        /// </summary>
        /// <param name="patientID"></param>
        /// <param name="visitID"></param>
        /// <returns></returns>
        public DataTable GetConsulations(string patientID, int visitID)
        {
            string sql = @"select m.consultation_id,m.patient_id,m.visit_id,m.consultation_type,m.apply_date_time,m.end_date_time,m.consultation_date_time,
                        s.name applydoctor,m.signature_no,d.consultation_id,s.name inviteddoctor,dd.dept_name,d.affirm_date_time,d.commit_date_time,d.consultation_idea,d.dept_assign
                        ,d.consultation_commit,d.signature_no 
                        from ordadm.consultation_doctor_master m
                        left join ordadm.consultation_doctor_detail d on m.consultation_id=d.consultation_id
                        left join staff_dict s on d.consultation_doctor=s.user_name or m.consultation_apply_doctor=s.user_name 
                        left join dept_dict dd on d.consultation_dept=dd.dept_code
                        where m.patient_id='" + patientID + "' and m.visit_id='" + visitID + "'";
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
                }
                DataTable dt = new DataTable("CONSULATION");
                DataSet ds = db.ExecuteDataSet(CommandType.Text, sql);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString()); ;
            }
        }
        /// <summary>
        /// 只查询主表数据
        /// </summary>
        /// <param name="patientID"></param>
        /// <param name="visitID"></param>
        /// <returns></returns>
        public DataTable GetConsulationMaster(string patientID, int visitID)
        {
            string sql = @"
                        select m.consultation_id,m.patient_id,m.visit_id,m.consultation_type,m.consultation_explain,m.apply_date_time,m.end_date_time,m.consultation_date_time
                        ,s.name applydoctor from ordadm.consultation_doctor_master m
                        left join staff_dict s on m.consultation_apply_doctor=s.user_name
                        where m.patient_id='" + patientID + "' and m.visit_id='" + visitID + "'";
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                DataTable dt = new DataTable();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(CommandType.Text, sql);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        /// <summary>
        /// 只查询从表数据
        /// </summary>
        /// <param name="consulationID"></param>
        /// <returns></returns>
        public DataTable GetConsulationDetail(string consulationID)
        {
            string sql = @"select d.sub_id,dd.dept_name,s.name,d.commit_date_time,d.consultation_idea,decode(d.consultation_commit,2,'已提交','未提交') consultation_commit
                         from ordadm.consultation_doctor_detail d
                         left join staff_dict s on d.consultation_doctor=s.user_name 
                         left join dept_dict dd on d.consultation_dept=dd.dept_code where d.consultation_id in (" + consulationID + ")";
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                DataTable dt = new DataTable();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(CommandType.Text, sql);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        #endregion

        #region 处置

        public DataTable GetTREAT_REC(DateTime VISIT_DATE, int VISIT_NO, bool isClinic)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            if (isClinic)
            {
                strSql.Append(@" select * from outp_treat_rec where visit_date=:VISIT_DATE and visit_no=:VISIT_NO
             and item_class in ('E','F','G','H')
union
select * from outp_treat_rec where visit_date=:VISIT_DATE and visit_no=:VISIT_NO and
             (item_class='C' or item_class='D') and appoint_no is null");
            }
            else
            {
                strSql.Append("select * from  outp_treat_rec ");
                strSql.Append(" where VISIT_DATE=:VISIT_DATE and VISIT_NO=:VISIT_NO  ");
            }
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                db.AddInParameter(cmd, "VISIT_DATE", DbType.Date, VISIT_DATE);
                db.AddInParameter(cmd, "VISIT_NO", DbType.Int32, VISIT_NO);
                DataTable dt = new DataTable("outp_treat_rec");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 查询初值数据
        /// </summary>
        /// <param name="VISIT_DATE"></param>
        /// <param name="VISIT_NO"></param>
        /// <returns></returns>
        public DataTable GetDATASOURCETREAT_REC(DateTime VISIT_DATE, int VISIT_NO)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select 0 ISCHECKED,a.* from  outp_treat_rec a ");
            strSql.Append(" where a.VISIT_DATE=:VISIT_DATE and a.VISIT_NO=:VISIT_NO  ");
            DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
            db.AddInParameter(cmd, "VISIT_DATE", DbType.Date, VISIT_DATE);
            db.AddInParameter(cmd, "VISIT_NO", DbType.Int32, VISIT_NO);
            try
            {
                DataTable dt = new DataTable("outp_treat_rec");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        public DataSet Get_OUTP_TREAT_REC(DateTime VISIT_DATE, int VISIT_NO, string strWhere)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"SELECT decode(OUTP_TREAT_REC.ITEM_CLASS,'D','检查','C','化验','E','治疗','F','手术','G','麻醉','H','护理','I','膳食','J','材料','其它') ITEM_CLASS,
         OUTP_TREAT_REC.ITEM_NAME,OUTP_TREAT_REC.AMOUNT ,
         OUTP_TREAT_REC.UNITS,OUTP_TREAT_REC.FREQUENCY,DEPT_DICT.DEPT_NAME,
         OUTP_ORDERS.ORDER_DATE,OUTP_ORDERS.DOCTOR,OUTP_ORDERS.ORDERED_BY,
         OUTP_TREAT_REC.CHARGES,decode(OUTP_TREAT_REC.CHARGE_INDICATOR,1,'已收费','未收费') CHARGE_INDICATOR,
         DEPT_DICT.POSITION,STAFF_DICT.EMP_NO,
         OUTP_TREAT_REC.SERIAL_NO,OUTP_TREAT_REC.ITEM_NO
    FROM OUTP_TREAT_REC, OUTP_ORDERS, DEPT_DICT, STAFF_DICT
   WHERE (STAFF_DICT.NAME = OUTP_ORDERS.DOCTOR)
     and (OUTP_ORDERS.SERIAL_NO = OUTP_TREAT_REC.SERIAL_NO)
     and (OUTP_TREAT_REC.PERFORMED_BY = DEPT_DICT.DEPT_CODE)");
            strSql.Append(" and (OUTP_TREAT_REC.VISIT_DATE = :VISIT_DATE)");
            strSql.Append(" AND (OUTP_TREAT_REC.VISIT_NO = :VISIT_NO)");
            if (!string.IsNullOrEmpty(strWhere))
            {
                strSql.Append(" AND " + strWhere);
            }
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                db.AddInParameter(cmd, "VISIT_DATE", DbType.Date, VISIT_DATE);
                db.AddInParameter(cmd, "VISIT_NO", DbType.Int32, VISIT_NO);
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                return ds;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        #endregion
        #region 门诊处方
        public DataTable GetOutpPrescMaster(DateTime vdate, int vno)
        {
            string sql = @"select visit_date,visit_no,presc_no,wm_concat(drug_name) drug_name from outp_presc 
                           where visit_date=to_date('" + vdate.ToShortDateString() + "','yyyy-MM-dd') and visit_no=" + vno + " group by visit_date,visit_no,presc_no";
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, sql).Tables[0];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        public DataTable GetOutpPrescDetail(DateTime vdate, int vno, string prescNo)
        {
            string sql = "select * from outp_presc where visit_date=to_date('" + vdate.ToShortDateString() + "','yyyy-MM-dd') and visit_no=" + vno + " and presc_no='" + prescNo + "'";
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, sql).Tables[0];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        #endregion
        #region 处方DOCT_DRUG_PRESC_MASTER
        /// <summary>
        /// 会诊
        /// </summary>
        /// <param name="PATIENT_ID"></param>
        /// <param name="VISIT_ID"></param>
        /// <returns></returns>
        public DataTable GetCONSULTATION_MASTER(string PATIENT_ID, int VISIT_ID)
        {
            Database db = DatabaseFactory.CreateDatabase();
            //住院志 --A
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * from  CONSULTATION_DOCTOR_MASTER ");
            strSql.Append(" where patient_id=:PATIENT_ID and VISIT_ID=:VISIT_ID  ");
            DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
            db.AddInParameter(cmd, "PATIENT_ID", DbType.String, PATIENT_ID);
            db.AddInParameter(cmd, "VISIT_ID", DbType.Int32, VISIT_ID);
            DataTable dt = new DataTable("CONSULTATION_DOCTOR_MASTER");
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            DataSet ds = db.ExecuteDataSet(cmd);
            if (ds != null)
                dt = ds.Tables[0];
            return dt;
        }
        /// <summary>
        /// 详细
        /// </summary>
        /// <param name="PRESC_DATE"></param>
        /// <param name="PRESC_NO"></param>
        /// <returns></returns>
        public DataTable GetCONSULTATION_MASTER_DETAIL(int CONSULTATION_ID)
        {
            Database db = DatabaseFactory.CreateDatabase();
            //住院志 --A
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * from  CONSULTATION_DOCTOR_DETAIL ");
            strSql.Append(" where CONSULTATION_ID=:CONSULTATION_ID  ");
            DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
            db.AddInParameter(cmd, "CONSULTATION_ID", DbType.Int32, CONSULTATION_ID);
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                DataTable dt = new DataTable("CONSULTATION_DOCTOR_DETAIL");
                DataSet ds = db.ExecuteDataSet(cmd);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion

        #region  OUTP_MR_FILE_INDEX

        /// <summary>
        /// 文件号
        /// </summary>
        /// <param name="PATIENT_ID"></param>
        /// <param name="VISIT_DATE"></param>
        /// <param name="VISIT_NO"></param>
        /// <returns></returns>
        public DataTable GetAddInfoOUTP(string PATIENT_ID, DateTime VISIT_DATE, int VISIT_NO)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select max(file_no)+1 as fileno from medrec.OUTP_MR_FILE_INDEX");
            strSql.Append(" where patient_id='" + PATIENT_ID + "' and VISIT_DATE=to_date('" + VISIT_DATE.ToString() + "','YYYY-MM-DD HH24:MI:SS')  and VISIT_NO=" + VISIT_NO + " ");
            DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
            DataTable dt = new DataTable("OUTP_MR_FILE_INDEX");
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            DataSet ds = db.ExecuteDataSet(cmd);
            if (ds != null)
                dt = ds.Tables[0];
            return dt;
        }

        /// <summary>
        /// 得到最大ID
        /// </summary>
        public int GetMaxId()
        {
            string strsql = "select max(VISIT_NO)+1 from medrec.OUTP_MR_FILE_INDEX";
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strsql.ToString(), endpoint.Address);
            }
            Database db = DatabaseFactory.CreateDatabase();
            object obj = db.ExecuteScalar(CommandType.Text, strsql);
            if (obj != null && obj != DBNull.Value)
            {
                return int.Parse(obj.ToString());
            }
            return 1;
        }

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(DateTime VISIT_DATE, int VISIT_NO, int FILE_NO)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from medrec.OUTP_MR_FILE_INDEX where VISIT_DATE=:VISIT_DATE and VISIT_NO=@VISIT_NO and FILE_NO=:FILE_NO ");
            DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
            db.AddInParameter(dbCommand, "VISIT_DATE", DbType.String, VISIT_DATE);
            db.AddInParameter(dbCommand, "VISIT_NO", DbType.String, VISIT_NO);
            db.AddInParameter(dbCommand, "FILE_NO", DbType.String, FILE_NO);
            int cmdresult;
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            object obj = db.ExecuteScalar(dbCommand);
            if ((Object.Equals(obj, null)) || (Object.Equals(obj, System.DBNull.Value)))
            {
                cmdresult = 0;
            }
            else
            {
                cmdresult = int.Parse(obj.ToString());
            }
            if (cmdresult == 0)
            {
                return false;
            }
            else
            {
                return true;
            }
        }
        /// <summary>
        /// 增加一条数据
        /// </summary>
        public int AddOUTP_MR_FILE_INDEX(OUTP_MR_FILE_INDEX model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into medrec.OUTP_MR_FILE_INDEX(");
            strSql.Append("VISIT_DATE,VISIT_NO,FILE_NO,PATIENT_ID,FILE_NAME,TOPIC,CREATOR_NAME,CREATOR_ID,CREATE_DATE_TIME,LAST_MODIFY_DATE_TIME,MR_CODE,FILE_ATTR,FILE_FLAG,ORDINAL)");
            strSql.Append(" values (");
            strSql.Append(":VISIT_DATE,:VISIT_NO,:FILE_NO,:PATIENT_ID,:FILE_NAME,:TOPIC,:CREATOR_NAME,:CREATOR_ID,:CREATE_DATE_TIME,:LAST_MODIFY_DATE_TIME,:MR_CODE,:FILE_ATTR,:FILE_FLAG,:ORDINAL)");
            Database db = DatabaseFactory.CreateDatabase();
            DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
            db.AddInParameter(dbCommand, "VISIT_DATE", DbType.DateTime, model.VISIT_DATE);
            db.AddInParameter(dbCommand, "VISIT_NO", DbType.Int32, model.VISIT_NO);
            db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, model.FILE_NO);
            db.AddInParameter(dbCommand, "PATIENT_ID", DbType.String, model.PATIENT_ID);
            db.AddInParameter(dbCommand, "FILE_NAME", DbType.String, model.FILE_NAME);
            db.AddInParameter(dbCommand, "TOPIC", DbType.String, model.TOPIC);
            db.AddInParameter(dbCommand, "CREATOR_NAME", DbType.String, model.CREATOR_NAME);
            db.AddInParameter(dbCommand, "CREATOR_ID", DbType.String, model.CREATOR_ID);
            db.AddInParameter(dbCommand, "CREATE_DATE_TIME", DbType.DateTime, model.CREATE_DATE_TIME);
            db.AddInParameter(dbCommand, "LAST_MODIFY_DATE_TIME", DbType.DateTime, model.LAST_MODIFY_DATE_TIME);
            db.AddInParameter(dbCommand, "MR_CODE", DbType.String, model.MR_CODE);
            db.AddInParameter(dbCommand, "FILE_ATTR", DbType.String, model.FILE_ATTR);
            db.AddInParameter(dbCommand, "FILE_FLAG", DbType.String, model.FILE_FLAG);
            db.AddInParameter(dbCommand, "ORDINAL", DbType.String, model.ORDINAL);
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            return db.ExecuteNonQuery(dbCommand);
        }
        /// <summary>
        /// 获取门诊病历索引信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public DataTable GetOutp(OUTP_MR_FILE_INDEX model)
        {
            DataSet ds = new DataSet();
            DataTable dt = null;
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select MR_CODE  from medrec.outp_mr_file_index ");
            strSql.Append(" where VISIT_DATE=:VISIT_DATE and VISIT_NO=:VISIT_NO and FILE_NO=@FILE_NO ");
            Database db = DatabaseFactory.CreateDatabase();
            DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
            db.AddInParameter(dbCommand, "VISIT_DATE", DbType.DateTime, model.VISIT_DATE);
            db.AddInParameter(dbCommand, "VISIT_NO", DbType.Int32, model.VISIT_NO);
            db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, model.FILE_NO);
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            ds = db.ExecuteDataSet(dbCommand);
            if (ds != null)
                dt = ds.Tables[0];
            return dt;
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public int Update(OUTP_MR_FILE_INDEX model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update medrec.OUTP_MR_FILE_INDEX set ");
            strSql.Append("PATIENT_ID='" + model.PATIENT_ID + "',");
            strSql.Append("FILE_NAME='" + model.FILE_NAME + "',");
            strSql.Append("TOPIC='" + model.TOPIC + "',");
            strSql.Append("CREATOR_NAME='" + model.CREATOR_NAME + "',");
            strSql.Append("CREATOR_ID='" + model.CREATOR_ID + "',");
            strSql.Append("CREATE_DATE_TIME=to_date('" + model.CREATE_DATE_TIME + "','yyyy-MM-dd HH24:mi:ss'),");
            strSql.Append("LAST_MODIFY_DATE_TIME=to_date('" + model.LAST_MODIFY_DATE_TIME + "','yyyy-MM-dd HH24:mi:ss'),");
            strSql.Append("MR_CODE='" + model.MR_CODE + "',");
            strSql.Append("FILE_ATTR='" + model.FILE_ATTR + "',");
            strSql.Append("FILE_FLAG='" + model.FILE_FLAG + "'");
            strSql.Append(" where VISIT_DATE=to_date('" + model.VISIT_DATE.ToShortDateString() + "','yyyy-MM-dd') and VISIT_NO='" + model.VISIT_NO + "' and FILE_NO='" + model.FILE_NO + "' ");
            Database db = DatabaseFactory.CreateDatabase();
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            return db.ExecuteNonQuery(CommandType.Text, strSql.ToString());
        }
        /// <summary>
        /// 更新最后时间
        /// </summary>
        public int UpdateLastTime(OUTP_MR_FILE_INDEX model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update medrec.OUTP_MR_FILE_INDEX set ");
            strSql.AppendFormat("LAST_MODIFY_DATE_TIME=to_date('{0}','yyyy-MM-dd HH24:mi:ss')", model.LAST_MODIFY_DATE_TIME);
            strSql.AppendFormat(" where VISIT_DATE=to_date('{0}','yyyy-MM-dd HH24:mi:ss') and VISIT_NO={1} and FILE_NO={2} ", model.VISIT_DATE, model.VISIT_NO, model.FILE_NO);
            Database db = DatabaseFactory.CreateDatabase();
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            return db.ExecuteNonQuery(CommandType.Text, strSql.ToString());
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public void Delete(DateTime VISIT_DATE, int VISIT_NO, int FILE_NO)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from medrec.OUTP_MR_FILE_INDEX ");
            strSql.Append(" where VISIT_DATE=:VISIT_DATE and VISIT_NO=:VISIT_NO and FILE_NO=:FILE_NO ");
            Database db = DatabaseFactory.CreateDatabase();
            DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
            db.AddInParameter(dbCommand, "VISIT_DATE", DbType.String, VISIT_DATE);
            db.AddInParameter(dbCommand, "VISIT_NO", DbType.String, VISIT_NO);
            db.AddInParameter(dbCommand, "FILE_NO", DbType.String, FILE_NO);
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            db.ExecuteNonQuery(dbCommand);
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public OUTP_MR_FILE_INDEX GetModelOUTP(DateTime VISIT_DATE, int VISIT_NO, int FILE_NO)
        {
            string strSql = "select VISIT_DATE,VISIT_NO,FILE_NO,PATIENT_ID,FILE_NAME,TOPIC,CREATOR_NAME,CREATOR_ID,CREATE_DATE_TIME,LAST_MODIFY_DATE_TIME,MR_CODE,FILE_ATTR,FILE_FLAG from medrec.OUTP_MR_FILE_INDEX" +
            " where VISIT_DATE=to_date('" + VISIT_DATE.ToString() + "','YYYY-MM-DD HH24:MI:SS') and VISIT_NO=" + VISIT_NO + " and FILE_NO=" + FILE_NO;
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            OUTP_MR_FILE_INDEX model = null;
            DataTable dataReader = DbHelperOra.GetDataReader(strSql);// db.ExecuteReader(dbCommand))
            if (dataReader.Rows.Count > 0)
            {
                model = ReaderBindOUTPINDEX(dataReader.Rows[0]);
            }
            return model;
        }
        /// <summary>
        /// 得到一个对象实体
        /// 只在航空医院使用为了兼容以前的门诊病历流程
        /// </summary>
        public OUTP_MR_FILE_INDEX GetModelOUTPForOld(DateTime VISIT_DATE, int VISIT_NO, int ordinal)
        {
            OUTP_MR_FILE_INDEX model = null;
            string strSql = "select VISIT_DATE,VISIT_NO,FILE_NO,PATIENT_ID,FILE_NAME,TOPIC,CREATOR_NAME,CREATOR_ID,CREATE_DATE_TIME,LAST_MODIFY_DATE_TIME,MR_CODE,FILE_ATTR,FILE_FLAG from medrec.OUTP_MR_FILE_INDEX "+
            " where VISIT_DATE=to_date('" + VISIT_DATE.ToString() + "','YYYY-MM-DD HH24:MI:SS') and VISIT_NO=" + VISIT_NO + " and ordinal=" + ordinal + " and file_attr='OA'";
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            DataTable dataReaders = DbHelperOra.GetDataReader(strSql);// db.ExecuteReader(dbCommand))
            if (dataReaders.Rows.Count>0)
            {
                model = ReaderBindOUTPINDEX(dataReaders.Rows[0]);
            }
            if (model == null)
            {
                strSql = "select VISIT_DATE,VISIT_NO,FILE_NO,PATIENT_ID,FILE_NAME,TOPIC,CREATOR_NAME,CREATOR_ID,CREATE_DATE_TIME,LAST_MODIFY_DATE_TIME,MR_CODE,FILE_ATTR,FILE_FLAG from medrec.OUTP_MR_FILE_INDEX "+
                " where VISIT_DATE=to_date('" + VISIT_DATE.ToString() + "','YYYY-MM-DD HH24:MI:SS') and VISIT_NO=" + VISIT_NO + " and ordinal=" + ordinal;
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
               
                DataTable dataReader =DbHelperOra.GetDataReader(strSql);// db.ExecuteReader(dbCommand))
                if (dataReader.Rows.Count>0)
                {
                    model = ReaderBindOUTPINDEX(dataReader.Rows[0]);
                }
            }
            return model;
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select VISIT_DATE,VISIT_NO,FILE_NO,PATIENT_ID,FILE_NAME,TOPIC,CREATE_NAME,CREATE_ID,CREATE_DATE_TIME,LAST_MODIFY_DATE_TIME,MR_CODE,STUDY_DOCTOR_ID,STUDY_DOCTOR_NAME ");
            strSql.Append(" FROM medrec.OUTP_MR_FILE_INDEX ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            Database db = DatabaseFactory.CreateDatabase();
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            return db.ExecuteDataSet(CommandType.Text, strSql.ToString());
        }

        /// <summary>
        /// 获得数据列表（比DataSet效率高，推荐使用）
        /// </summary>
        public List<OUTP_MR_FILE_INDEX> GetListArray(string strWhere)
        {
            string strSql = "select VISIT_DATE,VISIT_NO,FILE_NO,PATIENT_ID,FILE_NAME,TOPIC,CREATOR_NAME,CREATOR_ID,CREATE_DATE_TIME,LAST_MODIFY_DATE_TIME,MR_CODE,FILE_ATTR,FILE_FLAG"+
            " FROM medrec.OUTP_MR_FILE_INDEX";
            if (strWhere.Trim() != "")
            {
                strSql += " where " + strWhere;
            }
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            List<OUTP_MR_FILE_INDEX> list = new List<OUTP_MR_FILE_INDEX>();
            DataTable dataReader = DbHelperOra.GetDataReader(strSql);// db.ExecuteReader(CommandType.Text, strSql.ToString()))
            foreach (DataRow dr in dataReader.Rows)
            {
                list.Add(ReaderBindOUTPINDEX(dr));
            }
            return list;
        }

        /// <summary>
        /// 对象实体绑定数据
        /// </summary>
        public OUTP_MR_FILE_INDEX ReaderBindOUTPINDEX(DataRow dataReader)
        {
            OUTP_MR_FILE_INDEX model = new OUTP_MR_FILE_INDEX();
            object ojb;
            ojb = dataReader["VISIT_DATE"];
            if (ojb != null && ojb != DBNull.Value)
            {
                model.VISIT_DATE = (DateTime)ojb;
            }
            ojb = dataReader["VISIT_NO"];
            if (ojb != null && ojb != DBNull.Value)
            {
                model.VISIT_NO = int.Parse(ojb.ToString());
            }
            ojb = dataReader["FILE_NO"];
            if (ojb != null && ojb != DBNull.Value)
            {
                model.FILE_NO = int.Parse(ojb.ToString());
            }
            model.PATIENT_ID = dataReader["PATIENT_ID"].ToString();
            model.FILE_NAME = dataReader["FILE_NAME"].ToString();
            model.TOPIC = dataReader["TOPIC"].ToString();
            model.CREATOR_NAME = dataReader["CREATOR_NAME"].ToString();
            model.CREATOR_ID = dataReader["CREATOR_ID"].ToString();
            ojb = dataReader["CREATE_DATE_TIME"];
            if (ojb != null && ojb != DBNull.Value)
            {
                model.CREATE_DATE_TIME = (DateTime)ojb;
            }
            ojb = dataReader["LAST_MODIFY_DATE_TIME"];
            if (ojb != null && ojb != DBNull.Value)
            {
                model.LAST_MODIFY_DATE_TIME = (DateTime)ojb;
            }
            model.MR_CODE = dataReader["MR_CODE"].ToString();
            model.FILE_ATTR = dataReader["FILE_ATTR"].ToString();
            model.FILE_FLAG = dataReader["FILE_FLAG"].ToString();
            return model;
        }

        #endregion  成员方法

        #region xml
        #region add by chenj
        public int InsertNewRecord(System.Data.OracleClient.OracleConnection conn, string insertSql)
        {
            int ret = 0;
            System.Data.OracleClient.OracleCommand cmd = null;
            try
            {
                cmd = conn.CreateCommand();
                cmd.CommandText = insertSql;
                ret = cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                if (cmd != null)
                    cmd.Dispose();
            }
            return ret;
        }
        public void UpdateDataClobData(System.Data.OracleClient.OracleConnection conn, string selectSql, string clobString)
        {
            System.Data.OracleClient.OracleTransaction otx;
            System.Data.OracleClient.OracleCommand cmd;
            System.Data.OracleClient.OracleDataReader odr;
            System.Data.OracleClient.OracleLob clob;
            try
            {
                otx = conn.BeginTransaction();
                cmd = conn.CreateCommand();
                cmd.CommandText = selectSql;
                cmd.Transaction = otx;
                odr = cmd.ExecuteReader();
                odr.Read();
                if (odr.HasRows)
                {
                    clob = odr.GetOracleLob(0);
                    clob.Erase();
                    byte[] buffer = System.Text.Encoding.Unicode.GetBytes(clobString);
                    clob.Write(buffer, 0, buffer.Length);
                }
                otx.Commit();
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        private int Insert_OMF_XmlDoc(System.Data.OracleClient.OracleConnection conn, string sourTableName, string deskTableName, DateTime VASIT_DATE, int VISIT_NO, int FILE_NO)
        {
            int ret = 0;
            System.Data.OracleClient.OracleCommand cmd = null;
            StringBuilder strSql = new StringBuilder();
            try
            {
                cmd = conn.CreateCommand();
                strSql.Append(" insert into ");
                //cpr.outp_mr_file_xml
                strSql.Append(deskTableName);
                strSql.Append("(VASIT_DATE,VISIT_NO,FILE_NO,FILE_XML) ");
                strSql.Append(" values (:VASIT_DATE,:VISIT_NO,:FILE_NO,sys.xmlType.createXML((select FILE_XML_TEMP from ");
                //cpr.outp_mr_file_xml_temp
                strSql.Append(sourTableName);
                strSql.Append(" where VASIT_DATE=:VASIT_DATE and VISIT_NO=:VISIT_NO and FILE_NO=:FILE_NO ))) ");
                cmd.Parameters.Add("VASIT_DATE", System.Data.OracleClient.OracleType.DateTime).Value = VASIT_DATE;
                cmd.Parameters.Add("VISIT_NO", System.Data.OracleClient.OracleType.Int32).Value = VISIT_NO;
                cmd.Parameters.Add("FILE_NO", System.Data.OracleClient.OracleType.Int32).Value = FILE_NO;
                cmd.CommandText = strSql.ToString();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                ret = cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
            finally
            {
                if (cmd != null)
                    cmd.Dispose();
            }
            return ret;
        }
        private int Update_OMF_XmlDoc(System.Data.OracleClient.OracleCommand cmd, string sourTableName, string deskTableName, DateTime VASIT_DATE, int VISIT_NO, int FILE_NO)
        {
            int ret = 0;
            StringBuilder strSql = new StringBuilder();
            try
            {
                strSql.Append(" update ");
                strSql.Append(deskTableName);
                strSql.Append(" set FILE_XML= sys.xmlType.createXML((select FILE_XML_TEMP from ");
                strSql.Append(sourTableName);
                strSql.Append(" where VASIT_DATE=:VASIT_DATE and VISIT_NO=:VISIT_NO and FILE_NO=:FILE_NO )) ");
                strSql.Append(" where VASIT_DATE=:VASIT_DATE and VISIT_NO=:VISIT_NO and FILE_NO=:FILE_NO ");
                cmd.Parameters.Add("VASIT_DATE", System.Data.OracleClient.OracleType.DateTime).Value = VASIT_DATE;
                cmd.Parameters.Add("VISIT_NO", System.Data.OracleClient.OracleType.Int32).Value = VISIT_NO;
                cmd.Parameters.Add("FILE_NO", System.Data.OracleClient.OracleType.Int32).Value = FILE_NO;
                cmd.CommandText = strSql.ToString();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                ret = cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
            finally
            {
                if (cmd != null)
                    cmd.Dispose();
            }
            return ret;
        }

        public int InsertNewRecord(System.Data.OracleClient.OracleCommand cmd, string insertSql)
        {
            int ret = 0;
            try
            {
                cmd.CommandText = insertSql;
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", insertSql.ToString(), endpoint.Address);
                }
                ret = cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
            }
            return ret;
        }
        public void UpdateDataClobData(System.Data.OracleClient.OracleCommand cmd, string selectSql, string clobString)
        {
            System.Data.OracleClient.OracleDataReader odr;
            System.Data.OracleClient.OracleLob clob;
            try
            {
                cmd.CommandText = selectSql;
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "",selectSql.ToString(), endpoint.Address);
                }
                odr = cmd.ExecuteReader();
                odr.Read();
                if (odr.HasRows)
                {
                    clob = odr.GetOracleLob(0);
                    clob.Erase();
                    byte[] buffer = System.Text.Encoding.Unicode.GetBytes(clobString);
                    clob.Write(buffer, 0, buffer.Length);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        private int Insert_MRF_XmlDoc(System.Data.OracleClient.OracleCommand cmd, string sourTableName, string deskTableName, string PATIENT_ID, int VISIT_ID, int FILE_NO)
        {
            int ret = 0;
            StringBuilder strSql = new StringBuilder();
            try
            {
                strSql.Append(" insert into ");
                strSql.Append(deskTableName);
                strSql.Append("(PATIENT_ID,VISIT_ID,FILE_NO,FILE_XML) ");
                strSql.Append(" values (:PATIENT_ID,:VISIT_ID,:FILE_NO,sys.xmlType.createXML((select FILE_XML_TEMP from ");
                strSql.Append(sourTableName);
                strSql.Append(" where PATIENT_ID=:PATIENT_ID and VISIT_ID=:VISIT_ID and FILE_NO=:FILE_NO ))) ");
                cmd.Parameters.Add("PATIENT_ID", System.Data.OracleClient.OracleType.VarChar).Value = PATIENT_ID;
                cmd.Parameters.Add("VISIT_ID", System.Data.OracleClient.OracleType.Int32).Value = VISIT_ID;
                cmd.Parameters.Add("FILE_NO", System.Data.OracleClient.OracleType.Int32).Value = FILE_NO;
                cmd.CommandText = strSql.ToString();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                ret = cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
            finally
            {
            }
            return ret;
        }
        private int Update_MRF_XmlDoc(System.Data.OracleClient.OracleCommand cmd, string sourTableName, string deskTableName, string PATIENT_ID, int VISIT_ID, int FILE_NO)
        {
            int ret = 0;
            try
            {
                StringBuilder strSql = new StringBuilder();
                strSql.Append(" update ");
                strSql.Append(deskTableName);
                strSql.Append(" set FILE_XML= sys.xmlType.createXML((select FILE_XML_TEMP from ");
                strSql.Append(sourTableName);
                strSql.Append(" where PATIENT_ID=:PATIENT_ID and VISIT_ID=:VISIT_ID and FILE_NO=:FILE_NO )) ");
                strSql.Append(" where PATIENT_ID=:PATIENT_ID and VISIT_ID=:VISIT_ID and FILE_NO=:FILE_NO ");
                cmd.Parameters.Add("PATIENT_ID", System.Data.OracleClient.OracleType.VarChar).Value = PATIENT_ID;
                cmd.Parameters.Add("VISIT_ID", System.Data.OracleClient.OracleType.Int32).Value = VISIT_ID;
                cmd.Parameters.Add("FILE_NO", System.Data.OracleClient.OracleType.Int32).Value = FILE_NO;
                cmd.CommandText = strSql.ToString();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                ret = cmd.ExecuteNonQuery();
                if (ret == 0)
                {
                    StringBuilder strSql1 = new StringBuilder();
                    strSql1.Append(" insert into ");
                    strSql1.Append(deskTableName);
                    strSql1.Append("(PATIENT_ID,VISIT_ID,FILE_NO,FILE_XML) ");
                    strSql1.Append(" values (:PATIENT_ID,:VISIT_ID,:FILE_NO,sys.xmlType.createXML((select FILE_XML_TEMP from ");
                    strSql1.Append(sourTableName);
                    strSql1.Append(" where PATIENT_ID=:PATIENT_ID and VISIT_ID=:VISIT_ID and FILE_NO=:FILE_NO ))) ");
                    cmd.Parameters.Add("PATIENT_ID", System.Data.OracleClient.OracleType.VarChar).Value = PATIENT_ID;
                    cmd.Parameters.Add("VISIT_ID", System.Data.OracleClient.OracleType.Int32).Value = VISIT_ID;
                    cmd.Parameters.Add("FILE_NO", System.Data.OracleClient.OracleType.Int32).Value = FILE_NO;
                    cmd.CommandText = strSql1.ToString();
                    if (TrackCOMM.Track_Indicator)
                    {
                        OperationContext context = OperationContext.Current;
                        MessageProperties properties = context.IncomingMessageProperties;
                        RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                        TrackEntity entity = new TrackEntity();
                        entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                    }
                    ret = cmd.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            { }
            return ret;
        }

        public int AddOUTP_MR_FILE_XML(DateTime VASIT_DATE, int VISIT_NO, int FILE_NO, string content)
        {
            int ret = 0;
            //连接字符串
            string oracleConnectionString = ConfigurationManager.ConnectionStrings["connectionString"].ConnectionString;
            //添加到临时表
            StringBuilder insertNewRecord = new StringBuilder(" INSERT INTO CPR.OUTP_MR_FILE_XML_TEMP(VASIT_DATE,VISIT_NO,FILE_NO,FILE_XML_TEMP) VALUES(");
            insertNewRecord.Append("to_date('");
            insertNewRecord.Append(VASIT_DATE.ToString("yyyy-MM-dd"));
            insertNewRecord.Append("','yyyy/mm/dd'),");
            insertNewRecord.Append(VISIT_NO.ToString());
            insertNewRecord.Append(",");
            insertNewRecord.Append(FILE_NO.ToString());
            insertNewRecord.Append(",':CLOB')");
            StringBuilder selectRecord = new StringBuilder("SELECT FILE_XML_TEMP FROM CPR.OUTP_MR_FILE_XML_TEMP WHERE VASIT_DATE=");
            selectRecord.Append("to_date('");
            selectRecord.Append(VASIT_DATE.ToString("yyyy-MM-dd"));
            selectRecord.Append("','yyyy/mm/dd')");
            selectRecord.Append(" AND VISIT_NO=");
            selectRecord.Append(VISIT_NO.ToString());
            selectRecord.Append(" AND FILE_NO=");
            selectRecord.Append(FILE_NO.ToString());
            selectRecord.Append(" for update ");
            System.Data.OracleClient.OracleConnection conn = null;
            System.Data.OracleClient.OracleTransaction otx;
            System.Data.OracleClient.OracleCommand cmd;
            System.Data.OracleClient.OracleCommand cmd1;
            System.Data.OracleClient.OracleCommand cmd2;
            try
            {
                conn = new System.Data.OracleClient.OracleConnection(oracleConnectionString);
                conn.Open();
                otx = conn.BeginTransaction();
                cmd = conn.CreateCommand();
                cmd1 = conn.CreateCommand();
                cmd2 = conn.CreateCommand();
                cmd.Transaction = otx;
                cmd1.Transaction = otx;
                cmd2.Transaction = otx;
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", insertNewRecord.ToString(), endpoint.Address);
                }
                this.InsertNewRecord(cmd, insertNewRecord.ToString());
                this.UpdateDataClobData(cmd1, selectRecord.ToString(), content);
                ret = this.Update_OMF_XmlDoc(cmd2, "CPR.OUTP_MR_FILE_XML_TEMP", "CPR.OUTP_MR_FILE_XML", VASIT_DATE, VISIT_NO, FILE_NO);
                otx.Commit();
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                if (conn != null)
                {
                    conn.Close();
                    conn.Dispose();
                }
            }
            return ret;
        }

        public int UpdateOUTPMR_FILE_XML(DateTime VASIT_DATE, int VISIT_NO, int FILE_NO, string content)
        {
            int ret = 0;
            //连接字符串
            string oracleConnectionString = ConfigurationManager.ConnectionStrings["connectionString"].ConnectionString;
            //添加到临时表
            StringBuilder insertNewRecord = new StringBuilder(" INSERT INTO CPR.OUTP_MR_FILE_XML_TEMP(VASIT_DATE,VISIT_NO,FILE_NO,FILE_XML_TEMP) VALUES(");
            insertNewRecord.Append("to_date('");
            insertNewRecord.Append(VASIT_DATE.ToString("yyyy-MM-dd"));
            insertNewRecord.Append("','yyyy/mm/dd'),");
            insertNewRecord.Append(VISIT_NO.ToString());
            insertNewRecord.Append(",");
            insertNewRecord.Append(FILE_NO.ToString());
            insertNewRecord.Append(",':CLOB')");
            StringBuilder selectRecord = new StringBuilder("SELECT FILE_XML_TEMP FROM CPR.OUTP_MR_FILE_XML_TEMP WHERE VASIT_DATE=");
            selectRecord.Append("to_date('");
            selectRecord.Append(VASIT_DATE.ToString("yyyy-MM-dd"));
            selectRecord.Append("','yyyy/mm/dd')");
            selectRecord.Append(" AND VISIT_NO=");
            selectRecord.Append(VISIT_NO.ToString());
            selectRecord.Append(" AND FILE_NO=");
            selectRecord.Append(FILE_NO.ToString());
            selectRecord.Append(" for update ");
            System.Data.OracleClient.OracleConnection conn = null;
            System.Data.OracleClient.OracleTransaction otx;
            System.Data.OracleClient.OracleCommand cmd;
            System.Data.OracleClient.OracleCommand cmd1;
            System.Data.OracleClient.OracleCommand cmd2;
            try
            {
                conn = new System.Data.OracleClient.OracleConnection(oracleConnectionString);
                conn.Open();
                otx = conn.BeginTransaction();
                cmd = conn.CreateCommand();
                cmd1 = conn.CreateCommand();
                cmd2 = conn.CreateCommand();
                cmd.Transaction = otx;
                cmd1.Transaction = otx;
                cmd2.Transaction = otx;
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", insertNewRecord.ToString(), endpoint.Address);
                }
                this.InsertNewRecord(cmd, insertNewRecord.ToString());
                this.UpdateDataClobData(cmd1, selectRecord.ToString(), content);
                ret = this.Update_OMF_XmlDoc(cmd2, "CPR.OUTP_MR_FILE_XML_TEMP", "CPR.OUTP_MR_FILE_XML", VASIT_DATE, VISIT_NO, FILE_NO);
                otx.Commit();
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                if (conn != null)
                {
                    conn.Close();
                    conn.Dispose();
                }
            }
            return ret;
        }

        private System.Data.OracleClient.OracleLob CreateTempLob(System.Data.OracleClient.OracleCommand cmd, System.Data.OracleClient.OracleType lobtype)
        {
            cmd.CommandText = "DECLARE A " + lobtype + "; " +
                           "BEGIN " +
                              "DBMS_LOB.CREATETEMPORARY(A, FALSE); " +
                              ":LOC := A; " +
                           "END;";
            System.Data.OracleClient.OracleParameter p = cmd.Parameters.Add("LOC", lobtype);
            p.Direction = ParameterDirection.Output;
            cmd.ExecuteNonQuery();
            return (System.Data.OracleClient.OracleLob)p.Value;
        }

        public int AddMR_FILE_XML(string PATIENT_ID, int VISIT_ID, int FILE_NO, string content)
        {
            int ret = 0;
            //连接字符串
            string oracleConnectionString = ConfigurationManager.ConnectionStrings["connectionString"].ConnectionString;
            //添加到临时表
            StringBuilder insertNewRecord = new StringBuilder("INSERT INTO cpr.MR_FILE_XML_TEMP(PATIENT_ID,VISIT_ID,FILE_NO,FILE_XML_TEMP) VALUES('");
            insertNewRecord.Append(PATIENT_ID);
            insertNewRecord.Append("',");
            insertNewRecord.Append(VISIT_ID.ToString());
            insertNewRecord.Append(",");
            insertNewRecord.Append(FILE_NO.ToString());
            insertNewRecord.Append(",':CLOB')");
            StringBuilder selectRecord = new StringBuilder("SELECT FILE_XML_TEMP FROM cpr.MR_FILE_XML_TEMP WHERE PATIENT_ID='");
            selectRecord.Append(PATIENT_ID);
            selectRecord.Append("' AND VISIT_ID=");
            selectRecord.Append(VISIT_ID.ToString());
            selectRecord.Append(" AND FILE_NO=");
            selectRecord.Append(FILE_NO.ToString());
            selectRecord.Append(" for update ");
            StringBuilder Del_Sql = new StringBuilder();
            Del_Sql.Append("delete from cpr.MR_FILE_XML");
            Del_Sql.Append(" where PATIENT_ID=:PATIENT_ID and VISIT_ID=:VISIT_ID and FILE_NO=:FILE_NO ");
            System.Data.OracleClient.OracleConnection conn = null;
            System.Data.OracleClient.OracleTransaction otx;
            System.Data.OracleClient.OracleCommand cmd;
            System.Data.OracleClient.OracleCommand cmd1;
            System.Data.OracleClient.OracleCommand cmd2;
            System.Data.OracleClient.OracleCommand cmd3;
            conn = new System.Data.OracleClient.OracleConnection(oracleConnectionString);
            conn.Open();
            otx = conn.BeginTransaction();
            try
            {
                cmd = conn.CreateCommand();
                cmd1 = conn.CreateCommand();
                cmd2 = conn.CreateCommand();
                cmd3 = conn.CreateCommand();
                cmd3.CommandText = Del_Sql.ToString();
                System.Data.OracleClient.OracleParameter pPATIENT_ID = new System.Data.OracleClient.OracleParameter("PATIENT_ID", PATIENT_ID);
                System.Data.OracleClient.OracleParameter pVISIT_ID = new System.Data.OracleClient.OracleParameter("VISIT_ID", VISIT_ID);
                System.Data.OracleClient.OracleParameter pFILE_NO = new System.Data.OracleClient.OracleParameter("FILE_NO", FILE_NO);
                cmd3.Parameters.Add(pPATIENT_ID);
                cmd3.Parameters.Add(pVISIT_ID);
                cmd3.Parameters.Add(pFILE_NO);
                cmd.Transaction = otx;
                cmd1.Transaction = otx;
                cmd2.Transaction = otx;
                cmd3.Transaction = otx;
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", insertNewRecord.ToString(), endpoint.Address);
                }
                this.InsertNewRecord(cmd, insertNewRecord.ToString());
                this.UpdateDataClobData(cmd1, selectRecord.ToString(), content);
                cmd3.ExecuteNonQuery();//删除
                //插入表
                this.Insert_MRF_XmlDoc(cmd2, "cpr.MR_FILE_XML_TEMP", "cpr.MR_FILE_XML", PATIENT_ID, VISIT_ID, FILE_NO);
                otx.Commit();
            }
            catch (Exception ex)
            {
                otx.Rollback();
                throw ex;
            }
            finally
            {
                if (conn != null)
                {
                    conn.Close();
                    conn.Dispose();
                }
            }
            return ret;
        }

        public int UpdateMR_FILE_XML(string PATIENT_ID, int VISIT_ID, int FILE_NO, string content)
        {
            int ret = 0;
            //连接字符串
            string oracleConnectionString = ConfigurationManager.ConnectionStrings["connectionString"].ConnectionString;
            //添加到临时表
            StringBuilder insertNewRecord = new StringBuilder(" INSERT INTO cpr.MR_FILE_XML_TEMP(PATIENT_ID,VISIT_ID,FILE_NO,FILE_XML_TEMP) VALUES('");
            insertNewRecord.Append(PATIENT_ID);
            insertNewRecord.Append("',");
            insertNewRecord.Append(VISIT_ID.ToString());
            insertNewRecord.Append(",");
            insertNewRecord.Append(FILE_NO.ToString());
            insertNewRecord.Append(",':CLOB')");
            StringBuilder selectRecord = new StringBuilder("SELECT FILE_XML_TEMP FROM cpr.MR_FILE_XML_TEMP WHERE PATIENT_ID='");
            selectRecord.Append(PATIENT_ID);
            selectRecord.Append("' AND VISIT_ID=");
            selectRecord.Append(VISIT_ID.ToString());
            selectRecord.Append(" AND FILE_NO=");
            selectRecord.Append(FILE_NO.ToString());
            selectRecord.Append(" for update ");
            System.Data.OracleClient.OracleConnection conn = null;
            System.Data.OracleClient.OracleTransaction otx;
            System.Data.OracleClient.OracleCommand cmd;
            System.Data.OracleClient.OracleCommand cmd1;
            System.Data.OracleClient.OracleCommand cmd2;
            try
            {
                conn = new System.Data.OracleClient.OracleConnection(oracleConnectionString);
                conn.Open();
                otx = conn.BeginTransaction();
                cmd = conn.CreateCommand();
                cmd1 = conn.CreateCommand();
                cmd2 = conn.CreateCommand();
                cmd.Transaction = otx;
                cmd1.Transaction = otx;
                cmd2.Transaction = otx;
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", insertNewRecord.ToString(), endpoint.Address);
                }
                this.InsertNewRecord(cmd, insertNewRecord.ToString());
                this.UpdateDataClobData(cmd1, selectRecord.ToString(), content);
                ret = this.Update_MRF_XmlDoc(cmd2, "cpr.MR_FILE_XML_TEMP", "cpr.MR_FILE_XML", PATIENT_ID, VISIT_ID, FILE_NO);
                otx.Commit();
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                if (conn != null)
                {
                    conn.Close();
                    conn.Dispose();
                }
            }
            return ret;
        }
        #endregion

        #region 原代码
        /*
        /// <summary>
        /// 增加门诊病历xml
        /// </summary>
        public int AddOUTP_MR_FILE_XML(DateTime VASIT_DATE, int VISIT_NO, int FILE_NO, string content)
        {
            #region
            string oracleConnectionString = ConfigurationManager.ConnectionStrings["connectionString"].ConnectionString;
            XmlDocument xDocument = new XmlDocument();
            xDocument.LoadXml(content);
            OracleConnection conn = new OracleConnection(oracleConnectionString);
            conn.Open();
            OracleXmlType cxml = new OracleXmlType(conn, xDocument);
            OracleCommand cmd = new OracleCommand();
            cmd.Connection = conn;
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into cpr.outp_mr_file_xml(VASIT_DATE,VISIT_NO,FILE_NO,FILE_XML)");
            strSql.Append(" values (:VASIT_DATE,:VISIT_NO,:FILE_NO,:FILE_XML)");
            cmd.CommandText = strSql.ToString();
            cmd.Parameters.Add("VASIT_DATE", OracleDbType.Date).Value = VASIT_DATE;
            cmd.Parameters.Add("VISIT_NO", OracleDbType.Int32).Value = VISIT_NO;
            cmd.Parameters.Add("FILE_NO", OracleDbType.Int32).Value = FILE_NO;
            cmd.Parameters.Add("FILE_XML", OracleDbType.XmlType).Value = cxml;
            int re = cmd.ExecuteNonQuery();
            if (conn.State == ConnectionState.Open)
                conn.Close();
            return re;
            #endregion
            #region 临时表
            //StringBuilder strSql = new StringBuilder();
            //strSql.Append("insert into cpr.outp_mr_file_xml_temp(");
            //strSql.Append("VASIT_DATE,VISIT_NO,FILE_NO,FILE_XML_TEMP)");
            //strSql.Append(" values (");
            //strSql.Append(":VASIT_DATE,:VISIT_NO,:FILE_NO,:FILE_XML_TEMP)");
            //Database db = DatabaseFactory.CreateDatabase();
            //DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
            //db.AddInParameter(dbCommand, "VASIT_DATE", DbType.Date, VASIT_DATE);
            //db.AddInParameter(dbCommand, "VISIT_NO", DbType.Int32, VISIT_NO);
            //db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, FILE_NO);
            //db.AddInParameter(dbCommand, "FILE_XML_TEMP", DbType.String, content);
            //db.ExecuteNonQuery(dbCommand);
            //strSql.Remove(0, strSql.Length);
            //strSql.Append(" insert into cpr.outp_mr_file_xml(VASIT_DATE,VISIT_NO,FILE_NO,FILE_XML) ");
            ////strSql.Append(" values (:VASIT_DATE,:VISIT_NO,:FILE_NO,sys.xmlType.createXML((select FILE_XML_TEMP from cpr.outp_mr_file_xml_temp where VASIT_DATE=:VASIT_DATE and VISIT_NO=:VISIT_NO and FILE_NO=:FILE_NO ))) ");
            //strSql.Append(" values (:VASIT_DATE,:VISIT_NO,:FILE_NO,XMLTYPE((select FILE_XML_TEMP from cpr.outp_mr_file_xml_temp where VASIT_DATE=:VASIT_DATE and VISIT_NO=:VISIT_NO and FILE_NO=:FILE_NO ))) ");
            ////strSql.Append(" values (");
            ////strSql.Append(":VASIT_DATE,:VISIT_NO,:FILE_NO,:FILE_XML_TEMP)");
            //dbCommand = db.GetSqlStringCommand(strSql.ToString());
            //db.AddInParameter(dbCommand, "VASIT_DATE", DbType.Date, VASIT_DATE);
            //db.AddInParameter(dbCommand, "VISIT_NO", DbType.Int32, VISIT_NO);
            //db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, FILE_NO);
            //db.ExecuteNonQuery(dbCommand);
            //strSql.Remove(0, strSql.Length);
            //strSql.Append(" delete from  cpr.outp_mr_file_xml_temp ");
            //strSql.Append(" where VASIT_DATE=:VASIT_DATE and VISIT_NO=:VISIT_NO and FILE_NO=:FILE_NO ");
            //dbCommand = db.GetSqlStringCommand(strSql.ToString());
            //db.AddInParameter(dbCommand, "VASIT_DATE", DbType.Date, VASIT_DATE);
            //db.AddInParameter(dbCommand, "VISIT_NO", DbType.Int32, VISIT_NO);
            //db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, FILE_NO);
            //return db.ExecuteNonQuery(dbCommand);
            #endregion
        }
        /// <summary>
        /// 添加住院病历xml
        /// </summary>
        /// <param name="PATIENT_ID"></param>
        /// <param name="VISIT_ID"></param>
        /// <param name="FILE_NO"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        public int AddMR_FILE_XML(string PATIENT_ID, int VISIT_ID, int FILE_NO, string content)
        {
            #region 
            string oracleConnectionString = ConfigurationManager.ConnectionStrings["connectionString"].ConnectionString;
            XmlDocument xDocument = new XmlDocument();
            xDocument.LoadXml(content);
            OracleConnection conn = new OracleConnection(oracleConnectionString);
            conn.Open();
            // OracleXmlType cxml = new OracleXmlType(conn, outstring);
            OracleXmlType cxml = new OracleXmlType(conn, xDocument);
            OracleCommand cmd = new OracleCommand();
            cmd.Connection = conn;
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" insert into cpr.mr_file_xml(PATIENT_ID,VISIT_ID,FILE_NO,FILE_XML) ");
            strSql.Append(" values (:PATIENT_ID,:VISIT_ID,:FILE_NO,:FILE_XML) ");
            cmd.CommandText = strSql.ToString();
            cmd.Parameters.Add("PATIENT_ID", OracleDbType.NVarchar2).Value = PATIENT_ID;
            cmd.Parameters.Add("VISIT_ID", OracleDbType.Int32).Value = VISIT_ID;
            cmd.Parameters.Add("FILE_NO", OracleDbType.Int32).Value = FILE_NO;
            cmd.Parameters.Add("FILE_XML", OracleDbType.XmlType).Value = cxml;
            int  re =cmd.ExecuteNonQuery();
            if (conn.State == ConnectionState.Open)
                conn.Close();
            return re;
            #endregion
            #region 临时表方法
            //XmlDocument d = new XmlDocument();
            //d.LoadXml(content);
            //XmlElement e = d.DocumentElement;
            //StringBuilder strSql = new StringBuilder();
            //strSql.Append("insert into cpr.mr_file_xml(");
            //strSql.Append("PATIENT_ID,VISIT_ID,FILE_NO,FILE_XML)");
            //strSql.Append(" values (");
            //strSql.Append(":PATIENT_ID,:VISIT_ID,:FILE_NO,:FILE_XML)");
            //Database db = DatabaseFactory.CreateDatabase();
            //DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
            //db.AddInParameter(dbCommand, "PATIENT_ID", DbType.String, PATIENT_ID);
            //db.AddInParameter(dbCommand, "VISIT_ID", DbType.Int32, VISIT_ID);
            //db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, FILE_NO);
            //db.AddInParameter(dbCommand, "FILE_XML", DbType.Xml,e);
            //return db.ExecuteNonQuery(dbCommand);
            //StringBuilder strSql = new StringBuilder();
            //strSql.Append("insert into cpr.mr_file_xml_temp(");
            //strSql.Append("PATIENT_ID,VISIT_ID,FILE_NO,FILE_XML_TEMP)");
            //strSql.Append(" values (");
            //strSql.Append(":PATIENT_ID,:VISIT_ID,:FILE_NO,:FILE_XML_TEMP)");
            //Database db = DatabaseFactory.CreateDatabase();
            //DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
            //db.AddInParameter(dbCommand, "PATIENT_ID", DbType.String, PATIENT_ID);
            //db.AddInParameter(dbCommand, "VISIT_ID", DbType.Int32, VISIT_ID);
            //db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, FILE_NO);
            //db.AddInParameter(dbCommand, "FILE_XML_TEMP", DbType.String, "fdsa");
            //return db.ExecuteNonQuery(dbCommand);
            //strSql.Remove(0, strSql.Length);
            //strSql.Append(" insert into cpr.mr_file_xml(PATIENT_ID,VISIT_ID,FILE_NO,FILE_XML) ");
            //strSql.Append(" values (:PATIENT_ID,:VISIT_ID,:FILE_NO,sys.xmlType.createXML(:FILE_XML)) ");
            //DbCommand dbCommand1 = db.GetSqlStringCommand(strSql.ToString());
            //db.AddInParameter(dbCommand1, "PATIENT_ID", DbType.String, PATIENT_ID);
            //db.AddInParameter(dbCommand1, "VISIT_ID", DbType.Int32, VISIT_ID);
            //db.AddInParameter(dbCommand1, "FILE_NO", DbType.Int32, FILE_NO);
            //db.AddInParameter(dbCommand1, "FILE_XML", DbType.String, "<CDR>  <text>第几次入院记录</text>  <section name=\"SCHEMA_H.02\" code=\"\" code-system=\"\">  <text>姓名：</text>   <text />   <fieldelem name=\"HR02.01.002\" code=\"\" code-system=\"\">王33</fieldelem>   <text />   </section></CDR>");
            //return db.ExecuteNonQuery(dbCommand1);
            //strSql.Remove(0, strSql.Length);
            //strSql.Append(" insert into cpr.mr_file_xml(PATIENT_ID,VISIT_ID,FILE_NO,FILE_XML) ");
            //strSql.Append(" values (:PATIENT_ID,:VISIT_ID,:FILE_NO,sys.xmlType.createXML((select FILE_XML_TEMP from cpr.mr_file_xml_temp where PATIENT_ID=:PATIENT_ID and VISIT_ID=:VISIT_ID and FILE_NO=:FILE_NO ))) ");
            //DbCommand dbCommand1 = db.GetSqlStringCommand(strSql.ToString());
            //db.AddInParameter(dbCommand1, "PATIENT_ID", DbType.String, PATIENT_ID);
            //db.AddInParameter(dbCommand1, "VISIT_ID", DbType.Int32, VISIT_ID);
            //db.AddInParameter(dbCommand1, "FILE_NO", DbType.Int32, FILE_NO);
            // db.ExecuteNonQuery(dbCommand1);
            //strSql.Remove(0, strSql.Length);
            //strSql.Append(" delete from  cpr.mr_file_xml_temp ");
            //strSql.Append(" where PATIENT_ID=:PATIENT_ID and VISIT_ID=:VISIT_ID and FILE_NO=:FILE_NO ");
            //DbCommand dbCommand2 = db.GetSqlStringCommand(strSql.ToString());
            //db.AddInParameter(dbCommand2, "PATIENT_ID", DbType.String, PATIENT_ID);
            //db.AddInParameter(dbCommand2, "VISIT_ID", DbType.Int32, VISIT_ID);
            //db.AddInParameter(dbCommand2, "FILE_NO", DbType.Int32, FILE_NO);
            //return db.ExecuteNonQuery(dbCommand2);
            #endregion
        }

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="PATIENT_ID"></param>
        /// <param name="VISIT_ID"></param>
        /// <param name="FILE_NO"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        public int UpdateMR_FILE_XML(string PATIENT_ID, int VISIT_ID, int FILE_NO, string content)
        {
            #region
            string oracleConnectionString = ConfigurationManager.ConnectionStrings["connectionString"].ConnectionString;
            XmlDocument xDocument = new XmlDocument();
            content = "<cdr>11</cdr>";
            xDocument.LoadXml(content);
            OracleConnection conn = new OracleConnection(oracleConnectionString);
            conn.Open();
            // OracleXmlType cxml = new OracleXmlType(conn, outstring);
            OracleXmlType cxml = new OracleXmlType(conn, xDocument);
            OracleCommand cmd = new OracleCommand();
            cmd.Connection = conn;
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" update cpr.mr_file_xml ");
            strSql.Append(" set FILE_XML=:XML ");
            //strSql.Append(" set VISIT_ID=:VISIT_ID ");
            //strSql.Append(" set FILE_XML=sys.xmlType.createXML(:FILE_XML) ");
            strSql.Append(" where PATIENT_ID=':PATIENT_ID' and VISIT_ID=:VISIT_ID and FILE_NO=:FILE_NO ");//and FILE_NO=:FILE_NO
            cmd.CommandText = strSql.ToString();
            cmd.Parameters.Add("PATIENT_ID", OracleDbType.NVarchar2).Value = PATIENT_ID;
            cmd.Parameters.Add("VISIT_ID", OracleDbType.Int32).Value = VISIT_ID;
            cmd.Parameters.Add("FILE_NO", OracleDbType.Int32).Value = FILE_NO;
            cmd.Parameters.Add("XML", OracleDbType.XmlType).Value = cxml;
            int re = cmd.ExecuteNonQuery();
            if (conn.State == ConnectionState.Open)
                conn.Close();
            return re;
            #endregion
            #region MyRegion
            //StringBuilder strSql = new StringBuilder();
            //strSql.Remove(0, strSql.Length);
            //strSql.Append(" delete from  cpr.mr_file_xml_temp ");
            //strSql.Append(" where PATIENT_ID=:PATIENT_ID and VISIT_ID=:VISIT_ID and FILE_NO=:FILE_NO ");
            //Database db = DatabaseFactory.CreateDatabase();
            //DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
            //db.AddInParameter(dbCommand, "PATIENT_ID", DbType.String, PATIENT_ID);
            //db.AddInParameter(dbCommand, "VISIT_ID", DbType.Int32, VISIT_ID);
            //db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, FILE_NO);
            //db.ExecuteNonQuery(dbCommand);
            //strSql.Remove(0, strSql.Length);
            //strSql.Append("insert into cpr.mr_file_xml_temp(");
            //strSql.Append("PATIENT_ID,VISIT_ID,FILE_NO,FILE_XML_TEMP)");
            //strSql.Append(" values (");
            //strSql.Append(":PATIENT_ID,:VISIT_ID,:FILE_NO,:FILE_XML_TEMP)");
            //dbCommand = db.GetSqlStringCommand(strSql.ToString());
            //db.AddInParameter(dbCommand, "PATIENT_ID", DbType.String, PATIENT_ID);
            //db.AddInParameter(dbCommand, "VISIT_ID", DbType.Int32, VISIT_ID);
            //db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, FILE_NO);
            //db.AddInParameter(dbCommand, "FILE_XML_TEMP", DbType.String, content);
            //db.ExecuteNonQuery(dbCommand);
            //strSql.Remove(0, strSql.Length);
            //strSql.Append(" update cpr.mr_file_xml ");
            //strSql.Append(" set FILE_XML= sys.xmlType.createXML((select FILE_XML_TEMP from cpr.MR_FILE_XML_TEMP where PATIENT_ID=:PATIENT_ID and VISIT_ID=:VISIT_ID and FILE_NO=:FILE_NO )) ");
            //strSql.Append(" where PATIENT_ID=:PATIENT_ID and VISIT_ID=:VISIT_ID and FILE_NO=:FILE_NO ");
            //dbCommand = db.GetSqlStringCommand(strSql.ToString());
            //db.AddInParameter(dbCommand, "PATIENT_ID", DbType.String, PATIENT_ID);
            //db.AddInParameter(dbCommand, "VISIT_ID", DbType.Int32, VISIT_ID);
            //db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, FILE_NO);
            //return db.ExecuteNonQuery(dbCommand);
            #endregion
        }

        public int UpdateOUTPMR_FILE_XML(DateTime VASIT_DATE, int VISIT_NO, int FILE_NO, string content)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Remove(0, strSql.Length);
            strSql.Append(" delete from cpr.outp_mr_file_xml_temp ");
            strSql.Append(" where VASIT_DATE=:VASIT_DATE and VISIT_NO=:VISIT_NO and FILE_NO=:FILE_NO ");
            Database db = DatabaseFactory.CreateDatabase();
            DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
            db.AddInParameter(dbCommand, "VASIT_DATE", DbType.Date, VASIT_DATE);
            db.AddInParameter(dbCommand, "VISIT_NO", DbType.Int32, VISIT_NO);
            db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, FILE_NO);
            db.ExecuteNonQuery(dbCommand);
            strSql.Remove(0, strSql.Length);
            strSql.Append("insert into cpr.outp_mr_file_xml_temp(");
            strSql.Append("VASIT_DATE,VISIT_NO,FILE_NO,FILE_XML_TEMP)");
            strSql.Append(" values (");
            strSql.Append(":VASIT_DATE,:VISIT_NO,:FILE_NO,:FILE_XML_TEMP)");
            dbCommand = db.GetSqlStringCommand(strSql.ToString());
            db.AddInParameter(dbCommand, "VASIT_DATE", DbType.Date, VASIT_DATE);
            db.AddInParameter(dbCommand, "VISIT_NO", DbType.Int32, VISIT_NO);
            db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, FILE_NO);
            db.AddInParameter(dbCommand, "FILE_XML_TEMP", DbType.String, content);
            db.ExecuteNonQuery(dbCommand);
            strSql.Remove(0, strSql.Length);
            strSql.Append(" update cpr.outp_mr_file_xml ");
            strSql.Append(" set FILE_XML= sys.xmlType.createXML((select FILE_XML_TEMP from cpr.MR_FILE_XML_TEMP where VASIT_DATE=:VASIT_DATE and VISIT_NO=:VISIT_NO and FILE_NO=:FILE_NO )) ");
            strSql.Append(" where VASIT_DATE=:VASIT_DATE and VISIT_NO=:VISIT_NO and FILE_NO=:FILE_NO ");
            dbCommand = db.GetSqlStringCommand(strSql.ToString());
            db.AddInParameter(dbCommand, "VASIT_DATE", DbType.Date, VASIT_DATE);
            db.AddInParameter(dbCommand, "VISIT_NO", DbType.Int32, VISIT_NO);
            db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, FILE_NO);
            return db.ExecuteNonQuery(dbCommand);
        }
        */
        #endregion

        public int DeleteOUTPMR_FILE(DateTime VASIT_DATE, int VISIT_NO, int FILE_NO)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from medrec.outp_mr_file_index ");
            strSql.Append(" where VISIT_DATE=:VASIT_DATE and VISIT_NO=:VISIT_NO and FILE_NO=:FILE_NO ");
            Database db = DatabaseFactory.CreateDatabase();
            DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
            db.AddInParameter(dbCommand, "VASIT_DATE", DbType.Date, VASIT_DATE);
            db.AddInParameter(dbCommand, "VISIT_NO", DbType.Int32, VISIT_NO);
            db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, FILE_NO);
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            int re = db.ExecuteNonQuery(dbCommand);
            if (re > 0)
            {
                strSql.Remove(0, strSql.Length);
                strSql.Append(" delete from  cpr.outp_mr_file_xml ");
                strSql.Append(" where VASIT_DATE=:VASIT_DATE and VISIT_NO=:VISIT_NO and FILE_NO=:FILE_NO ");
                dbCommand = db.GetSqlStringCommand(strSql.ToString());
                db.AddInParameter(dbCommand, "VASIT_DATE", DbType.Date, VASIT_DATE);
                db.AddInParameter(dbCommand, "VISIT_NO", DbType.Int32, VISIT_NO);
                db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, FILE_NO);
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                re = db.ExecuteNonQuery(dbCommand);
            }
            return re;
        }
        #endregion

        #region 模板唯一性
        public int GetFileCount(string PATIENT_ID, int VISIT_ID, string dscode)
        {
            Database db = DatabaseFactory.CreateDatabase();
            //住院志 --A
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" select count(1) as num from mr_file_index where MR_code in (select MR_CODE from mr_item_index where  ");
            strSql.AppendFormat(" mr_class='N' and data_model_class='DS' and data_model_code='{0}' )  ", dscode);
            strSql.AppendFormat(" and Patient_id='{0}' and visit_id={1} ", PATIENT_ID, VISIT_ID);
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                object obj = db.ExecuteScalar(CommandType.Text, strSql.ToString());
                int re = 0;
                if (obj != null)
                    re = Convert.ToInt32(obj);
                return re;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        public int GetSubmit(string UserName, string PATIENT_ID, int VISIT_ID)
        {
            bool re = true;
            int val = 0;
            DataSet ds = new DataSet();
            Database db = DatabaseFactory.CreateDatabase();
            DbCommand cmd;
            object obj;
            StringBuilder strSql = new StringBuilder();
            //版本
            strSql.Append(" select 1 from MR_INDEX where MR_VERSION  ='3.7' and  ");
            strSql.Append(" patient_id=:PATIENT_ID   ");
            cmd = db.GetSqlStringCommand(strSql.ToString());
            db.AddInParameter(cmd, "PATIENT_ID", DbType.String, PATIENT_ID);
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
            }
            obj = db.ExecuteScalar(cmd);
            strSql.Remove(0, strSql.Length);
            if (obj == null)
            {
                re = false;
                val = 1;
                return val;
            }
            //是否在院//只有出院的病人才能提交
            if (re)
            {
                //版本
                strSql.Append(" select 1 from pats_in_hospital where    ");
                strSql.Append(" patient_id=:PATIENT_ID   ");
                cmd = db.GetSqlStringCommand(strSql.ToString());
                db.AddInParameter(cmd, "PATIENT_ID", DbType.String, PATIENT_ID);
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                obj = db.ExecuteScalar(cmd);
                strSql.Remove(0, strSql.Length);
                if (obj != null)
                {
                    re = false;
                    val = 2;
                    return val;
                }
            }
            //判断医生是否病人住院医生
            if (re)
            {
                strSql.Append(" select 1 from v_mr_online where  ");
                strSql.Append(" REQUEST_DOCTOR_ID=:UserName ");
                strSql.Append(" and patient_id=:PATIENT_ID   ");
                cmd = db.GetSqlStringCommand(strSql.ToString());
                db.AddInParameter(cmd, "UserName", DbType.String, UserName);
                db.AddInParameter(cmd, "PATIENT_ID", DbType.String, PATIENT_ID);
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                obj = db.ExecuteScalar(cmd);
                strSql.Remove(0, strSql.Length);
                if (obj == null)
                {
                    re = false;
                    val = 3;
                    return val;
                }
            }
            //检查是否存在未签名病历
            if (re)
            {
                strSql.Append(" select 1 from mr_file_index  ");
                strSql.Append(" where patient_id=:PATIENT_ID ");
                strSql.Append(" and (FILE_FLAG ='T'   ");
                strSql.Append(" or (FILE_FLAG='1' and LAST_MODIFY_DATE_TIME>CREATE_DATE_TIME)   ");
                strSql.Append(" or (FILE_FLAG='2' and LAST_MODIFY_DATE_TIME>MODIFY_DATE_TIME) )  ");
                cmd = db.GetSqlStringCommand(strSql.ToString());
                db.AddInParameter(cmd, "PATIENT_ID", DbType.String, PATIENT_ID);
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                obj = db.ExecuteScalar(cmd);
                strSql.Remove(0, strSql.Length);
                if (obj != null)
                {
                    re = false;
                    val = 4;
                    return val;
                }
            }
            return val;
        }

        public bool SetSubmit(string UserName, string PATIENT_ID, int VISIT_ID)
        {
            bool re = true;
            StringBuilder strSql = new StringBuilder();
            StringBuilder strSql1 = new StringBuilder();
            StringBuilder strSql2 = new StringBuilder();
            ArrayList SqlList = new ArrayList();
            strSql1.Append(" delete from MR_ON_LINE  ");
            strSql1.Append(" where PATIENT_ID='" + PATIENT_ID + "'   ");
            strSql1.Append(" and VISIT_ID=" + VISIT_ID.ToString() + "   ");
            strSql1.Append(" and REQUEST_DOCTOR_ID='" + UserName + "'  ");
            strSql2.Append(" update medrec.MR_INDEX set MR_STATUS='C',  ");
            strSql2.Append(" LAST_ACCESS_DATE_TIME=(select sysdate from dual), ");
            strSql2.Append(" LAST_ACCESS_USER ='" + UserName + "'   ");
            strSql2.Append(" where PATIENT_ID='" + PATIENT_ID + "'   ");
            strSql2.Append(" and VISIT_ID=" + VISIT_ID.ToString() + "   ");
            string sql3 = "update cpr.mr_recall_master set status=1,recovery_date=sysdate,recovery_user='" + UserName + "' where patient_id='" + PATIENT_ID + "' and visit_id= " + VISIT_ID;
            SqlList.Add(strSql1);
            SqlList.Add(strSql2);
            SqlList.Add(sql3);
            Database db = DatabaseFactory.CreateDatabase();
            using (DbConnection dbconn = db.CreateConnection())
            {
                dbconn.Open();
                DbTransaction dbtran = dbconn.BeginTransaction();
                try
                {
                    //执行语句
                    for (int n = 0; n < SqlList.Count; n++)
                    {
                        string strsql = SqlList[n].ToString();
                        if (strsql.Trim().Length > 1)
                        {
                            DbCommand dbCommand = db.GetSqlStringCommand(strsql);
                            if (TrackCOMM.Track_Indicator)
                            {
                                OperationContext context = OperationContext.Current;
                                MessageProperties properties = context.IncomingMessageProperties;
                                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                                TrackEntity entity = new TrackEntity();
                                entity.Add("MR_FILE_INDEXDAL", "", strsql.Trim().ToString(), endpoint.Address);
                            }
                            db.ExecuteNonQuery(dbCommand);
                        }
                    }
                    dbtran.Commit();
                    re = true;
                }
                catch
                {
                    dbtran.Rollback();
                    re = false;
                }
                finally
                {
                    dbconn.Close();
                }
            }
            return re;
        }

        /// <summary>
        /// 病历召回
        /// </summary>
        /// <param name="UserName"></param>
        /// <param name="PATIENT_ID"></param>
        /// <param name="VISIT_ID"></param>
        /// <returns></returns>
        public bool SetRecall(string PATIENT_ID, int VISIT_ID, string REQUEST, string PARENT, string SUPPER)
        {
            string sql = "select * from v_mr_online where patient_id='" + PATIENT_ID + "' and visit_id=" + VISIT_ID + "";
            Database db = DatabaseFactory.CreateDatabase();
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
            }
            DataSet ds = db.ExecuteDataSet(CommandType.Text, sql);
            if (ds.Tables[0] != null && ds.Tables[0].Rows.Count > 0)
                return false;
            //增加判断，mr_on_line有数据
            sql = "select * from mr_on_line where patient_id='" + PATIENT_ID + "' and visit_id=" + VISIT_ID + " and REQUEST_DOCTOR_ID='" + REQUEST + "'";
            db = DatabaseFactory.CreateDatabase();
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
            }
            ds = db.ExecuteDataSet(CommandType.Text, sql);
            if (ds.Tables[0] != null && ds.Tables[0].Rows.Count > 0)
            {
                string strSql1 = string.Empty;
                string strSql2 = string.Empty;
                ArrayList SqlList = new ArrayList();
                strSql1 = "update MR_ON_LINE set STATUS='0',SUPER_DOCTOR_ID = '"
                            + SUPPER + "',PARENT_DOCTOR_ID = '"
                            + PARENT + "' where PATIENT_ID = '" + PATIENT_ID + "' and VISIT_ID = " + VISIT_ID.ToString() + " and REQUEST_DOCTOR_ID = '" + REQUEST + "'";
                strSql2 = " update medrec.MR_INDEX set MR_STATUS='O' "
                            + " where PATIENT_ID='" + PATIENT_ID + "' "
                            + " and VISIT_ID=" + VISIT_ID.ToString() + " ";
                SqlList.Add(strSql1);
                SqlList.Add(strSql2);
                return dbinfo.SaveData(SqlList, null);
            }
            else
            {
                string strSql1 = string.Empty;
                string strSql2 = string.Empty;
                ArrayList SqlList = new ArrayList();
                strSql1 = " insert into MR_ON_LINE(PATIENT_ID,VISIT_ID,STATUS,REQUEST_DOCTOR_ID,REQUEST_DATE_TIME,SUPER_DOCTOR_ID,PARENT_DOCTOR_ID,OTHER_DOCTOR_ID) "
                            + " values('" + PATIENT_ID + "',"
                            + " " + VISIT_ID.ToString() + ",'0',"
                            + " '" + REQUEST + "',sysdate, "
                            + " '" + SUPPER + "',"
                            + " '" + PARENT + "','')";
                strSql2 = " update medrec.MR_INDEX set MR_STATUS='O' "
                            + " where PATIENT_ID='" + PATIENT_ID + "' "
                            + " and VISIT_ID=" + VISIT_ID.ToString() + " ";
                SqlList.Add(strSql1);
                SqlList.Add(strSql2);
                return dbinfo.SaveData(SqlList, null);
            }
        }
        #endregion

        #region 矛盾词
        public DataTable GetCONTRASTWORD()
        {
            Database db = DatabaseFactory.CreateDatabase();
            string str = "select * from cpr.mr_contradict_word ";
            try
            {
                DataTable dt = new DataTable("word");
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", str.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(CommandType.Text, str);
                if (ds != null)
                    dt = ds.Tables[0];
                return dt;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + str.ToString());
            }
        }
        #endregion

        #region 主任医师未签名病历
        public DataSet GetMR_NOSIGN(string PATIENT_ID, int VISIT_ID)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT A.*,B.NAME FROM MR_FILE_INDEX A,PAT_MASTER_INDEX B");
            strSql.Append(" WHERE A.PATIENT_ID = B.PATIENT_ID ");
            //strSql.Append(" AND A.FILE_FLAG <> '3'");
            strSql.Append(" AND (A.FILE_FLAG ='T'   ");
            strSql.Append(" OR (FILE_FLAG='1' and LAST_MODIFY_DATE_TIME>CREATE_DATE_TIME)   ");
            strSql.Append(" OR (FILE_FLAG='2' and LAST_MODIFY_DATE_TIME>MODIFY_DATE_TIME) )  ");
            strSql.Append(" AND A.PATIENT_ID = '" + PATIENT_ID + "'");
            if (VISIT_ID != -1)
                strSql.Append(" AND A.VISIT_ID=" + VISIT_ID + " ");
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                return ds;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion

        #region 续打更新
        /// <summary>
        /// 病程续打更新
        /// </summary>
        /// <param name="PATIENT_ID"></param>
        /// <param name="VISIT_ID"></param>
        /// <returns></returns>
        public bool PrintSubmit(string PATIENT_ID, int VISIT_ID)
        {
            DataSet ds = new DataSet();
            Database db = DatabaseFactory.CreateDatabase();
            DbCommand cmd;
            object obj;
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" update MR_FILE_INDEX set PRINT_FLAG='1' ");
            strSql.Append(" where PATIENT_ID='" + PATIENT_ID + "'   ");
            strSql.Append(" and VISIT_ID=" + VISIT_ID.ToString() + "   ");
            strSql.Append(" and FILE_ATTR='B'   ");
            try
            {
                cmd = db.GetSqlStringCommand(strSql.ToString());
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                obj = db.ExecuteNonQuery(cmd);
                strSql.Remove(0, strSql.Length);
                if (int.Parse(obj.ToString()) <= 0)
                {
                    return false;
                }
                return true;
            }
            catch (Exception ex)
            {
               throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        #endregion

        #region 病历数据处理
        public DataSet GetMR_SUMMARY_EXPORT_DEFINEByFileNo(string pid, int vid, int fileNo)
        {
            string sql = string.Format(@"SELECT * from CPR.MR_SUMMARY_EXPORT_DEFINE where ds_code=
                                            (SELECT distinct b.data_model_code from Mr_File_Index a 
                                           left join mr_item_index b on a.mr_code = b.mr_code 
                                            where a.PATIENT_ID='{0}'  and a.VISIT_ID={1} and a.FILE_NO={2})", pid, vid, fileNo);
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, sql);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        //病历抽取项信息
        public DataSet GetMR_SUMMARY_EXPORT_DEFINE(string DS_CODE)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT * from CPR.MR_SUMMARY_EXPORT_DEFINE");
            if (DS_CODE != "")
            {
                strSql.Append(" where DS_CODE='" + DS_CODE + "'   ");
            }
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                return ds;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获取抽取数据表名
        /// </summary>
        /// <returns>表名数据集</returns>
        public DataSet GetMR_SUMMARY_EXPORT_DEFINE_Tables()
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT TARGET_TABLE from CPR.MR_SUMMARY_EXPORT_DEFINE GROUP BY TARGET_TABLE");
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                return ds;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获取抽取数据表的字段信息项
        /// </summary>
        /// <param name="TABNAME">表名</param>
        /// <returns>字段信息项数据集</returns>
        public DataSet GetMR_SUMMARY_EXPORT_DEFINE_FIELD(string TABNAME)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT * from CPR.MR_SUMMARY_EXPORT_DEFINE");
            if (TABNAME != "")
            {
                strSql.Append(" where TARGET_TABLE='" + TABNAME + "'   ");
            }
            try
            {
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                return ds;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 设置数据集抽取项
        /// </summary>
        /// <param name="TYPE">操作类型</param>
        /// <param name="UpStr">操作数据</param>
        /// <returns>操作成功行数</returns>
        public int SetMR_SUMMARY_EXPORT_DEFINE_FIELD(string TYPE, string UpStr)
        {
            Database db = DatabaseFactory.CreateDatabase();
            string SQL = "";
            if (TYPE.ToLower().Trim() == "insert")
            {
                SQL = "insert into CPR.MR_SUMMARY_EXPORT_DEFINE";
                SQL += " (DS_CODE ,NODE_PATH,TARGET_TABLE,TARGET_COLUMN,DATA_TYPE ,DATA_FORMAT,DATA_TIP)";
                SQL += "values(" + UpStr + ")";
            }
            else if (TYPE.ToLower().Trim() == "delete")
            {
                SQL = "delete from CPR.MR_SUMMARY_EXPORT_DEFINE ";
                SQL += " where ";
                SQL += "TARGET_TABLE=" + UpStr.Split(',')[2].ToString();
                SQL += " and TARGET_COLUMN=" + UpStr.Split(',')[3].ToString();
            }
            else if (TYPE.ToLower() == "update")
            {
                SQL = "update CPR.MR_SUMMARY_EXPORT_DEFINE set ";
                SQL += "DS_CODE =" + UpStr.Split(',')[0].ToString();
                SQL += " ， NODE_PATH=" + UpStr.Split(',')[1].ToString();
                SQL += " ， DATA_TYPE=" + UpStr.Split(',')[4].ToString();
                SQL += " ， DATA_FORMAT=" + UpStr.Split(',')[5].ToString();
                SQL += " ， DATA_TIP=" + UpStr.Split(',')[6].ToString();
                SQL += " where ";
                SQL += "TARGET_TABLE=" + UpStr.Split(',')[2].ToString();
                SQL += " and TARGET_COLUMN=" + UpStr.Split(',')[3].ToString();
            }
            else
            {
                return -1;
            }
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", SQL, endpoint.Address);
                }
                return db.ExecuteNonQuery(CommandType.Text, SQL);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + SQL.ToString());
            }
        }

        /// <summary>
        /// 设置数据集抽取记录表
        /// </summary>
        /// <param name="TABLENAME">数据表名</param>
        /// <param name="UpDT">字段数据集</param>
        /// <returns>是否成功</returns>
        public bool Add_MR_SUMMARY_EXPORT_DEFINE_TABLE(string TABLENAME, string TABLEREADER, DataTable UpDT)
        {
            DbInfo di = new DbInfo();
            //UpDT必须有的字段：TNAME,TTYPE,TLENGTH,TREADER
            string sql = "create table CPR." + TABLENAME + "(";
            sql += "PATIENT_ID VARCHAR2(20) not null,";
            sql += "VISIT_ID   NUMBER(2) not null,";
            sql += "FILE_NO    NUMBER(4) not null,";
            for (int i = 0; i < UpDT.Rows.Count; i++)
            {
                sql += UpDT.Rows[i]["TNAME"].ToString() + " " + UpDT.Rows[i]["TTYPE"].ToString();
                if (UpDT.Rows[i]["TTYPE"].ToString() != "DATE")
                {
                    sql += "(" + UpDT.Rows[i]["TLENGTH"].ToString() + ")";
                }
                sql += ",";
            }
            sql += ")";
            sql += "tablespace TSP_CPR pctfree 10 initrans 1 maxtrans 255 storage";
            sql += "(initial 64 next 1 minextents 1 maxextents unlimited);";
            sql += "comment on table CPR." + TABLENAME + " is '" + TABLEREADER + "';";
            for (int i = 0; i < UpDT.Rows.Count; i++)
            {
                sql += "comment on column CPR." + TABLENAME + "." + UpDT.Rows[i]["TNAME"].ToString() + " is '" + UpDT.Rows[i]["TREADER"].ToString() + "';";
            }
            sql += "alter table CPR." + TABLENAME + " add constraint PK_" + TABLENAME + " primary key (PATIENT_ID, VISIT_ID, FILE_NO)";
            sql += "create or replace public synonym  " + TABLENAME + "   for CPR." + TABLENAME + "  ;";
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
                }
                return di.ExecuteSql(sql);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        /// <summary>
        /// 提取门诊病历内容
        /// </summary>
        /// <param name="InsertSql"></param>
        /// <param name="updateSql"></param>
        /// <param name="tableName"></param>
        /// <param name="visitDate"></param>
        /// <param name="visitNo"></param>
        /// <param name="fileNo"></param>
        /// <returns></returns>
        public int ExecuteClinicSummaryExport(string[] columnList, string[] valueList, string tableName, string patientid, string visitDate, string visitNo, string fileNo)
        {
            Database db = DatabaseFactory.CreateDatabase();
            string sql = "";
            string strWhere = " where visit_date=to_date('" + visitDate + "','yyyy-MM-dd') and visit_no='" + visitNo + "' and ORDINAL=" + fileNo;//; + //" and doctor_no="+valueList[8].ToString()+"";
            sql = "select visit_date from " + tableName + strWhere;
            object obj = db.ExecuteScalar(CommandType.Text, sql);
            if (obj == null || obj.ToString() == "")
            {
                string insertSql = "insert into " + tableName + " (";
                for (int i = 0; i < columnList.Length; i++)
                {
                    if (i != 0)
                        insertSql += ",";
                    insertSql += columnList[i].ToString();
                }
                if (columnList.Length > 0)
                    insertSql += ",";
                insertSql += "patient_id,visit_date,visit_no,ORDINAL) values (";
                for (int i = 0; i < valueList.Length; i++)
                {
                    if (i != 0)
                        insertSql += ",";
                    insertSql += valueList[i].ToString();
                }
                if (valueList.Length > 0)
                    insertSql += ",";
                insertSql += "'" + patientid + "',to_date('" + visitDate + "','yyyy-MM-dd'),'" + visitNo + "','" + fileNo + "')";
                sql = insertSql;
            }
            else
            {
                string updateSql = "update " + tableName + " set ";
                for (int i = 0; i < columnList.Length; i++)
                {
                    if (i != 0)
                        updateSql += ",";
                    if (!string.IsNullOrEmpty(valueList[i].ToString()))
                        updateSql += columnList[i].ToString() + "=" + valueList[i].ToString();
                }
                updateSql += strWhere;
                sql = updateSql;
            }
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
                }
                return db.ExecuteNonQuery(CommandType.Text, sql);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        /// <summary>
        /// 获取门诊病历使用数据集
        /// </summary>
        /// <param name="mrcode"></param>
        /// <returns></returns>
        public string GetClinicDsByCode(DateTime VISIT_DATE, int VISIT_NO, int FILE_NO)
        {
            string strSql = " select a.data_model_code from MR_ITEM_INDEX a,OUTP_MR_FILE_INDEX b where a.mr_code=b.mr_code and  b.VISIT_DATE=to_date('" + VISIT_DATE.ToString() + "','YYYY-MM-DD HH24:MI:SS') and b.VISIT_NO=" + VISIT_NO + " and b.FILE_NO=" + FILE_NO + " ";
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                object obj = db.ExecuteScalar(CommandType.Text, strSql);
                if (obj != null)
                    return obj.ToString();
                else
                    return null;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获取门诊病历使用数据集
        /// </summary>
        /// <param name="mrcode"></param>
        /// <returns></returns>
        public DataSet GetClinicDataByCode(DateTime VISIT_DATE, int VISIT_NO, int FILE_NO)
        {
            string strSql = " select a.data_model_code,a.mr_code from MR_ITEM_INDEX a,OUTP_MR_FILE_INDEX b where a.mr_code=b.mr_code and  b.VISIT_DATE=to_date('" + VISIT_DATE.ToString() + "','YYYY-MM-DD HH24:MI:SS') and b.VISIT_NO=" + VISIT_NO + " and b.FILE_NO=" + FILE_NO + " ";
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, strSql);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 提取住院病历内容
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="tableName"></param>
        /// <param name="patientID"></param>
        /// <param name="visitNO"></param>
        /// <param name="fileNo"></param>
        /// <returns></returns>
        public int ExecuteInSummaryExport(string[] columnList, string[] valueList, string tableName, string patientID, string visitNO, string fileNo)
        {
            Database db = DatabaseFactory.CreateDatabase();
            string sql = "";
            string strWhere = " where PATIENT_ID='" + patientID + "' and visit_id='" + visitNO + "' and file_no='" + fileNo + "'";
            sql = "select PATIENT_ID from " + tableName + strWhere;
            object obj = db.ExecuteScalar(CommandType.Text, sql);
            if (obj == null || obj.ToString() == "")
            {
                string insertSql = "insert into " + tableName + " (";
                for (int i = 0; i < columnList.Length; i++)
                {
                    if (i != 0)
                        insertSql += ",";
                    insertSql += columnList[i].ToString();
                }
                insertSql += "PATIENT_ID,visit_id,file_no) values (";
                for (int i = 0; i < valueList.Length; i++)
                {
                    if (i != 0)
                        insertSql += ",";
                    insertSql += valueList[i].ToString();
                }
                insertSql += ("'" + patientID + "','" + visitNO + "','" + fileNo + "')");
                sql = insertSql;
            }
            else
            {
                string updateSql = "update " + tableName + " set ";
                for (int i = 0; i < columnList.Length; i++)
                {
                    if (i != 0)
                        updateSql += ",";
                    updateSql += columnList[i].ToString() + "=" + valueList[i].ToString();
                }
                updateSql += strWhere;
                sql = updateSql;
            }
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
                }
                return db.ExecuteNonQuery(CommandType.Text, sql);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }

        //病历抽取项保存
        public string In_MR_SUMMARY_EXPORT_DEFINE(string PATIENT_ID, int VISIT_ID, string FILE_NO, string SqlStr, string TabName)
        {
            ArrayList al = new ArrayList();
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" delete from " + TabName);
            strSql.Append(" where PATIENT_ID='" + PATIENT_ID + "'   ");
            strSql.Append(" and VISIT_ID=" + VISIT_ID.ToString() + "   ");
            strSql.Append(" and FILE_NO='" + FILE_NO + "'  ");
            al.Add(strSql.ToString());
            al.Add(SqlStr);
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", al.ToString(), endpoint.Address);
            }
            return DbHelperOra.ExecuteSqlTranS(al).ToString();
        }
        //更新
        public int Up_MR_SUMMARY_EXPORT_DEFINE(string TabSqlWhere, string TabName, string TabSqlStr)
        {
            int obj = 0;
            try
            {
                ArrayList al = new ArrayList();
                StringBuilder strSql = new StringBuilder();
                strSql.Append(" update " + TabName + " set ");
                strSql.Append(TabSqlStr);
                strSql.Append(" where ");
                strSql.Append(TabSqlWhere);
                al.Add(strSql.ToString());
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                obj = DbHelperOra.ExecuteSql(strSql.ToString());
            }
            catch (Exception ee)
            {
                obj = 0;
            }
            return obj;
        }
        //直接执行SQL方法
        public DataSet Get_Sql_to_Date(string SqlStr)
        {
            StringBuilder strSql = new StringBuilder();
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                strSql.Append(SqlStr);
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(cmd);
                return ds;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        #endregion

        #region 病历退回
        /// <summary>
        /// 获取mr_recall_master表数据
        /// </summary>
        /// <param name="where">条件</param>
        /// <returns>数据集</returns>
        public DataSet Get_RecallMaster(string strWhere)
        {
            string strSql = "select * from cpr.mr_recall_master ";
            if (strWhere.Trim() != "")
            {
                strSql = strSql + " where " + strWhere;
            }
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                return dbinfo.GetData(strSql, "MR_RECALL_MASTER");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 获取mr_recall_detail表数据
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet Get_RecallDetail(string strWhere)
        {
            string strSql = "select a.patient_id,a.visit_id,a.recall_no,a.class_code,a.file_no,a.file_status,a.detail_remark,b.topic from cpr.MR_RECALL_DETAIL a  left join mr_file_index b on a.patient_id=b.patient_id and a.visit_id=b.visit_id and a.file_no=b.file_no";
            if (strWhere.Trim() != "")
            {
                strSql = strSql + " where " + strWhere;
            }
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                return dbinfo.GetData(strSql, "MR_RECALL_DETAIL");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());;
            }
        }
        /// <summary>
        /// 病历退回
        /// </summary>
        /// <param name="dsMaster">主记录数据集</param>
        /// <param name="dsDetail">明细记录数据集</param>
        /// <returns>true成功，false失败</returns>
        public bool EmrReturn(DataSet dsMaster, DataSet dsDetail, string PATIENT_ID, int VISIT_ID, string REQUEST, string PARENT, string SUPPER, bool isUpdate)
        {
            string strSql1 = string.Empty;
            string strSql2 = string.Empty;
            ArrayList SqlList = new ArrayList();
            object[] arrDataSet = { dsMaster, dsDetail };
            if (isUpdate)
            {
                //版本
                strSql1 = " insert into MR_ON_LINE(PATIENT_ID,VISIT_ID,STATUS,REQUEST_DOCTOR_ID,REQUEST_DATE_TIME,SUPER_DOCTOR_ID,PARENT_DOCTOR_ID,OTHER_DOCTOR_ID)  "
                            + " values('" + PATIENT_ID + "',"
                            + " " + VISIT_ID.ToString() + ",'0',"
                            + " '" + REQUEST + "',sysdate, "
                            + " '" + SUPPER + "',"
                            + " '" + PARENT + "','')";
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql1.ToString(), endpoint.Address);
                }
                SqlList.Add(strSql1);
            }
            strSql2 = " update medrec.MR_INDEX set MR_STATUS='O' "
                        + " where PATIENT_ID='" + PATIENT_ID + "' "
                        + " and VISIT_ID=" + VISIT_ID.ToString() + " ";
            SqlList.Add(strSql2);
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql2.ToString(), endpoint.Address);
            }
            return dbinfo.SaveData(SqlList, arrDataSet);
        }

        /// <summary>
        /// 病历召回
        /// </summary>
        /// <param name="dsMaster"></param>
        /// <param name="dsDetail"></param>
        /// <param name="PATIENT_ID"></param>
        /// <param name="VISIT_ID"></param>
        /// <returns></returns>
        public bool EmrBack(DataSet dsMaster, DataSet dsDetail, string PATIENT_ID, int VISIT_ID)
        {
            string strSql = string.Empty;
            string strSql1 = string.Empty;
            string strSql2 = string.Empty;
            ArrayList SqlList = new ArrayList();
            strSql1 = " delete from MR_ON_LINE  " +
                      " where PATIENT_ID='" + PATIENT_ID + "'   " +
                      " and VISIT_ID=" + VISIT_ID.ToString() + "   ";
            strSql2 = " update medrec.MR_INDEX set MR_STATUS='C'  " +
                      " where PATIENT_ID='" + PATIENT_ID + "'   " +
                      " and VISIT_ID=" + VISIT_ID.ToString() + "   ";
            SqlList.Add(strSql1);
            SqlList.Add(strSql2);
            object[] arrDataSet = { dsMaster, dsDetail };
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", strSql1.ToString()+"|"+strSql2, endpoint.Address);
            }
            return dbinfo.SaveData(SqlList, arrDataSet);
        }
        #endregion

        #region 会诊病历

        /// <summary>
        /// 查询会诊主记录
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet Get_ConsultationMaster(string strWhere)
        {
            string strSql = "select * from ORDADM.consultation_doctor_master ";
            if (strWhere.Trim() != "")
            {
                strSql = strSql + " where " + strWhere;
            }
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                return dbinfo.GetData(strSql, "consultation_doctor_master");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 查询会诊明细记录
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet Get_ConsultationDetail(string strWhere)
        {
            string strSql = @"select a.* from ORDADM.consultation_doctor_detail a";
            if (strWhere.Trim() != "")
            {
                strSql = strSql + " where " + strWhere;
            }
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                return dbinfo.GetData(strSql, "consultation_doctor_detail");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 查询会诊明细记录
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet Get_ConsultationDetailWithName(string strWhere)
        {
            string strSql = @"select a.*,b.dept_name,c.name from ORDADM.consultation_doctor_detail a
                            left join dept_dict b on a.consultation_dept=b.dept_code 
                            left join staff_dict c on a.consultation_doctor=c.user_name ";
            if (strWhere.Trim() != "")
            {
                strSql = strSql + " where " + strWhere;
            }
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                return dbinfo.GetData(strSql, "consultation_doctor_detail");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 会诊信息保存
        /// </summary>
        /// <param name="dsMaster">主记录</param>
        /// <param name="dsDetail">明细记录</param>
        /// <returns></returns>
        public bool EmrConsultation(DataSet dsMaster, DataSet dsDetail, DataSet dsMessageMaster, DataSet dsMessageDetail, DataSet dsOrders, DataSet dsCosts)
        {
            object[] arrDataSet = { dsMaster, dsDetail, dsMessageMaster, dsMessageDetail, dsOrders, dsCosts };
            return dbinfo.SaveData(null, arrDataSet);
        }

        /// <summary>
        /// 保存明细记录
        /// </summary>
        /// <param name="dsDetail"></param>
        /// <returns></returns>
        public bool Save_ConsultationDetail(DataSet dsDetail)
        {
            return dbinfo.SaveTablesData(dsDetail, null, null);
        }

        /// <summary>
        /// 获得最大id号
        /// </summary>
        /// <returns></returns>
        public string Get_MaxConsultationId()
        {
            string strSql = "select max(CONSULTATION_ID) from ORDADM.consultation_doctor_master ";
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                return dbinfo.GetValueBySql(strSql);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 查询会诊信息
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet Get_ConsultationInfor(string strWhere)
        {
            string strSql = "select * from consultation_doctor_master a,consultation_doctor_detail b where a.consultation_id = b.consultation_id ";
            if (strWhere.Trim() != "")
            {
                strSql = strSql + " and " + strWhere;
            }
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                return dbinfo.GetData(strSql, "consultation_doctor_detail");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        public DataTable Get_ConsultationHistoryList(string strWhere)
        {
            if (strWhere == "")
                return null;
            string sql = string.Format(@"select a.patient_id,a.visit_id,a.consultation_id,g.name pa_name,c.name apply_doctor_name,c.dept_code apply_dept_code,d.dept_name apply_dept_name,
                        decode(a.consultation_type,'1','紧急会诊','2','普通会诊','其他') consultation_type,a.consultation_explain,a.apply_date_time,a.consultation_date_time,a.end_date_time,
                        wm_concat(e.dept_name||'/'||f.name) invited_doctor,wm_concat(b.consultation_dept||'/'||b.consultation_doctor) invited_doctor_code from consultation_doctor_master a
                        left join consultation_doctor_detail b on a.consultation_id=b.consultation_id
                        left join staff_dict c on a.consultation_apply_doctor=c.user_name
                        left join dept_dict d on c.dept_code=d.dept_code
                        left join dept_dict e on b.consultation_dept=e.dept_code
                        left join staff_dict f on b.consultation_doctor=f.user_name
                        left join pat_master_index g on a.patient_id=g.patient_id
                        where {0}
                        group by a.patient_id,a.visit_id,a.consultation_id,g.name,a.apply_date_time,a.consultation_date_time,a.end_date_time,a.consultation_type,a.consultation_explain,
                        c.name,c.dept_code,d.dept_name", strWhere);
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
                }
                return dbinfo.GetData(sql, "consultation_doctor_detail").Tables[0];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }

        /// <summary>
        /// 提取价表项目名称
        /// </summary>
        /// <param name="ClinicItemCode"></param>
        /// <returns></returns>
        public DataSet Get_PriceItemName(string ClinicItemCode, string ItemClass)
        {
            //string strSql = "SELECT  PRICE_ITEM_NAME_DICT.ITEM_NAME,"
            //              + " CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS ,"
            //              + " CLINIC_VS_CHARGE.CHARGE_ITEM_SPEC ,"
            //              + " CLINIC_VS_CHARGE.CLINIC_ITEM_CODE ,"
            //              + " CLINIC_VS_CHARGE.CHARGE_ITEM_CODE ,"
            //              + " PRICE_ITEM_NAME_DICT.ITEM_CODE ,"
            //              + " CLINIC_VS_CHARGE.UNITS ,"
            //              + " CLINIC_VS_CHARGE.AMOUNT,"
            //              + " CLINIC_VS_CHARGE.BACKBILL_RULE "
            //              + " FROM CLINIC_VS_CHARGE ,"
            //              + " PRICE_ITEM_NAME_DICT   "
            //              + " WHERE "
            //              + " CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS = PRICE_ITEM_NAME_DICT.ITEM_CLASS and "
            //              + " CLINIC_VS_CHARGE.CHARGE_ITEM_CODE = PRICE_ITEM_NAME_DICT.ITEM_CODE and "
            //              + " PRICE_ITEM_NAME_DICT.STD_INDICATOR = 1 and "
            //              + " CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS = '" + ItemClass + "' and "
            //              + " CLINIC_VS_CHARGE.CLINIC_ITEM_CODE ='" + ClinicItemCode + "' ";
            string strSql = @"select A.*,B.ITEM_NAME,C.PRICE FROM CLINIC_VS_CHARGE A
                                left join PRICE_ITEM_NAME_DICT B on A.Charge_Item_Class=b.item_class and A.Charge_Item_Code=B.Item_Code 
                                left join comm.current_price_list C on A.CHARGE_ITEM_CLASS=C.item_class and A.CHARGE_ITEM_CODE=C.item_code 
                                                                    and A.CHARGE_ITEM_SPEC=C.item_spec and A.Units=C.units
                                WHERE  B.STD_INDICATOR = 1 and A.CHARGE_ITEM_CLASS = '" + ItemClass + "' and A.CLINIC_ITEM_CODE ='" + ClinicItemCode + "'";
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                return dbinfo.GetData(strSql, "PRICE_ITEM_NAME_DICT");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion

        #region 护理病历导出
        /// <summary>
        /// 查询护理记录单导出规则
        /// </summary>
        /// <returns></returns>
        public DataTable GetNursingExportDefine(string typeid)
        {
            string sql = "select * from CPR.MR_NURSING_RECORD_DEFINE where type_id='" + typeid + "'";
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, sql).Tables[0];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        /// <summary>
        /// 保存护理记录单导出的数据
        /// </summary>
        /// <param name="pid"></param>
        /// <param name="vid"></param>
        /// <param name="typeid"></param>
        /// <param name="alSQLs"></param>
        /// <returns></returns>
        public bool SaveNursingExportData(string pid, int vid, string typeid, List<string> alSQL)
        {
            System.Collections.ArrayList alSQLs = new System.Collections.ArrayList();
            string sql = "delete from  CPR.MR_NURSING_RECORD_CONTENT where patient_id='" + pid + "' and visit_id=" + vid + " and type_id='" + typeid + "'";
            if (TrackCOMM.Track_Indicator)
            {
                OperationContext context = OperationContext.Current;
                MessageProperties properties = context.IncomingMessageProperties;
                RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                TrackEntity entity = new TrackEntity();
                entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
            }
            alSQLs.Add(sql);
            for (int i = 0; i < alSQL.Count; i++)
            {
                alSQLs.Add(alSQL[i].ToString());
            }
            return DbHelperOra.ExecuteSqlTranS(alSQLs);
        }

        #endregion

        /// <summary>
        /// 病历列表树的加载(新生儿出生记录)
        /// </summary>
        /// <param name="patientID"></param>
        /// <param name="visitID"></param>
        /// <returns></returns>
        public DataTable GetNursingRecordNewBirth(string PATIENT_ID, int VISIT_ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT NURSING_RECORD_NEW_BIRTH.VISIT_ID, ");
            strSql.Append(" NURSING_RECORD_NEW_BIRTH.PATIENT_ID, ");
            strSql.Append(" NURSING_RECORD_NEW_BIRTH.PAT_DEPT, ");
            strSql.Append(" DEPT_DICT.DEPT_NAME, ");
            strSql.Append(" NURSING_RECORD_NEW_BIRTH.NURSING_RECORD_TYPE, ");
            strSql.Append(" NURSING_RECORD_NEW_BIRTH.NURSING_DATE ");
            strSql.Append(" FROM NURSING_RECORD_NEW_BIRTH,DEPT_DICT ");
            strSql.Append(" WHERE NURSING_RECORD_NEW_BIRTH.PATIENT_ID = '" + PATIENT_ID + "' ");
            strSql.Append(" AND NURSING_RECORD_NEW_BIRTH.VISIT_ID = " + VISIT_ID + " ");
            strSql.Append(" AND NURSING_RECORD_NEW_BIRTH.PAT_DEPT = DEPT_DICT.DEPT_CODE(+)");
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(CommandType.Text, strSql.ToString());
                return ds.Tables[0];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 病历列表树的加载(分娩记录)
        /// </summary>
        /// <param name="patientID"></param>
        /// <param name="visitID"></param>
        /// <returns></returns>
        public DataTable GetNursingRecordFMREC(string PATIENT_ID, int VISIT_ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT NURSING_RECORD_FM_REC.Patient_Id,  ");
            strSql.Append(" NURSING_RECORD_FM_REC.VISIT_ID,  ");
            strSql.Append(" NURSING_RECORD_FM_REC.DEPT_CODE, ");
            strSql.Append(" DEPT_DICT.DEPT_NAME,  ");
            strSql.Append(" NURSING_RECORD_FM_REC.NURSING_DATE  ");
            strSql.Append(" FROM NURSING_RECORD_FM_REC ,DEPT_DICT ");
            strSql.Append(" WHERE NURSING_RECORD_FM_REC.PATIENT_ID = '" + PATIENT_ID + "'  ");
            strSql.Append(" AND NURSING_RECORD_FM_REC.VISIT_ID = " + VISIT_ID + "  ");
            strSql.Append(" AND NURSING_RECORD_FM_REC.DEPT_CODE = DEPT_DICT.DEPT_CODE(+)");
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", strSql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(CommandType.Text, strSql.ToString());
                return ds.Tables[0];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获取门诊检验
        /// </summary>
        /// <param name="visitNo"></param>
        /// <param name="visitdate"></param>
        /// <returns></returns>
        public DataTable Get_Outp_LAB_TEST_MASTER(int visitNo, DateTime visitdate)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select l.*,");
            strSql.Append("       ed.exam_result_status_name,");
            strSql.Append("       (select ITEM_NAME");
            strSql.Append("          from lab_test_items a");
            strSql.Append("         where a.test_no = l.test_no");
            strSql.Append("           and a.item_no = 1) ITEM_NAME");
            strSql.Append("  from LAB_TEST_MASTER l");
            strSql.Append("  left join exam_result_status_dict ed");
            strSql.Append("    on l.result_status = ed.exam_result_status_code");
            strSql.Append(" where l.test_no in");
            strSql.Append("       (select t.appoint_no");
            strSql.Append("          from outpdoct.outp_treat_rec t");
            strSql.Append("         where item_class = 'C'");
            strSql.Append("           and trunc(t.visit_date) = to_date('{0}', 'yyyy/MM/dd')");
            strSql.Append("           and t.visit_no = {1}) ");
            string sql = strSql.ToString();
            sql = string.Format(sql, visitdate.ToString("yyyy/MM/dd"), visitNo);
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(CommandType.Text, sql);
                if (ds == null)
                    return null;
                return ds.Tables[0];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        /// <summary>
        /// 获取门诊检查
        /// </summary>
        /// <param name="visitNo"></param>
        /// <param name="visitdate"></param>
        /// <returns></returns>
        public DataTable Get_Outp_EXAM_MASTER(int visitNo, DateTime visitdate)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select em.exam_no,");
            strSql.Append("       ei.exam_item,");
            strSql.Append("       ed.exam_result_status_name,");
            strSql.Append("       em.report_date_time,");
            strSql.Append("       em.req_date_time,");
            strSql.Append("       er.description,");
            strSql.Append("       er.impression,");
            strSql.Append("       er.recommendation,");
            strSql.Append("       em.patient_id,");
            strSql.Append("       em.name,");
            strSql.Append("       em.sex,");
            strSql.Append("       em.REQ_PHYSICIAN,");
            strSql.Append("       em.req_dept,");
            strSql.Append("       em.PERFORMED_BY,");
            strSql.Append("       em.EXAM_CLASS,");
            strSql.Append("       em.exam_sub_class ");
            strSql.Append("  from V_EXAM_MASTER em ");
            strSql.Append("  left join exam_result_status_dict ed ");
            strSql.Append("    on em.result_status = ed.exam_result_status_code ");
            strSql.Append("  left join EXAM_REPORT er ");
            strSql.Append("    on em.exam_no = er.exam_no ");
            strSql.Append("  join EXAM_ITEMS ei ");
            strSql.Append("    on ei.exam_no = em.exam_no ");
            strSql.Append(" where em.exam_no in ");
            strSql.Append("       (select t.appoint_no ");
            strSql.Append("          from outpdoct.outp_treat_rec t ");
            strSql.Append("         where item_class = 'D' ");
            strSql.Append("           and trunc(t.visit_date) = to_date('{0}', 'yyyy/MM/dd') ");
            strSql.Append("           and t.visit_no = {1}) ");
            string sql = strSql.ToString();
            sql = string.Format(sql, visitdate.ToString("yyyy/MM/dd"), visitNo);
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEXDAL", "", sql.ToString(), endpoint.Address);
                }
                DataSet ds = db.ExecuteDataSet(CommandType.Text, sql);
                if (ds == null)
                    return null;
                return ds.Tables[0];
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
    }
}

