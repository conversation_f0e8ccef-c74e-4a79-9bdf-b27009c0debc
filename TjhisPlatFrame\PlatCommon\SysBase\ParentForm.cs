﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace PlatCommon.SysBase
{
    /// <summary>
    /// 父窗体（继承自XtraForm）
    /// </summary>
    public class ParentForm : DevExpress.XtraEditors.XtraForm
    {
        #region 属性
        public string SerializeString = string.Empty;

        /// <summary>
        /// 应用编码
        /// </summary>
        public string AppCode = string.Empty;
        /// <summary>
        /// 应用名称
        /// </summary>
        public string AppName = string.Empty;
        /// <summary>
        /// 当前科室编码
        /// </summary>
        public string DeptCode = string.Empty;
        /// <summary>
        /// 科室名称
        /// </summary>
        public string DeptName = string.Empty;
        /// <summary>
        /// 科室列表
        /// </summary>
        public string DeptList = string.Empty;
        /// <summary>
        /// 当前病区编码
        /// </summary>
        public string WardCode = string.Empty;
        /// <summary>
        /// 病区名称
        /// </summary>
        public string WardName = string.Empty;
        /// <summary>
        /// 病区列表
        /// </summary>
        public string WardList = string.Empty;

        /// <summary>
        /// 当前患者信息
        /// </summary>
        public DataRow PatientInfo = null;
        /// <summary>
        /// 当前APP用户权限（app_grants.Capability)
        /// </summary>
        public string Capability = string.Empty;
        /// <summary>
        /// 核算组使用方式(0-诊疗组核算;1-核算组核算),参数 CLINIC_CLASS设置
        /// </summary>
        public string ClinicClass = string.Empty;
        /// <summary>
        /// 母亲和新生儿是否一起出科，1是，0否，参数 NEW_BORN_OUT设置
        /// </summary>        
        public string NewBornOut = string.Empty;
        /// <summary>
        /// 护理单元护士角色(1-普通护士、2-护士长、3-大科护士长、4-护理部人员)，表NR_DICT_NURSE_ROLE设置
        /// </summary>
        public string NurseRole = string.Empty;
        /// <summary>
        /// 是否全病区(护理管理）
        /// </summary>
        public bool IsAllDept = false;
        /// <summary>
        /// 护士权限科室（护理管理、门诊护士站）
        /// </summary>
        public DataTable DutyRightDept;
        /// <summary>
        /// 护理管理报表设计角色设定(1-护理部,2-大护士长,3-护士长,4-护士。例：1,2,3),参数REPORT_DESIGN_ROLE设置
        /// </summary>
        public string ReportDesignRoles = string.Empty;

        /// <summary>
        /// 人员所有授权的职务,表 NURADM_DUTY_RIGHT设置
        /// </summary>
        public DataTable NurAdmDutyRight;

        /// <summary>
        /// 临床路径启用标志 (0-不启用,默认；1-启用)
        /// </summary>
        public string Clinical_Pathway = string.Empty;

        /// <summary>
        /// 医生出诊诊室名称，即队列名称,表OUTP_DOCTOR_SCHEDULE
        /// </summary>
        public string QueueName;
        #endregion

        public virtual void SaveData()
        { }//保存
        public virtual void NewData() { } //新增
        public virtual void DeleteData() { } //删除
        public virtual void RefreshData() { }//刷新
        public virtual void PrintData() { }//打印
        public virtual string Serialize() { return ""; }//序列化
        public virtual void ExportData() { }//导出
        public ParentForm()
        {
            InitializeComponent();            
            DevExpress.Data.CurrencyDataController.DisableThreadingProblemsDetection = true; //禁用DevExpress控件中与线程问题的检测
            System.Windows.Forms.Control.CheckForIllegalCrossThreadCalls = false; // 跨线程调用窗体控件错误捕捉 Yzw 20240410
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // ParentForm
            // 
            this.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(239)))));
            this.Appearance.Options.UseBackColor = true;
            this.ClientSize = new System.Drawing.Size(282, 253);
            this.Name = "ParentForm";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.ParentForm_FormClosed);
            this.ResumeLayout(false);

        }

        private void ParentForm_FormClosed(global::System.Object sender, global::System.Windows.Forms.FormClosedEventArgs e)
        {
            this.Dispose();
            GC.Collect();
        }

        #region 消息委托事件方法
        public Boolean SendHisMsessage(string msgType, string destDept, string message)
        {
            // 判断事件是否有调用委托
            if (null != SendMessageHandler)
            {
                return SendMessageHandler(msgType, destDept, message); //  如果有注册的对象，那就调用委托
            }
            else
            {
                return false;
            }
        }
        // 声明一个委托事件
        public event SendMsessage SendMessageHandler;
        // 声明一个委托
        public delegate Boolean SendMsessage(string messType, string destDeptCode, string strMessage);
        public static EventHandler ShowNurMessage;//用于显示护士站消息

        #endregion
    }
}
