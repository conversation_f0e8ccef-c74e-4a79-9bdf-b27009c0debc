﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Web;
using System.Windows.Forms;

namespace PlatCommon.Base01
{
    /// <summary>
    /// 加密解密类
    /// </summary>
    public class Cs01Security
    {
        static PaddingMode[] padding = {
            PaddingMode.PKCS7,
            PaddingMode.ANSIX923,
            PaddingMode.ISO10126,
            PaddingMode.None,
            PaddingMode.Zeros
        };

        /// <summary>
        /// MD5加密
        /// </summary>
        /// <param name="strSource">原文</param>
        /// <returns></returns>
        public static String MD5Encrypt(String strSource)
        {
            MD5CryptoServiceProvider md5CSP = new MD5CryptoServiceProvider();
            byte[] bArrSource = System.Text.Encoding.UTF8.GetBytes(strSource);
            byte[] bArrResult = md5CSP.ComputeHash(bArrSource);
            StringBuilder sbResult = new StringBuilder();
            for (int i = 0, n = bArrResult.Length; i < n; i++)
            {
                sbResult.Append(bArrResult[i].ToString("x2"));
            }
            return sbResult.ToString();
        }

        /// <summary>
        /// DES加密
        /// </summary>
        /// <param name="strSource">原文</param>
        /// <param name="strKey">key</param>
        /// <param name="IV">偏移量,使用ECB模式这里留空</param>
        /// <param name="Mode">CipherMode加密模式，Java 默认的是ECB模式，PKCS5 padding；C#默认的是CBC模式，PKCS7 padding </param>
        /// <param name="iPad">填充形式，0- PKCS7，1- ANSIX923，2-ISO10126，3-None，4-Zeros </param>
        /// <returns></returns>
        static public string DESEncrypt(string strSource, string strKey, string IV, CipherMode Mode = CipherMode.ECB, int iPad = 0)
        {
            try
            {
                byte[] keyBytes = Encoding.UTF8.GetBytes(strKey);
                byte[] keyIV = Encoding.UTF8.GetBytes(IV);
                byte[] inputByteArray = Encoding.UTF8.GetBytes(strSource);

                DESCryptoServiceProvider desProvider = new DESCryptoServiceProvider();

                //Java 默认的是ECB模式，PKCS5 padding；
                //C#默认的CBC模式，PKCS7 padding 

                desProvider.Mode = Mode;
                desProvider.Padding = padding[iPad];
                System.IO.MemoryStream memStream = new System.IO.MemoryStream();
                CryptoStream crypStream = new CryptoStream(memStream, desProvider.CreateEncryptor(keyBytes, keyIV), CryptoStreamMode.Write);

                crypStream.Write(inputByteArray, 0, inputByteArray.Length);
                crypStream.FlushFinalBlock();
                //return Convert.ToBase64String(memStream.ToArray());      //Base64输出        
                return ByteArrayToHexString(memStream.ToArray());          //Hex输出
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加密失败！\r\n错误信息：{ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// DES解密
        /// </summary>
        /// <param name="strEnMessage">密文</param>
        /// <param name="strKey">key</param>
        /// <param name="IV">偏移量,使用ECB模式这里留空</param>
        /// <param name="Mode">CipherMode加密模式，可以设为CipherMode.ECB</param>
        /// <param name="iPad">填充形式，0- PKCS7，1- ANSIX923，2-ISO10126，3-None，4-Zeros </param>
        /// <returns></returns>
        static public string DESDecrypt(string strEnMessage, string strKey, string IV, CipherMode Mode = CipherMode.ECB, int iPad = 0)
        {
            try
            {
                byte[] bArrKey = Encoding.UTF8.GetBytes(strKey);
                byte[] bArrIV = Encoding.UTF8.GetBytes(IV);
                byte[] bArrInput = HexStringToByteArray(strEnMessage); //Hex

                DESCryptoServiceProvider desProvider = new DESCryptoServiceProvider();

                //Java 默认的是ECB模式，PKCS5 padding；
                //C#默认的CBC模式，PKCS7 padding 

                desProvider.Mode = Mode;
                desProvider.Padding = padding[iPad];
                System.IO.MemoryStream memStream = new System.IO.MemoryStream();
                CryptoStream crypStream = new CryptoStream(memStream, desProvider.CreateDecryptor(bArrKey, bArrIV), CryptoStreamMode.Write);

                crypStream.Write(bArrInput, 0, bArrInput.Length);
                crypStream.FlushFinalBlock();
                return Encoding.UTF8.GetString(memStream.ToArray());
            }
            catch( Exception ex)
            {
                MessageBox.Show($"解密失败！\r\n错误信息：{ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// Hex转为Byte
        /// </summary>
        /// <param name="strSource"></param>
        /// <returns></returns>
        public static byte[] HexStringToByteArray(string strSource)
        {
            strSource = strSource.Replace(" ", "");
            byte[] bArrBuffer = new byte[strSource.Length / 2];
            for (int i = 0; i < strSource.Length; i += 2)
            {
                bArrBuffer[i / 2] = (byte)Convert.ToByte(strSource.Substring(i, 2), 16);
            }

            return bArrBuffer;
        }

        /// <summary>
        /// Byte转换为Hex
        /// </summary>
        /// <param name="bArrData"></param>
        /// <returns></returns>
        public static string ByteArrayToHexString(byte[] bArrData)
        {
            StringBuilder sbResult = new StringBuilder(bArrData.Length * 3);
            foreach (byte b in bArrData)
            {
                sbResult.Append(Convert.ToString(b, 16).PadLeft(2, '0').PadRight(3, ' '));
            }

            return sbResult.ToString().Replace(" ", "");
        }

        /// <summary>
        /// 取字符串哈希值
        /// </summary>
        /// <param name="strSource"></param>
        /// <returns></returns>
        public static string SHA1(string strSource)
        {
            SHA1CryptoServiceProvider sha1 = new SHA1CryptoServiceProvider(); /*System.Security.Cryptography.*/
            byte[] instrEncrypt = Encoding.UTF8.GetBytes(strSource);
            byte[] resultEncrypt = sha1.ComputeHash(instrEncrypt);
            StringBuilder sb = new StringBuilder();
            for (int i = 0, n = resultEncrypt.Length; i < n; i++)
            {
                sb.Append(resultEncrypt[i].ToString("x2"));
            }
            return sb.ToString();
        }

        /// <summary>
        /// 产生n位包含数字与大小字母的随机字符串
        /// </summary>
        /// <param name="n"></param>
        /// <returns></returns>
        public static string GetRandonKey(int n)
        {
            /*产生n位包含数字与大小字母的随机字符串*/
            char[] arrChar = new char[]{
               'a','b','d','c','e','f','g','h','i','j','k','l','m','n','p','r','q','s','t','u','v','w','z','y','x',
               '0','1','2','3','4','5','6','7','8','9',
               'A','B','C','D','E','F','G','H','I','J','K','L','M','N','Q','P','R','T','S','V','U','W','X','Y','Z'
              };

            StringBuilder num = new StringBuilder();
            int len = arrChar.Length;
            Random rnd = new Random(DateTime.Now.Millisecond);
            for (int i = 0; i < n; i++)
            {
                num.Append(arrChar[rnd.Next(0, len)].ToString());
            }
            return num.ToString();
        }
    
    }

    /// <summary>
    /// AES加密类
    /// </summary>
    public class Cs01AES
    {
        /// <summary>
        /// 
        /// </summary>
        public enum His00AESBits
        {
            /// <summary>
            /// 16位
            /// </summary>
            BITS128,
            /// <summary>
            /// 24位
            /// </summary>
            BITS192,
            /// <summary>
            /// 32位
            /// </summary>
            BITS256
        };

        private string _fPassword;

        private His00AESBits _fEncryptionBits;

        private byte[] _fSalt = { 0x00, 0x01, 0x02, 0x1C, 0x1D, 0x1E, 0x03, 0x04, 0x05, 0x0F, 0x20, 0x21, 0xAD, 0xAF, 0xA4 };

        /// <summary>
        /// 
        /// </summary>
        public Cs01AES()
        { }
        
        /// <summary>
        /// Initialize new AESEncryptor.
        /// </summary>
        /// <param name="password">The password to use for encryption/decryption.</param>
        /// <param name="encryptionBits">Encryption bits (128,192,256).</param>
        public Cs01AES(string password, His00AESBits encryptionBits)
        {
            _fPassword = password;
            _fEncryptionBits = encryptionBits;
        }

        /// <summary>
        /// Initialize new AESEncryptor.
        /// </summary>
        /// <param name="password">The password to use for encryption/decryption.</param>
        /// <param name="encryptionBits">Encryption bits (128,192,256).</param>
        /// <param name="salt">Salt bytes. Bytes length must be 15.</param>
        public Cs01AES(string password, His00AESBits encryptionBits, byte[] salt)
        {
            _fPassword = password;
            _fEncryptionBits = encryptionBits;
            _fSalt = salt;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="data"></param>
        /// <param name="key"></param>
        /// <param name="iV"></param>
        /// <returns></returns>
        private byte[] iEncrypt(byte[] data, byte[] key, byte[] iV)
        {
            var ms = new MemoryStream();

            Rijndael alg = Rijndael.Create();
            alg.Key = key;

            alg.IV = iV;
            var cs = new CryptoStream(ms, alg.CreateEncryptor(), CryptoStreamMode.Write);

            cs.Write(data, 0, data.Length);
            cs.Close();
            byte[] encryptedData = ms.ToArray();
            return encryptedData;
        }

        /// <summary>
        /// Encrypt string with AES algorith.
        /// </summary>
        /// <param name="data">String to encrypt.</param>
        public string Encrypt(string data)
        {
            byte[] clearBytes = System.Text.Encoding.Unicode.GetBytes(data);

            var pdb = new PasswordDeriveBytes(_fPassword, _fSalt);

            switch (_fEncryptionBits)
            {
                case His00AESBits.BITS128:
                    return Convert.ToBase64String(iEncrypt(clearBytes, pdb.GetBytes(16), pdb.GetBytes(16)));

                case His00AESBits.BITS192:
                    return Convert.ToBase64String(iEncrypt(clearBytes, pdb.GetBytes(24), pdb.GetBytes(16)));

                case His00AESBits.BITS256:
                    return Convert.ToBase64String(iEncrypt(clearBytes, pdb.GetBytes(32), pdb.GetBytes(16)));
            }
            return null;
        }

        /// <summary>
        /// Encrypt byte array with AES algorithm.
        /// </summary>
        /// <param name="data">Bytes to encrypt.</param>
        public byte[] Encrypt(byte[] data)
        {
            var pdb = new PasswordDeriveBytes(_fPassword, _fSalt);
            switch (_fEncryptionBits)
            {
                case His00AESBits.BITS128:
                    return iEncrypt(data, pdb.GetBytes(16), pdb.GetBytes(16));

                case His00AESBits.BITS192:
                    return iEncrypt(data, pdb.GetBytes(24), pdb.GetBytes(16));

                case His00AESBits.BITS256:
                    return iEncrypt(data, pdb.GetBytes(32), pdb.GetBytes(16));
            }
            return null;
        }

        private byte[] iDecrypt(byte[] data, byte[] key, byte[] iv)
        {
            var ms = new MemoryStream();
            Rijndael alg = Rijndael.Create();
            alg.Key = key;
            alg.IV = iv;
            var cs = new CryptoStream(ms, alg.CreateDecryptor(), CryptoStreamMode.Write);
            cs.Write(data, 0, data.Length);
            cs.Close();
            byte[] decryptedData = ms.ToArray();
            return decryptedData;
        }

        /// <summary>
        /// Decrypt string with AES algorithm.
        /// </summary>
        /// <param name="data">Encrypted string.</param>
        public string Decrypt(string data)
        {
            byte[] dataToDecrypt = Convert.FromBase64String(data);

            var pdb = new PasswordDeriveBytes(_fPassword, _fSalt);

            switch (_fEncryptionBits)
            {
                case His00AESBits.BITS128:
                    return System.Text.Encoding.Unicode.GetString(iDecrypt(dataToDecrypt, pdb.GetBytes(16), pdb.GetBytes(16)));

                case His00AESBits.BITS192:
                    return System.Text.Encoding.Unicode.GetString(iDecrypt(dataToDecrypt, pdb.GetBytes(24), pdb.GetBytes(16)));

                case His00AESBits.BITS256:
                    return System.Text.Encoding.Unicode.GetString(iDecrypt(dataToDecrypt, pdb.GetBytes(32), pdb.GetBytes(16)));
            }
            return null;
        }

        /// <summary>
        /// Decrypt byte array with AES algorithm.
        /// </summary>
        /// <param name="data">Encrypted byte array.</param>
        public byte[] Decrypt(byte[] data)
        {
            var pdb = new PasswordDeriveBytes(_fPassword, _fSalt);

            switch (_fEncryptionBits)
            {
                case His00AESBits.BITS128:
                    return iDecrypt(data, pdb.GetBytes(16), pdb.GetBytes(16));

                case His00AESBits.BITS192:
                    return iDecrypt(data, pdb.GetBytes(24), pdb.GetBytes(16));

                case His00AESBits.BITS256:
                    return iDecrypt(data, pdb.GetBytes(32), pdb.GetBytes(16));
            }
            return null;
        }

        /// <summary>
        /// Encryption/Decryption password.
        /// </summary>
        public string Password
        {
            get { return _fPassword; }
            set { _fPassword = value; }
        }

        /// <summary>
        /// Encryption/Decryption bits.
        /// </summary>
        public His00AESBits EncryptionBits
        {
            get { return _fEncryptionBits; }
            set { _fEncryptionBits = value; }
        }

        /// <summary>
        /// Salt bytes (bytes length must be 15).
        /// </summary>
        public byte[] Salt
        {
            get { return _fSalt; }
            set { _fSalt = value; }
        }
    }

    /// <summary>
    /// RSA加密类
    /// </summary>
    public class Cs01RSA
    {
        private RSACryptoServiceProvider _privateKeyRsaProvider;
        private RSACryptoServiceProvider _publicKeyRsaProvider;

        /// <summary>
        /// RSA加密
        /// </summary>
        /// <param name="strPrivateKey">私钥匙</param>
        /// <param name="strPublicKey">公钥匙</param>
        public Cs01RSA(string strPrivateKey, string strPublicKey = null)
        {
            if (!string.IsNullOrEmpty(strPrivateKey))
            {
                _privateKeyRsaProvider = CreateRsaProviderFromPrivateKey(strPrivateKey);
            }

            if (!string.IsNullOrEmpty(strPublicKey))
            {
                _publicKeyRsaProvider = CreateRsaProviderFromPublicKey(strPublicKey);
            }
        }

        /// <summary>
        /// 解密
        /// </summary>
        /// <param name="cipherText"></param>
        /// <returns></returns>
        public string Decrypt(string cipherText)
        {
            if (_privateKeyRsaProvider == null)
            {
                throw new Exception("_privateKeyRsaProvider is null");
            }
            return Encoding.UTF8.GetString(_privateKeyRsaProvider.Decrypt(System.Convert.FromBase64String(cipherText), false));
        }

        /// <summary>
        /// 加密
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        public string Encrypt(string text)
        {
            if (_publicKeyRsaProvider == null)
            {
                throw new Exception("_publicKeyRsaProvider is null");
            }
            return Convert.ToBase64String(_publicKeyRsaProvider.Encrypt(Encoding.UTF8.GetBytes(text), false));
        }

        private RSACryptoServiceProvider CreateRsaProviderFromPrivateKey(string privateKey)
        {
            var privateKeyBits = System.Convert.FromBase64String(privateKey);
            var RSA = new RSACryptoServiceProvider();
            var RSAparams = new RSAParameters();

            using (BinaryReader binr = new BinaryReader(new MemoryStream(privateKeyBits)))
            {
                byte bt = 0;
                ushort twobytes = 0;
                twobytes = binr.ReadUInt16();
                if (twobytes == 0x8130)
                {
                    binr.ReadByte();
                }
                else if (twobytes == 0x8230)
                {
                    binr.ReadInt16();
                }
                else
                {
                    throw new Exception("Unexpected value read binr.ReadUInt16()");
                }

                twobytes = binr.ReadUInt16();
                if (twobytes != 0x0102)
                {
                    throw new Exception("Unexpected version");
                }

                bt = binr.ReadByte();
                if (bt != 0x00)
                {
                    throw new Exception("Unexpected value read binr.ReadByte()");
                }

                RSAparams.Modulus = binr.ReadBytes(GetIntegerSize(binr));
                RSAparams.Exponent = binr.ReadBytes(GetIntegerSize(binr));
                RSAparams.D = binr.ReadBytes(GetIntegerSize(binr));
                RSAparams.P = binr.ReadBytes(GetIntegerSize(binr));
                RSAparams.Q = binr.ReadBytes(GetIntegerSize(binr));
                RSAparams.DP = binr.ReadBytes(GetIntegerSize(binr));
                RSAparams.DQ = binr.ReadBytes(GetIntegerSize(binr));
                RSAparams.InverseQ = binr.ReadBytes(GetIntegerSize(binr));
            }

            RSA.ImportParameters(RSAparams);
            return RSA;
        }

        private int GetIntegerSize(BinaryReader binr)
        {
            byte bt = 0;
            byte lowbyte = 0x00;
            byte highbyte = 0x00;
            int count = 0;
            bt = binr.ReadByte();
            if (bt != 0x02)
            {
                return 0;
            }
            bt = binr.ReadByte();

            if (bt == 0x81)
            {
                count = binr.ReadByte();
            }
            else if (bt == 0x82)
            {
                highbyte = binr.ReadByte();
                lowbyte = binr.ReadByte();
                byte[] modint = { lowbyte, highbyte, 0x00, 0x00 };
                count = BitConverter.ToInt32(modint, 0);
            }
            else
            {
                count = bt;
            }

            while (binr.ReadByte() == 0x00)
            {
                count -= 1;
            }
            binr.BaseStream.Seek(-1, SeekOrigin.Current);
            return count;
        }

        private RSACryptoServiceProvider CreateRsaProviderFromPublicKey(string publicKeyString)
        {
            // encoded OID sequence for  PKCS #1 rsaEncryption szOID_RSA_RSA = "1.2.840.113549.1.1.1"
            byte[] SeqOID = { 0x30, 0x0D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01, 0x05, 0x00 };
            byte[] x509key;
            byte[] seq = new byte[15];
            int x509size;

            x509key = Convert.FromBase64String(publicKeyString);
            x509size = x509key.Length;

            // ---------  Set up stream to read the asn.1 encoded SubjectPublicKeyInfo blob  ------
            using (MemoryStream mem = new MemoryStream(x509key))
            {
                using (BinaryReader binr = new BinaryReader(mem))  //wrap Memory Stream with BinaryReader for easy reading
                {
                    byte bt = 0;
                    ushort twobytes = 0;

                    twobytes = binr.ReadUInt16();
                    if (twobytes == 0x8130) //data read as little endian order (actual data order for Sequence is 30 81)
                        binr.ReadByte();    //advance 1 byte
                    else if (twobytes == 0x8230)
                        binr.ReadInt16();   //advance 2 bytes
                    else
                        return null;

                    seq = binr.ReadBytes(15);       //read the Sequence OID
                    if (!CompareBytearrays(seq, SeqOID))    //make sure Sequence for OID is correct
                        return null;

                    twobytes = binr.ReadUInt16();
                    if (twobytes == 0x8103) //data read as little endian order (actual data order for Bit String is 03 81)
                        binr.ReadByte();    //advance 1 byte
                    else if (twobytes == 0x8203)
                        binr.ReadInt16();   //advance 2 bytes
                    else
                        return null;

                    bt = binr.ReadByte();
                    if (bt != 0x00)     //expect null byte next
                        return null;

                    twobytes = binr.ReadUInt16();
                    if (twobytes == 0x8130) //data read as little endian order (actual data order for Sequence is 30 81)
                        binr.ReadByte();    //advance 1 byte
                    else if (twobytes == 0x8230)
                        binr.ReadInt16();   //advance 2 bytes
                    else
                        return null;

                    twobytes = binr.ReadUInt16();
                    byte lowbyte = 0x00;
                    byte highbyte = 0x00;

                    if (twobytes == 0x8102) //data read as little endian order (actual data order for Integer is 02 81)
                        lowbyte = binr.ReadByte();  // read next bytes which is bytes in modulus
                    else if (twobytes == 0x8202)
                    {
                        highbyte = binr.ReadByte(); //advance 2 bytes
                        lowbyte = binr.ReadByte();
                    }
                    else
                        return null;
                    byte[] modint = { lowbyte, highbyte, 0x00, 0x00 };   //reverse byte order since asn.1 key uses big endian order
                    int modsize = BitConverter.ToInt32(modint, 0);

                    int firstbyte = binr.PeekChar();
                    if (firstbyte == 0x00)
                    {   //if first byte (highest order) of modulus is zero, don't include it
                        binr.ReadByte();    //skip this null byte
                        modsize -= 1;   //reduce modulus buffer size by 1
                    }

                    byte[] modulus = binr.ReadBytes(modsize);   //read the modulus bytes

                    if (binr.ReadByte() != 0x02)            //expect an Integer for the exponent data
                        return null;
                    int expbytes = (int)binr.ReadByte();        // should only need one byte for actual exponent data (for all useful values)
                    byte[] exponent = binr.ReadBytes(expbytes);

                    // ------- create RSACryptoServiceProvider instance and initialize with public key -----
                    RSACryptoServiceProvider RSA = new RSACryptoServiceProvider();
                    RSAParameters RSAKeyInfo = new RSAParameters();
                    RSAKeyInfo.Modulus = modulus;
                    RSAKeyInfo.Exponent = exponent;
                    RSA.ImportParameters(RSAKeyInfo);

                    return RSA;
                }
            }
        }

        private bool CompareBytearrays(byte[] a, byte[] b)
        {
            if (a.Length != b.Length)
            {
                return false;
            }

            int i = 0;
            foreach (byte c in a)
            {
                if (c != b[i])
                    return false;
                i++;
            }
            return true;
        }
    }

}