﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Windows.Forms;

using DevExpress.XtraEditors;
using NM_Service.NMService;
using PlatCommon.SysBase;

namespace PlatCommonForm
{
    public partial class frmDiagnosisDict : ParentForm
    {
        public string code { get; set; }
        public string name { get; set; }
        public string  is_charge_type="";
        public string is_dqbm="";

        DataTable dt = new DataTable();

        public frmDiagnosisDict()
        {
            InitializeComponent();
        }

        private void frmDiagnosisDict_Load(object sender, EventArgs e)
        {
            tE_search.Focus();
            getDiagnosisDict();
        }

        private void gridView_CustomDrawRowIndicator(object sender, DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventArgs e)
        {
            if (e.Info.IsRowIndicator && e.RowHandle >= 0)
                e.Info.DisplayText = (e.RowHandle + 1).ToString();
        }

        private void sb_apply_Click(object sender, EventArgs e)
        {
            try
            {
                code = string.Empty;
                name = string.Empty;

                int[] selectedRows = gridView.GetSelectedRows();
                if (selectedRows.Length.Equals(1))
                {
                    code = gridView.GetRowCellDisplayText(selectedRows[0], "AKA120");
                    name = gridView.GetRowCellDisplayText(selectedRows[0], "AKA121");
                }
                else if (selectedRows.Length > 1)
                {
                    foreach (int i in selectedRows)
                    {
                        code += gridView.GetRowCellDisplayText(i, "AKA120") + ",";
                        name += gridView.GetRowCellDisplayText(i, "AKA121") + ",";
                    }
                    code = code.Substring(0, code.Length - 1);
                    name = name.Substring(0, name.Length - 1);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch(Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                code = gridView.GetFocusedRowCellDisplayText("AKA120");
                name = gridView.GetFocusedRowCellDisplayText("AKA121");

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }

        private void sb_clear_Click(object sender, EventArgs e)
        {
            code = string.Empty;
            name = string.Empty;

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void sb_cancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void tE_search_EditValueChanging(object sender, DevExpress.XtraEditors.Controls.ChangingEventArgs e)
        {
            try
            {
                if (dt == null || dt.Rows.Count == 0)
                    getDiagnosisDict();

                if (dt != null && dt.Rows.Count > 0)
                {
                    DataTable tempDt = dt.Copy();
                    tempDt.DefaultView.RowFilter = " AKA120 like '%" + tE_search.Text.Trim() + "%' or  AKA121 like '%" + tE_search.Text.Trim() + "%' or AKA066 like '%" + tE_search.Text.Trim() + "%' or input_code_wb like '%" + tE_search.Text.Trim() + "%' ";
                    gridControl.DataSource = tempDt.DefaultView.ToTable();
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }

        private void getDiagnosisDict()
        {
            string sql="";
            if (string.IsNullOrEmpty(is_charge_type))
            {
                sql = "select d.diagnosis_code AKA120, d.diagnosis_name AKA121, d.input_code AKA066, d.input_code_wb from diagnosis_dict d where new_flag ='1'";
            }
            else 
            {
                string ls_charge_type = is_charge_type + "诊断";
                sql = SetInputSetting(ls_charge_type, PlatCommon.SysBase.SystemParm.HisUnitCode);
            }

            if (is_charge_type.Equals("农合网"))
            {
                DataTable nhdt = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select a.parameter_value  from app_configer_parameter a WHERE a.parameter_name='NH_CONNECT' and a.his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'").Tables[0];
                if (nhdt.Rows.Count <= 0)
                {
                    XtraMessageBox.Show("没有查询到医保接连配置", "提示");
                    return;
                }
                string connsql = nhdt.Rows[0][0].ToString();
                sql = sql + " where DQBM='" + is_dqbm + "'";
                PlatCommon.Common.SqlConnectionPublic.GetSqlConnection(connsql, sql, ref dt);
            }
            else 
            {
                dt = new ServerPublicClient().GetDataBySql(sql).Tables[0];
            }

            
            if (dt != null && dt.Rows.Count > 0)
                gridControl.DataSource = dt;
            tE_search.Focus();
        }



        /// <summary>
        /// 设置inputsetting表，拼成查询语句
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private string SetInputSetting(string input, string hisunitcode)
        {
            string selectstr = "";
            string sql = "select * from input_setting s where s.dict_type='" + input + "' and s.his_unit_code='" + hisunitcode + "'  order by show_sort";
            DataTable inputdt = new ServerPublicClient().GetList(sql).Tables[0];
            if (inputdt.Rows.Count > 0)
            {
                string tablename = inputdt.Rows[0]["DATA_TABLE"].ToString();
                string columnNames = "";
                for (int i = 0; i < inputdt.Rows.Count; i++)
                {
                    columnNames += inputdt.Rows[i]["DATA_COL"].ToString();
                    if (i != inputdt.Rows.Count - 1)
                    {
                        columnNames += ",";
                    }
                }
                selectstr = "SELECT " + columnNames + " FROM " + tablename;
            }
            return selectstr;
        }

        private void tE_search_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyValue == 40 || e.KeyValue == 38)
            { gridView.Focus(); }
        }

        private void gridView_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                gridView_DoubleClick(sender, e);
            }
        }
 
        
    }
}
