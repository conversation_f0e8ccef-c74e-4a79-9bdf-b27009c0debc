﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System.IO;
using NM_Service.NMService;

namespace PlatCommon.Common
{
    public class IExamAppoints
    {
        //安装文件
        private static string installFiles = "C:\\RoomAppoint\\AppointPath.txt";

        #region 检查环境
        /// <summary>
        /// 检查预约环境
        /// </summary>
        /// <returns>ture--环境通过；false--环境不通过</returns>
        private static Boolean Environment()
        {
            if (!File.Exists(installFiles))
            {
                XtraMessageBox.Show("预约模块尚未安装，请稍后点击弹出页面上的运行按钮，进行安装！", "提示");
                // string examUrl = PlatCommon.SysBase.SystemParm.GetParaValue("APPOINTEXAM_URL", "*", "*", "*", "http://192.168.2.142:19999/publish.html");
                string examUrl = "";
                PublicFunction.GetInterfaceConfigDict("APPOINTEXAM_URL", ref examUrl);
                IeOpen(examUrl);
                return false;
            }
            return true;
        }
        #endregion

        #region 打开预约
        /// <summary>
        /// 打开预约
        /// </summary>
        /// <param name="parameterList">多个参数相连</param>
        public static void OpenAppoints(string parameterList)
        {
            if (!Environment())
            {
                return;
            }
            try
            {
                string conent = System.IO.File.ReadAllText(installFiles);
                conent = conent.Replace("\r\n", "");
                System.Diagnostics.Process.Start(conent, parameterList);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("发生错误" + ex.Message, "提示");
            }
        }
        #endregion

        #region 打开浏览器
        /// <summary>
        /// 打开浏览器
        /// </summary>
        /// <param name="url">URL地址</param>
        /// <param name="app">浏览器名称</param>
        private static void IeOpen(string url, string app = "IEXPLORE.EXE")
        {
            try
            {
                System.Diagnostics.Process.Start(app, url);
            }
            catch
            {
                //打开浏览器发生错误!
                XtraMessageBox.Show("打开浏览器发生错误", "提示");
            }
        }
        #endregion

        #region 预约申请原型方法
        /// <summary>
        /// 预约申请原型方法
        /// </summary>
        /// <param name="functionFlag">功能标识(R--查询;W--预约)</param>
        /// <param name="examClass">检查大类别名称</param>
        /// <param name="examSubClass">检查子类别名称</param>
        /// <param name="patientId">病人ID</param>
        /// <param name="visitId">住院次数</param>
        /// <param name="operation">操作人</param>
        /// <param name="reqDept">申请科室代码</param>
        /// <param name="doctorUser">申请医生</param>
        /// <param name="examNo">申请号</param>
        /// <param name="examItemCode">申请项目代码</param>
        /// <param name="patientSource">病人来源</param>
        public static void Appoints(string functionFlag, string examClass, string examSubClass, string patientId, string operation, string reqDept, string visitId = "", string doctorUser = "", string examNo = "", string examItemCode = "", string patientSource = "")
        {
            //功能标识(R--查询;W--预约)$检查大类别名称$检查子类别名称$病人ID号$申请单号$检查项目编码$病人来源$住院次数$申请科室编码$医生代码(STAFF_DICT.USER_NAME)$当前操作员
            string parms = string.Format(@"{0}${1}${2}${3}${4}${5}${6}${7}${8}${9}${10}",
                                        functionFlag, examClass, examSubClass, patientId, examNo, examItemCode, patientSource, visitId, reqDept, doctorUser, operation);
            OpenAppoints(parms);
        }
        #endregion

    }
}
