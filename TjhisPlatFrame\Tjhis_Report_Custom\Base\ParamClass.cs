﻿using System;
using System.Data;
using System.Text.RegularExpressions;
using DevExpress.XtraBars;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Mask;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid.Columns;

namespace Tjhis.Report.Custom.Base
{
    public class ParamClass
    {
        #region 字段
        const string FLAG_LOOKUPEDIT = "1";  //下拉选项
        const string FLAG_TEXTEDIT = "2";   //文本控件
        const string FLAG_DATEEDIT = "3";   //日期控件
        const string FLAG_SPINEDIT = "4";   //数值控件
        const string FLAG_SEARCHLOOKUPEDIT = "5";//下拉框（可检索）
        /// <summary>
        /// 序号
        /// </summary>
        int _seralNo;
        /// <summary>
        /// 参数名称（SQL中参数）
        /// </summary>
        string _paramName;
        /// <summary>
        /// 显示列
        /// </summary>
        string _displayMember;
        /// <summary>
        /// 取值列
        /// </summary>
        string _valueMember;
        /// <summary>
        /// 二级联动类
        /// </summary>
        ParamClass _nextEdit;
        /// <summary>
        /// 数据源 DataTable
        /// </summary>
        DataTable _dataSource;
        /// <summary>
        /// 控件类型 
        /// </summary>
        string _editType;
        //string sourceSql;
        /// <summary>
        /// 控件显示名称
        /// </summary>
        string _Caption;
        /// <summary>
        /// 控件宽度
        /// </summary>
        int _width;
        /// <summary>
        /// 控件默认值
        /// </summary>
        object _defaultValue;
        /// <summary>
        /// 控件选中值
        /// </summary>
        object _editValue;
        /// <summary>
        /// 控件显示值
        /// </summary>
        string _displayValue;
        /// <summary>
        /// 
        /// </summary>
        BarEditItem _editItem;
        /// <summary>
        /// 编辑控件
        /// </summary>
        RepositoryItem _editControl;
        /// <summary>
        /// 日期格式
        /// </summary>
        string _defaultDateFormat;
        #endregion

        #region 构造函数
        /// <summary>
        /// 如果是时间控件或者是文本框用此初始化
        /// </summary>
        /// <param name="paramName">SQL参数名称</param>
        /// <param name="editType">控件类型（1 下拉框  2 文本框  3 日期）</param>
        /// <param name="caption">控件显示名称</param>
        /// <param name="defaultValue">默认值</param>
        /// <param name="width">控件宽度</param>
        public ParamClass(string paramName, string editType, string caption, object defaultValue, string defaultDateFormat, int width = 100)
        {
            this._paramName = paramName.Trim().ToUpper();
            this._editType = editType;
            this._Caption = caption;
            this._defaultValue = defaultValue;
            if (defaultValue.ToString().StartsWith("{"))
            {
                this._defaultValue = GetSystemParamValues(defaultValue.ToString());
            }
            this._defaultDateFormat = defaultDateFormat;
            this._width = width == 0 ? 100 : width;
        }
        /// <summary>
        /// 下拉控件用此构造初始化
        /// </summary>
        /// <param name="paramName">SQL参数名称</param>
        /// <param name="editType">控件类型（1 下拉框  2 文本框  3 日期）</param>
        /// <param name="caption">控件显示名称</param>
        /// <param name="displayMember">显示内容绑定的列名</param>
        /// <param name="valueMember">值绑定的列名</param>
        /// <param name="dataSource">数据源</param>
        /// <param name="defaultValue">默认值</param>
        /// <param name="width">控件宽度</param>
        public ParamClass(string paramName, string editType, string caption, string displayMember, string valueMember, DataTable dataSource, object defaultValue, string defaultDateFormat, int width = 100)
        : this(paramName, editType, caption, defaultValue, defaultDateFormat, width)
        {
            this._displayMember = displayMember.Trim().ToUpper();
            this._valueMember = valueMember.Trim().ToUpper();
            this._dataSource = dataSource;
        }
        /// <summary>
        /// 如有二级联动，先创建联动类后用此构造初始化
        /// </summary>
        /// <param name="paramName">SQL参数名称</param>
        /// <param name="editType">控件类型（1 下拉框  2 文本框  3 日期）</param>
        /// <param name="caption">控件显示名称</param>
        /// <param name="displayMember">显示内容绑定的列名</param>
        /// <param name="valueMember">值绑定的列名</param>
        /// <param name="dataSource">数据源</param>
        /// <param name="nextEdit">二级联动项</param>
        /// <param name="defaultValue">默认值</param>
        /// <param name="width">控件宽度</param>
        public ParamClass(string paramName, string editType, string caption, string displayMember, string valueMember, DataTable dataSource, ParamClass nextEdit, object defaultValue, string defaultDateFormat, int width = 100)
        : this(paramName, editType, caption, displayMember, valueMember, dataSource, defaultValue, defaultDateFormat, width)
        {
            this._nextEdit = nextEdit;
        }
        #endregion

        #region 根据DataRow初始化此类
        /// <summary>
        /// 根据DataRow初始化此类
        /// </summary>
        /// <param name="dr"></param>
        public ParamClass(DataRow dr, DataTable dataSource, ParamClass nextEdit)
        : this(dr?["PARAM_NAME"]?.ToString(), dr?["EDIT_TYPE"]?.ToString(), dr?["CAPTION"]?.ToString(), dr?["DISPLAY_MEMBER"]?.ToString(), dr?["VALUE_MEMBER"]?.ToString(), dataSource, nextEdit, dr?["DEFAULT_VALUE"]?.ToString(), dr?["DATE_FORMAT"]?.ToString(), int.Parse(dr?["CONTROL_WIDTH"]?.ToString() ?? "0"))
        {
        }
        public ParamClass(DataRow dr, DataTable dataSource)
        : this(dr, dataSource, null)
        {
        }
        public ParamClass(DataRow dr)
        : this(dr, null)
        {
        }
        #endregion
        /// <summary>
        /// 过滤并得到系统参数对应的值
        /// </summary>
        /// <param name="Param"></param>
        /// <returns></returns>
        private string GetSystemParamValues(string Param)
        {
            Regex regex = new Regex(@"\{\s*\w+\s*\}"); //{}类型的参数
            var matches = regex.Matches(Param);
            foreach (Match match in matches)
            {
                string strparam = match.Value.TrimStart('{').TrimEnd('}').Trim();//去除首尾的花括号,并去除空格      
                System.Collections.Hashtable hastable = new Srv.srvStatisticalQuery().AddSystemParam();//zhj appcode和deptcode暂未处理
                return hastable[strparam]?.ToString() ?? Param;  //如果有多个系统参数，只取第一个系统参数                
            }
            return Param;
        }
        /// <summary>
        /// 获取参数名称
        /// </summary>
        /// <returns>参数名称</returns>
        public string GetParamName()
        {
            return _paramName;
        }
        /// <summary>
        /// 获取二级联动类
        /// </summary>
        /// <returns>ParamClass</returns>
        public ParamClass GetNextEdit()
        {
            return _nextEdit;
        }
        /// <summary>
        /// 设置二级联动控件
        /// </summary>
        /// <param name="nextEdit">ParamClass</param>
        public void SetNextEdit(ParamClass nextEdit)
        {
            this._nextEdit = nextEdit;
        }
        /// <summary>
        /// 设置控件数据源 DataTable
        /// </summary>
        /// <param name="dataSource">数据源 DataTable</param>
        public void SetDataSource(DataTable dataSource)
        {
            this._dataSource = dataSource;
            if (_editControl != null && _editControl is RepositoryItemLookUpEdit)
            {
                ((RepositoryItemLookUpEdit)_editControl).DataSource = dataSource;
            }
        }
        /// <summary>
        /// 获取控件类型 字符串（1-下拉框  2-文本框  3-日期）
        /// </summary>
        /// <returns>控件类型</returns>
        public string GetEditType()
        {
            return _editType;
        }
        /// <summary>
        /// 获取控件值
        /// </summary>
        /// <returns></returns>
        public object EditValue()
        {
            if (_editValue == null || string.IsNullOrEmpty(_editValue.ToString()))
            {
                if (_paramName.ToUpper().Contains("-NUM"))//数值类型参数 空值返回null
                {
                    return "null";
                }
                else
                {
                    if (string.IsNullOrEmpty(_defaultValue.ToString().Trim()))
                    {
                        return "*";
                    }
                    else
                    {
                        return _defaultValue.ToString().Trim();
                    }

                }
            }

            return _editValue;
        }
        /// <summary>
        /// 获取控件显示值
        /// </summary>
        /// <returns></returns>
        public string DisplayValue()
        {
            if (string.IsNullOrEmpty(_displayValue))
                return "*";
            return _displayValue;
        }


        /// <summary>
        /// 获取所需要的 BarEditItem
        /// </summary>
        /// <param name="bar"></param>
        /// <returns></returns>
        public BarEditItem GetEditItem(BarManager bar)
        {
            if (_editItem == null)
            {
                _editItem = new BarEditItem(bar, GetEdit());
                _editItem.Caption = this._Caption;
                _editItem.PaintStyle = BarItemPaintStyle.Caption;
                _editItem.EditWidth = _width == 0 ? 100 : _width;
                _editItem.CaptionAlignment = DevExpress.Utils.HorzAlignment.Near;

                _editItem.EditValueChanged += EditItem_EditValueChanged;

                //editItem.Edit = editControl;

                if (FLAG_DATEEDIT.Equals(_editType))
                {
                    //获取时间默认值
                    Tuple<bool, DateTime> result = new DefaultDateTimeValueProvider().GetDefaultDateTime(this._defaultValue.ToString());
                    this._editItem.EditValue = result.Item2;
                    //int day = 0;
                    //string strSuffix = "";
                    //string _DateEditDefaultValue = _defaultValue.ToString();
                    //if (!string.IsNullOrEmpty(_DateEditDefaultValue))
                    //{
                    //    bool b = int.TryParse(_DateEditDefaultValue, out day);
                    //    if (!b)
                    //    {
                    //        string strNum = _DateEditDefaultValue.Substring(0, _DateEditDefaultValue.Length - 1);
                    //        int.TryParse(strNum, out day);
                    //        strSuffix = _DateEditDefaultValue.Substring(_DateEditDefaultValue.Length - 1, 1);
                    //    }
                    //}
                    //switch (strSuffix.ToLower())
                    //{
                    //    case "y":
                    //        _editItem.EditValue = DateTime.Now.AddYears(day);
                    //        break;
                    //    case "m":
                    //        _editItem.EditValue = DateTime.Now.AddMonths(day);
                    //        break;
                    //    case "d":
                    //        _editItem.EditValue = DateTime.Now.AddDays(day);
                    //        break;
                    //    case "h":
                    //        _editItem.EditValue = DateTime.Now.AddHours(day);
                    //        break;
                    //    case "f":
                    //        _editItem.EditValue = DateTime.Now.AddMinutes(day);
                    //        break;
                    //    case "s":
                    //        _editItem.EditValue = DateTime.Now.AddSeconds(day);
                    //        break;
                    //    default:
                    //        _editItem.EditValue = DateTime.Now.AddDays(day);
                    //        break;
                    //}
                }
                else
                {
                    _editItem.EditValue = _defaultValue;
                    //数值框无默认值默认设置成0
                    if (FLAG_SPINEDIT.Equals(_editType) && string.IsNullOrEmpty(_defaultValue.ToString().Trim()))
                    {
                        _editItem.EditValue = 0;
                    }
                }
            }
            return _editItem;
        }

        /// <summary>
        /// 根据 editType（控件类型） 创建对应编辑控件
        /// </summary>
        /// <returns>RepositoryItem</returns>
        private RepositoryItem GetEdit()
        {

            if (!string.IsNullOrEmpty(_editType))
            {
                if (FLAG_LOOKUPEDIT.Equals(_editType))
                {
                    RepositoryItemLookUpEdit edit = new RepositoryItemLookUpEdit();
                    edit.DisplayMember = _displayMember;
                    edit.ValueMember = _valueMember;


                    LookUpColumnInfo col = new LookUpColumnInfo();
                    col.Caption = "值";
                    col.FieldName = _valueMember;
                    col.Width = Convert.ToInt32(this._width * 0.30);
                    edit.Columns.Add(col);

                    col = new LookUpColumnInfo();
                    col.Caption = "名称";
                    col.FieldName = _displayMember;
                    col.Width = Convert.ToInt32(this._width * 0.70);
                    edit.Columns.Add(col);

                    edit.PopupWidthMode = DevExpress.XtraEditors.PopupWidthMode.ContentWidth;
                    edit.DropDownItemHeight = 20;

                    System.Drawing.Rectangle ScreenArea = System.Windows.Forms.Screen.GetWorkingArea(new System.Drawing.Point());
                    edit.PopupFormMinSize = new System.Drawing.Size(_width, (int)(ScreenArea.Height / 2));

                    if (_dataSource != null)
                    {
                        edit.DataSource = _dataSource.DefaultView;
                    }
                    _editControl = edit;
                }
                else if (FLAG_TEXTEDIT.Equals(_editType))
                {
                    RepositoryItemTextEdit edit = new RepositoryItemTextEdit();
                    _editControl = edit;
                }
                else if (FLAG_DATEEDIT.Equals(_editType))
                {
                    RepositoryItemDateEdit edit = new RepositoryItemDateEdit();
                    string dateFormatString = "yyyy-MM-dd";
                    if (!string.IsNullOrEmpty(_defaultDateFormat))
                    {
                        dateFormatString = _defaultDateFormat;

                        if ("yyyy,yyyy-MM".Contains(dateFormatString))//日期控件样式修改
                        {
                            edit.CalendarDateEditing = true;
                            edit.CalendarView = CalendarView.TouchUI;
                        }
                    }
                    edit.EditMask = dateFormatString;
                    edit.EditFormat.FormatString = dateFormatString;
                    edit.DisplayFormat.FormatString = dateFormatString;
                    edit.Mask.MaskType = MaskType.DateTimeAdvancingCaret;
                    _editControl = edit;

                }
                else if (FLAG_SPINEDIT.Equals(_editType))
                {
                    RepositoryItemSpinEdit edit = new RepositoryItemSpinEdit();
                    edit.Increment = 10;
                    //edit.MaxValue = int.MaxValue;
                    //edit.MinValue = int.MinValue;
                    edit.AllowMouseWheel = true;

                    _editControl = edit;
                }
                else if (FLAG_SEARCHLOOKUPEDIT.Equals(_editType))//可搜索下拉框 add
                {

                    //GridColumn gdCol = new GridColumn();
                    //gdCol.Caption = "值";
                    //gdCol.FieldName = _valueMember;
                    //gdCol.Name = "gdCol1";
                    //gdCol.VisibleIndex = 0;
                    //gdCol.Width = 50;


                    //GridColumn gdCol2 = new GridColumn();
                    //gdCol2.Caption = "名称";
                    //gdCol2.FieldName = _displayMember;
                    //gdCol2.Name = "gdCol2";
                    //gdCol2.VisibleIndex = 1;



                    DevExpress.XtraGrid.Views.Grid.GridView gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
                    for (int i = 0; i < _dataSource.Columns.Count; i++)
                    {
                        DataColumn itemCol = _dataSource.Columns[i];
                        if (!itemCol.ColumnName.ToUpper().StartsWith("HIDE_"))
                        {
                            GridColumn gdCol = new GridColumn();
                            gdCol.Caption = itemCol.ColumnName;
                            gdCol.FieldName = itemCol.ColumnName;
                            gdCol.Name = "gdCol" + i.ToString();
                            gdCol.VisibleIndex = 0;
                            // gdCol.Width = 50;

                            gridView1.Columns.Add(gdCol);
                        }
                    }


                    //  gridView1.Columns.AddRange(new GridColumn[] { gdCol, gdCol2 });
                    gridView1.Name = "gridView1";
                    gridView1.OptionsView.ShowGroupPanel = false;
                    gridView1.OptionsCustomization.AllowFilter = false;

                    RepositoryItemSearchLookUpEdit edit = new RepositoryItemSearchLookUpEdit();
                    //  edit.PopupFormSize = new System.Drawing.Size(250, 300);
                    edit.NullText = "";
                    edit.DataSource = _dataSource;

                    edit.DisplayMember = _displayMember;
                    edit.ValueMember = _valueMember;
                    edit.Name = "reposItemSearchLookUpEdit1";
                    edit.PopupView = gridView1;
                    edit.ShowClearButton = true;

                    edit.PopupWidthMode = DevExpress.XtraEditors.PopupWidthMode.ContentWidth;


                    _editControl = edit;

                }
            }

            return _editControl;
        }

        /// <summary>
        /// 根据条件过滤控件数据显示内容
        /// </summary>
        /// <param name="columnName">控件绑定列名</param>
        /// <param name="value">值</param>
        public void DataFilterByValue(string columnName, string value)
        {
            if (_editControl != null && (_editControl is RepositoryItemLookUpEdit || _editControl is RepositoryItemSearchLookUpEdit))
            {
                if (_dataSource != null)
                {
                    if (value.Equals("*")||value.Equals(_defaultValue.ToString().Trim()))
                    {
                        _dataSource.DefaultView.RowFilter = "";
                        _editItem.EditValue = value;
                    }
                    else
                    {
                        string filter = columnName + " = '" + value + "' or " + columnName + " = '*'";
                        _dataSource.DefaultView.RowFilter = filter;
                        DataRow[] dr = _dataSource.Select(filter);

                        if (dr.Length > 0)
                        {
                            _editItem.EditValue = dr[0][_valueMember];
                        }
                        else
                        {
                            _editItem.EditValue = "*";
                        }
                    }
                }
                if (_editControl is RepositoryItemLookUpEdit)
                {
                    ((RepositoryItemLookUpEdit)_editControl).DataSource = _dataSource.DefaultView;
                }
                else if (_editControl is RepositoryItemSearchLookUpEdit)
                {
                    ((RepositoryItemSearchLookUpEdit)_editControl).DataSource = _dataSource.DefaultView;
                }
            }
        }

        /// <summary>
        /// 值更改事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void EditItem_EditValueChanged(object sender, EventArgs e)
        {
            _editValue = _editItem.EditValue;
            if (_editValue == null)
            {
                if (string.IsNullOrEmpty(_defaultValue.ToString().Trim()))
                {
                    _editValue = "*";
                }
                else
                {
                    _editValue = _defaultValue.ToString().Trim();
                }
            }
            if (_nextEdit != null)
                _nextEdit.DataFilterByValue(_valueMember, _editValue.ToString());

            if (_editControl is RepositoryItemLookUpEdit)
                _displayValue = _editControl.GetDisplayText(_editValue);
            else if (_editControl is RepositoryItemSearchLookUpEdit)
                _displayValue = _editControl.GetDisplayText(_editValue);


        }
    }
}
