﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System.IO;
using NM_Service.NMService;

namespace PlatCommon.Common
{
    public class TreatmentRoom
    {
        /// <summary>
        /// 插入治疗表函数 
        /// </summary>
        /// <returns></returns>
        public static int GetTreatmentRoomAdd(string ls_treate_appoints, string pid, string tname, string tpatient_source, string tvid, string ldt_visit_date, string vno, string clinicno, string orderedBy, string perfromBy, string ls_chargetype, string tamount, string tdosage,
            string titemclass, string titemcode, string titemname, string dosageUnit, string risk_flag, ref Dictionary<string, string> idc)
        {
            //ls_treate_appoints 治疗号序列，对应 outp_orders 的appoint_no
            //tpatient_source 为患者来源 门诊1 住院2 tvid 门诊0 住院 是住院次数
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            // Dictionary<string, string> idc = new Dictionary<string, string>();
            if (string.IsNullOrEmpty(ls_treate_appoints))
            {
                XtraMessageBox.Show("treat_appoints_serial_no.NextVal序列错误", "提示");
                return -1;
            }
            if (string.IsNullOrEmpty(ldt_visit_date))
            {
                ldt_visit_date = "null";
            }
            else
            {
                ldt_visit_date = "to_date('" + ldt_visit_date + "','yyyy-mm-dd hh24:mi:ss')";
            }
            if (string.IsNullOrEmpty(tamount))
            {
                XtraMessageBox.Show("数量不能为空", "提示");
                return -1;
            }
            if (string.IsNullOrEmpty(tdosage))
            {
                XtraMessageBox.Show("单次剂量不能为空", "提示");
                return -1;
            }
            string tsysdate = spc.GetSysDate().ToString("yyyy-MM-dd HH:mm:ss");
            tsysdate = "to_date('" + tsysdate + "','yyyy-mm-dd hh24:mi:ss')";
            StringBuilder sb = new StringBuilder();
            sb.Append(" INSERT INTO TREATE.TREAT_APPOINTS(treat_no, patient_id, name, patient_source, visit_id, visit_date,treate_status,");
            sb.Append(" visit_no, clinic_no, apply_doctor_no, apply_time, perfrom_by, charge_type, item_code,dept_code)");
            sb.Append(" values('" + ls_treate_appoints + "', '" + pid + "', '" + tname + "', '" + tpatient_source + "', '" + tvid + "', " + ldt_visit_date + ", '" + 1 + "', '");
            sb.Append( vno + "', '" + clinicno + "', '" + SystemParm.LoginUser.USER_NAME + "', " + tsysdate + ", '" + perfromBy + "', '" + ls_chargetype + "','" + titemcode + "','" + orderedBy + "')");
            idc.Add(sb.ToString(), "插入TREATE.TREAT_APPOINTS失败!");
            decimal ldc_amount = decimal.Parse(tamount);
            decimal ldc_dosage = decimal.Parse(tdosage);
            int ldc_perform_times = int.Parse(Math.Ceiling(ldc_amount / ldc_dosage).ToString());
            int li_schedule_sno = 0;
            if (tvid.Equals("0")) //门诊插入明细
            {
                for (int j = 0; j < ldc_perform_times; j++)
                {
                    li_schedule_sno++;
                    StringBuilder sb1 = new StringBuilder();
                    sb1.Append("INSERT INTO  TREATE.PAT_TREAT_SCHEDULE(treat_no,item_no,schedule_sno,patient_id,patient_source,clinic_no,item_class,item_code");
                    sb1.Append(",item_name,therape_dosage,dosage_unit,treat_dept,execute_status,charge_indicator,risk_flag)");
                    sb1.Append("values('" + ls_treate_appoints + "','1','" + li_schedule_sno.ToString() + "','" + pid + "','" + tpatient_source + "','" + clinicno + "','" + titemclass + "','" + titemcode);
                    sb1.Append("','" + titemname + "','" + tdosage + "','" + dosageUnit + "','" + perfromBy + "','0','0','" + risk_flag + "')");
                    idc.Add(sb1.ToString(), "插入TREATE.PAT_TREAT_SCHEDULE失败！");
                }
            }
            else //住院插入明细
            {

            }

            //string rev = spc.SaveTable(idc);
            //if(!string.IsNullOrEmpty(rev))
            //{
            //    XtraMessageBox.Show(rev, "提示");
            //    return -1;
            //}
            return 0;
        }
        /// <summary>
        /// 删除治疗表函数
        /// </summary>
        /// <returns></returns>
        public static int GetTreatmentRoomDel(string ls_treate_appoints, string tvid, ref Dictionary<string, string> idc)
        {
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            // Dictionary<string, string> idc = new Dictionary<string, string>();
            string sql = "delete from TREATE.TREAT_APPOINTS where treat_no = '" + ls_treate_appoints + "'";
            idc.Add(sql, "删除TREATE.TREAT_APPOINTS失败!");
            if (tvid.Equals("0")) //门诊删除明细
            {
                sql = "delete from TREATE.PAT_TREAT_SCHEDULE where  treat_no = '" + ls_treate_appoints + "'";
                idc.Add(sql, "删除TREATE.PAT_TREAT_SCHEDULE失败!");
            }
            else
            {
                sql = "delete from treate.treat_appoint_items where  treat_no = '" + ls_treate_appoints + "'";
                idc.Add(sql, "删除treate.treat_appoint_items失败!");
            }
            //string rev = spc.SaveTable(idc);
            //if (!string.IsNullOrEmpty(rev))
            //{
            //    XtraMessageBox.Show(rev, "提示");
            //    return -1;
            //}
            return 0;
        }
    }
}
