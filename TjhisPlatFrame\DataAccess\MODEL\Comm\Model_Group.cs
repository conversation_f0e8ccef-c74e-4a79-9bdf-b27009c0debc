﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using System.ComponentModel;
namespace Model
{

    [DataContract]
    public class MODEL_GROUP : NotificationObject
    {

        /// <summary>
        /// MODEL_CODE
        /// </summary>		
        private string _model_code;
        [DataMember]
        public string M<PERSON><PERSON>_CODE
        {
            get { return _model_code; }
            set
            {
                if (_model_code != value)
                {
                    _model_code = value;
                    this.RaisePropertyChanged("MODEL_CODE");
                }
            }
        }
        /// <summary>
        /// GROUP_CODE
        /// </summary>		
        private string _group_code;
        [DataMember]
        public string GROUP_CODE
        {
            get { return _group_code; }
            set
            {
                if (_group_code != value)
                {
                    _group_code = value;
                    this.RaisePropertyChanged("GROUP_CODE");
                }
            }
        }
        /// <summary>
        /// GROUP_NAME
        /// </summary>		
        private string _group_name;
        [DataMember]
        public string GROUP_NAME
        {
            get { return _group_name; }
            set
            {
                if (_group_name != value)
                {
                    _group_name = value;
                    this.RaisePropertyChanged("GROUP_NAME");
                }
            }
        }
        /// <summary>
        /// GROUP_SORT_NO
        /// </summary>		
        private decimal _group_sort_no;
        [DataMember]
        public decimal GROUP_SORT_NO
        {
            get { return _group_sort_no; }
            set
            {
                if (_group_sort_no != value)
                {
                    _group_sort_no = value;
                    this.RaisePropertyChanged("GROUP_SORT_NO");
                }
            }
        }
        /// <summary>
        /// ICON
        /// </summary>		
        private string _icon;
        [DataMember]
        public string ICON
        {
            get { return _icon; }
            set
            {
                if (_icon != value)
                {
                    _icon = value;
                    this.RaisePropertyChanged("ICON");
                }
            }
        }

    }
}