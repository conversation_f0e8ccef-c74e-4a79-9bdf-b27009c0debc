﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace Tjhis_Mq_Service
{
    internal class Tjhis_Xml_ConfigAccess
    {
        private XmlDocument _doc;
        private string _filePath;
        public Tjhis_Xml_ConfigAccess()
        {
            if (!File.Exists(Environment.CurrentDirectory + "\\RabbitMQ_Config.xml"))
            {
                throw new Exception($"RabbitMQ_Config.xml doesn't exsit in {Environment.CurrentDirectory + "\\RabbitMQ_Config.xml"}!");
            }
            else
            {
                _filePath = Environment.CurrentDirectory + "\\RabbitMQ_Config.xml";
                _doc = new XmlDocument();
            }
        }

        public Tjhis_Xml_ConfigAccess(string path)
        {
            if (!File.Exists(path))
            {
                throw new Exception($"RabbitMQ_Config.xml doesn't exsit in {path}!");
            }
            else
            {
                _filePath = path;
                _doc = new XmlDocument();
            }
        }
        public string ReadFactoryXml(string node)
        {
            string result = "";
            //加载要读取的XML
            _doc.Load(_filePath);
            //获得根节点
            XmlElement AFAC = _doc.DocumentElement;

            //获得某一类特定的子节点
            XmlNodeList xnl = AFAC.SelectNodes($"/RabbitMQ/Factory/{node}");


            foreach (XmlNode item in xnl)
            {
                result = item.InnerText;
            }
            return result;
        }

    }
}
