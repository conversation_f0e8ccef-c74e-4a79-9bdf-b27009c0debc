﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace PlatCommon.Common
{
    /// <summary>
    /// 自动ID处理器
    /// </summary>
    internal class AutoSettingIDProvider
    {
        /// <summary>
        /// 自动ID信息
        /// </summary>
        public class AutoSettingIDInfo
        {
            public int SerialNo { get; set; } = 0;
            public string TypeName { get; set; } = "";
            public string IDSql { get; set; } = "";
            public string StartValue { get; set; } = "";
            public int ValueLength { get; set; } = 0;
            public int Upgrade { get; set; } = 0;
            public int CurrentValue { get; set; } = 0;
            public string HisUnitCode { get; set; } = "";
        }

        /// <summary>
        /// 日志文件名
        /// </summary>
        private const string LOG_FILE_NAME = "AutoSettingIDProvider";

        /// <summary>
        /// 读取ID或序号，不回写更新ID
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <param name="hisUnitCode">医院编码</param>
        /// <param name="sCurrentIDValue">输出类型，格式化后的当前值</param>
        /// <param name="iCurrentIDValue">输出类型，当前值整数值</param>
        /// <param name="db">默认参数：用于控制事务的db，可为null</param>
        /// <param name="hasParams">可选参数：用于生成SQL语句结果前缀的参数，可为空</param>
        /// <param name="sqlFirst">可选参数：true为SQL语句结果前置， false为固定前缀前置，默认为true</param>
        /// <returns>true成功，false失败</returns>
        public bool GetId(string typeName, string hisUnitCode, out string sCurrentIDValue, out int iCurrentIDValue, Utility.OracleODP.OracleBaseClass db = null, Hashtable hasParams = null, bool sqlFirst = true)
        {
            sCurrentIDValue = "";
            iCurrentIDValue = 0;
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();

            try
            {
                //获得数据表中保存的生成ID信息
                bool bret = getAutoSettingIDInfo(typeName, hisUnitCode, out AutoSettingIDInfo idInfo, db);
                if (!bret)
                {
                    Utility.LogFile.WriteLogAuto("查询AUTO_SETTING_ID表失败", LOG_FILE_NAME);
                    return false;
                }

                iCurrentIDValue = idInfo.CurrentValue;

                //获得根据规则格式化后的ID值
                bret = formatID(idInfo, hasParams, sqlFirst, out sCurrentIDValue);
                if (!bret)
                {
                    Utility.LogFile.WriteLogAuto("格式化当前值失败", LOG_FILE_NAME);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                //写日志
                string errMsg = getErrorMsg(ex, MethodBase.GetCurrentMethod());
                Utility.LogFile.WriteLogAuto(errMsg, LOG_FILE_NAME);
                return false;
            }
        }

        /// <summary>
        /// 得到更新Id值的sql语句，方法会返回update语句用于更新id值，一般用于调用者在保存前执行更新Id的SQL语句
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <param name="hisUnitCode">医院编码</param>
        /// <param name="sCurrentIDValue">输出：格式化后的当前ID值</param>
        /// <param name="iCurrentIDValue">输出：当前ID值整数变量</param>
        /// <param name="sNextIDValue">输出：执行更新操作后的计算出的格式化后的新值</param>
        /// <param name="iNextIDValue">输出：执行更新操作后的计算出的新值</param>
        /// <param name="updateSql">输出：更新自动ID表的语句，用于调用者执行更新自动ID表</param>
        /// <param name="db">默认参数：用于控制事务的db，可为null</param>
        /// <param name="hasParams">可选参数：用于生成SQL语句结果前缀的参数，可为空</param>
        /// <param name="sqlFirst">可选参数：true为SQL语句结果前置， false为固定前缀前置，默认为true</param>
        /// <returns>true成功，false失败</returns>
        public bool GetUpdateIdSql(string typeName, string hisUnitCode, out string sCurrentIDValue, out int iCurrentIDValue, out string sNextIDValue, out int iNextIDValue, out string updateSql, Utility.OracleODP.OracleBaseClass db = null, Hashtable hasParams = null, bool sqlFirst = true)
        {
            updateSql = "";
            iCurrentIDValue = 0;
            sCurrentIDValue = "";
            iNextIDValue = 0;
            sNextIDValue = "";
            try
            {
                //得到自动id的信息
                AutoSettingIDInfo idInfo = new AutoSettingIDInfo();
                if (!getAutoSettingIDInfo(typeName, hisUnitCode, out idInfo, db))
                {
                    Utility.LogFile.WriteLogAuto("查询AUTO_SETTING_ID表失败", LOG_FILE_NAME);
                    return false;
                }

                iCurrentIDValue = idInfo.CurrentValue;

                //格式化当前值
                if (!formatID(idInfo, hasParams, sqlFirst, out sCurrentIDValue))
                {
                    Utility.LogFile.WriteLogAuto("格式化当前值失败", LOG_FILE_NAME);
                    return false;
                }

                //获得update语句
                bool bret = getUpdateIdSqlStatement(typeName, hisUnitCode, iCurrentIDValue, out sNextIDValue, out iNextIDValue, out updateSql, db, hasParams);
                return bret;
            }
            catch (Exception ex)
            {
                //写日志
                string errMsg = getErrorMsg(ex, MethodBase.GetCurrentMethod());
                Utility.LogFile.WriteLogAuto(errMsg, LOG_FILE_NAME);
                return false;
            }
        }

        /// <summary>
        /// 回写Id，该方法会计算新值自动更新Id，保存到AUTO_SETTING_ID表中，一般用于保存前更新Id
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <param name="hisUnitCode">医院编码</param>
        /// <param name="iCurrentIDValue">输出类型：当前值</param>
        /// <param name="sCurrentIDValue">输出类型：格式化后的当前ID值</param>
        /// <param name="sNexIDValue">输出类型：执行更新操作后的计算出的格式化后的新值</param>
        /// <param name="iNextIDValue">输出类型：执行更新操作后的计算出的新值</param>
        /// <param name="db">用于控制事务的db，必选项</param>
        /// <param name="hasParams">可选参数：用于生成SQL语句结果前缀的参数，可为空</param>
        /// <param name="sqlFirst">可选参数：true为SQL语句结果前置， false为固定前缀前置，默认为true</param>
        /// <returns>true成功，false失败</returns>
        public bool UpdateId(string typeName, string hisUnitCode, out string sCurrentIDValue, out int iCurrentIDValue, out string sNexIDValue, out int iNextIDValue, Utility.OracleODP.OracleBaseClass db, Hashtable hasParams = null, bool sqlFirst = true)
        {
            sNexIDValue = "";
            iNextIDValue = 0;
            sCurrentIDValue = "";
            iCurrentIDValue = 0;
            try
            {
                AutoSettingIDInfo idInfo = new AutoSettingIDInfo();
                //重新从数据库中读取对应的ID当前值
                if (!getAutoSettingIDInfo(typeName, hisUnitCode, out idInfo, db))
                {
                    Utility.LogFile.WriteLogAuto("查询AUTO_SETTING_ID表失败", LOG_FILE_NAME);
                    return false;
                }

                iCurrentIDValue = idInfo.CurrentValue;

                //格式化当前值
                if (!formatID(idInfo, hasParams, sqlFirst, out sCurrentIDValue))
                {
                    Utility.LogFile.WriteLogAuto("格式化当前值失败", LOG_FILE_NAME);
                    return false;
                }

                if (!writeIDValue(typeName, hisUnitCode, idInfo.CurrentValue, out sNexIDValue, out iNextIDValue, hasParams,db ))
                {
                    Utility.LogFile.WriteLogAuto("回写ID失败", LOG_FILE_NAME);
                    return false;
                }
                return true;
            }
            catch (Exception ex)
            {
                //写日志
                string errMsg = getErrorMsg(ex, MethodBase.GetCurrentMethod());
                Utility.LogFile.WriteLogAuto(errMsg, LOG_FILE_NAME);
                return false;
            }
        }

        #region 私有方法

        /// <summary>
        /// 查询AUTO_SETTING_ID表获得指定类型名称的自动ID的信息(不回写ID)
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <param name="hisUnitCode">医院编码</param>
        /// <param name="idInfo">输出类型参数 自动ID信息</param>
        /// <param name="db">用于事务控制的db，可选项</param>
        /// <returns>true成功，false失败</returns>
        private bool getAutoSettingIDInfo(string typeName, string hisUnitCode, out AutoSettingIDInfo idInfo, Utility.OracleODP.OracleBaseClass db = null)
        {
            idInfo = new AutoSettingIDInfo();
            try
            {
                DataTable dataTable = new DataTable();
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                //从AUTO_SETTING_ID 中读取   
                string strSQLNo = $"SELECT * FROM AUTO_SETTING_ID WHERE TYPE_NAME = '{typeName}' AND HIS_UNIT_CODE  = '{hisUnitCode}'  ";
                string strSQL = strSQLNo +" for update  ";
               
                if (db != null)
                {
                    dataTable = spc.GetListUseDB(strSQL, db).Tables[0]; //带事务 
                }
                else
                {
                    dataTable = spc.GetDataBySql(strSQLNo, "AUTO_SETTING_ID").Tables[0]; //不带事务 
                }

                if (dataTable.Rows.Count <= 0)
                {
                    Utility.LogFile.WriteLogAuto("查询AUTO_SETTING_ID表未找到匹配的TYPE_NAME项目", LOG_FILE_NAME);
                    return false;
                }

                DataRow dr = dataTable.Rows[0];
                string sID_START_VALUE = "",
                   sID_SQL = "";
                int iID_CURRENTLY_VALUE = 0,
                    iID_LENGTH = 0,
                    iID_UPGRADE = 0;

                sID_START_VALUE = getStringValueDBFiledValue(dr["ID_START_VALUE"]);
                sID_SQL = getStringValueDBFiledValue(dr["ID_SQL"]);
                iID_CURRENTLY_VALUE = getNumberValueDBFieldValue(dr["ID_CURRENTLY_VALUE"]);
                iID_LENGTH = getNumberValueDBFieldValue(dr["ID_LENGTH"]);
                iID_UPGRADE = getNumberValueDBFieldValue(dr["ID_UPGRADE"]);

                idInfo.SerialNo = getNumberValueDBFieldValue(dr["SERIAL_NO"]);
                idInfo.TypeName = getStringValueDBFiledValue(dr["TYPE_NAME"]);
                idInfo.HisUnitCode = getStringValueDBFiledValue(dr["HIS_UNIT_CODE"]);
                idInfo.IDSql = sID_SQL;
                idInfo.StartValue = sID_START_VALUE;
                idInfo.CurrentValue = iID_CURRENTLY_VALUE;
                idInfo.ValueLength = iID_LENGTH;
                idInfo.Upgrade = iID_UPGRADE;
                return true;
            }
            catch (Exception ex)
            {
                //写日志
                string errMsg = getErrorMsg(ex, MethodBase.GetCurrentMethod());
                Utility.LogFile.WriteLogAuto(errMsg, LOG_FILE_NAME);
                return false;
            }
        }

        /// <summary>
        /// 获得根据规则格式化后的ID值
        /// </summary>
        /// <param name="idInfo">自动ID信息</param>
        /// <param name="sIDValue">输出类型参数，符合格式的ID值字符串</param>
        /// <returns>true成功，false失败</returns>
        private bool formatID(AutoSettingIDInfo idInfo, Hashtable hasParams, bool sqlFirst, out string sIDValue)
        {
            sIDValue = "";
            try
            {
                //如果有sql语句查询结果作为前缀
                string sID_SQL_VALUE = "";//sql前缀的生成结果
                if (!string.IsNullOrEmpty(idInfo.IDSql))
                {
                    string strSQL = hasParams != null ? getSqlTextByParam(idInfo.IDSql, hasParams) : idInfo.IDSql;//如果有sql参数则转换sql语句
                    NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                    sID_SQL_VALUE = spc.GetSingleValue(strSQL);
                }

                int iIDValue = idInfo.CurrentValue; //当前值
                string sID_START_VALUE = idInfo.StartValue;//前缀
                string sID_CURRENTLY_VALUE = iIDValue.ToString();
                if (sID_CURRENTLY_VALUE.Length < idInfo.ValueLength) //如果当前值的长度小于规定的长度，则左侧填充0
                {
                    sID_CURRENTLY_VALUE = iIDValue.ToString().PadLeft(idInfo.ValueLength, '0');
                }
                if (sqlFirst)
                {
                    sIDValue = sID_SQL_VALUE + sID_START_VALUE + sID_CURRENTLY_VALUE; //拼成 SQL生成前缀 + 固定前缀 + 变量
                }
                else
                {
                    sIDValue = sID_START_VALUE + sID_SQL_VALUE + sID_CURRENTLY_VALUE; //拼成 固定前缀 + SQL生成前缀 + 变量
                }
                return true;
            }
            catch (Exception ex)
            {
                //写日志
                string errMsg = getErrorMsg(ex, MethodBase.GetCurrentMethod());
                Utility.LogFile.WriteLogAuto(errMsg, LOG_FILE_NAME);
                return false;
            }
        }

        /// <summary>
        /// 获得数据库字段的数值类型的值
        /// </summary>
        /// <param name="dbValue">DataRow读取出来的数据库字段值</param>
        /// <returns>true成功，false失败</returns>
        private int getNumberValueDBFieldValue(object dbValue)
        {
            int iValue = 0;
            string sValue = dbValue.ToString().Trim().Length > 0 ? dbValue.ToString().Trim() : "0";
            bool _ = int.TryParse(sValue, out iValue);
            return iValue;
        }

        /// <summary>
        /// 获得数据库字段的文本字符类型的值
        /// </summary>
        /// <param name="dbValue">DataRow读取出来的数据库字段值</param>
        /// <returns>true成功，false失败</returns>
        private string getStringValueDBFiledValue(object dbValue)
        {
            return dbValue.ToString().Trim();
        }

        /// <summary>
        /// 计算新值
        /// </summary>
        /// <param name="iIDValue">原值</param>
        /// <param name="valueLength">变量长度范围</param>
        /// <param name="upgrade">是否允许升位</param>
        /// <param name="iNewIDValue">输出类型，新值</param>
        /// <returns>true成功，false失败</returns>
        private bool calculationNewID(int iIDValue, int valueLength, int upgrade, out int iNewIDValue)
        {
            iNewIDValue = 0;
            try
            {
                iNewIDValue = iIDValue + 1;//当前值加一

                //如果设置了变量长度范围
                if (valueLength > 0)
                {
                    //如果加一后的长度超过了长度范围，并且不允许升位
                    if (iNewIDValue.ToString().Trim().Length > valueLength && upgrade != 1) //upgrade=0或null 不允许升位； upgrade=1 允许升位（即不受长度范围限制）
                    {
                        iNewIDValue = 1;
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                //写日志
                string errMsg = getErrorMsg(ex, MethodBase.GetCurrentMethod());
                Utility.LogFile.WriteLogAuto(errMsg, LOG_FILE_NAME);
                return false;
            }
        }

        /// <summary>
        /// 得到更新id值的sql语句，只需要提供当前值，方法会返回计算新id后的update语句
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <param name="hisUnitCode">医院编码</param>
        /// <param name="iCurrentIDValue">当前值数字</param>        
        /// <param name="iNewIDValue">输出类型：计算后的新值</param>
        /// <param name="updateSql">输出值：更新自动ID表的update语句</param>
        /// <param name="db">用于控制事务的db</param>
        /// <returns>true成功，false失败</returns>
        private bool getUpdateIdSqlStatement(string typeName, string hisUnitCode, int iCurrentIDValue, out string sNewIDValue, out int iNewIDValue, out string updateSql, Utility.OracleODP.OracleBaseClass db, Hashtable hasParams, bool sqlFirst = true)
        {
            updateSql = "";
            iNewIDValue = 0;
            sNewIDValue = "";
            try
            {
                //得到自动id的信息
                AutoSettingIDInfo idInfo = new AutoSettingIDInfo();
                if (!getAutoSettingIDInfo(typeName, hisUnitCode, out idInfo, db))
                {
                    Utility.LogFile.WriteLogAuto("查询AUTO_SETTING_ID表失败", LOG_FILE_NAME);
                    return false;
                }

                //计算新id
                if (!calculationNewID(iCurrentIDValue, idInfo.ValueLength, idInfo.Upgrade, out iNewIDValue))
                {
                    Utility.LogFile.WriteLogAuto("计算新ID失败", LOG_FILE_NAME);
                    return false;
                }

                idInfo.CurrentValue = iNewIDValue;

                //格式化新id
                if (!formatID(idInfo, hasParams, sqlFirst, out sNewIDValue))
                {
                    Utility.LogFile.WriteLogAuto("格式化新ID失败", LOG_FILE_NAME);
                    return false;
                }

                //拼凑update语句
                updateSql = $"UPDATE AUTO_SETTING_ID SET ID_CURRENTLY_VALUE = {iNewIDValue} WHERE TYPE_NAME = '{typeName}' AND  HIS_UNIT_CODE = '{hisUnitCode}'";
                return true;
            }
            catch (Exception ex)
            {
                //写日志
                string errMsg = getErrorMsg(ex, MethodBase.GetCurrentMethod());
                Utility.LogFile.WriteLogAuto(errMsg, LOG_FILE_NAME);
                return false;
            }
        }

        /// <summary>
        /// 回写ID，只需要提供当前值，方法会自动计算+1，保存到AUTO_SETTING_ID表中
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <param name="iCurrentIDValue">整数类型的当前值</param>
        /// <param name="hisUnitCode">医院编码</param>
        /// <param name="db">用于事务控制的db</param>
        /// <returns>true成功；false失败</returns>
        private bool writeIDValue(string typeName, string hisUnitCode, int iCurrentIDValue, out string sNextIDValue, out int iNextIDValue,Hashtable hasParams, Utility.OracleODP.OracleBaseClass db = null)
        {
            sNextIDValue = "";
            iNextIDValue = 0;
            try
            {
                //先得到更新的update语句
                bool bret = this.getUpdateIdSqlStatement(typeName, hisUnitCode, iCurrentIDValue, out sNextIDValue, out iNextIDValue, out string updateSQL, db, hasParams);
                if (!bret)
                    return false;

                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                var dict = new Dictionary<string, string> { { updateSQL, $"更新AutoSettingID表失败，更新值为{iNextIDValue}！" } };

                string sret = "";
                if (db != null) //事务由外部控制
                {
                    sret = spc.SaveExcSql(dict, db);
                }
                else
                {
                    sret = spc.SaveTable(dict);
                }
                return string.IsNullOrEmpty(sret);
            }
            catch (Exception ex)
            {
                //写日志
                string errMsg = getErrorMsg(ex, MethodBase.GetCurrentMethod());
                Utility.LogFile.WriteLogAuto(errMsg, LOG_FILE_NAME);
                return false;
            }
        }

        /// <summary>
        /// 获取SQL语句
        /// </summary>
        /// <returns></returns>
        private static string getSqlTextByParam(string sql, System.Collections.Hashtable hasParams)
        {
            // 获取SQL原文
            string sqlText = sql;

            // 替换参数
            if (hasParams == null) return sqlText;

            string paramName = string.Empty;
            string paramValue = string.Empty;

            string paramName0 = string.Empty;
            string paramValue0 = string.Empty;

            foreach (System.Collections.DictionaryEntry entry in hasParams)
            {
                // 参数名
                paramName = "{" + entry.Key.ToString().ToUpper() + "}";         // 这种情况 变成 ''
                paramName0 = "[" + entry.Key.ToString().ToUpper() + "]";        // 这种情况 变成 原样

                // 参数值
                if (entry.Value == null)
                {
                    paramValue = "NULL";
                }
                else if (entry.Value.GetType().Equals(typeof(DateTime)))
                {
                    paramValue = "TO_DATE('" + (DateTime)(entry.Value) + "','yyyy-MM-dd HH24:mi:ss')";
                }
                else if (entry.Value.GetType().Equals(typeof(int)) || entry.Value.GetType().Equals(typeof(float)) || entry.Value.GetType().Equals(typeof(Decimal)))
                {
                    paramValue = entry.Value.ToString();
                }
                else if (entry.Value.ToString().Contains("'"))
                {
                    paramValue = entry.Value.ToString();
                }
                else
                {
                    paramValue = "'" + entry.Value.ToString() + "'";
                }

                paramValue0 = entry.Value == null ? "" : entry.Value.ToString();

                // 替换参数
                if (sqlText.IndexOf(paramName) >= 0)
                {
                    sqlText = sqlText.Replace(paramName, paramValue);
                }
                if (sqlText.IndexOf(paramName0) >= 0)
                {
                    sqlText = sqlText.Replace(paramName0, paramValue0);
                }
            }

            sqlText = sqlText.Replace(@" = ''", " IS NULL");

            return sqlText;
        }


        /// <summary>
        /// 获得异常的内容
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="methodBase">方法的信息</param>
        /// <returns>自定义格式的错误信息</returns>
        public static string getErrorMsg(Exception exception, System.Reflection.MethodBase methodBase)
        => $"发生异常：[{methodBase.DeclaringType.Name}][{methodBase.Name}] - {exception.Message} \n\r 堆栈信息：{exception.StackTrace}";

        /// <summary>
        /// 获得异常的内容
        /// </summary>
        /// <param name="message">错误字符串</param>
        /// <param name="methodBase">方法的信息</param>
        /// <returns>自定义格式的错误信息</returns>
        public static string getErrorMsg(string message, System.Reflection.MethodBase methodBase)
        => $"发生异常：[{methodBase.DeclaringType.Name}][{methodBase.Name}] - {message} ";

        #endregion 私有方法
    }
}
