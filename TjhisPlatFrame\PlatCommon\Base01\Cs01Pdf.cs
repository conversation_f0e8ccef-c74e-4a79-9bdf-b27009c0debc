﻿/*********************************************
* 命名空间 ：Tjhis.CsComm
* 类 名 称 ：Cs01Pdf
* 功能说明 ：PDF类
* 作    者 ：刘成刚
* 创建时间 ：2020-10-25 23:20:23
* 更 新 人 ：
* 更新时间 ：
* 更新说明 ：
* 版 本 号 ：v1.0.0.0
* CLR 版本 ：4.0.30319.42000
* 版权说明：北京天健源达 HIS基础产品研发部
/*********************************************/

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Spire.Pdf;

namespace PlatCommon.Base01
{
    /// <summary>
    /// PDF类
    /// </summary>
    public static class Cs01Pdf
    {
        /// <summary>
        /// 打印PDF文件
        /// </summary>
        /// <param name="strFileName">PDF文件名</param>
        /// <param name="strPrinterName">打印机</param>
        public static void PrintPdfFile(string strFileName, string strPrinterName = "")
        {
            if (!System.IO.File.Exists(strFileName))
                throw new Exception($"文件[{strFileName}]不存在！");

            try
            {
                PdfDocument document = new PdfDocument();

                document.LoadFromFile(strFileName);

                if (string.IsNullOrEmpty(strPrinterName))
                    document.PrintSettings.PrinterName = strPrinterName;

                document.Print();
                document.Dispose();

            }
            catch (Exception ex)
            {
                throw new Exception($"文件[{strFileName}]打印失败！\r\n错误信息：{ex.Message}");
            }
        }
    }

}
