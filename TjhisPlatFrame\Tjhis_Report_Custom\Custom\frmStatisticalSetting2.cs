﻿using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraReports.UI;
using DevExpress.XtraReports.UserDesigner;
using DevExpress.XtraTreeList.Nodes;
using PlatCommon.Common;
using PlatCommon.SysBase;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using Tjhis.Report.Custom.Base;
using Tjhis.Report.Custom.Common;
using Tjhis.Report.Custom.Srv;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmStatisticalSetting2 : frmReportBase
    {
        private srvStatisticalQuery _srv = new srvStatisticalQuery();

        /// <summary>
        /// 医院代码
        /// </summary>
        private string _hospitalCode = SystemParm.HisUnitCode;
        /// <summary>
        /// 应用程序名
        /// </summary>
        private string _appName;
        /// <summary>
        /// 报表记录集合
        /// </summary>
        private DataSet _dsTemplet;
        /// <summary>
        /// 报表参数集合
        /// </summary>
        private DataSet _dsReprotParam;
        /// <summary>
        /// 报表名称修改标识 true 修改;false 未修改
        /// </summary>
        private bool _changeNameFlag = false;
        const string SYSTEMMGR = "SYSTEMMGR";

        private DataTable dt_ReportClass = null;
        string focusedNodeId;//焦点节点ID
        //private string sCurrentReportId = string.Empty;   // 报表ID
        //private string sCurrentReportName = string.Empty; // 报表名

        //private string sCurrentReportClassId = string.Empty;// 报表分类ID
        //private string sCurrentRole = string.Empty;       //角色id
        //private string sCurrentRoleUserDefine = string.Empty; // 自定义角色
        private DataTable dtCurrentReportParam = null;         // 参数列表

        private DataTable dtPaperSize = null; // 纸张类型
        //private string sCurrentSql = string.Empty;              // 绑定sql
        //private bool bCurrentIsTable = false;                  // 是否是表格
        //private string sCurrentAppName = string.Empty;         // 模块名

        DataTable dtReportMainRecord = null;// 报表主记录
        DataRow drCurrentRowMainRecord = null; // 主记录的行
        bool isSaveNew = true; // 新建的报表是否保存
        string lastDictId = string.Empty; // 新建报表的ID，用于第一次保存时
        string _reportNo = string.Empty;
        public frmStatisticalSetting2()
        {
            InitializeComponent();
        }

        private void frmStatisticalSetting_Load(object sender, EventArgs e)
        {
            try
            {
                // 初始化报表分类
                dt_ReportClass = GetReportClass();
                InitReportClassControl(dt_ReportClass);

                dtCurrentReportParam = _srv.GetReportParamEmpty();

                //List<System.Drawing.Printing.PaperSize> paperSizeList = new List<System.Drawing.Printing.PaperSize>();
                //System.Drawing.Printing.PaperSize pkSize;
                //System.Drawing.Printing.PrintDocument printDoc = new System.Drawing.Printing.PrintDocument();
                //for (int i = 0; i < printDoc.PrinterSettings.PaperSizes.Count; i++)
                //{
                //    pkSize = printDoc.PrinterSettings.PaperSizes[i];
                //    paperSizeList.Add(pkSize);
                //}
                //comboPaperSize.Properties.DisplayMember = "PaperName";
                //comboPaperSize.Properties.ValueMember = "Kind";
                //comboPaperSize.Properties.PopupWidth = 300;
                //comboPaperSize.Properties.DataSource = paperSizeList;
                InitReportParam(string.Empty, string.Empty, string.Empty, string.Empty, false, dtCurrentReportParam, string.Empty, string.Empty, string.Empty, string.Empty, "A4, 纵向");
                InitTreeView(treeTempleList);
                PlatCommon.Common.XtraTreeListHelper.Initialize(treeTempleList);

                slueReportClass.Enabled = false;
                dtReportMainRecord = _srv.GetReportTableEmpty();

                //参数控件类型下拉框绑定
                lueEditType.ValueMember = "ITEM_CODE";
                lueEditType.DisplayMember = "ITEM_NAME";
                lueEditType.DataSource = srvStatisticalQuery.CreateDict(Const.CONTROL_TYPES);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
            

            ////源类型下拉框绑定 1（sql） 2（自定义 格式： 值1-名称1;值2-名称2 ）
            //lueSourceType.ValueMember = "ITEM_CODE";
            //lueSourceType.DisplayMember = "ITEM_NAME";
            //lueSourceType.DataSource = srvStatisticalQuery.CreateDict("1-SQL脚本;2-自定义选项");
            //应用程序名下拉框绑定
            //slueAppName.DataSource = _srv.GetApplications().Tables[0];
            //slueAppName.PopupFormSize = new Size(150, 300);

            //BindingTemple();
            //BindDiffReportClass();

            //repositoryItemCheckEdit1.CheckedChanged += RepositoryItemCheckEdit1_CheckedChanged;
        }
        private DataTable GetReportClass()
        {
            DataSet ds_ReportClass = _srv.GetTempletClassDict();
            if (ds_ReportClass != null && ds_ReportClass.Tables.Count > 0)
            {
                return ds_ReportClass.Tables[0];
            }
            else
            {
                return null;
            }
        }
        private void InitReportClassControl(DataTable dt)
        {
            slueReportClass.Properties.ValueMember = "CLASS_ID";
            slueReportClass.Properties.DisplayMember = "CLASS_NAME";
            slueReportClass.Properties.DataSource = dt;
        }

        private void InitReportParam(string reportID, string reportName, string role, string userDefineRole, 
            bool isTable, DataTable dtParam, string bindingSql, string appName, string classID, string memo, string pageSettings)
        {
            // 报表分类
            slueReportClass.EditValue = classID;

            // 初始化报表名
            textEdit_ReportName.Text = reportName;

            // 初始化角色
            beRole.Text = role;
            // 初始化自定义
            txtRole.Text = userDefineRole;

            // 是否表格
            checkEdit_IsTable.Checked = isTable;

            // 初始化报表参数gridview
            gcReportParam.DataSource = dtParam;

            // 初始化SQL脚本
            txtSql.Text = bindingSql;

            // 备注
            txt_memo.Text = memo;

            //页面设置
            txtPageSettings.Text = pageSettings;
        }

        /// <summary>
        /// 初始化树控件
        /// </summary>
        /// <param name="treeView"></param>
        public static void InitTreeView(DevExpress.XtraTreeList.TreeList treeView)
        {
            treeView.OptionsView.ShowColumns = false;
            treeView.OptionsView.ShowIndicator = false;
            treeView.OptionsView.ShowVertLines = false;
            treeView.OptionsView.ShowHorzLines = false;
            treeView.OptionsBehavior.Editable = false;
            treeView.OptionsView.EnableAppearanceEvenRow = true;
            treeView.OptionsSelection.EnableAppearanceFocusedRow = true;
            treeView.KeyFieldName = "DICT_ID";
            treeView.ParentFieldName = "PARENTID";
        }

        /// <summary>
        /// 加载树节点数据
        /// </summary>
        /// <returns></returns>
        public bool LoadTreeList()
        {
            if (string.Equals(this.AppCode, SYSTEMMGR))
            {
                // 系统管理可以看到所有模块，其他只能看到自己模块
                _dsTemplet = _srv.GetTempTreeList();
            }
            //else
            //{
            //    _dsTemplet = _srv.GetTempTreeListModule(this.AppCode);
            //}

            if (_dsTemplet == null || _dsTemplet.Tables[0].Rows.Count <= 0)
            {
                return false;
            }
            else
            {
                DataTable treeDataSource = _dsTemplet.Tables[0];

                treeTempleList.DataSource = treeDataSource.DefaultView;//绑定数据源
                if (!string.Equals(this.AppCode, SYSTEMMGR))
                    treeTempleList.ExpandAll();  //如果不是从系统管理进入展开所有节点
                else
                {
                    treeTempleList.ExpandToLevel(0);
                }
                if (!string.IsNullOrEmpty(focusedNodeId))
                {

                    TreeListNode n = treeTempleList.GetNodeList().Find(x => x.GetValue("DICT_ID").Equals(focusedNodeId));
                    if (n != null)
                        n.Selected = true;

                }

                return true;
            }
        }

        #region 获取参数
        /// <summary>
        /// 获取参数
        /// </summary>
        /// <returns></returns>
        private Hashtable getParams()
        {
            if (drCurrentRowMainRecord == null || string.IsNullOrEmpty(drCurrentRowMainRecord["DICT_ID"].ToString()))
                return null;

            // 向SQL语句传递参数
            Hashtable hasParam = _srv.AddSystemParam();

            DataTable dtParam = null;
            if (dtCurrentReportParam != null)
            {
                dtParam = dtCurrentReportParam;
            }
            else
            {
                DataSet ds = _srv.GetReportParamNew(drCurrentRowMainRecord["APP_NAME"].ToString(), drCurrentRowMainRecord["DICT_ID"].ToString());
                if (ds != null && ds.Tables.Count > 0)
                    dtParam = ds.Tables[0];

            }
            if (dtParam != null && dtParam.Rows.Count > 0)
            {
                foreach (DataRow dr in dtParam.Rows)
                {
                    //CONTROL_TYPES = "1-下拉框;2-文本框;3-日期框;4-数值框;5-下拉框(可检索)";
                    switch (dr["EDIT_TYPE"].ToString())
                    {
                        case "3":
                            hasParam.Add(dr["PARAM_NAME"].ToString(), DateTime.Now);
                            break;
                        case "4":
                            hasParam.Add(dr["PARAM_NAME"].ToString(), 0);
                            break;
                        default:
                            hasParam.Add(dr["PARAM_NAME"].ToString(), "0");
                            break;
                    }
                }
            }
            return hasParam;
        }
        #endregion

        private int SaveReportParam()
        {
            int ret = -1;
            dgvReportParam.CloseEditor();
            dgvReportParam.UpdateCurrentRow();
            DataTable dtChange = dtCurrentReportParam.GetChanges();
            if (dtChange != null && dtChange.Rows.Count > 0)
            {
                if (dtCurrentReportParam.DataSet != null)
                    dtCurrentReportParam.DataSet.Tables.RemoveAt(0);
                //ret = _srv.SaveData(dtCurrentReportParam.DataSet);
                ret = _srv.SaveTables(new DataTable[] { dtCurrentReportParam });
            }
            else
            {
                ret = 1;
            }
            return ret;
        }

        private void Save()
        {
            //dgvTemple.CloseEditor();
            //dgvTemple.UpdateCurrentRow();
            if (_dsTemplet.HasChanges())
            {
                _srv.SaveData(_dsTemplet);
                DataRow drFocused = treeTempleList.GetFocusedDataRow();


                if (drFocused != null && drFocused["APP_NAME"] != null)
                {
                    //FocuseIndex = treeTempleList.FocusedRowHandle;
                    string strAppNameOld = "";
                    string strAppNameNew = drFocused["APP_NAME"].ToString();
                    if (_dsReprotParam.Tables[0].Rows.Count > 0)
                    {
                        strAppNameOld = _dsReprotParam.Tables[0].Rows[0]["APP_NAME"].ToString();
                        if (strAppNameOld != strAppNameNew && strAppNameOld != "")
                        {
                            for (int i = 0; i < _dsReprotParam.Tables[0].Rows.Count; i++)
                            {
                                _dsReprotParam.Tables[0].Rows[i]["APP_NAME"] = strAppNameNew;
                            }
                            SaveReportParam();
                        }
                    }
                    if (strAppNameOld != strAppNameNew && strAppNameOld != "")
                    {
                        //表格类报表
                        if (drFocused["IS_TABLE"] != null && drFocused["IS_TABLE"].ToString() == "1")
                        {
                            _srv.UpdateReportConfigByID(strAppNameOld, strAppNameNew, drFocused["DICT_ID"].ToString());
                        }
                    }
                }

                //BindingTemple();
            }
            _changeNameFlag = false;
        }


        string roleStr = string.Empty;

        private void txtRole_EditValueChanging(object sender, DevExpress.XtraEditors.Controls.ChangingEventArgs e)
        {
            //DataRow dr = treeTempleList.GetFocusedDataRow();
            //if (dr != null)
            //{
            //    dr["role_users"] = string.IsNullOrEmpty(e.NewValue.ToString()) ? null : e.NewValue;
            //}

            if (drCurrentRowMainRecord == null) return;
            drCurrentRowMainRecord["role_users"] = string.IsNullOrEmpty(e.NewValue.ToString()) ? null : e.NewValue;

        }

        /// <summary>
        /// 点击按钮选择        
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>        
        private void txtRole_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            if (drCurrentRowMainRecord == null) return;

            frmStaffDict fsd = new frmStaffDict(drCurrentRowMainRecord["app_name"].ToString());
            fsd.usersArray = txtRole.Text;
            DialogResult d = fsd.ShowDialog();
            if (d == DialogResult.OK)
            {
                txtRole.Text = fsd.usersArray;
            }
        }

        /// <summary>
        /// 双击选择人员
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void txtRole_DoubleClick(object sender, EventArgs e)
        {
            txtRole_ButtonClick(this.txtRole, null);
        }

        private void dgvReportParam_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            if (e.Clicks == 2)
            {
                DataRow dr = dgvReportParam.GetFocusedDataRow();
                ShowEditForm(dr);
            }
        }

        void ShowEditForm(DataRow dr)
        {
            frmParamEdit fe = new frmParamEdit(dr);
            DialogResult result = fe.ShowDialog();

            if (result == DialogResult.OK)
            {
                //SaveReportParam();
                dgvReportParam.CloseEditor();
                dgvReportParam.UpdateCurrentRow();
            }
        }

        private void frmStatisticalSetting2_FormClosing(object sender, FormClosingEventArgs e)
        {
            //dgvTemple.CloseEditor();
            //dgvTemple.UpdateCurrentRow();
            if (_changeNameFlag == true)
            {
                Save();
            }
        }

        private void beRole_EditValueChanging(object sender, DevExpress.XtraEditors.Controls.ChangingEventArgs e)
        {
            //DataRow dr = treeTempleList.GetFocusedDataRow();
            if (drCurrentRowMainRecord == null) return;
            drCurrentRowMainRecord["role_codes"] = string.IsNullOrEmpty(e.NewValue.ToString()) ? null : e.NewValue;
        }

        private void beRole_DoubleClick(object sender, EventArgs e)
        {
            beRole_ButtonClick(this.beRole, null);
        }

        private void beRole_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            if (drCurrentRowMainRecord == null) return;

            frmRoleDict fsd = new frmRoleDict(drCurrentRowMainRecord["APP_NAME"].ToString());
            fsd.rolesArray = beRole.Text;
            DialogResult d = fsd.ShowDialog();
            if (d == DialogResult.OK)
            {
                beRole.Text = fsd.rolesArray;
            }
        }

        private void barButtonMainten_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            frmStatisticalClassDict frm = new frmStatisticalClassDict();
            frm.ShowDialog();

            this.Cursor = Cursors.WaitCursor;
            //分类窗体关闭后重新绑定分类数据源
            slueReportClass.Properties.DataSource = _srv.GetTempletClassDict().Tables[0];

            this.focusedNodeId = treeTempleList.FocusedNode.GetValue("DICT_ID").ToString();
            this.LoadTreeList();
            this.Cursor = Cursors.Default;
        }

        private void CreateNewReportMainRecord()
        {
            dtReportMainRecord.Clear();
            DataRow dr = dtReportMainRecord.NewRow();
            dr["HOSPITAL_CODE"] = SystemParm.HisUnitCode;
            dr["DICT_ID"] = DateTime.Now.ToString("yyyyMMddHHmmssfff");
            try
            {
                dr["CREATE_NURSE"] = SystemParm.LoginUser.USER_NAME;
            }
            catch
            {
                dr["CREATE_NURSE"] = "admin";
            }
            dr["CREATE_DATE"] = DateTime.Now;
            dr["ROLE_TYPE"] = "4";
            dr["DICT_NAME"] = "(未命名)";
            dtReportMainRecord.Rows.Add(dr);
        }
        private void GetNewReportMainRecord(DataRow dataRow, ref DataRow drNew)
        {
            drNew["HOSPITAL_CODE"] = SystemParm.HisUnitCode;
            drNew["APP_NAME"] = dataRow["APP_NAME"];
            drNew["SERIAL_NO"] = 0;
            drNew["DICT_ID"] = dataRow["DICT_ID"];
            drNew["DICT_NAME"] = dataRow["DICT_NAME"];
            drNew["REMARK"] = string.Empty;
            drNew["TEMPLET_SQL"] = dataRow["TEMPLET_SQL"];
            drNew["ROLE_TYPE"] = dataRow["ROLE_TYPE"];
            drNew["ROLE_USERS"] = dataRow["ROLE_USERS"];
            drNew["IS_TABLE"] = dataRow["IS_TABLE"];
            drNew["CREATE_NURSE"] = dataRow["CREATE_NURSE"];
            //drNew["CREATE_DATE"] = DateTime.Now;
            drNew["ROLE_CODES"] = dataRow["ROLE_CODES"];
            drNew["PARENTID"] = dataRow["PARENTID"];
            drNew["PAGE_SETTINGS"] = dataRow["PAGE_SETTINGS"];
        }

        /// <summary>
        /// 新增报表
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barButtonNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            isSaveNew = false;
            slueReportClass.Enabled = true;
            // 报表主记录
            CreateNewReportMainRecord();
            drCurrentRowMainRecord = dtReportMainRecord.Rows[0];

            //报表参数
            dtCurrentReportParam = _srv.GetReportParamEmpty();
            InitReportParam(drCurrentRowMainRecord["DICT_ID"].ToString(),
                drCurrentRowMainRecord["DICT_NAME"].ToString(), 
                string.Empty, string.Empty, false, dtCurrentReportParam, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty);
        }

        private void barButtonCopy_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            ////bool isRole = GetRole();
            ////if (!isRole)
            ////{
            ////    XtraMessageBox.Show("没有权限复制此报表！", "系统提示");
            ////    return;
            ////}
            if (drCurrentRowMainRecord == null)
                return;
            DataRow dr = treeTempleList.GetFocusedDataRow();//母版
            if (dr == null)
            {
                XtraMessageBox.Show("没有选择报表!", "系统提示");
                return;
            }

            string treeLevel = dr["TREELEVEL"].ToString();
            if (treeLevel.Equals("0") || treeLevel.Equals("1"))
            {
                XtraMessageBox.Show("请选择3级菜单!", "系统提示");
                return;
            }


            isSaveNew = true;

            // 生成主记录
            DataRow newDr = dtReportMainRecord.NewRow();

            newDr["DICT_ID"] = DateTime.Now.ToString("yyyyMMddHHmmssfff");
            newDr["APP_NAME"] = dr["APP_NAME"];
            newDr["DICT_NAME"] = dr["DICT_NAME"] + "_副本";
            newDr["CREATE_NURSE"] = SystemParm.LoginUser.USER_NAME;
            newDr["ROLE_TYPE"] = "4";
            newDr["ROLE_USERS"] = "";
            newDr["ROLE_CODES"] = "";
            newDr["CREATE_DATE"] = DateTime.Now;
            newDr["REMARK"] = dr["REMARK"];
            newDr["TEMPLET_SQL"] = dr["TEMPLET_SQL"];
            newDr["IS_TABLE"] = dr["IS_TABLE"];
            newDr["PARENTID"] = dr["PARENTID"].ToString().Remove(0,1);
            newDr["SERIAL_NO"] = _srv.GetMaxSerialNum(dr["APP_NAME"].ToString()) + 1;
            newDr["HOSPITAL_CODE"] = SystemParm.HisUnitCode;
            if (dr["IS_TABLE"].ToString().Equals("1"))
            {
                newDr["PAGE_SETTINGS"] = dr["PAGE_SETTINGS"];
            }
            dtReportMainRecord.AcceptChanges();
            dtReportMainRecord.Rows.Add(newDr);
            // 保存主记录
            _srv.SaveData(newDr.Table.DataSet);


            string repxPath = ReportHelper.GetTempleteFileNameFull(dr["DICT_NAME"] + "_" + dr["DICT_ID"], dr["APP_NAME"].ToString());
            string repxNewPath = ReportHelper.GetTempleteFileNameFull(newDr["DICT_NAME"] + "_" + newDr["DICT_ID"], dr["APP_NAME"].ToString());
            if (File.Exists(repxPath))
            {
                File.Copy(repxPath, repxNewPath);
            }

            ///复制报表参数
            DataSet dsParam = _srv.GetReportParamNew(dr["APP_NAME"].ToString(), dr["DICT_ID"].ToString());
            DataTable dtParam = dsParam.Tables[0].Copy();
            foreach (DataRow eachRow in dtParam.Rows)
            {
                DataRow newParam = dsParam.Tables[0].NewRow();
                eachRow["REPORT_ID"] = newDr["DICT_ID"];
                newParam.ItemArray = eachRow.ItemArray;

                dsParam.Tables[0].Rows.Add(newParam);
            }
            if (dsParam.HasChanges())
            {
                _srv.SaveData(dsParam);
                dsParam.AcceptChanges();
            }

            //表格类报表复制自定义记录配置表NURADM_REPORT_CONFIG 
            if (dr["IS_TABLE"].ToString() == "1")
            {
                DataSet dsRpConfig = _srv.GetReportConfig(dr["DICT_ID"].ToString(), dr["APP_NAME"].ToString());
                DataTable dtRpConfig = dsRpConfig.Tables[0].Copy();
                foreach (DataRow eachRow in dtRpConfig.Rows)
                {
                    DataRow newParam = dsRpConfig.Tables[0].NewRow();
                    eachRow["DICT_ID"] = newDr["DICT_ID"];
                    newParam.ItemArray = eachRow.ItemArray;

                    dsRpConfig.Tables[0].Rows.Add(newParam);
                }
                if (dsRpConfig.HasChanges())
                {
                    _srv.SaveData(dsRpConfig);
                    dsRpConfig.AcceptChanges();
                }
            }

            LoadReportLeafNode(newDr["APP_NAME"].ToString(), newDr["PARENTID"].ToString());

            TreeListNode node = treeTempleList.FindNodeByFieldValue("DICT_ID", newDr["DICT_ID"].ToString());
            treeTempleList.FocusedNode = node;
            treeTempleList.FocusedNode.ParentNode.ExpandAll();
            //dgvTemple_FocusedRowChanged(null, null);

            //btnSave_Click(null, null);
        }
        private void LoadReportLeafNode(string app_name, string classID)
        {
            DataSet ds_leaf = _srv.GetLeafTreeList(app_name, classID);

            string tempClassID = string.Empty;
            string tempAppName = string.Empty;
            string filter = string.Empty;
            DataTable treeDataTable = _dsTemplet.Tables[0];

            foreach (DataRow tempDatarow in ds_leaf.Tables[0].Rows)
            {
                tempClassID = tempDatarow["DICT_ID"].ToString();
                tempAppName = tempDatarow["APP_NAME"].ToString();
                filter = string.Format($"DICT_ID = '{tempClassID}' and APP_NAME = '{tempAppName}'");
                DataRow[] existRows = treeDataTable.Select(filter);
                if (existRows != null && existRows.Length > 0)
                {
                    // 更新原来的数据
                    existRows[0].ItemArray = tempDatarow.ItemArray.Clone() as object[];
                }
                else
                {
                    // 加入新的数据
                    DataRow newRow = treeDataTable.NewRow();
                    newRow.ItemArray = tempDatarow.ItemArray.Clone() as object[];
                    treeDataTable.Rows.Add(newRow);
                }
            }
        }
        private void barButtonChoosePara_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (drCurrentRowMainRecord == null)
                return;
            frmSelectedParam fsp = new frmSelectedParam();
            DialogResult result = fsp.ShowDialog();
            if (result == DialogResult.OK)
            {
                if (fsp.paramRow != null)
                {
                    foreach (DataRow eachParam in fsp.paramRow)
                    {
                        DataRow drp = dtCurrentReportParam.NewRow();
                        drp.ItemArray = eachParam.ItemArray;
                        drp["APP_NAME"] = drCurrentRowMainRecord["APP_NAME"];
                        drp["HOSPITAL_CODE"] = drCurrentRowMainRecord["HOSPITAL_CODE"]; ;
                        drp["REPORT_ID"] = drCurrentRowMainRecord["DICT_ID"]; ;
                        try
                        {
                            DataRow[] selParms = dtCurrentReportParam.Select("report_id = '" + drp["report_id"] + "' and param_name like'" + drp["param_name"] + "%'");
                            if (selParms.Length > 0)
                            {
                                int no = 0;
                                foreach (DataRow eachRow in selParms)
                                {
                                    string[] nameSplit = eachRow["param_name"].ToString().Split('_');
                                    if (nameSplit.Length > 1)
                                    {
                                        int eacheNo = 0;
                                        bool isOk = int.TryParse(nameSplit[nameSplit.Length - 1], out eacheNo);
                                        if (isOk)
                                        {
                                            if (eacheNo > no)
                                            {
                                                no = eacheNo;
                                            }
                                        }
                                    }
                                }
                                drp["param_name"] = drp["param_name"] + "_" + (no + 1).ToString();
                            }
                            dtCurrentReportParam.Rows.Add(drp);
                        }
                        catch
                        {
                            XtraMessageBox.Show("参数已存在，请编辑后再添加！", "系统提示");
                        }
                    }

                    // 保存参数
                    //SaveReportParam();
                    dgvReportParam.CloseEditor();
                    dgvReportParam.UpdateCurrentRow();
                }
            }
        }

        private void barButtonNewPara_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (drCurrentRowMainRecord == null)
                return;
            try
            {
                //dgvReportParam.AddNewRow();
                //DataRow dr = dgvReportParam.GetFocusedDataRow();
                DataRow dr = dtCurrentReportParam.NewRow();
                dr["APP_NAME"] = drCurrentRowMainRecord["APP_NAME"];
                dr["HOSPITAL_CODE"] = drCurrentRowMainRecord["HOSPITAL_CODE"]; ;
                dr["REPORT_ID"] = drCurrentRowMainRecord["DICT_ID"];

                frmParamEdit fe = new frmParamEdit(dr, dtCurrentReportParam);
                DialogResult result = fe.ShowDialog();

                if (result == DialogResult.OK)
                {
                    // 保存参数
                    //SaveReportParam();
                    dtCurrentReportParam.Rows.Add(dr);

                    dgvReportParam.CloseEditor();
                    dgvReportParam.UpdateCurrentRow();
                }
                else
                {

                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "错误提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void barButtonEditPara_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            DataRow dr = dgvReportParam.GetFocusedDataRow();
            if (dr == null) return;

            ShowEditForm(dr);
        }

        private void barButtonBinding_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (drCurrentRowMainRecord == null)
                return;
            try
            {
                string sql = string.Empty;
                if (txtSql.EditValue != null)
                {
                    sql = txtSql.EditValue.ToString();
                }
                if (sql.EndsWith(";"))
                {
                    sql = sql.Substring(0, sql.Length - 1);
                }
                if (string.IsNullOrEmpty(sql))
                {
                    XtraMessageBox.Show("请先编写SQL，再绑定字段！", "提示");
                    return;
                }

                if (drCurrentRowMainRecord == null) return;

                if (checkEdit_IsTable.Checked)
                {
                    OpenBindingGrid(sql);
                }
                else
                {
                    OpenReportRepx(drCurrentRowMainRecord, false);
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message, "提示");
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void OpenBindingGrid(string sql)
        {
            frmEditField frm = new frmEditField();
            frm.dictID = drCurrentRowMainRecord["DICT_ID"].ToString();
            frm._appName = drCurrentRowMainRecord["APP_NAME"].ToString();

            DataRow[] drs = dtCurrentReportParam.Select("PARAM_NAME = 'REPORTNO'");
            if (drs != null && drs.Count() > 0)
            {
                DataTable dt = _srv.GetEditDataSource(drs[0]["SOURCE_TYPE"].ToString(), drs[0]["DATA_SOURCE_SQL"].ToString());
                frm._dtReportNo = dt;
                frm._displayMember = drs[0]["DISPLAY_MEMBER"].ToString();
                frm._valueMember = drs[0]["VALUE_MEMBER"].ToString();
                frm._defaultValue = drs[0]["DEFAULT_VALUE"].ToString();
            }

            DataSet ds = _srv.TestSQLNew(sql, getParams());
            if (ds != null)
            {
                frm.dataSourcesStruct = ds.Clone();
            }
            else
            {
                XtraMessageBox.Show("请先编写SQL，再绑定字段！", "提示");
                return;
            }
            frm.ShowDialog();
        }
        private void OpenReportRepx(DataRow drTemplet, bool isPrintFormat)
        {
            try
            {
                string templetePrintFile = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString();
                if (isPrintFormat)
                {
                    templetePrintFile += "_Print";
                }

                if (!string.IsNullOrEmpty(_reportNo))
                {
                    templetePrintFile = templetePrintFile + "_" + _reportNo;
                }
                //string strReportFileName = Application.StartupPath + $"\\Reports\\{TempleteFile}.repx";
                string strReportFileName = ReportHelper.GetTempleteFileNameFull(templetePrintFile, drCurrentRowMainRecord["APP_NAME"].ToString());

                ReportHelper.GetReportTemplateById(drCurrentRowMainRecord["HOSPITAL_CODE"].ToString(),
                    drCurrentRowMainRecord["APP_NAME"].ToString(),
                    drCurrentRowMainRecord["DICT_ID"].ToString(),
                    _reportNo, strReportFileName, isPrintFormat, ref DocViewer, ref mReport);

                TempleteSql = drTemplet["TEMPLET_SQL"].ToString();
                if (string.IsNullOrEmpty(TempleteSql.Trim()))
                {
                    XtraMessageBox.Show("该报表缺少SQL脚本，请您先在报表维护中填写！", "系统提示");
                    return;
                }
                //if (mReport.DataSource == null)//多个类型查询的报表情况会有问题 删除此条件
                //{

                mReport.DataSource = _srv.GetDataStruct(TempleteSql, getParams(), _reportNo.ToInt());
                //}

                // 打开模板设计器
                XRDesignFormEx frm = new XRDesignFormEx();
                frm.FileName = strReportFileName;
                frm.OpenReport(mReport);
                frm.ShowDialog();
                frm.Dispose();

                //保存报表设计模板到数据库
                _srv.SaveReportRepx(drCurrentRowMainRecord["APP_NAME"].ToString(),
                    drCurrentRowMainRecord["DICT_ID"].ToString(), _reportNo, strReportFileName, isPrintFormat);

                //重新加载模板
                this.Cursor = Cursors.WaitCursor;
                // XtraReportHelper.LoadTemplet(ref DocViewer, ref mReport, TempleteFile);
                if (!string.IsNullOrEmpty(_reportNo))
                {
                    mReport.DataSource = XtraReportHelper.GetPrintData(TempleteSql, getParams(), drCurrentRowMainRecord["APP_NAME"].ToString(), true);
                }
                else
                {
                    mReport.DataSource = XtraReportHelper.GetPrintData(TempleteSql, getParams(), drCurrentRowMainRecord["APP_NAME"].ToString(), true);
                }
                mReport.CreateDocument();

                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }

        }
        private void barButtonTest_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (drCurrentRowMainRecord == null)
                return;
            try
            {
                string sql = string.Empty;
                if (txtSql.EditValue != null)
                {
                    sql = txtSql.EditValue.ToString();
                }
                if (sql.EndsWith(";"))
                {
                    sql = sql.Substring(0, sql.Length - 1);
                }
                if (string.IsNullOrEmpty(sql))
                {
                    XtraMessageBox.Show("请先填写SQL！", "提示");
                    return;
                }
                DataSet ds = _srv.TestSQLNew(sql, getParams());
                if (ds != null)
                {
                    XtraMessageBox.Show("测试成功！", "提示");
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message, "提示");
            }
        }

        private void barButtonDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (drCurrentRowMainRecord == null)
                return;

            DataRow dr = treeTempleList.GetFocusedDataRow();
            if (dr == null) return;

            DialogResult result = XtraMessageBox.Show("确认要删除报表[" +dr["DICT_NAME"] + "]吗？", "提示", MessageBoxButtons.OKCancel);
            if (result == DialogResult.OK)
            {
                List<string> listReportNo = new List<string>();
                //删除报表所有数据
                int iDelSucc = _srv.DeleteReportAll(dr["DICT_ID"].ToString(), dr["APP_NAME"].ToString(), dr["IS_TABLE"].ToString(), ref listReportNo);
                if (iDelSucc != 0)
                {
                    return;
                }
                //删除报表模板文件
                //string repxNewPath = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\" + dr["DICT_NAME"] + "_" + dr["DICT_ID"] + ".repx");
                string repxNewPath = ReportHelper.GetTempleteFileNameFull(dr["DICT_NAME"] + "_" + dr["DICT_ID"], dr["APP_NAME"].ToString());
                if (listReportNo.Count > 0)
                {
                    foreach (string reportNo in listReportNo)
                    {
                        repxNewPath = ReportHelper.GetTempleteFileNameFull(dr["DICT_NAME"] + "_" + dr["DICT_ID"] + "_" + reportNo, dr["APP_NAME"].ToString());
                        //repxNewPath = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\" + dr["DICT_NAME"] + "_" + dr["DICT_ID"] + "_" + reportNo + ".repx");
                        if (File.Exists(repxNewPath))
                        {
                            File.Delete(repxNewPath);
                        }
                    }
                }
                else
                {
                    if (File.Exists(repxNewPath))
                    {
                        File.Delete(repxNewPath);
                    }
                }

                //删除行并保存界面数据
                treeTempleList.DeleteSelectedNodes();
                //dgvTemple.CloseEditor();
                //dgvTemple.UpdateCurrentRow();
                _dsTemplet.AcceptChanges();

            }
        }

        private void barButtonSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (drCurrentRowMainRecord == null)
                return;
            try
            {
                int ret = -1;
                if (!isSaveNew)
                {
                    lastDictId = drCurrentRowMainRecord["DICT_ID"].ToString();
                }
                else
                {
                    DataRow focusDataRow = treeTempleList.GetFocusedDataRow();
                    lastDictId = focusDataRow["DICT_ID"].ToString();
                }
                if (!string.IsNullOrEmpty(textEdit_ReportName.Text))
                {
                    drCurrentRowMainRecord["DICT_NAME"] = textEdit_ReportName.Text;
                }
                if (string.IsNullOrEmpty(drCurrentRowMainRecord["SERIAL_NO"].ToString()))
                {
                    drCurrentRowMainRecord["SERIAL_NO"] = _srv.GetMaxSerialNum(drCurrentRowMainRecord["APP_NAME"].ToString()) + 1;
                }
                if (!string.IsNullOrEmpty(beRole.Text))
                {
                    drCurrentRowMainRecord["ROLE_CODES"] = beRole.Text;
                    drCurrentRowMainRecord["ROLE_TYPE"] = 3;
                }
                if (!string.IsNullOrEmpty(txtRole.Text))
                {
                    drCurrentRowMainRecord["ROLE_USERS"] = txtRole.Text;
                    drCurrentRowMainRecord["ROLE_TYPE"] = 3;
                }
                if (!string.IsNullOrEmpty(txt_memo.Text))
                {
                    drCurrentRowMainRecord["REMARK"] = txt_memo.Text;
                }

                if (!isSaveNew)
                {
                    isSaveNew = true;
                    ret = _srv.SaveData(drCurrentRowMainRecord.Table.DataSet);
                }
                else
                {

                    if (drCurrentRowMainRecord != null)
                    {
                        DataRow dr = _srv.GetTempletByIDNew(drCurrentRowMainRecord["DICT_ID"].ToString(), drCurrentRowMainRecord["APP_NAME"].ToString());
                        if (dr != null)
                        {
                            dr["TEMPLET_SQL"] = drCurrentRowMainRecord["TEMPLET_SQL"];
                            dr["ROLE_TYPE"] = drCurrentRowMainRecord["ROLE_TYPE"];
                            dr["ROLE_CODES"] = beRole.Text;
                            dr["ROLE_USERS"] = txtRole.Text;

                            dr["IS_TABLE"] = drCurrentRowMainRecord["IS_TABLE"];
                            if (dr["IS_TABLE"].ToString().Equals("1"))
                            {
                                dr["PAGE_SETTINGS"] = drCurrentRowMainRecord["PAGE_SETTINGS"];
                            }
                            else
                            {
                                dr["PAGE_SETTINGS"] = string.Empty;
                            }
                            dr["CREATE_NURSE"] = drCurrentRowMainRecord["CREATE_NURSE"];
                            //drNew["CREATE_DATE"] = DateTime.Now;
                            dr["DICT_NAME"] = drCurrentRowMainRecord["DICT_NAME"];
                            dr["REMARK"] = txt_memo.Text;
                        }

                        ret = _srv.SaveData(dr.Table.DataSet);

                    }
                }
                if (ret > 0)
                {
                    ret = SaveReportParam();
                    if (ret > 0)
                    {
                        ret = SaveBindingSQL(drCurrentRowMainRecord);
                        if (ret > 0)
                        {
                            //重新加载叶子节点
                            LoadReportLeafNode(drCurrentRowMainRecord["APP_NAME"].ToString(), drCurrentRowMainRecord["PARENTID"].ToString());
                            TreeListNode node = treeTempleList.FindNodeByFieldValue("DICT_ID", lastDictId);
                            treeTempleList.FocusedNode = node;
                            treeTempleList.FocusedNode.ParentNode.ExpandAll();

                            XtraMessageBox.Show("保存成功!", "系统提示");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("保存失败!", "系统提示");
            }

        }

        private int SaveBindingSQL(DataRow dataRow)
        {
            string sqlStr = txtSql.Text;
            if (sqlStr.EndsWith(";"))
            {
                sqlStr = sqlStr.Substring(0, sqlStr.Length - 1);
            }
            if (!dataRow["TEMPLET_SQL"].Equals(sqlStr))
                dataRow["TEMPLET_SQL"] = sqlStr;
            string repxPath = ReportHelper.GetTempleteFileNameFull(dataRow["DICT_NAME"].ToString(), dataRow["APP_NAME"].ToString());
            string repxNewPath = ReportHelper.GetTempleteFileNameFull(dataRow["DICT_NAME"].ToString() + "_" + dataRow["DICT_ID"].ToString(), dataRow["APP_NAME"].ToString());
            if (File.Exists(repxNewPath) == false)
            {
                if (File.Exists(repxPath))
                {
                    File.Move(repxPath, repxNewPath);
                }
                else
                {
                    //File.Copy(repxModelPath, repxNewPath);
                }
            }
            DataRow dr = _srv.GetTempletByIDNew(dataRow["DICT_ID"].ToString(), dataRow["APP_NAME"].ToString());
            if (dr == null) return 1;

            dr["TEMPLET_SQL"] = sqlStr;
            return _srv.SaveData(dr.Table.DataSet);
        }
        private void barButtonParaDict_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            frmParamDict fp = new frmParamDict(this.AppCode);
            fp.ShowDialog();
        }

        private void frmStatisticalSetting2_Shown(object sender, EventArgs e)
        {
            LoadTreeList();
        }

        private void treeTempleList_FocusedNodeChanged(object sender, DevExpress.XtraTreeList.FocusedNodeChangedEventArgs e)
        {
            slueReportClass.Enabled = false;
            isSaveNew = true;
            DataRow dr = treeTempleList.GetFocusedDataRow();
            if (dr == null) return;

            string treeLevel = dr["TREELEVEL"].ToString();

            if (treeLevel.Equals("0") || treeLevel.Equals("1") || treeLevel.Equals("2"))
            {
                dtCurrentReportParam.Clear();
                drCurrentRowMainRecord = null;
                InitReportParam(string.Empty, string.Empty, string.Empty, string.Empty, false, dtCurrentReportParam, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty);
                if (treeLevel.Equals("2"))
                {
                    // 分类列表，获取下面的报表
                    string treeClassID = dr["DICT_ID"].ToString().Substring(1);
                    string treeAppName = dr["APP_NAME"].ToString();
                    LoadReportLeafNode(treeAppName, treeClassID);
                }
                return;
            }
            Const.customAppCode = dr["APP_NAME"].ToString();
            DataRow dataRow = _srv.GetTempletByIDNew(dr["DICT_ID"].ToString(), dr["APP_NAME"].ToString());

            dtReportMainRecord.Clear();
            drCurrentRowMainRecord = dtReportMainRecord.NewRow();
            dtReportMainRecord.Rows.Add(drCurrentRowMainRecord);
            GetNewReportMainRecord(dataRow, ref drCurrentRowMainRecord);

            //是否表格
            bool isTable = drCurrentRowMainRecord["IS_TABLE"].ToString() == "1" ? true : false;
            string pageSettings = string.Empty;
            if (isTable)
            {
                pageSettings = drCurrentRowMainRecord["PAGE_SETTINGS"].ToString();
            }
            string classId = dr["PARENTID"].ToString().Remove(0, 1);
            //获取报表参数
            dtCurrentReportParam = _srv.GetReportParamNew(dr["APP_NAME"].ToString(), dr["DICT_ID"].ToString()).Tables[0];
            InitReportParam(dr["DICT_ID"].ToString(), 
                dr["DICT_NAME"].ToString(), 
                dr["role_codes"].ToString(), 
                dr["role_users"].ToString(),
                isTable, dtCurrentReportParam, 
                dr["TEMPLET_SQL"].ToString(),
                dr["APP_NAME"].ToString(),
                classId, 
                dr["REMARK"].ToString(),
                pageSettings);
        }

        private void slueReportClass_EditValueChanged(object sender, EventArgs e)
        {
            DataRowView drv = slueReportClass.GetSelectedDataRow() as DataRowView;
            if (drv == null) return;
            if (drCurrentRowMainRecord != null)
            {
                drCurrentRowMainRecord["PARENTID"] = drv["CLASS_ID"].ToString();
                drCurrentRowMainRecord["APP_NAME"] = drv["APP_NAME"].ToString();
            }
        }

        private void barButtonDeleteParam_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            DataRow dr = dgvReportParam.GetFocusedDataRow();
            if (dr == null) return;
            dgvReportParam.DeleteSelectedRows();
        }

        private void checkEdit_IsTable_CheckedChanged(object sender, EventArgs e)
        {
            if (drCurrentRowMainRecord != null)
            {
                drCurrentRowMainRecord["IS_TABLE"] = checkEdit_IsTable.EditValue.ToString().Equals("True") ? 1 : 0;
                if (checkEdit_IsTable.EditValue.ToString().Equals("True"))
                {
                    txtPageSettings.Text = "A4,纵向";
                    drCurrentRowMainRecord["PAGE_SETTINGS"] = txtPageSettings.Text;
                    txtPageSettings.Visible = true;
                }
                else
                {
                    txtPageSettings.Visible = false;
                    drCurrentRowMainRecord["PAGE_SETTINGS"] = string.Empty;
                }
            }
        }

        private void barButtonClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Close();
        }

        private void barButtonPrintDesignSpecial_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            DataRow dr = dgvReportParam.GetFocusedDataRow();
            if (dr == null) return;

            OpenReportRepx(drCurrentRowMainRecord, true);
        }

        private void treeTempleList_GetSelectImage(object sender, DevExpress.XtraTreeList.GetSelectImageEventArgs e)
        {
            if (e.Node == null) return;
            TreeListNode node = e.Node;
            if (node.Level == 0)
                e.NodeImageIndex = 3;
            else if (node.Level == 3)
                e.NodeImageIndex = 1;
            else
            {
                if (node.Expanded)
                    e.NodeImageIndex = 0;
                else
                    e.NodeImageIndex = 2;
            }
        }

        private void barSubItem_display_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (drCurrentRowMainRecord == null)
                return;

            TJReportViewF doc = new TJReportViewF(this.DeptCode, this.AppCode, "");
            doc.IsShowFunctionButton = false;
            string myAppName = drCurrentRowMainRecord["APP_NAME"].ToString();
            doc.Tag = myAppName + "|" + drCurrentRowMainRecord["DICT_ID"] + (string.IsNullOrEmpty(drCurrentRowMainRecord["is_table"].ToString()) ? "" : "-" + drCurrentRowMainRecord["is_table"].ToString());
            doc.StartPosition = FormStartPosition.CenterParent;
            doc.ShowDialog();
        }

        private void barSubItem_specialPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                string reportNo = "";
                string templetePrintFile = drCurrentRowMainRecord["DICT_NAME"].ToString() + "_" + drCurrentRowMainRecord["DICT_ID"].ToString() + "_Print";
                //if (ParamList != null)
                //{
                //    foreach (ParamClass a in ParamList)
                //    {
                //        if (a.GetParamName().ToUpper() == "REPORTNO")
                //        {
                //            reportNo = a.EditValue().ToString();
                //            break;
                //        }
                //    }
                //}
                if (!string.IsNullOrEmpty(reportNo))
                {
                    templetePrintFile = templetePrintFile + "_" + reportNo;
                }
                XtraReportHelper.PrinterName = PrinterConfigFrm.GetPrinterName(templetePrintFile);
                DataSet printDs = XtraReportHelper.GetPrintData(TempleteSql, getParams(), reportNo.ToInt(), drCurrentRowMainRecord["APP_NAME"].ToString());

                XtraReportHelper.Print(templetePrintFile, printDs, true, false, drCurrentRowMainRecord["APP_NAME"].ToString());
            }
            catch (Exception ex)
            {

            }
        }

        private void barSubItem_ImportDisplay_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (drCurrentRowMainRecord == null)
                return;

            OpenFileDialog diag = new OpenFileDialog();
            diag.Filter = "所有文件(*.*)|*.*|repx文件(*.repx)|*.repx";
            diag.Multiselect = false;
            diag.Title = "导入显示用repx文件";
            DialogResult dr = diag.ShowDialog();
            if (dr != DialogResult.OK || diag.FileNames == null)
            {
                return;
            }
            int ret = _srv.SaveReportRepx(drCurrentRowMainRecord["APP_NAME"].ToString(),
                drCurrentRowMainRecord["DICT_ID"].ToString(),
                "",
                diag.FileName,
                false);
            if (ret > 0)
            {
                XtraMessageBox.Show("导入成功！");
            }
        }

        private void barSubItem_importSpecialPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (drCurrentRowMainRecord == null)
                return;

            OpenFileDialog diag = new OpenFileDialog();
            diag.Filter = "所有文件(*.*)|*.*|repx文件(*.repx)|*.repx";
            diag.Multiselect = false;
            diag.Title = "导入特殊打印用repx文件";
            DialogResult dr = diag.ShowDialog();
            if (dr != DialogResult.OK || diag.FileNames == null)
            {
                return;
            }
            int ret = _srv.SaveReportRepx(drCurrentRowMainRecord["APP_NAME"].ToString(),
                drCurrentRowMainRecord["DICT_ID"].ToString(),
                "",
                diag.FileName,
                true);
            if (ret > 0)
            {
                XtraMessageBox.Show("导入成功！");
            }
        }

        private void frmStatisticalSetting2_KeyUp(object sender, KeyEventArgs e)
        {
            if (drCurrentRowMainRecord == null)
                return;
            try
            {
                if (e.Control == false) return;

                this.Cursor = Cursors.WaitCursor;

                // 把数据集保存到Schema子目录中
                if (e.KeyCode == Keys.F12)
                {
                    XtraReportHelper.WriteSchemaData(TempleteFile, ref mReport, getParams(), drCurrentRowMainRecord["APP_NAME"].ToString());

                    //XtraMessageBox.Show(TempleteFile + "数据集保存成功!");
                }

            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void barLargeButton_export_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (drCurrentRowMainRecord == null)
                return;

            //获得保存文件的路径
            string templeteFile = drCurrentRowMainRecord["DICT_NAME"].ToString() + "_" + drCurrentRowMainRecord["DICT_ID"].ToString();
            if (!string.IsNullOrEmpty(_reportNo))
            {
                templeteFile = templeteFile + "_" + _reportNo;
            }
            string templetePrintFile = templeteFile + "_print";

            string strReportFileName = ReportHelper.GetTempleteFileNameFull(templeteFile, drCurrentRowMainRecord["APP_NAME"].ToString());
            string strPrintFileName = ReportHelper.GetTempleteFileNameFull(templetePrintFile, drCurrentRowMainRecord["APP_NAME"].ToString());
            string messaage = string.Empty;

            try
            {
                string designFile = ReportHelper.GetReportRepx(SystemParm.HisUnitCode, drCurrentRowMainRecord["APP_NAME"].ToString(), drCurrentRowMainRecord["DICT_ID"].ToString(),"", false);
                if (!string.IsNullOrEmpty(designFile))
                {
                    File.WriteAllText(strReportFileName, designFile);//保存  
                    messaage = $"文件导出成功！\n {strReportFileName}";
                }

                designFile = string.Empty;
                designFile = ReportHelper.GetReportRepx(SystemParm.HisUnitCode, drCurrentRowMainRecord["APP_NAME"].ToString(), drCurrentRowMainRecord["DICT_ID"].ToString(),"", true);
                if (!string.IsNullOrEmpty(designFile))
                {
                    File.WriteAllText(strPrintFileName, designFile);//保存
                    messaage = $"文件导出成功！\n {strReportFileName}\n{strPrintFileName}";
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show("导出文件失败!");
                return;
            }

            MessageBox.Show(messaage);
        }

        private void btn_pageSettingDiag_Click(object sender, EventArgs e)
        {
            using (frmPageSetup frmPageSetup = new frmPageSetup())
            {
                if (frmPageSetup.ShowDialog() == DialogResult.OK)
                {
                    PrintPageSettings pps = frmPageSetup.pps;
                    if (drCurrentRowMainRecord != null)
                    {
                        drCurrentRowMainRecord["IS_TABLE"] = checkEdit_IsTable.EditValue.ToString() == "True" ? 1 : 0;
                    }
                }
            }
        }

        private void txtPageSettings_ButtonClick(object sender, ButtonPressedEventArgs e)
        {
            if (drCurrentRowMainRecord != null)
            {
                bool landscape = false;
                System.Drawing.Printing.PaperKind kind = System.Drawing.Printing.PaperKind.A4;

                string pageSettings =drCurrentRowMainRecord["PAGE_SETTINGS"].ToString();
                string[] settings = pageSettings.Split(',');
                if (settings != null && settings.Length == 2)
                {
                    landscape = string.Equals(settings[1], "横向") ? true : false;
                    try
                    {
                        kind = (System.Drawing.Printing.PaperKind)Enum.Parse(typeof(System.Drawing.Printing.PaperKind), settings[0]);
                    }
                    catch (Exception ex) { }
                }
                using (frmPageSetup frmPageSetup = new frmPageSetup(kind, landscape))
                {
                    if (frmPageSetup.ShowDialog() == DialogResult.OK)
                    {
                        PrintPageSettings pps = frmPageSetup.pps;
                        if (pps != null)
                        {
                            pageSettings = pps.PaperSize?.Kind.ToString() + ',' + (pps.Landscape ? "横向" : "纵向");
                            txtPageSettings.Text = pageSettings;
                            if (drCurrentRowMainRecord != null)
                            {
                                drCurrentRowMainRecord["PAGE_SETTINGS"] = pageSettings;
                            }
                        }
                    }
                }
            }
        }

        private void frmStatisticalSetting2_FormClosed(object sender, FormClosedEventArgs e)
        {
            if (mReport != null)
            {
                mReport.Dispose();
                mReport = null;
            }

            if (DocViewer != null)
            {
                DocViewer.Dispose();
                DocViewer = null;
            }
            this.Dispose();
        }
    }
}
