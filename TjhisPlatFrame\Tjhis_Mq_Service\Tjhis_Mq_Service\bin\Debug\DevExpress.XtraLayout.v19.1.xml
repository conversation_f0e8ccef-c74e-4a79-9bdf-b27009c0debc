<?xml version="1.0" encoding="utf-8"?>
<doc xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <assembly>
    <name>DevExpress.XtraLayout.v19.1</name>
  </assembly>
  <members>
    <member name="N:DevExpress.XtraDataLayout">
      <summary>
        <para>Contains classes that implement the <see cref="T:DevExpress.XtraDataLayout.DataLayoutControl"/> control functionality.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraDataLayout.DataLayoutControl">
      <summary>
        <para>Creates and maintains a consistent layout of controls for editing a specific data source&#39;s fields. See Data Layout Control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDataLayout.DataLayoutControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDataLayout.DataLayoutControl"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDataLayout.DataLayoutControl.AddToHiddenItems(DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para>Adds a layout item to the control and hides it.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant that represents the layout item to be added to the control and hidden.</param>
    </member>
    <member name="P:DevExpress.XtraDataLayout.DataLayoutControl.AllowGeneratingCollectionProperties">
      <summary>
        <para>Gets or sets whether layout items that correspond to IList properties will be added to the generated layout. By default, these layout items will contain embedded <see cref="T:DevExpress.XtraGrid.GridControl"/>s to display and edit IList object contents.</para>
      </summary>
      <value>True to generate layout items for IList properties; Default or False to exclude these properties from the generated layout.</value>
    </member>
    <member name="P:DevExpress.XtraDataLayout.DataLayoutControl.AllowGeneratingNestedGroups">
      <summary>
        <para>Gets or sets whether nested layout groups are generated to present fields of business object properties.</para>
      </summary>
      <value>True, if nested groups are generated for business object properties; Default or False if nested groups are not generated.</value>
    </member>
    <member name="P:DevExpress.XtraDataLayout.DataLayoutControl.AutoRetrieveFields">
      <summary>
        <para>Gets or sets whether a layout is re-built each time a new value is assigned to the <see cref="P:DevExpress.XtraDataLayout.DataLayoutControl.DataSource"/> property.</para>
      </summary>
      <value>true, if a layout is re-built each time a new value is assigned to the <see cref="P:DevExpress.XtraDataLayout.DataLayoutControl.DataSource"/> property; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraDataLayout.DataLayoutControl.ControlsManager">
      <summary>
        <para>Gets an object that identifies which editors can be used to edit data of specific types.</para>
      </summary>
      <value>A DevExpress.XtraDataLayout.ControlsManager object.</value>
    </member>
    <member name="M:DevExpress.XtraDataLayout.DataLayoutControl.CreateLayoutCreator">
      <summary>
        <para>For internal use.</para>
      </summary>
      <returns></returns>
    </member>
    <member name="E:DevExpress.XtraDataLayout.DataLayoutControl.CurrentRecordChanged">
      <summary>
        <para>Fires when the current position in the bound data source changes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDataLayout.DataLayoutControl.DataMember">
      <summary>
        <para>Gets or sets the data source member which supplies data to the <see cref="T:DevExpress.XtraDataLayout.DataLayoutControl"/>&#39;s editors.</para>
      </summary>
      <value>A string value representing the data source member.</value>
    </member>
    <member name="P:DevExpress.XtraDataLayout.DataLayoutControl.DataSource">
      <summary>
        <para>Gets or sets the data source providing data for editors that are created by the <see cref="T:DevExpress.XtraDataLayout.DataLayoutControl"/> control.</para>
      </summary>
      <value>The object used as the data source.</value>
    </member>
    <member name="E:DevExpress.XtraDataLayout.DataLayoutControl.FieldRetrieved">
      <summary>
        <para>Fires after a layout is generated at runtime. Allows you to customize settings of individual generated layout items and editors.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraDataLayout.DataLayoutControl.FieldRetrieving">
      <summary>
        <para>Fires before a layout item with an embedded editor is generated and thus, prior to the editor&#39;s data binding. It allows you to customize the type of editor to be generated, modify editor binding settings and hide certain editors.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDataLayout.DataLayoutControl.ForceInitialize">
      <summary>
        <para>Forces the control to finish its initialization.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDataLayout.DataLayoutControl.GetLayoutElementsBindingsInfo">
      <summary>
        <para>Returns information on the layout column count and binding settings of public properties in the data source.</para>
      </summary>
      <returns>A value that specifies information on layout element binding settings.</returns>
    </member>
    <member name="M:DevExpress.XtraDataLayout.DataLayoutControl.GetLayoutElementsBindingsInfo(System.Collections.ICollection)">
      <summary>
        <para>Returns the layout element binding information from the specified PropertyDescriptor collection.</para>
      </summary>
      <param name="propertyDescriptorCollection">A collection of PropertyDescriptor objects.</param>
      <returns>A value that specifies the layout element binding information.</returns>
    </member>
    <member name="M:DevExpress.XtraDataLayout.DataLayoutControl.RetrieveFields">
      <summary>
        <para>Creates layout items for all public fields in the bound data source.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDataLayout.DataLayoutControl.RetrieveFields(DevExpress.XtraDataLayout.RetrieveFieldsParameters)">
      <summary>
        <para>Creates layout items for all public fields in the bound data source and allows you to customize general binding and layout settings.</para>
      </summary>
      <param name="parameters">An object that provides additional binding and layout customization options.</param>
    </member>
    <member name="P:DevExpress.XtraDataLayout.DataLayoutControl.Site">
      <summary>
        <para>Gets or sets the site associated with the current <see cref="T:DevExpress.XtraDataLayout.DataLayoutControl"/>.</para>
      </summary>
      <value>A System.ComponentModel.ISite object.</value>
    </member>
    <member name="T:DevExpress.XtraDataLayout.RetrieveFieldsParameters">
      <summary>
        <para>Contains parameters for the  method.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraDataLayout.RetrieveFieldsParameters.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraDataLayout.RetrieveFieldsParameters"/> class with the specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraDataLayout.RetrieveFieldsParameters.ColumnCount">
      <summary>
        <para>Gets or sets the number of columns in the layout that will be generated.</para>
      </summary>
      <value>An integer value that specifies the number of columns in the layout that will be generated.</value>
    </member>
    <member name="P:DevExpress.XtraDataLayout.RetrieveFieldsParameters.CustomListParameters">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraDataLayout.RetrieveFieldsParameters.DataSourceNullValue">
      <summary>
        <para>Gets or sets the default value used to initialize the Binding.DataSourceNullValue property for all auto-generated editors.
This property identifies the value to be stored in the data source if the editor&#39;s value is null or empty.</para>
      </summary>
      <value>The default value used to initialize the Binding.DataSourceNullValue property of all auto-generated editors.</value>
    </member>
    <member name="P:DevExpress.XtraDataLayout.RetrieveFieldsParameters.DataSourceUpdateMode">
      <summary>
        <para>Gets or sets the default value used to initialize the Binding.DataSourceUpdateMode property for all auto-generated editors.
This property indicates when changes to the bound editor property are propagated to the data source.</para>
      </summary>
      <value>The default value used to initialize the Binding.DataSourceNullValue property of all auto-generated editors.</value>
    </member>
    <member name="N:DevExpress.XtraLayout">
      <summary>
        <para>Contains the classes that encapsulate the Layout Control&#39;s main functionality.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.AutoAlignMode">
      <summary>
        <para>Obsolete. Instead use the options provided by the <see cref="T:DevExpress.XtraLayout.TextAlignMode"/> class.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.AutoAlignMode.AlignGlobal">
      <summary>
        <para>This member is obsolete. Controls are aligned throughout the LayoutControl.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.AutoAlignMode.AlignLocal">
      <summary>
        <para>This member is obsolete. Controls are aligned independently within layout groups.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.AutoAlignMode.AutoSize">
      <summary>
        <para>This member is obsolete. The auto-size feature is enabled.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.AutoSizeModes">
      <summary>
        <para>Enumerates the options that specify how a <see cref="T:DevExpress.XtraLayout.LayoutControl"/>&#39;s size is changed when it is positioned within another LayoutControl.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.AutoSizeModes.ResizeToMinSize">
      <summary>
        <para>The embedded LayoutControl&#39;s size is fixed and is equal to its minimum size.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.AutoSizeModes.UseMinAndMaxSize">
      <summary>
        <para>The embedded LayoutControl&#39;s size can vary between its minimum and maximum sizes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.AutoSizeModes.UseMinSizeAndGrow">
      <summary>
        <para>The embedded LayoutControl&#39;s minimum size is restricted; while its maximum size is unlimited.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.BaseLayoutItem">
      <summary>
        <para>Represents the base class for layout items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.Accept(DevExpress.XtraLayout.Utils.BaseVisitor)">
      <summary>
        <para>Invokes the Visit method of the specified visitor for each layout item that belongs to the current layout item.</para>
      </summary>
      <param name="visitor">A DevExpress.XtraLayout.Utils.BaseVisitor class descendant.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.AllowHide">
      <summary>
        <para>Gets or sets whether the item can be hidden to the Customization Form.</para>
      </summary>
      <value>true if the item can be hidden to the Customization Form; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.AppearanceItemCaption">
      <summary>
        <para>Gets the appearance settings used to paint a layout item&#39;s caption.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the layout item&#39;s caption.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.Assign(DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para></para>
      </summary>
      <param name="item"></param>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.BeginInit">
      <summary>
        <para>Starts the component&#39;s initialization.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.BestFitWeight">
      <summary>
        <para>Gets or sets the width of the current <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> relative to other items&#39; width after the <see cref="M:DevExpress.XtraLayout.LayoutControl.BestFit"/> method has been called.</para>
      </summary>
      <value>An Integer value that is the proportion of the current <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/>&#39;s width to other items&#39; width after the <see cref="M:DevExpress.XtraLayout.LayoutControl.BestFit"/> method has been called.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.BindingContext">
      <summary>
        <para>Gets or sets the BindingContext for the item.</para>
      </summary>
      <value>A BindingContext for the item.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Bounds">
      <summary>
        <para>Gets the layout item&#39;s bounding rectangle.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> structure that specifies the bounding rectangle.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.CanMove(DevExpress.XtraLayout.Customization.LayoutItemDragController)">
      <summary>
        <para>Returns whether the layout item can be moved to a specific position.</para>
      </summary>
      <param name="controller">A <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> object whose settings determine the position to which the layout item should be moved.</param>
      <returns>true if the layout item can be moved to a specific position; otherwise, false.</returns>
    </member>
    <member name="E:DevExpress.XtraLayout.BaseLayoutItem.Click">
      <summary>
        <para>Fires when the current item is clicked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.Clone(DevExpress.XtraLayout.LayoutGroup,DevExpress.XtraLayout.ILayoutControl)">
      <summary>
        <para>Creates a copy of the current layout item.</para>
      </summary>
      <param name="cloneParent">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object that represents the group to which the created layout item belongs.</param>
      <param name="cloneOwner">An object that implements the DevExpress.XtraLayout.ILayoutControl interface, and owns the created layout item.</param>
      <returns>A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object that represents an exact copy of the current <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> instance.</returns>
    </member>
    <member name="E:DevExpress.XtraLayout.BaseLayoutItem.CustomDraw">
      <summary>
        <para>Allows you to custom paint the current item.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.CustomizationFormText">
      <summary>
        <para>Gets or sets the layout item&#39;s caption within the Customization Form.</para>
      </summary>
      <value>A string value that specifies the layout item&#39;s caption within the Customization Form.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.DataBindings">
      <summary>
        <para>Gets the data bindings for the layout item.</para>
      </summary>
      <value>A ControlBindingsCollection that contains the Binding objects for the item</value>
    </member>
    <member name="E:DevExpress.XtraLayout.BaseLayoutItem.DoubleClick">
      <summary>
        <para>Fires when the current item is clicked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.EndInit">
      <summary>
        <para>Finishes the component&#39;s initialization.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Expanded">
      <summary>
        <para>Not supported.</para>
      </summary>
      <value>Always True.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.GetDefaultText">
      <summary>
        <para>Gets the default value of  the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> property.</para>
      </summary>
      <returns>A string that specifies the default value of  the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> property.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.GetDisplayText">
      <summary>
        <para>Returns the <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/>&#39;s <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> value without HTML tags and hotkey prefixes (the &quot;&amp;&quot; symbol).</para>
      </summary>
      <returns>The <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/>&#39;s display text.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Height">
      <summary>
        <para>Gets or sets the layout item&#39;s height.</para>
      </summary>
      <value>An integer that specifies the layout item&#39;s height.</value>
    </member>
    <member name="E:DevExpress.XtraLayout.BaseLayoutItem.Hidden">
      <summary>
        <para>Fires after an item has been hidden.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.HideToCustomization">
      <summary>
        <para>Hides the item.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.BaseLayoutItem.Hiding">
      <summary>
        <para>Fires when the item is about to be hidden (when dragging the item and dropping it onto on the Customization Form).</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.Invalidate">
      <summary>
        <para>Invalidates the region which is occupied by the layout item.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.IsDisposing">
      <summary>
        <para>Gets whether the current item is being disposed of.</para>
      </summary>
      <value>true if the current item is being disposed of; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.IsGroup">
      <summary>
        <para>Gets whether the current layout item represents a Layout Group.</para>
      </summary>
      <value>true if the layout item represents a Layout Group; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.IsHidden">
      <summary>
        <para>Gets whether the layout item is hidden.</para>
      </summary>
      <value>true if the layout item is hidden; otheriwse, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.IsUpdateLocked">
      <summary>
        <para>Gets whether the layout item has been locked for updating.</para>
      </summary>
      <value>true if the layout item is locked; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Location">
      <summary>
        <para>Gets or sets the coordinates of the layout item&#39;s top left corner.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Point"/> structure that specifies the coordinates of the layout item&#39;s top left corner.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.MaxSize">
      <summary>
        <para>Gets or sets the maximum size of the layout item.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the item&#39;s maximum width and height.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.MinSize">
      <summary>
        <para>Gets or sets the item&#39;s minimum size.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the item&#39;s minimum width and height.</value>
    </member>
    <member name="E:DevExpress.XtraLayout.BaseLayoutItem.MouseDown">
      <summary>
        <para>Occurs when the mouse pointer is over a layout item and a mouse button is pressed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.BaseLayoutItem.MouseUp">
      <summary>
        <para>Occurs when the mouse button is released if it was pressed within a layout item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.Move(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Moves the item to the specified visual position within the LayoutControl.</para>
      </summary>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant that represents the layout item within the LayoutControl.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new group relative to the specified baseItem.</param>
      <returns>true if the item has been successfully moved to a new position; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.Move(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType,DevExpress.XtraLayout.Utils.MoveType)">
      <summary>
        <para>Moves the item to the specified visual position within the LayoutControl.</para>
      </summary>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant that represents the layout item within the LayoutControl.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new group relative to the specified baseItem.</param>
      <param name="moveType">A <see cref="T:DevExpress.XtraLayout.Utils.MoveType"/> enumeration value that specifies how a layout item is inserted to another position.</param>
      <returns>true, if the item has been successfully moved to a new position; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.Move(DevExpress.XtraLayout.Customization.LayoutItemDragController)">
      <summary>
        <para>Moves the item to the specified position within the LayoutControl.</para>
      </summary>
      <param name="controller">A <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> object whose settings determine the position to which the layout item should be moved.</param>
      <returns>true if the item has been successfully moved to a new position; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Name">
      <summary>
        <para>Gets or sets the layout item&#39;s name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the item&#39;s name.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.OptionsCustomization">
      <summary>
        <para>Contains options that specify which operations can be performed on the layout item at runtime.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.BaseLayoutItemCustomizationOptions"/> object that contains the corresponding options.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.OptionsIconToolTip">
      <summary>
        <para>This member is obsolete. Use the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.OptionsToolTip"/> property instead.</para>
      </summary>
      <value>A BaseLayoutItemToolTipOptions object.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.OptionsItemToolTip">
      <summary>
        <para>This member is obsolete. Use the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.OptionsToolTip"/> property instead.</para>
      </summary>
      <value>A BaseLayoutItemToolTipOptions object.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.OptionsTableLayoutItem">
      <summary>
        <para>Provides access to the options that specify how a layout item is arranged within a table layout.</para>
      </summary>
      <value>An object that contains the layout item&#39;s arrangement options.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.OptionsToolTip">
      <summary>
        <para>Contains options that allow tooltips to be assigned to layout items and their images.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip"/> object providing corresponding options.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Owner">
      <summary>
        <para>Gets or set the LayoutControl that owns the current layout item.</para>
      </summary>
      <value>A LayoutControl control.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Padding">
      <summary>
        <para>Gets or sets the amount of space in pixels between the item&#39;s borders and its contents.</para>
      </summary>
      <value>A DevExpress.XtraLayout.Utils.Padding object that contains inner indents between the layout item&#39;s borders and its contents.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.PaintAppearanceItemCaption">
      <summary>
        <para>Provides access to the appearance settings currently used to paint the layout item.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Parent">
      <summary>
        <para>Gets or sets the group that owns the current item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> or its descendant that owns the current item.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.ParentName">
      <summary>
        <para>Gets or sets the name of the item&#39;s parent.</para>
      </summary>
      <value>A string that specifies the name of the item&#39;s parent.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.RestoreFromCustomization">
      <summary>
        <para>Restores the current layout item from the Customization Form and adds it to the root group with the default layout type.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.RestoreFromCustomization(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Restores the current layout item from the Customization Form and adds it to the parent group of the specified item at the specified visual position.</para>
      </summary>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant which represents the layout item within the LayoutControl.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the restored item relative to the baseItem.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.RestoreFromCustomization(DevExpress.XtraLayout.Customization.LayoutItemDragController)">
      <summary>
        <para>Restores the current layout item from the Customization Form and displays it at the specified position within the LayoutControl.</para>
      </summary>
      <param name="controller">A <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> object whose settings determine the position to which the layout item should be moved.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.RestoreFromCustomization(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Restores the current layout item from the Customization Form and adds it to the specified group with the default layout type.</para>
      </summary>
      <param name="parent">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> descendant which represents the layout group that the current layout item will be added to.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Selected">
      <summary>
        <para>Gets or sets the selection state of the current item.</para>
      </summary>
      <value>true if the current layout item is selected; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.ShowInCustomizationForm">
      <summary>
        <para>Gets or sets whether the layout item is displayed within the Customization Form when the item is hidden.</para>
      </summary>
      <value>true if the layout item is displayed within the Customization Form when it&#39;s hidden; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.XtraLayout.BaseLayoutItem.Showing">
      <summary>
        <para>Fires when the item is about to be added to the layout (by dragging it from the Customization Form).</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.BaseLayoutItem.Shown">
      <summary>
        <para>Fires after an item has been made visible.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Size">
      <summary>
        <para>Gets or sets the layout item&#39;s size.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the item&#39;s width and height.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Spacing">
      <summary>
        <para>Gets or sets the outer indents of the item&#39;s borders.</para>
      </summary>
      <value>A DevExpress.XtraLayout.Utils.Padding object that contains the outer indents of the layout item&#39;s borders.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.StartNewLine">
      <summary>
        <para>Gets or sets whether the current item starts a new row within a flow layout.</para>
      </summary>
      <value>true, if the layout item starts a new row within the flow layout; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Tag">
      <summary>
        <para>Gets or sets an object that contains data on the current layout item.</para>
      </summary>
      <value>An object that contains data on the layout item.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Text">
      <summary>
        <para>Gets or sets the text associated with the layout item.</para>
      </summary>
      <value>A string that specifies the layout item&#39;s text.</value>
    </member>
    <member name="E:DevExpress.XtraLayout.BaseLayoutItem.TextChanged">
      <summary>
        <para>Fires after the layout item&#39;s text has been changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.TextLocation">
      <summary>
        <para>Gets or sets the item caption position.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.Locations"/> value that specifies the item caption position.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.TextSize">
      <summary>
        <para>Gets or sets the size of the text region.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the width and height of the item&#39;s text region.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.TextToControlDistance">
      <summary>
        <para>Gets or sets the distance between the control displayed within the layout item and the text region.</para>
      </summary>
      <value>An integer that specifies the distance, in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.TextVisible">
      <summary>
        <para>Gets or sets whether the text region is visible.</para>
      </summary>
      <value>true, if the text region is visible; otherwise, false. For <see cref="T:DevExpress.XtraLayout.LayoutControlItem"/>s, the TextVisible property&#39;s default value 
is dependent on the type of control embedded in this layout item.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.TypeName">
      <summary>
        <para>Gets the name of the current layout item&#39;s type.</para>
      </summary>
      <value>A string that represents the name of the current layout item&#39;s type.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItem.Update">
      <summary>
        <para>Updates the region occupied by the layout item.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.ViewInfo">
      <summary>
        <para>Gets the object which contains the information used to render the layout item.</para>
      </summary>
      <value>A DevExpress.XtraLayout.ViewInfo.BaseLayoutItemViewInfo object.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Visibility">
      <summary>
        <para>Gets or sets whether the layout item is visible within the LayoutControl in regular mode (when layout customization is not performed) and in customization mode.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.Utils.LayoutVisibility"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Visible">
      <summary>
        <para>Gets whether the layout item is visible.</para>
      </summary>
      <value>true if the layout item is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Width">
      <summary>
        <para>Gets or sets the layout item&#39;s width.</para>
      </summary>
      <value>An integer that specifies the layout item&#39;s width.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.X">
      <summary>
        <para>Gets or sets the X coordinate of the item&#39;s top left corner relative to the parent&#39;s top left corner.</para>
      </summary>
      <value>An integer which specifies the X coordinate of the item&#39;s top left corner.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItem.Y">
      <summary>
        <para>Gets or sets the Y coordinate of the item&#39;s top left corner relative to the parent&#39;s top left corner.</para>
      </summary>
      <value>An integer which specifies the Y coordinate of the item&#39;s top left corner.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.BaseLayoutItemCustomizationOptions">
      <summary>
        <para>Contains options that specify which operations can be performed on a layout item at runtime.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItemCustomizationOptions.#ctor(DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.BaseLayoutItemCustomizationOptions"/> class.</para>
      </summary>
      <param name="owner">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object that will own the created object.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItemCustomizationOptions.AllowDrag">
      <summary>
        <para>Gets or sets whether the current layout item/group can be dragged-and-dropped to another position.</para>
      </summary>
      <value>An ItemDragDropMode value that specifies whether the current item/group can be dragged-and-dropped to another position.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItemCustomizationOptions.AllowDrop">
      <summary>
        <para>Gets or sets whether other items can be dragged-and-dropped onto the current layout item/group.</para>
      </summary>
      <value>An ItemDragDropMode value that specifies whether other items can be dragged-and-dropped onto the current layout item/group.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItemCustomizationOptions.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies options from the specified object to the current object.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItemCustomizationOptions.CanDrag">
      <summary>
        <para>Returns whether the layout item can be dragged-and-dropped to another position.</para>
      </summary>
      <returns>true if the layout item can be dragged-and-dropped to another position; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItemCustomizationOptions.CanDrop">
      <summary>
        <para>Returns whether other layout items can be dragged-and-dropped onto the current layout item.</para>
      </summary>
      <returns>true if other layout items can be dragged-and-dropped onto the current layout item; otherwise, false.</returns>
    </member>
    <member name="T:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip">
      <summary>
        <para>Contains options that allow tooltips to be assigned to layout items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip.AllowHtmlString">
      <summary>
        <para>Gets or sets whether HTML formatting tags can be used to format the tooltip&#39;s text.</para>
      </summary>
      <value>True if HTML formatting tags can be used to format the tooltip&#39;s text; False if not; Default uses the <see cref="P:DevExpress.Utils.ToolTipController.AllowHtmlText"/> global setting.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies options from the specified object to the current object.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip.CanShowIconToolTip">
      <summary>
        <para>Returns whether a tooltip for a layout item&#39;s image can be displayed.</para>
      </summary>
      <returns>true if a tooltip for a layout item&#39;s image can be displayed; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip.CanShowToolTip">
      <summary>
        <para>Returns whether a tooltip for a layout item&#39;s caption can be displayed.</para>
      </summary>
      <returns>true if a tooltip for a layout item&#39;s caption can be displayed; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip.EnableIconToolTip">
      <summary>
        <para>Gets or sets whether a tooltip displayed for the layout item&#39;s image is enabled.</para>
      </summary>
      <value>true if a tooltip displayed for the layout item&#39;s image is enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip.IconAllowHtmlString">
      <summary>
        <para>Gets or sets whether HTML tags can be used to format the tooltip text displayed for the item&#39;s icon.</para>
      </summary>
      <value>True if HTML tags can be used to format the tooltip text; False if not; Default uses the <see cref="P:DevExpress.Utils.ToolTipController.AllowHtmlText"/> global setting.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip.IconImmediateToolTip">
      <summary>
        <para>Gets or sets whether the tooltip for the layout item&#39;s icon is displayed immediately or after a delay.</para>
      </summary>
      <value>true, if the tooltip is displayed immediately; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip.IconToolTip">
      <summary>
        <para>Specifies the text of the tooltip displayed when the mouse cursor hovers over the layout item&#39;s image.</para>
      </summary>
      <value>A string that specifies the tooltip&#39;s text.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip.IconToolTipIconType">
      <summary>
        <para>Gets or sets the type of the icon for the tooltip that is displayed when the mouse cursor hovers over the layout item&#39;s image.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.ToolTipIconType"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip.IconToolTipTitle">
      <summary>
        <para>Gets or sets the title of the tooltip that is displayed when the mouse cursor hovers over the layout item&#39;s image.</para>
      </summary>
      <value>A string that specifies the tooltip&#39;s title.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip.ImmediateToolTip">
      <summary>
        <para>Gets or sets whether the tooltip is displayed immediately or after a delay.</para>
      </summary>
      <value>true, if the tooltip is displayed immediately; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip.ShouldSerializeCore(System.ComponentModel.IComponent)">
      <summary>
        <para>This method supports the internal infrastructure and is not intended to be called directly from your code.</para>
      </summary>
      <param name="owner">An IComponent object.</param>
      <returns>A Boolean value.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip.ToolTip">
      <summary>
        <para>Specifies the text of the tooltip displayed when the mouse cursor hovers over the layout item&#39;s caption, and by default over its image.</para>
      </summary>
      <value>A string that specifies the tooltip&#39;s text.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip.ToolTipIconType">
      <summary>
        <para>Gets or sets the type of the icon for the tooltip that is displayed when the mouse cursor hovers over the layout item&#39;s caption, and by default over its image.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.ToolTipIconType"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip.ToolTipTitle">
      <summary>
        <para>Gets or sets the title of the tooltip that is displayed when the mouse cursor hovers over the layout item&#39;s caption, and by default over its image.</para>
      </summary>
      <value>A string that specifies the tooltip&#39;s title.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.ColumnDefinition">
      <summary>
        <para>Defines a column in a table layout.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.ColumnDefinition.#ctor">
      <summary>
        <para>Initializes a new instance of a <see cref="T:DevExpress.XtraLayout.ColumnDefinition"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.ColumnDefinition.#ctor(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Initializes a new instance of a <see cref="T:DevExpress.XtraLayout.ColumnDefinition"/> class with the specified owner.</para>
      </summary>
      <param name="owner">A layout group that owns the created <see cref="T:DevExpress.XtraLayout.ColumnDefinition"/> object.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.ColumnDefinition.#ctor(DevExpress.XtraLayout.LayoutGroup,System.Double,System.Windows.Forms.SizeType)">
      <summary>
        <para>Initializes a new instance of a <see cref="T:DevExpress.XtraLayout.ColumnDefinition"/> class with the specified settings.</para>
      </summary>
      <param name="owner">A layout group that owns the created <see cref="T:DevExpress.XtraLayout.ColumnDefinition"/> object.</param>
      <param name="size">The width of the created column. This value is assigned to the <see cref="P:DevExpress.XtraLayout.ColumnDefinition.Width"/> property.</param>
      <param name="sizingType">The size type of the created column. This value is assigned to the <see cref="P:DevExpress.XtraLayout.ColumnDefinition.SizeType"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.ColumnDefinition.SizeType">
      <summary>
        <para>Gets or sets the column&#39;s size type in a table layout.</para>
      </summary>
      <value>A value that specifies the column&#39;s size type.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.ColumnDefinition.Width">
      <summary>
        <para>Gets or sets the column width, in pixels or as a percentage, according to the <see cref="P:DevExpress.XtraLayout.ColumnDefinition.SizeType"/>.</para>
      </summary>
      <value>A value that specifies the column width.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.ColumnDefinitions">
      <summary>
        <para>A column collection within a table layout.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.ColumnDefinitions.#ctor(DevExpress.XtraLayout.ColumnDefinitions,DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Initializes a new instance of a <see cref="T:DevExpress.XtraLayout.ColumnDefinitions"/> class with the settings of the specified source object and with the specified owner.</para>
      </summary>
      <param name="source">An object whose settings are used to initialize the created object.</param>
      <param name="ownerGroup">A layout group that owns the created object.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.ColumnDefinitions.#ctor(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Initializes a new instance of a <see cref="T:DevExpress.XtraLayout.ColumnDefinitions"/> class with the specified owner.</para>
      </summary>
      <param name="owner">A layout group that owns the created <see cref="T:DevExpress.XtraLayout.ColumnDefinitions"/> object.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.ColumnDefinitions.Add(DevExpress.XtraLayout.ColumnDefinition)">
      <summary>
        <para>Adds a specified column to the table layout.</para>
      </summary>
      <param name="columnDefinition">An object that specifies the column to be added.</param>
      <returns>The position into which the new element was inserted, or -1 to indicate that the item was not inserted into the collection.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.ColumnDefinitions.AddRange(DevExpress.XtraLayout.ColumnDefinition[])">
      <summary>
        <para>Adds an array of columns to the table layout.</para>
      </summary>
      <param name="items">An array of <see cref="T:DevExpress.XtraLayout.ColumnDefinition"/> objects to be added to the table layout.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.ColumnDefinitions.Insert(System.Int32,DevExpress.XtraLayout.ColumnDefinition,System.Boolean)">
      <summary>
        <para>Inserts an element into the collection at the specified index.</para>
      </summary>
      <param name="index">The zero-based index at which a columnDefinition should be inserted.</param>
      <param name="columnDefinition">The <see cref="T:DevExpress.XtraLayout.ColumnDefinition"/> to insert.</param>
      <param name="updateItemIndexes">true, to recalculate the <see cref="P:DevExpress.XtraLayout.OptionsTableLayoutItem.ColumnIndex"/> property of items in the current layout group; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.ColumnDefinitions.Item(System.Int32)">
      <summary>
        <para>Provides indexed access to columns within the table layout.</para>
      </summary>
      <param name="index">A zero-based integer value that specifies the index of the required column.</param>
      <value>A column at the specified position within the collection.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.ColumnDefinitions.RemoveAt(System.Int32,System.Boolean)">
      <summary>
        <para>Removes the element at the specified index.</para>
      </summary>
      <param name="index">The zero-based index of the element to remove.</param>
      <param name="updateItemIndexes">true, to recalculate the <see cref="P:DevExpress.XtraLayout.OptionsTableLayoutItem.ColumnIndex"/> property of items in the current layout group; otherwise, false.</param>
    </member>
    <member name="T:DevExpress.XtraLayout.ControlMaxSizeCalcMode">
      <summary>
        <para>Contains values that specify how default maximum size constraints are calculated for controls that implement the <see cref="T:DevExpress.Utils.Controls.IXtraResizableControl"/> interface.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.ControlMaxSizeCalcMode.CombineControlMaximumSizeAndIXtraResizableMaxSize">
      <summary>
        <para>The default maximum width is calculated for a control as the maximum of two values: Control.MaximumSize.Width and IXtraResizableControl.MaxSize.Width.The default maximum height is calculated for a control as the maximum of two values: Control.MaximumSize.Height and IXtraResizableControl.MaxSize.Height.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.ControlMaxSizeCalcMode.UseControlMaximumSize">
      <summary>
        <para>The default maximum width and height are specified by the Control.MaximumSize.Width and Control.MaximumSize.Height values, respectively.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.ControlMaxSizeCalcMode.UseControlMaximumSizeIfNotZero">
      <summary>
        <para>The default maximum width is specified by the Control.MaximumSize.Width property if it is not set to 0; otherwise, the default maximum width is specified by the IXtraResizableControl.MaxSize.Width property.The default maximum height is specified by the Control.MaximumSize.Height property if it is not set to 0; otherwise, the default maximum height is specified by the IXtraResizableControl.MaxSize.Height property.</para>
      </summary>
    </member>
    <member name="N:DevExpress.XtraLayout.Converter">
      <summary>
        <para>Contains classes used to convert layouts of controls.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.Converter.LayoutConverter">
      <summary>
        <para>Converts a regular layout of controls to the <see cref="T:DevExpress.XtraLayout.LayoutControl"/>, and vice versa.</para>
      </summary>
    </member>
    <member name="N:DevExpress.XtraLayout.Customization">
      <summary>
        <para>Contains classes that are used to customize the layout of the XtraLayout control.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.Customization.LayoutItemDragController">
      <summary>
        <para>Contains methods to move a layout item to a position next to another layout item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.Customization.LayoutItemDragController.#ctor(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para>Initializes a new <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> instance with settings specifying that the_dragItem_ should be inserted horizontally after the baseItem, occupying its largest part (typically, half the region).</para>
      </summary>
      <param name="dragItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object to be moved. This object is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> property.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object relative to which the dragItem will be positioned. This object is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.Item"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.Customization.LayoutItemDragController.#ctor(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertLocation,DevExpress.XtraLayout.Utils.LayoutType)">
      <summary>
        <para>Initializes a new <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> instance with settings specifying that the dragItem should be inserted next to the baseItem, occupying its largest part (typically, half the region).</para>
      </summary>
      <param name="dragItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object to be moved. This object is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> property.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object relative to which the dragItem will be positioned. This object is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.Item"/> property.</param>
      <param name="insertLocation">A <see cref="T:DevExpress.XtraLayout.Utils.InsertLocation"/> value that specifies whether the dragItem is inserted before or after the baseItem. This value is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.InsertLocation"/> property.</param>
      <param name="layoutType">A <see cref="T:DevExpress.XtraLayout.Utils.LayoutType"/> value that specifies whether the dragItem is inserted horizontally or vertically next to the baseItem. This value is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.LayoutType"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.Customization.LayoutItemDragController.#ctor(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.MoveType,DevExpress.XtraLayout.Utils.InsertLocation,DevExpress.XtraLayout.Utils.LayoutType)">
      <summary>
        <para>Initializes a new <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> instance with settings specifying that the dragItem should be inserted next to the baseItem.</para>
      </summary>
      <param name="dragItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object to be moved. This object is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> property.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object relative to which the dragItem will be positioned. This object is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.Item"/> property.</param>
      <param name="moveType">A <see cref="T:DevExpress.XtraLayout.Utils.MoveType"/> value that specifies whether the dragItem is inserted inside or outside the baseItem. This value is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.MoveType"/> property.</param>
      <param name="insertLocation">A <see cref="T:DevExpress.XtraLayout.Utils.InsertLocation"/> value that specifies whether the dragItem is inserted before or after the baseItem. This value is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.InsertLocation"/> property.</param>
      <param name="layoutType">A <see cref="T:DevExpress.XtraLayout.Utils.LayoutType"/> value that specifies whether the dragItem is inserted horizontally or vertically next to the baseItem. This value is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.LayoutType"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.Customization.LayoutItemDragController.#ctor(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.MoveType,DevExpress.XtraLayout.Utils.InsertLocation,DevExpress.XtraLayout.Utils.LayoutType,System.Drawing.Size)">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <param name="dragItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object to be moved. This object is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> property.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object relative to which the dragItem will be positioned.  This object is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.Item"/> property.</param>
      <param name="moveType">A <see cref="T:DevExpress.XtraLayout.Utils.MoveType"/> value that specifies whether the dragItem is inserted inside or outside the baseItem. This value is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.MoveType"/> property.</param>
      <param name="insertLocation">A <see cref="T:DevExpress.XtraLayout.Utils.InsertLocation"/> value that specifies whether the dragItem is inserted before or after the baseItem. This value is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.InsertLocation"/> property.</param>
      <param name="layoutType">A <see cref="T:DevExpress.XtraLayout.Utils.LayoutType"/> value that specifies whether the draItem is inserted horizontally or vertically next to the baseItem. This value is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.LayoutType"/> property.</param>
      <param name="rating"></param>
    </member>
    <member name="M:DevExpress.XtraLayout.Customization.LayoutItemDragController.#ctor(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Customization.LayoutItemDragController)">
      <summary>
        <para>Initializes a new <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> instance with settings specifying that the dragItem should be inserted next to another item, as specified by the settings of the controller parameter.</para>
      </summary>
      <param name="dragItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object to be moved. This object is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> property.</param>
      <param name="controller">A <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> object, whose settings determine how a dragItem is inserted next to another item.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.Customization.LayoutItemDragController.#ctor(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.LayoutGroup,System.Drawing.Point)">
      <summary>
        <para>Initializes a new <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> instance with settings specifying that the dragItem should be inserted at a certain position within a specific group.</para>
      </summary>
      <param name="dragItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object to be moved. This object is used to initialize the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> property.</param>
      <param name="group">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object into which the dragItem will be inserted.</param>
      <param name="pt">A Point where the dragItem will be inserted into the group.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.Customization.LayoutItemDragController.Drag">
      <summary>
        <para>Inserts the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> to a position, specified by the settings of the current <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> object. This method cannot be used to move items that do not belong to a <see cref="T:DevExpress.XtraLayout.LayoutControl"/>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragBounds">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem">
      <summary>
        <para>Gets the item to be inserted to a position, specified by the settings of the <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> object.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object that represents the item to be inserted.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragOutsideSize">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragWildItem">
      <summary>
        <para>Inserts the orphan <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> to a position specified by the settings of the current <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> object. This method cannot be used to move items that belong to a <see cref="T:DevExpress.XtraLayout.LayoutControl"/>.</para>
      </summary>
      <returns>true if the item has been successfully inserted; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.HitInfo">
      <summary>
        <para>Gets an object that identifies an element located at the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.HitPoint"/> point. This member is in effect if the current <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> object has been initiailized using a constructor with a Point parameter.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo"/> object that provides information on the element located at the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.HitPoint"/> point.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.HitPoint">
      <summary>
        <para>Gets the point at which the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> will be inserted. This member is in effect if the current <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> object has been initialized using a constructor with a Point parameter.</para>
      </summary>
      <value>A Point at which the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> will be inserted.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.InsertLocation">
      <summary>
        <para>Gets whether the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> is inserted before or after the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.Item"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.Utils.InsertLocation"/> value that specifies whether the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> is inserted before or after the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.Item"/>.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.Item">
      <summary>
        <para>Gets the item next to which the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> is inserted.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object that identifies the item next to which the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> is inserted.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.LayoutType">
      <summary>
        <para>Gets whether the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> is inserted horizontally or vertically next to the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.Item"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.Utils.LayoutType"/> value that specifies whether the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> is inserted horizontally or vertically next to the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.Item"/>.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.MoveType">
      <summary>
        <para>Gets whether the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> is inserted inside or outside the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.Item"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.Utils.MoveType"/> value that specifies whether the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.DragItem"/> is inserted inside or outside the <see cref="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.Item"/>.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.Rating">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.Customization.LayoutItemDragController.ShouldRestoreOriginalSize">
      <summary>
        <para>For internal use.</para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraLayout.CustomizationModes">
      <summary>
        <para>Enumerates the runtime customization modes for a <see cref="T:DevExpress.XtraLayout.LayoutControl"/>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.CustomizationModes.Default">
      <summary>
        <para>Default customization mode. Customization commands are provided using context menus.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.CustomizationModes.Quick">
      <summary>
        <para>Quick customization mode. Customization commands are available in the touch-friendly Customization Form.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.EmptySpaceItem">
      <summary>
        <para>Used to insert empty regions in the Layout Control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.EmptySpaceItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.EmptySpaceItem"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.EmptySpaceItem.#ctor(DevExpress.XtraLayout.LayoutControlGroup)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.EmptySpaceItem"/> class with the specified parent group.</para>
      </summary>
      <param name="parent">A <see cref="T:DevExpress.XtraLayout.LayoutControlGroup"/> object that owns the created item.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.AllowHotTrack">
      <summary>
        <para>This property is not supported by the <see cref="T:DevExpress.XtraLayout.EmptySpaceItem"/> class.</para>
      </summary>
      <value>false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.ContentVisible">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.Control">
      <summary>
        <para>Not supported.</para>
      </summary>
      <value>null.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.ControlAlignment">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.ControlMaxSize">
      <summary>
        <para>Not supported.</para>
      </summary>
      <value>A <see cref="F:System.Drawing.Size.Empty"/> structure.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.ControlMinSize">
      <summary>
        <para>Not supported.</para>
      </summary>
      <value>A <see cref="F:System.Drawing.Size.Empty"/> structure.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.FillControlToClientArea">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraLayout.EmptySpaceItem.GetDefaultText">
      <summary>
        <para>Returns the default value of the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> property.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value that specifies the default value of  the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> property.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.HighlightFocusedItem">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.Image">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.ImageAlignment">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.ImageIndex">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.ImageOptions">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.ImageToTextDistance">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.MaxSize">
      <summary>
        <para>Gets or sets the maximum size of the <see cref="T:DevExpress.XtraLayout.EmptySpaceItem"/>.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the item&#39;s maximum width and height.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.MinSize">
      <summary>
        <para>Gets or sets the minimum size of the <see cref="T:DevExpress.XtraLayout.EmptySpaceItem"/>.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the item&#39;s minimum width and height.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.TextLocation">
      <summary>
        <para>Not supported.</para>
      </summary>
      <value>The  <see cref="F:DevExpress.Utils.Locations.Default"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.TextToControlDistance">
      <summary>
        <para>Not supported.</para>
      </summary>
      <value>Zero.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.TextVisible">
      <summary>
        <para>Gets or sets whether the text region is visible.</para>
      </summary>
      <value>true if the text region is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.TrimClientAreaToControl">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.EmptySpaceItem.TypeName">
      <summary>
        <para>Gets the name of the item&#39;s type.</para>
      </summary>
      <value>The EmptySpaceItem value.</value>
    </member>
    <member name="N:DevExpress.XtraLayout.HitInfo">
      <summary>
        <para>Contains the classes which provide the functionality used to determine the hit information in the XtraLayout control.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo">
      <summary>
        <para>Contains information on a specific point within a Layout Control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.#ctor(DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo"/> class by copying the settings from the specified hitInfo object.</para>
      </summary>
      <param name="hitInfo">A <see cref="T:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo"/> object whose settings are copied to the object being created.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.#ctor(System.Drawing.Point,DevExpress.XtraLayout.HitInfo.LayoutItemHitTest,DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo"/> class with the specified settings.</para>
      </summary>
      <param name="point">A <see cref="T:System.Drawing.Point"/> structure that represents the test point. This value is assigned to the <see cref="P:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.HitPoint"/> property.</param>
      <param name="hitType">A <see cref="T:DevExpress.XtraLayout.HitInfo.LayoutItemHitTest"/> value that specifies the type of a layout item&#39;s element located under the test point. This value is assigned to the <see cref="P:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.HitType"/> property.</param>
      <param name="item">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object that represents the layout item located under the test point. This value is assigned to the <see cref="P:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.Item"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.HitPoint">
      <summary>
        <para>Gets the test point.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Point"/> structure that specifies the current test point.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.HitType">
      <summary>
        <para>Gets the type of the layout item&#39;s element located under the test point.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.HitInfo.LayoutItemHitTest"/> value that specifies the type of the element located under the test point.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.IsExpandButton">
      <summary>
        <para>Gets whether the test point belongs to a group&#39;s expand button.</para>
      </summary>
      <value>true if the test point belongs to a group&#39;s expand button; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.IsGroup">
      <summary>
        <para>Gets whether the current test point belongs to a Layout Group.</para>
      </summary>
      <value>true if the current test point belongs to a Layout Group; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.IsLastRow">
      <summary>
        <para>Gets whether the test point belongs to a tab of a <see cref="T:DevExpress.XtraLayout.TabbedControlGroup"/> and this tab resides within the last tab row. This property is in effect if tabs are allowed to be arranged in multiple rows (see <see cref="P:DevExpress.XtraLayout.TabbedGroup.MultiLine"/>).</para>
      </summary>
      <value>true, if a tab resides within the last tab row; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.IsSizing">
      <summary>
        <para>Gets whether the current layout item is being or about to be resized by dragging the item&#39;s border with the mouse.</para>
      </summary>
      <value>true if the current layout item is being or about to be resized; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.IsTabbedGroup">
      <summary>
        <para>Gets whether the current test point belongs to a Tabbed Group.</para>
      </summary>
      <value>true if the current test point belongs to a Tabbed Group; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.Item">
      <summary>
        <para>Gets the layout item (regular group or tabbed group) positioned under the test point.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant that specifies the layout item positioned under the test point.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.TabPageIndex">
      <summary>
        <para>If the test point belongs to a tab page header, gets the index of the corresponding tab page.</para>
      </summary>
      <value>An integer which specifies the index of a tab page. -1 if the test point doesn&#39;t belong to a tab page header.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.HitInfo.LayoutItemHitTest">
      <summary>
        <para>Lists the values that identify the Layout Control&#39;s visual elements.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.HitInfo.LayoutItemHitTest.ControlsArea">
      <summary>
        <para>The test point belongs to a control which is located within a layout item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.HitInfo.LayoutItemHitTest.HSizing">
      <summary>
        <para>A layout item is being or about to be resized horizontally. See the <see cref="P:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.IsSizing"/> topic.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.HitInfo.LayoutItemHitTest.Item">
      <summary>
        <para>The test point belongs to a layout item, group or tabbed group.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.HitInfo.LayoutItemHitTest.None">
      <summary>
        <para>The test point does not belong to any visual element or is outside the LayoutControl.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.HitInfo.LayoutItemHitTest.TextArea">
      <summary>
        <para>The test point belongs to a layout item&#39;s text area.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.HitInfo.LayoutItemHitTest.VSizing">
      <summary>
        <para>A layout item is being or about to be resized vertically. See the <see cref="P:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo.IsSizing"/> topic.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.LayoutAppearanceCollection">
      <summary>
        <para>Provides the appearance settings used to paint a LayoutControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutAppearanceCollection.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.LayoutAppearanceCollection"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutAppearanceCollection.Control">
      <summary>
        <para>Gets the appearance settings used to paint the contents of all the editors owned by the LayoutControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the editor&#39;s contents.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutAppearanceCollection.ControlDisabled">
      <summary>
        <para>Gets the appearance settings used to paint the contents of the editors owned by the LayoutControl when they are disabled. This option is only in effect for DevExpress controls that support the Style Controller mechanism.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the contents of the editors when they are disabled.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutAppearanceCollection.ControlDropDown">
      <summary>
        <para>Gets the appearance settings used to paint the popup window of all the editors owned by the LayoutControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the editor&#39;s popup window.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutAppearanceCollection.ControlDropDownHeader">
      <summary>
        <para>Gets the appearance settings used to paint the header panel within the popup windows of all the editors owned by the LayoutControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the header panel within the popup window.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutAppearanceCollection.ControlFocused">
      <summary>
        <para>Gets the appearance settings used to paint the currently focused editor within the LayoutControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance setting used to paint the currently focused editor within the LayoutControl.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutAppearanceCollection.ControlReadOnly">
      <summary>
        <para>Gets the appearance settings used to paint the contents of the editors owned by the LayoutControl when they are in the read-only state. This option is only in effect for DevExpress controls that support the Style Controller mechanism.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutAppearanceCollection.DisabledLayoutGroupCaption">
      <summary>
        <para>Gets the appearance settings used to paint captions of disabled layout groups.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that contains corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutAppearanceCollection.DisabledLayoutItem">
      <summary>
        <para>Gets the appearance settings used to paint captions of disabled layout items or items that display disabled controls.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that contains corresponding appearance settings.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.LayoutControl">
      <summary>
        <para>Creates and maintains a consistent layout of controls. See Layout and Data Layout Controls.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.LayoutControl"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.#ctor(System.Boolean)">
      <summary>
        <para>This constructor supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <param name="createFast">A Boolean value.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.#ctor(System.Boolean,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.LayoutControl"/> class with the specified settings.</para>
      </summary>
      <param name="fAllowUseSplitters">true to allow use splitters; otherwise, false.</param>
      <param name="fAllowUseTabbedGroup">true to allow use tabbed groups; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.About">
      <summary>
        <para>Activates the layout control&#39;s About dialog box.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddGroup">
      <summary>
        <para>Creates a new layout group with the default layout type and adds it to the root group.</para>
      </summary>
      <returns>The newly created group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddGroup(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Creates a new layout group at a position relative to an item within the root group.</para>
      </summary>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the root group) next to which a new group is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new group relative to the specified item.</param>
      <returns>The newly created group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddGroup(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Inserts the specified layout group to the root group.</para>
      </summary>
      <param name="newGroup">The group to add to the root group.</param>
      <returns>The added group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddGroup(DevExpress.XtraLayout.LayoutGroup,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Inserts the specified layout group at a position relative to an item within the root group.</para>
      </summary>
      <param name="newGroup">The group to add to the root group.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the root group) next to which the &#39;newGroup&#39; is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new group relative to the specified item.</param>
      <returns>The added group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddGroup(System.String)">
      <summary>
        <para>Creates a new layout group with the default layout type and the specified caption, and adds it to the root group.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value that specifies the group&#39;s caption. This value is assigned to the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> property.</param>
      <returns>The newly created group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddGroup(System.String,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Creates a new layout group with the specified caption at a position relative to an item within the root group.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value that specifies the group&#39;s caption. This value is assigned to the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> property.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the root group) next to which a new group is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new group relative to the specified item.</param>
      <returns>The newly created group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddItem">
      <summary>
        <para>Creates a new layout item with the default layout type and adds it to the root group.</para>
      </summary>
      <returns>The newly created layout item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddItem(DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para>Inserts the specified layout item to the root group.</para>
      </summary>
      <param name="newItem">The item to add to the root group.</param>
      <returns>The added item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddItem(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Inserts the specified layout item at a position relative to an item within the root group.</para>
      </summary>
      <param name="newItem">The item to add to the root group.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the root group) next to which the &#39;newItem&#39; is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new item relative to the specified item.</param>
      <returns>The added item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddItem(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Creates a new layout item at a position relative to another item within the root group.</para>
      </summary>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the root group) next to which a new item is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new item relative to the specified baseItem.</param>
      <returns>The newly created layout item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddItem(System.String)">
      <summary>
        <para>Creates a new layout item with the specified text and adds it to the root group.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value that specifies the layout item&#39;s text.</param>
      <returns>The newly created layout item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddItem(System.String,System.Windows.Forms.Control)">
      <summary>
        <para>Creates a new layout item with the specified text and control, and adds it to the root group.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value that specifies the layout item&#39;s text.</param>
      <param name="control">The control to display in the new layout item. This value is used to initialize the <see cref="P:DevExpress.XtraLayout.LayoutControlItem.Control"/> property.</param>
      <returns>The newly created layout item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddItem(System.String,System.Windows.Forms.Control,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Inserts the specified layout item at a position relative to another item within the root group. Allows a new text to be specified for the item.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value that specifies the new text to be assigned to the added item. The parameter is ignored if the value represents an empty string.</param>
      <param name="control">This parameter is ignored.</param>
      <param name="newItem">The item to add to the root group.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the root group) next to which the &#39;newItem&#39; is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new item relative to the specified item.</param>
      <returns>The newly created layout item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddItem(System.String,System.Windows.Forms.Control,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Creates a new layout item with the specified text and control, and adds it at a position relative to another item within the root group.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value that specifies the layout item&#39;s text.</param>
      <param name="control">The control to display in the new layout item. This value is used to initialize the <see cref="P:DevExpress.XtraLayout.LayoutControlItem.Control"/> property.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the root group) next to which a new item is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new item relative to the specified baseItem.</param>
      <returns>The newly created layout item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddTabbedGroup">
      <summary>
        <para>Creates a new tabbed group with the default layout type and adds it to the root group.</para>
      </summary>
      <returns>The newly created group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddTabbedGroup(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Creates a new tabbed group and adds it at a position relative to an item within the root group.</para>
      </summary>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the root group) next to which a new group is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new tabbed group relative to the baseItem.</param>
      <returns>The newly created group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddTabbedGroup(DevExpress.XtraLayout.TabbedGroup)">
      <summary>
        <para>Inserts the specified tabbed group to the root group.</para>
      </summary>
      <param name="newTabbedGroup">The group to add to the root group.</param>
      <returns>The added group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AddTabbedGroup(DevExpress.XtraLayout.TabbedGroup,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Inserts the specified tabbed group at a position relative to an item within the root group.</para>
      </summary>
      <param name="newTabbedGroup">The group to add to the root group.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the root group) next to which the &#39;newTabbedGroup&#39; is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new tabbed group relative to the specified item.</param>
      <returns>The added group.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.AllowCustomization">
      <summary>
        <para>Gets or sets whether customization mode can be activated by end-users at runtime.</para>
      </summary>
      <value>true, if end-users can activate customization mode at runtime; otherwise, false</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.AllowCustomizationMenu">
      <summary>
        <para>Gets or sets whether the customization menu can be invoked by end-users.</para>
      </summary>
      <value>true, to allow end-users to invoke the customization menu; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.AllowTouchGestures">
      <summary>
        <para>Gets or sets whether the <see cref="T:DevExpress.XtraLayout.LayoutControl"/> allows touch gestures.</para>
      </summary>
      <value>true, to enable touch gestures; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.Appearance">
      <summary>
        <para>Provides access to the properties that control the appearance of the controls located within the layout control.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.LayoutAppearanceCollection"/> object which provides the appearance settings for the layout controls.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.AppearanceController">
      <summary>
        <para>Gets an object that calculates appearances for layout items based on the appearance settings of items and their parent layout groups.</para>
      </summary>
      <value>An DevExpress.XtraLayout.Helpers.AppearanceController object.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.AssignNames">
      <summary>
        <para>Assigns the unique names to the items contained within the <see cref="P:DevExpress.XtraLayout.LayoutControl.Items"/> collection.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.AutoScroll">
      <summary>
        <para>Gets or sets whether the layout control will allow an end-user to scroll to any controls placed outside of its visible boundaries.</para>
      </summary>
      <value>true to enable the auto-scrolling feature; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.AutoScrollMargin">
      <summary>
        <para>Not supported.</para>
      </summary>
      <value>A <see cref="F:System.Drawing.Size.Empty"/> structure.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.AutoScrollMinSize">
      <summary>
        <para>Not supported.</para>
      </summary>
      <value>A <see cref="F:System.Drawing.Size.Empty"/> structure.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.AutoScrollPosition">
      <summary>
        <para>This property is not supported by the <see cref="T:DevExpress.XtraLayout.LayoutControl"/> class.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.AutoSize">
      <summary>
        <para>Gets or sets whether the control&#39;s size is automatically calculated based on its content.</para>
      </summary>
      <value>true, if the control&#39;s size is automatically calculated; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.AutoSizeMode">
      <summary>
        <para>Gets or sets how the control is resized when automatic resizing is enabled.</para>
      </summary>
      <value>A value that specifies how the control is automatically resized.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.BackColor">
      <summary>
        <para>Gets or sets the control&#39;s background color.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> that represents the control&#39;s background color.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.BackgroundImage">
      <summary>
        <para>Gets or sets the layout control&#39;s background image.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Image"/> descendant that represents the layout control&#39;s background image.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.BackgroundImageLayout">
      <summary>
        <para>Gets or sets the background image layout as defined in the ImageLayout enumeration.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.ImageLayout"/> value.</value>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.BeforeLoadLayout">
      <summary>
        <para>Occurs before a layout is restored from storage (a stream, xml file or the system registry).</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.BeginInit">
      <summary>
        <para>Starts the layout control&#39;s runtime initialization.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.BeginUpdate">
      <summary>
        <para>Locks the <see cref="T:DevExpress.XtraLayout.LayoutControl"></see> object by preventing visual updates of the object and its elements until the EndUpdate method is called.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.BestFit">
      <summary>
        <para>Proportionally resizes layout items that are displayed in a single row.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.BindingContext">
      <summary>
        <para>Gets or sets the <see cref="T:System.Windows.Forms.BindingContext"/> for the control.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.BindingContext"/> for the control.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.CalcHitInfo(System.Drawing.Point)">
      <summary>
        <para>Returns information on the layout elements located at the specified point.</para>
      </summary>
      <param name="hitPoint">A <see cref="T:System.Drawing.Point"/> structure which specifies the test point coordinates relative to the layout controls top-left corner.</param>
      <returns>A <see cref="T:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo"/> object which contains information about the layout elements located at the test point.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.CaptionImages">
      <summary>
        <para>Gets or sets a collection of images that can be displayed within layout group captions.</para>
      </summary>
      <value>An object which represents the image source.</value>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.Changed">
      <summary>
        <para>Fires when a property of the layout control is changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.Changing">
      <summary>
        <para>Fires when a property of the layout control is about to be changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.Clear">
      <summary>
        <para>Disposes all the layout items and their controls owned by the Layout Control. Hidden items are not removed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.Clear(System.Boolean,System.Boolean)">
      <summary>
        <para>Clears items, allowing hidden items to be removed.</para>
      </summary>
      <param name="clearHiddenItems">true to clear the <see cref="P:DevExpress.XtraLayout.LayoutControl.HiddenItems"/> collection; otherwise, false.</param>
      <param name="disposeControls">true to dispose of controls associated with layout items; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.ClientHeight">
      <summary>
        <para>Gets the layout control&#39;s client height.</para>
      </summary>
      <value>An integer value that specifies the layout control&#39;s client height.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.ClientWidth">
      <summary>
        <para>Gets the layout control&#39;s client width.</para>
      </summary>
      <value>An integer value that specifies the layout control&#39;s client width.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.ConstraintsManager">
      <summary>
        <para>Gets the constraints manager.</para>
      </summary>
      <value>A DevExpress.XtraLayout.ConstraintsManager object.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.CreateCustomizationForm">
      <summary>
        <para>Creates and returns a new Customization Form.</para>
      </summary>
      <returns>A DevExpress.XtraLayout.Customization.UserCustomizationForm object which represents the created Customization Form.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.CreateEmptySpaceItem(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Creates an Empty Space Items within the specified group.</para>
      </summary>
      <param name="parent">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object which specifies the parent group for the created item.</param>
      <returns>An <see cref="T:DevExpress.XtraLayout.EmptySpaceItem"/> object that has been created.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.CreateLayoutGroup(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Creates a new Layout Groups within the specified group.</para>
      </summary>
      <param name="parent">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object which specifies the parent group for the created group.</param>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutControlGroup"/> object which represents the newly created group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.CreateLayoutItem(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Creates a new Layout Item within the specified group.</para>
      </summary>
      <param name="parent">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object which specifies the parent group for the created item.</param>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutControlItem"/> object which represents the newly created item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.CreateSplitterItem(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Creates a <see cref="T:DevExpress.XtraLayout.SplitterItem"/> within the specified group.</para>
      </summary>
      <param name="parent">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object which represents the parent group for the created item.</param>
      <returns>A <see cref="T:DevExpress.XtraLayout.SplitterItem"/> object that has been created.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.CreateTabbedGroup(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Creates a new Tabbed Group within the specified group.</para>
      </summary>
      <param name="parent">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object which specifies the parent group for the created group.</param>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutControlGroup"/> object which represents the newly created tabbed group.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.Cursor">
      <summary>
        <para>Gets or sets the cursor that is displayed when the mouse pointer is over the control.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.Cursor"/> object.</value>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.CustomDraw">
      <summary>
        <para>Allows you to custom paint individual items in the LayoutControl.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.CustomizationForm">
      <summary>
        <para>Provides access to the Customization Form.</para>
      </summary>
      <value>A DevExpress.XtraLayout.Customization.UserCustomizationForm object which represents the Customization Form.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.CustomizationFormBounds">
      <summary>
        <para>Gets or sets the boundaries of the Customization Form.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Rectangle"/> structure which represents the Customization Form&#39;s boundaries.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.CustomizationMenuManager">
      <summary>
        <para>An object which provides the Context Menu.</para>
      </summary>
      <value>A DevExpress.XtraLayout.Customization.RightButtonMenuManager object.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.CustomizationMode">
      <summary>
        <para>Allows you to switch between the default and quick customization modes.</para>
      </summary>
      <value>The customization mode used to adjust the layout by end-users.</value>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.DefaultLayoutLoaded">
      <summary>
        <para>Fires after the default layout (the layout saved by the <see cref="M:DevExpress.XtraLayout.LayoutControl.SetDefaultLayout"/> method) has been loaded.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.DefaultLayoutLoading">
      <summary>
        <para>Fires before loading the default layout (the layout saved by the <see cref="M:DevExpress.XtraLayout.LayoutControl.SetDefaultLayout"/> method).</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.EnabledStateController">
      <summary>
        <para>Gets an object that calculates the enabled states for layout items based on the enabled states of layout groups and controls.</para>
      </summary>
      <value>An DevExpress.XtraLayout.Helpers.EnabledStateController object.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.EndInit">
      <summary>
        <para>Ends the layout control&#39;s runtime initialization.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.EndUpdate">
      <summary>
        <para>Unlocks the <see cref="T:DevExpress.XtraLayout.LayoutControl"></see> object after a call to the BeginUpdate method and causes an immediate visual update.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportLayout(System.IO.Stream)">
      <summary>
        <para>Exports the contents of the <see cref="T:DevExpress.XtraLayout.LayoutControl"/> to the specified stream in XML format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object which specifies the stream to which the <see cref="T:DevExpress.XtraLayout.LayoutControl"/> is exported in XML format.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToDocx(System.IO.Stream)">
      <summary>
        <para>Exports the control&#39;s data in the Office Open XML file format (DOCX file) and sends it to the specified stream.</para>
      </summary>
      <param name="stream">A System.IO.Stream object to which the created document should be exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToDocx(System.String)">
      <summary>
        <para>Exports the control&#39;s data in the Office Open XML file format and saves it to the specified DOCX file.</para>
      </summary>
      <param name="filePath">A System.String value which specifies the full path (including the file name and extension) where the DOCX file should be created.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToHtml(System.IO.Stream)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a stream in HTML format</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToHtml(System.IO.Stream,DevExpress.XtraPrinting.HtmlExportOptions)">
      <summary>
        <para>Exports the control&#39;s data to the specified stream in HTML format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.HtmlExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToHtml(System.IO.Stream,System.String,System.String,System.Boolean)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a stream in HTML format using the specified character encoding, with the specified title. The output file can be compressed (secondary characters e.g. spaces are removed) if required.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="htmlCharSet">A <see cref="T:System.String"/> representing the encoding name set in the output document (e.g. &quot;UTF-8&quot;).</param>
      <param name="title">A <see cref="T:System.String"/> containing the name shown as the title of the created document.</param>
      <param name="compressed">true if the HTML code is compressed (secondary characters e.g. spaces are removed); otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToHtml(System.String)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a file in HTML format</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToHtml(System.String,DevExpress.XtraPrinting.HtmlExportOptions)">
      <summary>
        <para>Exports the control&#39;s data to the specified file in HTML format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.HtmlExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToHtml(System.String,System.String)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a file in HTML format using the specified character encoding.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.</param>
      <param name="htmlCharSet">A <see cref="T:System.String"/> representing the encoding name set in the output document (e.g. &quot;UTF-8&quot;).</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToHtml(System.String,System.String,System.String,System.Boolean)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a file in HTML format using the specified character encoding, with the specified title. The output file can be compressed (secondary characters e.g. spaces are removed) if required.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.</param>
      <param name="htmlCharSet">A <see cref="T:System.String"/> representing the encoding name set in the output document (e.g. &quot;UTF-8&quot;).</param>
      <param name="title">A <see cref="T:System.String"/> containing the name shown as the title of the created document.</param>
      <param name="compressed">true if the HTML code is compressed (secondary characters e.g. spaces are removed); otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToMht(System.IO.Stream,DevExpress.XtraPrinting.MhtExportOptions)">
      <summary>
        <para>Exports the control&#39;s data to the specified stream in MHT format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.MhtExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToMht(System.IO.Stream,System.String,System.String,System.Boolean)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a stream in MHT format using the specified character encoding, with the specified title. The output file can be compressed (secondary characters e.g. spaces are removed) if required.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="htmlCharSet">A <see cref="T:System.String"/> representing the encoding name set in the output document (e.g. &quot;UTF-8&quot;).</param>
      <param name="title">A <see cref="T:System.String"/> containing the name shown as the title of the created document.</param>
      <param name="compressed">true if the MHT code is compressed (secondary characters e.g. spaces are removed); otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToMht(System.String)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a file in MHT format.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToMht(System.String,DevExpress.XtraPrinting.MhtExportOptions)">
      <summary>
        <para>Exports the control&#39;s data to the specified file in MHT format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.MhtExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToMht(System.String,System.String)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a file in MHT format using the specified character encoding.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.</param>
      <param name="htmlCharSet">A <see cref="T:System.String"/> representing the encoding name set in the output document (e.g. &quot;UTF-8&quot;).</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToMht(System.String,System.String,System.String,System.Boolean)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a file in MHT format using the specified character encoding, with the specified title. The output file can be compressed (secondary characters e.g. spaces are removed) if required.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.</param>
      <param name="htmlCharSet">A <see cref="T:System.String"/> representing the encoding name set in the output document (e.g. &quot;UTF-8&quot;).</param>
      <param name="title">A <see cref="T:System.String"/> containing the name shown as the title of the created document.</param>
      <param name="compressed">true if the MHT code is compressed (secondary characters e.g. spaces are removed); otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToPdf(System.IO.Stream)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a stream in PDF format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToPdf(System.String)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a file in PDF format.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToRtf(System.IO.Stream)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a stream in RTF format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToRtf(System.String)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a file in RTF format.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToText(System.IO.Stream)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a stream in TXT format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToText(System.IO.Stream,DevExpress.XtraPrinting.TextExportOptions)">
      <summary>
        <para>Exports the control&#39;s data to the specified stream in Text format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.TextExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToText(System.IO.Stream,System.String)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a stream in TXT format using the specified separator string.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="separator">A <see cref="T:System.String"/> containing symbols which will be used to separate the document&#39;s text elements in the created text file.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToText(System.IO.Stream,System.String,System.Boolean)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a stream in TXT format using the specified separator string and quotation settings.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="separator">A <see cref="T:System.String"/> containing symbols which will be used to separate the document&#39;s text elements in the created text file.</param>
      <param name="quoteStringsWithSeparators">true to quote (place quotation marks around) text elements that contain symbols which coincide with the specified separator string; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToText(System.IO.Stream,System.String,System.Boolean,System.Text.Encoding)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a stream in TXT format using the specified separator string, quotation and text encoding settings.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="separator">A <see cref="T:System.String"/> containing symbols which will be used to separate the document&#39;s text elements in the created text file.</param>
      <param name="quoteStringsWithSeparators">true to quote (place quotation marks around) text elements that contain symbols which coincide with the specified separator string; otherwise, false.</param>
      <param name="encoding">A <see cref="T:System.Text.Encoding"/> class descendant specifying the encoding of the created text document.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToText(System.String)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a file in TXT format.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToText(System.String,DevExpress.XtraPrinting.TextExportOptions)">
      <summary>
        <para>Exports the control&#39;s data to the specified file in Text format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.TextExportOptions"/> object which specifies the export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToText(System.String,System.String)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a file in TXT format using the specified separator string.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.</param>
      <param name="separator">A <see cref="T:System.String"/> containing symbols which will be used to separate the document&#39;s text elements in the created text file.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToText(System.String,System.String,System.Boolean)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a file in TXT format using the specified separator string and quotation settings.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.</param>
      <param name="separator">A <see cref="T:System.String"/> containing symbols which will be used to separate the document&#39;s text elements in the created text file.</param>
      <param name="quoteStringsWithSeparators">true to quote (place quotation marks around) text elements that contain symbols which coincide with the specified separator string; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToText(System.String,System.String,System.Boolean,System.Text.Encoding)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a file in TXT format using the specified separator string, quotation and text encoding settings.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.</param>
      <param name="separator">A <see cref="T:System.String"/> containing symbols which will be used to separate the document&#39;s text elements in the created text file.</param>
      <param name="quoteStringsWithSeparators">true to quote (place quotation marks around) text elements that contain symbols which coincide with the specified separator string; otherwise, false.</param>
      <param name="encoding">A <see cref="T:System.Text.Encoding"/> class descendant specifying the encoding of the created text document.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToXls(System.IO.Stream)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a stream in XLS format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToXls(System.IO.Stream,DevExpress.XtraPrinting.XlsExportOptions)">
      <summary>
        <para>Exports the control&#39;s data to the specified stream in XLS format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.XlsExportOptions"/> object which specifies the XLS export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToXls(System.IO.Stream,System.Boolean)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a stream in XLS format using the specified formatting settings.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
      <param name="useNativeFormat">true to use the data format of the exported controls for the cells in the XLS document; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToXls(System.String)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a file in XLS format.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToXls(System.String,DevExpress.XtraPrinting.XlsExportOptions)">
      <summary>
        <para>Exports the control&#39;s data to the specified file in XLS format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.</param>
      <param name="options">A <see cref="T:DevExpress.XtraPrinting.XlsExportOptions"/> object which specifies the XLS export options to be applied when the control is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToXls(System.String,System.Boolean)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a file in XLS format using the specified formatting settings.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.</param>
      <param name="useNativeFormat">true to use the data format of the exported controls for the cells in the XLS document; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToXlsx(System.IO.Stream)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a stream in XLSX (MS Excel 2007) format.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToXlsx(System.IO.Stream,DevExpress.XtraPrinting.XlsxExportOptions)">
      <summary>
        <para>Exports data to the specified stream in XLSX (MS Excel 2007) format using the specified options.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> object to which data is exported.</param>
      <param name="options">An <see cref="T:DevExpress.XtraPrinting.XlsxExportOptions"/> object that contains export options.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToXlsx(System.String)">
      <summary>
        <para>Exports the controls displayed within the LayoutControl to a file in XLSX (MS Excel 2007) format.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ExportToXlsx(System.String,DevExpress.XtraPrinting.XlsxExportOptions)">
      <summary>
        <para>Exports the control&#39;s data to the specified file in XLSX (MS Excel 2007) format using the specified options.</para>
      </summary>
      <param name="filePath">A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the data is exported.</param>
      <param name="options">An <see cref="T:DevExpress.XtraPrinting.XlsxExportOptions"/> object which specifies the XLS export options to be applied when the control is exported.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.FocusHelper">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.GetControlByName(System.String)">
      <summary>
        <para>Returns the control with the specified name, contained within the Layout Control.</para>
      </summary>
      <param name="name">A <see cref="T:System.String"/> value that specifies the control&#39;s name.</param>
      <returns>A <see cref="T:System.Windows.Forms.Control"/> descendant that represents the control with the specified name, contained within the Layout Control. null (Nothing in Visual Basic) if the control isn&#39;t found.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.GetControlName(System.Windows.Forms.Control)">
      <summary>
        <para>Gets the specified control&#39;s name.</para>
      </summary>
      <param name="control">A Control object whose name is to be retrieved.</param>
      <returns>A string that specifies the control&#39;s name.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.GetGroupAtPoint(System.Drawing.Point)">
      <summary>
        <para>Returns the group which is located at the specified point.</para>
      </summary>
      <param name="p">A <see cref="T:System.Drawing.Point"/> structure which specifies the test point coordinates relative to the layout control&#39;s top-left corner.</param>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object which represents the group located at the specified point. null (Nothing in Visual Basic) if there is no group at the specified point.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.GetItemByControl(System.Windows.Forms.Control)">
      <summary>
        <para>Returns the layout item contained within the root group which holds the specified control.</para>
      </summary>
      <param name="control">A <see cref="T:System.Windows.Forms.Control"/> object which represents the control whose parent layout item is to be returned.</param>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutControlItem"/> object which contains the specified control. null (Nothing in Visual Basic) if there is no layout item within the root group which holds the specified control.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.GetItemByControl(System.Windows.Forms.Control,DevExpress.XtraLayout.LayoutControlGroup)">
      <summary>
        <para>Returns the layout item which is contained within the specified group and holds the specified control.</para>
      </summary>
      <param name="control">A <see cref="T:System.Windows.Forms.Control"/> object which represents the control whose parent layout item is to be returned.</param>
      <param name="group">A <see cref="T:DevExpress.XtraLayout.LayoutControlGroup"/> object representing the group within which the search is performed.</param>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutControlItem"/> object which contains the specified control. null (Nothing in Visual Basic) if there is no layout item within the specified group which holds the specified control.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.GetItemByRepositoryItem(DevExpress.XtraEditors.Repository.RepositoryItem,DevExpress.XtraLayout.LayoutControlGroup)">
      <summary>
        <para></para>
      </summary>
      <param name="repositoryItem"></param>
      <param name="group"></param>
      <returns></returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.GetPreferredSize(System.Drawing.Size)">
      <summary>
        <para>Retrieves the size of a rectangular area into which the control can be fit.</para>
      </summary>
      <param name="proposedSize">The custom-sized area for the control.</param>
      <returns>A System.Drawing.Size value.</returns>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.GroupExpandChanged">
      <summary>
        <para>Fires after a layout group has been expanded/collapsed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.GroupExpandChanging">
      <summary>
        <para>Fires when a layout group is about to be expanded or collapsed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.HardUpdate">
      <summary>
        <para>Recalculates the view information and immediately updates the LayoutControl.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.HiddenItems">
      <summary>
        <para>Provides access to a collection of hidden layout items.</para>
      </summary>
      <value>A DevExpress.XtraLayout.HiddenItemsCollection object which represents a collection of hidden layout items.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.HiddenItemsSortComparer">
      <summary>
        <para>Allows you to specify a comparer object for sorting hidden items within the Customization Form.</para>
      </summary>
      <value>An object implementing the <see cref="T:System.Collections.IComparer"/> interface, which is used to sort hidden items; null if no object has been assigned.</value>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.HideCustomization">
      <summary>
        <para>Fires before the Customization Form is closed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.HideCustomizationForm">
      <summary>
        <para>Closes the Customization Form.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.HideItem(DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para>Hides the specified layout item.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant which represents the layout item to hide.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.HideSelectedItems">
      <summary>
        <para>Hides the currently selected items to the Customization Form.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.Images">
      <summary>
        <para>Gets or sets a collection of images that can be displayed within the LayoutControl&#39;s elements.</para>
      </summary>
      <value>An object that is an image collection providing images to be displayed in the LayoutControl&#39;s items.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.IsInitialized">
      <summary>
        <para>Indicates whether the LayoutControl has been initialized.</para>
      </summary>
      <value>true if the LayoutControl has been initialized; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.IsLayoutModified">
      <summary>
        <para>Gets whether the layout has been modified. This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value>true if the layout has been modified; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.IsModified">
      <summary>
        <para>Gets whether the layout has been changed.</para>
      </summary>
      <value>true if the layout has been changed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.IsPrintingAvailable">
      <summary>
        <para>Indicates whether the <see cref="T:DevExpress.XtraLayout.LayoutControl"/> can be printed.</para>
      </summary>
      <value>true if the LayoutControl can be printed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.IsUpdateLocked">
      <summary>
        <para>Gets whether the layout control has been locked for updating.</para>
      </summary>
      <value>true if the layout control is locked; otherwise, false;</value>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.ItemAdded">
      <summary>
        <para>Fires after a layout item has been added to the Items collection of its parent.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.ItemDragging">
      <summary>
        <para>Fires repeatedly when a layout item is being dragged and allows you to prevent the item from being dropped at a specific position.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.ItemRemoved">
      <summary>
        <para>Fires after a layout item has been removed from the Items collection of its parent.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.Items">
      <summary>
        <para>Provides access to all the layout items owned by the LayoutControl.</para>
      </summary>
      <value>A DevExpress.XtraLayout.Utils.ReadOnlyItemCollection object which represents the collection of layout items.</value>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.ItemSelectionChanged">
      <summary>
        <para>Fires after the selected layout item has been changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.LayoutChanged">
      <summary>
        <para>Updates the Layout Control.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.LayoutTreeViewPopupMenuShowing">
      <summary>
        <para>Occurs when the Layout Tree View Context Menu is about to be displayed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.LayoutUpdate">
      <summary>
        <para>Occurs after the size of any layout item has been changed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.LayoutUpgrade">
      <summary>
        <para>Occurs when a layout is restored from a data store (a stream, xml file or system registry), and its version differs from the version of the current layout.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.LayoutVersion">
      <summary>
        <para>Gets or sets the version of the layout.</para>
      </summary>
      <value>A string representing the version of the layout.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.LookAndFeel">
      <summary>
        <para>Provides access to the settings that control the layout control&#39;s look and feel.</para>
      </summary>
      <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object whose properties specify the layout control&#39;s look and feel.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.MenuManager">
      <summary>
        <para>Gets or sets an object that controls the look and feel of the popup menus.</para>
      </summary>
      <value>An object that controls the look and feel of the popup menus.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.OptionsCustomizationForm">
      <summary>
        <para>Provides access to the options which control the appearance and behavior of the Customization Form.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.OptionsCustomizationForm"/> object which provides options related to the Customization Form.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.OptionsFocus">
      <summary>
        <para>Provides access to the layout control&#39;s focus options.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.OptionsFocus"/> object that contains the layout control&#39;s focus options.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.OptionsItemText">
      <summary>
        <para>Provides access to the default alignment settings of the controls displayed in the LayoutControl.</para>
      </summary>
      <value>An <see cref="T:DevExpress.XtraLayout.OptionsItemText"/> object that provides the alignment options.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.OptionsPrint">
      <summary>
        <para>Provides access to the options that specify how a <see cref="T:DevExpress.XtraLayout.LayoutControl"/> is printed/exported.</para>
      </summary>
      <value>An object containing the LayoutControl&#39;s print and export options.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.OptionsSerialization">
      <summary>
        <para>Provides access to options that control how the LayoutControl&#39;s layout is saved to and restored from a data store (an XML file, stream or the system registry).</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.LayoutSerializationOptions"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.OptionsView">
      <summary>
        <para>Provides access to the display options of the <see cref="T:DevExpress.XtraLayout.LayoutControl"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.OptionsView"/> object which specify the control&#39;s display options.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.PaintStyles">
      <summary>
        <para>Provides access to the paint style collection.</para>
      </summary>
      <value>A DevExpress.XtraLayout.Registrator.LayoutPaintStyleCollection object which represents the paint style collection.</value>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.PopupMenuShowing">
      <summary>
        <para>Occurs when the Context Menu is about to be displayed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.Print">
      <summary>
        <para>Prints the <see cref="T:DevExpress.XtraLayout.LayoutControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.Refresh">
      <summary>
        <para>Updates the layout control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.RegisterCustomPropertyGridWrapper(System.Type,System.Type)">
      <summary>
        <para>Registers the specified wrapper object that identifies which properties to display for specific layout items in the Customization Form&#39;s Property Grid.</para>
      </summary>
      <param name="itemType">The type of layout item with which the current wrapper object is associated.</param>
      <param name="customWrapperType">The type of the wrapper object to be registered.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.RegisterFixedItemType(System.Type)">
      <summary>
        <para>Registers the specified &#39;fixed&#39; item for runtime use in the LayoutControl.</para>
      </summary>
      <param name="itemType">The type of the &#39;fixed&#39; item to be registered.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.RegisterUserCustomizationForm(System.Type)">
      <summary>
        <para>Registers the specified custom Customization Form.</para>
      </summary>
      <param name="customizationFormType">The type of the custom Customization Form.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.Remove(DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para>Removes the specified item from the control.</para>
      </summary>
      <param name="item">The <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object that should be removed.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.Remove(DevExpress.XtraLayout.BaseLayoutItem,System.Boolean)">
      <summary>
        <para>Removes the specified item from the control.</para>
      </summary>
      <param name="item">The <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object that should be removed.</param>
      <param name="disposeItemAndControls">true, to release all resources; otherwise false.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.RenameSelectedItem">
      <summary>
        <para>Activates an edit box that allows an end-user to rename the currently selected layout item.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.RepositoryItems">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.RequestUniqueName">
      <summary>
        <para>Allows you to provide unique names for layout items created in code, whose names conflict with existing names.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.RestoreDefaultLayout">
      <summary>
        <para>Restores the layout that has been saved using the <see cref="M:DevExpress.XtraLayout.LayoutControl.SetDefaultLayout"/> method.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.RestoreLayoutFromRegistry(System.String)">
      <summary>
        <para>Restores the layout which is stored at the specified system registry path.</para>
      </summary>
      <param name="path">A <see cref="T:System.String"/> value which specifies the system registry path. If the specified path doesn&#39;t exist, this method does nothing.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.RestoreLayoutFromStream(System.IO.Stream)">
      <summary>
        <para>Restores the layout from the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> descendant from which the layout is read. If null (Nothing in Visual Basic), an exception is raised.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.RestoreLayoutFromXml(System.String)">
      <summary>
        <para>Restores the layout from the specified XML file.</para>
      </summary>
      <param name="xmlFile">A <see cref="T:System.String"/> value which specifies the path to the XML file that contains the layout to be loaded. If the specified file doesn&#39;t exist, an exception is raised.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.Root">
      <summary>
        <para>Gets or sets the LayoutControl&#39;s root group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.LayoutControlGroup"/> object which represents the root group.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.SaveLayoutToRegistry(System.String)">
      <summary>
        <para>Saves the current layout to the specified system registry path.</para>
      </summary>
      <param name="path">A <see cref="T:System.String"/> value which specifies the system registry path to save the layout to.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.SaveLayoutToStream(System.IO.Stream)">
      <summary>
        <para>Saves the current layout to the specified stream.</para>
      </summary>
      <param name="stream">A <see cref="T:System.IO.Stream"/> descendant to which the current layout is written.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.SaveLayoutToXml(System.String)">
      <summary>
        <para>Saves the layout to an XML file.</para>
      </summary>
      <param name="xmlFile">A <see cref="T:System.String"/> value which specifies the path to the file where the layout should be stored. If an empty string is specified, an exception is raised.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.SelectParentGroup">
      <summary>
        <para>Selects the currently selected item&#39;s parent.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.SetCursor(System.Windows.Forms.Cursor)">
      <summary>
        <para>Sets the current cursor.</para>
      </summary>
      <param name="cursor">A <see cref="T:System.Windows.Forms.Cursor"/> object which represents the mouse cursor.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.SetDefaultLayout">
      <summary>
        <para>Saves the current layout to an internal memory buffer.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.ShowContextMenu">
      <summary>
        <para>Occurs when the Context Menu is about to be displayed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.ShowCustomization">
      <summary>
        <para>Fires immediately after the Customization Form has been invoked.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ShowCustomizationForm">
      <summary>
        <para>Invokes the Customization Form.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.ShowLayoutTreeViewContextMenu">
      <summary>
        <para>Occurs when the Layout Tree View Context Menu is about to be displayed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ShowPrintPreview">
      <summary>
        <para>Opens the Print Preview window for the <see cref="T:DevExpress.XtraLayout.LayoutControl"/> with a Bars UI.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.ShowRibbonPrintPreview">
      <summary>
        <para>Displays the Print Preview window with a Ribbon UI.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.StyleController">
      <summary>
        <para>Gets or sets the style controller component that provides appearance settings for the control.</para>
      </summary>
      <value>An object which implements the DevExpress.XtraEditors.IStyleController interface and provides appearance settings for the control.</value>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.TabPageCloseButtonClick">
      <summary>
        <para>Fires when a Close button within a tab page is clicked.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.ToolTipController">
      <summary>
        <para>Gets or sets the tooltip controller component that controls the appearance, position and content of hints displayed for layout items.</para>
      </summary>
      <value>The <see cref="T:DevExpress.Utils.ToolTipController"/> component controlling the appearance and behavior of hints displayed for layout items.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.UndoManager">
      <summary>
        <para>Gets the manager that handles undo/redo operations via the Customization Form.</para>
      </summary>
      <value>A DevExpress.XtraLayout.Customization.UndoManager object.</value>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutControl.UniqueNameRequest">
      <summary>
        <para>Allows you to provide unique names for layout items created in code, whose names conflict with existing names.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.UnRegisterCustomPropertyGridWrapper(System.Type)">
      <summary>
        <para>Unregisters the specified wrapper object that has been registered via the <see cref="M:DevExpress.XtraLayout.LayoutControl.RegisterCustomPropertyGridWrapper(System.Type,System.Type)"/> method.</para>
      </summary>
      <param name="itemType">The type of the wrapper object to be unregistered.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControl.UnRegisterFixedItemType(System.Type)">
      <summary>
        <para>Unregisters the specified &#39;fixed&#39; item in the current LayoutControl.</para>
      </summary>
      <param name="itemType">The type of the &#39;fixed&#39; item to be unregistered.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControl.UseLocalBindingContext">
      <summary>
        <para>Gets or sets whether controls within the current LayoutControl use the local BindingContext, or the form&#39;s BindingContext.</para>
      </summary>
      <value>true if controls use the LayoutControl&#39;s BindingContext; false if controls use the form&#39;s BindingContext.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.LayoutControlGroup">
      <summary>
        <para>A regular group with or without a header and borders.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.LayoutControlGroup"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.#ctor(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.LayoutControlGroup"/> class with the specified parent layout group.</para>
      </summary>
      <param name="lg">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> descendant which represents the parent layout group.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddGroup">
      <summary>
        <para>Creates a new group and adds it to the current group.</para>
      </summary>
      <returns>The newly created group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddGroup(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Creates a new layout group at a position relative to an item within the current group.</para>
      </summary>
      <param name="baseItem">The item (owned by the current group) next to which the new group is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new group relative to the specified item.</param>
      <returns>The new layout group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddGroup(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Inserts the specified group to the current group.</para>
      </summary>
      <param name="newGroup">The group to add to the current group.</param>
      <returns>The added group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddGroup(DevExpress.XtraLayout.LayoutGroup,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Inserts the specified layout group at a position relative to an item within the current group.</para>
      </summary>
      <param name="newGroup">The layout group to add to the current group.</param>
      <param name="baseItem">The item (owned by the current group) next to which the &#39;newGroup&#39; is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new group relative to the specified item.</param>
      <returns>The added layout group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddGroup(System.String)">
      <summary>
        <para>Creates a new group with a specified caption and adds it to the current group.</para>
      </summary>
      <param name="text">The group&#39;s caption. This value is used to initialize the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> property.</param>
      <returns>The newly created layout group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddGroup(System.String,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Creates a new group with the specified caption at a position relative to an item within the current group.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value that specifies the group&#39;s caption. This value is assigned to the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> property.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the current group) next to which a new group is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new group relative to the specified item.</param>
      <returns>The newly created layout group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddItem">
      <summary>
        <para>Creates a new layout item with the default layout type and adds it to the current group.</para>
      </summary>
      <returns>The newly created item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddItem(DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para>Inserts the specified layout item to the current group.</para>
      </summary>
      <param name="newItem">The layout item to add to the current group.</param>
      <returns>The added layout item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddItem(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Inserts the specified item at a position relative to another item within the current group.</para>
      </summary>
      <param name="newItem">The item to be moved to a new position.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the current group) next to which the &#39;newItem&#39; is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the &#39;newItem&#39; relative to the specified item.</param>
      <returns>The added layout item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddItem(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Creates a new layout item at a position relative to an item within the current group.</para>
      </summary>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the current group) next to which a new item is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new item relative to the specified item.</param>
      <returns>The newly created layout item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddItem(System.String)">
      <summary>
        <para>Creates a new layout item with the specified text and adds it to the current group.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value that specifies the layout item&#39;s text.</param>
      <returns>The newly created item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddItem(System.String,System.Windows.Forms.Control)">
      <summary>
        <para>Creates a new layout item with the specified text and control, and adds it to the current group.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value that specifies the layout item&#39;s text.</param>
      <param name="control">The control to display in the layout item. This value is used to initialize the <see cref="P:DevExpress.XtraLayout.LayoutControlItem.Control"/> property.</param>
      <returns>The newly created item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddItem(System.String,System.Windows.Forms.Control,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Creates a new layout item with the specified text and control at a position relative to another item within the current group.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value that specifies the new layout item&#39;s text.</param>
      <param name="control">The control to display in the new layout item. This value is used to initialize the <see cref="P:DevExpress.XtraLayout.LayoutControlItem.Control"/> property.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the current group) next to which a new item is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new item relative to the specified item.</param>
      <returns>The newly created layout item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddRange(DevExpress.XtraLayout.BaseLayoutItem[])">
      <summary>
        <para>Adds an array of layout items to the current group.</para>
      </summary>
      <param name="items">An array of <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> objects.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddTabbedGroup">
      <summary>
        <para>Creates a new tabbed group and adds it to the current group.</para>
      </summary>
      <returns>The newly created group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddTabbedGroup(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Creates a new tabbed group at a position relative to an item within the current group.</para>
      </summary>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the current group) next to which a new group is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new tabbed group relative to the specified item.</param>
      <returns>The newly created group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddTabbedGroup(DevExpress.XtraLayout.TabbedGroup)">
      <summary>
        <para>Inserts the specified tabbed group to the current group.</para>
      </summary>
      <param name="newTabbedGroup">The tabbed group to add to the current group.</param>
      <returns>The added tabbed group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.AddTabbedGroup(DevExpress.XtraLayout.TabbedGroup,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Inserts the specified tabbed group at a position relative to an item within the current group.</para>
      </summary>
      <param name="newTabbedGroup">The group that is to be moved to a new position.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the current group) next to which the &#39;newTabbedGroup&#39; is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new tabbed group relative to the specified item.</param>
      <returns>The added tabbed group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlGroup.HideToCustomization">
      <summary>
        <para>Hides the group.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlGroup.Items">
      <summary>
        <para>Provides access to the visible layout items owned by the current group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.Utils.LayoutGroupItemCollection"/> object which represents the collection of visible layout items.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlGroup.Owner">
      <summary>
        <para>Gets or set the LayoutControl that owns the current layout group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.LayoutControl"/> control.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlGroup.ViewInfo">
      <summary>
        <para>Gets the object which contains information used to render the layout group.</para>
      </summary>
      <value>A DevExpress.XtraLayout.ViewInfo.LayoutControlGroupViewInfo object.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.LayoutControlItem">
      <summary>
        <para>Displays an embedded control and an optional label.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.LayoutControlItem"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlItem.#ctor(DevExpress.XtraLayout.LayoutControl,System.Windows.Forms.Control)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.LayoutControlItem"/> class.</para>
      </summary>
      <param name="layoutControl">A <see cref="T:DevExpress.XtraLayout.LayoutControl"/> object which owns the created layout item. This value is assigned to the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Owner"/> property.</param>
      <param name="control">A <see cref="T:System.Windows.Forms.Control"/> descendant which is owned by the created layout item. This value is assigned to the <see cref="P:DevExpress.XtraLayout.LayoutControlItem.Control"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlItem.#ctor(DevExpress.XtraLayout.LayoutControlGroup)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.LayoutControlItem"/> class with the specified parent group.</para>
      </summary>
      <param name="parent">A <see cref="T:DevExpress.XtraLayout.LayoutControlGroup"/> object representing the layout group which owns the created item.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.AllowAutoAlignment">
      <summary>
        <para>Gets or sets whether the auto-alignment feature is enabled for the item.</para>
      </summary>
      <value>A Boolean value.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.AllowGlyphSkinning">
      <summary>
        <para>Gets or sets whether the <see cref="T:DevExpress.XtraLayout.LayoutControlItem"/>&#39;s icon should be painted in the item&#39;s foreground color.</para>
      </summary>
      <value>A DefaultBoolean enumerator value that specifies whether the <see cref="T:DevExpress.XtraLayout.LayoutControlItem"/>&#39;s icon should be painted in the item&#39;s foreground color.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.AllowHotTrack">
      <summary>
        <para>Gets or sets whether the layout item&#39;s borders are highlighted when the mouse cursor hovers over it.</para>
      </summary>
      <value>true if the layout item can be hot-tracked; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.AllowHtmlStringInCaption">
      <summary>
        <para>Gets or sets whether HTML formatting is allowed in the text that is associated with the layout item.</para>
      </summary>
      <value>true if HTML formatting is allowed; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlItem.Assign(DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para></para>
      </summary>
      <param name="item"></param>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.ContentVisible">
      <summary>
        <para>Gets or sets whether the layout item&#39;s contents are visible.</para>
      </summary>
      <value>true if the layout item&#39;s contents are visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.Control">
      <summary>
        <para>Gets or sets the control which is owned by the layout item.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.Control"/> descendant which is owned by the layout item.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.ControlAlignment">
      <summary>
        <para>Gets or sets the alignment of the <see cref="T:DevExpress.XtraLayout.LayoutControlItem"/>&#39;s control within the layout item.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.ContentAlignment"/> value that specifies the control&#39;s alignment within the layout item.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.ControlMaxSize">
      <summary>
        <para>Gets or sets the client area&#39;s maximum size.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the client area&#39;s maximum width and height.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.ControlMinSize">
      <summary>
        <para>Gets or sets the control&#39;s minimum size.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the control&#39;s minimum width and height.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.ControlName">
      <summary>
        <para>This property is for internal use only. It is used in serialization. Do not change it.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the name of the control which is owned by the current layout item.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.Enabled">
      <summary>
        <para>Gets or sets whether the layout item and its control are enabled.</para>
      </summary>
      <value>true, if the layout item and its control are enabled; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutControlItem.EndInit">
      <summary>
        <para>Finishes the component&#39;s initialization.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.Expanded">
      <summary>
        <para>Not supported.</para>
      </summary>
      <value>Always True.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.FillControlToClientArea">
      <summary>
        <para>Gets or sets whether the control occupies the layout item&#39;s maximum allowed client area.</para>
      </summary>
      <value>true if the control occupies the layout item&#39;s maximum allowed client area; otheriwse, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.HighlightFocusedItem">
      <summary>
        <para>Gets or sets whether the current layout item is highlighted according to the current skin when focused.</para>
      </summary>
      <value>True if the current layout item is highlighted according to the current skin when focused; False if not; Default use the <see cref="P:DevExpress.XtraLayout.OptionsView.HighlightFocusedItem"/> global setting.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.Image">
      <summary>
        <para>Gets or sets the image displayed in the item&#39;s label.</para>
      </summary>
      <value>An <see cref="T:System.Drawing.Image"/> object which represents the item&#39;s image.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.ImageAlignment">
      <summary>
        <para>Gets or sets the alignment of the image within the label.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.ContentAlignment"/> enumeration value that specifies the alignment of the image within the label.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.ImageIndex">
      <summary>
        <para>Gets or sets the index of the image assigned to the item.</para>
      </summary>
      <value>An integer which specifies the index of the item&#39;s image in a collection of images.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.ImageOptions">
      <summary>
        <para>Provides access to all properties that allow you to assign and customize an image for this item&#39;s label.</para>
      </summary>
      <value>A DevExpress.XtraLayout.LayoutControlItemImageOptions object that stores all properties that allow you to assign and customize an image for this item&#39;s label.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.Images">
      <summary>
        <para>Gets an image collection whose images can be displayed within the current item.</para>
      </summary>
      <value>An object that represents the image source.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.ImageToTextDistance">
      <summary>
        <para>Gets or sets the distance between the item&#39;s label and image.</para>
      </summary>
      <value>An integer value that specifies the distance  between the item&#39;s label and image, in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.ItemImages">
      <summary>
        <para>Returns the collection of images that can be displayed within the current item.</para>
      </summary>
      <value>An ImageCollection object that contains images.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.MaxSize">
      <summary>
        <para>Gets or sets the layout item&#39;s maximum size.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the item&#39;s maximum width and height.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.MinSize">
      <summary>
        <para>Gets or sets the item&#39;s minimum size.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the item&#39;s minimum width and height.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.OptionsPrint">
      <summary>
        <para>Provides access to options that specify how the <see cref="T:DevExpress.XtraLayout.LayoutControlItem"/> is printed/exported.</para>
      </summary>
      <value>An object containing the layout item&#39;s print and export options.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.Owner">
      <summary>
        <para>Gets or set the LayoutControl that owns the current layout item.</para>
      </summary>
      <value>A LayoutControl control.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.SizeConstraintsType">
      <summary>
        <para>Gets or sets the size constraints type.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.SizeConstraintsType"/> enumeration value which specifies the manner in which the current layout item can be resized.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.TextAlignMode">
      <summary>
        <para>Gets or sets the alignment settings of the item&#39;s control.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.TextAlignModeItem"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.TrimClientAreaToControl">
      <summary>
        <para>Gets or sets whether the client region&#39;s height is trimmed to match the control&#39;s height.</para>
      </summary>
      <value>true if the client region&#39;s height is trimmed to match the control&#39;s height; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.TypeName">
      <summary>
        <para>Gets the name of the item&#39;s type.</para>
      </summary>
      <value>The &quot;LayoutControlItem&quot; string.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutControlItem.ViewInfo">
      <summary>
        <para>Gets the object which contains information used to render the layout item.</para>
      </summary>
      <value>A DevExpress.XtraLayout.ViewInfo.LayoutControlItemViewInfo object.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.LayoutGroup">
      <summary>
        <para>Represents the base class for regular layout groups.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.Accept(DevExpress.XtraLayout.Utils.BaseVisitor)">
      <summary>
        <para>Invokes the Visit method of the specified visitor for each layout item that belongs to the current layout item group.</para>
      </summary>
      <param name="visitor">A DevExpress.XtraLayout.Utils.BaseVisitor class descendant.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.Add(DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para>Adds the specified item along the bottom or right edge of the current group.</para>
      </summary>
      <param name="item">The item to be added to the current group.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddGroup">
      <summary>
        <para>Creates a new group and adds it to the current group.</para>
      </summary>
      <returns>The newly added group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddGroup(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Creates a new group at a position relative to an item within the current group.</para>
      </summary>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the current group) next to which a new group is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new group relative to the specified item.</param>
      <returns>The newly created layout group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddGroup(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Inserts the specified group to the current group.</para>
      </summary>
      <param name="newGroup">The group to add to the current group.</param>
      <returns>The added layout group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddGroup(DevExpress.XtraLayout.LayoutGroup,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Inserts the specified group at a position relative to an item within the current group.</para>
      </summary>
      <param name="newGroup">The group to be moved to a new position.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the current group) next to which the &#39;newGroup&#39; is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new group relative to the specified item.</param>
      <returns>The added group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddGroup(System.String)">
      <summary>
        <para>Creates a new group with the default layout type and the specified caption, and adds it to the current group.</para>
      </summary>
      <param name="text">The group&#39;s caption. This value is used to initialize to the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> property.</param>
      <returns>The newly added group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddGroup(System.String,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Creates a new group with the specified caption at a position relative to an item within the current group.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value that specifies the group&#39;s caption. This value is assigned to the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> property.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the current group) next to which a new group is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new group relative to the specified item.</param>
      <returns>The newly created layout group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddItem">
      <summary>
        <para>Creates a new layout item and adds it to the current group.</para>
      </summary>
      <returns>The newly added item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddItem(DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para>Inserts the specified item to the current group.</para>
      </summary>
      <param name="newItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant which represents the layout item to add to the collection.</param>
      <returns>The added layout item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddItem(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Inserts the specified item at a position relative to another item within the current group.</para>
      </summary>
      <param name="newItem">The item to be moved to a new position.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the current group) next to which a new item is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new item relative to the baseItem.</param>
      <returns>The added layout item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddItem(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Creates a new layout item at a position relative to an item within the current group.</para>
      </summary>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the current group) next to which a new item is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new item relative to the baseItem.</param>
      <returns>The newly created layout item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddItem(System.String)">
      <summary>
        <para>Creates a new layout item with the specified text and adds it to the current group.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value that specifies the layout item&#39;s text.</param>
      <returns>The created layout item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddItem(System.String,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Inserts the specified item at a position relative to another item within the current group. Allows the new text to be specified for the item.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value that specifies the new text to be assigned to the added item. The parameter is ignored if the value represents an empty string.</param>
      <param name="newItem">The item to be moved to a new position.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the current group) next to which the &#39;newItem&#39; is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new item relative to the baseItem.</param>
      <returns>The added layout item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddItem(System.String,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Creates a new layout item with the specified text at a position relative to an item within the current group.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value that specifies the new layout item&#39;s text.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the current group) next to which a new item is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new item relative to the specified item.</param>
      <returns>The newly created layout item.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddTabbedGroup">
      <summary>
        <para>Creates a new tabbed group and adds it to the current group.</para>
      </summary>
      <returns>The created group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddTabbedGroup(DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Creates a new tabbed group at a position relative to an item within the current group.</para>
      </summary>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the current group) next to which a new group is inserted.</param>
      <param name="insertType">The position of the new tabbed group relative to the specified item.</param>
      <returns>The newly added tabbed group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddTabbedGroup(DevExpress.XtraLayout.TabbedGroup)">
      <summary>
        <para>Inserts the specified tabbed group to the current group.</para>
      </summary>
      <param name="newTabbedGroup">The tabbed group to add to the collection.</param>
      <returns>The added group.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.AddTabbedGroup(DevExpress.XtraLayout.TabbedGroup,DevExpress.XtraLayout.BaseLayoutItem,DevExpress.XtraLayout.Utils.InsertType)">
      <summary>
        <para>Inserts the specified tabbed group at a position relative to an item within the current group.</para>
      </summary>
      <param name="newTabbedGroup">The tabbed group to add to the collection.</param>
      <param name="baseItem">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> descendant (owned by the current group) next to which the &#39;newTabbedGroup&#39; is inserted.</param>
      <param name="insertType">A DevExpress.XtraLayout.Utils.InsertType enumeration value that specifies the position of the new tabbed group relative to the specified item.</param>
      <returns>The added tabbed group.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.AllowBorderColorBlending">
      <summary>
        <para>Gets or sets whether to colorize the layout group border and caption using the LayoutGroup.AppearanceGroup.BorderColor setting in skinning paint schemes. This property is ignored by default starting from v18.2.</para>
      </summary>
      <value>true, if the layout group border should be colorized according to its border color in skinning paint schemes; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.AllowDrawBackground">
      <summary>
        <para>Gets or sets whether to paint the group&#39;s background when the <see cref="P:DevExpress.XtraLayout.LayoutGroup.GroupBordersVisible"/> property is set to false.</para>
      </summary>
      <value>true if the group&#39;s background is painted; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.AllowGlyphSkinning">
      <summary>
        <para>Gets or sets whether the <see cref="T:DevExpress.XtraLayout.LayoutGroup"/>&#39;s icon should be painted in the same color as the <see cref="T:DevExpress.XtraLayout.LayoutGroup"/>&#39;s foreground color.</para>
      </summary>
      <value>A DefaultBoolean enumerator value that specifies whether the <see cref="T:DevExpress.XtraLayout.LayoutGroup"/>&#39;s icon should be painted in the same color as the <see cref="T:DevExpress.XtraLayout.LayoutGroup"/>&#39;s foreground color.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.AllowHtmlStringInCaption">
      <summary>
        <para>Gets or sets whether HTML formatting is allowed within the layout group caption.</para>
      </summary>
      <value>true, if the HTML formatting is allowed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.BackgroundImage">
      <summary>
        <para>Gets or sets the group&#39;s background image that can be stretched across the group, tiled, centered, etc.</para>
      </summary>
      <value>An <see cref="T:System.Drawing.Image"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.BackgroundImageLayout">
      <summary>
        <para>Gets or sets the position and behavior of the background image assigned to the <see cref="P:DevExpress.XtraLayout.LayoutGroup.BackgroundImage"/> property.</para>
      </summary>
      <value>An <see cref="T:System.Windows.Forms.ImageLayout"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.BackgroundImageOptions">
      <summary>
        <para>Provides access to all properties that allow you to assign and customize this group&#39;s background image at the bottom layer.</para>
      </summary>
      <value>A DevExpress.XtraLayout.LayoutGroupBackgroundImageOptions object that comprises properties that allow you to assign and customize this group&#39;s background image at the bottom layer.</value>
    </member>
    <member name="F:DevExpress.XtraLayout.LayoutGroup.backgroundImageOptionsCore">
      <summary>
        <para>The <see cref="P:DevExpress.XtraLayout.LayoutGroup.BackgroundImageOptions"/> property backing field.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.BackgroundImageVisible">
      <summary>
        <para>Gets or sets whether the background image, specified by the <see cref="P:DevExpress.XtraLayout.LayoutGroup.BackgroundImage"/> property, is in effect.</para>
      </summary>
      <value>true if the background image, specified by the <see cref="P:DevExpress.XtraLayout.LayoutGroup.BackgroundImage"/> property, is in effect; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.BestFit">
      <summary>
        <para>Resizes the group&#39;s elements so that they are displayed in the optimal way.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.CanChangeLayoutModeForChildSelectedGroup">
      <summary>
        <para>Returns whether the layout mode can be changed for a <see cref="T:DevExpress.XtraLayout.LayoutGroup"/>.</para>
      </summary>
      <value>true, if the layout mode can be changed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.CanCreateTabbedGroupForSelectedGroup">
      <summary>
        <para>Returns whether a new tabbed group can be created within the current group which will display a selected child group.</para>
      </summary>
      <value>true if a tabbed group can be created; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.CanGroupSelectedItems">
      <summary>
        <para>Returns whether a new group can be created within the current group which will display selected child items.</para>
      </summary>
      <value>true if a new group can be created; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.CanMoveItem(DevExpress.XtraLayout.Customization.LayoutItemDragController)">
      <summary>
        <para>Returns whether the specified item can be moved to the specified new position.</para>
      </summary>
      <param name="controller">A <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> object that contains information on the item that would be moved and the position that the item would be moved to.</param>
      <returns>true if the specified item can be moved to the specified new position; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.CaptionImage">
      <summary>
        <para>Specifies the image to display within the group&#39;s caption area.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Image"/> object that specifies the group&#39;s caption image.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.CaptionImageIndex">
      <summary>
        <para>Gets the index of an image in an image collection which must be displayed within the group&#39;s caption.</para>
      </summary>
      <value>A zero-based index of an image in an image collection, that is displayed within the group&#39;s caption.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.CaptionImageLocation">
      <summary>
        <para>Gets or sets the relative position of an image within the group caption.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.GroupElementLocation"/> value that specifies the image&#39;s position.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.CaptionImageOptions">
      <summary>
        <para>Provides access to all properties that allow you to assign and customize an image for this group&#39;s caption area.</para>
      </summary>
      <value>A DevExpress.XtraLayout.LayoutGroupCaptionImageOptions object that stores all properties that allow you to assign and customize an image for this group&#39;s caption area.</value>
    </member>
    <member name="F:DevExpress.XtraLayout.LayoutGroup.captionImageOptionsCore">
      <summary>
        <para>The <see cref="P:DevExpress.XtraLayout.LayoutGroup.CaptionImageOptions"/> property backing field.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.CaptionImagePadding">
      <summary>
        <para>Gets or sets padding for the image displayed in the current group&#39;s page header.</para>
      </summary>
      <value>A Padding value that specifies padding for the image displayed in the current group&#39;s page header.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.CaptionImageVisible">
      <summary>
        <para>Gets or sets whether the group caption image is visible.</para>
      </summary>
      <value>true if the group caption image is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.CellSize">
      <summary>
        <para>Not supported.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.Clear">
      <summary>
        <para>Removes the items from the current group.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.Clear(System.Boolean)">
      <summary>
        <para>Removes all items from the current group.</para>
      </summary>
      <param name="disposeItemAndControls">true, to release all resources; otherwise false.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.ClearSelection">
      <summary>
        <para>Clears selected items specified by the <see cref="P:DevExpress.XtraLayout.LayoutGroup.SelectedItems"/> property.</para>
      </summary>
      <returns>true if the selection was modified; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.ContentImage">
      <summary>
        <para>Gets or sets the group&#39;s background image that is displayed &quot;as is&quot;, and can be aligned to any group&#39;s edge.</para>
      </summary>
      <value>An <see cref="T:System.Drawing.Image"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.ContentImageAlignment">
      <summary>
        <para>Gets or sets the content image&#39;s alignment within the group.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.ContentAlignment"/> value that specifies the content image&#39;s alignment.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.ContentImageOptions">
      <summary>
        <para>Provides access to all properties that allow you to assign and customize this group&#39;s background image at the top layer.</para>
      </summary>
      <value>A DevExpress.XtraLayout.LayoutGroupContentImageOptions object that comprises properties that allow you to assign and customize this group&#39;s background image at the top layer.</value>
    </member>
    <member name="F:DevExpress.XtraLayout.LayoutGroup.contentImageOptionsCore">
      <summary>
        <para>The <see cref="P:DevExpress.XtraLayout.LayoutGroup.ContentImageOptions"/> property backing field.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.Count">
      <summary>
        <para>Gets the number of items that the current group owns.</para>
      </summary>
      <value>An integer that specifies the number of items that the current group own.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.CreateGroupForSelectedItems">
      <summary>
        <para>Creates a new group that will contain the currently selected child items.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> descendant that specifies the group that has been created.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.CreateTabbedGroupForSelectedGroup">
      <summary>
        <para>Creates a new tabbed group that will display the selected child group within the current group.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraLayout.TabbedGroup"/> descendant that specifies the group that has been created.</returns>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutGroup.CustomButtonChecked">
      <summary>
        <para>Fires when a custom header button is checked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutGroup.CustomButtonClick">
      <summary>
        <para>Fires on a regular (push) custom header button click.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutGroup.CustomButtonUnchecked">
      <summary>
        <para>Fires when a custom header button is unchecked.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutGroup.CustomDrawBackground">
      <summary>
        <para>Allows you to custom paint the group&#39;s client area background.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutGroup.CustomDrawCaption">
      <summary>
        <para>Allows you to custom paint the caption region.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.LayoutGroup.CustomDrawHeaderButton">
      <summary>
        <para>Allows you to custom paint header buttons (<see cref="P:DevExpress.XtraLayout.LayoutGroup.CustomHeaderButtons"/>).</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.CustomHeaderButtons">
      <summary>
        <para>Gets or sets the collection of custom buttons displayed in the group header.</para>
      </summary>
      <value>A collection of buttons displayed in the group header.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.DefaultLayoutType">
      <summary>
        <para>Gets or sets the default layout type for newly created items within the current group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.Utils.LayoutType"/> object that specifies the default layout type.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.Enabled">
      <summary>
        <para>Gets or sets whether layout items that belong to the current group are enabled.</para>
      </summary>
      <value>true if layout items that belong to the current group are enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.EnableIndentsWithoutBorders">
      <summary>
        <para>Gets or sets whether a layout group&#39;s indents, specified by its <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Spacing"/> and <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Padding"/> properties, are applied when the group&#39;s borders are hidden.</para>
      </summary>
      <value>true if a layout group&#39;s indents are applied when the group&#39;s borders are hidden; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.ExpandButtonLocation">
      <summary>
        <para>Gets or sets the position of the expand button within the group header.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.GroupElementLocation"/> value that specifies the expand button&#39;s position.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.ExpandButtonMode">
      <summary>
        <para>Gets or sets the direction of the expand button&#39;s arrow.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.Controls.ExpandButtonMode"/> value that specifies the direction of the expand button&#39;s arrow.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.ExpandButtonVisible">
      <summary>
        <para>Gets or sets whether the group&#39;s expand button is visible.</para>
      </summary>
      <value>true if the group&#39;s expand button is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.Expanded">
      <summary>
        <para>Gets or sets whether the current group is expanded.</para>
      </summary>
      <value>true if the group is expanded; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.ExpandOnDoubleClick">
      <summary>
        <para>Gets or sets whether the group is expanded/collapsed on double-clicking its caption.</para>
      </summary>
      <value>true if the group is expanded/collapsed on double-clicking its caption; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.FlipLayout">
      <summary>
        <para>Flips the current group&#39;s layout items around a vertical axis.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.FlipLayout(System.Boolean)">
      <summary>
        <para>Flips the current group&#39;s layout items around a vertical axis, and optionally allows you to flip the items in nested groups, if any.</para>
      </summary>
      <param name="processChildGroups">true if nested layout groups must be processed as well; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.FlowDirection">
      <summary>
        <para>Gets or sets the direction according to which the <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> arranges its child elements.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.FlowDirection"/> enumeration value that specifies the direction according to which the <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> arranges its child elements.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.GetDefaultText">
      <summary>
        <para>Gets the default value of  the group&#39;s <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> property.</para>
      </summary>
      <returns>A string that specifies the default value of  the group&#39;s <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> property.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.GroupBordersVisible">
      <summary>
        <para>Gets whether the group&#39;s borders are visible.</para>
      </summary>
      <value>true if the group&#39;s borders are visible; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.GroupStyle">
      <summary>
        <para>Gets or sets the border and the header style of this <see cref="T:DevExpress.XtraLayout.LayoutGroup"/>.</para>
      </summary>
      <value>A DevExpress.Utils.GroupStyle enumeration value that specifies the border and the header style of this <see cref="T:DevExpress.XtraLayout.LayoutGroup"/>.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.HeaderButtonsLocation">
      <summary>
        <para>Gets or sets whether the group header buttons are displayed before or after text.</para>
      </summary>
      <value>The position of the group header button relative to the header text.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.ImageList">
      <summary>
        <para>Gets an image collection whose images can be displayed within the group.</para>
      </summary>
      <value>An object which represents an image collection.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.Invalidate">
      <summary>
        <para>Invalidates the region which is occupied by the Layout Group.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.IsInTabbedGroup">
      <summary>
        <para>Gets whether the current group is displayed as a tab within a tabbed group.</para>
      </summary>
      <value>true if the current group is displayed as a tab within a tabbed group; otherwise false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.Item(System.Int32)">
      <summary>
        <para>Provides indexed access to the items that belong to the current group.</para>
      </summary>
      <param name="Index">An integer that specifies the index of the required item.</param>
      <value>A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object that represents the item at the specified index.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.Items">
      <summary>
        <para>Provides access to the collection of items owned by the current group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.Utils.LayoutGroupItemCollection"/> object that contains the group&#39;s child items.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.LayoutMode">
      <summary>
        <para>Gets or sets the layout mode for a <see cref="T:DevExpress.XtraLayout.LayoutGroup"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.Utils.LayoutMode"/> enumerator value that specifies the layout mode for the LayoutGroup.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.MaxSize">
      <summary>
        <para>Gets the maximum size of the group.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the group&#39;s maximum width and height.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.MinSize">
      <summary>
        <para>Gets the minimum size of the group.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the group&#39;s minimum width and height.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.Move(DevExpress.XtraLayout.Customization.LayoutItemDragController)">
      <summary>
        <para>Moves the layout group to the specified position within the LayoutControl.</para>
      </summary>
      <param name="controller">A <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> object whose settings determine the position to which the layout group should be moved.</param>
      <returns>true if the layout group has been successfully moved to a new position; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.MoveFocusDirection">
      <summary>
        <para>Gets or sets whether, in Automatic Tab Order mode when TAB is pressed, the focus moves across grouped controls and then down, or first down and then across.</para>
      </summary>
      <value>A DevExpress.XtraLayout.MoveFocusDirectionGroup enumeration value that specifies whether the focus moves across grouped controls and then down, or first down and then across. Default uses the <see cref="P:DevExpress.XtraLayout.OptionsFocus.MoveFocusDirection"/> setting.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.MoveFocusRightToLeft">
      <summary>
        <para>Gets or sets whether, in Automatic Tab Order mode when TAB is pressed, the focus moves across grouped controls from right to left.</para>
      </summary>
      <value>true to move focus from right to left; false to move focus from left to right. Default uses the <see cref="P:DevExpress.XtraLayout.OptionsFocus.MoveFocusRightToLeft"/> setting.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.OptionsItemText">
      <summary>
        <para>Gets the options that determine how the controls displayed within the current group are aligned.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.OptionsItemTextGroup"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.OptionsPrint">
      <summary>
        <para>Provides access to options that specify how the <see cref="T:DevExpress.XtraLayout.LayoutGroup"/>  is printed/exported .</para>
      </summary>
      <value>An object containing the layout group&#39;s print and export options.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.OptionsTableLayoutGroup">
      <summary>
        <para>Provides settings and events for table layout customization.</para>
      </summary>
      <value>An object that contains options and settings specific to table layout mode.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.PageEnabled">
      <summary>
        <para>Gets or sets whether the tab that corresponds to the current group is enabled. This property is in effect when the group is represented as a tab page within a <see cref="T:DevExpress.XtraLayout.TabbedControlGroup"/>.</para>
      </summary>
      <value>true if the tab that corresponds to the current group is enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.Parent">
      <summary>
        <para>Gets or sets the group that owns the current group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> or its descendant that owns the current group.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.ParentTabbedGroup">
      <summary>
        <para>Gets the parent tabbed group if the current group is displayed as a tab.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.TabbedGroup"/> descendant that specifies the parent tabbed group; null if the current group is not displayed as a tab within a tabbed group.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.Remove(DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para>Removes the specified child item within the current group.</para>
      </summary>
      <param name="item">The <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object that should be removed.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.RemoveAt(System.Int32)">
      <summary>
        <para>Removes the child item at the specified index.</para>
      </summary>
      <param name="index">An integer that specifies the index of the item to remove.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.RotateLayout">
      <summary>
        <para>Interchanges the columns and rows of layout items, including columns and rows of nested groups.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.RotateLayout(System.Boolean)">
      <summary>
        <para>Interchanges the columns and rows of layout items, and optionally allows you to rotate nested groups.</para>
      </summary>
      <param name="processChildGroups">true if nested layout groups must be rotated as well; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.SelectedItems">
      <summary>
        <para>Provides access to the collection of selected items within the current group.</para>
      </summary>
      <value>A DevExpress.XtraLayout.Utils.BaseItemCollection collection that contains the group&#39;s selected items.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.ShowTabPageCloseButton">
      <summary>
        <para>Gets or sets whether a Close button is displayed within a group when it&#39;s embedded into a <see cref="T:DevExpress.XtraLayout.TabbedGroup"/>.</para>
      </summary>
      <value>true if a Close button is visible when a group is embedded into a TabbedGroup; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.Size">
      <summary>
        <para>Gets or sets the group&#39;s size.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the group&#39;s width and height.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.TabbedGroupParentName">
      <summary>
        <para>Gets the name of the group&#39;s parent if the current group is displayed as a tab.</para>
      </summary>
      <value>A string which specifies the parent&#39;s name.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.TypeName">
      <summary>
        <para>Gets the name of the item&#39;s type.</para>
      </summary>
      <value>The LayoutGroup value.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutGroup.UngroupSelected">
      <summary>
        <para>Ungroups the selected child group.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutGroup.ViewInfo">
      <summary>
        <para>Gets the object which contains the information used to render the layout group.</para>
      </summary>
      <value>A DevExpress.XtraLayout.ViewInfo.LayoutGroupViewInfo object.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.LayoutItem">
      <summary>
        <para>Represents the base class for layout items.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.LayoutItemContainer">
      <summary>
        <para>Represets a container of layout items.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutItemContainer.AllowCustomizeChildren">
      <summary>
        <para>Gets or sets whether the layout of the current container can be customized using drag and drop.</para>
      </summary>
      <value>true if the layout of the current container can be customized using drag and drop; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutItemContainer.AppearanceGroup">
      <summary>
        <para>Provides access to the properties that control the group&#39;s appearance.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTab.PageAppearance"/> object that provides the appearance settings used to paint the group.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutItemContainer.AppearanceTabPage">
      <summary>
        <para>Provides access to a tabbed group&#39;s appearance settings.</para>
      </summary>
      <value>A DevExpress.XtraLayout.Helpers.LayoutPageAppearance object that provides the appearance settings for a tabbed group.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutItemContainer.BeginUpdate">
      <summary>
        <para>Locks the <see cref="T:DevExpress.XtraLayout.LayoutItemContainer"></see> object by preventing visual updates of the object and its elements until the EndUpdate method is called.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutItemContainer.Contains(DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para>Gets whether the specified item belongs to the current container.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> object whose membership should be tested.</param>
      <returns>true if the specified item belongs to the current container; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutItemContainer.EndUpdate">
      <summary>
        <para>Unlocks the <see cref="T:DevExpress.XtraLayout.LayoutItemContainer"></see> object after a call to the BeginUpdate method and causes an immediate visual update.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutItemContainer.PaintAppearanceGroup">
      <summary>
        <para>Provides access to the appearance settings currently used to paint the layout group.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutItemContainer.PaintAppearanceItemCaption">
      <summary>
        <para>Provides access to the appearance settings currently used to paint layout items that belong to the current container.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that contains the corresponding appearance settings.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutItemContainer.Parent">
      <summary>
        <para>Gets or sets the layout group that owns the <see cref="T:DevExpress.XtraLayout.LayoutItemContainer"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object that owns the <see cref="T:DevExpress.XtraLayout.LayoutItemContainer"/>.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutItemContainer.TabPageWidth">
      <summary>
        <para>Gets or sets the width, in pixels, of tab headers.</para>
      </summary>
      <value>An integer value that specifies the width, in pixels, of tab headers.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutItemContainer.TextLocation">
      <summary>
        <para>Gets or sets the container&#39;s side along which its caption is displayed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.Locations"/> value which specifies the container&#39;s side along which its caption is displayed.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutItemContainer.TextSize">
      <summary>
        <para>Not supported.</para>
      </summary>
      <value>A <see cref="F:System.Drawing.Size.Empty"/> structure.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutItemContainer.TextToControlDistance">
      <summary>
        <para>Not supported.</para>
      </summary>
      <value>Zero.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.LayoutMenuEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraLayout.LayoutControl.PopupMenuShowing"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutMenuEventArgs.#ctor(DevExpress.Utils.Menu.DXPopupMenu,DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.LayoutMenuEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="menu">A <see cref="T:DevExpress.Utils.Menu.DXPopupMenu"/> object representing the menu to be displayed. This object is assigned to the Menu property.</param>
      <param name="hitInfo">A <see cref="T:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo"/> object that identifies the clicked object. This object is assigned to the HitInfo property.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutMenuEventArgs.#ctor(DevExpress.Utils.Menu.DXPopupMenu,DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.LayoutMenuEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="menu">A <see cref="T:DevExpress.Utils.Menu.DXPopupMenu"/> object representing the menu to be displayed. This object is assigned to the Menu property.</param>
      <param name="hitInfo">A <see cref="T:DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo"/> object that identifies the clicked object. This object is assigned to the HitInfo property.</param>
      <param name="allow">A Boolean value that specifies whether the menu is allowed to be displayed. This value is assigned to the Allow property.</param>
    </member>
    <member name="T:DevExpress.XtraLayout.LayoutMenuEventHandler">
      <summary>
        <para>Represents a method for handling the <see cref="E:DevExpress.XtraLayout.LayoutControl.PopupMenuShowing"/> event.</para>
      </summary>
      <param name="sender">The event source. This identifies the LayoutControl control which fires the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraLayout.LayoutMenuEventArgs"/> object that contains data for the event.</param>
    </member>
    <member name="T:DevExpress.XtraLayout.LayoutRepositoryItem">
      <summary>
        <para>Represents a layout item capable of displaying information provided by a repository item object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutRepositoryItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.LayoutRepositoryItem"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutRepositoryItem.#ctor(DevExpress.XtraEditors.Repository.RepositoryItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.LayoutRepositoryItem"/> class with the specified repository item.</para>
      </summary>
      <param name="editor">A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> object to be associated with the created layout item. This value is assigned to the <see cref="P:DevExpress.XtraLayout.LayoutRepositoryItem.RepositoryItem"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutRepositoryItem.ControlName">
      <summary>
        <para>This property supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value>A string value.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutRepositoryItem.EditorPreferredWidth">
      <summary>
        <para>Specifies the desired width of the layout item&#39;s region displaying the editor (repository item).</para>
      </summary>
      <value>An integer value that specifies the width of the layout item&#39;s edit portion.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutRepositoryItem.EditValue">
      <summary>
        <para>Gets or sets a value to be displayed by a repository item. This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value>An object representing the value to be displayed.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutRepositoryItem.RepositoryItem">
      <summary>
        <para>Gets or sets a repository item associated with the current layout item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> object to be associated with the current layout item.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutRepositoryItem.ResetEditorPreferredWidth">
      <summary>
        <para>Resets the desired width of the layout item&#39;s region displaying an editor.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutRepositoryItem.TypeName">
      <summary>
        <para>Gets the name of the item&#39;s type.</para>
      </summary>
      <value>The &quot;LayoutRepositoryItem&quot; string.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutRepositoryItem.ViewInfo">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="T:DevExpress.XtraLayout.LayoutSerializationOptions">
      <summary>
        <para>Contains options that control how the LayoutControl&#39;s layout is saved to and restored from a data store (an XML file, stream or the system registry).</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutSerializationOptions.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.LayoutSerializationOptions"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.LayoutSerializationOptions.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Copies options from the specified object to the current object.</para>
      </summary>
      <param name="options">A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.DiscardOldItems">
      <summary>
        <para>Gets or sets whether the items that exist in a layout when it&#39;s restored, but that don&#39;t exist in the current layout control, should be discarded or added to the control.</para>
      </summary>
      <value>true to discard the items that exist in the layout being restored, but don&#39;t exist in the current layout control; false to add these items to the layout control.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RecreateIFixedItems">
      <summary>
        <para>Gets or sets whether or not secondary layout items should be re-created when restoring the LayoutControl&#39;s layout.</para>
      </summary>
      <value>true, if secondary layout items should be re-created; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RestoreAppearanceItemCaption">
      <summary>
        <para>Gets or sets whether the appearance settings of item captions are loaded when a layout is loaded from a data store.</para>
      </summary>
      <value>A Boolean value that specifies whether the corresponding appearance options are restored when a layout is restored.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RestoreAppearanceTabPage">
      <summary>
        <para>Gets or sets whether the appearance settings of tab pages are loaded when a layout is loaded from a data store.</para>
      </summary>
      <value>A Boolean value that specifies whether the corresponding appearance options are restored when a layout is restored.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RestoreGroupEnabled">
      <summary>
        <para>Gets or sets whether layout items&#39; state settings (<see cref="P:DevExpress.XtraLayout.LayoutGroup.Enabled"/>) are restored when restoring the control&#39;s layout from a data store.</para>
      </summary>
      <value>true, to restore the corresponding settings; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RestoreGroupPadding">
      <summary>
        <para>Gets or sets whether layout groups&#39; padding settings (<see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Padding"/>) are restored when restoring the control&#39;s layout from a data store.</para>
      </summary>
      <value>true to restore the corresponding settings; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RestoreGroupSpacing">
      <summary>
        <para>Gets or sets whether layout groups&#39; spacing settings (<see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Spacing"/>) are restored when restoring the control&#39;s layout from a data store.</para>
      </summary>
      <value>true to restore the corresponding settings; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RestoreLayoutGroupAppearanceGroup">
      <summary>
        <para>Gets or sets whether the appearance settings of groups are loaded when a layout is loaded from a data store.</para>
      </summary>
      <value>A Boolean value that specifies whether the corresponding appearance options are restored when a layout is restored.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RestoreLayoutItemCustomizationFormText">
      <summary>
        <para>Gets or sets whether values of layout items&#39; <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.CustomizationFormText"/> properties are restored when restoring the control&#39;s layout from a data store.</para>
      </summary>
      <value>true to restore the corresponding settings; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RestoreLayoutItemEnabled">
      <summary>
        <para>Gets or sets whether layout items&#39; state settings (<see cref="P:DevExpress.XtraLayout.LayoutControlItem.Enabled"/>) are restored when restoring the control&#39;s layout from a data store.</para>
      </summary>
      <value>true, to restore the corresponding settings; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RestoreLayoutItemPadding">
      <summary>
        <para>Gets or sets whether layout items&#39; padding settings (<see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Padding"/>) are restored when restoring the control&#39;s layout from a data store.</para>
      </summary>
      <value>true to restore the corresponding settings; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RestoreLayoutItemSpacing">
      <summary>
        <para>Gets or sets whether layout items&#39; spacing settings (<see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Spacing"/>) are restored when restoring the control&#39;s layout from a data store.</para>
      </summary>
      <value>true to restore the corresponding settings; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RestoreLayoutItemText">
      <summary>
        <para>Gets or sets whether layout items&#39; captions are restored when restoring the control&#39;s layout from a data store.</para>
      </summary>
      <value>true if layout items&#39; captions are restored when restoring the control&#39;s layout from a data store; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RestoreRootGroupPadding">
      <summary>
        <para>Gets or sets whether the layout root group&#39;s padding settings (<see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Padding"/>) are restored when restoring the control&#39;s layout from a data store.</para>
      </summary>
      <value>true to restore the corresponding settings; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RestoreRootGroupSpacing">
      <summary>
        <para>Gets or sets whether the layout root group&#39;s spacing settings (<see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Spacing"/>) are restored when restoring the control&#39;s layout from a data store.</para>
      </summary>
      <value>true to restore the corresponding settings; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RestoreTabbedGroupPadding">
      <summary>
        <para>Gets or sets whether tabbed groups&#39; padding settings (<see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Padding"/>) are restored when restoring the control&#39;s layout from a data store.</para>
      </summary>
      <value>true to restore the corresponding settings; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RestoreTabbedGroupSpacing">
      <summary>
        <para>Gets or sets whether tabbed groups&#39; spacing settings (<see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Spacing"/>) are restored when restoring the control&#39;s layout from a data store.</para>
      </summary>
      <value>true to restore the corresponding settings; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.LayoutSerializationOptions.RestoreTextToControlDistance">
      <summary>
        <para>Gets or sets whether values of layout items&#39; <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.TextToControlDistance"/> properties are restored when restoring the control&#39;s layout from a data store.</para>
      </summary>
      <value>true to restore the corresponding settings; otherwise, false.</value>
    </member>
    <member name="N:DevExpress.XtraLayout.Localization">
      <summary>
        <para>Contains classes and enumerations that are intended to localize the User Interface of the DevExpress WinForms Layout Manager.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.Localization.LayoutLocalizer">
      <summary>
        <para>A base class that provides necessary functionality for custom localizers of the Layout Manager.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.Localization.LayoutLocalizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.Localization.LayoutLocalizer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.Localization.LayoutLocalizer.Active">
      <summary>
        <para>Gets or sets a localizer object providing localization of the user interface at runtime.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> descendant, used to localize the user interface at runtime.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.Localization.LayoutLocalizer.CreateDefaultLocalizer">
      <summary>
        <para>Returns a localizer object, which provides resources based on the thread&#39;s language and regional settings (culture).</para>
      </summary>
      <returns>An <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object representing resources based on the thread&#39;s culture.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.Localization.LayoutLocalizer.CreateResXLocalizer">
      <summary>
        <para>Returns a localizer object, which provides resources based on the thread&#39;s language and regional settings (culture).</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object, which provides resources based on the thread&#39;s culture.</returns>
    </member>
    <member name="T:DevExpress.XtraLayout.Localization.LayoutResLocalizer">
      <summary>
        <para>A default localizer to translate resources for the WinForms Layout Manager.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.Localization.LayoutResLocalizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.Localization.LayoutResLocalizer"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.Localization.LayoutResLocalizer.GetLocalizedString(DevExpress.XtraLayout.Localization.LayoutStringId)">
      <summary>
        <para>Gets the string, localized by the current <see cref="T:DevExpress.XtraLayout.Localization.LayoutResLocalizer"/>, for the specified user interface element.</para>
      </summary>
      <param name="id">A DevExpress.XtraLayout.Localization.LayoutStringId enumeration value specifying the UI element whose caption (text) is to be localized.</param>
      <returns>A <see cref="T:System.String"/> representing the text to be displayed within the specified UI element.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.Localization.LayoutResLocalizer.Language">
      <summary>
        <para>Returns the name of the language currently used by this localizer object.</para>
      </summary>
      <value>A <see cref="T:System.String"/> specifying the language used to localize the user interface.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.MoveFocusDirection">
      <summary>
        <para>Specifies the direction in which focus is moved when the TAB key is pressed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.MoveFocusDirection.AcrossThenDown">
      <summary>
        <para>Focus moves from one &#39;row&#39; to another selecting each control in a row.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.MoveFocusDirection.DownThenAcross">
      <summary>
        <para>Focus moves from one &#39;column&#39; to another selecting each control in a column</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.OptionsCustomizationForm">
      <summary>
        <para>Contains options that affect the appearance of the Customization Form.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsCustomizationForm.#ctor(DevExpress.XtraLayout.ILayoutControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.OptionsCustomizationForm"/> class.</para>
      </summary>
      <param name="control">An object that implements the DevExpress.XtraLayout.ILayoutControl interface, and owns the created <see cref="T:DevExpress.XtraLayout.OptionsCustomizationForm"/> object.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.AllowHandleDeleteKey">
      <summary>
        <para>Gets or sets whether the default handling of the DELETE key in customization mode is disabled.</para>
      </summary>
      <value>true if the default handling of the DELETE key in customization mode is disabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.AllowUndoManager">
      <summary>
        <para>Gets or sets whether the LayoutControl allows an end-user to undo/redo runtime customization operations.</para>
      </summary>
      <value>true if the LayoutControl allows an end-user to undo/redo runtime customization operations; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.DefaultPage">
      <summary>
        <para>Gets or sets whether the Hidden Items Tab or Layout Tree View Tab is selected by default.</para>
      </summary>
      <value>A DevExpress.XtraLayout.CustomizationPage enumeration value, such as HiddenItems or LayoutTreeView, that specifies whether the Hidden Items Tab or Layout Tree View Tab is selected by default.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.DefaultRestoreDirectory">
      <summary>
        <para>Gets or sets the default directory for the Open dialog that is opened when clicking on the Customization Form&#39;s Load Layout button.</para>
      </summary>
      <value>A string that specifies the default directory for the Open dialog invoked via the Customization Form.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.DefaultSaveDirectory">
      <summary>
        <para>Gets or sets the default directory for the Save dialog that is opened when clicking on the Customization Form&#39;s Save Layout button.</para>
      </summary>
      <value>A string that specifies the default directory for the Save dialog invoked via the Customization Form.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize">
      <summary>
        <para>Gets or sets the coordinates and bounding rectangle of the control&#39;s Customization Form at design time.</para>
      </summary>
      <value>A Nullable Rectangle value that specifies the Customization Form&#39;s coordinates and bounds at design time.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.EnableUndoManager">
      <summary>
        <para>Gets or sets whether the Undo Manager, which allows customization operations to be reverted, is enabled.</para>
      </summary>
      <value>true if the Undo Manager is enabled; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsCustomizationForm.GetShowPropertyGrid">
      <summary>
        <para>Returns whether the Property Grid is displayed within the Customization Form.</para>
      </summary>
      <returns>true, to display the Property Grid within the Customization Form; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.QuickModeInitDelay">
      <summary>
        <para>Gets or sets a delay between pressing the <see cref="T:DevExpress.XtraLayout.LayoutControl"/> and the start of quick customization mode initialization.</para>
      </summary>
      <value>A value that specifies the delay between pressing the <see cref="T:DevExpress.XtraLayout.LayoutControl"/> and the start of quick customization mode initialization (displaying the load indicator).</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.QuickModeLoadIndicatorSize">
      <summary>
        <para>Gets or sets the size of the quick customization mode load indicator.</para>
      </summary>
      <value>A value that specifies the load indicator&#39;s height and width.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.QuickModeLoadTime">
      <summary>
        <para>Gets or sets quick mode activation time.</para>
      </summary>
      <value>A value that specifies quick mode activation time (load indicator filling time).</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.ShowLayoutTreeView">
      <summary>
        <para>Gets or sets whether the Layout Tree View tab page is visible in the Customization Form.</para>
      </summary>
      <value>true if the Layout Tree View tab page is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.ShowLoadButton">
      <summary>
        <para>Gets or sets whether the Customization Form&#39;s Load button is visible.</para>
      </summary>
      <value>true if the Load button is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.ShowPropertyGrid">
      <summary>
        <para>Gets or sets whether the Property Grid is displayed within the Customization Form.</para>
      </summary>
      <value>true to display the Property Grid within the Customization Form; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.ShowRedoButton">
      <summary>
        <para>Gets or sets whether the Customization Form&#39;s Redo button is visible.</para>
      </summary>
      <value>true if the Redo button is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.ShowSaveButton">
      <summary>
        <para>Gets or sets whether the Customization Form&#39;s Save button is visible.</para>
      </summary>
      <value>true if the Save button is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.ShowUndoButton">
      <summary>
        <para>Gets or sets whether the Customization Form&#39;s Undo button is visible.</para>
      </summary>
      <value>true if the Undo button is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsCustomizationForm.SnapMode">
      <summary>
        <para>Gets or sets to which objects this Customization Form is allowed to snap.</para>
      </summary>
      <value>A SnapMode enumeration value that specifies to which objects this Customization Form is allowed to snap.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.OptionsFocus">
      <summary>
        <para>Contains options that affect focus movement between controls within the Layout Control and layout groups.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsFocus.#ctor(DevExpress.XtraLayout.ILayoutControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.OptionsFocus"/> class.</para>
      </summary>
      <param name="owner">An object that implements the ILayoutControl interface, which will receive change notifications when properties of the created object are changed.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsFocus.#ctor(DevExpress.XtraLayout.MoveFocusDirection,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.OptionsFocus"/> class with the specified focus direction and right-to-left setting.</para>
      </summary>
      <param name="direction">A <see cref="T:DevExpress.XtraLayout.MoveFocusDirection"/> value that specifies how focus is moved when the TAB key is pressed. This value is assigned to the <see cref="P:DevExpress.XtraLayout.OptionsFocus.MoveFocusDirection"/> property.</param>
      <param name="rightToLeft">A Boolean value that specifies whether focus needs to be moved from right to left when the TAB key is pressed. This value is assigned to the <see cref="P:DevExpress.XtraLayout.OptionsFocus.MoveFocusRightToLeft"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsFocus.ActivateSelectedControlOnGotFocus">
      <summary>
        <para>Gets or sets whether the currently selected child control is activated when the LayoutControl receives focus.</para>
      </summary>
      <value>true if the currently selected child control is activated when the LayoutControl receives focus; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsFocus.AllowFocusControlOnActivatedTabPage">
      <summary>
        <para>Gets or sets whether the first control within a tab page is focused when the tab page is activated.</para>
      </summary>
      <value>true if the first control within a tab page is focused when the tab page is activated; false if focus is not moved to this control.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsFocus.AllowFocusControlOnLabelClick">
      <summary>
        <para>Gets or sets whether a layout item&#39;s control is focused when its label is clicked.</para>
      </summary>
      <value>true if a layout item&#39;s control is focused when its label is clicked; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsFocus.AllowFocusGroups">
      <summary>
        <para>Gets or sets whether the expand buttons of regular groups can be focused when the TAB key is pressed.</para>
      </summary>
      <value>true if the expand buttons of groups can receive focus; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsFocus.AllowFocusReadonlyEditors">
      <summary>
        <para>Gets or sets whether read-only editors are focused when focus is moved between controls using the TAB key.</para>
      </summary>
      <value>true if  read-only editors are focused when focus is moved between controls using the TAB key; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsFocus.AllowFocusTabbedGroups">
      <summary>
        <para>Gets or sets whether the headers of tabbed groups can be focused.</para>
      </summary>
      <value>true if the headers of tabbed groups can be focused; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsFocus.EnableAutoTabOrder">
      <summary>
        <para>Gets or sets whether the Automatic Tab Order feature is enabled.</para>
      </summary>
      <value>true if the Automatic Tab Order feature is enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsFocus.MoveFocusDirection">
      <summary>
        <para>Gets or sets the direction in which focus moves in Automatic Tab Order mode when the TAB key is pressed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.MoveFocusDirection"/> value that specifies the direction in which focus moves.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsFocus.MoveFocusRightToLeft">
      <summary>
        <para>Gets or sets whether focus moves from right to left.</para>
      </summary>
      <value>true if focus moves from right to left; false if focus moves from left to right.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.OptionsItemText">
      <summary>
        <para>Contains default options that specify how the text regions of layout items displayed within the LayoutControl are rendered.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsItemText.#ctor(DevExpress.XtraLayout.ILayoutControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.OptionsItemText"/> class.</para>
      </summary>
      <param name="owner">An object that implements the ILayoutControl interface, which will receive change notifications when properties of the created object are changed.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsItemText.AlignControlsWithHiddenText">
      <summary>
        <para>Gets or sets whether controls with hidden text must be aligned when the control horizontal alignment is enabled.</para>
      </summary>
      <value>true if controls with hidden text must be aligned when the control horizontal alignment is enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsItemText.AutoAlignMode">
      <summary>
        <para>Specifies the alignment options.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsItemText.Dispose">
      <summary>
        <para>Releases all resources used by the current object.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsItemText.EnableAutoAlignment">
      <summary>
        <para>Gets or sets whether the automatic alignment of controls is enabled.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsItemText.TextAlignMode">
      <summary>
        <para>Gets or sets the alignment settings of the controls which are displayed in the LayoutControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.TextAlignMode"/> value which specifies the current alignment mode.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsItemText.TextOptions">
      <summary>
        <para>Gets options that specify how text is rendered.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.TextOptions"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsItemText.TextToControlDistance">
      <summary>
        <para>Gets or sets the default distance between the text regions of layout items and the corresponding controls.</para>
      </summary>
      <value>An integer value that represents the distance between the text regions of layout items and the corresponding controls.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.OptionsItemTextGroup">
      <summary>
        <para>Contains options that specify how the text regions of layout items, displayed within a specific group, are rendered.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsItemTextGroup.#ctor(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.OptionsItemTextGroup"/> class with default settings.</para>
      </summary>
      <param name="owner">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object which will own the new object.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsItemTextGroup.AlignControlsWithHiddenText">
      <summary>
        <para>Gets or sets whether controls with hidden text must be aligned when the local horizontal alignment of controls within the group is enabled.</para>
      </summary>
      <value>true if controls with hidden text must be aligned when the local horizontal alignment is enabled; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsItemTextGroup.Dispose">
      <summary>
        <para>Disposes of the current object.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsItemTextGroup.TextAlignMode">
      <summary>
        <para>Gets or sets the alignment settings of the controls displayed in the current group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.TextAlignModeGroup"/> value which specifies the alignment settings of the controls.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsItemTextGroup.TextToControlDistance">
      <summary>
        <para>Gets or sets the distance between the text regions of layout items and the corresponding controls.</para>
      </summary>
      <value>An integer value that represents the distance between the text regions of layout items and  the corresponding controls.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.OptionsPrintBase">
      <summary>
        <para>The base class for classes that provide print and export options for layout items and groups.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsPrintBase.#ctor(DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para>Initializes a new instance of an <see cref="T:DevExpress.XtraLayout.OptionsPrintBase"/> class with the specified owner.</para>
      </summary>
      <param name="owner">An object that will own the created object.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsPrintBase.AllowPrint">
      <summary>
        <para>Gets or sets whether a layout item along with its nested items are included in a <see cref="T:DevExpress.XtraLayout.LayoutControl"/>&#39;s print/export output .</para>
      </summary>
      <value>true, if the layout item and its nested items are printed/exported; false, if they are excluded from the printing/export output.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsPrintBase.AppearanceItemCaption">
      <summary>
        <para>Contains appearance settings used to paint a layout item&#39;s caption in a <see cref="T:DevExpress.XtraLayout.LayoutControl"/>&#39;s print/export output.</para>
      </summary>
      <value>An object that contains settings to customize the appearance of the layout item caption.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsPrintBase.TextToControlDistance">
      <summary>
        <para>Gets or sets the distance between a layout item&#39;s text and its control in the LayoutControl&#39;s print/export output.</para>
      </summary>
      <value>A value that specifies the distance between the text region and control, in pixels.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.OptionsPrintControl">
      <summary>
        <para>Provides print and export options for the <see cref="T:DevExpress.XtraLayout.LayoutControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsPrintControl.#ctor(DevExpress.XtraLayout.ILayoutControl)">
      <summary>
        <para>Initializes a new instance of an <see cref="T:DevExpress.XtraLayout.OptionsPrintControl"/> class with the specified owner.</para>
      </summary>
      <param name="owner">An object that will own the created object.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsPrintControl.AllowFitToPage">
      <summary>
        <para>Gets or sets whether the <see cref="T:DevExpress.XtraLayout.LayoutControl"/>, when printed, is horizontally stretched to the width of the printing page.</para>
      </summary>
      <value>true, if the LayoutControl is fit to the page width; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsPrintControl.AppearanceGroupCaption">
      <summary>
        <para>Contains appearance settings used to paint captions of layout groups when a <see cref="T:DevExpress.XtraLayout.LayoutControl"/> is printed/exported.</para>
      </summary>
      <value>An object that contains settings to customize the appearance of layout group captions.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsPrintControl.AppearanceItemCaption">
      <summary>
        <para>Contains appearance settings used to paint layout item captions in a <see cref="T:DevExpress.XtraLayout.LayoutControl"/>&#39;s print/export output.</para>
      </summary>
      <value>An object that contains settings to customize the appearance of layout item captions.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsPrintControl.Dispose">
      <summary>
        <para>Disposes of the current object.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsPrintControl.OldPrinting">
      <summary>
        <para>Gets or sets whether a <see cref="T:DevExpress.XtraLayout.LayoutControl"/> utilizes the old print/export mode.</para>
      </summary>
      <value>true, if the LayoutControl uses the old print/export mode; false, if it uses the default mode.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsPrintControl.TextToControlDistance">
      <summary>
        <para>Gets or sets the distance between text regions of all layout items and their controls in a <see cref="T:DevExpress.XtraLayout.LayoutControl"/>&#39;s print/export output.</para>
      </summary>
      <value>A value that specifies the distance between text regions and controls, in pixels.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.OptionsPrintGroup">
      <summary>
        <para>Provides print and export options for layout groups.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsPrintGroup.#ctor(DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para>Initializes a new instance of an <see cref="T:DevExpress.XtraLayout.OptionsPrintGroup"/> class with the specified owner.</para>
      </summary>
      <param name="owner">An object that will own the created object.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsPrintGroup.AllowPrintGroupCaption">
      <summary>
        <para>Gets or sets whether the layout group&#39;s caption is included in the print/export output .</para>
      </summary>
      <value>true, if the layout group&#39;s caption is printed/exported; false, if the group&#39;s caption is excluded from the print/export output.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsPrintGroup.AppearanceGroupCaption">
      <summary>
        <para>Contains appearance settings used to paint a layout group&#39;s caption in a <see cref="T:DevExpress.XtraLayout.LayoutControl"/>&#39;s print/export output.</para>
      </summary>
      <value>An object that contains settings to customize the appearance of the layout group&#39;s caption.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.OptionsPrintItem">
      <summary>
        <para>Provides print and export options for layout items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsPrintItem.#ctor(DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para>Initializes a new instance of an <see cref="T:DevExpress.XtraLayout.OptionsPrintItem"/> class with the specified owner.</para>
      </summary>
      <param name="owner">An object that will own the created object.</param>
    </member>
    <member name="T:DevExpress.XtraLayout.OptionsTableLayoutGroup">
      <summary>
        <para>Provides options and methods to customize a table layout enabled for a <see cref="T:DevExpress.XtraLayout.LayoutGroup"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsTableLayoutGroup.#ctor(DevExpress.XtraLayout.ILayoutControl,DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Initializes a new instance of an <see cref="T:DevExpress.XtraLayout.OptionsTableLayoutGroup"/> class with the specified owners.</para>
      </summary>
      <param name="owner">An object that implements the ILayoutControl interface, and owns the created <see cref="T:DevExpress.XtraLayout.OptionsTableLayoutGroup"/> object.</param>
      <param name="ownerGroup">A layout group that owns the created <see cref="T:DevExpress.XtraLayout.OptionsTableLayoutGroup"/> object.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsTableLayoutGroup.Add(DevExpress.XtraLayout.ColumnDefinition)">
      <summary>
        <para>Adds the specified column to a table layout.</para>
      </summary>
      <param name="columnDefinition">An object that specifies the column to be added.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsTableLayoutGroup.Add(DevExpress.XtraLayout.RowDefinition)">
      <summary>
        <para>Adds the specified row to a table layout.</para>
      </summary>
      <param name="rowDefinition">An object that specifies the row to be added.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsTableLayoutGroup.AddColumn">
      <summary>
        <para>Creates a new column with default settings and adds it to the table layout.</para>
      </summary>
      <returns>An object that specifies the newly created column.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsTableLayoutGroup.AddRow">
      <summary>
        <para>Creates a new row with default settings and adds it to the table layout.</para>
      </summary>
      <returns>An object that specifies the new row.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsTableLayoutGroup.AutoSizeDefaultDefinitionLength">
      <summary>
        <para>Gets or sets the default size for empty rows/columns of the SizeType.AutoSize type in table layout mode.</para>
      </summary>
      <value>A value that specifies the size for rows/columns of the AutoSize type, in pixels.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsTableLayoutGroup.ColumnCount">
      <summary>
        <para>Returns the number of columns within the table layout.</para>
      </summary>
      <value>The number of columns within the table layout.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsTableLayoutGroup.ColumnDefinitions">
      <summary>
        <para>Provides access to columns of the table layout.</para>
      </summary>
      <value>The table layout&#39;s columns.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsTableLayoutGroup.GetColumn(System.Int32)">
      <summary>
        <para>Returns a column by its index within the table layout.</para>
      </summary>
      <param name="index">The column&#39;s zero-based index.</param>
      <returns>The column located at the specified position within the column collection.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsTableLayoutGroup.GetRow(System.Int32)">
      <summary>
        <para>Returns a row by its index within the table layout.</para>
      </summary>
      <param name="index">The row&#39;s zero-based index.</param>
      <returns>The row located at the specified position within the row collection.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsTableLayoutGroup.Insert(System.Int32,DevExpress.XtraLayout.ColumnDefinition,System.Boolean)">
      <summary>
        <para>Inserts an element into the collection at the specified index.</para>
      </summary>
      <param name="index">The zero-based index at which a columnDefinition should be inserted.</param>
      <param name="columnDefinition">The <see cref="T:DevExpress.XtraLayout.ColumnDefinition"/> to insert.</param>
      <param name="updateItemIndexes">true, to recalculate the <see cref="P:DevExpress.XtraLayout.OptionsTableLayoutItem.ColumnIndex"/> property of items in the current layout group; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsTableLayoutGroup.Insert(System.Int32,DevExpress.XtraLayout.RowDefinition,System.Boolean)">
      <summary>
        <para>Inserts an element into the collection at the specified index.</para>
      </summary>
      <param name="index">The zero-based index at which a rowDefinition should be inserted.</param>
      <param name="rowDefinition">The <see cref="T:DevExpress.XtraLayout.RowDefinition"/> to insert.</param>
      <param name="updateItemIndexes">true, to recalculate the <see cref="P:DevExpress.XtraLayout.OptionsTableLayoutItem.RowIndex"/> property of items in the current layout group; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsTableLayoutGroup.Remove(DevExpress.XtraLayout.ColumnDefinition,System.Boolean)">
      <summary>
        <para>Removes the specified column definition.</para>
      </summary>
      <param name="columnDefinition">The <see cref="T:DevExpress.XtraLayout.ColumnDefinition"/> to remove.</param>
      <param name="updateItemIndexes">true, to recalculate the <see cref="P:DevExpress.XtraLayout.OptionsTableLayoutItem.ColumnIndex"/> property of items in the current layout group; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsTableLayoutGroup.Remove(DevExpress.XtraLayout.RowDefinition,System.Boolean)">
      <summary>
        <para>Removes the specified row definition.</para>
      </summary>
      <param name="rowDefinition">The <see cref="T:DevExpress.XtraLayout.RowDefinition"/> to remove.</param>
      <param name="updateItemIndexes">true, to recalculate the <see cref="P:DevExpress.XtraLayout.OptionsTableLayoutItem.RowIndex"/> property of items in the current layout group; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsTableLayoutGroup.RemoveColumnAt(System.Int32,System.Boolean)">
      <summary>
        <para>Removes the element at the specified index.</para>
      </summary>
      <param name="index">The zero-based index of the element to remove.</param>
      <param name="updateItemIndexes">true, to recalculate the <see cref="P:DevExpress.XtraLayout.OptionsTableLayoutItem.ColumnIndex"/> property of items in the current layout group; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsTableLayoutGroup.RemoveRowAt(System.Int32,System.Boolean)">
      <summary>
        <para>Removes the element at the specified index.</para>
      </summary>
      <param name="index">The zero-based index of the element to remove.</param>
      <param name="updateItemIndexes">true, to recalculate the <see cref="P:DevExpress.XtraLayout.OptionsTableLayoutItem.RowIndex"/> property of items in the current layout group; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsTableLayoutGroup.RowCount">
      <summary>
        <para>Returns the number of rows within the table layout.</para>
      </summary>
      <value>The number of rows within the table layout.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsTableLayoutGroup.RowDefinitions">
      <summary>
        <para>Provides access to rows of the table layout.</para>
      </summary>
      <value>The table layout&#39;s rows.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsTableLayoutGroup.ShrinkEmptyAutoSizeDefinition">
      <summary>
        <para>Gets or sets whether to automatically minimize empty rows/columns whose SizeType property is set to AutoSize.</para>
      </summary>
      <value>A value that specifies if the LayoutControl minimizes empty rows/columns whose SizeType property is set to AutoSize.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.OptionsTableLayoutItem">
      <summary>
        <para>Provides settings that specify the item&#39;s position in a table layout.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsTableLayoutItem.#ctor(DevExpress.XtraLayout.BaseLayoutItem)">
      <summary>
        <para>Initializes a new instance of an <see cref="T:DevExpress.XtraLayout.OptionsTableLayoutItem"/> class with the specified owner.</para>
      </summary>
      <param name="ownerItem">A layout item that owns the created <see cref="T:DevExpress.XtraLayout.OptionsTableLayoutItem"/> object.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsTableLayoutItem.Assign(DevExpress.Utils.Controls.BaseOptions)">
      <summary>
        <para>Assigns the specified object&#39;s property values to the current object&#39;s corresponding properties.</para>
      </summary>
      <param name="options">The object whose property values to assign to the current object&#39;s corresponding properties.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsTableLayoutItem.ColumnIndex">
      <summary>
        <para>Gets or sets the index of the column in which the current layout item is displayed.</para>
      </summary>
      <value>The zero-based index that specifies the layout item&#39;s column position within the table layout.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsTableLayoutItem.ColumnSpan">
      <summary>
        <para>Gets or sets the number of columns spanned by the current layout item within the table layout.</para>
      </summary>
      <value>A positive integer value that specifies the number of columns spanned by the layout item.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsTableLayoutItem.RowIndex">
      <summary>
        <para>Gets or sets the index of the row in which the current layout item is displayed.</para>
      </summary>
      <value>The zero-based index that specifies the layout item&#39;s row position within the table layout.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsTableLayoutItem.RowSpan">
      <summary>
        <para>Gets or sets the number of rows spanned by the current layout item within the table layout.</para>
      </summary>
      <value>A positive integer value that specifies the number of rows spanned by the layout item.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.OptionsView">
      <summary>
        <para>Contains options that determine the control&#39;s display options</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.OptionsView.#ctor(DevExpress.XtraLayout.ILayoutControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.OptionsView"/> class.</para>
      </summary>
      <param name="owner">An object that implements the ILayoutControl interface, which will receive change notifications when properties of the created object are changed.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.AllowExpandAnimation">
      <summary>
        <para>Gets or sets whether layout groups are collapsed/expanded using animation.</para>
      </summary>
      <value>True, if  layout groups are collapsed/expanded using animation; False if not. Default uses the <see cref="P:DevExpress.XtraEditors.WindowsFormsSettings.AnimationMode"/> global setting.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.AllowGlyphSkinning">
      <summary>
        <para>Gets or sets whether icons for all layout items within the current <see cref="T:DevExpress.XtraLayout.LayoutControl"/> should be painted in the items&#39; foreground colors.</para>
      </summary>
      <value>true, if icons for all layout items within the current <see cref="T:DevExpress.XtraLayout.LayoutControl"/> should be painted in the items&#39; foreground colors; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.AllowHotTrack">
      <summary>
        <para>Gets or sets whether layout item borders are highlighted when the mouse cursor hovers over the layout items.</para>
      </summary>
      <value>true if the hot-tracking of layout items is enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.AllowItemSkining">
      <summary>
        <para>This option is equivalent to the <see cref="P:DevExpress.XtraLayout.OptionsView.AllowItemSkinning"/> option.</para>
      </summary>
      <value>A Boolean value.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.AllowItemSkinning">
      <summary>
        <para>Gets or sets whether the <see cref="P:DevExpress.XtraLayout.OptionsView.DrawItemBorders"/>, <see cref="P:DevExpress.XtraLayout.OptionsView.HighlightDisabledItem"/> and <see cref="P:DevExpress.XtraLayout.OptionsView.HighlightFocusedItem"/> options are in effect.</para>
      </summary>
      <value>True, if the corresponding options are in effect; otherwise; False.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.AllowLockSizeIndicators">
      <summary>
        <para>Gets or sets whether or not lock size indicators should be displayed during the runtime customization.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> enumerator value that specifies whether or not lock size indicators should be displayed during the runtime customization.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.AllowScaleControlsToDisplayResolution">
      <summary>
        <para>This property is now obsolete. Use the <see cref="P:DevExpress.XtraLayout.OptionsView.UseParentAutoScaleFactor"/> property instead.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.AllowTransparentBackColor">
      <summary>
        <para>Allows the parent&#39;s background to be seen through the LayoutControl.</para>
      </summary>
      <value>A Boolean value.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.AlwaysScrollActiveControlIntoView">
      <summary>
        <para>Gets or sets whether focusing a control that is out of view automatically scrolls the LayoutControl to make this control visible.</para>
      </summary>
      <value>true if focusing a control that is out of view automatically scrolls the LayoutControl to make this control visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.AutoSizeInLayoutControl">
      <summary>
        <para>Gets or sets how a LayoutControl&#39;s size is changed when it is positioned within another LayoutControl.</para>
      </summary>
      <value>A value that specifies how the LayoutControl&#39;s size is changed when it is positioned within another LayoutControl.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.AutoSizeModeInLayoutControl">
      <summary>
        <para>Gets or sets how a LayoutControl&#39;s size is changed when it&#39;s positioned within another LayoutControl.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Forms.AutoSizeMode"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.ControlDefaultMaxSizeCalcMode">
      <summary>
        <para>Gets or sets how default maximum size constraints are calculated for controls that implement the <see cref="T:DevExpress.Utils.Controls.IXtraResizableControl"/> interface.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.ControlMaxSizeCalcMode"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.DragFadeAnimationFrameCount">
      <summary>
        <para>Gets or sets the number of frames in the fade animation.</para>
      </summary>
      <value>An integer value that specifies the number of frames in the fade animation.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.DragFadeAnimationSpeed">
      <summary>
        <para>Gets or sets the fade animation length.</para>
      </summary>
      <value>An integer value that specifies the length of the fade animation, in miliseconds.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.DrawAdornerLayer">
      <summary>
        <para>Gets or sets whether to draw an adorner layer that is used to provide customization visual cues.</para>
      </summary>
      <value>A value that specifies whether to draw the adorner layer.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.DrawAdornerLayered">
      <summary>
        <para>Gets or sets whether to draw an adorner layer used to provide customization visual cues.</para>
      </summary>
      <value>A value that specifies whether to draw the adorner layer.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.DrawItemBorders">
      <summary>
        <para>Gets or sets whether layout item borders are visible at runtime.</para>
      </summary>
      <value>true if item borders are visible at runtime; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.EnableIndentsInGroupsWithoutBorders">
      <summary>
        <para>Gets or sets whether a layout group&#39;s indents, specified by its <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Spacing"/> and <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Padding"/> properties, are applied when the group&#39;s borders are hidden.</para>
      </summary>
      <value>true if a layout group&#39;s indents are applied when the group&#39;s borders are hidden; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.EnableTransparentBackColor">
      <summary>
        <para>Allows the parent&#39;s background to be seen through the LayoutControl.</para>
      </summary>
      <value>true if the parent&#39;s background is seen through the LayoutControl; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.ExpandAnimationSpeed">
      <summary>
        <para>Gets or sets the collapse/expand animation speed.</para>
      </summary>
      <value>An integer value that specifies the collapse/expand animation speed.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.FitControlsToDisplayAreaHeight">
      <summary>
        <para>Gets or sets whether controls are fitted to fill the entire height of the <see cref="T:DevExpress.XtraLayout.LayoutControl"/>.</para>
      </summary>
      <value>true, if controls are fitted to fill the entire height of the <see cref="T:DevExpress.XtraLayout.LayoutControl"/>; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.FitControlsToDisplayAreaWidth">
      <summary>
        <para>Gets or sets whether controls are fitted to fill the entire width of the <see cref="T:DevExpress.XtraLayout.LayoutControl"/>.</para>
      </summary>
      <value>true, if controls are fitted to fill the entire width of the <see cref="T:DevExpress.XtraLayout.LayoutControl"/>; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.GroupStyle">
      <summary>
        <para>Gets or sets the default group border and caption paint style.</para>
      </summary>
      <value>A value that specifies the group border and caption paint style.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.HighlightDisabledItem">
      <summary>
        <para>Gets or sets whether disabled layout items are highlighted according to the current skin.</para>
      </summary>
      <value>true if disabled layout items are highlighted; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.HighlightFocusedItem">
      <summary>
        <para>Gets or sets whether focused layout items are highlighted according to the current skin.</para>
      </summary>
      <value>true if focused layout items are highlighted; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.IsReadOnly">
      <summary>
        <para>Gets or sets whether the layout control is read-only.</para>
      </summary>
      <value>A value that specifies if the layout control is read-only.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.ItemBorderColor">
      <summary>
        <para>Gets or sets the color used to paint layout item borders when they are visible.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> value that specifies the color of layout item borders.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.PaddingSpacingMode">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value>A PaddingMode value.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.RightToLeftMirroringApplied">
      <summary>
        <para>Gets or sets whether the RightToLeft setting has been changed.</para>
      </summary>
      <value>true, if the RightToLeft setting has been changed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.ShareLookAndFeelWithChildren">
      <summary>
        <para>Determines whether the LayoutControl manages the look and feel and style settings of DevExpress .NET controls that are displayed within the LayoutControl.</para>
      </summary>
      <value>true if the look and feel and style settings of controls are determined by the <see cref="P:DevExpress.XtraLayout.LayoutControl.LookAndFeel"/> property; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.UseDefaultDragAndDropRendering">
      <summary>
        <para>Gets or sets whether to use the default paint mechanism to indicate drag-and-drop operations.</para>
      </summary>
      <value>true if the default paint mechanism is used to indicate drag-and-drop operations; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.UseParentAutoScaleFactor">
      <summary>
        <para>Gets or sets whether indents between elements in the LayoutControl are automatically adjusted, based on the owning container&#39;s auto-scale settings.</para>
      </summary>
      <value>true, if indents between elements in the LayoutControl are automatically adjusted based on the owning container&#39;s auto-scale settings; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.OptionsView.UseSkinIndents">
      <summary>
        <para>Gets or sets whether padding settings (<see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Padding"/> and <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Spacing"/>) of items and groups are obtained from the current skin. This is a backward compatibility option.</para>
      </summary>
      <value>true if padding settings of items and groups are obtained from the current skin; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.PopupMenuShowingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraLayout.LayoutControl.PopupMenuShowing"/> and <see cref="E:DevExpress.XtraLayout.LayoutControl.LayoutTreeViewPopupMenuShowing"/> events.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.PopupMenuShowingEventArgs.#ctor(DevExpress.Utils.Menu.DXPopupMenu,DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.PopupMenuShowingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="menu">The menu to be displayed. This object is used to initialize the <see cref="P:DevExpress.XtraLayout.PopupMenuShowingEventArgs.Menu"/> property.</param>
      <param name="hitInfo">The object that provides information on the clicked point. This object is used to initialize the <see cref="P:DevExpress.XtraLayout.PopupMenuShowingEventArgs.HitInfo"/> property.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.PopupMenuShowingEventArgs.#ctor(DevExpress.Utils.Menu.DXPopupMenu,DevExpress.XtraLayout.HitInfo.BaseLayoutItemHitInfo,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.PopupMenuShowingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="menu">The menu to be displayed. This object is used to initialize the <see cref="P:DevExpress.XtraLayout.PopupMenuShowingEventArgs.Menu"/> property.</param>
      <param name="hitInfo">The object that provides information on the clicked point. This object is used to initialize the <see cref="P:DevExpress.XtraLayout.PopupMenuShowingEventArgs.HitInfo"/> property.</param>
      <param name="allow">A Boolean value that specifies whether a menu can be displayed. This value is used to initialize the <see cref="P:DevExpress.XtraLayout.PopupMenuShowingEventArgs.Allow"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.PopupMenuShowingEventArgs.Allow">
      <summary>
        <para>Gets or sets whether the menu is allowed to be displayed.</para>
      </summary>
      <value>A Boolean value that specifies whether the menu is allowed to be displayed.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.PopupMenuShowingEventArgs.HitInfo">
      <summary>
        <para>Contains information on the clicked point within the Layout Control.</para>
      </summary>
      <value>An object that provides information on the clicked point.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.PopupMenuShowingEventArgs.Menu">
      <summary>
        <para>Gets or sets the menu that is about to be displayed.</para>
      </summary>
      <value>The menu that is about to be displayed.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.PopupMenuShowingEventArgs.Point">
      <summary>
        <para>Gets the point at which the menu is about to be displayed.</para>
      </summary>
      <value>The point at which to display the menu.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.RowDefinition">
      <summary>
        <para>Defines a row in a table layout.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.RowDefinition.#ctor">
      <summary>
        <para>Initializes a new instance of a <see cref="T:DevExpress.XtraLayout.RowDefinition"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.RowDefinition.#ctor(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Initializes a new instance of a <see cref="T:DevExpress.XtraLayout.RowDefinition"/> class with the specified owner.</para>
      </summary>
      <param name="owner">A layout group that owns the created <see cref="T:DevExpress.XtraLayout.RowDefinition"/> object.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.RowDefinition.#ctor(DevExpress.XtraLayout.LayoutGroup,System.Double,System.Windows.Forms.SizeType)">
      <summary>
        <para>Initializes a new instance of a <see cref="T:DevExpress.XtraLayout.RowDefinition"/> class with the specified settings.</para>
      </summary>
      <param name="owner">A layout group that owns the created <see cref="T:DevExpress.XtraLayout.RowDefinition"/> object.</param>
      <param name="size">The height of the created row. This value is assigned to the <see cref="P:DevExpress.XtraLayout.RowDefinition.Height"/> property.</param>
      <param name="sizingType">The size type of the created row. This value is assigned to the <see cref="P:DevExpress.XtraLayout.RowDefinition.SizeType"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.RowDefinition.Height">
      <summary>
        <para>Gets or sets the row height, in pixels or as a percentage, according to the <see cref="P:DevExpress.XtraLayout.RowDefinition.SizeType"/>.</para>
      </summary>
      <value>A value that specifies the row height.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.RowDefinition.SizeType">
      <summary>
        <para>Gets or sets the row&#39;s size type in a table layout.</para>
      </summary>
      <value>A value that specifies the row&#39;s size type.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.RowDefinitions">
      <summary>
        <para>A row collection within a table layout.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.RowDefinitions.#ctor(DevExpress.XtraLayout.DefinitionBaseCollection,DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Initializes a new instance of a <see cref="T:DevExpress.XtraLayout.RowDefinitions"/> class with the settings of the specified source object and with the specified owner.</para>
      </summary>
      <param name="source">An object whose settings are used to initialize the created object.</param>
      <param name="ownerGroup">A layout group that owns the created object.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.RowDefinitions.#ctor(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Initializes a new instance of a <see cref="T:DevExpress.XtraLayout.RowDefinitions"/> class with the specified owner.</para>
      </summary>
      <param name="owner">A layout group that owns the created <see cref="T:DevExpress.XtraLayout.RowDefinitions"/> object.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.RowDefinitions.Add(DevExpress.XtraLayout.RowDefinition)">
      <summary>
        <para>Adds the specified row to the table layout.</para>
      </summary>
      <param name="rowDefinition">An object that specifies the row to be added.</param>
      <returns>The position into which the new element was inserted, or -1 to indicate that the item was not inserted into the collection.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.RowDefinitions.AddRange(DevExpress.XtraLayout.RowDefinition[])">
      <summary>
        <para>Adds an array of row objects to the table layout.</para>
      </summary>
      <param name="items">An array of <see cref="T:DevExpress.XtraLayout.RowDefinition"/> objects to be added to the table layout.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.RowDefinitions.Insert(System.Int32,DevExpress.XtraLayout.RowDefinition,System.Boolean)">
      <summary>
        <para>Inserts an element into the collection at the specified index.</para>
      </summary>
      <param name="index">The zero-based index at which a rowDefinition should be inserted.</param>
      <param name="rowDefinition">The <see cref="T:DevExpress.XtraLayout.RowDefinition"/> to insert.</param>
      <param name="updateItemIndexes">true, to recalculate the <see cref="P:DevExpress.XtraLayout.OptionsTableLayoutItem.RowIndex"/> property of items in the current layout group; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.RowDefinitions.Item(System.Int32)">
      <summary>
        <para>Provides indexed access to rows within the current row collection.</para>
      </summary>
      <param name="index">A zero-based integer value that specifies the index of the required row.</param>
      <value>An object that represents the row at the specified position within the collection.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.RowDefinitions.RemoveAt(System.Int32,System.Boolean)">
      <summary>
        <para>Removes the element at the specified index.</para>
      </summary>
      <param name="index">The zero-based index of the element to remove.</param>
      <param name="updateItemIndexes">true, to recalculate the <see cref="P:DevExpress.XtraLayout.OptionsTableLayoutItem.RowIndex"/> property of items in the current layout group; otherwise, false.</param>
    </member>
    <member name="T:DevExpress.XtraLayout.SizeConstraintsType">
      <summary>
        <para>Enumerates size constraints modes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.SizeConstraintsType.Custom">
      <summary>
        <para>A layout item&#39;s size can be changed within the range which is specified by the <see cref="P:DevExpress.XtraLayout.LayoutControlItem.MinSize"/> and <see cref="P:DevExpress.XtraLayout.LayoutControlItem.MaxSize"/> properties.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.SizeConstraintsType.Default">
      <summary>
        <para>Default Size Constraints are applied to the layout item and its control. The default size constraints are determined automatically depending upon the type of its control.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.SizeConstraintsType.SupportHorzAlignment">
      <summary>
        <para>This mode is designed to support horizontal alignment of the controls that implement the automatic sizing feature, when they are displayed within a LayoutControl. A layout item&#39;s maximum and minimum sizes are determined automatically. To custom align a control, set its AutoSizeInLayoutControl property to true and specify the control&#39;s alignment via the <see cref="P:DevExpress.XtraLayout.LayoutControlItem.ControlAlignment"/> property. See Aligning Controls Within Layout Items to learn more.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.SplitterItem">
      <summary>
        <para>Allows you to resize and optionally collapse/expand adjacent layout items. Not supported in Table Layout and Flow Layout modes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.SplitterItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.SplitterItem"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.SplitterItem.#ctor(DevExpress.XtraLayout.LayoutControlGroup)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.SplitterItem"/> class with the specified parent.</para>
      </summary>
      <param name="parent">A <see cref="T:DevExpress.XtraLayout.LayoutControlGroup"/> object that owns the new splitter item.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.AllowHotTrack">
      <summary>
        <para>Gets whether the layout item&#39;s borders are highlighted when the mouse cursor hovers over the layout items</para>
      </summary>
      <value>Always true.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.AllowHtmlStringInCaption">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.AppearanceItemCaption">
      <summary>
        <para>Gets the appearance settings used to paint an item&#39;s caption. This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the item&#39;s caption.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.SplitterItem.BeginInit">
      <summary>
        <para>Starts the item&#39;s initialization.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.BestFitWeight">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
      <value></value>
    </member>
    <member name="M:DevExpress.XtraLayout.SplitterItem.EndInit">
      <summary>
        <para>Ends the item&#39;s initialization.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.FixedStyle">
      <summary>
        <para>Gets or sets whether and which layout item/group is fixed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.SplitterItemFixedStyles"/> value that specifies whether and which layout item/group is fixed.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.SplitterItem.GetDefaultText">
      <summary>
        <para>Returns the splitter item&#39;s name.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value that specifies the splitter item&#39;s name.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.SplitterItem.GetSizingRect">
      <summary>
        <para>Returns the bounds of the region used to drag the splitter item. This method supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <returns>A Rectangle object.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.Inverted">
      <summary>
        <para>Gets or sets whether the splitter collapses leftwards (for the vertically oriented splitter) and upwards (for the horizontally oriented splitter), or in the opposite directions (rightwards and downwards, respectively).</para>
      </summary>
      <value>true if the splitter collapses rightwards and upwards; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.IsCollapsed">
      <summary>
        <para>Gets or sets whether the layout content is collapsed using the current splitter.</para>
      </summary>
      <value>true if the layout content is collapsed using the current splitter; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.IsCollapsible">
      <summary>
        <para>Gets or sets whether the layout content can be collapsed using the current splitter.</para>
      </summary>
      <value>True, if the layout content can be collapsed using the current splitter; otherwise, Default or False.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.IsHorizontal">
      <summary>
        <para>Gets whether the splitter item is oriented horizontally.</para>
      </summary>
      <value>true if the splitter item is oriented horizontally; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.IsVertical">
      <summary>
        <para>Gets whether the splitter item is oriented vertically.</para>
      </summary>
      <value>true if the splitter item is oriented vertically; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.MaxSize">
      <summary>
        <para>Gets or set the maximum size of the splitter item. This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the item&#39;s maximum width and height.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.MinSize">
      <summary>
        <para>Gets or sets the minimum size of the splitter item. This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the item&#39;s minimum width and height.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.Padding">
      <summary>
        <para>Gets or sets the amount of space in pixels between the item&#39;s borders and its contents. This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value>A DevExpress.XtraLayout.Utils.Padding object that contains inner indents between the layout item&#39;s borders and its contents.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.Position">
      <summary>
        <para></para>
      </summary>
      <value></value>
    </member>
    <member name="E:DevExpress.XtraLayout.SplitterItem.PositionChanged">
      <summary>
        <para>Fires when the position of the current splitter in the layout is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.ResizeMode">
      <summary>
        <para>Gets or sets the splitter&#39;s resize mode.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.SplitterItemResizeMode"/> value that specifies the splitter&#39;s resize mode.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.ShowSplitGlyph">
      <summary>
        <para>Gets or sets whether to display the split glyph.</para>
      </summary>
      <value>A value that specifies the spit glyph&#39;s visibility. The DefaultBoolean.Default value, if the paint theme (skin) controls the split glyph visibility.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.Size">
      <summary>
        <para>Gets or sets the splitter item&#39;s size.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the splitter item&#39;s width and height.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.SizeConstraintsType">
      <summary>
        <para>Gets or sets the size constraints type. This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.SizeConstraintsType"/> enumeration value which specifies the manner in which the item can be resized. The default is SizeConstraintsType.Default.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.Spacing">
      <summary>
        <para>Gets or sets the outer indents of the item&#39;s borders. This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value>A DevExpress.XtraLayout.Utils.Padding object that contains the outer indents of the layout item&#39;s borders.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.SplitterWidth">
      <summary>
        <para>Gets or sets the splitter&#39;s width.</para>
      </summary>
      <value>An integer value that specifies the splitter&#39;s width.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.Text">
      <summary>
        <para>Gets or sets the item&#39;s text. This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the item&#39;s text.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.TextAlignMode">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.TextAlignMode"/> value.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.TextSize">
      <summary>
        <para>Gets or sets the size of the text region. This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the width and height of the item&#39;s text region.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.TextVisible">
      <summary>
        <para>Gets whether the text region is visible. This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value>Always false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.SplitterItem.TypeName">
      <summary>
        <para>Gets the name of the item&#39;s type.</para>
      </summary>
      <value>The &#39;SplitterItem&#39; value.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.SplitterItemFixedStyles">
      <summary>
        <para>Contains values that specify whether and which layout item/group is fixed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.SplitterItemFixedStyles.LeftTop">
      <summary>
        <para>The left or top item is fixed (depending on the SplitterItem&#39;s orientation).</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.SplitterItemFixedStyles.None">
      <summary>
        <para>No item is fixed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.SplitterItemFixedStyles.RightBottom">
      <summary>
        <para>The right or bottom item is fixed (depending on the SplitterItem&#39;s orientation).</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.SplitterItemResizeMode">
      <summary>
        <para>Enumerates the options that control how layout items are resized when using a splitter.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.SplitterItemResizeMode.AllSiblings">
      <summary>
        <para>Moving the splitter changes the size of adjacent layout items and all their neighbors even if they belong to other layout groups.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.SplitterItemResizeMode.OnlyAdjacentControls">
      <summary>
        <para>Moving the splitter allows adjacent layout items that belong to the same layout group to be resized (the layout item to the left, and all layout items to the right/top are allowed to be resized).</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.TabbedControlGroup">
      <summary>
        <para>The group that features a tabbed UI.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedControlGroup.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.TabbedControlGroup"/> class with default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedControlGroup.#ctor(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.TabbedControlGroup"/> class with the specified owner.</para>
      </summary>
      <param name="owner">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object or descendant which owns the created group.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedControlGroup.AddTabPage">
      <summary>
        <para>Creates a new tab page and appends it to the <see cref="P:DevExpress.XtraLayout.TabbedControlGroup.TabPages"/> collection.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutControlGroup"/> object that represents the new page.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedControlGroup.AddTabPage(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Appends the specified tab page to the <see cref="P:DevExpress.XtraLayout.TabbedControlGroup.TabPages"/> collection.</para>
      </summary>
      <param name="newItem">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object or descendant that represents the new tab page.</param>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutControlGroup"/> object that represents the new page. null (Nothing in Visual Basic) if the collection contains the specified tab page.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedControlGroup.AddTabPage(DevExpress.XtraLayout.LayoutGroup,System.String)">
      <summary>
        <para>Appends the specified tab page with the specified text to the <see cref="P:DevExpress.XtraLayout.TabbedControlGroup.TabPages"/> collection.</para>
      </summary>
      <param name="newItem">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object or descendant that represents the new tab page.</param>
      <param name="text">A <see cref="T:System.String"/> value that specifies the text displayed within the page header. This value is assigned to the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> property.</param>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutControlGroup"/> object that represents the new page. null (Nothing in Visual Basic) if the collection contains the specified tab page.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedControlGroup.AddTabPage(System.String)">
      <summary>
        <para>Creates a new tab page with the specified text and appends it to the <see cref="P:DevExpress.XtraLayout.TabbedControlGroup.TabPages"/> collection.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value that specifies the text displayed within the page header. This value is assigned to the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> property.</param>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutControlGroup"/> object that represents the new page.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedControlGroup.TabPages">
      <summary>
        <para>Gets the collection of layout groups which is owned by the current tabbed group.</para>
      </summary>
      <value>A DevExpress.XtraLayout.Utils.TabbedGroupsCollection object which represents the collection of layout groups owned by the tabbed group.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.TabbedGroup">
      <summary>
        <para>Represents the base class for tabbed layout groups.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedGroup.Accept(DevExpress.XtraLayout.Utils.BaseVisitor)">
      <summary>
        <para>Invokes the Visit method of the specified visitor for each item that belongs to the current layout tabbed group.</para>
      </summary>
      <param name="visitor">A DevExpress.XtraLayout.Utils.BaseVisitor class descendant.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedGroup.AddTabPage">
      <summary>
        <para>Adds a new tabbed page to the current tabbed group.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object or descendant which represents the new page.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedGroup.AddTabPage(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Appends the specified tab page to the <see cref="P:DevExpress.XtraLayout.TabbedGroup.TabPages"/> collection.</para>
      </summary>
      <param name="newItem">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object or descendant that represents the new tab page.</param>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object or descendant that represents the new page. null (Nothing in Visual Basic) if the collection contains the specified tab page.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedGroup.AddTabPage(DevExpress.XtraLayout.LayoutGroup,System.String)">
      <summary>
        <para>Appends the specified tab page with the specified text to the <see cref="P:DevExpress.XtraLayout.TabbedGroup.TabPages"/> collection.</para>
      </summary>
      <param name="newItem">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object or descendant that represents the new tab page.</param>
      <param name="text">A <see cref="T:System.String"/> value that specifies the text displayed within the page header. This value is assigned to the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> property.</param>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object or descendant that represents the new page. null (Nothing in Visual Basic) if the collection contains the specified tab page.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedGroup.AddTabPage(System.String)">
      <summary>
        <para>Creates a new tab page with the specified text and appends it to the <see cref="P:DevExpress.XtraLayout.TabbedGroup.TabPages"/> collection.</para>
      </summary>
      <param name="text">A <see cref="T:System.String"/> value that specifies the text displayed within the page header. This value is assigned to the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.Text"/> property.</param>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object or descendant that represents the new page.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.AllowHtmlDraw">
      <summary>
        <para>Gets or sets whether or not tabbed group captions can be formatted using HTML tags.</para>
      </summary>
      <value>true if tabbed group captions can be formatted using HTML tags; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.CaptionImagePadding">
      <summary>
        <para>Gets or sets default padding for images displayed in the page headers belonging to the current group.</para>
      </summary>
      <value>A Padding object that specifies default padding for images displayed in the page headers belonging to the current group.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedGroup.Clear">
      <summary>
        <para>Removes all tab pages from the <see cref="P:DevExpress.XtraLayout.TabbedControlGroup.TabPages"/> collection.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedGroup.Clear(System.Boolean)">
      <summary>
        <para>Removes all tab pages from the <see cref="P:DevExpress.XtraLayout.TabbedControlGroup.TabPages"/> collection.</para>
      </summary>
      <param name="disposeItemAndControls">true, to release all resources; otherwise false.</param>
    </member>
    <member name="E:DevExpress.XtraLayout.TabbedGroup.CustomDrawHeaderButton">
      <summary>
        <para>Allows you to custom paint header buttons (<see cref="P:DevExpress.XtraLayout.TabbedGroup.CustomHeaderButtons"/>).</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.TabbedGroup.CustomDrawTabHeader">
      <summary>
        <para>Allows you to custom paint tabs.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.TabbedGroup.CustomHeaderButtonClick">
      <summary>
        <para>Occurs when a custom header button is clicked.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.CustomHeaderButtons">
      <summary>
        <para>Provides access to the collection of custom buttons displayed in the tabbed group header.</para>
      </summary>
      <value>The <see cref="T:DevExpress.XtraTab.Buttons.CustomHeaderButtonCollection"/> object that represents a collection of custom buttons displayed in the tabbed group header.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedGroup.EndInit">
      <summary>
        <para>Finishes the component&#39;s initialization.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.HeaderAutoFill">
      <summary>
        <para>Gets or sets whether tab headers are stretched to the width of the <see cref="T:DevExpress.XtraLayout.TabbedGroup"/>&#39;s header region.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether tab headers are stretched to the width of the <see cref="T:DevExpress.XtraLayout.TabbedGroup"/>&#39;s header region.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.HeaderOrientation">
      <summary>
        <para>Gets or sets the orientation of tab headers.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTab.TabOrientation"/> enumeration member which specifies the orientation of tab headers.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedGroup.InsertTabPage(DevExpress.XtraLayout.LayoutGroup,DevExpress.XtraLayout.LayoutGroup,DevExpress.XtraLayout.Utils.InsertLocation)">
      <summary>
        <para>Adds the page to the <see cref="P:DevExpress.XtraLayout.TabbedGroup.TabPages"/> collection before or after the specified page.</para>
      </summary>
      <param name="insertTo">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object or descendant that represents the page before or after which the page will be inserted.</param>
      <param name="insertGroup">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object or descendant which represents the inserted page.</param>
      <param name="insertLocation">An <see cref="T:DevExpress.XtraLayout.Utils.InsertLocation"/> enumeration value which specifies the position that the page will be inserted at.</param>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedGroup.InsertTabPage(System.Int32,DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Adds the specified page to the <see cref="P:DevExpress.XtraLayout.TabbedGroup.TabPages"/> collection at the specified position.</para>
      </summary>
      <param name="insertIndex">An integer value which represents the zero-based index at which the specified page should be inserted. If it&#39;s negative or exceeds the number of elements within the collection an exception is thrown.</param>
      <param name="group">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object or descendant which represents the inserted page.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.MaxSize">
      <summary>
        <para>Gets the maximum size of the tabbed group.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that specifies the tabbed group&#39;s maximum size.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.MinSize">
      <summary>
        <para>Gets the minimum size of the tabbed group.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Size"/> structure that defines the tabbed group&#39;s minimum width and height.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedGroup.MoveTabPage(DevExpress.XtraLayout.Customization.LayoutItemDragController)">
      <summary>
        <para>Moves the page to another position within the <see cref="P:DevExpress.XtraLayout.TabbedGroup.TabPages"/> collection.</para>
      </summary>
      <param name="controller">A <see cref="T:DevExpress.XtraLayout.Customization.LayoutItemDragController"/> object which contains information on the page that would be moved and the position that the page would be moved to.</param>
      <returns>true if the page has been moved; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.MultiLine">
      <summary>
        <para>Gets or sets whether more than one row of tabs can be displayed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether tabs can be arranged in multiple rows.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.OptionsToolTip">
      <summary>
        <para>This property is not supported by the <see cref="T:DevExpress.XtraLayout.TabbedGroup"/> class.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.BaseLayoutItemOptionsToolTip"/> object.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.Owner">
      <summary>
        <para>Gets or set the LayoutControl that owns the current tabbed group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.LayoutControl"/> object or descendant which the tabbed group belongs to.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.PageImagePosition">
      <summary>
        <para>Gets or sets the alignment of images displayed within headers of pages that belong to the current group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraTab.TabPageImagePosition"/> value that specifies the alignment of images displayed within headers of pages that belong to the current group.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedGroup.RemoveTabPage(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Removes the specified tab page from the <see cref="P:DevExpress.XtraLayout.TabbedControlGroup.TabPages"/> collection.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object which represents the tab page to be removed from the collection.</param>
      <returns>true if the tab page has been removed; otherwise, false.</returns>
    </member>
    <member name="E:DevExpress.XtraLayout.TabbedGroup.SelectedPageChanged">
      <summary>
        <para>Occurs when a tab page is activated.</para>
      </summary>
    </member>
    <member name="E:DevExpress.XtraLayout.TabbedGroup.SelectedPageChanging">
      <summary>
        <para>Enables you to prevent changing the selected page.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.SelectedTabPage">
      <summary>
        <para>Gets or sets the currently selected layout group within the tabbed group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object which represents the tabbed page that is currently selected.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.SelectedTabPageIndex">
      <summary>
        <para>Gets or sets the index of the currently selected tab page.</para>
      </summary>
      <value>A zero-based integer which specifies the index of the selected tab page.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.SelectedTabPageName">
      <summary>
        <para>Gets or sets the name of the tab page currently being selected.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the name of the tab page currently being selected.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.ShowTabHeader">
      <summary>
        <para>Gets or sets whether tab headers are shown.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> enumeration value that specifies whether tab headers are shown.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.TabPages">
      <summary>
        <para>Gets the collection of layout groups which is owned by the current tabbed group.</para>
      </summary>
      <value>A DevExpress.XtraLayout.Utils.TabbedGroupsCollection object which represents the collection of layout groups owned by the tabbed group.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.TextSize">
      <summary>
        <para>Gets or sets the size of the text region.</para>
      </summary>
      <value>A System.Drawing.Size structure that defines the width and height of the text region.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.TextVisible">
      <summary>
        <para>Gets or sets whether the current object&#39;s text region is visible. This property is not supported by the <see cref="T:DevExpress.XtraLayout.TabbedGroup"/> class.</para>
      </summary>
      <value>Always false.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.Transition">
      <summary>
        <para>Provides access to state animation parameters.</para>
      </summary>
      <value>A DevExpress.XtraTab.XtraTabControlTransition object that specifies state animation parameters.</value>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.TypeName">
      <summary>
        <para>Gets the name of the item&#39;s type.</para>
      </summary>
      <value>The &#39;TabbedGroup&#39; value.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.TabbedGroup.Ungroup">
      <summary>
        <para>Ungroups the tabbed group.</para>
      </summary>
    </member>
    <member name="P:DevExpress.XtraLayout.TabbedGroup.ViewInfo">
      <summary>
        <para>Gets the object which contains the information used to render the tabbed group.</para>
      </summary>
      <value>A DevExpress.XtraLayout.ViewInfo.TabbedGroupViewInfo object.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.TextAlignMode">
      <summary>
        <para>Enumerates the options that specify how the controls, displayed within the LayoutControl, are aligned and the corresponding text regions are resized.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.TextAlignMode.AlignInGroups">
      <summary>
        <para>Controls are auto-aligned independently within each layout group.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.TextAlignMode.AlignInGroupsRecursive">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.TextAlignMode.AlignInLayoutControl">
      <summary>
        <para>Controls are auto-aligned throughout the LayoutControl.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.TextAlignMode.AutoSize">
      <summary>
        <para>The auto-size feature is enabled. The text regions of layout items are automatically resized to the minimum width that allows the text to be displayed in it entirety.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.TextAlignMode.CustomSize">
      <summary>
        <para>Enables custom size mode, in which the size of the text regions of all layout items must be specified manually via the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.TextSize"/> property.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.TextAlignModeGroup">
      <summary>
        <para>Enumerates the options that specify how the controls, displayed within a specific layout group, are aligned and the corresponding text regions are resized.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.TextAlignModeGroup.AlignLocal">
      <summary>
        <para>Controls displayed within the current group are equally aligned independent of other groups.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.TextAlignModeGroup.AlignWithChildren">
      <summary>
        <para>Controls displayed within the current and nested groups are equally aligned independent of other groups.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.TextAlignModeGroup.AutoSize">
      <summary>
        <para>The auto-size feature is applied to the layout items of the current group. The text regions of the layout items are automatically resized to the minimum width that allows the text to be displayed in its entirety.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.TextAlignModeGroup.CustomSize">
      <summary>
        <para>Enables custom size mode, in which the size of the text regions of the group&#39;s layout items must be specified manually via the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.TextSize"/> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.TextAlignModeGroup.UseParentOptions">
      <summary>
        <para>Alignment settings are determined by the group&#39;s parent. The <see cref="P:DevExpress.XtraLayout.OptionsItemTextGroup.TextToControlDistance"/> and <see cref="P:DevExpress.XtraLayout.OptionsItemTextGroup.AlignControlsWithHiddenText"/> properties of the current group are ignored. In this mode, these settings are determined by the group&#39;s parent.For the root group, its parent is the LayoutControl itself (the LayoutControl&#39;s alignment settings are specified by the <see cref="P:DevExpress.XtraLayout.LayoutControl.OptionsItemText"/> property). For other groups their parents are upper-level groups (a group&#39;s alignment settings are specified by the <see cref="P:DevExpress.XtraLayout.LayoutGroup.OptionsItemText"/> property).</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.TextAlignModeItem">
      <summary>
        <para>Enumerates the options that determine the alignment settings of a layout item&#39;s control.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.TextAlignModeItem.AutoSize">
      <summary>
        <para>The auto-size feature is applied to the current layout item. The item&#39;s text region is automatically resized to the minimum width that allows the text to be displayed in its entirety.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.TextAlignModeItem.CustomSize">
      <summary>
        <para>Enables custom size mode, in which the size of the layout item&#39;s text region must be specified manually via the <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.TextSize"/> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.TextAlignModeItem.UseParentOptions">
      <summary>
        <para>Alignment settings are determined by the item&#39;s parent. The item&#39;s <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.TextToControlDistance"/> property is ignored. In this mode, this setting is determined by the item&#39;s parent.The item&#39;s immediate parent is a group which provides the <see cref="P:DevExpress.XtraLayout.LayoutGroup.OptionsItemText"/> property to control the alignment settings of its items. However, by default the group&#39;s <see cref="P:DevExpress.XtraLayout.OptionsItemTextGroup.TextAlignMode"/> property is set to <see cref="F:DevExpress.XtraLayout.TextAlignModeGroup.UseParentOptions"/>. In this mode, the alignment settings of the group&#39;s items are determined by the group&#39;s parent.For the root group, its parent is the LayoutControl itself (the LayoutControl&#39;s alignment settings are specified by the <see cref="P:DevExpress.XtraLayout.LayoutControl.OptionsItemText"/> property). For other groups their parents are upper-level groups (a group&#39;s alignment settings are specified by the <see cref="P:DevExpress.XtraLayout.LayoutGroup.OptionsItemText"/> property).</para>
      </summary>
    </member>
    <member name="N:DevExpress.XtraLayout.Utils">
      <summary>
        <para>Contains utility classes for the Layout Control.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.Utils.InsertLocation">
      <summary>
        <para>Lists the values that specify the position to which a layout item is inserted.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.Utils.InsertLocation.After">
      <summary>
        <para>To insert an item after the specified one.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.Utils.InsertLocation.Before">
      <summary>
        <para>To insert an item before the specified one.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.Utils.LayoutGroupCancelEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraLayout.LayoutControl.GroupExpandChanging"/> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.Utils.LayoutGroupCancelEventArgs.#ctor(DevExpress.XtraLayout.LayoutGroup,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.Utils.LayoutGroupCancelEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="group">The currently processed group.</param>
      <param name="cancel">The value that specifies whether the current operation needs to be canceled.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.Utils.LayoutGroupCancelEventArgs.Cancel">
      <summary>
        <para>Gets or sets whether to cancel the current operation.</para>
      </summary>
      <value>A Boolean value that specifies whether to cancel the current operation.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.Utils.LayoutGroupEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.XtraLayout.LayoutControl.GroupExpandChanged"/> and <see cref="E:DevExpress.XtraLayout.LayoutControl.TabPageCloseButtonClick"/> events.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.Utils.LayoutGroupEventArgs.#ctor(DevExpress.XtraLayout.LayoutGroup)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.Utils.LayoutGroupEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="group">A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object that represents the group to which the created object will refer. This value is used to initialize the <see cref="P:DevExpress.XtraLayout.Utils.LayoutGroupEventArgs.Group"/> property.</param>
    </member>
    <member name="P:DevExpress.XtraLayout.Utils.LayoutGroupEventArgs.Group">
      <summary>
        <para>Gets the group currently being processed.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object that refers to the group currently being processed.</value>
    </member>
    <member name="T:DevExpress.XtraLayout.Utils.LayoutGroupEventHandler">
      <summary>
        <para>Represents the method for handling events which take a <see cref="T:DevExpress.XtraLayout.Utils.LayoutGroupEventArgs"/> object as a parameter.</para>
      </summary>
      <param name="sender">The event source. This identifies the LayoutControl control which fires the event.</param>
      <param name="e">A <see cref="T:DevExpress.XtraLayout.Utils.LayoutGroupEventArgs"/> object that contains data for the event.</param>
    </member>
    <member name="T:DevExpress.XtraLayout.Utils.LayoutGroupItemCollection">
      <summary>
        <para>Stores layout elements (descendants of the base <see cref="T:DevExpress.XtraLayout.BaseLayoutItem"/> class).</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.Utils.LayoutGroupItemCollection.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.XtraLayout.Utils.LayoutGroupItemCollection"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.XtraLayout.Utils.LayoutGroupItemCollection.AddGroup">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraLayout.LayoutControlGroup"/> and places it inside this <see cref="T:DevExpress.XtraLayout.Utils.LayoutGroupItemCollection"/>.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutControlGroup"/> object that has been created.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.Utils.LayoutGroupItemCollection.AddItem">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraLayout.LayoutControlItem"/> and places it inside this <see cref="T:DevExpress.XtraLayout.Utils.LayoutGroupItemCollection"/>.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraLayout.LayoutControlItem"/> object that has been created.</returns>
    </member>
    <member name="M:DevExpress.XtraLayout.Utils.LayoutGroupItemCollection.AddTabbedGroup">
      <summary>
        <para>Creates a new <see cref="T:DevExpress.XtraLayout.TabbedControlGroup"/> object and places it inside this <see cref="T:DevExpress.XtraLayout.Utils.LayoutGroupItemCollection"/>.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.XtraLayout.TabbedControlGroup"/> object that has been created.</returns>
    </member>
    <member name="P:DevExpress.XtraLayout.Utils.LayoutGroupItemCollection.Owner">
      <summary>
        <para>Returns a <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> that owns this <see cref="T:DevExpress.XtraLayout.Utils.LayoutGroupItemCollection"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.XtraLayout.LayoutGroup"/> object that owns this <see cref="T:DevExpress.XtraLayout.Utils.LayoutGroupItemCollection"/>.</value>
    </member>
    <member name="M:DevExpress.XtraLayout.Utils.LayoutGroupItemCollection.RaiseOnChanged(System.ComponentModel.CollectionChangeEventArgs)">
      <summary>
        <para>Raises the OnChanged event that notifies about changes made to this <see cref="T:DevExpress.XtraLayout.Utils.LayoutGroupItemCollection"/>.</para>
      </summary>
      <param name="e">A  object that represents the OnChanged event arguments.</param>
    </member>
    <member name="T:DevExpress.XtraLayout.Utils.LayoutMode">
      <summary>
        <para>Enumerates values that specify the layout mode for a <see cref="T:DevExpress.XtraLayout.LayoutGroup"/>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.Utils.LayoutMode.Flow">
      <summary>
        <para>Flow layout mode. Layout items are automatically arranged in rows according to their order in the <see cref="P:DevExpress.XtraLayout.LayoutGroup.Items"/> collection, and are automatically wrapped at the group&#39;s right edge</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.Utils.LayoutMode.Regular">
      <summary>
        <para>Regular layout mode. Layout items can stretch within the parent layout group and can have any size</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.Utils.LayoutMode.Table">
      <summary>
        <para>Table layout mode. Layout items are arranged in a two-dimensional table according to their row and column indexes and span values.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.Utils.LayoutType">
      <summary>
        <para>Contains the values that specify how an item is positioned next to a base item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.Utils.LayoutType.Horizontal">
      <summary>
        <para>Specifies that an item is positioned horizontally next to a base item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.Utils.LayoutType.Vertical">
      <summary>
        <para>Specifies that an item is positioned vertically next to a base item.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.Utils.LayoutVisibility">
      <summary>
        <para>Enumerates values that specify whether a layout item is visible in customization mode and regular mode (when customization is not performed).</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.Utils.LayoutVisibility.Always">
      <summary>
        <para>A layout item is visible in regular mode and in customization mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.Utils.LayoutVisibility.Never">
      <summary>
        <para>A layout item is not visible in regular mode or customization mode.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.Utils.LayoutVisibility.OnlyInCustomization">
      <summary>
        <para>A layout item is only visible in customization mode, and hidden in regular mode (when customization is not performed).</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.Utils.LayoutVisibility.OnlyInRuntime">
      <summary>
        <para>A layout item is only visible in regular mode, and hidden in customization mode.</para>
      </summary>
    </member>
    <member name="T:DevExpress.XtraLayout.Utils.MoveType">
      <summary>
        <para>Lists the values that specify how a layout item is inserted to another position.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.Utils.MoveType.Inside">
      <summary>
        <para>An item is inserted inside the specified item.</para>
      </summary>
    </member>
    <member name="F:DevExpress.XtraLayout.Utils.MoveType.Outside">
      <summary>
        <para>An item is inserted outside the specified item. In this case, the inserted item is reduced to its minimum size which is specified by its <see cref="P:DevExpress.XtraLayout.BaseLayoutItem.MinSize"/> property.</para>
      </summary>
    </member>
  </members>
</doc>