﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Microsoft.Practices.EnterpriseLibrary.Data.Sql;
using System.Data.Common;
using System.Data;
using Utility.DbInfo;
using System.Data.OracleClient;
using System.ServiceModel;
using System.ServiceModel.Channels;
using Utility.YangFan;
namespace OracleDAL
{
    public class MR_FILE_INDEX
    {
        #region  Method
		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
            string strsql = "select max(VISIT_ID)+1 from MEDREC.MR_FILE_INDEX";
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "GetMaxId", strsql, endpoint.Address);
                }
                object obj = db.ExecuteScalar(CommandType.Text, strsql);
                if (obj != null && obj != DBNull.Value)
                {
                    return int.Parse(obj.ToString());
                }
                return 1;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strsql.ToString());
            }
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string PATIENT_ID,int VISIT_ID,int FILE_NO)
		{
			Database db = DatabaseFactory.CreateDatabase();
			StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from MEDREC.MR_FILE_INDEX where PATIENT_ID=:PATIENT_ID and VISIT_ID=:VISIT_ID and FILE_NO=:FILE_NO ");
            try
            {
                DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
                db.AddInParameter(dbCommand, "PATIENT_ID", DbType.String, PATIENT_ID);
                db.AddInParameter(dbCommand, "VISIT_ID", DbType.Int32, VISIT_ID);
                db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, FILE_NO);
                int cmdresult;
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "Exists", strSql.ToString(), endpoint.Address);
                }
                object obj = db.ExecuteScalar(dbCommand);
                if ((Object.Equals(obj, null)) || (Object.Equals(obj, System.DBNull.Value)))
                {
                    cmdresult = 0;
                }
                else
                {
                    cmdresult = int.Parse(obj.ToString());
                }
                if (cmdresult == 0)
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
            catch (Exception ex) 
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
		}
		/// <summary>
		/// 增加一条数据
		/// </summary>
		public void Add(Model.MR_FILE_INDEX model)
		{
			StringBuilder strSql=new StringBuilder();
            strSql.Append("insert into MEDREC.MR_FILE_INDEX(");
            strSql.Append("PATIENT_ID,VISIT_ID,FILE_NO,FILE_NAME,TOPIC,CREATOR_NAME,CREATOR_ID,CREATE_DATE_TIME,LAST_MODIFY_DATE_TIME,FILE_MODIFY_DATE_TIME,FLAG,FILE_ATTR,PRINT_FLAG,MR_CODE,PARENT_NAME,PARENT_ID,FILE_FLAG,MODIFY_DATE_TIME,EPR_INDEX,SUPER_DATE_TIME,SUPER_ID,SUPER_NAME,SIGN_PAPER_DATETIME,FILE_ORDER,PARENT_SIGN_PAPER_DATETIME,SUPER_SIGN_PAPER_DATETIME,FILE_CREATOR_GRADE,HAND_SIGN_PAPER_DATETIME,EVALUATE_PASS_FALG,PATIENT_SIGN_FLAG,PATIENT_SIGN_DATETIME,APP_NO,STUDY_DOCTOR_ID,STUDY_DOCTOR_NAME,CREATE_DISPLAY_DATE_TIME,CREATE_DEPT_CODE)");
            strSql.Append(" values (");
            //strSql.AppendFormat("'{0}',{1},{2},'{3}','{4}','{5}','{6}',:CREATE_DATE_TIME,:LAST_MODIFY_DATE_TIME,:FILE_MODIFY_DATE_TIME,:FLAG,:FILE_ATTR,:PRINT_FLAG,:MR_CODE,:PARENT_NAME,:PARENT_ID,:FILE_FLAG,:MODIFY_DATE_TIME,:EPR_INDEX,:SUPER_DATE_TIME,:SUPER_ID,:SUPER_NAME,:SIGN_PAPER_DATETIME,:FILE_ORDER,:PARENT_SIGN_PAPER_DATETIME,:SUPER_SIGN_PAPER_DATETIME,:FILE_CREATOR_GRADE,:HAND_SIGN_PAPER_DATETIME,:EVALUATE_PASS_FALG,:PATIENT_SIGN_FLAG,:PATIENT_SIGN_DATETIME,:APP_NO,:STUDY_DOCTOR_ID,:STUDY_DOCTOR_NAME)",
            //    model.PATIENT_ID, model.VISIT_ID, model.FILE_NO, model.FILE_NAME, model.TOPIC, model.CREATOR_NAME, model.CREATOR_ID,);
            strSql.Append(":PATIENT_ID,:VISIT_ID,:FILE_NO,:FILE_NAME,:TOPIC,:CREATOR_NAME,:CREATOR_ID,:CREATE_DATE_TIME,:LAST_MODIFY_DATE_TIME,:FILE_MODIFY_DATE_TIME,:FLAG,:FILE_ATTR,:PRINT_FLAG,:MR_CODE,:PARENT_NAME,:PARENT_ID,:FILE_FLAG,:MODIFY_DATE_TIME,:EPR_INDEX,:SUPER_DATE_TIME,:SUPER_ID,:SUPER_NAME,:SIGN_PAPER_DATETIME,:FILE_ORDER,:PARENT_SIGN_PAPER_DATETIME,:SUPER_SIGN_PAPER_DATETIME,:FILE_CREATOR_GRADE,:HAND_SIGN_PAPER_DATETIME,:EVALUATE_PASS_FALG,:PATIENT_SIGN_FLAG,:PATIENT_SIGN_DATETIME,:APP_NO,:STUDY_DOCTOR_ID,:STUDY_DOCTOR_NAME,:CREATE_DISPLAY_DATE_TIME,:CREATE_DEPT_CODE)");
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
                db.AddInParameter(dbCommand, "PATIENT_ID", DbType.String, model.PATIENT_ID);
                db.AddInParameter(dbCommand, "VISIT_ID", DbType.Int32, model.VISIT_ID);
                db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, model.FILE_NO);
                db.AddInParameter(dbCommand, "FILE_NAME", DbType.String, model.FILE_NAME);
                db.AddInParameter(dbCommand, "TOPIC", DbType.String, model.TOPIC);
                db.AddInParameter(dbCommand, "CREATOR_NAME", DbType.String, model.CREATOR_NAME);
                db.AddInParameter(dbCommand, "CREATOR_ID", DbType.String, model.CREATOR_ID);
                db.AddInParameter(dbCommand, "CREATE_DATE_TIME", DbType.DateTime, model.CREATE_DATE_TIME);
                db.AddInParameter(dbCommand, "LAST_MODIFY_DATE_TIME", DbType.DateTime, model.LAST_MODIFY_DATE_TIME);
                db.AddInParameter(dbCommand, "FILE_MODIFY_DATE_TIME", DbType.DateTime, model.FILE_MODIFY_DATE_TIME);
                db.AddInParameter(dbCommand, "FLAG", DbType.String, model.FLAG);
                db.AddInParameter(dbCommand, "FILE_ATTR", DbType.String, model.FILE_ATTR);
                db.AddInParameter(dbCommand, "PRINT_FLAG", DbType.String, model.PRINT_FLAG);
                db.AddInParameter(dbCommand, "MR_CODE", DbType.String, model.MR_CODE);
                db.AddInParameter(dbCommand, "PARENT_NAME", DbType.String, model.PARENT_NAME);
                db.AddInParameter(dbCommand, "PARENT_ID", DbType.String, model.PARENT_ID);
                db.AddInParameter(dbCommand, "FILE_FLAG", DbType.String, model.FILE_FLAG);
                db.AddInParameter(dbCommand, "MODIFY_DATE_TIME", DbType.DateTime, model.MODIFY_DATE_TIME);
                db.AddInParameter(dbCommand, "EPR_INDEX", DbType.String, model.EPR_INDEX);
                db.AddInParameter(dbCommand, "SUPER_DATE_TIME", DbType.DateTime, model.SUPER_DATE_TIME);
                db.AddInParameter(dbCommand, "SUPER_ID", DbType.String, model.SUPER_ID);
                db.AddInParameter(dbCommand, "SUPER_NAME", DbType.String, model.SUPER_NAME);
                db.AddInParameter(dbCommand, "SIGN_PAPER_DATETIME", DbType.DateTime, model.SIGN_PAPER_DATETIME);
                db.AddInParameter(dbCommand, "FILE_ORDER", DbType.Int32, model.FILE_ORDER);
                db.AddInParameter(dbCommand, "PARENT_SIGN_PAPER_DATETIME", DbType.DateTime, model.PARENT_SIGN_PAPER_DATETIME);
                db.AddInParameter(dbCommand, "SUPER_SIGN_PAPER_DATETIME", DbType.DateTime, model.SUPER_SIGN_PAPER_DATETIME);
                db.AddInParameter(dbCommand, "FILE_CREATOR_GRADE", DbType.AnsiString, model.FILE_CREATOR_GRADE);
                db.AddInParameter(dbCommand, "HAND_SIGN_PAPER_DATETIME", DbType.DateTime, model.HAND_SIGN_PAPER_DATETIME);
                db.AddInParameter(dbCommand, "EVALUATE_PASS_FALG", DbType.String, model.EVALUATE_PASS_FALG);
                db.AddInParameter(dbCommand, "PATIENT_SIGN_FLAG", DbType.Int32, model.PATIENT_SIGN_FLAG);
                db.AddInParameter(dbCommand, "PATIENT_SIGN_DATETIME", DbType.DateTime, model.PATIENT_SIGN_DATETIME);
                db.AddInParameter(dbCommand, "APP_NO", DbType.String, model.APP_NO);
                db.AddInParameter(dbCommand, "STUDY_DOCTOR_ID", DbType.String, model.STUDY_DOCTOR_ID);
                db.AddInParameter(dbCommand, "STUDY_DOCTOR_NAME", DbType.String, model.STUDY_DOCTOR_NAME);
                db.AddInParameter(dbCommand, "CREATE_DISPLAY_DATE_TIME", DbType.DateTime, model.CREATE_DISPLAY_DATE_TIME);
                db.AddInParameter(dbCommand, "CREATE_DEPT_CODE", DbType.String, model.CREATE_DEPT_CODE);
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "Add", strSql.ToString(), endpoint.Address);
                }
                db.ExecuteNonQuery(dbCommand);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
        public bool Update(Model.MR_FILE_INDEX model)
		{
            DateTime tm;
			StringBuilder strSql=new StringBuilder();
            strSql.Append("update MEDREC.MR_FILE_INDEX set ");
            strSql.Append("FILE_NAME='" + model.FILE_NAME + "',");
            strSql.Append("TOPIC='" + model.TOPIC + "',");
            strSql.Append("CREATOR_NAME='" + model.CREATOR_NAME + "',");
            strSql.Append("CREATOR_ID='" + model.CREATOR_ID + "',");
            if (model.CREATE_DATE_TIME.ToString() != String.Empty)
            {
                tm = DateTime.Parse(model.CREATE_DATE_TIME.ToString());
                strSql.Append("CREATE_DATE_TIME=to_date('" + tm.ToString("yyyy-MM-dd HH:mm:ss") + "','yyyy-mm-dd hh24:mi:ss'),");
            }
            else
            {
                strSql.Append("CREATE_DATE_TIME=to_date('" + model.CREATE_DATE_TIME.ToString() + "','yyyy-mm-dd hh24:mi:ss'),");
            }
            if (model.LAST_MODIFY_DATE_TIME.ToString() != String.Empty)
            {
                tm = DateTime.Parse(model.LAST_MODIFY_DATE_TIME.ToString());
                strSql.Append("LAST_MODIFY_DATE_TIME=to_date('" + tm.ToString("yyyy-MM-dd HH:mm:ss") + "','yyyy-mm-dd hh24:mi:ss'),");
            }
            else
            {
                strSql.Append("LAST_MODIFY_DATE_TIME=to_date('" + model.LAST_MODIFY_DATE_TIME.ToString() + "','yyyy-mm-dd hh24:mi:ss'),");
            }
            if (model.FILE_MODIFY_DATE_TIME.ToString() != String.Empty)
            {
            tm = DateTime.Parse(model.FILE_MODIFY_DATE_TIME.ToString());
            strSql.Append("FILE_MODIFY_DATE_TIME=to_date('" + tm.ToString("yyyy-MM-dd HH:mm:ss") + "','yyyy-mm-dd hh24:mi:ss'),");
            }
             else
             {
                 strSql.Append("FILE_MODIFY_DATE_TIME=to_date('" + model.FILE_MODIFY_DATE_TIME.ToString() + "','yyyy-mm-dd hh24:mi:ss'),");
             }
            if (model.MODIFY_DATE_TIME.ToString() != String.Empty)
            {
            tm = DateTime.Parse(model.MODIFY_DATE_TIME.ToString());
            strSql.Append("MODIFY_DATE_TIME=to_date('" + tm.ToString("yyyy-MM-dd HH:mm:ss") + "','yyyy-mm-dd hh24:mi:ss'),");
            }
            else
            {
                strSql.Append("MODIFY_DATE_TIME=to_date('" + model.MODIFY_DATE_TIME.ToString() + "','yyyy-mm-dd hh24:mi:ss'),");
            } 
            //strSql.Append("FLAG='" + model.FILE_FLAG + "',");
            strSql.Append("FILE_ATTR='" + model.FILE_ATTR + "',");
			//strSql.Append("PRINT_FLAG=:PRINT_FLAG,");
            strSql.Append("MR_CODE='" + model.MR_CODE + "',");
            strSql.Append("PARENT_NAME='" + model.PARENT_NAME + "',");
            strSql.Append("PARENT_ID='" + model.PARENT_ID + "',");
            strSql.Append("FILE_FLAG='" + model.FILE_FLAG + "',");			
			//strSql.Append("EPR_INDEX=:EPR_INDEX,");
            strSql.Append("CREATE_DEPT_CODE='" + model.CREATE_DEPT_CODE + "',");	
            if (model.SUPER_DATE_TIME.ToString() != String.Empty)
            {
            tm = DateTime.Parse(model.SUPER_DATE_TIME.ToString());
            strSql.Append("SUPER_DATE_TIME=to_date('" + tm.ToString("yyyy-MM-dd HH:mm:ss") + "','yyyy-mm-dd hh24:mi:ss'),");
            }
            else
            {
                strSql.Append("SUPER_DATE_TIME=to_date('" + model.SUPER_DATE_TIME.ToString() + "','yyyy-mm-dd hh24:mi:ss'),");
            } 
            strSql.Append("SUPER_ID='" + model.SUPER_ID + "',");
            strSql.Append("SUPER_NAME='" + model.SUPER_NAME + "',");
            if (model.SIGN_PAPER_DATETIME.ToString() != String.Empty)
            {
                tm = DateTime.Parse(model.SIGN_PAPER_DATETIME.ToString());
                strSql.Append("SIGN_PAPER_DATETIME=to_date('" + tm.ToString("yyyy-MM-dd HH:mm:ss") + "','yyyy-mm-dd hh24:mi:ss'),");
            }
             else
             {
                 strSql.Append("SIGN_PAPER_DATETIME=to_date('" + model.SIGN_PAPER_DATETIME.ToString() + "','yyyy-mm-dd hh24:mi:ss'),");
             } 
            strSql.Append("FILE_ORDER='" + model.FILE_ORDER + "',");
            if (model.PARENT_SIGN_PAPER_DATETIME.ToString() != String.Empty)
            {
                tm = DateTime.Parse(model.PARENT_SIGN_PAPER_DATETIME.ToString());
                strSql.Append("PARENT_SIGN_PAPER_DATETIME=to_date('" + tm.ToString("yyyy-MM-dd HH:mm:ss") + "','yyyy-mm-dd hh24:mi:ss'),");
            }
             else
             {
                 strSql.Append("PARENT_SIGN_PAPER_DATETIME=to_date('" + model.PARENT_SIGN_PAPER_DATETIME.ToString() + "','yyyy-mm-dd hh24:mi:ss'),");
             }
            if (model.SUPER_SIGN_PAPER_DATETIME.ToString() != String.Empty)
            {
                tm = DateTime.Parse(model.SUPER_SIGN_PAPER_DATETIME.ToString());
                strSql.Append("SUPER_SIGN_PAPER_DATETIME=to_date('" + tm.ToString("yyyy-MM-dd HH:mm:ss") + "','yyyy-mm-dd hh24:mi:ss'),");
            }
             else
             {
                 strSql.Append("SUPER_SIGN_PAPER_DATETIME=to_date('" + model.SUPER_SIGN_PAPER_DATETIME.ToString() + "','yyyy-mm-dd hh24:mi:ss'),");
             } 
            strSql.Append("FILE_CREATOR_GRADE='" + model.FILE_CREATOR_GRADE + "',");
            if (model.HAND_SIGN_PAPER_DATETIME.ToString() != String.Empty)
            {
                tm = DateTime.Parse(model.HAND_SIGN_PAPER_DATETIME.ToString());
                strSql.Append("HAND_SIGN_PAPER_DATETIME=to_date('" + tm.ToString("yyyy-MM-dd HH:mm:ss") + "','yyyy-mm-dd hh24:mi:ss'),");
            }
             else
             {
                 strSql.Append("HAND_SIGN_PAPER_DATETIME=to_date('" + model.HAND_SIGN_PAPER_DATETIME.ToString() + "','yyyy-mm-dd hh24:mi:ss'),");
             } 
            strSql.Append("EVALUATE_PASS_FALG='" + model.EVALUATE_PASS_FALG + "',");
            strSql.Append("PATIENT_SIGN_FLAG='" + model.PATIENT_SIGN_FLAG + "',");
            if (model.PATIENT_SIGN_DATETIME.ToString() != String.Empty)
            {
                tm = DateTime.Parse(model.PATIENT_SIGN_DATETIME.ToString());
                strSql.Append("PATIENT_SIGN_DATETIME=to_date('" + tm.ToString("yyyy-MM-dd HH:mm:ss") + "','yyyy-mm-dd hh24:mi:ss'),");
            }
            else
            {
                strSql.Append("PATIENT_SIGN_DATETIME=to_date('" + model.PATIENT_SIGN_DATETIME.ToString() + "','yyyy-mm-dd hh24:mi:ss'),");
            } 
            strSql.Append("APP_NO='" + model.APP_NO + "',");
            strSql.Append("STUDY_DOCTOR_ID='" + model.STUDY_DOCTOR_ID + "',");
            strSql.Append("STUDY_DOCTOR_NAME='" + model.STUDY_DOCTOR_NAME + "'");
            strSql.Append(" where PATIENT_ID='" + model.PATIENT_ID + "' and VISIT_ID=" + model.VISIT_ID + " and FILE_NO=" + model.FILE_NO);
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
                //db.AddInParameter(dbCommand, "PATIENT_ID", DbType.String, model.PATIENT_ID);
                //db.AddInParameter(dbCommand, "VISIT_ID", DbType.Int32, model.VISIT_ID);
                //db.AddInParameter(dbCommand, "FILE_NO", DbType.Int32, model.FILE_NO);
                //db.AddInParameter(dbCommand, "FILE_NAME", DbType.String, model.FILE_NAME);
                //db.AddInParameter(dbCommand, "TOPIC", DbType.String, model.TOPIC);
                //db.AddInParameter(dbCommand, "CREATOR_NAME", DbType.String, model.CREATOR_NAME);
                //db.AddInParameter(dbCommand, "CREATOR_ID", DbType.String, model.CREATOR_ID);
                //db.AddInParameter(dbCommand, "CREATE_DATE_TIME", DbType.DateTime, model.CREATE_DATE_TIME);
                //db.AddInParameter(dbCommand, "LAST_MODIFY_DATE_TIME", DbType.DateTime, model.LAST_MODIFY_DATE_TIME);
                //db.AddInParameter(dbCommand, "FILE_MODIFY_DATE_TIME", DbType.DateTime, model.FILE_MODIFY_DATE_TIME);
                //db.AddInParameter(dbCommand, "FLAG", DbType.String, model.FLAG);
                //db.AddInParameter(dbCommand, "FILE_ATTR", DbType.String, model.FILE_ATTR);
                //db.AddInParameter(dbCommand, "PRINT_FLAG", DbType.String, model.PRINT_FLAG);
                //db.AddInParameter(dbCommand, "MR_CODE", DbType.String, model.MR_CODE);
                //db.AddInParameter(dbCommand, "PARENT_NAME", DbType.String, model.PARENT_NAME);
                //db.AddInParameter(dbCommand, "PARENT_ID", DbType.String, model.PARENT_ID);
                //db.AddInParameter(dbCommand, "FILE_FLAG", DbType.String, model.FILE_FLAG);
                //db.AddInParameter(dbCommand, "MODIFY_DATE_TIME", DbType.DateTime, model.MODIFY_DATE_TIME);
                //db.AddInParameter(dbCommand, "EPR_INDEX", DbType.String, model.EPR_INDEX);
                //db.AddInParameter(dbCommand, "SUPER_DATE_TIME", DbType.DateTime, model.SUPER_DATE_TIME);
                //db.AddInParameter(dbCommand, "SUPER_ID", DbType.String, model.SUPER_ID);
                //db.AddInParameter(dbCommand, "SUPER_NAME", DbType.String, model.SUPER_NAME);
                //db.AddInParameter(dbCommand, "SIGN_PAPER_DATETIME", DbType.DateTime, model.SIGN_PAPER_DATETIME);
                //db.AddInParameter(dbCommand, "FILE_ORDER", DbType.Int32, model.FILE_ORDER);
                //db.AddInParameter(dbCommand, "PARENT_SIGN_PAPER_DATETIME", DbType.DateTime, model.PARENT_SIGN_PAPER_DATETIME);
                //db.AddInParameter(dbCommand, "SUPER_SIGN_PAPER_DATETIME", DbType.DateTime, model.SUPER_SIGN_PAPER_DATETIME);
                //db.AddInParameter(dbCommand, "FILE_CREATOR_GRADE", DbType.AnsiString, model.FILE_CREATOR_GRADE);
                //db.AddInParameter(dbCommand, "HAND_SIGN_PAPER_DATETIME", DbType.DateTime, model.HAND_SIGN_PAPER_DATETIME);
                //db.AddInParameter(dbCommand, "EVALUATE_PASS_FALG", DbType.String, model.EVALUATE_PASS_FALG);
                //db.AddInParameter(dbCommand, "PATIENT_SIGN_FLAG", DbType.Int32, model.PATIENT_SIGN_FLAG);
                //db.AddInParameter(dbCommand, "PATIENT_SIGN_DATETIME", DbType.DateTime, model.PATIENT_SIGN_DATETIME);
                //db.AddInParameter(dbCommand, "APP_NO", DbType.String, model.APP_NO);
                //db.AddInParameter(dbCommand, "STUDY_DOCTOR_ID", DbType.String, model.STUDY_DOCTOR_ID);
                //db.AddInParameter(dbCommand, "STUDY_DOCTOR_NAME", DbType.String, model.STUDY_DOCTOR_NAME);
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "Update", strSql.ToString(), endpoint.Address);
                }
                return db.ExecuteNonQuery(dbCommand) > 0;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

		}
        /// <summary>
        /// 更新最后时间
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int UpdateLastTime(Model.MR_FILE_INDEX model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update MEDREC.MR_FILE_INDEX set ");
            strSql.AppendFormat("LAST_MODIFY_DATE_TIME=to_date('{0}','YYYY-MM-DD HH24:MI:SS')", model.LAST_MODIFY_DATE_TIME.ToString("yyyy-MM-dd HH:mm:ss"));
            strSql.AppendFormat(" where PATIENT_ID='{0}' and VISIT_ID={1} and FILE_NO={2} ", model.PATIENT_ID, model.VISIT_ID, model.FILE_NO);
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "UpdateLastTime", strSql.ToString(), endpoint.Address);
                }
                return db.ExecuteNonQuery(CommandType.Text, strSql.ToString());
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
		/// <summary>
		/// 删除一条数据
		/// </summary>
		public void Delete(string PATIENT_ID,int VISIT_ID,int FILE_NO)
		{
			StringBuilder strSql=new StringBuilder();
            strSql.Append("delete from MEDREC.MR_FILE_INDEX ");
            strSql.AppendFormat(" where PATIENT_ID='{0}' and VISIT_ID={1} and FILE_NO={2} ", PATIENT_ID, VISIT_ID, FILE_NO);
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "Delete", strSql.ToString(), endpoint.Address);
                }
                int re = db.ExecuteNonQuery(CommandType.Text, strSql.ToString());
                if (re > 0)
                {
                    strSql.Remove(0, strSql.Length);
                    strSql.Append("delete from cpr.mr_file_xml ");
                    strSql.AppendFormat(" where PATIENT_ID='{0}' and VISIT_ID={1} and FILE_NO={2} ", PATIENT_ID, VISIT_ID, FILE_NO);
                    if (TrackCOMM.Track_Indicator)
                    {
                        OperationContext context = OperationContext.Current;
                        MessageProperties properties = context.IncomingMessageProperties;
                        RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                        TrackEntity entity = new TrackEntity();
                        entity.Add("MR_FILE_INDEX", "Delete", strSql.ToString(), endpoint.Address);
                    }
                    re = db.ExecuteNonQuery(CommandType.Text, strSql.ToString());
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
		}
		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MR_FILE_INDEX GetModel(string PATIENT_ID,int VISIT_ID,int FILE_NO)
		{
		    string strSql="select * from MEDREC.MR_FILE_INDEX where PATIENT_ID='"+PATIENT_ID+"' and VISIT_ID="+VISIT_ID+" and FILE_NO="+ FILE_NO;
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "GetModel", strSql.ToString(), endpoint.Address);
                }
                Model.MR_FILE_INDEX model = null;
                DataTable dataReader = DbHelperOra.GetDataReader(strSql);// db.ExecuteReader(dbCommand))
                if (dataReader.Rows.Count > 0)
                {
                    model = ReaderBind(dataReader.Rows[0]);
                }
                return model;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
            strSql.Append("select PATIENT_ID,VISIT_ID,FILE_NO,FILE_NAME,TOPIC,CREATOR_NAME,CREATOR_ID,CREATE_DATE_TIME,LAST_MODIFY_DATE_TIME,FILE_MODIFY_DATE_TIME,FLAG,FILE_ATTR,PRINT_FLAG,MR_CODE,PARENT_NAME,PARENT_ID,FILE_FLAG,MODIFY_DATE_TIME,EPR_INDEX,SUPER_DATE_TIME,SUPER_ID,SUPER_NAME,SIGN_PAPER_DATETIME,FILE_ORDER,PARENT_SIGN_PAPER_DATETIME,SUPER_SIGN_PAPER_DATETIME,FILE_CREATOR_GRADE,HAND_SIGN_PAPER_DATETIME,EVALUATE_PASS_FALG,PATIENT_SIGN_FLAG,PATIENT_SIGN_DATETIME,APP_NO,CREATE_DISPLAY_DATE_TIME,CREATE_DEPT_CODE ");
            strSql.Append(" FROM MEDREC.MR_FILE_INDEX ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "GetList", strSql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, strSql.ToString());
            }
            catch (Exception ex) 
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
		}
        /// <summary>
        /// 获取电子病历状态
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public DataSet GetDataStatusList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select PATIENT_ID,VISIT_ID,TOPIC,CREATOR_NAME,FILE_FLAG FROM MEDREC.MR_FILE_INDEX");
            if(!string.IsNullOrEmpty(strWhere))
            {
                strSql.Append( " where "+strWhere);
            }
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "GetDataStatusList", strSql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, strSql.ToString());
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			Database db = DatabaseFactory.CreateDatabase();
			DbCommand dbCommand = db.GetStoredProcCommand("UP_GetRecordByPage");
			db.AddInParameter(dbCommand, "tblName", DbType.AnsiString, MR_FILE_INDEX);
			db.AddInParameter(dbCommand, "fldName", DbType.AnsiString, "FILE_NO");
			db.AddInParameter(dbCommand, "PageSize", DbType.Int32, PageSize);
			db.AddInParameter(dbCommand, "PageIndex", DbType.Int32, PageIndex);
			db.AddInParameter(dbCommand, "IsReCount", DbType.Boolean, 0);
			db.AddInParameter(dbCommand, "OrderType", DbType.Boolean, 0);
			db.AddInParameter(dbCommand, "strWhere", DbType.AnsiString, strWhere);
			return db.ExecuteDataSet(dbCommand);
		}*/

		/// <summary>
		/// 获得数据列表（比DataSet效率高，推荐使用）
		/// </summary>
		public List<Model.MR_FILE_INDEX> GetListArray(string strWhere)
		{
			string strSql="select * FROM MEDREC.MR_FILE_INDEX ";
			if(strWhere.Trim()!="")
			{
				strSql+=" where "+strWhere;
			}
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "GetListArray", strSql.ToString(), endpoint.Address);
                }
                List<Model.MR_FILE_INDEX> list = new List<Model.MR_FILE_INDEX>();
                DataTable dataReader = DbHelperOra.GetDataReader(strSql);// db.ExecuteReader(CommandType.Text, strSql.ToString()))
                foreach (DataRow dr in dataReader.Rows)
                {
                    list.Add(ReaderBind(dr));
                }
                return list;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
		}

		/// <summary>
		/// 对象实体绑定数据
		/// </summary>
		public Model.MR_FILE_INDEX ReaderBind(DataRow dataReader)
		{
			Model.MR_FILE_INDEX model=new Model.MR_FILE_INDEX();
			object ojb; 
			model.PATIENT_ID=dataReader["PATIENT_ID"].ToString();
			ojb = dataReader["VISIT_ID"];
			if(ojb != null && ojb != DBNull.Value)
			{
				model.VISIT_ID=int.Parse(ojb.ToString());
			}
			ojb = dataReader["FILE_NO"];
			if(ojb != null && ojb != DBNull.Value)
			{
				model.FILE_NO=int.Parse(ojb.ToString());
			}
			model.FILE_NAME=dataReader["FILE_NAME"].ToString();
			model.TOPIC=dataReader["TOPIC"].ToString();
			model.CREATOR_NAME=dataReader["CREATOR_NAME"].ToString();
			model.CREATOR_ID=dataReader["CREATOR_ID"].ToString();
			ojb = dataReader["CREATE_DATE_TIME"];
			if(ojb != null && ojb != DBNull.Value)
			{
				model.CREATE_DATE_TIME=(DateTime)ojb;
			}
			ojb = dataReader["LAST_MODIFY_DATE_TIME"];
			if(ojb != null && ojb != DBNull.Value)
			{
				model.LAST_MODIFY_DATE_TIME=(DateTime)ojb;
			}
			ojb = dataReader["FILE_MODIFY_DATE_TIME"];
			if(ojb != null && ojb != DBNull.Value)
			{
				model.FILE_MODIFY_DATE_TIME=(DateTime)ojb;
			}
			model.FLAG=dataReader["FLAG"].ToString();
			model.FILE_ATTR=dataReader["FILE_ATTR"].ToString();
			model.PRINT_FLAG=dataReader["PRINT_FLAG"].ToString();
			model.MR_CODE=dataReader["MR_CODE"].ToString();
			model.PARENT_NAME=dataReader["PARENT_NAME"].ToString();
			model.PARENT_ID=dataReader["PARENT_ID"].ToString();
			model.FILE_FLAG=dataReader["FILE_FLAG"].ToString();
			ojb = dataReader["MODIFY_DATE_TIME"];
			if(ojb != null && ojb != DBNull.Value)
			{
				model.MODIFY_DATE_TIME=(DateTime)ojb;
			}
			model.EPR_INDEX=dataReader["EPR_INDEX"].ToString();
			ojb = dataReader["SUPER_DATE_TIME"];
			if(ojb != null && ojb != DBNull.Value)
			{
				model.SUPER_DATE_TIME=(DateTime)ojb;
			}
			model.SUPER_ID=dataReader["SUPER_ID"].ToString();
			model.SUPER_NAME=dataReader["SUPER_NAME"].ToString();
			ojb = dataReader["SIGN_PAPER_DATETIME"];
			if(ojb != null && ojb != DBNull.Value)
			{
				model.SIGN_PAPER_DATETIME=(DateTime)ojb;
			}
			ojb = dataReader["FILE_ORDER"];
			if(ojb != null && ojb != DBNull.Value)
			{
				model.FILE_ORDER=int.Parse(ojb.ToString());
			}
			ojb = dataReader["PARENT_SIGN_PAPER_DATETIME"];
			if(ojb != null && ojb != DBNull.Value)
			{
				model.PARENT_SIGN_PAPER_DATETIME=(DateTime)ojb;
			}
			ojb = dataReader["SUPER_SIGN_PAPER_DATETIME"];
			if(ojb != null && ojb != DBNull.Value)
			{
				model.SUPER_SIGN_PAPER_DATETIME=(DateTime)ojb;
			}
			model.FILE_CREATOR_GRADE=dataReader["FILE_CREATOR_GRADE"].ToString();
			ojb = dataReader["HAND_SIGN_PAPER_DATETIME"];
			if(ojb != null && ojb != DBNull.Value)
			{
				model.HAND_SIGN_PAPER_DATETIME=(DateTime)ojb;
			}
			model.EVALUATE_PASS_FALG=dataReader["EVALUATE_PASS_FALG"].ToString();
			ojb = dataReader["PATIENT_SIGN_FLAG"];
			if(ojb != null && ojb != DBNull.Value)
			{
				model.PATIENT_SIGN_FLAG=int.Parse(ojb.ToString());
			}
			ojb = dataReader["PATIENT_SIGN_DATETIME"];
			if(ojb != null && ojb != DBNull.Value)
			{
				model.PATIENT_SIGN_DATETIME=(DateTime)ojb;
			}
            model.APP_NO = dataReader["APP_NO"].ToString();
            ojb = dataReader["SIGNATURE_NO"];
            if (ojb != null && ojb != DBNull.Value)
            {
                model.SIGNATURE_NO = int.Parse(ojb.ToString());
            }
            ojb = dataReader["PATIENT_SIGNATURE_NO"];
            if (ojb != null && ojb != DBNull.Value)
            {
                model.PATIENT_SIGNATURE_NO = int.Parse(ojb.ToString());
            }
            model.STUDY_DOCTOR_ID = dataReader["STUDY_DOCTOR_ID"].ToString();
            model.STUDY_DOCTOR_NAME = dataReader["STUDY_DOCTOR_NAME"].ToString();
            model.TAKE_FLAG = dataReader["TAKE_FLAG"].ToString();
            model.FIRST_SIGN_DEPT_CODE = dataReader["FIRST_SIGN_DEPT_CODE"].ToString();
            model.DIAG_WRITE_INDICATOR = dataReader["DIAG_WRITE_INDICATOR"].ToString();
            ojb = dataReader["SING_FLAG"];
            if (ojb != null && ojb != DBNull.Value)
            {
                model.SING_FLAG = int.Parse(ojb.ToString());
            }
            ojb = dataReader["CREATE_DISPLAY_DATE_TIME"];
			if(ojb != null && ojb != DBNull.Value)
			{
                model.CREATE_DISPLAY_DATE_TIME = (DateTime)ojb;
			}
            model.CREATE_DEPT_CODE = dataReader["CREATE_DEPT_CODE"].ToString();
			return model;
		}
		#endregion  Method

        #region
        public DataSet GetPatsInpExamDataSet(string patientID, int visitID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * from CPR.PATS_INP_EXAM ");
            strSql.Append(" where PATIENT_ID='" + patientID + "' and VISIT_ID=" + visitID + "  order by RECORD_NO ");
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "", strSql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, strSql.ToString());
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        public DataSet GetPatsOtherExamDataSet(string patientID, int visitID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * from CPR.PATS_OTHER_EXAM ");
            strSql.Append(" where PATIENT_ID='" + patientID + "' and VISIT_ID=" + visitID + " order by RECORD_NO");
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "", strSql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, strSql.ToString());
            }
            catch (Exception ex) 
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion
        /// <summary>
        /// 获得诊断别名数据列表
        /// </summary>
        public DataSet GetDataSetList(string strSql)
        {
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                return db.ExecuteDataSet(CommandType.Text, strSql);
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 依据模板编码获取模板数据集编码
        /// </summary>
        /// <param name="mrcode"></param>
        /// <returns></returns>
        public string GetDsByCode(string mrcode)
        {
            string strSql = " select data_model_code from MR_ITEM_INDEX where mr_code='" + mrcode + "'";
            Database db = DatabaseFactory.CreateDatabase();
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "", strSql.ToString(), endpoint.Address);
                }
                object obj = db.ExecuteScalar(CommandType.Text, strSql);
                if (obj != null)
                    return obj.ToString();
                else
                    return null;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 依据模板编码获取模板数据集编辑方式
        /// </summary>
        /// <param name="mrcode"></param>
        /// <returns></returns>
        public int GetDsEditType(string mrcode)
        {
            string strSql = " select nvl(a.ds_edit_type,0) from data_set a, mr_item_index b where a.ds_code = b.data_model_code and b.data_model_class='DS' and b.mr_code ='" + mrcode + "'";
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "", strSql.ToString(), endpoint.Address);
                }
                object obj = db.ExecuteScalar(CommandType.Text, strSql);
                if (obj != null)
                    return Convert.ToInt32(obj);
                else
                    return 0;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 数据集code取模板code
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public string GetMrcodeByDcode(string code)
        {
            string strSql = " select MR_CODE from MR_ITEM_INDEX where data_model_code=:mrcode";
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                DbCommand cmd = db.GetSqlStringCommand(strSql.ToString());
                db.AddInParameter(cmd, "mrcode", DbType.String, code);
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "", strSql.ToString(), endpoint.Address);
                }
                object obj = db.ExecuteScalar(cmd);
                return obj.ToString();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 医疗文书书写数量统计
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public DataSet GetMrFileStatistics(string startTime,string endTime)        
        {
            if (startTime != String.Empty)
            {
                DateTime tm = DateTime.Parse(startTime);
                startTime = tm.ToString("yyyy-MM-dd");
            }
            if (endTime != String.Empty)
            {
                DateTime tm = DateTime.Parse(endTime);
                endTime = tm.ToString("yyyy-MM-dd");
            }
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" select  USER_NAME ,  sum(CREATE_count) CREATE_count,  sum(SIGN_count) SIGN_count,  sum(B01_count)B01_count,  sum(A_count) A_count   from (");
            strSql.Append(" SELECT COMM.USERS.USER_NAME,count(creator_name) CREATE_count,0  SIGN_count, 0 B01_count,0 A_count ");
            strSql.Append(" FROM MEDREC.MR_FILE_INDEX, COMM.USERS   ");
            strSql.Append(string.Format(" WHERE ( COMM.USERS.DB_USER = MEDREC.MR_FILE_INDEX.CREATOR_ID ) AND ( ( MEDREC.MR_FILE_INDEX.CREATE_DATE_TIME >= to_date('{0}','yyyy-mm-dd') ) AND ",startTime));
            strSql.Append(string.Format(" ( MEDREC.MR_FILE_INDEX.CREATE_DATE_TIME <= to_date('{0}','yyyy-mm-dd') ) ) ",endTime));
            strSql.Append(" GROUP BY COMM.USERS.USER_NAME ");
            strSql.Append(" union all ");
            strSql.Append("   SELECT COMM.USERS.USER_NAME,   ");
            strSql.Append("  0 CREATE_count,   ");
            strSql.Append("   count(creator_name)  SIGN_count, ");
            strSql.Append("   0 B01_count,");
            strSql.Append("   0 A_count");
            strSql.Append("  FROM MEDREC.MR_FILE_INDEX,   ");
            strSql.Append("     COMM.USERS  ");
            strSql.Append(" WHERE ( COMM.USERS.DB_USER = MEDREC.MR_FILE_INDEX.SUPER_ID OR COMM.USERS.DB_USER = MEDREC.MR_FILE_INDEX.PATIENT_ID ) AND   ");
            strSql.Append(string.Format("      ( ( MEDREC.MR_FILE_INDEX.CREATE_DATE_TIME >= to_date('{0}','yyyy-mm-dd') ) AND  ", startTime));
            strSql.Append(string.Format("      ( MEDREC.MR_FILE_INDEX.CREATE_DATE_TIME <= to_date('{0}','yyyy-mm-dd') ) ) ", endTime));
            strSql.Append("GROUP BY COMM.USERS.USER_NAME  ");
            strSql.Append(" union all ");
            strSql.Append("SELECT COMM.USERS.USER_NAME, ");
            strSql.Append("      0 CREATE_count,");
            strSql.Append("    0 SIGN_count,   ");
            strSql.Append("   count(creator_name) B01_count, ");
            strSql.Append("    0 A_count       ");
            strSql.Append(" FROM MEDREC.MR_FILE_INDEX,   ");
            strSql.Append("     COMM.USERS  ,");
            strSql.Append("      CPR.MR_ITEM_INDEX");
            strSql.Append(" WHERE ( COMM.USERS.DB_USER = MEDREC.MR_FILE_INDEX.CREATOR_ID ) AND  ");
            strSql.Append(string.Format("      ( ( MEDREC.MR_FILE_INDEX.CREATE_DATE_TIME >= to_date('{0}','yyyy-mm-dd') ) AND  ", startTime));
            strSql.Append(string.Format("      ( MEDREC.MR_FILE_INDEX.CREATE_DATE_TIME <= to_date('{0}','yyyy-mm-dd') ) )   AND ", endTime));
            strSql.Append("     MEDREC.MR_FILE_INDEX.MR_CODE =  CPR.MR_ITEM_INDEX.MR_CODE AND");
            strSql.Append("    CPR.MR_ITEM_INDEX.DATA_MODEL_CODE   ='EMR10.00.01'      ");
            strSql.Append("GROUP BY COMM.USERS.USER_NAME    ");
            strSql.Append("union all ");
            strSql.Append(" SELECT COMM.USERS.USER_NAME, ");
            strSql.Append("   0 CREATE_count,");
            strSql.Append("   0 SIGN_count,  ");
            strSql.Append("  0 B01_count,     ");
            strSql.Append("  count(creator_name) A_count");
            strSql.Append(" FROM MEDREC.MR_FILE_INDEX,  ");
            strSql.Append("    COMM.USERS ,");
            strSql.Append("    CPR.MR_ITEM_INDEX");
            strSql.Append(" WHERE ( COMM.USERS.DB_USER = MEDREC.MR_FILE_INDEX.CREATOR_ID ) AND  ");
            strSql.Append(string.Format("      ( ( MEDREC.MR_FILE_INDEX.CREATE_DATE_TIME >= to_date('{0}','yyyy-mm-dd') ) AND  ", startTime));
            strSql.Append(string.Format("      ( MEDREC.MR_FILE_INDEX.CREATE_DATE_TIME <= to_date('{0}','yyyy-mm-dd') ) )   AND ", endTime));
            strSql.Append("       MEDREC.MR_FILE_INDEX.MR_CODE =  CPR.MR_ITEM_INDEX.MR_CODE AND");
            strSql.Append("       CPR.MR_ITEM_INDEX.DATA_MODEL_CODE  LIKE  'EMR09.00%'");
            strSql.Append("GROUP BY COMM.USERS.USER_NAME    )");
            strSql.Append("GROUP BY USER_NAME");
            try
            {
                DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "", strSql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(dbCommand);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        public DataSet GetMrSchemaFromXml(string patientId, string visitId, string schemaId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendFormat("select a.patient_id,a.visit_id,a.file_no,b.file_name,b.topic,'{0}' schemaId ", schemaId); //,a.file_xml.extract('//CDR/section[@name=\"{0}\"]').getStringval() groupXml
            strSql.AppendFormat(" from mr_file_xml a inner join medrec.mr_file_index b on a.patient_id=b.patient_id and a.visit_id=b.visit_id and a.file_no=b.file_no ");
            strSql.AppendFormat(" where a.patient_id='{1}' and a.visit_id={2} and a.file_xml.extract('//CDR/section[@name=\"{0}\"]').getclobVal() is not null ", schemaId, patientId, visitId);
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "", strSql.ToString(), endpoint.Address);
                }
                DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
                return db.ExecuteDataSet(dbCommand);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        public DataSet DataSearchByDataGroup(string dataGroupCode, string searchContent)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendFormat("      select a.file_xml.extract('//CDR/section[@name=\"{0}\"]').getclobval() xmlContent,a.vasit_date,a.visit_no,a.file_no,b.topic,b.patient_id from cpr.outp_mr_file_xml a ", dataGroupCode);
            strSql.AppendFormat(@"          inner join medrec.outp_mr_file_index b 
                                             on a.vasit_date=b.visit_date and a.visit_no=b.visit_no and a.file_no=b.file_no");
            strSql.AppendFormat("     where a.file_xml.extract('//CDR/section[@name=\"{0}\"]').getclobval() like '%{1}%' ", dataGroupCode, searchContent);
            try
            {
                DbInfo dbInfo = new DbInfo();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "", strSql.ToString(), endpoint.Address);
                }
                return dbInfo.GetData(strSql.ToString(), "outp_mr_file_xml");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        public DataSet DataSearchByDataGroupForInp(string dataGroupCode, string searchContent, string deptCode, string beginDate, string endDate)
        {
            string deptWhere = "";
            if (deptCode == "*")
            {
                deptWhere = "";
            }
            else
            {
                deptWhere = " and c.dept_discharge_from='" + deptCode + "'";
            }
            StringBuilder strSql = new StringBuilder();
            strSql.AppendFormat("  select a.file_xml.extract('//CDR/section[@name=\"{0}\"]').getclobval() xmlContent,a.patient_id,a.visit_id,a.file_no,b.topic ", dataGroupCode);
            strSql.AppendFormat(@" from cpr.mr_file_xml a  inner join medrec.mr_file_index b 
                                             on a.patient_id=b.patient_id and a.visit_id=b.visit_id and a.file_no=b.file_no
    inner join pat_visit c on a.patient_id=c.patient_id and a.visit_id=c.visit_id {0}
    and c.discharge_date_time>=to_date('{1}','yyyy-mm-dd') and c.discharge_date_time<=to_date('{2}','yyyy-mm-dd') ", deptWhere, beginDate, endDate);
            strSql.AppendFormat("     where a.file_xml.extract('//CDR/section[@name=\"{0}\"]').getclobval() like '%{1}%' ", dataGroupCode, searchContent);
            try
            {
                DbInfo dbInfo = new DbInfo();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_FILE_INDEX", "", strSql.ToString(), endpoint.Address);
                }
                return dbInfo.GetData(strSql.ToString(), "outp_mr_file_xml");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
    }
}
