﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using System.ComponentModel;
namespace Model
{

    /// <summary>
    ///费用模板明细记录
    /// </summary>

    [DataContract]
    public class BILL_PATTERN_DETAIL : NotificationObject
    {

        /// <summary>
        /// 模板名称
        /// </summary>		
        private string _pattern_name;
        [DataMember]
        public string PATTERN_NAME
        {
            get { return _pattern_name; }
            set
            {
                if (_pattern_name != value)
                {
                    _pattern_name = value;
                    this.RaisePropertyChanged("PATTERN_NAME");
                }
            }
        }
        /// <summary>
        /// 项目序号
        /// </summary>		
        private decimal _item_no;
        [DataMember]
        public decimal ITEM_NO
        {
            get { return _item_no; }
            set
            {
                if (_item_no != value)
                {
                    _item_no = value;
                    this.RaisePropertyChanged("ITEM_NO");
                }
            }
        }
        /// <summary>
        /// 项目分类
        /// </summary>		
        private string _item_class;
        [DataMember]
        public string ITEM_CLASS
        {
            get { return _item_class; }
            set
            {
                if (_item_class != value)
                {
                    _item_class = value;
                    this.RaisePropertyChanged("ITEM_CLASS");
                }
            }
        }
        /// <summary>
        /// 项目代码
        /// </summary>		
        private string _item_code;
        [DataMember]
        public string ITEM_CODE
        {
            get { return _item_code; }
            set
            {
                if (_item_code != value)
                {
                    _item_code = value;
                    this.RaisePropertyChanged("ITEM_CODE");
                }
            }
        }
        /// <summary>
        /// 项目规格
        /// </summary>		
        private string _item_spec;
        [DataMember]
        public string ITEM_SPEC
        {
            get { return _item_spec; }
            set
            {
                if (_item_spec != value)
                {
                    _item_spec = value;
                    this.RaisePropertyChanged("ITEM_SPEC");
                }
            }
        }
        /// <summary>
        /// 单位
        /// </summary>		
        private string _units;
        [DataMember]
        public string UNITS
        {
            get { return _units; }
            set
            {
                if (_units != value)
                {
                    _units = value;
                    this.RaisePropertyChanged("UNITS");
                }
            }
        }
        /// <summary>
        /// 执行科室
        /// </summary>		
        private string _performed_by;
        [DataMember]
        public string PERFORMED_BY
        {
            get { return _performed_by; }
            set
            {
                if (_performed_by != value)
                {
                    _performed_by = value;
                    this.RaisePropertyChanged("PERFORMED_BY");
                }
            }
        }
        /// <summary>
        /// 数量
        /// </summary>		
        private decimal _amount;
        [DataMember]
        public decimal AMOUNT
        {
            get { return _amount; }
            set
            {
                if (_amount != value)
                {
                    _amount = value;
                    this.RaisePropertyChanged("AMOUNT");
                }
            }
        }
        /// <summary>
        /// 部门代码
        /// </summary>		
        private string _dept_code;
        [DataMember]
        public string DEPT_CODE
        {
            get { return _dept_code; }
            set
            {
                if (_dept_code != value)
                {
                    _dept_code = value;
                    this.RaisePropertyChanged("DEPT_CODE");
                }
            }
        }

    }
}