﻿/*********************************************
* 文 件 名：Cs02StringHelper
* 类 名 称：Cs02StringHelper
* 功能说明：字符串处理类
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：付军
* 创建时间：2016-10-28
* 版 本 号：1.0.0.1
* 修改时间：尹志伟
* 修 改 人：2018-06-12
* CLR 版本：4.0.30319.42000
/*********************************************/

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Globalization;
using System.Collections;

namespace PlatCommon.Base02
{
    /// <summary>
    /// 字符串处理类
    /// </summary>
    public class Cs02StringHelper
    {
        /// <summary>
		/// 本对象不能实例化
		/// </summary>
        private Cs02StringHelper()
		{
        }

        #region 字符串处理
        /// <summary>
        /// 获取字符串中存在的分隔符, 支持',', ':', ';'三种, 包括全角
        /// </summary>
        /// <param name="strString">传入的字符串</param>
        /// <returns>如果存在分隔符 , 返回分隔符; 如果不存在, 返回null</returns>
        public static string GetSplitChar(string strString)
        {
            string[] splitChars = { ":", ";", ",", "：", "；", "，" };      // 支持指定分隔符

            // 查找分隔符
            string splitChar    = null;
            
            for (int i = 0; i < splitChars.Length; i++)
            {
                if (strString.Contains(splitChars[i]) == true)
                {
                    splitChar = splitChars[i];
                    break;
                }
            }
            
            return splitChar;
        }

        /// <summary>
        /// 获取值对
        /// </summary>
        /// <remarks>示例: Data Source = orcl, user id = system, pwd = xiou</remarks>
        /// <param name="strString">源字符串</param>
        /// <param name="strParaName">参数名称, 不区分大小写</param>
        /// <returns>若不存在指定名称的参数则返回空, 否则返回相应的值(大写)</returns>
        public static string GetParameterValue(string strString, string strParaName)
        {
            // 查找分隔符
            string splitChar    = GetSplitChar(strString);
            if (string.IsNullOrEmpty(splitChar) == true) return null;

            // 获取值
            strParaName = strParaName.ToUpper().Trim();
            
            string[] parts = strString.Split(splitChar.ToCharArray());
            for (int i = 0; i < parts.Length; i++)
            {
                string part = parts[i].Trim();
                if (part.StartsWith(strParaName, StringComparison.OrdinalIgnoreCase) == true)
                {
                    string paraValue = part.Substring(strParaName.Length).Trim();
                    if (paraValue.StartsWith("=") == true)
                    {
                        return paraValue.Substring(1).Trim();
                    }
                }
            }
            
            return null;
        }

        /// <summary>
        /// 设置参数的值
        /// </summary>
        /// <remarks>示例: Data Source = orcl, user id = system, pwd = xiou</remarks>
        /// <param name="strString">源字符串</param>
        /// <param name="strParaName">参数名称, 不区分大小写</param>
        /// <param name="strParaValueNew">新参数值</param>
        /// <returns>替换参数值后返回的字符串</returns>
        public static string SetParameterValue(string strString, string strParaName, string strParaValueNew)
        {
            // 查找分隔符
            string splitChar    = GetSplitChar(strString);
            if (string.IsNullOrEmpty(splitChar) == true) return strString;

            // 获取值
            strParaName = strParaName.ToUpper().Trim();
            
            string[] parts = strString.Split(splitChar.ToCharArray());
            strString = string.Empty;
            for (int i = 0; i < parts.Length; i++)
            {
                string part = parts[i].Trim();
                if (part.StartsWith(strParaName, StringComparison.OrdinalIgnoreCase) == true)
                {
                    string paraValue = part.Substring(strParaName.Length).Trim();
                    if (paraValue.StartsWith("=") == true)
                    {
                        if (strString.Length > 0) strString += splitChar;
                        strString += strParaName + "=" + strParaValueNew;                        
                        continue;
                    }
                }
                
                if (strString.Length > 0) strString += splitChar;
                strString += parts[i];    
            }
            
            return strString;
        }
        
        /// <summary>
        /// 获取数字的罗马表示
        /// </summary>
        /// <param name="num">数字</param>
        /// <returns>罗马字母表示的数字</returns>
        public static string GetRomaNum(int num)
        {
            if (num <= 0) { return string.Empty; }

            string[] arrNum = { "Ⅹ", "Ⅰ", "Ⅱ", "Ⅲ", "Ⅳ", "Ⅴ", "Ⅵ", "Ⅶ", "Ⅷ", "Ⅸ" };
            string romaNum = string.Empty;

            string numStr = num.ToString();

            for (int i = 0; i < numStr.Length; i++)
            {
                romaNum += arrNum[int.Parse(numStr.Substring(i, 1))];
            }

            return romaNum;
        }
        
        /// <summary>
        /// 字符串前后加单引号, 字符串内部单引号改成双引号
        /// </summary>
        /// <param name="strText">源文本</param>
        /// <returns>转换后的文本</returns>
        public static string SqlConvert(string strText)
        {
            strText = strText.Replace("'", "''");			// 单引号变双引号
            strText = strText.TrimEnd();                  // 删除字符串末尾空白字符
            strText = "'" + strText + "'";	            // 字符串首尾加单引号

            return strText + " ";
        }

        /// <summary>
        /// 字符串内部单引号改成双引号
        /// </summary>
        /// <param name="strText">源文本</param>
        /// <returns>转换后的文本</returns>
        public static string Convert(string strText)
        {
            strText = strText.Replace("'", "''");			// 单引号变双引号
            strText = strText.TrimEnd();                  // 删除字符串末尾空白字符

            return strText;
        }

        /// <summary>
        /// 获取SQL的日期表达
        /// </summary>
        /// <param name="dtmDate">时间</param>
        /// <returns></returns>
        public static string GetSqlDate(DateTime dtmDate)
        {
            return SqlConvert(dtmDate.ToString("yyyy-MM-dd HH:mm:ss"));
        }

        /// <summary>
        /// 获取Oracle的日期表达
        /// </summary>
        /// <param name="dtmDate">时间</param>
        /// <returns></returns>
        public static string GetOraDate(DateTime dtmDate)
        {
            return "TO_DATE(" + SqlConvert(dtmDate.ToString("yyyy-MM-dd HH:mm:ss")) + ",'yyyy-MM-dd HH24:mi:ss')";
        }

        /// <summary>
        /// 获取Oracle的日期表达（短日期格式）
        /// </summary>
        /// <param name="dtmDate">时间</param>
        /// <returns></returns>
        public static string GetOraDateShort(DateTime dtmDate)
        {
            return "TO_DATE('" + dtmDate.ToString("yyyy-MM-dd HH:mm:ss") + "','yyyy-MM-dd HH24:mi:ss')";
        }
        /// <summary>
        /// 转换为字符
        /// </summary>
        /// <param name="obj">object</param>
        /// <returns>string</returns>
        public static string CStr(object obj)
        {
            if (obj == null) return String.Empty;
            return obj.ToString();
        }
        /// <summary>
        /// Oracle的日期表达
        /// </summary>
        /// <param name="dtmDate">时间</param>
        /// <returns></returns>
        public static string GetOraDate(string dtmDate)
        {
            return "TO_DATE('" + dtmDate + "','yyyy-MM-dd HH24:mi:ss')";
        }

        /// <summary>
        /// 重复生成字符串
        /// </summary>
        /// <param name="strSource">被重复的字符串</param>
        /// <param name="iTimes">重复次数</param>
        /// <returns>重复生成的字符串</returns>
        public static string Repeat(string strSource, int iTimes)
        {
            string strResult = string.Empty;

            for (int i = 0; i < iTimes; i++)
                strResult += strSource;

            return strResult;
        }

        /// <summary>
        /// 从字符串右边取字符
        /// </summary>
        /// <param name="strSource">要取值的字符串</param>
        /// <param name="iLength">要取出的字符的个数，如果length大于字符串的长度，则取整个字符串</param>
        /// <returns>取得的字符串</returns>
        public static string Right(string strSource, int iLength)
        {
            if (iLength >= strSource.Length)
                return strSource;
            else
            {
                int pos = strSource.Length - iLength;
                return strSource.Substring(pos);
            }
        }

        /// <summary>
        /// 拼接字符串
        /// </summary>
        /// <param name="strSource">基串</param>
        /// <param name="strNewStr">要被追加在基串后面的字符串</param>
        /// <param name="strSplit">加在两个字符串之间的间隔符。如果基串为空的话，则忽略flag</param>
        /// <returns></returns>
        public static string AppendString(string strSource, string strNewStr, string strSplit)
        {
            if (string.IsNullOrEmpty( strSource))
                return strNewStr;
            else
                return strSource += strSplit + strNewStr;
        }

        /// <summary>
        /// 首字母大写
        /// </summary>
        /// <param name="strSource">要转换的字符穿表达式</param>
        /// <returns></returns>
        public static string UpperFirstChar(string strSource)
        {
            string strResult = "";

            if (strSource.Length > 0)
                strResult = strSource.Substring(0, 1).ToUpper() + strSource.Substring(1);

            return strResult;
        }

        /// <summary>
        /// 首字母小写
        /// </summary>
        /// <param name="strSource">要转换的字符穿表达式</param>
        /// <returns></returns>
        public static string LowerFirstChar(string strSource)
        {
            string strResult = "";

            if (strSource.Length > 0)
                strResult = strSource.Substring(0, 1).ToLower() + strSource.Substring(1);

            return strResult;
        }
        #endregion

        #region 转换检测
        /// <summary>
        /// 检测字符串是否可转换成int型
        /// </summary>
        /// <param name="strSource">传入的字符串</param>
        /// <returns>true:可以转换，false:不可以转换</returns>
        public static bool IsInt(string strSource)
        {
            int i;
            return int.TryParse(strSource, out i);
        }

        /// <summary>
        /// 检测字符串是否可转换成float型
        /// </summary>
        /// <param name="strSource">传入的字符串</param>
        /// <returns>true:可以转换，false:不可以转换</returns>
        public static bool IsFloat(string strSource)
        {
            float i;
            return float.TryParse(strSource, out i);
        }

        /// <summary>
        /// 检测字符串是否可转换成double型
        /// </summary>
        /// <param name="strSource">传入的字符串</param>
        /// <returns>true:可以转换，false:不可以转换</returns>
        public static bool IsDouble(string strSource)
        {
            double i;
            return double.TryParse(strSource, out i);
        }

        /// <summary>
        /// 检测字符串是否可转换成long型
        /// </summary>
        /// <param name="strSource">传入的字符串</param>
        /// <returns>true:可以转换，false:不可以转换</returns>
        public static bool IsLong(string strSource)
        {
            long i;
            return long.TryParse(strSource, out i);
        }

        /// <summary>
        /// 检测字符串是否可转换成IPv4地址
        /// </summary>
        /// <param name="strSource">传入的字符串</param>
        /// <returns>true:可以转换，false:不可以转换</returns>
        public static bool IsIPAdress(string strSource)
        {
            System.Net.IPAddress ipAddress;
            return System.Net.IPAddress.TryParse(strSource, out ipAddress);
        }

        /// <summary>
        /// 检测字符串是否可转换成int型并是否在某个值范围内
        /// </summary>
        /// <param name="strSource">要转换的字符串</param>
        /// <returns>true:可以转换，false:不可以转换</returns>
        public static bool IsNumeric(string strSource)
        {
            return IsNumeric(strSource, int.MinValue, int.MaxValue, false);
        }

        /// <summary>
        /// 检测字符串是否可转换成int型并是否在某个值范围内
        /// </summary>
        /// <param name="strSource">要转换的字符串</param>
        /// <param name="minValue">允许的最小值，不限的话可用int.MinValue表示</param>
        /// <param name="maxValue">允许的最大值，不限的话可用int.maxValue表示</param>
        /// <param name="AllowEmpty">是否允许空字符串。空字符串不收最大和最小值的约束</param>
        /// <returns>true:可以转换，false:不可以转换</returns>
        public static bool IsNumeric(string strSource, double minValue, double maxValue, bool AllowEmpty)
        {
            bool bResult = false;

            strSource = strSource.Trim();
            if (IsDouble(strSource))
            {
                double dblValue = double.Parse(strSource);
                bResult = (dblValue >= minValue && dblValue <= maxValue);
            }
            else
            {
                bResult = (strSource.Length == 0 && AllowEmpty);
            }

            return bResult;
        }

        /// <summary>
        /// 检测字符串是否可转换成DateTime型
        /// </summary>
        /// <param name="strSource">要转换的字符串</param>
        /// <returns>true:可以转换，false:不可以转换</returns>
        public static bool IsDate(string strSource)
        {
            DateTime i;
            return DateTime.TryParse(strSource, out i);
        }
        #endregion

        #region 判断函数
        /// <summary>
        /// 判断一个字符串是不是正整数
        /// </summary>
        /// <remarks>仅包含数字</remarks>
        /// <param name="strSource">待验证的字符串</param>
        /// <returns>TRUE: 符合要求; FALSE: 不符合要求</returns>
        public static bool IsNum(string strSource)
        {
            return Regex.IsMatch(strSource, @"^\d+$");
        }

        /// <summary>
        /// 用正则表达式检查是否是数字
        /// </summary>
        /// <param name="strNumber">字符串</param>
        /// <returns></returns>
        public static bool IsNumber(String strNumber)
        {
            Regex objNotNumberPattern = new Regex("[^0-9.-]");
            Regex objTwoDotPattern = new Regex("[0-9]*[.][0-9]*[.][0-9]*");
            Regex objTwoMinusPattern = new Regex("[0-9]*[-][0-9]*[-][0-9]*");

            String strValidRealPattern = "^([-]|[.]|[-.]|[0-9])[0-9]*[.]*[0-9]+$";
            String strValidIntegerPattern = "^([-]|[0-9])[0-9]*$";
            Regex objNumberPattern = new Regex("(" + strValidRealPattern + ")|(" + strValidIntegerPattern + ")");

            return !objNotNumberPattern.IsMatch(strNumber) &&
                    !objTwoDotPattern.IsMatch(strNumber) &&
                    !objTwoMinusPattern.IsMatch(strNumber) &&
                    objNumberPattern.IsMatch(strNumber);
        }

        /// <summary>
        /// 检查一个字符串是不是合法的时间格式,如果是: 把输入字符串转换成标准的日期格式HH:MM:SS
        /// </summary>
        /// <param name="strSource">表示时间的字符</param>
        /// <returns>TRUE: 是; FALSE: 不是</returns>
        public static bool IsTime(string strSource)
        {
            try
            {
                DateTime dtTmp = System.Convert.ToDateTime("2010-01-01 " + strSource);

                return true;
            }
            catch (Exception)
            {
                return false;
            }

            //const int MAX_TIME_ITEM_COUNT   = 3;

            //int intValue                    = 0;

            //string[] astrItem = str.Split(ConstStr.COLON.ToCharArray());

            //if (astrItem.Length < 1) return false;

            //for (int i = 0; i < astrItem.Length && i < MAX_TIME_ITEM_COUNT; i++)
            //{
            //    if (astrItem[i].Trim().Length == 0)
            //    {
            //        astrItem[i] = DateFormat.TIME_ITEM;
            //    }

            //    // 如果不是数字字符串, 退出
            //    if (IsNum(astrItem[i]) == false)
            //    {
            //        return false;
            //    }

            //    intValue = int.Parse(astrItem[i]);

            //    // 小时
            //    if (i == 0)
            //    {
            //        // 小时范围 0 <= X < 24
            //        if (intValue < 0 || intValue > 23)
            //        {
            //            return false;
            //        }

            //        str = intValue.ToString(DateFormat.TIME_ITEM);
            //    }
            //    // 分钟或秒
            //    else
            //    {
            //        // 分钟或秒范围 0 <= X < 60
            //        if (intValue < 0 || intValue > 60)
            //        {
            //            return false;
            //        }

            //        str += ConstStr.COLON + intValue.ToString(DateFormat.TIME_ITEM);
            //    }
            //}

            //// 补足不够的部份
            //for (int i = astrItem.Length; i < MAX_TIME_ITEM_COUNT; i++)
            //{
            //    str += ConstStr.COLON + DateFormat.TIME_ITEM;
            //}

            //return true;
        }

        /// <summary>
        /// 检查一个字符串是不是合法的时间格式
        /// 如果是: 把输入字符串转换成标准的日期格式HH:MM:SS
        /// </summary>
        /// <param name="strSource">表示时间的字符</param>
        /// <param name="formatedStr">格式化后的字符串</param>
        /// <returns>TRUE: 是; FALSE: 不是</returns>
        public static bool IsTime(string strSource, out string formatedStr)
        {
            formatedStr = "HH:MM:SS";
            try
            {
                DateTime dtTmp = System.Convert.ToDateTime("2010-01-01 " + strSource);
                return true;
            }
            catch (Exception)
            {
                return false;
            }

            //const int MAX_TIME_ITEM_COUNT = 3;

            //int intValue = 0;
            //formatedStr = string.Empty;
            
            //string[] astrItem = str.Split(ConstStr.COLON.ToCharArray());

            //if (astrItem.Length < 1) return false;

            //for (int i = 0; i < astrItem.Length && i < MAX_TIME_ITEM_COUNT; i++)
            //{
            //    if (astrItem[i].Trim().Length == 0)
            //    {
            //        astrItem[i] = DateFormat.TIME_ITEM;
            //    }

            //    // 如果不是数字字符串, 退出
            //    if (IsNum(astrItem[i]) == false)
            //    {
            //        return false;
            //    }

            //    intValue = int.Parse(astrItem[i]);

            //    // 小时
            //    if (i == 0)
            //    {
            //        // 小时范围 0 <= X < 24
            //        if (intValue < 0 || intValue > 23)
            //        {
            //            return false;
            //        }

            //        str = intValue.ToString(DateFormat.TIME_ITEM);
            //    }
            //    // 分钟或秒
            //    else
            //    {
            //        // 分钟或秒范围 0 <= X < 60
            //        if (intValue < 0 || intValue > 60)
            //        {
            //            return false;
            //        }

            //        str += ConstStr.COLON + intValue.ToString(DateFormat.TIME_ITEM);
            //    }
            //}

            //// 补足不够的部份
            //for (int i = astrItem.Length; i < MAX_TIME_ITEM_COUNT; i++)
            //{
            //    str += ConstStr.COLON + DateFormat.TIME_ITEM;
            //}
            
            //formatedStr = str;
            
            //return true;
        }

        /// <summary>
        /// 判断一个对象是否为空
        /// </summary>
        /// <param name="objSrc">要判断的对象</param>
        /// <returns>TRUE: 为空; FALSE: 非空</returns>
        public static bool IsNullOrEmpty(object objSrc)
        {
            if (objSrc == null || objSrc.ToString().Length == 0)
            {
                return true;
            }
            
            return false;
        }

        /// <summary>
        /// 判断一个对象是否为空
        /// </summary>
        /// <param name="objSrc">要判断的对象</param>
        /// <param name="emptyValue">空值</param>
        /// <returns>TRUE: 为空; FALSE: 非空</returns>
        public static bool IsNullOrEmpty(object objSrc, string emptyValue)
        {
            if (objSrc == null || objSrc.ToString().Length == 0)
            {
                return true;
            }
            
            if (objSrc.ToString().Trim().Equals(emptyValue) == true)
            {
                return true;
            }
            
            return false;
        }
        #endregion

        #region 格式处理
        /// <summary>
        /// 格式化数字
        /// </summary>
        /// <param name="strValue">值</param>
        /// <param name="strFormat">格式</param>
        /// <returns>以指定格式显示值</returns>
        public static string FormatDecimal(string strValue, string strFormat)
        {
            if (strValue == null) strValue = string.Empty;
            
            decimal dTemp = 0;
            decimal.TryParse(strValue, out dTemp);
            return dTemp.ToString(strFormat);
        }

        /// <summary>
        /// 格式化数字
        /// </summary>
        /// <param name="dValue">要格式化的数字</param>
        /// <param name="precision">精度，即取小数点后几位</param>
        /// <param name="groupDigits">是否使用逗号分割符</param>
        /// <param name="round">是否四舍五入</param>
        /// <param name="null0">无效的值是否显示为0，为否的话返回空字符串</param>
        /// <returns>格式化后的字符串</returns> 
        public static string FormatNumber(double dValue, int precision, bool groupDigits, bool round, bool null0)
        {
            return FormatNumber(dValue.ToString(), precision, groupDigits, round, null0);
        }

        /// <summary>
        /// 格式化数字
        /// </summary>
        /// <param name="strValue">要格式话的数字</param>
        /// <param name="precision">精度，即取小数点后几位</param>
        /// <param name="groupDigits">是否使用逗号分割符</param>
        /// <param name="round">是否四舍五入</param>
        /// <param name="null0">无效的值是否显示为0，为否的话返回空字符串</param>
        /// <returns>格式化后的字符串</returns> 
        public static string FormatNumber(string strValue, int precision, bool groupDigits, bool round, bool null0)
        {
            string strResult = string.Empty;
            double dblValue;
            string strFormat;

            if (double.TryParse(strValue, out dblValue))
            {
                if (groupDigits)
                    strFormat = "n";
                else
                    strFormat = "f";

                if (precision >= 0)
                    strFormat += precision.ToString();

                strResult = dblValue.ToString(strFormat);
            }
            else
            {
                if (null0)
                {
                    dblValue = 0;

                    if (groupDigits)
                        strFormat = "n";
                    else
                        strFormat = "f";

                    if (precision > 0)
                        strFormat += precision.ToString();

                    strResult = dblValue.ToString(strFormat);
                }

            }

            return strResult;
        }
        #endregion

        #region 字符串比较
        /// <summary>
        /// 比较字符穿大小
        /// </summary>
        /// <param name="str1">字符串1</param>
        /// <param name="str2">字符串2</param>
        /// <remarks>逐字母比较, 以ascii码为依据，短的比长的小。返回值-1/0/1分别表示小于/等于/大于</remarks>
        public static int CompareString(string str1, string str2)
        {
            int ret = 0;

            for (int i = 0; i < str1.Length && i < str2.Length; i++)
            {
                char c1 = str1[i];
                char c2 = str2[i];

                int asc1 = System.Convert.ToInt32(c1);
                int asc2 = System.Convert.ToInt32(c2);

                if (asc1 < asc2)
                {
                    ret = -1;
                    break;
                }
                else if (asc1 > asc2)
                {
                    ret = 1;
                    break;
                }
            }

            if (ret == 0)
                if (str1.Length < str2.Length)
                {
                    ret = -1;
                }
                else if (str1.Length > str2.Length)
                {
                    ret = 1;
                }

            return ret;
        }

        /// <summary>
        /// 支持通配符的字符串比较
        /// </summary>
        /// <param name="strPattern">字符串模板, 如"hou*"</param>
        /// <param name="strInput">要比较的字符串</param>
        /// <returns>是否匹配</returns>
        public static bool LikeCompare(string strPattern, string strInput)
        {
            strPattern = strPattern.ToLower();
            strInput = strInput.ToLower();

            if (strPattern.IndexOf("*") == -1)
            {
                return strPattern == strInput;
            }
            else
            {
                strPattern = strPattern.Replace("*", ".*");
                strPattern = "^" + strPattern;

                return System.Text.RegularExpressions.Regex.IsMatch(strInput, strPattern);
            }
        }
        #endregion

        #region 数组相关
        /// <summary>
        /// 用通用分隔符(,，;；)分隔字符串, 去除空串
        /// </summary>
        /// <param name="strSource">字符串</param>
        /// <returns></returns>
        public static string[] SplitStrByCommChar(string strSource)
        {
            return strSource.Split(new string[] { ";", "；", ",", "，" }, StringSplitOptions.RemoveEmptyEntries);
        }

        /// <summary>
        /// int型数组转换为字符串
        /// </summary>
        /// <param name="iArrData">int型数组</param>
        /// <returns></returns>
        public static string ChangeIntArrayToString(int[] iArrData)
        {
            string strResult = string.Empty;
            foreach (int id in iArrData)
            {
                strResult += id + ",";
            }
            if (strResult.Length > 0)
            {
                strResult = strResult.Substring(0, strResult.Length - 1);
            }
            return strResult;
        }

        /// <summary>
        /// 字符串转换为int型数组
        /// </summary>
        /// <param name="strSource">字符串</param>
        /// <returns>int型数组</returns>
        public static int[] ChangeStringToIntArray(string strSource)
        {
            int[] iArrData = new int[strSource.Split(',').Length];
            for (int i = 0; i < strSource.Split(',').Length; i++)
            {
                string id = strSource.Split(',')[i];
                iArrData[i] = int.Parse(id);
            }
            return iArrData;
        }
        #endregion

        #region 汉字相关
        /// <summary>
        /// 获取汉字拼音的第一个字母
        /// </summary>
        /// <param name="strText">汉字字符串</param>
        /// <returns></returns>
        public static string GetChineseSpell(string strText)
        {
            int iLen = strText.Length;
            string strResult = string.Empty;
            for (int i = 0; i < iLen; i++)
            {
                strResult += getSpell(strText.Substring(i, 1));
            }
            return strResult;
        }

        /// <summary>
        /// 根据一组汉字的获取他们的一组第一个字母拼音
        /// </summary>
        /// <param name="strText">汉字字符串</param>
        /// <returns></returns>
        public static string[] GetChineseSpell(string[] strText)
        {
            int iLen = strText.Length;
            string[] strArrResult = null;
            for (int i = 0; i < iLen; i++)
            {
                strArrResult[i] = getSpell(strText[i]);
            }
            return strArrResult;
        }

        /// <summary>
        /// 获取一个汉字的拼音首字母
        /// </summary>
        /// <param name="strText">汉字字符串</param>
        /// <returns></returns>
        public static string getSpell(string strText)
        {
            byte[] bArrCN = Encoding.Default.GetBytes(strText);
            if (bArrCN.Length > 1)
            {
                int area = (short)bArrCN[0];
                int pos = (short)bArrCN[1];
                int code = (area << 8) + pos;
                int[] areacode = { 45217, 45253, 45761, 46318, 46826, 47010, 47297, 47614, 48119, 48119, 49062, 49324, 49896, 50371, 50614, 50622, 50906, 51387, 51446, 52218, 52698, 52698, 52698, 52980, 53689, 54481 };
                for (int i = 0; i < 26; i++)
                {
                    int max = 55290;
                    if (i != 25) max = areacode[i + 1];
                    if (areacode[i] <= code && code < max)
                    {
                        return Encoding.Default.GetString(new byte[] { (byte)(65 + i) });
                    }
                }
                return "*";
            }
            else return strText;
        }

        /// <summary>
        /// 汉字转拼音缩写
        /// </summary>
        /// <param name="strText">汉字字符串</param>
        /// <returns>拼音首字母字符串</returns>
        public static string GetPYString(string strText)
        {
            string strResult = string.Empty;

            foreach (char c in strText)
            {
                // 字母和符号原样保留
                if ((int)c >= 33 && (int)c <= 126)
                {
                    strResult += c.ToString();
                }
                // 累加拼音声母   
                else
                {
                    strResult += GetPYChar(c.ToString());
                }
            }

            return strResult;
        }

        /// <summary>
        /// 取单个字符的拼音声母
        /// </summary>
        /// <param name="strText">要转换的单个汉字</param>
        /// <returns>拼音声母</returns>
        public static string GetPYChar(string strText)
        {
            if (string.IsNullOrEmpty(strText) || strText.Trim().Length == 0) return string.Empty;

            byte[] bArray = new byte[2];

            bArray = System.Text.Encoding.Default.GetBytes(strText);

            int i = (short)(bArray[0] - '\0') * 256 + ((short)(bArray[1] - '\0'));
            
            if (i < 0xB0A1) return "*";
            if (i < 0xB0C5) return "a";
            if (i < 0xB2C1) return "b";
            if (i < 0xB4EE) return "c";
            if (i < 0xB6EA) return "d";
            if (i < 0xB7A2) return "e";
            if (i < 0xB8C1) return "f";
            if (i < 0xB9FE) return "g";
            if (i < 0xBBF7) return "h";
            if (i < 0xBFA6) return "j";
            if (i < 0xC0AC) return "k";
            if (i < 0xC2E8) return "l";
            if (i < 0xC4C3) return "m";
            if (i < 0xC5B6) return "n";
            if (i < 0xC5BE) return "o";
            if (i < 0xC6DA) return "p";
            if (i < 0xC8BB) return "q";
            if (i < 0xC8F6) return "r";
            if (i < 0xCBFA) return "s";
            if (i < 0xCDDA) return "t";
            if (i < 0xCEF4) return "w";
            if (i < 0xD1B9) return "x";
            if (i < 0xD4D1) return "y";
            if (i < 0xD7FA) return "z";

            return "*";
        }

        /// <summary>
        /// 把数字转换为汉字
        /// </summary>
        /// <param name="strNumString">数字字符串</param>
        /// <returns></returns>
        public static string NumberToChnText(string strNumString)
        {
            char[] chnGenText = new char[] { '零', '一', '二', '三', '四', '五', '六', '七', '八', '九' };
            string strResult = string.Empty;

            if (strNumString.Length > 0)
            {
                for (int i = 0; i < chnGenText.Length; i++)
                {
                    char c = chnGenText[i];
                    if (c >= '0' && c <= '9')
                    {
                        strResult += chnGenText[c - 65];
                    }
                }
            }
            else
                strResult = string.Empty;

            return strResult;
        }
        #endregion

        #region UNICODE
        /// <summary>  
        /// 字符串转为UniCode码字符串  
        /// </summary>  
        /// <param name="strSource">字符串</param>  
        /// <returns>UniCode码字符串</returns>  
        public static string StringToUnicode(string strSource)
        {
            char[] charbuffers = strSource.ToCharArray();
            byte[] buffer;
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < charbuffers.Length; i++)
            {
                buffer = System.Text.Encoding.Unicode.GetBytes(charbuffers[i].ToString());
                sb.Append(String.Format("\\{0:X2}{1:X2}", buffer[1], buffer[0]));
            }
            return sb.ToString();
        }

        /// <summary>  
        /// Unicode字符串转为正常字符串  
        /// </summary>  
        /// <param name="strSource">UniCode码字符串</param>  
        /// <returns>字符串</returns>  
        public static string UnicodeToString(string strSource)
        {
            string strResult = string.Empty;
            string src = strSource;
            int len = strSource.Length / 6;
            for (int i = 0; i <= len - 1; i++)
            {
                string str = "";
                str = src.Substring(0, 6).Substring(2);
                src = src.Substring(6);
                byte[] bytes = new byte[2];
                bytes[1] = byte.Parse(int.Parse(str.Substring(0, 2), NumberStyles.HexNumber).ToString());
                bytes[0] = byte.Parse(int.Parse(str.Substring(2, 2), NumberStyles.HexNumber).ToString());
                strResult += Encoding.Unicode.GetString(bytes);
            }
            return strResult;
        }
        #endregion

        #region SQL相关
        /// <summary>
        /// 获取SQL的IN 条件 例：ITEM_ID IN ('1', '2')
        /// </summary>
        /// <param name="strColName">列名</param>
        /// <param name="strArrValue">值数组</param>
        /// <returns></returns>
        public static string GetSqlInFilter(string strColName, string[] strArrValue)
        {
            if (strArrValue == null || strArrValue.Length == 0) return string.Empty;

            string strSql = strColName + " IN (";

            for (int i = 0; i < strArrValue.Length; i++)
            {
                if (strArrValue[i] != null)
                {
                    if (i > 0) strSql += ", ";
                    strSql += SqlConvert(strArrValue[i]);
                }
            }

            strSql += ") ";

            return strSql;
        }


        /// <summary>
        /// 获取SQL的IN 条件 例：ITEM_ID IN ('1', '2')
        /// </summary>
        /// <param name="colName"></param>
        /// <param name="valList"></param>
        /// <returns></returns>
        public static string GetSqlInFilter(string colName, ArrayList arrID)
        {
            if (arrID == null || arrID.Count == 0) return string.Empty;

            string sql = colName + " IN (";

            for (int i = 0; i < arrID.Count; i++)
            {
                if (i > 0) sql += ", ";
                sql += SqlConvert(arrID[i].ToString());
            }

            sql += ") ";

            return sql;
        }

        /// <summary>
        /// 生成插入SQL语句
        /// </summary>
        /// <param name="strTableName">表名</param>
        /// <param name="hasItems">参数表</param>
        /// <returns></returns>
        public static string GetSqlTextInsert(string strTableName, Hashtable hasItems)
        {
            if (hasItems == null || hasItems.Count < 1) throw new Exception("GetSqlTextIns 参数不能为空!");

            string sqlText = "INSERT INTO " + strTableName + "(";

            foreach (DictionaryEntry entry in hasItems)
            {
                sqlText += entry.Key.ToString() + ", ";
            }
            sqlText = sqlText.Substring(0, sqlText.Length - 2) + ") ";

            sqlText += " VALUES (";
            foreach (DictionaryEntry entry in hasItems)
            {
                if (entry.Value == null || entry.Value == DBNull.Value)
                {
                    sqlText += "NULL";
                }
                else if (entry.Value.GetType().Equals(typeof(DateTime)))
                {
                    if (((DateTime)entry.Value).Equals(DateTime.MaxValue))
                    {
                        sqlText += "SYSDATE";
                    }
                    else
                    {
                        sqlText += GetOraDate((DateTime)(entry.Value));
                    }
                }
                else
                {
                    sqlText += SqlConvert(entry.Value.ToString());
                }

                sqlText += ", ";
            }

            sqlText = sqlText.Substring(0, sqlText.Length - 2) + ") ";

            return sqlText;
        }

        /// <summary>
        /// 生成Update的SQL语句
        /// </summary>
        /// <param name="strTableName">表名</param>
        /// <param name="hasItems">参数表</param>
        /// <param name="strFilter">条件</param>
        /// <returns></returns>
        public static string GetSqlTextUpdate(string strTableName, Hashtable hasItems, string strFilter)
        {
            if (hasItems == null || hasItems.Count < 1) throw new Exception("GetSqlTextIns 参数不能为空!");

            string strSqlText = "UPDATE " + strTableName + " SET ";

            foreach (DictionaryEntry entry in hasItems)
            {
                strSqlText += entry.Key.ToString() + " = ";

                if (entry.Value == null || entry.Value == DBNull.Value)
                {
                    strSqlText += "NULL";
                }
                else if (entry.Value.GetType().Equals(typeof(DateTime)))
                {
                    if (((DateTime)entry.Value).Equals(DateTime.MaxValue))
                    {
                        strSqlText += "SYSDATE";
                    }
                    else
                    {
                        strSqlText += GetOraDate((DateTime)(entry.Value));
                    }
                }
                else
                {
                    strSqlText += SqlConvert(entry.Value.ToString());
                }

                strSqlText += ", ";
            }

            strSqlText = strSqlText.Substring(0, strSqlText.Length - 2);

            if (string.IsNullOrEmpty(strFilter) == false)
            {
                strSqlText += " WHERE " + strFilter;
            }

            return strSqlText;
        }

        /// <summary>
        /// 生成条件SQL语句
        /// </summary>
        /// <param name="hasItems">参数表</param>
        /// <returns></returns>
        public static string GetSqlTextEqual(Hashtable hasItems)
        {
            if (hasItems == null || hasItems.Count < 1) throw new Exception("GetSqlTextIns 参数不能为空!");

            string strSqlText = "(";
            foreach (DictionaryEntry entry in hasItems)
            {
                if (entry.Value == null || entry.Value == DBNull.Value)
                {
                    strSqlText += entry.Key.ToString() + " IS NULL ";
                }
                else if (entry.Value.GetType().Equals(typeof(DateTime)))
                {
                    strSqlText += entry.Key.ToString() + " = " + GetOraDate((DateTime)(entry.Value));
                }
                else
                {
                    strSqlText += entry.Key.ToString() + " = " + SqlConvert(entry.Value.ToString());
                }

                strSqlText += " AND ";
            }

            strSqlText = strSqlText.Substring(0, strSqlText.Length - 5) + ") ";

            return strSqlText;
        }

		#endregion

		/// <summary>
		/// 获取SQL查询的在某一天内条件
		/// </summary>
		/// <param name="strColName">列名</param>
		/// <param name="dtmDate">时间</param>
		/// <returns></returns>
		public static string GetSqlInDay(string strColName, DateTime dtmDate)
        {
            string strFilter = " ("
                    + "(" + strColName + " IS NOT NULL) AND "
                    + "(" + strColName + " >= " + GetSqlDate(dtmDate.Date) 
                        + " AND " 
                        + strColName + " < " + GetSqlDate(dtmDate.AddDays(1).Date) 
                    + ")"
                 + ") ";

            return strFilter;
        }

        /// <summary>
        /// 获取空值
        /// </summary>
        /// <param name="str"></param>
        /// <param name="nullValue"></param>
        /// <returns></returns>
        public static string GetNullValue(string str, string nullValue)
        {
            if (string.IsNullOrEmpty(str)) return null;
            if (str.Equals(nullValue)) return null;

            return str;
        }
    }
}
