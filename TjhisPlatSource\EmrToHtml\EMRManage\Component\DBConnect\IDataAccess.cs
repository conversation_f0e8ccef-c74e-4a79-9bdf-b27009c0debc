﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Collections;
using System.Data.Common;

namespace Utility
{
    interface IDataAccess
    {
        DbConnection GetDbConnection();
        DataTable GetDataTable(string sql);
        DataTable GetDataTable(DbCommand dbCommand);
        bool ExecuteNonQuery(string sql);
        bool ExecuteNonQuery(DbCommand dbCommand);
        object ExecuteScalar(string sql);
        object ExecuteScalar(DbCommand dbCommand);
        bool ExecuteTransaction(List<string> sql);
        bool ExecuteTransaction(List<DbCommand> dbCommands);
        bool ExecuteTransaction(ArrayList sqlCommands, CustomCommandType type);
        DbDataReader GetDataReader(string sql);
        DbDataReader GetDataReader(DbCommand dbCommand);
        

    }
    // 摘要:
    //     指定如何解释命令字符串。
    public enum CustomCommandType
    {
        // 摘要:
        //     SQL 文本命令。（默认。）
        Text = 1,
        //
        // 摘要:
        //     存储过程的名称。
        DBCommand = 4,
    }
   
}
