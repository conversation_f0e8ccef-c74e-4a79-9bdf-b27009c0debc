﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Configuration;

namespace Utility
{
    class OledbDataAccess : IDataAccess
    {
        private string connectionString = ConfigurationManager.ConnectionStrings["connectionString"].ConnectionString;
        public OledbDataAccess(bool isEntcrypt)
        {
            if (isEntcrypt)
            {
                connectionString = EnDecrypt.TJDecrypt(connectionString);
            }
        }
        #region IDataAccess 成员

        public System.Data.DataTable GetDataTable(string sql)
        {
            throw new NotImplementedException();
        }

        public System.Data.DataTable GetDataTable(System.Data.Common.DbCommand dbCommand)
        {
            throw new NotImplementedException();
        }

        public bool ExecuteNonQuery(string sql)
        {
            throw new NotImplementedException();
        }

        public bool ExecuteNonQuery(System.Data.Common.DbCommand dbCommand)
        {
            throw new NotImplementedException();
        }

        public object ExecuteScalar(string sql)
        {
            throw new NotImplementedException();
        }

        public object ExecuteScalar(System.Data.Common.DbCommand dbCommand)
        {
            throw new NotImplementedException();
        }

        public bool ExecuteTransaction(List<string> sql)
        {
            throw new NotImplementedException();
        }

        public bool ExecuteTransaction(List<System.Data.Common.DbCommand> dbCommands)
        {
            throw new NotImplementedException();
        }

        public System.Data.Common.DbDataReader GetDataReader(string sql)
        {
            throw new NotImplementedException();
        }

        public System.Data.Common.DbDataReader GetDataReader(System.Data.Common.DbCommand dbCommand)
        {
            throw new NotImplementedException();
        }

        public bool ExecuteTransaction(System.Collections.ArrayList sqlCommands, CustomCommandType type)
        {
            throw new NotImplementedException();
        }

        public System.Data.Common.DbConnection GetDbConnection()
        {
            throw new NotImplementedException();
        }

        #endregion
    }
}
