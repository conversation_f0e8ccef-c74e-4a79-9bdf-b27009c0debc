﻿/*-----------------------------------------------------------------------
 * 类名称    ：frminputsetting
 * 类描述    ：输入法窗体,适用于gridview适用于某些条件下不能使用searchlookupedit的情况需传进来GS_INPUTSETING
 * 创建人    ： 杨红宇yhy
 * ---------------------------------------------------------------------- */


using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
//using PlatCommon.System;
using NM_Service.NMService;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.WinExplorer;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors;
//using Comm.Collection;
//using Comm.Message;
//using NPM.Bll;
namespace PlatCommonForm
{
    public partial class frminputsettingexstock : PlatCommon.SysBase.ParentForm
    {
        public frminputsettingexstock()
        {
            InitializeComponent();
        }
        public string GS_INPUTSETING;//输入法
        public string GS_ITEM_CLASS;
        public string GS_ITEM_NAME;
        public string GS_ITEM_CODE;
        public string GS_ITEM_SPEC;
        public string GS_PRICE;
        public string GS_UNITS;
        public string GS_PERFORMED_BY;
        public string GS_AAA;
        public string GS_BBB;

        public string GS_BATCH_NO; //批次
        DataTable dtPriceList;
        private void frminputsetting_Load(object sender, EventArgs e)
        {
      
            if (!string.IsNullOrEmpty(GS_INPUTSETING))
            {
                this.Text = GS_INPUTSETING;
            }
            LoadPriceList();
            deptdict_load();
        }
        public void LoadPriceList()
        {
            string sqlstr = "  select ITEM_CLASS,ITEM_CODE,ITEM_NAME,ITEM_SPEC,UNITS,PRICE,PREFER_PRICE AAA ,FOREIGNER_PRICE BBB,PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB from input_nodrug_list where HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'"; //其它价表
            if (string.IsNullOrEmpty(GS_INPUTSETING) || GS_INPUTSETING.Equals("耗材字典"))
            {
                sqlstr = "  select '' ITEM_CLASS,EX_CODE as ITEM_CODE,EX_NAME as ITEM_NAME,PACKAGE_SPEC as ITEM_SPEC, PACKAGE_UNITS as UNITS,PURCHASE_PRICE as PRICE,'' AAA,'' BBB,'' PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB from V_EX_NAME_DICT where HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";   //耗材字典
            }
            else if (GS_INPUTSETING.Equals("耗材厂商")) //耗材厂商
            {
                sqlstr = "  select SUPPLIER_CLASS as ITEM_CLASS,SUPPLIER_ID as ITEM_CODE,SUPPLIER_NAME as ITEM_NAME,'' ITEM_SPEC,'' UNITS,'' PRICE,''  AAA,'' BBB,'' PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB from EX_SUPPLIER_CATALOG "; //耗材厂商
            }
            else if (GS_INPUTSETING.Equals("耗材库存")) //耗材库存
            {
                sqlstr = "  select b.exp_form as ITEM_CLASS,b.EXP_CODE as ITEM_CODE,b.EXP_NAME as ITEM_NAME,a.PACKAGE_SPEC ITEM_SPEC,a.UNITS as UNITS,'' PRICE,a.FIRM_ID as  AAA,a.EX_SPEC BBB,a.package_units PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB from ex_price_list a ,V_EXP_NAME_DICT b where a.ex_code = b.exp_code AND a.his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode+"' and start_date <= sysdate AND ( stop_date IS NULL OR stop_date > sysdate ) and STORAGE_CODE = '" + GS_AAA + "'"; //库房号
            }
            // 其它输入法也继续字段别名保持一致就可以
            using (ServerPublicClient client = new ServerPublicClient())
            {
                DataSet ds = client.GetDataBySql(sqlstr);
                if (ds != null )
                {
                    dtPriceList = ds.Tables[0];
                    //if (string.IsNullOrEmpty(GS_INPUTSETING) || GS_INPUTSETING.Equals("其它价表"))
                    //{
                    //    if (string.IsNullOrEmpty(GS_ITEM_CLASS) || GS_ITEM_CLASS.Length == 0)
                    //    {
                    //        dtPriceList.DefaultView.RowFilter = "ITEM_CLASS NOT IN ('A','B','C','D')";
                    //    }
                    //    else
                    //    {
                    //        dtPriceList.DefaultView.RowFilter = "ITEM_CLASS  IN ('" + GS_ITEM_CLASS + "')";
                    //    }
                    //}

                }
                ds.Dispose();


                gridControl2.DataSource = dtPriceList;

                if (GS_INPUTSETING.Equals("耗材字典"))
                {
                    this.gridView3.Columns["PRICE"].Caption = "进货价";
                    this.gridView3.Columns["ITEM_CLASS"].Visible = false;
                }
                if (GS_INPUTSETING.Equals("耗材厂商"))
                {
                    this.gridView3.Columns["PRICE"].Caption = "进货价";
                    this.gridView3.Columns["ITEM_CLASS"].Caption = "类型";
                    this.gridView3.Columns["ITEM_SPEC"].Visible = false;
                    this.gridView3.Columns["UNITS"].Visible = false;
                    this.gridView3.Columns["PRICE"].Visible = false;
                }
                
            }
        }

        private void gridView3_MouseDown(object sender, MouseEventArgs e)
        {
      


        }

        private void gridView3_DoubleClick(object sender, EventArgs e)
        {
                        DataRow row = this.gridView3.GetDataRow(this.gridView3.FocusedRowHandle);
                        if (row != null)
                        {
                            GS_ITEM_NAME = row["ITEM_NAME"].ToString();
                            GS_ITEM_CODE = row["ITEM_CODE"].ToString();
                            GS_ITEM_SPEC = row["ITEM_SPEC"].ToString();
                            GS_ITEM_CLASS = row["ITEM_CLASS"].ToString();
                            GS_PRICE = row["PRICE"].ToString();
                            GS_UNITS = row["UNITS"].ToString();
                            GS_PERFORMED_BY = row["PERFORMED_BY"].ToString();
                            GS_AAA = row["AAA"].ToString();
                            GS_BBB = row["BBB"].ToString();
                            if (GS_INPUTSETING.Equals("诊疗药品"))
                            {
                                GS_BATCH_NO = row["BATCH_NO"].ToString();
                            }
                        }
                        this.Close();
        }
        private void textEdit1_EditValueChanged(object sender, EventArgs e)
        {
            this.gridView3.FindFilterText = this.textEdit1.Text.Trim();
            
        }

        private void frminputsetting_Shown(object sender, EventArgs e)
        {
            textEdit1.Focus();
        }

        private void gridView3_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                gridView3_DoubleClick(sender, e);
            }
            if (e.KeyCode == Keys.Escape)
            {
                Close();
            } 
        }

        private void textEdit1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                gridControl2.Focus();
                gridView3.FocusedColumn = gITEM_NAME;
            }
            if (e.KeyCode == Keys.Down || e.KeyCode == Keys.Up)
            {
                gridControl2.Focus();
                gridView3.FocusedColumn = gITEM_NAME;
            }
        }
        private void deptdict_load()
        {
            string sqlstr = "select dept_code,dept_name from dept_dict";
            using (ServerPublicClient client = new ServerPublicClient())
            {
                DataSet ds = client.GetDataBySql(sqlstr);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    DataTable dtdept = ds.Tables[0];
                    deptItemLookUpEdit3.DataSource = dtdept;
                    deptItemLookUpEdit3.DisplayMember = "DEPT_NAME";
                    deptItemLookUpEdit3.ValueMember = "DEPT_CODE";
                }
            }
        }
        #region 键盘
        private void B0_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "0";
            }
            else
            {
                taa = "0";
            }

            textEdit1.Text = taa;
        }
        private void B1_Click(object sender, EventArgs e)
        {
          string taa = textEdit1.Text;
          if(!string.IsNullOrEmpty(taa))
          {
              taa = taa +"1";
          }
          else
          {
              taa = "1";
          }

          textEdit1.Text = taa;
        }
        private void B2_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "2";
            }
            else
            {
                taa = "2";
            }

            textEdit1.Text = taa;
        }


        private void B3_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "3";
            }
            else
            {
                taa = "3";
            }

            textEdit1.Text = taa;
        }

        private void B4_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "4";
            }
            else
            {
                taa = "4";
            }

            textEdit1.Text = taa;
        }

        private void B5_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "5";
            }
            else
            {
                taa = "5";
            }

            textEdit1.Text = taa;
        }

        private void B6_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "6";
            }
            else
            {
                taa = "6";
            }

            textEdit1.Text = taa;
        }

        private void B7_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "7";
            }
            else
            {
                taa = "7";
            }

            textEdit1.Text = taa;
        }

        private void B8_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "8";
            }
            else
            {
                taa = "8";
            }

            textEdit1.Text = taa;
        }

        private void B9_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "9";
            }
            else
            {
                taa = "9";
            }

            textEdit1.Text = taa;
        }
        private void Q_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "Q";
            }
            else
            {
                taa = "Q";
            }
            textEdit1.Text = taa;
        }

        private void W_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "W";
            }
            else
            {
                taa = "W";
            }
            textEdit1.Text = taa;
        }

        private void E_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "E";
            }
            else
            {
                taa = "E";
            }
            textEdit1.Text = taa;
        }

        private void R_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "R";
            }
            else
            {
                taa = "R";
            }
            textEdit1.Text = taa;
        }

        private void T_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "T";
            }
            else
            {
                taa = "T";
            }
            textEdit1.Text = taa;
        }

        private void Y_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "Y";
            }
            else
            {
                taa = "Y";
            }
            textEdit1.Text = taa;
        }

        private void U_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "U";
            }
            else
            {
                taa = "U";
            }
            textEdit1.Text = taa;
        }

        private void I_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "I";
            }
            else
            {
                taa = "I";
            }
            textEdit1.Text = taa;
        }

        private void O_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "O";
            }
            else
            {
                taa = "O";
            }
            textEdit1.Text = taa;
        }

        private void P_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "P";
            }
            else
            {
                taa = "P";
            }
            textEdit1.Text = taa;
        }

        private void A_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "A";
            }
            else
            {
                taa = "A";
            }
            textEdit1.Text = taa;
        }

        private void S_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "S";
            }
            else
            {
                taa = "S";
            }
            textEdit1.Text = taa;
        }

        private void D_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "D";
            }
            else
            {
                taa = "D";
            }
            textEdit1.Text = taa;
        }

        private void F_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "F";
            }
            else
            {
                taa = "F";
            }
            textEdit1.Text = taa;
        }

        private void G_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "G";
            }
            else
            {
                taa = "G";
            }
            textEdit1.Text = taa;
        }

        private void H_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "H";
            }
            else
            {
                taa = "H";
            }
            textEdit1.Text = taa;
        }

        private void J_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "J";
            }
            else
            {
                taa = "J";
            }
            textEdit1.Text = taa;
        }

        private void K_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "K";
            }
            else
            {
                taa = "K";
            }
            textEdit1.Text = taa;
        }

        private void L_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "L";
            }
            else
            {
                taa = "L";
            }
            textEdit1.Text = taa;
        }

        private void Z_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "Z";
            }
            else
            {
                taa = "Z";
            }
            textEdit1.Text = taa;
        }

        private void X_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "X";
            }
            else
            {
                taa = "X";
            }
            textEdit1.Text = taa;
        }

        private void C_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "C";
            }
            else
            {
                taa = "C";
            }
            textEdit1.Text = taa;
        }

        private void V_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "V";
            }
            else
            {
                taa = "V";
            }
            textEdit1.Text = taa;
        }

        private void B_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "B";
            }
            else
            {
                taa = "B";
            }
            textEdit1.Text = taa;
        }

        private void N_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "N";
            }
            else
            {
                taa = "N";
            }
            textEdit1.Text = taa;
        }

        private void M_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "M";
            }
            else
            {
                taa = "M";
            }
            textEdit1.Text = taa;
        }

        private void panelControl1_Paint(object sender, PaintEventArgs e)
        {
        }

        private void Bspace_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + " ";
            }
            else
            {
                taa = " ";
            }
            textEdit1.Text = taa;
        }
        /// <summary>
        /// 清除
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Bclear_Click(object sender, EventArgs e)
        {
            textEdit1.Text = string.Empty;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Backspace_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (taa.Length > 0)
            {
                taa = taa.Substring(0, taa.Length - 1);
            }
            textEdit1.Text = taa;
        }
        #endregion



    }
}
