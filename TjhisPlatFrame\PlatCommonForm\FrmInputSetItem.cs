﻿/*-----------------------------------------------------------------------
 * 类名称    ：FrmInputSet
 * 类描述    ：
 * 创建人    ：梁吉lions
 * 创建时间  ：2016/12/27 20:55:30
 * 修改人    ：
 * 修改时间  ：
 * 修改备注  ：
 * 版本      ：
 * ----------------------------------------------------------------------
 */
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraGrid.Views.Grid;
using PlatCommon.SysBase;

namespace PlatCommonForm
{
    public partial class FrmInputSetItem : ParentForm
    {
        DataTable dtInput;//输入法数据源
        private NM_Service.NMService.ServerPublicClient dictBll = new NM_Service.NMService.ServerPublicClient();
        private String inputType = "";
        private String query_condition = null;
        public DataRow retRow;//传值行
        public FrmInputSetItem()
        {
            InitializeComponent();
        }

        private void FrmInputSetItem_Load(object sender, EventArgs e)
        {
            switch(this.inputType){
                case "计价项目":
                    this.LoadPriceList();
                    break;
                default:
                    this.Close();
                    break;
            }
            
            txtEditInput.Focus();
        }

        #region 使用方法
        /// <summary>
        /// 设置输入法类型 价表
        /// </summary>
        /// <param name="instr"></param>
        public void SetType(String instr)
        {
            this.inputType = instr;
            this.Text = inputType;
        }

        public void SetCondition(String condition)
        {
            this.query_condition = condition;
        }
        private void LoadPriceList()
        {
            // 价表
            string sql = @"select price_list.item_code,price_list.item_name,price_list.item_spec,price_list.units,price_list.price,price_list.prefer_price,price_list.foreigner_price,price_list.fee_type_mask,
price_list.class_on_inp_rcpt,price_list.class_on_outp_rcpt,price_list.subj_code,price_list.class_on_reckoning,price_list.start_date,price_list.material_code,price_list.package_spec,price_list.mr_bill_class,(select price_item_name_dict.input_code from price_item_name_dict where price_item_name_dict.item_class= price_list.item_class and price_item_name_dict.item_code = price_list.item_code  and rownum=1) input_code from price_list  WHERE (START_DATE < SYSDATE AND (STOP_DATE IS NULL  OR STOP_DATE >= SYSDATE))";
            sql += " AND ITEM_CLASS NOT IN ('A', 'B') ";
            //增加分院条件过滤
            if (!string.IsNullOrEmpty(PlatCommon.SysBase.SystemParm.HisUnitCode))
            {
                sql += " AND HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
            }
            if (!string.IsNullOrEmpty(query_condition))
            {
                sql += " AND  " + query_condition;
            }


            dtInput = dictBll.GetDataBySql(sql).Tables[0];
            gcInput.DataSource = dtInput.DefaultView;

        }
        #endregion

        private void txtEditInput_EditValueChanged(object sender, EventArgs e)
        {
            //查询信息
            string text = txtEditInput.Text;
            dtInput.DefaultView.RowFilter = " (INPUT_CODE LIKE '%"+ text+"%') ";
        }

        private void txtEditInput_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Down)
            {
                switch (this.inputType)
                {
                    case "计价项目":
                        gviewPrice_list.Focus();
                        break;
                    default:
 
                        break;
                }
                
            }
        }
        //双击选中行
        private void gviewPrice_list_DoubleClick(object sender, EventArgs e)
        {
            GridView view = sender as GridView;
            if (view.FocusedRowHandle >= 0)
            {
                //选中行
                retRow = view.GetDataRow(view.FocusedRowHandle);
                this.Close();
            }
        }

        private void gviewPrice_list_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode.Equals(Keys.Enter))
            {
                this.gviewPrice_list_DoubleClick(gviewPrice_list, null);
            }
        }
    }
}
