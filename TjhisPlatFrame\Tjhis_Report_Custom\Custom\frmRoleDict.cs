﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Tjhis.Report.Custom.Srv;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmRoleDict : Form
    {
        srvStatisticalQuery srv;      
        DataTable dtRoleDict;
       
        string roleFilter;//角色过滤
        string displayFilter;//显示过滤
        public string rolesArray { get; set; }
        string _appName = string.Empty;
        public frmRoleDict(string appName)
        {
            InitializeComponent();
            _appName = appName;
        }

        /// <summary>
        /// 加载事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void frmRoleDict_Load(object sender, EventArgs e)
        {
            srv = new srvStatisticalQuery();
            InitData();

            SetSelectRole();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        void InitData()
        {
            dtRoleDict = srv.GetRoleDict(_appName).Tables[0];
            dtRoleDict.Columns["COL_SEL"].ReadOnly = false;
            gridControl1.DataSource = dtRoleDict.DefaultView;
        }

        /// <summary>
        /// 设置已经选择过的角色
        /// </summary>
        void SetSelectRole()
        {
            if (string.IsNullOrEmpty(rolesArray)) return;

            string[] roles = rolesArray.Split(',');
            List<DataRow> drRoles = dtRoleDict.Select().ToList();         
            foreach (string role in roles)
            {
                DataRow drRole = drRoles.Find(dr => dr["ROLE_CODE"].Equals(role));
                if (drRole != null)
                {
                    drRole["COL_SEL"] = "1";
                }
            }
        }

        /// <summary>
        /// 获取选中角色的字符串
        /// </summary>
        /// <returns></returns>
        string GetSelectRole()
        {
            rolesArray = "";

            List<DataRow> drRoles = dtRoleDict.Select("col_sel = '1'").ToList();
            drRoles.ForEach(dr => rolesArray += dr["ROLE_CODE"] + ",");

            return rolesArray;
        }
     

        /// <summary>
        /// 角色过滤
        /// </summary>
        /// <param name="str"></param>
        void TxtRoleChanging(string str)
        {
            if (string.IsNullOrEmpty(str))
                roleFilter = "";
            else
                roleFilter = " and (ROLE_CODE like '%" + str + "%' or ROLE_NAME like '%" + str + "%')";
            SetFilter();
        }
        /// <summary>
        /// [查询]按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void simpleButton1_Click(object sender, EventArgs e)
        {
            TxtRoleChanging(textEdit1.Text);
        }
       

        /// <summary>
        /// 确认并退出当前窗体
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void simpleButton2_Click(object sender, EventArgs e)
        {
            GetSelectRole();
            this.DialogResult = DialogResult.OK;
        }

        /// <summary>
        /// 取消并退出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void simpleButton3_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        /// <summary>
        /// 显示已选择的角色数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void cbDisplayChecks_CheckedChanged(object sender, EventArgs e)
        {
            if (cbDisplayChecks.Checked)
            {
                displayFilter = " and col_sel = '1'";
            }
            else
            {
                displayFilter = "";
            }
            SetFilter();
        }

        /// <summary>
        /// 设置过滤条件
        /// </summary>
        void SetFilter()
        {
            dtRoleDict.DefaultView.RowFilter = " 1=1" +  roleFilter + displayFilter;
        }

        /// <summary>
        /// 回车事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void textEdit1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                TxtRoleChanging(textEdit1.Text);
            }
        }
    }
}
