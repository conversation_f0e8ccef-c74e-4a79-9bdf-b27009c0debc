﻿using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System;
using System.Windows.Forms;
using Tjhis.Report.Custom.Base;

namespace Tjhis.Report.Custom.Custom
{
    public partial class TJReportViewF : ParentForm
    {
        public bool IsShowFunctionButton { get; set; } = true;
        string reportID;
        string isTableFlag;
        frmReportBase docForm;     

        public TJReportViewF()
        {
            SetStyle(ControlStyles.UserPaint, true);
            SetStyle(ControlStyles.AllPaintingInWmPaint, true); // 禁止擦除背景.
            SetStyle(ControlStyles.DoubleBuffer, true); // 双缓冲

            InitializeComponent();
        }
        //lxm20230627新增 科室代码 和模块代码变量传入 
        public TJReportViewF(string deptCode,string appCode,string other)
        {
            this.DeptCode = deptCode;
            this.AppCode = appCode;
            SetStyle(ControlStyles.UserPaint, true);
            SetStyle(ControlStyles.AllPaintingInWmPaint, true); // 禁止擦除背景.
            SetStyle(ControlStyles.DoubleBuffer, true); // 双缓冲

            InitializeComponent();
        }
        public TJReportViewF(string reportID) :this()
        {
            this.reportID = reportID;
        }
        public TJReportViewF(string reportID,string isTableFlag) : this(reportID)
        {
            this.isTableFlag = isTableFlag;
        }

        private void AddForm()
        {
            //lxm20230627新增 科室代码 和模块代码变量传入 ~
            if (!string.IsNullOrEmpty(isTableFlag) && isTableFlag.Equals("1"))
                //docForm = new frmStatisticalQueryGrid();
                docForm = new frmStatisticalQueryGrid(this.DeptCode,this.AppCode);
            else
              //  docForm = new frmStatisticalQueryDoc();
                docForm = new frmStatisticalQueryDoc(this.DeptCode, this.AppCode);
            
            docForm.Tag = reportID;
            docForm.TopLevel = false;
            docForm.FormBorderStyle = FormBorderStyle.None;
            docForm.Dock = DockStyle.Fill;
            docForm.IsShowFunctionButton = IsShowFunctionButton;
            this.Controls.Add(docForm);

            docForm.Visible = true;
            
            this.Text = docForm.Text;
        }

        private void TJDocViewF_FormClosed(object sender, FormClosedEventArgs e)
        {
            if(docForm != null)//关闭本窗体时关闭内窗体--释放内存；不关闭得不到释放
            {
                if (docForm is frmStatisticalQueryGrid)
                {
                    frmStatisticalQueryGrid form = ((frmStatisticalQueryGrid)docForm);
                    form.SaveReportConfig();
                }
                docForm.Close();              
            }
        }
       

        private void TJDocViewF_Load(object sender, EventArgs e)
        {           
            if (string.IsNullOrEmpty(reportID))
            {
                if(this.Tag != null && !string.IsNullOrEmpty(this.Tag.ToString()))
                {
                    string[] param = null;

                    if(Tag.ToString().Contains("-"))
                        param = Tag.ToString().Split('-');
                    else
                        param = Tag.ToString().Split(';');

                    reportID =param[0];
                    //reportID = param[0].Split('|')[1]; 
                    if (param.Length > 1)
                    {
                        isTableFlag = param[1];
                    }
                }
                else
                {
                    XtraMessageBox.Show("请给定报表ID再打开！", "提示");
                    this.Close();
                    return;
                }
            }

            AddForm();
        }
    }
}
