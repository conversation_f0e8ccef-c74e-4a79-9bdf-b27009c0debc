﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using System.ComponentModel;
namespace Model
{

    /// <summary>
    ///床位记录
    /// </summary>

    [DataContract]
    public class BED_REC : NotificationObject
    {

        /// <summary>
        /// 病房（护理单元）代码
        /// </summary>		
        private string _ward_code;
        [DataMember]
        public string WARD_CODE
        {
            get { return _ward_code; }
            set
            {
                if (_ward_code != value)
                {
                    _ward_code = value;
                    this.RaisePropertyChanged("WARD_CODE");
                }
            }
        }
        /// <summary>
        /// 床号
        /// </summary>		
        private decimal _bed_no;
        [DataMember]
        public decimal BED_NO
        {
            get { return _bed_no; }
            set
            {
                if (_bed_no != value)
                {
                    _bed_no = value;
                    this.RaisePropertyChanged("BED_NO");
                }
            }
        }
        /// <summary>
        /// 床标号
        /// </summary>		
        private string _bed_label;
        [DataMember]
        public string BED_LABEL
        {
            get { return _bed_label; }
            set
            {
                if (_bed_label != value)
                {
                    _bed_label = value;
                    this.RaisePropertyChanged("BED_LABEL");
                }
            }
        }
        /// <summary>
        /// 房间
        /// </summary>		
        private string _room_no;
        [DataMember]
        public string ROOM_NO
        {
            get { return _room_no; }
            set
            {
                if (_room_no != value)
                {
                    _room_no = value;
                    this.RaisePropertyChanged("ROOM_NO");
                }
            }
        }
        /// <summary>
        /// 所属科室代码
        /// </summary>		
        private string _dept_code;
        [DataMember]
        public string DEPT_CODE
        {
            get { return _dept_code; }
            set
            {
                if (_dept_code != value)
                {
                    _dept_code = value;
                    this.RaisePropertyChanged("DEPT_CODE");
                }
            }
        }
        /// <summary>
        /// 床位编制类型
        /// </summary>		
        private string _bed_approved_type;
        [DataMember]
        public string BED_APPROVED_TYPE
        {
            get { return _bed_approved_type; }
            set
            {
                if (_bed_approved_type != value)
                {
                    _bed_approved_type = value;
                    this.RaisePropertyChanged("BED_APPROVED_TYPE");
                }
            }
        }
        /// <summary>
        /// 床位类型(1-男， 2-女， 9-不限)
        /// </summary>		
        private string _bed_sex_type;
        [DataMember]
        public string BED_SEX_TYPE
        {
            get { return _bed_sex_type; }
            set
            {
                if (_bed_sex_type != value)
                {
                    _bed_sex_type = value;
                    this.RaisePropertyChanged("BED_SEX_TYPE");
                }
            }
        }
        /// <summary>
        /// 床位等级
        /// </summary>		
        private string _bed_class;
        [DataMember]
        public string BED_CLASS
        {
            get { return _bed_class; }
            set
            {
                if (_bed_class != value)
                {
                    _bed_class = value;
                    this.RaisePropertyChanged("BED_CLASS");
                }
            }
        }
        /// <summary>
        /// 床位状态
        /// </summary>		
        private string _bed_status;
        [DataMember]
        public string BED_STATUS
        {
            get { return _bed_status; }
            set
            {
                if (_bed_status != value)
                {
                    _bed_status = value;
                    this.RaisePropertyChanged("BED_STATUS");
                }
            }
        }
        /// <summary>
        /// 不用
        /// </summary>		
        private string _lend_attr;
        [DataMember]
        public string LEND_ATTR
        {
            get { return _lend_attr; }
            set
            {
                if (_lend_attr != value)
                {
                    _lend_attr = value;
                    this.RaisePropertyChanged("LEND_ATTR");
                }
            }
        }
        /// <summary>
        /// 不用
        /// </summary>		
        private decimal _lend_bed_no;
        [DataMember]
        public decimal LEND_BED_NO
        {
            get { return _lend_bed_no; }
            set
            {
                if (_lend_bed_no != value)
                {
                    _lend_bed_no = value;
                    this.RaisePropertyChanged("LEND_BED_NO");
                }
            }
        }
        /// <summary>
        /// 不用
        /// </summary>		
        private string _lend_bed_dept;
        [DataMember]
        public string LEND_BED_DEPT
        {
            get { return _lend_bed_dept; }
            set
            {
                if (_lend_bed_dept != value)
                {
                    _lend_bed_dept = value;
                    this.RaisePropertyChanged("LEND_BED_DEPT");
                }
            }
        }
        /// <summary>
        /// 不用
        /// </summary>		
        private string _lend_bed_ward;
        [DataMember]
        public string LEND_BED_WARD
        {
            get { return _lend_bed_ward; }
            set
            {
                if (_lend_bed_ward != value)
                {
                    _lend_bed_ward = value;
                    this.RaisePropertyChanged("LEND_BED_WARD");
                }
            }
        }
        /// <summary>
        /// 是否锁住床位
        /// </summary>		
        private string _lock_status;
        [DataMember]
        public string LOCK_STATUS
        {
            get { return _lock_status; }
            set
            {
                if (_lock_status != value)
                {
                    _lock_status = value;
                    this.RaisePropertyChanged("LOCK_STATUS");
                }
            }
        }
        /// <summary>
        /// 锁床位操作员(staff_dict.user_name)
        /// </summary>		
        private string _lock_operator;
        [DataMember]
        public string LOCK_OPERATOR
        {
            get { return _lock_operator; }
            set
            {
                if (_lock_operator != value)
                {
                    _lock_operator = value;
                    this.RaisePropertyChanged("LOCK_OPERATOR");
                }
            }
        }
        /// <summary>
        /// 空调类型
        /// </summary>		
        private string _aircondition_class;
        [DataMember]
        public string AIRCONDITION_CLASS
        {
            get { return _aircondition_class; }
            set
            {
                if (_aircondition_class != value)
                {
                    _aircondition_class = value;
                    this.RaisePropertyChanged("AIRCONDITION_CLASS");
                }
            }
        }
        /// <summary>
        /// 患者标识
        /// </summary>		
        private string _patient_id;
        [DataMember]
        public string PATIENT_ID
        {
            get { return _patient_id; }
            set
            {
                if (_patient_id != value)
                {
                    _patient_id = value;
                    this.RaisePropertyChanged("PATIENT_ID");
                }
            }
        }

    }
}
