﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net;
using System.IO;
using System.Text.RegularExpressions;
using System.Globalization;
using System.Collections;

namespace Tj_Update
{
    class FTPClient
    {
        //设置进度条
        public decimal Progress {get;set;}
        /// <summary>
        /// 下载缓存
        /// </summary>
        public int BufferSize { get; set; }
        /// <summary>
        /// 编码
        /// </summary>
        public string EncodingStr { get; set; }
        private bool isDebug= false;
        public FTPClient(bool _isDebug=false)
        {
            isDebug = _isDebug;
        }
        //文件信息
        public struct FileStruct
        {
            public string Flags;
            public string Owner;
            public string Group;
            public string FileSize;
            public bool IsDirectory;
            public DateTime CreateTime;
            public string Name;
        }

        public enum FileListStyle
        {
            UnixStyle,
            WindowsStyle,
            Unknown
        }

        /// <summary>
        /// 单个文件下载方法
        /// </summary>
        /// <param name="adss">保存文件的本地路径</param>
        /// <param name="ftpadss">下载文件的FTP路径</param>
        public void download(string adss, string ftpadss,string ftpuser,string ftppassword)
        {
            //FileMode常数确定如何打开或创建文件,指定操作系统应创建新文件。
            //FileMode.Create如果文件已存在，它将被改写
            try
            {
                FileStream outputStream = new FileStream(adss, FileMode.Create);
                FtpWebRequest downRequest = (FtpWebRequest)WebRequest.Create(new Uri(ftpadss));
                downRequest.Credentials = new NetworkCredential(ftpuser, ftppassword);
                //设置要发送到 FTP 服务器的命令
                downRequest.Method = WebRequestMethods.Ftp.DownloadFile;
                downRequest.KeepAlive = false;
                FtpWebResponse response = (FtpWebResponse)downRequest.GetResponse();
                Stream ftpStream = response.GetResponseStream();
                long cl = response.ContentLength;
                //int bufferSize = 1073741824;
                int readCount;
                byte[] buffer = new byte[BufferSize];
                readCount = ftpStream.Read(buffer, 0, BufferSize);
                while (readCount > 0)
                {
                    outputStream.Write(buffer, 0, readCount);
                    readCount = ftpStream.Read(buffer, 0, BufferSize);
                }
                long readCount1 = outputStream.Length;
                ftpStream.Close();
                outputStream.Close();
                response.Close();
                if (isDebug)
                {
                    Utility.LogFile.WriteLogAuto("文件大小：" + readCount1, "AppUpdate");
                }
            }
            catch(Exception ex)
            {
                Utility.LogFile.WriteLogAuto("下载异常：" + ex.Message, "AppUpdate");
            }
        }
 
        /// </summary>
        /// <param name="ftpads">FTP地址路径</param>
        /// <param name="name">我们所选择的文件或者文件夹名字</param>
        /// <param name="type">要发送到FTP服务器的命令</param>
        /// <returns></returns>
        public string[] ftp(string ftpads, string name, string type, string ftpuser, string ftppassword)
        {
            //遍历结构体
            FileStruct[] fileList = ftpList(ftpads,name,type,ftpuser,ftppassword);
            int arryNum = fileList.Length;
            string[] arryFile = new string[arryNum];
            for(int i=0;i<fileList.Length;i++)
            {
                if(!fileList[i].IsDirectory)
                {
                    arryFile[i] = fileList[i].Name;
                }
                else
                {
                    if (type == "LIST")
                    {
                        arryFile[i] = "<DIR> " + fileList[i].Name;
                    }
                    else if (type == "NLST")
                    {
                        arryFile[i] = fileList[i].Name;
                    }
                }
            }
            return arryFile;
            
            //WebResponse webresp = null;
            //StreamReader ftpFileListReader = null;
            //FtpWebRequest ftpRequest=null;
            //try
            //{
            //     ftpRequest = (FtpWebRequest)WebRequest.Create(new Uri(ftpads + "//" + name));
            //     ftpRequest.Credentials = new NetworkCredential(ftpuser, ftppassword);
            //     ftpRequest.Method = type;
            //     webresp = ftpRequest.GetResponse();
            //     ftpFileListReader = new StreamReader(webresp.GetResponseStream(), Encoding.Default);
            //}
            //catch(Exception ex)
            //{
            //    ex.ToString();
            //}
            //StringBuilder str = new StringBuilder();
            //string line=ftpFileListReader.ReadLine();
            //while (line != null)
            //{
            //    str.Append(line);
            //    str.Append("\n");
            //    line = ftpFileListReader.ReadLine();
            //}
            //string[] fen = str.ToString().Split('\n');
            //return fen;
        }

        /// </summary>
        /// <param name="ftpads">FTP地址路径</param>
        /// <param name="name">我们所选择的文件或者文件夹名字</param>
        /// <param name="type">要发送到FTP服务器的命令</param>
        /// <returns></returns>
        public string[] ftpFile(string ftpads, string name, string type, string ftpuser, string ftppassword)
        {
            //遍历结构体
            FileStruct[] fileList = ftpList(ftpads, name, type, ftpuser, ftppassword);
            int arryNum = fileList.Length;
            string[] arryFile = new string[arryNum];
            for (int i = 0; i < fileList.Length; i++)
            {
                //if (!fileList[i].IsDirectory)
                //{
                    arryFile[i] = fileList[i].Name;
                //}
                //else
                //{
                //    if (type == "LIST")
                //    {
                //        arryFile[i] = "<DIR> " + fileList[i].Name;
                //    }
                //    else if (type == "NLST")
                //    {
                //        arryFile[i] = fileList[i].Name;
                //    }
                //}
            }
            return arryFile;

            //WebResponse webresp = null;
            //StreamReader ftpFileListReader = null;
            //FtpWebRequest ftpRequest=null;
            //try
            //{
            //     ftpRequest = (FtpWebRequest)WebRequest.Create(new Uri(ftpads + "//" + name));
            //     ftpRequest.Credentials = new NetworkCredential(ftpuser, ftppassword);
            //     ftpRequest.Method = type;
            //     webresp = ftpRequest.GetResponse();
            //     ftpFileListReader = new StreamReader(webresp.GetResponseStream(), Encoding.Default);
            //}
            //catch(Exception ex)
            //{
            //    ex.ToString();
            //}
            //StringBuilder str = new StringBuilder();
            //string line=ftpFileListReader.ReadLine();
            //while (line != null)
            //{
            //    str.Append(line);
            //    str.Append("\n");
            //    line = ftpFileListReader.ReadLine();
            //}
            //string[] fen = str.ToString().Split('\n');
            //return fen;
        }

        /// </summary>
        /// <param name="ftpads">FTP地址路径</param>
        /// <param name="name">我们所选择的文件或者文件夹名字</param>
        /// <param name="type">要发送到FTP服务器的命令</param>
        /// <returns></returns>
        public FileStruct[] ftpList(string ftpads, string name, string type, string ftpuser, string ftppassword)
        {
            WebResponse webresp = null;
            StreamReader ftpFileListReader = null;
            FtpWebRequest ftpRequest = null;
            try
            {
                ftpRequest = (FtpWebRequest)WebRequest.Create(new Uri(ftpads + name));
                ftpRequest.Credentials = new NetworkCredential(ftpuser, ftppassword);
                ftpRequest.Method = type;
                //ftpRequest.Method = WebRequestMethods.Ftp.GetDateTimestamp;
                webresp = ftpRequest.GetResponse();
                 ftpFileListReader = new StreamReader(webresp.GetResponseStream(), Encoding.GetEncoding(EncodingStr));
                //ftpFileListReader = new StreamReader(webresp.GetResponseStream(), Encoding.Default);
            }
            catch (Exception ex)
            {
                ex.ToString();
            }
            //StringBuilder str = new StringBuilder();
            //string line = ftpFileListReader.ReadLine();
            //while (line != null)
            //{
            //    str.Append(line);
            //    str.Append("\n");
            //    line = ftpFileListReader.ReadLine();
            //}
            //string[] fen = str.ToString().Split('\n');
            //return fen;
            string Datastring = ftpFileListReader.ReadToEnd();
            FileStruct[] list = GetList(Datastring);
            return list;
        }


        /// <summary>
        /// 列出FTP服务器上面当前目录的所有文件和目录
        /// </summary>
        //public FileStruct[] ListFilesAndDirectories()
        //{
        //    Response = Open(this.Uri, WebRequestMethods.Ftp.ListDirectoryDetails);
        //    StreamReader stream = new StreamReader(Response.GetResponseStream(), Encoding.Default);
        //    string Datastring = stream.ReadToEnd();
        //    FileStruct[] list = GetList(Datastring);
        //    return list;
        //}

         //<summary>
         //判断文件列表的方式Window方式还是Unix方式
         //</summary>
         //<param name="recordList">文件信息列表</param>
        public FileListStyle GuessFileListStyle(string[] recordList)
        {
            foreach (string s in recordList)
            {
                if (s.Length > 10
                 && Regex.IsMatch(s.Substring(0, 10), "(-|d)(-|r)(-|w)(-|x)(-|r)(-|w)(-|x)(-|r)(-|w)(-|x)"))
                {
                    return FileListStyle.UnixStyle;
                }
                else if (s.Length > 8
                 && Regex.IsMatch(s.Substring(0, 8), "[0-9][0-9]-[0-9][0-9]-[0-9][0-9]"))
                {
                    return FileListStyle.WindowsStyle;
                }
            }
            return FileListStyle.Unknown;
        }
        /// <summary>
        /// 获取文件时间格式
        /// </summary>
        /// <param name="recordList"></param>
        /// <returns></returns>
        public FileListStyle GuessFileStyle(string record)
        {
            if (record.Length > 10
                 && Regex.IsMatch(record.Substring(0, 10), "(-|d)(-|r)(-|w)(-|x)(-|r)(-|w)(-|x)(-|r)(-|w)(-|x)"))
            {
                return FileListStyle.UnixStyle;
            }
            else if (record.Length > 8
             && Regex.IsMatch(record.Substring(0, 8), "[0-9][0-9]-[0-9][0-9]-[0-9][0-9]"))
            {
                return FileListStyle.WindowsStyle;
            }
            return FileListStyle.Unknown;
        }
        /// <summary>
        /// 获得文件和目录列表
        /// </summary>
        /// <param name="datastring">FTP返回的列表字符信息</param>
        private FileStruct[] GetList(string datastring)
        {
            List<FileStruct> myListArray = new List<FileStruct>();
            string[] dataRecords = datastring.Split('\n');
            FileListStyle _directoryListStyle;// = GuessFileListStyle(dataRecords);
            if (isDebug)
            {
                Utility.LogFile.WriteLogAuto("文件信息开始：" + datastring, "AppUpdate");
            }
            int fCount = 1;
            foreach (string s in dataRecords)
            {
                _directoryListStyle = GuessFileStyle(s);
                if (_directoryListStyle != FileListStyle.Unknown && s != "")
                {
                    FileStruct f = new FileStruct();
                    f.Name = "..";
                    if (isDebug)
                    {
                        Utility.LogFile.WriteLogAuto("文件" + fCount +":"+ s, "AppUpdate");
                    }
                    fCount++;
                    switch (_directoryListStyle)
                    {
                        case FileListStyle.UnixStyle:
                            f = ParseFileStructFromUnixStyleRecordNew(s);//ParseFileStructFromUnixStyleRecord(s);
                            break;
                        case FileListStyle.WindowsStyle:
                            f = ParseFileStructFromWindowsStyleRecord(s);
                            break;
                    }

                    if (!(f.Name == "." || f.Name == ".."))
                    {
                        myListArray.Add(f);
                    }
                }
            }
            return myListArray.ToArray();
        }

        /**/
        /// <summary>
        /// 从Windows格式中返回文件信息
        /// </summary>
        /// <param name="Record">文件信息</param>
        private FileStruct ParseFileStructFromWindowsStyleRecord(string Record)
        {
            FileStruct f = new FileStruct();
            string processstr = Record.Trim();
            string dateStr = processstr.Substring(0, 8);
            processstr = (processstr.Substring(8, processstr.Length - 8)).Trim();
            string timeStr = processstr.Substring(0, 7);
            processstr = (processstr.Substring(7, processstr.Length - 7)).Trim();
            DateTimeFormatInfo myDTFI = new CultureInfo("en-US", false).DateTimeFormat;
            myDTFI.ShortTimePattern = "t";
            f.CreateTime = DateTime.Parse(dateStr + " " + timeStr, myDTFI);
            if (processstr.Substring(0, 5) == "<DIR>")
            {
                f.IsDirectory = true;
                processstr = (processstr.Substring(5, processstr.Length - 5)).Trim();
            }
            else
            {
                //string[] strs = processstr.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);   // true);
                //第一个空格以后是名称
                processstr = processstr.Substring(processstr.IndexOf(" ") + 1);//strs[1];
                f.IsDirectory = false;
            }
            f.Name = processstr;
            return f;
        }

        /// <summary>
        /// 从Unix格式中返回文件信息
        /// </summary>
        /// <param name="Record">文件信息</param>
        #region old
        private FileStruct ParseFileStructFromUnixStyleRecord(string Record)
        {
            FileStruct f = new FileStruct();
            string processstr = Record.Trim();
            if (processstr.Length <= 33) return f;
            f.Flags = processstr.Substring(0, 10);
            f.IsDirectory = (f.Flags[0] == 'd');
            processstr = (processstr.Substring(11)).Trim();
            _cutSubstringFromStringWithTrim(ref processstr, ' ', 0);   //跳过一部分
            f.Owner = _cutSubstringFromStringWithTrim(ref processstr, ' ', 0);
            f.Group = _cutSubstringFromStringWithTrim(ref processstr, ' ', 0);
            _cutSubstringFromStringWithTrim(ref processstr, ' ', 0);   //跳过一部分
            string[] timestrs = processstr.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            string yearOrTime = timestrs[2];//processstr.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries)[2];
            int year = 1, month = 1;

            if (yearOrTime.IndexOf(":") >= 0)  //time
            {
                string month_str = timestrs[0].ToUpper();
                switch (month_str)
                {
                    case "JAN":
                        month = 1;
                        break;
                    case "FEB":
                        month = 2;
                        break;
                    case "MAR":
                        month = 3;
                        break;
                    case "APR":
                        month = 4;
                        break;
                    case "MAY":
                        month = 5;
                        break;
                    case "JUN":
                        month = 6;
                        break;
                    case "JUL":
                        month = 7;
                        break;
                    case "AUG":
                        month = 8;
                        break;
                    case "SEP":
                        month = 9;
                        break;
                    case "OCT":
                        month = 10;
                        break;
                    case "NOV":
                        month = 11;
                        break;
                    case "DEC":
                        month = 12;
                        break;
                }

                if (month > DateTime.Now.Month)
                {
                    year = DateTime.Now.Year - 1;
                }
                else
                {
                    year = DateTime.Now.Year;
                }
                processstr = processstr.Replace(yearOrTime, year.ToString());
                f.CreateTime = DateTime.Parse(_cutSubstringFromStringWithTrim(ref processstr, ' ', 8) + " " + yearOrTime);
            }
            else
            {
                f.CreateTime = DateTime.Parse(_cutSubstringFromStringWithTrim(ref processstr, ' ', 8) + " " + "0:00:00");// + " " + yearOrTime
            }

            f.Name = processstr;   //最后就是名称
            return f;
        }
        #endregion
        private FileStruct ParseFileStructFromUnixStyleRecordNew(string Record)
        {
            FileStruct f = new FileStruct();
            string processstr = Record.Trim();
            if (processstr.Length <= 33) return f;
            f.Flags = processstr.Substring(0, 10);
            f.IsDirectory = (f.Flags[0] == 'd');
            processstr = (processstr.Substring(11)).Trim();
            
            string[] fileInfos = processstr.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            if (fileInfos.Length < 8)
            {
                if (isDebug)
                    Utility.LogFile.WriteLogAuto("解析文件信息错误，信息数量不足8个："+ Record, "时间字符串");
                return f;
            }
            
            f.FileSize = fileInfos[3].ToString();
            f.Owner = fileInfos[1];
            f.Group = fileInfos[2];
            int month = 0, year;
            string yearOrTime = fileInfos[6].Trim();//processstr.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries)[2];
            string month_str = fileInfos[4].Trim().ToUpper();//月份
            string dateStr = fileInfos[5].Trim().PadLeft(2, '0');//日期
            switch (month_str)
            {
                case "JAN":
                    month = 1;
                    break;
                case "FEB":
                    month = 2;
                    break;
                case "MAR":
                    month = 3;
                    break;
                case "APR":
                    month = 4;
                    break;
                case "MAY":
                    month = 5;
                    break;
                case "JUN":
                    month = 6;
                    break;
                case "JUL":
                    month = 7;
                    break;
                case "AUG":
                    month = 8;
                    break;
                case "SEP":
                    month = 9;
                    break;
                case "OCT":
                    month = 10;
                    break;
                case "NOV":
                    month = 11;
                    break;
                case "DEC":
                    month = 12;
                    break;
            }
            string timeStr = "";
            //= yearStr + month.ToString("D2") + timeStr;//yyyymmdd
            if (month > DateTime.Now.Month)
            {
                year = DateTime.Now.Year - 1;
            }
            else
            {
                year = DateTime.Now.Year;
            }
            if (yearOrTime.IndexOf(":") >= 0)  //time 如果是时间
            {
                timeStr = year + "-" + month.ToString("D2") + "-" + dateStr + " " + yearOrTime.PadLeft(5, '0') + ":00";//yyyymmdd

            }
            else
            {
                timeStr = year + "-" + month.ToString("D2") + "-" + dateStr + " " + " 00:00:00";//yyyymmdd
            }
            f.CreateTime = Convert.ToDateTime(timeStr);
            f.Name = fileInfos[7];//processstr;   //最后就是名称
            return f;
        }

        /// <summary>
        /// 按照一定的规则进行字符串截取
        /// </summary>
        /// <param name="s">截取的字符串</param>
        /// <param name="c">查找的字符</param>
        /// <param name="startIndex">查找的位置</param>
        private string _cutSubstringFromStringWithTrim(ref string s, char c, int startIndex)
        {
            int pos1 = s.IndexOf(c, startIndex);
            string retString = s.Substring(0, pos1);
            s = (s.Substring(pos1)).Trim();
            return retString;
        }

        /// <summary>
        /// 下载方法KO
        /// </summary>
        /// <param name="ftpads">FTP路径</param>
        /// <param name="name">需要下载文件路径</param>
        /// <param name="Myads">保存的本地路径</param>
        public void downftp(string ftpads, string name, string Myads, string ftpuser, string ftppassword)
        {
            string downloadDir = Myads + name;
            string ftpdir = ftpads + name; 
            string[] fullname = ftp(ftpads, name, WebRequestMethods.Ftp.ListDirectoryDetails, ftpuser, ftppassword);
            int total = fullname.Length;
            int nowNum = 0;
            //判断是否为单个文件 
            //if (fullname.Length <= 2)
            //{
            //    if (fullname[fullname.Length - 1] == "")
            //    {
            //        download(downloadDir + "/" + fullname[0], ftpads + "//" + name + "/" + fullname[0], ftpuser, ftppassword);
            //    }
            //}
            //else
            //{
            string[] onlyname = ftpFile(ftpads, name, WebRequestMethods.Ftp.ListDirectoryDetails, ftpuser, ftppassword);
            if (!Directory.Exists(downloadDir))
            {
                Directory.CreateDirectory(downloadDir);
            }
            foreach (string names in fullname)
            {
                if (names == null) continue;
                //判断是否具有文件夹标识<DIR>
                if (names.Contains("<DIR>"))
                {
                    string olname = names.Split(new string[] { "<DIR>" }, 
                    StringSplitOptions.None)[1].Trim();
                    downftp(ftpdir + "//", olname, downloadDir + "\\", ftpuser, ftppassword);
                }
                else
                {
                    foreach (string onlynames in onlyname)
                    {
                        if (onlynames == "" || onlynames == " " || names == "")
                        {
                            break;
                        }
                        else
                        {
                            //if (names.Contains(onlynames))
                            if (names.Equals(onlynames))
                            {
                                download(downloadDir + "\\" + onlynames, ftpads + name + "//" + onlynames, ftpuser, ftppassword);
                                //break;
                            }
                        }
                    }
                }
                //nowNum++;
                //Progress = (nowNum / total) * 100;
            }
            //}
        }
    }
}
