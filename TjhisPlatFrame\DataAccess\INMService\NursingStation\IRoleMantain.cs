﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace INMService
{
    [ServiceContract]
    public interface IRoleMantain
    {
        /// <summary>
        /// 删除组菜单
        /// </summary>
        /// <param name="APPLICATION_CODE"></param>
        /// <param name="RIGHT_GROUP_CODE"></param>
        /// <returns></returns>
        [OperationContract]
        bool DeleteMenus(string APPLICATION_CODE, string RIGHT_GROUP_CODE);
    }
}
