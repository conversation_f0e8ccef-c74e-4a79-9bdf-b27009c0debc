<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Utility</name>
    </assembly>
    <members>
        <member name="M:Utility.ConfigHelper.GetConfigString(System.String)">
            <summary>
            取得appSettings里的值
            </summary>
            <param name="key">键</param>
            <returns>值</returns>
        </member>
        <member name="M:Utility.ConfigHelper.GetConfigBool(System.String)">
            <summary>
            得到AppSettings中的配置bool信息.
            Gets the config bool.
            </summary>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
        <member name="M:Utility.ConfigHelper.GetConfigDecimal(System.String)">
            <summary>
            得到AppSettings中的配置decimal信息.
            Gets the config decimal.
            </summary>
            <param name="Key">The key.</param>
            <returns></returns>
        </member>
        <member name="M:Utility.ConfigHelper.GetConfigInt(System.String)">
            <summary>
            得到AppSettings中的配置int信息.
            Gets the config int.
            </summary>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
        <member name="M:Utility.ConfigHelper.GetConfigurationList(System.String)">
            <summary>
            取得appSettings里的值列表
            </summary>
            <param name="filePath">配置文件路径</param>
            <returns>值列表</returns>
        </member>
        <member name="M:Utility.ConfigHelper.SetConfiguration(System.String,System.String,System.String)">
            <summary>
            设置appSetting的值
            </summary>
            <param name="key">键</param>
            <param name="value">值</param>
            <param name="filePath">App.config文件路径</param>
        </member>
        <member name="M:Utility.ConfigHelper.DecryptHIS(System.String)">
            <summary>
            
            </summary>
            <param name="EncryptPwd"></param>
            <returns></returns>
        </member>
        <member name="M:Utility.ConfigHelper.GetConfigConnectionStr">
            <summary>
            获取配置文件中的数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Utility.Gloobal.DecryptHIS(System.String)">
            <summary>
            
            </summary>
            <param name="EncryptPwd"></param>
            <returns></returns>
        </member>
        <member name="M:Utility.Gloobal.SetOracleClientConnection">
            <summary>
            设置初始化数据库新连接方式的全局连接
            </summary>
        </member>
        <member name="M:Utility.LogFile.IsHidden(System.String)">
            <summary>
            判断是否是隐藏文件
            </summary>
            <param name="path">文件路径</param>
            <returns></returns>
        </member>
        <member name="M:Utility.LogFile.CreateDirectory(System.String)">
            <summary>
            创建指定目录
            </summary>
            <param name="targetDir"></param>
        </member>
        <member name="M:Utility.LogFile.DeleteFile(System.String,System.String)">
            <summary>
            删除指定的文件
            </summary>
            <param name="targetDir"></param>
            <param name="fileName"></param>
        </member>
        <member name="M:Utility.LogFile.DeleteFiles(System.String,System.Boolean)">
            <summary>
            删除指定目录的所有文件和子目录
            </summary>
            <param name="targetDir">操作目录</param>
            <param name="delSubDir">如果为true,包含对子目录的操作</param>
        </member>
        <member name="M:Utility.LogFile.DeleteSubDirectory(System.String)">
            <summary>
            删除指定目录的所有子目录,不包括对当前目录文件的删除
            </summary>
            <param name="targetDir">目录路径</param>
        </member>
        <member name="M:Utility.LogFile.DeleteDirectory(System.String)">
            <summary>
            删除指定目录,包括当前目录和所有子目录和文件
            </summary>
            <param name="targetDir">目录路径</param>
        </member>
        <member name="M:Utility.LogFile.MoveFile(System.String,System.String,System.Boolean,System.String)">
            <summary>
            剪切指定目录下的文件
            </summary>
            <param name="sourceDir">原始目录</param>
            <param name="targetDir">目标目录</param>
            <param name="overWrite">如果为true,覆盖同名文件,否则不覆盖</param>
            <param name="fileName">需要移动的文件名</param>
        </member>
        <member name="M:Utility.LogFile.MoveFiles(System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            剪切指定目录的所有文件
            </summary>
            <param name="sourceDir">原始目录</param>
            <param name="targetDir">目标目录</param>
            <param name="overWrite">如果为true,覆盖同名文件,否则不覆盖</param>
            <param name="moveSubDir">如果为true,包含目录,否则不包含</param>
        </member>
        <member name="M:Utility.LogFile.CopyFiles(System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            复制指定目录的所有文件
            </summary>
            <param name="sourceDir">原始目录</param>
            <param name="targetDir">目标目录</param>
            <param name="overWrite">如果为true,覆盖同名文件,否则不覆盖</param>
            <param name="copySubDir">如果为true,包含目录,否则不包含</param>
        </member>
        <member name="M:Utility.LogFile.getChildFileInfo(System.String)">
            <summary>
            获取子目录文件信息
            </summary>
            <param name="targetDir">文件夹名称</param>
        </member>
        <member name="M:Utility.LogFile.GetFindTextLine(System.String,System.String,System.Int32)">
            <summary>
            查找字符串在原字符串中出现的行数
            </summary>
            <param name="content">主文本</param>
            <param name="findText">查询文本</param>
            <param name="startIndex">开始行</param>
            <returns></returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.#ctor">
            <summary>构造函数
            构造函数
            </summary>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.#ctor(System.String)">
            <summary>构造函数
            构造函数
            </summary>
            <param name="DBConnectString">ODP数据库连接串，格式如：Data Source=(DESCRIPTION =(ADDRESS_LIST =(ADDRESS = (PROTOCOL = TCP)(HOST = *************)(PORT = 1521)))(CONNECT_DATA =(SERVICE_NAME = develop)));User ID=system;Password=*******;</param>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.Finalize">
            <summary>析构函数
            析构函数
            </summary>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.Dispose">
            <summary>实现资源释IDisposable放接口
            实现资源释IDisposable放接口
            </summary>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.Dispose(System.Boolean)">
            <summary>为析构函数调用重写资源释IDisposable放接口Dispose()方法
            为析构函数调用重写资源释IDisposable放接口Dispose()方法
            </summary>
            <param name="Disposing"></param>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.OpenDB">
            <summary>打开数据库
            打开数据库
            </summary>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.CloseDB">
            <summary>关闭数据库
            关闭数据库
            </summary>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.GetSingle(System.String)">
            <summary>
            执行一条计算查询结果语句，返回查询结果（object）。
            </summary>
            <param name="SQLString">计算查询结果语句</param>
            <returns>查询结果（object）</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.SelectData(System.String,System.Collections.Generic.List{Oracle.ManagedDataAccess.Client.OracleParameter})">
            <summary>查询数据
            查询数据
            </summary>
            <param name="sqlString">SQL语句</param>
            <param name="Parameters">SQL对应参数列表</param>
            <returns>数据读取器</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.SelectData(System.String)">
            <summary>查询数据
            查询数据
            </summary>
            <param name="sqlString">SQL语句</param>
            <returns>数据库读取器</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.SelectDataSet(System.String)">
            <summary>获取数据集
            获取数据集
            查询SQL存在datatable的DisplayExpression属性
            查询SQL存在datatable的ExtendedProperties属性"SQL"关键字对应的值;
            </summary>
            <param name="sqlString">SQL语句</param>
            <returns>数据集，默认tablename为“table”</returns>
        </member>
        <!-- 对于成员“M:Utility.OracleODP.OracleBaseClass.SelectDataSet(System.String,System.Collections.Generic.List{Oracle.ManagedDataAccess.Client.OracleParameter})”忽略有格式错误的 XML 注释 -->
        <member name="M:Utility.OracleODP.OracleBaseClass.SelectDataSet(System.String[],System.String[])">
            <summary>获取数据集
            获取数据集
            查询SQL存在datatable的DisplayExpression属性
            查询SQL存在datatable的ExtendedProperties属性"SQL"关键字对应的值;
            </summary>
            <param name="sqlStrings">SQL语句数组</param>
            <param name="TableNames">表名数组（建议使用全称）</param>
            <returns>数据集</returns>
        </member>
        <!-- 对于成员“M:Utility.OracleODP.OracleBaseClass.SelectDataSet(System.String[],System.String[],System.Collections.Generic.List{Oracle.ManagedDataAccess.Client.OracleParameter}[])”忽略有格式错误的 XML 注释 -->
        <member name="M:Utility.OracleODP.OracleBaseClass.SelectDataSet(System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{Oracle.ManagedDataAccess.Client.OracleDbType},System.Collections.Generic.List{System.String})">
             <summary>
            传参获取数据集，适合blob类型字段做条件
             </summary>
             <param name="sqlString"></param>
             <param name="ParaNames"></param>
             <param name="ParaTypes"></param>
             <param name="ParaValues"></param>
             <returns></returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.ExecuteATransaction(System.String)">
            <summary>执行一个事务 
            执行一个事务 默认共享锁 IsolationLevel.ReadCommitted
            </summary>
            <param name="sqlString">SQL语句</param>
            <returns>执行事务影响行数</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.ExecuteATransaction(System.String,System.Collections.Generic.List{Oracle.ManagedDataAccess.Client.OracleParameter})">
            <summary>执行一个事物
            执行一个事物
            </summary>
            <param name="sqlString">SQL语句</param>
            <param name="Parameters">参数列表</param>
            <returns>执行事务影响行数</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.ExecuteATransaction(System.String,System.Data.IsolationLevel)">
            <summary>执行一个事务
            执行一个事务
            </summary>
            <param name="sqlString">SQL语句</param>
            <param name="_IsolationLevel">数据库锁级别</param>
            <returns>执行事务影响行数</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.ExecuteATransaction(System.String,System.Collections.Generic.List{Oracle.ManagedDataAccess.Client.OracleParameter},System.Data.IsolationLevel)">
            <summary>执行一个事务
            执行一个事务
            </summary>
            <param name="sqlString">SQL语句</param>
            <param name="Parameters">参数列表</param>
            <param name="_IsolationLevel">数据库锁类型</param>
            <returns>执行事务影响行数</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.ExecuteATransaction(System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{Oracle.ManagedDataAccess.Client.OracleDbType},System.Collections.Generic.List{System.String},System.Data.IsolationLevel)">
            <summary>
            执行一个事务
            </summary>
            <param name="sqlString">SQL语句</param>
            <param name="ParaNames">参数字段名列表</param>
            <param name="ParaTypes">参数类型列表</param>
            <param name="ParaValues">参数列表</param>
            <param name="_IsolationLevel">数据库锁类型</param>
            <returns>执行事务影响行数</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.BeginTransaction">
            <summary>开始事务，初始化事务
            开始事务，初始化事务 默认锁类型：IsolationLevel.ReadCommitted
            </summary>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.BeginTransaction(System.Data.IsolationLevel)">
            <summary>开始事务，初始化事务
            开始事务，初始化事务
            </summary>
            <param name="_IsolationLevel">数据库事务锁类型</param>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.CommitTransaction">
            <summary>提交事务
            提交事务
            </summary>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.RollbackTransaction">
            <summary>回滚事务
            回滚事务
            </summary>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.ExecuteTransaction(System.String)">
            <summary> 执行事务
            执行事务
            </summary>
            <param name="sqlString">SQL语句</param>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.ExecuteTransactionNOLimit(System.String)">
            <summary> 执行事务无限制行数
            执行事务无限制行数
            </summary>
            <param name="sqlString">SQL语句</param>
            <returns>string</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.ExecuteTransaction(System.String,System.Collections.Generic.List{Oracle.ManagedDataAccess.Client.OracleParameter})">
            <summary>执行事务
            执行事务
            </summary>
            <param name="sqlString">SQL语句</param>
            <param name="Parameters">参数列表</param>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.ExecuteTransaction(System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{Oracle.ManagedDataAccess.Client.OracleDbType},System.Collections.Generic.List{System.String})">
            <summary>
            执行事务
            </summary>
            <param name="sqlString">语句</param>
            <param name="ParaNames">参数名</param>
            <param name="ParaTypes"></param>
            <param name="ParaValues">参数值列表</param>
            <param name="_IsolationLevel">锁定等级</param>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.UpData_A_Data(System.Data.DataTable)">
            <summary>数据更新
            数据更新
            优先提取ExtendedProperties属性中的SQL与Parameters键
            然后是DisplayExpression，此时已默认为字符串SQL,不会再添加数据库参数
            最后是= "select *from " + dt.TableName;
            </summary>
            <param name="dt">数据表入参DataTable，必须符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在，以及tablename是否存在数据库查询上的意义</param>
            <returns>成功更新的行数</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.UpData_A_Data(System.Data.DataTable,System.String)">
            <summary>数据更新
            数据更新
            </summary>
            <param name="dt">数据表</param>
            <param name="sqlString">SQL语句</param>
            <returns>成功更新的行数</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.UpData_A_Data(System.Data.DataSet)">
            <summary>更新数据
            更新数据 
            优先提取ExtendedProperties属性中的SQL与Parameters键
            然后是DisplayExpression，此时已默认为字符串SQL,不会再添加数据库参数
            最后是= "select *from " + dt.TableName;
            </summary>
            <param name="ds">数据集包含的DataTable，必须符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在，以及tablename是否存在数据库查询上的意义</param>
            <returns>成功更新的行数</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.UpData_A_DataSets(System.Collections.ArrayList)">
            <summary>
            批量更新dataset
            </summary>
            <param name="dsList"></param>
            <returns></returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.UpDate_Data(System.Data.DataTable)">
            <summary>
            数据更新  事务中控制 2016-03-18 by 梁吉
            优先提取ExtendedProperties属性中的SQL与Parameters键
            然后是DisplayExpression，此时已默认为字符串SQL,不会再添加数据库参数
            最后是= "select *from " + dt.TableName;
            </summary>
            <param name="dt">数据表入参DataTable，必须符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在，以及tablename是否存在数据库查询上的意义</param>
            <returns>成功更新的行数</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.UpDate_Data(System.Data.DataTable,System.String)">
            <summary>数据更新
            数据更新  事务中控制 2016-03-18 by 梁吉
            </summary>
            <param name="dt">数据表</param>
            <param name="sqlString">SQL语句</param>
            <returns>成功更新的行数</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.UpDate_Data(System.Data.DataSet,System.String@)">
            <summary>更新数据
            更新数据   事务中控制 2016-03-18 by 梁吉
            优先提取ExtendedProperties属性中的SQL与Parameters键
            然后是DisplayExpression，此时已默认为字符串SQL,不会再添加数据库参数
            最后是= "select *from " + dt.TableName;
            </summary>
            <param name="ds">数据集包含的DataTable，必须符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在，以及tablename是否存在数据库查询上的意义</param>
            <returns>成功更新的行数</returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.UpDate_DataSets(System.Collections.ArrayList,System.String@,System.Collections.Generic.List{System.Data.DataRow})">
            <summary>
            批量更新dataset  事务中控制 2016-03-18 by 梁吉 20220412 modify by pwf
            </summary>
            <param name="dsList"></param>
            <returns></returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.UpDate_DsArray(System.Collections.ArrayList,System.String@,System.Collections.Generic.List{System.Data.DataRow})">
            <summary>
            更新ds数据集列表，返回更新的ds数量，如果异常回滚事务
            </summary>
            <param name="dsList"></param>
            <param name="drsChange"></param>
            <returns></returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.GetPubicProcedureDs(System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.String@,System.String@,System.Data.DataTable@)">
            <summary>
            获取存储过程里的挂号收费用
            </summary>
            <param name="procedureName"></param>
            <param name="paramKey"></param>
            <param name="paramValue"></param>
            <param name="para1">resultcode</param>
            <param name="para2">errmsg</param>
            <param name="para3"></param>
            <returns></returns>
            
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.GetOracleType(Oracle.ManagedDataAccess.Client.OracleDbType)">
            <summary>
            数据库类型转换 转换位客户端连接
            </summary>
            <param name="dbType">要转换的类型</param>
            <returns></returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.GetOracleDevType(Oracle.ManagedDataAccess.Client.OracleDbType)">
            <summary>
            数据库类型转换 转换为Devart连接
            </summary>
            <param name="dbType">要转换的类型</param>
            <returns></returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.UpDate_Data_Nur(System.Data.DataSet,System.String@)">
             <summary>
            护理专用
             </summary>
             <param name="ds"></param>
             <returns></returns>
        </member>
        <member name="M:Utility.OracleODP.OracleBaseClass.ExecuteSP(System.String,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            执行存储过程返回int
            </summary>
            <param name="strSPName">存储过程名</param>
            <param name="dicParam">参数字典</param>
            <returns>返回int</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Factory.CreateDataBaseOperation">
            <summary>
            根据配置，创建数据库连接模式
            </summary>
            <returns></returns>
        </member>
        <member name="T:Utility.TJ_DataBase_Operation_Interface">
            <summary>
            数据库操作标准
            </summary>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.OpenDB">
            <summary>打开数据库
            打开数据库
            </summary>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.CloseDB">
            <summary>关闭数据库
            关闭数据库
            </summary>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.SelectData(System.String,System.Collections.Generic.List{Oracle.ManagedDataAccess.Client.OracleParameter})">
            <summary>查询数据
            查询数据
            </summary>
            <param name="sqlString">SQL语句</param>
            <param name="Parameters">SQL对应参数列表</param>
            <returns>数据读取器</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.SelectData(System.String)">
            <summary>查询数据
            查询数据
            </summary>
            <param name="sqlString">SQL语句</param>
            <returns>数据库读取器</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.SelectDataSet(System.String)">
            <summary>获取数据集
            获取数据集
            查询SQL存在datatable的DisplayExpression属性
            查询SQL存在datatable的ExtendedProperties属性"SQL"关键字对应的值;
            </summary>
            <param name="sqlString">SQL语句</param>
            <returns>数据集，默认tablename为“table”</returns>
        </member>
        <!-- 对于成员“M:Utility.TJ_DataBase_Operation_Interface.SelectDataSet(System.String,System.Collections.Generic.List{Oracle.ManagedDataAccess.Client.OracleParameter})”忽略有格式错误的 XML 注释 -->
        <member name="M:Utility.TJ_DataBase_Operation_Interface.SelectDataSet(System.String[],System.String[])">
            <summary>获取数据集
            获取数据集
            查询SQL存在datatable的DisplayExpression属性
            查询SQL存在datatable的ExtendedProperties属性"SQL"关键字对应的值;
            </summary>
            <param name="sqlStrings">SQL语句数组</param>
            <param name="TableNames">表名数组（建议使用全称）</param>
            <returns>数据集</returns>
        </member>
        <!-- 对于成员“M:Utility.TJ_DataBase_Operation_Interface.SelectDataSet(System.String[],System.String[],System.Collections.Generic.List{Oracle.ManagedDataAccess.Client.OracleParameter}[])”忽略有格式错误的 XML 注释 -->
        <member name="M:Utility.TJ_DataBase_Operation_Interface.ExecuteATransaction(System.String)">
            <summary>执行一个事务 
            执行一个事务 默认共享锁 IsolationLevel.ReadCommitted
            </summary>
            <param name="sqlString">SQL语句</param>
            <returns>执行事务影响行数</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.ExecuteATransaction(System.String,System.Collections.Generic.List{Oracle.ManagedDataAccess.Client.OracleParameter})">
            <summary>执行一个事物
            执行一个事物
            </summary>
            <param name="sqlString">SQL语句</param>
            <param name="Parameters">参数列表</param>
            <returns>执行事务影响行数</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.ExecuteATransaction(System.String,System.Data.IsolationLevel)">
            <summary>执行一个事务
            执行一个事务
            </summary>
            <param name="sqlString">SQL语句</param>
            <param name="_IsolationLevel">数据库锁级别</param>
            <returns>执行事务影响行数</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.ExecuteATransaction(System.String,System.Collections.Generic.List{Oracle.ManagedDataAccess.Client.OracleParameter},System.Data.IsolationLevel)">
            <summary>执行一个事务
            执行一个事务
            </summary>
            <param name="sqlString">SQL语句</param>
            <param name="Parameters">参数列表</param>
            <param name="_IsolationLevel">数据库锁类型</param>
            <returns>执行事务影响行数</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.BeginTransaction">
            <summary>开始事务，初始化事务
            开始事务，初始化事务 默认锁类型：IsolationLevel.ReadCommitted
            </summary>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.BeginTransaction(System.Data.IsolationLevel)">
            <summary>开始事务，初始化事务
            开始事务，初始化事务
            </summary>
            <param name="_IsolationLevel">数据库事务锁类型</param>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.CommitTransaction">
            <summary>提交事务
            提交事务
            </summary>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.RollbackTransaction">
            <summary>回滚事务
            回滚事务
            </summary>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.ExecuteTransaction(System.String)">
            <summary> 执行事务
            执行事务
            </summary>
            <param name="sqlString">SQL语句</param>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.ExecuteTransaction(System.String,System.Collections.Generic.List{Oracle.ManagedDataAccess.Client.OracleParameter})">
            <summary>执行事务
            执行事务
            </summary>
            <param name="sqlString">SQL语句</param>
            <param name="Parameters">参数列表</param>
            <returns>true or false</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.UpData_A_Data(System.Data.DataTable)">
            <summary>数据更新
            数据更新
            优先提取ExtendedProperties属性中的SQL与Parameters键
            然后是DisplayExpression，此时已默认为字符串SQL,不会再添加数据库参数
            最后是= "select *from " + dt.TableName;
            </summary>
            <param name="dt">数据表入参DataTable，必须符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在，以及tablename是否存在数据库查询上的意义</param>
            <returns>成功更新的行数</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.UpData_A_Data(System.Data.DataTable,System.String)">
            <summary>数据更新
            数据更新
            </summary>
            <param name="dt">数据表</param>
            <param name="sqlString">SQL语句</param>
            <returns>成功更新的行数</returns>
        </member>
        <member name="M:Utility.TJ_DataBase_Operation_Interface.UpData_A_Data(System.Data.DataSet)">
            <summary>更新数据
            更新数据 
            优先提取ExtendedProperties属性中的SQL与Parameters键
            然后是DisplayExpression，此时已默认为字符串SQL,不会再添加数据库参数
            最后是= "select *from " + dt.TableName;
            </summary>
            <param name="ds">数据集包含的DataTable，必须符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在，以及tablename是否存在数据库查询上的意义</param>
            <returns>成功更新的行数</returns>
        </member>
        <member name="F:Utility.UntilityConstant.DataBaseConnectionMode">
            <summary>
            数据库连接方式，app参数设置KEY值
            </summary>
        </member>
        <member name="F:Utility.UntilityConstant.DataBaseConnectionDefaultMode">
            <summary>
            数据库连接方式,系统默认模式
            </summary>
        </member>
        <member name="F:Utility.UntilityConstant.DataConnectionString">
            <summary>
            数据库连接方式,连接字符串
            </summary>
        </member>
        <member name="F:Utility.UntilityConstant.Connection">
            <summary>
            OracleClient连接，方便青蛙跟踪
            </summary>
        </member>
        <member name="F:Utility.UntilityConstant.DBConnectionMode">
            <summary>
            数据库连接方式 <!--数据库连接模式 0 默认OracleClient 方便工具跟踪数据库操作，1 Oracle官方的高效数据库连接，工具不能跟踪数据库操作-->
            </summary>
        </member>
        <member name="F:Utility.UntilityConstant.LogIsWrite">
            <summary>
            是否写日志 1-写，其他不写
            </summary>
        </member>
        <member name="F:Utility.UntilityConstant.ConnectionMethod">
            <summary>
            连接方式，webservice还是客户端 0默认本地客户端，1是web服务
            </summary>
        </member>
    </members>
</doc>
