﻿/*****************************************************************************************************
 * 本代码版权归Quber所有，All Rights Reserved (C) 2018-2066
 *****************************************************************************************************
 * 所属域：
 * 登录用户：lions
 * CLR版本：4.0.30319.42000
 * 唯一标识：10871c68-2c81-4cee-9a99-86eb9cbe38e4
 * 机器名称：LAPTOP-TVHOR9G9
 * 联系人邮箱：
 *****************************************************************************************************
 * 命名空间：NursingPlatform
 * 类名称：MessagePublic
 * 文件名：MessagePublic
 * 创建年份：2018
 * 创建时间：2018/2/28 13:13:07
 * 创建人：lions
 * 创建说明：
 *****************************************************************************************************
 * 修改人：消息公共类，包括相关公共变量
 * 修改时间：
 * 修改说明：
*****************************************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace PlatCommon.Comm
{
    public class MessagePublic
    {
        /// <summary>
        /// 是否启用消息标志 1表示启用 否则不启用 数据库参数设置
        /// </summary>
        public static string MESSAGE_IS_ON { set;get;}
        /// <summary>
        /// 心跳时间间隔默认60s，数据库参数设置
        /// </summary>
        public static int MESSAGE_KEEPALIVE_TIME { set; get; }
        /// <summary>
        /// 客户端
        /// </summary>
        public PublicClass.TcpMessClient tcpMessClient { set; get; }

        //发送消息
        public static PublicClass.TcpMessClient tcpSendMessageClient { set; get; }

        public static string testActive { set; get; }
    }
}
