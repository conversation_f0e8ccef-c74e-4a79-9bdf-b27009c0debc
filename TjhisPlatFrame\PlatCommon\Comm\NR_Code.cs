﻿using DevExpress.XtraEditors;
using NM_Service.NMService;
using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.Data;
using System.Windows.Forms;

namespace PlatCommon.Comm
{
    public class NR_Code
    {
        ServerPublicClient service=new ServerPublicClient ();
        string ls_id = "";
        int ll_regist_curr_acct_no = 0;
        int ll_outp_curr_acct_no = 0;
        int ll_prep_rcpt_curr_no = 0;
        int ll_med_rcpt_curr_no = 0;
        int ll_prep_acct_curr_no = 0;
        int ll_med_acct_curr_no = 0;
        int ll_reg_num = 0;
        string ls_sqlerrtext="";
        
        string ls_serial_no = "";
        public int his_auto_id(string usid, ref string rid, ref string sql_retutn, int reg_num)
        {
            string tclinicdate ="",ClinicDate = "";
            GetClinicDate( ref tclinicdate, ref ClinicDate);
            string oldclinicdate = string.Empty;
            string sql = "select id , regist_curr_acct_no , outp_curr_acct_no , prep_rcpt_curr_no , med_rcpt_curr_no , prep_acct_curr_no , med_acct_curr_no,clinic_date,nvl(reg_num,1) as reg_num from comm.auto_setting where  id =  '" + SystemParm.LoginUser.ID + "'";
            DataSet ds = new ServerPublicClient().GetList(sql);
            if (ds.Tables[0].Rows.Count >0)
            {
                ls_id = ds.Tables[0].Rows[0]["id"].ToString();
                ll_regist_curr_acct_no = Convert.ToInt32(ds.Tables[0].Rows[0]["regist_curr_acct_no"]);
                ll_outp_curr_acct_no = Convert.ToInt32(ds.Tables[0].Rows[0]["outp_curr_acct_no"]);
                ll_prep_rcpt_curr_no = Convert.ToInt32(ds.Tables[0].Rows[0]["prep_rcpt_curr_no"]);
                ll_med_rcpt_curr_no = Convert.ToInt32(ds.Tables[0].Rows[0]["med_rcpt_curr_no"]);
                ll_prep_acct_curr_no = Convert.ToInt32(ds.Tables[0].Rows[0]["prep_acct_curr_no"]);
                ll_med_acct_curr_no = Convert.ToInt32(ds.Tables[0].Rows[0]["med_acct_curr_no"]);
                oldclinicdate = ds.Tables[0].Rows[0]["clinic_date"].ToString();
                ll_reg_num = Convert.ToInt32(ds.Tables[0].Rows[0]["reg_num"]);
            }
            if( ls_id == "" || ls_id==null) 
             {
                 ls_sqlerrtext = "Insert into  comm.auto_setting  ( id , regist_curr_acct_no , outp_curr_acct_no , prep_rcpt_curr_no , med_rcpt_curr_no , prep_acct_curr_no , med_acct_curr_no,clinic_date,reg_num) values ('" + SystemParm.LoginUser.ID + "' , 1,1,1,1,1,1,'" + tclinicdate + "',1) ";
                Dictionary<string, string> idc = new Dictionary<string, string>();
                idc.Add(ls_sqlerrtext, "保存auto_setting当前收款员账号生成规则失败！");
                string db2 = service.SaveTable(idc);
                if (string.IsNullOrEmpty(db2))
                {

                }
                else
                {
                    //XtraMessageBox.Show("插入表comm.auto_setting表错误", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    XtraMessageBox.Show(db2, "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return -1;
                }
             
             }
             string ls_temp = ""; 
             ls_serial_no = SystemParm.LoginUser.ID;
             switch (usid)
             {
                 case "REGIST_CURR_ACCT_NO": //挂号结账号
                     for (int i = 0; i < 5 - ll_regist_curr_acct_no.ToString().Length; i++)
                     {
                         ls_temp = ls_temp + "0";
                     }
                     rid = ls_serial_no + ls_temp + ll_regist_curr_acct_no.ToString();
                     ll_regist_curr_acct_no++;
                     break;
                 case "OUTP_CURR_ACCT_NO":// 结算结帐号	
                     for (int i = 0; i < 5 - ll_outp_curr_acct_no.ToString().Length; i++)
                     {
                         ls_temp = ls_temp + "0";
                     }
                     rid = ls_serial_no + ls_temp + ll_outp_curr_acct_no.ToString();
                     ll_outp_curr_acct_no++;
                     break;
                 case "PREP_RCPT_CURR_NO": // '预交金收据号'	
                     for (int i = 0; i < 5 - ll_prep_rcpt_curr_no.ToString().Length; i++)
                     {
                         ls_temp = ls_temp + "0";
                     }
                     rid = ls_serial_no + ls_temp + ll_prep_rcpt_curr_no.ToString();
                     ll_prep_rcpt_curr_no++;
                     break;
                 case "MED_RCPT_CURR_NO":// '医疗收据号'
                     for (int i = 0; i < 5 - ll_med_rcpt_curr_no.ToString().Length; i++)
                     {
                         ls_temp = ls_temp + "0";
                     }
                     rid = ls_serial_no + ls_temp + ll_med_rcpt_curr_no.ToString();
                     ll_med_rcpt_curr_no++;
                     break;
                 case "PREP_ACCT_CURR_NO":// '预交金结帐号'
                     for (int i = 0; i < 5 - ll_prep_acct_curr_no.ToString().Length; i++)
                     {
                         ls_temp = ls_temp + "0";
                     }
                     rid = ls_serial_no + ls_temp + ll_prep_acct_curr_no.ToString();
                     ll_prep_acct_curr_no++;
                     break;
                 case "MED_ACCT_CURR_NO":// '医疗费结帐号'
                     for (int i = 0; i < 5 - ll_med_acct_curr_no.ToString().Length; i++)
                     {
                         ls_temp = ls_temp + "0";
                     }
                     rid = ls_serial_no + ls_temp + ll_med_acct_curr_no.ToString();
                    try
                    {
                        string sqla = " select count(*) from prepay_acct where acct_no = '" + rid + "'";
                        string taaaa = new NM_Service.NMService.ServerPublicClient().GetSingleValue(sqla);
                        if (!taaaa.Equals("0"))
                        {
                            ll_med_acct_curr_no = ll_med_acct_curr_no + 2;
                            rid = ls_serial_no + ls_temp + ll_med_acct_curr_no.ToString();                                                
                        }
                    }
                    catch
                    { }
                     ll_med_acct_curr_no++;
                     break;
                 case "REG_NUM"://挂号数量

                     ll_reg_num = ll_reg_num + reg_num;
                     for (int i = 0; i < 5 - (ll_reg_num-1).ToString().Length; i++)
                     { 
                         ls_temp = ls_temp + "0";
                     }
                     string  ls_tempid = "";
                     for (int j = 0; j < 8 - ls_serial_no.ToString().Length; j++)
                     {
                         ls_tempid = ls_tempid + "0";
                     }
                     //6位年月日 +5位人员ID +5位序号
                     //if (tclinicdate.Equals(oldclinicdate)) //如果当天系统日期与表里日期一致
                     //{
                     //    rid = tclinicdate + ls_tempid + ls_serial_no + ls_temp + (ll_reg_num - 1).ToString();
                     //}
                     //else //是当天日期第一笔挂号
                     //{
                         //每天第一笔的初始值是clinic_master 中以前预约今天的sum数量+1;
                         string sqlcm = "select nvl(count(*),0) from clinic_master where VISIT_DATE =TO_DATE('" + ClinicDate + "','YYYY-MM-DD') and operator ='" + ls_serial_no + "'";
                         DataSet dscm = new ServerPublicClient().GetList(sqlcm);
                         if (dscm.Tables[0].Rows.Count > 0)
                         {
                             ll_reg_num = Convert.ToInt32(dscm.Tables[0].Rows[0][0]) + 1; 
                         }
                         ll_reg_num = ll_reg_num + reg_num;
                         rid = tclinicdate + ls_tempid + ls_serial_no + ls_temp + (ll_reg_num - 1).ToString();
                     //}
                     //ll_reg_num++;
                     break;
                 default:
                     XtraMessageBox.Show("传入参数错误", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                 
                     break;
             }
             string sqls = "Update COMM.AUTO_SETTING Set  REGIST_CURR_ACCT_NO= '" + ll_regist_curr_acct_no + "',OUTP_CURR_ACCT_NO= '" + ll_outp_curr_acct_no + "' ,PREP_RCPT_CURR_NO ='" + ll_prep_rcpt_curr_no + "' ,MED_RCPT_CURR_NO = '" + ll_med_rcpt_curr_no + "' ,PREP_ACCT_CURR_NO =";
             sqls += "'" + ll_prep_acct_curr_no + "'   ,MED_ACCT_CURR_NO= '" + ll_med_acct_curr_no + "', CLINIC_DATE = '" + tclinicdate + "',REG_NUM ='" + ll_reg_num + "' Where id = '" + ls_serial_no + "'  ";
            sql_retutn = sqls;


            return 0;
        }
        /// <summary>
        ///  获取挂号当天日期
        /// </summary>
        /// <param name="age"></param>
        /// <returns></returns>
        private void GetClinicDate(ref string tclinicdate,ref string ClinicDate)
        {
            string sql = "SELECT TO_CHAR(SYSDATE,'YYMMDD'),TO_CHAR(SYSDATE,'YYYY-MM-DD')  FROM DUAL";
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            tclinicdate = dt.Rows[0][0].ToString();
            ClinicDate = dt.Rows[0][1].ToString();
        }
    }
}
