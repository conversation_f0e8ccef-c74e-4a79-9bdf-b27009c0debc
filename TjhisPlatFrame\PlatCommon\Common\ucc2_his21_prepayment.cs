﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Data;
using System.Collections;
using NM_Service.NMService;
using System.Xml;
using PlatCommon.SysBase;
using DevExpress.XtraEditors;

namespace PlatCommon.Common
{
    public class ucc2_his21_prepayment
    {
        /// <summary>
        /// 描述:  结算使用预交金时，预交金记录处理
        /// 参数: 
        ///     [value] datastore ads_pre 预交金使用情况的数据存储
        ///     [value] string as_settle_no 结算号
        ///     [value] deciaml adc_payment_amount 自付金额
        ///     [value] string  as_operator_no 操作员编号
        ///     [value] boolean ab_refund 多于预交金是否直接退还病人
        ///     ref DataSet lds_pre 返回给结算界面的DataSet 用于统一保存
        ///     string file 文件名,改为外部传入例如结算传入的是string file = @"Config\\ibilling.xml";
        /// </summary>
        public static int uf_settle_used(string as_pat_id, string al_visit_id, DataSet ads_pre, string as_settle_no, decimal adc_payment_amount, string as_operator_no, Boolean ab_refund, ref DataSet lds_pre, string file)
        {
            try
            {
                string ls_rcpt_no, gs_winno, ls_used, ls_new_rcpt_no;
                // Int32 ll_rcpt_no;
                int ii;
                decimal ldc_payment_amount;
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                DataSet ds;
                StringBuilder sb = new StringBuilder();
                string ls_old_rcpt_no;
                //XmlDocument doc = new XmlDocument();
                //doc.Load(file);
                //XmlElement root = doc.DocumentElement;   //获取根节点  
                //XmlNodeList nodes = root.GetElementsByTagName("Response"); //获取Person子节点集合 
                //gs_winno = "";
                //ls_rcpt_no = "";
                //foreach (XmlNode node in nodes)
                //{
                //    gs_winno = ((XmlElement)node).GetElementsByTagName("WIN_NO")[0].InnerText;
                //    ls_rcpt_no = ((XmlElement)node).GetElementsByTagName("PREP_RCPT_CURR_NO")[0].InnerText;
                //}
                //if (gs_winno.Length == 1) gs_winno = "0" + gs_winno;

                sb.Append(" select * from inpbill.prepayment_rcpt ");
                sb.Append(" where patient_id= '" + as_pat_id + "' and  visit_id =" + al_visit_id + " and transact_type<>'作废' and his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'");
                ds = spc.GetDataBySql(sb.ToString());
                lds_pre = ds; //返回给结算界面用于统一保存
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    //if (string.IsNullOrEmpty(ls_rcpt_no))
                    //{
                    //    ll_rcpt_no = 1;
                    //}
                    //else
                    //{
                    //    ll_rcpt_no = Convert.ToInt32(ls_rcpt_no);
                    //}
                    //自付金额
                    ldc_payment_amount = adc_payment_amount;
                    ii = 0;
                    for (int i = 0; i < ads_pre.Tables[0].Rows.Count; i++)
                    {
                        ls_used = ads_pre.Tables[0].Rows[i]["USED_FLAG"].ToString();
                        if (ls_used.Equals("1"))
                        {
                            //当预交金已经够用，并且不找回给病人时，不再使用剩余的预交金记录
                            if (ldc_payment_amount <= 0)
                            {
                                if (!ab_refund)
                                {
                                    continue;
                                }
                            }
                            //使用的预交金记"结算"
                            ls_old_rcpt_no = ads_pre.Tables[0].Rows[i]["RCPT_NO"].ToString();
                            //ls_new_rcpt_no = gs_winno + "000000".Substring(0, 6 - (ll_rcpt_no).ToString().Length) + ll_rcpt_no.ToString();
                            ls_new_rcpt_no = "";
                            if (uf_get_pre_rcpt_no(ref ls_new_rcpt_no) != 0)
                            {
                                DevExpress.XtraEditors.XtraMessageBox.Show("取预交金当前收据号失败auto_setting_id!");
                                return -1;
                            }

                            //ldc_payment_amount逐减
                            if (uf_set_settle(ls_old_rcpt_no, ls_new_rcpt_no, as_settle_no, ref ldc_payment_amount, ref lds_pre, as_operator_no, ab_refund, file) != 0)
                            {
                                return -1;
                            }
                            // ll_rcpt_no++;
                            ii++;
                        }
                    } //for (int i = 0; i < ads_pre.Tables[0].Rows.Count; i++)
                    ds.Dispose();
                    ////预交金收据号保存
                    //uf_set_next_rcptno(ii, file);
                }
                return 0;
            }
            catch (Exception e)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(e.Message);
                return -1;
            }
        }
        /// <summary>
        /// 描述:  改造的取参数表收据号,原来取INI的屏蔽;
        /// </summary> 
        public static int uf_get_pre_rcpt_no(ref string med_rcpt_no)
        {
            try
            {
                string ll_med_no;
                string li_len;
                string ls_type_name;
                string ls_inp_start_value;
                string ls_id_start_value;
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                Dictionary<string, string> idc_pre = new Dictionary<string, string>();
                DataSet ds;
                StringBuilder sb1 = new StringBuilder();
                sb1.Append(" select parameter_value from app_configer_parameter  ");
                sb1.Append(" where app_name ='IBILLING' and parameter_name ='PREP_SERIAL_NO'  and his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'");
                ds = spc.GetDataBySql(sb1.ToString());
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    ls_type_name = ds.Tables[0].Rows[0][0].ToString();
                }
                else
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("查找预交金结算参数PREP_SERIAL_NO失败", "提示");
                    return -1;
                }
                ds.Dispose();
                StringBuilder sb2 = new StringBuilder();
                sb2.Append(" select id_currently_value,id_length,inp_start_value,id_start_value from auto_setting_id   ");
                sb2.Append(" where TYPE_NAME = '" + ls_type_name + "' and his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'");
                ds = spc.GetDataBySql(sb2.ToString());
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    ll_med_no = ds.Tables[0].Rows[0][0].ToString();
                    li_len = ds.Tables[0].Rows[0][1].ToString();
                    ls_inp_start_value = ds.Tables[0].Rows[0][2].ToString();
                    ls_id_start_value = ds.Tables[0].Rows[0][3].ToString();
                }
                else
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("取‘预交金收据号’失败~r~n请查看表AUTO_SETTING_ID中是否有" + ls_type_name + "的记录。", "提示");
                    return -1;
                }
                ds.Dispose();
                if (string.IsNullOrEmpty(ll_med_no))
                {
                    ll_med_no = "0";
                }
                Int32 li_med_no, lii_len;
                li_med_no = Int32.Parse(ll_med_no);
                li_med_no++;
                if (string.IsNullOrEmpty(li_len))
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("取‘预交金收据号长度失败id_length’失败~r~n请查看表AUTO_SETTING_ID中是否有" + ls_type_name + "的记录。", "提示");
                    return -1;
                }
                lii_len = Int32.Parse(li_len);
                med_rcpt_no = ls_id_start_value + li_med_no.ToString().PadLeft(lii_len , '0'); 
                StringBuilder sb3 = new StringBuilder();   //" + li_med_no.ToString() + " 
                sb3.Append(" update auto_setting_id set id_currently_value= id_currently_value + 1   where type_name= '" + ls_type_name + "' and his_unit_code ='"+SystemParm.HisUnitCode+"'");
                idc_pre.Add(sb3.ToString(), "更新auto_setting_id收据号失败!");
                string rev = new NM_Service.NMService.ServerPublicClient().SaveTable(idc_pre);
                if (!string.IsNullOrEmpty(rev))
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("保存失败!" + rev, "错误信息");
                    return -1;
                }
                return 0;
            }
            catch (Exception e)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(e.Message);
                return -1;
            }
        }

        /// <summary>
        /// 描述: 使用预交金结算时，预交金表的处理
        /// 参数: 
        ///     [value]  string as_rcpt_no 使用的预交金收据号
        ///     [value]  dec adc_settle_amount  结算金额
        /// </summary>
        public static int uf_set_settle(string as_rcpt_no, string as_new_rcptno, string as_settle_no, ref decimal adc_settle_amount, ref DataSet ads_pre, string as_operator_no, Boolean ab_refund, string file)
        {
            try
            {
                decimal ldc_pre_amount = 0;
                string as_pat_id = "";
                string as_v_id = "0";
                DataRow[] drs = ads_pre.Tables[0].Select("rcpt_no ='" + as_rcpt_no + "'");
                if (drs.Length > 0)
                {
                    foreach (DataRow dr in drs)
                    {
                        as_pat_id = dr["patient_id"].ToString();
                        as_v_id = dr["visit_id"].ToString();
                        ldc_pre_amount = Convert.ToDecimal(dr["amount"].ToString());  //取预交金金额
                        //去掉已经使用的部分
                        for (int i = 0; i < ads_pre.Tables[0].Rows.Count; i++)
                        {
                            if (as_rcpt_no.Equals(ads_pre.Tables[0].Rows[i]["used_rcpt_no"].ToString()))
                            {
                                ldc_pre_amount += Convert.ToDecimal(ads_pre.Tables[0].Rows[i]["amount"].ToString());
                            }
                        }
                        dr["used_flag"] = 1;

                        DataRow drx = ads_pre.Tables[0].NewRow();
                        //for (int j = 0; j < ads_pre.Tables[0].Columns.Count; j++)
                        //{
                        //    drx[j] = dr[j];
                        //}
                        drx.ItemArray = dr.ItemArray;
                        drx["rcpt_no"] = as_new_rcptno;
                        drx["ACCT_NO"] = "";//20190604
                        //--设置金额
                        if (ldc_pre_amount <= adc_settle_amount)
                        {
                            drx["amount"] = (0 - ldc_pre_amount).ToString();
                        }
                        else //没有全部使用,记结算额
                        {
                            if (ab_refund)//找给病人时，该预交金全用
                            {
                                drx["amount"] = (0 - ldc_pre_amount).ToString();
                            }
                            else
                            {
                                drx["amount"] = (0 - adc_settle_amount).ToString();//不找给，记此时的结算额
                            }
                        }
                        drx["transact_type"] = "结算"; //--设置操作类型
                        drx["transact_date"] = new NM_Service.NMService.ServerPublicClient().GetSysDate().ToString("yyyy-MM-dd HH:mm:ss");	//--设置时间
                        drx["operator_no"] = as_operator_no;//设置操作员
                        drx["settled_no"] = as_settle_no;//设置结算号
                        //--设置使用的收据号
                        drx["used_rcpt_no"] = as_rcpt_no;
                        drx["used_flag"] = 1;
                        ads_pre.Tables[0].Rows.Add(drx);
                        //剩余的金额
                        adc_settle_amount = adc_settle_amount - ldc_pre_amount;
                    }
                } // if (drs.Length > 0)
                else
                {
                    return -1;
                }
                return 0;
            }
            catch (Exception e)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(e.Message);
                return -1;
            }
        }
        /// <summary>
        ///  描述: 根据传入的收据数量，直接设置收据号，适用于批量写收据的情况
        /// 参数:  [value] integer ai_number 已经使用过的收据数量
        /// </summary>
        public static int uf_set_next_rcptno(Int32 ai_number, string file)
        {
            try
            {
                string ls_rcpt_no;
                ls_rcpt_no = "0";
                if (ai_number > 0)
                {
                    //string file = @"Config\\ibilling.xml";
                    XmlDocument doc = new XmlDocument();
                    doc.Load(file);
                    XmlElement root = doc.DocumentElement;   //获取根节点  
                    XmlNodeList nodes = root.GetElementsByTagName("Response"); //获取Person子节点集合 
                    foreach (XmlNode node in nodes)
                    {
                        ls_rcpt_no = ((XmlElement)node).GetElementsByTagName("PREP_RCPT_CURR_NO")[0].InnerText;
                    }
                    if (!PlatCommon.SysBase.ValidateValue.IsNumber(ls_rcpt_no))
                    {
                        DevExpress.XtraEditors.XtraMessageBox.Show("ibilling.xml文件的PREP_RCPT_CURR_NO不符合规范", "错误信息");
                    }
                    ls_rcpt_no = (Convert.ToInt32(ls_rcpt_no) + ai_number).ToString();
                    foreach (XmlNode node in nodes)
                    {
                        XmlElement ele = (XmlElement)node;
                        XmlElement nameEle = (XmlElement)ele.GetElementsByTagName("PREP_RCPT_CURR_NO")[0];
                        nameEle.InnerText = ls_rcpt_no;
                    }
                    doc.Save(file);
                    //                   IbillingConfigHelper.UpdateAppConfig("PREP_RCPT_CURR_NO", ls_rcpt_no);
                }
                return 0;
            }
            catch (Exception e)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(e.Message);
                return -1;
            }
        }
        /// <summary>
        /// 功    能:根据用户提取当前为“使用”状态的发票号码
        /// 参数:[out] (string)as_fp_no[in]  (string)as_user       用户编号 [in]  (int)   ai_flag       发票类型，对应flag字段
        /// 返 回 值: (string)  0 发票已经用完; -1 取下一个发票号码信息失败  1 获得下一个发票号码   
        /// </summary> 
        public static int uf_get_currentno(ref string as_fp_no, string as_operator_no, string as_flag, ref Int64 il_curr_no, ref Int64 il_max_no)
        {
            try
            {
                il_curr_no = 0;
                il_max_no = 0;
                Int64 il_min_no = 0;
                Int32 il_sequence_length = 0;
                string is_prefix = "";  //前缀
                string is_suffix = "";  //后缀
                DataSet ds;
                string ls_rtn;
                StringBuilder sb = new StringBuilder();
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                string ls_flag = "";
                switch (as_flag)
                {
                    case "1":
                        ls_flag = "住院";
                        break;
                    case "2":
                        ls_flag = "门诊";
                        break;
                    case "3":
                        ls_flag = "挂号";
                        break;
                    case "4":
                        ls_flag = "预交金";
                        break;
                }
                sb.Append(" select nvl(curr_no,0),nvl(SEQUENCE_LENGTH,0),prefix,suffix,nvl(max_no,0),nvl(min_no,0)  ");
                sb.Append(" from invoice_manage_rec  where operator_no  =  '" + as_operator_no + "' and");
                sb.Append(" flag = '" + as_flag + "' and provide_date  =  ( select min(provide_date) from invoice_manage_rec ");
                sb.Append(" where operator_no  ='" + as_operator_no + "' and	flag ='" + as_flag + "' and using_status ='2') ");
                ds = spc.GetDataBySql(sb.ToString());
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    il_curr_no = Int64.Parse(ds.Tables[0].Rows[0][0].ToString());
                    il_sequence_length = Int32.Parse(ds.Tables[0].Rows[0][1].ToString());
                    is_prefix = ds.Tables[0].Rows[0][2].ToString();
                    is_suffix = ds.Tables[0].Rows[0][3].ToString();
                    il_max_no = Int64.Parse(ds.Tables[0].Rows[0][4].ToString());
                    il_min_no = Int64.Parse(ds.Tables[0].Rows[0][5].ToString());
                    ls_rtn = il_curr_no.ToString();
                    if (ls_rtn.Length < il_sequence_length)
                    {
                        ls_rtn = ls_rtn.PadLeft(il_sequence_length, '0');
                    }
                    ls_rtn = is_prefix + ls_rtn + is_suffix;
                    as_fp_no = ls_rtn;
                }
                else
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("你领取的" + ls_flag + "发票已经用完。");
                    return -1;
                }

                return 0;
            }
            catch (Exception e)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(e.Message);
                return -1;
            }
        }
        public static int uf_get_currentno(ref string as_fp_no, string as_operator_no, string as_flag, ref Int32 il_curr_no, ref Int32 il_max_no)
        {
            try
            {
                il_curr_no = 0;
                il_max_no = 0;
                Int32 il_min_no = 0;
                Int32 il_sequence_length = 0;
                string is_prefix = "";  //前缀
                string is_suffix = "";  //后缀
                DataSet ds;
                string ls_rtn;
                StringBuilder sb = new StringBuilder();
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                string ls_flag = "";
                switch (as_flag)
                {
                    case "1":
                        ls_flag = "住院";
                        break;
                    case "2":
                        ls_flag = "门诊";
                        break;
                    case "3":
                        ls_flag = "挂号";
                        break;
                    case "4":
                        ls_flag = "预交金";
                        break;
                }
                sb.Append(" select nvl(curr_no,0),nvl(SEQUENCE_LENGTH,0),prefix,suffix,nvl(max_no,0),nvl(min_no,0)  ");
                sb.Append(" from invoice_manage_rec  where operator_no  =  '" + as_operator_no + "' and");
                sb.Append(" flag = '" + as_flag + "' and provide_date  =  ( select min(provide_date) from invoice_manage_rec ");
                sb.Append(" where operator_no  ='" + as_operator_no + "' and	flag ='" + as_flag + "' and using_status ='2') ");
                ds = spc.GetDataBySql(sb.ToString());
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    il_curr_no = Int32.Parse(ds.Tables[0].Rows[0][0].ToString());
                    il_sequence_length = Int32.Parse(ds.Tables[0].Rows[0][1].ToString());
                    is_prefix = ds.Tables[0].Rows[0][2].ToString();
                    is_suffix = ds.Tables[0].Rows[0][3].ToString();
                    il_max_no = Int32.Parse(ds.Tables[0].Rows[0][4].ToString());
                    il_min_no = Int32.Parse(ds.Tables[0].Rows[0][5].ToString());
                    ls_rtn = il_curr_no.ToString();
                    if (ls_rtn.Length < il_sequence_length)
                    {
                        ls_rtn = ls_rtn.PadLeft(il_sequence_length, '0');
                    }
                    ls_rtn = is_prefix + ls_rtn + is_suffix;
                    as_fp_no = ls_rtn;
                }
                else
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("你领取的" + ls_flag + "发票已经用完。");
                    return -1;
                }

                return 0;
            }
            catch (Exception e)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(e.Message);
                return -1;
            }
        }
        /// <summary>
        /// 功    能:根据用户提取当前为“使用”状态的发票号码
        /// 参数:[out] (string)as_fp_no[in]  (string)as_user       用户编号 [in]  (int)   ai_flag       发票类型，对应flag字段
        /// 返 回 值: (string)  0 发票已经用完; -1 取下一个发票号码信息失败  1 获得下一个发票号码  [in]  (int)   ai_number     取发票量  
        /// </summary> 
        public static int uf_get_next(ref string as_fp_no, string as_operator_no, string as_flag, Int32 ai_number)
        {
            try
            {
                Int32 il_curr_no = 0;
                Int32 il_max_no = 0;
                Int32 il_min_no = 0;
                Int32 il_sequence_length = 0;
                string is_prefix = "";  //前缀
                string is_suffix = "";  //后缀
                DataSet ds;
                string ls_rtn;
                StringBuilder sb = new StringBuilder();
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                string ls_flag = "";
                switch (as_flag)
                {
                    case "1":
                        ls_flag = "住院";
                        break;
                    case "2":
                        ls_flag = "门诊";
                        break;
                    case "3":
                        ls_flag = "挂号";
                        break;
                    case "4":
                        ls_flag = "预交金";
                        break;
                }
                sb.Append(" select nvl(curr_no,0),nvl(SEQUENCE_LENGTH,0),prefix,suffix,nvl(max_no,0),nvl(min_no,0)  ");
                sb.Append(" from invoice_manage_rec  where operator_no  =  '" + as_operator_no + "' and");
                sb.Append(" flag = '" + as_flag + "' and provide_date  =  ( select min(provide_date) from invoice_manage_rec ");
                sb.Append(" where operator_no  ='" + as_operator_no + " and	flag ='" + as_flag + "' and using_status ='2') ");
                ds = spc.GetDataBySql(sb.ToString());
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    il_curr_no = Int32.Parse(ds.Tables[0].Rows[0][0].ToString());
                    il_sequence_length = Int32.Parse(ds.Tables[0].Rows[0][1].ToString());
                    is_prefix = ds.Tables[0].Rows[0][2].ToString();
                    is_suffix = ds.Tables[0].Rows[0][3].ToString();
                    il_max_no = Int32.Parse(ds.Tables[0].Rows[0][4].ToString());
                    il_min_no = Int32.Parse(ds.Tables[0].Rows[0][5].ToString());
                    ls_rtn = (il_curr_no + ai_number).ToString();
                    if ((il_curr_no + ai_number) > il_max_no)
                    {
                        DevExpress.XtraEditors.XtraMessageBox.Show("你当前的发票已经不够本次使用。");
                        return -1;
                    }

                    if (ls_rtn.Length < il_sequence_length)
                    {
                        ls_rtn = ls_rtn.PadLeft(il_sequence_length - ls_rtn.Length, '0');
                    }
                    ls_rtn = is_prefix + ls_rtn + is_suffix;
                    as_fp_no = ls_rtn;
                }
                else
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("你领取的" + ls_flag + "发票已经用完。");
                    return -1;
                }

                return 0;
            }
            catch (Exception e)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(e.Message);
                return -1;
            }
        }
        /// <summary>
        /// 取预交金余额(同时根据配置：是否加上透支额)和未结费用金额
        /// </summary>
        /// <param name="patientID">病人ID</param>
        /// <param name="visitID">住院次数</param>
        /// <param name="adc_amount">余额</param>
        /// <param name="adc_charges">未结算的实收费用和</param>
        /// <returns></returns>
        public static int uf_get_overdrawn(string patientID, string visitID, ref decimal adc_amount, ref decimal adc_charges)
        {
            decimal ldapprovemoney = 0, ldprepayment, ldcharges;
            ldprepayment = uf_inp_getbalance(patientID, visitID);
            //未结算的实收费用和
            string sql = "select nvl(sum(charges),0) from inp_bill_detail where patient_id='" + patientID + "' and visit_id=" + visitID + " and rcpt_no is null";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            DataTable dt = spc.GetList(sql).Tables[0];
            ldcharges = decimal.Parse(dt.Rows[0][0].ToString());
            string ls_config_value = SystemParm.GetParameterValue("PAYPERMENT_BALANCE", "*", "*", "*", SystemParm.HisUnitCode);
            if (ls_config_value.Equals("0"))
            {
                GetOverDraft(patientID, visitID, ref ldapprovemoney);
                adc_amount = ldprepayment + ldapprovemoney;
            }
            else
            {
                adc_amount = ldprepayment;
            }
            //adc_amount = adc_amount;
            adc_charges = ldcharges;
            return 1;

        }
        /// <summary>
        /// 取纯预交金总额
        /// </summary>
        /// <param name="patientID">病人ID</param>
        /// <param name="visitID">住院次数</param>
        /// <returns>总额</returns>
        public static decimal uf_inp_getbalance(string patientID, string visitID)
        {
            string sql = "select nvl(sum(amount),0) from inpbill.prepayment_rcpt where patient_id='" + patientID + "' and visit_id=" + visitID + " and transact_type<>'作废'  and his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            DataTable dt = spc.GetList(sql).Tables[0];
            decimal adc_prepayment = decimal.Parse(dt.Rows[0][0].ToString());
            return adc_prepayment;
        }

        /// <summary>
        /// 根据配置的策略,取透支金额
        /// </summary>
        /// <param name="patientid">病人ID</param>
        /// <param name="visitid">住院次数</param>
        /// <param name="draftMoney">返回金额</param>
        /// <returns></returns>
        public static int GetOverDraft(string patientid, string visitid, ref decimal draftMoney)
        {
            string sql = "SELECT ward_code From pats_in_hospital where  patient_id='" + patientid + "' and visit_id=" + visitid;
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            DataTable dt = spc.GetList(sql).Tables[0];
            string ls_dept_code = "";
            if (dt.Rows.Count > 0)
                ls_dept_code = dt.Rows[0][0].ToString();

            //取费别
            string ls_charge_type = "";
            sql = "select charge_type from   pat_master_index where  patient_id = '" + patientid + "'";
            dt = spc.GetList(sql).Tables[0];
            if (dt.Rows.Count > 0)
                ls_charge_type = dt.Rows[0][0].ToString();

            //取病人透支额
            int ll_count_person = 0;
            decimal ld_person = 0;
            sql = "select nvl(count(*),0),nvl(sum(TOTAL_SANCTIFIED_AMOUNT),0) from INP_OVERDRAFT_REG_MASTER where  patient_id='" + patientid + "' and visit_id  =" + visitid + " and charge_type= '*' and dept_code  = '*'";
            dt = spc.GetList(sql).Tables[0];
            ll_count_person = int.Parse(dt.Rows[0][0].ToString());
            ld_person = decimal.Parse(dt.Rows[0][1].ToString());
            //取特定费别透支额
            int ll_count_chargetype = 0;
            decimal ld_chargetype = 0;
            sql = "select nvl(count(*),0),nvl(sum(TOTAL_SANCTIFIED_AMOUNT),0) from INP_OVERDRAFT_REG_MASTER where  patient_id = '*' and  visit_id   = 0";
            sql += " and    charge_type= '" + ls_charge_type + "' and    dept_code  = '*'";
            dt = spc.GetList(sql).Tables[0];
            ll_count_chargetype = int.Parse(dt.Rows[0][0].ToString());
            ld_chargetype = decimal.Parse(dt.Rows[0][1].ToString());
            //取特定科室透支额
            int ll_count_dept = 0;
            decimal ld_dept = 0;
            sql = "select nvl(count(*),0),nvl(sum(TOTAL_SANCTIFIED_AMOUNT),0) from INP_OVERDRAFT_REG_MASTER where  patient_id = '*' and    visit_id   = 0 ";
            sql += "and    charge_type= '*' and    dept_code  = '" + ls_dept_code + "'";
            dt = spc.GetList(sql).Tables[0];
            ll_count_dept = int.Parse(dt.Rows[0][0].ToString());
            ld_dept = decimal.Parse(dt.Rows[0][1].ToString());
            //取全院透支额
            int ll_count_all = 0;
            decimal ld_all = 0;
            sql = "select nvl(count(*),0),nvl(sum(TOTAL_SANCTIFIED_AMOUNT),0) from  INP_OVERDRAFT_REG_MASTER where patient_id  = '*' and   charge_type = '*' and   dept_code   = '*'";
            dt = spc.GetList(sql).Tables[0];
            ll_count_all = int.Parse(dt.Rows[0][0].ToString());
            ld_all = decimal.Parse(dt.Rows[0][1].ToString());

            //取各级设置的计算关系
            string way = "";
            way = SystemParm.GetParameterValue("OVERDRAFT_WAY", "*", "*", "*", SystemParm.HisUnitCode);
            if (way.Equals("1"))
            {
                // 修改原因: 当有记录行, 而相应值被修改为0时, 导致优先序列无法传递下去
                //           因此, 要加上金额不等于0的判断
                if (ll_count_person > 0 && ld_person != 0)
                    draftMoney = ld_person;
                else if (ll_count_chargetype > 0 && ld_chargetype != 0)
                    draftMoney = ld_chargetype;
                else if (ll_count_dept > 0 && ld_dept != 0)
                    draftMoney = ld_dept;
                else if (ll_count_all > 0)
                    draftMoney = ld_all;
            }
            if (way.Equals("2"))
                draftMoney = ld_person + ld_chargetype + ld_dept + ld_all;

            return 1;
        }
        /// <summary>
        /// 预交金交易余额退款
        /// </summary>
        /// <param name="idc">保存数据使用</param>
        /// <param name="as_rcpt_no">原始交款预交金号</param>
        /// <param name="as_operator">操作员号</param>
        /// <param name="adc_amount">退款金额</param>
        /// <param name="as_payway">退款支付方式</param>
        /// <returns>成功返回true否则返回false</returns>
        public static bool uf_set_refund_by_balance(Dictionary<string, string> idc, string as_rcpt_no, string as_operator, decimal adc_amount, string as_payway)
        {
            string sql = "select * from inpbill.prepayment_rcpt where ( rcpt_no='" + as_rcpt_no + "' or used_rcpt_no = '" + as_rcpt_no + "')";
            sql += " and transact_type<>'作废'  and his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' order by rcpt_no";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            DataTable dt = spc.GetList(sql).Tables[0];
            if (dt.Rows.Count < 1)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("未找到【预交金号 = '" + as_rcpt_no + "'】的预交金记录！", "提示");
                return false;
            }
            decimal ldc_amount = 0;
            string ls_tot = dt.Compute("sum(amount)", "true").ToString();

            if (string.IsNullOrEmpty(ls_tot))
            {
                ldc_amount = 0;
            }
            else
            {
                ldc_amount = decimal.Parse(ls_tot);
            }

            if (ldc_amount <= 0)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("该笔预交金不需要退款，请确认！", "提示");
                return false;
            }
            if (ldc_amount != adc_amount)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("退款金额不正确，该笔预交金余额为：" + ldc_amount.ToString() + "请确认！", "提示");
                return false;
            }
            DataRow[] drs = dt.Select("rcpt_no='" + as_rcpt_no + "'");
            if (drs.Length < 1)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("查询原始预交金记录失败", "提示");
                return false;
            }
            DataRow dr = drs[0];
            string patientId = dr["patient_id"].ToString();
            string visitid = dr["visit_id"].ToString();

            decimal ldc_refundamount = -ldc_amount;
            string ls_newrcptno = "";
            int rev = uf_get_pre_rcpt_no(ref ls_newrcptno);
            if (rev == -1) return false;
            sql = "insert into prepayment_rcpt(patient_id,rcpt_no,amount,pay_way,transact_type,transact_date,";
            sql += "operator_no,used_rcpt_no,used_flag,visit_id,his_unit_code) values('" + patientId + "','" + ls_newrcptno + "',";
            sql += ldc_refundamount.ToString() + ",'" + as_payway + "','退款',sysdate,'" + as_operator + "','";
            sql += as_rcpt_no + "','0'," + visitid + ",'" + PlatCommon.SysBase.SystemParm.HisUnitCode + "')";
            idc.Add(sql, "写入退款预交金失败！");
            sql = "update prepayment_rcpt set refunded_rcpt_no='" + ls_newrcptno + "',used_flag = '1' where rcpt_no='" + as_rcpt_no + "'";
            idc.Add(sql, "更新原预交金失败！");

            return true;
        }
        /// <summary>
        /// 结算后的退费，预交金的处理
        /// </summary>
        /// <param name="as_old_rcptno">要退的结算主记录的收据号</param>
        /// <param name="as_new_rcptno">新写入退结算主记录的收据号</param>
        public static int uf_set_rcpt_refund(string as_old_rcptno, string as_new_rcptno, string as_operator_no, ref DataSet lds_pre, string file)
        {
            try
            {
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                DataSet ds;
                string ls_pre_new_rcptno = "";
                string ls_sql = "select * from inpbill.prepayment_rcpt where settled_no='" + as_old_rcptno + "'  and his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
                ds = spc.GetDataBySql(ls_sql);
                lds_pre = ds.Copy(); //复制结构与 数据
                if (ds != null && ds.Tables[0].Rows.Count > 0)//使用了预交金的，做如下处理
                {


                    DataRow[] drs = lds_pre.Tables[0].Select("");
                    foreach (DataRow dr in drs)
                    {
                        if (uf_get_pre_rcpt_no(ref ls_pre_new_rcptno) != 0)
                        {
                            DevExpress.XtraEditors.XtraMessageBox.Show("取预交金当前收据号失败auto_setting_id!");
                            return -1;
                        }
                        DataRow drx = lds_pre.Tables[0].NewRow();
                        drx.ItemArray = dr.ItemArray; //所有列复制
                        drx["rcpt_no"] = ls_pre_new_rcptno;
                        drx["operator_no"] = as_operator_no;
                        drx["settled_no"] = as_new_rcptno;
                        drx["transact_date"] = new NM_Service.NMService.ServerPublicClient().GetSysDate().ToString("yyyy-MM-dd HH:mm:ss");	//--设置时间
                        drx["amount"] = (0 - Convert.ToDecimal(dr["amount"])).ToString();
                        lds_pre.Tables[0].Rows.Add(drx);
                    }

                }
                ds.Dispose();

                return 0;
            }
            catch (Exception e)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(e.Message);
                return -1;
            }
        }
        /// <summary>
        /// 更新发票号
        /// </summary>
        public static int uf_set_nextno(string as_user, string ai_flag, Int32 ai_number, Int32 il_curr_no, Int32 il_max_no, ref Dictionary<string, string> idc)
        {
            try
            {
                Dictionary<string, string> idc1 = new Dictionary<string, string>();
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                if ((il_curr_no + ai_number) > il_max_no)
                {
                    //发票没有了
                    string sqla = " update invoice_manage_rec     set  curr_no  = curr_no +" + ai_number.ToString() + ",using_status ='3'";
                    sqla += " where  operator_no  = '" + as_user + "' and  flag = '" + ai_flag + "' and provide_date   = ( select min(provide_date)  from invoice_manage_rec ";
                    sqla += " where operator_no  = '" + as_user + "' and flag =  '" + ai_flag + "' and using_status ='2')";
                    idc.Add(sqla, "更新发票当前号失败");
                }
                else
                {
                    string sqla = " update invoice_manage_rec     set  curr_no  = curr_no +" + ai_number.ToString();
                    sqla += " where  operator_no  = '" + as_user + "' and  flag = '" + ai_flag + "' and provide_date   = ( select min(provide_date)  from invoice_manage_rec ";
                    sqla += " where operator_no  = '" + as_user + "' and flag =  '" + ai_flag + "' and using_status ='2')";
                    idc.Add(sqla, "更新发票当前号失败");
                }
                //string rev = new NM_Service.NMService.ServerPublicClient().SaveTable(idc1);
                //if (!string.IsNullOrEmpty(rev))
                //{
                //    DevExpress.XtraEditors.XtraMessageBox.Show("保存失败!" + rev, "错误信息");
                //    return -1 ;
                //}
                return 0;
            }
            catch (Exception e)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(e.Message);
                return -1;
            }
        }
        public static int uf_set_nextno(string as_user, string ai_flag, Int64 ai_number, Int64 il_curr_no, Int64 il_max_no, ref Dictionary<string, string> idc)
        {
            try
            {
                Dictionary<string, string> idc1 = new Dictionary<string, string>();
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                if ((il_curr_no + ai_number) > il_max_no)
                {
                    //发票没有了
                    string sqla = " update invoice_manage_rec     set  curr_no  = curr_no +" + ai_number.ToString() + ",using_status ='3'";
                    sqla += " where  operator_no  = '" + as_user + "' and  flag = '" + ai_flag + "' and provide_date   = ( select min(provide_date)  from invoice_manage_rec ";
                    sqla += " where operator_no  = '" + as_user + "' and flag =  '" + ai_flag + "' and using_status ='2')";
                    idc.Add(sqla, "更新发票当前号失败");
                }
                else
                {
                    string sqla = " update invoice_manage_rec     set  curr_no  = curr_no +" + ai_number.ToString();
                    sqla += " where  operator_no  = '" + as_user + "' and  flag = '" + ai_flag + "' and provide_date   = ( select min(provide_date)  from invoice_manage_rec ";
                    sqla += " where operator_no  = '" + as_user + "' and flag =  '" + ai_flag + "' and using_status ='2')";
                    idc.Add(sqla, "更新发票当前号失败");
                }
                //string rev = new NM_Service.NMService.ServerPublicClient().SaveTable(idc1);
                //if (!string.IsNullOrEmpty(rev))
                //{
                //    DevExpress.XtraEditors.XtraMessageBox.Show("保存失败!" + rev, "错误信息");
                //    return -1 ;
                //}
                return 0;
            }
            catch (Exception e)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(e.Message);
                return -1;
            }
        }
        #region 门诊卡管理预交金部分

        //描  述: 取得门诊预交金余额
        public static decimal uf_outp_getbalance(string as_pid)
        {
            decimal lde_prepayment = 0;
            string sql = "select nvl(sum(amount),0) prepayment  from inpbill.prepayment_rcpt where patient_id='" + as_pid + "' and visit_id=0 and used_flag = 0  and his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];

            lde_prepayment = decimal.Parse(dt.Rows[0]["prepayment"].ToString());

            return lde_prepayment;

        }

        ////================================================================================
        // 函  数: uf_outp_pay()
        //--------------------------------------------------------------------------------
        // 描  述: 门诊支付费用时，对预交金处理
        //--------------------------------------------------------------------------------
        // 参  数: 
        //		value    	string 	as_pid       		
        //		value    	decimal	ade_money    		
        //		reference	decimal	ade_pay_money		
        //		value    	string 	as_settle_no 	
        //      idc         
        //--------------------------------------------------------------------------------
        // 返  回: integer
        public static int uf_outp_pay(string as_pid, decimal ade_money, ref decimal ade_pay_money, string as_settle_no, ref Dictionary<string, string> idc)
        {
            decimal lde_sum_money, lde_used_money, lde_amount, lde_sum_cale, lde_jy;
            string ls_rcpt_no, ls_pay_way, ls_cur_rcpt_no="";
            try
            {
                string is_operator_no = PlatCommon.SysBase.SystemParm.LoginUser.ID;
                //获取预交金余额
                string sql = "select nvl(sum(amount),0) lde_sum_money from inpbill.prepayment_rcpt where patient_id ='" + as_pid + "' and visit_id = 0 and used_flag = 0  and his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
                DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
                lde_sum_money = decimal.Parse(dt.Rows[0]["LDE_SUM_MONEY"].ToString());
                if (lde_sum_money > ade_money) //如果预交金余额大于要支付的金额 要扣除的预交金金额就是传进来的要支付的金额
                {
                    lde_used_money = ade_money;
                }
                else   //如果预交金余额小于要支付的金额 要扣除的预交金金额就是预交金全部的剩余金额
                {
                    lde_used_money = lde_sum_money;
                }
                //支付的差额，如果要支付的金额－剩余的预交金>0,ade_pay_money>0,否则小于0
                ade_pay_money = ade_money - lde_sum_money;
                sql = "select amount,rcpt_no,PAY_WAY from inpbill.prepayment_rcpt where patient_id ='" + as_pid + "' and visit_id = 0 and used_flag = 0  and his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' order by transact_date";
                DataTable prepay = new ServerPublicClient().GetList(sql).Tables[0];
                if (prepay.Rows.Count <= 0)
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("获取预交金数据失败！", "提示");
                    return -1;
                }
                lde_sum_cale = 0;
                //循环判断数据 看每条可退数量情况 并更新相应的状态
                for (int i = 0; i < prepay.Rows.Count; i++)
                {
                    ls_rcpt_no = prepay.Rows[i]["RCPT_NO"].ToString();
                    ls_pay_way = prepay.Rows[i]["PAY_WAY"].ToString();
                    lde_amount = decimal.Parse(prepay.Rows[i]["AMOUNT"].ToString());

                    lde_sum_cale = lde_sum_cale + lde_amount;

                    if ((lde_sum_cale - lde_amount) < lde_used_money)
                    {
                        sql = "update inpbill.prepayment_rcpt set used_flag = 1 where patient_id ='" + as_pid + "' and visit_id = 0 and used_flag = 0 and rcpt_no = '" + ls_rcpt_no + "'";
                        idc.Add(sql, "更新预存金额记录使用标记失败！" + ls_rcpt_no);
                        lde_jy = lde_sum_cale - lde_used_money;
                        if (lde_jy > 0) //有结余，生成结余记录
                        {
                            uf_get_pre_rcpt_no(ref ls_cur_rcpt_no); //得到当前收据号
                            sql = "insert into inpbill.prepayment_rcpt (patient_id,rcpt_no,amount,pay_way,transact_type,transact_date,operator_no,used_flag,visit_id,his_unit_code) ";
                            sql = sql + "values('" + as_pid + "','" + ls_cur_rcpt_no + "'," + lde_jy + ",'现金','结余',sysdate,'" + is_operator_no + "','0',0,'"+PlatCommon.SysBase.SystemParm.HisUnitCode+"')";
                            idc.Add(sql, "插入预存预交金结余失败！" + as_pid);
                            break;
                        }
                    }
                    else
                    {
                        break;
                    }
                }
                uf_get_pre_rcpt_no(ref ls_cur_rcpt_no); //得到当前收据号
                sql = "insert into inpbill.prepayment_rcpt (patient_id,rcpt_no,amount,pay_way ,transact_type,transact_date,operator_no,used_flag,visit_id,settled_no,his_unit_code)";
                sql = sql+ "values('" + as_pid + "','"+ ls_cur_rcpt_no + "',"+ -lde_used_money + ",'现金','结算',sysdate,'"+ is_operator_no + "','1',0,'"+ as_settle_no + "','" + PlatCommon.SysBase.SystemParm.HisUnitCode + "')";
                idc.Add(sql, "插入预存结算记录失败！" + as_pid);

            }
            catch (Exception e)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(e.Message);
                return -1;
            }

            return 0;
        }



        //================================================================================
        // 函  数: uf_outp_repay()
        //--------------------------------------------------------------------------------
        // 描  述: 用预交金（部分或全部）完成门诊支付后，如果该笔支付需退回，
        //         预交金也要进行返回，Ade_money为返回的预交金
        //--------------------------------------------------------------------------------
        // 参  数: 
        //		value	string 	as_pid      		
        //		value	decimal	ade_money   		
        //		value	string 	as_settle_no		
        //		value	string	as_operator_no   		
        //		ref 	Dictionary<string, string> string 	idc	
        //--------------------------------------------------------------------------------
        // 返  回: integer
        //--------------------------------------------------------------------------------
        // 
        //--------------------------------------------------------------------------------
        // 修  改: 
        //	
        //--------------------------------------------------------------------------------
        //================================================================================
        public static int uf_outp_repay(string as_pid, decimal ade_money, string as_settle_no, string as_operator_no, ref Dictionary<string, string> idc)
        {
            string ls_cur_rcpt_no = "";
            string sql = "";
            string ls_datetime;
            uf_get_pre_rcpt_no(ref ls_cur_rcpt_no);
            ls_datetime = new ServerPublicClient().GetSysDateTime();
            sql = "insert into inpbill.prepayment_rcpt (patient_id,rcpt_no,amount,pay_way,transact_type,transact_date,operator_no,used_flag,visit_id,settled_no,his_unit_code) values ( ";
            sql = sql + "'" + as_pid + "','" + ls_cur_rcpt_no + "'," + ade_money + ",'现金','返回',to_date('" + ls_datetime + "','yyyy-mm-dd hh24:mi:ss'),'" + as_operator_no + "','0',0,'" + as_settle_no + "','" + PlatCommon.SysBase.SystemParm.HisUnitCode + "')";

            idc.Add(sql, "插入预交金返回记录失败!");


            return 0;
        }

        //================================================================================
        // 函  数: uf_outp_add()
        //--------------------------------------------------------------------------------
        // 描  述: 门诊预交金交纳
        //--------------------------------------------------------------------------------
        // 参  数: 
        //		value	string 	as_pid   		
        //		value	decimal	adc_money		
        //		value	string 	as_payway		
        //--------------------------------------------------------------------------------
        // 返  回: integer
        public static int uf_outp_add(string as_pid, decimal ade_money, string as_payway, string as_check_no, string as_operator_no, string as_flag, ref Dictionary<string, string> idc,ref string as_rcpt_no)
        {
            string ls_cur_rcpt_no = "";
            string sql = "";
            string ls_datetime;
            uf_get_pre_rcpt_no(ref ls_cur_rcpt_no);
            ls_datetime = new ServerPublicClient().GetSysDateTime();
            sql = "insert into inpbill.prepayment_rcpt (patient_id,rcpt_no,amount,transact_type,pay_way,transact_date,visit_id,used_flag,operator_no,bank,check_no,flag,his_unit_code)  values ( ";
            sql = sql + "'" + as_pid + "','" + ls_cur_rcpt_no + "'," + ade_money + ",'交款','" + as_payway + "',to_date('" + ls_datetime + "','yyyy-mm-dd hh24:mi:ss'),0,'0', '" + as_operator_no + "','门诊医疗卡预交金','" + as_check_no + "','" + as_flag + "' ,'" + PlatCommon.SysBase.SystemParm.HisUnitCode + "')";

            idc.Add(sql, "插入预交金返回记录失败!");

            as_rcpt_no = ls_cur_rcpt_no;

            return 0;
        }


        //================================================================================
        // 函  数: uf_outp_add_deposit()
        //--------------------------------------------------------------------------------
        // 描  述: 门诊卡押金
        //--------------------------------------------------------------------------------
        // 参  数: 
        //		value	string 	as_pid   		
        //		value	decimal	adc_money		
        //		value	string 	as_payway		
        //--------------------------------------------------------------------------------
        // 返  回: integer
        public static int uf_outp_add_deposit(string as_pid, decimal ade_money, string as_payway, string as_check_no, string as_operator_no, ref Dictionary<string, string> idc)
        {
            string ls_cur_rcpt_no = "";
            string sql = "";
            string ls_datetime;

            uf_get_pre_rcpt_no(ref ls_cur_rcpt_no);
            ls_datetime = new ServerPublicClient().GetSysDateTime();
            sql = "insert into inpbill.prepayment_rcpt (patient_id,rcpt_no,amount,transact_type,pay_way,transact_date,visit_id,used_flag,operator_no,bank,check_no,his_unit_code)  values ( ";
            sql = sql + "'" + as_pid + "','" + ls_cur_rcpt_no + "'," + ade_money + ",'交款','" + as_payway + "',to_date('" + ls_datetime + "','yyyy-mm-dd hh24:mi:ss'),0,'9', '" + as_operator_no + "','门诊医疗卡押金','" + as_check_no + "' ,'" + PlatCommon.SysBase.SystemParm.HisUnitCode + "')";

            idc.Add(sql, "插入预交金返回记录失败!");


            return 0;
        }

        //================================================================================
        // 函  数: uf_outp_untread()
        //--------------------------------------------------------------------------------
        // 描  述: 门诊预交金退回给病人
        //--------------------------------------------------------------------------------
        // 参  数: 
        //		value	string	as_pid		
        //--------------------------------------------------------------------------------
        // 返  回: integer
        public static int uf_outp_untread(string as_pid, decimal adc_refund, string as_payway, string as_operator_no, ref Dictionary<string, string> idc,ref string as_cur_rcpt_no)
        {
            decimal lde_can_refund, lde_amount, lde_sum_cale, lde_jy;
            string ls_rcpt_no, ls_pay_way, ls_cur_rcpt_no = "";
            try
            {
                string is_operator_no = PlatCommon.SysBase.SystemParm.LoginUser.ID;
                //获取预交金余额
                string sql = "select nvl(sum(amount),0) lde_can_refund from inpbill.prepayment_rcpt where patient_id ='" + as_pid + "' and visit_id = 0 and used_flag = 0  and his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
                DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
                lde_can_refund = decimal.Parse(dt.Rows[0]["lde_can_refund"].ToString());

                //所退预交金大于病人剩余的!
                if ((adc_refund - lde_can_refund) > 0)
                {
                    XtraMessageBox.Show("所退预交金大于病人剩余的！", "提示");
                    return -1;
                }
                sql = "select amount,rcpt_no,PAY_WAY from inpbill.prepayment_rcpt where patient_id ='" + as_pid + "' and visit_id = 0 and used_flag = 0  and his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' order by transact_date";
                DataTable prepay = new ServerPublicClient().GetList(sql).Tables[0];
                if (prepay.Rows.Count <= 0)
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("获取预交金数据失败！", "提示");
                    return -1;
                }
                lde_sum_cale = 0;
                //循环判断数据 看每条可退数量情况 并更新相应的状态
                for (int i = 0; i < prepay.Rows.Count; i++)
                {
                    ls_rcpt_no = prepay.Rows[i]["RCPT_NO"].ToString();
                    ls_pay_way = prepay.Rows[i]["PAY_WAY"].ToString();
                    lde_amount = decimal.Parse(prepay.Rows[i]["AMOUNT"].ToString());

                    lde_sum_cale = lde_sum_cale + lde_amount;

                    if ((lde_sum_cale - lde_amount) < adc_refund)
                    {
                        sql = "update inpbill.prepayment_rcpt set used_flag = 1 where patient_id ='" + as_pid + "' and visit_id = 0 and used_flag = 0 and rcpt_no = '" + ls_rcpt_no + "'";
                        idc.Add(sql, "更新预存金额记录使用标记失败！" + ls_rcpt_no);
                        lde_jy = lde_sum_cale - adc_refund; 
                        if (lde_jy > 0) //有结余，生成结余记录
                        {
                            uf_get_pre_rcpt_no(ref ls_cur_rcpt_no); //得到当前收据号
                            sql = "insert into inpbill.prepayment_rcpt (patient_id,rcpt_no,amount,transact_type,transact_date,operator_no,used_flag,visit_id,his_unit_code,pay_way) ";
                            sql = sql + "values('" + as_pid + "','" + ls_cur_rcpt_no + "'," + lde_jy + ", '结余',sysdate,'" + is_operator_no + "','0',0,'" + PlatCommon.SysBase.SystemParm.HisUnitCode + "','" + as_payway + "')";
                            idc.Add(sql, "插入预存预交金结余失败！" + as_pid);
                            break;
                        }
                    }
                    else
                    {
                        break;
                    }
                }
                uf_get_pre_rcpt_no(ref ls_cur_rcpt_no); //得到当前收据号
                sql = "insert into inpbill.prepayment_rcpt (patient_id,rcpt_no,amount,transact_type,transact_date,operator_no,used_flag,visit_id,his_unit_code,pay_way)";
                sql = sql + "values('" + as_pid + "','" + ls_cur_rcpt_no + "'," + -adc_refund + ", '退款',sysdate,'" + is_operator_no + "','1',0,'" + PlatCommon.SysBase.SystemParm.HisUnitCode + "','" + as_payway + "')";
                idc.Add(sql, "插入预交金退回记录失败！" + as_pid);
                as_cur_rcpt_no = ls_cur_rcpt_no;
            }
            catch (Exception e)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(e.Message);
                return -1;
            }
            return 0;
        }


        #endregion

    }
}
