﻿using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System;
using System.Data;
using System.Windows.Forms;
using Tjhis.Report.Custom.Common;
using Tjhis.Report.Custom.Srv;

namespace Tjhis.Report.Custom.Custom
{
    /// <summary>
    /// 参数维护
    /// </summary>
    public partial class frmParamDict : ParentForm
    {
        srvStatisticalQuery srv;
        private string hospitalCode = SystemParm.HisUnitCode;  //院区代码
        private string appName;            //程序代码

        DataSet dsParamDict;
        public frmParamDict(string appName)
        {
            InitializeComponent();
            this.appName = appName;
        }
        /// <summary>
        /// Load事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void frmParamDict_Load(object sender, EventArgs e)
        {
            //LookupEdit下拉框绑定
            LUE_EditType.ValueMember = "ITEM_CODE";
            LUE_EditType.DisplayMember = "ITEM_NAME";
            LUE_EditType.DataSource = srvStatisticalQuery.CreateDict(Const.CONTROL_TYPES);
            LUE_SourceType.ValueMember = "ITEM_CODE";
            LUE_SourceType.DisplayMember = "ITEM_NAME";
            LUE_SourceType.DataSource = srvStatisticalQuery.CreateDict("1-SQL脚本;2-自定义选项");
            //绑定Grid
            srv = new srvStatisticalQuery();
            dsParamDict = srv.GetParamDict();

            gridControl1.DataSource = dsParamDict.Tables[0];
        }
        /// <summary>
        /// 双击弹出编辑窗体
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gridView1_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            if(e.Clicks == 2)
            {
                DataRow dr = gridView1.GetFocusedDataRow();
                if (dr != null)
                {
                    ShowEditForm(dr);
                }
            }
        }
        /// <summary>
        /// [新增]按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnAdd_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            gridView1.AddNewRow();
            DataRow dr = gridView1.GetFocusedDataRow();
            DataRowView drv = gridView1.GetFocusedRow() as DataRowView;
            frmParamEdit fe = new frmParamEdit(dr);
            DialogResult result = fe.ShowDialog();

            if (result == DialogResult.OK)
            {
                SaveData();
            }
            else
            {
                drv.Delete();
                dsParamDict.AcceptChanges();
            }
        }
        /// <summary>
        /// 新增初始化事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gridView1_InitNewRow(object sender, DevExpress.XtraGrid.Views.Grid.InitNewRowEventArgs e)
        {
            DataRow dr = gridView1.GetDataRow(e.RowHandle);
            dr["HOSPITAL_CODE"] = hospitalCode;
            dr["APP_NAME"] = appName;
            dr["PARAM_ID"] = DateTime.Now.ToString("yyyyMMddHHmmssfff");
            dr["PARAM_NAME"] = "";
            dr["STATUS"] = "1"; //默认启用
        }
        /// <summary>
        /// 显示编辑界面
        /// </summary>
        /// <param name="dr"></param>
        void ShowEditForm(DataRow dr)
        {
            if (dr == null) return;  
            frmParamEdit fe = new frmParamEdit(dr);
            DialogResult result = fe.ShowDialog();

            if (result == DialogResult.OK)
            {
                SaveData();
            }
        }
        /// <summary>
        /// 保存
        /// </summary>
        public override void SaveData()
        {
            gridView1.CloseEditor();
            gridView1.UpdateCurrentRow();
            if (dsParamDict!=null && dsParamDict.HasChanges())
            {             
                srv.SaveData(dsParamDict);

                dsParamDict.AcceptChanges();

                dsParamDict = srv.GetParamDict();

                gridControl1.DataSource = dsParamDict.Tables[0];

                XtraMessageBox.Show("保存成功" ,"提示信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        /// <summary>
        /// [编辑]按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnEdit_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            DataRow dr = gridView1.GetFocusedDataRow();
            if (dr != null)
            {
                ShowEditForm(dr);
            }
        }
        /// <summary>
        /// [删除]按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            gridView1.DeleteSelectedRows();
        }
        /// <summary>
        /// [保存]按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            SaveData();
        }

        private void barLargeButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Close();
        }
    }
}
