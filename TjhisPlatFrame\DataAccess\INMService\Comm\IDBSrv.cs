﻿using System;
using System.Data;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ServiceModel;

using HashItem = Comm.Object.HashItem;

namespace INMService
{
    /// <summary>
    /// 接口层DB
    /// </summary>
    [ServiceContract]
    public interface IDBSrv
    {
        #region SQL语句处理
        /// <summary>
        /// 通过SQLID获取SQL语句. Sql语句保存到表NR_DICT_SQL中
        /// </summary>
        /// <param name="sqlId">sqlId</param>
        /// <param name="items">参数列表</param>
        /// <returns>sql文本</returns>
        [OperationContract]
        string GetSqlTextA(string sqlId, ObservableCollection<HashItem> items);
                

        /// <summary>
        /// 通过SQLID获取SQL语句. Sql语句保存到表NR_DICT_SQL中。
        /// </summary>
        /// <param name="sqlId">sqlId</param>
        /// <returns>sql文本</returns>
        [OperationContract]
        string GetSqlText(string sqlId);


        /// <summary>
        /// 获取SQL属性
        /// </summary>
        /// <param name="sqlId"></param>
        /// <param name="tableName"></param>
        /// <param name="blnWithKey"></param>
        /// <returns></returns>
        [OperationContract]
        bool GetSqlProperty(string sqlId, ref string tableName, ref bool blnWithKey);
        #endregion

        
        #region 数据查询
        /// <summary>
        /// 获取下一个序列号
        /// </summary>
        /// <param name="seqName"></param>
        /// <returns></returns>
        [OperationContract]
        int GetSeq(string seqName);


        /// <summary>
        /// 单值查询
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        [OperationContract]
        string SelectValueBySql(string sql);


        /// <summary>
        /// 单值查询
        /// </summary>
        /// <param name="sqlId"></param>
        /// <param name="items"></param>
        /// <returns></returns>
        [OperationContract]
        string SelectValue(string sqlId, ObservableCollection<HashItem> items);


        /// <summary>
        /// 查询数据集
        /// </summary>
        /// <param name="sqlId"></param>
        /// <param name="items"></param>
        /// <returns></returns>
        [OperationContract]
        DataSet SelectData(string sqlId, ObservableCollection<HashItem> items);


        [OperationContract]
        DataSet SelectDataA(string sql, string tableName, bool blnWithKey);


        [OperationContract]
        DataSet SelectDataBySql(string sql, string tableName);
        #endregion
        

        #region 数据更新
        [OperationContract]
        bool SaveDataC(ArrayList arrDataSet, ArrayList arrSql);


        [OperationContract]
        bool SaveDataB(DataSet ds, ArrayList arrSql);


        [OperationContract]
        int SaveDataA(DataSet ds);


        [OperationContract]
        int SaveDataD(DataSet ds, string tableName, string sqlSel);


        [OperationContract]
        int ExecuteNoQueryB(ArrayList sqlCol);


        [OperationContract]
        int ExecuteNoQuery(string sql);


        [OperationContract]
        int ExecuteNoQueryA(string sqlId, ObservableCollection<HashItem> items);


        [OperationContract]
        int ExecuteProcedure(string sqlId, ObservableCollection<HashItem> items);
        #endregion


        #region 调用存储过程
        /// <summary>
        ///调用存储过程
        /// 方法的输入产生都默认Varchar2, 输出默认一个游标
        /// add by 荣守国 2015.9.7
        /// </summary>
        /// <param name="procedureName"></param>
        /// <param name="items"></param>
        /// <returns></returns>
        [OperationContract]
        DataSet GetDataByProc(string procedureName, ObservableCollection<HashItem> items);
        #endregion
    }
}