﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Runtime.Serialization;
using System.IO;
using System.IO.Compression;
using System.Runtime.Serialization.Formatters.Binary;
using System.Data.OracleClient;

namespace Utility.DbInfo
{
    public class DbInfo
    {
        OracleAccess _oraConnection = null;
        
        public DbInfo()
        {
            
        }
        
        /// <summary>
        /// 单值查询
        /// </summary>
        /// <returns></returns>
        public string GetValueBySql(string sql)
        {
            ConnectionStringHelper.CheckOracleConnection(ref _oraConnection);

            if (_oraConnection.SelectValue(sql) == true)
            {
                return _oraConnection.GetResult(0);
            }
            else
            {
                return null;
            }
        }
        
        /// <summary>
        /// 通过SQL语句获取数据
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public DataSet GetData(string sql, string tableName)
        {
            ConnectionStringHelper.CheckOracleConnection(ref _oraConnection);
            return _oraConnection.SelectData_NoKey(sql, tableName);
        }
        /// <summary>
        /// 通过一组TABLENAME 获取一个数据集
        /// </summary>
        /// <param name="TableNames"></param>
        /// <returns></returns>
        public DataSet GetDataSet_ByTableNames(String[] TableNames)
        {
            ConnectionStringHelper.CheckOracleConnection(ref _oraConnection);

            return _oraConnection.SelectDataSet_ByTableNames(TableNames);
        }
        /// <summary>
        /// 通过一组TABLENAME 和 对应的SQL语句  获取一个数据集
        /// </summary>
        /// <param name="TableNameAndSql"></param>
        /// <returns></returns>
        public DataSet GetDataSet_ByTableNameAndSql(String[,] TableNameAndSql)
        {
            ConnectionStringHelper.CheckOracleConnection(ref _oraConnection);

            return _oraConnection.SelectDataSet_ByTableNamesAndSql(TableNameAndSql);
        }
        /// <summary>
        /// 系统参数初始化
        /// </summary>
        /// <param name="settingParameter"></param>
        /// <returns></returns>
        public List<String> InitSystemParameter(DataSet settingParameter,DataSet NowParameter)
        {
            List<String> ret = new List<string>();
            ConnectionStringHelper.CheckOracleConnection(ref _oraConnection);
            //获取到comm.app_configer_baseinfo表主键列的投影
            var baseinfoPK= settingParameter.Tables["comm.app_configer_baseinfo"].AsEnumerable().
                            Select(pk=>new 
                                 {
                                     APP_NAME = pk.Field<String>("APP_NAME"),
                                     PARAMETER_NO = pk.Field<object>("PARAMETER_NO"),
                                     PARAMETER_NAME = pk.Field<String>("PARAMETER_NAME")
                                 }) ;
            //获取到comm.app_configer_baseinfo表主键列的投影
            var nowbaseinfoPK = NowParameter.Tables["comm.app_configer_baseinfo"].AsEnumerable().
                               Select(pk => new
                               {
                                   APP_NAME = pk.Field<String>("APP_NAME"),
                                   PARAMETER_NO = pk.Field<object>("PARAMETER_NO"),
                                   PARAMETER_NAME = pk.Field<String>("PARAMETER_NAME")
                               });
            // var baseinfo = settingParameter.Tables["comm.app_configer_baseinfo"].AsEnumerable().Except(NowParameter.Tables["comm.app_configer_baseinfo"].Select("APP_NAME,PARAMETER_NO,PARAMETER_NAME").AsEnumerable(), DataRowComparer.Default);
            //两个数据集做差运算，取得baseinfoPK存在但nowbaseinfoPK不存在的项
            var baseinfo = baseinfoPK.Except(nowbaseinfoPK);

            //获取到comm.app_configer_parameter表主键列的投影
            var parameterinfoPK = settingParameter.Tables["comm.app_configer_parameter"].AsEnumerable().
                            Select(pk => new
                            {
                                APP_NAME = pk.Field<String>("APP_NAME"),
                                DEPT_CODE = pk.Field<String>("DEPT_CODE"),
                                EMP_NO = pk.Field<String>("EMP_NO"),
                                PARAMETER_NAME = pk.Field<String>("PARAMETER_NAME")
                            });
            //获取到comm.app_configer_parameter表主键列的投影
            var nowparameterinfoPK = NowParameter.Tables["comm.app_configer_parameter"].AsEnumerable().
                            Select(pk => new
                            {
                                APP_NAME = pk.Field<String>("APP_NAME"),
                                DEPT_CODE = pk.Field<String>("DEPT_CODE"),
                                EMP_NO = pk.Field<String>("EMP_NO"),
                                PARAMETER_NAME = pk.Field<String>("PARAMETER_NAME")
                            });
           
            //var parameterinfo = settingParameter.Tables["comm.app_configer_parameter"].AsEnumerable().Except(NowParameter.Tables["comm.app_configer_parameter"].AsEnumerable(), DataRowComparer.Default);
            var parameterinfo = parameterinfoPK.Except(nowparameterinfoPK);
            
            _oraConnection.BeginTrans();
            try
            {
                foreach (var modevar in baseinfo.ToArray())
                {
                    String selectsql="APP_Name='" + modevar.APP_NAME + "' and PARAMETER_NO='" + modevar.PARAMETER_NO.ToString() + "' and PARAMETER_NAME='" + modevar.PARAMETER_NAME + "'";
                    DataRow[] temp = settingParameter.Tables["comm.app_configer_baseinfo"].Select(selectsql);
                  
                    if (temp.Length > 0)
                    {
                        DataRow var = temp[0];
                        String APP_NAME = var["APP_NAME"] == null ? "" : var["APP_NAME"].ToString();
                        String PARAMETER_NO = var["PARAMETER_NO"] == null ? "" : var["PARAMETER_NO"].ToString();
                        String PARAMETER_NAME = var["PARAMETER_NAME"] == null ? "" : var["PARAMETER_NAME"].ToString();
                        String PARAINIT_VALUE = var["PARAINIT_VALUE"] == null ? "" : var["PARAINIT_VALUE"].ToString();
                        String PARAMETER_SCOPE = var["PARAMETER_SCOPE"] == null ? "" : var["PARAMETER_SCOPE"].ToString();
                        String EXPLANATION = var["EXPLANATION"] == null ? "" : var["EXPLANATION"].ToString();
                        String INITIALIZE_SERIAL_NO = var["INITIALIZE_SERIAL_NO"] == null ? "" : var["INITIALIZE_SERIAL_NO"].ToString();
                        String CUSTOMIZATION_FLAG = var["CUSTOMIZATION_FLAG"] == null ? "" : var["CUSTOMIZATION_FLAG"].ToString();
                        String DEPRECATED_FLAG = var["DEPRECATED_FLAG"] == null ? "" : var["DEPRECATED_FLAG"].ToString();
                        String CLASS_CODE = var["CLASS_CODE"] == null ? "" : var["CLASS_CODE"].ToString();
                        //String sql = "insert into comm.app_configer_baseinfo(APP_NAME,PARAMETER_NO,PARAMETER_NAME,PARAINIT_VALUE,PARAMETER_SCOPE,EXPLANATION,INITIALIZE_SERIAL_NO,CUSTOMIZATION_FLAG,DEPRECATED_FLAG,CLASS_CODE) values('{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}')";
                        //sql = String.Format(sql, APP_NAME, PARAMETER_NO, PARAMETER_NAME, PARAINIT_VALUE, PARAMETER_SCOPE, EXPLANATION, INITIALIZE_SERIAL_NO, CUSTOMIZATION_FLAG, DEPRECATED_FLAG, CLASS_CODE);
                        String sql = "insert into comm.app_configer_baseinfo(APP_NAME,PARAMETER_NO,PARAMETER_NAME,PARAINIT_VALUE,PARAMETER_SCOPE,EXPLANATION,INITIALIZE_SERIAL_NO,CUSTOMIZATION_FLAG,DEPRECATED_FLAG,CLASS_CODE) values(:APP_NAME,:PARAMETER_NO,:PARAMETER_NAME,:PARAINIT_VALUE,:PARAMETER_SCOPE,:EXPLANATION,:INITIALIZE_SERIAL_NO,:CUSTOMIZATION_FLAG,:DEPRECATED_FLAG,:CLASS_CODE)";

                        List<IDataParameter> parameterlist = new List<IDataParameter>();
                        parameterlist.Add(new OracleParameter(":APP_NAME", APP_NAME));
                        parameterlist.Add(new OracleParameter(":PARAMETER_NO", PARAMETER_NO));
                        parameterlist.Add(new OracleParameter(":PARAMETER_NAME", PARAMETER_NAME));
                        parameterlist.Add(new OracleParameter(":PARAINIT_VALUE", PARAINIT_VALUE));
                        parameterlist.Add(new OracleParameter(":PARAMETER_SCOPE", PARAMETER_SCOPE));
                        parameterlist.Add(new OracleParameter(":EXPLANATION", EXPLANATION));
                        parameterlist.Add(new OracleParameter(":INITIALIZE_SERIAL_NO", INITIALIZE_SERIAL_NO));
                        parameterlist.Add(new OracleParameter(":CUSTOMIZATION_FLAG", CUSTOMIZATION_FLAG));
                        parameterlist.Add(new OracleParameter(":DEPRECATED_FLAG", DEPRECATED_FLAG));
                        parameterlist.Add(new OracleParameter(":CLASS_CODE", CLASS_CODE));
                        int executeNum = _oraConnection.ExecuteNoQuery(sql, parameterlist);
                        if (executeNum > 0)
                        {
                            ret.Add(APP_NAME + ";" + PARAMETER_NO + ";" + PARAMETER_NAME + ";初始化comm.app_configer_baseinfo插入成功");
                        }
                        else
                        {
                            ret.Add(APP_NAME + ";" + PARAMETER_NO + ";" + PARAMETER_NAME + ";初始化comm.app_configer_baseinfo插入失败");
                        }
                    }
                   
                    
                }
                foreach (var modevar in parameterinfo.ToArray())
                {
                    String selectsql = "APP_Name='" + modevar.APP_NAME + "' and DEPT_CODE='" + modevar.DEPT_CODE + "' and EMP_NO='" + modevar.EMP_NO + "' and PARAMETER_NAME='" + modevar.PARAMETER_NAME + "'";
                    DataRow[] temp = settingParameter.Tables["comm.app_configer_parameter"].Select(selectsql);
                    if (temp.Length > 0)
                    {
                        DataRow varrow = temp[0];
                        String APP_NAME = varrow["APP_NAME"] == null ? "" : varrow["APP_NAME"].ToString();
                        String DEPT_CODE = varrow["DEPT_CODE"] == null ? "" : varrow["DEPT_CODE"].ToString();
                        String EMP_NO = varrow["EMP_NO"] == null ? "" : varrow["EMP_NO"].ToString();
                        String PARAMETER_NAME = varrow["PARAMETER_NAME"] == null ? "" : varrow["PARAMETER_NAME"].ToString();
                        String PARAMETER_VALUE = varrow["PARAMETER_VALUE"] == null ? "" : varrow["PARAMETER_VALUE"].ToString();
                        String POSITION = varrow["POSITION"] == null ? "" : varrow["POSITION"].ToString();
                        String MEMO = varrow["MEMO"] == null ? "" : varrow["MEMO"].ToString();
                        //String sql = "insert into comm.app_configer_parameter(app_name,dept_code,emp_no,parameter_name,parameter_value,position,memo)values('{0}','{1}','{2}','{3}','{4}','{5}','{6}')";
                        //sql = String.Format(sql, APP_NAME, DEPT_CODE, EMP_NO, PARAMETER_NAME, PARAMETER_VALUE, POSITION, MEMO);
                        String sql = "INSERT INTO COMM.APP_CONFIGER_PARAMETER(APP_NAME,DEPT_CODE,EMP_NO,PARAMETER_NAME,PARAMETER_VALUE,POSITION,MEMO)VALUES(:APP_NAME,:DEPT_CODE,:EMP_NO,:PARAMETER_NAME,:PARAMETER_VALUE,:POSITION,:MEMO)";

                        List<IDataParameter> parameterlist = new List<IDataParameter>();
                        parameterlist.Add(new OracleParameter(":APP_NAME", APP_NAME));
                        parameterlist.Add(new OracleParameter(":DEPT_CODE", DEPT_CODE));
                        parameterlist.Add(new OracleParameter(":EMP_NO", EMP_NO));
                        parameterlist.Add(new OracleParameter(":PARAMETER_NAME", PARAMETER_NAME));
                        parameterlist.Add(new OracleParameter(":PARAMETER_VALUE", PARAMETER_VALUE));
                        parameterlist.Add(new OracleParameter(":POSITION", POSITION));
                        parameterlist.Add(new OracleParameter(":MEMO", MEMO));
                        int executeNum = _oraConnection.ExecuteNoQuery(sql, parameterlist);
                        if (executeNum > 0)
                        {
                            ret.Add(APP_NAME + ";" + DEPT_CODE + ";" + EMP_NO + ";" + PARAMETER_NAME + ";初始化comm.app_configer_parameter插入成功");
                        }
                        else
                        {
                            ret.Add(APP_NAME + ";" + DEPT_CODE + ";" + EMP_NO + ";" + PARAMETER_NAME + ";初始化comm.app_configer_parameter插入失败");
                        }
                    }
                  
                }
                _oraConnection.Commit();
            }
            catch (Exception ex)
            {
                _oraConnection.RollBack();
                throw ex;
            }
            finally
            {
                _oraConnection.DisConnect();
            }
            return ret;
        }
        /// <summary>
        /// 执行查询语句，返回DataSet
        /// </summary>
        /// <param name="SQLString">查询语句</param>
        /// <returns>DataSet</returns>
        public DataSet Query(string SQLString, params OracleParameter[] cmdParms)
        {
            ConnectionStringHelper.CheckOracleConnection(ref _oraConnection);
            using (OracleConnection connection = new OracleConnection(_oraConnection.ConnectionString))
            {
                OracleCommand cmd = new OracleCommand();
                PrepareCommand(cmd, connection, null, SQLString, cmdParms);
                using (OracleDataAdapter da = new OracleDataAdapter(cmd))
                {
                    DataSet ds = new DataSet();
                    try
                    {
                        da.Fill(ds, "ds");
                        cmd.Parameters.Clear();
                    }
                    catch (System.Data.OracleClient.OracleException ex)
                    {
                        throw new Exception(ex.Message+"|SQL:"+SQLString);
                    }
                    finally
                    {
                        cmd.Dispose();
                        connection.Close();
                    }
                    return ds;
                }
            }
        }
        
        /// <summary>
        /// 获取表中数据
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        public DataSet GetTableData(string tableName, string filter)
        {
            ConnectionStringHelper.CheckOracleConnection(ref _oraConnection);
            
            string sql = "SELECT * FROM " + tableName;
            
            if (filter.Trim().Length > 0)
            {
                sql += " WHERE " + filter;
            }
            
            return _oraConnection.SelectData(sql, tableName);
        }
        /// <summary>
        /// 保存数据集中 所有表中数据
        /// </summary>
        /// <param name="dsChanged"></param>
        /// <returns></returns>
        public bool SaveDataSet_AllData(DataSet dsChanged)
        {
            ConnectionStringHelper.CheckOracleConnection(ref _oraConnection);
            _oraConnection.BeginTrans();
            try
            {

                foreach (DataTable dt in dsChanged.Tables)
                {
                    if (dsChanged != null && dsChanged.HasChanges())
                    {
                        _oraConnection.Update(ref dsChanged, dt.TableName, string.Empty);
                    }
                }
                _oraConnection.Commit();
                return true;
            }
            catch (Exception ex)
            {
                _oraConnection.RollBack();
                throw ex;
            }
        }
        /// <summary>
        /// 保存表中数据
        /// </summary>
        /// <param name="dsChanged"></param>
        /// <returns></returns>
        public bool SaveTableData(DataSet dsChanged)
        {
            ConnectionStringHelper.CheckOracleConnection(ref _oraConnection);
            _oraConnection.Update(ref dsChanged, dsChanged.Tables[0].TableName, string.Empty);
            dsChanged.AcceptChanges();
            return true;
        }
        
        /// <summary>
        /// 多表保存
        /// </summary>
        /// <param name="dsChanged1"></param>
        /// <param name="dsChanged2"></param>
        /// <param name="dsChanged3"></param>
        /// <returns></returns>
        public bool SaveTablesData(DataSet dsChanged1, DataSet dsChanged2, DataSet dsChanged3)
        {
            ConnectionStringHelper.CheckOracleConnection(ref _oraConnection);
            _oraConnection.BeginTrans();
            try
            {
                if (dsChanged1 != null && dsChanged1.HasChanges())
                {
                    _oraConnection.Update(ref dsChanged1, dsChanged1.Tables[0].TableName, string.Empty);
                }
                if (dsChanged2 != null && dsChanged2.HasChanges())
                {
                    _oraConnection.Update(ref dsChanged2, dsChanged2.Tables[0].TableName, string.Empty);
                }
                if (dsChanged3 != null && dsChanged3.HasChanges())
                {
                    _oraConnection.Update(ref dsChanged3, dsChanged3.Tables[0].TableName, string.Empty);
                }
                _oraConnection.Commit();
                return true;
            }
            catch(Exception ex)
            {
                _oraConnection.RollBack();
                throw ex;
            }
        }        
        
        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="arrSql"></param>
        /// <param name="dataSets"></param>
        /// <returns></returns>
        public bool SaveData(ArrayList arrSql, object[] arrDataSet)
        {
            ConnectionStringHelper.CheckOracleConnection(ref _oraConnection);
            _oraConnection.BeginTrans();
            try
            {
                // SQL语句
                if (arrSql != null && arrSql.Count > 0)
                {
                    _oraConnection.ExecuteNoQuery(ref arrSql);
                }
                // DataSet 
                if (arrDataSet != null)
                {
                    DataSet ds = null;
                    for(int i = 0; i < arrDataSet.Length; i++)
                    {
                        ds = arrDataSet[i] as DataSet;
                        if (ds != null && ds.HasChanges())
                        {
                            _oraConnection.Update(ref ds, ds.Tables[0].TableName, string.Empty);
                        }
                    }
                }
                _oraConnection.Commit();
                return true;
            }
            catch(Exception ex)
            {
                _oraConnection.RollBack();
                string exStr = +arrSql.Count > 0 ? arrSql[0].ToString() : string.Empty;
                throw new Exception(ex.Message + "|SQL" +exStr);
            }
        }
        
        /// <summary>
        /// 执行SQL语句
        /// </summary>
        /// <returns></returns>
        public bool ExecuteSql(string sql)
        {
            ConnectionStringHelper.CheckOracleConnection(ref _oraConnection);
            _oraConnection.ExecuteNoQuery(sql);
            return true;            
        }

        /// <summary>
        /// 执行存储过程		
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <param name="rowsAffected">影响的行数</param>
        /// <returns></returns>
        public void ExecuteProcedure(string storedProcName, IDataParameter[] parameters)
        {
            ConnectionStringHelper.CheckOracleConnection(ref _oraConnection);
            using (OracleConnection connection = new OracleConnection(_oraConnection.ConnectionString))
            {
                connection.Open();
                OracleCommand command = BuildStoredProcedureCommand(connection, storedProcName, parameters);
                command.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// 执行存储过程(带返回结果集)		
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <returns></returns>
        public DataSet ExecuteProcedureWithResult(string storedProcName, IDataParameter[] parameters)
        {
            ConnectionStringHelper.CheckOracleConnection(ref _oraConnection);
            using (OracleConnection connection = new OracleConnection(_oraConnection.ConnectionString))
            {
                connection.Open();
                DataSet ds = new DataSet();
                OracleCommand command = BuildStoredProcedureCommand(connection, storedProcName, parameters);
                //OracleDataReader odr =command.ExecuteReader();
                OracleDataAdapter oda = new OracleDataAdapter();
                oda.SelectCommand = command;
                oda.Fill(ds);
                return ds;
            }
        }       
        
        private void PrepareCommand(OracleCommand cmd, OracleConnection conn, OracleTransaction trans, string cmdText, OracleParameter[] cmdParms)
        {
            if (conn.State != ConnectionState.Open)
                conn.Open();
            cmd.Connection = conn;
            cmd.CommandText = cmdText;
            if (trans != null)
                cmd.Transaction = trans;
            cmd.CommandType = CommandType.Text;//cmdType;
            if (cmdParms != null)
            {
                foreach (OracleParameter parm in cmdParms)
                    cmd.Parameters.Add(parm);
            }
        }

        /// <summary>
        /// 构建 存储过程 OracleCommand
        /// </summary>
        /// <param name="connection">数据库连接</param>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <returns>OracleCommand</returns>
        private static OracleCommand BuildStoredProcedureCommand(OracleConnection connection, string storedProcName, IDataParameter[] parameters)
        {
            OracleCommand command = new OracleCommand(storedProcName, connection);
            command.CommandType = CommandType.StoredProcedure;
            foreach (OracleParameter parameter in parameters)
            {
                command.Parameters.Add(parameter);
            }
            return command;
        }
    }
}
