﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using System.IO;

using DevExpress.XtraPrinting.Preview;
using DevExpress.XtraReports.UI;
using DevExpress.XtraReports.UserDesigner;
using DevExpress.XtraEditors;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraReports.Parameters;
using PlatCommon.Base01;
using PlatCommon.Base02;
using PlatCommon.SysBase;

namespace PlatCommon.Common
{
    /// <summary>
    /// XtraReportHelper 助手
    /// </summary>
    public class XtraReportHelper
    {
        /// <summary>
        /// 打印机
        /// </summary>
        public static string PrinterName = string.Empty;
        // API声明 王代迪2017-02-07
        [System.Runtime.InteropServices.DllImport("kernel32.dll")]
        public static extern IntPtr GetModuleHandle(string name);
        public static string Appcode;

        /// <summary>
        /// 加载模板
        /// </summary>
        public static void LoadTemplet(ref DocumentViewer docViewer, ref XtraReport mReport, string templeteFile, string appCode)
        {
            string templeteFileNameFull = GetTempleteFileNameFull(templeteFile, appCode);

            if (System.IO.File.Exists(templeteFileNameFull) == false)
            {
                XtraMessageBox.Show("模板文件缺失! [" + @"Reports\" + appCode + "\\" + templeteFile + ".repx]");
                return;
            }
            Appcode = appCode;
            if (mReport == null) mReport = new XtraReport();
            mReport.LoadLayout(templeteFileNameFull);
            docViewer.DocumentSource = mReport;
            mReport.CreateDocument();
        }


        /// <summary>
        /// 增加数据库取打印设计 20230727 liul
        /// </summary>
        /// <param name="templetFileName">模板名称</param>
        /// <param name="ds">数据集</param>
        /// <param name="appCode">应用名称</param>
        public static void Print(string templetFileName, DataSet ds, string appCode)
        {
            // 加载模板
            XtraReport rp = new XtraReport();
            string sql = "SELECT  A.PREVIEW_FLAG  ,A.REPORT_REPX ,A.USER_FLAG FROM REPORT_HIS_REPX   A   WHERE A.APP_NAME = '" + appCode + "' AND A.REPORT_NAME= '" + templetFileName + "'"
                + " AND HIS_UNIT_CODE = " + Cs02StringHelper.SqlConvert(SystemParm.HisUnitCode);
            DataSet dsReport = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql);
            string flag = "0";
            MemoryStream ms = new MemoryStream();
            if (dsReport != null && dsReport.Tables[0].Rows.Count > 0)
            {
                string useFlae = dsReport.Tables[0].Rows[0]["USER_FLAG"].ToString();
                if (useFlae.Equals("1"))
                {
                    flag = dsReport.Tables[0].Rows[0]["PREVIEW_FLAG"].ToString();
                    byte[] buffer = Encoding.UTF8.GetBytes(dsReport.Tables[0].Rows[0]["REPORT_REPX"].ToString());
                    ms.Write(buffer, 0, buffer.Length);
                    rp.LoadLayout(ms);
                    ms.Close();
                }
                else
                {
                    XtraMessageBox.Show("您设计的报表打印未启用，请联系信息科进行维护！");
                    return;
                }
            }
            rp.PrinterName = PrinterName;
            BindSingleRowTable(ds);
            // 加载数据
            rp.DataSource = ds;

            // 打印或预览
            ReportPrintTool rpt = new ReportPrintTool(rp);
            if (flag.Equals("1"))
            {
                rpt.ShowPreview();
            }
            else
            {
                rpt.Print();
            }
        }
        /// <summary>
        /// 增加数据库取打印传参设计 20230727 liul
        /// </summary>
        /// <param name="templetFileName"></param>
        /// <param name="ds"></param>
        /// <param name="listParameters"></param>
        /// <param name="appCode"></param>
        public static void Print(string templetFileName, DataSet ds, List<Parameter> listParameters, string appCode)
        {
            // 加载模板
            XtraReport rp = new XtraReport();
            string sql = "SELECT  A.PREVIEW_FLAG  ,A.REPORT_REPX ,A.USER_FLAG FROM REPORT_HIS_REPX   A   WHERE A.APP_NAME = '" + appCode + "' AND A.REPORT_NAME= '" + templetFileName + "'"
                + " AND HIS_UNIT_CODE = " + Cs02StringHelper.SqlConvert(SystemParm.HisUnitCode);
            DataSet dsReport = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql);
            string flag = "0";
            MemoryStream ms = new MemoryStream();
            if (dsReport != null && dsReport.Tables[0].Rows.Count > 0)
            {
                string useFlae = dsReport.Tables[0].Rows[0]["USER_FLAG"].ToString();
                if (useFlae.Equals("1"))
                {
                    flag = dsReport.Tables[0].Rows[0]["PREVIEW_FLAG"].ToString();
                    byte[] buffer = Encoding.UTF8.GetBytes(dsReport.Tables[0].Rows[0]["REPORT_REPX"].ToString());
                    ms.Write(buffer, 0, buffer.Length);
                    rp.LoadLayout(ms);
                    ms.Close();
                }
                else
                {
                    XtraMessageBox.Show("您设计的报表打印未启用，请联系信息科进行维护！");
                    return;
                }
            }
            rp.PrinterName = PrinterName;
            //加载参数列表
            foreach (Parameter p in listParameters)
            {
                String pName = p.Name;
                if (rp.Parameters[pName] != null)
                {
                    rp.Parameters[pName].Value = p.Value;
                }
            }
            rp.RequestParameters = false;
            BindSingleRowTable(ds);
            // 加载数据
            rp.DataSource = ds;

            // 打印或预览
            ReportPrintTool rpt = new ReportPrintTool(rp);
            rpt.AutoShowParametersPanel = false;
            if (flag.Equals("1"))
            {
                rpt.ShowPreviewDialog();
            }
            else
            {
                rpt.Print();
            }
        }
        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="templetFileName">报表名称</param>
        /// <param name="ds">数据集</param>
        /// <param name="blnPreview">是否预览，True是，False否</param>
        public static void Print(string templetFileName, DataSet ds, bool blnPreview, string appCode)
        {
            XtraReport rp = new XtraReport();
            Stream stream = GetRepxStreamTemplate(appCode, templetFileName, PlatCommon.SysBase.SystemParm.HisUnitCode);
            if (stream != null && stream.Length > 0)
            {
                //加载模板
                rp = XtraReport.FromStream(stream);
            }
            else
            {
                // 加载模板
                rp.LoadLayout(GetTempleteFileNameFull(templetFileName, appCode));
            }
            if (!string.IsNullOrEmpty(PrinterName))
            {
                rp.PrinterName = PrinterName;
            }
            // 加载数据
            rp.DataSource = ds;

            // 打印或预览
            ReportPrintTool rpt = new ReportPrintTool(rp);
            if (blnPreview)
            {
                rpt.ShowPreviewDialog();
            }
            else
            {
                rpt.Print();
            }
        }


        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="templetFileName">报表名称</param>
        /// <param name="ds">数据集</param>
        /// <param name="blnPreview">是否预览，True是，False否</param>
        /// <param name="thread"></param>
        public static void Print(string templetFileName, DataSet ds, bool blnPreview, bool thread, string appCode)
        {
            // 加载模板
            XtraReport rp = new XtraReport();
            rp.LoadLayout(GetTempleteFileNameFull(templetFileName, appCode));
            rp.PrinterName = PrinterName;

            // 加载数据
            rp.DataSource = ds;

            // 打印或预览
            ReportPrintTool rpt = new ReportPrintTool(rp);
            if (blnPreview)
            {
                if (thread)
                {
                    rpt.ShowPreview();
                }
                else
                {
                    rpt.ShowPreviewDialog();
                }
            }
            else
            {
                rpt.Print();
            }
        }
        /// <summary>
        /// 打印(带参数)
        /// </summary>
        /// <param name="templetFileName">报表名称</param>
        /// <param name="ds">数据集</param>
        /// <param name="listParameters">参数列表</param>
        /// <param name="blnPreview">是否预览，True是，False否</param>
        public static void Print(string templetFileName, DataSet ds, List<Parameter> listParameters, bool blnPreview, string appCode)
        {
            // 加载模板
            XtraReport rp = new XtraReport();
            Stream stream = GetRepxStreamTemplate(appCode, templetFileName, PlatCommon.SysBase.SystemParm.HisUnitCode);
            if (stream != null && stream.Length > 0)
            {
                //加载模板
                rp = XtraReport.FromStream(stream);
            }
            else
            {
                rp.LoadLayout(GetTempleteFileNameFull(templetFileName, appCode));
            }
            rp.PrinterName = PrinterName;

            // 加载数据
            rp.DataSource = ds;

            //加载参数列表
            foreach (Parameter p in listParameters)
            {
                String pName = p.Name;
                if (rp.Parameters[pName] != null)
                {
                    rp.Parameters[pName].Value = p.Value;
                }
            }
            rp.RequestParameters = false;

            // 打印或预览
            ReportPrintTool rpt = new ReportPrintTool(rp);
            rpt.AutoShowParametersPanel = false;
            if (blnPreview)
            {
                rpt.ShowPreview();
            }
            else
            {
                rpt.Print();
            }
        }

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="templetFileName"></param>
        /// <param name="ds"></param>
        /// <param name="blnPreview"></param>
        /// <param name="thread"></param>
        /// <param name="listParameters"></param>
        public static void Print(string templetFileName, DataSet ds, bool blnPreview, bool thread, List<Parameter> listParameters, string appCode)
        {
            // 加载模板
            XtraReport rp = new XtraReport();
            string sql = "SELECT  A.PREVIEW_FLAG  ,A.REPORT_REPX ,A.USER_FLAG FROM REPORT_HIS_REPX   A   WHERE A.APP_NAME = '" + appCode + "' AND A.REPORT_NAME= '" + templetFileName + "'"
                + " AND HIS_UNIT_CODE = " + Cs02StringHelper.SqlConvert(SystemParm.HisUnitCode);
            DataSet dsReport = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql);
            string flag = "0";
            MemoryStream ms = new MemoryStream();
            if (dsReport != null && dsReport.Tables[0].Rows.Count > 0)
            {
                string useFlae = dsReport.Tables[0].Rows[0]["USER_FLAG"].ToString();
                if (useFlae.Equals("1"))
                {
                    flag = dsReport.Tables[0].Rows[0]["PREVIEW_FLAG"].ToString();
                    byte[] buffer = Encoding.UTF8.GetBytes(dsReport.Tables[0].Rows[0]["REPORT_REPX"].ToString());
                    ms.Write(buffer, 0, buffer.Length);
                    rp.LoadLayout(ms);
                    ms.Close();
                }
                else
                {
                    XtraMessageBox.Show("您设计的报表打印未启用，请联系信息科进行维护！");
                    return;
                }
            }

            // 加载数据
            rp.DataSource = ds;

            //加载参数列表
            foreach (Parameter p in listParameters)
            {
                String pName = p.Name;
                if (rp.Parameters[pName] != null)
                {
                    rp.Parameters[pName].Value = p.Value;
                }
            }
            rp.RequestParameters = false;

            // 打印或预览
            ReportPrintTool rpt = new ReportPrintTool(rp);
            rpt.AutoShowParametersPanel = false;
            if (flag.Equals("1"))
            {
                if (thread)
                {
                    rpt.ShowPreview();
                }
                else
                {
                    rpt.ShowPreviewDialog();
                }
            }
            else
            {
                rpt.Print();
            }
        }

        /// <summary>
        /// 打印，传打印机名称参数
        /// </summary>
        /// <param name="templetFileName">报表名称</param>
        /// <param name="ds">数据集</param>
        /// <param name="blnPreview">是否预览，True是，False否</param>
        /// <param name="appCode">模块代码</param>
        /// <param name="printerName">打印机名称</param>
        public static void Print(string templetFileName, DataSet ds, bool blnPreview, string appCode, string printerName)
        {
            // 加载模板
            XtraReport rp = new XtraReport();
            rp.LoadLayout(GetTempleteFileNameFull(templetFileName, appCode));
            rp.PrinterName = printerName;

            // 加载数据
            rp.DataSource = ds;

            // 打印或预览
            ReportPrintTool rpt = new ReportPrintTool(rp);
            if (blnPreview)
            {
                rpt.ShowPreview();
            }
            else
            {
                rpt.Print();
            }
        }


        /// <summary>
        /// 打印，传打印机名称参数
        /// </summary>
        /// <param name="templetFileName">报表名称</param>
        /// <param name="ds">数据集</param>
        /// <param name="blnPreview">是否预览，True是，False否</param>
        /// <param name="thread">线程</param>
        /// <param name="appCode">模块代码</param>
        /// <param name="printerName">打印机名称</param>
        public static void Print(string templetFileName, DataSet ds, bool blnPreview, bool thread, string appCode, string printerName)
        {
            // 加载模板
            XtraReport rp = new XtraReport();
            rp.LoadLayout(GetTempleteFileNameFull(templetFileName, appCode));
            rp.PrinterName = printerName;

            // 加载数据
            rp.DataSource = ds;

            // 打印或预览
            ReportPrintTool rpt = new ReportPrintTool(rp);
            if (blnPreview)
            {
                if (thread)
                {
                    rpt.ShowPreview();
                }
                else
                {
                    rpt.ShowPreviewDialog();
                }
            }
            else
            {
                rpt.Print();
            }
        }
        /// <summary>
        /// 打印，传打印机名称参数
        /// </summary>
        /// <param name="templetFileName">报表名称</param>
        /// <param name="ds">数据集</param>
        /// <param name="listParameters">参数集合</param>
        /// <param name="blnPreview">是否预览，True是，False否</param>
        /// <param name="appCode">模块代码</param>
        /// <param name="printerName">打印机名称</param>
        public static void Print(string templetFileName, DataSet ds, List<Parameter> listParameters, bool blnPreview, string appCode, string printerName)
        {
            // 加载模板
            XtraReport rp = new XtraReport();
            Stream stream = GetRepxStreamTemplate(appCode, templetFileName, PlatCommon.SysBase.SystemParm.HisUnitCode);
            if (stream != null && stream.Length > 0)
            {
                //加载模板
                rp = XtraReport.FromStream(stream);
            }
            else
            {
                rp.LoadLayout(GetTempleteFileNameFull(templetFileName, appCode));
            }
            rp.PrinterName = printerName;

            // 加载数据
            rp.DataSource = ds;

            //加载参数列表
            foreach (Parameter p in listParameters)
            {
                String pName = p.Name;
                if (rp.Parameters[pName] != null)
                {
                    rp.Parameters[pName].Value = p.Value;
                }
            }
            rp.RequestParameters = false;

            // 打印或预览
            ReportPrintTool rpt = new ReportPrintTool(rp);
            rpt.AutoShowParametersPanel = false;
            if (blnPreview)
            {
                rpt.ShowPreview();
            }
            else
            {
                rpt.Print();
            }
        }

        /// <summary>
        /// 打印，传打印机名称参数
        /// </summary>
        /// <param name="templetFileName">报表名称</param>
        /// <param name="ds">数据集</param>
        /// <param name="blnPreview">是否预览，True是，False否</param>
        /// <param name="thread">线程</param>
        /// <param name="listParameters">参数集合</param>
        /// <param name="appCode">模块代码</param>
        /// <param name="printerName">打印机名称</param>
        public static void Print(string templetFileName, DataSet ds, bool blnPreview, bool thread, List<Parameter> listParameters, string appCode, string printerName)
        {
            // 加载模板
            XtraReport rp = new XtraReport();
            rp.LoadLayout(GetTempleteFileNameFull(templetFileName, appCode));
            rp.PrinterName = printerName;

            // 加载数据
            rp.DataSource = ds;

            //加载参数列表
            foreach (Parameter p in listParameters)
            {
                String pName = p.Name;
                if (rp.Parameters[pName] != null)
                {
                    rp.Parameters[pName].Value = p.Value;
                }
            }
            rp.RequestParameters = false;

            // 打印或预览
            ReportPrintTool rpt = new ReportPrintTool(rp);
            rpt.AutoShowParametersPanel = false;
            if (blnPreview)
            {
                if (thread)
                {
                    rpt.ShowPreview();
                }
                else
                {
                    rpt.ShowPreviewDialog();
                }
            }
            else
            {
                rpt.Print();
            }
        }
        /// <summary>
        /// 把架构数据写入\Schema目录
        /// </summary>
        public static void WriteSchemaData(string templetFileName, ref XtraReport mReport, Hashtable hasParam, string appCode)
        {
            DataSet ds = GetPrintData(templetFileName, hasParam, appCode);

            string fileName = System.IO.Path.Combine(Application.StartupPath, "Reports\\" + appCode);
            #region 判断是否为开发环境 王代迪2017-02-07
            //if (GetModuleHandle("devenv.exe").ToInt32() == 0)
            if (System.Diagnostics.Debugger.IsAttached)
            {
                fileName = System.IO.Path.Combine(Application.StartupPath.Replace(@"bin\Debug", ""), "Reports\\" + appCode);
            }
            #endregion
            if (System.IO.Directory.Exists(fileName) == false)
            {
                System.IO.Directory.CreateDirectory(fileName);
            }

            fileName = System.IO.Path.Combine(fileName, templetFileName + ".xml");
            ds.WriteXml(fileName, XmlWriteMode.WriteSchema);
        }
        public static void WriteSchemaData(string templetFileName, string sql, ref XtraReport mReport, Hashtable hasParam, string appCode)
        {
            DataSet ds = GetPrintData(sql, hasParam, appCode, true);

            string fileName = System.IO.Path.Combine(Application.StartupPath, "Reports\\" + appCode);
            if (System.IO.Directory.Exists(fileName) == false)
            {
                System.IO.Directory.CreateDirectory(fileName);
            }

            fileName = System.IO.Path.Combine(fileName, templetFileName + ".xml");
            ds.WriteXml(fileName, XmlWriteMode.WriteSchema);
        }


        /// <summary>
        /// 生成打印数据
        /// </summary>
        /// <returns></returns>
        public static DataSet GetPrintData(ref XtraReport mReport, Hashtable hasParam)
        {
            DataSet dsResult = GetData(ref mReport, hasParam);

            return CreatePrintData(new DataTable[] { dsResult.Tables[0] }, hasParam);
        }


        /// <summary>
        /// 生成打印数据
        /// </summary>
        /// <param name="templetName"></param>
        /// <param name="hasParam"></param>
        /// <returns></returns>
        public static DataSet GetPrintData(string templetName, Hashtable hasParam, string appCode)
        {
            DataSet dsResult = GetData(templetName, hasParam, appCode);

            return CreatePrintData(new DataTable[] { dsResult.Tables[0] }, hasParam);
        }
        /// <summary>
        /// 生成打印数据
        /// </summary>
        /// <param name="templetName"></param>
        /// <param name="hasParam"></param> 
        /// /// <param name="isSql"></param>
        /// <returns></returns>
        public static DataSet GetPrintData(string templetName, Hashtable hasParam, string appCode, bool isSql = false, string[] tablename = null)
        {
            DataTable[] tableList;
            if (isSql)
            {
                string[] sqlArray = templetName.Split(';');
                tableList = new DataTable[sqlArray.Length];
                for (int i = 0; i < sqlArray.Length; i++)
                {
                    if (!string.IsNullOrEmpty(sqlArray[i]))
                    {
                        DataSet ds = GetData(sqlArray[i], hasParam, appCode, isSql);
                        //ds.Tables[0].TableName = "Table_" + (i + 1);
                        ds.Tables[0].TableName = tablename != null ? tablename[i] : "Table_" + (i + 1);

                        tableList[i] = ds.Tables[0];
                    }

                }
            }
            else
            {
                DataSet dsResult = GetData(templetName, hasParam, appCode, isSql);
                tableList = new DataTable[] { dsResult.Tables[0] };
            }

            return CreatePrintData(tableList, hasParam);
        }

        /// <summary>
        /// 生成打印数据
        /// </summary>
        /// <param name="templetName"></param>
        /// <param name="hasParam"></param> 
        /// /// <param name="isSql"></param>
        /// <returns></returns>
        public static DataSet GetPrintData(string templetName, Hashtable hasParam, string appCode, bool isSql = false)
        {
            DataTable[] tableList;
            if (isSql)
            {
                string[] sqlArray = templetName.Split(';');
                tableList = new DataTable[sqlArray.Length];
                for (int i = 0; i < sqlArray.Length; i++)
                {
                    if (!string.IsNullOrEmpty(sqlArray[i]))
                    {
                        DataSet ds = GetData(sqlArray[i], hasParam, appCode, isSql);
                       ds.Tables[0].TableName = "Table_" + (i + 1);
                       // ds.Tables[0].TableName = tablename != null ? tablename[i] : "Table_" + (i + 1);

                        tableList[i] = ds.Tables[0];
                    }

                }
            }
            else
            {
                DataSet dsResult = GetData(templetName, hasParam, appCode, isSql);
                tableList = new DataTable[] { dsResult.Tables[0] };
            }

            return CreatePrintData(tableList, hasParam);
        }


        /// <summary>
        /// 生成打印数据
        /// </summary>
        /// <param name="templetName"></param>
        /// <param name="hasParam"></param>
        /// <param name="reportNo"></param>
        /// <returns></returns>
        public static DataSet GetPrintData(string templetName, Hashtable hasParam, int reportNo, string appCode)
        {
            DataTable[] tableList;
            if (reportNo > 0)
            {
                //DataTable[] tableList;
                string[] sqlArray = templetName.Split(';');
                string sql = sqlArray[reportNo - 1];
                tableList = new DataTable[1];
                DataSet ds = GetData(sql, hasParam, appCode, true);
                ds.Tables[0].TableName = "Table_" + reportNo;
                tableList[0] = ds.Tables[0];
                //return CreatePrintData(tableList, hasParam);
            }
            else
            {
                //start.by.gwz.2022.9.23
                string[] sqlArray = templetName.Split(';');
                tableList = new DataTable[sqlArray.Length];
                for (int i = 0; i < tableList.Length; i++)
                {
                    DataSet ds = GetData(sqlArray[i], hasParam, appCode, true);
                    ds.Tables[0].TableName = "Table_" + (i + 1);
                    tableList[i] = ds.Tables[0];
                }
                //end.by.gwz.2022.9.23

                //DataTable[] tableList;
                //start.by.gwz.2022.9.23
                //string[] sqlArray = templetName.Split(';');
                //string sql = sqlArray[0];
                //tableList = new DataTable[1];
                //DataSet ds = GetData(sql, hasParam, appCode, true);
                //ds.Tables[0].TableName = "Table_" + 1;
                //tableList[0] = ds.Tables[0];
                //end.by.gwz.2022.9.23
                //return CreatePrintData(tableList, hasParam);
            }
            return CreatePrintData(tableList, hasParam);
        }

        /// <summary>
        /// 生成打印数据 - 取数据库语句
        /// </summary>
        /// <param name="templetName"></param>
        /// <param name="hasParam"></param>
        /// <returns></returns>
        public static DataSet GetPrintData_DataBase(string templetName, Hashtable hasParam, string appCode)
        {
            try
            {
                DataSet dsResult = GetData_DataBase(templetName, hasParam, appCode);

                if (dsResult != null && dsResult.Tables.Count > 0)
                {
                    DataTable[] dtArr = new DataTable[dsResult.Tables.Count];
                    for (int i = 0; i < dtArr.Length; i++)
                    {
                        dtArr[i] = dsResult.Tables[i];
                    }
                    return CreatePrintData(dtArr, hasParam);
                }
                return CreatePrintData(new DataTable[] { dsResult.Tables[0] }, hasParam);
            }
            catch (Exception ex)
            {
                throw new Exception("\r\nXtraReportHelper-GetPrintData_DataBase-Exception: " + ex.Message);
            }
        }



        /// <summary>
        /// 获取完整的模板文件名称
        /// </summary>
        /// <returns></returns>
        public static string GetTempleteFileNameFull(string templetFileName, string appCode)
        {
            #region 判断是否为开发环境 王代迪2017-02-07
            string fileName = Path.Combine(Application.StartupPath, @"Reports\" + appCode + "\\" + templetFileName + ".repx");
            //if (GetModuleHandle("devenv.exe").ToInt32() == 0)
            if (System.Diagnostics.Debugger.IsAttached)
            {
                fileName = System.IO.Path.Combine(Application.StartupPath.Replace(@"bin\Debug", ""), "Reports\\" + appCode + "\\") + templetFileName + ".repx";
            }

            if (File.Exists(fileName) == false)
            {
                MessageBox.Show("文件不存在: " + fileName);
            }

            return fileName;
            #endregion
            //return Path.Combine(Application.StartupPath, @"Reports\" + templetFileName + ".repx");
        }


        /// <summary>
        /// 获取完整的模板文件名称
        /// </summary>
        /// <returns></returns>
        public static string GetTemplateFileNameFull(string templetFileName)
        {
            string fileName = Path.Combine(Application.StartupPath, @"Reports\" + templetFileName + ".repx");
            //if (GetModuleHandle("devenv.exe").ToInt32() == 0)
            if (System.Diagnostics.Debugger.IsAttached)
            {
                fileName = System.IO.Path.Combine(Application.StartupPath.Replace(@"bin\Debug", ""), "Reports\\") + templetFileName + ".repx";
            }

            return fileName;
        }


        /// <summary>
        /// 生成配置文件结构
        /// </summary>
        /// <returns></returns>
        public static DataSet GetConfigSchema()
        {
            DataSet ds = new DataSet("DataGridView_Cols_Config");
            DataTable dt = ds.Tables.Add("DataGridView_Cols");

            dt.Columns.Add("DATA_PROPERTY_NAME", typeof(System.String));    // 值字段
            dt.Columns.Add("HEADER_TEXT", typeof(System.String));           // 列标题            
            dt.Columns.Add("STYLE_FORMAT", typeof(System.String));          // 格式
            dt.Columns.Add("COL_WIDTH", typeof(System.Int32));              // 列宽
            dt.Columns.Add("SHOW_ORDER", typeof(System.Int32));             // 显示顺序
            dt.Columns.Add("SHOW_VISIBLE", typeof(System.String));          // 显示隐藏属性
            return ds;
        }
        /// <summary>
        /// 获取DataGridView的列配置信息
        /// </summary>
        /// <param name="dgvId"></param>
        /// <returns></returns>
        public static DataSet GetColumnConfig(string dgvId)
        {
            string fileName = Path.Combine(Cs01FileHelper.GetCurrDllPath(), "Config");
            fileName = Path.Combine(fileName, dgvId + ".xml");

            if (File.Exists(fileName))
            {
                DataSet ds = new DataSet();
                ds.ReadXml(fileName, XmlReadMode.ReadSchema);
                return ds;
            }
            else
            {
                return GetConfigSchema();
            }
        }
        /// <summary>
        /// 通过配置信息生成DataGridView的列
        /// </summary>
        public static void AddColByConfig(GridView dgv, DataSet ds)
        {
            try
            {
                if (Cs02DataSetHelper.HasRecord(ds) == false) return;

                // 显示已配置的列
                DataRow[] drFind = ds.Tables[0].Select("", "SHOW_ORDER ASC");

                for (int i = 0; i < drFind.Length; i++)
                {
                    DataRow dr = drFind[i];

                    string colName = dr["DATA_PROPERTY_NAME"].ToString();   // 列名
                    string styleFormat = dr["STYLE_FORMAT"].ToString();     // 格式

                    // 判断列是否存在
                    GridColumn dgvColumn = null;
                    foreach (GridColumn dgvCol in dgv.Columns)
                    {
                        if (dgvCol.Name.Equals(colName))
                        {
                            dgvColumn = dgvCol;
                            int sn = -1;
                            if (!int.TryParse(dr["SHOW_ORDER"].ToString(), out sn))
                            {
                                sn = -1;
                            }
                            dgvColumn.VisibleIndex = sn;
                            break;
                        }
                    }

                    if (dgvColumn == null) continue;

                    // 设置列宽
                    dgvColumn.Width = Converter.ToInt(dr["COL_WIDTH"].ToString());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }


        /// <summary>
        /// 通过配置信息生成DataGridView的列
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="dgvId"></param>
        public static void AddColByConfig(GridView dgv, string dgvId)
        {
            DataSet ds = GetColumnConfig(dgvId);
            AddColByConfig(dgv, ds);
        }

        /// <summary>
        /// 保存列宽
        /// </summary>
        public static void SaveColumnConfig(GridView dgv, string dgvId)
        {
            // 获取本地配置文件
            DataSet ds = GetColumnConfig(dgvId);

            // 更新内容
            foreach (GridColumn dgvCol in dgv.Columns)
            {
                if (dgvCol.Visible == true)
                {
                    DataRow drEdit = null;

                    string colName = dgvCol.Name;
                    string filter = "DATA_PROPERTY_NAME = '" + colName + "'";
                    DataRow[] drFind = ds.Tables[0].Select(filter);
                    if (drFind.Length == 0)
                    {
                        drEdit = ds.Tables[0].NewRow();
                        drEdit["DATA_PROPERTY_NAME"] = colName;                 // 列名
                        ds.Tables[0].Rows.Add(drEdit);
                    }
                    else
                    {
                        drEdit = drFind[0];
                    }

                    drEdit["HEADER_TEXT"] = dgvCol.Caption;                     // 标题                   
                    drEdit["COL_WIDTH"] = dgvCol.Width;                         // 列宽
                    drEdit["SHOW_ORDER"] = dgvCol.VisibleIndex;                    // 显示顺序
                }
            }

            // 保存本地配置文件
            SaveColumnConfig(ds, dgvId);
        }
        /// <summary>
        /// 保存列的配置文件
        /// </summary>
        /// <param name="dsColConfig"></param>
        /// <param name="dgvId"></param>
        public static void SaveColumnConfig(DataSet dsColConfig, string dgvId)
        {
            // 保存
            string fileName = Path.Combine(Cs01FileHelper.GetCurrDllPath(), "Config");
            if (Directory.Exists(fileName) == false)
            {
                Directory.CreateDirectory(fileName);
            }

            fileName = Path.Combine(fileName, dgvId + ".xml");

            dsColConfig.WriteXml(fileName, XmlWriteMode.WriteSchema);
        }
        #region 共通函数
        /// <summary>
        /// 获取模板的SQL语句
        /// </summary>
        /// <param name="mReport"></param>
        /// <param name="hasParam"></param>
        /// <returns></returns>
        private static string GetSql(ref XtraReport mReport, Hashtable hasParam)
        {
            string sql = string.Empty;

            if (mReport != null)
            {
                sql = Converter.ToString(mReport.Tag);
            }

            if (string.IsNullOrEmpty(sql))
            {
                throw new Exception("请在报表模板的标签中设置查询SQL语句!");
            }

            return GetSqlText(sql, hasParam);
        }

        /// <summary>
        /// 获取模板的SQL语句
        /// </summary>
        /// <param name="templetName"></param>
        /// <param name="hasParam"></param>
        /// <returns></returns>
        private static string GetSql(string templetName, Hashtable hasParam, string appCode)
        {
            string sql = string.Empty;

            templetName = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\" + appCode + "\\" + templetName + ".SQL");
            if (File.Exists(templetName) == false)
            {
                throw new Exception(templetName + " 不存在!");
            }

            sql = Cs01FileHelper.GetFileContent(templetName);

            string[] arrParts = sql.Split(';');

            if (string.IsNullOrEmpty(sql) || arrParts.Length == 0 || string.IsNullOrEmpty(arrParts[0]))
            {
                throw new Exception(templetName + " 中没有内容!");
            }

            if (arrParts.Length >= 2)
            {
                sql = arrParts[1];
            }

            return GetSqlText(sql, hasParam);
        }

        /// <summary>
        /// 获取模板的SQL语句 取数据库 
        /// </summary>
        /// <param name="templetName"></param>
        /// <param name="hasParam"></param>
        /// <returns></returns>
        private static List<string> GetSql_DataBase(string templetName, Hashtable hasParam, string appCode, Boolean tablename = false)
        {
            try
            {
                List<string> sqlList = new List<string>();
                string sql = string.Empty;
                string strREPORT_HIS_REPX = "";
                if (tablename)
                {
                    strREPORT_HIS_REPX = "SELECT  REPORT_TABLENAME FROM REPORT_HIS_REPX  ";
                }
                else
                {
                    strREPORT_HIS_REPX = "SELECT  REPORT_SQL FROM REPORT_HIS_REPX  ";

                }
                strREPORT_HIS_REPX += " WHERE APP_NAME = '" + appCode + "'";
                strREPORT_HIS_REPX += " AND REPORT_NAME = '" + templetName + "'";
                strREPORT_HIS_REPX += " AND HIS_UNIT_CODE = " + Cs02StringHelper.SqlConvert(SystemParm.HisUnitCode);
                sql = new NM_Service.NMService.ServerPublicClient().GetSingleValue(strREPORT_HIS_REPX).Trim();

                //sql = Cs01FileHelper.GetFileContent(templetName);
                if (!string.IsNullOrEmpty(sql))
                {
                    if (!sql.EndsWith(";"))
                    {
                        sql = sql + ";";
                    }
                    string[] arrParts = sql.Split(';').Where(x => !string.IsNullOrEmpty(x)).ToArray();
                    if (arrParts != null && arrParts.Length > 0)
                    {
                        for (int i = 0; i < arrParts.Length; i++)
                        {
                            sqlList.Add(GetSqlText(arrParts[i], hasParam));
                        }
                    }
                    return sqlList;
                }
                else
                {
                    return new List<string>();
                    //throw new Exception("GetSql_DataBase " + templetName + " 中没有内容!");
                }
            }
            catch (Exception ex)
            {
                throw new Exception("\r\nXtraReportHelper-GetSql_DataBase-Exception: " + ex.Message);
            }

        }

        /// <summary>
        /// 获取模板的SQL语句
        /// </summary>
        /// <param name="templetName"></param>
        /// <param name="hasParam"></param>
        /// <returns></returns>
        private static string GetSql_Tree(string templetName, Hashtable hasParam, string appCode)
        {
            string sql = string.Empty;

            templetName = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\" + appCode + "\\" + templetName + ".SQL");
            if (File.Exists(templetName) == false)
            {
                throw new Exception(templetName + " 不存在!");
            }

            sql = Cs01FileHelper.GetFileContent(templetName);

            string[] arrParts = sql.Split(';');

            if (string.IsNullOrEmpty(sql) || arrParts.Length == 0 || string.IsNullOrEmpty(arrParts[0]))
            {
                throw new Exception(templetName + " 中没有内容!");
            }

            return GetSqlText(arrParts[0], hasParam);
        }


        /// <summary>
        /// 获取报表数据
        /// </summary>
        /// <param name="mReport"></param>
        /// <param name="hasParam"></param>
        /// <returns></returns>
        private static DataSet GetData(ref XtraReport mReport, Hashtable hasParam)
        {
            string sql = GetSql(ref mReport, hasParam);

            NM_Service.NMService.ServerPublicClient dbConn = new NM_Service.NMService.ServerPublicClient();
            return dbConn.GetDataBySql(sql);
        }


        /// <summary>
        /// 获取报表数据
        /// </summary>
        /// <param name="templetName"></param>
        /// <param name="hasParam"></param>
        /// <returns></returns>
        private static DataSet GetData(string templetName, Hashtable hasParam, string appCode)
        {
            string specialCondition = string.Empty;
            if (hasParam != null && hasParam.ContainsKey("一日清单查询条件"))
            {
                specialCondition = hasParam["一日清单查询条件"].ToString();
                hasParam["一日清单查询条件"] = "";
            }

            string sql = GetSql(templetName, hasParam, appCode);

            if (!string.IsNullOrWhiteSpace(specialCondition))
            {
                sql += " " + specialCondition;
            }
            NM_Service.NMService.ServerPublicClient dbConn = new NM_Service.NMService.ServerPublicClient();
            return dbConn.GetDataBySql(sql);
        }

        /// <summary>
        /// 获取报表数据 - 取数据库
        /// </summary>
        /// <param name="templetName"></param>
        /// <param name="hasParam"></param>
        /// <returns></returns>
        private static DataSet GetData_DataBase(string templetName, Hashtable hasParam, string appCode)
        {
            try
            {
                List<string> sqlList = GetSql_DataBase(templetName, hasParam, appCode);
                List<string> tablename = GetSql_DataBase(templetName, hasParam, appCode, true);
                DataSet ds = new DataSet();
                NM_Service.NMService.ServerPublicClient dbConn = new NM_Service.NMService.ServerPublicClient();
                if (sqlList != null && sqlList.Count > 0)
                {
                    for (int i = 0; i < sqlList.Count; i++)
                    {
                        DataTable dt = dbConn.SelectDataTable(sqlList[i], tablename.Count == sqlList.Count ? tablename[i] : "Table_" + (i + 1));
                        ds.Tables.Add(dt.Copy());
                    }
                }
                return ds;
            }
            catch (Exception ex)
            {
                throw new Exception("\r\nXtraReportHelper-GetData_DataBase-Exception: " + ex.Message);
            }

        }

        /// <summary>
        /// 获取报表数据
        /// </summary>
        /// <param name="templetName"></param>
        /// <param name="hasParam"></param>
        /// <returns></returns>
        private static DataSet GetData(string templetName, Hashtable hasParam, string appCode, bool isSql = false)
        {
            string sql = string.Empty;
            if (isSql)
                sql = GetSqlText(templetName, hasParam);
            else
                sql = GetSql(templetName, hasParam, appCode);
            NM_Service.NMService.ServerPublicClient dbConn = new NM_Service.NMService.ServerPublicClient();
            return dbConn.GetDataBySql(sql);
        }
        /// <summary>
        /// 获取报表结构（无数据）
        /// </summary>
        /// <param name="templetName">报表模板名称</param>
        /// <returns></returns>
        public static DataSet GetDataStruct(string templetName)
        {
            string sql = string.Empty;

            string templetNameFull = Path.Combine(Application.StartupPath, @"Reports\" + templetName + ".SQL");

            //考虑在开发调试环境下的运行，Reports路径不同的问题，加入下面代码
            //if(GetModuleHandle("devenv.exe").ToInt32() ==0)
            //{
            //    templetNameFull = Path.Combine(Application.StartupPath.Replace(@"bin\Debug", ""), "Reports\\") + templetName + ".SQL";
            //}
            if (!File.Exists(templetNameFull))
            {
                //XtraMessageBox.Show(templetNameFull + " 不存在，将无法构建表结构！","系统提示"); //测试时可以放开提示 Yzw
                //throw new Exception(templetNameFull + " 不存在!");
                return null;
            }

            sql = Cs01FileHelper.GetFileContent(templetNameFull);

            string[] arrParts = sql.Split(';');

            if (string.IsNullOrEmpty(sql) || arrParts.Length == 0 || string.IsNullOrEmpty(arrParts[0]))
            {
                //XtraMessageBox.Show(templetNameFull + "中没有内容，将无法构建表结构！", "系统提示");
                //throw new Exception(templetNameFull + " 中没有内容!");
                return null;
            }

            if (arrParts.Length >= 2)
            {
                sql = arrParts[1];
            }
            //截掉where后的条件，增加非成立条件以便获取无数据的结构
            string sqlUpper = sql.ToUpper();
            int pos1 = sqlUpper.LastIndexOf("WHERE");
            if (pos1 >= 0)
            {
                sqlUpper = sqlUpper.Substring(0, pos1) + " WHERE 1=2";
            }
            NM_Service.NMService.ServerPublicClient dbConn = new NM_Service.NMService.ServerPublicClient();
            return dbConn.GetDataBySql(sql);
        }

        /// <summary>
        /// 获取报表数据
        /// </summary>
        /// <param name="templetName"></param>
        /// <param name="hasParam"></param>
        /// <returns></returns>
        public static DataSet GetData_Tree(string templetName, Hashtable hasParam, string appCode)
        {
            string sql = GetSql_Tree(templetName, hasParam, appCode);

            NM_Service.NMService.ServerPublicClient dbConn = new NM_Service.NMService.ServerPublicClient();
            return dbConn.GetDataBySql(sql);
        }


        /// <summary>
        /// 把架构数据写入\Schema目录
        /// </summary>
        public static void WriteSchemaData(string templetFileName, DataSet ds)
        {
            string fileName = System.IO.Path.Combine(Application.StartupPath, "Schema");
            #region 判断是否为开发环境 王代迪2017-02-07
            //if (GetModuleHandle("devenv.exe").ToInt32() == 0)
            if (System.Diagnostics.Debugger.IsAttached)
            {
                fileName = System.IO.Path.Combine(Application.StartupPath.Replace(@"bin\Debug", ""), "Schema");
            }
            #endregion
            if (System.IO.Directory.Exists(fileName) == false)
            {
                System.IO.Directory.CreateDirectory(fileName);
            }

            fileName = System.IO.Path.Combine(fileName, templetFileName + ".xml");
            ds.WriteXml(fileName, XmlWriteMode.WriteSchema);
        }


        /// <summary>
        /// 生成打印数据
        /// </summary>
        /// <returns></returns>
        public static DataSet CreatePrintData(DataTable[] arrTables, Hashtable hasParam)
        {
            DataSet ds = new DataSet();
            if (arrTables != null)
            {
                for (int i = 0; i < arrTables.Length; i++)
                {
                    if (arrTables[i] != null)
                    {
                        ds.Tables.Add(arrTables[i].Copy());
                    }
                }
            }

            // 添加单独的值
            if (hasParam != null)
            {
                DataTable dtSinleRow = ds.Tables.Add("SINGLE_ROW_TABLE");

                foreach (DictionaryEntry de in hasParam)
                {
                    if (de.Value.GetType().Equals(typeof(System.DateTime)))
                    {
                        dtSinleRow.Columns.Add(de.Key.ToString(), typeof(System.DateTime));
                    }
                    else
                    {
                        dtSinleRow.Columns.Add(de.Key.ToString());
                    }
                }


                dtSinleRow.Columns.Add("PRINT_USER_NAME");
                dtSinleRow.Columns.Add("PRINT_NAME");
                dtSinleRow.Columns.Add("PRINT_DATE");
                dtSinleRow.Columns.Add("HOSPITAL_NAME");
                DataRow dr = dtSinleRow.NewRow();
                foreach (DictionaryEntry de in hasParam)
                {
                    dr[de.Key.ToString()] = de.Value;
                }
                //添加打印相关信息  20230728 liul
                dr["PRINT_USER_NAME"] = PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;
                dr["PRINT_NAME"] = PlatCommon.SysBase.SystemParm.LoginUser.NAME;
                dr["PRINT_DATE"] = new NM_Service.NMService.ServerPublicClient().GetSysDate().ToString();
                dr["HOSPITAL_NAME"] = PlatCommon.SysBase.SystemParm.HospitalID;
                dtSinleRow.Rows.Add(dr);
            }

            return ds;
        }



        /// <summary> 
        /// 2022-04-20
        /// </summary>
        /// <param name="_DS">传入dataset带数据</param>
        /// <param name="hasParam"> 变量 例如 统计日期 打印人 等信息的传递 </param>
        /// <returns></returns>
        public static DataSet CreatePrintData(DataSet _DS, Hashtable hasParam)
        {
            try
            {
                DataSet ds = new DataSet();
                ds.Tables.Add(_DS.Tables[0].Copy());

                // 添加单独的值
                if (hasParam != null)
                {
                    DataTable dtSinleRow = ds.Tables.Add("SINGLE_ROW_TABLE");

                    foreach (DictionaryEntry de in hasParam)
                    {
                        if (de.Value.GetType().Equals(typeof(System.DateTime)))
                        {
                            dtSinleRow.Columns.Add(de.Key.ToString(), typeof(System.DateTime));
                        }
                        else
                        {
                            dtSinleRow.Columns.Add(de.Key.ToString());
                        }
                    }

                    DataRow dr = dtSinleRow.NewRow();
                    foreach (DictionaryEntry de in hasParam)
                    {
                        dr[de.Key.ToString()] = de.Value;
                    }
                    dtSinleRow.Rows.Add(dr);
                }

                return ds;
            }
            catch (Exception ex)
            {

                throw new Exception("\r\nXtraReportHelper-CreatePrintData2-Exception:" + ex.Message);
            }

        }


        /// <summary>
        /// 获取SQL语句
        /// </summary>
        /// <returns></returns>
        private static string GetSqlText(string sql, Hashtable hasParams)
        {
            // 获取SQL原文
            string sqlText = sql;

            // 替换参数
            if (hasParams == null) return sqlText;

            string paramName = string.Empty;
            string paramValue = string.Empty;

            string paramName0 = string.Empty;
            string paramValue0 = string.Empty;

            foreach (DictionaryEntry entry in hasParams)
            {
                // 参数名
                paramName = "{" + entry.Key.ToString().ToUpper() + "}";         // 这种情况 变成 ''
                paramName0 = "[" + entry.Key.ToString().ToUpper() + "]";        // 这种情况 变成 原样

                // 参数值
                if (entry.Value == null)
                {
                    paramValue = "NULL";
                }
                else if (entry.Value.GetType().Equals(typeof(DateTime)))
                {
                    paramValue = "TO_DATE('" + (DateTime)(entry.Value) + "','yyyy-MM-dd HH24:mi:ss')";
                }
                else if (entry.Value.GetType().Equals(typeof(int)) || entry.Value.GetType().Equals(typeof(float)) || entry.Value.GetType().Equals(typeof(Decimal)))
                {
                    paramValue = entry.Value.ToString();
                }
                else if (entry.Value.ToString().Contains("'"))
                {
                    paramValue = entry.Value.ToString();
                }
                else
                {
                    paramValue = "'" + entry.Value.ToString() + "'";
                }

                paramValue0 = entry.Value == null ? "" : entry.Value.ToString();

                // 替换参数
                if (sqlText.IndexOf(paramName) >= 0)
                {
                    sqlText = sqlText.Replace(paramName, paramValue);
                }
                if (sqlText.IndexOf(paramName0) >= 0)
                {
                    sqlText = sqlText.Replace(paramName0, paramValue0);
                }
            }

            sqlText = sqlText.Replace(@" = ''", " IS NULL");

            return sqlText;
        }
        #endregion

        /// <summary>
        /// 报表设计
        /// </summary>
        public static void ShowReportDesign(string strFileName, string appCode)
        {
            XRDesignFormEx frm = new XRDesignFormEx();
            frm.FileName = XtraReportHelper.GetTempleteFileNameFull(strFileName, appCode);
            if (File.Exists(frm.FileName))
            {
                frm.OpenReport(frm.FileName);
            }
            frm.ShowDialog();
        }

        # region  自定义设计调整gridview界面风格 20230627lxm
        /// <summary>
        /// 加载 GridView 风格配置 
        /// </summary>
        /// <param name="gvOrders"></param>
        /// <param name="item_class"></param>
        public static void ShowGridViewDesign(GridView gvShow, string item_class)
        {
            string username = PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;
            string fileName = "";
            string dgvId = username;
            GetfileName(dgvId, ref fileName);
            DataSet dsSave = null;
            string sql = "select * from COMM.COLUMNTABLEDESIGN where doctor_code='" + username + "' and item_class='" + item_class + "' ";

            dsSave = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql);
            if (dsSave.Tables[0].Rows.Count > 0)
            {
                string data = dsSave.Tables[0].Rows[0]["COLUMNXML"].ToString();
                File.WriteAllText(fileName, data);
                AddColByTable(gvShow, username);
            }
        }

        /// <summary>
        /// 新增GridView保存列属性方法  列款 排序 显示隐藏 保存列的配置文件
        /// </summary>
        /// <param name="dsColConfig"></param>
        /// <param name="dgvId"></param>
        public static void GetfileName(string dgvId, ref string fileName)
        {
            // 保存
            fileName = Path.Combine(Cs01FileHelper.GetCurrDllPath(), "Config");
            if (Directory.Exists(fileName) == false)
            {
                Directory.CreateDirectory(fileName);
            }

            fileName = Path.Combine(fileName, dgvId + ".xml");

        }
        /// <summary>
        /// 通过配置信息生成DataGridView的列
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="dgvId"></param>
        public static void AddColByTable(GridView dgv, string dgvId)
        {
            DataSet ds = GetColumnConfig(dgvId);
            AddColByTable(dgv, ds);
        }
        /// <summary>
        /// 通过配置信息生成DataGridView的列 新增从表取值
        /// </summary>
        public static void AddColByTable(GridView dgv, DataSet ds)
        {
            try
            {
                if (Cs02DataSetHelper.HasRecord(ds) == false) return;

                // 显示已配置的列
                DataRow[] drFind = ds.Tables[0].Select("", "SHOW_ORDER ASC");

                for (int i = 0; i < drFind.Length; i++)
                {
                    DataRow dr = drFind[i];

                    string colName = dr["DATA_PROPERTY_NAME"].ToString();   // 列名
                    string styleFormat = dr["STYLE_FORMAT"].ToString();     // 格式

                    // 判断列是否存在
                    GridColumn dgvColumn = null;
                    foreach (GridColumn dgvCol in dgv.Columns)
                    {
                        if (dgvCol.Name.Equals(colName))
                        {
                            dgvColumn = dgvCol;
                            int sn = -1;
                            if (!int.TryParse(dr["SHOW_ORDER"].ToString(), out sn))
                            {
                                sn = -1;
                            }
                            dgvColumn.VisibleIndex = sn;
                            break;
                        }
                    }

                    if (dgvColumn == null) continue;
                    if (dr["SHOW_VISIBLE"].ToString().Equals("False"))
                    {
                        dgvColumn.Visible = false;
                    }
                    else
                    {
                        dgvColumn.Visible = true;
                    }
                    // 设置列宽
                    dgvColumn.Width = Converter.ToInt(dr["COL_WIDTH"].ToString());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 新增通用保存gridview风格到数据库和提取风格从数据库
        /// </summary>
        /// <param name="gvOrders"></param>
        /// <param name="item_class"></param>
        public static void SaveGridViewDesign(GridView gvSave, string item_class)
        {
            string filename = "";//xml存储路径
            string username = PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;
            SaveColumnToTable(gvSave, username, ref filename);
            if (!string.IsNullOrEmpty(filename))
            {
                //按患者和医生进行保存 
                DataSet dsSave = null;
                string sql = "select * from COMM.COLUMNTABLEDESIGN where doctor_code='" + username + "' and item_class='" + item_class + "' ";
                dsSave = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql);
                if (dsSave.Tables[0].Rows.Count <= 0)
                {
                    dsSave = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select * from COMM.COLUMNTABLEDESIGN where 1=0");

                    DataRow drSave = dsSave.Tables[0].NewRow();
                    drSave["COLUMNXML"] = File.ReadAllText(filename);
                    drSave["DOCTOR_CODE"] = PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;
                    drSave["ITEM_CLASS"] = item_class;
                    dsSave.Tables[0].Rows.Add(drSave);
                }
                else
                {
                    dsSave.Tables[0].Rows[0]["COLUMNXML"] = File.ReadAllText(filename);
                }
                int li_ret = new NM_Service.NMService.ServerPublicClient().SaveDataSet(dsSave);
                if (li_ret <= 0)
                {
                    XtraMessageBox.Show("界面风格保存失败！", "提示");
                }
                else
                {
                    XtraMessageBox.Show("界面风格保存成功！", "提示");
                }
            }
        }
        /// <summary>
        /// 新增GridView保存列属性方法  列款 排序 显示隐藏
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="dgvId"></param>
        public static void SaveColumnToTable(GridView dgv, string dgvId, ref string fileName)
        {
            // 获取本地配置文件
            DataSet ds = GetColumnConfig(dgvId);

            // 更新内容
            foreach (GridColumn dgvCol in dgv.Columns)
            {
                //if (dgvCol.Visible == true)
                //{
                DataRow drEdit = null;

                string colName = dgvCol.Name;
                string filter = "DATA_PROPERTY_NAME = '" + colName + "'";
                DataRow[] drFind = ds.Tables[0].Select(filter);
                if (drFind.Length == 0)
                {
                    drEdit = ds.Tables[0].NewRow();
                    drEdit["DATA_PROPERTY_NAME"] = colName;                 // 列名
                    ds.Tables[0].Rows.Add(drEdit);
                }
                else
                {
                    drEdit = drFind[0];
                }

                drEdit["HEADER_TEXT"] = dgvCol.Caption;                     // 标题                   
                drEdit["COL_WIDTH"] = dgvCol.Width;                         // 列宽
                drEdit["SHOW_ORDER"] = dgvCol.VisibleIndex;                    // 显示顺序
                drEdit["SHOW_VISIBLE"] = dgvCol.Visible;

            }

            // 保存本地配置文件
            SaveColumnToTable(ds, dgvId, ref fileName);

            //保存本地后 
        }
        /// <summary>
        /// 新增GridView保存列属性方法  列款 排序 显示隐藏 保存列的配置文件
        /// </summary>
        /// <param name="dsColConfig"></param>
        /// <param name="dgvId"></param>
        public static void SaveColumnToTable(DataSet dsColConfig, string dgvId, ref string fileName)
        {
            // 保存
            fileName = Path.Combine(Cs01FileHelper.GetCurrDllPath(), "Config");
            if (Directory.Exists(fileName) == false)
            {
                Directory.CreateDirectory(fileName);
            }

            fileName = Path.Combine(fileName, dgvId + ".xml");

            dsColConfig.WriteXml(fileName, XmlWriteMode.WriteSchema);
        }

        #endregion

        /// <summary>
        /// 得到数据库中保存的报表模板格式的内存流
        /// </summary>
        /// <param name="reportHelper"></param>
        /// <param name="AppName">应用名称</param>
        /// <param name="ReportName">报表模板名称</param>
        /// <param name="HisUnitCode">院区代码</param>
        /// <returns></returns>
        public static System.IO.Stream GetRepxStreamTemplate(string AppName, string ReportName, string HisUnitCode)
        {
            //从数据库表REPORT_HIS_REPX中取报表格式
            string strSql = $"SELECT REPORT_REPX FROM REPORT_HIS_REPX where APP_NAME = '{AppName}' AND REPORT_NAME = '{ReportName}' AND HIS_UNIT_CODE = '{HisUnitCode}' "
              + " AND  HIS_UNIT_CODE = " + Cs02StringHelper.SqlConvert(SystemParm.HisUnitCode);
            string reportRepx = new NM_Service.NMService.ServerPublicClient().GetSingleValue(strSql);
            MemoryStream stream = new MemoryStream();
            if (reportRepx.Length == 0)//没有取到，则返回null
            {
                stream = null;
                return stream;
            }
               
            //转换为stream
    
            StreamWriter writer = new StreamWriter(stream);
            writer.Write(reportRepx);
            writer.Flush();
            return stream;
        }

        public static void BindSingleRowTable(DataSet ds)
        {
            if (ds != null && ds.Tables.Count > 0)
            {
                if (!ds.Tables.Contains("SINGLE_ROW_TABLE"))
                {
                    DataTable dtSinleRow = ds.Tables.Add("SINGLE_ROW_TABLE");
                    dtSinleRow.Columns.Add("PRINT_USER_NAME");
                    dtSinleRow.Columns.Add("PRINT_NAME");
                    dtSinleRow.Columns.Add("PRINT_DATE");
                    dtSinleRow.Columns.Add("HOSPITAL_NAME");
                    DataRow dr = dtSinleRow.NewRow();
                    //添加打印相关信息  
                    dr["PRINT_USER_NAME"] = PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;
                    dr["PRINT_NAME"] = PlatCommon.SysBase.SystemParm.LoginUser.NAME;
                    dr["PRINT_DATE"] = new NM_Service.NMService.ServerPublicClient().GetSysDate().ToString();
                    dr["HOSPITAL_NAME"] = PlatCommon.SysBase.SystemParm.HospitalID;
                    dtSinleRow.Rows.Add(dr);
                }
            }
        }
    }
}
