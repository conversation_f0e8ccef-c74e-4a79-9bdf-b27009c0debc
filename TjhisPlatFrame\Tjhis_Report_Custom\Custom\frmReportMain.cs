﻿using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraTab;
using DevExpress.XtraTab.ViewInfo;
using DevExpress.XtraTreeList.Nodes;
using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Tjhis.Report.Custom.Common;
using Tjhis.Report.Custom.Srv;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmReportMain : ParentForm
    {
        srvStatisticalQuery srv;
        //用户有的角色编码值
        List<string> roleCodeLists = new List<string>();
        const string SYSTEMMGR = "SYSTEMMGR";
        #region 构造函数
        public frmReportMain()
        {
            InitializeComponent();
        }
        #endregion

        #region 窗体加载
        /// <summary>
        /// 窗体加载
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void frmSearchWardInspection_Load(object sender, EventArgs e)
        {
            try
            {
                Const.customAppCode = this.AppCode;
                Const.customDeptCode = this.DeptCode;
                roleCodeLists = new srvRoleUserQuery().GetUserRoles(SystemParm.LoginUser.USER_NAME);

                if (!this.DesignMode)//不为设计模式才初始Srv
                    srv = new srvStatisticalQuery();
                InitTreeView(treeTempleList);
                PlatCommon.Common.XtraTreeListHelper.Initialize(treeTempleList);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }


        /// <summary>
        /// 初始化树控件
        /// </summary>
        /// <param name="treeView"></param>
        public static void InitTreeView(DevExpress.XtraTreeList.TreeList treeView)
        {
            treeView.OptionsView.ShowColumns = false;
            treeView.OptionsView.ShowIndicator = false;
            treeView.OptionsView.ShowVertLines = false;
            treeView.OptionsView.ShowHorzLines = false;
            treeView.OptionsBehavior.Editable = false;
            treeView.OptionsSelection.EnableAppearanceFocusedCell = false;
            treeView.KeyFieldName = "DICT_ID";
            treeView.ParentFieldName = "PARENTID";
        }

        /// <summary>
        /// 加载树节点数据
        /// </summary>
        /// <returns></returns>
        public bool LoadTreeList()
        {
            DataSet dsTemplet = null;
            if (string.Equals(this.AppCode, SYSTEMMGR))
            {
                // 系统管理可以看到所有模块，其他只能看到自己模块
                dsTemplet = srv.GetReportTreeList();
            }
            else
            {
                dsTemplet = srv.GetTempTreeListModule(this.AppCode);
            }

            if (dsTemplet == null || dsTemplet.Tables[0].Rows.Count <= 0)
            {
                return false;
            }
            else
            {
                DataTable treeDataSource = dsTemplet.Tables[0];

                //循环当前所有报表，如果当前用户没有权限则删除；暂时这么处理，后期有更好的办法会优化这里
                for (int i = 0; i < treeDataSource.Rows.Count; i++)
                {
                    DataRow dr = treeDataSource.Rows[i];
                    bool haveRole = GetRole(dr);

                    if (!haveRole)
                    {
                        dr.Delete();
                    }
                }
                ////在这里处理是否有未分类的报表，如果没有则删除未分类节点
                //int rlength = dsTemplet.Tables[0].Select("PARENTID = '*'").Length;
                //if(rlength == 0)
                //{
                //    dsTemplet.Tables[0].Select("DICT_ID = '*'")[0].Delete();
                //}

                //如果父节点没有子节点 则删除
                DeleteEmptyParentNode(this.AppCode, ref treeDataSource);

                focusedNode = treeTempleList.FocusedNode;
                treeTempleList.DataSource = treeDataSource.DefaultView;//绑定数据源
                if (!string.Equals(this.AppCode, SYSTEMMGR))
                    treeTempleList.ExpandAll();  //如果不是从系统管理进入展开所有节点
                else
                {
                    treeTempleList.ExpandToLevel(0);
                }
                if (focusedNode != null)
                {

                    TreeListNode n = treeTempleList.GetNodeList().Find(x => x.GetValue("DICT_ID").Equals(focusedNode.GetValue("DICT_ID")));
                    if (n != null)
                        n.Selected = true;

                    //如果原有报表处于打开状态，但列表已删除，移除tab页
                    for (int i = 0; i < xtraTabControl1.TabPages.Count;)
                    {
                        DataRow n1 = dsTemplet.Tables[0].Select().ToList().Find(x => x["DICT_ID"].Equals(xtraTabControl1.TabPages[0].Tag)); ;
                        if (n1 == null)
                        {
                            xtraTabControl1.TabPages.Remove(xtraTabControl1.TabPages[0]);
                        }
                        else
                        {
                            i++;
                        }
                    }
                }

                return true;
            }
        }

        private void DeleteEmptyParentNode(string appCode, ref DataTable dt)
        {
            if (dt == null || dt.Rows.Count == 0) return;

            //如果父节点没有子节点 则删除
            if (string.Equals(appCode, SYSTEMMGR))
            {
                // 系统管理三级菜单，前两级都要判断是否有子节点
                // 三级
                DataRow[] rows = dt.Select("TREELEVEL = '2'");
                for (int i = 0; i < rows.Length; i++)
                {
                    string parentid = rows[i]["DICT_ID"].ToString();
                    if (dt.Select("PARENTID = '" + parentid + "'").Length == 0)
                    {
                        dt.Rows.Remove(rows[i]);
                    }
                }

                // 二级
                DataRow[] rowsTop = dt.Select("TREELEVEL = '1'");
                for (int i = 0; i < rowsTop.Length; i++)
                {
                    string parentid = rowsTop[i]["DICT_ID"].ToString();
                    if (dt.Select("PARENTID = '" + parentid + "'").Length == 0)
                    {
                        dt.Rows.Remove(rowsTop[i]);
                    }
                }

                dt.AcceptChanges();
            }
            else
            {
                DataRow[] rows = dt.Select("TREELEVEL = '0'");
                for (int i = 0; i < rows.Length; i++)
                {
                    string parentid = rows[i]["DICT_ID"].ToString();
                    if (dt.Select("PARENTID = '" + parentid + "'").Length == 0)
                    {
                        dt.Rows.Remove(rows[i]);
                    }
                }
                dt.AcceptChanges();
            }
        }
        void AddAccordionElement(DevExpress.XtraBars.Navigation.AccordionControlElementCollection elements, TreeListNodes nodes)
        {
            foreach (TreeListNode node in nodes)
            {
                DevExpress.XtraBars.Navigation.AccordionControlElement element = new DevExpress.XtraBars.Navigation.AccordionControlElement();
                element.Tag = node;
                element.Text = node.GetDisplayText("DICT_NAME").ToString();
                if (node.Nodes != null && node.Nodes.Count > 0)
                {
                    AddAccordionElement(element.Elements, node.Nodes);
                }
                else
                {
                    element.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
                }
                elements.Add(element);
            }
        }
        #endregion


        /// <summary>
        /// 获取是否有当前报表的权限
        /// </summary>
        /// <returns></returns>
        private bool GetRole(DataRow dr = null)
        {
            if (dr == null)
                dr = treeTempleList.GetFocusedDataRow();

            string treeLevel = dr["TREELEVEL"].ToString();
            //类型节点不判断
            if (this.AppCode == SYSTEMMGR)
            {
                return true;
            }
            else if (treeLevel.Equals("0"))
            {
                return true;
            }

            string roleType = dr["role_type"].ToString();
            string roles = dr["role_users"].ToString();
            string roleCodes = dr["role_codes"].ToString();

            string[] roleArray = roles.Split( new char[]{ ','} , StringSplitOptions.RemoveEmptyEntries);
            string[] roleCodeArray = roleCodes.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (string.IsNullOrEmpty(roles) && string.IsNullOrEmpty(roleCodes))
            {
                return true;
            }
            if (SystemParm.LoginUser.USER_NAME.Equals(dr["CREATE_USER"].ToString()))
            {
                return true;
            }
            if (roleArray.Length > 0)
            {
                string len = roleArray.ToList().Find(str => str.Equals(SystemParm.LoginUser.USER_NAME));//暂定只授权人员
                if (len != null && len.Length > 0)
                    return true;
            }
            if (roleCodeArray.Length > 0)
            {
                if (roleCodeArray.ToList().Intersect(roleCodeLists).Count() > 0) 
                {
                    return true;
                }
            }
            return false;
            //if (string.IsNullOrEmpty(roleType) || roleType.Equals("1"))
            //{
            //    return true;
            //}
            //else if ("2".Equals(roleType))
            //{
            //    if (SystemParm.LoginUser.USER_NAME.Equals(dr["CREATE_USER"]))
            //    {
            //        return true;
            //    }
            //    else
            //    {
            //        return false;
            //    }
            //}
            //else if ("3".Equals(roleType))
            //{
            //    string[] roleArray = roles.Split(',');
            //    if (SystemParm.LoginUser.USER_NAME.Equals(dr["CREATE_USER"].ToString()))
            //    {
            //        return true;
            //    }
            //    else
            //    {
            //        string len = roleArray.ToList().Find(str => str.Equals(SystemParm.LoginUser.USER_NAME));//暂定只授权人员
            //        if (len != null && len.Length > 0)
            //            return true;
            //        else
            //            return false;
            //    }
            //}

            //return true;
        }

        TreeListNode focusedNode;//焦点节点
        /// <summary>
        /// 节点切换
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void treeTempleList_FocusedNodeChanged(object sender, DevExpress.XtraTreeList.FocusedNodeChangedEventArgs e)
        {
            DataRow dr = treeTempleList.GetFocusedDataRow();
            if (dr == null) return;

            string treeLevel = dr["TREELEVEL"].ToString();
            if (string.Equals(this.AppCode, SYSTEMMGR))
            {
                if (string.Equals(treeLevel, "0") || string.Equals(treeLevel, "1") || string.Equals(treeLevel, "2"))
                {
                    // 系统管理一级和二级菜单没有实际的报表
                    return;
                }
            }
            else
            {
                // 其他模块一级菜单没有实际的报表
                if (string.Equals(treeLevel, "0"))
                {
                    return;
                }
            }

            bool isRole = GetRole();
            if (!isRole)
            {
                XtraMessageBox.Show("没有查询权限！");
                if (xtraTabControl1.TabPages.Count > 0)
                {
                    TreeListNode n = treeTempleList.GetNodeList().Find(x => x.GetValue("DICT_ID").Equals(xtraTabControl1.SelectedTabPage.Tag));

                    n.Selected = true;
                }
                else
                {
                    treeTempleList.Nodes[0].Selected = true;
                }
                return;
            }
            XtraTabPage tempPage = null;
            foreach (XtraTabPage page in xtraTabControl1.TabPages)
            {
                if (page.Tag.Equals(dr["DICT_ID"]))
                {
                    tempPage = page;
                    xtraTabControl1.SelectedTabPage = tempPage;
                }
            }
            if (tempPage == null)
            {
                tempPage = new XtraTabPage();

                tempPage.Text = dr["dict_name"].ToString();
                //lxm20230627新增 科室代码 和模块代码变量传入 ~
                // Form doc = new TJReportViewF();
                Form doc = new TJReportViewF(this.DeptCode,this.AppCode,"");
                //doc.Tag = dr["DICT_ID"] + (string.IsNullOrEmpty(dr["is_table"].ToString()) ? "" : "-" + dr["is_table"].ToString());
                //if (dr["PARENTID"].ToString() == "*")//未分类报表
                //{
                //    string myAppName1 = SystemParm.AppName;
                //}
                //TreeListNode nodeLevel1 = treeTempleList.GetNodeList().Find(x => x.GetValue("DICT_ID").Equals(dr["PARENTID"].ToString()));
                string myAppName = dr["APP_NAME"].ToString();
                //string myAppName = treeTempleList.FocusedNode.ParentNode.ParentNode.GetValue(0).ToString();
                doc.Tag = myAppName + "|" + dr["DICT_ID"] + (string.IsNullOrEmpty(dr["is_table"].ToString()) ? "" : "-" + dr["is_table"].ToString());
                doc.TopLevel = false;
                doc.FormBorderStyle = FormBorderStyle.None;

                doc.Dock = DockStyle.Fill;
                
                tempPage.ShowCloseButton = DevExpress.Utils.DefaultBoolean.True;
                tempPage.Tag = dr["DICT_ID"];
                tempPage.Controls.Add(doc);

                xtraTabControl1.TabPages.Add(tempPage);
                xtraTabControl1.SelectedTabPage = tempPage;
                doc.Visible = true;
            }
        }
        /// <summary>
        /// 窗体显示后加载树数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void frmStatisticalQuery_Shown(object sender, EventArgs e)
        {
            RefreshTreeList();
        }

        private void RefreshTreeList()
        {
            bool isOk = LoadTreeList();
            if (!isOk)
            {
                XtraMessageBox.Show("请先配置自定义查询字典！", "提示");
            }
        }

        /// <summary>
        /// tabpage关闭按钮事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void xtraTabControl1_CloseButtonClick(object sender, EventArgs e)
        {
            if (xtraTabControl1.SelectedTabPage != null)
            {
                if (xtraTabControl1.SelectedTabPage.Controls.Count > 0)
                    (xtraTabControl1.SelectedTabPage.Controls?[0] as Form)?.Close();
            }
            xtraTabControl1.TabPages.Remove(xtraTabControl1.SelectedTabPage);

            if (xtraTabControl1.TabPages.Count > 0)
            {
                xtraTabControl1.SelectedTabPage = xtraTabControl1.TabPages[0];

                TreeListNode n = treeTempleList.GetNodeList().Find(x => x.GetValue("DICT_ID").Equals(xtraTabControl1.TabPages[xtraTabControl1.TabPages.Count - 1].Tag));
                n.Selected = true;
            }
            else
            {
                treeTempleList.Nodes[0].Selected = true;
            }
        }
        /// tabpage选中事件处理（主要处理对应的选中后对应树节点也选中）
        /// </summary>

        /// <summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void xtraTabControl1_SelectedPageChanged(object sender, TabPageChangedEventArgs e)
        {
            if (xtraTabControl1.TabPages.Count > 0)
            {
                TreeListNode n = treeTempleList.GetNodeList().Find(x => x.GetValue("DICT_ID").Equals(xtraTabControl1.SelectedTabPage.Tag));
                if (n == null)
                {
                    xtraTabControl1.TabPages.Remove(xtraTabControl1.SelectedTabPage);
                }
                else
                    n.Selected = true;
            }
            else
            {
                treeTempleList.Nodes[0].Selected = true;
            }
        }


        /// <summary>
        /// 关闭其他页签
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barButtonItem1_ItemClick(object sender, ItemClickEventArgs e)
        {
            closeOtherPage();
        }
        private void closeOtherPage()
        {
            if (xtraTabControl1.SelectedTabPage == null)
            {
                return;
            }
            string strName = xtraTabControl1.SelectedTabPage.Tag.ToString();
            int iCount = xtraTabControl1.TabPages.Count;
            for (int i = iCount - 1; i >= 0; i--)
            {
                if (xtraTabControl1.TabPages[i].Tag.ToString() != strName)
                {
                    xtraTabControl1.TabPages.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// 关闭全部页签
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barButtonItem2_ItemClick(object sender, ItemClickEventArgs e)
        {
            for (int i = xtraTabControl1.TabPages.Count - 1; i >= 0; i--)
            {
                xtraTabControl1.TabPages.RemoveAt(i);
            }
        }

        /// <summary>
        /// 右键弹出关闭菜单
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void xtraTabControl1_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                XtraTabControl tabCtrl = sender as XtraTabControl;
                Point pt = MousePosition;
                XtraTabHitInfo info = tabCtrl.CalcHitInfo(tabCtrl.PointToClient(pt));
                if (info.HitTest == XtraTabHitTest.PageHeader)
                {
                    popupMenu1.ShowPopup(pt);
                }
            }
        }

        private void treeTempleList_GetSelectImage(object sender, DevExpress.XtraTreeList.GetSelectImageEventArgs e)
        {
            if (e.Node == null) return;
            TreeListNode node = e.Node;
            if (this.AppCode == SYSTEMMGR)
            {
                if (node.Level == 0)
                    e.NodeImageIndex = 0;
                else if (node.Level == 3)
                    e.NodeImageIndex = 3;
                else
                {
                    if (node.Expanded)
                        e.NodeImageIndex = 1;
                    else
                        e.NodeImageIndex = 2;
                }
            }
            else
            {
                if (node.Level == 1)
                    e.NodeImageIndex = 3;
                else
                {
                    if (node.Expanded)
                        e.NodeImageIndex = 1;
                    else
                        e.NodeImageIndex = 2;
                }
            }
        }
    }
}
