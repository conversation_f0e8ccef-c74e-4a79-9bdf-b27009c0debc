﻿using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Tjhis_Mq_Service
{

    public class Tjhis_Mq_Factory
    {
        //主机
        public static void Init()
        {
            try
            {
                DataSet  ds= new NM_Service.NMService.ServerPublicClient().GetDataBySql(" select a.* from comm.interface_config_dict  a where  a.config_code='MQ_SERVICE_URL'");
                // Tjhis_Xml_ConfigAccess config = new Tjhis_Xml_ConfigAccess();
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    DataRow datarow = ds.Tables[0].Rows[0];
                    string[] urlArr= datarow["config_path"].ToString().Split(';');
                    Tjhis_Mq_Object.HostName = urlArr[0];
                    Tjhis_Mq_Object.Port = int.Parse(urlArr[1]);
                    string[] userPw = datarow["parameter_order"].ToString().Split(';');
                    Tjhis_Mq_Object.UserName = userPw[0];
                    Tjhis_Mq_Object.Password = userPw[1];
                    Tjhis_Mq_Object.VirtualHost = userPw[2];
                }
            }
            catch (Exception)
            {
            }
        }
        public static void Init(string path)
        {
            try
            {
                Tjhis_Xml_ConfigAccess config = new Tjhis_Xml_ConfigAccess(path);
                Tjhis_Mq_Object.HostName = config.ReadFactoryXml("HostName");
                Tjhis_Mq_Object.Port = Convert.ToInt32(config.ReadFactoryXml("Port"));
                Tjhis_Mq_Object.UserName = config.ReadFactoryXml("UserName");
                Tjhis_Mq_Object.Password = config.ReadFactoryXml("Password");
                Tjhis_Mq_Object.VirtualHost = config.ReadFactoryXml("VirtualHost");
            }
            catch (Exception)
            {

                throw;
            }
        }
        private static ConnectionFactory GetFactory()
        {
            ConnectionFactory factory = new ConnectionFactory();
            factory.HostName = Tjhis_Mq_Object.HostName;
            factory.Port = Tjhis_Mq_Object.Port;
            factory.UserName = Tjhis_Mq_Object.UserName;
            factory.Password = Tjhis_Mq_Object.Password;
            factory.VirtualHost = Tjhis_Mq_Object.VirtualHost;
            return factory;
        }
        /// <summary>
        /// 订阅模式下发布消息
        /// </summary>
        /// <param name="exchangeName">写到交换机的名字</param>
        /// <param name="routingKey">写到哪个队列的路由key，不写为广播模式</param>
        /// <param name="message">要发布的消息</param>
        public static void Publish_PubSub(string exchangeName, string routingKey, string message)
        {
            ConnectionFactory factory = GetFactory();
            using (var connection = factory.CreateConnection())
            {
                if (exchangeName == "")
                {
                    throw new Exception("交换机的不存在!");
                }
                if (message == "")
                {
                    throw new Exception("没有消息!");
                }
                using (var channel = connection.CreateModel())
                {
                    var body = Encoding.UTF8.GetBytes(message);
                    //消息持久化
                    var props = channel.CreateBasicProperties();
                    props.Persistent = true; ;
                    //将消息和routingkey发布到指定交换机，由交换机根据队列绑定的routingKey来分配消息
                    channel.BasicPublish(exchangeName, routingKey, props, body);
                }
            }
        }
        /// <summary>
        /// 订阅模式下创建消费者
        /// </summary>
        /// <param name="exchangeName">连接交换机的名字</param>
        /// <param name="routingKeys">消费者拥有的routingKey</param>
        /// <param name="queueName">消费者绑定队列的名字</param>
        /// <param name="triggerEvent">消费者绑定的触发事件</param>
        /// <exception cref="Exception"></exception>
        public static void Create_Consumer_PubSub(string exchangeName, List<string> routingKeys, string queueName, Tjhis_Mq_TriggerEvent triggerEvent)
        {
            ConnectionFactory factory = GetFactory();
            var connection = factory.CreateConnection();
            if (queueName == "")
            {
                throw new Exception("队列不存在!");
            }

            if (triggerEvent == null)
            {
                throw new Exception("触发事件不存在!");
            }
            if (exchangeName == "")
            {
                throw new Exception("交换机名称不存在!");
            }
           
            var channel = connection.CreateModel();

            //当routingKey没有时，连接广播式的交换机，当有时，连接对点对的交换机
            if (routingKeys.Count == 0)
            {
                channel.ExchangeDeclare(exchangeName, ExchangeType.Fanout);
                channel.QueueDeclare(queueName, true, false, false, null);
                channel.QueueBind(queueName, exchangeName, "");
            }
            else
            {
                channel.ExchangeDeclare(exchangeName, ExchangeType.Direct);
                channel.QueueDeclare(queueName, true, false, false, null);

                //给消费者队列绑定多个routingKey
                foreach (var routingKey in routingKeys)
                {
                    channel.QueueBind(queueName, exchangeName, routingKey);
                }
            }
            channel.BasicQos(0, 1, false);
            EventingBasicConsumer consumers = new EventingBasicConsumer(channel);
            consumers.Received += (model, ea) =>
            {
                var body = ea.Body.ToArray();
                triggerEvent.Message = Encoding.UTF8.GetString(body);
                triggerEvent.OnMessage();
                channel.BasicAck(ea.DeliveryTag, false);
            };
            channel.BasicConsume(queueName, false, consumers);
        }
    }
}
