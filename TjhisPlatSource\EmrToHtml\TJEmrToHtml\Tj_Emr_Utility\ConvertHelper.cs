﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Collections;

namespace TJ.EMR.Utility
{
    public class ConvertHelper
    {
        /// <summary>
        /// Ilist<T> 转换成 DataSet
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static DataSet ConvertToDataSet<T>(IList<T> i_objlist)
        {
            if (i_objlist == null || i_objlist.Count <= 0)
            {
                return null;
            }

            DataSet ds = new DataSet();
            DataTable dt = new DataTable(typeof(T).Name);
            DataColumn column;
            DataRow row;

            System.Reflection.PropertyInfo[] myPropertyInfo = typeof(T).GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);

            foreach (T t in i_objlist)
            {
                if (t == null)
                {
                    continue;
                }

                row = dt.NewRow();

                for (int i = 0, j = myPropertyInfo.Length; i < j; i++)
                {
                    System.Reflection.PropertyInfo pi = myPropertyInfo[i];

                    string name = pi.Name;

                    if (dt.Columns[name] == null)
                    {

                        column = new DataColumn(name);
                        dt.Columns.Add(column);
                    }

                    row[name] = pi.GetValue(t, null);
                }

                dt.Rows.Add(row);
            }

            ds.Tables.Add(dt);

            return ds;
        }
        /// <summary>
        /// Ilist<T> 转换成 DataTable
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static DataTable ConvertToDataTable<T>(IList<T> i_objlist)
        {
            if (i_objlist == null || i_objlist.Count <= 0)
            {
                return null;
            }
            DataTable dt = new DataTable(typeof(T).Name);
            DataColumn column;
            DataRow row;

            System.Reflection.PropertyInfo[] myPropertyInfo = typeof(T).GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);

            foreach (T t in i_objlist)
            {
                if (t == null)
                {
                    continue;
                }

                row = dt.NewRow();

                for (int i = 0, j = myPropertyInfo.Length; i < j; i++)
                {
                    System.Reflection.PropertyInfo pi = myPropertyInfo[i];

                    string name = pi.Name;

                    if (dt.Columns[name] == null)
                    {
                        column = new DataColumn(name, pi.PropertyType);
                        dt.Columns.Add(column);
                    }

                    row[name] = pi.GetValue(t, null);
                }

                dt.Rows.Add(row);
            }
            return dt;
        }
        /**/
        /// <summary>
        /// Ilist 转换成 DataTable
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static DataTable ConvertToDataTableNoT(IList i_objlist)
        {
            if (i_objlist == null || i_objlist.Count <= 0)
            {
                return null;
            }
            DataTable dt = new DataTable();
            DataRow row;

            string namess = i_objlist[0].GetType().Name;

            System.Reflection.PropertyInfo[] myPropertyInfo = i_objlist[0].GetType().GetProperties();

            foreach (System.Reflection.PropertyInfo pi in myPropertyInfo)
            {
                if (pi == null)
                {
                    continue;
                }
                dt.Columns.Add(pi.Name, System.Type.GetType(pi.PropertyType.ToString()));
            }

            for (int j = 0; j < i_objlist.Count; j++)
            {
                row = dt.NewRow();
                for (int i = 0; i < myPropertyInfo.Length; i++)
                {
                    System.Reflection.PropertyInfo pi = myPropertyInfo[i];
                    row[pi.Name] = pi.GetValue(i_objlist[j], null);
                }

                dt.Rows.Add(row);
            }
            return dt;
        }
        /**/
        /// <summary>
        /// Ilist 转换成 DataSet
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static DataSet ConvertToDataSetNoT(IList i_objlist)
        {
            if (i_objlist == null || i_objlist.Count <= 0)
            {
                return null;
            }
            DataSet ds = new DataSet();
            DataTable dt = new DataTable();
            DataRow row;

            System.Reflection.PropertyInfo[] myPropertyInfo = i_objlist[0].GetType().GetProperties();

            foreach (System.Reflection.PropertyInfo pi in myPropertyInfo)
            {
                if (pi == null)
                {
                    continue;
                }
                dt.Columns.Add(pi.Name, System.Type.GetType(pi.PropertyType.ToString()));
            }
            for (int j = 0; j < i_objlist.Count; j++)
            {
                row = dt.NewRow();
                for (int i = 0; i < myPropertyInfo.Length; i++)
                {
                    System.Reflection.PropertyInfo pi = myPropertyInfo[i];
                    row[pi.Name] = pi.GetValue(i_objlist[j], null);
                }
                dt.Rows.Add(row);
            }
            ds.Tables.Add(dt);
            return ds;
        }

    }
}
