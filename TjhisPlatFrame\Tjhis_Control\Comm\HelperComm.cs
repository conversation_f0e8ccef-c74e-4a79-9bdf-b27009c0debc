﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms; 

namespace Tjhis.Controls.Comm
{
    public static class HelperComm
    {
        /// <summary>
        /// 设置GDI高质量模式抗锯齿
        /// </summary>
        /// <param name="g">The g.</param>
        public static void SetGDIHigh(this Graphics g)
        {
            g.SmoothingMode = SmoothingMode.AntiAlias;  //使绘图质量最高，即消除锯齿
            g.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g.CompositingQuality = CompositingQuality.HighQuality;
        }

        /// <summary>
        /// 颜色加深
        /// </summary>
        /// <param name="color"></param>
        /// <param name="correctionFactor">-1.0f <= correctionFactor <= 1.0f</param>
        /// <returns></returns>
        public static Color ChangeColor(this Color color, float correctionFactor)
        {
            float red = (float)color.R;
            float green = (float)color.G;
            float blue = (float)color.B;

            if (correctionFactor < 0)
            {
                correctionFactor = 1 + correctionFactor;
                red *= correctionFactor;
                green *= correctionFactor;
                blue *= correctionFactor;
            }
            else
            {
                red = (255 - red) * correctionFactor + red;
                green = (255 - green) * correctionFactor + green;
                blue = (255 - blue) * correctionFactor + blue;
            }

            if (red < 0) red = 0;

            if (red > 255) red = 255;

            if (green < 0) green = 0;

            if (green > 255) green = 255;

            if (blue < 0) blue = 0;

            if (blue > 255) blue = 255;



            return Color.FromArgb(color.A, (int)red, (int)green, (int)blue);
        }


        #region 检查文本控件输入类型是否有效
        /// <summary>
        /// 功能描述:检查文本控件输入类型是否有效
 
        /// </summary>
        /// <param name="strValue">值</param>
        /// <param name="inputType">控制类型</param>
        /// <param name="decMaxValue">最大值</param>
        /// <param name="decMinValue">最小值</param>
        /// <param name="intLength">小数位长度</param>
        /// <param name="strRegexPattern">正则</param>
        /// <returns>返回值</returns>
        //public static bool CheckInputType(
        //    string strValue,
        //    TInputType inputType,
        //    decimal decMaxValue = default(decimal),
        //    decimal decMinValue = default(decimal),
        //    int intLength = 2,
        //    string strRegexPattern = null)
        //{
        //    bool result;
        //    switch (inputType)
        //    {
        //        case TInputType.NotControl:
        //            result = true;
        //            return result;
        //        case TInputType.UnsignNumber:
        //            if (string.IsNullOrEmpty(strValue))
        //            {
        //                result = true;
        //                return result;
        //            }
        //            else
        //            {
        //                if (strValue.IndexOf("-") >= 0)
        //                {
        //                    result = false;
        //                    return result;
        //                }
        //            }
        //            break;
        //        case TInputType.Number:
        //            if (string.IsNullOrEmpty(strValue))
        //            {
        //                result = true;
        //                return result;
        //            }
        //            else
        //            {
        //                if (!Regex.IsMatch(strValue, "^-?\\d*(\\.?\\d*)?$"))
        //                {
        //                    result = false;
        //                    return result;
        //                }
        //            }
        //            break;
        //        case TInputType.Integer:
        //            if (string.IsNullOrEmpty(strValue))
        //            {
        //                result = true;
        //                return result;
        //            }
        //            else
        //            {
        //                if (!Regex.IsMatch(strValue, "^-?\\d*$"))
        //                {
        //                    result = false;
        //                    return result;
        //                }
        //            }
        //            break;
        //        case TInputType.PositiveInteger:
        //            if (string.IsNullOrEmpty(strValue))
        //            {
        //                result = true;
        //                return result;
        //            }
        //            else
        //            {
        //                if (!Regex.IsMatch(strValue, "^\\d+$"))
        //                {
        //                    result = false;
        //                    return result;
        //                }
        //            }
        //            break;
        //        case TInputType.Regex:
        //            result = (string.IsNullOrEmpty(strRegexPattern) || Regex.IsMatch(strValue, strRegexPattern));
        //            return result;
        //    }
        //    if (strValue == "-")
        //    {
        //        return true;
        //    }
        //    decimal d;
        //    if (!decimal.TryParse(strValue, out d))
        //    {
        //        result = false;
        //    }
        //    else if (d < decMinValue || d > decMaxValue)
        //    {
        //        result = false;
        //    }
        //    else
        //    {
        //        if (inputType == TInputType.Number || inputType == TInputType.UnsignNumber || inputType == TInputType.PositiveNumber)
        //        {
        //            if (strValue.IndexOf(".") >= 0)
        //            {
        //                string text = strValue.Substring(strValue.IndexOf("."));
        //                if (text.Length > intLength + 1)
        //                {
        //                    result = false;
        //                    return result;
        //                }
        //            }
        //        }
        //        result = true;
        //    }
        //    return result;
        //}
        #endregion


        /// <summary>
        /// 相对于屏幕显示的位置
        /// </summary>
        /// <param name="screen">窗体需要显示的屏幕</param>
        /// <param name="left">left</param>
        /// <param name="top">top</param>
        /// <returns></returns>
        public static Point GetScreenLocation(Screen screen, int left, int top)
        {
            return new Point(screen.Bounds.Left + left, screen.Bounds.Top + top);
        }

        /// <summary>
        /// Gets the foreground window.
        /// </summary>
        /// <returns>IntPtr.</returns>
        [DllImport("user32.dll", CharSet = CharSet.Auto, ExactSpelling = true)]
        public static extern IntPtr GetForegroundWindow();

        [DllImport("user32.dll")]
        public static extern bool SetForegroundWindow(IntPtr hWnd);
    }
}
