﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace Tjhis.Controls.StatisticsControls
{
    public partial class ucClinicStatistics : RadiusUserControl
    {
        public UCClinicStatisicsData data = new UCClinicStatisicsData();
        private const int TitlesLength = 13;
        private string[] titles;
        /// <summary>
        /// 长度必须13，不更改的赋值""，顺序先从左到右
        /// </summary>
        [Obsolete("属性已弃用，请使用ControlTitle等代替")]
        public string[] Titles
        {
            set
            {
                if(null == value)
                {
                    throw new Exception("Titles is not null");
                }
                if(value.Length != TitlesLength)
                {
                    throw new Exception("Titles Length must be equels 13!");
                }
                titles = value;
            }
            private get
            {
                return titles;
            }
        }
        [CategoryAttribute("设计"), DisplayNameAttribute("控件标题")]
        public string ControlTitle
        {
            get
            {
                return this.labelControl1.Text;
            }
            set
            {
                this.labelControl1.Text = value;
            }
        }
        [CategoryAttribute("设计"), DisplayNameAttribute("子标题1")]
        public string SubTitle1
        {
            get
            {
                return this.labelControl2.Text;
            }
            set
            {
                this.labelControl2.Text = value;
            }
        }
        [CategoryAttribute("设计"), DisplayNameAttribute("子标题2")]
        public string SubTitle2
        {
            get
            {
                return this.labelControl5.Text;
            }
            set
            {
                this.labelControl5.Text = value;
            }
        }
        [CategoryAttribute("设计"), DisplayNameAttribute("子标题3")]
        public string SubTitle3
        {
            get
            {
                return this.labelControl7.Text;
            }
            set
            {
                this.labelControl7.Text = value;
            }
        }
        [CategoryAttribute("设计"), DisplayNameAttribute("子标题4")]
        public string SubTitle4
        {
            get
            {
                return this.labelControl9.Text;
            }
            set
            {
                this.labelControl9.Text = value;
            }
        }
        [CategoryAttribute("设计"), DisplayNameAttribute("自定义单位")]
        public string SubTitleOtherUnit
        {
            get
            {
                return TimeUnit;
            }
            set
            {
                TimeUnit = value;
            }
        }
        

        public ucClinicStatistics()
        {
            InitializeComponent();
        }

        public override void InitData()
        {
            if (null != Titles)
            {
                labelControl2.Text = string.IsNullOrEmpty(Titles[0]) ? labelControl2.Text : Titles[0];
                labelControl5.Text = string.IsNullOrEmpty(Titles[1]) ? labelControl5.Text : Titles[1];
                labelControl7.Text = string.IsNullOrEmpty(Titles[2]) ? labelControl7.Text : Titles[2];
                labelControl9.Text = string.IsNullOrEmpty(Titles[3]) ? labelControl9.Text : Titles[3];
                labelControl10.Text = string.IsNullOrEmpty(Titles[4]) ? labelControl10.Text : Titles[4];
                labelControl11.Text = string.IsNullOrEmpty(Titles[5]) ? labelControl11.Text : Titles[5];
                labelControl13.Text = string.IsNullOrEmpty(Titles[6]) ? labelControl13.Text : Titles[6];
                labelControl20.Text = string.IsNullOrEmpty(Titles[7]) ? labelControl20.Text : Titles[7];
                labelControl19.Text = string.IsNullOrEmpty(Titles[8]) ? labelControl19.Text : Titles[8];
                labelControl22.Text = string.IsNullOrEmpty(Titles[9]) ? labelControl22.Text : Titles[9];
                labelControl17.Text = string.IsNullOrEmpty(Titles[10]) ? labelControl17.Text : Titles[10];
                labelControl26.Text = string.IsNullOrEmpty(Titles[11]) ? labelControl26.Text : Titles[11];
                labelControl25.Text = string.IsNullOrEmpty(Titles[12]) ? labelControl25.Text : Titles[12];
            }
            if (null != data)
            {
                //已接诊人数
                this.LCliniced.Text = GetLabelHtmlText(data.Cliniced.Sum, PatUnit);
                this.LClinicedFirstVisit.Text = GetLabelHtmlText(data.Cliniced.firstVisit, PatUnit);
                this.LClinicedVisited.Text = GetLabelHtmlText(data.Cliniced.visited, PatUnit);
                this.LClinicedOther.Text = GetLabelHtmlText(data.Cliniced.other, PatUnit);
                
                //未接诊人数
                this.LWaitClinic.Text = GetLabelHtmlText(data.WaitClinic.Sum, PatUnit);
                this.LWaitClinicDetail.Text = GetLabelHtmlText(data.WaitClinic.WaitClinic, PatUnit);
                this.LWaitClinicOther.Text = GetLabelHtmlText(data.WaitClinic.Other, PatUnit);
                
                //平均等候时间
                this.LAvgWatiTime.Text = GetLabelHtmlText(data.WaitAvgTime.Sum, TimeUnit);
                this.LAvgWatiTimeYesterday.Text = GetLabelHtmlText(data.WaitAvgTime.Yesterday, TimeUnit);
                this.LAvgWatiTimeCompareYesterDay.Text = GetLabelHtmlWithMarkText(data.WaitAvgTime.CompareToYesterday);

                //平均治疗时间
                this.LAvgTreatTime.Text = GetLabelHtmlText(data.TreatAvgTime.Sum, TimeUnit);
                this.LAvgTreatTimeYesterDay.Text = GetLabelHtmlText(data.TreatAvgTime.Yesterday, TimeUnit);
                this.LAvgTreatTimeCompareYesterday.Text = GetLabelHtmlWithMarkText(data.TreatAvgTime.CompareToYesterday);
            }
        }

        

        protected override void SetContentSize()
        {
            TablePanelMain.Width = this.Width;
            TablePanelMain.Height = this.Height - TopBannerHeight;
        }
        
    }
}
