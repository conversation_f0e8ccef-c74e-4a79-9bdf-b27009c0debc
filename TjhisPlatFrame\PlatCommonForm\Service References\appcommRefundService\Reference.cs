﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace PlatCommonForm.appcommRefundService {
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="appcommRefundService.webServiceSoap")]
    public interface webServiceSoap {
        
        // CODEGEN: 命名空间 http://tempuri.org/ 的元素名称 HelloWorldResult 以后生成的消息协定未标记为 nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HelloWorld", ReplyAction="*")]
        PlatCommonForm.appcommRefundService.HelloWorldResponse HelloWorld(PlatCommonForm.appcommRefundService.HelloWorldRequest request);
        
        // CODEGEN: 命名空间 http://tempuri.org/ 的元素名称 xmlparam 以后生成的消息协定未标记为 nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/orderRefundFromHis1", ReplyAction="*")]
        PlatCommonForm.appcommRefundService.orderRefundFromHis1Response orderRefundFromHis1(PlatCommonForm.appcommRefundService.orderRefundFromHis1Request request);
        
        // CODEGEN: 命名空间 http://tempuri.org/ 的元素名称 code 以后生成的消息协定未标记为 nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/orderRefundFromHis2", ReplyAction="*")]
        PlatCommonForm.appcommRefundService.orderRefundFromHis2Response orderRefundFromHis2(PlatCommonForm.appcommRefundService.orderRefundFromHis2Request request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class HelloWorldRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="HelloWorld", Namespace="http://tempuri.org/", Order=0)]
        public PlatCommonForm.appcommRefundService.HelloWorldRequestBody Body;
        
        public HelloWorldRequest() {
        }
        
        public HelloWorldRequest(PlatCommonForm.appcommRefundService.HelloWorldRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute()]
    public partial class HelloWorldRequestBody {
        
        public HelloWorldRequestBody() {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class HelloWorldResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="HelloWorldResponse", Namespace="http://tempuri.org/", Order=0)]
        public PlatCommonForm.appcommRefundService.HelloWorldResponseBody Body;
        
        public HelloWorldResponse() {
        }
        
        public HelloWorldResponse(PlatCommonForm.appcommRefundService.HelloWorldResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class HelloWorldResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string HelloWorldResult;
        
        public HelloWorldResponseBody() {
        }
        
        public HelloWorldResponseBody(string HelloWorldResult) {
            this.HelloWorldResult = HelloWorldResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class orderRefundFromHis1Request {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="orderRefundFromHis1", Namespace="http://tempuri.org/", Order=0)]
        public PlatCommonForm.appcommRefundService.orderRefundFromHis1RequestBody Body;
        
        public orderRefundFromHis1Request() {
        }
        
        public orderRefundFromHis1Request(PlatCommonForm.appcommRefundService.orderRefundFromHis1RequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class orderRefundFromHis1RequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string xmlparam;
        
        public orderRefundFromHis1RequestBody() {
        }
        
        public orderRefundFromHis1RequestBody(string xmlparam) {
            this.xmlparam = xmlparam;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class orderRefundFromHis1Response {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="orderRefundFromHis1Response", Namespace="http://tempuri.org/", Order=0)]
        public PlatCommonForm.appcommRefundService.orderRefundFromHis1ResponseBody Body;
        
        public orderRefundFromHis1Response() {
        }
        
        public orderRefundFromHis1Response(PlatCommonForm.appcommRefundService.orderRefundFromHis1ResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class orderRefundFromHis1ResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string orderRefundFromHis1Result;
        
        public orderRefundFromHis1ResponseBody() {
        }
        
        public orderRefundFromHis1ResponseBody(string orderRefundFromHis1Result) {
            this.orderRefundFromHis1Result = orderRefundFromHis1Result;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class orderRefundFromHis2Request {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="orderRefundFromHis2", Namespace="http://tempuri.org/", Order=0)]
        public PlatCommonForm.appcommRefundService.orderRefundFromHis2RequestBody Body;
        
        public orderRefundFromHis2Request() {
        }
        
        public orderRefundFromHis2Request(PlatCommonForm.appcommRefundService.orderRefundFromHis2RequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class orderRefundFromHis2RequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string code;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string xmlparam;
        
        public orderRefundFromHis2RequestBody() {
        }
        
        public orderRefundFromHis2RequestBody(string code, string xmlparam) {
            this.code = code;
            this.xmlparam = xmlparam;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class orderRefundFromHis2Response {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="orderRefundFromHis2Response", Namespace="http://tempuri.org/", Order=0)]
        public PlatCommonForm.appcommRefundService.orderRefundFromHis2ResponseBody Body;
        
        public orderRefundFromHis2Response() {
        }
        
        public orderRefundFromHis2Response(PlatCommonForm.appcommRefundService.orderRefundFromHis2ResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class orderRefundFromHis2ResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string orderRefundFromHis2Result;
        
        public orderRefundFromHis2ResponseBody() {
        }
        
        public orderRefundFromHis2ResponseBody(string orderRefundFromHis2Result) {
            this.orderRefundFromHis2Result = orderRefundFromHis2Result;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface webServiceSoapChannel : PlatCommonForm.appcommRefundService.webServiceSoap, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class webServiceSoapClient : System.ServiceModel.ClientBase<PlatCommonForm.appcommRefundService.webServiceSoap>, PlatCommonForm.appcommRefundService.webServiceSoap {
        
        public webServiceSoapClient() {
        }
        
        public webServiceSoapClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public webServiceSoapClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public webServiceSoapClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public webServiceSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        PlatCommonForm.appcommRefundService.HelloWorldResponse PlatCommonForm.appcommRefundService.webServiceSoap.HelloWorld(PlatCommonForm.appcommRefundService.HelloWorldRequest request) {
            return base.Channel.HelloWorld(request);
        }
        
        public string HelloWorld() {
            PlatCommonForm.appcommRefundService.HelloWorldRequest inValue = new PlatCommonForm.appcommRefundService.HelloWorldRequest();
            inValue.Body = new PlatCommonForm.appcommRefundService.HelloWorldRequestBody();
            PlatCommonForm.appcommRefundService.HelloWorldResponse retVal = ((PlatCommonForm.appcommRefundService.webServiceSoap)(this)).HelloWorld(inValue);
            return retVal.Body.HelloWorldResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        PlatCommonForm.appcommRefundService.orderRefundFromHis1Response PlatCommonForm.appcommRefundService.webServiceSoap.orderRefundFromHis1(PlatCommonForm.appcommRefundService.orderRefundFromHis1Request request) {
            return base.Channel.orderRefundFromHis1(request);
        }
        
        public string orderRefundFromHis1(string xmlparam) {
            PlatCommonForm.appcommRefundService.orderRefundFromHis1Request inValue = new PlatCommonForm.appcommRefundService.orderRefundFromHis1Request();
            inValue.Body = new PlatCommonForm.appcommRefundService.orderRefundFromHis1RequestBody();
            inValue.Body.xmlparam = xmlparam;
            PlatCommonForm.appcommRefundService.orderRefundFromHis1Response retVal = ((PlatCommonForm.appcommRefundService.webServiceSoap)(this)).orderRefundFromHis1(inValue);
            return retVal.Body.orderRefundFromHis1Result;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        PlatCommonForm.appcommRefundService.orderRefundFromHis2Response PlatCommonForm.appcommRefundService.webServiceSoap.orderRefundFromHis2(PlatCommonForm.appcommRefundService.orderRefundFromHis2Request request) {
            return base.Channel.orderRefundFromHis2(request);
        }
        
        public string orderRefundFromHis2(string code, string xmlparam) {
            PlatCommonForm.appcommRefundService.orderRefundFromHis2Request inValue = new PlatCommonForm.appcommRefundService.orderRefundFromHis2Request();
            inValue.Body = new PlatCommonForm.appcommRefundService.orderRefundFromHis2RequestBody();
            inValue.Body.code = code;
            inValue.Body.xmlparam = xmlparam;
            PlatCommonForm.appcommRefundService.orderRefundFromHis2Response retVal = ((PlatCommonForm.appcommRefundService.webServiceSoap)(this)).orderRefundFromHis2(inValue);
            return retVal.Body.orderRefundFromHis2Result;
        }
    }
}
