﻿using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Columns;
using PlatCommon.Common;
using PlatCommon.SysBase;
using System;
using System.Data;
using System.Linq;
using System.Windows.Forms;
using Tjhis.Report.Custom.Base;
using Tjhis.Report.Custom.Srv;
using DataSetHelper = PlatCommon.Common.DataSetHelper;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmEditField : ParentForm
    {
        DataSet dsColumns;
        srvStatisticalQuery _srv;
        public DataTable _dtReportNo { get; set; }
        public string _displayMember { get; set; }
        public string _valueMember { get; set; }
        public string _defaultValue { get; set; }

        public DataSet dataSourcesStruct { get; set; }
        public string dictID { get; set; } = string.Empty;

        public string _appName { get; set; }

        private int iReportNo { get; set; }

        public frmEditField()
        {
            InitializeComponent();

            barBtnAdd.ItemClick += (s, e) =>
            {
                if (dsColumns != null && dsColumns.HasChanges())
                {
                    GridViewUpdateCurrentRow();

                    DataSet dsChanges = dsColumns.GetChanges();
                    DataRow[] drChanges = dsChanges.Tables[0].Select("COLUMN_NAME='列名' OR COLUMN_FIELD is null");
                    if (drChanges.Length > 0)
                    {
                        XtraMessageBox.Show("当前列名或绑定字段异常，请完善信息并保存后再新增", "提示");
                        return;
                    }
                    else
                    {
                        SaveData();
                    }

                }
                gridView1.AddNewRow();
                GridViewUpdateCurrentRow();
            };

            barBtnDelete.ItemClick += (s, e) =>
            {
                gridView1.DeleteSelectedRows();
            };

            barBtnSave.ItemClick += (s, e) =>
            {
                if (SaveData())
                {
                    InitRecordColumns();
                    //XtraMessageBox.Show("保存成功","提示");
                }
            };

            this.FormClosed += (s, e) =>
            {
                GridViewUpdateCurrentRow();
                if (dsColumns != null && dsColumns.HasChanges())
                {
                    if (XtraMessageBox.Show("有数据更改，是否保存？", "提示", MessageBoxButtons.YesNo) == DialogResult.Yes)
                    {
                        SaveData();
                    }
                }
            };
            gridView1.FocusedRowChanged += (s, e) =>
            {
                DataRow focusedRow = gridView1.GetFocusedDataRow();
                if (focusedRow == null)
                {
                    return;
                }
                DataView view = dsColumns.Tables[0].AsDataView();

                view.RowFilter = "COLUMN_ID = '" + focusedRow["COLUMN_ID"] + "'";

                vColumnsDetail.DataSource = view;
            };

            rColumnField.Properties.RowEdit = COLUMN_FIELD.ColumnEdit;
            rColumnWidth.Properties.RowEdit = COLUMN_WIDTH.ColumnEdit;
            rIfSummary.Properties.RowEdit = IF_SUMMARY.ColumnEdit;
            rSummaryType.Properties.RowEdit = SUMMARY_TYPE.ColumnEdit;
            rUnbound.Properties.RowEdit = UNBOUND.ColumnEdit;
            rExpression.Properties.RowEdit = EXPRESSION.ColumnEdit;
            rUnboundType.Properties.RowEdit = UNBOUND_TYPE.ColumnEdit;
            rVisible.Properties.RowEdit = VISIBLE.ColumnEdit;
            rFormatRule.Properties.RowEdit = IF_SUMMARY.ColumnEdit;
        }

        private void frmEditField_Load(object sender, EventArgs e)
        {
            if (!DesignMode) _srv = new srvStatisticalQuery();
            if (_dtReportNo != null)
            {
                barReportNo.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
                lookUpReportNo.DataSource = _dtReportNo;
                lookUpReportNo.DisplayMember = _displayMember;
                lookUpReportNo.ValueMember = _valueMember;
                barReportNo.EditValue = _defaultValue;
                lookUpReportNo.EditValueChanged += LookUpReportNo_EditValueChanged;
                iReportNo = _defaultValue.ToInt();
            }
            else
            {
                barReportNo.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }
            BindingFieldList();
            BindingSummrayType();
            BindingUnboundType();
            BindingDefindeName();
            BindingFormatType();

            gridView1.InitNewRow += (s, ev) =>
            {
                InitNewRow(gridView1.GetDataRow(ev.RowHandle));
            };

            BindingColumsConfig();
        }

        void BindingColumsConfig()
        {
            if (iReportNo == 0)
            {
                dsColumns = _srv.GetReportConfig(dictID, _appName);
            }
            else
            {
                dsColumns = _srv.GetReportConfigReportNo(dictID, _appName, iReportNo);
            }
            gridControl1.DataSource = dsColumns.Tables[0];

            InitRecordColumns();
        }

        void BindingFieldList()
        {
            string coustomDictStr = string.Empty;
            int iTbIndex = 0;
            if (iReportNo > 0)
            {
                iTbIndex = iReportNo - 1;
            }
            foreach (DataColumn dc in dataSourcesStruct.Tables[iTbIndex].Columns)
            {
                coustomDictStr += dc.ColumnName + "-" + dc.ColumnName + ";";
            }
            DataTable dsCuostomDict = srvStatisticalQuery.CreateDict(coustomDictStr);

            repLupFields.DataSource = dsCuostomDict;
        }

        void BindingFormatType()
        {
            //None = 0,Numeric = 1,DateTime = 2,Custom = 3
            string formatTypeStr = "None-无;Numeric-数值;DateTime-日期;Custom-自定义";
            DataTable dsFormatTypeDict = srvStatisticalQuery.CreateDict(formatTypeStr);

            repLupFormatType.DataSource = dsFormatTypeDict;
        }

        void BindingSummrayType()
        {
            //SUM  MIN MAX COUNT AVG
            string sumTypeStr = "Sum-和;Min-最小值;Max-最大值;Count-数量;Average-平均值;Custom-自定义";
            DataTable dsCuostomDict = srvStatisticalQuery.CreateDict(sumTypeStr);

            repLupSummrayType.DataSource = dsCuostomDict;
        }

        void BindingUnboundType()
        {
            //Bound,Integer,Decimal,DateTime,String,Boolean,Object

            string unboundType = "Decimal-数值;Integer-整型;String-字符串;DateTime-时间;Boolean-布尔";
            DataTable dsUnboundType = srvStatisticalQuery.CreateDict(unboundType);

            repLupUnboundType.DataSource = dsUnboundType;
        }

        void BindingDefindeName()
        {
            string names = @"Bold Text-粗体;Green Bold Text-绿色粗体;Green Fill-绿色填充;Green Fill, Green Text-绿色字体加填充;Green Text-绿色字体;Italic Text-斜体;Red Bold Text-红色粗体;Red Fill-红色填充;Red Fill, Red Text-红色字体加填充;Red Text-红色字体;Strikeout Text-删除线;Yellow Fill, Yellow Text-黄色字体加填充";
            DataTable dtNames = srvStatisticalQuery.CreateDict(names);

            repLupPreNames.DataSource = dtNames;
        }

        void InitNewRow(DataRow newRow)
        {

            newRow["SERIAL_NO"] = DataSetHelper.GetMaxValue(dsColumns, "", "SERIAL_NO", 0) + 1;
            newRow["COLUMN_ID"] = DateTime.Now.ToString("yyyyMMddHHmmssfff");
            newRow["DICT_ID"] = dictID;
            newRow["COLUMN_NAME"] = "列名";
            newRow["COLUMN_WIDTH"] = 80;
            newRow["HOSPITAL_CODE"] = SystemParm.HisUnitCode;
            newRow["APP_NAME"] = _appName;// SystemParm.AppName;
            newRow["IF_GROUP"] = "0";
            newRow["GROUP_INDEX"] = 1;
            newRow["IF_SUMMARY"] = "0";
            newRow["SUMMARY_TYPE"] = "Sum";
            newRow["IF_UNBOUND"] = "0";
            newRow["UNBOUND_TYPE"] = "String";
            newRow["VISIBLE"] = "0";
            if (barReportNo.Visibility == DevExpress.XtraBars.BarItemVisibility.Always)
            {
                newRow["REPORTNO"] = barReportNo.EditValue.ToInt();
            }

            GridViewUpdateCurrentRow();
        }

        private void GridViewUpdateCurrentRow()
        {
            gridView1.CloseEditor();
            gridView1.UpdateCurrentRow();
            vColumnsDetail.CloseEditor();
            if (vColumnsDetail.FocusedRecord > 0)
            {
                vColumnsDetail.UpdateFocusedRecord();
            }
        }

        string ExpressionEdit(GridColumn gc, string expression)
        {
            DevExpress.XtraEditors.Design.UnboundColumnExpressionEditorForm f1 = new DevExpress.XtraEditors.Design.UnboundColumnExpressionEditorForm(gc, null);

            if (f1.ShowDialog() == DialogResult.OK)
                return f1.Expression;

            return expression;
        }

        private void InitRecordColumns()
        {
            gvDesign.Columns.Clear();
            //gvDesign.OptionsView.ColumnAutoWidth = false;

            if (dsColumns == null || dsColumns.Tables[0].Rows.Count == 0)
            {
                return;
            }

            GridViewHelper.InitGridViewColumns(gvDesign, dsColumns.Tables[0]);
            gvDesign.HorzScrollVisibility = DevExpress.XtraGrid.Views.Base.ScrollVisibility.Auto;

            int colWidth = 0;
            foreach (DataRow configRow in dsColumns.Tables[0].Rows)
            {
                colWidth += int.Parse(configRow["COLUMN_WIDTH"].ToString());
            }
            gvDesign.OptionsView.ColumnAutoWidth = false;
            this.gcDesign.Width = colWidth;
            this.gcDesign.Parent.Width = colWidth;
        }

        private bool SaveData()
        {
            GridViewUpdateCurrentRow();

            if (dsColumns.HasChanges())
                return _srv.SaveData(dsColumns) > 0;
            return false;
        }

        private void repositoryItemButtonEdit1_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            GridViewUpdateCurrentRow();
            InitRecordColumns();

            DataRow dr = gridView1.GetFocusedDataRow();
            if (dr == null) return;

            GridColumn gc = gvDesign.Columns.ToList().Find(s => s.Name.Equals("COL_" + dr["SERIAL_NO"].ToString()));//["COL_" + dr["SERIAL_NO"].ToString().ToUpper()];

            string expStr = ExpressionEdit(gc, dr["EXPRESSION"].ToString());

            if (!expStr.Equals(dr["EXPRESSION"]))
                dr["EXPRESSION"] = expStr;
        }

        private void repositoryItemButtonEdit2_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            GridViewUpdateCurrentRow();
            InitRecordColumns();

            DataRow dr = gridView1.GetFocusedDataRow();
            if (dr == null) return;

            GridColumn gc = gvDesign.Columns.ToList().Find(s => s.Name.Equals("COL_" + dr["SERIAL_NO"].ToString()));//["COL_" + dr["SERIAL_NO"].ToString().ToUpper()];

            string expStr = RuleExpressionEdit(gc, dr["RULE_EXPRESSION"].ToString());

            if (!expStr.Equals(dr["RULE_EXPRESSION"]))
                dr["RULE_EXPRESSION"] = expStr;
        }

        string RuleExpressionEdit(GridColumn gc, string expression)
        {
            DevExpress.XtraGrid.GridFormatRule formatRule = new DevExpress.XtraGrid.GridFormatRule();
            FormatConditionRuleValue r = new FormatConditionRuleValue();
            r.Condition = FormatCondition.Expression;
            r.Expression = expression;

            //r.PredefinedName = predefinedName;// "Red Text";
            formatRule.Rule = r;
            formatRule.Column = gc;
            gvDesign.FormatRules.Clear();
            gvDesign.FormatRules.Add(formatRule);
            DevExpress.XtraEditors.Design.FormatRuleExpressionEditorForm f1 = new DevExpress.XtraEditors.Design.FormatRuleExpressionEditorForm(r, null, expression);

            if (f1.ShowDialog() == DialogResult.OK)
                return f1.Expression;

            return expression;
        }

        private void LookUpReportNo_EditValueChanged(object sender, EventArgs e)
        {
            iReportNo = (sender as LookUpEdit).EditValue.ToInt();
            BindingFieldList();
            BindingSummrayType();
            BindingUnboundType();
            BindingDefindeName();
            BindingFormatType();
            BindingColumsConfig();
        }

        private void barLargeButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Close();
        }
    }
}
