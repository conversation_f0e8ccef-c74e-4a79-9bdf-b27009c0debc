﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Oracle.ManagedDataAccess.Client;

namespace OracleDAL
{
    public class SEC_RIGHT_GROUP_VS_MENUS_Dao : SEC_RIGHT_GROUP_VS_MENUS_Dao_Base
    {
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string APPLICATION_CODE, string RIGHT_GROUP_CODE, Utility.OracleODP.OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from SEC_RIGHT_GROUP_VS_MENUS ");
            strSql.Append(" where APPLICATION_CODE=:APPLICATION_CODE and RIGHT_GROUP_CODE=:RIGHT_GROUP_CODE");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":APPLICATION_CODE", OracleDbType.Varchar2, 16);
            p.Value = APPLICATION_CODE;
            parameters.Add(p);

            p = new OracleParameter(":RIGHT_GROUP_CODE", OracleDbType.Varchar2, 4);
            p.Value = RIGHT_GROUP_CODE;
            parameters.Add(p);

            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

    }
}
