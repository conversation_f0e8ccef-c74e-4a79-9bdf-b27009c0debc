﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace PlatCommon.Common
{
    public class ClassInsurVsMessage
    {
        /// <summary>
        /// 医保编码
        /// </summary>
        public string insur_code { get; set; }
        /// <summary>
        /// 医保比例
        /// </summary>
        public string insur_scale { get; set; }
        /// <summary>
        /// 医保等级
        /// </summary>
        public string insur_level { get; set; }
        /// <summary>
        /// 医保名称
        /// </summary>
        public string insur_name { get; set; }
        /// <summary>
        /// 医保备注说明
        /// </summary>
        public string insur_memo { get; set; }
        /// <summary>
        /// 医保类别
        /// </summary>
        public string insur_class { get; set; }
        /// <summary>
        /// 医保限制标识
        /// </summary>
        public string insur_xzbs { get; set; }
        /// <summary>
        /// 医保限制说明
        /// </summary>
        public string insur_xzsm { get; set; }
    }
}
