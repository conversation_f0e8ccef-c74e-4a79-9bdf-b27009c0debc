﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Data.OleDb;

namespace PlatCommon.Common
{
    /// <summary>
    /// 公共泛型类
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class Result1<T>
    {
        public Result1()
        {
            HasError = false;
            ErrMsg = string.Empty;
        }
        /// <summary>
        /// 正常返回参数使用
        /// </summary>
        /// <param name="t">object类型</param>
        public Result1(T t)
        {
            HasError = false;
            ErrMsg = string.Empty;
            MsgTip = string.Empty;
            Data = t;
        }
        /// <summary>
        /// 出现异常时使用
        /// </summary>
        /// <param name="ex"></param>
        public Result1(Exception ex)
        {
            HasError = true;
            ErrMsg = ex.Message;


        }
        /// <summary>
        /// 是否存在错误
        /// </summary>
        public bool HasError { get; set; }

        /// <summary>
        /// 错误输出语句
        /// </summary>
        public string ErrMsg { get; set; }
        /// <summary>
        /// 提示信息
        /// </summary>
        public string MsgTip { get; set; }

        /// <summary>
        /// 数据集
        /// </summary>
        public T Data { get; set; }
    }
    public class database
    {
        public struct Area_Type
        {
            /// <summary>
            /// 省代码
            /// </summary>
           public string strShengCode;
            /// <summary>
            /// 省名称
            /// </summary>
           public string strShengName;
            /// <summary>
            /// 市代码
            /// </summary>
           public string strCityCode;
            /// <summary>
            /// 市名称
            /// </summary>
           public string strCityName;
            /// <summary>
            /// 县代码
            /// </summary>
           public string strAreaCode;
            /// <summary>
            /// 县名称
            /// </summary>
           public string strAreaName;
        }

        private static OleDbConnection conn = null;
        private static string strConn = "Provider=MSDAORA.1;Data Source=(DESCRIPTION =(ADDRESS_LIST =(ADDRESS = (PROTOCOL = TCP)" +
                 "(HOST = *************)(PORT = 1521)))(CONNECT_DATA =(SERVICE_NAME = ly)));" +
          "User ID=system;Password=manager;";
        public database()
        {
        }
        public database(string constr) {
            strConn = constr;
        }
        public static void setConnStr(string constr)
        {
            strConn = constr;
        }
        /// <summary>
        /// 返回打开连接的OleDbConnect对象实例
        /// </summary>
        /// <param name="strconn"></param>
        /// <returns></returns>
        public static OleDbConnection connDB()
        {
            if (conn == null)
                conn = new OleDbConnection(strConn);
            if (conn.State == ConnectionState.Closed)
            {
                conn.ConnectionString = strConn;
                try
                {
                    conn.Open();
                    return conn;
                }
                catch (Exception ex)
                {
                    return null;
                }
            }
            return conn;
        }
        /// <summary>
        /// 根据指定的sql语句，返回相应的dataset对象实例
        /// </summary>
        /// <param name="strSQL"></param>
        /// <returns></returns>
        //public static DataSet getDS(string strSQL)
        //{
        //    if (connDB() != null)
        //    {
        //        OleDbDataAdapter odaDS = new OleDbDataAdapter(strSQL, conn);
        //        DataSet ds = new DataSet();
        //        odaDS.Fill(ds);
        //        return ds;
        //    }
        //    return null;
        //}
        public static DataSet getDS(string strSQL)
        {
            return new NM_Service.NMService.ServerPublicClient().GetDataBySql(strSQL);
        }
        /// <summary>
        /// 分割地区代码，分割成省、市、县
        /// </summary>
        /// <param name="strCode"></param>
        /// <returns></returns>
        public static Area_Type getBreakAreaCode(string strCode)
        {
            Area_Type areType = new Area_Type();
            string ls_code1, ls_code2, ls_code3;
            string ls_name1, ls_name2, ls_name3;
            string ls_a1, ls_a2, ls_a3;

            try
            {
                if (string.IsNullOrEmpty(strCode))
                {
                    areType.strShengCode = string.Empty;
                    areType.strShengName = string.Empty;

                    areType.strCityCode = string.Empty;
                    areType.strCityName = string.Empty;

                    areType.strAreaCode = string.Empty;
                    areType.strAreaName = string.Empty;
                    return areType;
                }
                ls_a1 = strCode.Trim().Substring(0, 2) + "0000";
                DataSet dsArea = getDS("select area_name from mr_area_dict where area_code='" + ls_a1 + "'");
                if (dsArea == null || dsArea.Tables[0].Rows.Count == 0)
                {
                    ls_name1 = "-";
                    ls_code1 = string.Empty;
                    ls_name2 = "-";
                    ls_code2 = string.Empty;
                    ls_name3 = "-";
                    ls_code3 = string.Empty;
                }
                else//有值
                {
                    ls_name1 = dsArea.Tables[0].Rows[0]["area_name"].ToString().Trim();
                    switch (ls_a1)
                    {
                        case "150000":
                            ls_name1 = "内蒙古";
                            break;
                        case "540000":
                            ls_name1 = "西藏";
                            break;

                        case "640000":
                            ls_name1 = "宁夏";
                            break;
                        case "650000":
                            ls_name1 = "新疆";
                            break;
                        case "450000":
                            ls_name1 = "广西";
                            break;
                        case "710000":
                            ls_name1 = "台湾";
                            break;
                        case "810000":
                            ls_name1 = "香港";
                            break;
                        case "820000":
                            ls_name1 = "澳门";
                            break;
                        default:
                            ls_name1 = ls_name1.Substring(0, ls_name1.Length - 1);//去掉省字
                            break;
                    }
                    ls_code1 = ls_a1;//当前的代码

                    if (ls_code1 == strCode)//仅一级
                    {
                        ls_name2 = "-";
                        ls_code2 = string.Empty;
                        ls_name3 = "-";
                        ls_code3 = string.Empty;
                    }
                    else//多级
                    {
                        ls_a2 = strCode.Trim().Substring(0, 4) + "00";
                        dsArea.Clear();
                        dsArea = getDS("select area_name from mr_area_dict where area_code='" + ls_a2 + "'");
                        if (dsArea== null || dsArea.Tables[0].Rows.Count == 0)
                        {
                            ls_name2 = "-";
                            ls_code2 = string.Empty;
                        }
                        else
                        {
                            ls_name2 = dsArea.Tables[0].Rows[0]["area_name"].ToString().Trim();
                            if (ls_name2 == "市辖区" || ls_name2 == "县")
                            {
                                ls_name2 = "-";
                                ls_code2 = string.Empty;
                            }
                            else
                            {
                                if (ls_name2.Substring(ls_name2.Length - 1, 1) == "市" && ls_name2.Length > 2)
                                {
                                    ls_name2 = ls_name2.Substring(0, ls_name2.Length - 1);
                                }
                                ls_code2 = ls_a2;
                            }

                        }
                        if (ls_code2 == strCode)//仅两级
                        {
                            ls_name3 = "-";
                            ls_code3 = string.Empty;
                        }
                        else //多级
                        {
                            ls_a3 = strCode.Trim();
                            dsArea.Tables[0].Rows.Clear();
                            dsArea = getDS("select area_name from mr_area_dict where area_code='" + ls_a3 + "'");
                            if (dsArea != null && dsArea.Tables[0].Rows.Count == 1)
                            {
                                ls_name3 = dsArea.Tables[0].Rows[0]["area_name"].ToString().Trim();
                                if (ls_name3.Substring(ls_name3.Length - 1, 1) == "县" && ls_name3.Length > 2 && ls_name3.Substring(ls_name3.Length - 3, 3) != "自治县")
                                {
                                    ls_name3 = ls_name3.Substring(0, ls_name3.Length - 1);
                                }
                            }
                            else
                                ls_name3 = string.Empty;
                            ls_code3 = ls_a3;
                        }
                    }


                }
                areType.strShengCode = ls_code1;
                areType.strShengName = ls_name1;

                areType.strCityCode = ls_code2;
                areType.strCityName = ls_name2;

                areType.strAreaCode = ls_code3;
                areType.strAreaName = ls_name3;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            
            return areType;
        }
        /// <summary>
        /// 执行指定的sql语句
        /// 成功返回true,否则返回false
        /// </summary>
        /// <param name="strSQL"></param>
        /// <returns></returns>
        public static Boolean saveSql(string strSQL)
        {
            try{
                if (connDB() != null)
                {
                    //开始执行sql语句
                    OleDbCommand cmd = new OleDbCommand(strSQL, conn);
                    cmd.CommandType = CommandType.Text;
                    if (cmd.ExecuteNonQuery() < 0)
                        return false;
                }
            }catch(Exception ex)
            {
                return false;
            }
            return true;
        }
        /// <summary>
        /// 带事物的sql语句执行操作,操作失败会自动回滚
        /// </summary>
        /// <param name="strSQL"></param>
        /// <param name="tranObj"></param>
        /// <returns></returns>
        //public static Result1<Boolean> saveSql(string strSQL,OleDbTransaction tranObj,OleDbConnection conObj)
        //{
        //    Result1<Boolean> _res = new Result1<bool>();
        //    try
        //    {
        //        //if (connDB() != null)
        //        //{
        //            //开始执行sql语句
        //            OleDbCommand cmd = new OleDbCommand(strSQL, conObj, tranObj);
        //            cmd.CommandType = CommandType.Text;
        //            cmd.ExecuteNonQuery();
        //        //}
        //    }
        //    catch (Exception ex)
        //    {
        //        //tranObj.Rollback();
        //        _res.HasError = true;
        //        _res.ErrMsg = ex.Message;
        //        _res.Data = false;
              
        //    }
        //    return _res;
        //}
        public static Result1<Boolean> saveSql(string strSQL, OleDbTransaction tranObj, OleDbConnection conObj)
        {
            Result1<Boolean> _res = new Result1<bool>();
            try
            {
                //开始执行sql语句
                OleDbCommand cmd = new OleDbCommand(strSQL, conObj, tranObj);
                cmd.CommandType = CommandType.Text;
                cmd.ExecuteNonQuery();
                //}
            }
            catch (Exception ex)
            {
                //tranObj.Rollback();
                _res.HasError = true;
                _res.ErrMsg = ex.Message;
                _res.Data = false;

            }
            return _res;
        }
        /// <summary>
        /// 验证身份证号
        /// </summary>
        /// <param name="strID"></param>
        /// <returns></returns>
        public static Result1<Boolean> isSFZH(string strID)
        {
            Result1<Boolean> ret_obj = new Result1<Boolean>();

            strID = strID.Replace("-", "");
            string ls_date = string.Empty;

            if (strID.Trim().Length == 15)//15位身份证号
            {
                ls_date = "19" + strID.Trim().Substring(6,2)+"-"+strID.Trim().Substring(8,2)+"-"+strID.Trim().Substring(10,2);
            }
            else if (strID.Trim().Length == 18)//18位身份证号
            {
                ls_date = "19" + strID.Trim().Substring(6, 2) + "-" + strID.Trim().Substring(10, 2) + "-" + strID.Trim().Substring(12, 2);
            }
            else
            {
                ret_obj.Data = false;
                return ret_obj;
            }
            try
            {
                DateTime.Parse(ls_date);
            }
            catch(Exception ex)
            {
                ret_obj.HasError = true;
                ret_obj.ErrMsg = ex.Message;
                return ret_obj;
            }
            ret_obj.MsgTip = ls_date;
            return ret_obj;
        }
        /// <summary>
        /// 根据diagnonsis_code值返回字典标准名称
        /// </summary>
        /// <param name="strDescCode"></param>
        /// <returns></returns>
        public static Result1<string> fGetICDDesc(string strDescCode)
        {
            Result1<string> _res = new Result1<string>();
            try
            {
                DataTable dtData = getDS(string.Format("select diagnosis_name from diagnosis_dict  where diagnosis_code = '{0}' and STD_INDICATOR=1 and rownum = 1",strDescCode.Trim())).Tables[0];
                if (dtData.Rows.Count == 0)
                {
                    dtData = getDS(string.Format("select diagnosis_name from diagnosis_dict  where diagnosis_code = '{0}' and rownum = 1", strDescCode.Trim())).Tables[0];
                    if(dtData.Rows.Count>0)
                        _res.Data = dtData.Rows[0]["diagnosis_name"].ToString().Trim();
                }
                else
                {
                    _res.Data = dtData.Rows[0]["diagnosis_name"].ToString().Trim();
                }
            }
            catch(Exception ex)
            {
                _res.HasError = true;
                _res.Data = string.Empty;
                _res.ErrMsg = ex.Message;
                _res.MsgTip = string.Empty;
            }
            return _res;
        }


        /// <summary>
        /// 验证身份证号
        /// </summary>
        /// <param name="strID"></param>
        /// <returns></returns>
        public static bool ISIDNO(string strID)
        {
            bool bl = false;
            if (strID.Trim().Length == 15)//15位身份证号
            {
                bl = CheckIDCard15(strID);
            }
            else if (strID.Trim().Length == 18)//18位身份证号
            {
                bl = CheckIDCard18(strID);
            }
            return bl;
        }
        /// <summary>
        /// 15位身份证号验证
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        private static bool CheckIDCard15(string Id)

        {
            long n = 0;
            if (long.TryParse(Id, out n) == false || n < Math.Pow(10, 14))

            {
                return false;//数字验证

            }

            string address = "11x22x35x44x53x12x23x36x45x54x13x31x37x46x61x14x32x41x50x62x15x33x42x51x63x21x34x43x52x64x65x71x81x82x91";

            if (address.IndexOf(Id.Remove(2)) == -1)
            {
                return false;//省份验证
            }

            string birth = Id.Substring(6, 6).Insert(4, "-").Insert(2, "-");
            DateTime time = new DateTime();

            if (DateTime.TryParse(birth, out time) == false)
            {
                return false;//生日验证
            }

            return true;//符合15位身份证标准

        }
        /// <summary>
        /// 18位身份证号验证
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        private static bool CheckIDCard18(string Id)
        {
            long n = 0;
            if (long.TryParse(Id.Remove(17), out n) == false || n < Math.Pow(10, 16) || long.TryParse(Id.Replace('x', '0').Replace('X', '0'), out n) == false)
            {
                return false;//数字验证
            }

            string address = "11x22x35x44x53x12x23x36x45x54x13x31x37x46x61x14x32x41x50x62x15x33x42x51x63x21x34x43x52x64x65x71x81x82x91";

            if (address.IndexOf(Id.Remove(2)) == -1)
            {
                return false;//省份验证
            }
            string birth = Id.Substring(6, 8).Insert(6, "-").Insert(4, "-");
            DateTime time = new DateTime();
            if (DateTime.TryParse(birth, out time) == false)
            {
                return false;//生日验证
            }
            string[] arrVarifyCode = ("1,0,x,9,8,7,6,5,4,3,2").Split(',');
            string[] Wi = ("7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2").Split(',');
            char[] Ai = Id.Remove(17).ToCharArray();
            int sum = 0;
            for (int i = 0; i < 17; i++)
            {
                sum += int.Parse(Wi[i]) * int.Parse(Ai[i].ToString());
            }
            int y = -1;
            Math.DivRem(sum, 11, out y);
            if (arrVarifyCode[y] != Id.Substring(17, 1).ToLower())
            {
                return false;//校验码验证
            }
            return true;//符合GB11643-1999标准
        }
        /////////////////

    }
}
