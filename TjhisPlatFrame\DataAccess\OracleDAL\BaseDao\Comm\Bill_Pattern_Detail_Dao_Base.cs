﻿using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using Utility;
using Utility.OracleODP;
using Oracle.ManagedDataAccess.Client;

namespace OracleDAL
{

    /// <summary>
    /// 费用模板明细记录 数据库操作类
    /// </summary>

    public class BILL_PATTERN_DETAIL_Dao_Base
    {
        #region   Method
        public bool Exists(string PATTERN_NAME, decimal ITEM_NO, string DEPT_CODE, OracleBaseClass db)
        {
            #region  init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from BILL_PATTERN_DETAIL");
            strSql.Append(" where ");
            strSql.Append(" PATTERN_NAME = :PATTERN_NAME and  ");
            strSql.Append(" ITEM_NO = :ITEM_NO and  ");
            strSql.Append(" DEPT_CODE = :DEPT_CODE ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":PATTERN_NAME", OracleDbType.Varchar2, 40);
            p.Value = PATTERN_NAME;
            parameters.Add(p);

            p = new OracleParameter(":ITEM_NO", OracleDbType.Decimal, 3);
            p.Value = ITEM_NO;
            parameters.Add(p);

            p = new OracleParameter(":DEPT_CODE", OracleDbType.Varchar2, 8);
            p.Value = DEPT_CODE;
            parameters.Add(p);

            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    int cmdresult;
                    cmdresult = int.Parse(ds.Tables[0].Rows[0][0].ToString());
                    if (cmdresult <= 0)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                    return false;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.BILL_PATTERN_DETAIL model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into BILL_PATTERN_DETAIL(");
            strSql.Append("PATTERN_NAME,ITEM_NO,ITEM_CLASS,ITEM_CODE,ITEM_SPEC,UNITS,PERFORMED_BY,AMOUNT,DEPT_CODE");
            strSql.Append(") values (");
            strSql.Append(":PATTERN_NAME,:ITEM_NO,:ITEM_CLASS,:ITEM_CODE,:ITEM_SPEC,:UNITS,:PERFORMED_BY,:AMOUNT,:DEPT_CODE");
            strSql.Append(") ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":PATTERN_NAME", OracleDbType.Varchar2, 40);
            p.Value = model.PATTERN_NAME;
            parameters.Add(p);

            p = new OracleParameter(":ITEM_NO", OracleDbType.Decimal, 3);
            p.Value = model.ITEM_NO;
            parameters.Add(p);

            p = new OracleParameter(":ITEM_CLASS", OracleDbType.Varchar2, 1);
            p.Value = model.ITEM_CLASS;
            parameters.Add(p);

            p = new OracleParameter(":ITEM_CODE", OracleDbType.Varchar2, 20);
            p.Value = model.ITEM_CODE;
            parameters.Add(p);

            p = new OracleParameter(":ITEM_SPEC", OracleDbType.Varchar2, 50);
            p.Value = model.ITEM_SPEC;
            parameters.Add(p);

            p = new OracleParameter(":UNITS", OracleDbType.Varchar2, 8);
            p.Value = model.UNITS;
            parameters.Add(p);

            p = new OracleParameter(":PERFORMED_BY", OracleDbType.Varchar2, 8);
            p.Value = model.PERFORMED_BY;
            parameters.Add(p);

            p = new OracleParameter(":AMOUNT", OracleDbType.Decimal, 6);
            p.Value = model.AMOUNT;
            parameters.Add(p);

            p = new OracleParameter(":DEPT_CODE", OracleDbType.Varchar2, 8);
            p.Value = model.DEPT_CODE;
            parameters.Add(p);
            #endregion
            try
            {

                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.BILL_PATTERN_DETAIL model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update BILL_PATTERN_DETAIL set ");

            strSql.Append(" PATTERN_NAME = :PATTERN_NAME , ");
            strSql.Append(" ITEM_NO = :ITEM_NO , ");
            strSql.Append(" ITEM_CLASS = :ITEM_CLASS , ");
            strSql.Append(" ITEM_CODE = :ITEM_CODE , ");
            strSql.Append(" ITEM_SPEC = :ITEM_SPEC , ");
            strSql.Append(" UNITS = :UNITS , ");
            strSql.Append(" PERFORMED_BY = :PERFORMED_BY , ");
            strSql.Append(" AMOUNT = :AMOUNT , ");
            strSql.Append(" DEPT_CODE = :DEPT_CODE  ");
            strSql.Append(" where PATTERN_NAME=:PATTERN_NAME and ITEM_NO=:ITEM_NO and DEPT_CODE=:DEPT_CODE  ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":PATTERN_NAME", OracleDbType.Varchar2, 40);
            p.Value = model.PATTERN_NAME;
            parameters.Add(p);

            p = new OracleParameter(":ITEM_NO", OracleDbType.Decimal, 3);
            p.Value = model.ITEM_NO;
            parameters.Add(p);

            p = new OracleParameter(":ITEM_CLASS", OracleDbType.Varchar2, 1);
            p.Value = model.ITEM_CLASS;
            parameters.Add(p);

            p = new OracleParameter(":ITEM_CODE", OracleDbType.Varchar2, 20);
            p.Value = model.ITEM_CODE;
            parameters.Add(p);

            p = new OracleParameter(":ITEM_SPEC", OracleDbType.Varchar2, 50);
            p.Value = model.ITEM_SPEC;
            parameters.Add(p);

            p = new OracleParameter(":UNITS", OracleDbType.Varchar2, 8);
            p.Value = model.UNITS;
            parameters.Add(p);

            p = new OracleParameter(":PERFORMED_BY", OracleDbType.Varchar2, 8);
            p.Value = model.PERFORMED_BY;
            parameters.Add(p);

            p = new OracleParameter(":AMOUNT", OracleDbType.Decimal, 6);
            p.Value = model.AMOUNT;
            parameters.Add(p);

            p = new OracleParameter(":DEPT_CODE", OracleDbType.Varchar2, 8);
            p.Value = model.DEPT_CODE;
            parameters.Add(p);
            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string PATTERN_NAME, decimal ITEM_NO, string DEPT_CODE, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from BILL_PATTERN_DETAIL ");
            strSql.Append(" where PATTERN_NAME=:PATTERN_NAME and ITEM_NO=:ITEM_NO and DEPT_CODE=:DEPT_CODE ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":PATTERN_NAME", OracleDbType.Varchar2, 40);
            p.Value = PATTERN_NAME;
            parameters.Add(p);

            p = new OracleParameter(":ITEM_NO", OracleDbType.Decimal, 3);
            p.Value = ITEM_NO;
            parameters.Add(p);

            p = new OracleParameter(":DEPT_CODE", OracleDbType.Varchar2, 8);
            p.Value = DEPT_CODE;
            parameters.Add(p);

            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }



        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.BILL_PATTERN_DETAIL GetModel(string PATTERN_NAME, decimal ITEM_NO, string DEPT_CODE, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select PATTERN_NAME, ITEM_NO, ITEM_CLASS, ITEM_CODE, ITEM_SPEC, UNITS, PERFORMED_BY, AMOUNT, DEPT_CODE  ");
            strSql.Append("  from BILL_PATTERN_DETAIL ");
            strSql.Append(" where PATTERN_NAME=:PATTERN_NAME and ITEM_NO=:ITEM_NO and DEPT_CODE=:DEPT_CODE ");
            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":PATTERN_NAME", OracleDbType.Varchar2, 40);
            p.Value = PATTERN_NAME;
            parameters.Add(p);

            p = new OracleParameter(":ITEM_NO", OracleDbType.Decimal, 3);
            p.Value = ITEM_NO;
            parameters.Add(p);

            p = new OracleParameter(":DEPT_CODE", OracleDbType.Varchar2, 8);
            p.Value = DEPT_CODE;
            parameters.Add(p);
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;
                    Model.BILL_PATTERN_DETAIL model = new Model.BILL_PATTERN_DETAIL();

                    if (cmdresult > 0)
                    {
                        model = CopyToModel(ds.Tables[0].Rows[0]);
                        return model;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM BILL_PATTERN_DETAIL ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得几行数据
        /// </summary>
        public DataSet GetList(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            #region 初始化参数
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM BILL_PATTERN_DETAIL T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.BILL_PATTERN_DETAIL> GetObservableCollection(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM BILL_PATTERN_DETAIL ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.BILL_PATTERN_DETAIL> list = new System.Collections.ObjectModel.ObservableCollection<Model.BILL_PATTERN_DETAIL>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.BILL_PATTERN_DETAIL model = new Model.BILL_PATTERN_DETAIL();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表 
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.BILL_PATTERN_DETAIL> GetObservableCollection(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM BILL_PATTERN_DETAIL T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }

            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.BILL_PATTERN_DETAIL> list = new System.Collections.ObjectModel.ObservableCollection<Model.BILL_PATTERN_DETAIL>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.BILL_PATTERN_DETAIL model = new Model.BILL_PATTERN_DETAIL();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion   Method
        #region
        /// <summary>
        /// 
        /// </summary>
        protected Model.BILL_PATTERN_DETAIL CopyToModel(DataRow dRow)
        {
            Model.BILL_PATTERN_DETAIL model1 = new Model.BILL_PATTERN_DETAIL();

            if (dRow["PATTERN_NAME"] != null && dRow["PATTERN_NAME"].ToString() != "")
            {
                model1.PATTERN_NAME = dRow["PATTERN_NAME"].ToString();
            }

            if (dRow["ITEM_NO"] != null && dRow["ITEM_NO"].ToString() != "")
            {
                model1.ITEM_NO = decimal.Parse(dRow["ITEM_NO"].ToString());
            }

            if (dRow["ITEM_CLASS"] != null && dRow["ITEM_CLASS"].ToString() != "")
            {
                model1.ITEM_CLASS = dRow["ITEM_CLASS"].ToString();
            }

            if (dRow["ITEM_CODE"] != null && dRow["ITEM_CODE"].ToString() != "")
            {
                model1.ITEM_CODE = dRow["ITEM_CODE"].ToString();
            }

            if (dRow["ITEM_SPEC"] != null && dRow["ITEM_SPEC"].ToString() != "")
            {
                model1.ITEM_SPEC = dRow["ITEM_SPEC"].ToString();
            }

            if (dRow["UNITS"] != null && dRow["UNITS"].ToString() != "")
            {
                model1.UNITS = dRow["UNITS"].ToString();
            }

            if (dRow["PERFORMED_BY"] != null && dRow["PERFORMED_BY"].ToString() != "")
            {
                model1.PERFORMED_BY = dRow["PERFORMED_BY"].ToString();
            }

            if (dRow["AMOUNT"] != null && dRow["AMOUNT"].ToString() != "")
            {
                model1.AMOUNT = decimal.Parse(dRow["AMOUNT"].ToString());
            }

            if (dRow["DEPT_CODE"] != null && dRow["DEPT_CODE"].ToString() != "")
            {
                model1.DEPT_CODE = dRow["DEPT_CODE"].ToString();
            }

            return model1;
        }
        #endregion

    }
}

