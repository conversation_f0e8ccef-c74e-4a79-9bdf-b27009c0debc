﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;

namespace Model
{
    /// <summary>
    /// MR_WORK_PATH:实体类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [DataContract]
    public class MR_WORK_PATH
    {
        #region Model
        private string _mr_path;
        private string _templet_path;
        private string _file_user;
        private string _file_pwd;
        private string _ip_addr;
        /// <summary>
        /// 病历文件路径
        /// </summary>
        [DataMember]
        public string MR_PATH
        {
            set { _mr_path = value; }
            get { return _mr_path; }
        }
        /// <summary>
        /// 病历模板路径
        /// </summary>
        [DataMember]
        public string TEMPLET_PATH
        {
            set { _templet_path = value; }
            get { return _templet_path; }
        }
        /// <summary>
        /// 访问文件服务用户
        /// </summary>
        [DataMember]
        public string FILE_USER
        {
            set { _file_user = value; }
            get { return _file_user; }
        }
        /// <summary>
        /// 访问文件服务用户口令
        /// </summary>
        [DataMember]
        public string FILE_PWD
        {
            set { _file_pwd = value; }
            get { return _file_pwd; }
        }
        /// <summary>
        /// 文件服务器IP地址
        /// </summary>
        [DataMember]
        public string IP_ADDR
        {
            set { _ip_addr = value; }
            get { return _ip_addr; }
        }
        #endregion Model

    }
}
