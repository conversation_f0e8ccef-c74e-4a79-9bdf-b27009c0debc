<?xml version="1.0"?>
<doc>
    <assembly>
        <name>OracleDAL</name>
    </assembly>
    <members>
        <member name="T:OracleDAL.BILL_PATTERN_DETAIL_Dao_Base">
            <summary>
            费用模板明细记录 数据库操作类
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_DETAIL_Dao_Base.Add(Model.BILL_PATTERN_DETAIL,Utility.OracleODP.OracleBaseClass)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_DETAIL_Dao_Base.Update(Model.BILL_PATTERN_DETAIL,Utility.OracleODP.OracleBaseClass)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_DETAIL_Dao_Base.Delete(System.String,System.Decimal,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_DETAIL_Dao_Base.GetModel(System.String,System.Decimal,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_DETAIL_Dao_Base.GetList(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_DETAIL_Dao_Base.GetList(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得几行数据
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_DETAIL_Dao_Base.GetObservableCollection(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_DETAIL_Dao_Base.GetObservableCollection(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表 
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_DETAIL_Dao_Base.CopyToModel(System.Data.DataRow)">
            <summary>
            
            </summary>
        </member>
        <member name="T:OracleDAL.BILL_PATTERN_MASTER_Dao_Base">
            <summary>
            费用模板主记录 数据库操作类
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_MASTER_Dao_Base.Add(Model.BILL_PATTERN_MASTER,Utility.OracleODP.OracleBaseClass)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_MASTER_Dao_Base.Update(Model.BILL_PATTERN_MASTER,Utility.OracleODP.OracleBaseClass)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_MASTER_Dao_Base.Delete(System.Decimal,System.String,System.String,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_MASTER_Dao_Base.GetModel(System.Decimal,System.String,System.String,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_MASTER_Dao_Base.GetList(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_MASTER_Dao_Base.GetList(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得几行数据
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_MASTER_Dao_Base.GetObservableCollection(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_MASTER_Dao_Base.GetObservableCollection(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表 
            </summary>
        </member>
        <member name="M:OracleDAL.BILL_PATTERN_MASTER_Dao_Base.CopyToModel(System.Data.DataRow)">
            <summary>
            
            </summary>
        </member>
        <member name="T:OracleDAL.BJCA_INTERFACE_Dao_Base">
            <summary>
            数字签名 数据库操作类
            </summary>
        </member>
        <member name="M:OracleDAL.BJCA_INTERFACE_Dao_Base.Add(Model.BJCA_INTERFACE,Utility.OracleODP.OracleBaseClass)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.BJCA_INTERFACE_Dao_Base.Update(Model.BJCA_INTERFACE,Utility.OracleODP.OracleBaseClass)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.BJCA_INTERFACE_Dao_Base.Delete(System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.BJCA_INTERFACE_Dao_Base.GetModel(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:OracleDAL.BJCA_INTERFACE_Dao_Base.GetList(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.BJCA_INTERFACE_Dao_Base.GetList(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得几行数据
            </summary>
        </member>
        <member name="M:OracleDAL.BJCA_INTERFACE_Dao_Base.GetObservableCollection(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.BJCA_INTERFACE_Dao_Base.GetObservableCollection(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表 
            </summary>
        </member>
        <member name="M:OracleDAL.BJCA_INTERFACE_Dao_Base.CopyToModel(System.Data.DataRow)">
            <summary>
            
            </summary>
        </member>
        <member name="M:OracleDAL.MAIN_MENU_Dao_Base.Add(Model.MAIN_MENU,Utility.OracleODP.OracleBaseClass)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.MAIN_MENU_Dao_Base.Update(Model.MAIN_MENU,Utility.OracleODP.OracleBaseClass)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.MAIN_MENU_Dao_Base.Delete(System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.MAIN_MENU_Dao_Base.GetModel(System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:OracleDAL.MAIN_MENU_Dao_Base.GetList(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.MAIN_MENU_Dao_Base.GetList(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得几行数据
            </summary>
        </member>
        <member name="M:OracleDAL.MAIN_MENU_Dao_Base.GetObservableCollection(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.MAIN_MENU_Dao_Base.GetObservableCollection(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表 
            </summary>
        </member>
        <member name="M:OracleDAL.MAIN_MENU_Dao_Base.CopyToModel(System.Data.DataRow)">
            <summary>
            
            </summary>
        </member>
        <member name="M:OracleDAL.MODELS_Dao_Base.Add(Model.MODELS,Utility.OracleODP.OracleBaseClass)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.MODELS_Dao_Base.Update(Model.MODELS,Utility.OracleODP.OracleBaseClass)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.MODELS_Dao_Base.Delete(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.MODELS_Dao_Base.GetModel(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:OracleDAL.MODELS_Dao_Base.GetList(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.MODELS_Dao_Base.GetList(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得几行数据
            </summary>
        </member>
        <member name="M:OracleDAL.MODELS_Dao_Base.GetObservableCollection(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.MODELS_Dao_Base.GetObservableCollection(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表 
            </summary>
        </member>
        <member name="M:OracleDAL.MODELS_Dao_Base.CopyToModel(System.Data.DataRow)">
            <summary>
            
            </summary>
        </member>
        <member name="M:OracleDAL.MODEL_GROUP_Dao_Base.Add(Model.MODEL_GROUP,Utility.OracleODP.OracleBaseClass)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.MODEL_GROUP_Dao_Base.Update(Model.MODEL_GROUP,Utility.OracleODP.OracleBaseClass)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.MODEL_GROUP_Dao_Base.Delete(System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.MODEL_GROUP_Dao_Base.GetModel(System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:OracleDAL.MODEL_GROUP_Dao_Base.GetList(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.MODEL_GROUP_Dao_Base.GetList(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得几行数据
            </summary>
        </member>
        <member name="M:OracleDAL.MODEL_GROUP_Dao_Base.GetObservableCollection(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.MODEL_GROUP_Dao_Base.GetObservableCollection(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表 
            </summary>
        </member>
        <member name="M:OracleDAL.MODEL_GROUP_Dao_Base.CopyToModel(System.Data.DataRow)">
            <summary>
            
            </summary>
        </member>
        <member name="T:OracleDAL.SEC_MENUS_DICT_Dao_Base">
            <summary>
            应用程序菜单字典 数据库操作类
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_MENUS_DICT_Dao_Base.Add(Model.SEC_MENUS_DICT,Utility.OracleODP.OracleBaseClass)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_MENUS_DICT_Dao_Base.Update(Model.SEC_MENUS_DICT,Utility.OracleODP.OracleBaseClass)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_MENUS_DICT_Dao_Base.Delete(System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_MENUS_DICT_Dao_Base.GetModel(System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_MENUS_DICT_Dao_Base.GetList(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_MENUS_DICT_Dao_Base.GetList(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得几行数据
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_MENUS_DICT_Dao_Base.GetObservableCollection(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_MENUS_DICT_Dao_Base.GetObservableCollection(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表 
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_MENUS_DICT_Dao_Base.CopyToModel(System.Data.DataRow)">
            <summary>
            
            </summary>
        </member>
        <member name="T:OracleDAL.SEC_RIGHT_GROUP_Dao_Base">
            <summary>
            应用程序菜单字典 数据库操作类
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_Dao_Base.Add(Model.SEC_RIGHT_GROUP,Utility.OracleODP.OracleBaseClass)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_Dao_Base.Update(Model.SEC_RIGHT_GROUP,Utility.OracleODP.OracleBaseClass)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_Dao_Base.Delete(System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_Dao_Base.GetModel(System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_Dao_Base.GetList(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_Dao_Base.GetList(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得几行数据
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_Dao_Base.GetObservableCollection(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_Dao_Base.GetObservableCollection(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表 
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_Dao_Base.CopyToModel(System.Data.DataRow)">
            <summary>
            
            </summary>
        </member>
        <member name="T:OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao_Base">
            <summary>
            应用程序菜单字典 数据库操作类
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao_Base.Add(Model.SEC_RIGHT_GROUP_VS_MENUS,Utility.OracleODP.OracleBaseClass)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao_Base.Update(Model.SEC_RIGHT_GROUP_VS_MENUS,Utility.OracleODP.OracleBaseClass)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao_Base.Delete(System.String,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao_Base.GetModel(System.String,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao_Base.GetList(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao_Base.GetList(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得几行数据
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao_Base.GetObservableCollection(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao_Base.GetObservableCollection(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表 
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao_Base.CopyToModel(System.Data.DataRow)">
            <summary>
            
            </summary>
        </member>
        <member name="T:OracleDAL.SEC_RIGHT_GROUP_VS_USERS_Dao_Base">
            <summary>
            应用程序菜单字典 数据库操作类
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_USERS_Dao_Base.Add(Model.SEC_RIGHT_GROUP_VS_USERS,Utility.OracleODP.OracleBaseClass)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_USERS_Dao_Base.Update(Model.SEC_RIGHT_GROUP_VS_USERS,Utility.OracleODP.OracleBaseClass)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_USERS_Dao_Base.Delete(System.String,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_USERS_Dao_Base.GetModel(System.String,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_USERS_Dao_Base.GetList(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_USERS_Dao_Base.GetList(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得几行数据
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_USERS_Dao_Base.GetObservableCollection(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_USERS_Dao_Base.GetObservableCollection(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表 
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_USERS_Dao_Base.CopyToModel(System.Data.DataRow)">
            <summary>
            
            </summary>
        </member>
        <member name="T:OracleDAL.STAFF_DICT_Dao_Base">
            <summary>
            工作人员字典 数据库操作类
            </summary>
        </member>
        <member name="M:OracleDAL.STAFF_DICT_Dao_Base.Add(Model.STAFF_DICT,Utility.OracleODP.OracleBaseClass)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.STAFF_DICT_Dao_Base.Update(Model.STAFF_DICT,Utility.OracleODP.OracleBaseClass)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.STAFF_DICT_Dao_Base.Delete(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.STAFF_DICT_Dao_Base.GetModel(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:OracleDAL.STAFF_DICT_Dao_Base.GetList(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.STAFF_DICT_Dao_Base.GetList(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得几行数据
            </summary>
        </member>
        <member name="M:OracleDAL.STAFF_DICT_Dao_Base.GetObservableCollection(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.STAFF_DICT_Dao_Base.GetObservableCollection(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表 
            </summary>
        </member>
        <member name="M:OracleDAL.STAFF_DICT_Dao_Base.CopyToModel(System.Data.DataRow)">
            <summary>
            
            </summary>
        </member>
        <member name="T:OracleDAL.LNCA_INTERFACE_Dao_Base">
            <summary>
            辽宁CA接口 数据库操作类
            </summary>
        </member>
        <member name="M:OracleDAL.LNCA_INTERFACE_Dao_Base.Add(Model.LNCA_INTERFACE,Utility.OracleODP.OracleBaseClass)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.LNCA_INTERFACE_Dao_Base.Update(Model.LNCA_INTERFACE,Utility.OracleODP.OracleBaseClass)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.LNCA_INTERFACE_Dao_Base.UpdateByHisID(Model.LNCA_INTERFACE,Utility.OracleODP.OracleBaseClass)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.LNCA_INTERFACE_Dao_Base.Delete(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.LNCA_INTERFACE_Dao_Base.GetModel(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:OracleDAL.LNCA_INTERFACE_Dao_Base.GetList(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.LNCA_INTERFACE_Dao_Base.GetList(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得几行数据
            </summary>
        </member>
        <member name="M:OracleDAL.LNCA_INTERFACE_Dao_Base.GetObservableCollection(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.LNCA_INTERFACE_Dao_Base.GetObservableCollection(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表 
            </summary>
        </member>
        <member name="M:OracleDAL.LNCA_INTERFACE_Dao_Base.CopyToModel(System.Data.DataRow)">
            <summary>
            
            </summary>
        </member>
        <member name="T:OracleDAL.BED_REC_Dao_Base">
            <summary>
            床位记录 数据库操作类
            </summary>
        </member>
        <member name="M:OracleDAL.BED_REC_Dao_Base.Add(Model.BED_REC,Utility.OracleODP.OracleBaseClass)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.BED_REC_Dao_Base.Update(Model.BED_REC,Utility.OracleODP.OracleBaseClass)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.BED_REC_Dao_Base.Delete(System.String,System.Decimal,Utility.OracleODP.OracleBaseClass)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="M:OracleDAL.BED_REC_Dao_Base.GetModel(System.String,System.Decimal,Utility.OracleODP.OracleBaseClass)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:OracleDAL.BED_REC_Dao_Base.GetList(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.BED_REC_Dao_Base.GetList(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得几行数据
            </summary>
        </member>
        <member name="M:OracleDAL.BED_REC_Dao_Base.GetObservableCollection(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:OracleDAL.BED_REC_Dao_Base.GetObservableCollection(System.Int32,System.Int32,System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            获得ObservableCollection数据列表 
            </summary>
        </member>
        <member name="M:OracleDAL.BED_REC_Dao_Base.CopyToModel(System.Data.DataRow)">
            <summary>
            
            </summary>
        </member>
        <member name="T:OracleDAL.BILL_PATTERN_MASTER_Dao">
            <summary>
            费用模板主记录 数据库操作类
            </summary>
        </member>
        <member name="M:OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao.Delete(System.String,System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            删除一条数据
            </summary>
        </member>
        <member name="T:OracleDAL.STAFF_DICT_Dao">
            <summary>
            工作人员字典操作类
            </summary>
        </member>
        <member name="M:OracleDAL.STAFF_DICT_Dao.Exists(System.String,System.String)">
            <summary>
            登录用户验证
            </summary>
            <param name="USER_NAME">用户名</param>
            <param name="PASSWORD">密码</param>
            <returns>返回值：-1-用户名错误，0-密码错误 1-正常登录，2-不可用账号</returns>
        </member>
        <member name="M:OracleDAL.STAFF_DICT_Dao.GetModelByUserName(System.String)">
            <summary>
            得到一个用户对象实体
            </summary>
        </member>
        <member name="M:OracleDAL.STAFF_DICT_Dao.GetModelByUserName(System.String,System.Boolean)">
            <summary>
            得到一个对象实体,增加医院编号
            </summary>
        </member>
        <member name="M:OracleDAL.STAFF_DICT_Dao.Finalize">
            <summary>
            TODO: 仅当以上 Dispose(bool disposing) 拥有用于释放未托管资源的代码时才替代终结器。
            </summary>
        </member>
        <member name="M:OracleDAL.STAFF_DICT_Dao.Dispose">
            <summary>
            添加此代码以正确实现可处置模式。
            </summary>
        </member>
        <member name="M:OracleDAL.BedExchange_Dao.ExchangeBed(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            换床位，如果床位都有人，则交换，否则换到空床
            </summary>
            <param name="bedNoFrom">需要换床的床号</param>
            <param name="bedStatusFrom">需要换床的床状态</param>
            <param name="bedLabelFrom">需要换床的床标号</param>
            <param name="patient_IDFrom">需要换床的患者</param>
            <param name="bedNoTo">目标床号</param>
            <param name="bedStatusTo">目标床状态</param>
            <param name="bedLabelTo">目标床标号</param>
            <param name="patient_IDTo">目标床患者</param>
            <param name="wardCode">所在护理单元</param>
            <param name="db">数据源</param>
            <returns>
            -9 打开数据库连接失败
            -1 数据库异常
            0- 返回成功
            1-换床者床是空床更新换床者床位状态失败
            2-换床者床是空床更新被换床者床位状态失败
            3-被换床者床是空床更新换床者床位状态失败
            4-被换床者床是空床更新被换床者床位状态失败
            5-换床者床已有患者更新患者换到被换床者床位失败
            6-被换床者床已有患者更新患者换到换床者床位失败
            7-说明换床者是母亲，有婴儿，更新婴儿床标号到被换床者失败
            8-说明被换床者是母亲，有婴儿，更新婴儿床标号到换床者失败
            </returns>
        </member>
        <member name="T:OracleDAL.BedSideCardDao">
            <summary>
            床头卡类
            </summary>
        </member>
        <member name="M:OracleDAL.BedSideCardDao.GetBedSideCardBySql(System.String,System.String)">
            <summary>
            根据SQL语句获得一个DataSet
            </summary>
            <param name="sql"></param>
            <returns></returns>
        </member>
        <member name="M:OracleDAL.BedSideCardDao.GetBedSideRightMenus_EMR(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            融合电子病历后的右键菜单 梁吉  2016-07-22 
            </summary>
            <param name="applicationcode"></param>
            <param name="username"></param>
            <param name="menugroup"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:OracleDAL.BedSideCardDao.CopyToModel(System.Data.DataRow)">
            <summary>
            
            </summary>
        </member>
        <member name="M:OracleDAL.RoleMantain_Dao.Delete(System.String,System.String)">
            <summary>
            删除主表及明细
            </summary>
            <param name="APPLICATION_CODE"></param>
            <param name="RIGHT_GROUP_CODE"></param>
            <returns></returns>
        </member>
        <member name="M:OracleDAL.ServerPublic_Dao.GetSysDate">
            <summary>
            取得服务器的时间
            </summary>
        </member>
        <member name="M:OracleDAL.ServerPublic_Dao.GetSingleValue(System.String)">
            <summary>
            获得返回值
            </summary>
        </member>
        <member name="M:OracleDAL.ServerPublic_Dao.GetTableData(System.String,System.String)">
            <summary>
            获取表数据
            </summary>
        </member>
        <member name="M:OracleDAL.ServerPublic_Dao.GetDataBySql(System.String)">
            <summary>
            根据SQL语句获得一个DataSet
            </summary>
            <param name="sql"></param>
            <returns></returns>
        </member>
        <member name="M:OracleDAL.ServerPublic_Dao.GetDataBySql_Tran(System.String)">
            <summary>
            根据SQL语句获得一个DataSet
            </summary>
            <param name="sql"></param>
            <returns></returns>
        </member>
        <member name="M:OracleDAL.ServerPublic_Dao.GetProcedureDataSet(System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.String,System.String)">
            <summary>
            获取存储过程数据集
            </summary>
            <param name="procedureName"></param>
            <param name="paramKey"></param>
            <param name="paramValue"></param>
            <param name="tableName"></param>
            <param name="sql"></param>
            <returns></returns>
        </member>
        <member name="M:OracleDAL.ServerPublic_Dao.GetPubicProcedureDs(System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.String@,System.String@,System.Data.DataTable@)">
            <summary>
            获取存储过程里的挂号收费用
            </summary>
            <param name="procedureName"></param>
            <param name="paramKey"></param>
            <param name="paramValue"></param>
            <param name="para1">resultcode</param>
            <param name="para2">errmsg</param>
            <param name="para3"></param>
            <returns></returns>
            
        </member>
        <member name="M:OracleDAL.ServerPublic_Dao.SaveDataSet(System.Data.DataSet)">
            <summary>
            直接保存Dataset
            </summary>
            <param name="dataset"></param>
            <returns></returns>
        </member>
        <member name="M:OracleDAL.ServerPublic_Dao.SaveDataSetNur(System.Data.DataSet)">
            <summary>
            护理专用保存更新DATASET
            </summary>
            <param name="dataset"></param>
            <returns></returns>
        </member>
        <member name="M:OracleDAL.ServerPublic_Dao.SaveTablesData(System.Collections.ArrayList,Utility.OracleODP.OracleBaseClass,System.String@)">
            <summary>
            更新多个DataSet
            </summary>
        </member>
        <member name="M:OracleDAL.ServerPublic_Dao.GetListUseDB(System.String,Utility.OracleODP.OracleBaseClass)">
            <summary>
            根据SQL语句获得一个DataSet待数据连接
            </summary>
            <param name="sql"></param>
            <returns></returns>
        </member>
        <member name="M:OracleDAL.ServerPublic_Dao.GetSysDateTime">
            <summary>
            获取系统时间
            </summary>
            <returns></returns>
        </member>
        <member name="M:OracleDAL.ServerPublic_Dao.GetDataTable_Para(System.String,System.Collections.Generic.List{System.String},System.Collections.ArrayList)">
            <summary>
            参数查询
            </summary>
            <param name="sql"></param>
            <param name="ParaList"></param>
            <param name="ValueList"></param>
            <returns></returns>
        </member>
        <member name="M:OracleDAL.ServerPublic_Dao.GetDataTable_Para(System.String,System.Collections.Generic.List{System.String},System.Collections.ArrayList,Utility.OracleODP.OracleBaseClass)">
            <summary>
            参数查询
            </summary>
            <param name="sql"></param>
            <param name="ParaList"></param>
            <param name="ValueList"></param>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:OracleDAL.ServerPublic_Dao.GetDataTable_Para_tran(System.String,System.Collections.Generic.List{System.String},System.Collections.ArrayList)">
            <summary>
            参数查询
            </summary>
            <param name="sql"></param>
            <param name="ParaList"></param>
            <param name="ValueList"></param>
            <returns></returns>
        </member>
    </members>
</doc>
