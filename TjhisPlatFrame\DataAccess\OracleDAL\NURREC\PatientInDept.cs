﻿//**********************************************
//说明:入科操作
//计算机名称：LINDP
//创建日期：2016/5/18 10:21:45
//作者：林大鹏
//版本号：V1.00
//**********************************************
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace OracleDAL
{
    public class PatientInDept
    {
        //保存入科数据
        public string SaveTable(Dictionary<string, string> list)
        {

            
            Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass();
            if (db.OpenDB())
            {
                db.BeginTransaction();
                foreach (var item in list)
                {
                    string rev = db.ExecuteTransactionNOLimit(item.Key);
                    if (!string.IsNullOrEmpty(rev))
                    {
                        db.RollbackTransaction();
                        db.CloseDB();
                        return item.Value + rev;
                    }

                }
                db.CommitTransaction();
                db.CloseDB();

                return null;
            }

            return null;
        }
    }
}
