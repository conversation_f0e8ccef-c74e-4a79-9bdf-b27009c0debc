﻿using System;
using System.Text;
using System.Collections.Generic;
using System.Data;
using Model;
using Utility.OracleODP;

namespace NM_Service.NMService
{

    /// <summary>
    /// 应用程序菜单字典实现类
    /// </summary> 	

    public class SEC_RIGHT_GROUP_VS_MENUSClient : INMService.ISEC_RIGHT_GROUP_VS_MENUS, IDisposable
    {
        #region  Method
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists_SEC_RIGHT_GROUP_VS_MENUS(string APPLICATION_CODE, string RIGHT_GROUP_CODE, string MENU_NAME)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao().Exists(APPLICATION_CODE, RIGHT_GROUP_CODE, MENU_NAME, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }

        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add_SEC_RIGHT_GROUP_VS_MENUS(Model.SEC_RIGHT_GROUP_VS_MENUS model)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao().Add(model, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update_SEC_RIGHT_GROUP_VS_MENUS(Model.SEC_RIGHT_GROUP_VS_MENUS model)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao().Update(model, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete_SEC_RIGHT_GROUP_VS_MENUS(string APPLICATION_CODE, string RIGHT_GROUP_CODE, string MENU_NAME)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao().Delete(APPLICATION_CODE, RIGHT_GROUP_CODE, MENU_NAME, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.SEC_RIGHT_GROUP_VS_MENUS GetModel_SEC_RIGHT_GROUP_VS_MENUS(string APPLICATION_CODE, string RIGHT_GROUP_CODE, string MENU_NAME)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    Model.SEC_RIGHT_GROUP_VS_MENUS ret = new OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao().GetModel(APPLICATION_CODE, RIGHT_GROUP_CODE, MENU_NAME, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList_All_SEC_RIGHT_GROUP_VS_MENUS(string strWhere)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    DataSet ret = new OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao().GetList(strWhere, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>   
        public DataSet GetList_SEC_RIGHT_GROUP_VS_MENUS(int startIndex, int endIndex, string strWhere, string filedOrder)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    DataSet ret = new OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao().GetList(startIndex, endIndex, strWhere, filedOrder, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>  
        public System.Collections.ObjectModel.ObservableCollection<Model.SEC_RIGHT_GROUP_VS_MENUS> GetObservableCollection_All_SEC_RIGHT_GROUP_VS_MENUS(string strWhere)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    System.Collections.ObjectModel.ObservableCollection<Model.SEC_RIGHT_GROUP_VS_MENUS> ret = new OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao().GetObservableCollection(strWhere, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 获得ObservableCollection根据分页获得数据列表
        /// </summary>  
        public System.Collections.ObjectModel.ObservableCollection<Model.SEC_RIGHT_GROUP_VS_MENUS> GetObservableCollection_SEC_RIGHT_GROUP_VS_MENUS(int startIndex, int endIndex, string strWhere, string filedOrder)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    System.Collections.ObjectModel.ObservableCollection<Model.SEC_RIGHT_GROUP_VS_MENUS> ret = new OracleDAL.SEC_RIGHT_GROUP_VS_MENUS_Dao().GetObservableCollection(startIndex, endIndex, strWhere, filedOrder, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }
        #endregion

        #region IDisposable 成员

        /// <summary>
　　　　/// 实现IDisposable接口
　　　　/// </summary>
　　　　public void Dispose()
　　　　{
　　　　　　Dispose(true);
　　　　　　//.NET Framework 类库
　　　　　　// GC..::.SuppressFinalize 方法
　　　　　　//请求系统不要调用指定对象的终结器。
　　　　　　GC.SuppressFinalize(this);
　　　　}
　　　　/// <summary>
　　　　/// 虚方法，可供子类重写
　　　　/// </summary>
　　　　/// <param name="disposing"></param>
　　　　protected virtual void Dispose(bool disposing)
　　　　{
　　　　　　if (disposing)
　　　　　　　　{
　　　　　　　　　　// Release managed resources
　　　　　　　　}
　　　　}
　　　　/// <summary>
　　　　/// 析构函数
　　　　/// 当客户端没有显示调用Dispose()时由GC完成资源回收功能
　　　　/// </summary>
    ~SEC_RIGHT_GROUP_VS_MENUSClient()
　　　　{
　　　　　　Dispose();
　　　　}

        #endregion
    }
}