﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;

namespace Model
{
    /// <summary>
    /// 实体类KEY_DICT 。(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public class KEY_DICT
    {
        public KEY_DICT()
        { }
        #region Model
        private string _product_code;
        private string _product_name;
        private string _key;
        /// <summary>
        /// 
        /// </summary>
        public string PRODUCT_CODE
        {
            set { _product_code = value; }
            get { return _product_code; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string PRODUCT_NAME
        {
            set { _product_name = value; }
            get { return _product_name; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string KEY
        {
            set { _key = value; }
            get { return _key; }
        }
        #endregion Model

    }
}
