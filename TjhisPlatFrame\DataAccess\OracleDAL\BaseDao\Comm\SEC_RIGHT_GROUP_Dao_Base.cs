﻿using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using Utility;
using Utility.OracleODP;
using Oracle.ManagedDataAccess.Client;

namespace OracleDAL
{

    /// <summary>
    /// 应用程序菜单字典 数据库操作类
    /// </summary>

    public class SEC_RIGHT_GROUP_Dao_Base
    {
        #region   Method
        public bool Exists(string APPLICATION_CODE, string RIGHT_GROUP_CODE, OracleBaseClass db)
        {
            #region  init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from SEC_RIGHT_GROUP");
            strSql.Append(" where ");
            strSql.Append(" APPLICATION_CODE = :APPLICATION_CODE and  ");
            strSql.Append(" RIGHT_GROUP_CODE = :RIGHT_GROUP_CODE ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":APPLICATION_CODE", OracleDbType.Varchar2, 16);
            p.Value = APPLICATION_CODE;
            parameters.Add(p);

            p = new OracleParameter(":RIGHT_GROUP_CODE", OracleDbType.Varchar2, 4);
            p.Value = RIGHT_GROUP_CODE;
            parameters.Add(p);

            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    int cmdresult;
                    cmdresult = int.Parse(ds.Tables[0].Rows[0][0].ToString());
                    if (cmdresult <= 0)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                    return false;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.SEC_RIGHT_GROUP model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into SEC_RIGHT_GROUP(");
            strSql.Append("APPLICATION_CODE,RIGHT_GROUP_CODE,RIGHT_GROUP_TEXT,RIGHT_GROUP_MEMOS");
            strSql.Append(") values (");
            strSql.Append(":APPLICATION_CODE,:RIGHT_GROUP_CODE,:RIGHT_GROUP_TEXT,:RIGHT_GROUP_MEMOS");
            strSql.Append(") ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":APPLICATION_CODE", OracleDbType.Varchar2, 16);
            p.Value = model.APPLICATION_CODE;
            parameters.Add(p);

            p = new OracleParameter(":RIGHT_GROUP_CODE", OracleDbType.Varchar2, 4);
            p.Value = model.RIGHT_GROUP_CODE;
            parameters.Add(p);

            p = new OracleParameter(":RIGHT_GROUP_TEXT", OracleDbType.Varchar2, 100);
            p.Value = model.RIGHT_GROUP_TEXT;
            parameters.Add(p);

            p = new OracleParameter(":RIGHT_GROUP_MEMOS", OracleDbType.Varchar2, 100);
            p.Value = model.RIGHT_GROUP_MEMOS;
            parameters.Add(p);
            #endregion
            try
            {

                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.SEC_RIGHT_GROUP model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update SEC_RIGHT_GROUP set ");

            strSql.Append(" APPLICATION_CODE = :APPLICATION_CODE , ");
            strSql.Append(" RIGHT_GROUP_CODE = :RIGHT_GROUP_CODE , ");
            strSql.Append(" RIGHT_GROUP_TEXT = :RIGHT_GROUP_TEXT , ");
            strSql.Append(" RIGHT_GROUP_MEMOS = :RIGHT_GROUP_MEMOS  ");
            strSql.Append(" where APPLICATION_CODE=:APPLICATION_CODE and RIGHT_GROUP_CODE=:RIGHT_GROUP_CODE  ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":APPLICATION_CODE", OracleDbType.Varchar2, 16);
            p.Value = model.APPLICATION_CODE;
            parameters.Add(p);

            p = new OracleParameter(":RIGHT_GROUP_CODE", OracleDbType.Varchar2, 4);
            p.Value = model.RIGHT_GROUP_CODE;
            parameters.Add(p);

            p = new OracleParameter(":RIGHT_GROUP_TEXT", OracleDbType.Varchar2, 100);
            p.Value = model.RIGHT_GROUP_TEXT;
            parameters.Add(p);

            p = new OracleParameter(":RIGHT_GROUP_MEMOS", OracleDbType.Varchar2, 100);
            p.Value = model.RIGHT_GROUP_MEMOS;
            parameters.Add(p);
            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string APPLICATION_CODE, string RIGHT_GROUP_CODE, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from SEC_RIGHT_GROUP ");
            strSql.Append(" where APPLICATION_CODE=:APPLICATION_CODE and RIGHT_GROUP_CODE=:RIGHT_GROUP_CODE ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":APPLICATION_CODE", OracleDbType.Varchar2, 16);
            p.Value = APPLICATION_CODE;
            parameters.Add(p);

            p = new OracleParameter(":RIGHT_GROUP_CODE", OracleDbType.Varchar2, 4);
            p.Value = RIGHT_GROUP_CODE;
            parameters.Add(p);

            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }



        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.SEC_RIGHT_GROUP GetModel(string APPLICATION_CODE, string RIGHT_GROUP_CODE, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select APPLICATION_CODE, RIGHT_GROUP_CODE, RIGHT_GROUP_TEXT, RIGHT_GROUP_MEMOS  ");
            strSql.Append("  from SEC_RIGHT_GROUP ");
            strSql.Append(" where APPLICATION_CODE=:APPLICATION_CODE and RIGHT_GROUP_CODE=:RIGHT_GROUP_CODE ");
            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":APPLICATION_CODE", OracleDbType.Varchar2, 16);
            p.Value = APPLICATION_CODE;
            parameters.Add(p);

            p = new OracleParameter(":RIGHT_GROUP_CODE", OracleDbType.Varchar2, 4);
            p.Value = RIGHT_GROUP_CODE;
            parameters.Add(p);
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;
                    Model.SEC_RIGHT_GROUP model = new Model.SEC_RIGHT_GROUP();

                    if (cmdresult > 0)
                    {
                        model = CopyToModel(ds.Tables[0].Rows[0]);
                        return model;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM SEC_RIGHT_GROUP ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得几行数据
        /// </summary>
        public DataSet GetList(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            #region 初始化参数
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM SEC_RIGHT_GROUP T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.SEC_RIGHT_GROUP> GetObservableCollection(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM SEC_RIGHT_GROUP ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.SEC_RIGHT_GROUP> list = new System.Collections.ObjectModel.ObservableCollection<Model.SEC_RIGHT_GROUP>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.SEC_RIGHT_GROUP model = new Model.SEC_RIGHT_GROUP();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表 
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.SEC_RIGHT_GROUP> GetObservableCollection(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM SEC_RIGHT_GROUP T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }

            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.SEC_RIGHT_GROUP> list = new System.Collections.ObjectModel.ObservableCollection<Model.SEC_RIGHT_GROUP>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.SEC_RIGHT_GROUP model = new Model.SEC_RIGHT_GROUP();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion   Method
        #region
        /// <summary>
        /// 
        /// </summary>
        protected Model.SEC_RIGHT_GROUP CopyToModel(DataRow dRow)
        {
            Model.SEC_RIGHT_GROUP model1 = new Model.SEC_RIGHT_GROUP();

            if (dRow["APPLICATION_CODE"] != null && dRow["APPLICATION_CODE"].ToString() != "")
            {
                model1.APPLICATION_CODE = dRow["APPLICATION_CODE"].ToString();
            }

            if (dRow["RIGHT_GROUP_CODE"] != null && dRow["RIGHT_GROUP_CODE"].ToString() != "")
            {
                model1.RIGHT_GROUP_CODE = dRow["RIGHT_GROUP_CODE"].ToString();
            }

            if (dRow["RIGHT_GROUP_TEXT"] != null && dRow["RIGHT_GROUP_TEXT"].ToString() != "")
            {
                model1.RIGHT_GROUP_TEXT = dRow["RIGHT_GROUP_TEXT"].ToString();
            }

            if (dRow["RIGHT_GROUP_MEMOS"] != null && dRow["RIGHT_GROUP_MEMOS"].ToString() != "")
            {
                model1.RIGHT_GROUP_MEMOS = dRow["RIGHT_GROUP_MEMOS"].ToString();
            }

            return model1;
        }
        #endregion

    }
}

