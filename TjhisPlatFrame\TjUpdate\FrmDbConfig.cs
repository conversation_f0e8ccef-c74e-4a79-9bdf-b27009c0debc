﻿
using System;
using System.Data;
using System.Text;
using System.Windows.Forms;
using Utility;

namespace Tj_Update
{
    /// <summary>
    /// 数据库参数设置窗口
    /// </summary>
    public partial class FrmDbConfig : DevExpress.XtraEditors.XtraForm
    {
        /// <summary>
        /// 初始化
        /// </summary>
        public FrmDbConfig()
        {
            InitializeComponent();
            //设置图标
            Icon = System.Drawing.Icon.ExtractAssociatedIcon(Application.ExecutablePath);
            //双缓冲，减少闪烁
            SetStyle(ControlStyles.UserPaint, true);
            SetStyle(ControlStyles.AllPaintingInWmPaint, true);
            SetStyle(ControlStyles.DoubleBuffer, true);
        }

        #region Models
        private class ConnectInfo
        {
            public string ServerName = "127.0.0.1";
            public int Port = 1521;
            public string DbServiceName = "his";
            public string DBAliasName = "tjhis";
            public string AppVersion = "true";
            public string UserID = "";
            public string Password = "";
            public string strHisUnitCode = "";
            public int DBConnectionMode = 0;

            /// <summary>
            /// 数据库字符集是否是英文字符集
            /// </summary>
            public bool DbCharsetUS7ASCII = false;

            /// <summary>
            /// 数据库提供者
            /// </summary>
            public string DBProviderName = "Oracle.ManagedDataAccess.Client";

            /// <summary>
            ///使用oledb时是否使用oracle驱动
            /// </summary>
            public bool UseOralceOledbDriver = false;
            
        }
        private ConnectInfo connInfo = new ConnectInfo();

        #endregion Models

        #region Functions
        /// <summary>
        /// 读数据到connInfo
        /// </summary>
        private void SaveToInfo()
        {
            connInfo.ServerName = txtServer.Text.Trim();
            connInfo.Port = Convert.ToInt32(txtPort.Text);
            if (connInfo.Port <= 0) connInfo.Port = 1521;
            connInfo.DbServiceName = txtDbName.Text.Trim();
            connInfo.DBProviderName = cbxProviderInvariantName.Text;
            connInfo.DbCharsetUS7ASCII = checkEditUS7ASCII.Checked;
            if (chkModi.Checked)
            {
                connInfo.UserID = txtUserId.Text.Trim();
                connInfo.Password = txtPassword.Text.Trim();
            }
        }

        /// <summary>
        /// connInfo数据显示到界面
        /// </summary>
        private void ShowInfo()
        {
            txtServer.Text = connInfo.ServerName;
            txtPort.Text = connInfo.Port.ToString();
            txtDbName.Text = connInfo.DbServiceName;
            cbxProviderInvariantName.SelectedIndex = connInfo.DBConnectionMode;
            checkEditUS7ASCII.Checked = connInfo.DbCharsetUS7ASCII;
            txtUserId.Text = connInfo.UserID;
            txtPassword.Text = connInfo.Password;
            

            SetUserIdPasswordEnable();
        }

        /// <summary>
        /// 是否允许编辑登录用户和密码
        /// </summary>
        private void SetUserIdPasswordEnable()
        {
            if (chkModi.Checked)
            {
                txtUserId.Enabled = true;
                txtPassword.Enabled = true;
            }
            else
            {
                txtUserId.Enabled = false;
                txtPassword.Enabled = false;
            }
        }

        /// <summary>
        /// 将配置写入IniFile
        /// </summary>
        /// <returns></returns>
        private bool SaveToIniFile()
        {
            string fileName = Environment.CurrentDirectory + @"\Config\connection.xml";
            DataSet ds = new DataSet();
            ds.ReadXml(fileName);
            DataTable table = ds.Tables["connection"];
            DataRow row = table.Rows[0];
            row["ip"] = this.connInfo.ServerName;
            row["server"] = this.connInfo.DbServiceName;
            row["user"] = this.connInfo.UserID;
            if (this.chkModi.Checked)
            {
                string pwd = Gloobal.EncryptHIS(this.connInfo.Password);
                byte[] pwdEnicode = Encoding.UTF8.GetBytes(pwd);
                row["password"] = Convert.ToBase64String(pwdEnicode);
            }
            row["port"] = this.connInfo.Port;
            row["DbCharsetUS7ASCII"] = this.connInfo.DbCharsetUS7ASCII ? "0" : "1";
            row["DBConnectionMode"] = this.cbxProviderInvariantName.SelectedIndex;
            ds.WriteXml(fileName);
            return true;
        }

        /// <summary>
        /// 从配置文件中读
        /// </summary>
        private void ReadFromXMLFile()
        {
            string fileName = Environment.CurrentDirectory + @"\Config\connection.xml";
            DataSet ds = new DataSet();
            ds.ReadXml(fileName);
            DataTable table = ds.Tables["connection"];
            DataRow row = table.Rows[0];
            this.connInfo.ServerName = row["ip"].ToString();
            this.connInfo.DbServiceName = row["server"].ToString();
            this.connInfo.UserID = row["user"].ToString();
            string password = row["password"].ToString();
            try
            {
                byte[] convertByte = Convert.FromBase64String(password);
                string conpwd = Encoding.UTF8.GetString(convertByte);
                this.connInfo.Password = Gloobal.DecryptHIS(conpwd);
            }
            catch
            {
                this.connInfo.Password = "HISUSER";
            }
            this.connInfo.Port = Convert.ToInt32(row["port"]);
            this.connInfo.DbCharsetUS7ASCII = row["DbCharsetUS7ASCII"].ToString().Equals("0");
            this.connInfo.DBConnectionMode = Convert.ToInt32(row["DBConnectionMode"]);
        }

        #endregion Functions

        #region Events
        //Oracle版本号按键事件
        private void textDBMS_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    txtServer.SelectAll();
                    txtServer.Focus();
                    break;
                case Keys.Escape:
                    btnCancel.PerformClick();
                    break;
                default:
                    break;
            }
        }
        

        //登录用户按键事件
        private void textUserId_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    if (string.IsNullOrEmpty(txtUserId.Text.Trim()))
                    {
                        txtUserId.Focus();
                        return;
                    }
                    else
                    {
                        txtPassword.SelectAll();
                        txtPassword.Focus();
                    }
                    break;
                case Keys.Escape:
                    btnCancel.PerformClick();
                    break;
                default:
                    break;
            }
        }

        //登录密码按键事件
        private void textPassword_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    if (string.IsNullOrEmpty(txtPassword.Text.Trim()))
                    {
                        txtPassword.Focus();
                        return;
                    }
                    else
                    {
                        btnOK.PerformClick();
                    }
                    break;
                case Keys.Escape:
                    btnCancel.PerformClick();
                    break;
                default:
                    break;
            }
        }

        //确定按钮事件
        private void btnOK_Click(object sender, EventArgs e)
        {
            SaveToInfo();

            //当是统一DBA登录时，进行登录检查
            /*===============edit begin @lupg 20200324 由于3.3之前的版本采用的是DB用户登录，
             用户名和密码是由操作员录入，这里不做验证=======*/
            //if (connInfo.AppVersion == "true")
            //{
            //    His01DbHelper dbhelper = new His01DbHelper(connInfo.DBProviderInvariantName,connInfo.ToString());
            //    try
            //    {
            //        if (!dbhelper.OpenConn())
            //        {
            //            Cs02MessageBox.ShowError("数据库连接不成功，请检查设置！");
            //            return;
            //        }
            //    }
            //    catch (Exception ex)
            //    {
            //        Cs02MessageBox.ShowError($"数据库连接不成功！\r\n错误信息: {ex.Message}");
            //        return;
            //    }
            //}
            /*===============edit end @lupg 20200324 由于3.3之前的版本采用的是DB用户登录，
           用户名和密码是由操作员录入，这里不做验证=======*/
            //保存INI
            SaveToIniFile();
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        //窗口载入时
        private void frmDbConfig_Load(object sender, EventArgs e)
        {

            //64位下Oracle.DataAccess及微软提供的OleDb驱动无法使用
            if (Environment.Is64BitProcess)
                cbxProviderInvariantName.Properties.Items.Remove("Oracle.DataAccess");
            
            ReadFromXMLFile();
            ShowInfo();
        }

        //登录模式选择事件
        private void cbAPP_SelectedIndexChanged(object sender, EventArgs e)
        {
            SetUserIdPasswordEnable();
        }

        //激活界面事件
        private void frmDbConfig_Activated(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.None;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.Close();
        }

        private void txtServer_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (string.IsNullOrEmpty(txtServer.Text.Trim()))
                {
                    txtServer.Focus();
                    return;
                }
                else
                {
                    txtPort.SelectAll();
                    txtPort.Focus();
                }
            }
        }

        private void txtPort_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (string.IsNullOrEmpty(txtPort.Text.Trim()))
                {
                    txtPort.Focus();
                    return;
                }
                else
                {
                    txtDbName.SelectAll();
                    txtDbName.Focus();
                }
            }
        }

        private void txtDbName_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (string.IsNullOrEmpty(txtDbName.Text.Trim()))
                {
                    txtDbName.Focus();
                    return;
                }
            }
        }

        private void chkModi_CheckedChanged(object sender, EventArgs e)
        {
            SetUserIdPasswordEnable();
        }

       
        #endregion  Events

    }
}