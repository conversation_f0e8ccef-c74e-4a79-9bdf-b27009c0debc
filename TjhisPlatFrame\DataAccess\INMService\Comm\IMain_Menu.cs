﻿using System;
using System.Data;
using System.ServiceModel;
namespace INMService
{
    /// <summary>
    /// 接口层MAIN_MENU
    /// </summary>
    [ServiceContract]
    public interface IMAIN_MENU
    {
        #region  成员方法
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        [OperationContract]
        bool Exists_MAIN_MENU(string NODE, string PARENTNODE);
        /// <summary>
        /// 增加一条数据
        /// </summary>
        [OperationContract]
        bool Add_MAIN_MENU(Model.MAIN_MENU model);
        /// <summary>
        /// 更新一条数据
        /// </summary>
        [OperationContract]
        bool Update_MAIN_MENU(Model.MAIN_MENU model);
        /// <summary>
        /// 删除数据
        /// </summary>
        [OperationContract]
        bool Delete_MAIN_MENU(string NODE, string PARENTNODE);
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        [OperationContract]
        Model.MAIN_MENU GetModel_MAIN_MENU(string NODE, string PARENTNODE);
        /// <summary>
        /// 获得数据列表
        /// </summary>
        [OperationContract]
        DataSet GetList_All_MAIN_MENU(string strWhere);
        /// <summary>
        /// 获得前几行数据
        /// </summary>
        [OperationContract]
        DataSet GetList_MAIN_MENU(int startIndex, int endIndex, string strWhere, string filedOrder);
        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.MAIN_MENU> GetObservableCollection_All_MAIN_MENU(string strWhere);
        /// <summary>
        /// 获得ObservableCollection根据分页获得数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.MAIN_MENU> GetObservableCollection_MAIN_MENU(int startIndex, int endIndex, string strWhere, string filedOrder);
        #endregion  成员方法
    }
}