﻿using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System;
using System.Collections;
using System.Data;
using System.Windows.Forms;
using Tjhis.Report.Custom.Common;
using Tjhis.Report.Custom.Srv;
using SQL = PlatCommon.Base02.Cs02StringHelper;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmSaveReportToLocal : ParentForm
    {
        srvStatisticalQuery srv = new srvStatisticalQuery();
        private string hospitalCode = SystemParm.HisUnitCode;
        private string appName = Const.customAppCode;
        DataSet dsTemplet;
        DataTable dtTemplet;
        DataTable dtParams;
        DataTable dtConfigs;
        DataRow tempRow;

        public frmSaveReportToLocal()
        {
            InitializeComponent();
        }

        private void frmStatisticalSetting_Load(object sender, EventArgs e)
        {
            //LookupEdit下拉框绑定
            LUE_EditType.ValueMember = "ITEM_CODE";
            LUE_EditType.DisplayMember = "ITEM_NAME";
            LUE_EditType.DataSource = srvStatisticalQuery.CreateDict(Const.CONTROL_TYPES);
            LUE_SourceType.ValueMember = "ITEM_CODE";
            LUE_SourceType.DisplayMember = "ITEM_NAME";
            LUE_SourceType.DataSource = srvStatisticalQuery.CreateDict("1-SQL脚本;2-自定义选项");

            BindingTemple();
        }

        void BindingTemple()
        {
            dsTemplet = srv.GetReport("news");
            InitBaseData();
        }
        void OpenLocalReport(string path)
        {
            dsTemplet = new DataSet();
            dsTemplet.ReadXml(path);

            InitBaseData();
        }
        void InitBaseData()
        {
            dtTemplet = dsTemplet.Tables["NURADM_REPORT_STATISTICS_DICT"];

            dtConfigs = dsTemplet.Tables["NURADM_REPORT_CONFIG"];

            dtParams = dsTemplet.Tables["NURADM_REPORT_VS_PARAM"];
            gcReportParam.DataSource = dtParams;

            if(dtTemplet.Rows.Count <= 0)
            {
                tempRow = dtTemplet.NewRow();
                dgvTemple_InitNewRow(tempRow);
            }
            tempRow = dtTemplet.Rows[0];

            OpenFile();
        }
        private void OpenFile()
        {
            DataRow dr = tempRow;
            if (dr == null) return;

            //txtSql.EditValue = GetSql(dr["dict_name"].ToString(), getParams());
            txtSql.EditValue = dr["templet_sql"];
            selectTempName = dr["dict_name"].ToString();
            txtReportName.EditValue = selectTempName;
            //txtReportName.EditValue = selectTempName;
        }



        #region 获取参数
        /// <summary>
        /// 获取参数
        /// </summary>
        /// <returns></returns>
        private Hashtable getParams()
        {
            DataRow drow = tempRow;
            if (drow == null) return null;

            // 向SQL语句传递参数
            Hashtable hasParam = new Hashtable();
            DataSet ds = srv.GetReportParamNew(appName, drow["dict_id"].ToString());
            if( dtParams!=null )
            {
                foreach(DataRow dr in dtParams.Rows)
                {
                    if(dr["EDIT_TYPE"].ToString().Equals("1") || dr["EDIT_TYPE"].ToString().Equals("2"))
                        hasParam.Add(dr["PARAM_NAME"].ToString(),"0");
                    else if (dr["EDIT_TYPE"].ToString().Equals("3"))
                        hasParam.Add(dr["PARAM_NAME"].ToString(), DateTime.Now);
                    else
                        hasParam.Add(dr["PARAM_NAME"].ToString(), 0);
                }
            }
            //hasParam.Add("WARD_NAME", "0");
            //hasParam.Add("WARD_CODE", "0");
            //hasParam.Add("DATE_BEGIN", DateTime.Now);
            //hasParam.Add("DATE_END", DateTime.Now);
            //hasParam.Add("USER_NAME", "0");
            //hasParam.Add("NURSE_ID", "0");
            //hasParam.Add("NURSE_NAME", "0");
            return hasParam;
        }
        #endregion

        private void btnTest_Click(object sender, EventArgs e)
        {
            try { 
                
                string sql = string.Empty; 
                if(txtSql.EditValue != null)
                {
                    sql = txtSql.EditValue.ToString();
                }
                if(sql.EndsWith(";"))
                {
                    sql = sql.Substring(0, sql.Length - 1);
                }
                DataSet ds = srv.TestSQL(sql,getParams());//XtraReportHelper.GetPrintData(dr["dict_name"].ToString(), getParams());
                if(ds != null)
                {
                    XtraMessageBox.Show("测试成功！","提示");
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message,"提示");
            }
        }

        private void sbtnRparamAdd_Click(object sender, EventArgs e)
        {
            DataRow drTemp = tempRow;
            if (drTemp == null) return;

            dgvReportParam.AddNewRow();
            DataRow dr = dgvReportParam.GetFocusedDataRow();
            DataRowView drv = dgvReportParam.GetFocusedRow() as DataRowView;
            frmParamEdit fe = new frmParamEdit(dr);
            DialogResult result = fe.ShowDialog();

            if (result == DialogResult.OK)
            {
                //SaveReportParam();
            }
            else
            {
                if(drv != null)
                {
                    drv.Delete();
                }
            }
        }
        private void dgvReportParam_InitNewRow(object sender, DevExpress.XtraGrid.Views.Grid.InitNewRowEventArgs e)
        {
            DataRow dr = dgvReportParam.GetDataRow(e.RowHandle);
            dr["HOSPITAL_CODE"] = hospitalCode;
            dr["APP_NAME"] = appName;
            dr["REPORT_ID"] = tempRow?["dict_id"]; //DateTime.Now.ToString("yyyyMMddHHmmssfff");
            dr["PARAM_NAME"] = "";
        }

        private void sbtnRparamDel_Click(object sender, EventArgs e)
        {
            dgvReportParam.DeleteSelectedRows();
        }
        private void sbtnRparamEdit_Click(object sender, EventArgs e)
        {
            DataRow dr = dgvReportParam.GetFocusedDataRow();
            if (dr == null) return;

            ShowEditForm(dr);
        }
        private void sbtnRparamSave_Click(object sender, EventArgs e)
        {
            //SaveReportParam();
            XtraMessageBox.Show("应用参数成功", "提示");
        }
        
        
        private void btnSaveSql_Click(object sender, EventArgs e)
        {
            

            XtraMessageBox.Show("保存成功！", "提示");
            
        }


        private void dgvTemple_InitNewRow(DataRow dr)
        {
            //DataRow dr = dgvTemple.GetDataRow(e.RowHandle);//GetFocusedDataRow();
            dr["HOSPITAL_CODE"] = hospitalCode;
            dr["APP_NAME"] = appName;
            dr["DICT_ID"] = DateTime.Now.ToString("yyyyMMddHHmmssfff");
            try
            {
                dr["CREATE_NURSE"] = SystemParm.LoginUser.USER_NAME;
            }
            catch
            {
                dr["CREATE_NURSE"] = "admin";
            }

            dr["CREATE_DATE"] = DateTime.Now;
            dr["ROLE_TYPE"] = "1";
            dr["DICT_NAME"] = "(未命名)";

            //取当前最大序号
            int maxSortNo = 99;
            dr["SERIAL_NO"] = maxSortNo; //序号

            dtTemplet.Rows.Add(dr);
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            

            if (dsTemplet.HasChanges())
            {
                srv.SaveData(dsTemplet);
                BindingTemple();
            }

            XtraMessageBox.Show("保存成功！","提示");
        }

        /// <summary>
        /// 获取SQL语句
        /// </summary>
        /// <returns></returns>
        private static string GetSqlText(string sql, Hashtable hasParams)
        {
            // 获取SQL原文
            string sqlText = sql;

            // 替换参数
            if (hasParams == null) return sqlText;

            string paramName = string.Empty;
            string paramValue = string.Empty;

            string paramName0 = string.Empty;
            string paramValue0 = string.Empty;

            foreach (DictionaryEntry entry in hasParams)
            {
                // 参数名
                paramName = "{" + entry.Key.ToString().ToUpper() + "}";         // 这种情况 变成 ''
                paramName0 = "[" + entry.Key.ToString().ToUpper() + "]";        // 这种情况 变成 原样

                // 参数值
                if (entry.Value == null)
                {
                    paramValue = "NULL";
                }
                else if (entry.Value.GetType().Equals(typeof(DateTime)))
                {
                    paramValue = SQL.GetOraDate((DateTime)(entry.Value));
                }
                else if (entry.Value.GetType().Equals(typeof(int)) || entry.Value.GetType().Equals(typeof(float)) || entry.Value.GetType().Equals(typeof(Decimal)))
                {
                    paramValue = entry.Value.ToString();
                }
                else if (entry.Value.ToString().Contains("'"))
                {
                    paramValue = entry.Value.ToString();
                }
                else
                {
                    paramValue = SQL.SqlConvert(entry.Value.ToString());
                }

                paramValue0 = entry.Value == null ? "" : entry.Value.ToString();

                // 替换参数
                if (sqlText.IndexOf(paramName) >= 0)
                {
                    sqlText = sqlText.Replace(paramName, paramValue);
                }
                else if (sqlText.IndexOf(paramName0) >= 0)
                {
                    sqlText = sqlText.Replace(paramName0, paramValue0);
                }
            }

            return sqlText;
        }

        private void txtSql_Enter(object sender, EventArgs e)
        {
            //OpenFile();
            //btnTest.Enabled = true;
            //btnSaveSql.Enabled = true;
        }
        
        private void sbtnReportParam_Click(object sender, EventArgs e)
        {
            frmParamDict fp = new frmParamDict();
            fp.ShowDialog();
        }

        string selectTempName;
        private void txtTemple_CellValueChanged(string value)
        {
            {
                DataRow dr = tempRow;

                dr["dict_name"] = txtReportName.EditValue.ToString();

                if (string.IsNullOrEmpty(dr["dict_name"].ToString()))
                {
                    dr["dict_name"] = selectTempName;
                    return;
                }
                //string repxPath = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\" + selectTempName +"_"+ dr["dict_id"] + ".repx");
                //string repxNewPath = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\"+ dr["dict_name"] + "_" + dr["dict_id"] + ".repx");
                //if (File.Exists(repxPath))
                //{
                //    File.Move(repxPath, repxNewPath);

                //}
                //else
                //{
                //    string repxModelPath = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\模板.repx");
                //    if (File.Exists(repxPath) == false)
                //    {
                //        File.Copy(repxModelPath, repxPath);
                //    }
                //}

                selectTempName = dr["dict_name"].ToString();
            }
        }
        
        private void dgvReportParam_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            if (e.Clicks == 2)
            {
                DataRow dr = dgvReportParam.GetFocusedDataRow();
                ShowEditForm(dr);
            }
        }

        void ShowEditForm(DataRow dr)
        {
            frmParamEdit fe = new frmParamEdit(dr);
            DialogResult result = fe.ShowDialog();

            if (result == DialogResult.OK)
            {
                //SaveReportParam();
            }
        }

        private void sbtnOpenParamDict_Click(object sender, EventArgs e)
        {
            DataRow dr = tempRow;
            
            frmSelectedParam fsp = new frmSelectedParam();
            //fsp.paramRow = dsReprotParam.Tables[0].Select();
            DialogResult result = fsp.ShowDialog();

            if(result == DialogResult.OK)
            {
                if (dr == null) return;

                if(fsp.paramRow != null)
                {
                    foreach(DataRow eachParam in fsp.paramRow)
                    {
                        DataRow drp = dtParams.NewRow();
                        drp.ItemArray = eachParam.ItemArray;
                        drp["report_id"] = dr["dict_id"];
                        try
                        {
                            DataRow[] selParms = dtParams.Select("report_id = '" + drp["report_id"] + "' and param_name like'" + drp["param_name"] + "%'");
                            if (selParms.Length > 0)
                            {
                                int no = 0;
                                foreach (DataRow eachRow in selParms)
                                {
                                    string[] nameSplit = eachRow["param_name"].ToString().Split('_');
                                    if (nameSplit.Length > 1)
                                    {
                                        int eacheNo = 0;
                                        bool isOk = int.TryParse(nameSplit[nameSplit.Length - 1], out eacheNo);
                                        if (isOk)
                                        {
                                            if (eacheNo > no)
                                            {
                                                no = eacheNo;
                                            }
                                        }
                                    }
                                }
                                drp["param_name"] = drp["param_name"] + "_" + (no + 1).ToString();
                            }
                            dtParams.Rows.Add(drp);
                        }
                        catch
                        {
                            XtraMessageBox.Show("参数已存在，请编辑后再添加！", "系统提示");
                        }
                    }
                }
                
            }
        }
        int groupReportParamWidth;
        private void groupReportParam_CustomButtonClick(object sender, DevExpress.XtraBars.Docking2010.BaseButtonEventArgs e)
        {
            if(e.Button == groupReportParam.CustomHeaderButtons[0])
            {
                if(groupReportParam.Width > 15)
                {
                    groupReportParamWidth = groupReportParam.Width;
                    groupReportParam.Width = 15;
                }
                else
                    groupReportParam.Width = groupReportParamWidth;
            }
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            DataRow dr = tempRow;
            if (dr == null) return;

            string id = dr["dict_id"].ToString();
            //frmCreateSQL frmSql = new frmCreateSQL();
            frmEditFieldLocal frm = new frmEditFieldLocal();
            frm.dictID = id;
            frm.dtColumns = dtConfigs;

            try
            {
                string sql = string.Empty;
                if (txtSql.EditValue != null)
                {
                    sql = txtSql.EditValue.ToString();
                }
                if (sql.EndsWith(";"))
                {
                    sql = sql.Substring(0, sql.Length - 1);
                }
                if (string.IsNullOrEmpty(sql))
                {
                    XtraMessageBox.Show("请先编写SQL，再绑定字段！", "提示");
                    return;
                }
                DataSet ds = srv.TestSQL(sql, getParams());//XtraReportHelper.GetPrintData(dr["dict_name"].ToString(), getParams());
                if (ds != null)
                {
                    frm.dataSourcesStruct = ds.Clone();
                }
                else
                {
                    XtraMessageBox.Show("请先编写SQL，再绑定字段！", "提示");
                    return;
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message, "提示");
            }
            frm.ShowDialog();
        }

        private void txtReportName_EditValueChanged(object sender, EventArgs e)
        {
            txtTemple_CellValueChanged(txtReportName.EditValue.ToString());
        }

        private void sBtnClear_Click(object sender, EventArgs e)
        {
            BindingTemple();
        }

        private void sBtnSave_Click(object sender, EventArgs e)
        {
            SaveFileDialog sd = new SaveFileDialog();
            sd.Filter = "DataSet数据xml|*.xml";
            sd.InitialDirectory = $@"{ Application.StartupPath}\reports\{Const.customAppCode}\";
            sd.FileName = "" + txtReportName.EditValue;
            sd.DefaultExt = ".xml";
            //sd.FileName = $@"{ Application.StartupPath}\reports\{txtReportName.EditValue}.xml";
            if (sd.ShowDialog() == DialogResult.OK)
            {
                string path = sd.FileName; //$@"{ Application.StartupPath}\reports\{txtReportName.EditValue}.xml";
                try
                {
                    System.Xml.XmlDocument xdoc = new System.Xml.XmlDocument();

                    System.Xml.XmlElement dsElement = xdoc.CreateElement(dsTemplet.DataSetName);
                    foreach (DataTable dt in dsTemplet.Tables)
                    {
                        foreach (DataRow dr in dt.Rows)
                        {

                            System.Xml.XmlElement dtElement = xdoc.CreateElement(dt.TableName);
                            foreach (DataColumn dc in dt.Columns)
                            {
                                System.Xml.XmlElement dcElement = xdoc.CreateElement(dc.ColumnName);
                                dcElement.InnerText = dr[dc.ColumnName].ToString();
                                dcElement.Prefix = dc.Prefix;
                                dtElement.AppendChild(dcElement);
                            }
                            dsElement.AppendChild(dtElement);
                        }
                    }
                    xdoc.AppendChild(dsElement);

                    xdoc.Save(path);

                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
            
            //dsTemplet.WriteXml(path);
        }

        private void sBtnOpen_Click(object sender, EventArgs e)
        {
            try
            {
                OpenFileDialog od = new OpenFileDialog();
                od.Filter = "DataSet xml文件|*.xml";

                if (od.ShowDialog() == DialogResult.OK)
                {
                    OpenLocalReport(od.FileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "错误提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        

        private void txtSql_EditValueChanging(object sender, DevExpress.XtraEditors.Controls.ChangingEventArgs e)
        {

            if (tempRow != null)
            {
                tempRow["templet_sql"] = e.NewValue;
            }
        }

        private void sBtnSearch_Click(object sender, EventArgs e)
        {
            string appName = txtAppName.EditValue?.ToString();
            string reportId = txtReportID.EditValue?.ToString();
            if(string.IsNullOrEmpty(reportId))
            {
                return;
            }
            if (string.IsNullOrEmpty(appName))
                appName = Const.customAppCode;

            dsTemplet = srv.GetReport(reportId,appName);
            InitBaseData();
        }
    }
}
