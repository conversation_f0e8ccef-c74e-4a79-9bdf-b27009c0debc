﻿using System;
using System.Data;
using System.ServiceModel;
namespace INMService
{
    /// <summary>
    /// 接口层BILL_PATTERN_DETAIL
    /// </summary>
    [ServiceContract]
    public interface IBILL_PATTERN_DETAIL
    {
        #region  成员方法
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        [OperationContract]
        bool Exists_BILL_PATTERN_DETAIL(string PATTERN_NAME, decimal ITEM_NO, string DEPT_CODE);
        /// <summary>
        /// 增加一条数据
        /// </summary>
        [OperationContract]
        bool Add_BILL_PATTERN_DETAIL(Model.BILL_PATTERN_DETAIL model);
        /// <summary>
        /// 更新一条数据
        /// </summary>
        [OperationContract]
        bool Update_BILL_PATTERN_DETAIL(Model.BILL_PATTERN_DETAIL model);
        /// <summary>
        /// 删除数据
        /// </summary>
        [OperationContract]
        bool Delete_BILL_PATTERN_DETAIL(string PATTERN_NAME, decimal ITEM_NO, string DEPT_CODE);
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        [OperationContract]
        Model.BILL_PATTERN_DETAIL GetModel_BILL_PATTERN_DETAIL(string PATTERN_NAME, decimal ITEM_NO, string DEPT_CODE);
        /// <summary>
        /// 获得数据列表
        /// </summary>
        [OperationContract]
        DataSet GetList_All_BILL_PATTERN_DETAIL(string strWhere);
        /// <summary>
        /// 获得前几行数据
        /// </summary>
        [OperationContract]
        DataSet GetList_BILL_PATTERN_DETAIL(int startIndex, int endIndex, string strWhere, string filedOrder);
        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.BILL_PATTERN_DETAIL> GetObservableCollection_All_BILL_PATTERN_DETAIL(string strWhere);
        /// <summary>
        /// 获得ObservableCollection根据分页获得数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.BILL_PATTERN_DETAIL> GetObservableCollection_BILL_PATTERN_DETAIL(int startIndex, int endIndex, string strWhere, string filedOrder);
        #endregion  成员方法
    }
}