﻿using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace PlatCommon.Common
{
    public partial class w_his33_input_pwd : ParentForm
    {
        public static string ls_temp;
        public w_his33_input_pwd()
        {
            InitializeComponent();
        }

        private void textEdit1_Modified(object sender, EventArgs e)
        {
            int li_pwdlen = 0;
            ls_temp = sle_pwd.Text.Trim();//待试
            if (ls_temp.Length <= 0)
            {
                XtraMessageBox.Show("密码未输入。", "提示");
                return;
            }            
            else if ( System.Text.RegularExpressions.Regex.IsMatch(ls_temp, "^[0-9]+$"))
            {
                XtraMessageBox.Show("密码必须由数字组成", "提示");
                return;
            }
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void w_his33_input_pwd_Load(object sender, EventArgs e)
        {
            /*environment env
              integer li_rtn
              i_rtn = GetEnvironment(env)
              IF li_rtn<> 1 THEN RETURN
              this.x = (PixelsToUnits(env.ScreenWidth, XPixelsToUnits!) - this.width) / 2
              this.y = (PixelsToUnits(env.ScreenHeight, YPixelsToUnits!) - this.height) / 2
             */
            sle_pwd.Focus();
        }

        private void w_his33_input_pwd_KeyDown(object sender, KeyEventArgs e)
        {
            //If KeyDown(KeyEscape!) Then CloseWithReturn(This, '') 
            if (e.KeyCode == Keys.Escape)
            {
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
        }
    }
}
