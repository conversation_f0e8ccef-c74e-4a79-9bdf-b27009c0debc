﻿/*-----------------------------------------------------------------------
 * 类名称    ：frminputsetting
 * 类描述    ：输入法窗体,适用于gridview适用于某些条件下不能使用searchlookupedit的情况需传进来GS_INPUTSETING
 * 创建人    ： 杨红宇yhy
 * ---------------------------------------------------------------------- */


using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
//using PlatCommon.System;
using NM_Service.NMService;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.WinExplorer;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors;
//using Comm.Collection;
//using Comm.Message;
//using NPM.Bll;
namespace PlatCommon.Common
{
    public partial class frminputsettingoutp : PlatCommon.SysBase.ParentForm
    {
        public frminputsettingoutp()
        {
            InitializeComponent();
        }
        public string GS_INPUTSETING;//输入法
        public string GS_ITEM_CLASS;
        public string GS_ITEM_NAME;
        public string GS_ITEM_CODE;
        public string GS_ITEM_SPEC;
        public string GS_PRICE;
        public string GS_UNITS;
        public string GS_PERFORMED_BY;
        public string GS_AAA;
        public string GS_BBB;
        public string GS_DISP; //摆药药局 格式为'A','B'
        public string GS_BATCH_NO; //批次
        public string GS_CHARGE_TYPE;
        public string GS_GJBM;//国家编码
        public string GS_GJMC;//国家名称
        public string GS_SJBM;//国家编码
        public string GS_SJMC;//国家名称
        DataTable dtPriceList;
        private void frminputsetting_Load(object sender, EventArgs e)
        {

            if (!string.IsNullOrEmpty(GS_INPUTSETING))
            {
                this.Text = GS_INPUTSETING;
            }
            LoadPriceList();
            deptdict_load();
            this.gridView3.ColumnPositionChanged += new System.EventHandler(this.gridView3_ColumnPositionChanged);
            this.gridView3.ColumnWidthChanged += new DevExpress.XtraGrid.Views.Base.ColumnEventHandler(this.gridView3_ColumnWidthChanged);
            // 设置列宽和列位置
            XtraReportHelper.AddColByConfig(this.gridView3, this.gridView3.Tag.ToString());
        }
        public void LoadPriceList()
        {
            using (ServerPublicClient client = new ServerPublicClient())
            {
                //取医保接口名，同时使用此变量标记是否为医保
                string chargeType = string.IsNullOrEmpty(GS_CHARGE_TYPE) ? "" : GS_CHARGE_TYPE;
                string sqlstr = @" select a.interfacecode from insurance.tj_interface_vs_charge a where a.chargetype = '" + chargeType + "' ";
                DataTable dt = client.GetDataBySql(sqlstr).Tables[0];
                string interfaceCode = (dt != null && dt.Rows.Count > 0) ? dt.Rows[0]["interfacecode"].ToString() : "";
                sqlstr = "  select ITEM_CLASS,ITEM_CODE,ITEM_NAME,ITEM_SPEC,UNITS,PRICE,PREFER_PRICE AAA ,FOREIGNER_PRICE BBB,PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB from input_nodrug_list where HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'"; //其它价表
                if (string.IsNullOrEmpty(GS_INPUTSETING) || GS_INPUTSETING.Equals("其它价表"))
                {
                    if (!string.IsNullOrEmpty(interfaceCode))
                    {
                        sqlstr = "  select A.ITEM_CLASS,A.ITEM_CODE,A.ITEM_NAME,A.ITEM_SPEC,A.UNITS,A.PRICE,A.PREFER_PRICE AAA,A.FOREIGNER_PRICE BBB,A.PERFORMED_BY,A.INPUT_CODE,A.INPUT_CODE_WB,A.GJBM,A.GJMC,A.GJBM SJBM,A.GJMC SJMC from input_nodrug_list A,insurance.tj_vs_price b where A.HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' and B.INTERFACECODE='" + interfaceCode + "' and  A.ITEM_CLASS=B.ITEM_CLASS AND A.ITEM_CODE=B.ITEM_CODE AND A.ITEM_SPEC=B.ITEM_SPEC";   //其它价表
                    }
                    else
                    {
                        sqlstr = "  select ITEM_CLASS,ITEM_CODE,ITEM_NAME,ITEM_SPEC,UNITS,PRICE,PREFER_PRICE AAA,FOREIGNER_PRICE BBB,PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB,GJBM,GJMC,GJBM SJBM,GJMC SJMC from input_nodrug_list A where HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";   //其它价表
                    }
                        
                }
                else if (GS_INPUTSETING.Equals("全部价表"))
                {
                    sqlstr = "  select ITEM_CLASS,ITEM_CODE,ITEM_NAME,ITEM_SPEC,UNITS,PRICE,PREFER_PRICE AAA,FOREIGNER_PRICE BBB,PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB,GJBM,GJMC,GJBM SJBM,GJMC SJMC  from INPUT_ALLPRICE_LIST A where HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'"; //全部价表
                }
                else if (GS_INPUTSETING.Equals("诊疗项目"))
                {
                    sqlstr = " select ITEM_CLASS,ITEM_CODE,ITEM_NAME, '/' ITEM_SPEC, '' UNITS ,'' PRICE,'' AAA,'' BBB,'' PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB,GJBM,GJMC,GJBM SJBM,GJMC SJMC  from V_CLINIC_NAME_DICT A where HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";  //诊疗项目
                }
                else if (GS_INPUTSETING.Equals("诊疗项目_门诊"))
                {
                    if (!string.IsNullOrEmpty(interfaceCode))
                    {
                        sqlstr = " select ITEM_CLASS,ITEM_CODE,ITEM_NAME,NVL(ITEM_SPEC,'/') ITEM_SPEC,  UNITS , PRICE,'' AAA,'' BBB,PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB,GJBM,GJMC,GJBM SJBM,GJMC SJMC   from V_CLINIC_NAME_DICT_OUTP A where HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + @"' and (decode(ITEM_CLASS,'J','I',ITEM_CLASS),ITEM_CODE) in (select t.item_class,t.item_code 
  from tj_vs_price t
 where t.interfacecode = '" + interfaceCode + @"')";  //诊疗项目
                    }
                    else
                    {
                        sqlstr = " select ITEM_CLASS,ITEM_CODE,ITEM_NAME,NVL(ITEM_SPEC,'/') ITEM_SPEC,  UNITS , PRICE,'' AAA,'' BBB,PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB,GJBM,GJMC,GJBM SJBM,'' SJMC   from V_CLINIC_NAME_DICT_OUTP A where HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";  //诊疗项目
                    }
                }
                else if (GS_INPUTSETING.Equals("诊疗药品"))
                {
                    if (!string.IsNullOrEmpty(interfaceCode))
                    {
                        sqlstr = " select aa.drug_indicator ITEM_CLASS,aa.drug_code ITEM_CODE,aa.drug_name ITEM_NAME,aa.drug_spec ITEM_SPEC,aa.package_units UNITS,aa.retail_price PRICE,aa.dose_per_unit AAA,aa.dose_units BBB,aa.storage_name ,aa.storage PERFORMED_BY,aa.INPUT_CODE,aa.INPUT_CODE_WB,aa.storage,aa.BATCH_NO,aa.QUANTITY,aa.GJBM,aa.GJMC,aa.GJBM SJBM,aa.GJMC SJMC   from INPUT_DRUG_LISTS aa, insurance.tj_vs_price a  where aa.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'  and ( a.interfacecode = '" + interfaceCode + @"' and aa.drug_code = a.ITEM_CODE and aa.drug_spec = a.item_spec and aa.package_units = a.units)";  //诊疗药品INPUT_DRUG_LISTS
                        if (!string.IsNullOrEmpty(GS_DISP))
                            sqlstr += " AND aa.STORAGE  IN ('" + GS_DISP + "')";
                    }
                    else
                    {
                        sqlstr = " select drug_indicator ITEM_CLASS,drug_code ITEM_CODE,drug_name ITEM_NAME,drug_spec ITEM_SPEC,package_units UNITS,retail_price PRICE,dose_per_unit AAA,dose_units BBB,storage_name ,storage PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB,storage,BATCH_NO,QUANTITY,GJBM,GJMC,GJBM SJBM,GJMC SJMC   from INPUT_DRUG_LISTS  where HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";  //诊疗药品INPUT_DRUG_LISTS
                        if (!string.IsNullOrEmpty(GS_DISP))
                            sqlstr += " AND STORAGE  IN ('" + GS_DISP + "')";
                    }
                }
                else if (GS_INPUTSETING.Equals("诊疗药品干诊"))
                {
                    sqlstr = " select drug_indicator ITEM_CLASS,drug_code ITEM_CODE,drug_name ITEM_NAME,drug_spec ITEM_SPEC,package_units UNITS,retail_price PRICE,dose_per_unit AAA,dose_units BBB,storage_name ,storage PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB,storage,BATCH_NO,QUANTITY,GJBM,GJMC,GJBM SJBM,GJMC SJMC   from INPUT_DRUG_LISTS_G  where HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";  //诊疗药品INPUT_DRUG_LISTS_G
                }
                else if (GS_INPUTSETING.Equals("诊疗药品_合并"))
                {
                    if (!string.IsNullOrEmpty(interfaceCode))
                    {
                        sqlstr = @" select aa.drug_indicator ITEM_CLASS,aa.drug_code ITEM_CODE,aa.drug_name ITEM_NAME,aa.drug_spec ITEM_SPEC,aa.package_units UNITS,aa.retail_price PRICE,aa.dose_per_unit AAA,aa.dose_units BBB,aa.storage_name ,aa.storage PERFORMED_BY,aa.INPUT_CODE,aa.INPUT_CODE_WB,aa.storage,aa.QUANTITY,aa.DRUG_FORM,aa.SUPPLY_INDICATOR,aa.GJBM,aa.GJMC,aa.GJBM SJBM,aa.GJMC SJMC 
                            from COMM.INPUT_DRUG_LISTS_SUM aa, insurance.tj_vs_price a where aa.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' and  (a.interfacecode = '" + interfaceCode + @"' and aa.drug_code = a.ITEM_CODE and aa.drug_spec = a.item_spec and aa.package_units = a.units)";
                    }
                    else
                    {
                        sqlstr = @" select drug_indicator ITEM_CLASS,drug_code ITEM_CODE,drug_name ITEM_NAME,drug_spec ITEM_SPEC,package_units UNITS,retail_price PRICE,dose_per_unit AAA,dose_units BBB,storage_name ,storage PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB,storage,QUANTITY,DRUG_FORM,SUPPLY_INDICATOR,GJBM,GJMC,GJBM SJBM,GJMC SJMC 
                            from COMM.INPUT_DRUG_LISTS_SUM where HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
                    }
                        
                }
                else if (GS_INPUTSETING.Equals("诊疗项目_门诊1"))
                {
                    sqlstr = " select ITEM_CLASS,ITEM_CODE,ITEM_NAME,NVL(ITEM_SPEC,'/')  ITEM_SPEC,  UNITS , PRICE,'' AAA,'' BBB, PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB,GJBM,GJMC,GJBM SJBM,GJMC SJMC   from V_CLINIC_NAME_DICT_OUTP A where HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' AND ITEM_CLASS NOT IN ('C','D')";  //诊疗项目
                }
                else if (GS_INPUTSETING.Equals("检验项目"))
                {
                    //     sqlstr = "select ITEM_CLASS,ITEM_CODE,ITEM_NAME, ITEM_CLASS ITEM_SPEC, '' UNITS ,'' PRICE,EXPAND1 AAA,EXPAND2 BBB,EXPAND3 PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB from v_clab_name_dict where HIS_UNIT_CODE='" + PlatCommon.System.SystemParm.HisUnitCode + "'";  //检验项目
                    sqlstr = "select ITEM_CLASS,ITEM_CODE,ITEM_NAME, ITEM_CLASS ITEM_SPEC, '' UNITS ,'' PRICE,EXPAND1 AAA,EXPAND2 BBB,EXPAND3 PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB from v_clab_name_dict where HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";  //检验项目
                }
                else if (GS_INPUTSETING.Equals("科室"))
                {
                    sqlstr = "select t.CLINIC_ATTR ITEM_CLASS,t.DEPT_CODE ITEM_CODE,t.DEPT_NAME ITEM_NAME,''ITEM_SPEC ,''UNITS,''PRICE,''AAA,'' BBB,'' PERFORMED_BY,INPUT_CODE,INPUT_CODE_WB  from comm.v8_dept_dict t where HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";  //科室
                }
                else if (GS_INPUTSETING.Equals("检查项目"))
                {
                    sqlstr = "SELECT distinct a.exam_class AS ITEM_CLASS,a.description_code AS ITEM_CODE,a.description AS ITEM_NAME ,a.exam_sub_class AS ITEM_SPEC,'' UNITS,'' PRICE,'' AAA,'' BBB,b.perform_by AS PERFORMED_BY,a.input_code AS INPUT_CODE FROM EXAM_RPT_PATTERN a ,EXAM_CLASS_DICT b   WHERE a.desc_item = '检查项目'AND a.exam_class = b.exam_class_name and a.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";  //检验项目
                }
                // 其它输入法也继续字段别名保持一致就可以

                DataSet ds = client.GetDataBySql(sqlstr);
                if (ds != null)
                {
                    dtPriceList = ds.Tables[0];
                    if (string.IsNullOrEmpty(GS_INPUTSETING) || GS_INPUTSETING.Equals("其它价表"))
                    {
                        if (string.IsNullOrEmpty(GS_ITEM_CLASS) || GS_ITEM_CLASS.Length == 0)
                        {
                            dtPriceList.DefaultView.RowFilter = "ITEM_CLASS NOT IN ('A','B','C','D')";
                        }
                        else
                        {
                            dtPriceList.DefaultView.RowFilter = "ITEM_CLASS  IN ('" + GS_ITEM_CLASS + "')";
                        }
                    }

                }
                ds.Dispose();

                gridControl2.DataSource = dtPriceList;
                if (GS_INPUTSETING.Equals("诊疗项目") || (GS_INPUTSETING.Equals("检验项目")) || GS_INPUTSETING.Equals("诊疗项目_门诊"))
                {
                    this.gridView3.Columns["ITEM_SPEC"].Caption = "规格";
                    this.gridView3.Columns["PERFORMED_BY"].Visible = false;
                    if (GS_INPUTSETING.Equals("检验项目"))
                    {
                        this.gridView3.Columns["PRICE"].Visible = false;
                    }
                    //一些药品专用的字段
                    this.gridView3.Columns["AAA"].Visible = false;
                    this.gridView3.Columns["BBB"].Visible = false;
                    this.gridView3.Columns["DRUG_FORM"].Visible = false;
                    this.gridView3.Columns["SUPPLY_INDICATOR"].Visible = false;
                    //
                }

                if (GS_INPUTSETING.Equals("诊疗药品") || GS_INPUTSETING.Equals("诊疗药品_合并") || GS_INPUTSETING.Equals("诊疗药品干诊"))
                {
                    this.gridView3.Columns["PERFORMED_BY"].Caption = "库房";
                    this.gridView3.Columns["PERFORMED_BY"].Visible = true;

                    //一些药品专用的字段
                    this.gridView3.Columns["AAA"].Visible = true;
                    this.gridView3.Columns["BBB"].Visible = true;
                    this.gridView3.Columns["DRUG_FORM"].Visible = true;
                    this.gridView3.Columns["SUPPLY_INDICATOR"].Visible = true;
                    //

                    if (GS_INPUTSETING.Equals("诊疗药品"))
                    {
                        this.gridView3.Columns["BATCH_NO"].Caption = "批次";
                        this.gridView3.Columns["BATCH_NO"].Visible = true;
                    }
                    else
                    {
                        this.gridView3.Columns["BATCH_NO"].Visible = false;
                    }

                    this.gridView3.Columns["QUANTITY"].Caption = "库存";
                    this.gridView3.Columns["QUANTITY"].Visible = true;
                    if (!string.IsNullOrEmpty(GS_DISP) && dtPriceList.Rows.Count > 0)
                    {
                        if (!string.IsNullOrEmpty(GS_ITEM_CLASS) && (GS_INPUTSETING.Equals("诊疗药品")))
                        {
                            dtPriceList.DefaultView.RowFilter = "ITEM_CLASS IN ('" + GS_ITEM_CLASS + "') AND STORAGE  IN ('" + GS_DISP + "')";
                            //drug_indicator
                        }
                        else
                        {
                            string ls_filter = "STORAGE  IN ('" + GS_DISP + "')";
                            dtPriceList.DefaultView.RowFilter = ls_filter;
                        }
                    }
                }
                else
                {
                    this.gridView3.Columns["PERFORMED_BY"].Visible = false;
                    this.gridView3.Columns["BATCH_NO"].Visible = false;
                    this.gridView3.Columns["QUANTITY"].Visible = false;
                }
            }
        }

        private void gridView3_MouseDown(object sender, MouseEventArgs e)
        {

        }

        private void gridView3_DoubleClick(object sender, EventArgs e)
        {
            DataRow row = this.gridView3.GetDataRow(this.gridView3.FocusedRowHandle);
            if (row != null)
            {
                try
                {
                    if (GS_INPUTSETING.Equals("诊疗药品_合并") && row["SUPPLY_INDICATOR"].ToString() == "不可供")
                    {
                        XtraMessageBox.Show("此药品不可供!", "提示");
                        return;
                    }

                    GS_ITEM_NAME = row["ITEM_NAME"].ToString();
                    GS_ITEM_CODE = row["ITEM_CODE"].ToString();
                    GS_ITEM_SPEC = row["ITEM_SPEC"].ToString();
                    GS_ITEM_CLASS = row["ITEM_CLASS"].ToString();
                    GS_PRICE = row["PRICE"].ToString();
                    GS_UNITS = row["UNITS"].ToString();
                    GS_PERFORMED_BY = row["PERFORMED_BY"].ToString();
                    GS_AAA = row["AAA"].ToString();
                    GS_BBB = row["BBB"].ToString();
                    if (GS_INPUTSETING.Equals("诊疗药品"))
                    {
                        GS_BATCH_NO = row["BATCH_NO"].ToString();
                    }
                    GS_GJBM = row["GJBM"].ToString();
                    GS_GJMC = row["GJMC"].ToString();
                    GS_SJBM = row["SJBM"].ToString();
                    GS_SJMC = row["SJMC"].ToString();
                }
                catch
                { }
            }
            this.Close();
        }
        private void textEdit1_EditValueChanged(object sender, EventArgs e)
        {
            this.gridView3.FindFilterText = this.textEdit1.Text.Trim();
        }

        private void frminputsetting_Shown(object sender, EventArgs e)
        {
            textEdit1.Focus();
        }

        private void gridView3_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                gridView3_DoubleClick(sender, e);
            }
            if (e.KeyCode == Keys.Escape)
            {
                Close();
            }
        }

        private void textEdit1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                gridControl2.Focus();
                gridView3.FocusedColumn = gITEM_NAME;
                gridView3_KeyDown(sender,e);
            }
            if (e.KeyCode == Keys.Down || e.KeyCode == Keys.Up)
            {
                gridControl2.Focus();
                gridView3.FocusedColumn = gITEM_NAME;
                if(e.KeyCode == Keys.Up&& gridView3.FocusedRowHandle>0)
                    gridView3.FocusedRowHandle = gridView3.FocusedRowHandle - 1;
                if (e.KeyCode == Keys.Down && gridView3.FocusedRowHandle != gridView3.RowCount-1)
                    gridView3.FocusedRowHandle = gridView3.FocusedRowHandle + 1;
                textEdit1.Focus();
            }
            if (e.KeyCode == Keys.Escape)
            {
                Close();
            }
        }
        private void deptdict_load()
        {
            string sqlstr = "select dept_code,dept_name from dept_dict";
            using (ServerPublicClient client = new ServerPublicClient())
            {
                DataSet ds = client.GetDataBySql(sqlstr);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    DataTable dtdept = ds.Tables[0];
                    deptItemLookUpEdit3.DataSource = dtdept;
                    deptItemLookUpEdit3.DisplayMember = "DEPT_NAME";
                    deptItemLookUpEdit3.ValueMember = "DEPT_CODE";
                }
            }
        }
        #region 键盘
        private void B0_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "0";
            }
            else
            {
                taa = "0";
            }

            textEdit1.Text = taa;
        }
        private void B1_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "1";
            }
            else
            {
                taa = "1";
            }

            textEdit1.Text = taa;
        }
        private void B2_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "2";
            }
            else
            {
                taa = "2";
            }

            textEdit1.Text = taa;
        }

        private void B3_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "3";
            }
            else
            {
                taa = "3";
            }

            textEdit1.Text = taa;
        }

        private void B4_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "4";
            }
            else
            {
                taa = "4";
            }

            textEdit1.Text = taa;
        }

        private void B5_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "5";
            }
            else
            {
                taa = "5";
            }

            textEdit1.Text = taa;
        }

        private void B6_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "6";
            }
            else
            {
                taa = "6";
            }

            textEdit1.Text = taa;
        }

        private void B7_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "7";
            }
            else
            {
                taa = "7";
            }

            textEdit1.Text = taa;
        }

        private void B8_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "8";
            }
            else
            {
                taa = "8";
            }

            textEdit1.Text = taa;
        }

        private void B9_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "9";
            }
            else
            {
                taa = "9";
            }

            textEdit1.Text = taa;
        }
        private void Q_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "Q";
            }
            else
            {
                taa = "Q";
            }
            textEdit1.Text = taa;
        }

        private void W_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "W";
            }
            else
            {
                taa = "W";
            }
            textEdit1.Text = taa;
        }

        private void E_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "E";
            }
            else
            {
                taa = "E";
            }
            textEdit1.Text = taa;
        }

        private void R_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "R";
            }
            else
            {
                taa = "R";
            }
            textEdit1.Text = taa;
        }

        private void T_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "T";
            }
            else
            {
                taa = "T";
            }
            textEdit1.Text = taa;
        }

        private void Y_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "Y";
            }
            else
            {
                taa = "Y";
            }
            textEdit1.Text = taa;
        }

        private void U_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "U";
            }
            else
            {
                taa = "U";
            }
            textEdit1.Text = taa;
        }

        private void I_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "I";
            }
            else
            {
                taa = "I";
            }
            textEdit1.Text = taa;
        }

        private void O_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "O";
            }
            else
            {
                taa = "O";
            }
            textEdit1.Text = taa;
        }

        private void P_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "P";
            }
            else
            {
                taa = "P";
            }
            textEdit1.Text = taa;
        }

        private void A_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "A";
            }
            else
            {
                taa = "A";
            }
            textEdit1.Text = taa;
        }

        private void S_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "S";
            }
            else
            {
                taa = "S";
            }
            textEdit1.Text = taa;
        }

        private void D_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "D";
            }
            else
            {
                taa = "D";
            }
            textEdit1.Text = taa;
        }

        private void F_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "F";
            }
            else
            {
                taa = "F";
            }
            textEdit1.Text = taa;
        }

        private void G_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "G";
            }
            else
            {
                taa = "G";
            }
            textEdit1.Text = taa;
        }

        private void H_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "H";
            }
            else
            {
                taa = "H";
            }
            textEdit1.Text = taa;
        }

        private void J_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "J";
            }
            else
            {
                taa = "J";
            }
            textEdit1.Text = taa;
        }

        private void K_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "K";
            }
            else
            {
                taa = "K";
            }
            textEdit1.Text = taa;
        }

        private void L_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "L";
            }
            else
            {
                taa = "L";
            }
            textEdit1.Text = taa;
        }

        private void Z_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "Z";
            }
            else
            {
                taa = "Z";
            }
            textEdit1.Text = taa;
        }

        private void X_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "X";
            }
            else
            {
                taa = "X";
            }
            textEdit1.Text = taa;
        }

        private void C_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "C";
            }
            else
            {
                taa = "C";
            }
            textEdit1.Text = taa;
        }

        private void V_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "V";
            }
            else
            {
                taa = "V";
            }
            textEdit1.Text = taa;
        }

        private void B_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "B";
            }
            else
            {
                taa = "B";
            }
            textEdit1.Text = taa;
        }

        private void N_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "N";
            }
            else
            {
                taa = "N";
            }
            textEdit1.Text = taa;
        }

        private void M_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + "M";
            }
            else
            {
                taa = "M";
            }
            textEdit1.Text = taa;
        }

        private void panelControl1_Paint(object sender, PaintEventArgs e)
        {
        }

        private void Bspace_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (!string.IsNullOrEmpty(taa))
            {
                taa = taa + " ";
            }
            else
            {
                taa = " ";
            }
            textEdit1.Text = taa;
        }
        /// <summary>
        /// 清除
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Bclear_Click(object sender, EventArgs e)
        {
            textEdit1.Text = string.Empty;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Backspace_Click(object sender, EventArgs e)
        {
            string taa = textEdit1.Text;
            if (taa.Length > 0)
            {
                taa = taa.Substring(0, taa.Length - 1);
            }
            textEdit1.Text = taa;
        }
        #endregion

        private void frminputsettingoutp_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                Close();
            }
        }
        /// <summary>
        /// 保存列宽
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gridView3_ColumnWidthChanged(object sender, ColumnEventArgs e)
        {
            XtraReportHelper.SaveColumnConfig(this.gridView3, this.gridView3.Tag.ToString());
        }
        /// <summary>
        /// 保存列位置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gridView3_ColumnPositionChanged(object sender, EventArgs e)
        {
            XtraReportHelper.SaveColumnConfig(this.gridView3, this.gridView3.Tag.ToString());
        }
    }
}
