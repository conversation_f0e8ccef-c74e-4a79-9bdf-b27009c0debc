﻿using DevExpress.XtraEditors;
using DevExpress.XtraPrinting.Preview;
using DevExpress.XtraReports.UI;
using PlatCommon.Base01;
using PlatCommon.Common;
using System;
using System.Data;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace Tjhis.Report.Custom.Common
{
    public class ReportHelper
    {
        /// <summary>
        /// 预设定的空白模板
        /// </summary>
        /// <returns></returns>
        public static string GetTempletRepx()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n");
            sb.Append("<XtraReportsLayoutSerializer SerializerVersion=\"18.1.12.0\" Ref=\"0\" ControlType=\"DevExpress.XtraReports.UI.XtraReport, DevExpress.XtraReports.v18.1, Version=18.1.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a\" Name=\"XtraReport\" PageWidth=\"850\" PageHeight=\"1100\" Version=\"18.1\">\r\n");
            sb.Append("  <Bands>\r\n");
            sb.Append("    <Item1 Ref=\"1\" ControlType=\"TopMarginBand\" Name=\"TopMargin\" HeightF=\"100\"/>\r\n");
            sb.Append("    <Item2 Ref=\"4\" ControlType=\"DetailBand\" Name=\"Detail\" HeightF=\"100\"/>\r\n");
            sb.Append("    <Item3 Ref=\"5\" ControlType=\"BottomMarginBand\" Name=\"BottomMargin\" HeightF=\"100\"/>\r\n");
            sb.Append("  </Bands>\r\n");
            sb.Append("</XtraReportsLayoutSerializer>\r\n");
            return sb.ToString();
        }

        public static void GetReportTemplateById(string strHospitalCode, string strAppName, string strReportId, string reportNo, string strReportFileName, bool isPrint, ref DocumentViewer docViewer, ref XtraReport mReport)
        {
            //加载报表模板信息
            string strRepx = GetReportRepx(strHospitalCode, strAppName, strReportId, reportNo,isPrint);
            if (string.IsNullOrEmpty(strRepx))
            {
                //预设定好的空白模板
                strRepx = GetTempletRepx();
            }
            //生成报表定义的临时文件
            string testDir = strReportFileName.Substring(0, strReportFileName.LastIndexOf('\\') + 1);
            if (!Directory.Exists(testDir))
            {
                Directory.CreateDirectory(testDir);
            }
            ////生成报表定义的临时文件
            //if (!Directory.Exists(Application.StartupPath + $"\\Reports\\{Const.customAppCode}"))
            //{
            //    Directory.CreateDirectory(Application.StartupPath + $"\\Reports\\{Const.customAppCode}");
            //}

            if (File.Exists(strReportFileName))//先删除然后写入
            {
                File.Delete(strReportFileName);
            }
            try
            {
                File.AppendAllText(strReportFileName, strRepx, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message, "错误提示");
            }
            if (mReport == null)
                mReport = new XtraReport();

            mReport.LoadLayout(strReportFileName);
            if (docViewer != null)
            {
                docViewer.DocumentSource = mReport;
            }            
            //mReport.CreateDocument();
        }

        public static string GetReportRepx(string strHospitalCode, string strAppName, string strReportId, string reportNo, bool isPrint)
        {
            string strReturn = string.Empty;
            if (string.IsNullOrEmpty(reportNo))
            {
                reportNo = "0";
            }
            string strQuerySQL = string.Empty;
            if (isPrint)
            {
                strQuerySQL = $"SELECT REPORT_PRINT_REPX FROM COMM.NURADM_REPORT_REPX WHERE HOSPITAL_CODE='{strHospitalCode}' AND REPORT_ID='{strReportId}' AND APP_NAME='{strAppName}' AND REPORTNO={reportNo}";
            }
            else
            {
                strQuerySQL = $"SELECT REPORT_REPX FROM COMM.NURADM_REPORT_REPX WHERE HOSPITAL_CODE='{strHospitalCode}' AND REPORT_ID='{strReportId}' AND APP_NAME='{strAppName}' AND REPORTNO={reportNo}";
            }             

            DataTable dtQuery = CommDataBase.GetDataTable(strQuerySQL, "NURADM_REPORT_REPX");
            if (dtQuery == null || dtQuery.Rows.Count < 1)
            {
                return strReturn;
            }
            if (isPrint)
            {
                strReturn = Cs01Functions.CStr(dtQuery.Rows[0]["REPORT_PRINT_REPX"]);
            }
            else
            {
                strReturn = Cs01Functions.CStr(dtQuery.Rows[0]["REPORT_REPX"]);
            }          
            return strReturn;
        }


        /// <summary>
        /// 获取完整的模板文件名称
        /// </summary>
        /// <returns></returns>
        public static string GetTempleteFileNameFull(string templetFileName, string appCode)
        {            
            return Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\" + appCode + "\\" + templetFileName + ".repx");
        }



        /// <summary>
        /// 获取报表的列宽xml
        /// </summary>
        /// <param name="strHospitalCode"></param>
        /// <param name="strAppName"></param>
        /// <param name="strReportId"></param>
        /// <param name="reportNo"></param>   
        /// <param name="strFileName"></param>   
        /// <returns></returns>
        public static void GetReportColumnWidthXml(string strHospitalCode, string strAppName, string strReportId, string reportNo, string strFileName)
        {
            string strReturn = string.Empty;
            if (string.IsNullOrEmpty(reportNo))
            {
                reportNo = "0";
            }
            string strQuerySQL = $"SELECT COLUMN_WIDTH_CONFIG FROM COMM.NURADM_REPORT_REPX WHERE HOSPITAL_CODE='{strHospitalCode}' AND REPORT_ID='{strReportId}' AND APP_NAME='{strAppName}' AND REPORTNO={reportNo}";

            DataTable dtQuery = CommDataBase.GetDataTable(strQuerySQL, "NURADM_REPORT_REPX");
            if (dtQuery == null || dtQuery.Rows.Count < 1)
            {
                return;
            }         
            strReturn = Cs01Functions.CStr(dtQuery.Rows[0]["COLUMN_WIDTH_CONFIG"]);
            if (string.IsNullOrEmpty(strReturn))
            {              
                return;
            }
            if (File.Exists(strFileName))//先删除然后写入
            {
                File.Delete(strFileName);
            }
            try
            {               
                File.AppendAllText(strFileName, strReturn, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message, "错误提示");
            }
        }



        public static string GetXmlFileNameFull(string fileName)
        {
           return Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Config\" + fileName + ".xml");           
        }      
    }
}
