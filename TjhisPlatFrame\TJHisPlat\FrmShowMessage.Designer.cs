﻿namespace TJHisPlat
{
    partial class FrmShowMessage
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.simpleButton1 = new DevExpress.XtraEditors.SimpleButton();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.BedLabel = new DevExpress.XtraGrid.Columns.GridColumn();
            this.PatientID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.VisitID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.Memo = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.simpleButton1);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelControl1.Location = new System.Drawing.Point(0, 278);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(448, 48);
            this.panelControl1.TabIndex = 0;
            // 
            // simpleButton1
            // 
            this.simpleButton1.Location = new System.Drawing.Point(289, 6);
            this.simpleButton1.Name = "simpleButton1";
            this.simpleButton1.Size = new System.Drawing.Size(147, 37);
            this.simpleButton1.TabIndex = 0;
            this.simpleButton1.Text = "关闭消息";
            this.simpleButton1.Click += new System.EventHandler(this.simpleButton1_Click);
            // 
            // gridControl1
            // 
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(448, 278);
            this.gridControl1.TabIndex = 1;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.BedLabel,
            this.PatientID,
            this.VisitID,
            this.Memo});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // BedLabel
            // 
            this.BedLabel.AppearanceHeader.Options.UseTextOptions = true;
            this.BedLabel.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.BedLabel.Caption = "床号";
            this.BedLabel.FieldName = "BedLabel";
            this.BedLabel.Name = "BedLabel";
            this.BedLabel.OptionsColumn.AllowEdit = false;
            this.BedLabel.Visible = true;
            this.BedLabel.VisibleIndex = 0;
            // 
            // PatientID
            // 
            this.PatientID.AppearanceHeader.Options.UseTextOptions = true;
            this.PatientID.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.PatientID.Caption = "患者ID";
            this.PatientID.FieldName = "PatientID";
            this.PatientID.Name = "PatientID";
            this.PatientID.OptionsColumn.AllowEdit = false;
            this.PatientID.Visible = true;
            this.PatientID.VisibleIndex = 1;
            // 
            // VisitID
            // 
            this.VisitID.AppearanceHeader.Options.UseTextOptions = true;
            this.VisitID.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.VisitID.Caption = "次数";
            this.VisitID.FieldName = "VisitID";
            this.VisitID.Name = "VisitID";
            this.VisitID.OptionsColumn.AllowEdit = false;
            this.VisitID.Visible = true;
            this.VisitID.VisibleIndex = 2;
            // 
            // Memo
            // 
            this.Memo.AppearanceHeader.Options.UseTextOptions = true;
            this.Memo.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.Memo.Caption = "消息";
            this.Memo.FieldName = "Memo";
            this.Memo.Name = "Memo";
            this.Memo.OptionsColumn.AllowEdit = false;
            this.Memo.Visible = true;
            this.Memo.VisibleIndex = 3;
            // 
            // FrmShowMessage
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(448, 326);
            this.ControlBox = false;
            this.Controls.Add(this.gridControl1);
            this.Controls.Add(this.panelControl1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FrmShowMessage";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "消息列表";
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.SimpleButton simpleButton1;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn BedLabel;
        private DevExpress.XtraGrid.Columns.GridColumn PatientID;
        private DevExpress.XtraGrid.Columns.GridColumn VisitID;
        private DevExpress.XtraGrid.Columns.GridColumn Memo;
    }
}