﻿using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Columns;
using PlatCommon.SysBase;
using System;
using System.Data;
using System.Linq;
using System.Windows.Forms;
using Tjhis.Report.Custom.Base;
using Tjhis.Report.Custom.Common;
using Tjhis.Report.Custom.Srv;
using DataSetHelper = PlatCommon.Common.DataSetHelper;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmEditFieldLocal : ParentForm
    {
       // DataSet dsColumns;
        public DataTable dtColumns;
        srvStatisticalQuery _srv;

        public DataSet dataSourcesStruct { get; set; }
        public string dictID { get; set; } = string.Empty;

        public frmEditFieldLocal()
        {
            InitializeComponent();

            barBtnAdd.ItemClick += (s, e) => {
                GridViewUpdateCurrentRow();
                gridView1.AddNewRow();
            };

            barBtnDelete.ItemClick += (s, e) => {
                gridView1.DeleteSelectedRows();
            };

            barBtnSave.ItemClick += (s, e) => {
                
            };

            this.FormClosed += (s,e) => {
                
            };
            gridView1.FocusedRowChanged += (s, e) => {
                DataRow focusedRow = gridView1.GetFocusedDataRow();
                if(focusedRow == null)
                {
                    return;
                }
                //dsColumns.Tables[0].DefaultView.RowFilter = "COLUMN_ID = '"+ focusedRow["COLUMN_ID"] + "'";
                DataView view = dtColumns.AsDataView();

                view.RowFilter = "COLUMN_ID = '" + focusedRow["COLUMN_ID"] + "'";
                
                vColumnsDetail.DataSource = view;
                //vColumnsDetail.DataSource = focusedRow;
            };

            rColumnField.Properties.RowEdit = COLUMN_FIELD.ColumnEdit;
            rColumnWidth.Properties.RowEdit = COLUMN_WIDTH.ColumnEdit;
            rIfSummary.Properties.RowEdit = IF_SUMMARY.ColumnEdit;
            rSummaryType.Properties.RowEdit = SUMMARY_TYPE.ColumnEdit;
            rUnbound.Properties.RowEdit = UNBOUND.ColumnEdit;
            rExpression.Properties.RowEdit = EXPRESSION.ColumnEdit;
            rUnboundType.Properties.RowEdit = UNBOUND_TYPE.ColumnEdit;
            rVisible.Properties.RowEdit = VISIBLE.ColumnEdit;
            rFormatRule.Properties.RowEdit = IF_SUMMARY.ColumnEdit;
        }

        private void frmEditField_Load(object sender, EventArgs e)
        {
            if (!DesignMode) _srv = new srvStatisticalQuery();

            BindingFieldList();
            BindingSummrayType();
            BindingUnboundType();
            BindingDefindeName();

            gridView1.InitNewRow += (s, ev) => {
                InitNewRow(gridView1.GetDataRow(ev.RowHandle));
            };

            BindingColumsConfig();
        }

        void BindingColumsConfig()
        {
            //dsColumns = _srv.GetReportConfig(dictID);
            gridControl1.DataSource = dtColumns;//dsColumns.Tables[0];

            InitRecordColumns();
        }

        void BindingFieldList()
        {
            string coustomDictStr = string.Empty;
            foreach (DataColumn dc in dataSourcesStruct.Tables[0].Columns)
            {
                coustomDictStr += dc.ColumnName + "-" + dc.ColumnName + ";";
            }
            DataTable dsCuostomDict = srvStatisticalQuery.CreateDict(coustomDictStr);

            repLupFields.DataSource = dsCuostomDict;
        }
        void BindingSummrayType()
        {
            //SUM  MIN MAX COUNT AVG
            string sumTypeStr = "Sum-和;Min-最小值;Max-最大值;Count-数量;Average-平均值";
            DataTable dsCuostomDict = srvStatisticalQuery.CreateDict(sumTypeStr);

            repLupSummrayType.DataSource = dsCuostomDict;
        }

        void BindingUnboundType()
        {
            //Bound,Integer,Decimal,DateTime,String,Boolean,Object

            string unboundType = "Decimal-数值;Integer-整型;String-字符串;DateTime-时间;Boolean-布尔";
            DataTable dsUnboundType = srvStatisticalQuery.CreateDict(unboundType);

            repLupUnboundType.DataSource = dsUnboundType;
        }

        void BindingDefindeName()
        {
            string names = @"Bold Text-粗体;Green Bold Text-绿色粗体;Green Fill-绿色填充;Green Fill, Green Text-绿色字体加填充;Green Text-绿色字体;Italic Text-斜体;Red Bold Text-红色粗体;Red Fill-红色填充;Red Fill, Red Text-红色字体加填充;Red Text-红色字体;Strikeout Text-删除线;Yellow Fill, Yellow Text-红色字体加填充";
            DataTable dtNames = srvStatisticalQuery.CreateDict(names);

            repLupPreNames.DataSource = dtNames;
        }

        void InitNewRow(DataRow newRow)
        {
            DataSet ds = new DataSet();
            ds.Tables.Add(dtColumns.Copy());
            newRow["SERIAL_NO"] = DataSetHelper.GetMaxValue(ds, "", "SERIAL_NO", 0) + 1;
            newRow["COLUMN_ID"] = DateTime.Now.ToString("yyyyMMddHHmmssfff");
            newRow["DICT_ID"] = dictID;
            newRow["COLUMN_NAME"] = "列名";
            newRow["COLUMN_WIDTH"] = 80;
            newRow["HOSPITAL_CODE"] = SystemParm.HisUnitCode;
            newRow["APP_NAME"] = Const.customAppCode;
            newRow["IF_GROUP"] = "0";
            newRow["GROUP_INDEX"] = 1;
            newRow["IF_SUMMARY"] = "0";
            newRow["SUMMARY_TYPE"] = "Sum";
            newRow["IF_UNBOUND"] = "0";
            newRow["UNBOUND_TYPE"] = "String";
            newRow["VISIBLE"] = "0";


            GridViewUpdateCurrentRow();
        }

        private void GridViewUpdateCurrentRow()
        {
            gridView1.CloseEditor();
            gridView1.UpdateCurrentRow();
            vColumnsDetail.CloseEditor();
            vColumnsDetail.UpdateFocusedRecord();
        }

        string ExpressionEdit(GridColumn gc,string expression)
        {
            DevExpress.XtraEditors.Design.UnboundColumnExpressionEditorForm f1 = new DevExpress.XtraEditors.Design.UnboundColumnExpressionEditorForm(gc, null);
            
            if(f1.ShowDialog()==DialogResult.OK)
                return f1.Expression;

            return expression;
        }

        private void InitRecordColumns()
        {
            gvDesign.Columns.Clear();
            gvDesign.OptionsView.ColumnAutoWidth = false;

            if (dtColumns == null || dtColumns.Rows.Count == 0)
            {
                return;
            }

            GridViewHelper.InitGridViewColumns(gvDesign, dtColumns);
        }
   
        
        private void repositoryItemButtonEdit1_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            GridViewUpdateCurrentRow();
            InitRecordColumns();

            DataRow dr = gridView1.GetFocusedDataRow();
            if (dr == null) return;

            GridColumn gc = gvDesign.Columns.ToList().Find(s => s.Name.Equals("COL_" + dr["SERIAL_NO"].ToString()));//["COL_" + dr["SERIAL_NO"].ToString().ToUpper()];

            string expStr = ExpressionEdit(gc, dr["EXPRESSION"].ToString());

            if (!expStr.Equals(dr["EXPRESSION"]))
                dr["EXPRESSION"] = expStr;
        }

        private void repositoryItemButtonEdit2_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            GridViewUpdateCurrentRow();
            InitRecordColumns();

            DataRow dr = gridView1.GetFocusedDataRow();
            if (dr == null) return;

            GridColumn gc = gvDesign.Columns.ToList().Find(s => s.Name.Equals("COL_" + dr["SERIAL_NO"].ToString()));//["COL_" + dr["SERIAL_NO"].ToString().ToUpper()];

            string expStr = RuleExpressionEdit(gc, dr["RULE_EXPRESSION"].ToString());

            if (!expStr.Equals(dr["RULE_EXPRESSION"]))
                dr["RULE_EXPRESSION"] = expStr;
        }

        string RuleExpressionEdit(GridColumn gc, string expression)
        {
            DevExpress.XtraGrid.GridFormatRule formatRule = new DevExpress.XtraGrid.GridFormatRule();
            FormatConditionRuleValue r = new FormatConditionRuleValue();
            r.Condition = FormatCondition.Expression;
            r.Expression = expression;
            
            //r.PredefinedName = predefinedName;// "Red Text";
            formatRule.Rule = r;
            formatRule.Column = gc;
            gvDesign.FormatRules.Clear();
            gvDesign.FormatRules.Add(formatRule);
            DevExpress.XtraEditors.Design.FormatRuleExpressionEditorForm f1 = new DevExpress.XtraEditors.Design.FormatRuleExpressionEditorForm(r,null, expression);

            if (f1.ShowDialog() == DialogResult.OK)
                return f1.Expression;

            return expression;
        }
    }
}
