﻿using PlatCommon.Common;

//**********************************************
//说明:
//计算机名称：LINDP
//创建日期：2016/7/27 15:37:37
//作者：林大鹏
//版本号：V1.00
//**********************************************
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace TJHisPlat
{
    public partial class FrmShowMessage : PlatCommon.SysBase.ParentForm
    {
        private List<MessageDisplay> list;
        public bool show = false;
        public FrmShowMessage()
        {
            InitializeComponent();
            Init();
        }
        //初始化
        public void Init()
        {
            list = new List<MessageDisplay>();
            gridControl1.DataSource = list;
        }
        public void AddData(MessageDisplay md)
        {
            list.Add(md);
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            list.Clear();
            this.show = false;
            this.Hide();
            
        }
        public void RefreshData()
        {
            gridView1.RefreshData();
        }


    }
}
