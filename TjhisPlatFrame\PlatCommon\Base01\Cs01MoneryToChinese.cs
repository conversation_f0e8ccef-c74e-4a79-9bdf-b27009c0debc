﻿/*********************************************
* 文 件 名：Cs01MoneryToChinese
* 类 名 称：Cs01MoneryToChinese
* 功能说明：金额数字转汉字大写
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：吴新才
* 创建时间：2018-05-22 15:38:26
* 版 本 号：1.0.0.1
* 修改时间：2020-02-18 15:38:26
* 修 改 人：吴新才
* CLR 版本：4.0.30319.42000
/*********************************************/

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace PlatCommon.Base01
{
    /// <summary>
    /// 金额数字转汉字大写
    /// </summary>
    public static class Cs01MoneryToChinese
    {
        private static Dictionary<char, char> d;
        private static Dictionary<int, string> dpos;

        private static bool isInited = false;
        private static void InitData()
        {
            isInited = false;

            d = new Dictionary<char, char>();
            d.Add('0', '零');
            d.Add('1', '壹');
            d.Add('2', '贰');
            d.Add('3', '叁');
            d.Add('4', '肆');
            d.Add('5', '伍');
            d.Add('6', '陆');
            d.Add('7', '柒');
            d.Add('8', '捌');
            d.Add('9', '玖');

            dpos = new Dictionary<int, string>();
            dpos.Add(1, "万");
            dpos.Add(2, "亿");
            dpos.Add(3, "兆");
            dpos.Add(4, "京");
            dpos.Add(5, "垓");
            dpos.Add(6, "秭");
            dpos.Add(7, "穰");
            dpos.Add(8, "沟");
            dpos.Add(9, "涧");
            dpos.Add(10, "正");
            dpos.Add(11, "载");
            dpos.Add(12, "极");
            dpos.Add(13, "恒河沙");
            dpos.Add(14, "阿僧祗");
            dpos.Add(15, "那由他");
            dpos.Add(16, "不可思议");
            dpos.Add(17, "无量");
            dpos.Add(18, "大数");
            dpos.Add(19, "∞");
            isInited = true;
        }

        /// <summary>
        /// 转换程序
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <param name="value">值</param>
        /// <returns></returns>
        public static string ToChinese<T>(T value)
        {
            string zhengshu = "";
            string xiaoshu = "";
            string chinese = "";
            string fh = "";
            decimal amount = Cs01Functions.CDecimal<T>(value);
            if (amount == 0)
            {
                return "零元整";
            }

            if (!isInited) InitData();
            if (amount < 0) fh = "负";

            string[] values = amount.ToString().Split('.');
            if (values.Length == 1)
            {
                zhengshu = ConvertZhengshu(values[0]);
                return $"{fh}{zhengshu}元整";
            }

            if (amount < 0)
            {
                amount = Math.Round(amount - 0.0001M, 2, MidpointRounding.AwayFromZero);
                amount = 0 - amount;
            }
            else
            {
                amount = Math.Round(amount + 0.0001M, 2, MidpointRounding.AwayFromZero);
            }
            values = amount.ToString().Split('.');
            if (values.Length == 1)
            {
                zhengshu = ConvertZhengshu(values[0]);
                return $"{fh}{zhengshu}元整";
            }

            if (amount < 1) //此处amount 不可能为0
            {
                xiaoshu = ConvertXaioshu(values[1]);
                chinese = $"{fh}{zhengshu}整";
            }
            else
            {
                zhengshu = ConvertZhengshu(values[0]);
                xiaoshu = ConvertXaioshu(values[1]);
                chinese = $"{fh}{zhengshu}元{xiaoshu}整";
            }
            return chinese;
        }
        
        /// <summary>
        /// 小数部分转换
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private static string ConvertXaioshu(string value)
        {
            int amount = Cs01Functions.CInt(value);
            value = value.TrimStart('0');
            if (amount == 0) return "";
            if (amount < 10)
            {
                return $"{d[value[0]]}分";
            }
            return $"{d[value[0]]}角{d[value[1]]}分";
        }
        /*
        万：10的4次方
        亿：10的8次方
        兆：10的12次方
        京：10的16次方。 
        垓：10的20次方。 
        秭：10的24次方。 
        穰：10的28次方。 
        沟：10的32次方。 
        涧：10的36次方。 
        正：10的40次方。 
        载：10的44次方。 
        极：10的48次方。 
        恒河沙：10的52次方。 
        阿僧祗：10的56次方。 
        那由他：10的60次方。 
        不可思议：10的64次方。 
        无量：10的68次方。 
        大数：10的72次方
        无量数（∞）。
        */
        private static string GetPosName(int pos)
        {
            string v = "";
            switch (pos)
            {
                case 1:
                    v = "";
                    break;
                case 2:
                    v = "拾";
                    break;
                case 3:
                    v = "佰";
                    break;
                case 4:
                    v = "仟";
                    break;
                case 5:
                    v = "万";
                    break;
                case 6:
                    v = "拾";
                    break;
                case 7:
                    v = "佰";
                    break;
                case 8:
                    v = "仟";
                    break;
                case 9:
                    v = "亿";
                    break;
                case 10:
                    v = "拾";
                    break;
                case 11:
                    v = "佰";
                    break;
                case 12:
                    v = "仟";
                    break;
                case 13:
                    v = "兆"; //万亿:兆（10的12次方）
                    break;
                case 14:
                    v = "拾";
                    break;
                case 15:
                    v = "佰";
                    break;
                case 16:
                    v = "仟";
                    break;
                case 17:
                    v = "京";//亿亿:京〔10的16次方）
                    break;
                case 18:
                    v = "拾";
                    break;
                case 19:
                    v = "佰";
                    break;
                case 20:
                    v = "仟";
                    break;
                default:
                    throw new Exception("暂时只支持decimal(22,2)长度的数据，需要更大数(∞)的支持，请和系统提供商联系。");
            }
            return v;
        }
        private static string ConvertZhengshu(string value)
        {
            StringBuilder sb = new StringBuilder();
            int n = value.Length;
            if (n == 0) return "零";
            char c = value[0];
            if (n == 1) return $"{ d[c]}";

            int p = FindNextNotZero(value, 0);
            int p0 = p;
            while (p > 0)
            {
                int k = (p0 - 1) / 4;
                if ((p0 % 4 != 1) && k > (p / 4)) sb.Append(dpos[k]);
                k = n - p - 1;
                if (p % 4 > 0 && k >= 1 && value[k] == '0') sb.Append('零');

                sb.Append(d[value[n - p]]);
                sb.Append(GetPosName(p));
                p0 = p;
                p = FindNextNotZero(value, n - p + 1);
            }
            return sb.ToString();
        }

        /// <summary>
        /// 找到第一个非零数字的位置，计数位置是从右到左依次增大,起始位为1， 查找是从左往右找
        /// </summary>
        /// <param name="value"></param>
        /// <param name="startIndex"></param>
        /// <returns></returns>
        private static int FindNextNotZero(string value, int startIndex)
        {
            int n = value.Length;
            if (startIndex >= n) return -1;
            if (startIndex < 0) startIndex = 0;
            int p = -1;
            for (int i = startIndex; i < n; i++)
            {
                if (value[i] != '0')
                {
                    p = n - i;
                    break;
                }
            }
            return p;
        }
    }
}
