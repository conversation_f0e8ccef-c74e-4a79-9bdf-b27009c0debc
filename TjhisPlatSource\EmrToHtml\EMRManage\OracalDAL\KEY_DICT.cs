﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Microsoft.Practices.EnterpriseLibrary.Data.Sql;
using System.Data.Common;
using System.Data;
using System.ServiceModel;
using System.ServiceModel.Channels;
using Utility.DbInfo;
using Utility.YangFan;

namespace OracleDAL
{
    /// <summary>
    /// 数据访问类KEY_DICT。
    /// </summary>
    public class KEY_DICT 
    {
        public KEY_DICT()
        { }
        #region  成员方法
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public string GetENCRYPT_CODE(string selectSql)
        {
            Database db = DatabaseFactory.CreateDatabase();
            DbCommand dbCommand = db.GetSqlStringCommand(selectSql.ToString());
            object obj=null;
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("KEY_DICT", "GetENCRYPT_CODE", selectSql.ToString(), endpoint.Address);
                }
                obj = db.ExecuteScalar(dbCommand);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + selectSql.ToString());
            }
            if ((Object.Equals(obj, null)) || (Object.Equals(obj,DBNull.Value)))
            {
               return "";
            }
            else
            {
                return obj.ToString();
            }
        }
        /// <summary>
        /// 增加一条数据
        /// </summary>
        public void Add(Model.KEY_DICT model)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into KEY_DICT(");
            strSql.Append("PRODUCT_CODE,PRODUCT_NAME,KEY");
            strSql.Append(")");
            strSql.Append(" values (");
            strSql.Append("'" + model.PRODUCT_CODE + "',");
            strSql.Append("'" + model.PRODUCT_NAME + "',");
            strSql.Append("'" + model.KEY + "'");
            strSql.Append(")");
            try
            {
                DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("KEY_DICT", "Add", strSql.ToString(), endpoint.Address);
                }
                db.ExecuteNonQuery(dbCommand);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public void Update(Model.KEY_DICT model)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update KEY_DICT set ");
            strSql.Append("PRODUCT_CODE='" + model.PRODUCT_CODE + "',");
            strSql.Append("KEY='" + model.KEY + "'");
            strSql.Append(" where PRODUCT_NAME='" + model.PRODUCT_NAME + "' ");
            try
            {
                DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("KEY_DICT", "Update", strSql.ToString(), endpoint.Address);
                }
                db.ExecuteNonQuery(dbCommand);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public void Delete(string PRODUCT_NAME)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from KEY_DICT ");
            strSql.Append(" where PRODUCT_NAME='" + PRODUCT_NAME + "' ");
            try
            {
                DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("KEY_DICT", "Delete", strSql.ToString(), endpoint.Address);
                }
                db.ExecuteNonQuery(dbCommand);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.KEY_DICT GetModel(string PRODUCT_NAME)
        {
            string strSql ="select PRODUCT_CODE,PRODUCT_NAME,KEY from KEY_DICT where PRODUCT_NAME='" + PRODUCT_NAME + "'";
            Model.KEY_DICT model = null;
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("KEY_DICT", "GetModel", strSql.ToString(), endpoint.Address);
                }
                DataTable dataReader = DbHelperOra.GetDataReader(strSql);// db.ExecuteReader(dbCommand))
                if (dataReader.Rows.Count > 0)
                {
                    model = ReaderBind(dataReader.Rows[0]);
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
            return model;
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select PRODUCT_CODE,PRODUCT_NAME,KEY ");
            strSql.Append(" FROM KEY_DICT ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("KEY_DICT", "GetList", strSql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, strSql.ToString());
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            Database db = DatabaseFactory.CreateDatabase();
            DbCommand dbCommand = db.GetStoredProcCommand("UP_GetRecordByPage");
            db.AddInParameter(dbCommand, "tblName", DbType.AnsiString, "KEY_DICT");
            db.AddInParameter(dbCommand, "fldName", DbType.AnsiString, "System.Collections.Generic.List`1[LTP.CodeHelper.ColumnInfo]");
            db.AddInParameter(dbCommand, "PageSize", DbType.Int32, PageSize);
            db.AddInParameter(dbCommand, "PageIndex", DbType.Int32, PageIndex);
            db.AddInParameter(dbCommand, "IsReCount", DbType.Boolean, 0);
            db.AddInParameter(dbCommand, "OrderType", DbType.Boolean, 0);
            db.AddInParameter(dbCommand, "strWhere", DbType.AnsiString, strWhere);
            return db.ExecuteDataSet(dbCommand);
        }*/

        /// <summary>
        /// 获得数据列表（比DataSet效率高，推荐使用）
        /// </summary>
        public List<Model.KEY_DICT> GetListArray(string strWhere)
        {
            string strSql = "select PRODUCT_CODE,PRODUCT_NAME,KEY FROM KEY_DICT ";
            if (strWhere.Trim() != "")
            {
                strSql += " where " + strWhere;
            }
            List<Model.KEY_DICT> list = new List<Model.KEY_DICT>();
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("KEY_DICT", "GetListArray", strSql.ToString(), endpoint.Address);
                }
                DataTable dataReader = DbHelperOra.GetDataReader(strSql);// db.ExecuteReader(CommandType.Text, strSql.ToString()))
                foreach (DataRow dr in dataReader.Rows)
                {
                    list.Add(ReaderBind(dr));
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            } 
            return list;
        }
        /// <summary>
        /// 对象实体绑定数据
        /// </summary>
        public Model.KEY_DICT ReaderBind(DataRow dataReader)
        {
            Model.KEY_DICT model = new Model.KEY_DICT();
            model.PRODUCT_CODE = dataReader["PRODUCT_CODE"].ToString();
            model.PRODUCT_NAME = dataReader["PRODUCT_NAME"].ToString();
            model.KEY = dataReader["KEY"].ToString();
            return model;
        }
        #endregion  成员方法
    }
}

