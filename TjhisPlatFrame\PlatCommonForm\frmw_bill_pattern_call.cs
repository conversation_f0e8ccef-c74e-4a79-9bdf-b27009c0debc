﻿/*-----------------------------------------------------------------------
 * 类名称    ：frmw_bill_pattern_call
 * 类描述    ：费用模板调用(计价单，多项录入)
 * 创建人    ：杨红宇yhy
 * 创建时间  ：2016/7/4 10:00:00
 * 修改人    ：杨红宇yhy
 * 修改时间  ：2016/7/4 10:00:00
 * 修改备注  ：
 * 版本      ：1.1
 * ----------------------------------------------------------------------
 */

using PlatCommon.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
//using PlatCommon.System;
using NM_Service.NMService;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.WinExplorer;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors;
//using Comm.Collection;
//using Comm.Message;
//using NPM.Bll;
namespace PlatCommonForm
{
    public partial class frmw_bill_pattern_call : PlatCommon.SysBase.ParentForm
    {
        public frmw_bill_pattern_call(string ward_code)
        {
            InitializeComponent();
            this.WardCode = ward_code;
        }
        string gs_PATTERN_NAME;//模板名称
        //模板数据源，取当前护理单元
        private DataSet dw_pattern = new DataSet();
        //模板明细
        private DataSet dtdetail = new DataSet();
        public DataSet wcdetail = new DataSet(); //传出去
        string titem_name, gschecks;
        string tITEM_CLASS, tITEM_CODE, tITEM_SPEC, tUNITS, tPERFORMED_BY, tAMOUNT, tDEPT_CODE,tPRICE;
        string tmemo;

        #region 加载数据源
        /// <summary>
        /// 加载模板名称数据源
        /// </summary>
        public void Loadpattern()
        {
            string sqlstr = " SELECT a.SERIAL_NO, a.PATTERN_NAME,a.INPUT_CODE,a.INPUT_CODE_WB,a.DEPT_CODE, '1' as IS_VALID,'0' CHECKS "
                        + "  FROM COMM.BILL_PATTERN_MASTER  a"
                        + "  WHERE a.DEPT_CODE = '" + this.WardCode + "' ORDER BY a.SERIAL_NO ASC";
            using (ServerPublicClient client = new ServerPublicClient())
            {
                DataSet ds = client.GetDataBySql(sqlstr);
                dw_pattern = ds;

                gridControl2.DataSource = dw_pattern.Tables[0];

                ds.Dispose();
            }
        }
        /// <summary>
        /// 加载模板明细数据源
        /// </summary>
        public void Loaddetail()
        {
            string sqlstr = "SELECT '0' CHECKS, b.PATTERN_NAME, b.ITEM_NO,b.ITEM_CLASS,b.ITEM_CODE,b.ITEM_SPEC,b.UNITS, b.PERFORMED_BY,b.AMOUNT, "
                        + " (case when b.ITEM_CLASS = 'A' or b.ITEM_CLASS = 'B' then 'A' else 'B' end) BB,b.DEPT_CODE , c.ITEM_NAME,d.PRICE, "
                        + "decode(d.PRICE , null , '无效项目', '项目正常' ) as MEMO"
                        + "  FROM COMM.BILL_PATTERN_DETAIL b ,  COMM.PRICE_LIST c, COMM.CURRENT_PRICE_LIST d "
                        + "  WHERE  b.DEPT_CODE = '" + this.WardCode + "' and b.PATTERN_NAME ='" + gs_PATTERN_NAME + "' and"
                        + " ( b.ITEM_CLASS = c.ITEM_CLASS(+) ) and  ( b.ITEM_CODE =c.ITEM_CODE(+) ) and   ( c.stop_date is null )   and  "
                        + "  ( b.ITEM_CLASS = d.ITEM_CLASS(+) ) and ( b.ITEM_CODE = d.ITEM_CODE(+) ) and ( b.ITEM_SPEC = d.ITEM_SPEC(+) ) and  ( b.UNITS = d.UNITS(+) )";
            using (ServerPublicClient client = new ServerPublicClient())
            {
                DataSet ds = client.GetDataBySql(sqlstr);

                dtdetail = ds;

                gridControl1.DataSource = dtdetail.Tables[0];

                ds.Dispose();
            }
        }

        /// <summary>
        /// 已选择明细数据源
        /// </summary>
        public void Loadwcdetail()
        {
            string sqlstr = "SELECT '0' CHECKS, b.PATTERN_NAME, b.ITEM_NO,b.ITEM_CLASS,b.ITEM_CODE,b.ITEM_SPEC,b.UNITS, b.PERFORMED_BY,b.AMOUNT, "
                        + " (case when b.ITEM_CLASS = 'A' or b.ITEM_CLASS = 'B' then 'A' else 'B' end) BB,b.DEPT_CODE , c.ITEM_NAME,d.PRICE, "
                        + "decode(d.PRICE , null , '无效项目', '项目正常' ) as MEMO"
                        + "  FROM COMM.BILL_PATTERN_DETAIL b ,  COMM.PRICE_ITEM_NAME_DICT c, COMM.CURRENT_PRICE_LIST d "
                        + "  WHERE  b.DEPT_CODE = '" + this.WardCode + "' and b.PATTERN_NAME ='" + "" + "' and"
                        + " ( b.ITEM_CLASS = c.ITEM_CLASS(+) ) and  ( b.ITEM_CODE =c.ITEM_CODE(+) ) and   ( c.STD_INDICATOR = 1 )   and  "
                        + "  ( b.ITEM_CLASS = d.ITEM_CLASS(+) ) and ( b.ITEM_CODE = d.ITEM_CODE(+) ) and ( b.ITEM_SPEC = d.ITEM_SPEC(+) ) and  ( b.UNITS = d.UNITS(+) )";
            using (ServerPublicClient client = new ServerPublicClient())
            {
                DataSet ds = client.GetDataBySql(sqlstr);

                wcdetail = ds;

                gridControl3.DataSource = wcdetail.Tables[0];

                ds.Dispose();
            }
        }


        #endregion
        //关闭按钮
        private void simpleButton4_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        //增加按钮
        private void simpleButton1_Click(object sender, EventArgs e)
        {
            for (int rowIndex = 0; rowIndex < gridView1.RowCount; rowIndex++)
            {
                gschecks = gridView1.GetRowCellValue(rowIndex, "CHECKS").ToString();
                if (gschecks != "1") continue;
                titem_name = gridView1.GetRowCellValue(rowIndex, "ITEM_NAME").ToString();
                tITEM_CLASS = gridView1.GetRowCellValue(rowIndex, "ITEM_CLASS").ToString();
                tITEM_CODE = gridView1.GetRowCellValue(rowIndex, "ITEM_CODE").ToString();
                tITEM_SPEC = gridView1.GetRowCellValue(rowIndex, "ITEM_SPEC").ToString();
                tUNITS = gridView1.GetRowCellValue(rowIndex, "UNITS").ToString();
                tPERFORMED_BY = gridView1.GetRowCellValue(rowIndex, "PERFORMED_BY").ToString();
                tAMOUNT = gridView1.GetRowCellValue(rowIndex, "AMOUNT").ToString();
                tDEPT_CODE = gridView1.GetRowCellValue(rowIndex, "DEPT_CODE").ToString();
                tPRICE = gridView1.GetRowCellValue(rowIndex, "PRICE").ToString();
                tmemo =  gridView1.GetRowCellValue(rowIndex, "MEMO").ToString();
                gridView10.AddNewRow();
                gridView10.CloseEditor();
                gridView10.UpdateCurrentRow();
            }
            
        }
        //删除按钮
        private void simpleButton2_Click(object sender, EventArgs e)
        {
            int iSelectRowCount = gridView10.SelectedRowsCount;
            if (iSelectRowCount > 0)
            {

                gridView10.DeleteSelectedRows();

            }
            //gridView10.CloseEditor();
            //gridView10.UpdateCurrentRow();
            //for (int rowIndex = 0; rowIndex < gridView10.RowCount; rowIndex++)
            //{
            //    string tchecks = gridView10.GetRowCellValue(rowIndex, "CHECKS").ToString();
            //    if (tchecks != "1") continue;
            //    gridView10.DeleteRow(rowIndex);

            //}

        }
        //确认按钮
        private void simpleButton3_Click(object sender, EventArgs e)
        {
            gridView10.CloseEditor();
            gridView10.UpdateCurrentRow();
            DataSet ds = wcdetail;
            this.Close();
        }
        //初始化
        private void frmw_bill_pattern_call_Load(object sender, EventArgs e)
        {
            Loadpattern();
            Loaddetail();
            Loadwcdetail();
        }
        #region 查看模板的明细
        //查看模板的明细
        private void gridView3_RowClick(object sender, RowClickEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView dview = (DevExpress.XtraGrid.Views.Grid.GridView)sender;
            DataRow DRow = dview.GetDataRow(e.RowHandle);
            if (DRow != null)
            {
                gs_PATTERN_NAME = gridView3.GetRowCellValue(gridView3.FocusedRowHandle, "PATTERN_NAME").ToString();
                Loaddetail();
            }
        }
        #endregion
        #region 全选与取消全选
        /// <summary>
        /// 模板明细的全选
        /// </summary>
        public void selAll()
        {
            for (int rowIndex = 0; rowIndex < gridView1.RowCount; rowIndex++)
            {
                gridView1.SetRowCellValue(rowIndex, "CHECKS", "1");
            }
         
            gridView1.UpdateCurrentRow();
        }
        /// <summary>
        /// 模板明细的取消全选
        /// </summary>
        public void removeAll()
        {
            for (int rowIndex = 0; rowIndex < gridView1.RowCount; rowIndex++)
            {
                gridView1.SetRowCellValue(rowIndex, "CHECKS", "0");
            }
            
            gridView1.UpdateCurrentRow();
        }
        //模板明细全选与取消全选
        private void checkEdit2_CheckedChanged(object sender, EventArgs e)
        {
            if (checkEdit2.CheckState == CheckState.Checked)
            {
                selAll();
            }
            else
            {
                removeAll();
            }

        }
        /// <summary>
        /// 准备确认明细的全选
        /// </summary>
        public void wcselAll()
        {
            for (int rowIndex = 0; rowIndex < gridView10.RowCount; rowIndex++)
            {
                gridView10.SetRowCellValue(rowIndex, "CHECKS", "1");
            }

            gridView10.UpdateCurrentRow();
        }
        /// <summary>
        /// 准备确认明细的取消全选
        /// </summary>
        public void wcremoveAll()
        {
            for (int rowIndex = 0; rowIndex < gridView10.RowCount; rowIndex++)
            {
                gridView10.SetRowCellValue(rowIndex, "CHECKS", "0");
            }

            gridView10.UpdateCurrentRow();
        }
        //准备确认明细的全选与取消全选操作
        private void checkEdit1_CheckedChanged(object sender, EventArgs e)
        {
            if (checkEdit1.CheckState == CheckState.Checked)
            {
                wcselAll();
            }
            else
            {
                wcremoveAll();
            }


        }
         #endregion

        private void gridView1_RowClick(object sender, RowClickEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView dview = (DevExpress.XtraGrid.Views.Grid.GridView)sender;
            DataRow DRow = dview.GetDataRow(e.RowHandle);
            if (DRow != null)
            {
              string  tchecks = gridView1.GetRowCellValue(gridView1.FocusedRowHandle, "CHECKS").ToString();
              if (tchecks == "0" )
                {
                    gridView1.SetRowCellValue(gridView1.FocusedRowHandle, "CHECKS", "1");
                }
              else
              {
                  gridView1.SetRowCellValue(gridView1.FocusedRowHandle, "CHECKS", "0");
              }
                
            }

        }

        private void gridView10_RowClick(object sender, RowClickEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView dview = (DevExpress.XtraGrid.Views.Grid.GridView)sender;
            DataRow DRow = dview.GetDataRow(e.RowHandle);
            if (DRow != null)
            {
                string tchecks = gridView10.GetRowCellValue(gridView10.FocusedRowHandle, "CHECKS").ToString();
                if (tchecks == "0")
                {
                    gridView10.SetRowCellValue(gridView10.FocusedRowHandle, "CHECKS", "1");
                }
                else
                {
                    gridView10.SetRowCellValue(gridView10.FocusedRowHandle, "CHECKS", "0");
                }

            }
        }
        #region 新增事件
        private void gridView10_InitNewRow(object sender, InitNewRowEventArgs e)
        {

            if (tmemo != "项目正常")
            {
                XtraMessageBox.Show("查找项目价格失败" +titem_name + tITEM_CODE, "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

                DataRowView drv = gridView10.GetRow(e.RowHandle) as DataRowView;
                string maxid = wcdetail.Tables[0].Compute("max(ITEM_NO)", "").ToString();
                if (string.IsNullOrEmpty(maxid))
                    maxid = "1";
                else
                {
                    int id = int.Parse(maxid) + 1;
                    maxid = id.ToString();
                }
                drv.Row["ITEM_NO"] = maxid;
                drv.Row["DEPT_CODE"] = tDEPT_CODE;
                drv.Row["BB"] = "B";
                drv.Row["ITEM_CLASS"] = tITEM_CLASS;
                drv.Row["ITEM_CODE"] = tITEM_CODE;
                drv.Row["ITEM_SPEC"] = tITEM_SPEC;
                drv.Row["UNITS"] = tUNITS;
                drv.Row["AMOUNT"] = tAMOUNT;
                drv.Row["PERFORMED_BY"] = tPERFORMED_BY;
                drv.Row["ITEM_NAME"] = titem_name;
                drv.Row["PRICE"] = tPRICE;
                drv.Row["MEMO"] = tmemo;
        }
        #endregion

    }
}
