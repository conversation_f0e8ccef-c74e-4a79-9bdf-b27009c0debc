﻿using PlatCommon.Base01;
using PlatCommon.Base02;
using PlatCommon.Common;
using PlatCommon.SysBase;
using System;
using System.Collections;
using System.Data;
using System.Drawing.Printing;
using System.IO;
using System.Windows.Forms;
using Tjhis.Report.Custom.Common;
using SQL = PlatCommon.Base02.Cs02StringHelper;

namespace Tjhis.Report.Custom.Base
{
    public partial class PrinterConfigFrm : ParentForm
    {
        private static string configFileName = "PrinterConfig.xml";
        private DataSet dsPrinterConfig = null;

        public PrinterConfigFrm()
        {
            InitializeComponent();
        }


        private void PrinterConfigFrm_Load(object sender, EventArgs e)
        {
            try
            {
                dsPrinterConfig = loadTempleteFileData(Path.Combine(Application.StartupPath, "Reports",Const.customAppCode));
                this.gridControl1.DataSource = dsPrinterConfig.Tables[0].DefaultView;

                loadPrinterList();
            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }


        /// <summary>
        /// 按钮[设置打印机]
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void xbtnSelectPrinter_Click(object sender, EventArgs e)
        {
            try
            {
                DataRow dr = this.gridView1.GetFocusedDataRow();
                if (dr == null) return;

                if (this.lstPrinter.SelectedIndex < 0) return;

                dr["PRINTER_NAME"] = this.lstPrinter.SelectedItem.ToString();

                this.xbtnOK.Enabled = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }


        /// <summary>
        /// 按钮[清除打印机]
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void xbtnUnSelectPrinter_Click(object sender, EventArgs e)
        {
            try
            {
                DataRow dr = this.gridView1.GetFocusedDataRow();
                if (dr == null) return;

                dr["PRINTER_NAME"] = DBNull.Value;

                this.xbtnOK.Enabled = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        
        /// <summary>
        /// 按钮[保存]
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void xbtnOK_Click(object sender, EventArgs e)
        {
            try
            {
                string fileName = Path.Combine(Application.StartupPath, "Reports", Const.customAppCode);
                fileName = Path.Combine(fileName, configFileName);

                dsPrinterConfig.WriteXml(fileName, XmlWriteMode.WriteSchema);

                this.DialogResult = System.Windows.Forms.DialogResult.OK;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }


        private void SIB_EXIT_Click(object sender, EventArgs e)
        {
            this.Close();
        }


        /// <summary>
        /// 加载打印模版文件
        /// </summary>
        /// <returns></returns>
        private DataSet loadTempleteFileData(string sPath)
        {
            // 创建存储结构
            DataSet dsConfig = new DataSet();
            DataTable dt = dsConfig.Tables.Add("TEMPLETE_FILE");
            dt.Columns.Add("REPX_FILE_NAME");
            dt.Columns.Add("PRINTER_NAME");
            //20190802 liul  加载护理记录单打印模板
            ArrayList NurReport = Cs01FileHelper.GetDirFiles(Path.Combine(Application.StartupPath, "Reports", Const.customAppCode));
            // 加载目录下的所有文件
            ArrayList arrFiles = Cs01FileHelper.GetDirFiles(sPath);
            arrFiles.Add(NurReport);
            string fileName = string.Empty;
            for (int i = 0; i < arrFiles.Count; i++)
            {
                fileName = arrFiles[i].ToString();
                if (Path.GetExtension(fileName).Equals(".repx", StringComparison.OrdinalIgnoreCase) == false) continue;

                DataRow drNew = dsConfig.Tables[0].NewRow();
                drNew["REPX_FILE_NAME"] = Path.GetFileNameWithoutExtension(fileName);
                dsConfig.Tables[0].Rows.Add(drNew);
            }

            // 加载已有配置
            DataSet ds = new DataSet();

            fileName = Path.Combine(sPath, configFileName);
            if (File.Exists(fileName) == false) return dsConfig;

            try
            {
                ds.ReadXml(fileName);
            }
            catch
            {
                return dsConfig;
            }

            foreach (DataRow dr in dsConfig.Tables[0].Rows)
            {
                string filter = "REPX_FILE_NAME = " + SQL.SqlConvert(dr["REPX_FILE_NAME"].ToString());

                DataRow[] drFind = ds.Tables[0].Select(filter);
                if (drFind.Length == 0) continue;

                string printerName = drFind[0]["PRINTER_NAME"].ToString();
                if (string.IsNullOrEmpty(printerName)) continue;

                dr["PRINTER_NAME"] = printerName;
            }

            return dsConfig;
        }


        /// <summary>
        /// 加载打印机列表
        /// </summary>
        private void loadPrinterList()
        {
            PrintDocument print = new PrintDocument();

            foreach (string sPrint in PrinterSettings.InstalledPrinters)    // 获取所有打印机名称
            {
                lstPrinter.Items.Add(sPrint);
            }
        }

        
        /// <summary>
        /// 获取打印机名称
        /// </summary>
        /// <param name="templetName">模版名称</param>
        /// <returns></returns>
        public static string GetPrinterName(string templetName)
        {
            PrintDocument print = new PrintDocument();
            string printerName = print.PrinterSettings.PrinterName;

            // 加载已有配置
            DataSet ds = new DataSet();

            string sPath = Path.Combine(Application.StartupPath, "Reports", Const.customAppCode);
            string fileName = Path.Combine(sPath, configFileName);
            if (File.Exists(fileName) == false) return printerName;

            try
            {
                ds.ReadXml(fileName);
            }
            catch
            {
                return printerName;
            }

            if (Cs02DataSetHelper.HasRecord(ds) == false) return printerName;

            string filter = "REPX_FILE_NAME = " + SQL.SqlConvert(templetName);
            DataRow[] drFind = ds.Tables[0].Select(filter);
            if (drFind.Length > 0) printerName = drFind[0]["PRINTER_NAME"].ToString();

            if (string.IsNullOrEmpty(printerName) == false) return printerName;

            return print.PrinterSettings.PrinterName;
        }
    }
}
