﻿using System;
using System.Data;
using Tjhis.Report.Custom.Common;

namespace Tjhis.Report.Custom.Srv
{
    /// <summary>
    /// 自定义报表数据访问类
    /// </summary>
    public class srvT
    {
        ///// <summary>
        ///// 获取数据库用户
        ///// </summary>
        ///// <returns></returns>
        //public DataSet GetUsers()
        //{
        //    string sql = @"SELECT * FROM ALL_USERS";

        //    return _his01DbHelper.GetDataSet(sql, "ALL_USERS", false);
        //}

        ///// <summary>
        ///// 获取用户表
        ///// </summary>
        ///// <returns></returns>
        //public DataSet GetUserTables(string user)
        //{
        //    string sql = @"SELECT A.OWNER, A.TABLE_NAME, B.COMMENTS
        //                      FROM ALL_TABLES A, ALL_TAB_COMMENTS B
        //                     WHERE A.TABLE_NAME = B.TABLE_NAME
        //                       AND A.OWNER = B.OWNER
        //                       AND A.OWNER='" + user+"'";
        //    return _his01DbHelper.GetDataSet(sql, "ALL_TABLES", false);
        //}

        /// <summary>
        /// 获取表所包含字段
        /// </summary>
        /// <returns></returns>
        public DataSet GetTableColums(string tableName)
        {
            string sql = @"SELECT * FROM ALL_COL_COMMENTS T WHERE T.TABLE_NAME = '" + tableName + "'";
            return CommDataBase.GetDataSet(sql, "ALL_COL_COMMENTS", false);
        }

        public DataSet TestSQL(string sqlStr)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// 创建字典
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static DataTable CreateDict(string str)
        {
            throw new NotImplementedException();
        }

        public DataSet GetUsers()
        {
            throw new NotImplementedException();
        }

        public DataSet GetUserTables(string user)
        {
            throw new NotImplementedException();
        }

        ///// <summary>
        ///// 创建字典
        ///// </summary>
        ///// <param name="str"></param>
        ///// <returns></returns>
        //public static DataTable CreateDict(string str)
        //{
        //    //1-wei;2-wei;3-wei
        //    string[] items = str.Split(';');
        //    if (items.Length <= 0) return null;

        //    DataTable dtDict = new DataTable();
        //    dtDict.Columns.Add("COLUMN_NAME", Type.GetType("System.String"));
        //    dtDict.Columns.Add("COMMENTS", Type.GetType("System.String"));

        //    foreach (string item in items)
        //    {
        //        if(!string.IsNullOrEmpty(item))
        //            dtDict.Rows.Add(item.Split('-'));
        //    }

        //    return dtDict;
        //}

        ///// <summary>
        ///// 测试脚本
        ///// </summary>
        ///// <param name="sql"></param>
        ///// <returns></returns>
        //public DataSet TestSQL(string sql)
        //{
        //    string[] sqlArray = sql.Split(';');
        //    DataSet ds = null;
        //    foreach(string sqlStr in sqlArray)
        //    {
        //        ds = new DataSet();
        //        DataSet ds1 = _his01DbHelper.GetDataSet(sqlStr, "table1", false);
        //        ds.Tables.Add(ds1.Tables[0].Copy());
        //    }
        //    return ds;
        //}
        ///// <summary>
        ///// 保存数据
        ///// </summary>
        ///// <param name="ds"></param>
        ///// <returns></returns>
        //public int SaveData(DataSet ds)
        //{
        //    if (ds.HasChanges())
        //    {
        //        return _his01DbHelper.SaveDataWithTrans(ds);
        //    }

        //    return 0;
        //}

        //public int Execute(string sql)
        //{
        //    return _his01DbHelper.ExecuteWithTrans(sql);
        //}
    }
}
