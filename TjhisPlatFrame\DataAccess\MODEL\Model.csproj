﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9F136B85-9E87-42E5-B33B-A1BCEEFB6022}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Model</RootNamespace>
    <AssemblyName>Model</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\..\TjhisPlatSource\TJHisPlatEXE\Client\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
    <DocumentationFile>..\..\..\TjhisPlatSource\TJHisPlatEXE\Client\Model.xml</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Comm\Bill_Pattern_Detail.cs" />
    <Compile Include="Comm\Bill_Pattern_Master.cs" />
    <Compile Include="Comm\Model_Group.cs" />
    <Compile Include="Comm\Main_Menu.cs" />
    <Compile Include="Comm\Models.cs" />
    <Compile Include="Comm\SEC_MENUS_DICT.cs" />
    <Compile Include="Comm\SEC_RIGHT_GROUP.cs" />
    <Compile Include="Comm\SEC_RIGHT_GROUP_VS_MENUS.cs" />
    <Compile Include="Comm\SEC_RIGHT_GROUP_VS_USERS.cs" />
    <Compile Include="Comm\Staff_Dict.cs" />
    <Compile Include="EMR\EMR_3RD_FILE_INDEX.cs" />
    <Compile Include="EMR\MR_FILE_INDEX.cs" />
    <Compile Include="EMR\MR_TEMPLET_INDEX.cs" />
    <Compile Include="EMR\OUTP_MR_FILE_INDEX.cs" />
    <Compile Include="HISINTERFACE\LNCA_INTERFACE.cs" />
    <Compile Include="INPADM\Bed_Rec.cs" />
    <Compile Include="NotificationObject.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Comm\BJCA_INTERFACE.cs" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>