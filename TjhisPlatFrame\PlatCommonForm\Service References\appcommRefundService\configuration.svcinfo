﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;webServiceSoap&quot; /&gt;" bindingType="basicHttpBinding" name="webServiceSoap" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://**************:1014/webService.asmx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;webServiceSoap&quot; contract=&quot;appcommRefundService.webServiceSoap&quot; name=&quot;webServiceSoap&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://**************:1014/webService.asmx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;webServiceSoap&quot; contract=&quot;appcommRefundService.webServiceSoap&quot; name=&quot;webServiceSoap&quot; /&gt;" contractName="appcommRefundService.webServiceSoap" name="webServiceSoap" />
  </endpoints>
</configurationSnapshot>