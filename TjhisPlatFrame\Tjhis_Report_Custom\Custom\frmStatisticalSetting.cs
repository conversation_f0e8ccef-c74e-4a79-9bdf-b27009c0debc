﻿using DevExpress.XtraEditors;
using PlatCommon.Base01;
using PlatCommon.Common;
using PlatCommon.SysBase;
using System;
using System.Collections;
using System.Data;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using Tjhis.Report.Custom.Common;
using Tjhis.Report.Custom.Srv;
using SQL = PlatCommon.Base02.Cs02StringHelper;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmStatisticalSetting : ParentForm
    {
        srvStatisticalQuery srv = new srvStatisticalQuery();
        private string hospitalCode = SystemParm.HisUnitCode;
        private string appName = Const.customAppCode;
        DataSet dsTemplet;

        public frmStatisticalSetting()
        {
            InitializeComponent();

            ////----for test
            //layoutControlItem2.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;
            //layoutControlItem3.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;
            //emptySpaceItem16.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;
            //layoutControlItem4.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;
        }

        private void frmStatisticalSetting_Load(object sender, EventArgs e)
        {
            //是否再护理管理中打开，如果是则显示 添加至 按钮
            if (appName.Equals("NURADM"))
            {
                //layoutControlItem21.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always;
                layoutControlItem26.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always;
            }
            else
            {
                //layoutControlItem21.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;
                layoutControlItem26.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;
            }

            //LookupEdit下拉框绑定
            LUE_EditType.ValueMember = "ITEM_CODE";
            LUE_EditType.DisplayMember = "ITEM_NAME";
            LUE_EditType.DataSource = srvStatisticalQuery.CreateDict("1-下拉框;2-文本框;3-日期框;4-数值框");
            LUE_SourceType.ValueMember = "ITEM_CODE";
            LUE_SourceType.DisplayMember = "ITEM_NAME";
            LUE_SourceType.DataSource = srvStatisticalQuery.CreateDict("1-SQL脚本;2-自定义选项");

            repositoryItemLookUpEdit1.DataSource = srv.GetApplications().Tables[0];
            BindingTemple();
            BoundDiffRepositoryItem();
        }

        void BindingTemple()
        {
            if (dsTemplet != null)
            {
                gcTemplet.DataSource = null;
                gcTemplet.RefreshDataSource();
            }
            dsTemplet = srv.GetTempletList();
            for (int i = 0; i < dsTemplet.Tables[0].Rows.Count; i++)
            {
                DataRow dr = dsTemplet.Tables[0].Rows[i];
                bool haveRole = GetRole(dr);

                if (!haveRole)
                {
                    dr.Delete();
                }
            }
            dsTemplet.AcceptChanges();

            dgvTemple.FocusedRowChanged -= dgvTemple_FocusedRowChanged;

            gcTemplet.DataSource = dsTemplet.Tables[0];

            repLupTempletClass.DataSource = srv.GetTempletClassDict().Tables[0];

            dgvTemple.FocusedRowChanged += dgvTemple_FocusedRowChanged;

            if (FocuseIndex <= dgvTemple.DataRowCount - 1)
            {
                dgvTemple.MoveBy(FocuseIndex);
                dgvTemple_FocusedRowChanged(null, null);
            }
            else
            {
                dgvTemple.MoveBy(dgvTemple.DataRowCount - 1);
            }
            //如果表中没有相关配置则隐藏此列
            if (!dsTemplet.Tables[0].Columns.Contains("SHOW_MENU"))
            {
                gcShowMenu.Visible = false;
            }
        }

        private void OpenFile()
        {
            DataRow dr = dgvTemple.GetFocusedDataRow();
            if (dr == null) return;

            //txtSql.EditValue = GetSql(dr["dict_name"].ToString(), getParams());
            txtSql.EditValue = dr["templet_sql"];
            selectTempName = dr["dict_name"].ToString();
        }



        #region 获取参数
        /// <summary>
        /// 获取参数
        /// </summary>
        /// <returns></returns>
        private Hashtable getParams()
        {
            DataRow drow = dgvTemple.GetFocusedDataRow();
            //if (drow == null) return;

            // 向SQL语句传递参数
            Hashtable hasParam = new Hashtable();
            DataSet ds = srv.GetReportParamNew(appName,drow["dict_id"].ToString());
            if (ds != null && ds.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    if (dr["EDIT_TYPE"].ToString().Equals("1") || dr["EDIT_TYPE"].ToString().Equals("2"))
                        hasParam.Add(dr["PARAM_NAME"].ToString(), "0");
                    else if (dr["EDIT_TYPE"].ToString().Equals("3"))
                        hasParam.Add(dr["PARAM_NAME"].ToString(), DateTime.Now);
                    else
                        hasParam.Add(dr["PARAM_NAME"].ToString(), 0);
                }
            }
            //hasParam.Add("WARD_NAME", "0");
            //hasParam.Add("WARD_CODE", "0");
            //hasParam.Add("DATE_BEGIN", DateTime.Now);
            //hasParam.Add("DATE_END", DateTime.Now);
            //hasParam.Add("USER_NAME", "0");
            //hasParam.Add("NURSE_ID", "0");
            //hasParam.Add("NURSE_NAME", "0");
            return hasParam;
        }
        #endregion

        private void btnTest_Click(object sender, EventArgs e)
        {
            try
            {
                //btnSaveSql_Click(null,null);
                DataRow dr = dgvTemple.GetFocusedDataRow();
                if (dr == null) return;
                string sql = string.Empty;
                if (txtSql.EditValue != null)
                {
                    sql = txtSql.EditValue.ToString();
                }
                if (sql.EndsWith(";"))
                {
                    sql = sql.Substring(0, sql.Length - 1);
                }              
                DataSet ds = srv.TestSQLNew(sql, getParams());//XtraReportHelper.GetPrintData(dr["dict_name"].ToString(), getParams());
                if (ds != null)
                {
                    XtraMessageBox.Show("测试成功！", "提示");
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message, "提示");
            }
        }
        DataSet dsReprotParam;
        //bool isFirst;
        private void dgvTemple_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            //btnTest.Enabled = false;
            //btnSaveSql.Enabled = false;
            DataRow dr = dgvTemple.GetFocusedDataRow();

            if (dr == null) return;

            FocuseIndex = dgvTemple.GetSelectedRows()[0];

            string roleType = dr["role_type"].ToString();
            txtRole.Text = dr["role_users"].ToString();
            bool isRole = GetRole();



            if (string.IsNullOrEmpty(roleType))
            {
                roleType = "1";
                dr["role_type"] = roleType;
            }

            if ("1".Equals(roleType))
            {
                cbPublic.Checked = true;
            }
            else if ("2".Equals(roleType))
            {
                cbPrivate.Checked = true;
            }
            else if ("3".Equals(roleType))
            {
                cbSet.Checked = true;
            }

            OpenFile();

            if (!isRole)
            {
                groupReportParam.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;

                txtSql.Text = "";
                //XtraMessageBox.Show("没有权限修改！");
            }
            else
                groupReportParam.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always;

            btnSaveSql.Enabled = isRole;
            sbtnOpenParamDict.Enabled = isRole;
            sbtnRparamAdd.Enabled = isRole;
            sbtnRparamDel.Enabled = isRole;
            sbtnRparamEdit.Enabled = isRole;
            sbtnRparamSave.Enabled = isRole;
            gcReportParam.Enabled = isRole;



            isRole = isRole && SystemParm.LoginUser.USER_NAME.Equals(dr["create_nurse"]);
            if (!isRole)
            {
                groupReportRole.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;
            }
            else
            {
                groupReportRole.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always;
            }
            cbPrivate.Enabled = isRole;
            cbPublic.Enabled = isRole;
            cbSet.Enabled = isRole;
            txtRole.Enabled = isRole;

            //获取报表参数
            dsReprotParam = srv.GetReportParamNew(appName,dr["dict_id"].ToString());
            gcReportParam.DataSource = dsReprotParam.Tables[0];
        }

        private void sbtnRparamAdd_Click(object sender, EventArgs e)
        {
            try
            {
                DataRow drTemp = dgvTemple.GetFocusedDataRow();
                if (drTemp == null) return;

                dgvReportParam.AddNewRow();
                DataRow dr = dgvReportParam.GetFocusedDataRow();
                DataRowView drv = dgvReportParam.GetFocusedRow() as DataRowView;
                DataTable dt1 = gcReportParam.DataSource as DataTable;
                frmParamEdit fe = new frmParamEdit(dr, dt1);
                DialogResult result = fe.ShowDialog();

                if (result == DialogResult.OK)
                {
                    SaveReportParam();
                }
                else
                {
                    if (drv != null)
                    {
                        drv.Delete();
                        dsReprotParam.AcceptChanges();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "错误提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void dgvReportParam_InitNewRow(object sender, DevExpress.XtraGrid.Views.Grid.InitNewRowEventArgs e)
        {
            DataRow dr = dgvReportParam.GetDataRow(e.RowHandle);
            dr["HOSPITAL_CODE"] = hospitalCode;
            dr["APP_NAME"] = appName;
            dr["REPORT_ID"] = dgvTemple.GetFocusedDataRow()?["dict_id"]; //DateTime.Now.ToString("yyyyMMddHHmmssfff");
            dr["PARAM_NAME"] = "";
        }

        private void sbtnRparamDel_Click(object sender, EventArgs e)
        {
            dgvReportParam.DeleteSelectedRows();
        }
        private void sbtnRparamEdit_Click(object sender, EventArgs e)
        {
            DataRow dr = dgvReportParam.GetFocusedDataRow();
            if (dr == null) return;

            ShowEditForm(dr);
        }
        private void sbtnRparamSave_Click(object sender, EventArgs e)
        {
            SaveReportParam();
            XtraMessageBox.Show("应用参数成功", "提示");
        }

        private void SaveReportParam()
        {
            dgvReportParam.CloseEditor();
            dgvReportParam.UpdateCurrentRow();
            if (dsReprotParam.HasChanges())
            {
                srv.SaveData(dsReprotParam);
                dsReprotParam.AcceptChanges();
            }
        }

        /// <summary>
        /// 获取模板的SQL语句
        /// </summary>
        /// <param name="templetName"></param>
        /// <param name="hasParam"></param>
        /// <returns></returns>
        private static string GetSql(string templetName, Hashtable hasParam)
        {
            bool isCreate = false;
            string sql = string.Empty;
            string name = templetName;
            templetName = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\" + templetName + ".SQL");
            if (File.Exists(templetName) == false)
            {
                try
                {
                    //File.Create(templetName);
                    string sql1 = @"select 0 from dual a
                            where (a.ward_code = {WARD_CODE} or '*' = {WARD_CODE}) 
                             and   a.date >= {DATE_BEGIN}
                             and   a.date <= {DATE_END}";
                    byte[] myByte = System.Text.Encoding.Default.GetBytes(sql1);
                    using (FileStream fsWrite = new FileStream(templetName, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.ReadWrite))
                    {
                        fsWrite.Write(myByte, 0, myByte.Length);
                        fsWrite.Close();
                    };
                    //isCreate = true;
                }
                catch { }
            }
            string repxPath = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\" + name + ".repx");
            string repxModelPath = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\模板.repx");
            if (File.Exists(repxPath) == false)
            {
                File.Copy(repxModelPath, repxPath);
            }
            if (!isCreate)
                if (File.Exists(templetName) == true)
                {
                    StreamReader sr = new StreamReader(templetName, System.Text.Encoding.Default);

                    try
                    {
                        sql = sr.ReadToEnd();
                    }
                    finally
                    {
                        sr.Close();
                    }
                }
                else
                {
                    sql = string.Empty;
                }

            string[] arrParts = sql.Split(';');

            if (string.IsNullOrEmpty(sql) || arrParts.Length == 0 || string.IsNullOrEmpty(arrParts[0]))
            {
                sql += @"select 0 from dual a
                            where (a.ward_code = {WARD_CODE} or '*' = {WARD_CODE}) 
                             and   a.date >= {DATE_BEGIN}
                             and   a.date <= {DATE_END}";
            }

            if (arrParts.Length >= 2)
            {
                sql = arrParts[1];
            }

            return sql;
        }

        private void btnSaveSql_Click(object sender, EventArgs e)
        {
            DataRow dr = dgvTemple.GetFocusedDataRow();
            if (dr == null) return;
            string sqlStr = txtSql.Text;
            if (sqlStr.EndsWith(";"))
            {
                sqlStr = sqlStr.Substring(0, sqlStr.Length - 1);
            }
            if (!dr["templet_sql"].ToString().Equals(sqlStr))
                dr["templet_sql"] = sqlStr;


            string repxPath = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\" + dr["dict_name"] + ".repx");
            string repxNewPath = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\" + dr["dict_name"] + "_" + dr["dict_id"] + ".repx");
            string repxModelPath = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\模板.repx");
            if (File.Exists(repxNewPath) == false)
            {
                if (File.Exists(repxPath))
                {
                    File.Move(repxPath, repxNewPath);
                }
                else
                {
                    File.Copy(repxModelPath, repxNewPath);
                }
            }

            dgvTemple.CloseEditor();
            dgvTemple.UpdateCurrentRow();
            if (dsTemplet.HasChanges())
            {
                srv.SaveData(dsTemplet);
                BindingTemple();
            }

            XtraMessageBox.Show("保存成功！", "提示");

        }
        /// <summary>
        /// 新增按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            dgvTemple.AddNewRow();
            dgvTemple.CloseEditor();
            dgvTemple.UpdateCurrentRow();
            if (dsTemplet.HasChanges())
            {
                srv.SaveData(dsTemplet);
                if (dsTemplet.Tables[0].Rows.Count == 1)
                    BindingTemple();
            }

        }


        private void dgvTemple_InitNewRow(object sender, DevExpress.XtraGrid.Views.Grid.InitNewRowEventArgs e)
        {
            DataRow dr = dgvTemple.GetDataRow(e.RowHandle);//GetFocusedDataRow();
            dr["HOSPITAL_CODE"] = hospitalCode;
            dr["APP_NAME"] = appName;
            dr["DICT_ID"] = DateTime.Now.ToString("yyyyMMddHHmmssfff");
            try
            {
                dr["CREATE_NURSE"] = SystemParm.LoginUser.USER_NAME;
            }
            catch
            {
                dr["CREATE_NURSE"] = "admin";
            }

            dr["CREATE_DATE"] = DateTime.Now;
            dr["ROLE_TYPE"] = "1";
            dr["DICT_NAME"] = "(未命名)";

            //取当前最大序号
            int sortNo = 0;
            int maxSortNo = 0;
            for (int i = 0; i < dgvTemple.RowCount; i++)
            {
                if (dgvTemple.GetRowCellValue(i, gcSe) == null) continue;
                sortNo = dgvTemple.GetRowCellValue(i, gcSe).ToInt();
                if (sortNo > maxSortNo)
                {
                    maxSortNo = sortNo;
                }
            }
            maxSortNo = maxSortNo + 1;
            dr["SERIAL_NO"] = maxSortNo; //序号

            dgvTemple.RefreshData();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            dgvTemple.CloseEditor();
            dgvTemple.UpdateCurrentRow();

            if (dsTemplet.HasChanges())
            {
                srv.SaveData(dsTemplet);
                BindingTemple();
            }

            XtraMessageBox.Show("保存成功！", "提示");
        }

        private bool GetRole(DataRow dr = null)
        {
            if (dr == null)
                dr = dgvTemple.GetFocusedDataRow();

            string roleType = dr["role_type"].ToString();
            string roles = dr["role_users"].ToString();
            if (string.IsNullOrEmpty(roleType) || roleType.Equals("1"))
            {
                return true;
            }
            else if ("2".Equals(roleType))
            {
                if (SystemParm.LoginUser.USER_NAME.Equals(dr["create_nurse"]))
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else if ("3".Equals(roleType))
            {
                string[] roleArray = roles.Split(',');
                if (SystemParm.LoginUser.USER_NAME.Equals(dr["create_nurse"].ToString()))
                {
                    return true;
                }
                else
                {
                    string len = roleArray.ToList().Find(str => str.Equals(SystemParm.LoginUser.USER_NAME));//暂定只授权人员
                    if (len != null && len.Length > 0)
                        return true;
                    else
                        return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 获取SQL语句
        /// </summary>
        /// <returns></returns>
        private static string GetSqlText(string sql, Hashtable hasParams)
        {
            // 获取SQL原文
            string sqlText = sql;

            // 替换参数
            if (hasParams == null) return sqlText;

            string paramName = string.Empty;
            string paramValue = string.Empty;

            string paramName0 = string.Empty;
            string paramValue0 = string.Empty;

            foreach (DictionaryEntry entry in hasParams)
            {
                // 参数名
                paramName = "{" + entry.Key.ToString().ToUpper() + "}";         // 这种情况 变成 ''
                paramName0 = "[" + entry.Key.ToString().ToUpper() + "]";        // 这种情况 变成 原样

                // 参数值
                if (entry.Value == null)
                {
                    paramValue = "NULL";
                }
                else if (entry.Value.GetType().Equals(typeof(DateTime)))
                {
                    paramValue = SQL.GetOraDate((DateTime)(entry.Value));
                }
                else if (entry.Value.GetType().Equals(typeof(int)) || entry.Value.GetType().Equals(typeof(float)) || entry.Value.GetType().Equals(typeof(Decimal)))
                {
                    paramValue = entry.Value.ToString();
                }
                else if (entry.Value.ToString().Contains("'"))
                {
                    paramValue = entry.Value.ToString();
                }
                else
                {
                    paramValue = SQL.SqlConvert(entry.Value.ToString());
                }

                paramValue0 = entry.Value == null ? "" : entry.Value.ToString();

                // 替换参数
                if (sqlText.IndexOf(paramName) >= 0)
                {
                    sqlText = sqlText.Replace(paramName, paramValue);
                }
                else if (sqlText.IndexOf(paramName0) >= 0)
                {
                    sqlText = sqlText.Replace(paramName0, paramValue0);
                }
            }

            return sqlText;
        }

        private void txtSql_Enter(object sender, EventArgs e)
        {
            //OpenFile();
            //btnTest.Enabled = true;
            //btnSaveSql.Enabled = true;
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            DataRow dr = dgvTemple.GetFocusedDataRow();
            if (dr == null) return;

            DialogResult result = XtraMessageBox.Show("确认要删除报表[" + dr["dict_name"] + "]吗？", "提示", MessageBoxButtons.OKCancel);
            if (result == DialogResult.OK)
            {
                //删除报表模板文件
                string repxNewPath = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\" + dr["dict_name"] + "_" + dr["dict_id"] + ".repx");
                if (File.Exists(repxNewPath))
                {
                    File.Delete(repxNewPath);
                }
                //删除报表关联的参数
                srv.DeleteReportParam(dr["dict_id"].ToString());
                srv.DeleteReportByID(dr["dict_id"].ToString());
                //删除行并保存数据
                dgvTemple.DeleteSelectedRows();
                dgvTemple.CloseEditor();
                dgvTemple.UpdateCurrentRow();
                dsTemplet.AcceptChanges();
                //srv.SaveData(dsTemplet);

                dgvTemple_FocusedRowChanged(null, null);
            }
        }

        private void sBtnTempClass_Click(object sender, EventArgs e)
        {
            frmStatisticalClassDict frm = new frmStatisticalClassDict();
            frm.ShowDialog();

            //分类窗体关闭后重新绑定分类数据源
            repLupTempletClass.DataSource = srv.GetTempletClassDict().Tables[0];
        }

        string roleStr = string.Empty;
        private void cbSet_CheckedChanged(object sender, EventArgs e)
        {
            if (cbSet.Checked)
            {
                roleStr = "3";
                txtRole.Enabled = true;
                cbPublic.Checked = false;
                cbPrivate.Checked = false;
                SetTempRole(roleStr);
            }

        }

        private void cbPrivate_CheckedChanged(object sender, EventArgs e)
        {
            if (cbPrivate.Checked)
            {
                roleStr = "2";
                txtRole.Enabled = false;
                cbPublic.Checked = false;
                cbSet.Checked = false;
                SetTempRole(roleStr);
            }

        }

        private void cbPublic_CheckedChanged(object sender, EventArgs e)
        {

            if (cbPublic.Checked)
            {
                roleStr = "1";
                txtRole.Enabled = false;
                cbSet.Checked = false;
                cbPrivate.Checked = false;
                SetTempRole(roleStr);
            }
        }

        private void SetTempRole(string roleStr)
        {
            DataRow dr = dgvTemple.GetFocusedDataRow();
            if (dr != null)
            {
                dr["role_type"] = roleStr;
            }
        }

        private void txtRole_EditValueChanging(object sender, DevExpress.XtraEditors.Controls.ChangingEventArgs e)
        {
            DataRow dr = dgvTemple.GetFocusedDataRow();
            if (dr != null)
            {
                dr["role_users"] = e.NewValue;
            }
        }
 
        private void dgvTemple_ShowingEditor(object sender, System.ComponentModel.CancelEventArgs e)
        {
            e.Cancel = !GetRole();
        }
        /// <summary>
        /// 点击按钮选择        
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>        
        private void txtRole_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            frmStaffDict fsd = new frmStaffDict();
            fsd.usersArray = txtRole.Text;
            DialogResult d = fsd.ShowDialog();
            if (d == DialogResult.OK)
            {
                txtRole.Text = fsd.usersArray;
            }

        }
        /// <summary>
        /// 双击选择人员
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void txtRole_DoubleClick(object sender, EventArgs e)
        {
            txtRole_ButtonClick(this.txtRole, null);
        }

        private void sbtnReportParam_Click(object sender, EventArgs e)
        {
            frmParamDict fp = new frmParamDict();
            fp.ShowDialog();
        }

        string selectTempName;
        private void dgvTemple_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (e.Column == gcName)
            {
                DataRow dr = dgvTemple.GetDataRow(e.RowHandle);
                if (string.IsNullOrEmpty(dr["dict_name"].ToString()))
                {
                    dr["dict_name"] = selectTempName;
                    return;
                }
                string repxPath = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\" + selectTempName + "_" + dr["dict_id"] + ".repx");
                string repxNewPath = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\" + dr["dict_name"] + "_" + dr["dict_id"] + ".repx");
                if (File.Exists(repxPath))
                {
                    File.Move(repxPath, repxNewPath);

                }
                else
                {
                    string repxModelPath = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\模板.repx");
                    if (File.Exists(repxPath) == false)
                    {
                        File.Copy(repxModelPath, repxPath);
                    }
                }

                selectTempName = dr["dict_name"].ToString();
            }
        }

        private void btnCopyCreate_Click(object sender, EventArgs e)
        {
            bool isRole = GetRole();
            if (isRole)
            {
                DataRow dr = dgvTemple.GetFocusedDataRow();
                if (dr == null)
                {
                    XtraMessageBox.Show("没有选择报表!", "系统提示");
                    return;
                }
                DataRow descDr = dsTemplet.Tables[0].NewRow();
                descDr.ItemArray = dr.ItemArray;
                btnAdd_Click(null, null);
                DataRow newDr = dgvTemple.GetFocusedDataRow();
                string dictName = string.Empty;
                bool isContains = false;
                for (int i = 0; ; i++)
                {
                    isContains = dsTemplet.Tables[0].Select($"dict_name = '{descDr["dict_name"]}_{i.ToString()}'").Length > 0;
                    if (isContains)
                    {
                        continue;
                    }
                    else
                    {
                        dictName = $"{descDr["dict_name"]}_{i.ToString()}";
                        break;
                    }
                }
                descDr["dict_id"] = newDr["dict_id"];
                descDr["dict_name"] = dictName;//descDr["dict_name"] + "_1";
                descDr["CREATE_NURSE"] = SystemParm.LoginUser.USER_NAME;
                descDr["ROLE_TYPE"] = "1";
                descDr["ROLE_USERS"] = "";
                descDr["CREATE_DATE"] = DateTime.Now;

                newDr.ItemArray = descDr.ItemArray;

                string repxPath = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\" + dr["dict_name"] + "_" + dr["dict_id"] + ".repx");
                string repxNewPath = Path.Combine(Cs01FileHelper.GetCurrDllPath(), @"Reports\" + newDr["dict_name"] + "_" + newDr["dict_id"] + ".repx");
                if (File.Exists(repxPath))
                {
                    File.Copy(repxPath, repxNewPath);
                }

                ///复制报表参数
                DataSet dsParam = srv.GetReportParamNew(appName,dr["dict_id"].ToString());
                DataTable dtParam = dsParam.Tables[0].Copy();
                foreach (DataRow eachRow in dtParam.Rows)
                {
                    DataRow newParam = dsParam.Tables[0].NewRow();
                    eachRow["REPORT_ID"] = newDr["dict_id"];
                    newParam.ItemArray = eachRow.ItemArray;

                    dsParam.Tables[0].Rows.Add(newParam);
                }
                if (dsParam.HasChanges())
                {
                    srv.SaveData(dsParam);
                    dsParam.AcceptChanges();
                }

                dgvTemple_FocusedRowChanged(null, null);

                btnSave_Click(null, null);
            }
            else
            {
                XtraMessageBox.Show("没有权限复制此报表！");
            }
        }

        private void dgvReportParam_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            if (e.Clicks == 2)
            {
                DataRow dr = dgvReportParam.GetFocusedDataRow();
                ShowEditForm(dr);
            }
        }

        void ShowEditForm(DataRow dr)
        {
            //DataTable dt1 = gcReportParam.DataSource as DataTable;
            frmParamEdit fe = new frmParamEdit(dr);
            DialogResult result = fe.ShowDialog();

            if (result == DialogResult.OK)
            {
                SaveReportParam();
            }
        }

        private void sbtnOpenParamDict_Click(object sender, EventArgs e)
        {
            DataRow dr = dgvTemple.GetFocusedDataRow();

            frmSelectedParam fsp = new frmSelectedParam();
            //fsp.paramRow = dsReprotParam.Tables[0].Select();
            DialogResult result = fsp.ShowDialog();

            if (result == DialogResult.OK)
            {
                if (dr == null) return;

                if (fsp.paramRow != null)
                {
                    foreach (DataRow eachParam in fsp.paramRow)
                    {
                        DataRow drp = dsReprotParam.Tables[0].NewRow();
                        drp.ItemArray = eachParam.ItemArray;
                        drp["report_id"] = dr["dict_id"];
                        try
                        {
                            DataRow[] selParms = dsReprotParam.Tables[0].Select("report_id = '" + drp["report_id"] + "' and param_name like'" + drp["param_name"] + "%'");
                            if (selParms.Length > 0)
                            {
                                int no = 0;
                                foreach (DataRow eachRow in selParms)
                                {
                                    string[] nameSplit = eachRow["param_name"].ToString().Split('_');
                                    if (nameSplit.Length > 1)
                                    {
                                        int eacheNo = 0;
                                        bool isOk = int.TryParse(nameSplit[nameSplit.Length - 1], out eacheNo);
                                        if (isOk)
                                        {
                                            if (eacheNo > no)
                                            {
                                                no = eacheNo;
                                            }
                                        }
                                    }
                                }
                                drp["param_name"] = drp["param_name"] + "_" + (no + 1).ToString();
                            }
                            dsReprotParam.Tables[0].Rows.Add(drp);
                        }
                        catch
                        {
                            XtraMessageBox.Show("参数已存在，请编辑后再添加！", "系统提示");
                        }
                    }
                }

            }
        }
        int groupReportParamWidth;
        private void groupReportParam_CustomButtonClick(object sender, DevExpress.XtraBars.Docking2010.BaseButtonEventArgs e)
        {
            if (e.Button == groupReportParam.CustomHeaderButtons[0])
            {
                if (groupReportParam.Width > 15)
                {
                    groupReportParamWidth = groupReportParam.Width;
                    groupReportParam.Width = 15;
                }
                else
                    groupReportParam.Width = groupReportParamWidth;
            }
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            DataRow dr = dgvTemple.GetFocusedDataRow();
            if (dr == null) return;

            string id = dr["dict_id"].ToString();
            //frmCreateSQL frmSql = new frmCreateSQL();
            frmEditField frm = new frmEditField();
            frm.dictID = id;

            try
            {
                string sql = string.Empty;
                if (txtSql.EditValue != null)
                {
                    sql = txtSql.EditValue.ToString();
                }
                if (sql.EndsWith(";"))
                {
                    sql = sql.Substring(0, sql.Length - 1);
                }
                if (string.IsNullOrEmpty(sql))
                {
                    XtraMessageBox.Show("请先编写SQL，再绑定字段！", "提示");
                    return;
                }
                DataSet ds = srv.TestSQL(sql, getParams());//XtraReportHelper.GetPrintData(dr["dict_name"].ToString(), getParams());
                if (ds != null)
                {
                    frm.dataSourcesStruct = ds.Clone();
                }
                else
                {
                    XtraMessageBox.Show("请先编写SQL，再绑定字段！", "提示");
                    return;
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message, "提示");
            }
            frm.ShowDialog();
        }

        /// <summary>
        /// 将报表添加至报表中心某一系报表中
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void sbtnAddTo_Click(object sender, EventArgs e)
        {
            DataRow dr = dgvTemple.GetFocusedDataRow();
            if (dr == null) return;

            string id = dr["dict_id"].ToString();
            string name = dr["dict_name"].ToString();

            frmInsertReportToMenu fir = new frmInsertReportToMenu();
            fir.ReportID = id;
            fir.ReportName = name;

            fir.ShowDialog();
        }

        int FocuseIndex;
        private void dgvTemple_SelectionChanged(object sender, DevExpress.Data.SelectionChangedEventArgs e)
        {
            FocuseIndex = dgvTemple.GetFocusedDataSourceRowIndex();
        }

        private void sBtnExportXml_Click(object sender, EventArgs e)
        {
            DataRow fdr = dgvTemple.GetFocusedDataRow();
            if (fdr == null) return;
            string repName = fdr["dict_name"].ToString();
            string repID = fdr["dict_id"].ToString();

            SaveFileDialog sd = new SaveFileDialog();
            sd.Filter = "DataSet数据xml|*.xml";
            sd.InitialDirectory = $@"{ Application.StartupPath}\reports\";
            sd.FileName = "" + repName;
            sd.DefaultExt = ".xml";

            if (sd.ShowDialog() == DialogResult.OK)
            {
                DataSet saveDs = srv.GetReport(repID, Const.customAppCode);
                if (saveDs == null || saveDs.Tables[0].Rows.Count == 0) return;

                string path = sd.FileName; //$@"{ Application.StartupPath}\reports\{txtReportName.EditValue}.xml";
                try
                {
                    System.Xml.XmlDocument xdoc = new System.Xml.XmlDocument();

                    System.Xml.XmlElement dsElement = xdoc.CreateElement(saveDs.DataSetName);
                    foreach (DataTable dt in saveDs.Tables)
                    {
                        foreach (DataRow dr in dt.Rows)
                        {

                            System.Xml.XmlElement dtElement = xdoc.CreateElement(dt.TableName);
                            foreach (DataColumn dc in dt.Columns)
                            {
                                System.Xml.XmlElement dcElement = xdoc.CreateElement(dc.ColumnName);
                                dcElement.InnerText = dr[dc.ColumnName].ToString();
                                dcElement.Prefix = dc.Prefix;
                                dtElement.AppendChild(dcElement);
                            }
                            dsElement.AppendChild(dtElement);
                        }
                    }
                    xdoc.AppendChild(dsElement);

                    xdoc.Save(path);

                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
        }

        private void repositoryItemLookUpEdit1_EditValueChanged(object sender, EventArgs e)
        {


        }

        private void dgvTemple_CustomRowCellEdit(object sender, DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs e)
        {
        }

        private void BoundDiffRepositoryItem()
        {
            dgvTemple.CustomRowCellEditForEditing += (x, y) =>
            {
                if (y.Column.Name != "gcClass")
                    return;
                //DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
                DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repositoryItem = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
                y.RepositoryItem = repositoryItem;
                string s = Convert.ToString(dgvTemple.GetFocusedDataRow()["APP_NAME"]).Trim();
                if (s == null)
                    return;
                repositoryItem.DisplayMember = "CLASS_NAME";
                repositoryItem.ValueMember = "CLASS_ID";
                repositoryItem.DataSource = srv.GetTempletClassDictByAppName(s).Tables[0];
                repositoryItem.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
                //////从列表中选择项后，绑定一个选值改变事件，以便于把列表中的其他列的值赋与gridView2中需要的列
                //repositoryItem.EditValueChanged += (m, n) =>
                //{
                //    DataRow dr = dgvTemple.GetFocusedDataRow();
                //    var materiaObject = (m as DevExpress.XtraEditors.LookUpEdit).GetSelectedDataRow();
                //    //dr["CLASS_NAME"] = materiaObject.GetType().GetProperty("cpmc").GetValue(materiaObject);
                //};

            };
        }
    }
}
