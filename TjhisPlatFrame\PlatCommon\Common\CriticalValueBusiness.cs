﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.IO;
using PlatCommon.SysBase;

namespace PlatCommon.Common
{
    public class CriticalValueBusiness
    {
        #region 私有变量
        private static Process _startUpPro;
        private static Process _shutDownPro;
        private static string _criticalSwitch = string.Empty;       //开关
        private static string _startUpPath = "C:\\Program Files\\Spark\\Spark.exe";         //开启危急值IM工具，执行exe文件+参数模式
        private static string _shutDownPath = Application.StartupPath + "\\close_tjim.vbs"; //关闭危急值IM工具，执行bat文件模式
        private static string _password = Utility.Gloobal.DecryptHIS(PlatCommon.SysBase.SystemParm.LoginUser.PASSWORD);
        #endregion

        #region 私有方法
        private static bool Init()
        {
            bool b = false;
            //取开关，危急值系统是否随着his系统启动和关闭，1是 0否
            _criticalSwitch = PlatCommon.SysBase.SystemParm.GetParameterValue("CRITICAL_VALUE_SWITCH", "*", "*", "*", SystemParm.HisUnitCode);
            if (_criticalSwitch.Equals("1"))
            {
                b = true;
            }
            return b;
        }

        private static void StartUp()
        {
            if (File.Exists(_startUpPath))
            {
                _startUpPro = new Process();
                _startUpPro.StartInfo.FileName = _startUpPath;
                //参数示例 username=YUANF&password=明文密码&server=192.168.0.74
                _startUpPro.StartInfo.Arguments = "username=" + PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME + "&password=" + _password + "&server=192.168.0.74"; //传参数
                _startUpPro.Start();
                //_startUpPro.WaitForExit();  //等待程序执行完退出进程
                //_startUpPro.Close();
            }
        }

        private static void ShutDown()
        {
            if (File.Exists(_shutDownPath))
            {
                _shutDownPro = new Process();
                _shutDownPro.StartInfo.FileName = _shutDownPath;
                _shutDownPro.Start();
                _shutDownPro.WaitForExit(); //等待程序执行完退出进程
                _shutDownPro.Close();
            }
        }
        #endregion

        #region 公共业务方法
        public static void DoBusiness(string type)
        {
            if (Init()) //开关
            {
                switch (type)
                {
                    case "1": //开启危急值IM工具
                        StartUp();
                        break;
                    case "0": //关闭危急值IM工具
                        ShutDown();
                        break;
                }
            }
        }
        #endregion
    }
}

