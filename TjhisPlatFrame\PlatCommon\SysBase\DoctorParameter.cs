﻿//住院医生站系统参数
using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Collections;
using System.Reflection;
using PlatCommon.Common;
using Model;
using NM_Service.NMService;
using DevExpress.XtraEditors;

namespace PlatCommon.SysBase
{
    public static class DoctorParameter
    {
        #region 初始化参数
        public static void Init()
        {
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            //His01dtParameter dbHelper = new His01dtParameter();
            //Cs02Controls.OpenWait("初始化系统参数...", "系统启动");
            //DataSet ds = HisdtParameterSrv.GetParameterUpRelationTable(dbHelper);
            string sqla = "select * from app_configer_baseinfo where  APP_NAME='*' OR APP_NAME='DOCTWS' OR APP_NAME='CP' ";
            DataTable dtBaseinfo = spc.GetDataBySql(sqla).Tables[0];
            string sqlb = "select * from app_configer_parameter a where a.his_unit_code ='"+PlatCommon.SysBase.SystemParm.HisUnitCode+ "' and ( APP_NAME='*' OR APP_NAME='DOCTWS' OR APP_NAME='CP' )";
            DataTable dtParameter = spc.GetDataBySql(sqlb).Tables[0];
            DataSet ds = new DataSet();
            dtParameter.TableName = "APP_CONFIGER_PARAMETER";
            ds.Tables.Add(dtParameter.Copy());
            dtBaseinfo.TableName = "APP_CONFIGER_BASEINFO";
            ds.Tables.Add(dtBaseinfo.Copy());
            #region 平台
            #region 是否启用天健平台功能 0-不启用,1-启用
            if (!JudgeParam(dtParameter, "USE_SEND_PLATFORM"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "USE_SEND_PLATFORM", "0");//默认不启用平台
                if (!JudgeParamBaseinfo(dtBaseinfo, "USE_SEND_PLATFORM"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "USE_SEND_PLATFORM", "0", "0,1", "是否启用天健平台功能 0-不启用,1-启用", "天健平台");
                }
            }
            #endregion 
            #region 危机值
            #region 是否启用危急值功能， 0-不启用,1-启用
            if (!JudgeParam(dtParameter, "USE_CRITICAL_VALUE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "USE_CRITICAL_VALUE", "0");//默认不启用危急值
                if (!JudgeParamBaseinfo(dtBaseinfo, "USE_CRITICAL_VALUE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "USE_CRITICAL_VALUE", "0", "0,1", "是否启用危急值功能， 0-不启用,1-启用", "危机值");
                }
            }
            #endregion
            #region 危急值刷新时间秒数
            if (!JudgeParam(dtParameter, "CRITICAL_RESULT_ALARM_TIME"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "CRITICAL_RESULT_ALARM_TIME", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "CRITICAL_RESULT_ALARM_TIME"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "CRITICAL_RESULT_ALARM_TIME", "120", "120", "危急值刷新时间秒数", "危机值");
                }
            }
            #endregion
            #region 天健平台--危机值处理结果通知服务功能号ID
            //if (!JudgeParam(dtParameter,"PLATFORM_CV_MSGTYPE"))
            //{
            //    InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_CV_MSGTYPE", "TJ608");//默认TJ608
            //    if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_CV_MSGTYPE"))
            //    {
            //        InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_CV_MSGTYPE", "TJ608", "", "天健平台--危机值处理结果通知服务功能号ID", "天健平台");
            //    }
            //}
            #endregion
            #region 天健平台--危机值处理消息源系统域标识
            //if (!JudgeParam(dtParameter,"PLATFORM_CV_SOURCEID"))
            //{
            //    InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_CV_SOURCEID", "1.3.6.1.4.1.1000000.2016.100");//默认1.3.6.1.4.1.1000000.2016.100
            //    if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_CV_SOURCEID"))
            //    {
            //        InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_CV_SOURCEID", "1.3.6.1.4.1.1000000.2016.100", "", "天健平台--危机值处理消息源系统域标识", "天健平台");
            //    }
            //}
            #endregion
            #region 天健平台--危机值处理消息目标系统域标识
            //if (!JudgeParam(dtParameter,"PLATFORM_CV_TARGETID"))
            //{
            //    InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_CV_TARGETID", "1.3.6.1.4.1.1000000.2016.xxx");//默认1.3.6.1.4.1.1000000.2016.xxx
            //    if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_CV_TARGETID"))
            //    {
            //        InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_CV_TARGETID", "1.3.6.1.4.1.1000000.2016.xxx", "", "天健平台--危机值处理消息目标系统域标识", "天健平台");
            //    }
            //}
            #endregion
            #region 天健平台--危机值处理系统密码
            //if (!JudgeParam(dtParameter,"PLATFORM_CV_SYSPASSWORD"))
            //{
            //    InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_CV_SYSPASSWORD", "");
            //    if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_CV_SYSPASSWORD"))
            //    {
            //        InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_CV_SYSPASSWORD", "", "", "天健平台--危机值处理系统密码", "天健平台");
            //    }
            //}
            #endregion

            #endregion
            #region 平台--检查申请
            #region 天健平台--检查申请功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_EXAM_ADD_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_EXAM_ADD_MSGTYPE", "TJ001");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_EXAM_ADD_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_EXAM_ADD_MSGTYPE", "TJ001", "TJ001", "天健平台--检查申请功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--检查撤销功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_EXAM_DEL_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_EXAM_DEL_MSGTYPE", "TJ003");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_EXAM_DEL_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_EXAM_DEL_MSGTYPE", "TJ003", "TJ003", "天健平台--检查申请功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院检查申请操作类型--开立
            if (!JudgeParam(dtParameter, "PLATFORM_INP_EXAM_ACTIONTYPE_ADD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_INP_EXAM_ACTIONTYPE_ADD", "D1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_INP_EXAM_ACTIONTYPE_ADD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_INP_EXAM_ACTIONTYPE_ADD", "D1", "D1", "天健平台--住院检查申请操作类型--开立", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院检查申请操作类型--作废
            if (!JudgeParam(dtParameter, "PLATFORM_INP_EXAM_ACTIONTYPE_DEL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_INP_EXAM_ACTIONTYPE_DEL", "D29");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_INP_EXAM_ACTIONTYPE_DEL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_INP_EXAM_ACTIONTYPE_DEL", "D29", "D29", "住院检查申请操作类型--作废", "天健平台");
                }
            }
            #endregion
            #endregion
            #region 平台--用血申请
            #region 天健平台--用血申请功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_BLOOD_ADD_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_BLOOD_ADD_MSGTYPE", "TJ051");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_BLOOD_ADD_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_BLOOD_ADD_MSGTYPE", "TJ051", "TJ051", "天健平台--用血申请功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--用血撤销功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_BLOOD_DEL_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_BLOOD_DEL_MSGTYPE", "TJ053");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_BLOOD_DEL_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_BLOOD_DEL_MSGTYPE", "TJ053", "TJ053", "天健平台--用血撤销功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院用血申请操作类型--开立
            if (!JudgeParam(dtParameter, "PLATFORM_INP_BLOOD_ACTIONTYPE_ADD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_INP_BLOOD_ACTIONTYPE_ADD", "B1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_INP_BLOOD_ACTIONTYPE_ADD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_INP_BLOOD_ACTIONTYPE_ADD", "B1", "B1", "天健平台--住院用血申请操作类型--开立", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院用血申请操作类型--作废
            if (!JudgeParam(dtParameter, "PLATFORM_INP_BLOOD_ACTIONTYPE_DEL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_INP_BLOOD_ACTIONTYPE_DEL", "B2");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_INP_BLOOD_ACTIONTYPE_DEL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_INP_BLOOD_ACTIONTYPE_DEL", "B2", "B2", "天健平台--住院用血申请操作类型--作废", "天健平台");
                }
            }
            #endregion
            #endregion
            #region 平台--检验申请
            #region 天健平台--检验申请功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_LAB_ADD_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_LAB_ADD_MSGTYPE", "TJ021");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_LAB_ADD_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_LAB_ADD_MSGTYPE", "TJ021", "TJ021", "天健平台--检验申请功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--检验撤销功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_LAB_DEL_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_LAB_DEL_MSGTYPE", "TJ023");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_LAB_DEL_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_LAB_DEL_MSGTYPE", "TJ023", "TJ023", "天健平台--检验撤销功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院检验申请操作类型--开立
            if (!JudgeParam(dtParameter, "PLATFORM_INP_LAB_ACTIONTYPE_ADD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_INP_LAB_ACTIONTYPE_ADD", "C1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_INP_LAB_ACTIONTYPE_ADD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_INP_LAB_ACTIONTYPE_ADD", "C1", "C1", "天健平台--住院检验申请操作类型--开立", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院检验申请操作类型--作废
            if (!JudgeParam(dtParameter, "PLATFORM_INP_LAB_ACTIONTYPE_DEL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_INP_LAB_ACTIONTYPE_DEL", "C27");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_INP_LAB_ACTIONTYPE_DEL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_INP_LAB_ACTIONTYPE_DEL", "C27", "C27", "天健平台--住院检验申请操作类型--作废", "天健平台");
                }
            }
            #endregion
            #endregion
            #region 平台--手术申请
            #region 天健平台--手术申请功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_OPER_ADD_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_OPER_ADD_MSGTYPE", "TJ031");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_OPER_ADD_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_OPER_ADD_MSGTYPE", "TJ031", "TJ031", "天健平台--手术申请功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--手术撤销功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_OPER_DEL_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_OPER_DEL_MSGTYPE", "TJ033");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_OPER_DEL_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_OPER_DEL_MSGTYPE", "TJ033", "TJ033", "天健平台--手术撤销功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--手术审核功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_OPER_EXAMINE_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_OPER_EXAMINE_MSGTYPE", "TJ034");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_OPER_EXAMINE_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_OPER_EXAMINE_MSGTYPE", "TJ034", "TJ034", "天健平台--手术审核功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院手术申请操作类型--开立
            if (!JudgeParam(dtParameter, "PLATFORM_INP_OPER_ACTIONTYPE_ADD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_INP_OPER_ACTIONTYPE_ADD", "O1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_INP_OPER_ACTIONTYPE_ADD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_INP_OPER_ACTIONTYPE_ADD", "O1", "O1", "天健平台--住院手术申请操作类型--开立", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院手术申请操作类型--作废
            if (!JudgeParam(dtParameter, "PLATFORM_INP_OPER_ACTIONTYPE_DEL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_INP_OPER_ACTIONTYPE_DEL", "O24");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_INP_OPER_ACTIONTYPE_DEL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_INP_OPER_ACTIONTYPE_DEL", "O24", "O24", "天健平台--住院手术申请操作类型--作废", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院手术申请操作类型--审核
            if (!JudgeParam(dtParameter, "PLATFORM_INP_OPER_ACTIONTYPE_EXAMINE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_INP_OPER_ACTIONTYPE_EXAMINE", "O3");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_INP_OPER_ACTIONTYPE_EXAMINE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_INP_OPER_ACTIONTYPE_EXAMINE", "O3", "O3", "天健平台--住院手术申请操作类型--审核", "天健平台");
                }
            }
            #endregion
            #endregion
            #region 平台--医嘱
            #region 天健平台--医嘱开立功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_ORDERS_ADD_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_ORDERS_ADD_MSGTYPE", "TJ181");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_ORDERS_ADD_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_ORDERS_ADD_MSGTYPE", "TJ181", "TJ181", "天健平台--医嘱开立功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--医嘱停止功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_ORDERS_STOP_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_ORDERS_STOP_MSGTYPE", "TJ183");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_ORDERS_STOP_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_ORDERS_STOP_MSGTYPE", "TJ183", "TJ183", "天健平台--医嘱停止功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--医嘱作废功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_ORDERS_DEL_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_ORDERS_DEL_MSGTYPE", "TJ183");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_ORDERS_DEL_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_ORDERS_DEL_MSGTYPE", "TJ183", "TJ183", "天健平台--医嘱作废功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院医嘱操作类型--开立
            if (!JudgeParam(dtParameter, "PLATFORM_INP_ORDERS_ACTIONTYPE_ADD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_INP_ORDERS_ACTIONTYPE_ADD", "M1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_INP_ORDERS_ACTIONTYPE_ADD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_INP_ORDERS_ACTIONTYPE_ADD", "M1", "M1", "天健平台--住院医嘱操作类型--开立", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院医嘱操作类型--停止
            if (!JudgeParam(dtParameter, "PLATFORM_INP_ORDERS_ACTIONTYPE_STOP"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_INP_ORDERS_ACTIONTYPE_STOP", "M15");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_INP_ORDERS_ACTIONTYPE_STOP"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_INP_ORDERS_ACTIONTYPE_STOP", "M15", "M15", "天健平台--住院医嘱操作类型--停止", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院医嘱操作类型--作废
            if (!JudgeParam(dtParameter, "PLATFORM_INP_ORDERS_ACTIONTYPE_DEL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_INP_ORDERS_ACTIONTYPE_DEL", "M19");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_INP_ORDERS_ACTIONTYPE_DEL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_INP_ORDERS_ACTIONTYPE_DEL", "M19", "M19", "天健平台--住院医嘱操作类型--作废", "天健平台");
                }
            }
            #endregion
            #endregion
            #region 平台--处方
            #region 天健平台--处方开立功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_PRESC_ADD_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_PRESC_ADD_MSGTYPE", "TJ081");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_PRESC_ADD_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_PRESC_ADD_MSGTYPE", "TJ081", "TJ081", "天健平台--处方开立功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--处方作废功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_PRESC_DEL_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_PRESC_DEL_MSGTYPE", "TJ083");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_PRESC_DEL_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_PRESC_DEL_MSGTYPE", "TJ083", "TJ083", "天健平台--处方作废功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院处方操作类型--开立
            if (!JudgeParam(dtParameter, "PLATFORM_INP_PRESC_ACTIONTYPE_ADD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_INP_PRESC_ACTIONTYPE_ADD", "IP1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_INP_PRESC_ACTIONTYPE_ADD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_INP_PRESC_ACTIONTYPE_ADD", "IP1", "IP1", "天健平台--住院处方操作类型--开立", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院处方请操作类型--作废
            if (!JudgeParam(dtParameter, "PLATFORM_INP_PRESC_ACTIONTYPE_DEL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_INP_PRESC_ACTIONTYPE_DEL", "IP14");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_INP_PRESC_ACTIONTYPE_DEL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_INP_PRESC_ACTIONTYPE_DEL", "IP14", "IP14", "天健平台--住院处方请操作类型--作废", "天健平台");
                }
            }
            #endregion
            #endregion
            #region 天健平台--治疗申请
            #region 天健平台--治疗申请开立功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_TREAT_ADD_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_TREAT_ADD_MSGTYPE", "TJ091");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_TREAT_ADD_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_TREAT_ADD_MSGTYPE", "TJ091", "TJ091", "天健平台--治疗申请开立功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--治疗申请作废功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_TREAT_DEL_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_TREAT_DEL_MSGTYPE", "TJ093");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_TREAT_DEL_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_TREAT_DEL_MSGTYPE", "TJ093", "TJ093", "天健平台--治疗申请作废功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--治疗申请操作类型--开立
            if (!JudgeParam(dtParameter, "PLATFORM_INP_TREAT_ACTIONTYPE_ADD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_INP_TREAT_ACTIONTYPE_ADD", "IT1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_INP_TREAT_ACTIONTYPE_ADD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_INP_TREAT_ACTIONTYPE_ADD", "IT1", "IT1", "天健平台--治疗申请操作类型--开立", "天健平台");
                }
            }
            #endregion
            #region 天健平台--治疗申请请操作类型--作废
            if (!JudgeParam(dtParameter, "PLATFORM_INP_TREAT_ACTIONTYPE_DEL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_INP_TREAT_ACTIONTYPE_DEL", "IT10");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_INP_TREAT_ACTIONTYPE_DEL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_INP_TREAT_ACTIONTYPE_DEL", "IT10", "IT10", "天健平台--治疗申请操作类型--作废", "天健平台");
                }
            }
            #endregion
            #endregion
            #region 查询地址 
            #region 天健平台--浏览器访问地址
            if (!JudgeParam(dtParameter, "PLATFORM_BROWSER_ACCESS_PATH"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_BROWSER_ACCESS_PATH", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_BROWSER_ACCESS_PATH"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_BROWSER_ACCESS_PATH", "", "", "天健平台--浏览器程序访问地址", "天健平台");
                }
            }
            #endregion
            #region 天健平台--访问护理地址
            if (!JudgeParam(dtParameter, "PLATFORM_NURSE_URL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_NURSE_URL", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_NURSE_URL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_NURSE_URL", "", "", "天健平台--访问护理地址", "天健平台");
                }
            }
            #endregion
            #region 天健平台--检验报告查询地址
            if (!JudgeParam(dtParameter, "PLATFORM_LAB_REPORT"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_LAB_REPORT", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_LAB_REPORT"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_LAB_REPORT", "", "", "天健平台--检验报告查询地址", "天健平台");
                }
            }
            #endregion
            #region 天健平台--访问患者单独闭环地址
            if (!JudgeParam(dtParameter, "PLATFORM_ALONE_CLOSED_LOOP_URL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_ALONE_CLOSED_LOOP_URL", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_ALONE_CLOSED_LOOP_URL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_ALONE_CLOSED_LOOP_URL", "", "", "天健平台--访问患者单独闭环地址", "天健平台");
                }
            }
            #endregion
            #region 天健平台--访问住院患者全部闭环地址
            if (!JudgeParam(dtParameter, "PLATFORM_ALL_INP_CLOSED_LOOP_URL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_ALL_INP_CLOSED_LOOP_URL", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_ALL_INP_CLOSED_LOOP_URL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_ALL_INP_CLOSED_LOOP_URL", "", "", "天健平台--访问住院患者全部闭环地址", "天健平台");
                }
            }
            #endregion
            #region 天健平台--访问患者全景病历地址
            if (!JudgeParam(dtParameter, "PLATFORM_PANORAMA_MR_URL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_PANORAMA_MR_URL", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_PANORAMA_MR_URL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_PANORAMA_MR_URL", "", "", "天健平台--访问患者全景病历地址", "天健平台");
                }
            }
            #endregion
            #region 天健平台--体检报告地址
            if (!JudgeParam(dtParameter, "PLATFORM_BODYEXAM_REPORT_URL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_BODYEXAM_REPORT_URL", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_BODYEXAM_REPORT_URL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_BODYEXAM_REPORT_URL", "", "", "天健平台--体检报告地址", "天健平台");
                }
            }
            #endregion 
            #endregion
            #region 天健平台--报告中心
            #region 天健平台--报告中心地址
            if (!JudgeParam(dtParameter, "PLATFORM_REPORT_CENTER_URL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_REPORT_CENTER_URL", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_REPORT_CENTER_URL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_REPORT_CENTER_URL", "", "", "天健平台--报告中心地址", "天健平台");
                }
            }
            #endregion
            #region 是否启用报告中心上传
            if (!JudgeParam(dtParameter, "PLATFORM_REPORT_CENTER_UPLOAD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_REPORT_CENTER_UPLOAD", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_REPORT_CENTER_UPLOAD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_REPORT_CENTER_UPLOAD", "0", "0,1", "是否启用报告中心上传:0--不启用;1--启用", "天健平台");
                }
            }
            #endregion
            #region 报告中心上传功能号
            if (!JudgeParam(dtParameter, "PLATFORM_REPORT_CENTER_UPLOAD_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_REPORT_CENTER_UPLOAD_MSGTYPE", "TJ508");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_REPORT_CENTER_UPLOAD_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_REPORT_CENTER_UPLOAD_MSGTYPE", "TJ508", "TJ508", "天健平台--报告中心上传功能号", "天健平台");
                }
            }
            #endregion
            #region 报告中心上传消息源系统域标识
            if (!JudgeParam(dtParameter, "PLATFORM_REPORT_CENTER_UPLOAD_SOURCE_ID"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_REPORT_CENTER_UPLOAD_SOURCE_ID", "1.3.6.1.4.1.1000000.2016.100");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_REPORT_CENTER_UPLOAD_SOURCE_ID"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_REPORT_CENTER_UPLOAD_SOURCE_ID", "", "", "天健平台--报告中心上传消息源系统域标识", "天健平台");
                }
            }
            #endregion
            #region 报告中心上传消息目标系统域标识
            if (!JudgeParam(dtParameter, "PLATFORM_REPORT_CENTER_UPLOAD_TARGET_ID"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_REPORT_CENTER_UPLOAD_TARGET_ID", "1.3.6.1.4.1.1000000.2016.xxx");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_REPORT_CENTER_UPLOAD_TARGET_ID"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_REPORT_CENTER_UPLOAD_TARGET_ID", "", "", "天健平台--报告中心上传消息目标系统域标识", "天健平台");
                }
            }
            #endregion
            #region 报告中心上传系统密码
            if (!JudgeParam(dtParameter, "PLATFORM_REPORT_CENTER_UPLOAD_SYS_PASSWORD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_REPORT_CENTER_UPLOAD_SYS_PASSWORD", "1.3.6.1.4.1.1000000.2016.xxx");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_REPORT_CENTER_UPLOAD_SYS_PASSWORD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_REPORT_CENTER_UPLOAD_SYS_PASSWORD", "", "", "天健平台--报告中心上传系统密码", "天健平台");
                }
            }
            #endregion
            #endregion
            #region IM 接口
            if (!JudgeParam(dtParameter, "TJIM_FLAG"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "TJIM_FLAG", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "TJIM_FLAG"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "TJIM_FLAG", "0", "0,1", "是否启用天健IM 0-不启用,1-启用", "天健平台");
                }
            }
            if (!JudgeParam(dtParameter, "TJIM_SERVER"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "TJIM_SERVER", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "TJIM_SERVER"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "TJIM_SERVER", "", "IP地址", "天健--IM服务器地址", "天健平台");
                }
            }
            if (!JudgeParam(dtParameter, "TJIM_ACCESS_PATH"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "TJIM_ACCESS_PATH", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "TJIM_ACCESS_PATH"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "TJIM_ACCESS_PATH", "", "访问地址", "天健--IM程序访问地址含exe", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院状态
            #region 天健平台--住院状态--接治
            #region 天健平台--住院状态--接治功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_HOSPITAL_STATUS_CREATE_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_HOSPITAL_STATUS_CREATE_MSGTYPE", "TJ153");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_HOSPITAL_STATUS_CREATE_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_HOSPITAL_STATUS_CREATE_MSGTYPE", "TJ153", "TJ153", "天健平台--住院状态--接治功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院状态--操作类型--接治
            if (!JudgeParam(dtParameter, "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_CREATE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_CREATE", "V33");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_CREATE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_CREATE", "V33", "V33", "天健平台--住院状态--操作类型--接治", "天健平台");
                }
            }
            #endregion
            #endregion
            #region 天健平台--住院状态--移入
            #region 天健平台--住院状态--移入功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_HOSPITAL_STATUS_IN_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_HOSPITAL_STATUS_IN_MSGTYPE", "TJ153");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_HOSPITAL_STATUS_IN_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_HOSPITAL_STATUS_IN_MSGTYPE", "TJ153", "TJ153", "天健平台--住院状态--移入功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院状态--操作类型--移入
            if (!JudgeParam(dtParameter, "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_IN"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_IN", "V32");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_IN"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_IN", "V32", "V32", "天健平台--住院状态--操作类型--移入", "天健平台");
                }
            }
            #endregion
            #endregion
            #region 天健平台--住院状态--移出
            #region 天健平台--住院状态--移出功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_HOSPITAL_STATUS_OUT_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_HOSPITAL_STATUS_OUT_MSGTYPE", "TJ153");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_HOSPITAL_STATUS_OUT_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_HOSPITAL_STATUS_OUT_MSGTYPE", "TJ153", "TJ153", "天健平台--住院状态--移出功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院状态--操作类型--移出
            if (!JudgeParam(dtParameter, "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_OUT"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_OUT", "V34");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_OUT"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_OUT", "V34", "V34", "天健平台--住院状态--操作类型--移出", "天健平台");
                }
            }
            #endregion
            #endregion
            #region 天健平台--住院状态--出院通知
            #region 天健平台--住院状态--出院通知功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_HOSPITAL_STATUS_LEAVE_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_HOSPITAL_STATUS_LEAVE_MSGTYPE", "TJ171");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_HOSPITAL_STATUS_LEAVE_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_HOSPITAL_STATUS_LEAVE_MSGTYPE", "TJ171", "TJ171", "天健平台--住院状态--出院通知功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院状态--操作类型--出院通知
            if (!JudgeParam(dtParameter, "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_LEAVE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_LEAVE", "V29");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_LEAVE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_LEAVE", "V29", "V29", "天健平台--住院状态--操作类型--出院通知", "天健平台");
                }
            }
            #endregion
            #endregion
            #region 天健平台--住院状态--取消出院通知
            #region 天健平台--住院状态--取消出院通知功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_HOSPITAL_STATUS_CANCEL_LEAVE_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_HOSPITAL_STATUS_CANCEL_LEAVE_MSGTYPE", "TJ172");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_HOSPITAL_STATUS_CANCEL_LEAVE_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_HOSPITAL_STATUS_CANCEL_LEAVE_MSGTYPE", "TJ172", "TJ172", "天健平台--住院状态--取消出院通知功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--住院状态--操作类型--取消出院通知
            if (!JudgeParam(dtParameter, "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_CANCEL_LEAVE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_CANCEL_LEAVE", "V31");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_CANCEL_LEAVE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_HOSPITAL_STATUS_ACTIONTYPE_CANCEL_LEAVE", "V31", "V31", "天健平台--住院状态--操作类型--取消出院通知", "天健平台");
                }
            }
            #endregion
            #endregion
            #endregion
            #region 天健平台--诊断
            #region 天健平台--诊断--新增功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_DIAGNOSIS_ADD_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_DIAGNOSIS_ADD_MSGTYPE", "TJ191");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_DIAGNOSIS_ADD_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_DIAGNOSIS_ADD_MSGTYPE", "TJ191", "TJ191", "天健平台--诊断--新增功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--诊断--操作类型--新增
            if (!JudgeParam(dtParameter, "PLATFORM_DIAGNOSIS_ACTIONTYPE_ADD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_DIAGNOSIS_ACTIONTYPE_ADD", "1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_DIAGNOSIS_ACTIONTYPE_ADD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_DIAGNOSIS_ACTIONTYPE_ADD", "1", "1", "天健平台--诊断--操作类型--新增", "天健平台");
                }
            }
            #endregion
            #region 天健平台--诊断--更新功能号ID
            if (!JudgeParam(dtParameter, "PLATFORM_DIAGNOSIS_UP_MSGTYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_DIAGNOSIS_UP_MSGTYPE", "TJ192");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_DIAGNOSIS_UP_MSGTYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_DIAGNOSIS_UP_MSGTYPE", "TJ192", "TJ192", "天健平台--诊断--更新功能号ID", "天健平台");
                }
            }
            #endregion
            #region 天健平台--诊断--操作类型--更新
            if (!JudgeParam(dtParameter, "PLATFORM_DIAGNOSIS_ACTIONTYPE_UP"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PLATFORM_DIAGNOSIS_ACTIONTYPE_UP", "2");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PLATFORM_DIAGNOSIS_ACTIONTYPE_UP"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PLATFORM_DIAGNOSIS_ACTIONTYPE_UP", "2", "2", "天健平台--诊断--操作类型--更新", "天健平台");
                }
            }
            #endregion
            #endregion
            #endregion 平台 
            #region 合理用药
            #region 合理用药厂家
            if (!JudgeParam(dtParameter, "PASS_FIRM"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PASS_FIRM", "");//默认不支持
                if (!JudgeParamBaseinfo(dtBaseinfo, "PASS_FIRM"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PASS_FIRM", "", "美康,大通,慧药通,链药", "合理用药厂家名称", "合理用药");
                }
            }
            #endregion
            #region 合理用药厂家秘钥
            if (!JudgeParam(dtParameter, "PASS_FIRM_AUTHERKEY"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PASS_FIRM_AUTHERKEY", "");//默认不支持
                if (!JudgeParamBaseinfo(dtBaseinfo, "PASS_FIRM_AUTHERKEY"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PASS_FIRM_AUTHERKEY", "", "", "合理用药厂家秘钥", "合理用药");
                }
            }

            #endregion
            #region 合理用药功能
            //合理用药接口0--不启用;1--美康合理用药;2--大通合理用药;3--老版本美康合理用药;4--美德医
            if (!JudgeParam(dtParameter, "ISPASS"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ISPASS", "0");//默认不支持
                if (!JudgeParamBaseinfo(dtBaseinfo, "ISPASS"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ISPASS", "0", "0,1", "合理用药功能：0==不支持(默认)，1==美康合理用药", "合理用药");
                }
            }
            #endregion
            #region 美康合理用药
            #region 美康越权用药
            if (!JudgeParam(dtParameter, "ISPASS_EXCEED_POWER"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ISPASS_EXCEED_POWER", "0");//默认不支持
                if (!JudgeParamBaseinfo(dtBaseinfo, "ISPASS_EXCEED_POWER"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ISPASS_EXCEED_POWER", "0", "0,1", "是否启用美康越权用药：0--不启用；1--启用", "合理用药");
                }
            }
            #endregion
            #region 美康合理用药验证显示黑灯的时候，是否进行拦截，0不拦截，1开启拦截
            if (!JudgeParam(dtParameter, "PASS_WARNING_ENABLE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PASS_WARNING_ENABLE", "0");//默认不支持
                if (!JudgeParamBaseinfo(dtBaseinfo, "PASS_WARNING_ENABLE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PASS_WARNING_ENABLE", "0", "0,1", "美康合理用药验证显示黑灯的时候，是否进行拦截，0不拦截，1开启拦截", "合理用药");
                }
            }
            #endregion
            #region 美康合理用药院区代码
            if (!JudgeParam(dtParameter, "PASS_HIS_CODE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PASS_HIS_CODE", "0");//默认单医院
                if (!JudgeParamBaseinfo(dtBaseinfo, "PASS_HIS_CODE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PASS_HIS_CODE", "0", "", "单医院传0，区域版传PASS预置的对应院区的HISCODE。此处传值必须匹配PASSPA2DB.dbo.mc_hospital_match_relation表的hiscode。如果此处传值不匹配，则初始化返回 -3", "合理用药");
                }
            }
            #endregion
            #region 链药合理用药医院级别
            if (!JudgeParam(dtParameter, "PASS_HOSP_TYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PASS_HOSP_TYPE", "");//
                if (!JudgeParamBaseinfo(dtBaseinfo, "PASS_HOSP_TYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PASS_HOSP_TYPE", "", "", "链药合理用药医院级别", "合理用药");
                }
            }
            #endregion


            #endregion
            #endregion
            #region 临床路径
            #region 路径模板使用范围
            if (!JudgeParam(dtParameter, "TEMPLETE_UNIVERSAL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "TEMPLETE_UNIVERSAL", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "TEMPLETE_UNIVERSAL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "TEMPLETE_UNIVERSAL", "0", "0,1", "路径模板使用范围 0-科室,1-全院", "临床路径");
                }
            }
            #endregion
            #region 是否启用临床路径功能
            if (!JudgeParam(dtParameter, "CLINICAL_PATHWAY"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "CLINICAL_PATHWAY", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "CLINICAL_PATHWAY"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "CLINICAL_PATHWAY", "0", "0,1", "是否启用临床路径功能 0-不启用,1-启用", "临床路径");
                }
            }
            #endregion
            #region 临床路径版本号
            if (!JudgeParam(dtParameter, "DOCTWS", "CLINICAL_PATHWAY_VERSION"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "CLINICAL_PATHWAY_VERSION", "6.6.2");
                if (!JudgeParamBaseinfo(dtBaseinfo, "CLINICAL_PATHWAY_VERSION"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "CLINICAL_PATHWAY_VERSION", "6.6.2", "6.6.2", "是否启用临床路径功能 0-不启用,1-启用", "临床路径");
                }
            }
            #endregion
            #region 非特异性评估表治疗结果数据取值来源
            if (!JudgeParam(dtParameter, "CP", "TREAT_RESULT"))
            {
                InserParameter(dtParameter, "CP", "*", "*", "TREAT_RESULT", "1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "TREAT_RESULT"))
                {
                    InsertBaseinfo(dtBaseinfo, "CP", 1, "TREAT_RESULT", "1", "1,2", "非特异性评估表治疗结果数据取值来源：1:病人完成路径选择的结果；2:首页第一条诊断的结果", "临床路径");
                }
            }
            #endregion
            #region 大包装药品能否开医嘱
            if (!JudgeParam(dtParameter, "CP", "AMOUNT_PER_PACKAGE"))
            {
                InserParameter(dtParameter, "CP", "*", "*", "AMOUNT_PER_PACKAGE", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "AMOUNT_PER_PACKAGE"))
                {
                    InsertBaseinfo(dtBaseinfo, "CP", 1, "AMOUNT_PER_PACKAGE", "0", "0,1", "大包装药品能否开医嘱: 0控制(默认);1不控制", "临床路径");
                }
            }
            #endregion
            #region 自动分析优化的最低人次数
            if (!JudgeParam(dtParameter, "CP", "ANALYSIS_NUMBER"))
            {
                InserParameter(dtParameter, "CP", "*", "*", "ANALYSIS_NUMBER", "10");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ANALYSIS_NUMBER"))
                {
                    InsertBaseinfo(dtBaseinfo, "CP", 1, "ANALYSIS_NUMBER", "10", "整数", "自动分析优化的最低人次数,不能少于10人", "临床路径");
                }
            }
            #endregion
            #region 自动分析优化的可选转必选基数
            if (!JudgeParam(dtParameter, "CP", "ANALYSIS_MUST"))
            {
                InserParameter(dtParameter, "CP", "*", "*", "ANALYSIS_MUST", "0.6");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ANALYSIS_MUST"))
                {
                    InsertBaseinfo(dtBaseinfo, "CP", 1, "ANALYSIS_MUST", "0.6", "小于1的系数", "自动分析优化的可选转必选基数,不能小于0.6", "临床路径");
                }
            }
            #endregion
            #region 自动分析优化的必选转可选基数
            if (!JudgeParam(dtParameter, "CP", "ANALYSIS_MAY"))
            {
                InserParameter(dtParameter, "CP", "*", "*", "ANALYSIS_MAY", "0.4");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ANALYSIS_MAY"))
                {
                    InsertBaseinfo(dtBaseinfo, "CP", 1, "ANALYSIS_MAY", "0.4", "小于1的系数", "自动分析优化的必选转可选基数,不能大于0.4", "临床路径");
                }
            }
            #endregion
            #endregion
            #region 医嘱
            #region 医嘱续执行用药途径
            if (!JudgeParam(dtParameter, "CONTINUE_ORDER"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "CONTINUE_ORDER", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "CONTINUE_ORDER"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "CONTINUE_ORDER", "", "", "医嘱续执行用药途径，多途径间用;间隔，不支持续静滴请配置为空", "医嘱");
                }
            }
            #endregion
            #region 是否支持自动根据途径下达治疗费用、是否允许调整费用明细
            if (!JudgeParam(dtParameter, "ORDER_COSTS"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ORDER_COSTS", "1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "CONTINUE_ORDER"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ORDER_COSTS", "1", "0,1", "是否支持自动根据途径下达治疗费用、是否允许调整费用明细，1允许(默认)，0不允许", "医嘱");
                }
            }
            #endregion
            #region 医嘱费用操作限制
            if (!JudgeParam(dtParameter, "CAN_DEL_ORDERCOSTS_ACCTED"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "CAN_DEL_ORDERCOSTS_ACCTED", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "CAN_DEL_ORDERCOSTS_ACCTED"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "CAN_DEL_ORDERCOSTS_ACCTED", "0", "0,1,,3", "医嘱费用操作限制：0--医嘱计价后不允许删除(默认)，1--医嘱计价后允许删除费用，3--医嘱费用不可见", "医嘱");
                }
            }
            #endregion
            #region 长期医嘱是否可以预停以及编辑
            if (!JudgeParam(dtParameter, "ORDER_STOP_TIME"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ORDER_STOP_TIME", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ORDER_STOP_TIME"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ORDER_STOP_TIME", "0", "0,1,,3", "长期医嘱是否可以预停以及编辑,1-可编辑，0-不可编辑", "医嘱");
                }
            }
            #endregion
            #region 提前下达医嘱时限，默认24小时
            if (!JudgeParam(dtParameter, "ORDER_BEFORSTARTTIME"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ORDER_BEFORSTARTTIME", "24");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ORDER_BEFORSTARTTIME"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ORDER_BEFORSTARTTIME", "24", "整数", "提前下达医嘱时限，默认24小时", "医嘱");
                }
            }
            #endregion
            #region 后补医嘱时限，默认6小时
            if (!JudgeParam(dtParameter, "ORDER_BACKSTARTTIME"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ORDER_BACKSTARTTIME", "6");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ORDER_BACKSTARTTIME"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ORDER_BACKSTARTTIME", "6", "整数", "后补医嘱时限，默认6小时", "医嘱");
                }
            }
            #endregion
            #region 生成出院医嘱--出院方式
            #region 下达出院通知时是否自动生成出院医嘱
            if (!JudgeParam(dtParameter, "PREOUT_ORDER"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PREOUT_ORDER", "1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PREOUT_ORDER"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PREOUT_ORDER", "1", "0,1", "下达出院通知时是否自动生成出院医嘱：1自动生成(默认)，0不生成", "医嘱");
                }
            }
            #endregion
            #region 出院方式--自主离院诊疗项目代码
            if (!JudgeParam(dtParameter, "DISCHARGE_WAY_ZZLY"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "DISCHARGE_WAY_ZZLY", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "DISCHARGE_WAY_ZZLY"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "DISCHARGE_WAY_ZZLY", "", "诊疗项目代码", "生成出院医嘱--出院方式--自主离院诊疗项目代码", "医嘱");
                }
            }
            #endregion
            #region 出院方式--今日出院诊疗项目代码
            if (!JudgeParam(dtParameter, "DISCHARGE_WAY_ZCLY_TODAY"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "DISCHARGE_WAY_ZCLY_TODAY", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "DISCHARGE_WAY_ZCLY_TODAY"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "DISCHARGE_WAY_ZCLY_TODAY", "", "诊疗项目代码", "生成出院医嘱--出院方式--今日出院诊疗项目代码", "医嘱");
                }
            }
            #endregion
            #region 出院方式--明日出院诊疗项目代码
            if (!JudgeParam(dtParameter, "DISCHARGE_WAY_ZCLY_TOMORROW"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "DISCHARGE_WAY_ZCLY_TOMORROW", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "DISCHARGE_WAY_ZCLY_TOMORROW"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "DISCHARGE_WAY_ZCLY_TOMORROW", "", "诊疗项目代码", "生成出院医嘱--出院方式--明日出院诊疗项目代码", "医嘱");
                }
            }
            #endregion
            #region 出院方式--死亡离院诊疗项目代码
            if (!JudgeParam(dtParameter, "DISCHARGE_WAY_SWLY"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "DISCHARGE_WAY_SWLY", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "DISCHARGE_WAY_SWLY"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "DISCHARGE_WAY_SWLY", "", "诊疗项目代码", "生成出院医嘱--出院方式--死亡离院诊疗项目代码", "医嘱");
                }
            }
            #endregion
            #endregion
            #region 是否医嘱打印启用图片签名打印
            if (!JudgeParam(dtParameter, "ORDERS_PRINT_PIC_SIG"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ORDERS_PRINT_PIC_SIG", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ORDERS_PRINT_PIC_SIG"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ORDERS_PRINT_PIC_SIG", "0", "0,1", "是否医嘱打印启用图片签名打印:0--文字;1--图片", "医嘱");
                }
            }
            #endregion
            #region 医嘱状态
            #region 医嘱状态--医生新增医嘱保存
            if (!JudgeParam(dtParameter, "ORDER_STATUS_ADD_SAVE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ORDER_STATUS_ADD_SAVE", "5");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ORDER_STATUS_ADD_SAVE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ORDER_STATUS_ADD_SAVE", "5", "医嘱状态字典(ORDER_STATUS_DICT)", "医嘱状态--医生新增医嘱保存，默认为5", "医嘱");
                }
            }
            #endregion
            #region 医嘱状态--医生新增医嘱提交
            if (!JudgeParam(dtParameter, "ORDER_STATUS_SUBMIT"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ORDER_STATUS_SUBMIT", "6");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ORDER_STATUS_SUBMIT"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ORDER_STATUS_SUBMIT", "6", "医嘱状态字典(ORDER_STATUS_DICT)", "医嘱状态--医生新增医嘱提交，默认为6", "医嘱");
                }
            }
            #endregion
            #region 医嘱状态--医生停止医嘱保存
            if (!JudgeParam(dtParameter, "ORDER_STATUS_STOP_SAVE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ORDER_STATUS_STOP_SAVE", "7");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ORDER_STATUS_STOP_SAVE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ORDER_STATUS_STOP_SAVE", "7", "医嘱状态字典(ORDER_STATUS_DICT)", "医嘱状态--医生停止医嘱保存，默认为7", "医嘱");
                }
            }
            #endregion
            #region 医嘱状态--医生停止医嘱提交
            if (!JudgeParam(dtParameter, "ORDER_STATUS_STOP_SUBMIT"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ORDER_STATUS_STOP_SUBMIT", "6");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ORDER_STATUS_STOP_SUBMIT"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ORDER_STATUS_STOP_SUBMIT", "6", "医嘱状态字典(ORDER_STATUS_DICT)", "医嘱状态--医生停止医嘱提交，默认为6", "医嘱");
                }
            }
            #endregion
            #region 医嘱状态--医生作废医嘱保存
            if (!JudgeParam(dtParameter, "ORDER_STATUS_CANCEL_SAVE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ORDER_STATUS_CANCEL_SAVE", "8");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ORDER_STATUS_CANCEL_SAVE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ORDER_STATUS_CANCEL_SAVE", "8", "医嘱状态字典(ORDER_STATUS_DICT)", "医嘱状态--医生作废医嘱保存，默认为8", "医嘱");
                }
            }
            #endregion
            #region 医嘱状态--医生作废医嘱提交
            if (!JudgeParam(dtParameter, "ORDER_STATUS_CANCEL_SUBMIT"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ORDER_STATUS_CANCEL_SUBMIT", "6");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ORDER_STATUS_CANCEL_SUBMIT"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ORDER_STATUS_CANCEL_SUBMIT", "6", "医嘱状态字典(ORDER_STATUS_DICT)", "医嘱状态--医生作废医嘱提交，默认为6", "医嘱");
                }
            }
            #endregion
            #region 医嘱状态--护士医嘱转抄
            if (!JudgeParam(dtParameter, "ORDER_STATUS_TRANSCRIBE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ORDER_STATUS_TRANSCRIBE", "1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ORDER_STATUS_TRANSCRIBE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ORDER_STATUS_TRANSCRIBE", "1", "医嘱状态字典(ORDER_STATUS_DICT)", "医嘱状态--护士医嘱转抄，默认为1", "医嘱");
                }
            }
            #endregion
            #region 医嘱状态--护士新增医嘱校对
            if (!JudgeParam(dtParameter, "ORDER_STATUS_ADD_PROOFREAD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ORDER_STATUS_ADD_PROOFREAD", "2");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ORDER_STATUS_ADD_PROOFREAD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ORDER_STATUS_ADD_PROOFREAD", "2", "医嘱状态字典(ORDER_STATUS_DICT)", "医嘱状态--护士新增医嘱校对，默认为2", "医嘱");
                }
            }
            #endregion
            #region 医嘱状态--护士停止医嘱校对
            if (!JudgeParam(dtParameter, "ORDER_STATUS_STOP_PROOFREAD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ORDER_STATUS_STOP_PROOFREAD", "3");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ORDER_STATUS_STOP_PROOFREAD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ORDER_STATUS_STOP_PROOFREAD", "3", "医嘱状态字典(ORDER_STATUS_DICT)", "医嘱状态--护士停止医嘱校对，默认为3", "医嘱");
                }
            }
            #endregion
            #region 医嘱状态--护士作废医嘱校对
            if (!JudgeParam(dtParameter, "ORDER_STATUS_CANCEL_PROOFREAD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ORDER_STATUS_CANCEL_PROOFREAD", "4");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ORDER_STATUS_CANCEL_PROOFREAD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ORDER_STATUS_CANCEL_PROOFREAD", "4", "医嘱状态字典(ORDER_STATUS_DICT)", "医嘱状态--护士作废医嘱校对，默认为4", "医嘱");
                }
            }
            #endregion 
            #endregion
            #endregion
            #region 处方
            #region 毒麻、精神一类处方对应毒理属性
            if (!JudgeParam(dtParameter, "PRESC_TOXI_PROTITIES_DUMA"))
            {
                InserParameter(dtParameter, "*", "*", "*", "PRESC_TOXI_PROTITIES_DUMA", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PRESC_TOXI_PROTITIES_DUMA"))
                {
                    InsertBaseinfo(dtBaseinfo, "*", 1, "PRESC_TOXI_PROTITIES_DUMA", "", "毒理属性字典", "毒麻、精神一类处方对应毒理属性，不同属性间以;间隔", "处方");
                }
            }
            #endregion
            #region 精神二类处方对应毒理属性
            if (!JudgeParam(dtParameter, "PRESC_TOXI_PROTITIES_JINGSHEN"))
            {
                InserParameter(dtParameter, "*", "*", "*", "PRESC_TOXI_PROTITIES_JINGSHEN", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PRESC_TOXI_PROTITIES_JINGSHEN"))
                {
                    InsertBaseinfo(dtBaseinfo, "*", 1, "PRESC_TOXI_PROTITIES_JINGSHEN", "", "毒理属性字典", "精神二类处方对应毒理属性，不同属性间以;间隔", "处方");
                }
            }
            #endregion
            #region 处方保存成果后是否自动打印处方
            if (!JudgeParam(dtParameter, "PRESC_PRINT_NEW"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PRESC_PRINT_NEW", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PRESC_PRINT_NEW"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PRESC_PRINT_NEW", "0", "0,1", "处方保存成果后是否自动打印处方：0不打印(默认)，1打印", "处方");
                }
            }
            #endregion
            #region 已经保存后又修改的处方保存时是否自动打印
            if (!JudgeParam(dtParameter, "PRESC_PRINT_MODIFIED"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PRESC_PRINT_MODIFIED", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PRESC_PRINT_NEW"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PRESC_PRINT_MODIFIED", "0", "0,1", "已经保存后又修改的处方保存时是否自动打印： 0不打印(默认)，1打印", "处方");
                }
            }
            #endregion
            #region 草药缺省给药途径
            if (!JudgeParam(dtParameter, "DEFAULT_ADMINISTRATION"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "DEFAULT_ADMINISTRATION", "煎服");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PRESC_PRINT_NEW"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "DEFAULT_ADMINISTRATION", "煎服", "途径字典表", "草药缺省给药途径,来源于用药途径字典表,默认 是煎服", "处方");
                }
            }
            #endregion
            #region 西药处方保存时是否默认生成临时医嘱
            if (!JudgeParam(dtParameter, "GEN_DRUG_ORDER"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "GEN_DRUG_ORDER", "1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PRESC_PRINT_NEW"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "GEN_DRUG_ORDER", "1", "0,1", "处方保存时是否默认生成临时医嘱：1--生成临时医嘱(默认)，0--不生成临床医嘱，2--生成长期医嘱", "处方");
                }
            }
            #endregion
            #region 处方是否默认勾选出院带药属性
            if (!JudgeParam(dtParameter, "GEN_DISCHARGE_ORDER"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "GEN_DISCHARGE_ORDER", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PRESC_PRINT_NEW"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "GEN_DISCHARGE_ORDER", "0", "0,1", "处方是否默认勾选出院带药属性：1--默认出院带药，0--不默认出院带药(默认)", "处方");
                }
            }
            #endregion
            #region 默认中药剂数(付数）
            if (!JudgeParam(dtParameter, "TCM_REPETITION"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "TCM_REPETITION", "7");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PRESC_PRINT_NEW"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "TCM_REPETITION", "7", "整数", "默认中药剂数(付数）", "处方");
                }
            }
            #endregion
            #region 草药处方生成临时医嘱记录的医嘱类别
            if (!JudgeParam(dtParameter, "CDRUG_ORDER_CLASS"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "CDRUG_ORDER_CLASS", "B");
                if (!JudgeParamBaseinfo(dtBaseinfo, "CDRUG_ORDER_CLASS"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "CDRUG_ORDER_CLASS", "B", "诊疗项目类型字典", "草药处方生成临时医嘱记录的医嘱类别，默认为：B", "处方");
                }
            }
            #endregion
            #region 草药处方总量除以最小单位剂量
            if (!JudgeParam(dtParameter, "HERBS_DIVIDE_DOSE_PER_UNIT"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "HERBS_DIVIDE_DOSE_PER_UNIT", "1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "HERBS_DIVIDE_DOSE_PER_UNIT"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "HERBS_DIVIDE_DOSE_PER_UNIT", "1", "0,1", "草药处方总量除以最小单位剂量，0--不需要，1--需要，默认为：1", "处方");
                }
            }
            #endregion

            #endregion
            #region 检验申请
            #region 检验类医嘱是否计价
            if (!JudgeParam(dtParameter, "LAB_BILLING"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "LAB_BILLING", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "LAB_BILLING"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "LAB_BILLING", "0", "0,1", "检验类医嘱是否计价：0-- 不计价通过检验划价程序来进行划价(默认)，1--计价", "检验申请");
                }
            }
            #endregion
            #region HBSAG检验结果代码
            if (!JudgeParam(dtParameter, "LAB_HBSAG"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "LAB_HBSAG", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "LAB_HBSAG"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "LAB_HBSAG", "", "LIS返回结果代码", "输血前检验项目中HBSAG检验结果代码", "检验申请");
                }
            }
            #endregion
            #region HCV检验结果代码
            if (!JudgeParam(dtParameter, "LAB_HCV"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "LAB_HCV", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "LAB_HCV"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "LAB_HCV", "", "LIS返回结果代码", "输血前检验项目中HCV检验结果代码", "检验申请");
                }
            }
            #endregion
            #region HIV检验结果代码
            if (!JudgeParam(dtParameter, "LAB_HIV"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "LAB_HIV", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "LAB_HIV"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "LAB_HIV", "", "LIS返回结果代码", "输血前检验项目中HIV检验结果代码", "检验申请");
                }
            }
            #endregion
            #region TP检验结果代码
            if (!JudgeParam(dtParameter, "LAB_TP"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "LAB_TP", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "LAB_TP"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "LAB_TP", "", "LIS返回结果代码", "输血前检验项目中TP检验结果代码", "检验申请");
                }
            }
            #endregion
            #region HB检验结果代码
            if (!JudgeParam(dtParameter, "LAB_HB"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "LAB_HB", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "LAB_HB"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "LAB_HB", "", "LIS返回结果代码", "输血前检验项目中HB检验结果代码", "检验申请");
                }
            }
            #endregion
            #region PLT检验结果代码
            if (!JudgeParam(dtParameter, "LAB_PIT"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "LAB_PIT", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "LAB_PIT"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "LAB_PIT", "", "LIS返回结果代码", "输血前检验项目中PLT检验结果代码", "检验申请");
                }
            }
            #endregion
            #region ABO检验结果代码
            if (!JudgeParam(dtParameter, "LAB_ABO"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "LAB_ABO", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "LAB_ABO"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "LAB_ABO", "", "LIS返回结果代码", "输血前检验项目中ABO检验结果代码", "检验申请");
                }
            }
            #endregion
            #region RHD检验结果代码
            if (!JudgeParam(dtParameter, "LAB_RHD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "LAB_RHD", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "LAB_RHD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "LAB_RHD", "", "LIS返回结果代码", "输血前检验项目中RHD检验结果代码", "检验申请");
                }
            }
            #endregion
            #region 抗体筛检实验检验结果代码
            if (!JudgeParam(dtParameter, "LAB_SCREENING_TEST"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "LAB_SCREENING_TEST", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "LAB_SCREENING_TEST"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "LAB_SCREENING_TEST", "", "LIS返回结果代码", "输血前检验项目中抗体筛检实验检验结果代码", "检验申请");
                }
            }
            #endregion
            #region 输血前四项诊疗项目代码
            if (!JudgeParam(dtParameter, "LAB_PRETEST_FOURITEMS_CODE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "LAB_PRETEST_FOURITEMS_CODE", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "LAB_PRETEST_FOURITEMS_CODE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "LAB_PRETEST_FOURITEMS_CODE", "", "诊疗项目代码", "输血前四项诊疗项目代码", "检验申请");
                }
            }
            #endregion
            #region 血型鉴定诊疗项目代码
            if (!JudgeParam(dtParameter, "LAB_PRETEST_BLOODTYPE_CODE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "LAB_PRETEST_BLOODTYPE_CODE", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "LAB_PRETEST_BLOODTYPE_CODE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "LAB_PRETEST_BLOODTYPE_CODE", "", "诊疗项目代码", "血型鉴定诊疗项目代码", "检验申请");
                }
            }
            #endregion
            #region 血型抗体筛查诊疗项目代码
            if (!JudgeParam(dtParameter, "LAB_PRETEST_ANTIBODY_CODE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "LAB_PRETEST_ANTIBODY_CODE", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "LAB_PRETEST_ANTIBODY_CODE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "LAB_PRETEST_ANTIBODY_CODE", "", "诊疗项目代码", "血型抗体筛查诊疗项目代码", "检验申请");
                }
            }
            #endregion
            #endregion
            #region 检查申请
            #region 检查类医嘱是否计价
            if (!JudgeParam(dtParameter, "EXAM_BILLING"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "EXAM_BILLING", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "EXAM_BILLING"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "EXAM_BILLING", "0", "0,1", "检查类医嘱是否计价：0 不计价通过检查划价程序来进行划价(默认)，1计价", "检查申请");
                }
            }
            #endregion
            #region 检查结果查询分类
            if (!JudgeParam(dtParameter, "PACS_RESULT_CLASS"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PACS_RESULT_CLASS", "标准版本");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PACS_RESULT_CLASS"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PACS_RESULT_CLASS", "标准版本", "", "配置检查结果查询分类；公司发布标准版本，根据医院客户化", "检查申请");
                }
            }
            #endregion 
            #region PACS 服务器 图文报告访问地址
            if (!JudgeParam(dtParameter, "PACS_IMAGE_TEXT_PATH"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PACS_IMAGE_TEXT_PATH", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PACS_IMAGE_TEXT_PATH"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PACS_IMAGE_TEXT_PATH", "", "URL地址", "PACS 服务器 图文报告访问地址", "检查申请");
                }
            }
            #endregion
            #region PACS 服务器 心电图报告访问地址
            if (!JudgeParam(dtParameter, "PACS_IMAGE_HEART_PATH"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PACS_IMAGE_HEART_PATH", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PACS_IMAGE_HEART_PATH"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PACS_IMAGE_HEART_PATH", "", "URL地址", "PACS 服务器 心电图报告访问地址", "检查申请");
                }
            }
            #endregion

            #region PACS 服务器 图像报告访问地址
            if (!JudgeParam(dtParameter, "PACS_IMAGE_PATH"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PACS_IMAGE_PATH", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PACS_IMAGE_PATH"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PACS_IMAGE_PATH", "", "URL地址", "PACS 服务器 图像报告访问地址", "检查申请");
                }
            }
            #endregion
            #region 检查申请必填项配置
            if (!JudgeParam(dtParameter, "CP_EXAM_APP_MUST"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "CP_EXAM_APP_MUST", "临床诊断");
                if (!JudgeParamBaseinfo(dtBaseinfo, "CP_EXAM_APP_MUST"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "CP_EXAM_APP_MUST", "临床诊断", "检查申请内容名称:症状;体征;临床诊断;相关化验结果;", "检查申请必填项配置", "检查申请");
                }
            }
            #endregion
            #region 是否支持诊间预约
            if (!JudgeParam(dtParameter, "INP_EXAM_APPOINTMENT"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "INP_EXAM_APPOINTMENT", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "INP_EXAM_APPOINTMENT"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "INP_EXAM_APPOINTMENT", "0", "0,1", "检查申请支持诊间预约；0--不支持;1--支持", "检查申请");
                }
            }
            #endregion
            #region 诊间预约exe下载地址
            if (!JudgeParam(dtParameter, "EXAM_APPOINTS_DOWNLOAD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "EXAM_APPOINTS_DOWNLOAD", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "EXAM_APPOINTS_DOWNLOAD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "EXAM_APPOINTS_DOWNLOAD", "", "", "诊间预约exe下载地址", "检查申请");
                }
            }
            #endregion
            #region 诊间预约可预约检查类别
            if (!JudgeParam(dtParameter, "EXAM_APPOINTS_CLASS"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "EXAM_APPOINTS_CLASS", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "EXAM_APPOINTS_CLASS"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "EXAM_APPOINTS_CLASS", "", "检查类别字典", "诊间预约可预约检查类别", "检查申请");
                }
            }
            #endregion
            #region 诊间预约--撤销预约WebService地址
            if (!JudgeParam(dtParameter, "EXAM_APPOINTS_REVOKE_URL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "EXAM_APPOINTS_REVOKE_URL", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "EXAM_APPOINTS_REVOKE_URL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "EXAM_APPOINTS_REVOKE_URL", "", "URL地址", "诊间预约--撤销预约WebService地址", "检查申请");
                }
            }
            #endregion
            #region 诊间预约--撤销预约WebService方法名称
            if (!JudgeParam(dtParameter, "EXAM_APPOINTS_REVOKE_METHOD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "EXAM_APPOINTS_REVOKE_METHOD", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "EXAM_APPOINTS_REVOKE_METHOD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "EXAM_APPOINTS_REVOKE_METHOD", "", "方法名", "诊间预约--撤销预约WebService方法名称", "检查申请");
                }
            }
            #endregion
            #region 诊间预约机构编码
            if (!JudgeParam(dtParameter, "EXAM_APPOINTS_UNIT_CODE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "EXAM_APPOINTS_UNIT_CODE", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "EXAM_APPOINTS_UNIT_CODE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "EXAM_APPOINTS_UNIT_CODE", "000000", "000000", "检查预约机构编码", "检查申请");
                }
            }
            #endregion
            #region 诊间预约服务地址
            if (!JudgeParam(dtParameter, "EXAM_APPOINTS_SERVICE_IP"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "EXAM_APPOINTS_SERVICE_IP", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "EXAM_APPOINTS_SERVICE_IP"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "EXAM_APPOINTS_SERVICE_IP", "", "", "检查预约服务地址", "检查申请");
                }
            }
            #endregion
            #endregion
            #region 用血申请 
            #region 输血科室代码
            if (!JudgeParam(dtParameter, "BLOOD_DEPT_CODE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "BLOOD_DEPT_CODE", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "BLOOD_DEPT_CODE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "BLOOD_DEPT_CODE", "", "科室字典", "输血科室代码", "用血申请");
                }
            }
            #endregion
            #region 用血申请厂家
            if (!JudgeParam(dtParameter, "BLOOD_FIRM"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "BLOOD_FIRM", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "BLOOD_FIRM"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "BLOOD_FIRM", "", "天健;瑞美", "用血申请厂家", "用血申请");
                }
            }
            #endregion
            #region 用血申请--开具申请URL
            if (!JudgeParam(dtParameter, "BLOOD_CALL_APP_URL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "BLOOD_CALL_APP_URL", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "BLOOD_CALL_APP_URL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "BLOOD_CALL_APP_URL", "", "", "用血申请--开具申请URL", "用血申请");
                }
            }
            #endregion
            #region 用血申请--查询用血URL
            if (!JudgeParam(dtParameter, "BLOOD_CALL_QUERY_URL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "BLOOD_CALL_QUERY_URL", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "BLOOD_CALL_QUERY_URL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "BLOOD_CALL_QUERY_URL", "", "", "用血申请--查询用血URL", "用血申请");
                }
            }
            #endregion
            #region 用血申请--调用方式
            if (!JudgeParam(dtParameter, "BLOOD_CALL_MODE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "BLOOD_CALL_MODE", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "BLOOD_CALL_MODE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "BLOOD_CALL_MODE", "", "URL;EXE;DLL", "用血申请--调用方式", "用血申请");
                }
            }
            #endregion
            #region 用血申请--EXE调用路径
            if (!JudgeParam(dtParameter, "BLOOD_CALL_PATH"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "BLOOD_CALL_PATH", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "BLOOD_CALL_PATH"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "BLOOD_CALL_PATH", "", "", "用血申请--EXE调用路径", "用血申请");
                }
            }
            #endregion 
            #endregion
            #region 手术申请
            #region 手术申请中，普通手术申请允许的星期几
            if (!JudgeParam(dtParameter, "OPER_WEEK"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "OPER_WEEK", "星期一;星期二;星期三;星期四;星期五;");
                if (!JudgeParamBaseinfo(dtBaseinfo, "OPER_WEEK"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "OPER_WEEK", "星期一;星期二;星期三;星期四;星期五;", "星期一;星期二;星期三;星期四;星期五;星期六;星期日;", "手术申请中，普通手术申请允许的星期几", "手术申请");
                }
            }
            #endregion
            #region 手术申请中，普通手术允许申请的开始时间段
            if (!JudgeParam(dtParameter, "OPER_ORD_STAR_TIME"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "OPER_ORD_STAR_TIME", "08:00");
                if (!JudgeParamBaseinfo(dtBaseinfo, "OPER_ORD_STAR_TIME"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "OPER_ORD_STAR_TIME", "08:00", "24小时时间", "手术申请中，普通手术允许申请的开始时间段，例如00:00", "手术申请");
                }
            }
            #endregion
            #region 手术申请中，普通手术允许申请的结束时间段
            if (!JudgeParam(dtParameter, "OPER_ORD_END_TIME"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "OPER_ORD_END_TIME", "17:00");
                if (!JudgeParamBaseinfo(dtBaseinfo, "OPER_ORD_END_TIME"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "OPER_ORD_END_TIME", "17:00", "24小时时间", "手术申请中，普通手术允许申请的结束时间段，例如14:00", "手术申请");
                }
            }
            #endregion
            #region 手术申请中，急诊手术允许申请的开始时间段
            if (!JudgeParam(dtParameter, "OPER_EME_STAR_TIME"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "OPER_EME_STAR_TIME", "00:00");
                if (!JudgeParamBaseinfo(dtBaseinfo, "OPER_EME_STAR_TIME"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "OPER_EME_STAR_TIME", "00:00", "24小时时间", "手术申请中，急诊手术允许申请的开始时间段，例如00:00", "手术申请");
                }
            }
            #endregion
            #region 手术申请中，急诊手术允许申请的结束时间段
            if (!JudgeParam(dtParameter, "OPER_EME_END_TIME"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "OPER_EME_END_TIME", "07:59");
                if (!JudgeParamBaseinfo(dtBaseinfo, "OPER_EME_END_TIME"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "OPER_EME_END_TIME", "07:59", "24小时时间", "手术申请中，急诊手术允许申请的结束时间段，例如14:00", "手术申请");
                }
            }
            #endregion
            #region 手术名称,手术医生,助手等信息可否手写
            if (!JudgeParam(dtParameter, "OPERATION_EDIT"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "OPERATION_EDIT", "1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "OPERATION_EDIT"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "OPERATION_EDIT", "1", "0,1", "手术名称,手术医生,助手等信息可否手写,0-不可手写，1-可手写", "手术申请");
                }
            }
            #endregion
            #region 是否支持周六和周天手术室上班模式
            if (!JudgeParam(dtParameter, "OPERATE_SATURDAY"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "OPERATE_SATURDAY", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "OPERATE_SATURDAY"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "OPERATE_SATURDAY", "0", "0,1", "是否支持周六和周天手术室上班模式：0不支持(默认)，1支持", "手术申请");
                }
            }
            #endregion 
            #endregion
            #region 电子病历
            if (!JudgeParam(dtParameter, "EMR_VISION"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "EMR_VISION", "42");
                if (!JudgeParamBaseinfo(dtBaseinfo, "EMR_VISION"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "EMR_VISION", "42", "37,38,41,42", "当前在用电子病历控件版本：37,38,41,42", "电子病历");
                }
            }
            if (!JudgeParam(dtParameter, "EMR_OTHER_INP_FIRM"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "EMR_OTHER_INP_FIRM", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "EMR_OTHER_INP_FIRM"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "EMR_OTHER_INP_FIRM", "", "旌旗", "其他住院电子病历厂商", "电子病历");
                }
            }
            if (!JudgeParam(dtParameter, "EMR_OTHER_OUTP_FIRM"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "EMR_OTHER_OUTP_FIRM", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "EMR_OTHER_OUTP_FIRM"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "EMR_OTHER_OUTP_FIRM", "", "旌旗", "其他门诊电子病历厂商", "电子病历");
                }
            }
            if (!JudgeParam(dtParameter, "EMR_OTHER_INP_FIRM_PATH"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "EMR_OTHER_INP_FIRM_PATH", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "EMR_OTHER_INP_FIRM_PATH"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "EMR_OTHER_INP_FIRM_PATH", "", "URL地址", "调用其他电子病历厂商住院电子病历地址", "电子病历");
                }
            }
            if (!JudgeParam(dtParameter, "EMR_OTHER_OUTP_FIRM_PATH"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "EMR_OTHER_OUTP_FIRM_PATH", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "EMR_OTHER_OUTP_FIRM_PATH"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "EMR_OTHER_OUTP_FIRM_PATH", "", "URL地址", "调用其他电子病历厂商门诊电子病历地址", "电子病历");
                }
            }
            #endregion
            #region 急诊留观
            #region 住院医生站查询急诊留观数据天数
            if (!JudgeParam(dtParameter, "INP_QUERY_OB_DAY"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "INP_QUERY_OB_DAY", "-1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "INP_QUERY_OB_DAY"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "INP_QUERY_OB_DAY", "-1", "整数", "住院医生站查询急诊留观数据天数,小于0时不显示留观节点", "急诊留观");
                }
            }
            #endregion
            #endregion
            #region 门诊
            #region 住院医生站查询门诊数据天数
            if (!JudgeParam(dtParameter, "INP_QUERY_OUTP_DAY"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "INP_QUERY_OUTP_DAY", "-1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "INP_QUERY_OUTP_DAY"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "INP_QUERY_OUTP_DAY", "-1", "整数", "住院医生站查询门诊数据天数,小于0时不显示门诊节点", "门诊");
                }
            }
            #endregion
            #endregion
            #region 疾病控制
            #region 疾病控制厂家
            if (!JudgeParam(dtParameter, "DISEASE_FIRM"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "DISEASE_FIRM", "天健");
                if (!JudgeParamBaseinfo(dtBaseinfo, "DISEASE_FIRM"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "DISEASE_FIRM", "天健", "天健,蓝蜻蜓,南泉", "疾病控制厂家", "疾病控制");
                }
            }
            #endregion
            #region 疾病控制版本号
            if (!JudgeParam(dtParameter, "DISEASE_VERSION"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "DISEASE_VERSION", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "DISEASE_VERSION"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "DISEASE_VERSION", "", "", "疾病控制版本号", "疾病控制");
                }
            }
            #endregion
            #region 南泉
            #region 南泉--疾病上报URL地址
            if (!JudgeParam(dtParameter, "DISEASE_FIRM_NANQUAN_REPORT_URL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "DISEASE_FIRM_NANQUAN_REPORT_URL", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "DISEASE_FIRM_NANQUAN_REPORT_URL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "DISEASE_FIRM_NANQUAN_REPORT_URL", "", "URL地址", "南泉--疾病上报URL地址", "疾病控制");
                }
            }
            #endregion
            #endregion
            #endregion
            #region 感染控制
            #region 感染控制厂家
            if (!JudgeParam(dtParameter, "INFECTED_FIRM"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "INFECTED_FIRM", "天健");
                if (!JudgeParamBaseinfo(dtBaseinfo, "INFECTED_FIRM"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "INFECTED_FIRM", "天健", "", "感染控制厂家", "感染控制");
                }
            }
            #endregion
            #region 感染控制版本号
            if (!JudgeParam(dtParameter, "INFECTED_VERSION"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "INFECTED_VERSION", "天健");
                if (!JudgeParamBaseinfo(dtBaseinfo, "INFECTED_VERSION"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "INFECTED_VERSION", "天健", "", "感染控制版本号", "感染控制");
                }
            }
            #endregion
            #region 杏林
            #region 杏林上报URL地址
            if (!JudgeParam(dtParameter, "INFECTED_FIRM_XINGLIN_REPORT_URL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "INFECTED_FIRM_XINGLIN_REPORT_URL", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "INFECTED_FIRM_XINGLIN_REPORT_URL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "INFECTED_FIRM_XINGLIN_REPORT_URL", "", "URL地址", "杏林上报URL地址", "感染控制");
                }
            }
            #endregion
            #endregion 
            #endregion
            #region 公共参数
            #region 是否启用医生分限用药
            if (!JudgeParam(dtParameter, "LIMIT_DRUG"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "LIMIT_DRUG", "1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "LIMIT_DRUG"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "LIMIT_DRUG", "1", "0,1,2", "是否启用医生分限用药：0不启用(默认)，1启用只提示，2启用并限制继续用药", "公共参数");
                }
            }
            #endregion
            #region 药库维护专科用药等级(DRUG_DICT.LIMIT_CLASS),用于医生开药限制
            if (!JudgeParam(dtParameter, "LIMIT_DRUG_SPECIALTY"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "LIMIT_DRUG_SPECIALTY", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "LIMIT_DRUG_SPECIALTY"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "LIMIT_DRUG_SPECIALTY", "", "与药品等级对应", "药库维护专科用药等级(DRUG_DICT.LIMIT_CLASS),用于医生开药限制", "公共参数");
                }
            }
            #endregion
            #region 分线用药的通用级别(DRUG_DICT.LIMIT_CLASS)，不受医生级别与分线等级控制
            if (!JudgeParam(dtParameter, "LIMIT_DRUG_PUBLIC"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "LIMIT_DRUG_PUBLIC", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "LIMIT_DRUG_PUBLIC"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "LIMIT_DRUG_PUBLIC", "", "与药品等级对应", "分线用药的通用级别(DRUG_DICT.LIMIT_CLASS)，不受医生级别与分线等级控制", "公共参数");
                }
            }
            #endregion
            #region 输入法是否进行前后模糊匹
            if (!JudgeParam(dtParameter, "IS_FUZZY_FILTER"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "IS_FUZZY_FILTER", "1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "IS_FUZZY_FILTER"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "IS_FUZZY_FILTER", "1", "0,1", "输入法是否进行前后模糊匹 1--是 0--否", "公共参数");
                }
            }
            #endregion
            #region 输入法采用的类型
            if (!JudgeParam(dtParameter, "INPUT"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "INPUT", "01");
                if (!JudgeParamBaseinfo(dtBaseinfo, "INPUT"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "INPUT", "01", "01,02,03", "输入法采用的类型 ,输入法类别字典 input_type ,默认值是01 拼音输入法", "公共参数");
                }
            }
            #endregion
            #region 在费用发生的地方对预交金不足提示
            if (!JudgeParam(dtParameter, "PAYPERMENT_CHARGE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PAYPERMENT_CHARGE", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PAYPERMENT_CHARGE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PAYPERMENT_CHARGE", "", "费别字典", "在费用发生的地方对预交金不足提示，不进行计费 ，各种费别之间用分号区分,所有的费别用*来代替，反之用空串来代替", "公共参数");
                }
            }
            #endregion
            #region 是否进行透支付管理
            if (!JudgeParam(dtParameter, "PREPAYMENT_ADD"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PREPAYMENT_ADD", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PREPAYMENT_ADD"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PREPAYMENT_ADD", "0", "0,1", "是否进行透支付管理 0，允许 ,1不允许,不管欠不欠费都可以开医嘱", "公共参数");
                }
            }
            #endregion
            #region 透支方式
            if (!JudgeParam(dtParameter, "OVERDRAFT_WAY"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "OVERDRAFT_WAY", "1");
                if (!JudgeParamBaseinfo(dtBaseinfo, "OVERDRAFT_WAY"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "OVERDRAFT_WAY", "1", "1,2", "透支方式(OVERDRAFT_WAY)：平台级；取值：1 - 各个透支排斥；2 - 各个透支联合，即相加；", "公共参数");
                }
            }
            #endregion
            #region 余额中是否含透支额
            if (!JudgeParam(dtParameter, "PAYPERMENT_BALANCE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PAYPERMENT_BALANCE", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PAYPERMENT_BALANCE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PAYPERMENT_BALANCE", "0", "0,1", "余额中是否含透支额：1--余额中不含透支额  0--余额中含透支额", "公共参数");
                }
            }
            #endregion
            #region 公费用药费别
            if (!JudgeParam(dtParameter, "OFFICIAL_CHARGETYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "OFFICIAL_CHARGETYPE", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "OFFICIAL_CHARGETYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "OFFICIAL_CHARGETYPE", "", "费别字典", "公费用药费别", "公共参数");
                }
            }
            #endregion
            #region 是否启用公费用药消息提示
            if (!JudgeParam(dtParameter, "OFFICIAL_REMIND"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "OFFICIAL_REMIND", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "OFFICIAL_REMIND"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "OFFICIAL_REMIND", "0", "0,1", "是否启用公费用药消息提示：0不进行消息提示(默认)，1消息框提示", "公共参数");
                }
            }
            #endregion
            #region 缓存
            if (!JudgeParam(dtParameter, "CACHEINPUT_MINUTES"))
            {
                InserParameter(dtParameter, "*", "*", "*", "CACHEINPUT_MINUTES", "30");
                if (!JudgeParamBaseinfo(dtBaseinfo, "CACHEINPUT_MINUTES"))
                {
                    InsertBaseinfo(dtBaseinfo, "*", 1, "CACHEINPUT_MINUTES", "30", "大于0的整数", "刷新缓存间隔时间(分钟)", "公共参数");
                }
            }
            #endregion
            #region 消息提醒列表显示数量
            if (!JudgeParam(dtParameter, "MESSAGE_WARN_DISPLAY_AMOUNT"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "MESSAGE_WARN_DISPLAY_AMOUNT", "10");
                if (!JudgeParamBaseinfo(dtBaseinfo, "MESSAGE_WARN_DISPLAY_AMOUNT"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "MESSAGE_WARN_DISPLAY_AMOUNT", "10", "整数", "消息提醒列表显示数量 默认10条", "公共参数");
                }
            }
            #endregion
            #region 配置待选药房单位性质
            if (!JudgeParam(dtParameter, "CONFIG_SEL_STORAGE_TYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "CONFIG_SEL_STORAGE_TYPE", "药房");
                if (!JudgeParamBaseinfo(dtBaseinfo, "CONFIG_SEL_STORAGE_TYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "CONFIG_SEL_STORAGE_TYPE", "药房", "", "配置发药药房、摆药药房待选药房中单位性质(STORAGE_TYPE)，多个单位性质以分号分隔(;)", "公共参数");
                }
            }
            #endregion

            #endregion
            #region CA签名参数 
            #region CA厂家
            if (!JudgeParam(dtParameter, "CA_ID"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "CA_ID", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "CA_ID"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "CA_ID", "", "", "CA厂家", "数字认证");
                }
            }
            #endregion
            #region CA验证KEY时认证服务器IP地址
            if (!JudgeParam(dtParameter, "CA_SERVER_URL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "CA_SERVER_URL", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "CA_SERVER_URL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "CA_SERVER_URL", "", "", "CA验证KEY时认证服务器IP地址", "数字认证");
                }
            }
            #endregion
            #region CA验证KEY时认证服务器端口号
            if (!JudgeParam(dtParameter, "CA_SERVER_PORT"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "CA_SERVER_PORT", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "CA_SERVER_PORT"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "CA_SERVER_PORT", "", "", "CA验证KEY时认证服务器端口号", "数字认证");
                }
            }
            #endregion
            #region CA数据签名验证功能，1启用，0不启用，默认为0
            //if (!JudgeParam(dtParameter, "CA_DATASIGN_ENABLED"))
            //{
            //    InserParameter(dtParameter, "DOCTWS", "*", "*", "CA_DATASIGN_ENABLED", "0");
            //    if (!JudgeParamBaseinfo(dtBaseinfo, "CA_DATASIGN_ENABLED"))
            //    {
            //        InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "CA_DATASIGN_ENABLED", "0", "0,1", "CA数据签名验证功能，1启用，0不启用，默认为0", "数字认证");
            //    }
            //}
            #endregion
            #region 是否启用CA
            if (!JudgeParam(dtParameter, "CA_ENABLED"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "CA_ENABLED", "0");
                if (!JudgeParamBaseinfo(dtBaseinfo, "CA_ENABLED"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "CA_ENABLED", "0", "0,1", "是否启用UKey身份验证，0--不启用，1--可选用，2--强制使用,默认0", "数字认证");
                }
            }
            #endregion
            #endregion
            #region 麻醉记录地址 
            if (!JudgeParam(dtParameter, "ANAESTHESIA_RECORD_PATH"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "ANAESTHESIA_RECORD_PATH", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "ANAESTHESIA_RECORD_PATH"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "ANAESTHESIA_RECORD_PATH", "", "URL地址", "麻醉记录访问地址", "麻醉记录");
                }
            }
            #endregion
            #region 病案系统
            #region 病案系统厂家 
            if (!JudgeParam(dtParameter, "MEDICAL_RECORD_FIRM"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "MEDICAL_RECORD_FIRM", "天健");
                if (!JudgeParamBaseinfo(dtBaseinfo, "MEDICAL_RECORD_FIRM"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "MEDICAL_RECORD_FIRM", "天健", "天健", "病案厂家", "病案");
                }
            }
            #endregion
            #region 病案系统版本号
            if (!JudgeParam(dtParameter, "MEDICAL_RECORD_VISION"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "MEDICAL_RECORD_VISION", "6.6.2");
                if (!JudgeParamBaseinfo(dtBaseinfo, "MEDICAL_RECORD_VISION"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "MEDICAL_RECORD_VISION", "6.6.2", "6.6.2", "病案系统版本号", "病案");
                }
            }
            #endregion 
            #endregion
            #region 护理
            #region 住院护理病历厂家 
            if (!JudgeParam(dtParameter, "NURSING_INP_MR_FIRM"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "NURSING_INP_MR_FIRM", "天健");
                if (!JudgeParamBaseinfo(dtBaseinfo, "NURSING_INP_MR_FIRM"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "NURSING_INP_MR_FIRM", "天健", "天健", "护理病历厂家", "护理");
                }
            }
            #endregion
            #region 住院护理病历版本号
            if (!JudgeParam(dtParameter, "NURSING_INP_MR_VISION"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "NURSING_INP_MR_VISION", "6.6.2");
                if (!JudgeParamBaseinfo(dtBaseinfo, "NURSING_INP_MR_FIRM"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "NURSING_INP_MR_VISION", "6.6.2", "2.1,2.2,6.6.2", "住院护理病历版本号;2.1--DEV17.2以EXE方式调用;2.2--DEV18.1以EXE方式调用;6.6.2--融合版本", "护理");
                }
            }
            #endregion 
            #region 护理记录地址
            if (!JudgeParam(dtParameter, "NURSING_RECORD_PATH"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "NURSING_RECORD_PATH", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "NURSING_RECORD_PATH"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "NURSING_RECORD_PATH", "", "URL地址", "护理记录访问地址", "护理");
                }
            }
            #endregion
            #region 医保事前提醒
            #region 医保事前提醒区域
            if (!JudgeParam(dtParameter, "PRE_WARNING_REGION"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "PRE_WARNING_REGION", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "PRE_WARNING_REGION"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "PRE_WARNING_REGION", "", "区域名称", "医保事前提醒区域", "医保");
                }
            }
            #endregion
            #endregion
            #region 医疗质控监控
            #region 医疗质控监控厂家
            if (!JudgeParam(dtParameter, "MEDICAL_QUALITY_FIRM"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "MEDICAL_QUALITY_FIRM", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "MEDICAL_QUALITY_FIRM"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "MEDICAL_QUALITY_FIRM", "", "厂家名称", "医疗质控监控", "医疗质控");
                }
            }
            #endregion
            #region 是否启用医疗质量监控
            if (!JudgeParam(dtParameter, "YLZLKZ_ENABLED"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "YLZLKZ_ENABLED", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "YLZLKZ_ENABLED"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "YLZLKZ_ENABLED", "0", "0,1", "是否启用医疗质量监控:0--不启用;1--启用", "医疗质控");
                }
            }
            #endregion
            #region 医疗质量监控---定点机构ID
            if (!JudgeParam(dtParameter, "MEDICAL_QUALITY_HOSPITAL_ID"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "MEDICAL_QUALITY_HOSPITAL_ID", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "MEDICAL_QUALITY_HOSPITAL_ID"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "MEDICAL_QUALITY_HOSPITAL_ID", "", "", "医疗质量监控---定点机构ID", "医疗质控");
                }
            }
            #endregion
            #region 医疗质量监控---定点机构等级
            if (!JudgeParam(dtParameter, "MEDICAL_QUALITY_HOSPITAL_LEVEL"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "MEDICAL_QUALITY_HOSPITAL_LEVEL", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "MEDICAL_QUALITY_HOSPITAL_LEVEL"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "MEDICAL_QUALITY_HOSPITAL_LEVEL", "", "", "医疗质量监控---定点机构等级", "医疗质控");
                }
            }
            #endregion
            #region 医疗质量监控---定点机构类型
            if (!JudgeParam(dtParameter, "MEDICAL_QUALITY_HOSPITAL_TYPE"))
            {
                InserParameter(dtParameter, "DOCTWS", "*", "*", "MEDICAL_QUALITY_HOSPITAL_TYPE", "");
                if (!JudgeParamBaseinfo(dtBaseinfo, "MEDICAL_QUALITY_HOSPITAL_TYPE"))
                {
                    InsertBaseinfo(dtBaseinfo, "DOCTWS", 1, "MEDICAL_QUALITY_HOSPITAL_TYPE", "", "", "医疗质量监控---定点机构类型", "医疗质控");
                }
            }
            #endregion
            #endregion
            #endregion
           
            int rev = spc.SaveDataSet(ds);
            if(rev <0)
            {
                XtraMessageBox.Show("初始化参数时，保存参数失败！");
            }
            //if (!HisdtParameterSrv.UpdateDataTable(dtParameter, dtParameter, dtBaseinfo))
            //{
            //    XtraMessageBox.Show("初始化参数时，保存参数失败！");
            //}
            //Common.dtParameter = null;
            //PlatCommon.SysBase.SystemParm.DtParameter = null;
            sqlb = "select * from app_configer_parameter a where a.his_unit_code ='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' and  APP_NAME='*' OR APP_NAME='DOCTWS' OR APP_NAME='CP'";
            dtParameter = spc.GetDataBySql(sqlb).Tables[0];
            //PlatCommon.SysBase.SystemParm.DtParameter = dtParameter.Copy();
            dtParameter = null;
        }
        #endregion
        #region 判断参数是否存在
        public static Boolean JudgeParam(DataTable dtParameter, string appName, string paraName)
        {
            //return JudgeParam(dtParameter, appName, PlatCommon.SysBase.SystemParm.Deptcode, PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO, paraName);
            return JudgeParam(dtParameter, appName, "*", PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO, paraName);
        }
        public static Boolean JudgeParam(DataTable dtParameter, string appName, string deptCode, string empNo, string paraName)
        {
            string paraValue = string.Empty;
            
            dtParameter.CaseSensitive = true;
            dtParameter.PrimaryKey = new DataColumn[5]
            {
                    dtParameter.Columns["APP_NAME"],
                    dtParameter.Columns["DEPT_CODE"],
                    dtParameter.Columns["EMP_NO"],
                    dtParameter.Columns["PARAMETER_NAME"],
                    dtParameter.Columns["HIS_UNIT_CODE"]
            };
            DataRow drParameter = null;
            dtParameter.Rows.Find(new object[] { appName, deptCode, empNo, paraName, PlatCommon.SysBase.SystemParm.HisUnitCode });
            if (drParameter != null)
            {
                return true;
            }
                drParameter = dtParameter.Rows.Find(new object[] { appName, deptCode, "*", paraName, PlatCommon.SysBase.SystemParm.HisUnitCode });
            if (drParameter != null)
            {
                return true;
            }
                drParameter = dtParameter.Rows.Find(new object[] { appName, "*", empNo, paraName, PlatCommon.SysBase.SystemParm.HisUnitCode });

            if (drParameter != null)
            {
                return true;
            }
                drParameter = dtParameter.Rows.Find(new object[] { appName, "*", "*", paraName, PlatCommon.SysBase.SystemParm.HisUnitCode });

            if (drParameter != null)
            {
                return true;
            }
                drParameter = dtParameter.Rows.Find(new object[] { "*", "*", "*", paraName, PlatCommon.SysBase.SystemParm.HisUnitCode });

            if (drParameter != null)
            {
                return true;
            }
            return false;
        }
        /// <summary>
        /// 判断参数是否存在
        /// </summary>
        /// <param name="paraName">参数名称</param>
        /// <returns>true--存在;false--不存在</returns>
        public static Boolean JudgeParam(DataTable dtParameter, string paraName)
        {
            //return JudgeParam(dtParameter, PlatCommon.SysBase.SystemParm.AppName, PlatCommon.SysBase.SystemParm.Deptcode, PlatCommon.SysBase.SystemParm.HisUnitCode, paraName);
            return JudgeParam(dtParameter, "*", "*", PlatCommon.SysBase.SystemParm.HisUnitCode, paraName);
        }
        #endregion
        #region 判断参数说明是否存在
        /// <summary>
        /// 判断参数说明是否存在
        /// </summary>
        /// <param name="dtBaseinfo">参数说明表</param>
        /// <param name="parameterName">参数名称</param>
        /// <returns></returns>
        public static Boolean JudgeParamBaseinfo(DataTable dtBaseinfo, string parameterName)
        {
            DataRow[] drs = dtBaseinfo.Select("PARAMETER_NAME='" + parameterName + "'");
            if (drs != null && drs.Length > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion
        #region 插入参数表
        /// <summary>
        /// 插入参数表
        /// </summary>
        /// <param name="dtParameter">参数表</param>
        /// <param name="appName">应用名</param>
        /// <param name="deptCode">适用科室</param>
        /// <param name="empNo">适用人员</param>
        /// <param name="parameterName">参数名称</param>
        /// <param name="parameterValue">参数值</param>
        private static void InserParameter(DataTable dtParameter, string appName, string deptCode, string empNo, string parameterName, string parameterValue)
        {
            DataRow[] drs = dtParameter.Select("APP_NAME ='" + appName + "' and DEPT_CODE ='" + deptCode + "' and EMP_NO ='" + empNo + "' and PARAMETER_NAME ='" + parameterName + "' and HIS_UNIT_CODE ='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'");
            if (drs.Length <= 0)
            {
                DataRow drNew = dtParameter.NewRow();
                drNew["APP_NAME"] = appName;
                drNew["DEPT_CODE"] = deptCode;
                drNew["EMP_NO"] = empNo;
                drNew["PARAMETER_NAME"] = parameterName;
                drNew["PARAMETER_VALUE"] = parameterValue;
                drNew["HIS_UNIT_CODE"] = PlatCommon.SysBase.SystemParm.HisUnitCode;
                dtParameter.Rows.Add(drNew);
            }
        }
        #endregion
        #region 插入参数说明表
        /// <summary>
        /// 插入参数说明表
        /// </summary>
        /// <param name="dtBaseinfo">参数说明表</param>
        /// <param name="appName">应用名</param>
        /// <param name="parameterNo">参数序号</param>
        /// <param name="parameterName">参数名称</param>
        /// <param name="parainitValue">参数初始值</param>
        /// <param name="parameterScope">取值范围</param>
        /// <param name="explanation">说明</param>
        /// <param name="parameterClass">参数分类</param>
        private static void InsertBaseinfo(DataTable dtBaseinfo, string appName, int parameterNo, string parameterName, string parainitValue, string parameterScope, string explanation, string parameterClass)
        {
            DataRow[] drs = dtBaseinfo.Select("APP_NAME ='" + appName + "' and PARAMETER_NAME ='" + parameterName + "'");
            if (drs.Length <= 0)
            {
                DataRow drNew = dtBaseinfo.NewRow();
                drNew["APP_NAME"] = appName;
                drNew["PARAMETER_NO"] = parameterNo;
                drNew["PARAMETER_NAME"] = parameterName;
                drNew["PARAINIT_VALUE"] = parainitValue;
                drNew["PARAMETER_SCOPE"] = parameterScope;
                drNew["EXPLANATION"] = explanation;
                drNew["PARAMETER_CLASS"] = parameterClass;
                dtBaseinfo.Rows.Add(drNew);
            }
        }
        #endregion
    }
}
