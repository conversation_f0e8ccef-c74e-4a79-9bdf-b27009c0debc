﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;

namespace Model.EMR
{
    public partial class EMR_3RD_FILE_INDEX
    {
        public EMR_3RD_FILE_INDEX()
        { }
        #region Model
        private string _patient_id;
        private decimal _visit_id;
        private decimal _file_no;
        private decimal _file_sub_no;
        private decimal? _serial_no;
        private string _requst_no;
        private string _app_code;
        private string _file_content_type_name;
        private string _file_type;
        private string _file_path;
        private string _file_name;
        private DateTime? _create_date_time;
        private string _file_content_type_code;
        private string _topic;
        
        /// <summary>
        /// #患者ID
        /// </summary>
        public string TOPIC
        {
            set { _topic = value; }
            get { return _topic; }
        }
        /// <summary>
        /// #患者ID
        /// </summary>
        public string PATIENT_ID
        {
            set { _patient_id = value; }
            get { return _patient_id; }
        }
        /// <summary>
        /// #住院次数
        /// </summary>
        public decimal VISIT_ID
        {
            set { _visit_id = value; }
            get { return _visit_id; }
        }
        /// <summary>
        /// #文件号
        /// </summary>
        public decimal FILE_NO
        {
            set { _file_no = value; }
            get { return _file_no; }
        }
        /// <summary>
        /// #子序号
        /// </summary>
        public decimal FILE_SUB_NO
        {
            set { _file_sub_no = value; }
            get { return _file_sub_no; }
        }
        /// <summary>
        /// 显示序号
        /// </summary>
        public decimal? SERIAL_NO
        {
            set { _serial_no = value; }
            get { return _serial_no; }
        }
        /// <summary>
        /// 文件申请号
        /// </summary>
        public string REQUST_NO
        {
            set { _requst_no = value; }
            get { return _requst_no; }
        }
        /// <summary>
        /// 文件创建系统的代码
        /// </summary>
        public string APP_CODE
        {
            set { _app_code = value; }
            get { return _app_code; }
        }
        /// <summary>
        /// 文件内容类型名称
        /// </summary>
        public string FILE_CONTENT_TYPE_NAME
        {
            set { _file_content_type_name = value; }
            get { return _file_content_type_name; }
        }
        /// <summary>
        /// 文件类型
        /// </summary>
        public string FILE_TYPE
        {
            set { _file_type = value; }
            get { return _file_type; }
        }
        /// <summary>
        /// 相对路径
        /// </summary>
        public string FILE_PATH
        {
            set { _file_path = value; }
            get { return _file_path; }
        }
        /// <summary>
        /// 文件名
        /// </summary>
        public string FILE_NAME
        {
            set { _file_name = value; }
            get { return _file_name; }
        }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CREATE_DATE_TIME
        {
            set { _create_date_time = value; }
            get { return _create_date_time; }
        }
        /// <summary>
        /// 文件内容类型代码
        /// </summary>
        public string FILE_CONTENT_TYPE_CODE
        {
            set { _file_content_type_code = value; }
            get { return _file_content_type_code; }
        }
        #endregion Model

    }
}
