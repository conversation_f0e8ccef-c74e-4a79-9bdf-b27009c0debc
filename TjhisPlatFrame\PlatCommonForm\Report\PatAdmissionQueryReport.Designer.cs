﻿namespace PlatCommonForm.Report
{
    partial class PatAdmissionQueryReport
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraReports.UI.XRSummary xrSummary1 = new DevExpress.XtraReports.UI.XRSummary();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.GroupFooter1 = new DevExpress.XtraReports.UI.GroupFooterBand();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrlblDateBegin = new DevExpress.XtraReports.UI.XRLabel();
            this.xrlblDateEnd = new DevExpress.XtraReports.UI.XRLabel();
            this.GroupHeader1 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.xrlblPatientID = new DevExpress.XtraReports.UI.XRLabel();
            this.xrlblName = new DevExpress.XtraReports.UI.XRLabel();
            this.xrlblIdentity = new DevExpress.XtraReports.UI.XRLabel();
            this.xrlblChargeType = new DevExpress.XtraReports.UI.XRLabel();
            this.xrlblVisitID = new DevExpress.XtraReports.UI.XRLabel();
            this.xrlblAdmissionDate = new DevExpress.XtraReports.UI.XRLabel();
            this.xrlblAdmissionDept = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel10 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel11 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrSummaryGroup = new DevExpress.XtraReports.UI.XRLabel();
            this.xrSummary = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel14 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel15 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrPatientID = new DevExpress.XtraReports.UI.XRLabel();
            this.xrName = new DevExpress.XtraReports.UI.XRLabel();
            this.xrIdentity = new DevExpress.XtraReports.UI.XRLabel();
            this.xrChargeType = new DevExpress.XtraReports.UI.XRLabel();
            this.xrVisitID = new DevExpress.XtraReports.UI.XRLabel();
            this.xrAdmissionDate = new DevExpress.XtraReports.UI.XRLabel();
            this.xrAdmissionDept = new DevExpress.XtraReports.UI.XRLabel();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPatientID,
            this.xrName,
            this.xrIdentity,
            this.xrChargeType,
            this.xrVisitID,
            this.xrAdmissionDate,
            this.xrAdmissionDept});
            this.Detail.HeightF = 28.20835F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrlblDateEnd,
            this.xrlblDateBegin,
            this.xrLabel3,
            this.xrLabel2,
            this.xrLabel1});
            this.TopMargin.HeightF = 93.75F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel15,
            this.xrLabel11,
            this.xrSummary});
            this.BottomMargin.HeightF = 47.91667F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // GroupFooter1
            // 
            this.GroupFooter1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel14,
            this.xrSummaryGroup,
            this.xrLabel10});
            this.GroupFooter1.HeightF = 26.04167F;
            this.GroupFooter1.Name = "GroupFooter1";
            // 
            // xrLabel1
            // 
            this.xrLabel1.Font = new System.Drawing.Font("微软雅黑", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(0F, 30.45832F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(750F, 23F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "入院患者查询";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel2
            // 
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(318.1251F, 68.04166F);
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(83.33328F, 23F);
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            this.xrLabel2.Text = "入院时间：";
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel3
            // 
            this.xrLabel3.LocationFloat = new DevExpress.Utils.PointFloat(551.4584F, 68.04166F);
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel3.SizeF = new System.Drawing.SizeF(38.5416F, 23F);
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            this.xrLabel3.Text = "到";
            this.xrLabel3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrlblDateBegin
            // 
            this.xrlblDateBegin.LocationFloat = new DevExpress.Utils.PointFloat(401.4584F, 68.04166F);
            this.xrlblDateBegin.Name = "xrlblDateBegin";
            this.xrlblDateBegin.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrlblDateBegin.SizeF = new System.Drawing.SizeF(150F, 23F);
            this.xrlblDateBegin.StylePriority.UseTextAlignment = false;
            this.xrlblDateBegin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrlblDateEnd
            // 
            this.xrlblDateEnd.LocationFloat = new DevExpress.Utils.PointFloat(589.9999F, 68.04166F);
            this.xrlblDateEnd.Name = "xrlblDateEnd";
            this.xrlblDateEnd.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrlblDateEnd.SizeF = new System.Drawing.SizeF(150F, 23F);
            this.xrlblDateEnd.StylePriority.UseTextAlignment = false;
            this.xrlblDateEnd.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // GroupHeader1
            // 
            this.GroupHeader1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrlblAdmissionDept,
            this.xrlblAdmissionDate,
            this.xrlblVisitID,
            this.xrlblChargeType,
            this.xrlblIdentity,
            this.xrlblName,
            this.xrlblPatientID});
            this.GroupHeader1.HeightF = 25.08335F;
            this.GroupHeader1.Name = "GroupHeader1";
            // 
            // xrlblPatientID
            // 
            this.xrlblPatientID.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrlblPatientID.Name = "xrlblPatientID";
            this.xrlblPatientID.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrlblPatientID.SizeF = new System.Drawing.SizeF(110.4167F, 25.08335F);
            this.xrlblPatientID.StylePriority.UseTextAlignment = false;
            this.xrlblPatientID.Text = "患者ID";
            this.xrlblPatientID.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrlblName
            // 
            this.xrlblName.LocationFloat = new DevExpress.Utils.PointFloat(110.4167F, 0F);
            this.xrlblName.Name = "xrlblName";
            this.xrlblName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrlblName.SizeF = new System.Drawing.SizeF(107.2918F, 25.08335F);
            this.xrlblName.StylePriority.UseTextAlignment = false;
            this.xrlblName.Text = "患者姓名";
            this.xrlblName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrlblIdentity
            // 
            this.xrlblIdentity.LocationFloat = new DevExpress.Utils.PointFloat(217.7085F, 0F);
            this.xrlblIdentity.Name = "xrlblIdentity";
            this.xrlblIdentity.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrlblIdentity.SizeF = new System.Drawing.SizeF(100.4165F, 25.08335F);
            this.xrlblIdentity.StylePriority.UseTextAlignment = false;
            this.xrlblIdentity.Text = "人员类别";
            this.xrlblIdentity.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrlblChargeType
            // 
            this.xrlblChargeType.LocationFloat = new DevExpress.Utils.PointFloat(318.1251F, 0F);
            this.xrlblChargeType.Name = "xrlblChargeType";
            this.xrlblChargeType.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrlblChargeType.SizeF = new System.Drawing.SizeF(95.83322F, 25.08335F);
            this.xrlblChargeType.StylePriority.UseTextAlignment = false;
            this.xrlblChargeType.Text = "费用类别";
            this.xrlblChargeType.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrlblVisitID
            // 
            this.xrlblVisitID.LocationFloat = new DevExpress.Utils.PointFloat(413.9583F, 0F);
            this.xrlblVisitID.Name = "xrlblVisitID";
            this.xrlblVisitID.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrlblVisitID.SizeF = new System.Drawing.SizeF(86.04178F, 25.08335F);
            this.xrlblVisitID.StylePriority.UseTextAlignment = false;
            this.xrlblVisitID.Text = "住院次数";
            this.xrlblVisitID.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrlblAdmissionDate
            // 
            this.xrlblAdmissionDate.LocationFloat = new DevExpress.Utils.PointFloat(500F, 0F);
            this.xrlblAdmissionDate.Name = "xrlblAdmissionDate";
            this.xrlblAdmissionDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrlblAdmissionDate.SizeF = new System.Drawing.SizeF(113.5416F, 25.08335F);
            this.xrlblAdmissionDate.StylePriority.UseTextAlignment = false;
            this.xrlblAdmissionDate.Text = "入院时间";
            this.xrlblAdmissionDate.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrlblAdmissionDept
            // 
            this.xrlblAdmissionDept.LocationFloat = new DevExpress.Utils.PointFloat(613.5416F, 0F);
            this.xrlblAdmissionDept.Name = "xrlblAdmissionDept";
            this.xrlblAdmissionDept.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrlblAdmissionDept.SizeF = new System.Drawing.SizeF(136.4584F, 25.08335F);
            this.xrlblAdmissionDept.StylePriority.UseTextAlignment = false;
            this.xrlblAdmissionDept.Text = "入院科室";
            this.xrlblAdmissionDept.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel10
            // 
            this.xrLabel10.LocationFloat = new DevExpress.Utils.PointFloat(457.2919F, 0F);
            this.xrLabel10.Name = "xrLabel10";
            this.xrLabel10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel10.SizeF = new System.Drawing.SizeF(104.1666F, 23F);
            this.xrLabel10.StylePriority.UseTextAlignment = false;
            this.xrLabel10.Text = "分组小计：";
            this.xrLabel10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel11
            // 
            this.xrLabel11.LocationFloat = new DevExpress.Utils.PointFloat(457.2919F, 3.999996F);
            this.xrLabel11.Name = "xrLabel11";
            this.xrLabel11.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel11.SizeF = new System.Drawing.SizeF(104.1666F, 23F);
            this.xrLabel11.StylePriority.UseTextAlignment = false;
            this.xrLabel11.Text = "合计：";
            this.xrLabel11.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrSummaryGroup
            // 
            this.xrSummaryGroup.LocationFloat = new DevExpress.Utils.PointFloat(561.4584F, 0F);
            this.xrSummaryGroup.Name = "xrSummaryGroup";
            this.xrSummaryGroup.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrSummaryGroup.SizeF = new System.Drawing.SizeF(119.1666F, 23F);
            this.xrSummaryGroup.StylePriority.UseTextAlignment = false;
            this.xrSummaryGroup.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrSummary
            // 
            this.xrSummary.LocationFloat = new DevExpress.Utils.PointFloat(561.4584F, 3.999996F);
            this.xrSummary.Name = "xrSummary";
            this.xrSummary.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrSummary.SizeF = new System.Drawing.SizeF(119.1666F, 23F);
            this.xrSummary.StylePriority.UseTextAlignment = false;
            this.xrSummary.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel14
            // 
            this.xrLabel14.LocationFloat = new DevExpress.Utils.PointFloat(680.625F, 0F);
            this.xrLabel14.Name = "xrLabel14";
            this.xrLabel14.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel14.SizeF = new System.Drawing.SizeF(47.91663F, 23F);
            this.xrLabel14.StylePriority.UseTextAlignment = false;
            this.xrLabel14.Text = "人";
            this.xrLabel14.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel15
            // 
            this.xrLabel15.LocationFloat = new DevExpress.Utils.PointFloat(680.6249F, 4F);
            this.xrLabel15.Name = "xrLabel15";
            this.xrLabel15.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel15.SizeF = new System.Drawing.SizeF(47.91663F, 23F);
            this.xrLabel15.StylePriority.UseTextAlignment = false;
            this.xrLabel15.Text = "人";
            this.xrLabel15.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrPatientID
            // 
            this.xrPatientID.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrPatientID.Name = "xrPatientID";
            this.xrPatientID.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrPatientID.SizeF = new System.Drawing.SizeF(110.4167F, 28.20835F);
            this.xrPatientID.StylePriority.UseTextAlignment = false;
            this.xrPatientID.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrName
            // 
            this.xrName.LocationFloat = new DevExpress.Utils.PointFloat(110.4167F, 0F);
            this.xrName.Name = "xrName";
            this.xrName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrName.SizeF = new System.Drawing.SizeF(107.2918F, 28.20835F);
            this.xrName.StylePriority.UseTextAlignment = false;
            this.xrName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrIdentity
            // 
            this.xrIdentity.LocationFloat = new DevExpress.Utils.PointFloat(217.7085F, 0F);
            this.xrIdentity.Name = "xrIdentity";
            this.xrIdentity.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrIdentity.SizeF = new System.Drawing.SizeF(100.4165F, 28.20835F);
            this.xrIdentity.StylePriority.UseTextAlignment = false;
            this.xrIdentity.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrChargeType
            // 
            this.xrChargeType.LocationFloat = new DevExpress.Utils.PointFloat(318.1251F, 0F);
            this.xrChargeType.Name = "xrChargeType";
            this.xrChargeType.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrChargeType.SizeF = new System.Drawing.SizeF(95.83322F, 28.20835F);
            this.xrChargeType.StylePriority.UseTextAlignment = false;
            this.xrChargeType.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrVisitID
            // 
            this.xrVisitID.LocationFloat = new DevExpress.Utils.PointFloat(413.9583F, 0F);
            this.xrVisitID.Name = "xrVisitID";
            this.xrVisitID.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrVisitID.SizeF = new System.Drawing.SizeF(86.04178F, 28.20835F);
            this.xrVisitID.StylePriority.UseTextAlignment = false;
            this.xrVisitID.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrAdmissionDate
            // 
            this.xrAdmissionDate.LocationFloat = new DevExpress.Utils.PointFloat(500F, 0F);
            this.xrAdmissionDate.Name = "xrAdmissionDate";
            this.xrAdmissionDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrAdmissionDate.SizeF = new System.Drawing.SizeF(113.5416F, 28.20835F);
            this.xrAdmissionDate.StylePriority.UseTextAlignment = false;
            this.xrAdmissionDate.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrAdmissionDept
            // 
            this.xrAdmissionDept.LocationFloat = new DevExpress.Utils.PointFloat(613.5416F, 0F);
            this.xrAdmissionDept.Name = "xrAdmissionDept";
            this.xrAdmissionDept.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrAdmissionDept.SizeF = new System.Drawing.SizeF(136.4584F, 28.20835F);
            this.xrAdmissionDept.StylePriority.UseTextAlignment = false;
            xrSummary1.Running = DevExpress.XtraReports.UI.SummaryRunning.Group;
            this.xrAdmissionDept.Summary = xrSummary1;
            this.xrAdmissionDept.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // PatAdmissionQueryReport
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.GroupFooter1,
            this.GroupHeader1});
            this.Margins = new System.Drawing.Printing.Margins(50, 50, 94, 48);
            this.Version = "15.2";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.GroupFooterBand GroupFooter1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel xrlblDateEnd;
        private DevExpress.XtraReports.UI.XRLabel xrlblDateBegin;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.GroupHeaderBand GroupHeader1;
        private DevExpress.XtraReports.UI.XRLabel xrlblAdmissionDept;
        private DevExpress.XtraReports.UI.XRLabel xrlblAdmissionDate;
        private DevExpress.XtraReports.UI.XRLabel xrlblVisitID;
        private DevExpress.XtraReports.UI.XRLabel xrlblChargeType;
        private DevExpress.XtraReports.UI.XRLabel xrlblIdentity;
        private DevExpress.XtraReports.UI.XRLabel xrlblName;
        private DevExpress.XtraReports.UI.XRLabel xrlblPatientID;
        private DevExpress.XtraReports.UI.XRLabel xrLabel15;
        private DevExpress.XtraReports.UI.XRLabel xrLabel11;
        private DevExpress.XtraReports.UI.XRLabel xrSummary;
        private DevExpress.XtraReports.UI.XRLabel xrLabel14;
        private DevExpress.XtraReports.UI.XRLabel xrSummaryGroup;
        private DevExpress.XtraReports.UI.XRLabel xrLabel10;
        private DevExpress.XtraReports.UI.XRLabel xrPatientID;
        private DevExpress.XtraReports.UI.XRLabel xrName;
        private DevExpress.XtraReports.UI.XRLabel xrIdentity;
        private DevExpress.XtraReports.UI.XRLabel xrChargeType;
        private DevExpress.XtraReports.UI.XRLabel xrVisitID;
        private DevExpress.XtraReports.UI.XRLabel xrAdmissionDate;
        private DevExpress.XtraReports.UI.XRLabel xrAdmissionDept;
    }
}
