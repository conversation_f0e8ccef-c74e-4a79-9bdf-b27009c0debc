﻿/*-----------------------------------------------------------------------
 * 类名称    ：FrmSetDate
 * 类描述    ：
 * 创建人    ：梁吉lions
 * 创建时间  ：2016/6/16 11:27:50
 * 修改人    ：
 * 修改时间  ：
 * 修改备注  ：
 * 版本      ：
 * ----------------------------------------------------------------------
 */
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace PlatCommonForm
{
    public partial class FrmSetDate : PlatCommon.SysBase.ParentForm
    {
        public string DateString = "";
        public string PatientID = "";
        public string CaptionText = "";
        private DateTime disdate;
        public FrmSetDate()
        {
            InitializeComponent();
            dateEdit1.DateTime = new NM_Service.NMService.ServerPublicClient().GetSysDate();
        }
        public FrmSetDate(string tCaption):this()
        {
            SetCaption(tCaption);
        }
        public void SetCaption(string myCaption)
        {
            groupControl1.Text = myCaption;
        }
        /// <summary>
        /// 设置按钮文本
        /// </summary>
        /// <param name="myCaption"></param>
        /// <param name="idx"></param>
        public void SetButtonCaption(string myCaption,int idx)
        {
            if (idx == 1) simpleButton1.Text = myCaption;
            else if (idx == 2) simpleButton2.Text = myCaption;
        }

        public void SetButtonVisble(bool enabled, int idx)
        {
            if (idx == 1) simpleButton1.Visible = enabled;
            else if (idx == 2) simpleButton2.Visible = enabled;
        }

        public void SetLabel(string text)
        {
            labelControl1.Text = text;
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(dateEdit1.Text))
            {
                XtraMessageBox.Show("请输入时间!", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                dateEdit1.Focus();
                return;
            }
            DateTime inputdate = DateTime.Parse(dateEdit1.DateTime.ToString("yyyy-MM-dd HH:mm:ss").Substring(0,dateEdit1.DateTime.ToString("yyyy-MM-dd HH24:mm:ss").Length-5));
            DateTime sysdate = DateTime.Parse(new NM_Service.NMService.ServerPublicClient().GetSysDate().ToString("yyyy-MM-dd HH:mm:ss").Substring(0, new NM_Service.NMService.ServerPublicClient().GetSysDate().ToString("yyyy-MM-dd HH:mm:ss").Length - 3));
            double hours = (inputdate - sysdate).TotalHours;
            if (hours > 720)
            {
                XtraMessageBox.Show("停止时间不能超过当前720小时!", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                dateEdit1.Focus();
                return;
            }
            else if (hours < 0)
            {
                XtraMessageBox.Show("停止时间不能小于当前时间!", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                dateEdit1.Focus();
                return;
            }
            DateString = inputdate.ToString("yyyy-MM-dd HH:mm:ss");
            this.Close();
        }

        private void simpleButton2_Click(object sender, EventArgs e)
        {
            DateString = "";
            this.Close();
        }
    }
}
