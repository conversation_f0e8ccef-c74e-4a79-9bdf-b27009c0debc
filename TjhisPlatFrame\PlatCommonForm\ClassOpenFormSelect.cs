﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace PlatCommonForm
{
    public class ClassOpenFormSelect
    {
        public static int OpenPkpForm(string appname)
        {
            string is_pkp = Utility.ConfigHelper.GetConfigString("PKP").ToString();
            if (is_pkp.Equals("1"))
            {
                string ls_type = "";
                if (appname.Equals("IBILLING"))
                {
                    ls_type = "35";
                }
                else
                {
                    ls_type = "32";
                }
                frmPkp frmbs = null;
                for (int i = 0; i < Application.OpenForms.Count; i++)
                {
                    if (Application.OpenForms[i] is frmPkp)
                    {
                        frmbs = (frmPkp)Application.OpenForms[i];
                        frmbs.is_invotype = ls_type;
                        frmbs.wf_refresh();
                        break;
                    }
                }
                if (frmbs == null)
                {
                    frmPkp fp = new frmPkp();
                    fp.is_invotype = ls_type;
                    fp.Show();
                }
            }
            return 0;
        }
    }
}
