﻿using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmSelectedFiled : ParentForm
    {
        public string result { get; set; }
        public List<DataTable> DataSource  { get; set; }
        public Point panelPoint { get; set; }
        public bool showList { get; set; }

        public frmSelectedFiled()
        {
            InitializeComponent();
        }

        private void barLargeButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (xtraTabControl1.SelectedTabPage == xtraTabPage2)
            {
                result = richTextBox1.Text;
            }
            else if(xtraTabControl1.SelectedTabPage == xtraTabPage1)
            {
                DataRow dr = gridView1.GetFocusedDataRow();
                if (dr == null)
                    result = "";
                else
                {
                    DataView dt = gridView1.DataSource as DataView;
                    if (dt.Table.Columns.Contains("table_name"))
                    {
                        result = dr["table_name"].ToString() + "." + dr["column_name"].ToString();
                    }
                    else
                    {
                        result = dr["column_name"].ToString();
                    }
                }
            }
            this.DialogResult = DialogResult.OK;
        }

        private void frmSelectedFiled_Load(object sender, EventArgs e)
        {
            this.TransparencyKey = Color.Blue;
            this.WindowState = FormWindowState.Maximized;

            if (panelPoint.X < 0)
            {
                int _index = 0 - (this.Location.X - this.panelPoint.X);
                panelPoint = new Point(_index, panelPoint.Y);
            }
            panelControl1.Location = panelPoint;
            panelControl1.Visible = true;
            if (DataSource != null)
            {
                gridControl1.DataSource = GetAllDataTable(DataSource);
            }
            if (!string.IsNullOrEmpty(result) && !result.Contains("[<"))
            {
                richTextBox1.Text = result;
            }

            if (showList)
            {
                xtraTabControl1.SelectedTabPage = xtraTabPage1;
            }
            else
            {
                xtraTabControl1.SelectedTabPage = xtraTabPage2;
            }
        }

        private void gridView1_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            if(e.Clicks == 2)
            {
                DataRow dr = gridView1.GetFocusedDataRow();

                DataView dt = gridView1.DataSource as DataView;
                if (dt.Table.Columns.Contains("table_name"))
                {
                    result = dr["table_name"].ToString() + "." + dr["column_name"].ToString();
                }
                else
                {
                    result = dr["column_name"].ToString();
                }

                this.DialogResult = DialogResult.OK;
            }
        }

        public DataTable GetAllDataTable(List<DataTable> dtList)
        {
            if (dtList.Count == 0) return null;

            DataTable newDataTable = dtList[0].Clone();                //创建新表 克隆以有表的架构。
            object[] objArray = new object[newDataTable.Columns.Count];   //定义与表列数相同的对象数组 存放表的一行的值。
            dtList.ForEach(t => 
            {
                t.Select().ToList().ForEach(r => 
                {
                    r.ItemArray.CopyTo(objArray, 0);//将表的一行的值存放数组中。
                    newDataTable.Rows.Add(objArray);//将数组的值添加到新表中。
                });
            });
            return newDataTable; //返回新表。
        }

        private void barLargeButtonItem2_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void frmSelectedFiled_MouseDown(object sender, MouseEventArgs e)
        {
            if((e.X<panelPoint.X || e.X > this.panelPoint.X+this.panelControl1.Width)||(e.Y<this.panelPoint.Y || e.Y > this.panelPoint.Y + this.panelControl1.Height))
            {
                this.DialogResult = DialogResult.Cancel;
            }
        }

        private void frmSelectedFiled_MouseClick(object sender, MouseEventArgs e)
        {

        }
    }
}
