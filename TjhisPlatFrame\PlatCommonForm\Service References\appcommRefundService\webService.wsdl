<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="HelloWorld">
        <s:complexType />
      </s:element>
      <s:element name="HelloWorldResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="HelloWorldResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="orderRefundFromHis1">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="xmlparam" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="orderRefundFromHis1Response">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="orderRefundFromHis1Result" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="orderRefundFromHis2">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="xmlparam" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="orderRefundFromHis2Response">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="orderRefundFromHis2Result" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="HelloWorldSoapIn">
    <wsdl:part name="parameters" element="tns:HelloWorld" />
  </wsdl:message>
  <wsdl:message name="HelloWorldSoapOut">
    <wsdl:part name="parameters" element="tns:HelloWorldResponse" />
  </wsdl:message>
  <wsdl:message name="orderRefundFromHis1SoapIn">
    <wsdl:part name="parameters" element="tns:orderRefundFromHis1" />
  </wsdl:message>
  <wsdl:message name="orderRefundFromHis1SoapOut">
    <wsdl:part name="parameters" element="tns:orderRefundFromHis1Response" />
  </wsdl:message>
  <wsdl:message name="orderRefundFromHis2SoapIn">
    <wsdl:part name="parameters" element="tns:orderRefundFromHis2" />
  </wsdl:message>
  <wsdl:message name="orderRefundFromHis2SoapOut">
    <wsdl:part name="parameters" element="tns:orderRefundFromHis2Response" />
  </wsdl:message>
  <wsdl:portType name="webServiceSoap">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldSoapIn" />
      <wsdl:output message="tns:HelloWorldSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="orderRefundFromHis1">
      <wsdl:input message="tns:orderRefundFromHis1SoapIn" />
      <wsdl:output message="tns:orderRefundFromHis1SoapOut" />
    </wsdl:operation>
    <wsdl:operation name="orderRefundFromHis2">
      <wsdl:input message="tns:orderRefundFromHis2SoapIn" />
      <wsdl:output message="tns:orderRefundFromHis2SoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="webServiceSoap" type="tns:webServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="orderRefundFromHis1">
      <soap:operation soapAction="http://tempuri.org/orderRefundFromHis1" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="orderRefundFromHis2">
      <soap:operation soapAction="http://tempuri.org/orderRefundFromHis2" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="webServiceSoap12" type="tns:webServiceSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap12:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="orderRefundFromHis1">
      <soap12:operation soapAction="http://tempuri.org/orderRefundFromHis1" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="orderRefundFromHis2">
      <soap12:operation soapAction="http://tempuri.org/orderRefundFromHis2" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="webService">
    <wsdl:port name="webServiceSoap" binding="tns:webServiceSoap">
      <soap:address location="http://**************:1014/webService.asmx" />
    </wsdl:port>
    <wsdl:port name="webServiceSoap12" binding="tns:webServiceSoap12">
      <soap12:address location="http://**************:1014/webService.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>