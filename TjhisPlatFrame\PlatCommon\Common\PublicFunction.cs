﻿

//**********************************************
//说明:通用函数类
//计算机名称：LINDP
//创建日期：2016/6/23 14:50:05
//作者：林大鹏
//版本号：V1.00
//**********************************************

using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using NM_Service.NMService;
using System.Net;
using System.IO;
using System.Threading;
using System.Net.Sockets;
using System.Diagnostics;
using System.Collections;
using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System.Net.NetworkInformation;
using System.Reflection;

namespace PlatCommon.Common
{
    public class PublicFunction
    {
        [System.Runtime.InteropServices.DllImport("user32.dll", EntryPoint = "FindWindow")]
        public extern static IntPtr FindWindow(string lpClassName, string lpWindowName);

        [System.Runtime.InteropServices.DllImport("user32.dll", EntryPoint = "SendMessageA")]
        public static extern int SendMessage(IntPtr hwnd, int wMsg, int wParam, int lParam);

        [DllImport("EncryptDecrypt.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode)]
        public static extern bool DecryptStringForCS(string cipher, StringBuilder outputText);

  

        /// <summary>
        /// // 参  数: 
        ///		reference	decimal	ldprepayment   		// 预交金总额
        ///		reference	decimal	ldapprovemoney 		// 透支额度
        ///		reference	decimal	ldcharges      		// 未结算费用
        ///		reference	decimal	ldprepaybalance		// 预交金余额(根据参数设置, 可能包含透支额度)
        ///		reference	decimal	ldusemoney     		// 可用预交金总额(根据参数设置, 可能包含透支额度)
        /// 返  回: integer
        /// </summary>
        /// <returns></returns>
        public static int GetPrepayment(string appname, string patientid, string visitid, ref decimal ldprepayment, ref decimal ldapprovemoney, ref decimal ldcharges, ref decimal ldprepaybalance, ref decimal ldusemoney)
        {
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            if (!ValidData(appname, patientid, visitid)) return 0;
            string sql = @"
                select nvl(sum(amount), 0)
                  from inpbill.prepayment_rcpt
                 where patient_id = :t1
                   and visit_id = :t2
                   and transact_type <> '作废' ";
            ArrayList list = new ArrayList();
            List<string> paras = new List<string>();
            paras.Add("t1");
            paras.Add("t2");
            list.Add(patientid);
            list.Add(visitid);
            DataTable dt = spc.GetDataTable_Para(sql, paras, list).Tables[0];
            ldprepayment = decimal.Parse(dt.Rows[0][0].ToString());

            //本次住院未结算的实收费用=本次住院已经花费的-sum(本次住院已经结算的)
            sql = @"
                select nvl(sum(charges), 0)
                  from inp_bill_detail
                 where PATIENT_ID = :t1
                   and visit_id = :t2
                   and rcpt_no is null ";
            ArrayList list1 = new ArrayList();
            List<string> paras1 = new List<string>();
            paras1.Add("t1");
            paras1.Add("t2");
            list1.Add(patientid);
            list1.Add(visitid);
            dt = spc.GetDataTable_Para(sql, paras1, list1).Tables[0];
            ldcharges = decimal.Parse(dt.Rows[0][0].ToString());

            decimal ldl_tz = 0;
            GetOverDraft(appname, patientid, visitid, ref ldl_tz, ldprepayment);

            // 透支额
            ldapprovemoney = ldl_tz;
            // 可用预交金总额
            ldusemoney = ldprepayment;
            // 预交金余额
            ldprepaybalance = ldusemoney - ldcharges;

            string ls_config_value = PlatCommon.SysBase.SystemParm.GetParameterValue("PAYPERMENT_BALANCE", appname, "*", "*", SystemParm.HisUnitCode);
            if (ls_config_value.Equals("0"))
            {
                // 可用额
                ldusemoney += ldl_tz;
                // 预交金余额
                ldprepaybalance += ldl_tz;
            }
            //
            decimal ldc_hold = 0;//预扣金额
            GetWithhold(appname, patientid, visitid, ref ldc_hold);
            ls_config_value = SystemParm.GetParameterValue("PAYPERMENT_HOLD", appname, "*", "*", SystemParm.HisUnitCode);
            if (ls_config_value.Equals("1"))
            {
                // 预交金余额
                ldprepaybalance = ldprepaybalance - ldc_hold;
            }

            return 1;
        }

        //检查属性
        public static bool ValidData(string appname, string patientid, string visitid)
        {
            if (string.IsNullOrEmpty(appname))
                return false;
            if (string.IsNullOrEmpty(patientid))
                return false;
            if (string.IsNullOrEmpty(visitid))
                return false;
            return true;
        }

        //获取预扣金额
        public static int GetWithhold(string appname, string patientid, string visitid, ref decimal hold)
        {
            decimal ldc_exam, ldc_lab, ldc_drug;
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            //检查申请
            string sql = "select nvl(sum(b.costs),0) costs from exam_appoints a,exam_items b where a.exam_no=b.exam_no and a.patient_id =:t1 and a.visit_id =:t2 and a.queue_no is null ";
            ArrayList list = new ArrayList();
            List<string> paras = new List<string>();
            paras.Add("t1");
            paras.Add("t2");
            list.Add(patientid);
            list.Add(visitid);
            DataTable exam = spc.GetDataTable_Para(sql, paras, list).Tables[0];
            ldc_exam = decimal.Parse(exam.Rows[0]["COSTS"].ToString());
            //检验申请
            sql = "select nvl(sum(a.charges),0) charges from lab_test_items a,lab_test_master b where b.patient_id =:t1 and b.visit_id =:t2 and a.test_no=b.test_no and b.billing_indicator is null ";
            ArrayList list1 = new ArrayList();
            List<string> paras1 = new List<string>();
            paras1.Add("t1");
            paras1.Add("t2");
            list1.Add(patientid);
            list1.Add(visitid);
            DataTable lab = spc.GetDataTable_Para(sql, paras1, list1).Tables[0];
            ldc_lab = decimal.Parse(lab.Rows[0]["CHARGES"].ToString());
            //处方待发药
            sql = "select nvl(sum(a.payments),0) payments  from doct_drug_presc_master a where a.patient_id =:t1 and a.visit_id =:t2 and a.presc_status in ('0','3') ";
            ArrayList list2 = new ArrayList();
            List<string> paras2 = new List<string>();
            paras2.Add("t1");
            paras2.Add("t2");
            list2.Add(patientid);
            list2.Add(visitid);
            DataTable drug = spc.GetDataTable_Para(sql, paras2, list2).Tables[0];
            ldc_drug = decimal.Parse(drug.Rows[0]["PAYMENTS"].ToString());

            hold = ldc_exam + ldc_lab + ldc_drug;

            return 1;
        }

        //取透支额
        public static int GetOverDraft(string appname, string patientid, string visitid, ref decimal draftMoney, decimal prepayment)
        {
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            string sql = @"
                SELECT ward_code
                  From pats_in_hospital
                 where patient_id = :t1
                   and visit_id = :t2 ";
            ArrayList list = new ArrayList();
            List<string> paras = new List<string>();
            paras.Add("t1");
            paras.Add("t2");
            list.Add(patientid);
            list.Add(visitid);
            DataTable dt = spc.GetDataTable_Para(sql, paras, list).Tables[0];
            string ls_dept_code = "";
            if (dt.Rows.Count > 0)
            {
                ls_dept_code = dt.Rows[0][0].ToString();
            }

            //取费别
            string ls_charge_type = "";
            sql = @"
                select charge_type
                  from pat_visit
                 where patient_id = :t1
                   and visit_id = :t2 ";
            ArrayList list1 = new ArrayList();
            List<string> paras1 = new List<string>();
            paras1.Add("t1");
            paras1.Add("t2");
            list1.Add(patientid);
            list1.Add(visitid);
            dt = spc.GetDataTable_Para(sql, paras1, list1).Tables[0];
            if (dt.Rows.Count > 0)
            {
                ls_charge_type = dt.Rows[0][0].ToString();
            }

            //取病人透支额
            int ll_count_person = 0;
            decimal ld_person = 0;
            sql = @"
                select nvl(count(*), 0), nvl(sum(TOTAL_SANCTIFIED_AMOUNT), 0)
                  from INP_OVERDRAFT_REG_MASTER
                 where patient_id = :t1
                   and visit_id = :t2
                   and charge_type = '*'
                   and dept_code = '*' ";
            ArrayList list2 = new ArrayList();
            List<string> paras2 = new List<string>();
            paras2.Add("t1");
            paras2.Add("t2");
            list2.Add(patientid);
            list2.Add(visitid);
            dt = spc.GetDataTable_Para(sql, paras2, list2).Tables[0];
            ll_count_person = int.Parse(dt.Rows[0][0].ToString());
            ld_person = decimal.Parse(dt.Rows[0][1].ToString());

            //取特定费别透支额
            string psn_type = "";
            if (ls_charge_type.Equals("平台医保"))
            {
                psn_type = spc.GetSingleValue("select in_insutype from insurance.inp_register_ptyb where patient_id = '"+ patientid + "' and visit_id = '"+ visitid + "' and refund_date is null and rownum = 1");
            }
            psn_type = string.IsNullOrEmpty(psn_type) ? "" : psn_type;
            int ll_count_chargetype = 0;
            decimal ld_chargetype = 0;
            sql = @"
                select nvl(count(*), 0), nvl(sum(TOTAL_SANCTIFIED_AMOUNT), 0)
                  from INP_OVERDRAFT_REG_MASTER
                 where patient_id = '*'
                   and visit_id = 0
                   and charge_type = :t1
                   and dept_code = '*' ";
            if (ls_charge_type.Equals("平台医保") )
            {
                sql += " and psn_type = :t2";
            }
            ArrayList list3 = new ArrayList();
            List<string> paras3 = new List<string>();
            paras3.Add("t1");
            list3.Add(ls_charge_type);
            if (ls_charge_type.Equals("平台医保"))
            {
                paras3.Add("t2");
                list3.Add(psn_type);
            }
            dt = spc.GetDataTable_Para(sql, paras3, list3).Tables[0];
            ll_count_chargetype = int.Parse(dt.Rows[0][0].ToString());
            ld_chargetype = decimal.Parse(dt.Rows[0][1].ToString());

            //取特定费别比例透支额
            decimal ld_fee_ratio = 0, ldc_ratio = 0;
            decimal feeFd = 0;
            int ll_fee_ratio = 0;
            sql = @"
                select nvl(fee_ratio, 0) fee_ratio, nvl(fee_fd, 0) fee_fd
                  from INP_OVERDRAFT_REG_MASTER
                 where patient_id = '*'
                   and visit_id = 0
                   and charge_type = :t1
                   and dept_code = '*' ";
            if (ls_charge_type.Equals("平台医保"))
            {
                sql += " and psn_type = :t2";
            }
            ArrayList list5 = new ArrayList();
            List<string> paras5 = new List<string>();
            paras5.Add("t1");
            list5.Add(ls_charge_type);
            if (ls_charge_type.Equals("平台医保"))
            {
                paras5.Add("t2");
                list5.Add(psn_type);
            }
            dt = spc.GetDataTable_Para(sql, paras5, list5).Tables[0];
            if (dt.Rows.Count > 0)
            {
                ll_fee_ratio = 1;
                ldc_ratio = decimal.Parse(dt.Rows[0][0].ToString());
                feeFd = decimal.Parse(dt.Rows[0][1].ToString());
                ld_fee_ratio = Math.Round(prepayment * ldc_ratio, 0, MidpointRounding.AwayFromZero);

                //大于封顶取封顶
                if (feeFd > 0 && ld_fee_ratio > feeFd)
                {
                    ld_fee_ratio = feeFd;
                }
            }

            //取特定科室透支额
            int ll_count_dept = 0;
            decimal ld_dept = 0;
            sql = @"
                select nvl(count(*), 0), nvl(sum(TOTAL_SANCTIFIED_AMOUNT), 0)
                  from INP_OVERDRAFT_REG_MASTER
                 where patient_id = '*'
                   and visit_id = 0
                   and charge_type = '*'
                   and dept_code = :t1 ";
            ArrayList list4 = new ArrayList();
            List<string> paras4 = new List<string>();
            paras4.Add("t1");
            list4.Add(ls_dept_code);
            dt = spc.GetDataTable_Para(sql, paras4, list4).Tables[0];
            ll_count_dept = int.Parse(dt.Rows[0][0].ToString());
            ld_dept = decimal.Parse(dt.Rows[0][1].ToString());

            //取全院透支额
            int ll_count_all = 0;
            decimal ld_all = 0;
            sql = @"
                select nvl(count(*), 0), nvl(sum(TOTAL_SANCTIFIED_AMOUNT), 0)
                  from INP_OVERDRAFT_REG_MASTER
                 where patient_id = '*'
                   and charge_type = '*'
                   and dept_code = '*' ";
            dt = spc.GetList(sql).Tables[0];
            ll_count_all = int.Parse(dt.Rows[0][0].ToString());
            ld_all = decimal.Parse(dt.Rows[0][1].ToString());

            //取各级设置的计算关系
            string way = "";
            way = PlatCommon.SysBase.SystemParm.GetParameterValue("OVERDRAFT_WAY", appname, "*", "*", SystemParm.HisUnitCode);
            if (way.Equals("1"))
            {
                // 修改原因: 当有记录行, 而相应值被修改为0时, 导致优先序列无法传递下去
                //           因此, 要加上金额不等于0的判断
                if (ll_count_person > 0 && ld_person != 0)
                    draftMoney = ld_person;
                else if (ll_fee_ratio > 0 && ld_fee_ratio > 0)
                    draftMoney = ld_fee_ratio;
                else if (ll_count_chargetype > 0 && ld_chargetype != 0)
                    draftMoney = ld_chargetype;
                else if (ll_count_dept > 0 && ld_dept != 0)
                    draftMoney = ld_dept;
                else if (ll_count_all > 0)
                    draftMoney = ld_all;
            }
            if (way.Equals("2"))
                draftMoney = ld_person + ld_chargetype + ld_dept + ld_all;

            return 1;
        }

        //截取double类型的小数位数，不舍入
        public static double Truncate(double d, int c)
        {
            string str = d.ToString();
            double d1;
            int count = str.LastIndexOf(".");
            if (count > 0)
            {
                if (str.Substring(count + 1, str.Length - count - 1).Length > c)
                {
                    d1 = Double.Parse(str.Substring(0, count + c));
                }
                else
                    d1 = d;
            }
            else
                d1 = d;

            return d1;
        }
        /// <summary>
        /// 不判断预交金的人
        /// </summary>
        /// <returns></returns>
        public static int UnCheckPrepayment(string patientId, string visitId)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("select count(*) nums from inpadm.v_support_the_poor where patient_id='" + patientId + "' and visit_id=to_number('" + visitId + "')");
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sb.ToString()).Tables[0];
            if (dt == null || dt.Rows.Count < 1)
            {
                return -1;
            }
            return Convert.ToInt32(dt.Rows[0]["NUMS"].ToString());

        }
        #region 特殊项目定义
        // 	计算一个收费项目对特定病人的应收费。调用时指定病人的费别，项目以及默认系数（一般由费别确定），该函数检查指定项目是否为特殊项目。如果是，则按
        // 特殊项目定义的系数或免费限额计算该项目的应收费价格；否则，按默认的收费系数计算该项目的应收费价格。在访问数据库时，本函数使用调用者指定的事务对象。如果访问数据库出错，本函数显示错误信息
        public static int GetCalcChargePrice(string is_charge_type, string fitem_class, string fitem_code, string fitem_spec, decimal fprice, ref decimal default_factor, ref decimal charge_price)
        {
            //					default_factor，decimal，输入 输出，默认收费系数，在非特殊项目情况下，
            //								按此系数收费
            //					charge_price，decimal，输出，考虑特殊项目因素后计算得到的应收价格（单价）
            //
            // 返回值：
            //					0-非特殊项目，按默认系数计算
            //					1-特殊项目，按特殊项目计算
            //					2-特殊排斥项目，按特殊排斥项目计算
            //					<0，计算出错，返回数据库错误码
            int v_proportion_numerator, v_proportion_denominator;
            int ind_proportion_numerator, ind_proportion_denominator, ind_free_limit;
            int return_val;
            decimal calc_limit, v_free_limit;
            //收费系数表标识不为1时，取收费系数表的系数
            //特殊项目排斥字典 20220526调整为预编译
            string texceptdict = "SELECT nvl(PROPORTION_NUMERATOR,1) PROPORTION_NUMERATOR,nvl(PROPORTION_DENOMINATOR,1) PROPORTION_DENOMINATOR,nvl(FREE_LIMIT,0) FREE_LIMIT";
            texceptdict += " FROM CHARGE_SPECIAL_EXCEPT_DICT ";
            texceptdict += " WHERE CHARGE_TYPE = :is_charge_type  " +
                " AND ITEM_CLASS = :fitem_class   " +
                " AND ( ITEM_CODE = :fitem_code  or :fitem_code = '*') " +
                " AND (ITEM_SPEC = :fitem_spec  or :fitem_spec = '*' )";
             
            List<string> paras = new List<string>();
            ArrayList values = new ArrayList();
            paras.Clear();
            values.Clear();
            paras.Add("is_charge_type");
            paras.Add("fitem_class");
            paras.Add("fitem_code");
            paras.Add("fitem_spec");
            values.Add(is_charge_type);
            values.Add(fitem_class);
            values.Add(fitem_code);
            values.Add(fitem_spec); 

            using (ServerPublicClient client = new ServerPublicClient())
            {
                //DataSet ds = client.GetDataBySql(texceptdict); 
                DataSet ds = client.GetDataTable_Para(texceptdict, paras, values);
                if (ds != null && ds.Tables[0].Rows.Count > 0) //存在排斥项目
                {
                    return_val = 2;
                    v_proportion_numerator = Convert.ToInt16(ds.Tables[0].Rows[0]["PROPORTION_NUMERATOR"].ToString());
                    ind_proportion_numerator = Convert.ToInt16(ds.Tables[0].Rows[0]["PROPORTION_NUMERATOR"].ToString());
                    v_proportion_denominator = Convert.ToInt16(ds.Tables[0].Rows[0]["PROPORTION_DENOMINATOR"].ToString());
                    ind_proportion_denominator = Convert.ToInt16(ds.Tables[0].Rows[0]["PROPORTION_DENOMINATOR"].ToString());
                    v_free_limit = Convert.ToInt16(ds.Tables[0].Rows[0]["FREE_LIMIT"].ToString());
                    ind_free_limit = Convert.ToInt16(ds.Tables[0].Rows[0]["FREE_LIMIT"].ToString());
                    if (ind_free_limit == -1 || v_free_limit == 0) //按比例
                    {
                        if (ind_proportion_numerator == 0 && ind_proportion_denominator == 0)
                        {
                            charge_price = (fprice * v_proportion_numerator) / v_proportion_denominator;
                            default_factor = v_proportion_numerator / v_proportion_denominator;    // 使用系数 lh
                        }
                        else
                        {
                            charge_price = fprice;
                        }
                    }
                    else //否则//按限额
                    {
                        charge_price = (fprice * v_proportion_numerator) / v_proportion_denominator;
                        calc_limit = fprice - charge_price;  //优惠的金额
                        if (calc_limit - v_free_limit > 0) //优惠的金额 > 最高的优惠金额 
                        {
                            charge_price = fprice - v_free_limit;
                        }
                        if (charge_price < 0) //价格低于限额
                        {
                            charge_price = 0;
                        }
                        default_factor = charge_price / fprice;
                    }

                }
                else //不存在排斥项目 
                {
                    ////20220526调整为预编译
                    string tspecial_item = "SELECT nvl(PROPORTION_NUMERATOR,1) PROPORTION_NUMERATOR,nvl(PROPORTION_DENOMINATOR,1) PROPORTION_DENOMINATOR,nvl(FREE_LIMIT,0) FREE_LIMIT";
                    tspecial_item += " FROM CHARGE_SPECIAL_ITEM_DICT";
                    tspecial_item += " WHERE CHARGE_TYPE = :is_charge_type  " +
                        "AND ITEM_CLASS = :fitem_class  " +
                        "AND ( ITEM_CODE =:fitem_code   or :fitem_code = '*') " +
                        "AND (ITEM_SPEC =:fitem_spec   or :fitem_spec = '*') "; 
                    List<string> paras1 = new List<string>();
                    ArrayList values1 = new ArrayList();
                    paras1.Clear();
                    values1.Clear();
                    paras1.Add("is_charge_type");
                    paras1.Add("fitem_class");
                    paras1.Add("fitem_code");
                    paras1.Add("fitem_spec");
                    values1.Add(is_charge_type);
                    values1.Add(fitem_class);
                    values1.Add(fitem_code);
                    values1.Add(fitem_spec);

                    using (ServerPublicClient client1 = new ServerPublicClient())
                    {
                        //DataSet dsitem = client1.GetDataBySql(texceptdict);
                        DataSet dsitem = client.GetDataTable_Para(tspecial_item, paras1, values1);
                        if (dsitem != null && dsitem.Tables[0].Rows.Count > 0) //存在特殊项目
                        {
                            return_val = 1;
                            v_proportion_numerator = Convert.ToInt16(dsitem.Tables[0].Rows[0]["PROPORTION_NUMERATOR"].ToString());
                            ind_proportion_numerator = Convert.ToInt16(dsitem.Tables[0].Rows[0]["PROPORTION_NUMERATOR"].ToString());
                            v_proportion_denominator = Convert.ToInt16(dsitem.Tables[0].Rows[0]["PROPORTION_DENOMINATOR"].ToString());
                            ind_proportion_denominator = Convert.ToInt16(dsitem.Tables[0].Rows[0]["PROPORTION_DENOMINATOR"].ToString());
                            v_free_limit = Convert.ToInt16(dsitem.Tables[0].Rows[0]["FREE_LIMIT"].ToString());
                            ind_free_limit = Convert.ToInt16(dsitem.Tables[0].Rows[0]["FREE_LIMIT"].ToString());
                            if (ind_free_limit == -1 || v_free_limit == 0) //按比例
                            {
                                if (ind_proportion_numerator == 0 && ind_proportion_denominator == 0)
                                {
                                    charge_price = (fprice * v_proportion_numerator) / v_proportion_denominator;
                                    default_factor = v_proportion_numerator / v_proportion_denominator;    // 使用系数 lh
                                }
                                else
                                {
                                    charge_price = fprice;
                                }
                            }
                            else //否则//按限额
                            {
                                charge_price = (fprice * v_proportion_numerator) / v_proportion_denominator;
                                calc_limit = fprice - charge_price;  //优惠的金额
                                if (calc_limit - v_free_limit > 0) //优惠的金额 > 最高的优惠金额 
                                {
                                    charge_price = fprice - v_free_limit;
                                }
                                if (charge_price < 0) //价格低于限额
                                {
                                    charge_price = 0;
                                }
                                default_factor = charge_price / fprice;
                            }

                        }
                        else
                        {
                            return_val = 0;
                            charge_price = fprice * default_factor;
                        }
                        //dsitem.Dispose();
                    }

                }
                //ds.Dispose();
                return return_val;
            }
        }

        public static int Get_Treat_Charge(string v, string decoction_code, string text, ref object ls_costs_temp, ref object ls_charge_temp, ref object asmessage)
        {
            throw new NotImplementedException();
        }
        #endregion
        #region 消耗品减库存
        public static int gf_ex_patient_export_inp(Dictionary<string, string> idc, string as_storage_code, string as_patient_id, Int32 ai_visit_id, Int32 al_item_no, string as_item_code, string as_item_spec, decimal ad_amount, string as_operator, ref string as_message)
        {
            // 描述: 该函数用于护士站计价单、手术计价、检查预约、检查报告、lis接口中往inp_bill_detail中插入数据且数量大于零后，根据插入的数据减执行科室消耗
            // 品库房的库存量，所操作的表有：ex_export_master、ex_export_detail,ex_stock,另外操作这些表后同时做出库记帐
            // 返回值: INTEGER 0 成功 -1 失败
            decimal ld_quantity = 0;
            string ls_documentno;
            //inp_bill_detail中取出的值存放的变量
            Int32 ll_itemno = 0;
            //EX_EXPORT_DETAIL中所需的变量
            string ls_batchno, ls_firmid;
            string ls_expcode, ls_expspec, ls_packagespec, ls_packageunits, ls_expname, ls_units;
            string ls_barcodeno;
            string ls_supplierid, ls_locationcode, ls_batchcode;
            decimal ld_purchaseprice = 0, ld_tradeprice = 0, ld_retailprice = 0;
            decimal ld_exportquantity = 0;
            decimal ld_inventory;
            DateTime ldt_expiredate;
            int li_inp_or_outp;
            Int32 ll_genserial;
            //EX_EXPORT_MASTER中所需的变量
            decimal ld_accountreceivable, ld_accountpayed;
            DateTime ldt_exportdate;
            int ll_loop1;
            decimal ld_sumquantity, ld_amount;
            int li_calu_return;
            string ls_name;//患者姓名
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            ldt_exportdate = spc.GetSysDate();
            //是否有此库房
            string sql = "select nvl(count(*),0) TCOUNT from ex_storage_dept where storage_code='" + as_storage_code + "'";
            DataTable dt = spc.GetList(sql).Tables[0];
            if (dt.Rows.Count > 0)
            {
                string tcount = dt.Rows[0][0].ToString();
                if (tcount.Equals("0"))
                {
                    as_message = as_storage_code + "此库房不存在!";
                    return -1;
                }
            }
            dt.Dispose();
            //是否有对照
            sql = "select t.ex_code,t.ex_spec from ex_vs_price t where t.item_code='" + as_item_code + "' and and t.item_spec='" + as_item_spec + "'";
            sql += " and rownum = 1";
            dt = spc.GetList(sql).Tables[0];
            if (dt.Rows.Count > 0) //
            {
                ls_expcode = dt.Rows[0]["EX_CODE"].ToString();
                ls_packagespec = dt.Rows[0]["EX_SPEC"].ToString();
                if (string.IsNullOrEmpty(ls_expcode))
                {
                    as_message = as_item_code + "此耗材没有对照!";
                    return -1;
                }
            }
            else
            {
                as_message = as_item_code + "此耗材没有对照!";
                return -1;
            }
            dt.Dispose();
            //库存是否充足
            sql = "select nvl(sum(a.quantity),0) from ex_stock a, ex_price_list b, EX_PACKAGE_STOCK_DICT c where a.ex_code = b.ex_code";
            sql += " and a.ex_spec = b.ex_spec and a.package_spec = b.package_spec  and a.firm_id = b.firm_id and a.storage_code = c.storage_code";
            sql += " and a.ex_code = c.ex_code and a.ex_spec = c.ex_spec and a.package_spec = c.package_spec  and c.Manager_indicator = '1'";
            sql += " and b.start_date <= sysdate AND (stop_date IS NULL OR stop_date >= sysdate) and a.storage_code = '" + as_storage_code + "'";
            sql += "  and a.ex_code ='" + ls_expcode + "' and a.package_spec ='" + ls_packagespec + "'  and a.quantity > 0 ";
            dt = spc.GetList(sql).Tables[0];
            ld_sumquantity = 0;
            if (dt.Rows.Count > 0) //
            {
                ld_sumquantity = Convert.ToDecimal(dt.Rows[0][0].ToString());
            }
            dt.Dispose();
            //没有库存或库存不足是否允许计费(数据库里加个参数)
            int ls_ex_billing = int.Parse(PlatCommon.SysBase.SystemParm.GetParameterValue("EX_BILLING", "EXSTOCK", "*", "*", SystemParm.HisUnitCode));//耗材库存不足是否允许计费,0,允许,1,不允许
            if ((ld_sumquantity == 0 && ad_amount > 0) || (ld_sumquantity < ad_amount))
            {
                if (ls_ex_billing == 1)
                {
                    if (ld_sumquantity == 0)
                    {
                        as_message = ls_expcode + "耗材库存为零!";
                        return -1;
                    }
                    if (ld_sumquantity < ad_amount)
                    {
                        as_message = ls_expcode + "耗材库存不足!库存为:" + ld_sumquantity.ToString();
                        return -1;
                    }
                }
            }
            /*============================================================*/
            // 得到出库单号。
            /*============================================================*/
            string ls_prefixno, ls_mid = "", ls_ava;
            Int32 ll_ava, ll_length, ll_loop;
            string ls_sql;
            string ls_temp = "";
            //取得组成单据的数据
            sql = "SELECT 	EXPORT_NO_PREFIX, nvl(EXPORT_NO_AVA,1),nvl(EXPORT_TYPE_LEN,8),EXPORT_IS_SQL FROM EXSTOCK.EX_STORAGE_DEPT	WHERE storage_code = '" + as_storage_code + "'";
            dt = spc.GetList(sql).Tables[0];
            if (dt.Rows.Count > 0) //
            {
                ls_prefixno = dt.Rows[0][0].ToString(); //前缀
                ll_ava = Convert.ToInt32(dt.Rows[0][1].ToString()); //当前出库单号
                ll_length = Convert.ToInt32(dt.Rows[0][2].ToString()); //除前缀和SQL语句后，剩下的长度
                ls_sql = dt.Rows[0][3].ToString(); //中间部分的SQL语句
            }
            else
            {
                as_message = ls_expcode + "取组成出库单的数据时发生错误!" + ld_sumquantity.ToString();
                return -1;
            }
            dt.Dispose();
            //运行sql，产生单号的中间部分
            if (!string.IsNullOrEmpty(ls_sql))
            {
                dt = spc.GetList(ls_sql).Tables[0];
                if (dt.Rows.Count > 0) //
                {
                    ls_mid = dt.Rows[0][0].ToString(); //产生单号的中间部分
                }
                dt.Dispose();
            }

            //取得后面部分
            ls_ava = ll_ava.ToString();
            if (ls_ava.Length <= ll_length)
            {
                for (int rowIndex = 0; rowIndex < ll_length - ls_ava.Length; rowIndex++)
                {
                    ls_temp = ls_temp + "0";
                }
            }
            else
            {
                ls_ava = "1";
                for (ll_loop = 0; ll_loop < ll_length - 1; ll_loop++)
                {
                    ls_temp = ls_temp + "0";
                }
            }
            ls_ava = ls_temp + ls_ava;
            //生成出库单据号
            if ((!string.IsNullOrEmpty(ls_prefixno)) && (!string.IsNullOrEmpty(ls_mid)))
            { ls_documentno = ls_prefixno + ls_mid + ls_ava; }
            else
            {
                if ((string.IsNullOrEmpty(ls_prefixno) || ls_prefixno.Length == 0) && (!string.IsNullOrEmpty(ls_mid)))
                { ls_documentno = ls_mid + ls_ava; }
                else//只有前缀，没有SQL
                { ls_documentno = ls_prefixno + ls_ava; }
            }
            int ll_ava1 = ll_ava + 1;
            string sqla = "update 	EXSTOCK.EX_STORAGE_DEPT  set    EXPORT_NO_AVA = '" + ll_ava1 + "' where  storage_code ='" + as_storage_code + "' and    nvl(EXPORT_NO_AVA,1) ='" + ll_ava + "'";
            idc.Add(sqla, "更新EX_STORAGE_DEPT中当前出库单号发生错误!");
            li_inp_or_outp = 2;
            ld_amount = ad_amount;
            ll_loop1 = -1;
            string sqleie = "select a.ex_code, a.ex_name, a.ex_spec,a.units,a.batch_no,a.firm_id, a.batch_code, a.package_spec,a.PACKAGE_UNITS,a.EXPIRE_DATE, a.PURCHASE_PRICE, b.TRADE_PRICE, b.RETAIL_PRICE,";
            sqleie += "a.quantity,a.SUPPLIER_ID,a.LOCATION_CODE,a.BARCODE_NO from ex_stock a, ex_price_list b, EX_PACKAGE_STOCK_DICT c  where a.ex_code = b.ex_code and a.ex_spec = b.ex_spec and a.package_spec = b.package_spec";
            sqleie += " and a.firm_id = b.firm_id and a.storage_code = c.storage_code  and a.ex_code = c.ex_code and a.ex_spec = c.ex_spec and a.package_spec = c.package_spec";
            sqleie += " and c.Manager_indicator = '1' and b.start_date <= sysdate  AND (stop_date IS NULL OR stop_date >= sysdate) and a.storage_code = '" + as_storage_code + "'";
            sqleie += "  and a.ex_code ='" + ls_expcode + "' and a.package_spec ='" + ls_packagespec + "' and a.quantity > 0    order by a.batch_no";
            dt = spc.GetList(sqleie).Tables[0];
            string texpiredate;
            string purchaseprice, tradeprice, retailprice, quantity;
            if (dt.Rows.Count > 0) //aaaa
            {
                while (ld_amount != 0)
                {
                    ll_loop1 = ll_loop1 + 1;
                    ls_expcode = dt.Rows[ll_loop1]["EX_CODE"].ToString();
                    ls_expname = dt.Rows[ll_loop1]["EX_NAME"].ToString();
                    ls_expspec = dt.Rows[ll_loop1]["EX_SPEC"].ToString();
                    ls_units = dt.Rows[ll_loop1]["UNITS"].ToString();
                    ls_batchno = dt.Rows[ll_loop1]["BATCH_NO"].ToString();
                    ls_firmid = dt.Rows[ll_loop1]["FIRM_ID"].ToString();
                    ls_batchcode = dt.Rows[ll_loop1]["BATCH_CODE"].ToString();
                    ls_packagespec = dt.Rows[ll_loop1]["PACKAGE_SPEC"].ToString();
                    ls_packageunits = dt.Rows[ll_loop1]["PACKAGE_UNITS"].ToString();
                    texpiredate = dt.Rows[ll_loop1]["EXPIRE_DATE"].ToString();
                    if (!string.IsNullOrEmpty(texpiredate))
                    {
                        ldt_expiredate = Convert.ToDateTime(texpiredate);
                    }
                    purchaseprice = dt.Rows[ll_loop1]["PURCHASE_PRICE"].ToString();
                    tradeprice = dt.Rows[ll_loop1]["TRADE_PRICE"].ToString();
                    retailprice = dt.Rows[ll_loop1]["RETAIL_PRICE"].ToString();
                    quantity = dt.Rows[ll_loop1]["QUANTITY"].ToString();
                    if (!string.IsNullOrEmpty(purchaseprice)) ld_purchaseprice = Math.Round(Convert.ToDecimal(purchaseprice), 4);
                    if (!string.IsNullOrEmpty(tradeprice)) ld_tradeprice = Math.Round(Convert.ToDecimal(tradeprice), 4);
                    if (!string.IsNullOrEmpty(retailprice)) ld_retailprice = Math.Round(Convert.ToDecimal(retailprice), 4);
                    if (!string.IsNullOrEmpty(quantity)) ld_quantity = Math.Round(Convert.ToDecimal(quantity), 2);
                    ls_supplierid = dt.Rows[ll_loop1]["SUPPLIER_ID"].ToString();
                    ls_locationcode = dt.Rows[ll_loop1]["LOCATION_CODE"].ToString();
                    ls_barcodeno = dt.Rows[ll_loop1]["BARCODE_NO"].ToString(); //条码号
                    if (ld_quantity >= ad_amount)
                    {
                        ld_exportquantity = ld_amount;
                        ld_amount = 0;
                    }
                    else
                    {
                        ld_exportquantity = ld_quantity;
                        ld_amount = ld_amount - ld_quantity;
                    }
                    ld_inventory = 0;
                    ll_genserial = 0;
                    ll_itemno = ll_itemno + 1;
                    if (ld_exportquantity != 0)//出库零库存不执行(多批次有库存为零的)
                    {
                        ld_inventory = ld_quantity - ld_exportquantity;//结存
                        //string sqlb = "SELECT EXSTOCK.GEN_SERIAL_EX.NEXTVAL  FROM DUAL";
                        //DataTable ds = spc.GetList(sqlb).Tables[0];
                        //string genserial;
                        //if (ds.Rows.Count > 0) //
                        //{
                        //    genserial = ds.Rows[0][0].ToString();
                        //    if (!string.IsNullOrEmpty(genserial))
                        //    {
                        //        ll_genserial = Convert.ToInt32(genserial); //生产序号
                        //    }
                        //    else
                        //    {
                        //        as_message = "取GEN_SERIAL_SEQ序列失败!";
                        //        return -1;
                        //    }
                        //}
                        //else
                        //{
                        //    as_message = "取GEN_SERIAL_SEQ序列失败!";
                        //    return -1;
                        //}
                        #region
                        DateTime sysdate = spc.GetSysDate();
                        string ls_gen_serail = "";
                        if (!PlatCommon.Common.PublicFunction.GetSequeceFromAuto("消耗品出入库单号序列", PlatCommon.SysBase.SystemParm.HisUnitCode, ref ls_gen_serail) || string.IsNullOrEmpty(ls_gen_serail))
                        {
                            as_message = "取GEN_SERIAL_EX序列失败!";
                            return -1;//获取序列失败
                        }
                        ls_gen_serail = sysdate.ToString("yyMMdd") + ls_gen_serail;

                        #endregion
                        string sqlc = "INSERT INTO EX_EXPORT_DETAIL(STORAGE_CODE,DOCUMENT_NO,ITEM_NO,EX_CODE,EX_NAME,EX_SPEC,UNITS,PACKAGE_SPEC,";
                        sqlc += "PACKAGE_UNITS,FIRM_ID,BATCH_NO,GEN_SERIAL,QUANTITY,INVENTORY,BATCH_CODE,EXPIRE_DATE,";
                        sqlc += "PURCHASE_PRICE,TRADE_PRICE,RETAIL_PRICE,SUPPLIER_ID,LOCATION_CODE,BARCODE_NO, PATIENT_ID,";
                        sqlc += "VISIT_ID,INP_OR_OUTP,EXPORT_DETAIL_MEMOS,bill_item_no) ";
                        sqlc += " values( '" + as_storage_code + "','" + ls_documentno + "','" + ll_itemno + "','" + ls_expcode + "','" + ls_expname;
                        sqlc += "','" + ls_expspec + "','" + ls_units + "','" + ls_packagespec + "','" + ls_packageunits + "','" + ls_firmid + "','" + ls_batchno;
                        sqlc += "','" + ls_gen_serail + "','" + ld_exportquantity + "','" + ld_inventory + "','" + ls_batchcode + "',to_date('" + texpiredate + "','yyyy-mm-dd hh24:mi:ss')";
                        sqlc += ",'" + ld_purchaseprice + "','" + ld_tradeprice + "','" + ld_retailprice + "','" + ls_supplierid + "','" + ls_locationcode + "','" + as_patient_id;
                        sqlc += "','" + ai_visit_id + "','" + li_inp_or_outp + "','" + "" + "','" + al_item_no + "')";
                        idc.Add(sqlc, "往ex_EXPORT_DETAIL插入数据时出错!");
                        //减库存
                        string sqld = "update ex_stock t set t.quantity = nvl(t.quantity,0) - " + ld_exportquantity + ",t.last_updatetime = sysdate";
                        sqld += " where t.storage_code ='" + as_storage_code + "' and t.ex_code = '" + ls_expcode + "' and t.ex_spec = '" + ls_expspec + "' and ";
                        sqld += " t.firm_id ='" + ls_firmid + "' and t.package_spec ='" + ls_packagespec + "' and t.batch_no ='" + ls_batchno + "'";
                        idc.Add(sqld, "减执行科室的库存失败出错!");
                        //调价盈亏
                        li_calu_return = f_epe_cale_profit(idc, as_storage_code, ls_expcode, ls_expspec, ls_units, ls_firmid, ls_packagespec, ls_packageunits, ls_batchno, ld_exportquantity, "3", ldt_exportdate, ref as_message);
                        if (li_calu_return == -1)
                        {
                            as_message = "计算调价盈亏出错";
                            return -1;
                        }


                    } //(ld_exportquantity != 0)//出库零库存不执行(多批次有库存为零的) 结束

                }//while循环结束
                /*============================================================*/
                // 往ex_export_master中插入数据
                /*============================================================*/
                string sqle = "select sum(RETAIL_PRICE * quantity)  from ex_export_detail where document_no = '" + ls_documentno + "'and storage_code ='" + as_storage_code + "'";
                ld_accountreceivable = 0;
                dt = spc.GetList(sqleie).Tables[0];
                if (dt.Rows.Count > 0)
                {
                    ld_accountreceivable = Convert.ToDecimal(dt.Rows[0][0].ToString());
                }
                ld_accountpayed = ld_accountreceivable;
                dt.Dispose();
                sql = "select name  from pat_master_index where patient_id='" + as_patient_id + "'";
                dt = spc.GetList(sql).Tables[0];
                ls_name = "";
                if (dt.Rows.Count > 0)
                {
                    ls_name = dt.Rows[0][0].ToString();
                }
                dt.Dispose();
                string sqlf = "INSERT INTO EX_EXPORT_master(STORAGE_CODE,			DOCUMENT_NO,			export_class,				export_date,";
                sqlf += "receiver_id,			receiver_name,			account_receivable,		account_payed,";
                sqlf += "additional_fee,		operator_usercode,	account_indicator,		acct_date,";
                sqlf += "acct_usercode ) ";
                sqlf += " values( '" + as_storage_code + "','" + ls_documentno + "','" + "患者使用" + "',to_date('" + ldt_exportdate.ToString() + "','yyyy-mm-dd hh24:mi:ss')";
                sqlf += ",'" + as_patient_id + "','" + ls_name + "','" + ls_packagespec + "','" + ld_accountreceivable + "','" + ld_accountpayed;
                sqlf += "','" + "" + "','" + as_operator + "','" + 1 + "',to_date('" + ldt_exportdate.ToString() + "','yyyy-mm-dd hh24:mi:ss')";
                sqlf += ",'" + as_operator + "')";
                idc.Add(sqlf, "往ex_EXPORT_master插入数据时出错!");

            }// aaaa if (dt.Rows.Count > 0)结束

            return 0;
        }
        #endregion
        #region 消耗品调价盈亏
        /*------------------------------------------------------------*/
        // 描述: 调价盈亏确认时生成的盈亏数据是当时那个时间点上的数据，
        //       但是新的批发价和零售价当时并没有执行，即从盈亏确认时间
        //       到新价格真正执行时间这一段时间上的盈亏在当时并没有计算
        //       上。该函数就是解决这个问题，将盈亏确认时间到新价格真正
        //       执行时间这一段时间出入库盈亏额补算上。
        /*------------------------------------------------------------*/
        public static int f_epe_cale_profit(Dictionary<string, string> idc, string as_storage_code, string as_expcode, string as_expspec, string as_units, string as_firmid, string as_package_spec, string as_package_units, string as_batchno, decimal ad_quantity, string as_typex, DateTime as_date, ref string as_message)
        {
            string ls_ex_name;
            int li_typex;
            decimal ld_original_trade_price = 0, ld_current_trade_price = 0;
            decimal ld_original_retail_price = 0, ld_current_retail_price = 0;
            string original_trade_price, current_trade_price;
            string original_retail_price, current_retail_price;
            string actual_efficient_date;
            decimal ld_trade_price_profit, ld_retail_price_profit;
            Int32 ll_ITEM_NO, ll_gen_serial;
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            //是否有此库房
            string sql = "select ex_name,ACTUAL_EFFICIENT_DATE, ORIGINAL_TRADE_PRICE,CURRENT_TRADE_PRICE,ORIGINAL_RETAIL_PRICE,CURRENT_RETAIL_PRICE	 from EX_PRICE_MODIFY";
            sql += " where to_date('" + as_date + "','yyyy-mm-dd hh24:mi:ss') between NOTICE_EFFICIENT_DATE and ACTUAL_EFFICIENT_DATE and firm_id ='" + as_firmid + "' ";
            sql += " and    package_spec ='" + as_package_spec + "' and    ex_spec ='" + as_expspec + "' and    ex_code =" + as_expcode + "'";

            DataTable dt = spc.GetList(sql).Tables[0];
            if (dt.Rows.Count > 0)
            {
                ls_ex_name = dt.Rows[0]["EX_NAME"].ToString();
                actual_efficient_date = dt.Rows[0]["ACTUAL_EFFICIENT_DATE"].ToString();
                original_trade_price = dt.Rows[0]["ORIGINAL_TRADE_PRICE"].ToString();
                current_trade_price = dt.Rows[0]["CURRENT_TRADE_PRICE"].ToString();
                original_retail_price = dt.Rows[0]["ORIGINAL_RETAIL_PRICE"].ToString();
                current_retail_price = dt.Rows[0]["CURRENT_RETAIL_PRICE"].ToString();
                if (!string.IsNullOrEmpty(original_trade_price)) ld_original_trade_price = Math.Round(Convert.ToDecimal(original_trade_price), 4);
                if (!string.IsNullOrEmpty(current_trade_price)) ld_current_trade_price = Math.Round(Convert.ToDecimal(current_trade_price), 4);
                if (!string.IsNullOrEmpty(original_retail_price)) ld_original_retail_price = Math.Round(Convert.ToDecimal(original_retail_price), 4);
                if (!string.IsNullOrEmpty(current_retail_price)) ld_current_retail_price = Math.Round(Convert.ToDecimal(current_retail_price), 4);
            }
            else
            {
                return 0;
            }
            dt.Dispose();
            //盈亏项目序号
            sql = "select nvl(max(PROFIT_ITEM_NO),0) from 	EX_PRICE_MODIFY_PROFIT where STORAGE_code ='" + as_storage_code + "' and  EX_CODE ='" + as_expcode + "'";
            sql += " and ex_spec ='" + as_expspec + "' and  package_spec ='" + as_package_spec + "'and  firm_id ='" + as_firmid + "' and  BATCH_NO = '" + as_batchno + "'";
            sql += " and ACTUAL_EFFICIENT_DATE =to_date('" + actual_efficient_date + "','yyyy-mm-dd hh24:mi:ss')";
            dt = spc.GetList(sql).Tables[0];
            if (dt.Rows.Count > 0)
            {
                ll_ITEM_NO = Convert.ToInt32(dt.Rows[0][0].ToString());
            }
            else
            {
                return 0;
            }
            ll_ITEM_NO = ll_ITEM_NO + 1;
            if (as_typex == "IMPORT")
            {
                li_typex = 2;  //代表入库
                //批发价盈亏、零售价盈亏金额
                ld_trade_price_profit = (ld_current_trade_price - ld_original_trade_price) * ad_quantity;
                ld_retail_price_profit = (ld_current_retail_price - ld_original_retail_price) * ad_quantity;
            }
            else
            {
                li_typex = 3; //代表出库
                              //批发价盈亏、零售价盈亏金额
                ld_trade_price_profit = 0 - (ld_current_trade_price - ld_original_trade_price) * ad_quantity;
                ld_retail_price_profit = 0 - (ld_current_retail_price - ld_original_retail_price) * ad_quantity;
            }
            //string sqlb = "SELECT EXSTOCK.GEN_SERIAL_EX.NEXTVAL  FROM DUAL";
            //DataTable ds = spc.GetList(sqlb).Tables[0];
            //string genserial;
            //if (ds.Rows.Count > 0) //
            //{
            //    genserial = ds.Rows[0][0].ToString();
            //    if (!string.IsNullOrEmpty(genserial))
            //    {
            //        ll_gen_serial = Convert.ToInt32(genserial); //生产序号
            //    }
            //    else
            //    {
            //        as_message = "取GEN_SERIAL_EX序列失败";
            //        return -1;
            //    }
            //}
            //else
            //{
            //    as_message = "取GEN_SERIAL_EX序列失败!";
            //    return -1;
            //}
            //ds.Dispose();
            #region
            DateTime sysdate = spc.GetSysDate();
            string ls_gen_serail = "";
            if (!PlatCommon.Common.PublicFunction.GetSequeceFromAuto("消耗品出入库单号序列", PlatCommon.SysBase.SystemParm.HisUnitCode, ref ls_gen_serail) || string.IsNullOrEmpty(ls_gen_serail))
            {
                as_message = "取GEN_SERIAL_EX序列失败!";
                return -1;//获取序列失败
            }
            ls_gen_serail = sysdate.ToString("yyMMdd") + ls_gen_serail;

            #endregion
            string sqla = "INSERT INTO EX_PRICE_MODIFY_PROFIT(STORAGE_code,			EX_CODE,						ex_name,						EX_SPEC,";
            sqla += "UNITS,						package_spec,					package_units,					FIRM_ID,	";
            sqla += "batch_no,					ACTUAL_EFFICIENT_DATE,	GEN_DATE,						PROFIT_ITEM_NO,";
            sqla += "TYPEX,						QUANTITY,						ORIGINAL_TRADE_PRICE,	CURRENT_TRADE_PRICE,	";
            sqla += "TRADE_PRICE_PROFIT,	ORIGINAL_RETAIL_PRICE,	CURRENT_RETAIL_PRICE,	RETAIL_PRICE_PROFIT,GEN_SERIAL)";
            sqla += " values( '" + as_storage_code + "','" + as_expcode + "','" + ls_ex_name + "','" + as_expspec + "'";
            sqla += ",'" + as_units + "','" + as_package_spec + "','" + as_package_units + "','" + as_firmid;
            sqla += "','" + as_batchno + "',to_date('" + actual_efficient_date + "','yyyy-mm-dd hh24:mi:ss') , sysdate , '" + ll_ITEM_NO + "'";
            sqla += ",'" + li_typex + "','" + ad_quantity + "','" + ld_original_trade_price + "','" + ld_current_trade_price + "'";
            sqla += ",'" + ld_trade_price_profit + "','" + ld_original_retail_price + "','" + ld_current_retail_price + "','" + ld_retail_price_profit + "','" + ls_gen_serail + "')";

            idc.Add(sqla, "插入调价盈亏记录出错!");


            return 0;
        }
        #endregion
        #region F9选择COMM.INPUT_SETTING
        public static DataSet Get_INPUT_SETTING_List(string strWhere)
        {
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select DICT_TYPE,DATA_TABLE,DATA_COL,INPUT_CODE,DATA_TITLE,FLAG_SHOW,SHOW_SORT,FLAG_ISNAME,RESULT_SORT,SHOW_WIDTH ");
            strSql.Append(" FROM COMM.INPUT_SETTING ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            DataSet inputSet = spc.GetDataBySql(strSql.ToString());

            return inputSet;
        }
        #endregion

        #region 处置计价 pb 中叫 f_treat_charge()
        public static int Get_Treat_Charge(string as_clinic_class, string as_clinic_code, string as_chargetype, ref decimal adc_price, ref decimal adc_charges, ref string asmessage)
        {
            // 参数:
            // 	      	string	as_clinic_class 			项目类型
            // 	      	string	as_clinic_code  		项目代码
            // 	      	string	as_chargetype		费别
            // 	ref   	decimal       	adc_price       			应收金额
            // 	ref   	decimal       	adc_charges		实收金额
            decimal default_factor;//4位小数
            decimal price_coeff_numerator, price_coeff_denominator;
            decimal ldc_price = 0, ld_charges = 0, ldc_chg_amount = 0;
            string ls_itemclass, ls_itemcode, ls_itemspec, ls_units;
            string sql = "SELECT charge_item_class, charge_item_code, charge_item_spec, amount, units FROM clinic_vs_charge Where clinic_item_class = '" + as_clinic_class + "'  And clinic_item_code = '" + as_clinic_code + "'";
            DataTable cur_clinic_vs_charge = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            if (cur_clinic_vs_charge.Rows.Count <= 0)
            {
                asmessage = "从临床与物价对照表中查询不到数据。请联系物价部门!";
                return -1;
            }
            // 查找收费系数
            sql = "SELECT price_coeff_numerator,price_coeff_denominator FROM charge_price_schedule	Where charge_type = '" + as_chargetype + "'";
            DataTable charge_price_schedule = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            if (charge_price_schedule.Rows.Count <= 0)
            {
                asmessage = "从charge_price_schedule表中查询收费系数失败!";
                return -1;
            }
            price_coeff_denominator = decimal.Parse(charge_price_schedule.Rows[0]["PRICE_COEFF_DENOMINATOR"].ToString());
            price_coeff_numerator = decimal.Parse(charge_price_schedule.Rows[0]["PRICE_COEFF_NUMERATOR"].ToString());
            if (price_coeff_denominator == 0)
            {
                default_factor = 1;
            }
            else
            {
                default_factor = Math.Round((price_coeff_numerator / price_coeff_denominator), 4);
            }
            for (int i = 0; i < cur_clinic_vs_charge.Rows.Count; i++)
            {
                ls_itemclass = cur_clinic_vs_charge.Rows[i]["CHARGE_ITEM_CLASS"].ToString();
                ls_itemcode = cur_clinic_vs_charge.Rows[i]["CHARGE_ITEM_CODE"].ToString();
                ls_itemspec = cur_clinic_vs_charge.Rows[i]["CHARGE_ITEM_SPEC"].ToString();
                ldc_chg_amount = decimal.Parse(cur_clinic_vs_charge.Rows[i]["AMOUNT"].ToString());
                ls_units = cur_clinic_vs_charge.Rows[i]["UNITS"].ToString();
                //计算应收费用，单价
                sql = "	SELECT price FROM current_price_list	WHERE item_class ='" + ls_itemclass + "' AND item_code ='" + ls_itemcode + "' AND item_spec ='" + ls_itemspec + "' And units ='" + ls_units + "'";
                DataTable current_price_list = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
                if (current_price_list.Rows.Count <= 0)
                {
                    asmessage = "未查询到项目价格信息！";
                    return -1;
                }
                ldc_price = decimal.Parse(current_price_list.Rows[0]["PRICE"].ToString());
                //计算一个收费项目对特定病人的应收费
                if (GetCalcChargePrice(as_chargetype, ls_itemclass, ls_itemcode, ls_itemspec, ldc_price, ref default_factor, ref ld_charges) < 0)
                {
                    asmessage = "计算一个收费项目对特定病人的应收费错误！";
                    return -1;
                }

                adc_price += ldc_price * ldc_chg_amount;
                adc_charges += ld_charges * ldc_chg_amount;

            }

            return 0;
        }
        #endregion

        #region 公用获取年龄根据出生日期
        public static string GetAge(DateTime dtBirthday, DateTime dtNow)
        {
            string strAge = string.Empty;                         // 年龄的字符串表示
            int intYear = 0;                                    // 岁
            int intMonth = 0;                                    // 月
            int intDay = 0;                                    // 天

            // 如果没有设定出生日期, 返回空
            //if (dtBirthday<DateTime.Parse("19000000"))
            //{
            //    return string.Empty;
            //}

            // 计算天数
            intDay = dtNow.Day - dtBirthday.Day;
            if (intDay < 0)
            {
                dtNow = dtNow.AddMonths(-1);
                intDay += DateTime.DaysInMonth(dtNow.Year, dtNow.Month);
            }

            // 计算月数
            intMonth = dtNow.Month - dtBirthday.Month;
            if (intMonth < 0)
            {
                intMonth += 12;
                dtNow = dtNow.AddYears(-1);
            }

            // 计算年数
            intYear = dtNow.Year - dtBirthday.Year;

            // 格式化年龄输出
            if (intYear >= 1)                                            // 年份输出
            {
                strAge = intYear.ToString() + "岁";
            }

            if (intMonth > 0 && intYear <= 5)                           // 五岁以下可以输出月数
            {
                strAge += intMonth.ToString() + "月";
            }

            if (intDay >= 0 && intYear < 1)                              // 一岁以下可以输出天数
            {
                if (strAge.Length == 0 || intDay > 0)
                {
                    strAge += intDay.ToString() + "天";
                }
            }

            return strAge;
        }
        #endregion
        /// <summary>
        /// 高质耗材库存处理
        /// </summary>
        /// <param name="db">处理事务的连接对象</param>
        /// <param name="idc">要执行的sql语句组</param>
        /// <param name="storage_code">三级库房编码</param>
        /// <param name="patient_id">患者id</param>
        /// <param name="visit_id">住院次数</param>
        /// <param name="patient_name">患者名称</param>
        /// <param name="barcode_no">耗材条码</param>
        /// <param name="return_flag">退费标志</para
        /// <returns></returns>
        #region 高质耗材库存处理
        public static string SetHighQualityStock(Utility.OracleODP.OracleBaseClass db, Dictionary<string, string> idc, string storage_code, string patient_id, string visit_id, string patient_name, string barcode_no, string billitemno, bool return_flag)
        {
            string sql = "select t.storage_code,t.storage_name from ex_storage_dept t where t.storage_type='1' and nvl(t.vs_high_value,'0')='1' and rownum =1";
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            if (dt.Rows.Count < 1)
                return "没有找到高值耗材对应的一级库房！";
            string ls_storage_code_one = dt.Rows[0][0].ToString();
            string ls_storage_name_one = dt.Rows[0][1].ToString();
            if (string.IsNullOrEmpty(ls_storage_code_one) || string.IsNullOrEmpty(ls_storage_name_one))
                return "没有找到高值耗材对应的一级库房！";
            ////一级库入库单号
            string ls_documentno_one_imp = "";
            string ls_documentno_one_exp = "";
            string rev = GetImportNo(db, ls_storage_code_one, ref ls_documentno_one_imp);
            if (!string.IsNullOrEmpty(rev))
                return rev;
            ////一级库出库单号
            rev = GetExportNo(db, ls_storage_code_one, ref ls_documentno_one_exp);
            if (!string.IsNullOrEmpty(rev))
                return rev;
            //取数据
            string ls_ex_code, ls_ex_name, ls_ex_spec, ls_units, ls_package_spec, ls_package_units, ls_firm_id, ls_batch_code;
            string ls_supplier_id;
            string ldtm_expire_date;
            decimal ldec_purchase, ldec_trade, ldec_retail;
            string ls_documentno_old = "", ll_bill_item_no = "";
            string ls_supplier_name;
            string ls_operator_usercode;

            if (return_flag)
            {
                //退
                sql = "SELECT   req_storage_code,t.item_no,item_sub_no,ex_code,ex_name,ex_spec,t.units,package_spec,package_units,purchase_price,RETAIL_PRICE,";
                sql += "firm_id,supplier_id,batch_code,expire_date,barcode_no,status,t.patient_id,t.visit_id,t.bill_item_no,t.document_no FROM ex_prepare_supplier t,inp_bill_detail c ";
                sql += "where   t.confirm_storage_code = '" + storage_code + "'  and  t.status = '患者使用' and t.patient_id=c.patient_id and t.visit_id=c.visit_id ";
                sql += " and t.document_no=c.document_no and c.rcpt_no is null and c.return_document_no is null and c.memo = '高值耗材' ";
                sql += " and c.patient_id='" + patient_id + "' and  c.visit_id=" + visit_id + "  and t.barcode_no = '" + barcode_no + "' order by c.item_no";
            }
            else
            {
                //收
                sql = "SELECT 	req_storage_code,item_no,item_sub_no,ex_code,ex_name,ex_spec,units,package_spec,package_units,purchase_price,(select b.price from current_price_list b ";
                sql += " where (b.item_class,b.item_code,b.item_spec,b.units) = (select a.item_class,a.item_code,a.item_spec,a.item_units from ex_vs_price a where a.ex_code = t.ex_code and a.ex_spec = t.package_spec) ";
                sql += ") RETAIL_PRICE,firm_id,supplier_id,batch_code,expire_date,barcode_no,status FROM ex_prepare_supplier t where   t.confirm_storage_code = '" + storage_code + "' ";
                sql += " and t.barcode_no = '" + barcode_no + "'   and t.status = '已备货确认'";

            }
            dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            if (dt.Rows.Count < 1)
                return "取耗材项目失败！";
            DataRow dr = dt.Rows[0];
            ls_ex_code = dr["ex_code"].ToString();
            ls_ex_name = dr["ex_name"].ToString();
            ls_ex_spec = dr["ex_spec"].ToString();
            ls_units = dr["units"].ToString();
            ls_package_spec = dr["package_spec"].ToString();
            ls_package_units = dr["package_units"].ToString();
            ls_firm_id = dr["firm_id"].ToString();
            ls_batch_code = dr["batch_code"].ToString();
            ldtm_expire_date = dr["expire_date"].ToString();
            ldec_purchase = decimal.Parse(dr["purchase_price"].ToString());
            ldec_trade = ldec_purchase;
            ldec_retail = decimal.Parse(dr["retail_price"].ToString());
            ls_supplier_id = dr["supplier_id"].ToString();
            if (return_flag)
            {
                ls_documentno_old = dr["document_no"].ToString();
                ll_bill_item_no = dr["bill_item_no"].ToString();
            }
            sql = "select supplier_name  from  ex_supplier_catalog t where t.supplier_id='" + ls_supplier_id + "'";
            dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            if (dt.Rows.Count > 0)
            {
                ls_supplier_name = dt.Rows[0][0].ToString();
            }
            else
                ls_supplier_name = "";
            ls_operator_usercode = PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;

            //一级库入库
            string import_class = "";
            decimal account_receivable;
            if (return_flag)
            {
                import_class = "患者退费";
                account_receivable = -1 * ldec_purchase;
            }
            else
            {
                import_class = "备货入库";
                account_receivable = ldec_purchase;
            }
            sql = "insert into exstock.ex_import_master(storage_code,document_no,import_class,import_date,supplier_id,supplier_name,account_receivable,account_payed,";
            sql += "additional_fee,operator_usercode,account_indicator,acct_date,acct_usercode,ex_document_no,import_memos)";
            sql += " values('" + ls_storage_code_one + "','" + ls_documentno_one_imp + "','" + import_class + "',sysdate,'" + ls_supplier_id + "','" + ls_supplier_name + "'," + account_receivable.ToString() + ",null,";
            sql += " null,'" + ls_operator_usercode + "',1,sysdate,'" + ls_operator_usercode + "','" + ls_documentno_one_exp + "','高值耗材')";
            idc.Add(sql, "一级库入库失败!");
            string ls_batch_no = GetEpeGenNewBatchNo();
            if (string.IsNullOrEmpty(ls_batch_code))
                return "取批次号失败!";
            //sql = "SELECT EXSTOCK.GEN_SERIAL_EX.NEXTVAL FROM DUAL";
            //string ll_gen_serial;
            //dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            //if (dt.Rows.Count < 1)
            //    return "取生产序列号失败!";
            //else
            //    ll_gen_serial = dt.Rows[0][0].ToString();
            #region
            string sysdate = PlatCommon.Common.PublicFunction.GetSysDate();
            string ll_gen_serial = "";
            if (!PlatCommon.Common.PublicFunction.GetSequeceFromAuto("消耗品出入库单号序列", PlatCommon.SysBase.SystemParm.HisUnitCode, ref ll_gen_serial) || string.IsNullOrEmpty(ll_gen_serial) || string.IsNullOrEmpty(sysdate))
            {
                return "取GEN_SERIAL_EX序列失败!";//获取序列失败
            }
            ll_gen_serial = Convert.ToDateTime(sysdate).ToString("yyMMdd") + ll_gen_serial;

            #endregion
            sql = "select t.price_ratio from ex_price_list t where t.ex_code = '" + ls_ex_code + "' and t.ex_spec = '" + ls_ex_spec + "' and ";
            sql += " t.package_spec = '" + ls_package_spec + "' and t.firm_id = '" + ls_firm_id + "' and (( start_date <= sysdate ) and (stop_date IS NULL OR  stop_date > sysdate))";
            decimal ldec_radio;
            dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            if (dt.Rows.Count < 1)
                ldec_radio = 0;
            else
                ldec_radio = decimal.Parse(dt.Rows[0][0].ToString());
            string ls_location_code;
            sql = "select location_code from ex_location_rec where storage_code='" + ls_storage_code_one + "' and ex_code ='" + ls_ex_code + "'";
            dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            if (dt.Rows.Count < 1)
                ls_location_code = "";
            else
                ls_location_code = dt.Rows[0][0].ToString();
            //写入细表
            decimal quantity;
            if (return_flag)
                quantity = -1;
            else
                quantity = 1;
            sql = "insert into exstock.ex_import_detail(storage_code,document_no,item_no,ex_code,ex_name,ex_spec,units,package_spec,package_units,firm_id,batch_no,gen_serial,";
            sql += "quantity,inventory,batch_code,expire_date,purchase_price,discount,trade_price,retail_price,supplier_id,location_code,prduce_date,barcode_no,";
            sql += "import_detail_memos) values('" + ls_storage_code_one + "','" + ls_documentno_one_imp + "',1,'" + ls_ex_code + "','" + ls_ex_name + "','" + ls_ex_spec + "','" + ls_units + "','" + ls_package_spec + "',";
            sql += "'" + ls_package_units + "','" + ls_firm_id + "','" + ls_batch_no + "','" + ll_gen_serial + "'," + quantity.ToString() + ",1,'" + ls_batch_code + "',to_date('" + ldtm_expire_date + "','yyyy-mm-dd hh24:mi:ss'),";
            sql += "" + ldec_purchase.ToString() + "," + ldec_radio.ToString() + "," + ldec_trade.ToString() + "," + ldec_retail.ToString() + ",'" + ls_supplier_id + "','" + ls_location_code + "',null,'" + barcode_no + "','高值耗材') ";

            idc.Add(sql, "写ex_import_detail表失败!");

            //一级库存
            sql = "select count(*) from ex_stock t where t.storage_code='" + ls_storage_code_one + "' and t.ex_code='" + ls_ex_code + "' and ";
            sql += " t.ex_spec='" + ls_ex_spec + "' and t.package_spec='" + ls_package_spec + "' and t.firm_id='" + ls_firm_id + "' and t.batch_no='" + ls_batch_no + "'";
            dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            if (dt.Rows.Count < 1 || int.Parse(dt.Rows[0][0].ToString()) < 1)
            {
                sql = "insert into exstock.ex_stock(storage_code,ex_code,ex_name,ex_spec,units,package_spec,package_units,firm_id,batch_no,quantity,batch_code,expire_date,";
                sql += "purchase_price,trade_price,retail_price,supplier_id,location_code,supply_indicator,barcode_no,last_updatetime) values(";
                sql += "'" + ls_storage_code_one + "','" + ls_ex_code + "','" + ls_ex_name + "','" + ls_ex_spec + "','" + ls_units + "','" + ls_package_spec + "','" + ls_package_units + "','" + ls_firm_id + "',";
                sql += "'" + ls_batch_no + "',0,'" + ls_batch_code + "',to_date('" + ldtm_expire_date + "','yyyy-mm-dd hh24:mi:ss')," + ldec_purchase.ToString() + "," + ldec_trade.ToString() + "," + ldec_retail.ToString() + ",'" + ls_supplier_id + "',";
                sql += "'" + ls_location_code + "',1,'" + barcode_no + "',sysdate) ";
                idc.Add(sql, "写入ex_stock表失败!");
            }
            else
            {
                sql = "update ex_stock t  set t.batch_no = '" + ls_batch_no + "',t.last_updatetime = sysdate where t.storage_code='" + ls_storage_code_one + "' and ";
                sql += " t.ex_code='" + ls_ex_code + "' and t.ex_spec='" + ls_ex_spec + "' and t.package_spec='" + ls_package_spec + "' and t.firm_id='" + ls_firm_id + "'";
                idc.Add(sql, "更新ex_stock表失败!");
            }

            //一级库出库
            if (return_flag)
            {
                import_class = "患者退费";
                account_receivable = -1 * ldec_purchase;
            }
            else
            {
                import_class = "备货出库";
                account_receivable = ldec_purchase;
            }
            string storage_name;
            sql = "select dept_name from dept_dict where dept_code = '" + storage_code + "'";
            dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            if (dt.Rows.Count < 1)
            {
                storage_name = "";
            }
            else
                storage_name = dt.Rows[0][0].ToString();
            sql = "insert into exstock.ex_export_master(storage_code,document_no,export_class,export_date,receiver_id,receiver_name,account_receivable,account_payed,";
            sql += "additional_fee,operator_usercode,account_indicator,acct_date,acct_usercode, imp_document_no,export_memos ) values( ";
            sql += "'" + ls_storage_code_one + "','" + ls_documentno_one_exp + "','" + import_class + "',sysdate,'" + storage_code + "','" + storage_name + "'," + account_receivable.ToString() + ",null,";
            sql += "null,'" + ls_operator_usercode + "',1,sysdate,'" + ls_operator_usercode + "',null,'高值耗材') ";
            idc.Add(sql, "写出库ex_export_master表失败!");
            //细表
            //sql = "SELECT EXSTOCK.GEN_SERIAL_EX.NEXTVAL FROM DUAL";
            //dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            //if (dt.Rows.Count < 1)
            //    return "取生产序列号失败!";
            //else
            //    ll_gen_serial = dt.Rows[0][0].ToString();
            #region
            ll_gen_serial = "";
            if (!PlatCommon.Common.PublicFunction.GetSequeceFromAuto("消耗品出入库单号序列", PlatCommon.SysBase.SystemParm.HisUnitCode, ref ll_gen_serial) || string.IsNullOrEmpty(ll_gen_serial) || string.IsNullOrEmpty(sysdate))
            {
                return "取GEN_SERIAL_EX序列失败!";//获取序列失败
            }
            ll_gen_serial = Convert.ToDateTime(sysdate).ToString("yyMMdd") + ll_gen_serial;

            #endregion
            sql = "insert into exstock.ex_export_detail(storage_code,document_no,item_no,ex_code,ex_name,ex_spec,units,package_spec,package_units,firm_id,batch_no,gen_serial,";
            sql += "quantity,inventory,batch_code,expire_date,purchase_price,trade_price,retail_price,supplier_id,location_code,provide_no,barcode_no,patient_id,visit_id,inp_or_outp,visit_date,visit_no,";
            sql += "bill_item_no,export_detail_memos ) values('" + ls_storage_code_one + "','" + ls_documentno_one_exp + "',1,'" + ls_ex_code + "','" + ls_ex_name + "','" + ls_ex_spec + "','" + ls_units + "','" + ls_package_spec + "',";
            sql += "'" + ls_package_units + "','" + ls_firm_id + "','" + ls_batch_no + "','" + ll_gen_serial + "'," + quantity.ToString() + ",0,'" + ls_batch_code + "',to_date('" + ldtm_expire_date + "','yyyy-mm-dd hh24:mi:ss'),";
            sql += "" + ldec_purchase.ToString() + "," + ldec_trade.ToString() + "," + ldec_retail.ToString() + ",'" + ls_supplier_id + "','" + ls_location_code + "',null,'" + barcode_no + "',null,";
            sql += "null,null,null,null,null,'高值耗材') ";
            idc.Add(sql, "写出库ex_import_detail表失败!");

            //三级库入库单号
            string ls_documentno_clinic_imp = "";
            string ls_documentno_clinic_exp = "";
            rev = GetImportNo(db, storage_code, ref ls_documentno_clinic_imp);
            if (!string.IsNullOrEmpty(rev))
                return rev;
            //出库单号
            rev = GetExportNo(db, storage_code, ref ls_documentno_clinic_exp);
            if (!string.IsNullOrEmpty(rev))
                return rev;
            if (return_flag)
            {
                import_class = "患者退费";
                account_receivable = -1 * ldec_purchase;
            }
            else
            {
                import_class = "备货入库";
                account_receivable = ldec_purchase;
            }
            sql = "insert into exstock.ex_import_master(storage_code,document_no,import_class,import_date,supplier_id,supplier_name,account_receivable,account_payed,";
            sql += "additional_fee,operator_usercode,account_indicator,acct_date,acct_usercode,ex_document_no,import_memos ) values('" + storage_code + "','" + ls_documentno_clinic_imp + "','" + import_class + "',sysdate,";
            sql += "'" + ls_storage_code_one + "','" + ls_storage_name_one + "'," + account_receivable.ToString() + ",null,null,'" + ls_operator_usercode + "',1,sysdate,'" + ls_operator_usercode + "','" + ls_documentno_clinic_exp + "','高值耗材') ";
            idc.Add(sql, "三级库入库失败");
            //细表
            //sql = "SELECT EXSTOCK.GEN_SERIAL_EX.NEXTVAL FROM DUAL";
            //dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            //if (dt.Rows.Count < 1)
            //    return "取生产序列号失败!";
            //else
            //    ll_gen_serial = dt.Rows[0][0].ToString();
            #region
            ll_gen_serial = "";
            if (!PlatCommon.Common.PublicFunction.GetSequeceFromAuto("消耗品出入库单号序列", PlatCommon.SysBase.SystemParm.HisUnitCode, ref ll_gen_serial) || string.IsNullOrEmpty(ll_gen_serial) || string.IsNullOrEmpty(sysdate))
            {
                return "取生产序列号失败!";//获取序列失败
            }
            ll_gen_serial = Convert.ToDateTime(sysdate).ToString("yyMMdd") + ll_gen_serial;

            #endregion
            sql = "insert into exstock.ex_import_detail(storage_code,document_no,item_no,ex_code,ex_name,ex_spec,units,package_spec,package_units,firm_id,batch_no,gen_serial,quantity,inventory,batch_code,expire_date,";
            sql += "purchase_price,discount,trade_price,retail_price,supplier_id,location_code,prduce_date,barcode_no,import_detail_memos) values('" + storage_code + "','" + ls_documentno_clinic_imp + "',1,'" + ls_ex_code + "',";
            sql += "'" + ls_ex_name + "','" + ls_ex_spec + "','" + ls_units + "','" + ls_package_spec + "','" + ls_package_units + "','" + ls_firm_id + "','" + ls_batch_no + "','" + ll_gen_serial + "',";
            sql += quantity.ToString() + ",1,'" + ls_batch_code + "',to_date('" + ldtm_expire_date + "','yyyy-mm-dd hh24:mi:ss')," + ldec_purchase.ToString() + "," + ldec_radio.ToString() + "," + ldec_trade.ToString() + "," + ldec_retail.ToString() + ",";
            sql += "'" + ls_supplier_id + "','" + ls_location_code + "',null,'" + barcode_no + "','高值耗材') ";
            idc.Add(sql, "写三级库细表失败");
            //三级库存
            sql = "select count(*) from ex_stock t where t.storage_code='" + storage_code + "' and t.ex_code='" + ls_ex_code + "' and t.ex_spec='" + ls_ex_spec + "' and ";
            sql += "t.package_spec='" + ls_package_spec + "' and t.firm_id='" + ls_firm_id + "' and t.batch_no='" + ls_batch_no + "'";
            dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            if (dt.Rows.Count < 1 || int.Parse(dt.Rows[0][0].ToString()) < 1)
            {
                sql = "insert into exstock.ex_stock(storage_code,ex_code,ex_name,ex_spec,units,package_spec,package_units,firm_id,batch_no,quantity,batch_code,expire_date,";
                sql += "purchase_price,trade_price,retail_price,supplier_id,location_code,supply_indicator,barcode_no,last_updatetime) values( ";
                sql += "'" + storage_code + "','" + ls_ex_code + "','" + ls_ex_name + "','" + ls_ex_spec + "','" + ls_units + "','" + ls_package_spec + "','" + ls_package_units + "','" + ls_firm_id + "',";
                sql += "'" + ls_batch_no + "',0,'" + ls_batch_code + "',to_date('" + ldtm_expire_date + "','yyyy-mm-dd hh24:mi:ss')," + ldec_purchase.ToString() + "," + ldec_trade.ToString() + "," + ldec_retail.ToString() + ",'" + ls_supplier_id + "',";
                sql += "'" + ls_location_code + "',1,'" + barcode_no + "',sysdate )";
                idc.Add(sql, "插入三级库存失败");
            }
            else
            {
                sql = "update ex_stock t  set t.batch_no = '" + ls_batch_no + "',t.last_updatetime = sysdate where t.storage_code='" + storage_code + "' and ";
                sql += "t.ex_code='" + ls_ex_code + "' and t.ex_spec='" + ls_ex_spec + "' and t.package_spec='" + ls_package_spec + "' and t.firm_id='" + ls_firm_id + "'";
                idc.Add(sql, "更新三级库存失败");
            }
            //三级库出库
            if (return_flag)
            {
                import_class = "患者退费";
                account_receivable = -1 * ldec_purchase;
            }
            else
            {
                import_class = "患者使用";
                account_receivable = ldec_purchase;
            }
            sql = "insert into exstock.ex_export_master(storage_code,document_no,export_class,export_date,receiver_id,receiver_name,account_receivable,account_payed,";
            sql += "additional_fee,operator_usercode,account_indicator,acct_date,acct_usercode, imp_document_no,export_memos) values(";
            sql += "'" + storage_code + "','" + ls_documentno_clinic_exp + "','" + import_class + "',sysdate,'" + patient_id + "','" + patient_name + "'," + account_receivable.ToString() + ",null,";
            sql += "null,'" + ls_operator_usercode + "',1,sysdate,'" + ls_operator_usercode + "',null,'高值耗材')";
            idc.Add(sql, "三级出库失败");
            //细表
            //sql = "SELECT EXSTOCK.GEN_SERIAL_EX.NEXTVAL FROM DUAL";
            //dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            //if (dt.Rows.Count < 1)
            //    return "取生产序列号失败!";
            //else
            //    ll_gen_serial = dt.Rows[0][0].ToString();
            #region
            ll_gen_serial = "";
            if (!PlatCommon.Common.PublicFunction.GetSequeceFromAuto("消耗品出入库单号序列", PlatCommon.SysBase.SystemParm.HisUnitCode, ref ll_gen_serial) || string.IsNullOrEmpty(ll_gen_serial) || string.IsNullOrEmpty(sysdate))
            {
                return "取生产序列号失败!";//获取序列失败
            }
            ll_gen_serial = Convert.ToDateTime(sysdate).ToString("yyMMdd") + ll_gen_serial;

            #endregion
            sql = "insert into exstock.ex_export_detail(storage_code,document_no,item_no,ex_code,ex_name,ex_spec,units,package_spec,package_units,firm_id,batch_no,gen_serial,";
            sql += "quantity,inventory,batch_code,expire_date,purchase_price,trade_price,retail_price,supplier_id,location_code,provide_no,barcode_no,patient_id,";
            sql += "visit_id,inp_or_outp,visit_date,visit_no,bill_item_no,export_detail_memos) values('" + storage_code + "','" + ls_documentno_clinic_exp + "',1,'" + ls_ex_code + "',";
            sql += "'" + ls_ex_name + "','" + ls_ex_spec + "','" + ls_units + "','" + ls_package_spec + "','" + ls_package_units + "','" + ls_firm_id + "','" + ls_batch_no + "','" + ll_gen_serial + "',";
            sql += quantity.ToString() + ",0,'" + ls_batch_code + "',to_date('" + ldtm_expire_date + "','yyyy-mm-dd hh24:mi:ss')," + ldec_purchase.ToString() + "," + ldec_trade.ToString() + ",";
            sql += ldec_retail.ToString() + ",'" + ls_supplier_id + "','" + ls_location_code + "',null,'" + barcode_no + "','" + patient_id + "'," + visit_id + ",2,null,null," + billitemno + ",'高值耗材') ";
            idc.Add(sql, "写出库细表失败");

            if (return_flag)
            {
                //更新原出库记录
                sql = "update ex_export_detail t set t.return_document_no='" + ls_documentno_clinic_exp + "' where t.document_no='" + ls_documentno_old + "' and t.barcode_no='" + barcode_no + "'";
                idc.Add(sql, "更新原出库记录失败");
                //更新原费用明细
                sql = "update  inp_bill_detail set return_document_no ='" + ls_documentno_clinic_exp + "' where patient_id ='" + patient_id + "' and visit_id = " + visit_id + " and   item_no =" + ll_bill_item_no;
                idc.Add(sql, "更新原费用明细失败");
                //更新备货信息表
                sql = "update ex_prepare_supplier t set t.status = '已备货确认',t.name=null,t.patient_id=null,t.visit_id=null,t.inp_or_outp=null,t.bill_item_no=null,t.document_no=null,retail_price=null,use_date=null ";
                sql += " where   t.confirm_storage_code = '" + storage_code + "' and t.barcode_no = '" + barcode_no + "' and t.status =  '患者使用'";
                idc.Add(sql, "更新备货信息表失败");
                //取价表信息，更新费用明细表
                sql = "update  inp_bill_detail set document_no ='" + ls_documentno_clinic_exp + "' where patient_id ='" + patient_id + "' and visit_id = " + visit_id + " and  item_no =" + billitemno;
                idc.Add(sql, "取价表信息，更新费用明细表");
            }
            else
            {
                //更新备货信息表
                sql = "update ex_prepare_supplier t set t.status = '患者使用',t.name='" + patient_name + "',t.patient_id='" + patient_id + "',t.visit_id=" + visit_id + ",t.inp_or_outp=2,";
                sql += " t.bill_item_no=" + billitemno + ",t.document_no='" + ls_documentno_clinic_exp + "',retail_price=" + ldec_retail.ToString() + ",use_date=sysdate ";
                sql += " where   t.confirm_storage_code = '" + storage_code + "'  and	t.barcode_no = '" + barcode_no + "'  and	t.status =  '已备货确认'";
                idc.Add(sql, "更新备货信息表");
                //取价表信息，更新费用明细表
                sql = "update  inp_bill_detail set document_no ='" + ls_documentno_clinic_exp + "' where patient_id ='" + patient_id + "' and visit_id = " + visit_id + "  and  item_no =" + billitemno;
                idc.Add(sql, "取价表信息，更新费用明细表");
            }
            return "";
        }
        //取一级库入库单号
        /// <summary>
        /// 得到某一库房的新入库单号
        /// </summary>
        /// <param name="idc">待更新数据表</param>
        /// <param name="as_storage">库房编码</param>
        /// <param name="as_importno">新单据号</param>
        /// <returns>成功时返回空否则返回错误</returns>
        public static string GetImportNo(Utility.OracleODP.OracleBaseClass db, string as_storage, ref string as_importno)
        {
            //取得组成单据的数据
            string sql = "SELECT IMPORT_NO_PREFIX,nvl(IMPORT_NO_AVA,1) IMPORT_NO_AVA,nvl(IMPORT_TYPE_LEN,8) IMPORT_TYPE_LEN,IMPORT_IS_SQL ";
            sql += "FROM EXSTOCK.EX_STORAGE_DEPT	WHERE storage_code = '" + as_storage + "' ";

            string ls_prefixno; //前缀
            string ll_ava;         //当前入库单号
            int ll_length;       //除前缀和SQL语句后，剩下的长度
            string ls_sql;            //中间部分的SQL语句
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetListUseDB(sql, db).Tables[0];
            if (dt.Rows.Count < 1)
            {
                return "取组成入库单的数据时发生错误！";
            }
            ls_prefixno = dt.Rows[0]["IMPORT_NO_PREFIX"].ToString();
            ll_ava = dt.Rows[0]["IMPORT_NO_AVA"].ToString();
            ll_length = int.Parse(dt.Rows[0]["IMPORT_TYPE_LEN"].ToString());
            ls_sql = dt.Rows[0]["IMPORT_IS_SQL"].ToString();

            //运行sql，产生单号的中间部分
            string ls_mid = "";
            if (!string.IsNullOrEmpty(ls_sql))
            {
                dt = new NM_Service.NMService.ServerPublicClient().GetListUseDB(sql, db).Tables[0];
                if (dt.Rows.Count > 0) //
                {
                    ls_mid = dt.Rows[0][0].ToString(); //产生单号的中间部分
                }
            }
            //取得后面部分
            string ls_temp = "";

            if (ll_ava.Length <= ll_length)
            {
                for (int rowIndex = 0; rowIndex < ll_length - ll_ava.Length; rowIndex++)
                {
                    ls_temp = ls_temp + "0";
                }
            }
            else
            {
                ll_ava = "1";
                for (int ll_loop = 0; ll_loop < ll_length - 1; ll_loop++)
                {
                    ls_temp = ls_temp + "0";
                }
            }
            ll_ava = ls_temp + ll_ava;
            //生成出库单据号
            if ((!string.IsNullOrEmpty(ls_prefixno)) && (!string.IsNullOrEmpty(ls_mid)))
            { as_importno = ls_prefixno + ls_mid + ll_ava; }
            else
            {
                if ((string.IsNullOrEmpty(ls_prefixno) || ls_prefixno.Length == 0) && (!string.IsNullOrEmpty(ls_mid)))
                { as_importno = ls_mid + ll_ava; }
                else//只有前缀，没有SQL
                { as_importno = ls_prefixno + ll_ava; }
            }
            int ll_ava1 = int.Parse(ll_ava) + 1;
            string sqla = "update 	EXSTOCK.EX_STORAGE_DEPT  set    IMPORT_NO_AVA = " + ll_ava1 + " where  storage_code ='" + as_storage + "' and    nvl(IMPORT_NO_AVA,1) =" + (ll_ava1 - 1).ToString();
            Dictionary<string, string> idc = new Dictionary<string, string>();
            idc.Add(sqla, "更新EX_STORAGE_DEPT中当前出库单号发生错误!");
            string rev = new NM_Service.NMService.ServerPublicClient().SaveExcSql(idc, db);
            if (!string.IsNullOrEmpty(rev))
            {
                db.RollbackTransaction();
                return rev;
            }
            return "";

        }
        /// <summary>
        /// 得到某一库房的新入库单号
        /// </summary>
        /// <param name="idc">待更新数据表</param>
        /// <param name="as_storage">库房编码</param>
        /// <param name="as_importno">新单据号</param>
        /// <returns>成功时返回空否则返回错误</returns>
        public static string GetExportNo(Utility.OracleODP.OracleBaseClass db, string as_storage, ref string as_importno)
        {
            //取得组成单据的数据
            string sql = "SELECT EXPORT_NO_PREFIX,nvl(EXPORT_NO_AVA,1) EXPORT_NO_AVA,nvl(EXPORT_TYPE_LEN,8) EXPORT_TYPE_LEN,EXPORT_IS_SQL ";
            sql += "FROM EXSTOCK.EX_STORAGE_DEPT	WHERE storage_code = '" + as_storage + "' ";

            string ls_prefixno; //前缀
            string ll_ava;         //当前入库单号
            int ll_length;       //除前缀和SQL语句后，剩下的长度
            string ls_sql;            //中间部分的SQL语句
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetListUseDB(sql, db).Tables[0];
            if (dt.Rows.Count < 1)
            {
                return "取组成入库单的数据时发生错误！";
            }
            ls_prefixno = dt.Rows[0]["EXPORT_NO_PREFIX"].ToString();
            ll_ava = dt.Rows[0]["EXPORT_NO_AVA"].ToString();
            ll_length = int.Parse(dt.Rows[0]["EXPORT_TYPE_LEN"].ToString());
            ls_sql = dt.Rows[0]["EXPORT_IS_SQL"].ToString();

            //运行sql，产生单号的中间部分
            string ls_mid = "";
            if (!string.IsNullOrEmpty(ls_sql))
            {
                dt = new NM_Service.NMService.ServerPublicClient().GetListUseDB(sql, db).Tables[0];
                if (dt.Rows.Count > 0) //
                {
                    ls_mid = dt.Rows[0][0].ToString(); //产生单号的中间部分
                }
            }
            //取得后面部分
            string ls_temp = "";

            if (ll_ava.Length <= ll_length)
            {
                for (int rowIndex = 0; rowIndex < ll_length - ll_ava.Length; rowIndex++)
                {
                    ls_temp = ls_temp + "0";
                }
            }
            else
            {
                ll_ava = "1";
                for (int ll_loop = 0; ll_loop < ll_length - 1; ll_loop++)
                {
                    ls_temp = ls_temp + "0";
                }
            }
            ll_ava = ls_temp + ll_ava;
            //生成出库单据号
            if ((!string.IsNullOrEmpty(ls_prefixno)) && (!string.IsNullOrEmpty(ls_mid)))
            { as_importno = ls_prefixno + ls_mid + ll_ava; }
            else
            {
                if ((string.IsNullOrEmpty(ls_prefixno) || ls_prefixno.Length == 0) && (!string.IsNullOrEmpty(ls_mid)))
                { as_importno = ls_mid + ll_ava; }
                else//只有前缀，没有SQL
                { as_importno = ls_prefixno + ll_ava; }
            }
            int ll_ava1 = int.Parse(ll_ava) + 1;
            string sqla = "update 	EXSTOCK.EX_STORAGE_DEPT  set    EXPORT_NO_AVA = " + ll_ava1 + " where  storage_code ='" + as_storage + "' and    nvl(EXPORT_NO_AVA,1) =" + (ll_ava1 - 1).ToString();
            Dictionary<string, string> idc = new Dictionary<string, string>();
            idc.Add(sqla, "更新EX_STORAGE_DEPT中当前出库单号发生错误!");
            string rev = new NM_Service.NMService.ServerPublicClient().SaveExcSql(idc, db);
            if (!string.IsNullOrEmpty(rev))
            {
                db.RollbackTransaction();
                return rev;
            }
            return "";

        }
        //批次号生成
        public static string GetEpeGenNewBatchNo()
        {
            string sql = "SELECT EXSTOCK.BATCH_NO_EX.NEXTVAL,to_char(sysdate,'yymmdd') FROM DUAL";
            int ll_batch_no;
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            ll_batch_no = int.Parse(dt.Rows[0][0].ToString());
            string ls_sys = dt.Rows[0][1].ToString();
            string as_batch_no = ls_sys + ll_batch_no.ToString("000000");
            if (string.IsNullOrEmpty(as_batch_no))
                return "";
            return as_batch_no;
        }
        #endregion

        /// <summary>
        /// 获取医嘱状态颜色
        /// </summary>
        /// <returns></returns>
        public static Color GetOrderStatusColor(string statusCode, bool blnLongTerm, DateTime dtOrderEnter, DateTime dtOrderCancel, DateTime dtOrderStop, bool blnForeColor)
        {
            // 开医嘱录入日期: ENTER_DATE_TIME
            // 作废医嘱时间:   CANCEL_DATE_TIME
            // 停医嘱录入日期: STOP_ORDER_DATE_TIME
            switch (statusCode)
            {
                case "0":   //	 返回 状态     (暗金字/白底)      
                    if (blnLongTerm == false)       // 临时： (黑字/白底)
                    {
                        return (blnForeColor ? Color.Black : Color.White);
                    }
                    else                            // 长期： (暗金字/白)
                    {
                        return (blnForeColor ? Color.DarkOrange : Color.White);
                    }
                case "1":   //	 新开       (绿字/白底)      
                    if (blnLongTerm == false)       // 临时： (黑字/白底)
                    {
                        return (blnForeColor ? Color.Black : Color.White);
                    }
                    else                            // 长期： (蓝/白)
                    {
                        return (blnForeColor ? Color.Blue : Color.White);
                    }
                case "5":   //	医生保存        (蓝字/白底)                    
                    return (blnForeColor ? Color.Blue : Color.White);

                case "6":   //	新开提交
                            // 如果是 新开医嘱的提交状态

                    if (dtOrderCancel.Equals(DateTime.MinValue)
                        && dtOrderStop.Equals(DateTime.MinValue))
                    {
                        if (blnLongTerm == false)       // 临时： (黑字/白底)
                        {
                            return (blnForeColor ? Color.Black : Color.White);
                        }
                        else                            // 长期： (蓝/白)
                        {
                            return (blnForeColor ? Color.Blue : Color.White);
                        }
                    }

                    // 作废医嘱的提交状态        
                    else if (dtOrderStop.Equals(DateTime.MinValue))
                    {
                        // 临时:  (红/白)   长期:   (红/白)
                        return (blnForeColor ? Color.Red : Color.White);
                    }

                    // 停止医嘱的提交状态        
                    else if (dtOrderCancel.Equals(DateTime.MinValue))
                    {
                        if (blnLongTerm == false)       // 临时： (黑/白)
                        {
                            return (blnForeColor ? Color.Black : Color.LightGray);
                        }
                        else                            // 长期: (蓝/白)
                        {
                            return (blnForeColor ? Color.Blue : Color.White);
                        }
                    }
                    else
                    {
                        throw new Exception("医嘱同时具[开医嘱时间]、[作废医嘱时间]、[停医嘱日期]，是不正确的!");
                    }
                case "7":   //	医生停止        (黑字/灰底)
                    return (blnForeColor ? Color.Black : Color.LightGray);

                case "8":   //	医生作废        (红字/灰底)
                    return (blnForeColor ? Color.Red : Color.LightGray);

                case "2":   //	执行            
                    if (blnLongTerm == false)       // 临: (黑/灰)   
                    {
                        return (blnForeColor ? Color.Black : Color.LightGray);
                    }
                    else                           // 长:  (蓝/灰)
                    {
                        return (blnForeColor ? Color.Blue : Color.LightGray);
                    }

                case "3":   //	停止
                    if (blnLongTerm == false)       // 临: 黑/灰
                    {
                        return (blnForeColor ? Color.Black : Color.LightGray);
                    }
                    else                           // 长: 蓝/灰
                    {
                        return (blnForeColor ? Color.Blue : Color.LightGray);
                    }

                case "4":   //	作废
                    // 临： 红/灰  长: 红/灰
                    return (blnForeColor ? Color.Red : Color.LightGray);

                default:
                    //    throw new Exception("不支持的医嘱状态!");
                    return (blnForeColor ? Color.LightGray : Color.LightGray);
            }
        }
        /// <summary>
        /// 判断系统授权
        /// </summary>
        /// <param name="hisunitcode">院区编码</param>
        /// <param name="as_app_code">系统代码</param>
        /// <param name="as_app_name">系统名称</param>
        /// <param name="as_app_version">系统版本</param>
        /// <param name="encryptCode">系统授权码</param>
        /// <param name="msg">返回消息</param>
        /// <param name="as_day">返回剩余可使用天数</param>
        /// <returns>true可用</returns>
        public static bool GetSystemAuthorization(string hisunitcode, string as_app_code, string as_app_name, string as_app_version, string encryptCode, ref string msg, ref int as_day)
        {
            string sql = "";
            msg = "";
            DataTable dt;
            ServerPublicClient spc = new ServerPublicClient();
            DateTime sysdate;
            string ls_hospital_name;
            sql = "SELECT trim(HOSPITAL_CONFIG.HOSPITAL) FROM HOSPITAL_CONFIG where HIS_UNIT_CODE='" + hisunitcode + "'";
            dt = spc.GetList(sql).Tables[0];
            if (dt.Rows.Count < 1)
            {
                msg = "取医院名称失败!";
                return false;
            }
            else
            {
                ls_hospital_name = dt.Rows[0][0].ToString();
            }
            sysdate =  spc.GetSysDate();
            DateTime ld_today = sysdate.Date;
            string ls_appname = as_app_code.Trim().ToUpper();
            //取密码
            //sql = "Select encrypt_code From encrypt_dict Where upper(application_code)='" + ls_appname + "'";
            //dt = spc.GetList(sql).Tables[0];
            //string ls_source = "";
            //if (dt.Rows.Count < 1)
            //{
            //    msg = "您没有购买[" + as_app_name + "]系统，如果您要使用，请与供应商联系。!";
            //    return false;
            //}
            //else
            //{
            //    ls_source = dt.Rows[0][0].ToString();
            //}
        
            if (string.IsNullOrEmpty(encryptCode))
            {
                msg = "您没有[" + as_app_name + "]系统注册码，如果您要使用，请与供应商联系。";
                return false;
            }
            ////解密传入的字符串
            //string ls_dest = DecryptNew(encryptCode);
            //if (string.IsNullOrEmpty(ls_dest))
            //{
            //    msg = "系统解密错误，请与供应商联系。";
            //    return false;
            //}
            string ls_dest = "";
            //解密传入的字符串
            if (sysdate >= Convert.ToDateTime("2019-01-02"))
            {
                ls_dest = DecryptNew(encryptCode);
                if (string.IsNullOrEmpty(ls_dest))
                {
                    msg = "系统解密错误，请与供应商联系。";
                    return false;
                }
            }
            else
            {
                //MessageBox.Show("ok");
                return true;
            }
            //返回值   北京天健医院,100,HISMACHINES,2016-12-31,6.6,
            string[] str = ls_dest.Split(',');
            string ls_dncrypt_hospital_name = str[0].Trim();
            string ls_dncrypt_client_number = str[1].Trim();
            string ls_dncrypt_app_code = str[2].Trim();
            string ls_dncrypt_expire_date = str[3].Trim();
            string ls_dncrypt_app_version = str[4].Trim();
            string ls_unitcode= str[5].Trim();
            //判断医院名称
            if (!ls_dncrypt_hospital_name.Equals(ls_hospital_name))
            {
                msg = "您没有购买天健医院信息系统，如果您要使用，请与供应商联系。";
                return false;
            }
            //判断程序名称
            if (!ls_dncrypt_app_code.Equals(ls_appname) &&!ls_appname.Equals("PLATFORM") )
            {
                msg = "您没有购买[" + as_app_name + "]系统，如果您要使用，请与供应商联系。";
                return false;
            }
            ////判断版本号
            //if (!ls_dncrypt_app_version.Equals(PlatCommon.SysBase.SystemParm.copyright_verson))
            //{
            //    msg = "当前版本与授权本版号不一致，请与供应商联系。";
            //    return false;
            //}
            ////判断版本号
            //if (!ls_unitcode.Equals(PlatCommon.SysBase.SystemParm.copyright_unit))
            //{
            //    msg = "当前所属医院标识与授权对应的医院标识不一致，请与供应商联系。";
            //    return false;
            //}
            //计算失效天数
            string ls_dncrypt_expire_date_disp = ls_dncrypt_expire_date;  // 显示提示使用的到期日期 Yzw20240920
            ls_dncrypt_expire_date = ls_dncrypt_expire_date + " 23:59:59"; // 解决剩余时间提醒不准确的问题，有效期截止到当天23:59:59 Yzw20240920
            DateTime ld_dncrypt_expiredate = DateTime.Parse(ls_dncrypt_expire_date);
            //加上日期比较逻辑，解决在超时1天的情况下，系统不提示到期的问题 Yzw20240920
            if (ld_dncrypt_expiredate.Date < ld_today.Date)
            {
                msg = "[" + as_app_name + "]系统到期，不能继续使用，请您立刻与供应商联系。" + ls_dncrypt_expire_date_disp;
                return false;
            }
            int ll_days = (ld_dncrypt_expiredate - ld_today).Days + 1;
            as_day = ll_days;
            if (ll_days > 30)
            {
                return true;
            }
            else if (ll_days <= 30 && ll_days > 0)
            {
                msg = "[" + as_app_name + "]系统还有 " + ll_days.ToString() + " 天使用权,请及时与供应商联系";

                return true;
            }
            else if (ll_days <= 0)
            {
                msg = "[" + as_app_name + "]系统到期，不能继续使用，请您立刻与供应商联系。" + ls_dncrypt_expire_date_disp;
                return false;
            }
            return true;
        }
        /// <summary>
        /// 判断系统授权
        /// </summary>
        /// <param name="as_app_code">系统代码</param>
        /// <param name="as_app_name">系统名称</param>
        /// <param name="as_app_version">系统版本</param>
        /// <param name="encryptCode">系统授权码</param>
        /// <param name="msg">返回消息</param>
        /// <param name="as_day">返回剩余可使用天数</param>
        /// <returns>true可用</returns>
        public static bool GetSystemAuthorization(string as_app_code, string as_app_name, string as_app_version, string encryptCode, ref string msg, ref int as_day)
        {
            string sql = "";
            msg = "";
            DataTable dt;
            ServerPublicClient spc = new ServerPublicClient();
            DateTime sysdate;
            string ls_hospital_name;
            sql = "SELECT trim(HOSPITAL_CONFIG.HOSPITAL) FROM HOSPITAL_CONFIG";
            dt = spc.GetList(sql).Tables[0];
            if (dt.Rows.Count < 1)
            {
                msg = "取医院名称失败!";
                return false;
            }
            else
            {
                ls_hospital_name = dt.Rows[0][0].ToString();
            }
            sysdate = spc.GetSysDate();
            DateTime ld_today = sysdate.Date;
            string ls_appname = as_app_code.Trim().ToUpper();
            //取密码
            //sql = "Select encrypt_code From encrypt_dict Where upper(application_code)='" + ls_appname + "'";
            //dt = spc.GetList(sql).Tables[0];
            //string ls_source = "";
            //if (dt.Rows.Count < 1)
            //{
            //    msg = "您没有购买[" + as_app_name + "]系统，如果您要使用，请与供应商联系。!";
            //    return false;
            //}
            //else
            //{
            //    ls_source = dt.Rows[0][0].ToString();
            //}
            if (string.IsNullOrEmpty(encryptCode))
            {
                msg = "您没有[" + as_app_name + "]系统注册码，如果您要使用，请与供应商联系。";
                return false;
            }
            string ls_dest = "";
            //解密传入的字符串
            if (sysdate >= Convert.ToDateTime("2019-01-02"))
            {
                ls_dest = DecryptNew(encryptCode);
                if (string.IsNullOrEmpty(ls_dest))
                {
                    msg = "系统解密错误，请与供应商联系。";
                    return false;
                }
            }
            else
            {
                MessageBox.Show("ok");
                return true;
            }
            //返回值   北京天健医院,100,HISMACHINES,2016-12-31,6.6,
            string[] str = ls_dest.Split(',');
            string ls_dncrypt_hospital_name = str[0];
            string ls_dncrypt_client_number = str[1];
            string ls_dncrypt_app_code = str[2];
            string ls_dncrypt_expire_date = str[3];
            string ls_dncrypt_app_version = str[4];
            //判断医院名称
            if (!ls_dncrypt_hospital_name.Equals(ls_hospital_name))
            {
                msg = "您没有购买天健医院信息系统，如果您要使用，请与供应商联系。";
                return false;
            }
            //判断程序名称
            //if (!ls_dncrypt_app_code.Equals(ls_appname))
            //{
            //    msg = "您没有购买[" + as_app_name + "]系统，如果您要使用，请与供应商联系。";
            //    return false;
            //}
            ////判断版本号
            //if (!ls_dncrypt_app_code.Equals(ls_appname))
            //{
            //    msg = "您没有购买此子系统，如果您要使用，请与供应商联系。";
            //    return false;
            //}
            //计算失效天数
            string ls_dncrypt_expire_date_disp = ls_dncrypt_expire_date;  // 显示提示使用的到期日期 Yzw20240920
            ls_dncrypt_expire_date = ls_dncrypt_expire_date + " 23:59:59"; // 解决剩余天数提醒不准确的问题，有效期截止到当天23:59:59 Yzw20240920
            DateTime ld_dncrypt_expiredate = DateTime.Parse(ls_dncrypt_expire_date);
            //加上日期比较逻辑，解决在超时1天的情况下，系统不提示到期的问题 Yzw20240920
            if (ld_dncrypt_expiredate.Date < ld_today.Date)
            {
                msg = "[" + as_app_name + "]系统到期，不能继续使用，请您立刻与供应商联系。" + ls_dncrypt_expire_date_disp;
                return false;
            }
            int ll_days = (ld_dncrypt_expiredate - ld_today).Days + 1;
            as_day = ll_days;
            if (ll_days > 30)
            {
                return true;
            }
            else if (ll_days <= 30 && ll_days > 0)
            {
                msg = "[" + as_app_name + "]系统还有 " + ll_days.ToString() + " 天使用权,请及时与供应商联系";

                return true;
            }
            else if (ll_days <= 0)
            {
                msg = "[" + as_app_name + "]系统到期，不能继续使用，请您立刻与供应商联系。" + ls_dncrypt_expire_date_disp;
                return false;
            }
            return true;
        }

        /// <summary>
        /// 解密字符串
        /// </summary>
        /// <param name="Source">要解密的字符串</param>
        /// <param name="Key">密钥</param>
        /// <returns>解密后的字符串</returns>
        public static string DecryptNew(string src)
        {
            StringBuilder key = new StringBuilder(1000);

            string tmpStr = src.Replace("\r\n", "");

            if (DecryptStringForCS(tmpStr, key))
            {
                return key.ToString();
            }
            else
            {
                return "";
            }
        }

        /// <summary>
        /// 取年龄
        /// </summary>
        /// <param name="patientID">病人ID</param>
        /// <param name="visitID">住院次数</param>
        /// <param name="dischargeFlag">是否出院标识</param>
        /// <returns>返回年龄字符串</returns>
        public static string GetAge(string patientID, string visitID, bool dischargeFlag)
        {
            string sql = "";
            string age = "";
            if (dischargeFlag)
            {
                sql = "select cpr.chineseage(PAT_MASTER_INDEX.DATE_OF_BIRTH,pat_visit.discharge_date_time )from PAT_MASTER_INDEX, pat_visit ";
                sql += " where PAT_MASTER_INDEX.Patient_Id = pat_visit.patient_id and pat_visit.patient_id = '" + patientID + "' and pat_visit.visit_id = " + visitID;
            }
            else
            {
                sql = "select cpr.chineseage(DATE_OF_BIRTH,sysdate) from PAT_MASTER_INDEX where patient_id = '" + patientID + "'";
            }
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            if (dt.Rows.Count > 0)
                age = dt.Rows[0][0].ToString();
            else
                age = "";
            return age;
        }
        #region 系统时间
        public static string GetSysDate()
        {
            return new NM_Service.NMService.ServerPublicClient().GetSysDateTime();
        }
        #endregion
        #region 登录获取公共参数
        /// <summary>
        /// 根据医生站编码获取护理单元编码列表，用逗号间隔
        /// 2016-07-05 by lxm
        /// </summary>
        public static string GetWarcodeVsDept(string dept_code)
        {
            string warcode = "";
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetList("Select ward_code  From dept_vs_ward Where dept_code='" + dept_code + "'").Tables[0];
            if (dt.Rows.Count > 0)
            {
                warcode = dt.Rows[0]["WARD_CODE"].ToString();
            }
            return warcode;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns>返回药品类别字典</returns>
        public static DataTable GetDrugIndicatorDict()
        {
            string sql = "select INDICATOR_CODE,INDICATOR_NAME from DRUG_INDICATOR_DICT";
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            return dt;
        }
        /// <summary>
        /// 根据根据护理单元取医生科室
        /// 2016-07-05 by lxm
        /// </summary>
        public static string GetDeptVsWarCode(string ward_code)
        {
            string deptcode = "";
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetList("Select dept_code  From dept_vs_ward Where ward_code='" + ward_code + "'").Tables[0];
            if (dt.Rows.Count > 0)
            {
                deptcode = dt.Rows[0]["dept_code"].ToString();
            }
            return deptcode;
        }
        /// <summary>
        /// 获取护理人员的护理单元编码列表，用逗号间隔
        /// 2016-03-22 by 梁吉
        /// </summary>
        /// <param name="userName">登录用户名</param>
        /// <returns>护理单元编码列表</returns>
        public static bool GetWarcodelist(string userName)
        {
            NM_Service.NMService.ServerPublicClient sc = new NM_Service.NMService.ServerPublicClient();
            string sqlstr = "select a.group_code,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and a.group_class = '病区护士' and emp_no = '" + userName + "'  and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
            DataSet ds = sc.GetDataBySql(sqlstr);
            if (ds == null)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("您无权使用本系统！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
            int count = ds.Tables[0].Rows.Count;
            if (count < 1)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("您无权使用本系统！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
            if (count == 1)
            {
                //PlatCommon.SysBase.SystemParm.Ward_ID = ds.Tables[0].Rows[0][0].ToString();
                //PlatCommon.SysBase.SystemParm.WardName = ds.Tables[0].Rows[0][1].ToString();
                //PlatCommon.SysBase.SystemParm.Warcode_list = "'" + PlatCommon.SysBase.SystemParm.Ward_ID + "'";
            }
            else
            {

                frmSelectWard frm = new frmSelectWard();
                frm.GetWardList(userName);
                frm.ShowDialog();
                String ret = "";
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    //SystemParm.Ward_ID = ds.Tables[0].Rows[0][0].ToString();
                    foreach (DataRow dr in ds.Tables[0].Rows)
                    {
                        ret += "'" + dr[0] + "',";
                    }
                    if (!"".Equals(ret))
                        ret = ret.Remove(ret.Length - 1);
                }
            }
            return true;


        }
        //初始化系统参数
        public static void InitSystemPara(string appName)
        {
            using (NM_Service.NMService.ServerPublicClient client = new NM_Service.NMService.ServerPublicClient())
            {
                //增加读取医院
                string sql = "Select HOSPITAL,HIS_UNIT_CODE From HOSPITAL_CONFIG where HIS_UNIT_CODE='" + Utility.ConfigHelper.GetConfigString("UNITCODE") + "'";
                //string sql = "Select HOSPITAL,HIS_UNIT_CODE From HOSPITAL_CONFIG where HIS_UNIT_CODE='" + SystemParm.LoginUser.HISUNITCODE + "'";
                DataTable dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
                if (dt != null && dt.Rows.Count > 0)
                {
                    PlatCommon.SysBase.SystemParm.HospitalID = dt.Rows[0]["HOSPITAL"] == null ? "" : dt.Rows[0][0].ToString();
                    //PlatCommon.SysBase.SystemParm.HisUnitCode = dt.Rows[0]["HIS_UNIT_CODE"] == null ? "" : dt.Rows[0][0].ToString();
                }
                //读取CA参数 by梁吉 16-09-19 
                //#region 
                //string gs_enabled = PlatCommon.SysBase.SystemParm.GetParameterValue("BJCA_ENABLED", appName, "*", "*", SystemParm.HisUnitCode);
                //if ("1".Equals(gs_enabled))
                //{
                //    PlatCommon.SysBase.SystemParm.Gs_Bjca_Enabled = true;
                //}
                //else
                //{
                //    PlatCommon.SysBase.SystemParm.Gs_Bjca_Enabled = false;
                //}
                //string gs_snap = PlatCommon.SysBase.SystemParm.GetParameterValue("IS_TIME_SNAP", appName, "*", "*", SystemParm.HisUnitCode);
                //if ("1".Equals(gs_enabled))
                //{
                //    PlatCommon.SysBase.SystemParm.Gs_Time_Snap = true;
                //}
                //else
                //{
                //    PlatCommon.SysBase.SystemParm.Gs_Time_Snap = false;
                //}
                //#endregion
            }
            //消息
            string message = PlatCommon.SysBase.SystemParm.GetParameterValue("ENABLEMESSAGE", "*", "*", "*", SystemParm.HisUnitCode);
            if (!string.IsNullOrEmpty(message) && message.Equals("1"))
            {
                //取端口和IP
                string sql = "select ip,send_port,rec_port from message_port_ip";
                DataTable dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
                if (dt.Rows.Count < 1)
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("取收发消息ip表失败", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                if (string.IsNullOrEmpty(dt.Rows[0]["IP"].ToString()) || string.IsNullOrEmpty(dt.Rows[0]["SEND_PORT"].ToString()) || string.IsNullOrEmpty(dt.Rows[0]["REC_PORT"].ToString()))
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("取收发消息ip表失败", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                PlatCommon.SysBase.SystemParm.UdpClass = new PlatCommon.Common.UdpMessage(int.Parse(dt.Rows[0]["SEND_PORT"].ToString()), int.Parse(dt.Rows[0]["REC_PORT"].ToString()), dt.Rows[0]["IP"].ToString());

                //PlatCommon.SysBase.SystemParm.UdpClass = new PlatCommon.Common.UdpMessage(int.Parse(dt.Rows[0]["SEND_PORT"].ToString()));
            }

            #region CA
            //暂时没判断科室，后期看需求调整
            //bool caEnabled = Tjhis.Interface.CA.DbExt.getCaEnabled(appName, "", "");
            //if (caEnabled)
            //{
            //    if (PlatCommon.SysBase.SystemParm.CaBusiness == null)
            //    {
            //        PlatCommon.SysBase.SystemParm.CaBusiness = new Tjhis.Interface.CA.CaBusiness();
            //        PlatCommon.SysBase.SystemParm.CaBusiness.Init();
            //    }
            //}
            #endregion
        }

        /// <summary>
        /// 描述:保存操作员登录和登出日志,并实时更新机位在线状态 yhy
        /// </summary>
        /// <param name="as_application">应用程序名称  </param>
        /// <param name="as_user_name">程序用户名</param>
        /// <param name="as_in_out_flag">登录登出标志 in：登录；out：登出</param>
        /// <returns> (none)</returns>
        public static string gf_login_logout(string as_application, string as_user_name, string as_in_out_flag)
        {
            string ls_IpAddress, ls_hostname;//, ls_onlineflag, ls_msg;

            string sql = "";
            //获取主机名和IP地址
            sql = "Select sys_context('userenv','ip_address')  From dual";
            DataTable dt1 = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            if (dt1.Rows.Count > 0)
                ls_IpAddress = dt1.Rows[0][0].ToString();
            else
                return "";
            sql = "Select sys_context('userenv','host') From dual";
            DataTable dt2 = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
            if (dt2.Rows.Count > 0)
                ls_hostname = dt2.Rows[0][0].ToString();
            else
                return "";
            //向系统日志表中插入一条用户登录日志
            as_in_out_flag = as_in_out_flag.ToLower();
            Dictionary<string, string> idc = new Dictionary<string, string>();
            string sqllog = "Insert Into TJHIS_LOG(DATEX,SUBNO,TYPEX,CLIENT_NAME,APPLICATION,CLIENTINFO,USER_NAME, app_version)";
            sqllog += " Values (sysdate, 1,'" + as_in_out_flag + "','" + ls_hostname + "','" + as_application + "','" + ls_IpAddress + "','" + as_user_name + "','6.6')";
            idc.Add(sqllog, "插入日志出错！");
            string result = new NM_Service.NMService.PatientInDeptClient().SaveTable(idc);


            return "";
        }
        #endregion
        /// <summary>
        /// 打折公共函数
        /// </summary>
        /// <param name="ldec_discount_price">打折金额(减免金额)</param>
        /// <param name="ls_discount_demo">打折备注</param>
        public static int Getdiscount(string tpatient_id, string tvisit_id, string ls_item_class, string ls_item_code, string ls_item_spec, string ls_item_units, string ls_operator_code, decimal ldc_amount, decimal ldc_price, ref decimal ldec_discount_price, ref string ls_discount_demo)
        {
            ////暂时不打折暂时注释掉原来代码  2016-10-08 by 梁吉
            //NM_Service.NMService.ServerPublicClient dzspc = new NM_Service.NMService.ServerPublicClient();
            //string ls_sale_flag; ///是否打折   1打折   0或空不打折  
            //string tdiscount_ratio;
            //decimal ldec_discount_ratio;  ///折扣比例 不能大于1   0.01  0.21
            //string tdiscount_starttime, tdiscount_endtime;
            //DateTime ldt_discount_starttime, ldt_discount_endtime;  ////折扣开始时间   折扣结束时间
            //string ls_discount_operator;  ///折扣维护人
            //string tdiscount_datetime;
            //string ls_discount_deptcode; ///	打折科室代码
            //string tpricelist = "SELECT sale_flag , discount_ratio , discount_starttime , discount_endtime ,discount_operator , discount_datetime , discount_deptcode ";
            //tpricelist += " FROM PRICE_LIST ";
            //tpricelist += " WHERE ITEM_CLASS = '" + ls_item_class + "' AND ITEM_CODE = '" + ls_item_code + "' AND ITEM_SPEC = '" + ls_item_spec + "' AND UNITS = '" + ls_item_units;
            //tpricelist += "' AND   ( SYSDATE >= START_DATE AND (SYSDATE < STOP_DATE or STOP_DATE is null)) AND ROWNUM = 1 ";
            //DataTable dtdz = dzspc.GetList(tpricelist).Tables[0];
            //ls_sale_flag = dtdz.Rows[0]["SALE_FLAG"].ToString();
            //tdiscount_ratio = dtdz.Rows[0]["DISCOUNT_RATIO"].ToString();
            //if (string.IsNullOrEmpty(tdiscount_ratio))
            //{ ldec_discount_ratio = 0; }
            //else
            //{ ldec_discount_ratio = Convert.ToDecimal(tdiscount_ratio); }
            //tdiscount_starttime = dtdz.Rows[0]["DISCOUNT_STARTTIME"].ToString();
            //if (string.IsNullOrEmpty(tdiscount_starttime))
            //    tdiscount_starttime = "2011-01-01 00:00:00";
            //tdiscount_endtime = dtdz.Rows[0]["DISCOUNT_ENDTIME"].ToString();
            //if (string.IsNullOrEmpty(tdiscount_endtime))
            //    tdiscount_endtime = "2999-01-01 00:00:00";
            //ldt_discount_starttime = Convert.ToDateTime(tdiscount_starttime);
            //ldt_discount_endtime = Convert.ToDateTime(tdiscount_endtime);

            //ls_discount_operator = dtdz.Rows[0]["DISCOUNT_OPERATOR"].ToString();
            //tdiscount_datetime = dtdz.Rows[0]["DISCOUNT_DATETIME"].ToString();


            //ls_discount_deptcode = dtdz.Rows[0]["DISCOUNT_DEPTCODE"].ToString();
            //string ls_discount_name; //取患者打折方案名
            //string ls_discount_flag;  //打折方案是否可以标识
            //DateTime  ldt_date; 
            //string sqldz1 = "select discount_name,sysdate  from pat_visit where patient_id ='" + tpatient_id + "' and visit_id = " + tvisit_id;
            //DataTable dtdz1 = dzspc.GetList(sqldz1).Tables[0];
            //ls_discount_name = dtdz1.Rows[0][0].ToString();
            //ldt_date = DateTime.Parse(dtdz1.Rows[0][1].ToString());
            //dtdz1.Clear();
            //if (string.IsNullOrEmpty(ls_sale_flag))
            //    ls_sale_flag = "0";
            //if (ls_sale_flag.Equals("1")) ////aa 不能打折判断标识  
            //{
            //    if (string.IsNullOrEmpty(ls_discount_name) || ls_discount_name.Equals("无-9.00"))
            //    {
            //        if (ldt_discount_starttime < ldt_date && ldt_discount_endtime > ldt_date)
            //        {
            //            ldec_discount_price = Math.Round(ldc_amount * ldc_price * ldec_discount_ratio);
            //            ls_discount_demo = "取价表比例: " + ldec_discount_ratio.ToString() + " ||折扣开始时间: " + ldt_discount_starttime.ToString() + " ||折扣结束时间: " + ldt_discount_endtime.ToString() +
            //                                        " ||维护人: " + ls_discount_operator + " ||维护时间:" + tdiscount_datetime.ToString() +
            //                                        " ||数据录入人: " + ls_operator_code + " ||数据录入时间: " + ldt_date.ToString();
            //        }
            //        else
            //        {
            //            ldec_discount_price = 0;
            //            ls_discount_demo = "取价表比例: " + ldec_discount_ratio.ToString() + " ||折扣开始时间: " + ldt_discount_starttime.ToString() + " ||折扣结束时间: " + ldt_discount_endtime.ToString() +
            //                                        " ||维护人: " + ls_discount_operator + " ||维护时间:" + tdiscount_datetime.ToString() +
            //                                        " ||数据录入人: " + ls_operator_code + " ||数据录入时间: " + ldt_date.ToString();
            //        }
            //    }
            //    else
            //    {
            //        ldec_discount_ratio = 0;
            //        tdiscount_starttime = null;
            //        tdiscount_endtime = null;
            //        ls_discount_flag = null;
            //        ls_discount_operator = null;
            //        tdiscount_datetime = null;
            //        string tdiscountscheme = "select  discount_ratio , discount_flag , discount_starttime , discount_endtime,DISCOUNT_OPERATOR,DISCOUNT_DATETIME ";
            //        tdiscountscheme += " from comm.discount_scheme  ";
            //        tdiscountscheme += " WHERE discount_name = '" + ls_discount_name + "' AND  rownum = 1 ";
            //        DataTable dtdz2 = dzspc.GetList(tdiscountscheme).Tables[0];
            //        tdiscount_ratio = dtdz2.Rows[0]["DISCOUNT_RATIO"].ToString();
            //        if (string.IsNullOrEmpty(tdiscount_ratio))
            //        { ldec_discount_ratio = 0; }
            //        else
            //        { ldec_discount_ratio = Convert.ToDecimal(tdiscount_ratio); }

            //        ls_discount_flag = dtdz2.Rows[0]["DISCOUNT_FLAG"].ToString();

            //        tdiscount_starttime = dtdz2.Rows[0]["DISCOUNT_STARTTIME"].ToString();
            //        if (string.IsNullOrEmpty(tdiscount_starttime))
            //            tdiscount_starttime = "2011-01-01 00:00:00";
            //        tdiscount_endtime = dtdz2.Rows[0]["DISCOUNT_ENDTIME"].ToString();
            //        if (string.IsNullOrEmpty(tdiscount_endtime))
            //            tdiscount_endtime = "2999-01-01 00:00:00";
            //        ldt_discount_starttime = Convert.ToDateTime(tdiscount_starttime);
            //        ldt_discount_endtime = Convert.ToDateTime(tdiscount_endtime);
            //        ls_discount_operator = dtdz2.Rows[0]["DISCOUNT_OPERATOR"].ToString();
            //        tdiscount_datetime = dtdz2.Rows[0]["DISCOUNT_DATETIME"].ToString();


            //        if (ls_discount_flag.Equals("1") && ldt_discount_starttime < ldt_date && ldt_discount_endtime > ldt_date)
            //        {
            //            ldec_discount_price =Math.Round(ldc_amount * ldc_price * ldec_discount_ratio,2);
            //            ls_discount_demo = "取打折方案: " + ls_discount_name.ToString() + "-" + ldec_discount_ratio.ToString() + " ||方案标识 " + ls_discount_flag +
            //                                       " ||方案开始时间: " + ldt_discount_starttime.ToString() + " ||方案结束时间: " + ldt_discount_endtime.ToString() +
            //                                       " ||方案人: " + ls_discount_operator + " ||方案时间: " + tdiscount_datetime +
            //                                       " ||数据录入人: " + ls_operator_code + " ||数据录入时间: " + ldt_date.ToString();
            //        }
            //        else
            //        {
            //            ldec_discount_price = 0;
            //            ls_discount_demo = "取打折方案: " + ls_discount_name.ToString() + "-" + ldec_discount_ratio.ToString() + " ||方案标识 " + ls_discount_flag +
            //                                       " ||方案开始时间: " + ldt_discount_starttime.ToString() + " ||方案结束时间: " + ldt_discount_endtime.ToString() +
            //                                       " ||方案人: " + ls_discount_operator + " ||方案时间: " + tdiscount_datetime +
            //                                       " ||数据录入人: " + ls_operator_code + " ||数据录入时间: " + ldt_date.ToString();
            //        }

            //    }
            //}
            //else //aa 打折方案不可用 
            //{ 
            //     ldec_discount_price = 0;
            //     ls_discount_demo = "价表打折方案标识不可用，不能打折" + " ||程序插入时间: " + ldt_date.ToString();
            //}
            //暂时不打折  2016-10-08 by 梁吉
            ldec_discount_price = 0;
            ls_discount_demo = "本院要求不打折";

            return 0;
        }
        #region CA验证
        /// <summary>
        /// CA验证（工作站）
        /// </summary>
        /// <param name="user_id">用户ID</param>
        /// <param name="user_pwd">用户密码</param>
        /// <param name="msg">返回消息</param>
        /// <returns></returns>
        //public static bool GetCAAuthorization(String user_id, String user_pwd, string appcode, ref String msg, Tj_CA.CaBusiness cab = null)
        //{
        //    string caused = PlatCommon.SysBase.SystemParm.GetParameterValue("CA_USED", appcode, "*", "*",  SystemParm.HisUnitCode);
        //    string ca_enabled = "";
        //    if ("0".Equals(caused))
        //    {
        //        ca_enabled = PlatCommon.SysBase.SystemParm.GetParaValue("BJCA_ENABLED", appcode, "*", "*", "");
        //    }
        //    if ("1".Equals(caused))
        //    {
        //        ca_enabled = PlatCommon.SysBase.SystemParm.GetParaValue("LNCA_ENABLED", appcode, "*", "*", "");
        //    }
        //    if (!"1".Equals(ca_enabled))
        //    {
        //        msg = "对应ca没有使用启用！";
        //        return false;
        //    }

        //    //Tj_CA.CaBusiness cab = new Tj_CA.CaBusiness();
        //    if (cab == null) cab = new Tj_CA.CaBusiness();
        //    cab.CA_USED = caused;
        //    cab.Init();
        //    msg = cab.CAStationLogin(ref user_id, ref user_pwd);
        //    //ca登录登录成功
        //    if ("0".Equals(msg))
        //    {
        //        //if (cab.ReadKey(user_pwd) == 1)
        //        //{
        //        //    //保存证书信息

        //        //}
        //        //else
        //        //{
        //        //    msg = "读取证书失败";
        //        //    return false;
        //        //}

        //    }
        //    else
        //    {
        //        msg = "CA登录失败";
        //        return false;
        //    }
        //    // 获取图片信息
        //    byte[] pic_arr = cab.GetKeyPicByte(ref msg);
        //    if (pic_arr == null)
        //    {
        //        msg = "获取签名印章失败";
        //        return false;
        //    }
        //    Model.BJCA_INTERFACE bjca = new Model.BJCA_INTERFACE();
        //    Model.LNCA_INTERFACE lnca = new Model.LNCA_INTERFACE();
        //    string ca_id = null;
        //    string sql = "";// "SELECT NVL(T.CA_ID,'') CA_ID,T.ID FROM COMM.STAFF_DICT T WHERE T.CA_ID='" + cab.CASn + "'";

        //    DataSet ds;
        //    try
        //    {
        //        ////获取证书对应His用户
        //        //using (ServerPublicClient spc = new ServerPublicClient())
        //        //{
        //        //    ds = spc.GetDataBySql(sql);
        //        //    if (ds != null && ds.Tables[0].Rows.Count > 0)
        //        //    {
        //        //        u_id = ds.Tables[0].Rows[0]["ID"].ToString();
        //        //    }
        //        //    else
        //        //    {
        //        //        msg = "查无此证书[" + cab.CASn + "]用户";
        //        //        return false;
        //        //    }
        //        //}
        //        if ("0".Equals(caused))
        //        {
        //            //电子证书序列号
        //            bjca.BJCA_ID = cab.CACertSerialNO;
        //            //图片内容
        //            bjca.BJCA_IMG = pic_arr;
        //            //64位加密
        //            bjca.BJCA_IMG_STR = cab.GetKeyPicStr(ref msg);
        //            //his用户ID
        //            bjca.HIS_ID = user_id;
        //            //证书内容
        //            bjca.BJCA_INFO = cab.CAClientCert;
        //            //取出来的用户证书唯一标示
        //            bjca.CA_ID = cab.CASn;
        //        }
        //        else if ("1".Equals(caused))
        //        {
        //            //电子证书序列号
        //            lnca.LNCA_ID = cab.CACertSerialNO;
        //            //图片内容
        //            lnca.CA_IMG = pic_arr;
        //            //64位加密
        //            lnca.CA_IMG_STR = cab.GetKeyPicStr(ref msg);
        //            //his用户ID
        //            lnca.HIS_ID = user_id;
        //            //证书内容
        //            lnca.LNCA_INFO = cab.CAClientCert;
        //            //取出来的用户证书唯一标示
        //            lnca.CA_SN = cab.CASn;
        //        }
        //        //判断证书唯一码
        //        using (ServerPublicClient spc = new ServerPublicClient())
        //        {
        //            //sql = "SELECT NVL(CA_SN,'') CA_ID FROM hisinterface.lnca_interface WHERE ID = '" + user_id + "'";
        //            if ("0".Equals(caused))
        //            {
        //                //sql = "SELECT NVL(CA_ID,'') CA_ID,NVL(HIS_ID,'') HIS_ID FROM comm.bjca_interface WHERE BJCA_ID = UPPER('" + cab.CACertSerialNO + "')";
        //                sql = "SELECT NVL(CA_ID,'') CA_ID,NVL(HIS_ID,'') HIS_ID FROM comm.bjca_interface WHERE HIS_ID = UPPER('" + user_id + "')";
        //            }
        //            else if ("1".Equals(caused))
        //            {
        //                sql = "SELECT NVL(CA_SN,'') CA_ID,NVL(HIS_ID,'') HIS_ID  FROM hisinterface.lnca_interface WHERE HIS_ID = UPPER('" + user_id + "')";
        //            }
        //            ds = spc.GetDataBySql(sql);
        //            if (ds != null && ds.Tables[0].Rows.Count > 0)
        //            {
        //                ca_id = ds.Tables[0].Rows[0]["CA_ID"] == DBNull.Value ? "" : ds.Tables[0].Rows[0]["CA_ID"].ToString();
        //                if (string.IsNullOrEmpty(ca_id))
        //                {
        //                    msg = "此用户还没有证书唯一码，请先维护";
        //                    return false;
        //                }
        //                else if (!ca_id.Equals(cab.CASn))
        //                {
        //                    msg = "证书唯一码不匹配";
        //                    return false;
        //                }
        //                else if (!user_id.Equals(ds.Tables[0].Rows[0]["HIS_ID"].ToString()))
        //                {
        //                    msg = "证书用户与登录用户不匹配";
        //                    return false;
        //                }
        //            }
        //            else
        //            {
        //                msg = "此证书用户[" + user_id + "]未在HIS注册！";
        //                return false;//测试注释
        //            }
        //        }
        //        if (string.IsNullOrEmpty(cab.CASn))
        //        {
        //            msg = "证书序列号不能为空";
        //            return false;
        //        }
        //        ////更新证书信息到数据库
        //        //using (BJCA_INTERFACEClient bj = new BJCA_INTERFACEClient())
        //        //{
        //        //    if (bj.Exists_BJCA_INTERFACE(cab.CACertSerialNO))
        //        //    {
        //        //        bj.Update_BJCA_INTERFACE(bjca);
        //        //    }
        //        //    else
        //        //    {
        //        //        bj.Add_BJCA_INTERFACE(bjca);
        //        //    }
        //        //}
        //    }
        //    catch
        //    {
        //        msg = "验证证书出错！";
        //        return false;
        //    }
        //    msg = "";
        //    return true;
        //}
        /// <summary>
        /// 
        /// </summary>
        /// <param name="user_id"></param>
        /// <param name="user_pwd"></param>
        /// <param name="appcode"></param>
        /// <param name="msg"></param>
        /// <param name="ca_pin">返回key密码</param>
        /// <param name="cab"></param>
        /// <returns></returns>
        //public static bool GetCAAuthorization(String user_id, String user_pwd, string appcode, string his_unitcode, ref String msg, ref string ca_pin, Tj_CA.CaBusiness cab = null)
        //{
        //    string caused = PlatCommon.SysBase.SystemParm.GetParaValue("CA_USED", appcode, "*", "*", "0", his_unitcode);
        //    string ca_enabled = "";
        //    if ("0".Equals(caused))
        //    {
        //        ca_enabled = PlatCommon.SysBase.SystemParm.GetParaValue("BJCA_ENABLED", appcode, "*", "*", "", his_unitcode);
        //    }
        //    else if ("1".Equals(caused))
        //    {
        //        ca_enabled = PlatCommon.SysBase.SystemParm.GetParaValue("LNCA_ENABLED", appcode, "*", "*", "", his_unitcode);
        //    }
        //    if (!"1".Equals(ca_enabled))
        //    {
        //        msg = "对应ca没有使用启用！";
        //        return false;
        //    }

        //    //Tj_CA.CaBusiness cab = new Tj_CA.CaBusiness();
        //    if (cab == null) cab = new Tj_CA.CaBusiness();
        //    cab.CA_USED = caused;
        //    if (cab.Init() != 1)
        //    {
        //        msg = "初始化CA失败！";
        //        return false;
        //    }
        //    if (cab.ReadKey() != 1)
        //    {
        //        msg = "读取CA失败！";
        //        return false;
        //    }

        //    // 获取图片信息
        //    byte[] pic_arr = cab.GetKeyPicByte(ref msg);
        //    if (pic_arr == null)
        //    {
        //        msg = "获取签名印章失败";
        //        return false;
        //    }
        //    try
        //    {
        //        string sql = "";
        //        DataSet ds = null;
        //        string ca_id = string.Empty;
        //        //判断证书唯一码
        //        using (ServerPublicClient spc = new ServerPublicClient())
        //        {
        //            //sql = "SELECT NVL(CA_SN,'') CA_ID FROM hisinterface.lnca_interface WHERE ID = '" + user_id + "'";
        //            if ("0".Equals(caused))
        //            {
        //                //sql = "SELECT NVL(CA_ID,'') CA_ID,NVL(HIS_ID,'') HIS_ID FROM comm.bjca_interface WHERE BJCA_ID = UPPER('" + cab.CACertSerialNO + "')";
        //                sql = "SELECT NVL(CA_ID,'') CA_ID,NVL(HIS_ID,'') HIS_ID FROM comm.bjca_interface WHERE HIS_ID = UPPER('" + user_id + "')";
        //            }
        //            else if ("1".Equals(caused))
        //            {
        //                sql = "SELECT NVL(CA_SN,'') CA_ID,NVL(HIS_ID,'') HIS_ID  FROM hisinterface.lnca_interface WHERE UPPER(CA_SN) = UPPER('" + cab.CASn + "')";
        //            }
        //            ds = spc.GetDataBySql(sql);
        //            if (ds != null && ds.Tables[0].Rows.Count > 0)
        //            {
        //                ca_id = ds.Tables[0].Rows[0]["CA_ID"] == DBNull.Value ? "" : ds.Tables[0].Rows[0]["CA_ID"].ToString();
        //                if (string.IsNullOrEmpty(ca_id))
        //                {
        //                    msg = "此用户还没有证书唯一码，请先维护";
        //                    return false;
        //                }
        //                else if (!ca_id.ToUpper().Equals(cab.CASn.ToUpper()))
        //                {
        //                    msg = "证书唯一码不匹配";
        //                    return false;
        //                }
        //                else if (!user_id.Equals(ds.Tables[0].Rows[0]["HIS_ID"].ToString()))
        //                {
        //                    msg = "证书用户与登录用户不匹配";
        //                    return false;
        //                }
        //            }
        //            else
        //            {
        //                msg = "此证书用户[" + user_id + "]未在HIS注册！";
        //                return false;//测试注释
        //            }
        //        }
        //        if (string.IsNullOrEmpty(cab.CASn))
        //        {
        //            msg = "证书序列号不能为空";
        //            return false;
        //        }

        //    }
        //    catch (Exception ex)
        //    {
        //        msg = "验证证书异常！" + ex.Message;
        //        return false;
        //    }
        //    msg = "";
        //    return true;
        //}
        ///// <summary>
        /// 验证CA的时间戳值
        /// </summary>
        /// <param name="SignTimeStampValue">经base64编码的时间戳</param>
        /// <returns></returns>
        //public static bool VerifyTimeSpam(String SignTimeStampValue)
        //{
        //    Tj_CA.CaBusiness cab = new Tj_CA.CaBusiness();
        //    //CA登录

        //    if (cab.Init() != 1)
        //    {
        //        return false;
        //    }
        //    if (cab.ReadKey() != 1)
        //    {
        //        return false;
        //    }
        //    if (cab.VerifyTimeStamp(SignTimeStampValue) == 0)
        //    {
        //        return true;
        //    }
        //    else
        //        return false;
        //}

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="downLoadUrl">文件的url路径</param>
        /// <param name="saveFullName">需要保存在本地的路径(包含文件名)</param>
        /// <returns></returns>
        public static bool DownloadFile(string downLoadUrl, string saveFullName)
        {
            bool flagDown = false;
            HttpWebRequest httpWebRequest = null;
            try
            {
                //根据url获取远程文件流
                httpWebRequest = (HttpWebRequest)HttpWebRequest.Create(downLoadUrl);

                HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
                Stream sr = httpWebResponse.GetResponseStream();

                //创建本地文件写入流
                Stream sw = new FileStream(saveFullName, FileMode.Create);

                long totalDownloadedByte = 0;
                byte[] by = new byte[1024];
                int osize = sr.Read(by, 0, (int)by.Length);
                while (osize > 0)
                {
                    totalDownloadedByte = osize + totalDownloadedByte;
                    sw.Write(by, 0, osize);
                    osize = sr.Read(by, 0, (int)by.Length);
                }
                Thread.Sleep(100);
                flagDown = true;
                sw.Close();
                sr.Close();
            }
            catch (Exception)
            {
                if (httpWebRequest != null)
                    httpWebRequest.Abort();
            }
            return flagDown;
        }


        #endregion
        #region 医嘱处理
        /// <summary>
        /// 东北国际医嘱状态显示
        /// </summary>
        /// <param name="statusCode"></param>
        /// <param name="blnLongTerm"></param>
        /// <param name="blnForeColor"></param>
        /// <returns></returns>
        public static Color GetOrderStatusColor_NEINTER(string statusCode, bool blnLongTerm, bool blnForeColor)
        {
            // 开医嘱录入日期: ENTER_DATE_TIME
            // 作废医嘱时间:   CANCEL_DATE_TIME
            // 停医嘱录入日期: STOP_ORDER_DATE_TIME
            #region old
            //switch (statusCode)
            //{
            //    case "0":   //	 驳回 状态     (暗金字/白底)      
            //        return (blnForeColor ? Color.DarkOrange : Color.White);
            //    case "1":   //	 新开       (蓝字/白底)      
            //        return (blnForeColor ? Color.LightBlue : Color.White);
            //    case "5":   //	医生保存        (黑字/白底)                    
            //        return (blnForeColor ? Color.Blue : Color.White);
            //    case "6":   //	新开提交        (黑字/白底)                    
            //        return (blnForeColor ? Color.Black : Color.White);
            //    case "7":   //	医生停止        (黑字/灰底)
            //        return (blnForeColor ? Color.Black : Color.LightGray);

            //    case "8":   //	医生作废        (红字/白底)
            //        return (blnForeColor ? Color.Red : Color.White);
            //    case "2":   //	执行            
            //        return (blnForeColor ? Color.Black : Color.White);

            //    case "3":   //	停止  黑字/灰底)
            //        return (blnForeColor ? Color.Black : Color.LightGray);

            //    case "4":   //	作废
            //        //  红/灰
            //        return (blnForeColor ? Color.Red : Color.LightGray);
            //    default:
            //        //    throw new Exception("不支持的医嘱状态!");
            //        return (blnForeColor ? Color.LightGray : Color.LightGray);
            //}
            #endregion
            switch (statusCode)
            {
                case "0":   //	 驳回 状态
                    return ((blnForeColor) ? Color.Black : Color.Yellow);
                //if (blnLongTerm)
                //{
                //    return ((blnForeColor) ? Color.Blue : Color.White);
                //}
                //else
                //{
                //    return ((blnForeColor) ? Color.Black : Color.White);
                //}
                case "1":   //	 新开       (蓝字/白底)      
                    if (blnLongTerm)
                    {
                        return ((blnForeColor) ? Color.Blue : Color.White);
                    }
                    else
                    {
                        return ((blnForeColor) ? Color.Black : Color.White);
                    }
                case "2":   //	执行            
                    if (blnLongTerm)
                    {
                        return ((blnForeColor) ? Color.Blue : Color.White);
                    }
                    else
                    {
                        return ((blnForeColor) ? Color.Black : Color.White);
                    }

                case "3":   //	停止  (蓝字/白底)
                    if (blnLongTerm)
                    {
                        return ((blnForeColor) ? Color.DeepPink : Color.White);
                    }
                    else
                    {
                        return ((blnForeColor) ? Color.Black : Color.White);
                    }

                case "4":   //	作废
                    //  红/灰
                    return (blnForeColor ? Color.Red : Color.White);
                case "5":   //	医生保存        (黑字/灰底)                    
                    //if (blnLongTerm)
                    //{
                    //    return ((blnForeColor) ? Color.Blue : Color.White);
                    //}
                    //else
                    //{
                    //    return ((blnForeColor) ? Color.Black : Color.White);
                    //}
                    return ((blnForeColor) ? Color.Black : Color.LightGray);
                case "6":   //	新开提交        (蓝字/灰底)                    
                    //if (blnLongTerm)
                    //{
                    //    return ((blnForeColor) ? Color.Blue : Color.White);
                    //}
                    //else
                    //{
                    //    return ((blnForeColor) ? Color.Black : Color.White);
                    //}
                    return ((blnForeColor) ? Color.Blue : Color.LightGray);
                case "7":   //	医生停止        (蓝字/白底)
                    if (blnLongTerm)
                    {
                        return ((blnForeColor) ? Color.DeepPink : Color.White);
                    }
                    else
                    {
                        return ((blnForeColor) ? Color.Black : Color.White);
                    }

                case "8":   //	医生作废        (红字/白底)
                    return (blnForeColor ? Color.Red : Color.White);

                default:
                    //    throw new Exception("不支持的医嘱状态!");
                    return (blnForeColor ? Color.LightGray : Color.LightGray);
            }
        }
        /// <summary>
        /// 获取医嘱状态颜色
        /// </summary>
        /// <returns></returns>
        public static Color GetOrderStatusColor(string statusCode, bool blnLongTerm, bool blnForeColor)
        {
            // 开医嘱录入日期: ENTER_DATE_TIME
            // 作废医嘱时间:   CANCEL_DATE_TIME
            // 停医嘱录入日期: STOP_ORDER_DATE_TIME
            #region
            //switch (statusCode)
            //{
            //    case "0":   //	 返回 状态     (暗金字/白底)      
            //        if (blnLongTerm == false)       // 临时： (黑字/白底)
            //        {
            //            return (blnForeColor ? Color.Black : Color.White);
            //        }
            //        else                            // 长期： (暗金字/白)
            //        {
            //            return (blnForeColor ? Color.DarkOrange : Color.White);
            //        }
            //    case "1":   //	 新开       (绿字/白底)      
            //        if (blnLongTerm == false)       // 临时： (黑字/白底)
            //        {
            //            return (blnForeColor ? Color.Black : Color.White);
            //        }
            //        else                            // 长期： (蓝/白)
            //        {
            //            return (blnForeColor ? Color.Blue : Color.White);
            //        }
            //    case "5":   //	医生保存        (蓝字/白底)                    
            //        return (blnForeColor ? Color.Blue : Color.White);

            //    case "6":   //	新开提交
            //                // 如果是 新开医嘱的提交状态

            //        if (dtOrderCancel.Equals(DateTime.MinValue)
            //            && dtOrderStop.Equals(DateTime.MinValue))
            //        {
            //            if (blnLongTerm == false)       // 临时： (黑字/白底)
            //            {
            //                return (blnForeColor ? Color.Black : Color.White);
            //            }
            //            else                            // 长期： (蓝/白)
            //            {
            //                return (blnForeColor ? Color.Blue : Color.White);
            //            }
            //        }

            //        // 作废医嘱的提交状态        
            //        else if (dtOrderStop.Equals(DateTime.MinValue))
            //        {
            //            // 临时:  (红/白)   长期:   (红/白)
            //            return (blnForeColor ? Color.Red : Color.White);
            //        }

            //        // 停止医嘱的提交状态        
            //        else if (dtOrderCancel.Equals(DateTime.MinValue))
            //        {
            //            if (blnLongTerm == false)       // 临时： (黑/白)
            //            {
            //                return (blnForeColor ? Color.Black : Color.LightGray);
            //            }
            //            else                            // 长期: (蓝/白)
            //            {
            //                return (blnForeColor ? Color.Blue : Color.White);
            //            }
            //        }
            //        else
            //        {
            //            if (blnLongTerm)
            //            {
            //                throw new Exception("长期医嘱同时具[开医嘱时间]、[作废医嘱时间]、[停医嘱日期]，是不正确的!");
            //            }
            //            else
            //            {
            //                return (blnForeColor ? Color.Black : Color.LightGray);
            //            }
            //        }

            //    case "7":   //	医生停止        (黑字/灰底)
            //        return (blnForeColor ? Color.Black : Color.LightGray);

            //    case "8":   //	医生作废        (红字/灰底)
            //        return (blnForeColor ? Color.Red : Color.LightGray);

            //    case "2":   //	执行            
            //        if (blnLongTerm == false)       // 临: (黑/灰)   
            //        {
            //            return (blnForeColor ? Color.Black : Color.LightGray);
            //        }
            //        else                           // 长:  (蓝/灰)
            //        {
            //            return (blnForeColor ? Color.Blue : Color.LightGray);
            //        }

            //    case "3":   //	停止
            //        if (blnLongTerm == false)       // 临: 黑/灰
            //        {
            //            return (blnForeColor ? Color.Black : Color.LightGray);
            //        }
            //        else                           // 长: 蓝/灰
            //        {
            //            return (blnForeColor ? Color.Blue : Color.LightGray);
            //        }

            //    case "4":   //	作废
            //        // 临： 红/灰  长: 红/灰
            //        return (blnForeColor ? Color.Red : Color.LightGray);

            //    default:
            //        //    throw new Exception("不支持的医嘱状态!");
            //        return (blnForeColor ? Color.LightGray : Color.LightGray);
            //}
            #endregion

            #region 
            switch (statusCode)
            {
                case "0":   //	 驳回 状态     (暗金字/白底)      
                    return (blnForeColor ? Color.DarkOrange : Color.White);
                case "1":   //	 新开       (蓝字/白底)      
                    return (blnForeColor ? Color.Blue : Color.White);
                case "5":   //	医生保存        (黑字/白底)                    
                    return (blnForeColor ? Color.Blue : Color.White);
                case "6":   //	新开提交        (黑字/白底)                    
                    return (blnForeColor ? Color.Black : Color.White);
                case "7":   //	医生停止        (粉字/灰底)
                    return (blnForeColor ? Color.DeepPink : Color.White);

                case "8":   //	医生作废        (红字/白底)
                    return (blnForeColor ? Color.Red : Color.White);
                case "2":   //	执行            
                    return (blnForeColor ? Color.Black : Color.LightGray);

                case "3":   //	停止  粉字/灰底)
                    return (blnForeColor ? Color.DeepPink : Color.LightGray);

                case "4":   //	作废
                    //  红/灰
                    return (blnForeColor ? Color.Red : Color.LightGray);
                default:
                    //    throw new Exception("不支持的医嘱状态!");
                    return (blnForeColor ? Color.LightGray : Color.LightGray);
            }
            #endregion
        }
        #endregion
        #region 公共函数
        #region 取本机ip
        public static string GetLoacalIP()
        {
            IPAddress[] addressList = Dns.GetHostAddresses(Dns.GetHostName());
            IEnumerable<IPAddress> ips = addressList.Where(x => x.AddressFamily == AddressFamily.InterNetwork);
            try
            {
                if (ips.First<IPAddress>() != null)
                {
                    return ips.First<IPAddress>().ToString();
                }
                else
                {
                    return "";
                }
            }
            catch
            {
                return "";
            }

        }
        #endregion
        #region 获得本机电脑名称
        /// <summary>
        /// 获得本机电脑名称
        /// </summary>
        /// <returns></returns>
        public static string GetLocalHostName()
        {
            return Dns.GetHostName();
        }
        #endregion
        #region 获取本机Mac地址
        /// <summary>
        /// 获取本机Mac地址
        /// </summary>
        /// <returns></returns>
        public static string GetLocalMac()
        {
            var interfaces = NetworkInterface.GetAllNetworkInterfaces();
            foreach (var @interface in interfaces)
            {
                var up = @interface.OperationalStatus == OperationalStatus.Up;
                var loopback = @interface.NetworkInterfaceType == NetworkInterfaceType.Loopback;

                if (up && !loopback)
                {
                    var address = @interface.GetPhysicalAddress().ToString();

                    // insert ":" then remove the last ":"
                    var result = System.Text.RegularExpressions.Regex.Replace(address, ".{2}", "$0:");
                    var mac = result.Remove(result.Length - 1);

                    return mac.ToString();
                }
            }
            return "";
        }
        #endregion
        /// <summary>
        /// 取药品限制标志
        /// </summary>
        /// <param name="drug_code">处方代码</param>
        /// <param name="drug_spec">处方规格</param>
        /// <param name="drug_indicator">分类</param>
        /// <returns>限制标志 空 1，2，3</returns>
        public static string GetDrugLimitClass(string drug_code, string drug_spec, string drug_indicator)
        {
            string ret = "-1";
            string sqlstr = " Select LIMIT_CLASS  From drug_dict Where drug_code ='" + drug_code + "' and drug_spec ='" + drug_spec + "' And rownum = 1";
            using (ServerPublicClient client = new ServerPublicClient())
            {
                DataSet dst = client.GetDataBySql(sqlstr);
                if (dst != null && dst.Tables[0].Rows.Count > 0)
                {
                    ret = dst.Tables[0].Rows[0][0].ToString();
                }

            }
            return ret;
        }
        /// <summary>
        ///  //判断是否在路径里 by lions
        /// </summary>
        /// <param name="pat_id"></param>
        /// <param name="visit_id"></param>
        /// <param name="status">路径状态 0 在路径里 1正常结束路径 2退出路径</param>
        /// <returns></returns>
        public static bool IsInClinicPath(string pat_id, string visit_id, int status)
        {
            bool result = false;
            try
            {
                string sql = string.Format(@" select *
                                      from cp_pat_master pm
                                     where pm.patient_id = '{0}'
                                       and pm.visit_id = '{1}'
                                        and status = {2}", pat_id, visit_id, status);
                using (ServerPublicClient client = new ServerPublicClient())
                {
                    DataTable dt = new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
                    if (dt != null && dt.Rows.Count > 0) result = true;
                }

            }
            catch { }
            return result;
        }
        /// <summary>
        /// 获取用户权限
        /// </summary>
        /// <param name="appcode">应用模板编码</param>
        /// <param name="dbuser">用户名</param>
        /// <returns></returns>
        public static string GetCAPABILITY(string appcode, string dbuser)
        {
            string ret = "";
            string sqlstr = @" Select APP_GRANTS.CAPABILITY    FROM APP_GRANTS, USERS  WHERE ( APP_GRANTS.USER_ID = USERS.USER_ID ) and ";
            sqlstr += "( UPPER(RTRIM(APP_GRANTS.APPLICATION))= '" + appcode + "' ) and  ";
            sqlstr += "( UPPER(RTRIM(USERS.DB_USER)) ='" + dbuser + "' )";

            using (ServerPublicClient client = new ServerPublicClient())
            {
                DataSet dst = client.GetDataBySql(sqlstr);
                if (dst != null && dst.Tables[0].Rows.Count > 0)
                {
                    ret = dst.Tables[0].Rows[0][0].ToString();
                }

            }
            return ret;
        }

        /// <summary>
        /// 取输入法数据 by lions 2018-12-16
        /// </summary>
        /// <param name="GS_INPUTSETING">输入法类型</param>
        /// <param name="GS_STORAGES">药房编码</param>
        /// <param name="GS_CONDITIONS">可能的条件</param>
        /// <returns></returns>
        public static DataTable GetInputSetting(string GS_INPUTSETING, string GS_STORAGES, string GS_INPUT_TYPE, ref DataTable inpuStruct, string GS_CONDITIONS = "")
        {
            string ls_cols;    //显示列串
            string ls_col_name, name_col = "";   //列名,名称列
            string ls_col_title;  //列标题;
            string ls_table_name, ls_tmp, ls_inputname = "";
            string ls_sql = "";
            NM_Service.NMService.ServerPublicClient service = new NM_Service.NMService.ServerPublicClient();
            List<string> paras = new List<string>();
            ArrayList values = new ArrayList();
            #region 1、先查出输入发设置的表和字段
            string sql = "select * from comm.input_setting where dict_type=:GS_INPUTSETING order by show_sort";
            paras.Add("GS_INPUTSETING");
            values.Add(GS_INPUTSETING);
            DataSet ds = service.GetDataTable_Para(sql, paras, values);
            if (ds == null || ds.Tables[0].Rows.Count == 0) return null;
            inpuStruct = ds.Tables[0].Copy();
            #endregion
            #region 2、查出表里对应的字段的值
            ls_table_name = ds.Tables[0].Rows[0]["DATA_TABLE"].ToString();
            ls_cols = "";
            foreach (DataRow dr in ds.Tables[0].Rows)
            {
                //取列名
                ls_col_name = dr["DATA_COL"].ToString();
                //所有的列都列出来
                if (string.IsNullOrEmpty(ls_col_name))
                {
                    ls_cols += ls_col_name;
                }
                else
                {
                    ls_cols += "," + ls_col_name;
                }
                //取输入类型
                ls_tmp = dr["INPUT_CODE"].ToString();
                if (ls_tmp.Equals(GS_INPUT_TYPE))
                {
                    ls_inputname = ls_col_name;
                }
                //是否名称列
                ls_tmp = dr["FLAG_ISNAME"].ToString();
                if ("Y".Equals(ls_tmp))
                {
                    name_col = ls_col_name;
                }
                ls_col_title = dr["DATA_TITLE"].ToString();
            }

            if (string.IsNullOrEmpty(ls_cols))
            {
                return null;
            }
            if (string.IsNullOrEmpty(name_col))
            {
                return null;
            }
            ls_cols = ls_cols.Remove(0, 1);
            ls_sql = "select " + ls_cols + " from " + ls_table_name;
            //药房条件
            if (!string.IsNullOrEmpty(GS_STORAGES))
            {
                ls_sql += " where storage in (" + GS_STORAGES + ")";
            }

            //其他条件
            if (!string.IsNullOrEmpty(GS_CONDITIONS))
            {
                if (ls_sql.IndexOf("where") > 0)
                {
                    ls_sql = ls_sql + " and " + GS_CONDITIONS;
                }
                else
                {
                    ls_sql += " where  " + GS_CONDITIONS;
                }

            }
            //排序
            ls_sql += " order by " + ls_inputname + " asc";
            //查询
            ds = service.GetDataBySql(ls_sql);
            if (ds == null || ds.Tables[0].Rows.Count == 0)
            {
                return null;
            }
            return ds.Tables[0];
            #endregion
        }
        /// <summary>
        /// 从动态库中获取窗体
        /// </summary>
        /// <param name="dllName">动态库的名称</param>
        /// <param name="typeName">类名</param>
        /// <returns>窗体类</returns>
        public static ParentForm GetFormInDll(string dllName, string typeName, string[] paras = null)
        {
            try
            {
                Environment.CurrentDirectory = Application.StartupPath; //g 116
                Assembly asmAssembly = Assembly.LoadFrom(dllName);
                Type typeToLoad = asmAssembly.GetType(typeName);
                object GenericInstance;
                //if (paras == null)
                //{
                //    GenericInstance = Activator.CreateInstance(typeToLoad);
                //}
                //else
                //{
                GenericInstance = Activator.CreateInstance(typeToLoad, paras);
                //}
                ParentForm formToLoad;// = new ParentForm();
                formToLoad = (ParentForm)(GenericInstance);

                //Assembly assembly = Assembly.GetExecutingAssembly();
                //// 实例化窗体
                //ParentForm formToLoad = assembly.CreateInstance(typeName) as ParentForm;

                return formToLoad;
            }
            catch (Exception ex)
            {
                string msg = "DLL名称: " + dllName + " 类名:" + typeName + " 没被发现! ";
                throw new Exception(msg + ex.Message);
            }
        }
        #endregion
        #region 初始化参数
        /// <summary>
        /// 判断参数是否存在，不存在则新增
        /// </summary>
        /// <param name="appCode">程序代码</param>
        /// <param name="paraName">参数名称</param>
        /// <param name="deptCode">科室代码</param>
        /// <param name="empNO">员工编码</param>
        /// <param name="defaltValue">默认值</param>
        /// <param name="paramScope">取值范围</param>
        /// <param name="explanation">说明</param>
        /// <param name="hisUnitCode">医院编码</param>
        /// <returns>成功是返回true否则false</returns>
        public static bool ExistsAndInsertPara(string appCode, string paraName, string deptCode, string empNO, string defaltValue, string paramScope, string explanation, string hisUnitCode)
        {
            //if (paraName.Equals("DOCUMENT_NO_LENGTH_DEFAULT"))
            //    MessageBox.Show("");
            string ls_sql = "select * from app_configer_baseinfo ";
            ls_sql += " where upper(app_name) = :as_appname  and upper(parameter_name) = upper(:as_param_name)";

            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("as_appname");
            para.Add("as_param_name");

            para_val.Add(appCode);
            para_val.Add(paraName);

            DataTable dtPara = spc.GetDataTable_Para(ls_sql, para, para_val).Tables[0];
            int liMaxParaNo;
            Dictionary<string, string> idc = new Dictionary<string, string>();
            if (dtPara.Rows.Count < 1)
            {
                ls_sql = "select nvl(max(parameter_no),0) from app_configer_baseinfo ";
                ls_sql += " where upper(app_name) = upper(:as_appname) ";
                para.Clear();
                para_val.Clear();
                para.Add("as_appname");

                para_val.Add(appCode);

                dtPara = spc.GetDataTable_Para(ls_sql, para, para_val).Tables[0];
                if (dtPara.Rows.Count < 1)
                    liMaxParaNo = 0;
                else
                    liMaxParaNo = int.Parse(dtPara.Rows[0][0].ToString());
                liMaxParaNo++;
                ls_sql = "insert into app_configer_baseinfo(app_name,parameter_no,parameter_name,";
                ls_sql += "parainit_value,parameter_scope,explanation,his_unit_code) values (";
                ls_sql += "'" + appCode + "'," + liMaxParaNo + ",'" + paraName + "','" + defaltValue + "',";
                ls_sql += "'" + paramScope + "','" + explanation + "','" + hisUnitCode + "')";
                idc.Add(ls_sql, "在app_configer_baseinfo表中插入参数" + paraName + "出错!");

            }

            ls_sql = "select * from app_configer_parameter ";
            ls_sql += " where upper(app_name) = :as_appname and upper(parameter_name) = :as_param_name ";
            ls_sql += " and dept_code = :as_storage_code and emp_no=:as_emp_no and his_unit_code=:gs_unitcode";
            para.Clear();
            para_val.Clear();
            para.Add("as_appname");
            para.Add("as_param_name");
            para.Add("as_storage_code");
            para.Add("as_emp_no");
            para.Add("gs_unitcode");
            para_val.Add(appCode);
            para_val.Add(paraName);
            para_val.Add(deptCode);
            para_val.Add(empNO);
            para_val.Add(hisUnitCode);
            dtPara = spc.GetDataTable_Para(ls_sql, para, para_val).Tables[0];
            if (dtPara.Rows.Count < 1)
            {
                ls_sql = "insert into app_configer_parameter(app_name,parameter_name,dept_code,emp_no,";
                ls_sql += "parameter_value,his_unit_code) values('" + appCode + "','" + paraName + "',";
                ls_sql += "'" + deptCode + "','" + empNO + "','" + defaltValue + "','" + hisUnitCode + "')";
                idc.Add(ls_sql, "在app_configer_parameter表中插入参数" + paraName + "科室为" + deptCode + "出错!");
            }
            if (idc.Count > 0)
            {
                string result = spc.SaveTable(idc);
                if (result.Length > 0)
                {
                    MessageBox.Show(result);
                    return false;
                }
            }
            return true;
        }

        public static bool InitParaForMudle(string deptCode, string asEmpNo, string hisUnitCode, string appName)
        {
            string paramName, defaltValue, empNo, paraScope, explanation;
            empNo = asEmpNo;
            switch (appName)
            {
                case "PHSTOCK":
                    #region 药库
                    paramName = "CLASS_ON_OUTP_RCPT";
                    defaltValue = "A";
                    empNo = "*";
                    paraScope = "";
                    explanation = "药品价格维护界面-【门诊收据类别编码】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "CLASS_ON_INP_RCPT";
                    defaltValue = "A";
                    empNo = "*";
                    paraScope = "";
                    explanation = "药品价格维护界面-【住院收据类别编码】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "CLASS_ON_RECKONING";
                    defaltValue = "H01";
                    empNo = "*";
                    paraScope = "";
                    explanation = "药品价格维护界面-【核算科目类别编码】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "SUBJ_CODE";
                    defaltValue = "H01";
                    empNo = "*";
                    paraScope = "";
                    explanation = "药品价格维护界面-【会计科目类别编码】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "CLASS_ON_MR";
                    defaltValue = "13";
                    empNo = "*";
                    paraScope = "";
                    explanation = "药品价格维护界面-【病案首页分类编码】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "MR_BILL_CLASS";
                    defaltValue = "13";
                    empNo = "*";
                    paraScope = "";
                    explanation = "药品价格维护界面-【对应的病案首页新首页分类编码】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "BASICPRICE";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "药品价格维护界面-【批发价与零售价那个是输入值，哪个是自动产生值，具体设置为，0-零售价=批发价*retailpriceratio,1-批发价=零售价/retailpriceratio】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "RETAILPRICERATIO";
                    defaltValue = "1.15";
                    empNo = "*";
                    paraScope = "";
                    explanation = "药品价格维护界面-【零售价与批发价格之间的比例关系。 另外，该参数的值还为药品调价记录维护中：新零售价=原零售价*retailpriceratio参数设置的值 】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "DRUGCLASSLEVEL";
                    defaltValue = "6";
                    empNo = "*";
                    paraScope = "";
                    explanation = "药品目录维护界面-【药品代码类别的过滤长度（正整数）  相关模块:药品目录维护】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "DRUGINDICATOR";
                    defaltValue = "1,2,3,5";
                    empNo = "*";
                    paraScope = "1,2,3,5,6,9";
                    explanation = "药品价格维护界面-【调价时可维护的药品种类,1-西药，2-中草药，3-中成药，5-辅料，6-试剂，9-其他 】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "DRUGPLANSUMDAYS";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "";
                    explanation = "采购计划界面-【采购计划数量 的统计出库天数（正整数）】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "IMPORT_BUYDRUGPLAN_LIMIT";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "采购入库界面-【处理入库是否受采购计划的限制，0-不受限制 ，1-受限制】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "STOCKMGR_IMPORT_FOREIGN";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "采购入库界面-【药库入库时的供货方是选择范围 ，1-供应商+厂商，0-供应商】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "SUPPLIERCLASS";
                    defaltValue = "生产商,供应商";
                    empNo = "*";
                    paraScope = "生产商,供应商";
                    explanation = "供货商字典选择界面-【生产商,供应商】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "DEFAULTSUPPLIER";
                    defaltValue = "京诺华";
                    empNo = "*";
                    paraScope = "";
                    explanation = "采购入库、采购退货界面-【默认供货方】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "DOCUMENT_NO_LENGTH_DEFAULT";
                    defaltValue = "10";
                    empNo = "*";
                    paraScope = "";
                    explanation = "所有产生单据号码的地方-【 入出库单据号默认长度,不足时补零,超过时产生则按实际长度(正整数),但最大长度受数据库设计限制】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "IOAUTOKEY";
                    defaltValue = "9";
                    empNo = "*";
                    paraScope = "9,12";
                    explanation = "所有出入库界面-【出入库需要输入药品编码时自动触发的按键，9指的是F9，12指的是F12】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "PERMITNEGATIVE";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "1,0";
                    explanation = "所有修改库存的地方-【是否允许库存为负，1-允许为负，0-不允许为负】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "DONATION";
                    defaltValue = "捐赠库";
                    empNo = "*";
                    paraScope = "";
                    explanation = "其他出库界面-【捐赠出库默认发往科室】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "WASTE";
                    defaltValue = "报损库";
                    empNo = "*";
                    paraScope = "";
                    explanation = "其他出库界面-【报损出库默认发往科室】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "APPLICATION_OPS_EXPORT_CLASS";
                    defaltValue = "手术出库";
                    empNo = "*";
                    paraScope = "发放出库,生产出库,手术出库";
                    explanation = "手术领药单领药界面-【手术领药单使用出库类型】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "DRUG_PROVIDE_APPLICATION_DELETE";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "申请出库界面-【药库是否可以删除药房的申请 ，0-否 1-是】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "EXPORT_APP_POSITIVE";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "申请出库，退药入库界面-【是否限制申请出库数量为正，0-不限制 1-限制】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "EXPORT_PRIORITY";
                    defaltValue = "expiredate";
                    empNo = "*";
                    paraScope = "expiredate,batchno";
                    explanation = "所有减库存界面-【出库优先顺序，expiredate-效期优先 batchno-批号的先进先出 】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "EXPORT_BYVALIDDATE";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "所有减库存界面-【严格按有效期出库 ，1-严格 0-不严格，严格的话，过了效期的药不能出库】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "HANDLECODELEVEL";
                    defaltValue = "20";
                    empNo = "*";
                    paraScope = "";
                    explanation = "所有查询界面-【出入库和查询统计搜寻的代码级范围(正整数),就是输入几位码就可把相关的药品显示出来】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "BACK_MEDICINE_METHOD";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "退药入库界面-【退药时决定，0-入库入负,1-出库出正】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "SHOWZEROSTOCK";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "所有显示库存信息界面-【是否显示零库存，0-否 ，1-是】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "PRINTDOCUMENT";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "所有出入库界面-【入库出库单据处理完毕是否打印入库出库单据，1-是，0-否】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "CONFIRM_LOGIN";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "所有的出入库界面-【出库保存后记帐前是否重新输入操作员口令，0-是，1-否】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "INVENTORYCHANGESTOCK";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "盘点界面-【盘点后是否生成未记帐出入库单，1-允许，0-不允许】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "IMPORT_AUTO";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1,2";
                    explanation = "所有的出库界面-【出库时接收方自动入库参数，0-无操作 ，1-自动产生入库记录 ，2-自动产生入库记录并自动记帐 】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "SAVEANDACCOUNTIMPORT";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "所有入库界面-【入库存是否保存单据同时作记帐处理，1-是,0-否 】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "SAVEANDACCOUNTEXPORT";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "所有出库界面-【出库存是否保存单据同时作记帐处理，1-是,0-否】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "IMPORT_DRUGINFOMAINT";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "所有入库界面-【入库处理界面是否链接药品价格维护窗口,入库时是否可以维护供应商，0-是，1-否】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "LIMIT_ALARM";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "登陆系统界面后-【库存是否报警，1-是, 0-否】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "EXPIRED_ALARM_DAYS";
                    defaltValue = "7";
                    empNo = "*";
                    paraScope = "";
                    explanation = "登陆系统界面后-【药品失效日期限度(整数)，正数-进行提示，负数-不提示】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "LOWRATIO";
                    defaltValue = "0.2";
                    empNo = "*";
                    paraScope = "";
                    explanation = "库存上线下维护界面-【库存量下限与消耗量的关系（正数）】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "UPPERRATIO";
                    defaltValue = "1.9";
                    empNo = "*";
                    paraScope = "";
                    explanation = "库存上下线维护界面-【库存量上限与消耗量的关系（正数）】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "DSBALANCE_DAY_LASTBALANCED";
                    defaltValue = "2006-11-20 10:19";
                    empNo = "*";
                    paraScope = "";
                    explanation = "月结界面-【药库上次月结时间，月结年月日时分秒】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "STOCKBALANCE";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "1,0";
                    explanation = "月结界面-【月结标志系统登陆时取本参数】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "PHARM_STOCK_BALANCE_END_DATE_DAYNO";
                    defaltValue = "28";
                    empNo = "*";
                    paraScope = "";
                    explanation = "月结界面-【月结日期（整数:不能超过28）】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "PHARM_STOCK_BALANCE_END_DATE_TIME";
                    defaltValue = "16:00:00";
                    empNo = "*";
                    paraScope = "";
                    explanation = "月结界面-【月结点的时间部分】，时分秒";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "PRINT_BEFORESAVE_IMEX";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "1,0";
                    explanation = "所有出入库界面-【是否允许保存前打印-出入库单据，0-不具备， 1-具备】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "PREVIEW";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "1,0";
                    explanation = "所有打印的地方-【打印是否预览，1-是， 0-否】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    break;
                #endregion
                case "STOCKMGR":
                    #region 药房
                    paramName = "DOCUMENT_NO_LENGTH_DEFAULT";
                    defaltValue = "10";
                    empNo = "*";
                    paraScope = "";
                    explanation = "所有入出库界面-【入出库单据号默认长度(正整数),不足时补零,超过时则按实际长度,但最大长度受数据库设计限制】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "IOAUTOKEY";
                    defaltValue = "9";
                    empNo = "*";
                    paraScope = "9,12";
                    explanation = "所有入出库界面-【出入库需要输入药品编码时自动触发的按键，9指的是F9，12指的是F12】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "PERMITNEGATIVE";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "1,0";
                    explanation = "出库界面-【是否允许库存为负，1-允许为负，0-不允许】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "DONATION";
                    defaltValue = "捐赠库";
                    empNo = "*";
                    paraScope = "";
                    explanation = "其他出库界面-【捐赠出库默认发往科室】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "WASTE";
                    defaltValue = "报损库";
                    empNo = "*";
                    paraScope = "";
                    explanation = "其他出库界面-【报损出库默认发往科室】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "APPLICATION_OPS_EXPORT_CLASS";
                    defaltValue = "手术出库";
                    empNo = "*";
                    paraScope = "发放出库,生产出库,手术出库";
                    explanation = "领药单领药界面-【手术领药单使用出库类型】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "DRUG_PROVIDE_APPLICATION_DELETE";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "申请出库界面-【药库是否可以删除药房的申请 0-否 1-是】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "EXPORT_APP_POSITIVE";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "申请出库界面-【是否限制申请出库数量为正：0-不限制 1-限制】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "EXPORT_PRIORITY";
                    defaltValue = "expiredate";
                    empNo = "*";
                    paraScope = "expiredate,batchno";
                    explanation = "所有的出库界面-【出库优先顺序：expiredate-效期优先 batchno-批号的先进先出】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "EXPORT_BYVALIDDATE";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "所有出库界面-【严格按有效期出库 1-严格 0-不严格，严格的话，过了效期的药不能出库】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "HANDLECODELEVEL";
                    defaltValue = "20";
                    empNo = "*";
                    paraScope = "";
                    explanation = "所有出入库及查询界面-【出入库和查询统计搜寻的代码级范围,就是输入几位码就可把相关的药品显示出来（正整数）】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "BACK_MEDICINE_METHOD";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "退药入库-【退药时，0入库入负，1出库出正】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "SHOWZEROSTOCK";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "所有出库界面-【是否显示零库存：0-否 1-是】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "PRINTDOCUMENT";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "所有出入库界面-【入库出库单据处理完毕是否打印入库出库单据，1-是，0-否】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "CONFIRM_LOGIN";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "所有出入库界面-【出库保存后记帐前是否重新输入操作员口令，0-否，1-是】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "INVENTORYCHANGESTOCK";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "盘点界面-【盘点后是否生成未记帐出入库单，1-允许，0-不允许】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "IMPORT_AUTO";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1,2";
                    explanation = "所有出库界面-【出库时接收方自动入库参数0-无操作 1-自动产生入库记录 2-自动产生入库记录并自动记帐】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "SAVEANDACCOUNTIMPORT";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "所有入库界面-【入库存是否保存单据同时作记帐处理，1-是,0-否】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "SAVEANDACCOUNTEXPORT";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "所有出库界面-【出库存是否保存单据同时作记帐处理，1-是,0-否】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "DEFAULTSUPPLIER";
                    defaltValue = "京诺华";
                    empNo = "*";
                    paraScope = "";
                    explanation = "采购入库，采购退货界面-【默认供货方】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "LIMIT_ALARM";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "登陆界面-【库存是否报警，1-是 0-否】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "EXPIRED_ALARM_DAYS";
                    defaltValue = "7";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "登陆界面-【药品失效日期限度，为正数进行提示，为负数不提示（整数）】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "LOWRATIO";
                    defaltValue = "0.2";
                    empNo = "*";
                    paraScope = "";
                    explanation = "上下限维护界面-【库存量下限与消耗量的关系（正数）】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "UPPERRATIO";
                    defaltValue = "1.9";
                    empNo = "*";
                    paraScope = "";
                    explanation = "上下限维护界面-【库存量上限与消耗量的关系（正数）】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "DSBALANCE_DAY_LASTBALANCED";
                    defaltValue = "2006-11-20 10:19";
                    empNo = "*";
                    paraScope = "";
                    explanation = "月结界面-【药房上次月结时间】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "STOCKBALANCE";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "月结界面-【月结标志系统登陆时取本参数，0-否，1-是】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "PHARM_STOCK_BALANCE_END_DATE_DAYNO";
                    defaltValue = "28";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "月结界面-【月结日期，整数:不能超过31】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "PHARM_STOCK_BALANCE_END_DATE_TIME";
                    defaltValue = "16:00:00";
                    empNo = "*";
                    paraScope = "";
                    explanation = "月结界面-【月结点的时间部分】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "PRINT_BEFORESAVE_IMEX";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "所有出入库界面-【是否允许保存前打印-出入库单据：0-不具备 1-具备】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    paramName = "PREVIEW";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "所有打印地方-【打印是否预览，1-是，0-否】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode))
                        return false;
                    break;
                #endregion
                case "PRESDISP":
                    #region 处方
                    //1参数:BACKPROCFLAG
                    paramName = "BACKPROCFLAG";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "门诊处方自动配药程序界面-【门诊处方自动配药程序(presdeploy)是否启动，0-不启动,1-启动】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //2参数:PRESCQUEUE
                    paramName = "PRESCQUEUE";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "门诊处方发药界面-【门诊处方是否支持大屏队列显示，0-不支持,1-支持】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //3参数:CALL_LEDDISP
                    paramName = "CALL_LEDDISP";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "门诊处方发药界面-【门诊处方是否支持大屏上显示处方明细，0-不支持,1-支持】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //4参数:DRUG_PRESC_QUEUE_MAX
                    paramName = "DRUG_PRESC_QUEUE_MAX";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "";
                    explanation = "门诊处方发药界面-【处方发药队列当前排号(循环),需配合大屏,不需修改（大于等于0的整数,最大不超过10000）】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //5参数:SHOW_LCD
                    paramName = "SHOW_LCD";
                    defaltValue = "no";
                    empNo = "*";
                    paraScope = "yes,no";
                    explanation = "门诊处方发药界面-【是否采用液晶显示屏显示取药情况 yes -是， no -否】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //6参数:COM_NO
                    paramName = "COM_NO";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "";
                    explanation = "门诊处方发药界面-【液晶显示屏采用的串口号（串口号码），1-为COM1 依此类推】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //7参数:WORKPATH
                    paramName = "WORKPATH";
                    defaltValue = @"c:\presdisp\lcd";
                    empNo = "*";
                    paraScope = "";
                    explanation = "门诊处方发药界面-【液晶显示屏所使用的工作目录（磁盘完整路径）】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //8参数:OUTP_SOUND
                    paramName = "OUTP_SOUND";
                    defaltValue = "no";
                    empNo = "*";
                    paraScope = "yes,no";
                    explanation = "门诊处方发药界面-【是否支持语音输出，yes-支持，no-不支持】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //9参数:USE_CARD
                    paramName = "USE_CARD";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "门诊处方发药界面-【是否支持门诊持卡就医 1-支持(读卡按钮可见),0- 不支持】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //10参数:CARD_TYPE
                    paramName = "CARD_TYPE";
                    defaltValue = "m_card";
                    empNo = "*";
                    paraScope = "m_card,ic_card";
                    explanation = "门诊处方发药界面-【当前使用卡的种类】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //=======================================================================
                    //====================1:通用设置===================================

                    //=======================================================================
                    //====================2:门诊处方设置===================================
                    //11参数:PRESC_VERIFIED
                    paramName = "PRESC_VERIFIED";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "门诊处方发药界面-【处方是否需要审核才能发药，0-不需要,1-需要】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //12参数:CHARGE_FLAG
                    paramName = "CHARGE_FLAG";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "门诊处方发药界面-【是否只为某一部分费别的病人发药，0-不是,1-是】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //13参数:CHARGETYPES
                    paramName = "CHARGETYPES";
                    defaltValue = "免费;自费";
                    empNo = "*";
                    paraScope = "";
                    explanation = "门诊处方发药界面-【为某一部分费别的病人发药（费别,以分号间隔）】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;
                    //14参数:SHOW_PRESC_DAYS
                    paramName = "SHOW_PRESC_DAYS";
                    defaltValue = "90";
                    empNo = "*";
                    paraScope = "";
                    explanation = "门诊处方发药界面-【显示几天的处方（整数）】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //15参数:DISP_SPAN_STOCK
                    paramName = "DISP_SPAN_STOCK";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1,2";
                    explanation = "门诊处方发药界面-【是否支持跨药房取药和提示该处方在哪个药房取药:0-为既不支持跨药房取药也不提示该处方在哪个药房取药,1-为只提示该处方在哪个药房取药,2-为支持跨药房取药】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //16参数:REFRASHFLAG
                    paramName = "REFRASHFLAG";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "门诊处方发药界面-【是否刷新 0－否， 1－是】已经没有实际作用了 ";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //17参数:REFRASHTIME
                    paramName = "REFRASHTIME";
                    defaltValue = "5000";
                    empNo = "*";
                    paraScope = "";
                    explanation = "门诊处方发药界面-【刷新时间（正整数）】已经没有实际作用了";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //18参数:MODIFY_PRESC
                    paramName = "MODIFY_PRESC";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "门诊处方发药界面-【门诊处方发药时是否可以修改处方,0 -不可修改,1 -可修改】 已经没有实际作用了";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //19参数:COMMIT_CONFIRM
                    paramName = "COMMIT_CONFIRM";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "门诊处方发药和退药界面-【处方发药和退药进行确认时是否提示，1 -提示， 0- 不提示】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //20参数:CONFIRM_LOGIN
                    paramName = "CONFIRM_LOGIN";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "门诊处方发药界面-【发药时是否重新输入操作员口令】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //21参数:PRESC_ATTR
                    paramName = "PRESC_ATTR";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "门诊处方发药界面-【是否在发药时提示选择处方类别， 0-不提示 1-提示】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //22参数:OUTP_DIAG_DESC
                    paramName = "OUTP_DIAG_DESC";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "门诊处方发药界面-【门诊处方确认的诊断是否来自待发药处方表，0-不是,1-是】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //23参数:DECSTOCKBATCHNO
                    paramName = "DECSTOCKBATCHNO";
                    defaltValue = "2";
                    empNo = "*";
                    paraScope = "1,2";
                    explanation = "所有减库存界面-【减库存模式，1－只减一个批号的药品， 2－按先进先出原则减不同批号的库存】";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //24参数:FORCEDEC
                    paramName = "FORCEDEC";
                    defaltValue = "2";
                    empNo = "*";
                    paraScope = "0,1,2";
                    explanation = "所有减库存界面-【库存不够时减库存选项,是否强行减库存 0-提示用户,1-强行减库存,2-不允许】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;
                    //=======================================================================
                    //====================2:门诊处方设置===================================

                    //=======================================================================
                    //====================3:住院处方发药部分参数=============================

                    //25参数:CONFIRM
                    paramName = "CONFIRM";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "住院处方发药界面-【预交金不足是否允许发药，0-不允许 ，1-允许】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //26参数:VERIFY
                    paramName = "VERIFY";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "住院处方发药界面-【处方确认时住院处方是否需要审核药师， 0－不需要 ，1－需要】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //27参数:VERIFY_CHARGETYPES
                    paramName = "VERIFY_CHARGETYPES";
                    defaltValue = "医疗保险;自费";
                    empNo = "*";
                    paraScope = "";
                    explanation = "住院处方发药界面-【需要审核处方的病人费别（费别,以分号间隔）】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //28参数:DRUG_CATALOG_CLASS
                    paramName = "DRUG_CATALOG_CLASS";
                    defaltValue = "甲类药;乙类药";
                    empNo = "*";
                    paraScope = "";
                    explanation = "住院处方发药界面-【需要审核的药品类型（药品大类,以分号间隔）】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //29参数:DECOCT_INDICATOR
                    paramName = "DECOCT_INDICATOR";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "住院处方发药界面-【是否收取煎药费 1-收取 ，0-不收】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;
                    //30参数:DECOCT_CODE
                    paramName = "DECOCT_CODE";
                    defaltValue = "QT00000047";
                    empNo = "*";
                    paraScope = "";
                    explanation = "住院处方发药界面-【煎药费在价表中的代码（煎药费代码）】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //31参数:CONFIRM_CHARGE_TYPE  //20150906lcz
                    paramName = "CONFIRM_CHARGE_TYPE";
                    defaltValue = "免费;自费";
                    empNo = "*";
                    paraScope = "";
                    explanation = "住院处方发药界面-【住院处方需要判断预交金的费用类别】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //=======================================================================
                    //====================3:住院处方发药部分参数=============================

                    //=======================================================================
                    //====================3:打印部分参数=============================
                    //32参数:PRNFLAG
                    paramName = "PRNFLAG";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1,2";
                    explanation = "处方发药界面-【0- 不打印,1-为打印,2-为提示是否打印】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //33参数:PRINTERTYPE
                    paramName = "PRINTERTYPE";
                    defaltValue = "normal";
                    empNo = "*";
                    paraScope = "hot,normal";
                    explanation = "所有打印地方-【打印机类型，hot-热敏打印机 normal-普通打印机】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //34参数:PRINTTYPE
                    paramName = "PRINTTYPE";
                    defaltValue = "normal";
                    empNo = "*";
                    paraScope = "string,normal";
                    explanation = "所有打印的地方-【打印方式：string-字符串打印方式， normal-普通打印方式】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //35参数:PRNLINE
                    paramName = "PRNLINE";
                    defaltValue = "5";
                    empNo = "*";
                    paraScope = "";
                    explanation = "所有打印的地方-【处方打印时处方明细的文本行数（正整数）】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //36参数:BACKPROCTIMES
                    paramName = "BACKPROCTIMES";
                    defaltValue = "5";
                    empNo = "*";
                    paraScope = "";
                    explanation = "所有打印的地方-【门诊处方自动打印处理时间间隔（整数型，单位为秒）】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;
                    //=======================================================================
                    //====================3:打印部分参数=============================

                    //=======================================================================
                    //====================4:退药部分参数=============================
                    //37参数:STRIDE_STORAGE_RETURN
                    paramName = "STRIDE_STORAGE_RETURN";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "处方退药界面-【是否支持跨药房退药,0-为不支持跨药房退药,1-为支持跨药房退药】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //38参数:PRESC_REGRET_DOCTORPERMIT
                    paramName = "PRESC_REGRET_DOCTORPERMIT";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "门诊处方退药界面-【是否需要医生许可才能退药：0-不需要， 1-需要】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //39参数:RETURN_CONFIRM_LOGIN
                    paramName = "RETURN_CONFIRM_LOGIN";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "处方退药界面-【退药时是否重新输入操作员口令】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //40参数:REGRET_APP
                    paramName = "REGRET_APP";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "门诊处方退药界面-【门诊处方退药是否走退药申请来源流程，0-不是,1-是】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //41参数:CARD_BILL_FLAG
                    paramName = "CARD_BILL_FLAG";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "处方退药界面-【是否支持卡退费，0-支持,1-不支持】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //=======================================================================
                    //====================4:退药部分参数=============================

                    //=======================================================================
                    //====================5:处方录入部分参数=============================
                    //42参数:quantitycheck
                    paramName = "QUANTITYCHECK";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "处方录入界面-【处方录入时是否检查库存数量:0为不检查,1为检查】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //43参数:quantitycheck_hasqueue_outp
                    paramName = "QUANTITYCHECK_HASQUEUE_OUTP";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "处方录入界面-【处方录入检查库存数量时是否检查门诊处方，0-为不检查,1-为检查】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //44参数:quantitycheck_hasqueue_inpm
                    paramName = "QUANTITYCHECK_HASQUEUE_INPM";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "处方录入界面-【处方录入检查库存数量时是否检查住院处方，0-为不检查,1-为检查】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;
                    //=======================================================================
                    //====================5:处方录入部分参数=============================

                    //45参数:INPUTCFG
                    paramName = "INPUTCFG";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "1,2";
                    explanation = "所有可以输入界面-【1-拼音，2-五笔】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;

                    //46参数:BILL_PRINT_TYPE   2015-08-10
                    paramName = "BILL_PRINT_TYPE";
                    defaltValue = @"口服\";
                    empNo = "*";
                    paraScope = "";
                    explanation = "处方确认界面-【服药单据打印类型】";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;
                    #endregion
                    break;
                case "ORDDISP":
                    #region 医嘱摆药

                    //1参数:WARDCODE
                    paramName = "WARDCODE";
                    defaltValue = "全部";
                    empNo = "*";
                    paraScope = "";
                    explanation = "为空则药局为全部科室摆药,指定几个护理单元,就可以为几个护理单元摆药";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //2参数:缺省的摆药类型
                    string spli = ";", defvalue = "";
                    string sql = "select distinct dispensing_property from DRUG_DISP_PROPERTY_DICT";
                    NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                    DataTable dt = spc.GetList(sql).Tables[0];
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        if (i != dt.Rows.Count - 1)
                        {
                            defvalue = dt.Rows[i]["dispensing_property"] == DBNull.Value ? defvalue + "" : defvalue + dt.Rows[i]["dispensing_property"].ToString() + spli;
                        }
                        else
                        {
                            defvalue = dt.Rows[i]["dispensing_property"] == DBNull.Value ? defvalue + "" : defvalue + dt.Rows[i]["dispensing_property"].ToString();
                        }
                    }
                    paramName = "缺省摆药类型";
                    defaltValue = defvalue;
                    empNo = "*";
                    paraScope = "输液类;注射类";
                    explanation = "摆药界面默认的摆药类型,这个值根据摆药类别表数据来定义";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //3参数:DISPENSE_REQ_ONLY
                    paramName = "DISPENSE_REQ_ONLY";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "是否只按申请摆药 0 ;NO 1;YES!";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //4参数:AUTO_DISPENSE_REQ  
                    paramName = "AUTO_DISPENSE_REQ";
                    defaltValue = "YES";
                    empNo = "*";
                    paraScope = "YES,NO";
                    explanation = "是否自动提取摆药申请";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //5参数:AUTO_DISPENSE_REQ_SPAN
                    paramName = "AUTO_DISPENSE_REQ_SPAN";
                    defaltValue = "2";
                    empNo = "*";
                    paraScope = "1-1000";
                    explanation = "提取摆药申请的包含天数 整数值";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //6参数:AUTO_DISPENSE_REQ_TIME
                    paramName = "AUTO_DISPENSE_REQ_TIME";
                    defaltValue = "60";
                    empNo = "*";
                    paraScope = "1-1000";
                    explanation = "自动提取摆药申请的时间间隔,单位秒  整数值";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;



                    //7参数:IS_MODIFY_DISP_DATETIME
                    paramName = "IS_MODIFY_DISP_DATETIME";
                    defaltValue = "YES";
                    empNo = "*";
                    paraScope = "YES,NO";
                    explanation = "摆药界面的摆药时间和摆药天数是否可调整";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //8参数:DISPENSE_START_DAY
                    paramName = "DISPENSE_START_DAY";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "";
                    explanation = "0 表示摆药从当天开始 DISPENSE_START_DAY=-1 表示摆药从昨天开始. 输入整数";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;



                    //9参数:DISPENSE_START_TIME  
                    paramName = "DISPENSE_START_TIME";
                    defaltValue = "16:00";
                    empNo = "*";
                    paraScope = "";
                    explanation = "摆药开始时间,包含这个时间点";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //10参数:DISPENSE_STOP_TIME
                    paramName = "DISPENSE_STOP_TIME";
                    defaltValue = "10:00";
                    empNo = "*";
                    paraScope = "";
                    explanation = "摆药截止时间,不包含这个时间点";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //11参数:DISPENSE_DAYS
                    paramName = "DISPENSE_DAYS";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "1,2";
                    explanation = "摆药天数 整数值";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //12参数BEFORE_DAYS
                    paramName = "BEFORE_DAYS";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "1,2";
                    explanation = "取临时医嘱的提前天数 整数值";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //===============================================
                    //13参数:DOCTOR_SPEC_FIRST;
                    paramName = "DOCTOR_SPEC_FIRST";
                    defaltValue = "YES";
                    empNo = "*";
                    paraScope = "YES,NO";
                    explanation = "决定是否按照指定的规格摆药,按照orders_costs中的item_spec规格进行摆药yes按照;no不按照";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //14参数:MATCH_OTHER_FIRM
                    paramName = "MATCH_OTHER_FIRM";
                    defaltValue = "NO";
                    empNo = "*";
                    paraScope = "YES,NO";
                    explanation = "在按照医生指定规格摆药匹配不到的情况下，是否自动匹配一个相同规格不同厂家的药";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //15参数:MATCH_OTHER_SPEC
                    paramName = "MATCH_OTHER_SPEC";
                    defaltValue = "NO";
                    empNo = "*";
                    paraScope = "YES,NO";
                    explanation = "在上面两者都匹配不到的情况下，是否匹配其他不同规格的药";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //==============================================================================================

                    //16参数:ITEM
                    paramName = "ITEM";
                    defaltValue = "片剂;针剂";
                    empNo = "*";
                    paraScope = "";
                    explanation = "可分割摆药的药品剂型";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;



                    //17参数:PREPAYMENTS;
                    paramName = "PREPAYMENTS";
                    defaltValue = "NO";
                    empNo = "*";
                    paraScope = "YES,NO";
                    explanation = "是否判断预交金yes判断;no不判断";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //18参数:PREPAYMENTS_IDENTITY
                    paramName = "PREPAYMENTS_IDENTITY";
                    defaltValue = "全费";
                    empNo = "*";
                    paraScope = "";
                    explanation = "需判断预交金的身份";

                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //19参数:TYPE
                    paramName = "TYPE";
                    defaltValue = "0";
                    empNo = "*";
                    paraScope = "1,0";
                    explanation = "摆药方式 1代表预交金不足就不进行摆药;0代表按剩余金额摆药!";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //20参数:IS_MANAGE_STOCK
                    paramName = "IS_MANAGE_STOCK";
                    defaltValue = "YES";
                    empNo = "*";
                    paraScope = "YES,NO";
                    explanation = "摆药减库存yes-减; no-不减";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;



                    //21参数:NEGATIVE_STOCK
                    paramName = "NEGATIVE_STOCK";
                    defaltValue = "NO";
                    empNo = "*";
                    paraScope = "YES,NO";
                    explanation = "库存可为负YES可为负; NO-不可为负";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;



                    //22参数:NEGATIVE_STOCK_ALARM
                    paramName = "NEGATIVE_STOCK_ALARM";
                    defaltValue = "YES";
                    empNo = "*";
                    paraScope = "YES,NO";
                    explanation = "库存可为负的情况下，摆药减库存使库存为零或负进行提示yes-提示; no-不提示";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //23参数:CHARGE_INDICATOR
                    paramName = "CHARGE_INDICATOR";
                    defaltValue = "YES";
                    empNo = "*";
                    paraScope = "YES,NO";
                    explanation = "计价,不计价!";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //24参数:CHARGE_MERGE
                    paramName = "CHARGE_MERGE";
                    defaltValue = "NO";
                    empNo = "*";
                    paraScope = "YES,NO";
                    explanation = "合并同病人同种药的计价信息!";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //25参数:PRINTTESTMODE
                    paramName = "PRINTTESTMODE";
                    defaltValue = "1";
                    empNo = "*";
                    paraScope = "0,1";
                    explanation = "打印测试模式0-关; 1-开";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    //26参数:缺省打印机类型
                    paramName = "缺省打印机类型";
                    defaltValue = "针式打印机";
                    empNo = "*";
                    paraScope = "";
                    explanation = "摆药单使用的打印几类型   例如针式打印机;激光打印机";
                    if (!ExistsAndInsertPara(appName, paramName, deptCode, empNo, defaltValue, paraScope, explanation, hisUnitCode)) return false;


                    #endregion
                    break;
                default:
                    break;
            }
            return true;
        }
        #endregion
        #region 打开浏览器
        public static bool OpenUrl(string myexe, string myurl)
        {
            if (string.IsNullOrEmpty(myexe))
            {
                if (!File.Exists(@"C:\Program Files\Internet Explorer\iexplore.exe") && !File.Exists(@"C:\Program Files (x86)\Internet Explorer\iexplore.exe"))
                {
                    if (!File.Exists(@"C:\Users\<USER>\AppData\Local\SogouExplorer\SogouExplorer.exe"))
                    {
                        DevExpress.XtraEditors.XtraMessageBox.Show("请安装ie浏览器或者搜狗浏览器", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return false;
                    }
                    else
                    {
                        try
                        {
                            Process.Start(@"C:\Users\<USER>\AppData\Local\SogouExplorer\SogouExplorer.exe", myurl);//搜狗
                            return true;
                        }
                        catch
                        {
                            DevExpress.XtraEditors.XtraMessageBox.Show("搜狗浏览器打开url失败", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            return false;
                        }

                    }
                }
                try
                {
                    Process.Start("iexplore.exe", myurl);
                    return true;
                }
                catch
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("IE浏览器打开url失败", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            else
            {
                try
                {
                    Process.Start(myexe, myurl);
                    return true;
                }
                catch
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show(myexe + "打开" + myurl + "失败", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            //var result360 = Process.Start(@"C:\Users\<USER>\AppData\Roaming\360se6\Application\360se.exe", url);//360
            //var resultsg = Process.Start(@"C:\Users\<USER>\AppData\Local\SogouExplorer\SogouExplorer.exe", url);//搜狗
            //var resultchrome = Process.Start(@"C:\Program Files (x86)\Google Chrome\chrome.exe", url);//谷歌浏览器

        }


        #endregion
        #region 通用函数
        #region 获取检验单申请号
        public static string GetLabTest_No(ref string errmessage)
        {
            //描述: 生成检验申请单号
            string ls_test_no = "";
            try
            {
                ServerPublicClient service = new ServerPublicClient();
                string ls_date = service.GetSysDate().ToString("yyMMdd");
                DataTable testno = service.GetDataBySql("Select lab.test_no.nextval test_no  From dual").Tables[0];
                if (testno.Rows.Count <= 0)
                {
                    errmessage = "生成检验序号失败";
                    return "";
                }
                ls_test_no = testno.Rows[0]["TEST_NO"].ToString().PadLeft(6, '0');
                ls_test_no = ls_test_no + ls_date;
                //if (!GetSequeceFromAuto("检验单号序列", PlatCommon.SysBase.SystemParm.HisUnitCode, ref ls_test_no) || string.IsNullOrEmpty(ls_test_no))
                //{
                //    errmessage = "查询检验单号序列失败！";
                //    return "";
                //}
                //return ls_test_no;

            }
            catch (Exception ex)
            {
                errmessage = ex.Message;
            }

            return ls_test_no;
        }
        #endregion
        #region 获取检查申请号
        public static string GetExam_No()
        {
            //描述: 生成检验申请单号

            //string sqlb = " Select exam_no_seq.nextval  From dual ";
            //DataSet dsseq = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sqlb.ToString());
            //if (dsseq == null || dsseq.Tables[0].Rows.Count <= 0)
            //{
            //    return null;
            //}
            //return dsseq.Tables[0].Rows[0][0].ToString();
            string ls_appointno = "";
            if (!PlatCommon.Common.PublicFunction.GetSequeceFromAuto("检查单号序列", PlatCommon.SysBase.SystemParm.HisUnitCode, ref ls_appointno) || string.IsNullOrEmpty(ls_appointno))
            {
                return "";
            }
            return ls_appointno;
            //string datestr = Convert.ToDateTime(GetSysDate()).ToString("yyMMdd");
            //return (datestr + ls_appointno);
        }
        #endregion
        #region 用血申请号
        public static string GetBlood_No()
        {
            //描述: 生成用血申请号

            string sql = " SELECT TO_CHAR(SYSDATE,'yyyyMMdd'),APPLY_NUM.NEXTVAL FROM DUAL ";
            using (ServerPublicClient serv=new ServerPublicClient())
            {
                DataSet ds = serv.GetDataBySql(sql);
                string sysDate = ds.Tables[0].Rows[0][0].ToString();
                string seqdtApplyNum = ds.Tables[0].Rows[0][1].ToString();
                return sysDate + seqdtApplyNum.PadLeft(3, '0');
            }

            //string ls_appointno = "";
            //if (!PlatCommon.Common.PublicFunction.GetSequeceFromAuto("检查单号序列", PlatCommon.SysBase.SystemParm.HisUnitCode, ref ls_appointno) || string.IsNullOrEmpty(ls_appointno))
            //{
            //    return "";
            //}
            //return ls_appointno;
            //string datestr = Convert.ToDateTime(GetSysDate()).ToString("yyMMdd");
            //return (datestr + ls_appointno);
        }
        
        #endregion
        /// <summary>
        /// 获取处方序列号 by lions
        /// </summary>
        /// <returns></returns>
        public static string GetPrescNo()
        {
            //StringBuilder sbSql = new StringBuilder();
            //sbSql.Append("Select presc_no_seq.nextval From dual");
            try
            {
                //DataSet dsPresc = new NM_Service.NMService.ServerPublicClient().GetList(sbSql.ToString());
                string strPrescNo = null;
                //if (dsPresc.Tables[0] != null)
                //{
                //    if (dsPresc.Tables[0].Rows.Count > 0)
                //        strPrescNo = dsPresc.Tables[0].Rows[0][0].ToString().Trim();
                //}
                if (!GetSequeceFromAuto("处方号序列", PlatCommon.SysBase.SystemParm.HisUnitCode, ref strPrescNo) || string.IsNullOrEmpty(strPrescNo))
                {
                    return "";
                }
                return strPrescNo;
            }
            catch
            {
                return null;
            }

        }

        //根据身份证返回patient_id 和 visit_id
        /// <summary>
        /// 根据身份证返回patient_id 和 visit_id
        /// </summary>
        /// <param name="patient_id"></param>
        /// <param name="visit_id"></param>
        /// <param name="id_no">身份证号 如果此参数不为空则直接根据此参数提取，否则读取卡机</param>
        /// <returns>true成功</returns>
        public static bool GetPatientIdForIdNo(ref string patient_id, ref string visit_id, string id_no = "")
        {
            string idcard_nn = id_no;
            //if (string.IsNullOrEmpty(idcard_nn))
            //{
            //    //为空时读取设备
            //    IDCardData idCardData = new IDCardData();
            //    int cd = 0;

            //    cd = ReadCardAPI.ReadCardID(ref idCardData);
            //    if (string.IsNullOrEmpty(idCardData.IDCardNo))
            //    {
            //        XtraMessageBox.Show("未读取到患者的身份证信息，请重试！", "提示");
            //        return false;
            //    }
            //    else
            //    {
            //        idcard_nn = idCardData.IDCardNo;
            //    }
            //}
            //从在院表中读取数据
            string sql = "select a.patient_id,a.visit_id from pats_in_hospital a,pat_master_index b ";
            sql += " where a.patient_id = b.patient_id and b.id_no = '" + idcard_nn + "'";
            DataTable dt = new ServerPublicClient().GetDataBySql(sql).Tables[0];
            if (dt.Rows.Count < 1)
            {
                sql = "select patient_id from pat_master_index where id_no='" + idcard_nn + "'";
                dt = new ServerPublicClient().GetDataBySql(sql).Tables[0];
                if (dt.Rows.Count < 1)
                    return false;
                else
                {
                    patient_id = dt.Rows[0]["patient_id"].ToString();
                    visit_id = "0";
                    return true;
                }
            }
            else
            {
                patient_id = dt.Rows[0]["patient_id"].ToString();
                visit_id = dt.Rows[0]["visit_id"].ToString();
            }
            return true;
        }
        /// <summary>
        /// 从设置表中查询序列号由前缀+序列号当前值（序列号当前值不足位数的，前面补0）
        /// </summary>
        /// <param name="typename">类型名</param>
        /// <param name="hisunitcode">医院编码</param>
        /// <param name="sequece">返回序列号</param>
        /// <returns>获取成功失败 true false</returns>
        public static bool GetSequeceFromAuto(string typename, string hisunitcode, ref string sequece, Utility.OracleODP.OracleBaseClass db = null)
        {

            //从在院表中读取数据
            string sql = "select SERIAL_NO, TYPE_NAME, ID_START_VALUE, ID_CURRENTLY_VALUE, ID_LENGTH, ID_UPGRADE,ID_SQL, HIS_UNIT_CODE from auto_setting_id  where TYPE_NAME= :typename and HIS_UNIT_CODE=:gs_unitcode";
            if (db == null)
            {
                NM_Service.NMService.ServerPublicClient service = new NM_Service.NMService.ServerPublicClient();
                List<string> paras = new List<string>();
                ArrayList values = new ArrayList();
                paras.Add("typename");
                paras.Add("gs_unitcode");
                values.Add(typename);
                values.Add(hisunitcode);
                try
                {
                    DataSet ds = service.GetDataTable_Para(sql, paras, values);
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        string idcurvalue = ds.Tables[0].Rows[0]["ID_CURRENTLY_VALUE"].ToString();
                        int idvalue = Convert.ToInt32(idcurvalue) + 1;
                        int idlen = Convert.ToInt32(ds.Tables[0].Rows[0]["ID_LENGTH"]);
                        if (idvalue.ToString().Length > idlen)
                        {
                            //超出值规定长度，从头计算
                            idvalue = 1;

                        }
                        ds.Tables[0].Rows[0]["ID_CURRENTLY_VALUE"] = idvalue;
                        try
                        {
                            //更新当前值
                            if (service.SaveDataSet(ds) > 0)
                            {
                                string sql_pref = "";
                                if (!string.IsNullOrEmpty(ds.Tables[0].Rows[0]["ID_SQL"].ToString()))
                                {
                                    DataSet ds1 = service.GetDataBySql(ds.Tables[0].Rows[0]["ID_SQL"].ToString());
                                    if (ds1 != null && ds1.Tables[0].Rows.Count > 0)
                                    {
                                        sql_pref = ds1.Tables[0].Rows[0][0].ToString();
                                    }
                                }
                                idcurvalue = idvalue.ToString().PadLeft(idlen, '0');
                                sequece = sql_pref + ds.Tables[0].Rows[0]["ID_START_VALUE"].ToString() + idcurvalue;
                                return true;
                            }
                            else
                            {
                                return false;
                            }
                        }
                        catch
                        {
                            return false;
                        }

                    }
                    return true;
                }
                catch
                {
                    return false;
                }
            }
            else
            {
                sql = "select SERIAL_NO, TYPE_NAME, ID_START_VALUE, ID_CURRENTLY_VALUE, ID_LENGTH, ID_UPGRADE, ID_SQL, HIS_UNIT_CODE from auto_setting_id  where TYPE_NAME='{0}' and HIS_UNIT_CODE='{1}'";
                try
                {
                    DataSet ds = db.SelectDataSet(string.Format(sql, typename, hisunitcode));
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        string idcurvalue = ds.Tables[0].Rows[0]["ID_CURRENTLY_VALUE"].ToString();
                        int idvalue = Convert.ToInt32(idcurvalue) + 1;
                        int idlen = Convert.ToInt32(ds.Tables[0].Rows[0]["ID_LENGTH"]);
                        if (idvalue.ToString().Length > idlen)
                        {
                            //超出值规定长度，从头计算
                            idvalue = 1;

                        }
                        ds.Tables[0].Rows[0]["ID_CURRENTLY_VALUE"] = idvalue;
                        try
                        {
                            string sql_update = "update auto_setting_id set id_currently_value = id_currently_value + 1 where  TYPE_NAME='" + typename + "' and HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
                            //更新当前值
                            if (db.ExecuteTransaction(sql_update))
                            {
                                string sql_pref = "";
                                if (!string.IsNullOrEmpty(ds.Tables[0].Rows[0]["ID_SQL"].ToString()))
                                {
                                    DataSet ds1 = db.SelectDataSet(ds.Tables[0].Rows[0]["ID_SQL"].ToString()); //service.GetDataBySql(ds.Tables[0].Rows[0]["ID_SQL"].ToString());
                                    if (ds1 != null && ds1.Tables[0].Rows.Count > 0)
                                    {
                                        sql_pref = ds1.Tables[0].Rows[0][0].ToString();
                                    }
                                }
                                idcurvalue = idvalue.ToString().PadLeft(idlen, '0');
                                sequece = sql_pref + ds.Tables[0].Rows[0]["ID_START_VALUE"].ToString() + idcurvalue;
                                return true;
                            }
                            else
                            {
                                return false;
                            }
                        }
                        catch
                        {
                            return false;
                        }

                    }
                    return true;
                }
                catch
                {
                    return false;
                }
            }


        }
        /// <summary>
        /// 根据序号取id值
        /// </summary>
        /// <param name="serialno"></param>
        /// <param name="type">0,ID序列；1，INP序列</param>
        /// <param name="hisunitcode"></param>
        /// <param name="sequece"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public static bool GetIDNoFromAuto(int serialno, int type, string hisunitcode, ref string sequece, Utility.OracleODP.OracleBaseClass db = null)
        {

            //从在院表中读取数据
            string sql = "select SERIAL_NO, TYPE_NAME, ID_START_VALUE, ID_CURRENTLY_VALUE, ID_LENGTH, ID_UPGRADE,ID_SQL, INP_START_VALUE, INP_CURRENTLY_VALUE, INP_LENGTH, INP_UPGRADE,INP_SQL,HIS_UNIT_CODE from auto_setting_id  where SERIAL_NO= :serialno and HIS_UNIT_CODE=:gs_unitcode";

            NM_Service.NMService.ServerPublicClient service = new NM_Service.NMService.ServerPublicClient();
            List<string> paras = new List<string>();
            ArrayList values = new ArrayList();
            paras.Add("serialno");
            paras.Add("gs_unitcode");
            values.Add(serialno);
            values.Add(hisunitcode);
            try
            {
                DataSet ds = service.GetDataTable_Para(sql, paras, values);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    if (type == 0)
                    {
                        string idcurvalue = ds.Tables[0].Rows[0]["ID_CURRENTLY_VALUE"].ToString();
                        int idvalue = Convert.ToInt32(idcurvalue) + 1;
                        int idlen = Convert.ToInt32(ds.Tables[0].Rows[0]["ID_LENGTH"]);
                        if (idvalue.ToString().Length > idlen)
                        {
                            //超出值规定长度，从头计算
                            idvalue = 1;

                        }
                        ds.Tables[0].Rows[0]["ID_CURRENTLY_VALUE"] = idvalue;
                        try
                        {

                            //更新当前值
                            if (service.SaveDataSet(ds) > 0)
                            {
                                string sql_pref = "";
                                if (!string.IsNullOrEmpty(ds.Tables[0].Rows[0]["ID_SQL"].ToString()))
                                {
                                    DataSet ds1 = service.GetDataBySql(ds.Tables[0].Rows[0]["ID_SQL"].ToString());
                                    if (ds1 != null && ds1.Tables[0].Rows.Count > 0)
                                    {
                                        sql_pref = ds1.Tables[0].Rows[0][0].ToString();
                                    }
                                }
                                idcurvalue = idvalue.ToString().PadLeft(idlen, '0');
                                sequece = sql_pref + ds.Tables[0].Rows[0]["ID_START_VALUE"].ToString() + idcurvalue;
                                return true;
                            }
                            else
                            {
                                return false;
                            }
                        }
                        catch
                        {
                            return false;
                        }
                    }
                    else
                    {
                        string idcurvalue = ds.Tables[0].Rows[0]["INP_CURRENTLY_VALUE"].ToString();
                        int idvalue = Convert.ToInt32(idcurvalue) + 1;
                        int idlen = Convert.ToInt32(ds.Tables[0].Rows[0]["INP_LENGTH"]);
                        if (idvalue.ToString().Length > idlen)
                        {
                            //超出值规定长度，从头计算
                            idvalue = 1;

                        }
                        ds.Tables[0].Rows[0]["INP_CURRENTLY_VALUE"] = idvalue;
                        try
                        {
                            //更新当前值
                            if (service.SaveDataSet(ds) > 0)
                            {
                                string sql_pref = "";
                                if (!string.IsNullOrEmpty(ds.Tables[0].Rows[0]["INP_SQL"].ToString()))
                                {
                                    DataSet ds1 = service.GetDataBySql(ds.Tables[0].Rows[0]["INP_SQL"].ToString());
                                    if (ds1 != null && ds1.Tables[0].Rows.Count > 0)
                                    {
                                        sql_pref = ds1.Tables[0].Rows[0][0].ToString();
                                    }
                                }
                                idcurvalue = idvalue.ToString().PadLeft(idlen, '0');
                                sequece = sql_pref + ds.Tables[0].Rows[0]["INP_START_VALUE"].ToString() + idcurvalue;
                                return true;
                            }
                            else
                            {
                                return false;
                            }
                        }
                        catch
                        {
                            return false;
                        }
                    }

                }
                return true;
            }
            catch
            {
                return false;
            }

        }
        #endregion
        #region 消耗品自动加价价格计算 by lions 2018-10-17
        public static bool GetPriceAddRules(decimal price, ref decimal destprice, string texfrom, string texclass)
        {

            string sql = "select ear.add_type,ear.price_add from EXSTOCK.EX_ADDPRICE_RULE ear where ear.price_down<" + price + " and ear.price_up>=" + price + " and ear.status='1' and ear.his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' and ear.EX_FORM = '" + texfrom + "' and ear.EX_CLASS = '" + texclass + "'";

            ServerPublicClient service = new ServerPublicClient();
            DataTable dt = service.GetDataBySql(sql).Tables[0];
            if (dt.Rows.Count > 0)
            {
                string add_type = dt.Rows[0]["ADD_TYPE"].ToString();
                if ("1".Equals(add_type))
                {
                    destprice = price + Convert.ToDecimal(dt.Rows[0]["PRICE_ADD"]);
                }
                else
                {
                    destprice = price * Convert.ToDecimal(dt.Rows[0]["PRICE_ADD"]);
                    destprice = price + Math.Round(destprice, 4, MidpointRounding.AwayFromZero);
                }
                dt.Dispose();
                return true;
            }
            dt.Dispose();
            return false;//没有计价规则，采用原价
        }
        #endregion
        #region 根据各明细的item_code 取价表的material_code
        public static bool GetPriceListMaterialcode(string itemclass, string itemcode, string itemspec, string tunits, ref string tmaterialcode)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("select material_code from current_price_list a ");
            sb.Append(" where a.item_class = '" + itemclass + "' and a.item_code = '" + itemcode + "' ");
            sb.Append("and a.item_spec = '" + itemspec + "' and a.units = '" + tunits + "'");
            ServerPublicClient service = new ServerPublicClient();
            DataTable dt = service.GetDataBySql(sb.ToString()).Tables[0];
            if (dt.Rows.Count > 0)
            {
                tmaterialcode = dt.Rows[0][0].ToString();
                if (string.IsNullOrEmpty(tmaterialcode)) tmaterialcode = itemcode;
                dt.Dispose();
                return true;
            }

            return false;
        }
        #endregion

        #region  httppost函数 by lions 2019-03-24
        public static string HttpPost(String url, String postStr, string header = "", string ContentType = "application/x-www-form-urlencoded")
        {
            try
            {
                HttpWebRequest request = (HttpWebRequest)HttpWebRequest.Create(url);    //创建一个请求示例
                request.Accept = "image/gif, image/x-xbitmap, image/jpeg, image/pjpeg, application/x-shockwave-flash, application/x-silverlight, application/vnd.ms-excel, application/vnd.ms-powerpoint, application/msword, application/x-ms-application, application/x-ms-xbap, application/vnd.ms-xpsdocument, application/xaml+xml, application/x-silverlight-2-b1, */*";
                //request.SendChunked = true;
                //request.TransferEncoding = "UTF-8";
                //request.UserAgent = "Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/32.0.1667.0 Safari/537.36";
                request.Method = "POST";
                if (!string.IsNullOrEmpty(header))
                {
                    string[] hs = header.Split('=');
                    if (hs.Length != 2) return "";
                    request.Headers.Add(hs[0], hs[1]);//"apiKey=153df0bc69fek8w4ad9d574d02c105f4" 物流系统
                }

                request.ContentType = ContentType;
                byte[] postdatabyte = Encoding.UTF8.GetBytes(postStr);     //提交的请求主体的内容

                request.ContentLength = postdatabyte.Length;    //提交的请求主体的长度
                request.AllowAutoRedirect = false;
                request.KeepAlive = true;

                //提交请求
                Stream stream;
                stream = request.GetRequestStream();
                stream.Write(postdatabyte, 0, postdatabyte.Length);     //带上请求主体
                stream.Close();

                //接收响应
                HttpWebResponse response = (HttpWebResponse)request.GetResponse();　　//获取响应，即发送请求
                Stream responseStream = response.GetResponseStream();
                StreamReader streamReader = new StreamReader(responseStream, Encoding.UTF8);
                string html = streamReader.ReadToEnd();
                return html;
            }
            catch (Exception e)
            {
                return "";
            }

        }
        #endregion

        #region 插入费用明细
        public static string InsertInpBillDetail(string pat_id, int visit_id, string item_class, string item_name,
            string item_code, string item_spec, string units, string ordered_by, string performed_by,
            decimal amount, decimal costs, string operator_no, string order_group,
            string order_doctor, DateTime bill_date_time, string ls_doctor_user, string as_oper_type,
            string as_oper_code, int gs_item_no, string deptCode, string chargeType)
        {

            decimal ldc_price, ldc_scale;  //标价,使用系数
            ldc_scale = 1;
            string ls_class_on_reckoning = "*";
            string ls_class_on_inp_rcpt = "*";
            string ls_subj_code = "*";
            string ls_class_on_mr = "*";
            DateTime ldt_stop_datetime;  //计价停止时间
            ldt_stop_datetime = bill_date_time;
            ldc_price = 0;
            string sqlstr = "  select  b.price,b.class_on_reckoning,b.class_on_inp_rcpt,b.subj_code,class_on_mr,b.mr_bill_class from  price_master_list a, price_detail_list b  ";
            sqlstr += " where  a.item_class = b.item_class  and a.item_code = b.item_code   and a.item_spec = b.item_spec   and a.units = b.units and    a.item_class='" + item_class + "' and a.item_code='" + item_code + "' and (a.item_spec= '";
            sqlstr += item_spec + "' or a.item_spec IS NULL) AND (a.units='" + units + "' or a.units='天') AND ((b.start_date<= to_date('";
            sqlstr += ldt_stop_datetime + "','yyyy-mm-dd hh24:mi:ss') AND b.stop_date IS NULL) OR (b.stop_date IS NOT NULL AND b.start_date< to_date('";
            sqlstr += ldt_stop_datetime + "','yyyy-mm-dd hh24:mi:ss')and to_date('" + ldt_stop_datetime + "','yyyy-mm-dd hh24:mi:ss')<=stop_date)) and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
            string specialIndicator = "";
            int li_fee_coeff_num = 0, li_fee_coeff_den = 1;
            using (ServerPublicClient client = new ServerPublicClient())
            {
                DataSet ds = client.GetDataBySql(sqlstr);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    ldc_price = Convert.ToDecimal(ds.Tables[0].Rows[0]["PRICE"].ToString());
                    ls_class_on_reckoning = ds.Tables[0].Rows[0]["CLASS_ON_RECKONING"].ToString();
                    ls_class_on_inp_rcpt = ds.Tables[0].Rows[0]["CLASS_ON_INP_RCPT"].ToString();
                    ls_subj_code = ds.Tables[0].Rows[0]["SUBJ_CODE"].ToString();
                    ls_class_on_mr = ds.Tables[0].Rows[0]["mr_bill_class"].ToString();

                }
                else
                {
                    if (amount != 0)
                    {
                        ldc_price = Math.Round(costs / amount, 2);
                    }
                    else
                    {
                        ldc_price = costs;
                    }
                }
                sqlstr = "SELECT PRICE_COEFF_NUMERATOR,PRICE_COEFF_DENOMINATOR,CHARGE_SPECIAL_INDICATOR,CHARGE_SPECIAL_INDICATOR ";
                sqlstr += " FROM CHARGE_PRICE_SCHEDULE WHERE CHARGE_TYPE = '" + chargeType + "' ";
                ds = client.GetDataBySql(sqlstr);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    specialIndicator = ds.Tables[0].Rows[0]["CHARGE_SPECIAL_INDICATOR"].ToString();
                    if (string.IsNullOrEmpty(ds.Tables[0].Rows[0]["PRICE_COEFF_NUMERATOR"].ToString()))
                        li_fee_coeff_num = 0;
                    else
                        li_fee_coeff_num = int.Parse(ds.Tables[0].Rows[0]["PRICE_COEFF_NUMERATOR"].ToString());
                    if (string.IsNullOrEmpty(ds.Tables[0].Rows[0]["PRICE_COEFF_DENOMINATOR"].ToString()))
                        li_fee_coeff_den = 1;
                    else
                    {
                        li_fee_coeff_den = int.Parse(ds.Tables[0].Rows[0]["PRICE_COEFF_DENOMINATOR"].ToString());
                        if (li_fee_coeff_den == 0)
                            li_fee_coeff_den = 1;
                    }
                }
                else
                    specialIndicator = "0";
                ds.Dispose();
            }
            if (string.IsNullOrEmpty(ls_class_on_inp_rcpt))
            { ls_class_on_inp_rcpt = "*"; }
            if (string.IsNullOrEmpty(ls_class_on_reckoning))
            { ls_class_on_reckoning = "*"; }
            if (string.IsNullOrEmpty(ls_subj_code))
            { ls_subj_code = "*"; }
            if (string.IsNullOrEmpty(ls_class_on_mr))
            { ls_class_on_mr = "*"; }
            decimal charges = costs;

            //if (costs != 0)
            //{
            //    ldc_scale = Math.Round(charges / costs, 2);
            //}
            //else
            //{
            //    ldc_scale = 1;
            //    return "";
            //}
            ////提取病人明细的最大值

            if (string.IsNullOrEmpty(as_oper_code))
            {
                as_oper_code = gs_item_no.ToString();
            }
            amount = Math.Round(amount, 2);
            costs = Math.Round(costs, 2);
            charges = Math.Round(charges, 2);
            //打折检索开始
            // Getdiscount(string tpatient_id, string tvisit_id, string ls_item_class, string ls_item_code, string ls_item_spec, string ls_item_units, string ls_operator_code, decimal ldc_amount, decimal ldc_price, ref decimal ldec_discount_price, ref string ls_discount_demo)
            decimal ldec_discount_price = 0;
            string ls_discount_demo = "";
            PlatCommon.Common.PublicFunction.Getdiscount(pat_id, visit_id.ToString(), item_class, item_code, item_spec, units, PlatCommon.SysBase.SystemParm.LoginUser.ID, amount, ldc_price, ref ldec_discount_price, ref ls_discount_demo);
            decimal decDefault = (li_fee_coeff_num / li_fee_coeff_den);
            decimal decChargePrice = 0;
            //decimal ldc_calc_price = 0;
            if (specialIndicator.Equals("1"))
            {
                if (GetCalcChargePrice(chargeType, item_class, item_code, item_spec, ldc_price, ref decDefault, ref decChargePrice) < 0)
                {
                    XtraMessageBox.Show("计算使用价格错误!");
                    return "";
                }
            }
            else
                decChargePrice = decDefault * ldc_price;
            charges = decChargePrice * amount;
            charges = Math.Round(charges, 2);
            //打折检索结束
            string sql = "Insert Into inp_bill_detail(patient_id,visit_id,item_no,item_class,ITEM_NAME,";
            sql += "ITEM_CODE,ITEM_SPEC,UNITS,ORDERED_BY,PERFORMED_BY,ORDER_DOCTOR,";
            sql += "PERFORM_DOCTOR,PERFORM_GROUP,ORDER_GROUP,CLASS_ON_RECKONING,DOCTOR_USER,";
            sql += "WARD_CODE,CLASS_ON_INP_RCPT,SUBJ_CODE,CLASS_ON_MR,ITEM_PRICE,PRICE_QUOTIETY,DISCHARGE_TAKING_INDICATOR,";
            sql += "CLINIC_CODE,AMOUNT,COSTS,CHARGES,OPERATOR_NO,OPER_CODE,OPER_TYPE,DOCUMENT_NO,billing_date_time,discount_price ,discount_demo,his_unit_code)";
            sql += " Values ('" + pat_id + "','" + Convert.ToInt16(visit_id) + "','" + gs_item_no + "','" + item_class + "','" + item_name;
            sql += "','" + item_code + "','" + item_spec + "','" + units + "','" + ordered_by + "','" + performed_by + "','" + order_doctor;
            sql += "','" + "" + "','" + performed_by + "','" + order_group + "','" + ls_class_on_reckoning + "','" + ls_doctor_user;
            sql += "','" + deptCode + "','" + ls_class_on_inp_rcpt + "','" + ls_subj_code + "','" + ls_class_on_mr + "','" + ldc_price + "','" + ldc_scale + "','" + 0;
            sql += "','" + "" + "','" + amount + "','" + costs.ToString("F2") + "','" + charges.ToString("F2") + "','" + PlatCommon.SysBase.SystemParm.LoginUser.ID + "','" + as_oper_code + "','" + as_oper_type + "','" + "" + "',to_date('" + bill_date_time.ToString() + "','yyyy-mm-dd hh24:mi:ss'),'" + ldec_discount_price + "','" + ls_discount_demo + "','"+SystemParm.HisUnitCode+"')";

            return sql;
        }
        #endregion

        public static void WriteWrrLog(string str)
        {
            DateTime d = DateTime.Now;
            string path = AppDomain.CurrentDomain.BaseDirectory + "LOG/";
            string fileName = d.ToString("yyyy-MM-dd-HH") + ".txt";

            if (!Directory.Exists(path))
                Directory.CreateDirectory(path);

            using (StreamWriter file = new StreamWriter(path + fileName, true))
            {
                if (string.IsNullOrEmpty(str))
                    file.WriteLine("");
                else
                {
                    file.WriteLine(d.ToString("MM-dd HH:mm:ss") + " ==>> " + str);
                    //file.WriteLine(str);
                }
            }
        }


        /// <summary>
        /// 取用户对应图片
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="hisUnitcode"></param>
        /// <returns></returns>
        public static byte[] GetUserImage(string userName, string hisUnitcode)
        {
            string sql = "SELECT AUTOGRAPH FROM COMM.STAFF_DICT WHERE USER_NAME= '" + userName + "' and HIS_UNIT_CODE = '"+ hisUnitcode + "'";

            using (ServerPublicClient serv = new ServerPublicClient())
            {
                DataSet ds = serv.GetDataBySql(sql);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    if (ds.Tables[0].Rows[0][0] != DBNull.Value)
                        return (byte[])ds.Tables[0].Rows[0][0];
                }
                return new byte[0];
            }

        }


        /// <summary>
        /// 取科室名称
        /// </summary>
        /// <param name="deptCode"></param>
        /// <returns></returns>
        public static string GetDeptNameByCode(string deptCode, string hisUnitcode)
        {
            string sql = "SELECT DEPT_NAME FROM COMM.DEPT_DICT WHERE DEPT_CODE= '" + deptCode + "' and HIS_UNIT_CODE = '" + hisUnitcode + "'";
            using (ServerPublicClient serv = new ServerPublicClient())
            {
                DataSet ds = serv.GetDataBySql(sql);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    if (ds.Tables[0].Rows[0][0] != DBNull.Value)
                        return ds.Tables[0].Rows[0][0].ToString();
                }
                return String.Empty;
            }
        }

        public static int  InitAppConfigerParameterTable(string appCode,string deptCode)
        {
            //ServerPublicClient spc = new ServerPublicClient();
            //ArrayList arrSql = getInitAppConfigerParameterSql();

            //// 查询现有记录
            //string sql = "SELECT parameter_name FROM comm.app_configer_parameter t where t.app_name = 'NURADM' ";
            //DataSet ds = spc.GetDataBySql(sql, "app_configer_parameter", false);

            //string beginStr = "values ('"+ appCode + "',";
            //string stopStr = "',";
            //for (int i = arrSql.Count - 1; i >= 0; i--)
            //{
            //    // 获取主键名
            //    sql = arrSql[i].ToString();

            //    int pos0 = sql.IndexOf(beginStr);
            //    if (pos0 < 1) continue;

            //    pos0 += beginStr.Length;

            //    pos0 = sql.IndexOf("'", pos0);
            //    if (pos0 < 1) continue;
            //    pos0 += 1;

            //    int pos1 = sql.IndexOf(stopStr, pos0);
            //    if (pos1 < 1) continue;

            //    string menuName = sql.Substring(pos0, pos1 - pos0);

            //    // 如果存在, 不插入
            //    if (ds.Tables[0].Select("parameter_name = " + SQL.SqlConvert(menuName)).Length > 0)
            //    {
            //        arrSql.RemoveAt(i);
            //    }
            //}

            //spc.ExecuteSqlArray(arrSql);
            return 0;
        }
        #region 获取url接口配置字典表方法
        /// <summary>
        /// 获取url接口配置字典表方法
        /// </summary>
        /// <param name="config_code"></param>
        /// <param name="config_path"></param>
        /// <returns></returns>
        public static string GetInterfaceConfigDict(string config_code, ref string config_path)
        {
            try
            {
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                string sql = "select * from comm.interface_config_dict  a where a.config_code='" + config_code + "' and use_flag='1' and his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
                DataTable dt_data = spc.GetDataBySql(sql).Tables[0];
                if (dt_data.Rows.Count > 0)
                {
                    config_path = dt_data.Rows[0]["config_path"].ToString();
                    // parameter_order = dt_data.Rows[0]["parameter_order"].ToString();
                }
                else
                {
                    return "存在多个有启用的配置参数" + config_code;
                }
            }
            catch (Exception ex)
            {

                return "接口调用异常" + ex.Message;
            }
            return "";
        }
        #endregion
    }
}
