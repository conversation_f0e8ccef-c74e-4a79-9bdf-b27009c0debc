﻿//-----------------------------------------------------------------------
//  系统名称        : 基础类库
//  子系统名称      : 辅助类
//  功能概要        : ListView操作类
//  作  者          : 付军
//  创建时间        : 2016-10-28
//  版本            : 1.0.0.0
//-----------------------------------------------------------------------
using System.Windows.Forms;
using System.Data;

namespace PlatCommon.Common
{

    /// <summary>
    /// ListView操作类
    /// </summary>
    public class ListViewHelper
    {
        /// <summary>
        /// 设置DataGridView的属性
        /// </summary>
        /// <param name="lstV"></param>
        /// <param name="dvSrc"></param>
        /// <param name="displayMember"></param>
        /// <param name="valueMember"></param>
        public static void DataBind(ListView lstV, DataView dvSrc, string displayMember, string valueMember)
        {
            lstV.Items.Clear();

            foreach (DataRowView drv in dvSrc)
            {
                ListViewItem item = new ListViewItem();
                item.Text = drv[displayMember].ToString();
                item.Tag = drv[valueMember];

                lstV.Items.Add(item);
            }
        }
    }
}
