﻿using System;
using System.Data;
using System.ServiceModel;
namespace INMService
{
    /// <summary>
    /// 接口层MODELS
    /// </summary>
    [ServiceContract]
    public interface IMODELS
    {
        #region  成员方法
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        [OperationContract]
        bool Exists_MODELS(string CODE);
        /// <summary>
        /// 增加一条数据
        /// </summary>
        [OperationContract]
        bool Add_MODELS(Model.MODELS model);
        /// <summary>
        /// 更新一条数据
        /// </summary>
        [OperationContract]
        bool Update_MODELS(Model.MODELS model);
        /// <summary>
        /// 删除数据
        /// </summary>
        [OperationContract]
        bool Delete_MODELS(string CODE);
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        [OperationContract]
        Model.MODELS GetModel_MODELS(string CODE);
        /// <summary>
        /// 获得数据列表
        /// </summary>
        [OperationContract]
        DataSet GetList_All_MODELS(string strWhere);
        /// <summary>
        /// 获得前几行数据
        /// </summary>
        [OperationContract]
        DataSet GetList_MODELS(int startIndex, int endIndex, string strWhere, string filedOrder);
        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.MODELS> GetObservableCollection_All_MODELS(string strWhere);
        /// <summary>
        /// 获得ObservableCollection根据分页获得数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.MODELS> GetObservableCollection_MODELS(int startIndex, int endIndex, string strWhere, string filedOrder);
        #endregion  成员方法
    }
}