﻿using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Layout;
using DevExpress.XtraGrid.Views.Layout.Events;
using DevExpress.XtraGrid.Views.Layout.ViewInfo;
using PlatCommon.SysBase;
using PlatCommonForm;
using PlatPublic.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Tjhis.Interface.Station;

namespace TJHisPlat
{
    /// <summary>
    /// 模块选择界面
    /// </summary>
    public partial class frmLayoutView : XtraForm
    {
        DataTable dt_main = null;
        int li_count = 0;
        DevExpress.XtraGrid.Views.Layout.ViewInfo.LayoutViewHitInfo m_DownHitInfo_TuXing = null;
        public frmLayoutView()
        {
            InitializeComponent();
        }
        public frmLayoutView(DataTable dt)
        {
            InitializeComponent();
            dt_main=dt.Copy();         
        }
  
        private void InitData(DataTable dt)
        {
            DataTable table = dt.Copy();
            table.Columns.Remove("deptcode");
            table.Columns.Remove("imageindex");
            table.Columns.Remove("deptname");
            table.Columns.Add("deptcode");
            table.Columns.Add("deptname");
            table.Columns.Add("IMAGE", typeof(System.Byte[]));
            // 假设table中包含重复数据
            IEnumerable<DataRow> rows = table.AsEnumerable();
            IEnumerable<DataRow> distinctRows = rows.Distinct(DataRowComparer.Default);
            DataTable resultTable = distinctRows.CopyToDataTable();
            string pathImage = System.IO.Directory.GetCurrentDirectory() + @"\Images\System\SelectUI\PRCMGR.png";
            resultTable.Columns.Add("CLASS_FLAG");
            byte[] newmale = ImageHelper.ImageToBytes(pathImage, System.Drawing.Imaging.ImageFormat.Png);
            //循环加载图片 
            DataTable dt_class = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select * from comm.applications ").Tables[0];
            foreach (DataRow dr in resultTable.Rows)
            {
                string appcode = dr["appcode"].ToString();                  
                pathImage = System.IO.Directory.GetCurrentDirectory() + @"\Images\System\SelectUI\" + appcode + ".png";
                try
                {
                    newmale = ImageHelper.ImageToBytes(pathImage, System.Drawing.Imaging.ImageFormat.Png);
                }
                catch
                { }
                dr["IMAGE"] = newmale;
                DataRow[] drs = dt_main.Select("appcode ='" + appcode + "'");
                string deptcode = drs[0]["deptcode"].ToString();
                string deptname = drs[0]["deptname"].ToString();
                dr["deptcode"] = deptcode;
                DataRow[] dra = dt_class.Select("application ='" + appcode + "'");
                if (dra.Length > 0)
                {
                    dr["CLASS_FLAG"] = dra[0]["CLASS_FLAG"].ToString();
                }
               
                if (drs.Length == 1)
                {
                    dr["deptname"] = deptname;
                }
                else
                {
                    dr["deptname"] = deptname;
                }
            }
            resultTable.DefaultView.Sort = "CLASS_FLAG asc";
            var dv = resultTable.DefaultView.ToTable();
            gridControl1.DataSource = dv;
        }
        private void frmLayoutView_Load(object sender, EventArgs e)
        {

            InitData(dt_main.Copy());
            //20200904天健危急值IM工具启动--mchi
            PlatCommon.Common.CriticalValueBusiness.DoBusiness("1");
        }

        private void layoutView1_CustomRowCellEdit(object sender, DevExpress.XtraGrid.Views.Layout.Events.LayoutViewCustomRowCellEditEventArgs e)
        {
            if (e.Column.FieldName == "deptname" && e.RowHandle == layoutView1.FocusedRowHandle)
            {
                string appcode = layoutView1.GetRowCellValue(e.RowHandle, "appcode").ToString();
                DataRow[] dra = dt_main.Select("appcode='" + appcode + "'");
                if (dra.Length == 1)//只有一个科室直接进入
                {
                    e.RepositoryItem = this.TextEditlist;
                   
                }
                else
                {
                    DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repositoryItem = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
                    repositoryItem.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
                    new DevExpress.XtraEditors.Controls.LookUpColumnInfo("deptname", "科室", 5, DevExpress.Utils.FormatType.None, "", true, DevExpress.Utils.HorzAlignment.Center, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.Default)});
                    repositoryItem.DataSource = (DataTable)dra.CopyToDataTable();
                    repositoryItem.ValueMember = "deptname";
                    repositoryItem.DisplayMember = "deptname";
                    repositoryItem.ShowHeader = false;
                    repositoryItem.PopupWidth = 5;
                    if (dra.Length < 9)
                    {
                        repositoryItem.DropDownRows = 10;
                    }
                    else
                    {
                        repositoryItem.DropDownRows = dra.Length + 1;
                    }
      
                    repositoryItem.EditValueChanged += LookUp_EditValueChanged;
                    e.RepositoryItem = repositoryItem;

                }
            }

        }


        private void LookUp_EditValueChanged(object sender, EventArgs e)
        {
            layoutView1.RefreshData();
            layoutView1.CloseEditor();
 
            LookUpEdit LookupEdit = sender as LookUpEdit;
            DataRowView SelectedDataRow = (DataRowView)LookupEdit.GetSelectedDataRow();
            string deptcode = SelectedDataRow.Row["deptcode"].ToString();
            string appcode = SelectedDataRow.Row["appcode"].ToString();

            DataRow[] rows = dt_main.Select("appcode ='" + appcode + "' and deptcode='" + deptcode + "' ");
            OpenMain(rows[0]);
        }
        /// <summary>
        /// 打开主窗体
        /// </summary>
        /// <param name="dr"></param>
        private void OpenMain(DataRow dr)
        {
             
            if (dr == null) return;
            //1、初始化模块参数设置表
            ParameterSetting.InitParameterSetting(dr["appcode"].ToString(), dr["deptcode"].ToString(), PlatCommon.SysBase.SystemParm.HisUnitCode);//参数初始化入口
           
            #region 注释说明：医生站合理用药初始化的代码迁移到医生站患者列表中进行加载 20240410
            //住院医生站合理用药
            //if (dr["appcode"].ToString().Equals("DOCTWS"))
            //{
            //    //合理用药初始化
            //    RationalAdministration.Init(CurrentUser.UserName);

            //}
            #endregion

            //2、创建模块的打开主窗体
            FrmNewMain frmfnm = new FrmNewMain();

            //门诊医生站特殊处理-获取医生出诊诊室
            if (dr["appcode"].ToString().Equals("OUTPDOCT"))
            {
                string doctorQueueName = ""; //出诊诊室
                // 是否启用分诊台
                string counseltypeNo ; //启用分诊台（1-启用，0-不启用）
                string counselno = string.Empty; //分诊台编号
                string ls_loginmode = PlatCommon.SysBase.SystemParm.GetParameterValue("CONSULATION_ENABLED", "OUTPDOCT", dr["deptcode"].ToString(), "*", SystemParm.HisUnitCode);
                if (ls_loginmode.Equals("1"))
                {
                    counseltypeNo = "1";
                }
                else
                {
                    counseltypeNo = "0";
                }
                StringBuilder sqla = new StringBuilder();
                if (counseltypeNo.Equals("1"))
                {
                    //此刻 counselno 分诊台应该获取值，预留吧，原来是查OUTPADM.OUTP_COUNSEL_DEPT.COUNSEL_NO 
                    //据说以后的分诊，再上线,改动了,因此此参数预留给新分诊模式
                }
                else
                {
                    //不启用分诊台
                    NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                    Int32 li_day_nbr = 0;
                    string sql0 = " Select to_number(to_char(sysdate,'D'))  From dual";
                    DataTable dt0 = spc.GetDataBySql(sql0.ToString()).Tables[0];
                    // 确定周次
                    if (dt0 != null && dt0.Rows.Count > 0)
                    {
                        li_day_nbr = Convert.ToInt32(dt0.Rows[0][0].ToString());
                        if (li_day_nbr.ToString().Equals("1"))
                        {
                            li_day_nbr = 7;
                        }
                        else
                        { li_day_nbr--; }
                    }
                    sqla.Append(" SELECT OUTP_DOCTOR_SCHEDULE.COUNSEL_NO, ");
                    sqla.Append(" OUTP_DOCTOR_SCHEDULE.CLINIC_DEPT,");
                    sqla.Append(" (select dept_name from dept_dict where dept_code = OUTP_DOCTOR_SCHEDULE.CLINIC_DEPT) DEPT_NAME,");
                    sqla.Append(" OUTP_DOCTOR_SCHEDULE.DOCTOR,");
                    sqla.Append(" OUTP_DOCTOR_SCHEDULE.DOCTOR_NO,");
                    sqla.Append(" OUTP_DOCTOR_SCHEDULE.DAY_OF_WEEK,");
                    sqla.Append(" OUTP_DOCTOR_SCHEDULE.CLINIC_DURATION,");
                    sqla.Append(" OUTP_DOCTOR_SCHEDULE.QUEUE_NAME,");
                    sqla.Append(" OUTP_DOCTOR_SCHEDULE.AUTO_ASSIGN_PATIENT,");
                    sqla.Append(" sysdate as COUNSEL_DATE ");
                    sqla.Append(" FROM OUTP_DOCTOR_SCHEDULE");
                    sqla.Append(" WHERE  (OUTP_DOCTOR_SCHEDULE.DOCTOR_NO = '" + PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME + "')");
                    sqla.Append(" And (OUTP_DOCTOR_SCHEDULE.DAY_OF_WEEK = '" + li_day_nbr + "')");
                    sqla.Append(" and (OUTP_DOCTOR_SCHEDULE.CLINIC_DEPT = '" + dr["deptcode"].ToString() + "')");
                    DataTable dtods = spc.GetDataBySql(sqla.ToString()).Tables[0];
                    if (dtods != null && dtods.Rows.Count > 0)
                    {                        
                        if(dtods.Rows.Count == 1)
                        {
                            //单个出诊诊室，直接取值
                            doctorQueueName = dtods.Rows[0]["QUEUE_NAME"].ToString();
                        }
                        else
                        {
                            //该科室有多个出诊诊室，弹出窗口选择诊室
                            FrmSelectOutpDoctorS frm = new FrmSelectOutpDoctorS();
                            frm.gs_dtOutpDs = dtods;
                            if (frm.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                            {
                                doctorQueueName = frm.Doctor_QueueName;
                            }
                        }                        
                    }
                    else
                    {
                        StringBuilder sba = new StringBuilder();
                        sba.Append(" SELECT distinct CLINIC_INDEX.CLINIC_LABEL ");
                        sba.Append("  FROM CLINIC_FOR_REGIST,  CLINIC_INDEX    ");
                        sba.Append("  WHERE ( CLINIC_FOR_REGIST.CLINIC_LABEL = CLINIC_INDEX.CLINIC_LABEL ) and   ");
                        sba.Append("   ( CLINIC_FOR_REGIST.CLINIC_DATE = trunc(sysdate) )AND ");
                        sba.Append("    ( CLINIC_INDEX.CLINIC_DEPT = '" + dr["deptcode"].ToString() + "' ) AND  ");
                        sba.Append(" ( CLINIC_INDEX.DOCTOR = '" + PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME + "')");
                        DataTable dta = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sba.ToString()).Tables[0];
                        if (dta != null && dta.Rows.Count > 0)
                        {
                            doctorQueueName = dta.Rows[0][0].ToString();
                        }
                        else
                        {
                            doctorQueueName = "";
                        }
                    }
                }
                frmfnm.QueueName = doctorQueueName; //将诊室封装在主窗体中
            }

            //3、主窗体变量赋值   
            frmfnm.AppCode = dr["appcode"].ToString();
            frmfnm.AppName = dr["appname"].ToString();
            frmfnm.DeptCode = dr["deptcode"].ToString();
            frmfnm.DeptName = dr["deptname"].ToString();
            frmfnm.WardCode = dr["deptcode"].ToString();
            frmfnm.WardName = dr["deptname"].ToString();
            frmfnm.EncryptCode = dr["Encrypt_Code"].ToString();
            InitFrmNewMain(frmfnm, frmfnm.AppCode);
            if (frmfnm.AppCode.Equals("NURSWS"))
            {
                //护士站的科室列表即是病区列表
                frmfnm.DeptList = frmfnm.WardList;
            }

            // 增加判断已打开相同模块的相同科室的情况下不让再重复打开
            FormCollection fc = Application.OpenForms;
            foreach (Form f in fc)
            {
                try
                {
                    if (f != null && ((PlatCommonForm.FrmNewMain)f).AppCode == frmfnm.AppCode && ((PlatCommonForm.FrmNewMain)f).DeptCode == frmfnm.DeptCode)
                    {
                        MessageBox.Show("有其它相同窗体正在运行，请先将其关闭！");
                        return;
                    }
                }
                catch
                {

                }
            }
            // 增加判断已打开相同模块的相同科室的情况下不让再重复打开

            frmfnm.Show();
            this.WindowState = FormWindowState.Minimized;
            //this.Hide();
        }

   

        private void layoutView1_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            DataRow dr = layoutView1.GetFocusedDataRow();
            if (dr == null) return;
            string deptname = dr["deptname"].ToString();
            if (deptname.Equals("进入系统"))
            {
                layoutView1.OptionsBehavior.Editable = false;
            }
            else
            {
                layoutView1.OptionsBehavior.Editable = true;
            }
        }

        private void layoutView1_DoubleClick(object sender, EventArgs e)
        {
            this.Cursor = Cursors.WaitCursor;
            DataRow dr = layoutView1.GetFocusedDataRow();
            if (dr == null) return;
            string deptcode = dr["deptcode"].ToString();
            string appcode = dr["appcode"].ToString();

            LookUpEdit lookUpEdit = layoutView1.ActiveEditor as LookUpEdit;
            if (lookUpEdit != null)
            {
                DataRowView drLookupEdit = lookUpEdit.GetSelectedDataRow() as DataRowView;
                if (drLookupEdit != null)
                {
                    deptcode = drLookupEdit["deptcode"].ToString();
                }
            }
            DataRow[] rows = dt_main.Select("appcode ='" + appcode + "' and deptcode='" + deptcode + "' ");
            OpenMain(rows[0]);

            this.Cursor = Cursors.WaitCursor;
        }

        private void layoutView1_MouseDown(object sender, MouseEventArgs e)
        {
        }

        private void frmLayoutView_FormClosing(object sender, FormClosingEventArgs e)
        {
            bool isOpen = false;
            foreach (Form frm in Application.OpenForms) //遍历已打开窗口
            {
                if (frm is FrmNewMain)
                {
                    isOpen = true;
                    break;
                }
            }
            if (isOpen)
            {
                e.Cancel = true;
                MessageBox.Show("有其它窗体正在运行，请先将其关闭！");
            }
            //20200904天健危急值IM工具关闭--mchi
            PlatCommon.Common.CriticalValueBusiness.DoBusiness("0");
            
            //关闭ca
            if (PlatCommon.SysBase.SystemParm.CaBusiness != null)
                PlatCommon.SysBase.SystemParm.CaBusiness.CALogOut(PlatCommon.SysBase.SystemParm.LoginUser.ID);
        }

        private void layoutView1_CustomCardLayout(object sender, DevExpress.XtraGrid.Views.Layout.Events.LayoutViewCustomCardLayoutEventArgs e)
        {
            int hand = e.RowHandle;
            if (hand < 0) return;
            DataRow dr = layoutView1.GetDataRow(hand);
            if (dr == null) return;
            string appcode = dr["appcode"].ToString();
            DataRow[] drs = dt_main.Select($"appcode = '{appcode}'");
            if (drs.Length == 1)
            {
                string colName = this.DEPTNAME.LayoutViewField.Name;

                e.CardDifferences.AddItemDifference(colName,
                   LayoutItemDifferenceType.ItemVisibility, false);
            }
        }
        bool countItemInRow = false;
        bool locked;
        bool forward;
        int visibleIndex;
        int itemsInRow;
        private void layoutView1_VisibleRecordIndexChanged(object sender, LayoutViewVisibleRecordIndexChangedEventArgs e)
        {
            LayoutView layoutView = sender as LayoutView;
            LayoutViewInfo viewInfo = layoutView.GetViewInfo() as LayoutViewInfo;

            if (!countItemInRow)
            {
                countItemInRow = true;
                int visibleCards = viewInfo.VisibleCards.Count;
                if (visibleCards == 0) return;
                int firstRow = viewInfo.VisibleCards[0].VisibleRow;
                int lastRow = viewInfo.VisibleCards[viewInfo.VisibleCards.Count - 1].VisibleRow;
                int rowCount = lastRow - firstRow + 1;
                itemsInRow = visibleCards / rowCount;
            }

            if (locked) return;
            locked = true;
            forward = e.PrevVisibleRecordIndex < e.VisibleRecordIndex? true : false;
            layoutView.VisibleRecordIndex = e.PrevVisibleRecordIndex;
            if (forward)
            {
                layoutView.VisibleRecordIndex = e.PrevVisibleRecordIndex + itemsInRow;
            }
            else
            {                                    
                layoutView.VisibleRecordIndex = e.PrevVisibleRecordIndex - itemsInRow;
            }

            locked = false;
            visibleIndex = layoutView.VisibleRecordIndex;
        }

        private void frmLayoutView_Resize(object sender, EventArgs e)
        {
            countItemInRow = false;
        }
        
        /// <summary>
        /// 初始化主窗体的科室列表和病区列表
        /// </summary>        
        public void InitFrmNewMain(FrmNewMain frm, string appCode)
        {            
            string sqlstr = string.Empty; 
            if ("DOCTWS".Equals(appCode.ToUpper()) || appCode.ToUpper().Equals("EMRINP"))
            {
                sqlstr = "select a.group_code,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and a.group_class = '病区医生' and emp_no = '" + PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO + "'  and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";                               
            }
            else if ("DISINFECT".Equals(appCode.ToUpper()))
            {
                sqlstr = "select a.group_code,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and a.group_class = '供应室管理' and emp_no = '" + PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO + "'  and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";               
            }
            else if ("NURSWS".Equals(appCode.ToUpper()) || "NURINPREC".Equals(appCode.ToUpper()))
            {
                sqlstr = "select a.group_code,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and a.group_class = '病区护士' and emp_no = '" + PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO + "'  and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
            }
            else if ("NUROB".Equals(appCode.ToUpper()))
            {
                sqlstr = "select a.group_code,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and a.group_class = '留观护士' and emp_no = '" + PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO + "'  and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";                
            }
            else if ("PHSTOCK".Equals(appCode.ToUpper()))
            {
                sqlstr = "select a.group_code,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and a.group_class = '药库管理' and emp_no = '" + PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO + "'  and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";               
            }
            else if ("STOCKMGR".Equals(appCode.ToUpper()) || "PRESDISP".Equals(appCode) || "ORDDISP".Equals(appCode))
            {
                sqlstr = "select a.group_code,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and a.group_class = '药房管理' and emp_no = '" + PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO + "'  and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";                
            }
            else if ("OUTPDOCT".Equals(appCode))
            {
                sqlstr = "select a.group_code,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and a.group_class = '门诊医生' and emp_no = '" + PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO + "'  and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";              
            }           
            else if ("OPERBILL".Equals(appCode.ToUpper()))
            {
                sqlstr = "select a.group_code,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and (a.group_class = '手术医生' or a.group_class = '麻醉医生') and emp_no = '" + PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO + "'  and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";               
            }
            else if ("EXSTOCK".Equals(appCode.ToUpper()))
            {
                sqlstr = "select a.group_code,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and a.group_class = '耗材管理' and emp_no = '" + PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO + "'  and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";               
            }
            else if ("EQUIP".Equals(appCode.ToUpper()))
            {
                sqlstr = "select a.group_code,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and a.group_class = '设备管理' and emp_no = '" + PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO + "'  and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";               
            }
            else if ("EXAMAPT".Equals(appCode.ToUpper()))//检查确认选择科室界面
            {
                sqlstr = "select a.group_code,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and a.group_class = '检查医生' and emp_no = '" + PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO + "'  and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";                
            }               
            else
            {
                //默认取登录人所在科室
                sqlstr = "select b.dept_code group_code, b.dept_name from dept_dict b where dept_code = '" + PlatCommon.SysBase.SystemParm.LoginUser.DEPT_CODE + "'";
            }
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            System.Data.DataSet ds = spc.GetDataBySql(sqlstr);
            if (ds == null || ds.Tables.Count == 0 || ds.Tables[0].Rows.Count == 0)
            {
                return;
            }
            int count = ds.Tables[0].Rows.Count;
            if (count == 1)
            {
                //仅有一个科室,直接给主窗体赋值
                if (frm != null)
                {
                    frm.DeptList = "'" + ds.Tables[0].Rows[0]["group_code"].ToString() + "'";
                    frm.WardList = "'" + PlatCommon.Common.PublicFunction.GetWarcodeVsDept(ds.Tables[0].Rows[0]["group_code"].ToString()) + "'";
                }
            }
            else
            {
                //多个科室
                String ret = "", ret1 = "";
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    string wardCodeVsDept; //科室对应病区编码
                    foreach (System.Data.DataRow dr in ds.Tables[0].Rows)
                    {
                        wardCodeVsDept = string.Empty;
                        ret += "'" + dr[0] + "',";
                        wardCodeVsDept = PlatCommon.Common.PublicFunction.GetWarcodeVsDept(dr[0].ToString());
                        if (!string.IsNullOrEmpty(wardCodeVsDept))
                        {
                            ret1 += "'" + wardCodeVsDept + "',";
                        }
                    }
                    if (!"".Equals(ret))
                        ret = ret.Remove(ret.Length - 1);
                    if (!"".Equals(ret1))
                        ret1 = ret1.Remove(ret1.Length - 1);
                }
                if (frm != null)
                {
                    frm.DeptList = ret;
                    frm.WardList = ret1;
                }
            }            
        }
    }
}
