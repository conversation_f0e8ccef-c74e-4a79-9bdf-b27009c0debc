﻿/*********************************************
* 文 件 名：Cs02MyEval
* 类 名 称：Cs02MyEval
* 功能说明：动态编译执行字符串形式表达式类
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：尹志伟
* 创建时间：2016-10-28
* 版 本 号：1.0.0.1
* 修改时间：尹志伟
* 修 改 人：2018-06-12
* CLR 版本：4.0.30319.42000
/*********************************************/

using Microsoft.CSharp;
using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace PlatCommon.Base02
{
    /// <summary>
    /// 动态编译执行字符串形式表达式类
    /// </summary>
    public class Cs02MyEval
    {
        /// <summary>
        /// CodeDom动态编译执行表达式
        /// </summary>
        /// <param name="expression">表达式字符串</param>
        /// <returns></returns>
        public static object Eval(string expression)
        {
            //创建编译器
            CSharpCodeProvider comp = new CSharpCodeProvider();             
            CompilerParameters paramerts = new CompilerParameters();

            //设置源代码
            StringBuilder objBuild = new StringBuilder();
            objBuild.Append("using System; \n");
            objBuild.Append("namespace Aptech.Showlin._temp { \n");
            objBuild.Append("  public class _evalTemp { \n");
            objBuild.Append("    public object _get() ");
            objBuild.Append("{ ");
            objBuild.AppendFormat("      return ({0}); ", expression);
            objBuild.Append("}\n");
            objBuild.Append("} }");

            //动态编译
            CompilerResults cr = comp.CompileAssemblyFromSource(paramerts, objBuild.ToString());

            System.Reflection.Assembly objAss = cr.CompiledAssembly;
            object o = objAss.CreateInstance("Aptech.Showlin._temp._evalTemp");
            System.Reflection.MethodInfo mi = o.GetType().GetMethod("_get");
            return mi.Invoke(o, null);
        }

        /// <summary>
        /// 利用Dataset的Express表达式，但仅仅支持sql表达式
        /// </summary>
        /// <param name="expression">SQL表达式</param>
        /// <returns></returns>
        public static object EvalSql(string expression)
        {
            System.Data.DataTable dt = new System.Data.DataTable();
            System.Data.DataColumn dc = new System.Data.DataColumn("temp", typeof(string), expression);
            dt.Columns.Add(dc);
            dt.Rows.Add(dt.NewRow());
            return dt.Rows[0][0];
        }

        /// <summary>
        /// CodeDom动态编译执行判断逻辑的表达式,返回判断结果
        /// </summary>
        /// <param name="expression">判断表达式</param>
        /// <returns></returns>
        public static bool EvalBoolean(string expression)
        {
            Object obj = Eval(expression);
            Boolean bl = (bool)obj;
            return bl;
        }
    }
}
