﻿/*********************************************
* 文 件 名：Cs02JsonHelper
* 类 名 称：Cs02JsonHelper
* 功能说明：Json类
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：刘成刚
* 创建时间：2019-05-18 19:41:55
* 版 本 号：*******
* 修 改 人：刘成刚
* 修改时间：2020-02-18
* CLR 版本：4.0.30319.42000
/*********************************************/

using System;
using System.Text;
using System.IO;
using System.Data;
using System.Web.Script.Serialization;
using System.Runtime.Serialization.Json;
using System.Collections.Generic;
using System.Collections;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace PlatCommon.Base02
{
    /// <summary>
    /// Json类
    /// </summary>
    public static class Cs02JsonHelper
    {
        /// <summary>
        /// Json序列化
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">数据对象</param>
        /// <returns></returns>
        public static string JsonSerializer<T>(T obj)
        {
            if (obj == null) return string.Empty;

            string strJsonString = string.Empty;
            try
            {
                DataContractJsonSerializer serializer = new DataContractJsonSerializer(typeof(T));
                using (MemoryStream ms = new MemoryStream())
                {
                    serializer.WriteObject(ms, obj);
                    strJsonString = Encoding.UTF8.GetString(ms.ToArray());
                }
            }
            catch
            {
                strJsonString = string.Empty;
            }
            return strJsonString;
        }

        /// <summary>
        /// 将 DataTable 序列化成 json 字符串
        /// </summary>
        /// <param name="dtData">数据表</param>
        /// <returns></returns>
        public static string DataTableToJson(DataTable dtData)
        {
            if (dtData == null || dtData.Rows.Count == 0)
            {
                return "\"\"";
            }

            JavaScriptSerializer myJson = new JavaScriptSerializer();
            List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();
            foreach (DataRow drCurrentRow in dtData.Rows)
            {
                Dictionary<string, object> DicResult = new Dictionary<string, object>();
                foreach (DataColumn dcCurrentColumn in dtData.Columns)
                {
                    DicResult.Add(dcCurrentColumn.ColumnName, drCurrentRow[dcCurrentColumn].ToString());
                }
                list.Add(DicResult);
            }
            return myJson.Serialize(list);
        }

        /// <summary>
        /// 将对象序列化成 json 字符串
        /// </summary>
        /// <param name="obj">数据对象</param>
        /// <returns></returns>
        public static string ObjectToJson(object obj)
        {
            if (obj == null) return string.Empty;

            JavaScriptSerializer myJson = new JavaScriptSerializer();
            return myJson.Serialize(obj);
        }

        /// <summary>
        /// Json反序列化
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="strJson">Json字符串</param>
        /// <returns></returns>
        public static T JsonDeserialize<T>(string strJson)
        {
            if (string.IsNullOrEmpty(strJson)) return default(T);

            T obj = Activator.CreateInstance<T>();
            try
            {
                using (MemoryStream myMemoryStream = new MemoryStream(Encoding.UTF8.GetBytes(strJson)))
                {
                    DataContractJsonSerializer ser = new DataContractJsonSerializer(obj.GetType());
                    T jsonObject = (T)ser.ReadObject(myMemoryStream);
                    myMemoryStream.Close();
                    return jsonObject;
                }
            }
            catch
            {
                return default(T);
            }
        }

        /// <summary>
        /// 将 json 字符串反序列化成对象
        /// </summary>
        /// <param name="strJson">Json字符串</param>
        /// <returns></returns>
        public static object JsonToObject(string strJson)
        {
            if (string.IsNullOrEmpty(strJson)) return null;

            JavaScriptSerializer myJson = new JavaScriptSerializer();
            return myJson.DeserializeObject(strJson);
        }

        /// <summary>
        /// 将 json 字符串反序列化成对象
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="strJson">Json字符串</param>
        /// <returns></returns>
        public static T JsonToObject<T>(string strJson)
        {
            if (string.IsNullOrEmpty(strJson)) return default(T);

            JavaScriptSerializer myJson = new JavaScriptSerializer();
            return myJson.Deserialize<T>(strJson);
        }

        /// <summary>
        /// Json字符串转换为 DataTable,一层数据
        /// </summary>
        /// <param name="strJson">Json字符串</param>
        /// <param name="bColConstant">Json列是否确定</param>
        /// <returns></returns>
        public static DataTable JsonToDataTable(string strJson, Boolean bColConstant = true)
        {
            //结果实例化
            DataTable dtResult = new DataTable();

            if (!strJson.StartsWith("["))
                strJson = "[" + strJson;

            if (!strJson.EndsWith("]"))
                strJson += "]";

            strJson = strJson.Replace("null", "\"\"");
            if (string.IsNullOrEmpty(strJson)) return dtResult;

            ArrayList myArrayList;
            try
            {
                //数据处理成ArrayList
                JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();
                javaScriptSerializer.MaxJsonLength = Int32.MaxValue; //取得最大数值
                myArrayList = javaScriptSerializer.Deserialize<ArrayList>(strJson);
            }
            catch (Exception)
            {
                return dtResult;
            }

            //木有数据
            if (myArrayList.Count < 1) return dtResult;

            ArrayList myColumnList = new ArrayList();
            foreach (Dictionary<string, object> myDictionary in myArrayList)
            {
                //Key集合木有内容
                if (myDictionary.Keys.Count == 0) return dtResult;

                //处理列，当列固定时，只需要处理一次；当列不固定时，后面动态处理
                if ((bColConstant) && (dtResult.Columns.Count == 0))
                {
                    foreach (string strKey in myDictionary.Keys)
                    {
                        if (myDictionary[strKey] == null)
                            dtResult.Columns.Add(strKey, Type.GetType("System.String"));
                        else
                            dtResult.Columns.Add(strKey, myDictionary[strKey].GetType());

                        myColumnList.Add(strKey);
                    }
                }

                //处理行，循环添加行到DataTable中
                DataRow drNew = dtResult.NewRow();
                foreach (string strKey in myDictionary.Keys)
                {
                    //当列不固定时，处理列
                    if ((!bColConstant) && (!myColumnList.Contains(strKey)))
                    {
                        if (myDictionary[strKey] == null)
                            dtResult.Columns.Add(strKey, Type.GetType("System.String"));
                        else
                            dtResult.Columns.Add(strKey, myDictionary[strKey].GetType());

                        myColumnList.Add(strKey);
                    }

                    //对列赋值
                    if (myColumnList.Contains(strKey))
                        drNew[strKey] = myDictionary[strKey];
                }
                dtResult.Rows.Add(drNew);
            }

            return dtResult;
        }

        /// <summary>
        /// Json字符串转换为 DataTable,二层数据
        /// </summary>
        /// <param name="strJson">Json字符串</param>
        /// <param name="strDetailColumn">明细项的名称</param>
        /// <param name="bColConstant">Json列是否确定</param>
        /// <returns></returns>
        public static DataTable JsonToDataTableMult(string strJson, string strDetailColumn, Boolean bColConstant = true)
        {
            //结果实例化
            DataTable dtResult = new DataTable();
            strJson = strJson.Replace("null", "\"\"");

            if (string.IsNullOrEmpty(strJson)) return dtResult;

            //数据处理成ArrayList
            ArrayList myArrayList;
            try
            {
                JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();
                javaScriptSerializer.MaxJsonLength = Int32.MaxValue; //取得最大数值
                myArrayList = javaScriptSerializer.Deserialize<ArrayList>(strJson);
            }
            catch (Exception)
            {
                return dtResult;
            }

            //木有数据
            if (myArrayList.Count < 1) return dtResult;

            ArrayList myColumnList = new ArrayList();
            foreach (Dictionary<string, object> myDictionary in myArrayList)
            {
                //Key集合木有内容
                if (myDictionary.Keys.Count == 0) return dtResult;

                //处理列，当列固定时，只需要处理一次；当列不固定时，后面动态处理
                if ((bColConstant) && (dtResult.Columns.Count == 0))
                {
                    foreach (string strKey in myDictionary.Keys)
                    {
                        if (strKey != strDetailColumn)
                        {
                            //处理主表列
                            if (myDictionary[strKey] == null)
                                dtResult.Columns.Add(strKey, Type.GetType("System.String"));
                            else
                                dtResult.Columns.Add(strKey, myDictionary[strKey].GetType());

                            myColumnList.Add(strKey);
                        }
                        else
                        {
                            //处理子表列
                            ArrayList myDetailArrayList = myDictionary[strKey] as ArrayList;
                            foreach (Dictionary<string, object> myDetailDictionary in myDetailArrayList)
                            {
                                foreach (string strDetailKey in myDetailDictionary.Keys)
                                {
                                    if (myDetailDictionary[strDetailKey] == null)
                                        dtResult.Columns.Add(strDetailKey, Type.GetType("System.String"));
                                    else
                                        dtResult.Columns.Add(strDetailKey, myDetailDictionary[strDetailKey].GetType());

                                    myColumnList.Add(strDetailKey);
                                }
                                break;
                            }
                        }
                    }
                }

                //处理行，循环添加行到DataTable中
                Boolean bDetailAdd = false;
                DataRow drNew = dtResult.NewRow();
                foreach (string strKey in myDictionary.Keys)
                {
                    if (strKey == strDetailColumn)
                    {
                        //处理明细项数据
                        ArrayList myDetailArrayList = myDictionary[strKey] as ArrayList;
                        foreach (Dictionary<string, object> myDetailDictionary in myDetailArrayList)
                        {
                            DataRow drDetailNew = dtResult.NewRow();

                            //从主表数据赋值
                            drDetailNew.ItemArray = drNew.ItemArray;

                            //处理子表数据
                            foreach (string strDetailKey in myDetailDictionary.Keys)
                            {
                                if ((!bColConstant) && (!myColumnList.Contains(strDetailKey)))
                                {
                                    if (myDetailDictionary[strDetailKey] == null)
                                        dtResult.Columns.Add(strDetailKey, Type.GetType("System.String"));
                                    else
                                        dtResult.Columns.Add(strDetailKey, myDetailDictionary[strDetailKey].GetType());

                                    myColumnList.Add(strDetailKey);
                                }

                                if (myColumnList.Contains(strDetailKey))
                                    drDetailNew[strDetailKey] = myDetailDictionary[strDetailKey];

                            }

                            //加入结果表中
                            dtResult.Rows.Add(drDetailNew);
                            bDetailAdd = true;
                        }
                    }
                    else
                    {
                        //处理主表数据
                        if (!bColConstant && (!myColumnList.Contains(strKey)))
                        {
                            if (myDictionary[strKey] == null)
                                dtResult.Columns.Add(strKey, Type.GetType("System.String"));
                            else
                                dtResult.Columns.Add(strKey, myDictionary[strKey].GetType());

                            myColumnList.Add(strKey);
                        }

                        if (myColumnList.Contains(strKey))
                            drNew[strKey] = myDictionary[strKey];
                    }
                }

                //如果没有明细项目，加上主数据
                if (!bDetailAdd)
                {
                    dtResult.Rows.Add(drNew);
                }
            }

            return dtResult;
        }

        /// <summary>
        /// 将字典转为JSON串
        /// </summary>
        /// <param name="dicData">数据字典</param>
        /// <returns></returns>
        public static string DicToJson(Dictionary<string, string> dicData)
        {
            if (dicData == null || dicData.Count < 1) return string.Empty;

            //拼JSON 串
            StringBuilder sbJson = new StringBuilder();
            sbJson.Append("{");
            foreach (var item in dicData)
            {
                sbJson.Append($"\"{item.Key }\":\"{item.Value}\",");
            }
            sbJson.Remove(sbJson.Length - 1, 1);
            sbJson.Append("}");
            return sbJson.ToString();
        }

        /// <summary>
        /// 将JSON串转为字典
        /// <param name="strJson">Json字符串</param>
        /// </summary>
        /// <returns></returns>
        public static Dictionary<string, object> JsonToDic(string strJson)
        {
            JavaScriptSerializer javaScriptSerializer = new JavaScriptSerializer();
            if (strJson.StartsWith("["))
                strJson = strJson.Substring(1, strJson.Length - 1);

            if (strJson.EndsWith("]"))
                strJson = strJson.Substring(0, strJson.Length - 1);

            Dictionary<string, object> dicResult = javaScriptSerializer.Deserialize<Dictionary<string, object>>(strJson);
            return dicResult;
        }

        /// <summary>
        /// 将JSON串转为Jobject
        /// </summary>
        /// <param name="strJson">Json字符串</param>
        /// <returns></returns>
        public static JObject JsonToJobject(string strJson)
        {
            JObject joResult = null;
            if (!string.IsNullOrEmpty(strJson))
            {
                try
                {
                    joResult = (JObject)JsonConvert.DeserializeObject(strJson);
                }
                catch (Exception ex)
                {
                    Cs02MessageBox.ShowError("转换出错了！\r\n错误信息：" + ex.Message);
                    throw;
                }
            }
            return joResult;
        }

    }
}
