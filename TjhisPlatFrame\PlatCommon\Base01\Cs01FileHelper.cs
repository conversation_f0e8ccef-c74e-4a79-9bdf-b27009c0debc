﻿/*********************************************
* 文 件 名：Cs01FileHelper
* 类 名 称：Cs01FileHelper
	/// <summary>
    /// 目录及文件操作助手
	/// </summary>
/*********************************************/
using System;
using System.Data;
using System.Collections;
using System.IO;
using System.Reflection;

namespace PlatCommon.Base01
{
    /// <summary>
    /// 目录及文件操作助手
    /// </summary>
    public class Cs01FileHelper
    {
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Cs01FileHelper()
        {
        }

        #region 目录
        /// <summary>
        /// 获取当前动态库所在路径(非网络路径，即不含 file:\)
        /// </summary>
        /// <returns>当前动态库所在路径</returns>
        public static string GetCurrDllPath()
        {
            string dirFixPre = @"file:\";
            string directory = Assembly.GetExecutingAssembly().CodeBase;
            directory = Path.GetDirectoryName(directory);
            if (directory.StartsWith(dirFixPre, StringComparison.CurrentCultureIgnoreCase))
            {
                directory = directory.Substring(dirFixPre.Length);
            }

            return directory;
        }

        /// <summary>
        /// 拷贝目录
        /// </summary>
        /// <param name="dirInfoOld">源目录</param>
        /// <param name="dirInfoNew">目标目录, 如果不存在,则创建</param>
        public static void CopyDirectory(DirectoryInfo dirInfoOld, DirectoryInfo dirInfoNew)
        {
            string newDirFullName = Path.Combine(dirInfoNew.FullName, dirInfoOld.Name);

            // 如果目标目录不存在, 创建它
            if (Directory.Exists(newDirFullName) == false)
            {
                Directory.CreateDirectory(newDirFullName);
            }

            // 拷贝文件
            FileInfo[] arrOldFile = dirInfoOld.GetFiles();
            foreach (FileInfo fileInfo in arrOldFile)
            {
                CopyFile(fileInfo.FullName, Path.Combine(newDirFullName, fileInfo.Name));
            }

            // 拷贝子目录
            DirectoryInfo[] arrOldSubDir = dirInfoOld.GetDirectories();
            DirectoryInfo dirNew = new DirectoryInfo(newDirFullName);
            foreach (DirectoryInfo dirInfo in arrOldSubDir)
            {
                CopyDirectory(dirInfo, dirNew);
            }
        }

        /// <summary>
        /// 拷贝目录, 不拷贝下一级目录
        /// </summary>
        /// <param name="dirSrc">源目录</param>
        /// <param name="dirDest">目标目录, 如果不存在,则创建</param>
        public static void CopyDirectory_NoSub(string dirSrc, string dirDest)
        {
            // 如果目标目录不存在, 创建它
            if (Directory.Exists(dirDest) == false)
            {
                Directory.CreateDirectory(dirDest);
            }

            DirectoryInfo dir = new DirectoryInfo(dirSrc);

            // 拷贝文件
            FileInfo[] arrFile = dir.GetFiles();
            foreach (FileInfo fileInfo in arrFile)
            {
                CopyFile(fileInfo.FullName, Path.Combine(dirDest, fileInfo.Name));
            }
        }

        /// <summary>
        /// 拷贝目录
        /// </summary>
        /// <param name="oldDir">源目录</param>
        /// <param name="newDir">目标目录, 如果不存在,则创建</param>
        public static void CopyDirectory(string oldDir, string newDir)
        {
            DirectoryInfo dirInfoOld = new DirectoryInfo(oldDir);
            DirectoryInfo dirInfoNew = new DirectoryInfo(newDir);

            CopyDirectory(dirInfoOld, dirInfoNew);
        }

        /// <summary>
        /// 获取目录下的所有文件
        /// </summary>
        /// <param name="path">目录</param>
        /// <returns>文件集合</returns>
        public static ArrayList GetDirFiles(string path)
        {
            ArrayList files = new ArrayList();

            GetDirFiles(path, ref files);

            return files;
        }

        /// <summary>
        /// 获取目录下的所有文件
        /// </summary>
        /// <param name="path">目录</param>
        /// <param name="files">文件集合</param>
        public static void GetDirFiles(string path, ref ArrayList files)
        {
            // 判断目录是否存在
            if (Directory.Exists(path) == false)
            {
                return;
            }

            // 获取子目录
            String[] arrDir = Directory.GetDirectories(path);

            foreach (string str in arrDir)
            {
                GetDirFiles(str, ref files);
            }

            // 获取文件
            String[] arrFile = Directory.GetFiles(path);

            foreach (string str in arrFile)
            {
                files.Add(str);
            }
        }
        #endregion

        #region 文件
        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="path">文件</param>
        /// <remarks>对于只读文件会先去掉只读属性, 再删除</remarks>
        public static void DeleteFile(string path)
        {
            if (System.IO.Directory.Exists(path))
            {
                ArrayList arrFile = GetDirFiles(path);

                for (int i = 0; i < arrFile.Count; i++)
                {
                    try
                    {
                        File.Delete(arrFile[i].ToString());
                    }
                    catch
                    { }
                }
            }

            // 判断文件是否存在
            if (File.Exists(path) == false)
            {
                return;
            }

            // 去掉文件的只读属性
            FileInfo fi = new FileInfo(path);
            if ((fi.Attributes & FileAttributes.ReadOnly) == FileAttributes.ReadOnly)
            {
                fi.Attributes = fi.Attributes & (~FileAttributes.ReadOnly);
            }

            // 删除文件
            File.Delete(path);
        }

        /// <summary>
        /// 尽力Copy文件, 忽略错误
        /// </summary>
        /// <param name="srcFile">源文件</param>
        /// <param name="destFile">目标文件</param>
        public static void CopyFile(string srcFile, string destFile)
        {
            try
            {
                DeleteFile(destFile);

                File.Copy(srcFile, destFile, true);
            }
            catch { }
        }

        /// <summary>
        /// 获取文件的完整路径名称
        /// </summary>
        /// <param name="currentPath">当前目录</param>
        /// <param name="fileName">文件名</param>
        /// <returns>文件的绝对路径</returns>
        /// <remarks>把..\...\fileName转换成绝对路径</remarks>
        public static string GetFullFileName(string currentPath, string fileName)
        {
            string placeHolder = @"..\";

            while (fileName.StartsWith(placeHolder) == true)
            {
                currentPath = Path.GetDirectoryName(currentPath);
                fileName = fileName.Substring(placeHolder.Length);
            }

            return Path.Combine(currentPath, fileName);
        }

        /// <summary>
        /// 获取文件内容
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>文件内容字符串</returns>
        public static string GetFileContent(string fileName)
        {
            if (File.Exists(fileName) == true)
            {
                StreamReader sr = new StreamReader(fileName, System.Text.Encoding.Default);

                try
                {
                    return sr.ReadToEnd();
                }
                finally
                {
                    sr.Close();
                }
            }
            else
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 从数据流中创建文件
        /// </summary>
        /// <param name="fileName">创建的文件名</param>
        /// <param name="s"></param>
        /// <returns></returns>
        public static bool CreateFileFromStream(string fileName, Stream s)
        {
            // 写入流
            FileStream fs = new FileStream(fileName, FileMode.OpenOrCreate);

            try
            {
                BinaryWriter writer = new BinaryWriter(fs);
                long offset = fs.Length;

                // 读取
                BinaryReader reader = new BinaryReader(s);

                // 写入
                byte[] buffer;

                writer.Seek((int)offset, SeekOrigin.Begin);
                do
                {
                    buffer = reader.ReadBytes(1024);
                    writer.Write(buffer);
                } while (buffer.Length > 0);

                return true;
            }
            finally
            {
                fs.Close();
                s.Close();
            }
        }

        /// <summary>
        /// 将传入的byte[]写入到指定文件中
        /// </summary>
        /// <param name="data"></param>
        /// <param name="fileName">指定的文件名</param>
        /// <returns></returns>
        public static bool WriteFile(byte[] data, string fileName)
        {
            FileStream pFileStream = null;

            try
            {
                if (File.Exists(fileName)) File.Delete(fileName);

                pFileStream = new FileStream(fileName, FileMode.CreateNew);
                pFileStream.Write(data, 0, data.Length);
            }
            finally
            {
                if (pFileStream != null)
                    pFileStream.Close();
            }

            return true;
        }

        /// <summary>
        /// 从指定的文件中读取数据到byte[]中
        /// </summary>
        /// <param name="fileName">指定的文件名</param>
        /// <returns>读取到的byte[]</returns>
        public static byte[] ReadFile(string fileName)
        {
            FileStream fs = null;
            byte[] buffer;

            try
            {
                FileInfo fi = new FileInfo(fileName);
                long len = fi.Length;

                fs = new FileStream(fileName, FileMode.Open);
                buffer = new byte[len];
                fs.Read(buffer, 0, (int)len);
                fs.Close();
            }
            finally
            {
                if (fs != null) fs.Close();
            }

            return buffer;
        }

        /// <summary>
        /// 从指定的临时文件中读取数据到byte[]中
        /// </summary>
        /// <param name="fileName">指定的临时文件名</param>
        /// <returns>读取到的byte[]</returns>
        public static byte[] ReadFileTemp(string fileName)
        {
            FileStream fs = null;
            byte[] buffer;

            try
            {
                string fileNameNew = Guid.NewGuid().ToString();
                File.Copy(fileName, fileNameNew);
                fileName = fileNameNew;

                FileInfo fi = new FileInfo(fileName);
                long len = fi.Length;

                fs = new FileStream(fileName, FileMode.Open);
                buffer = new byte[len];
                fs.Read(buffer, 0, (int)len);
                fs.Close();
            }
            finally
            {
                if (fs != null) fs.Close();
                //判断文件是否存在 王代迪2017-01-17
                if (File.Exists(fileName))
                {
                    File.Delete(fileName);
                }
            }

            return buffer;
        }
        #endregion
    }
}
