﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace PlatCommon.Comm
{
    public class CLINIC_MASTER
    {
        private string _patient_id;

        public string Patient_id
        {
            get { return _patient_id; }
            set { _patient_id = value; }
        }

        private string name;

        public string Name
        {
            get { return name; }
            set { name = value; }
        }
        private string identity;

        public string Identity
        {
            get { return identity; }
            set { identity = value; }
        }
        private string charge_type;

        public string Charge_type
        {
            get { return charge_type; }
            set { charge_type = value; }
        }
        private int first_visit_indicator;

        public int First_visit_indicator
        {
            get { return first_visit_indicator; }
            set { first_visit_indicator = value; }
        }
        private decimal other_fee;

        public decimal Other_fee
        {
            get { return other_fee; }
            set { other_fee = value; }
        }
        private string name_phonetic;

        public string Name_phonetic
        {
            get { return name_phonetic; }
            set { name_phonetic = value; }
        }
        private string sex;

        public string Sex
        {
            get { return sex; }
            set { sex = value; }
        }
        private string symptom;

        public string Symptom
        {
            get { return symptom; }
            set { symptom = value; }
        }
        private int age;

        public int Age
        {
            get { return age; }
            set { age = value; }
        }
        private int mr_provided_indicator;

        public int Mr_provided_indicator
        {
            get { return mr_provided_indicator; }
            set { mr_provided_indicator = value; }
        }
        private DateTime visit_date;

        public DateTime Visit_date
        {
            get { return visit_date; }
            set { visit_date = value; }
        }
        private int visit_no;

        public int Visit_no
        {
            get { return visit_no; }
            set { visit_no = value; }
        }
        private string clinic_label;

        public string Clinic_label
        {
            get { return clinic_label; }
            set { clinic_label = value; }
        }
        private string visit_time_desc;

        public string Visit_time_desc
        {
            get { return visit_time_desc; }
            set { visit_time_desc = value; }
        }
        private int serial_no;

        public int Serial_no
        {
            get { return serial_no; }
            set { serial_no = value; }
        }
        private string insurance_type;

        public string Insurance_type
        {
            get { return insurance_type; }
            set { insurance_type = value; }
        }
        private string insurance_no;

        public string Insurance_no
        {
            get { return insurance_no; }
            set { insurance_no = value; }
        }
        private string unit_in_contract;

        public string Unit_in_contract
        {
            get { return unit_in_contract; }
            set { unit_in_contract = value; }
        }
        private string clinic_type;

        public string Clinic_type
        {
            get { return clinic_type; }
            set { clinic_type = value; }
        }
        private string visit_dept;

        public string Visit_dept
        {
            get { return visit_dept; }
            set { visit_dept = value; }
        }
        private string visit_special_clinic;

        public string Visit_special_clinic
        {
            get { return visit_special_clinic; }
            set { visit_special_clinic = value; }
        }
        private string doctor;

        public string Doctor
        {
            get { return doctor; }
            set { doctor = value; }
        }
        private int registration_status;

        public int Registration_status
        {
            get { return registration_status; }
            set { registration_status = value; }
        }
        private DateTime registering_date;

        public DateTime Registering_date
        {
            get { return registering_date; }
            set { registering_date = value; }
        }
        private decimal regist_fee;

        public decimal Regist_fee
        {
            get { return regist_fee; }
            set { regist_fee = value; }
        }
        private decimal clinic_fee;

        public decimal Clinic_fee
        {
            get { return clinic_fee; }
            set { clinic_fee = value; }
        }
        private string operators;

        public string Operators
        {
            get { return operators; }
            set { operators = value; }
        }
        private DateTime returned_date;

        public DateTime Returned_date
        {
            get { return returned_date; }
            set { returned_date = value; }
        }
        private string returned_operator;

        public string Returned_operator
        {
            get { return returned_operator; }
            set { returned_operator = value; }
        }
        private string insurance_diseaseno;

        public string Insurance_diseaseno
        {
            get { return insurance_diseaseno; }
            set { insurance_diseaseno = value; }
        }
        private string insurance_diseasename;

        public string Insurance_diseasename
        {
            get { return insurance_diseasename; }
            set { insurance_diseasename = value; }
        }
        private string insurance_sbm;

        public string Insurance_sbm
        {
            get { return insurance_sbm; }
            set { insurance_sbm = value; }
        }
        private decimal insurance_money;

        public decimal Insurance_money
        {
            get { return insurance_money; }
            set { insurance_money = value; }
        }
        private int flag;

        public int Flag
        {
            get { return flag; }
            set { flag = value; }
        }
        private string card_name;

        public string Card_name
        {
            get { return card_name; }
            set { card_name = value; }
        }
        private string card_no;

        public string Card_no
        {
            get { return card_no; }
            set { card_no = value; }
        }
        private DateTime date_of_birth;

        public DateTime Date_of_birth
        {
            get { return date_of_birth; }
            set { date_of_birth = value; }
        }
        private string id_no;

        public string Id_no
        {
            get { return id_no; }
            set { id_no = value; }
        }

        private string his_Unit_Code;

        public string His_Unit_Code
        {
            get { return his_Unit_Code; }
            set { his_Unit_Code = value; }
        }

        private DateTime clinic_Date_Scheduled;

        public DateTime Clinic_Date_Scheduled
        {
            get { return clinic_Date_Scheduled; }
            set { clinic_Date_Scheduled = value; }
        }

        private string clinic_No;

        public string Clinic_No
        {
            get { return clinic_No; }
            set { clinic_No = value; }
        }

        private string insur_card_no;

        public string Insur_card_no
        {
            get { return insur_card_no; }
            set { insur_card_no = value; }
        }


        private string insur_dbr;//代办人

        public string Insur_dbr
        {
            get { return insur_dbr; }
            set { insur_dbr = value; }
        }

        private string insr_dbridno;//代办人身份证号

        public string Insr_dbridno
        {
            get { return insr_dbridno; }
            set { insr_dbridno = value; }
        }

        //职业
        private string occupation;

        public string Occupation
        {
            get { return occupation; }
            set { occupation = value; }
        }

        //地址
        private string address;

        public string Address
        {
            get { return address; }
            set { address = value; }
        }

        //电话
        private string telphone;

        public string Telphone
        {
            get { return telphone; }
            set { telphone = value; }
        }


        //是否打印二维码信息
        private string printbarcode;

        public string Printbarcode
        {
            get { return printbarcode; }
            set { printbarcode = value; }
        }

        //接口交易时间 或者银行时间
        private string bank_trans_date;

        public string Bank_trans_date
        {
            get { return bank_trans_date; }
            set { bank_trans_date = value; }
        }

        //桃谷 海鹚 12320 自助机等订单号 退费及对账用
        private string tranno;

        public string Tranno
        {
            get { return tranno; }
            set { tranno = value; }
        }
        //APP 来源 如 桃谷 海鹚 健康卡  pos 
        private string app_source;

        public string App_source
        {
            get { return app_source; }
            set { app_source = value; }
        }
        //APP支付方式码 0A 支付宝 OB微信
        private string paymentmode;

        public string Paymentmode
        {
            get { return paymentmode; }
            set { paymentmode = value; }
        }
        //退款用 的参考号
        private string systemreferno;
        public string Systemreferno
        {
            get { return systemreferno; }
            set { systemreferno = value; }
        }

        private string insurance_sbmname;

        public string Insurance_sbmname
        {
            get { return insurance_sbmname; }
            set { insurance_sbmname = value; }
        }


        private string member_no;

        public string Member_no
        {
            get { return member_no; }
            set { member_no = value; }
        }
        private string army_no;
        public string Army_no
        {
            get { return army_no; }
            set { army_no = value; }
        }
    }
}
