﻿/*-----------------------------------------------------------------------
 * 类名称    ：FrmInsuranceVsItemInput
 * 类描述    ：
 * 创建人    ：梁吉lions
 * 创建时间  ：2017/1/11 9:24:06
 * 修改人    ：
 * 修改时间  ：
 * 修改备注  ：
 * 版本      ：
 * ----------------------------------------------------------------------
 */
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using PlatCommon.SysBase;

namespace PlatCommonForm
{
    public partial class FrmInsuranceVsItemInput : ParentForm
    {
        public FrmInsuranceVsItemInput()
        {
            InitializeComponent();
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="code">HIS编码 改为物价码了</param>
        /// <param name="spec">HIS规格</param>
        /// <param name="units">HIS单位</param>
        /// <param name="iclass">HIS分类</param>
        public FrmInsuranceVsItemInput(string code,string spec,string units,string iclass)
        {
            InitializeComponent();
            this.item_code = code;
            this.item_class = iclass;
            this.item_spec = spec;
            this.item_units = units;
        }
        #region 自定义变量
        /// <summary>
        /// 项目分类标志，0 -默认材料，1-药品类
        /// </summary>
        private int class_flag = 0;

        public int Class_Flag
        {
            get { return class_flag; }
            set { class_flag = value; }
        }
        //药品普通限制标志 0，不计价
        string iv_out_ypptxzbs = "";
        //药品特殊限制标志
        string iv_out_yptsxzbs = "";
        //药品普通限制范围
        string iv_out_ypptxzfw = "";
        //药品特殊限制范围
        string iv_out_yptsxzfw = "";
        //材料限制标志
        string iv_out_clxzsybs = "";
        //材料限制范围
        string iv_out_xzsyfw = "";
        
        private string iv_xzbz = "00";
        /// <summary>
        /// 限制标志 00，12，22，21
        /// </summary>
        public string Iv_Xzbz
        {
            get { return iv_xzbz; }
            set { iv_xzbz = value; }
        }

        private string iv_bwbm = "";
        /// <summary>
        /// 材料部位编码
        /// </summary>
        public string Iv_Bwbm
        {
            get { return iv_bwbm; }
            set { iv_bwbm = value; }
        }
        private string iv_xzbz_fw = "00";
        /// <summary>
        /// 限制范围
        /// </summary>
        public string Iv_Xzbz_Fw
        {
            get { return iv_xzbz_fw; }
            set { iv_xzbz_fw = value; }
        }
        private string iv_insur_level = "";
        /// <summary>
        /// 医保等级
        /// </summary>
        public string Iv_Insur_Level
        {
            get { return iv_insur_level; }
            set { iv_insur_level = value; }
        }
        private string item_code= string.Empty;
        private string item_spec= string.Empty;
        private string item_units= string.Empty;
        private string item_class= string.Empty;
        #endregion
        #region 自定义方法 
        /// <summary>
        /// 加载限制等级 材料-不限制，限制    药品-不限制，普通限制，特殊限制
        /// </summary>
        private void LoadXzdj()
        {
            cbox_xzdj.Properties.Items.Clear();
            if (class_flag == 0)
            {
                cbox_xzdj.Properties.Items.Add("不限制");
                cbox_xzdj.Properties.Items.Add("限制");
            }
            else if (class_flag == 1)
            {
                cbox_xzdj.Properties.Items.Add("不限制");
                cbox_xzdj.Properties.Items.Add("普通限制");
                cbox_xzdj.Properties.Items.Add("特殊限制");
            }
            if (cbox_xzdj.Properties.Items.Count > 0) cbox_xzdj.SelectedIndex = 0;
        }
        private void LoadBwdm()
        {
            string sqlstr = "select a.out_bwdm,a.out_bwmc,a.out_xmbm,a.out_xmmc from insurance.sitecode_dict_cityyb a  where a.out_xmbm ='"+ lblc_insurcode.Text.Trim()+ "' order by a.out_xmbm,a.out_bwdm";
            using (NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient())
            {
                DataSet dst = spc.GetDataBySql(sqlstr);
                if (dst != null)
                {
                    slook_out_bwdm.Properties.DataSource = dst.Tables[0].DefaultView;
                    slook_out_bwdm.Refresh();
                }
            }
        }

        private void LoadInsurItemInfo()
        {
            string sqlstr = "";
            lblc_histype.Text = "";
            lblc_insurcode.Text = "";
            lblc_INSUR_LEVEL.Text = "";
            lblc_INSUR_SCALE.Text = "";
            string material_code = getMaterialcode(this.item_code,this.item_spec,this.item_class,this.item_units);
            if (class_flag == 0)
            {
                //,费用级别,自付比例, HIS类型,中心编码 
                sqlstr = "select b.out_clxzsybs,b.out_xzsyfw,decode(a.insur_level, '1', '甲类', '2', '乙类', '3', '丙类') insur_level,a.insur_scale,a.item_class,a.insur_code "
                        + "from insurance.tj_vs_price a, insurance.items_dict_cityyb b "
                        + "where a.insur_code = b.out_zlxmbm(+) "
                        + "and a.interfacecode = 'syyb' "
                        + "and a.item_code ='" + material_code + "' "
                        + "and a.item_class ='" + this.item_class + "' "
                        + "and a.item_spec ='" + this.item_spec + "' "
                        + "and a.his_unit_code ='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' ";

                // + "and a.units ='" + this.item_units + "' ";
            }
            else if (class_flag == 1)
            {
                //药品普通限制标识,药品普通限制范围,药品特殊限制标志,药品特殊限制范围-40,费用级别,自付比例, HIS类型,中心编码
                sqlstr = "select b.out_ypptxzbs,b.out_yptsxzbs,b.out_ypptxzfw,b.out_yptsxzfw,decode(a.insur_level, '1', '甲类', '2', '乙类', '3', '丙类') insur_level,a.insur_scale,a.item_class,a.insur_code "
                        + "from insurance.tj_vs_price a, insurance.drugs_dict_cityyb b "
                        + "where a.insur_code = b.out_ypbm(+) "
                        + "and a.interfacecode = 'syyb' "
                        + "and a.item_code ='" + material_code + "' "
                        + "and a.item_class ='" + this.item_class + "' "
                        + "and a.item_spec ='" + this.item_spec + "' "
                        + "and a.units ='" + this.item_units + "' "
                        + "and a.his_unit_code ='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' ";
            }
            using (NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient())
            {
                lblc_insurcode.Text = "";
                DataSet dst = spc.GetDataBySql(sqlstr);
                if (dst != null && dst.Tables[0].Rows.Count > 0)
                {
                    //设置医保对照项目信息
                    if (class_flag == 0)
                    {
                        //材料
                        iv_out_clxzsybs = dst.Tables[0].Rows[0]["OUT_CLXZSYBS"] == DBNull.Value ? "" : dst.Tables[0].Rows[0]["OUT_CLXZSYBS"].ToString();

                        if (!string.IsNullOrEmpty(iv_out_clxzsybs))
                        {
                            iv_out_xzsyfw = dst.Tables[0].Rows[0]["OUT_XZSYFW"] == DBNull.Value ? "" : dst.Tables[0].Rows[0]["OUT_XZSYFW"].ToString();
                        }
                        else
                        {
                            iv_out_xzsyfw = "";
                        }
                        mmo_tsxzfw.Text = iv_out_xzsyfw;
                        lblc_histype.Text = dst.Tables[0].Rows[0]["ITEM_CLASS"] == DBNull.Value ? "" : dst.Tables[0].Rows[0]["ITEM_CLASS"].ToString();
                        lblc_insurcode.Text = dst.Tables[0].Rows[0]["INSUR_CODE"] == DBNull.Value ? "" : dst.Tables[0].Rows[0]["INSUR_CODE"].ToString();
                        lblc_INSUR_LEVEL.Text = dst.Tables[0].Rows[0]["INSUR_LEVEL"] == DBNull.Value ? "" : dst.Tables[0].Rows[0]["INSUR_LEVEL"].ToString();
                        lblc_INSUR_SCALE.Text = dst.Tables[0].Rows[0]["INSUR_SCALE"] == DBNull.Value ? "" : dst.Tables[0].Rows[0]["INSUR_SCALE"].ToString();

                    }
                    else if (class_flag == 1)
                    {
                        //药品 普通限制标志，普通限制范围，特殊限制标志，特殊限制范围
                        iv_out_ypptxzbs = dst.Tables[0].Rows[0]["OUT_YPPTXZBS"] == DBNull.Value ? "" : dst.Tables[0].Rows[0]["OUT_YPPTXZBS"].ToString();
                        iv_out_ypptxzfw = dst.Tables[0].Rows[0]["OUT_YPPTXZFW"] == DBNull.Value ? "" : dst.Tables[0].Rows[0]["OUT_YPPTXZFW"].ToString();
                        iv_out_yptsxzbs = dst.Tables[0].Rows[0]["OUT_YPTSXZBS"] == DBNull.Value ? "" : dst.Tables[0].Rows[0]["OUT_YPTSXZBS"].ToString();
                        iv_out_yptsxzfw = dst.Tables[0].Rows[0]["OUT_YPTSXZFW"] == DBNull.Value ? "" : dst.Tables[0].Rows[0]["OUT_YPTSXZFW"].ToString();

                        if (!string.IsNullOrEmpty(iv_out_yptsxzbs))
                        {
                            iv_out_yptsxzfw = dst.Tables[0].Rows[0]["OUT_YPTSXZFW"] == DBNull.Value ? "" : dst.Tables[0].Rows[0]["OUT_YPTSXZFW"].ToString();
                            mmo_tsxzfw.Text = iv_out_yptsxzfw;
                            cbox_xzdj.SelectedIndex = 2;
                        }
                        else if (!string.IsNullOrEmpty(iv_out_ypptxzbs))
                        {
                            cbox_xzdj.SelectedIndex = 1;
                            mmo_tsxzfw.Text = iv_out_ypptxzfw;
                            //cbox_xzdj.Properties.Items.RemoveAt(2);//没有特殊限制，移除选项?
                        }
                        //获取普通限制范围
                        iv_out_ypptxzfw = dst.Tables[0].Rows[0]["OUT_YPPTXZFW"] == DBNull.Value ? "" : dst.Tables[0].Rows[0]["OUT_YPPTXZFW"].ToString();

                        lblc_histype.Text = dst.Tables[0].Rows[0]["ITEM_CLASS"] == DBNull.Value ? "" : dst.Tables[0].Rows[0]["ITEM_CLASS"].ToString();
                        lblc_insurcode.Text = dst.Tables[0].Rows[0]["INSUR_CODE"] == DBNull.Value ? "" : dst.Tables[0].Rows[0]["INSUR_CODE"].ToString();
                        lblc_INSUR_LEVEL.Text = dst.Tables[0].Rows[0]["INSUR_LEVEL"] == DBNull.Value ? "" : dst.Tables[0].Rows[0]["INSUR_LEVEL"].ToString();
                        lblc_INSUR_SCALE.Text = dst.Tables[0].Rows[0]["INSUR_SCALE"] == DBNull.Value ? "" : dst.Tables[0].Rows[0]["INSUR_SCALE"].ToString();

                    }
                }
                
            }
        }
        /// <summary>
        /// 获取物价码
        /// </summary>
        /// <param name="item_code"></param>
        /// <param name="spec"></param>
        /// <param name="itemclass"></param>
        /// <param name="units"></param>
        /// <returns></returns>
        private string getMaterialcode(string item_code,string spec,string itemclass,string units)
        {
            string sqlstr = "select t.material_code from price_list t where t.item_class=:itemclass and t.item_code=:item_code and t.item_spec=:spec and t.units=:units and t.his_unit_code=:hisunit";
            List<string> paras = new List<string>();
            System.Collections.ArrayList values = new System.Collections.ArrayList();
            paras.Add("itemclass");
            paras.Add("item_code");
            paras.Add("spec");
            paras.Add("units");
            paras.Add("hisunit");
            values.Add(itemclass);
            values.Add(item_code);
            values.Add(spec);
            values.Add(units);
            values.Add(PlatCommon.SysBase.SystemParm.HisUnitCode);
            using (NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient())
            {
                lblc_insurcode.Text = "";
                DataSet dst = spc.GetDataTable_Para(sqlstr,paras,values);
                if (dst != null && dst.Tables.Count>0 && dst.Tables[0].Rows.Count > 0)
                {
                    return dst.Tables[0].Rows[0][0].ToString();
                }

            }
            return "";
        }
        #endregion
        private void FrmInsuranceVsItemInput_Load(object sender, EventArgs e)
        {
            slook_out_bwdm.Properties.DisplayMember = "OUT_BWMC";
            slook_out_bwdm.Properties.ValueMember = "OUT_BWDM";
            //NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            try
            {
                this.LoadXzdj();
                this.LoadInsurItemInfo();
                if (string.IsNullOrEmpty(lblc_insurcode.Text.Trim()))
                {
                    this.Close();
                    return;
                }
                lblc_ptxzfw_item.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;
                if (class_flag == 0)
                {
                    lblc_tsxzfw_item.Text = "材料限制：";
                    lay_out_bwdm_lbl.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always;
                    this.LoadBwdm();
                }
                else
                {
                    lay_out_bwdm_lbl.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;
                    lblc_tsxzfw_item.Text = "药品限制：";
                }
                
                
                
                
            }
            catch(Exception ex){
                DevExpress.XtraEditors.XtraMessageBox.Show(ex.Message, "错误信息");
                return;
            }
            
            cbox_xzdj.Focus();
        }

        private void btnOk_Click(object sender, EventArgs e)
        {
            this.iv_insur_level = lblc_INSUR_LEVEL.Text;
            if (cbox_xzdj.SelectedIndex < 0)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("请先选择限制等级", "提示信息");
                return;
            }
            if (class_flag == 0)
            {
                if (slook_out_bwdm.EditValue == null)
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("请先选择材料部位编码", "提示信息");
                    return;
                }
                // 材料
                this.Iv_Xzbz = cbox_xzdj.SelectedIndex.ToString();
                this.Iv_Bwbm = slook_out_bwdm.EditValue.ToString();
            }
            else if(class_flag==1){
                //药品
                switch (cbox_xzdj.SelectedIndex)
                {
                    case 0:
                        //不限制
                        this.Iv_Xzbz = "22";
                        break;
                    case 1:
                        //普通限制
                        this.Iv_Xzbz = "12";
                        break;
                    case 2:
                        //特殊限制
                        this.Iv_Xzbz = "21";
                        break;
                }
            }
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }
        //限制等级变化，显示对应等级的范围，不限制，不显示
        private void cbox_xzdj_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (class_flag == 0)
            {
                //材料--不限制，限制
                if (cbox_xzdj.SelectedIndex == 0)
                {
                    //不限制
                    mmo_tsxzfw.Text = "";
                }
                else if (cbox_xzdj.SelectedIndex == 1)
                {
                    mmo_tsxzfw.Text = iv_out_xzsyfw;
                }
            }
            else if (class_flag == 1)
            {
                //药品--不限制，普通限制，特殊限制
                if (cbox_xzdj.SelectedIndex == 0)
                {
                    //不限制
                    mmo_tsxzfw.Text = "";
                }
                else if (cbox_xzdj.SelectedIndex == 1)
                {
                    mmo_tsxzfw.Text = iv_out_xzsyfw;
                }
                else if (cbox_xzdj.SelectedIndex == 2)
                {
                    mmo_tsxzfw.Text = iv_out_xzsyfw;
                }
            }
        }
    }
}
