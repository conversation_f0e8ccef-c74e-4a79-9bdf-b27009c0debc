﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{C3773D56-3347-4EA5-A149-8DEB8C8DFB96}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Tjhis.Report.Custom</RootNamespace>
    <AssemblyName>Tjhis.Report.Custom</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\DLL\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Data.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.Data.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Printing.v19.1.Core, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.Printing.v19.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.Utils.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraBars.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraBars.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v19.1.UI, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraCharts.v19.1.UI.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraEditors.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraGrid.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraGrid.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraLayout.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraLayout.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraPrinting.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraPrinting.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraReports.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v19.1.Extensions, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraReports.v19.1.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraTreeList.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraTreeList.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraVerticalGrid.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraVerticalGrid.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="Model">
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\Model.dll</HintPath>
    </Reference>
    <Reference Include="NMService">
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\NMService.dll</HintPath>
    </Reference>
    <Reference Include="PlatCommon, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\PlatCommon.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="Utility">
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\Utility.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Base\DefaultDateTimeValueProvider.cs" />
    <Compile Include="Base\frmReportBase.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Base\frmReportBase.designer.cs">
      <DependentUpon>frmReportBase.cs</DependentUpon>
    </Compile>
    <Compile Include="Base\GridViewHelper.cs" />
    <Compile Include="Base\ModuleParam.cs" />
    <Compile Include="Base\ParamClass.cs" />
    <Compile Include="Base\PrinterConfigFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Base\PrinterConfigFrm.designer.cs">
      <DependentUpon>PrinterConfigFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\CommDataBase.cs" />
    <Compile Include="Common\Const.cs" />
    <Compile Include="Common\ReportHelper.cs" />
    <Compile Include="Custom\frmPageSetup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmPageSetup.Designer.cs">
      <DependentUpon>frmPageSetup.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmColumnView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmColumnView.Designer.cs">
      <DependentUpon>frmColumnView.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmCreateSQL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmCreateSQL.Designer.cs">
      <DependentUpon>frmCreateSQL.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmEditField.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmEditField.Designer.cs">
      <DependentUpon>frmEditField.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmFluentReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmFluentReport.Designer.cs">
      <DependentUpon>frmFluentReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmInsertReportToMenu.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmInsertReportToMenu.Designer.cs">
      <DependentUpon>frmInsertReportToMenu.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmParamDict.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmParamDict.Designer.cs">
      <DependentUpon>frmParamDict.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmParamEdit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmParamEdit.Designer.cs">
      <DependentUpon>frmParamEdit.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmReportMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmReportMain.designer.cs">
      <DependentUpon>frmReportMain.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmRoleDict.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmRoleDict.Designer.cs">
      <DependentUpon>frmRoleDict.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmSelectedFiled.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmSelectedFiled.Designer.cs">
      <DependentUpon>frmSelectedFiled.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmSelectedParam.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmSelectedParam.Designer.cs">
      <DependentUpon>frmSelectedParam.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmSQLQueryTest.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmSQLQueryTest.Designer.cs">
      <DependentUpon>frmSQLQueryTest.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmStaffDict.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmStaffDict.Designer.cs">
      <DependentUpon>frmStaffDict.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmStatisticalClassDict.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmStatisticalClassDict.Designer.cs">
      <DependentUpon>frmStatisticalClassDict.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmStatisticalQueryDoc.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmStatisticalQueryDoc.designer.cs">
      <DependentUpon>frmStatisticalQueryDoc.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmStatisticalQueryGrid.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmStatisticalQueryGrid.designer.cs">
      <DependentUpon>frmStatisticalQueryGrid.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\frmStatisticalSetting2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\frmStatisticalSetting2.Designer.cs">
      <DependentUpon>frmStatisticalSetting2.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\TJReportGridView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\TJReportGridView.Designer.cs">
      <DependentUpon>TJReportGridView.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\TJReportView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\TJReportView.Designer.cs">
      <DependentUpon>TJReportView.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\TJReportViewC.cs" />
    <Compile Include="Custom\TJReportViewF.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Custom\TJReportViewF.Designer.cs">
      <DependentUpon>TJReportViewF.cs</DependentUpon>
    </Compile>
    <Compile Include="Custom\PaperSizeCollection.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Srv\ConstStr.cs" />
    <Compile Include="Srv\SrvInsertReportToMenu.cs" />
    <Compile Include="Srv\srvRoleUserQuery.cs" />
    <Compile Include="Srv\srvStatisticalQuery.cs" />
    <Compile Include="Srv\srvT.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Base\frmReportBase.resx">
      <DependentUpon>frmReportBase.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Base\PrinterConfigFrm.resx">
      <DependentUpon>PrinterConfigFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmPageSetup.resx">
      <DependentUpon>frmPageSetup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmColumnView.resx">
      <DependentUpon>frmColumnView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmCreateSQL.resx">
      <DependentUpon>frmCreateSQL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmEditField.resx">
      <DependentUpon>frmEditField.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmFluentReport.resx">
      <DependentUpon>frmFluentReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmInsertReportToMenu.resx">
      <DependentUpon>frmInsertReportToMenu.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmParamDict.resx">
      <DependentUpon>frmParamDict.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmParamEdit.resx">
      <DependentUpon>frmParamEdit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmReportMain.resx">
      <DependentUpon>frmReportMain.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmRoleDict.resx">
      <DependentUpon>frmRoleDict.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmSelectedFiled.resx">
      <DependentUpon>frmSelectedFiled.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmSelectedParam.resx">
      <DependentUpon>frmSelectedParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmSQLQueryTest.resx">
      <DependentUpon>frmSQLQueryTest.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmStaffDict.resx">
      <DependentUpon>frmStaffDict.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmStatisticalClassDict.resx">
      <DependentUpon>frmStatisticalClassDict.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmStatisticalQueryDoc.resx">
      <DependentUpon>frmStatisticalQueryDoc.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmStatisticalQueryGrid.resx">
      <DependentUpon>frmStatisticalQueryGrid.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\frmStatisticalSetting2.resx">
      <DependentUpon>frmStatisticalSetting2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\TJReportView.resx">
      <DependentUpon>TJReportView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Custom\TJReportViewF.resx">
      <DependentUpon>TJReportViewF.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="Properties\licenses.licx" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\application_double.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\application_form_add.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\delete.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\disk.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\large_tiles.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\page_add.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\page_code.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\page_edit.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\report.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\spellcheck.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\script_gear.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\删除参数.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\特殊打印.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Export_32x32.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\DefaultPrinterNetwork_32x32.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Print_32x32.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Find_32x32.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Cancel_32x32.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Horizontal.bmp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Vertical.bmp" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>