﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System.Collections;

namespace PlatCommonForm.Query
{
    public partial class Dict : ParentForm
    {
        public Dict()
        {
            InitializeComponent();
        }
        string dict_type;
        string dict_where_by;//查询条件，默认为空
        DataTable dt_dict;
        DataSet ds_struct;
        public DataRow dr_return;
        string filter = null;
        public Dict(string type,string where_by="")
        {
            InitializeComponent();
            dict_type = type;
            dict_where_by = where_by;
            this.Text = type;
        }

        private void Dict_Load(object sender, EventArgs e)
        {
            SetGridControl();
        }

        private void SetGridControl()
        {
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            string sql = "select * from input_setting a where a.dict_type='" + dict_type + "' and  a.his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' order by show_sort";
            ds_struct = spc.GetList(sql);
            if (ds_struct.Tables[0].Rows.Count < 1) return;
            sql = "SELECT ";
            string sql_order = "";
            foreach (DataRow dr in ds_struct.Tables[0].Rows)
            {
                var col = new DevExpress.XtraGrid.Columns.GridColumn();
                col.Caption = dr["DATA_TITLE"].ToString();
                if (dr["DATA_TITLE"].ToString().IndexOf("拼音") >= 0) filter = dr["DATA_TITLE"].ToString();
                col.FieldName = dr["DATA_COL"].ToString();
                col.Width = 200;
                col.Visible = true;
                col.OptionsFilter.AllowFilter = true;
                gridView1.Columns.Add(col);
                sql_order += dr["DATA_COL"].ToString() + ",";
                beType.Properties.Items.Add(dr["DATA_TITLE"].ToString());

            }
            sql_order = sql_order.Substring(0, sql_order.Length - 1);
            sql += sql_order + " FROM " + ds_struct.Tables[0].Rows[0]["DATA_TABLE"].ToString()+" ";
            if (!string.IsNullOrEmpty(dict_where_by))
            {
                sql += dict_where_by;
            }
            sql += " ORDER BY " + sql_order;
            dt_dict = spc.GetList(sql).Tables[0];
            gridControl1.DataSource = dt_dict;
            beType.EditValue = filter;
        }


        private void gridControl1_MouseDown(object sender, MouseEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo hInfo = gridView1.CalcHitInfo(new Point(e.X, e.Y));
            if (e.Button == MouseButtons.Left && e.Clicks == 2)
            {
                //判断光标是否在行范围内  
                if (hInfo.InRow)
                {
                    //取得选定行信息  
                    dr_return = this.gridView1.GetDataRow(gridView1.FocusedRowHandle);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
        }

        private void Dict_FormClosed(object sender, FormClosedEventArgs e)
        {
            if (gridView1.FocusedRowHandle < 0)
            {
                this.DialogResult = DialogResult.Cancel;
            }
        }


        private void gridControl1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (gridView1.FocusedRowHandle > -1)
                {
                    dr_return = this.gridView1.GetDataRow(gridView1.FocusedRowHandle);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
        }



        private void btnConfirm_Click(object sender, EventArgs e)
        {
            if (this.gridView1.FocusedRowHandle > 0)
            {
                dr_return = this.gridView1.GetDataRow(gridView1.FocusedRowHandle);
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void beFilter_EditValueChanged(object sender, EventArgs e)
        {
            string filter_type = "";
            if (beType.EditValue == null) return;
            foreach (DataRow dr in ds_struct.Tables[0].Rows)
            {
                if (dr["DATA_TITLE"].ToString() == beType.EditValue.ToString())
                {
                    filter_type = dr["DATA_COL"].ToString();
                }
            }
            string keyvalue = "";
            if (beFilter.EditValue != null && beFilter.EditValue != DBNull.Value)
            {
                keyvalue = beFilter.EditValue.ToString();
            }
            dt_dict.DefaultView.RowFilter = filter_type + " LIKE '%" + keyvalue + "%'";
        }
    }
}