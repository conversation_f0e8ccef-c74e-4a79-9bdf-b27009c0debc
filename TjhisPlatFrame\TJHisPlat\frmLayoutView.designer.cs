﻿namespace TJHisPlat
{
    partial class frmLayoutView
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmLayoutView));
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.layoutView1 = new DevExpress.XtraGrid.Views.Layout.LayoutView();
            this.colAPPNAME = new DevExpress.XtraGrid.Columns.LayoutViewColumn();
            this.layoutViewField_colAPPNAME = new DevExpress.XtraGrid.Views.Layout.LayoutViewField();
            this.colIMAGE = new DevExpress.XtraGrid.Columns.LayoutViewColumn();
            this.repositoryItemPictureEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemPictureEdit();
            this.layoutViewField_colIMAGE = new DevExpress.XtraGrid.Views.Layout.LayoutViewField();
            this.DEPTNAME = new DevExpress.XtraGrid.Columns.LayoutViewColumn();
            this.TextEditlist = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.layoutViewField_layoutViewColumn1 = new DevExpress.XtraGrid.Views.Layout.LayoutViewField();
            this.layoutViewColumn2 = new DevExpress.XtraGrid.Columns.LayoutViewColumn();
            this.layoutViewField_layoutViewColumn2 = new DevExpress.XtraGrid.Views.Layout.LayoutViewField();
            this.layoutViewCard1 = new DevExpress.XtraGrid.Views.Layout.LayoutViewCard();
            this.item1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.item2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.item3 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.item4 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.item5 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.item6 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.item7 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.item8 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.item9 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.item10 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.item11 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.item12 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.item13 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.item14 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.repositoryItemImageEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemImageEdit();
            this.repositoryItemSearchLookUpEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemSearchLookUpEdit();
            this.repositoryItemSearchLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.LookUpList = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutViewField_colAPPNAME)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemPictureEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutViewField_colIMAGE)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.TextEditlist)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutViewField_layoutViewColumn1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutViewField_layoutViewColumn2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutViewCard1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.item1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.item2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.item3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.item4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.item5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.item6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.item7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.item8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.item9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.item10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.item11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.item12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.item13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.item14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemImageEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSearchLookUpEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSearchLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.LookUpList)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl1
            // 
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.EmbeddedNavigator.Margin = new System.Windows.Forms.Padding(7, 9, 7, 9);
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.layoutView1;
            this.gridControl1.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemImageEdit1,
            this.repositoryItemSearchLookUpEdit1,
            this.repositoryItemPictureEdit1,
            this.LookUpList,
            this.TextEditlist});
            this.gridControl1.Size = new System.Drawing.Size(766, 414);
            this.gridControl1.TabIndex = 0;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.layoutView1});
            // 
            // layoutView1
            // 
            this.layoutView1.Appearance.Card.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.layoutView1.Appearance.Card.Options.UseBorderColor = true;
            this.layoutView1.Appearance.FocusedCardCaption.BackColor = System.Drawing.Color.Blue;
            this.layoutView1.Appearance.FocusedCardCaption.BackColor2 = System.Drawing.Color.Blue;
            this.layoutView1.Appearance.FocusedCardCaption.Options.UseBackColor = true;
            this.layoutView1.Appearance.HideSelectionCardCaption.BackColor = System.Drawing.Color.Blue;
            this.layoutView1.Appearance.HideSelectionCardCaption.BackColor2 = System.Drawing.Color.Blue;
            this.layoutView1.Appearance.HideSelectionCardCaption.Options.UseBackColor = true;
            this.layoutView1.Appearance.SelectionFrame.BorderColor = System.Drawing.Color.Blue;
            this.layoutView1.Appearance.SelectionFrame.Options.UseBorderColor = true;
            this.layoutView1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.layoutView1.CardHorzInterval = 13;
            this.layoutView1.CardMinSize = new System.Drawing.Size(167, 174);
            this.layoutView1.CardVertInterval = 5;
            this.layoutView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.LayoutViewColumn[] {
            this.colAPPNAME,
            this.colIMAGE,
            this.DEPTNAME,
            this.layoutViewColumn2});
            this.layoutView1.DetailHeight = 321;
            this.layoutView1.GridControl = this.gridControl1;
            this.layoutView1.HiddenItems.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutViewField_layoutViewColumn2});
            this.layoutView1.Name = "layoutView1";
            this.layoutView1.OptionsBehavior.AutoFocusCardOnScrolling = true;
            this.layoutView1.OptionsBehavior.AutoFocusNewCard = true;
            this.layoutView1.OptionsBehavior.KeepFocusedRowOnUpdate = false;
            this.layoutView1.OptionsBehavior.ScrollVisibility = DevExpress.XtraGrid.Views.Base.ScrollVisibility.Auto;
            this.layoutView1.OptionsCarouselMode.BottomCardScale = 0.5F;
            this.layoutView1.OptionsCustomization.AllowSort = false;
            this.layoutView1.OptionsFilter.AllowColumnMRUFilterList = false;
            this.layoutView1.OptionsFilter.AllowFilterEditor = false;
            this.layoutView1.OptionsFilter.AllowFilterIncrementalSearch = false;
            this.layoutView1.OptionsFilter.AllowMRUFilterList = false;
            this.layoutView1.OptionsFilter.AllowMultiSelectInCheckedFilterPopup = false;
            this.layoutView1.OptionsFilter.ShowAllTableValuesInCheckedFilterPopup = false;
            this.layoutView1.OptionsFind.AllowFindPanel = false;
            this.layoutView1.OptionsHeaderPanel.ShowCarouselModeButton = false;
            this.layoutView1.OptionsHeaderPanel.ShowColumnModeButton = false;
            this.layoutView1.OptionsHeaderPanel.ShowCustomizeButton = false;
            this.layoutView1.OptionsHeaderPanel.ShowMultiColumnModeButton = false;
            this.layoutView1.OptionsHeaderPanel.ShowMultiRowModeButton = false;
            this.layoutView1.OptionsHeaderPanel.ShowPanButton = false;
            this.layoutView1.OptionsHeaderPanel.ShowRowModeButton = false;
            this.layoutView1.OptionsHeaderPanel.ShowSingleModeButton = false;
            this.layoutView1.OptionsItemText.AlignMode = DevExpress.XtraGrid.Views.Layout.FieldTextAlignMode.AutoSize;
            this.layoutView1.OptionsItemText.TextToControlDistance = 16;
            this.layoutView1.OptionsMultiRecordMode.MaxCardColumns = 10;
            this.layoutView1.OptionsMultiRecordMode.MaxCardRows = -1;
            this.layoutView1.OptionsMultiRecordMode.MultiRowScrollBarOrientation = DevExpress.XtraGrid.Views.Layout.ScrollBarOrientation.Vertical;
            this.layoutView1.OptionsView.AllowHotTrackFields = false;
            this.layoutView1.OptionsView.DefaultColumnCount = 2;
            this.layoutView1.OptionsView.PartialCardsSimpleScrolling = DevExpress.Utils.DefaultBoolean.True;
            this.layoutView1.OptionsView.ShowCardCaption = false;
            this.layoutView1.OptionsView.ShowCardExpandButton = false;
            this.layoutView1.OptionsView.ShowCardLines = false;
            this.layoutView1.OptionsView.ShowFieldHints = false;
            this.layoutView1.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
            this.layoutView1.OptionsView.ShowHeaderPanel = false;
            this.layoutView1.OptionsView.ViewMode = DevExpress.XtraGrid.Views.Layout.LayoutViewMode.MultiRow;
            this.layoutView1.PaintStyleName = "Flat";
            this.layoutView1.TemplateCard = this.layoutViewCard1;
            this.layoutView1.CustomRowCellEdit += new DevExpress.XtraGrid.Views.Layout.Events.LayoutViewCustomRowCellEditEventHandler(this.layoutView1_CustomRowCellEdit);
            this.layoutView1.VisibleRecordIndexChanged += new DevExpress.XtraGrid.Views.Layout.Events.LayoutViewVisibleRecordIndexChangedEventHandler(this.layoutView1_VisibleRecordIndexChanged);
            this.layoutView1.CustomCardLayout += new DevExpress.XtraGrid.Views.Layout.Events.LayoutViewCustomCardLayoutEventHandler(this.layoutView1_CustomCardLayout);
            this.layoutView1.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.layoutView1_FocusedRowChanged);
            this.layoutView1.MouseDown += new System.Windows.Forms.MouseEventHandler(this.layoutView1_MouseDown);
            this.layoutView1.DoubleClick += new System.EventHandler(this.layoutView1_DoubleClick);
            // 
            // colAPPNAME
            // 
            this.colAPPNAME.AppearanceCell.Font = new System.Drawing.Font("微软雅黑", 11F, System.Drawing.FontStyle.Bold);
            this.colAPPNAME.AppearanceCell.Options.UseFont = true;
            this.colAPPNAME.AppearanceCell.Options.UseTextOptions = true;
            this.colAPPNAME.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colAPPNAME.Caption = "模块名称";
            this.colAPPNAME.FieldName = "appname";
            this.colAPPNAME.LayoutViewField = this.layoutViewField_colAPPNAME;
            this.colAPPNAME.MinWidth = 17;
            this.colAPPNAME.Name = "colAPPNAME";
            this.colAPPNAME.OptionsColumn.AllowEdit = false;
            this.colAPPNAME.OptionsColumn.AllowFocus = false;
            this.colAPPNAME.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colAPPNAME.OptionsColumn.ReadOnly = true;
            this.colAPPNAME.OptionsColumn.ShowInExpressionEditor = false;
            this.colAPPNAME.OptionsFilter.AllowAutoFilter = false;
            this.colAPPNAME.OptionsFilter.AllowFilter = false;
            this.colAPPNAME.Width = 62;
            // 
            // layoutViewField_colAPPNAME
            // 
            this.layoutViewField_colAPPNAME.EditorPreferredWidth = 132;
            this.layoutViewField_colAPPNAME.ImageOptions.Alignment = System.Drawing.ContentAlignment.MiddleCenter;
            this.layoutViewField_colAPPNAME.Location = new System.Drawing.Point(31, 70);
            this.layoutViewField_colAPPNAME.MaxSize = new System.Drawing.Size(0, 49);
            this.layoutViewField_colAPPNAME.MinSize = new System.Drawing.Size(26, 27);
            this.layoutViewField_colAPPNAME.Name = "layoutViewField_colAPPNAME";
            this.layoutViewField_colAPPNAME.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.layoutViewField_colAPPNAME.Size = new System.Drawing.Size(137, 26);
            this.layoutViewField_colAPPNAME.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutViewField_colAPPNAME.TextSize = new System.Drawing.Size(0, 0);
            this.layoutViewField_colAPPNAME.TextVisible = false;
            // 
            // colIMAGE
            // 
            this.colIMAGE.AppearanceCell.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.colIMAGE.AppearanceCell.Options.UseFont = true;
            this.colIMAGE.Caption = "模块图";
            this.colIMAGE.ColumnEdit = this.repositoryItemPictureEdit1;
            this.colIMAGE.FieldName = "IMAGE";
            this.colIMAGE.LayoutViewField = this.layoutViewField_colIMAGE;
            this.colIMAGE.MinWidth = 17;
            this.colIMAGE.Name = "colIMAGE";
            this.colIMAGE.OptionsColumn.AllowEdit = false;
            this.colIMAGE.OptionsColumn.AllowFocus = false;
            this.colIMAGE.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colIMAGE.OptionsColumn.ReadOnly = true;
            this.colIMAGE.OptionsFilter.AllowAutoFilter = false;
            this.colIMAGE.OptionsFilter.AllowFilter = false;
            this.colIMAGE.Width = 62;
            // 
            // repositoryItemPictureEdit1
            // 
            this.repositoryItemPictureEdit1.Name = "repositoryItemPictureEdit1";
            // 
            // layoutViewField_colIMAGE
            // 
            this.layoutViewField_colIMAGE.EditorPreferredWidth = 171;
            this.layoutViewField_colIMAGE.HighlightFocusedItem = DevExpress.Utils.DefaultBoolean.True;
            this.layoutViewField_colIMAGE.ImageOptions.ImageIndex = 1;
            this.layoutViewField_colIMAGE.Location = new System.Drawing.Point(10, 0);
            this.layoutViewField_colIMAGE.Name = "layoutViewField_colIMAGE";
            this.layoutViewField_colIMAGE.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.layoutViewField_colIMAGE.Size = new System.Drawing.Size(176, 70);
            this.layoutViewField_colIMAGE.StartNewLine = true;
            this.layoutViewField_colIMAGE.TextSize = new System.Drawing.Size(0, 0);
            this.layoutViewField_colIMAGE.TextVisible = false;
            // 
            // DEPTNAME
            // 
            this.DEPTNAME.AppearanceCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(74)))), ((int)(((byte)(170)))), ((int)(((byte)(244)))));
            this.DEPTNAME.AppearanceCell.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.DEPTNAME.AppearanceCell.ForeColor = System.Drawing.Color.White;
            this.DEPTNAME.AppearanceCell.Options.UseBackColor = true;
            this.DEPTNAME.AppearanceCell.Options.UseFont = true;
            this.DEPTNAME.AppearanceCell.Options.UseForeColor = true;
            this.DEPTNAME.AppearanceCell.Options.UseTextOptions = true;
            this.DEPTNAME.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.DEPTNAME.Caption = "科室";
            this.DEPTNAME.ColumnEdit = this.TextEditlist;
            this.DEPTNAME.FieldName = "deptname";
            this.DEPTNAME.LayoutViewField = this.layoutViewField_layoutViewColumn1;
            this.DEPTNAME.MinWidth = 17;
            this.DEPTNAME.Name = "DEPTNAME";
            this.DEPTNAME.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.DEPTNAME.OptionsFilter.AllowAutoFilter = false;
            this.DEPTNAME.OptionsFilter.AllowFilter = false;
            this.DEPTNAME.Width = 62;
            // 
            // TextEditlist
            // 
            this.TextEditlist.AllowFocused = false;
            this.TextEditlist.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(74)))), ((int)(((byte)(170)))), ((int)(((byte)(244)))));
            this.TextEditlist.Appearance.Options.UseBackColor = true;
            this.TextEditlist.Appearance.Options.UseTextOptions = true;
            this.TextEditlist.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.TextEditlist.AppearanceFocused.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(74)))), ((int)(((byte)(170)))), ((int)(((byte)(244)))));
            this.TextEditlist.AppearanceFocused.Options.UseBackColor = true;
            this.TextEditlist.AppearanceFocused.Options.UseTextOptions = true;
            this.TextEditlist.AppearanceFocused.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.TextEditlist.AppearanceReadOnly.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(74)))), ((int)(((byte)(170)))), ((int)(((byte)(244)))));
            this.TextEditlist.AppearanceReadOnly.Options.UseBackColor = true;
            this.TextEditlist.AutoHeight = false;
            this.TextEditlist.Name = "TextEditlist";
            this.TextEditlist.ReadOnly = true;
            // 
            // layoutViewField_layoutViewColumn1
            // 
            this.layoutViewField_layoutViewColumn1.EditorPreferredWidth = 132;
            this.layoutViewField_layoutViewColumn1.Location = new System.Drawing.Point(31, 106);
            this.layoutViewField_layoutViewColumn1.MaxSize = new System.Drawing.Size(0, 37);
            this.layoutViewField_layoutViewColumn1.MinSize = new System.Drawing.Size(22, 24);
            this.layoutViewField_layoutViewColumn1.Name = "layoutViewField_layoutViewColumn1";
            this.layoutViewField_layoutViewColumn1.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.layoutViewField_layoutViewColumn1.Size = new System.Drawing.Size(137, 23);
            this.layoutViewField_layoutViewColumn1.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutViewField_layoutViewColumn1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutViewField_layoutViewColumn1.TextVisible = false;
            // 
            // layoutViewColumn2
            // 
            this.layoutViewColumn2.AppearanceCell.Font = new System.Drawing.Font("微软雅黑", 11F);
            this.layoutViewColumn2.AppearanceCell.Options.UseFont = true;
            this.layoutViewColumn2.AppearanceCell.Options.UseTextOptions = true;
            this.layoutViewColumn2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.layoutViewColumn2.Caption = "进入系统";
            this.layoutViewColumn2.FieldName = "INSYSTEM";
            this.layoutViewColumn2.LayoutViewField = this.layoutViewField_layoutViewColumn2;
            this.layoutViewColumn2.MinWidth = 17;
            this.layoutViewColumn2.Name = "layoutViewColumn2";
            this.layoutViewColumn2.Width = 62;
            // 
            // layoutViewField_layoutViewColumn2
            // 
            this.layoutViewField_layoutViewColumn2.EditorPreferredWidth = 20;
            this.layoutViewField_layoutViewColumn2.ImageOptions.Alignment = System.Drawing.ContentAlignment.MiddleCenter;
            this.layoutViewField_layoutViewColumn2.Location = new System.Drawing.Point(0, 0);
            this.layoutViewField_layoutViewColumn2.Name = "layoutViewField_layoutViewColumn2";
            this.layoutViewField_layoutViewColumn2.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.layoutViewField_layoutViewColumn2.Size = new System.Drawing.Size(196, 139);
            this.layoutViewField_layoutViewColumn2.TextSize = new System.Drawing.Size(0, 0);
            this.layoutViewField_layoutViewColumn2.TextVisible = false;
            // 
            // layoutViewCard1
            // 
            this.layoutViewCard1.BackgroundImageOptions.Layout = System.Windows.Forms.ImageLayout.None;
            this.layoutViewCard1.ContentImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("layoutViewCard1.ContentImageOptions.Image")));
            this.layoutViewCard1.CustomizationFormText = "TemplateCard";
            this.layoutViewCard1.GroupBordersVisible = false;
            this.layoutViewCard1.HeaderButtonsLocation = DevExpress.Utils.GroupElementLocation.AfterText;
            this.layoutViewCard1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.item1,
            this.item2,
            this.item3,
            this.item4,
            this.item5,
            this.item6,
            this.item7,
            this.item8,
            this.item9,
            this.item10,
            this.item11,
            this.item12,
            this.layoutViewField_colAPPNAME,
            this.layoutViewField_colIMAGE,
            this.item13,
            this.layoutViewField_layoutViewColumn1,
            this.item14});
            this.layoutViewCard1.Name = "layoutViewTemplateCard";
            this.layoutViewCard1.OptionsItemText.TextToControlDistance = 16;
            this.layoutViewCard1.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.layoutViewCard1.ShowInCustomizationForm = false;
            this.layoutViewCard1.Text = "TemplateCard";
            // 
            // item1
            // 
            this.item1.AllowHotTrack = false;
            this.item1.CustomizationFormText = "item1";
            this.item1.Location = new System.Drawing.Point(0, 0);
            this.item1.Name = "item1";
            this.item1.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.item1.Size = new System.Drawing.Size(10, 10);
            this.item1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // item2
            // 
            this.item2.AllowHotTrack = false;
            this.item2.CustomizationFormText = "item2";
            this.item2.Location = new System.Drawing.Point(186, 0);
            this.item2.Name = "item2";
            this.item2.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.item2.Size = new System.Drawing.Size(10, 28);
            this.item2.TextSize = new System.Drawing.Size(0, 0);
            // 
            // item3
            // 
            this.item3.AllowHotTrack = false;
            this.item3.CustomizationFormText = "item3";
            this.item3.Location = new System.Drawing.Point(0, 70);
            this.item3.Name = "item3";
            this.item3.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.item3.Size = new System.Drawing.Size(31, 59);
            this.item3.TextSize = new System.Drawing.Size(0, 0);
            // 
            // item4
            // 
            this.item4.AllowHotTrack = false;
            this.item4.CustomizationFormText = "item4";
            this.item4.Location = new System.Drawing.Point(168, 70);
            this.item4.Name = "item4";
            this.item4.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.item4.Size = new System.Drawing.Size(28, 59);
            this.item4.TextSize = new System.Drawing.Size(0, 0);
            // 
            // item5
            // 
            this.item5.AllowHotTrack = false;
            this.item5.CustomizationFormText = "item5";
            this.item5.Location = new System.Drawing.Point(186, 60);
            this.item5.Name = "item5";
            this.item5.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.item5.Size = new System.Drawing.Size(10, 10);
            this.item5.TextSize = new System.Drawing.Size(0, 0);
            // 
            // item6
            // 
            this.item6.AllowHotTrack = false;
            this.item6.CustomizationFormText = "item6";
            this.item6.Location = new System.Drawing.Point(0, 60);
            this.item6.Name = "item6";
            this.item6.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.item6.Size = new System.Drawing.Size(10, 10);
            this.item6.TextSize = new System.Drawing.Size(0, 0);
            // 
            // item7
            // 
            this.item7.AllowHotTrack = false;
            this.item7.CustomizationFormText = "item7";
            this.item7.Location = new System.Drawing.Point(186, 28);
            this.item7.Name = "item7";
            this.item7.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.item7.Size = new System.Drawing.Size(10, 32);
            this.item7.TextSize = new System.Drawing.Size(0, 0);
            // 
            // item8
            // 
            this.item8.AllowHotTrack = false;
            this.item8.CustomizationFormText = "item8";
            this.item8.Location = new System.Drawing.Point(0, 30);
            this.item8.Name = "item8";
            this.item8.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.item8.Size = new System.Drawing.Size(10, 10);
            this.item8.TextSize = new System.Drawing.Size(0, 0);
            // 
            // item9
            // 
            this.item9.AllowHotTrack = false;
            this.item9.CustomizationFormText = "item9";
            this.item9.Location = new System.Drawing.Point(0, 50);
            this.item9.Name = "item9";
            this.item9.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.item9.Size = new System.Drawing.Size(10, 10);
            this.item9.TextSize = new System.Drawing.Size(0, 0);
            // 
            // item10
            // 
            this.item10.AllowHotTrack = false;
            this.item10.CustomizationFormText = "item10";
            this.item10.Location = new System.Drawing.Point(0, 10);
            this.item10.Name = "item10";
            this.item10.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.item10.Size = new System.Drawing.Size(10, 10);
            this.item10.TextSize = new System.Drawing.Size(0, 0);
            // 
            // item11
            // 
            this.item11.AllowHotTrack = false;
            this.item11.CustomizationFormText = "item11";
            this.item11.Location = new System.Drawing.Point(0, 40);
            this.item11.Name = "item11";
            this.item11.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.item11.Size = new System.Drawing.Size(10, 10);
            this.item11.TextSize = new System.Drawing.Size(0, 0);
            // 
            // item12
            // 
            this.item12.AllowHotTrack = false;
            this.item12.CustomizationFormText = "item12";
            this.item12.Location = new System.Drawing.Point(0, 20);
            this.item12.Name = "item12";
            this.item12.Padding = new DevExpress.XtraLayout.Utils.Padding(1, 1, 1, 1);
            this.item12.Size = new System.Drawing.Size(10, 10);
            this.item12.TextSize = new System.Drawing.Size(0, 0);
            // 
            // item13
            // 
            this.item13.AllowHotTrack = false;
            this.item13.CustomizationFormText = "item13";
            this.item13.Location = new System.Drawing.Point(0, 129);
            this.item13.Name = "item13";
            this.item13.Size = new System.Drawing.Size(196, 10);
            this.item13.TextSize = new System.Drawing.Size(0, 0);
            // 
            // item14
            // 
            this.item14.AllowHotTrack = false;
            this.item14.CustomizationFormText = "item14";
            this.item14.Location = new System.Drawing.Point(31, 96);
            this.item14.Name = "item14";
            this.item14.Size = new System.Drawing.Size(137, 10);
            this.item14.TextSize = new System.Drawing.Size(0, 0);
            // 
            // repositoryItemImageEdit1
            // 
            this.repositoryItemImageEdit1.AutoHeight = false;
            this.repositoryItemImageEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemImageEdit1.Name = "repositoryItemImageEdit1";
            // 
            // repositoryItemSearchLookUpEdit1
            // 
            this.repositoryItemSearchLookUpEdit1.Appearance.Options.UseTextOptions = true;
            this.repositoryItemSearchLookUpEdit1.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.repositoryItemSearchLookUpEdit1.AutoHeight = false;
            this.repositoryItemSearchLookUpEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemSearchLookUpEdit1.Name = "repositoryItemSearchLookUpEdit1";
            this.repositoryItemSearchLookUpEdit1.PopupView = this.repositoryItemSearchLookUpEdit1View;
            // 
            // repositoryItemSearchLookUpEdit1View
            // 
            this.repositoryItemSearchLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1});
            this.repositoryItemSearchLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemSearchLookUpEdit1View.Name = "repositoryItemSearchLookUpEdit1View";
            this.repositoryItemSearchLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemSearchLookUpEdit1View.OptionsView.ColumnAutoWidth = false;
            this.repositoryItemSearchLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn1.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn1.Caption = "科室名";
            this.gridColumn1.FieldName = "APPNAME";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 179;
            // 
            // LookUpList
            // 
            this.LookUpList.AutoHeight = false;
            this.LookUpList.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.LookUpList.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("deptname", "科室", 15, DevExpress.Utils.FormatType.None, "", true, DevExpress.Utils.HorzAlignment.Center, DevExpress.Data.ColumnSortOrder.None, DevExpress.Utils.DefaultBoolean.Default)});
            this.LookUpList.ImmediatePopup = true;
            this.LookUpList.Name = "LookUpList";
            this.LookUpList.NullText = "进入系统";
            this.LookUpList.NullValuePrompt = "进入系统";
            this.LookUpList.ShowHeader = false;
            // 
            // frmLayoutView
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(5F, 11F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(766, 414);
            this.Controls.Add(this.gridControl1);
            this.Font = new System.Drawing.Font("Tahoma", 7F);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
            this.Name = "frmLayoutView";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "本平台为非涉密平台，严禁处理、传输国家秘密";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frmLayoutView_FormClosing);
            this.Load += new System.EventHandler(this.frmLayoutView_Load);
            this.Resize += new System.EventHandler(this.frmLayoutView_Resize);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutViewField_colAPPNAME)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemPictureEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutViewField_colIMAGE)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.TextEditlist)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutViewField_layoutViewColumn1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutViewField_layoutViewColumn2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutViewCard1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.item1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.item2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.item3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.item4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.item5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.item6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.item7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.item8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.item9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.item10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.item11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.item12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.item13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.item14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemImageEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSearchLookUpEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSearchLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.LookUpList)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Layout.LayoutView layoutView1;
        private DevExpress.XtraGrid.Columns.LayoutViewColumn colAPPNAME;
        private DevExpress.XtraGrid.Columns.LayoutViewColumn colIMAGE;
        private DevExpress.XtraEditors.Repository.RepositoryItemPictureEdit repositoryItemPictureEdit1;
        private DevExpress.XtraGrid.Columns.LayoutViewColumn DEPTNAME;
        private DevExpress.XtraEditors.Repository.RepositoryItemSearchLookUpEdit repositoryItemSearchLookUpEdit1;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemSearchLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.LayoutViewColumn layoutViewColumn2;
        private DevExpress.XtraEditors.Repository.RepositoryItemImageEdit repositoryItemImageEdit1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit LookUpList;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit TextEditlist;
        private DevExpress.XtraGrid.Views.Layout.LayoutViewField layoutViewField_colAPPNAME;
        private DevExpress.XtraGrid.Views.Layout.LayoutViewField layoutViewField_colIMAGE;
        private DevExpress.XtraGrid.Views.Layout.LayoutViewField layoutViewField_layoutViewColumn1;
        private DevExpress.XtraGrid.Views.Layout.LayoutViewField layoutViewField_layoutViewColumn2;
        private DevExpress.XtraGrid.Views.Layout.LayoutViewCard layoutViewCard1;
        private DevExpress.XtraLayout.EmptySpaceItem item1;
        private DevExpress.XtraLayout.EmptySpaceItem item2;
        private DevExpress.XtraLayout.EmptySpaceItem item3;
        private DevExpress.XtraLayout.EmptySpaceItem item4;
        private DevExpress.XtraLayout.EmptySpaceItem item5;
        private DevExpress.XtraLayout.EmptySpaceItem item6;
        private DevExpress.XtraLayout.EmptySpaceItem item7;
        private DevExpress.XtraLayout.EmptySpaceItem item8;
        private DevExpress.XtraLayout.EmptySpaceItem item9;
        private DevExpress.XtraLayout.EmptySpaceItem item10;
        private DevExpress.XtraLayout.EmptySpaceItem item11;
        private DevExpress.XtraLayout.EmptySpaceItem item12;
        private DevExpress.XtraLayout.EmptySpaceItem item13;
        private DevExpress.XtraLayout.EmptySpaceItem item14;
    }
}