﻿using DevExpress.XtraSplashScreen;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

using System.Diagnostics;
using DevExpress.XtraEditors;
using System.Data;
using System.Collections;
using PlatCommonForm;

namespace TJHisPlat
{
    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main(string[] args)
        {
            try
            {
                Application.EnableVisualStyles();                
                //-----------2016-07-19 梁吉创建 检查单个实例，不用可以注释
                Environment.CurrentDirectory = System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetAssembly(typeof(Program)).CodeBase).Replace("file:\\", "");

                //注释单个实例检查，医院要求  by 梁吉 2016-12-10
                if (RunningInstance() != null)
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("程序已经有一个实例在运行！", "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
           
                //-- 梁吉 获取数据连接信息，增加方便跟踪的数据库连接方式
                Utility.UntilityConstant.DataConnectionString = Utility.ConfigHelper.GetConfigConnectionStr();

                if (string.IsNullOrEmpty(Utility.UntilityConstant.DataConnectionString))
                {
                    XtraMessageBox.Show("数据库连接未配置!");
                    Application.Exit();
                    return;
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
            //Lis和PACS打印地址获取
            PlatCommon.SysBase.SystemParm.Lis_print_url = Utility.ConfigHelper.GetConfigString("LIS_PRINT_URL");
            PlatCommon.SysBase.SystemParm.Pacs_print_url = Utility.ConfigHelper.GetConfigString("PACS_PRINT_URL");
            
            //登录界面
            FrmLoginNew frm = new FrmLoginNew();
            System.Windows.Forms.Control.CheckForIllegalCrossThreadCalls = false;
            
            #region 增加设置登录参数
            //判断
            string userId = "";
            string userPw = "";

            if (args.Length > 0)
            {
                //带启动参数的方式
                string[] arr = args[0].Trim().Split(new string[] { "|" }, StringSplitOptions.None);

                if (arr.Length >= 2)
                {
                    userId = arr[0];
                    userPw = arr[1];                  

                    if (!string.IsNullOrEmpty(userId) && (!string.IsNullOrEmpty(userPw)))
                    {
                        //开始验证登录用户，加载登录信息
                        try
                        {
                            if (LoginCommand(userId.ToUpper(), userPw.ToUpper()))
                            {
                                // A 特殊用户直接进入主界面（系统管理SYSTEMMGR界面）
                                if ("TJSYSTEM".Equals(userId.ToUpper()))
                                {
                                    SplashScreenManager.ShowForm((Form)null, typeof(frmSplashScreen), true, true);
                                    FrmNewMain frmfnm = new FrmNewMain();

                                    DataTable dtMenu = CreateMenu("TJSYSTEM");
                                    
                                    frmfnm.AppCode = dtMenu.Rows[0]["appcode"].ToString();
                                    frmfnm.AppName = dtMenu.Rows[0]["appname"].ToString();
                                    frmfnm.DeptCode = dtMenu.Rows[0]["deptcode"].ToString();                                                                      
                                    frmfnm.DeptName = dtMenu.Rows[0]["deptname"].ToString();
                                    frmfnm.EncryptCode = dtMenu.Rows[0]["Encrypt_Code"].ToString();

                                    InitFrmNewMain(frmfnm, dtMenu.Rows[0]["deptcode"].ToString());

                                    Application.Run(frmfnm);
                                    return;
                                }
                                else
                                {
                                    // B 正常用户登录

                                    DataTable dt = CreateMenu(PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME);
                                    //如果dt只有一条记录直接进入模块
                                    if (dt.Rows.Count == 1)
                                    {
                                        SplashScreenManager.ShowForm((Form)null, typeof(frmSplashScreen), true, true);
                                        FrmNewMain frmfnm = new FrmNewMain();
                                        frmfnm.AppCode = dt.Rows[0]["appcode"].ToString();
                                        frmfnm.AppName = dt.Rows[0]["appname"].ToString();
                                        frmfnm.DeptCode = dt.Rows[0]["deptcode"].ToString();
                                        frmfnm.DeptName = dt.Rows[0]["deptname"].ToString();
                                        frmfnm.EncryptCode = dt.Rows[0]["Encrypt_Code"].ToString();

                                        InitFrmNewMain(frmfnm, dt.Rows[0]["deptcode"].ToString());

                                        //20230419 lilei 单独上护理需要增加护理病历模板的判断没有这个如果只有一个科室权限的用户上护理病历报错
                                        if (frmfnm.AppCode.Equals("NURSWS") || frmfnm.AppCode.Equals("NURINPREC"))
                                        {
                                            frmfnm.WardCode = dt.Rows[0]["deptcode"].ToString();
                                            frmfnm.WardName = dt.Rows[0]["deptname"].ToString();
                                            frmfnm.WardList = "'" + frmfnm.WardCode + "'";
                                            frmfnm.DeptList = "'" + frmfnm.WardCode + "'";
                                        }                                        
                                        Application.Run(frmfnm);
                                    }
                                    else if (dt.Rows.Count > 1)
                                    {
                                        SplashScreenManager.ShowForm((Form)null, typeof(frmSplashScreen), true, true);
                                        //进入选择界面
                                        frmLayoutView fsdr = new frmLayoutView(dt);
                                        Application.Run(fsdr);
                                    }
                                    return;
                                }
                            }
                            else
                            {
                                Application.Exit();
                            }
                        }
                        catch (Exception ex)
                        {
                            XtraMessageBox.Show("登录异常，请检查数据看配置", "异常信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            Application.Exit();
                        }
                    }
                    else
                    {
                        XtraMessageBox.Show("登录异常，用户名密码不能为空", "异常信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        Application.Exit();
                    }
                }
            }            
            else
            {
                //无启动参数
                try
                {
                    if (frm.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                    {

                        // A 特殊用户直接进入主界面
                        if ("TJSYSTEM".Equals(PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME.ToUpper()))
                        {
                            SplashScreenManager.ShowForm((Form)null, typeof(frmSplashScreen), true, true);
                            DataTable dtMenu = CreateMenu("TJSYSTEM");
                            if (dtMenu.Rows.Count == 0)
                            {
                                XtraMessageBox.Show("TJSYSTEM没有分配权限可能是该院区没有授权", "提示");
                                return;
                            }
                            FrmNewMain frmfnm = new FrmNewMain();
                            frmfnm.DeptCode = dtMenu.Rows[0]["deptcode"].ToString();
                            frmfnm.AppCode = dtMenu.Rows[0]["appcode"].ToString();
                            frmfnm.DeptName = dtMenu.Rows[0]["deptname"].ToString();
                            frmfnm.EncryptCode = dtMenu.Rows[0]["Encrypt_Code"].ToString();
                            frmfnm.AppName = dtMenu.Rows[0]["appname"].ToString();

                            InitFrmNewMain(frmfnm, dtMenu.Rows[0]["deptcode"].ToString());

                            Application.Run(frmfnm);
                            return;
                        }
                        else
                        {
                            // B 正常用户登录
                            DataTable dt = CreateMenu(PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME);

                            //如果dt只有一条记录直接进入
                            if (dt.Rows.Count == 1)
                            {
                                SplashScreenManager.ShowForm((Form)null, typeof(frmSplashScreen), true, true);
                                FrmNewMain frmfnm = new FrmNewMain();
                                frmfnm.AppCode = dt.Rows[0]["appcode"].ToString();
                                frmfnm.AppName = dt.Rows[0]["appname"].ToString();
                                frmfnm.DeptCode = dt.Rows[0]["deptcode"].ToString();
                                frmfnm.DeptName = dt.Rows[0]["deptname"].ToString();
                                frmfnm.EncryptCode = dt.Rows[0]["Encrypt_Code"].ToString();

                                InitFrmNewMain(frmfnm, dt.Rows[0]["deptcode"].ToString());

                                //20230419 lilei 没有这个如果只有一个科室权限的上护理病历报错
                                if (frmfnm.AppCode.Equals("NURSWS") || frmfnm.AppCode.Equals("NURINPREC"))
                                {
                                    frmfnm.WardCode = dt.Rows[0]["deptcode"].ToString();
                                    frmfnm.WardName = dt.Rows[0]["deptname"].ToString();
                                    frmfnm.WardList = "'" + frmfnm.WardCode + "'";
                                    frmfnm.DeptList = "'" + frmfnm.WardCode + "'";
                                }
                                
                                Application.Run(frmfnm);
                            }
                            else if (dt.Rows.Count > 1)
                            {
                                SplashScreenManager.ShowForm((Form)null, typeof(frmSplashScreen), true, true);
                                frmLayoutView frmView = new frmLayoutView(dt);
                                Application.Run(frmView);
                            }
                            else
                            {
                                XtraMessageBox.Show("没有分配权限", "提示");
                            }
                            return;
                        }
                    }
                    else
                    {
                        Application.Exit();
                    }
                }
                catch (Exception ex)
                {
                    XtraMessageBox.Show(ex.Message);
                }
            }
            #endregion
        }

        /// <summary>
        /// 判断
        /// </summary>
        /// <returns></returns>
        public static Process RunningInstance()
        {
            Process currentProcess = Process.GetCurrentProcess();
            Process[] sameProcess = Process.GetProcessesByName
     (System.IO.Path.GetFileNameWithoutExtension(AppDomain.CurrentDomain.BaseDirectory + currentProcess.ProcessName));
            if (sameProcess != null && sameProcess.Length > 0)
            {
                //遍历正在有相同名字运行的例程     
                foreach (Process process in sameProcess)
                {      //忽略现有的例程  
                    if (process.Id != currentProcess.Id && (currentProcess.StartTime - process.StartTime).TotalMilliseconds <= 0)
                    {
                        return process;
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// 登录函数,验证用户
        /// </summary>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="appname"></param>
        public static bool LoginCommand(string username, string password)
        {
            NM_Service.NMService.STAFF_DICTClient client = new NM_Service.NMService.STAFF_DICTClient();
            int result = client.LoginValidate_STAFF_DICT(username.ToUpper(), password.ToUpper());
            switch (result)
            {
                case -1:
                    XtraMessageBox.Show("用户不存在！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break;
                case 0:
                    XtraMessageBox.Show("密码错误！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break;
                case 1:
                    PlatCommon.SysBase.SystemParm.LoginUser = client.GetModelByUserName_STAFF_DICT(username);
                    PlatCommon.SysBase.SystemParm.HisUnitCode = Utility.ConfigHelper.GetConfiguration("UNITCODE");
                    #region 根据参数初始化CA，以免用户使用用户名密码登录后，后续使用ca签名时出错。
                    bool caEnabled = Tjhis.Interface.CA.DbExt.getCaEnabled(PlatCommon.SysBase.SystemParm.HisUnitCode, "", "", "");
                    string CA_USER_ENABLED = Tjhis.Interface.CA.DbExt.getCaEnabledByUserName(username.ToUpper()) ? "1" : "0";
                    PlatCommon.SysBase.SystemParm.LoginUser.CA_ENABLED = CA_USER_ENABLED;
                    //MessageBox.Show($"caEnabled:{caEnabled}, CA_USER_ENABLED:{CA_USER_ENABLED}");
                    if (caEnabled && CA_USER_ENABLED.Equals("1"))
                    {
                        if (PlatCommon.SysBase.SystemParm.CaBusiness == null)
                        {
                            PlatCommon.SysBase.SystemParm.CaBusiness = new Tjhis.Interface.CA.CaBusiness();
                            PlatCommon.SysBase.SystemParm.CaBusiness.HisUnitCode = PlatCommon.SysBase.SystemParm.HisUnitCode;
                            PlatCommon.SysBase.SystemParm.CaBusiness.Init();
                        }
                    }
                    #endregion
                    //增加特殊权限用户 分配权限 by 梁吉 2016-08-25
                    if ("TJSYSTEM".Equals(PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME.ToUpper()))
                    {
                        return true;
                    }                  
                    Utility.ConfigHelper.SetConfiguration("LastLoginUser", username);
                    return true;
                case 2:
                    XtraMessageBox.Show("用户已锁定，请联系管理员！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break;
                default:
                    XtraMessageBox.Show("未知错误！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break;
            }
            return false;
        }

        /// <summary>
        /// 根据用户创建菜单
        /// </summary>        
        public static DataTable CreateMenu(string userID)
        {
            //创建表 存储操作员可使用的药房或护理单元
            DataTable deptTables = new DataTable();

            deptTables.Columns.Add("appcode", Type.GetType("System.String"));
            deptTables.Columns.Add("appname", Type.GetType("System.String"));
            deptTables.Columns.Add("deptcode", Type.GetType("System.String"));
            deptTables.Columns.Add("deptname", Type.GetType("System.String"));
            deptTables.Columns.Add("imageindex", Type.GetType("System.Int32"));
            deptTables.Columns.Add("Encrypt_Code", Type.GetType("System.String"));
            deptTables.Columns.Add("hisunitcode", Type.GetType("System.String"));

            Random randObj = new Random();
            string unitcode = Utility.ConfigHelper.GetConfigString("UNITCODE");
            string sql = @"SELECT T1.App_Code,T6.APPLICATION_NAME,T6.Encrypt_Code,T6.HIS_UNIT_CODE  
                            FROM MR_APP_ENTRY T1 
                            LEFT JOIN MR_RIGHT_VS_UI_OBJECT T2 ON T1.NODE_CODE = T2.UI_OBJECT_ID 
                            LEFT JOIN MR_ROLE_RIGHT T3 ON T2.RIGHT_ID = T3.RIGHT_ID LEFT JOIN MR_USER_ROLE T4 ON T3.ROLE_CODE = T4.ROLE_CODE 
                            LEFT JOIN STAFF_DICT T5 ON UPPER(T5.USER_NAME) = UPPER(T4.DB_USER) LEFT JOIN MR_ROLE_DICT T7 ON T4.ROLE_CODE=T7.ROLE_CODE 
                            LEFT JOIN MR_RIGHT_DICT T8 ON T8.RIGHT_ID=T2.RIGHT_ID  
                            LEFT JOIN encrypt_dict T6 ON T6.APPLICATION_CODE = T1.App_Code ";
            if (userID.Equals("TJSYSTEM"))
            {
                sql += " WHERE T1.App_Code = 'SYSTEMMGR' AND T2.ENABLE = 1 AND T1.STATUS = 1 AND T1.FORM_CONTROL IS NULL  ";
            }
            else
            {
                sql += " WHERE T5.USER_NAME = '" + userID + "' AND T2.ENABLE = 1 AND T1.STATUS = 1 AND T1.FORM_CONTROL IS NULL  AND T8.HIS_UNIT_CODE='" + unitcode + "'  AND T7.HIS_UNIT_CODE='" + unitcode + "'  AND T5.HIS_UNIT_CODE='" + unitcode + "'  ";
            }
            sql += " AND T6.HIS_UNIT_CODE='" + unitcode + "' AND T6.APPLICATION_VERSION='6.6'   ";//获取6.6授权版本，因当前是验证6.6版本，后面建议版本加在配置文件中 Yzw20230411
            sql += " GROUP BY T1.App_Code,T6.APPLICATION_NAME,T6.Encrypt_Code,T6.HIS_UNIT_CODE order by T1.App_Code ";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            DataTable dt = spc.GetList(sql).Tables[0];         
                                  
            string appCode, appName, encryptCode, hisunitcode; 
            string msg = "";
            string msglist = "";

            //循环判断授权 考虑要到期的值
            foreach (DataRow item in dt.Rows)
            {
                appCode = item["app_code"].ToString();
                appName = item["application_name"].ToString();
                encryptCode = item["Encrypt_Code"].ToString();
                hisunitcode = item["HIS_UNIT_CODE"].ToString();

                //判断住院工作站权限
                if ("DOCTWS".Equals(appCode.ToUpper()))
                {
                    //住院科室显示改成医生所属 病区(原)
                    string sqlstr = "select a.dept_name from dept_dict a where a.dept_code = '"+ PlatCommon.SysBase.SystemParm.LoginUser.DEPT_CODE+"'";
                    DataSet ds = spc.GetDataBySql(sqlstr);
                    DataRow newRow = deptTables.NewRow();
                    newRow["appcode"] = appCode;
                    newRow["appname"] = appName;                    
                    newRow["deptcode"] = PlatCommon.SysBase.SystemParm.LoginUser.DEPT_CODE;
                    if (ds!=null && ds.Tables[0].Rows.Count>0)
                    {
                        newRow["deptname"] = ds.Tables[0].Rows[0]["DEPT_NAME"].ToString(); ;
                    }
                    newRow["imageindex"] = randObj.Next(0, 10);
                    newRow["Encrypt_Code"] = encryptCode;
                    newRow["hisunitcode"] = hisunitcode;
                    deptTables.Rows.Add(newRow);    
                }
                else if ("DISINFECT".Equals(appCode.ToUpper()))
                {
                    string sqlstr = "select a.group_code,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and a.group_class = '供应室管理' and emp_no = '" + PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO + "'  and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
                    DataSet ds = spc.GetDataBySql(sqlstr);
                    if (ds == null) continue;
                    if (ds.Tables[0].Rows.Count < 1) continue;
                    
                    //添加护理单元
                    foreach (DataRow drrow in ds.Tables[0].Rows)
                    {
                        DataRow newRow = deptTables.NewRow();
                        newRow["appcode"] = appCode;
                        newRow["appname"] = appName;
                        newRow["deptcode"] = drrow["group_code"].ToString();
                        newRow["deptname"] = drrow["dept_name"].ToString();
                        newRow["imageindex"] = randObj.Next(0, 10);
                        newRow["Encrypt_Code"] = encryptCode;
                        newRow["hisunitcode"] = hisunitcode;
                        deptTables.Rows.Add(newRow);
                    }
                }
                else if ("NURSWS".Equals(appCode.ToUpper()) || "NURINPREC".Equals(appCode.ToUpper()))
                {
                    string sqlstr = "select a.group_code,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and a.group_class = '病区护士' and emp_no = '" + PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO + "'  and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
                    
                    DataSet ds = spc.GetDataBySql(sqlstr);
                    if (ds == null)
                    {
                        continue;
                    }
                    int count = ds.Tables[0].Rows.Count;
                    if (count < 1)
                    {
                        continue;
                    }
                    //添加护理单元
                    foreach (DataRow drrow in ds.Tables[0].Rows)
                    {
                        DataRow newRow = deptTables.NewRow();
                        newRow["appcode"] = appCode;
                        newRow["appname"] = appName;
                        newRow["deptcode"] = drrow["group_code"].ToString();
                        newRow["deptname"] = drrow["dept_name"].ToString();
                        newRow["imageindex"] = randObj.Next(0, 10);
                        newRow["Encrypt_Code"] = encryptCode;
                        newRow["hisunitcode"] = hisunitcode;
                        deptTables.Rows.Add(newRow);
                    }
                }               
                else if ("NUROB".Equals(appCode.ToUpper()))
                {
                    string sqlstr = "select a.group_code,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and a.group_class = '留观护士' and emp_no = '" + PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO + "'  and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
                    DataSet ds = spc.GetDataBySql(sqlstr);
                    if (ds == null)
                    {
                        continue;
                    }
                    int count = ds.Tables[0].Rows.Count;
                    if (count < 1)
                    {
                        continue;
                    }
                    //添加护理单元
                    foreach (DataRow drrow in ds.Tables[0].Rows)
                    {
                        DataRow newRow = deptTables.NewRow();
                        newRow["appcode"] = appCode;
                        newRow["appname"] = appName;
                        newRow["deptcode"] = drrow["group_code"].ToString();
                        newRow["deptname"] = drrow["dept_name"].ToString();
                        newRow["imageindex"] = randObj.Next(0, 10);
                        newRow["Encrypt_Code"] = encryptCode;
                        newRow["hisunitcode"] = hisunitcode;
                        deptTables.Rows.Add(newRow);
                    }
                }
                else if ("PHSTOCK".Equals(appCode.ToUpper()))
                {
                    sql = "select a.group_code,b.dept_name,b.input_code from staff_vs_group a,dept_dict b where a.group_code = b.dept_code ";
                    sql += " and emp_no =:emp_no and group_class =:group_class ";
                    spc = new NM_Service.NMService.ServerPublicClient();
                    List<string> para = new List<string>();
                    ArrayList para_val = new ArrayList();
                    para.Add("emp_no");
                    para.Add("group_class");
                    para_val.Add(userID);
                    para_val.Add("药库管理");
                    DataSet ds = spc.GetDataTable_Para(sql, para, para_val);
                    if (ds == null || ds.Tables.Count < 1 || ds.Tables[0].Rows.Count < 1)
                    {
                        continue;
                    }
                    foreach (DataRow drrow in ds.Tables[0].Rows)
                    {
                        DataRow newRow = deptTables.NewRow();
                        newRow["appcode"] = appCode;
                        newRow["appname"] = appName;
                        newRow["deptcode"] = drrow["group_code"].ToString();
                        newRow["deptname"] = drrow["dept_name"].ToString();
                        newRow["imageindex"] = randObj.Next(0, 10);
                        newRow["Encrypt_Code"] = encryptCode;
                        newRow["hisunitcode"] = hisunitcode;
                        deptTables.Rows.Add(newRow);
                    }
                }
                else if ("STOCKMGR".Equals(appCode.ToUpper()))
                {
                    sql = "select a.group_code,b.dept_name,b.input_code from staff_vs_group a,dept_dict b where a.group_code = b.dept_code ";
                    sql += " and emp_no =:emp_no and group_class =:group_class ";
                    spc = new NM_Service.NMService.ServerPublicClient();
                    List<string> para = new List<string>();
                    ArrayList para_val = new ArrayList();
                    para.Add("emp_no");
                    para.Add("group_class");
                    para_val.Add(userID);
                    para_val.Add("药房管理");
                    DataSet ds = spc.GetDataTable_Para(sql, para, para_val);
                    if (ds == null || ds.Tables.Count < 1 || ds.Tables[0].Rows.Count < 1)
                    {
                        continue;
                    }
                    foreach (DataRow drrow in ds.Tables[0].Rows)
                    {
                        DataRow newRow = deptTables.NewRow();
                        newRow["appcode"] = appCode;
                        newRow["appname"] = appName;
                        newRow["deptcode"] = drrow["group_code"].ToString();
                        newRow["deptname"] = drrow["dept_name"].ToString();
                        newRow["imageindex"] = randObj.Next(0, 10);
                        newRow["Encrypt_Code"] = encryptCode;
                        newRow["hisunitcode"] = hisunitcode;
                        deptTables.Rows.Add(newRow);
                    }
                }
                else if ("OUTPDOCT".Equals(appCode))
                {
                    sql = "select a.group_code,b.dept_name,b.input_code from staff_vs_group a,dept_dict b where a.group_code = b.dept_code ";
                    sql += " and emp_no =:emp_no and group_class =:group_class ";
                    spc = new NM_Service.NMService.ServerPublicClient();
                    List<string> para = new List<string>();
                    ArrayList para_val = new ArrayList();
                    para.Add("emp_no");
                    para.Add("group_class");
                    para_val.Add(PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO);
                    para_val.Add("门诊医生");
                    DataSet ds = spc.GetDataTable_Para(sql, para, para_val);
                    if (ds == null || ds.Tables.Count < 1 || ds.Tables[0].Rows.Count < 1)
                    {
                        continue;
                    }
                    foreach (DataRow drrow in ds.Tables[0].Rows)
                    {
                        DataRow newRow = deptTables.NewRow();
                        newRow["appcode"] = appCode;
                        newRow["appname"] = appName;
                        newRow["deptcode"] = drrow["group_code"].ToString();
                        newRow["deptname"] = drrow["dept_name"].ToString();
                        newRow["imageindex"] = randObj.Next(0, 10);
                        newRow["Encrypt_Code"] = encryptCode;
                        newRow["hisunitcode"] = hisunitcode;
                        deptTables.Rows.Add(newRow);
                    }                    
                }
                else if ("PRESDISP".Equals(appCode))
                {
                    sql = "select a.group_code,b.dept_code,b.dept_name,b.input_code from staff_vs_group a,dept_dict b where a.group_code = b.dept_code ";
                    sql += " and emp_no =:emp_no and group_class =:group_class ";
                    List<string> para = new List<string>();
                    ArrayList para_val = new ArrayList();
                    para.Add("emp_no");
                    para.Add("group_class");
                    para_val.Add(PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO);
                    para_val.Add("药房管理");
                    DataSet ds = spc.GetDataTable_Para(sql, para, para_val);
                    if (ds == null || ds.Tables.Count < 1 || ds.Tables[0].Rows.Count < 1)
                    {
                        continue;
                    }
                    foreach (DataRow drrow in ds.Tables[0].Rows)
                    {
                        DataRow newRow = deptTables.NewRow();
                        newRow["appcode"] = appCode;
                        newRow["appname"] = appName;
                        newRow["deptcode"] = drrow["group_code"].ToString();
                        newRow["deptname"] = drrow["dept_name"].ToString();
                        newRow["imageindex"] = randObj.Next(0, 10);
                        newRow["Encrypt_Code"] = encryptCode;
                        newRow["hisunitcode"] = hisunitcode;
                        deptTables.Rows.Add(newRow);
                    }                    
                }
                else if ("ORDDISP".Equals(appCode))
                {
                    sql = "SELECT DEPT_CODE GROUP_CODE,DEPT_NAME,INPUT_CODE FROM DEPT_DICT , STAFF_VS_GROUP WHERE DEPT_DICT.DEPT_CODE= STAFF_VS_GROUP.GROUP_CODE AND STAFF_VS_GROUP.EMP_NO =:p1 AND STAFF_VS_GROUP.GROUP_CLASS =:p2  ORDER BY DEPT_DICT.DEPT_NAME    ASC";
                    List<string> paras = new List<string>();
                    ArrayList values = new ArrayList();
                    paras.Add("p1");
                    paras.Add("p2");
                    values.Add(PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO);
                    values.Add("药房管理");
                    
                    DataSet ds = spc.GetDataTable_Para(sql, paras, values);
                    if (ds == null || ds.Tables.Count == 0 || ds.Tables[0].Rows.Count == 0)
                    {
                        continue;
                    }
                    foreach (DataRow drrow in ds.Tables[0].Rows)
                    {
                        DataRow newRow = deptTables.NewRow();
                        newRow["appcode"] = appCode;
                        newRow["appname"] = appName;
                        newRow["deptcode"] = drrow["group_code"].ToString();
                        newRow["deptname"] = drrow["dept_name"].ToString();
                        newRow["imageindex"] = randObj.Next(0, 10);
                        newRow["Encrypt_Code"] = encryptCode;
                        newRow["hisunitcode"] = hisunitcode;
                        deptTables.Rows.Add(newRow);
                    }                                          
                }
                else if ("OPERBILL".Equals(appCode.ToUpper()))
                {
                    sql = "SELECT DEPT_CODE GROUP_CODE,DEPT_NAME,INPUT_CODE FROM DEPT_DICT , STAFF_VS_GROUP WHERE DEPT_DICT.DEPT_CODE= STAFF_VS_GROUP.GROUP_CODE AND STAFF_VS_GROUP.EMP_NO =:p1 AND STAFF_VS_GROUP.GROUP_CLASS =:p2  ORDER BY DEPT_DICT.DEPT_NAME    ASC";
                    List<string> paras = new List<string>();
                    ArrayList values = new ArrayList();
                    paras.Add("p1");
                    paras.Add("p2");
                    values.Add(PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO);
                    values.Add("手术医生");
                    string sql2 = "SELECT DEPT_CODE GROUP_CODE,DEPT_NAME,INPUT_CODE FROM DEPT_DICT , STAFF_VS_GROUP WHERE DEPT_DICT.DEPT_CODE= STAFF_VS_GROUP.GROUP_CODE AND STAFF_VS_GROUP.EMP_NO =:p1 AND STAFF_VS_GROUP.GROUP_CLASS =:p2  ORDER BY DEPT_DICT.DEPT_NAME    ASC";
                    List<string> paras2 = new List<string>();
                    ArrayList values2 = new ArrayList();
                    paras2.Add("p1");
                    paras2.Add("p2");
                    values2.Add(PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO);
                    values2.Add("麻醉医生");
                    try
                    {
                        DataSet ds = spc.GetDataTable_Para(sql, paras, values);
                        DataSet ds2 = spc.GetDataTable_Para(sql2, paras2, values2);
                        if ((ds == null || ds.Tables[0].Rows.Count == 0) && (ds2 == null || ds2.Tables[0].Rows.Count == 0))
                        {
                            XtraMessageBox.Show("你所在的分组没有手术医生,所以无权使用本系统！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            continue;
                        }
                        if ((ds == null || ds.Tables[0].Rows.Count == 0))
                        {
                            ds = ds2;
                        }
                        else
                        {
                            if (ds2 != null && ds2.Tables.Count > 0 && ds2.Tables[0].Rows.Count > 0)
                            {
                                foreach (DataRow dr in ds2.Tables[0].Rows)
                                {
                                    ds.Tables[0].ImportRow(dr);
                                }
                            }
                        }
                        foreach (DataRow drrow in ds.Tables[0].Rows)
                        {
                            DataRow newRow = deptTables.NewRow();
                            newRow["appcode"] = appCode;
                            newRow["appname"] = appName;
                            newRow["deptcode"] = drrow["group_code"].ToString();
                            newRow["deptname"] = drrow["dept_name"].ToString();
                            newRow["imageindex"] = randObj.Next(0, 10);
                            newRow["Encrypt_Code"] = encryptCode;
                            newRow["hisunitcode"] = hisunitcode;
                            deptTables.Rows.Add(newRow);
                        }
                    }
                    catch
                    {
                        continue;
                    }
                }
                else if ("EXSTOCK".Equals(appCode.ToUpper()))
                {
                    sql = "SELECT DEPT_CODE GROUP_CODE,DEPT_NAME,INPUT_CODE FROM DEPT_DICT , STAFF_VS_GROUP WHERE DEPT_DICT.DEPT_CODE= STAFF_VS_GROUP.GROUP_CODE AND STAFF_VS_GROUP.EMP_NO =:p1 AND STAFF_VS_GROUP.GROUP_CLASS =:p2  ORDER BY DEPT_DICT.DEPT_NAME    ASC";
                    List<string> paras = new List<string>();
                    ArrayList values = new ArrayList();
                    paras.Add("p1");
                    paras.Add("p2");
                    values.Add(PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO);
                    values.Add("耗材管理");
                    
                    DataSet ds = spc.GetDataTable_Para(sql, paras, values);
                    if (ds == null || ds.Tables[0].Rows.Count == 0)
                    {
                        XtraMessageBox.Show("你所在的分组没有耗材管理,所以无权使用本系统！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        continue;
                    }                        
                    foreach (DataRow drrow in ds.Tables[0].Rows)
                    {
                        DataRow newRow = deptTables.NewRow();
                        newRow["appcode"] = appCode;
                        newRow["appname"] = appName;
                        newRow["deptcode"] = drrow["group_code"].ToString();
                        newRow["deptname"] = drrow["dept_name"].ToString();
                        newRow["imageindex"] = randObj.Next(0, 10);
                        newRow["Encrypt_Code"] = encryptCode;
                        newRow["hisunitcode"] = hisunitcode;
                        deptTables.Rows.Add(newRow);
                    }                    
                }
                else if ("EQUIP".Equals(appCode.ToUpper()))
                {
                    sql = "SELECT DEPT_CODE GROUP_CODE,DEPT_NAME,INPUT_CODE FROM DEPT_DICT , STAFF_VS_GROUP WHERE DEPT_DICT.DEPT_CODE= STAFF_VS_GROUP.GROUP_CODE AND STAFF_VS_GROUP.EMP_NO =:p1 AND STAFF_VS_GROUP.GROUP_CLASS =:p2  ORDER BY DEPT_DICT.DEPT_NAME    ASC";
                    List<string> paras = new List<string>();
                    ArrayList values = new ArrayList();
                    paras.Add("p1");
                    paras.Add("p2");
                    values.Add(PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO);
                    values.Add("设备管理");
                    try
                    {
                        DataSet ds = spc.GetDataTable_Para(sql, paras, values);
                        if (ds == null || ds.Tables[0].Rows.Count == 0)
                        {
                            XtraMessageBox.Show("你所在的分组没有设备管理,所以无权使用本系统！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            continue;
                        }
                        foreach (DataRow drrow in ds.Tables[0].Rows)
                        {
                            DataRow newRow = deptTables.NewRow();
                            newRow["appcode"] = appCode;
                            newRow["appname"] = appName;
                            newRow["deptcode"] = drrow["group_code"].ToString();
                            newRow["deptname"] = drrow["dept_name"].ToString();
                            newRow["imageindex"] = randObj.Next(0, 10);
                            newRow["Encrypt_Code"] = encryptCode;
                            newRow["hisunitcode"] = hisunitcode;
                            deptTables.Rows.Add(newRow);
                        }
                    }
                    catch
                    {
                        continue;
                    }
                }
                else if ("EXAMAPT".Equals(appCode.ToUpper()))//检查确认选择科室界面
                {
                    string sql1 = "SELECT ROWNUM ROWINDEX,DEPT_DICT.DEPT_CODE, DEPT_DICT.DEPT_NAME, DEPT_DICT.INPUT_CODE,";
                    sql1 += "       (SELECT PARAMETER_VALUE FROM app_configer_parameter ";
                    sql1 += "  WHERE app_name = 'EXAMAPT' and parameter_name = 'EXAM_CLASS' and dept_code = dept_dict.dept_code and his_unit_code = '"+PlatCommon.SysBase.SystemParm.HisUnitCode+"') EXAM_CLASS" +
                        " FROM DEPT_DICT WHERE (DEPT_CODE IN (SELECT S.GROUP_CODE FROM STAFF_VS_GROUP S WHERE S.GROUP_CLASS = '检查医生' AND S.EMP_NO = '" + PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO + "')) ORDER BY ROWINDEX, DEPT_DICT.DEPT_NAME ASC";

                    DataSet ds = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql1);
                    if (ds == null || ds.Tables[0].Rows.Count < 1)
                    {
                        DevExpress.XtraEditors.XtraMessageBox.Show("1.你所在的分组没有检查确认属性,所以无权使用本系统", "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        continue;
                    }
                    foreach (DataRow drrow in ds.Tables[0].Rows)
                    {
                        DataRow newRow = deptTables.NewRow();
                        newRow["appcode"] = appCode;
                        newRow["appname"] = appName;
                        newRow["deptcode"] = drrow["DEPT_CODE"].ToString();
                        newRow["deptname"] = drrow["dept_name"].ToString();
                        newRow["imageindex"] = randObj.Next(0, 10);
                        newRow["Encrypt_Code"] = encryptCode;
                        newRow["hisunitcode"] = hisunitcode;
                        deptTables.Rows.Add(newRow);
                    }
                }
                else if (appCode.ToUpper().Equals("ADMIT"))
                {
                    //取登陆人所在科室
                    DataTable dt1 = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select dept_name from dept_dict where dept_code = '" + PlatCommon.SysBase.SystemParm.LoginUser.DEPT_CODE + "'").Tables[0];                
                    DataRow newRow = deptTables.NewRow();
                    newRow["appcode"] = appCode;
                    newRow["appname"] = appName;
                    newRow["deptcode"] = PlatCommon.SysBase.SystemParm.LoginUser.DEPT_CODE;
                    newRow["deptname"] = dt1.Rows[0][0].ToString();
                    newRow["imageindex"] = randObj.Next(0, 10);
                    newRow["Encrypt_Code"] = encryptCode;
                    newRow["hisunitcode"] = hisunitcode;
                    deptTables.Rows.Add(newRow);
                }
                else if (appCode.ToUpper().Equals("EMRINP"))
                {
                    //住院科室显示改成医生所属科室（同住院医生站）
                    string sqlstr = "select a.dept_name from dept_dict a where a.dept_code = '" + PlatCommon.SysBase.SystemParm.LoginUser.DEPT_CODE + "'";
                    DataSet ds = spc.GetDataBySql(sqlstr);
                    DataRow newRow = deptTables.NewRow();
                    newRow["appcode"] = appCode;
                    newRow["appname"] = appName;                    
                    newRow["deptcode"] = PlatCommon.SysBase.SystemParm.LoginUser.DEPT_CODE;
                    if (ds != null && ds.Tables[0].Rows.Count > 0)
                    {
                        newRow["deptname"] = ds.Tables[0].Rows[0]["DEPT_NAME"].ToString(); ;
                    }          
                    newRow["imageindex"] = randObj.Next(0, 10);
                    newRow["Encrypt_Code"] = encryptCode;
                    newRow["hisunitcode"] = hisunitcode;
                    deptTables.Rows.Add(newRow);                   
                }
                else if (appCode.ToUpper().Equals("PRCMGR"))
                {
                    DataTable dt1 = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select dept_name from dept_dict where dept_code = '" + PlatCommon.SysBase.SystemParm.LoginUser.DEPT_CODE + "'").Tables[0];
                   
                    DataRow newRow = deptTables.NewRow();
                    newRow["appcode"] = appCode;
                    newRow["appname"] = appName;
                    newRow["deptcode"] = PlatCommon.SysBase.SystemParm.LoginUser.DEPT_CODE;
                    newRow["deptname"] = dt1.Rows[0][0].ToString();
                    newRow["imageindex"] = randObj.Next(0, 10);
                    newRow["Encrypt_Code"] = encryptCode;
                    newRow["hisunitcode"] = hisunitcode;
                    deptTables.Rows.Add(newRow);
                }
                else
                {
                    DataTable dt1 = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select dept_name from dept_dict where dept_code = '" + PlatCommon.SysBase.SystemParm.LoginUser.DEPT_CODE + "'").Tables[0];
                    
                    DataRow newRow = deptTables.NewRow();
                    newRow["appcode"] = appCode;
                    newRow["appname"] = appName;
                    newRow["deptcode"] = PlatCommon.SysBase.SystemParm.LoginUser.DEPT_CODE;
                    if (dt1.Rows.Count > 0)
                    {
                        newRow["deptname"] = dt1.Rows[0][0].ToString();
                    }
                    newRow["imageindex"] = randObj.Next(0, 10);
                    newRow["Encrypt_Code"] = encryptCode;
                    newRow["hisunitcode"] = hisunitcode;
                    deptTables.Rows.Add(newRow);
                }

                if (msg.Length > 0)
                {
                    msglist = msglist + msg + "\r\n";
                }
            }

            if (msglist.Length > 0)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(msglist, "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            return deptTables;
        }
        /// <summary>
        /// 初始化主窗体的科室列表和病区列表-用于跳过frmLayoutView直接打开主界面的情形（单应用单科室）
        /// </summary>        
        public static void InitFrmNewMain(FrmNewMain frm, string deptCode)
        {
            if(frm != null && !string.IsNullOrEmpty(deptCode))
            {
                frm.DeptList = "'" + deptCode + "'";
                frm.WardList = "'" + PlatCommon.Common.PublicFunction.GetWarcodeVsDept(deptCode) + "'";
            }
        }
    }
}
