﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using PlatCommon.SysBase;
using NM_Service.NMService;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.WinExplorer;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors;


namespace PlatCommonForm
{
    public partial class frmSelectBatchNo : PlatCommon.SysBase.ParentForm
    {
        public frmSelectBatchNo()
        {
            InitializeComponent();
        }
        public string gs_ITEM_CODE;
        public string gs_ITEM_SPEC;
        public string gs_UNITS;
        public string gs_storage;
        public string GS_BATCH_NO;
        DataTable dtPriceList = new DataTable();
        private void frmSelectBatchNo_Load(object sender, EventArgs e)
        {
            loadinit();
        }
        private void loadinit()
        {
            string sqlstr = " select drug_name ,storage,BATCH_NO ,QUANTITY from INPUT_DRUG_LISTS where HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
           sqlstr += " and drug_code = '" + gs_ITEM_CODE + "' and drug_spec = '" + gs_ITEM_SPEC + "' and package_units = '" + gs_UNITS + "'";
           sqlstr += " and storage = '" + gs_storage + "'";
           using (ServerPublicClient client = new ServerPublicClient())
           {
               DataSet ds = client.GetDataBySql(sqlstr);
               if (ds != null)
               {
                   dtPriceList = ds.Tables[0];
               }
               ds.Dispose();
               gridControl1.DataSource = dtPriceList;
           }
        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                gridView1_DoubleClick(sender, e);
            }
            if (e.KeyCode == Keys.Escape)
            {
                Close();
            }     
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            DataRow row = this.gridView1.GetDataRow(this.gridView1.FocusedRowHandle);
            if (row != null)
            {
                GS_BATCH_NO = row["BATCH_NO"].ToString();
                Close();
            }
        }

    }

}
