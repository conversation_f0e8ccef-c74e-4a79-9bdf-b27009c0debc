﻿/**************************************************************************************
 * 公共类，用来返回泛型及其相关属性信息等
 * Auther:wangwz
 * Date:2016-6-25 14:25
 * Modify:NONE
 **************************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Runtime.InteropServices;
using System.Data;

namespace PlatCommon.Common
{
    /// <summary>
    /// 公共类,扩展泛型返回附属信息
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class Result<T>
    {
        #region 私有变量定义
        private T _data;//泛型类，实体数据
        private bool _hasError;//是否报错
        private object _errObj;//错误对象
        private string _msgTip;//返回的提示信息
        #endregion 私有变量定义

        #region 属性定义
        /// <summary>
        /// 类型数据
        /// </summary>
        public T Data
        {
            get { return _data; }
            set { _data = value; }
        }
        /// <summary>
        /// 返回的错误信息
        /// </summary>
        public object ErrObj
        {
            get { return _errObj; }
            set { _errObj = value; }
        }
        /// <summary>
        /// 是否报错
        /// </summary>
        public bool HasError
        {
            get { return _hasError; }
            set { _hasError = value; }
        }
        /// <summary>
        /// 返回提示信息
        /// </summary>
        public string MsgTip
        {
            get { return _msgTip; }
            set { _msgTip = value; }
        }
        #endregion 属性定义

        public Result()
        {
            _hasError = false;
            _msgTip = string.Empty;
            _errObj = null;
            _data = default(T);
        }
        /// <summary>
        /// 清除此类状态
        /// </summary>
        public void Clear()
        {
            _hasError = false;
            _msgTip = string.Empty;
            _errObj = null;
            _data = default(T);
        }
    }
    /// <summary>
    /// 公共函数类
    /// 合理用药外部函数定义
    /// 函数多为静态函数或方法
    /// </summary>
    public partial class commFunct
    {
        #region 结构体定义
        public struct str_Rect
        {
            long Top;
            long Left;
            long Right;
            long Bottom;
        }
        #endregion 结构体定义

        #region 合理用药dll引用

        [DllImport("ShellRunAs.dll")]
        public static extern int RegisterServer();//注册服务器
        [DllImport("DIFPassDll.dll")]
        public static extern int PassInit(String UserName, String DepartMentName, int WorkstationType);//PASS初始化
        [DllImport("DIFPassDll.dll")]
        public static extern int PassSetControlParam(int SaveCheckResult, int AllowAllegen, int CheckMode, int DisqMode, int UseDiposeIdea);//PASS运行模式设置
        [DllImport("DIFPassDll.dll")]
        public static extern int PassSetPatientInfo(string PatientID, string VisitID, string Name, string Sex, string Birthday, string Weight,
                                                 string cHeight,string DepartMentName,string Doctor_name,string LeaveHospitalDate);//传患者基本信息
        [DllImport("DIFPassDll.dll")]
        public static extern int PassSetRecipeInfo(string OrderUniqueCode, string DrugCode, string DrugName, string SingleDose, string DoseUnit,
                                                    string frequency, string StartOrderDate, string StopOrderDate, string RouteName, string GroupTag,
                                                    string OrderType, string OrderDoctor);//传患者药品信息
        [DllImport("DIFPassDll.dll")]
        public static extern int PassSetAllergenInfo(string AllergenIndex, string AllergenCode, string AllergenDesc, string AllergenType, string Reaction);//患者过敏史
        [DllImport("DIFPassDll.dll")]
        public static extern int PassSetMedCond(string MedCondIndex, string MedCondCode, string MedCondDesc, string MedCondType, string StartDate, string EndDate);//病生状态
        [DllImport("DIFPassDll.dll")]
        public static extern int PassSetQueryDrug(string DrugCode, string DrugName, string DoseUnit, string RouteName);//药品信息查询
        [DllImport("DIFPassDll.dll")]
        public static extern int PassGetState(string QueryItemNo);//右键菜单是否可用
        [DllImport("DIFPassDll.dll")]
        public static extern int PassDoCommand(int CommandNo);//PASS功能调用
        [DllImport("DIFPassDll.dll")]
        public static extern int PassGetWarn(string drugUniqueCode);//获取药品获示级别
        [DllImport("DIFPassDll.dll")]
        public static extern int PassSetFloatWinPos(int Left, int Top, int Right, int Bottom);//设置药品浮动窗口位置
        [DllImport("DIFPassDll.dll")]
        public static extern int PassSetWarnDrug(string drugUniqueCode);//设置需要进行单药警告的药品
        [DllImport("DIFPassDll.dll")]
        public static extern int PassQuit();//PASS退出函数
        [DllImport("user32.dll")]
        public static extern ulong GetWindowRect(ulong HWND, ref str_Rect lpRect);//windows API坐标获取
        [DllImport("DIFPassDll.dll")]
        public static extern int MDC_MatchDrug(string userdrugcode, string userdrugname, string userpym, string userdoseform, string userdrugspec,
                                                string userdoseunit, string DrugCode, ref string drugdesc, ref string doseform, ref string drugspec,
                                                ref string specunit, ref string converstion);//药品配对修改
        [DllImport("DIFPassDll.dll")]
        public static extern int MDC_MatchRoute(string userroutecode, string userroutename, string userpym1, string userpym2, string routecode, string RouteName);//给药途径配对修改
        [DllImport("dtywzxUI.dll")]
        public static extern long dtywzxUI(long IMsg, long IParm, string IpString);//大通

        #endregion 合理用药dll引用

        public commFunct()
        {
        }

        #region 药物信息查询、浮动窗体、用药研究、警告及审查功能
        /// <summary>
        /// 药物信息查询、浮动窗体、用药研究、警告及审查功能
        /// (合理用药,暂时不使用---未完)
        /// </summary>
        public static Result<bool> PassQueryOrCheck()
        {
            Result<bool> _res = new Result<bool>();
            _res.Data = true;
            try
            {
                //开始处理
                string strDrugIndex=string.Empty;//药品唯一码
                string strDrugCode = string.Empty;//药品编码
                string strDrupName = string.Empty;//药品名称
                string strSingleDose = string.Empty;//每次用量
                string strDrupUnit = string.Empty;//剂量单位
                DateTime ldtStart = DateTime.Now;//开嘱时间
                DateTime ldtEnd = DateTime.Now;//停嘱时间
                string strFrequency = string.Empty;//频次
                string strRouteName = string.Empty;//给药途径（中文名称）
                string strGroupTag = string.Empty;//成组医嘱标识
                string strOrdersTap = string.Empty;//长/临标识(1:长期;0:临时)
                string strDoctor = string.Empty;//经治医生
            }
            catch (Exception ex)
            {
                _res.HasError = true;
                _res.Data = false;
                _res.ErrObj = ex;
            }
            return _res;
        }
        #endregion 药物信息查询、浮动窗体、用药研究、警告及审查功能
    }
    /// <summary>
    /// 定义一些实例变量信息类
    /// </summary>
    public class DefineValue
    {
        #region 变量定义
        /// <summary>
        /// 住院次数
        /// </summary>
        public static string isVisitid = string.Empty;
        /// <summary>
        /// 所在病房代码
        /// </summary>
        public static string isWardCode = string.Empty;
        /// <summary>
        /// 住院ID号
        /// </summary>
        public static string isPatientID = string.Empty;
        /// <summary>
        /// 经治医生
        /// </summary>
        public static string isOrderDoc = string.Empty;
        /// <summary>
        /// 医生代码
        /// </summary>
        public static string isUserDoc = string.Empty;
        /// <summary>
        /// 费别(自费、医保)
        /// </summary>
        public static string isCharge = string.Empty;
        /// <summary>
        /// 享受优惠价格标志
        /// </summary>
        public static string isPriceInd = "0";
        /// <summary>
        /// 收费系数分子
        /// </summary>
        public static string isPriceNum = string.Empty;
        /// <summary>
        /// 收费系数分母
        /// </summary>
        public static string isPriceDenom = string.Empty;
        /// <summary>
        /// 适用特殊收费项目标志
        /// </summary>
        public static string isSpecInd = string.Empty;
        /// <summary>
        /// 预交金余额
        /// </summary>
        public static decimal ideBlance = 0;
        /// <summary>
        /// 毁方用DataSet对象
        /// </summary>
        public static DataSet iDSOrder = null;
        #endregion 变量定义

        public DefineValue()
        {}
    }
}
