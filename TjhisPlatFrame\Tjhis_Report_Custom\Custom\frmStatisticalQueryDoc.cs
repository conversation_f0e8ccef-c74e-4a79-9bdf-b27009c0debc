﻿using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;
using DevExpress.XtraReports.UserDesigner;
using DevExpress.XtraTab;
using PlatCommon.Common;
using PlatCommon.SysBase;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Windows.Forms;
using Tjhis.Report.Custom.Base;
using Tjhis.Report.Custom.Common;
using Tjhis.Report.Custom.Properties;
using Tjhis.Report.Custom.Srv;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmStatisticalQueryDoc : frmReportBase
    {
        private srvStatisticalQuery _srv;
        public DataRow drTemplet;
        public XtraReport thisReport;
        public bool isSubReport;
        //打印报表设计的模板名称
        private string templetePrintFile;
        public string reportID { get; set; }

        public string myAppName { get; set; }
         

        public string GetTempleteName()
        {
            return base.TempleteFile;
        }
        
        public static frmStatisticalQueryDoc Create(string dictID, string strAppName, bool isSubReport, Hashtable hashtable)
        {
            return new frmStatisticalQueryDoc(dictID, strAppName, isSubReport, hashtable);
        }

        #region 构造函数
        public frmStatisticalQueryDoc()
        {
            SetStyle(ControlStyles.UserPaint, true);
            SetStyle(ControlStyles.AllPaintingInWmPaint, true); // 禁止擦除背景
            SetStyle(ControlStyles.DoubleBuffer, true); // 双缓冲

            InitializeComponent();
            //布局重绘闪烁问题
            if (!this.DesignMode)
            {
                _srv = new srvStatisticalQuery();
            }
            //布局重绘闪烁问题
            docViewer.GetType().GetProperty("DoubleBuffered", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic).SetValue(docViewer, true, null);

            this.TempleteFile = "";
            this.DocViewer = this.docViewer;
        }

        public frmStatisticalQueryDoc(string deptCode,string appCode)
        {
            this.AppCode = appCode;
            this.deptCode = deptCode;
            SetStyle(ControlStyles.UserPaint, true);
            SetStyle(ControlStyles.AllPaintingInWmPaint, true); // 禁止擦除背景
            SetStyle(ControlStyles.DoubleBuffer, true); // 双缓冲

            InitializeComponent();
            //布局重绘闪烁问题
            if (!this.DesignMode)
            {
                _srv = new srvStatisticalQuery();
            }
            //布局重绘闪烁问题
            docViewer.GetType().GetProperty("DoubleBuffered", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic).SetValue(docViewer, true, null);

            this.TempleteFile = "";
            this.DocViewer = this.docViewer;
        }

        public frmStatisticalQueryDoc(DataRow drTemplet) : this()
        {
            this.drTemplet = drTemplet;
            this.reportID = drTemplet["DICT_ID"].ToString();

            this.TempleteFile = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString();
            this.TempleteSql = drTemplet["TEMPLET_SQL"].ToString();
        }
        public frmStatisticalQueryDoc(string dictID) : this()
        {
            this.reportID = dictID;
            TempletInfo_load();
        }
        public frmStatisticalQueryDoc(string dictID, bool isSubReport) : this(dictID)
        {
            this.reportID = dictID;
            TempletInfo_load();
            if (isSubReport)
            {
                InitBarEditItems();
                InitFixedBarItems();
                btnSearch_Sql_Click(null, null);
            }
            this.thisReport = mReport;
        }
        public DataSet reportDataSource;
        public frmStatisticalQueryDoc(string dictID, string strAppName, bool isSubReport, Hashtable hashtable) : this()
        {
            this.reportID = dictID;
            this.myAppName = strAppName;
            Const.customAppCode = strAppName;
            TempletInfo_load();
            if (isSubReport)
            {
                InitBarEditItems();
                InitFixedBarItems();
                this.Map = hashtable;
                reportDataSource = XtraReportHelper.GetPrintData(TempleteSql, getParams(), myAppName, true);
            }
        }

        #endregion

        #region 窗体加载
        /// <summary>
        /// 窗体加载
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Form_Load(object sender, EventArgs e)
        {
            try
            {
                if (this.DesignMode)
                    return;
                _srv = new srvStatisticalQuery();
                if (string.IsNullOrEmpty(reportID) && this.Tag != null)
                {
                    reportID = Tag.ToString().Split('|')[1];
                    myAppName = Tag.ToString().Split('|')[0];
                }
                TempletInfo_load();
                InitBarEditItems();
                InitFixedBarItems();
                if (mReport == null)
                    mReport = new XtraReport();
                string templateFullPath = ReportHelper.GetTempleteFileNameFull(TempleteFile, myAppName);
                string reportNo = string.Empty;
                if (myAppName == "DISINFECT")
                    reportNo = "1";

                ReportHelper.GetReportTemplateById(SystemParm.HisUnitCode, myAppName, reportID, reportNo, templateFullPath, false, ref DocViewer, ref mReport);

                this.Cursor = Cursors.WaitCursor;
                //XtraReportHelper.LoadTemplet(ref DocViewer, ref mReport, TempleteFile);
                mReport.CreateDocument();
                this.Cursor = Cursors.Default;

                //this.seZoomBar.EditValueChanged += base.seZoom_EditValueChanged;
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }

        protected virtual void TempletInfo_load()
        {
            if (drTemplet == null)
            {
                if (_srv == null) _srv = new srvStatisticalQuery();
                drTemplet = GetTempletNew();

                this.TempleteFile = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString();
                this.TempleteSql = drTemplet["TEMPLET_SQL"].ToString();
                //this.Text = drTemplet["REPORT_SQL"].ToString();
            }
        }
        protected virtual DataRow GetTempletNew()
        {
            return _srv.GetTempletByIDNew(reportID, myAppName);
        }
        #endregion

        #region 自定义参数控件初始化
        /// <summary>
        /// 自定义参数控件初始化
        /// </summary>
        protected virtual void InitBarEditItems()
        {
            // 基础数据准备
            bar2.ItemLinks.Clear();

            DataSet dsParam = GetReportParamNew(myAppName, reportID);
            ParamList = new List<ParamClass>();

            if (dsParam == null) return;

            foreach (DataRow drParam in dsParam.Tables[0].Rows)
            {
                try
                {
                    CreateParamControl(dsParam, drParam, ParamList);
                }
                catch (Exception e)
                {
                    MessageBox.Show(drParam["PARAM_NAME"].ToString() + "：初始化错误，信息：" + e.Message);
                }
            }
        }
        private void InitFixedBarItems()
        {
            // 
            // barQuery
            // 
            DevExpress.XtraBars.BarLargeButtonItem barQuery = new BarLargeButtonItem();
            barQuery.Caption = "查询(&S)";
            barQuery.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            barQuery.Id = 8;
            barQuery.ImageOptions.Image = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("Find_32x32")));
            barQuery.ImageOptions.LargeImage = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("Find_32x32")));
            barQuery.Name = "barQuery";
            barQuery.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barQuery_ItemClick);
            bar2.ItemLinks.Add(barQuery, barQuery.Caption);

            // 
            // barSeZoom
            // 
            DevExpress.XtraBars.BarEditItem barSeZoom = new BarEditItem();
            DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit seZoomBar = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            barSeZoom.Caption = "显示比例：";
            barSeZoom.PaintStyle = BarItemPaintStyle.Caption;
            barSeZoom.Edit = seZoomBar;
            barSeZoom.EditValue = ((short)(100));
            barSeZoom.EditWidth = 60;
            barSeZoom.Id = 6;
            barSeZoom.Name = "barSeZoom";
            // 
            // seZoomBar
            // 
            seZoomBar.AutoHeight = false;

            seZoomBar.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            seZoomBar.IsFloatValue = false;
            seZoomBar.Mask.EditMask = "N00";
            seZoomBar.MaxValue = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            seZoomBar.MinValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            seZoomBar.Name = "seZoomBar";
            seZoomBar.EditValueChanged += new System.EventHandler(this.seZoomBar_EditValueChanged);
            bar2.ItemLinks.Add(barSeZoom, true, barSeZoom.Caption);

            if (IsShowFunctionButton)
            {
                DevExpress.XtraBars.BarLargeButtonItem barExport = new BarLargeButtonItem();
                DevExpress.XtraBars.BarSubItem barSubItem1 = new BarSubItem();
                DevExpress.XtraBars.BarSubItem barExportPdf = new BarSubItem();
                DevExpress.XtraBars.PopupMenu popupMenu_export = new PopupMenu();
                // 
                // barExport
                // 
                barExport.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
                barExport.Caption = "导出(&E)";
                barExport.CaptionAlignment = BarItemCaptionAlignment.Bottom;
                barExport.DropDownControl = this.popupMenu_export;
                barExport.Id = 37;
                barExport.ImageOptions.Image = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("Export_32x32")));
                barExport.ImageOptions.LargeImage = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("Export_32x32")));
                barExport.Name = "barExport";
                barExport.ActAsDropDown = true;
                barExport.DropDownEnabled = true;
                barExport.DropDownControl = popupMenu_export;
                // 
                // popupMenu_export
                // 
                popupMenu_export.ItemLinks.Add(barSubItem1);
                popupMenu_export.ItemLinks.Add(barExportPdf);
                //    popupMenu_export.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
                //new DevExpress.XtraBars.LinkPersistInfo(barSubItem1),
                //new DevExpress.XtraBars.LinkPersistInfo(barExportPdf)});
                popupMenu_export.Manager = this.barManager1;
                popupMenu_export.Name = "popupMenu_export";
                // 
                // barSubItem1
                // 
                barSubItem1.AllowDrawArrow = DevExpress.Utils.DefaultBoolean.False;
                barSubItem1.Caption = "XLS";
                barSubItem1.Id = 38;
                barSubItem1.Name = "barSubItem1";
                barSubItem1.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barExport_Xls_ItemClick);
                // 
                // barExportPdf
                // 
                barExportPdf.AllowDrawArrow = DevExpress.Utils.DefaultBoolean.False;
                barExportPdf.Caption = "PDF";
                barExportPdf.Id = 39;
                barExportPdf.Name = "barExportPdf";
                barExportPdf.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barExport_Pdf_ItemClick);

                bar2.ItemLinks.Add(barExport, barExport.Caption);

                DevExpress.XtraBars.BarLargeButtonItem barPrint = new BarLargeButtonItem();
                // 
                // barPrint
                // 
                barPrint.Caption = "显示打印(&P)";
                barPrint.Id = 10;
                barPrint.ImageOptions.Image = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("Print_32x32")));
                barPrint.ImageOptions.LargeImage = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("Print_32x32")));
                barPrint.Name = "barPrint";
                barPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barPrint_ItemClick);
                bar2.ItemLinks.Add(barPrint, barPrint.Caption);

                // 
                // barReportPrint
                // 
                if (GetPrintTemplateFile())
                {
                    DevExpress.XtraBars.BarLargeButtonItem barReportPrint = new BarLargeButtonItem();
                    barReportPrint.Caption = "特殊打印";
                    barReportPrint.Id = 36;
                    barReportPrint.ImageOptions.Image = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("DefaultPrinterNetwork_32x32")));
                    barReportPrint.ImageOptions.LargeImage = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("DefaultPrinterNetwork_32x32")));
                    barReportPrint.Name = "barReportPrint";
                    barReportPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barReportPrint_ItemClick);
                    bar2.ItemLinks.Add(barReportPrint, barReportPrint.Caption);
                }
                //
                //barClose
                //

                DevExpress.XtraBars.BarLargeButtonItem barClose = new BarLargeButtonItem();
                barClose.Caption = "关闭(&C)";
                barClose.Id = 11;
                barClose.ImageOptions.Image = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("Cancel_32x32")));
                barClose.ImageOptions.LargeImage = ((System.Drawing.Image)(Resources.ResourceManager.GetObject("Cancel_32x32")));
                barClose.Name = "barClose";
                barClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barClose_ItemClick);
                bar2.ItemLinks.Add(barClose, barClose.Caption);
            }
        }
        private bool GetPrintTemplateFile()
        {
            templetePrintFile = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString() + "_Print";
            //if (!string.IsNullOrEmpty(_reportNo))
            //{
            //    templetePrintFile = templetePrintFile + "_" + _reportNo;
            //}

            string fileName = System.IO.Path.Combine(Application.StartupPath, @"Reports\" + myAppName + "\\" + templetePrintFile + ".repx");
            if (System.Diagnostics.Debugger.IsAttached)
            {
                fileName = System.IO.Path.Combine(Application.StartupPath.Replace(@"bin\Debug", ""), "Reports\\" + myAppName + "\\") + templetePrintFile + ".repx";
            }

            return System.IO.File.Exists(fileName);
        }
        protected virtual DataSet GetReportParamNew(string strAppName, string reportID)
        {
            return _srv.GetReportParamNew(strAppName, reportID);
        }
        #endregion

        #region 创建参数控件
        List<ParamClass> ParamList;
        public Hashtable Map;
        /// <summary>
        /// 创建自定义参数控件
        /// </summary>
        /// <param name="dsParam"></param>
        /// <param name="dr"></param>
        /// <param name="list"></param>
        void CreateParamControl(DataSet dsParam, DataRow dr, List<ParamClass> list)
        {
            ParamClass newPc = ParamList.Find(pc => pc.GetParamName().Equals(dr["PARAM_NAME"].ToString()));
            if (newPc == null)
            {
                DataTable dt = null;

                if ("1".Equals(dr["EDIT_TYPE"]) || "5".Equals(dr["EDIT_TYPE"]))
                    dt = _srv.GetEditDataSource(dr["SOURCE_TYPE"].ToString(), dr["DATA_SOURCE_SQL"].ToString());
                ParamClass editItems = new ParamClass(dr, dt);
                bar2.ItemLinks.Add(editItems.GetEditItem(barManager1), dr["CAPTION"].ToString());
                list.Add(editItems);

                if (dsParam.Tables[0].Columns.Contains("NEXT_PARAM_NAME") && !string.IsNullOrEmpty(dr["NEXT_PARAM_NAME"].ToString()))
                {
                    DataRow[] drr = dsParam.Tables[0].Select("PARAM_NAME = '" + dr["NEXT_PARAM_NAME"].ToString() + "'");
                    if (drr.Length > 0)
                    {
                        CreateParamControl(dsParam, drr[0], list);
                        ParamClass nextPc = ParamList.Find(pc => pc.GetParamName().Equals(drr[0]["PARAM_NAME"].ToString()));
                        if (nextPc != null)
                        {
                            editItems.SetNextEdit(nextPc);
                        }
                    }
                }
            }
        }
        #endregion

        #region 获取参数
        /// <summary>
        /// 获取参数
        /// </summary>
        /// <returns></returns>
        protected override Hashtable getParams()
        {
            // 向SQL语句传递参数
            //lxm20230627新增 科室代码 和模块代码变量传入 ~
            Hashtable hasParam = _srv.AddSystemParam(this.deptCode,this.AppCode);
            //hasParam.Add("LOGIN_USER_NAME", SystemParm.LoginUser.USER_NAME);
            //hasParam.Add("HIS_UNIT_CODE", SystemParm.HisUnitCode);
            //hasParam.Add("STORAGE", Const.customDeptCode);
            //hasParam.Add("LOGIN_DEPT_CODE", SystemParm.LoginUser.DEPT_CODE);
            //hasParam.Add("HospitalID", SystemParm.HospitalID);
            ParamList.ForEach(param =>
            {

                hasParam.Add(param.GetParamName(), param.EditValue());

            });
            if (Map != null)
            {
                foreach (DictionaryEntry entry in Map)
                {
                    if (Map.Contains(entry.Key))
                    {
                        hasParam[entry.Key] = entry.Value;
                    }
                    else
                        hasParam.Add(entry.Key, entry.Value);
                }
            }
            return hasParam;
        }
        #endregion

        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barQuery_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(TempleteSql.Trim()))
                {
                    XtraMessageBox.Show("该报表缺少SQL脚本，请您先在报表维护中填写！", "系统提示");
                    return;
                }
                if (mReport != null)
                {
                    mReport.Dispose();
                    mReport = null;
                    GC.Collect();
                }

                string reportNo = "";
                if (ParamList != null)
                {
                    foreach (ParamClass a in ParamList)
                    {
                        if (a.GetParamName().ToUpper() == "REPORTNO")
                        {
                            reportNo = a.EditValue().ToString();
                            break;
                        }
                    }
                }
                if (!string.IsNullOrEmpty(reportNo))
                {
                    this.TempleteFile = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString() + "_" + reportNo;
                }
                //string strReportFileName = Application.StartupPath + $"\\Reports\\{Const.customAppCode}\\{TempleteFile}.repx";
                string strReportFileName = ReportHelper.GetTempleteFileNameFull(TempleteFile, myAppName);
                ReportHelper.GetReportTemplateById(SystemParm.HisUnitCode, myAppName, reportID, reportNo, strReportFileName, false, ref DocViewer, ref mReport);
                this.Cursor = Cursors.WaitCursor;
                //XtraReportHelper.LoadTemplet(ref DocViewer, ref mReport, TempleteFile);
                mReport.DataSource = XtraReportHelper.GetPrintData(TempleteSql, getParams(), reportNo.ToInt(), myAppName);
                mReport.CreateDocument();
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                System.GC.Collect();
            }
        }

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barPrint_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                mReport.PrinterName = PrinterConfigFrm.GetPrinterName(TempleteFile);
                mReport.PrintDialog();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 报表设计
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barReportDesign_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                string reportNo = "";
                if (ParamList != null)
                {
                    foreach (ParamClass a in ParamList)
                    {
                        if (a.GetParamName().ToUpper() == "REPORTNO")
                        {
                            reportNo = a.EditValue().ToString();
                            break;
                        }
                    }
                }
                if (!string.IsNullOrEmpty(reportNo))
                {
                    this.TempleteFile = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString() + "_" + reportNo;
                }
                //string strReportFileName = Application.StartupPath + $"\\Reports\\{TempleteFile}.repx";
                string strReportFileName = ReportHelper.GetTempleteFileNameFull(TempleteFile, myAppName);
                ReportHelper.GetReportTemplateById(SystemParm.HisUnitCode, myAppName, reportID, reportNo, strReportFileName, false, ref DocViewer, ref mReport);
                if (string.IsNullOrEmpty(TempleteSql.Trim()))
                {
                    XtraMessageBox.Show("该报表缺少SQL脚本，请您先在报表维护中填写！", "系统提示");
                    return;
                }
                //if (mReport.DataSource == null)//多个类型查询的报表情况会有问题 删除此条件
                //{
                mReport.DataSource = _srv.GetDataStruct(TempleteSql, getParams(), reportNo.ToInt());
                //}

                // 打开模板设计器
                XRDesignFormEx frm = new XRDesignFormEx();
                frm.FileName = strReportFileName;
                frm.OpenReport(mReport);
                frm.ShowDialog();
                frm.Dispose();

                //保存报表设计模板到数据库
                _srv.SaveReportRepx(myAppName, reportID, reportNo, strReportFileName, false);

                //重新加载模板
                this.Cursor = Cursors.WaitCursor;
                // XtraReportHelper.LoadTemplet(ref DocViewer, ref mReport, TempleteFile);
                mReport.DataSource = XtraReportHelper.GetPrintData(TempleteSql, getParams(), reportNo.ToInt(), myAppName);
                mReport.CreateDocument();

                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 关闭
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barClose_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                if (this.Parent != null && this.Parent is TJReportViewF parentForm
                     && parentForm.Parent != null && parentForm.Parent is XtraTabPage tabPage)
                {
                    XtraTabControl xtraTabControl = tabPage.Parent as XtraTabControl;
                    if (xtraTabControl == null) return;
                    string strName = tabPage.Text.ToString();
                    int iCount = xtraTabControl.TabPages.Count;
                    for (int i = iCount - 1; i >= 0; i--)
                    {
                        if (xtraTabControl.TabPages[i].Text.ToString() == strName)
                        {
                            xtraTabControl.TabPages.RemoveAt(i);
                        }
                    }

                    tabPage.Dispose();
                }
                else
                    this.Close();
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 导出XLS文件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barExport_Xls_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                SaveFileDialog sfd = new SaveFileDialog();
                //sfd.Filter = "Excel（*.xlsx）|*.xlsx |Excel 97-2003（*.xls）|*.xls"; // 设定文件类型
                sfd.Filter = "Excel（*.xlsx）|*.xlsx"; // 设定文件类型
                sfd.FilterIndex = 1;
                sfd.RestoreDirectory = true;
                sfd.DefaultExt = "xlsx";
                string defFileName = TempleteFile + "_" + DateTime.Now.ToString("yyyyMMddHHmmss");
                sfd.FileName = defFileName;

                if (sfd.ShowDialog() != DialogResult.OK)
                {
                    return;
                }

                // 导出文件                    
                if (sfd.FileName.ToLower().Trim().EndsWith(".xlsx"))
                {
                    XlsxExportOptions xlsxOptions = mReport.ExportOptions.Xlsx;
                    xlsxOptions.ShowGridLines = false;
                    mReport.ExportToXlsx(sfd.FileName);
                }
                else
                {
                    XlsExportOptions xlsOptions = mReport.ExportOptions.Xls;
                    xlsOptions.ShowGridLines = false;

                    mReport.ExportToXls(sfd.FileName);
                }
                XtraMessageBox.Show("导出成功!");
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 导出PDF文件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barExport_Pdf_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                if (mReport.DataSource == null)
                {
                    XtraMessageBox.Show("没有数据可导出！", "提示");
                    return;
                }
                SaveFileDialog sfd = new SaveFileDialog();
                sfd.Filter = "PDF文件（*.pdf）|*.pdf ";  // 设置文件类型
                sfd.FilterIndex = 1;
                sfd.RestoreDirectory = true;
                sfd.DefaultExt = "pdf";
                string defFileName = TempleteFile + "_" + DateTime.Now.ToString("yyyyMMddHHmmss");
                sfd.FileName = defFileName;

                if (sfd.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                {
                    return;
                }

                // 导出文修的
                PdfExportOptions pdfOptions = mReport.ExportOptions.Pdf;

                // Set PDF-specific export options.
                pdfOptions.Compressed = true;
                pdfOptions.ImageQuality = PdfJpegImageQuality.Low;
                //pdfOptions.NeverEmbeddedFonts = "Tahoma;Courier New";
                //pdfOptions.DocumentOptions.Application = "Test Application";
                //pdfOptions.DocumentOptions.Author = "DX Documentation Team";
                //pdfOptions.DocumentOptions.Keywords = "XtraReports, XtraPrinting";
                //pdfOptions.DocumentOptions.Subject = "Test Subject";
                pdfOptions.DocumentOptions.Title = TempleteFile;

                // Export the report to PDF.
                mReport.ExportToPdf(sfd.FileName);

                XtraMessageBox.Show("导出成功!");
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 显示比例
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void seZoomBar_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                SpinEdit seZoom = sender as SpinEdit;
                if (seZoom == null) return;
                this.DocViewer.Zoom = (float)(seZoom.Value / 100);
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 窗体关闭事件（主要处理一些对象的释放，垃圾回收，不然内存得不到释放）
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void frmStatisticalQueryDoc_FormClosed(object sender, FormClosedEventArgs e)
        {
            if (mReport != null)
            {
                mReport.Dispose();
                mReport = null;
            }
            if (_srv != null)
                _srv = null;

            docViewer.Dispose();
            docViewer = null;

            this.Dispose();
            GC.Collect();
        }

        private void barReportPrintDesign_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                string reportNo = "";
                templetePrintFile = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString() + "_Print";
                if (ParamList != null)
                {
                    foreach (ParamClass a in ParamList)
                    {
                        if (a.GetParamName().ToUpper() == "REPORTNO")
                        {
                            reportNo = a.EditValue().ToString();
                            break;
                        }
                    }
                }
                if (!string.IsNullOrEmpty(reportNo))
                {
                    templetePrintFile = templetePrintFile + "_" + reportNo;
                }
                //string strReportFileName = Application.StartupPath + $"\\Reports\\{TempleteFile}.repx";
                string strReportFileName = ReportHelper.GetTempleteFileNameFull(templetePrintFile, myAppName);
                ReportHelper.GetReportTemplateById(SystemParm.HisUnitCode, myAppName, reportID, reportNo, strReportFileName, true, ref DocViewer, ref mReport);
                if (string.IsNullOrEmpty(TempleteSql.Trim()))
                {
                    XtraMessageBox.Show("该报表缺少SQL脚本，请您先在报表维护中填写！", "系统提示");
                    return;
                }
                //if (mReport.DataSource == null)//多个类型查询的报表情况会有问题 删除此条件
                //{
                mReport.DataSource = _srv.GetDataStruct(TempleteSql, getParams(), reportNo.ToInt());
                //}

                // 打开模板设计器
                XRDesignFormEx frm = new XRDesignFormEx();
                frm.FileName = strReportFileName;
                frm.OpenReport(mReport);
                frm.ShowDialog();
                frm.Dispose();

                //保存报表设计模板到数据库
                _srv.SaveReportRepx(myAppName, reportID, reportNo, strReportFileName, true);

                //重新加载模板
                this.Cursor = Cursors.WaitCursor;
                // XtraReportHelper.LoadTemplet(ref DocViewer, ref mReport, TempleteFile);
                mReport.DataSource = XtraReportHelper.GetPrintData(TempleteSql, getParams(), reportNo.ToInt(), myAppName);
                mReport.CreateDocument();

                this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                XtraMessageBox.Show(ex.Message);
            }
        }

        private void barReportPrint_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                string reportNo = "";
                templetePrintFile = drTemplet["DICT_NAME"].ToString() + "_" + drTemplet["DICT_ID"].ToString() + "_Print";
                if (ParamList != null)
                {
                    foreach (ParamClass a in ParamList)
                    {
                        if (a.GetParamName().ToUpper() == "REPORTNO")
                        {
                            reportNo = a.EditValue().ToString();
                            break;
                        }
                    }
                }
                if (!string.IsNullOrEmpty(reportNo))
                {
                    templetePrintFile = templetePrintFile + "_" + reportNo;
                }
                XtraReportHelper.PrinterName = PrinterConfigFrm.GetPrinterName(templetePrintFile);
                DataSet printDs = XtraReportHelper.GetPrintData(TempleteSql, getParams(), reportNo.ToInt(), myAppName);



                XtraReportHelper.Print(templetePrintFile, printDs, true, false, myAppName);
            }
            catch (Exception ex)
            {

            }
        }
    }
}
