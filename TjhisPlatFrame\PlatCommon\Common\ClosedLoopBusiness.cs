﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Net;
using System.IO;
using System.Threading;
using System.Data;
using PlatCommon.SysBase;

namespace PlatCommon.Common
{
    public class ClosedLoopBusiness
    {
        #region 私有变量定义
        private static string _postSwicth = string.Empty;   //闭环开关
        private static string _postUrl = string.Empty;      //平台接收xml地址
        private static string _content = string.Empty;      //xml串
        private static DataTable _dt;
        #endregion

        #region 公共方法
        /// <summary>
        /// 创建DataTable方法
        /// </summary>
        /// <param name="type">类型：1医嘱</param>
        /// <returns>DataTable表结构</returns>
        public static DataTable CreateDataTable(string type)
        {
            DataTable dt = new DataTable();

            switch (type)
            {
                //医嘱闭环
                case "1":
                    dt.Columns.Add("actionType", typeof(System.String));
                    dt.Columns.Add("patientId", typeof(System.String));
                    dt.Columns.Add("visitId", typeof(System.String));
                    dt.Columns.Add("orderNo", typeof(System.String));
                    dt.Columns.Add("orderSubNo", typeof(System.String));
                    dt.Columns.Add("orderClass", typeof(System.String));
                    dt.Columns.Add("orderStatus", typeof(System.String));
                    break;
            }
            return dt;
        }

        /// <summary>
        /// 业务入口方法
        /// </summary>
        /// <param name="type">类型：1医嘱</param>
        /// <param name="dt"></param>
        public static void DoBusiness(string type, DataTable dt)
        {
            _dt = dt.Copy();

            //开辟异步线程，防止推送卡住影响正常业务流程
            ClosedLoopBusiness clb = new ClosedLoopBusiness();

            switch (type)
            {
                //医嘱闭环
                case "1":
                    Thread t = new Thread(new ThreadStart(clb.OrdersClosedLoop));
                    t.Start();
                    break;
            }
        }
        #endregion

        #region 私有基础方法
        /// <summary>
        /// 初始化方法
        /// </summary>
        /// <returns> </returns>
        private static bool Init()
        {
            bool b = false;
            //取开关
            //_postSwicth = PlatCommon.SysBase.SystemParm.GetParameterValue("CLOSED_LOOP_SWITCH", "*", PlatCommon.SysBase.SystemParm.Deptcode, "*", SystemParm.HisUnitCode);//经查，无CLOSED_LOOP_SWITCH参数
            _postSwicth = "0";
            if (string.IsNullOrEmpty(_postSwicth) || _postSwicth.Equals("0"))
            {
                _postSwicth = PlatCommon.SysBase.SystemParm.GetParameterValue("CLOSED_LOOP_SWITCH", "*", "*", "*", SystemParm.HisUnitCode);
            }
            if (_postSwicth.Equals("1"))
            {
                //取平台闭环获取地址 
                PublicFunction.GetInterfaceConfigDict("CLOSED_LOOP_URL", ref _postUrl);
               // _postUrl = PlatCommon.SysBase.SystemParm.GetParaValue("CLOSED_LOOP_URL", "*", "*", "*", "");
                if (!string.IsNullOrEmpty(_postUrl))
                {
                    b = true;
                }
            }
            return b;
        }

        /// <summary>
        /// 推送xml方法
        /// </summary>
        /// <param name="content">xml字符串</param>
        private static void PostXml(string content)
        {
            string result = string.Empty;       //返回值，如果需要的话
            try
            {
                HttpWebRequest req = (HttpWebRequest)WebRequest.Create(_postUrl);
                req.Method = "POST";
                req.Connection = "acceptMessage";
                //req.ContentType = "application/x-www-form-urlencoded";
                req.ContentType = "text/xml";
                #region 添加Post 参数
                byte[] data = Encoding.UTF8.GetBytes(content);
                req.ContentLength = data.Length;
                using (Stream reqStream = req.GetRequestStream())
                {
                    reqStream.Write(data, 0, data.Length);
                    reqStream.Close();
                }
                #endregion
                HttpWebResponse resp = (HttpWebResponse)req.GetResponse();
                Stream stream = resp.GetResponseStream();
                //获取响应内容  
                using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                {
                    result = reader.ReadToEnd();
                }
                //return result;
                return;
            }
            catch (Exception ex)
            {
                //Log.Write(ex.ToString());
                //return string.Empty;
                return;
            }
        }
        #endregion

        #region 私有业务方法
        /// <summary>
        /// 医嘱闭环入口方法
        /// </summary>
        private void OrdersClosedLoop()
        {
            DataTable dt = _dt.Copy();
            if (Init() && dt.Rows.Count > 0)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    OrdersClosedLoop(
                        dt.Rows[i]["actionType"].ToString(),
                        dt.Rows[i]["patientId"].ToString(),
                        dt.Rows[i]["visitId"].ToString(),
                        dt.Rows[i]["orderNo"].ToString(),
                        dt.Rows[i]["orderSubNo"].ToString(),
                        dt.Rows[i]["orderClass"].ToString(),
                        dt.Rows[i]["orderStatus"].ToString()
                        );
                }
            }
        }

        /// <summary>
        /// 医嘱闭环实体方法
        /// </summary>
        /// <param name="actionType">闭环节点 M1医嘱开立 M2转抄 M3校对 M4签名等等，参考平台提供的闭环字典</param>
        /// <param name="patientId"></param>
        /// <param name="visitId"></param>
        /// <param name="orderNo"></param>
        /// <param name="orderSubNo"></param>
        /// <param name="orderClass"></param>
        /// <param name="orderStatus"></param>
        private static void OrdersClosedLoop(string actionType, string patientId, string visitId, string orderNo, string orderSubNo, string orderClass, string orderStatus)
        {
            if (string.IsNullOrEmpty(actionType))
            {
                return;
            }

            string xmlResult = string.Empty;
            StringBuilder sb = new StringBuilder();

            sb.AppendLine("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ws=\"http://ws.connectors.connect.mirth.com/\">");
            sb.AppendLine("<soapenv:Header/>");
            sb.AppendLine("<soapenv:Body>");
            sb.AppendLine("<ws:acceptMessage>");
            sb.AppendLine("<!--Optional:-->");
            sb.AppendLine("<arg0>");
            sb.AppendLine("<![CDATA[<xml>");
            sb.AppendLine("<EventType>TJ610</EventType>");
            sb.AppendLine("<ListInfo>");
            sb.AppendLine("<ActionType>" + actionType + "</ActionType>"); //1新开 2执行 3停止 4作废
            sb.AppendLine("<ListRow>");
            sb.AppendLine("<ORDERS>");
            sb.AppendLine("<PriKeyList>");
            sb.AppendLine("<PATIENT_ID>" + patientId + "</PATIENT_ID>");
            sb.AppendLine("<VISIT_ID>" + visitId + "</VISIT_ID>");
            sb.AppendLine("<ORDER_NO>" + orderNo + "</ORDER_NO>");
            sb.AppendLine("<ORDER_SUB_NO>" + orderSubNo + "</ORDER_SUB_NO>");
            sb.AppendLine("<ORDER_CLASS>" + orderClass + "</ORDER_CLASS>");
            sb.AppendLine("<ORDER_STATUS>" + orderStatus + "</ORDER_STATUS>");
            sb.AppendLine("</PriKeyList>");
            sb.AppendLine("</ORDERS>");
            sb.AppendLine("</ListRow>");
            sb.AppendLine("</ListInfo>");
            sb.AppendLine("</xml>]]>");
            sb.AppendLine("</arg0>");
            sb.AppendLine("</ws:acceptMessage>");
            sb.AppendLine("</soapenv:Body>");
            sb.AppendLine("</soapenv:Envelope>");

            //推送xml串给平台
            PostXml(sb.ToString());
        }
        #endregion
    }
}
