﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;

namespace Model.EMR
{
    public class OUTP_MR_FILE_INDEX
    {
        /// <summary>
        /// 实体类OUTP_MR_FILE_INDEX 。(属性说明自动提取数据库字段的描述信息)
        /// </summary>
        public OUTP_MR_FILE_INDEX()
        { }
        #region Model
        /// <summary>
        /// 就诊日期
        /// </summary>
        public DateTime VISIT_DATE
        {
            set;
            get;
        }
        /// <summary>
        /// 就诊序号
        /// </summary>
        public int VISIT_NO
        {
            set;
            get;
        }
        /// <summary>
        /// 文件序号
        /// </summary>
        public int FILE_NO
        {
            set;
            get;
        }
        /// <summary>
        /// 病人ID
        /// </summary>
        public string PATIENT_ID
        {
            set;
            get;
        }
        /// <summary>
        /// 文件名
        /// </summary>
        public string FILE_NAME
        {
            set;
            get;
        }
        /// <summary>
        /// 主题
        /// </summary>
        public string TOPIC
        {
            set;
            get;
        }
        /// <summary>
        /// 创建者姓名
        /// </summary>
        public string CREATOR_NAME
        {
            set;
            get;
        }
        /// <summary>
        /// 创建者ID
        /// </summary>
        public string CREATOR_ID
        {
            set;
            get;
        }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CREATE_DATE_TIME
        {
            set;
            get;
        }
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LAST_MODIFY_DATE_TIME
        {
            set;
            get;
        }
        /// <summary>
        /// 病历模板编码
        /// </summary>
        public string MR_CODE
        {
            set;
            get;
        }
        public string FILE_ATTR
        {
            set;
            get;
        }


        public string FILE_FLAG
        {
            set;
            get;
        }

        /// <summary>
        /// 实习医生id
        /// </summary>
        public int SIGNATURE_NO { get; set; }

        /// <summary>
        /// 实习医生id
        /// </summary>
        public string STUDY_DOCTOR_ID { get; set; }
        /// <summary>
        /// 实习医生姓名
        /// </summary>
        public string STUDY_DOCTOR_NAME { get; set; }
        public int ORDINAL { get; set; }
        #endregion Model

    }
}
