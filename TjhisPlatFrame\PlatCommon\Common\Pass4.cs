﻿//**********************************************
//说明:合理用药包装类
//计算机名称：LINDP
//创建日期：2016/8/23 16:48:39
//作者：林大鹏
//版本号：V1.00
//**********************************************


using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace PlatCommon.Common
{
    public class Pass4
    {
        #region 初始化及关闭
        public static string Pass_Init(string as_pcCheckMode,string as_pcHisCode,string as_pcDoctorCode)
        {
            string   ls_pcCheckMode; 	//审查模式  如："mz"-门诊；"zy"-住院
            string   ls_pcHisCode; 		//医院编码
            string   ls_pcDoctorCode; 	//登录医生编码
            bool	 lb_FileExist;  		//判断文件是否存在
            string   ls_error; 			//返回错误值
            int      rvn;               //返回值

            ls_pcCheckMode = as_pcCheckMode;
            ls_pcHisCode = as_pcHisCode;
            ls_pcDoctorCode = as_pcDoctorCode;
            //判断动态库是否存在
            lb_FileExist = File.Exists("PASS4Invoke.dll");
            if (!lb_FileExist)
            {
                SysBase.SystemParm.PassEnabled = false;
                return "合理用药接口PASS4Invoke.dll文件查找失败，该工作站不能进行合理用药审查！" + "~r~n" + "请重新启动工作站，如果还存在问题，请与信息科联系！！！";

            }
            //初始化
            rvn = PASS4Invoke.MDC_Init(ls_pcCheckMode, ls_pcHisCode, ls_pcDoctorCode);
            if (rvn != 1)
            {
                ls_error = PASS4Invoke.MDC_GetLastError();
                SysBase.SystemParm.PassEnabled = false;
                return "合理用药接口初始化失败 [" + ls_error + "],合理用药系统不可用，请与信息科联系!";
            }
            SysBase.SystemParm.PassEnabled = true;
            return null;
        }
        //退出合理用药
        public static string Pass_Stop()
        {
            if (SysBase.SystemParm.PassEnabled)
            {
                int li_return = PASS4Invoke.MDC_Quit();
                if (li_return != 1)
                {
                    string ls_error = PASS4Invoke.MDC_GetLastError();
                    return "合理用药接口 [" + ls_error + "] 失败,请与信息科联系!";
                }
            }
            return null;
        }
        #endregion

        //医嘱审核
        public static int Pass_CheckedOrders(int ai_checkmode, int ai_row,DataTable orders)
        {
        //    #region 说明
        //    /*******************美康4.0嵌套代码《用户自定义函数》开始************
        //    ** 函 数 名: PassQueryOrCheck()
        //    ** 输    入: ai_checkmode------传入监测摸式
        //    **                       1    保存自动审查
        //    **								 2    提交自动审查
        //    **                       3    手工审查
        //    **                       4    临床药学单病人审查
        //    **                       5    临床药学多病人审查
        //    **                       6    查看单药警告
        //    **                       110  右键菜单有效性控制标识  
        //    **                       401	显示药品浮动窗口
        //    **                       402	关闭所有浮动窗口
        //    **                       403	显示单药最近一次审查结果浮动提示窗口
        //    **								 
        //    **           ai_row ----------传入当前所选择医嘱或处方的行
        //    ** 输    出:  
        //    ** 功能描述: 适用于药物信息查询、浮动窗口、用药研究、警告和审查功能
        //    ** 全局变量: 
        //    ** 调用模块: 可以在右键弹出菜单、点击事件、保存按钮、提交按钮、用药研究
        //    **           警告、和审查调用，输入不同的参数值，实现不同的PASS系统功能
        //    ****************************************************************/
        //    #endregion
        //    DateTime ldt_CheckStrat = DateTime.Parse(DateTime.Today.ToShortDateString() + " 00:00:00"); //必须赋值，用于判断当天用药医嘱开始范围，格式：yyyy-mm-dd hh:mm:ss，嵌套时最好改为服务器时间。
        //    DateTime ldt_CheckEnd = DateTime.Parse(DateTime.Today.ToShortDateString() + " 23:59:59"); //必须赋值，用于判断当天用药医嘱结束范围，格式：yyyy-mm-dd hh:mm:ss，嵌套时最好改为服务器时间。
        //    DateTime ldt_stop_date;
        //    DateTime ldt_starttime;
        //    DateTime ldt_endtime = DateTime.MinValue;
        //    string ls_class, ls_drugcode, ls_drugname, ls_drugunit, ls_routename, ls_grouptag;
        //    string ls_stoptag, ls_statetag, ls_ldt_stop_date, ls_orderstag;
        //    int li_return, li_repeat_indicator;
        //    int li_freq_counter, li_freq_interval;
        //    string ls_freq_interval_unit, ls_frequency;
        //    string ls_singledose, ls_doctor, ls_doctor_pass_name, ls_doctor_pass;
        //    string sql;
        //    DataTable dt;
        //    string ls_drugindex, tpcOrderType;
        //    string error = "";
        //    List<string> ls_IndexDrugs = new List<string>();
        //    List<int> ll_DrugListRow = new List<int>();
        //    NM_Service.NMService.ServerPublicClient sp = new NM_Service.NMService.ServerPublicClient();
        //    //-------药物信息查询功能嵌套部份----------------
        //    if (ai_checkmode == 6 || ai_checkmode == 110 || ai_checkmode == 401 || ai_checkmode == 403 || ai_checkmode == 1010 || ai_checkmode == 1011 || ai_checkmode == 51)
        //    {
        //        #region
        //        //药物信息查询功能变量赋值，嵌套时用户根据自已HIS系统，进行修改赋值
        //        ls_class = orders.Rows[ai_row]["ORDER_CLASS"].ToString().Trim(); //必须赋值，根据HIS系统药品标识来赋值，例如:'A'表示药品
        //        ls_drugcode = orders.Rows[ai_row]["ORDER_CODE"].ToString().Trim();  //必须赋值，指医生下达医嘱时药品唯一编码，例如: J.TAD000000
        //        ls_drugname = orders.Rows[ai_row]["ORDER_TEXT"].ToString().Trim();  //必须赋值，指医生下达医嘱时药品名称，例如：安定片
        //        ls_drugunit = orders.Rows[ai_row]["DOSAGE_UNITS"].ToString().Trim();  //必须赋值，指医生下达医嘱时给药单位剂量，例如：mg
        //        ls_routename = orders.Rows[ai_row]["ADMINISTRATION"].ToString().Trim();  //必须赋值，指医生下达医嘱时给药途径中文名称，例如：口服
        //        ls_grouptag = orders.Rows[ai_row]["ORDER_NO"].ToString().Trim();  //必须赋值，指医生下达医嘱时当前的医嘱序号。
        //        //li_Warning = orders.Rows[ai_row][""].ToString().Trim();  //必须赋值，指医生下达医嘱时当前的警告值。	

        //        //组合唯一码，用户根据实际情况进行组合
        //        //SysBase.SystemParm.gs_drugindexnew = ls_drugcode + "/" + ls_grouptag; //必须组合赋值，指传入病人用药医嘱接口时医嘱唯一标识码,该标识码在医嘱中不会发现变化。
        //        if (SysBase.SystemParm.PassEnabled) //判断是否启用PASS系统
        //        {
        //            //******查看警告浮动窗口和警告详细信息*********
        //            if (ai_checkmode == 6 || ai_checkmode == 403)
        //            {
        //                //如果当前获得的组合唯一码与上一次的唯一码相同并且属于查看警告浮动窗口时，不在调用警告接口
        //                //if (SysBase.SystemParm.gs_drugindexnew.Equals(SysBase.SystemParm.gs_drugindexold) && ai_checkmode == 403) return -1;
        //                //调用警告查看接口
        //                //li_return = PASS4Invoke.MDC_ShowWarningHint(SysBase.SystemParm.gs_drugindexnew);  //传入查看警告组合唯一码
        //                //临时保存当前组合唯一码
        //                //SysBase.SystemParm.gs_drugindexold = SysBase.SystemParm.gs_drugindexnew;

        //            }
        //            else if (ai_checkmode == 110 || ai_checkmode == 51 || ai_checkmode == 401 || ai_checkmode == 1010 || ai_checkmode == 1011)
        //            {
        //                //SysBase.SystemParm.gs_drugindexold = ""; //清除上一次临时保存的组合唯一码				
        //                //==========合理用药4.0====
        //                li_return = PASS4Invoke.MDC_GetDrugQueryInfo(ls_drugcode, ls_drugname, 51, 0, 0);
        //                if (li_return != 1)
        //                {
        //                    error = PASS4Invoke.MDC_GetLastError();
        //                    error = "合理用药接口提示 [" + error + "]，请与信息科联系!";

        //                }
        //                //==========合理用药4.0====
        //            }
        //        }
        //        #endregion
        //    }
        //    else
        //    {
        //        //-------用药医嘱审查或用药研究功能嵌套部份----------------
        //        li_return = PASS4Invoke.MDC_CloseDrugHint();  //关闭当前所有浮动窗口
        //        if (li_return != 1)
        //        {
        //            error = PASS4Invoke.MDC_GetLastError();

        //            DevExpress.XtraEditors.XtraMessageBox.Show("合理用药接口提示 [" + error + "]，请与信息科联系!");
        //        }
        //        //SysBase.SystemParm.gs_drugindexold = ""; //清除当前警告浮动窗口赋值
        //        #region 循环获取病人用药信息
        //        foreach (DataRow item in orders.Rows)
        //        {
        //            ls_class = item["ORDER_CLASS"].ToString().Trim(); //根据HIS系统实际情况赋值,如果没有区分"药品标识",可以取消该项赋值，例如:'A'表示药品，'B'表示护理
        //            if (string.IsNullOrEmpty(item["stop_date_time"].ToString()))
        //                ls_stoptag = "0";
        //            else
        //                ls_stoptag = "1";
        //            ls_ldt_stop_date = item["stop_date_time"].ToString();
        //            if (string.IsNullOrEmpty(ls_ldt_stop_date))
        //            {
        //                ldt_stop_date = DateTime.Parse(ls_ldt_stop_date).Date;
        //            }
        //            else
        //                ldt_stop_date = DateTime.Today.AddDays(-1);
        //            li_repeat_indicator = int.Parse(item["repeat_indicator"].ToString());
        //            ls_statetag = item["order_status"].ToString().Trim(); //根据HIS系统实际情况赋值,如果没有区分"停嘱标识",可以取消该项赋值，例如:'8'为作废医嘱，'2'为提交医嘱
        //            //当前医嘱不符合条件，继续下一个循环
        //            if (!ls_class.Equals("A")) continue;
        //            if (ls_statetag.Equals("8")) continue;

        //            if (li_repeat_indicator == 0 && (DateTime.Today - ldt_stop_date).Days != 0) continue;
        //            if (ls_stoptag == "1" && li_repeat_indicator == 1 && (DateTime.Today - ldt_stop_date).Days < 0) continue;
        //            //必须赋值，指医生下达医嘱的时间，格式 yyyy-mm-dd hh:mm:ss 例如：2005-08-12 11:12:00
        //            if (string.IsNullOrEmpty(item["ENTER_DATE_TIME"].ToString()))
        //                ldt_starttime = DateTime.Parse(DateTime.Now.ToShortDateString() + " 00:00:00");
        //            else
        //                ldt_starttime = DateTime.Parse(item["ENTER_DATE_TIME"].ToString());

        //            ls_orderstag = item["repeat_indicator"].ToString(); //必须赋值，指当前医嘱是否为"长期医嘱"或"临时医嘱"传入值为"1"长期医嘱或"0"临时医嘱，而不是中文

        //            if (ls_orderstag.Equals("0")) //判断如果为临时医嘱时,开嘱时间为停嘱时间
        //            {
        //                ldt_endtime = ldt_starttime;
        //                ls_orderstag = "1";
        //            }
        //            else if (ls_orderstag.Equals("1"))
        //            {
        //                if (!string.IsNullOrEmpty(item["stop_date_time"].ToString())) //必须赋值，指医生停止医嘱的时间，格式 yyyy-mm-dd hh:mm:ss  例如：2005-08-14 11:12:00
        //                    ldt_endtime = DateTime.Parse(item["stop_date_time"].ToString());
        //                else
        //                    ldt_endtime = DateTime.MinValue;
        //                ls_orderstag = "0";
        //            }
        //            //如果没有开嘱时间时,赋于当天的时间
        //            if (ldt_endtime == DateTime.MinValue)
        //                ldt_endtime = DateTime.Parse(DateTime.Now.ToShortDateString() + " 23:59:59");

        //            //*****判断当前监测的用药医嘱是否符合监测时间范围****
        //            if (!(ldt_endtime < ldt_CheckStrat || ldt_starttime > ldt_CheckEnd)) //当前医嘱不符合监测时间范围条件，继续下一个循环
        //            {
        //                //获取用户用药频次，嵌套时用户可以根据实际情况修改
        //                try
        //                {
        //                    li_freq_counter = int.Parse(item["freq_counter"].ToString()); //频次次数
        //                    li_freq_interval = int.Parse(item["freq_interval"].ToString()); //频率间隔
        //                    ls_freq_interval_unit = item["freq_interval_unit"].ToString().Trim(); //频率间隔单位
        //                    ls_frequency = "";
        //                    switch (ls_freq_interval_unit)
        //                    {
        //                        case "日":
        //                            ls_frequency = li_freq_counter.ToString() + "/" + li_freq_interval.ToString();
        //                            break;
        //                        case "小时":
        //                            if (li_freq_interval <= 24)
        //                                ls_frequency = (Math.Floor(double.Parse((24 / li_freq_interval).ToString())) * li_freq_counter).ToString() + "/" + li_freq_counter.ToString();

        //                            if (li_freq_interval > 24)
        //                                ls_frequency = li_freq_counter.ToString() + "/" + (Math.Floor(double.Parse((li_freq_interval / 24).ToString()) * li_freq_counter)).ToString();
        //                            break;
        //                        case "分钟":
        //                            ls_frequency = (Math.Floor(double.Parse((24 * 60 / li_freq_interval).ToString())) * li_freq_counter).ToString() + "/" + li_freq_counter.ToString();
        //                            break;
        //                        default:
        //                            break;
        //                    }
        //                    //获取病人用药信息
        //                    ls_drugcode = item["order_code"].ToString().Trim(); //必须赋值，指医生下达医嘱时药品唯一编码，例如: J.TAD000000
        //                    ls_drugname = item["order_text"].ToString().Trim(); //必须赋值，指医生下达医嘱时药品名称，例如：安定片
        //                    ls_singledose = item["dosage"].ToString().Trim(); //必须赋值,指医生下达医嘱时每次用量 ，例如：120
        //                    ls_drugunit = item["dosage_units"].ToString().Trim(); //必须赋值，指医生下达医嘱时给药单位剂量，例如：mg
        //                    ls_routename = item["administration"].ToString().Trim(); //必须赋值，指医生下达医嘱时给药途径中文名称，例如：口服
        //                    ls_grouptag = item["order_no"].ToString().Trim(); //必须赋值，指医生下达配伍医嘱时子医嘱的标识，通常HIS系统采用医嘱序号来标识，医嘱序号相同的两两医嘱表示配伍医嘱，不同的两两医嘱表示不属于配伍医嘱。
        //                    ls_doctor = SysBase.SystemParm.LoginUser.ID + "/" + item["doctor"].ToString().Trim(); //必须赋值，指下达医嘱时的医生 例如：01/伊丽娜。
        //                    ls_doctor_pass_name = item["doctor"].ToString();
        //                    sql = "select db_user from users where user_name='" + ls_doctor_pass_name + "'";
        //                    dt = sp.GetList(sql).Tables[0];
        //                    if (dt.Rows.Count > 0)
        //                        ls_doctor_pass = dt.Rows[0][0].ToString();
        //                    else
        //                        ls_doctor_pass = "";

        //                    //组合唯一码，用户根据实际情况组合
        //                    ls_drugindex = ls_drugcode + "/" + ls_grouptag; //必须组合赋值，指传入病人用药医嘱接口时医嘱唯一标识码
        //                    if (ls_orderstag.Equals("1"))
        //                        ls_orderstag = "0";  //合理用药 值0表示长期医嘱 ,1表示临时医嘱
        //                    else
        //                        ls_orderstag = "1";
        //                    tpcOrderType = "0";
        //                    li_return = PASS4Invoke.MDC_AddScreenDrug(ls_drugindex, ls_grouptag, ls_drugcode, ls_drugname, ls_singledose, ls_drugunit,
        //                                     ls_frequency, ls_routename, ls_routename,
        //                                     ldt_starttime.ToString("yyyy-mm-dd"), ldt_endtime.ToString("yyyy-mm-dd"), ldt_starttime.ToString("yyyy-mm-dd"),
        //                                     ls_grouptag, ls_orderstag, tpcOrderType, SysBase.SystemParm.Deptcode, SysBase.SystemParm.Deptname, ls_doctor_pass,
        //                                     ls_doctor_pass_name, "", "", "", "", "", "", "");
        //                    if (li_return != 1)
        //                    {
        //                        error = PASS4Invoke.MDC_GetLastError();
        //                        error = "合理用药接口提示 [" + error + "]，请与信息科联系!";
        //                        DevExpress.XtraEditors.XtraMessageBox.Show(error);
        //                    }
        //                    //将当前传入病人用药信息接口时的组合唯一码，保存在数组中，目的用于合理用药监测完成之后，重新将组合唯一码，传入接口中取回监测结果警示色的值，并将该值用于判断警示颜色图标的显示。嵌套时用户可以根据实际情况考虑是否需要保存或考虑其它方式来保存。
        //                    ls_IndexDrugs.Add(ls_drugindex);
        //                    ll_DrugListRow.Add(orders.Rows.IndexOf(item));


        //                }
        //                catch (Exception e)
        //                {
        //                    //error = e.Message;
        //                    DevExpress.XtraEditors.XtraMessageBox.Show(e.Message);
        //                    return -1;
        //                }
        //            }

        //        }
        //        #endregion //循环获取病人用药信息
        //    }

        //    if (ai_checkmode == 12 || ai_checkmode == 6 || ai_checkmode == 51 || ai_checkmode == 403 || ai_checkmode == 401) return -1;
        //    int li_docheck = -1;
        //    li_docheck = PASS4Invoke.MDC_DoCheck(1, 1);
        //    if (li_docheck < 0)
        //    {
        //        error = PASS4Invoke.MDC_GetLastError();
        //        error = "合理用药接口提示 [" + error + "]，请与信息科联系!";
        //        DevExpress.XtraEditors.XtraMessageBox.Show(error);
        //    }
        //    //循环获取监测结果警告值
        //    int ll_ShowDrugRow;
        //    int li_Warning;
        //    int ll_error = 0;
        //    for (int i = 0; i < ll_DrugListRow.Count; i++)
        //    {
        //        ll_ShowDrugRow = ll_DrugListRow[i]; //获取传入的医嘱行号
        //        ls_drugindex = ls_IndexDrugs[i]; //获取传入的组合唯一码
        //        if (ls_drugindex.Length > 0)
        //        {
        //            li_Warning = PASS4Invoke.MDC_GetWarningCode(ls_drugindex);
        //            orders.Rows[ll_ShowDrugRow]["nwarn"] = li_Warning;
        //            if (li_Warning == 1)
        //                ll_error++;
        //        }
        //    }
        //    if (ll_error > 0)
        //    {
        //        error = "PASS系统审查出存在不合理用药？继续保存吗？";
        //        if (DevExpress.XtraEditors.XtraMessageBox.Show("", error, MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
        //            return -1;

        //    }
            return 0;
            
        }
        //处方审核
        public static int Pass_CheckedDrugs(int ai_checkmode, int ai_row, DataTable drugs,string Deptcode,string Deptname)
        {
            //#region 说明
            ///*******************美康4.0 合理用药嵌套代码《用户自定义函数》开始****************\
            //** 函 数 名: PassQueryOrCheck_new4()
            //** 输    入: ai_checkmode------传入监测摸式
            //**                       1		住院医生站保存自动审查
            //**								 2		住院医生站提交自动审查
            //**                       3		手工显示审查界面
            //**                       4		临床药学单病人审查
            //**                       5		临床药学多病人审查
            //**                       6		查看单药警告
            //**                       7		手工审查不显示审查界面
            //**                       33	门诊医生站保存自动审查
            //**                       34	门诊医生站提交自动审查
            //**                       110  右键菜单有效性控制标识  
            //**                       401	显示药品浮动窗口
            //**                       402	关闭所有浮动窗口
            //**                       403	显示单药最近一次审查结果浮动提示窗口
            //**								 
            //**           ai_row ----------传入当前所选择医嘱或处方的行
            //** 输    出:  integer ---- 0 		处理成功
            //**									100	未作处理
            //**									-1		取系统事件失败而返回
            //**									1		保存自动审查成功，但审查有警告项目
            //**									
            //** 功能描述: 适用于药物信息查询、浮动窗口、用药研究、警告和审查功能
            //** 全局变量: 
            //** 调用模块: 可以在右键弹出菜单、点击事件、保存按钮、提交按钮、用药研究
            //**           警告、和审查调用，输入不同的参数值，实现不同的PASS系统功能
            //\*****************************************************************************/
            //#endregion
            //#region
            ////integer	ll_ax,ll_bx,ll_ay,ll_by,ll_cellwidth
            ////long		ll_firstrow,ll_jumprow,ll_rowht,ll_ytodw,ll_headerht
            ////long		ll_width,ll_height
            ////long ls_yymd

            ////long pos_start,pos_end 
            //#endregion
            //int ll_c, ll_l;//长期越权数,临时越权数
            ////int ll_axtoscreen, ll_aytoscreen, ll_bxtoscreen, ll_bytoscreen;//药品编辑框左上角X坐标,药品编辑框左上角Y坐标,药品编辑框右上角X坐标,药品编辑框右上角Y坐标

            //DateTime ldt_CheckStrat = DateTime.Parse(DateTime.Today.ToShortDateString() + " 00:00:00"); //监测开始时间,必须赋值，用于判断当天用药医嘱开始范围，格式：yyyy-mm-dd hh:mm:ss，嵌套时最好改为服务器时间。
            //DateTime ldt_CheckEnd = DateTime.Parse(DateTime.Today.ToShortDateString() + " 23:59:59"); //监测结束时间,必须赋值，用于判断当天用药医嘱结束范围，格式：yyyy-mm-dd hh:mm:ss，嵌套时最好改为服务器时间。
            //DateTime ldt_stop_date ;
            //DateTime ldt_starttime=DateTime.Parse("1900-01-01 00:00:00");//开嘱时间
            //DateTime ldt_endtime = DateTime.MinValue;//停嘱时间
            //string ls_class, ls_drugcode, ls_drugname, ls_drugunit, ls_routename, ls_grouptag;//药品标识,药品编码,药品名称,剂量单位,给药途径中文名称,成组医嘱标识
            //string ls_stoptag, ls_statetag, ls_ldt_stop_date, ls_orderstag;//停嘱标识,作废标识,,长/临标识(1:长期;0临时)
            //int li_return, li_repeat_indicator;//返回值,
            //int li_freq_counter, li_freq_interval;//频次次数,频率间隔
            //string ls_freq_interval_unit, ls_frequency;//频率间隔单位,频次
            //string ls_singledose, ls_doctor, ls_doctor_pass_name, ls_doctor_pass;//每次用量,经治医生,,
            //string sql;
            //DataTable dt;
            //string ls_drugindex, tpcOrderType;//药品唯一码,
            //string error = "";//返回错误
            //string ls_passxml, ls_xml_mid;//美康MDC_GetResultDetail 函数返回值
            //string tdept_name, ls_prescno, ls_amount, ls_units;
            //string tyymd;//用药目的
            //DateTime ldt_sysdate;
            //List<string> ls_IndexDrugs = new List<string>();//药品列表
            //List<int> ll_DrugListRow = new List<int>();//药品唯一码 
            //ls_orderstag = "0";
            //NM_Service.NMService.ServerPublicClient sp = new NM_Service.NMService.ServerPublicClient();
            ////获取系统时间
            //ldt_sysdate = sp.GetSysDate();
            //if (ldt_sysdate==null)
            //{
            //    error = "取系统时间失败,无法继续合理用药审查.";
            //    return -1;
            //}
            //ldt_CheckStrat = DateTime.Parse(ldt_sysdate.ToShortDateString() + " 00:00:00"); //监测开始时间,必须赋值，用于判断当天用药医嘱开始范围，格式：yyyy-mm-dd hh:mm:ss，嵌套时最好改为服务器时间。
            //ldt_CheckEnd = DateTime.Parse(ldt_sysdate.ToShortDateString() + " 23:59:59"); //监测结束时间,必须赋值，用于判断当天用药医嘱结束范围，格式：yyyy-mm-dd hh:mm:ss，嵌套时最好改为服务器时间。

            ////循环获取监测结果警告值
            //int ll_ShowDrugRow;
            //int li_Warning;
            //int ll_error = 0;
            ////-------药物信息查询功能嵌套部份----------------
            //if (ai_checkmode == 6 || ai_checkmode == 110 || ai_checkmode == 401 || ai_checkmode == 403 || ai_checkmode == 1010 || ai_checkmode == 1011 || ai_checkmode == 51)
            //{
            //    #region
            //    //药物信息查询功能变量赋值，嵌套时用户根据自已HIS系统，进行修改赋值
            //    ls_class = drugs.Rows[ai_row]["ITEM_CLASS"].ToString().Trim(); //必须赋值，根据HIS系统药品标识来赋值，例如:'A'表示药品
            //    ls_drugcode = drugs.Rows[ai_row]["DRUG_CODE"].ToString().Trim();  //必须赋值，指医生下达医嘱时药品唯一编码，例如: J.TAD000000
            //    ls_drugname = drugs.Rows[ai_row]["DRUG_NAME"].ToString().Trim();  //必须赋值，指医生下达医嘱时药品名称，例如：安定片
            //    ls_drugunit = drugs.Rows[ai_row]["PACKAGE_UNITS"].ToString().Trim();  //必须赋值，指医生下达医嘱时给药单位剂量，例如：mg
            //    ls_routename = drugs.Rows[ai_row]["ADMINISTRATION"].ToString().Trim();  //必须赋值，指医生下达医嘱时给药途径中文名称，例如：口服
            //    ls_grouptag = ai_row.ToString();  //必须赋值，指医生下达医嘱时当前的医嘱序号。
            //    int.TryParse(drugs.Rows[ai_row]["NWARN"].ToString(), out li_Warning);  //必须赋值，指医生下达医嘱时当前的警告值。	

            //    //若药品代码为空，则返回
	           // if(string.IsNullOrEmpty(ls_drugcode)){
            //        return -1;
            //    }
            //    //如果不属于用药医嘱时，不进行用药信息查询，嵌套时用户根据自已HIS系统来判断，或删除此段嵌套代码
            //    if(!"A".Equals(ls_class)){
            //        return -1;//表示不属于用药医嘱时，不执行下面的嵌套代码，用户嵌套时根据HIS的药品标识来修改。
            //    }

            //    //组合唯一码，用户根据实际情况进行组合
            //    SysBase.SystemParm.gs_drugindexnew = ls_drugcode + "/" + ls_grouptag; //必须组合赋值，指传入病人用药医嘱接口时医嘱唯一标识码,该标识码在医嘱中不会发现变化。
            //    if (SysBase.SystemParm.PassEnabled) //判断是否启用PASS系统
            //    {
            //        //******查看警告浮动窗口和警告详细信息*********
            //        if (ai_checkmode == 6 || ai_checkmode == 403)
            //        {
            //            //如果当前获得的组合唯一码与上一次的唯一码相同并且属于查看警告浮动窗口时，不在调用警告接口
            //            if (SysBase.SystemParm.gs_drugindexnew.Equals(SysBase.SystemParm.gs_drugindexold) && ai_checkmode == 403) return -1;
            //            //调用警告查看接口
            //            li_return = PASS4Invoke.MDC_ShowWarningHint(SysBase.SystemParm.gs_drugindexnew);  //传入查看警告组合唯一码
            //            //临时保存当前组合唯一码
            //            SysBase.SystemParm.gs_drugindexold = SysBase.SystemParm.gs_drugindexnew;

            //        }
            //        else if (ai_checkmode == 110 || ai_checkmode == 51 || ai_checkmode == 401 || ai_checkmode == 1010 || ai_checkmode == 1011)
            //        {
            //            SysBase.SystemParm.gs_drugindexold = ""; //清除上一次临时保存的组合唯一码				
            //            //==========合理用药4.0====
            //            li_return = PASS4Invoke.MDC_GetDrugQueryInfo(ls_drugcode, ls_drugname, 51, 0, 0);
            //            if (li_return != 1)
            //            {
            //                error = PASS4Invoke.MDC_GetLastError();
            //                error = "合理用药接口提示 [" + error + "]，请与信息科联系!";

            //            }
            //            //==========合理用药4.0====
            //        }
            //    }
            //    #endregion
            //}
            //else
            //{
            //    //-------用药医嘱审查或用药研究功能嵌套部份----------------
            //    li_return = PASS4Invoke.MDC_CloseDrugHint();  //关闭当前所有浮动窗口
            //    if (li_return != 1)
            //    {
            //        error = PASS4Invoke.MDC_GetLastError();

            //        DevExpress.XtraEditors.XtraMessageBox.Show("合理用药接口提示 [" + error + "]，请与信息科联系!");
            //    }
            //    SysBase.SystemParm.gs_drugindexold = ""; //清除当前警告浮动窗口赋值

            //    //子处方变量
            //    long  li_z_order_no,ll_order_no,ll_order_sub_no;
            //    //sql = "Select nvl(Max(order_no),0) From orders Where patient_id = :ls_patient_id And visit_id = :ll_visit_id;";
            //    //DataSet ds = sp.GetDataBySql(sql);
            //    ll_order_no = 0;//最大医嘱号，不知道pid和vid
            //    #region 循环获取病人用药信息
            //    foreach (DataRow item in drugs.Rows)
            //    {
            //        ls_class = item["ITEM_CLASS"].ToString().Trim(); //根据HIS系统实际情况赋值,如果没有区分"药品标识",可以取消该项赋值，例如:'A'表示药品，'B'表示护理
            //        if ("A".Equals(ls_class)) continue;//当前医嘱不符合条件，继续下一个循环
            //        ls_drugname = item["DRUG_NAME"]== DBNull.Value?"":item["DRUG_NAME"].ToString().Trim();
            //        if (string.IsNullOrEmpty("DRUG_NAME")) continue;   //若当前记录中无药品，则继续下一个循环
            //        //子处方
            //        li_z_order_no = long.Parse(item["ORDER_SUB_NO"]== DBNull.Value?"1":item["ORDER_SUB_NO"].ToString().Trim());
				   
            //        if(li_z_order_no>1) {
            //            //  说明是子处方，直接使用
            //            ll_order_no = li_z_order_no;
            //        }
            //        else{
            //            ll_order_no ++;
            //        }
            //        //获取相关时间
            //        if(ai_row<0){
            //            if(ai_checkmode == 3 || ai_checkmode == 12){
            //                ldt_starttime = DateTime.Parse(item["PRESC_DATE"]==DBNull.Value?"1900-01-01 00:00:00":item["PRESC_DATE"].ToString());
            //            }
            //            else if(ai_checkmode == 1){
            //                ldt_starttime=ldt_sysdate;
            //            }
            //        }
            //        if(ldt_starttime.ToString("yyyy-MM-dd HH:mm:ss").Equals("1900-01-01 00:00:00")){
            //            //如果没有开嘱时间时,赋于当天的时间
            //            ldt_starttime=ldt_sysdate;
            //        }
            //        ls_orderstag = item["REPEAT_INDICATOR"].ToString();
            //        ldt_endtime = ldt_starttime;
            //        //如果没有开嘱时间时,赋于当天的时间
            //        if(ldt_endtime==null)
            //            ldt_endtime = ldt_sysdate;

            //        #region  复写代码
            //        ////*****判断当前监测的用药医嘱是否符合监测时间范围****
            //        ////当前医嘱不符合监测时间范围条件，继续下一个循环
            //        ////获取用户用药频次，嵌套时用户可以根据实际情况修改
            //        //ls_frequency = item["FREQUENCY"] == DBNull.Value ? "" : item["FREQUENCY"].ToString().Trim();
            //        //sql = "Select  FREQ_COUNTER, FREQ_INTERVAL, FREQ_INTERVAL_UNITS "
            //        //    + "From PERFORM_FREQ_DICT Where FREQ_DESC='"+ls_frequency+"'";
            //        //DataSet ds = sp.GetDataBySql(sql);
            //        //ls_freq_interval_unit = "";
            //        //if (ds != null && ds.Tables[0].Rows.Count > 0)
            //        //{
            //        //    int.TryParse(ds.Tables[0].Rows[0]["FREQ_COUNTER"].ToString(), out li_freq_counter);
            //        //    int.TryParse(ds.Tables[0].Rows[0]["FREQ_INTERVAL"].ToString(), out li_freq_interval);
            //        //    ls_freq_interval_unit = ds.Tables[0].Rows[0]["FREQ_INTERVAL_UNITS"] == DBNull.Value ? "" : ds.Tables[0].Rows[0]["FREQ_INTERVAL_UNITS"].ToString();
            //        //}
            //        //switch(ls_freq_interval_unit){
            //        //    case "周":
            //        //        ls_frequency=li_freq_counter.ToString()+"/"+(li_freq_interval*7).ToString();
            //        //        break;
            //        //    case "日":
            //        //        ls_frequency=li_freq_counter.ToString()+"/"+li_freq_interval.ToString();
            //        //        break;
            //        //    case "小时":
            //        //        if(li_freq_interval<= 24) {
            //        //            ls_frequency=((int)(24/li_freq_interval)*li_freq_counter).ToString() +"/"+li_freq_counter.ToString();
            //        //        }
            //        //        else{
            //        //            ls_frequency=li_freq_counter.ToString() +"/"+((int)(li_freq_interval/24)*li_freq_counter).ToString();
            //        //        }
            //        //        break;
            //        //    case "分钟":
            //        //        ls_frequency = ((int)(24 * 60 / li_freq_interval) * li_freq_counter).ToString() + "/" + li_freq_counter;
            //        //        break;                            
            //        //    default:
            //        //        ls_frequency="1/1";
            //        //        break;
            //        //}
            //        #endregion
            //        //获取用户用药频次，嵌套时用户可以根据实际情况修改
            //        try
            //        {
            //            li_freq_counter = int.Parse(item["freq_counter"].ToString()); //频次次数
            //            li_freq_interval = int.Parse(item["freq_interval"].ToString()); //频率间隔
            //            ls_freq_interval_unit = item["freq_interval_unit"].ToString().Trim(); //频率间隔单位
            //            ls_frequency = "";
            //            switch (ls_freq_interval_unit)
            //            {
            //                case "日":
            //                    ls_frequency = li_freq_counter.ToString() + "/" + li_freq_interval.ToString();
            //                    break;
            //                case "小时":
            //                    if (li_freq_interval <= 24)
            //                        ls_frequency = (Math.Floor(double.Parse((24 / li_freq_interval).ToString())) * li_freq_counter).ToString() + "/" + li_freq_counter.ToString();

            //                    if (li_freq_interval > 24)
            //                        ls_frequency = li_freq_counter.ToString() + "/" + (Math.Floor(double.Parse((li_freq_interval / 24).ToString()) * li_freq_counter)).ToString();
            //                    break;
            //                case "分钟":
            //                    ls_frequency = (Math.Floor(double.Parse((24 * 60 / li_freq_interval).ToString())) * li_freq_counter).ToString() + "/" + li_freq_counter.ToString();
            //                    break;
            //                default:
            //                    ls_frequency = "1/1";
            //                    break;
            //            }
            //            //获取病人用药信息
            //            ls_drugcode = item["drug_code"].ToString().Trim(); //必须赋值，指医生下达医嘱时药品唯一编码，例如: J.TAD000000
            //            ls_drugname = item["drug_name"].ToString().Trim(); //必须赋值，指医生下达医嘱时药品名称，例如：安定片
            //            ls_singledose = item["dosage_each"].ToString().Trim(); //必须赋值,指医生下达医嘱时每次用量 ，例如：120
            //            ls_drugunit = item["dosage_units"].ToString().Trim(); //必须赋值，指医生下达医嘱时给药单位剂量，例如：mg
            //            ls_routename = item["administration"].ToString().Trim(); //必须赋值，指医生下达医嘱时给药途径中文名称，例如：口服
                        
            //            ls_grouptag = ll_order_no==0?"":ll_order_no.ToString();//item["order_no"].ToString().Trim(); //必须赋值，指医生下达配伍医嘱时子医嘱的标识，通常HIS系统采用医嘱序号来标识，医嘱序号相同的两两医嘱表示配伍医嘱，不同的两两医嘱表示不属于配伍医嘱。
            //            ls_doctor = SysBase.SystemParm.LoginUser.ID + "/" + item["doctor"].ToString().Trim(); //必须赋值，指下达医嘱时的医生 例如：01/伊丽娜。
            //            ls_doctor_pass_name = item["doctor"].ToString();
            //            sql = "select db_user from users where user_name='" + ls_doctor_pass_name + "'";
            //            dt = sp.GetList(sql).Tables[0];
            //            if (dt!=null && dt.Rows.Count > 0)
            //                ls_doctor_pass = dt.Rows[0][0].ToString();
            //            else
            //                ls_doctor_pass = "";

            //            //组合唯一码，用户根据实际情况组合
            //            ls_drugindex = ls_drugcode + "/" + ls_grouptag; //必须组合赋值，指传入病人用药医嘱接口时医嘱唯一标识码
            //            if (SysBase.SystemParm.PassEnabled) //判断是否启用PASS系统
            //            {
            //                if (ls_orderstag.Equals("1"))
            //                    ls_orderstag = "0";  //合理用药 值0表示长期医嘱 ,1表示临时医嘱
            //                else
            //                    ls_orderstag = "1";
            //                tpcOrderType = "0";
            //                li_return = PASS4Invoke.MDC_AddScreenDrug(ls_drugindex, ls_grouptag, ls_drugcode, ls_drugname, ls_singledose, ls_drugunit,
            //                             ls_frequency, ls_routename, ls_routename,
            //                             ldt_starttime.ToString("yyyy-MM-dd"), ldt_endtime.ToString("yyyy-MM-dd"), ldt_starttime.ToString("yyyy-MM-dd"),
            //                             ls_grouptag, ls_orderstag, tpcOrderType,Deptcode, Deptname, ls_doctor_pass,
            //                             ls_doctor_pass_name, "", "", "", "", "", "", "");
            //                if (li_return != 1)
            //                {
            //                    error = PASS4Invoke.MDC_GetLastError();
            //                    error = "合理用药接口提示 [" + error + "]，请与信息科联系!";
            //                    DevExpress.XtraEditors.XtraMessageBox.Show(error);
            //                }
            //            }
                        
                        
                        
            //            //将当前传入病人用药信息接口时的组合唯一码，保存在数组中，目的用于合理用药监测完成之后，重新将组合唯一码，传入接口中取回监测结果警示色的值，并将该值用于判断警示颜色图标的显示。嵌套时用户可以根据实际情况考虑是否需要保存或考虑其它方式来保存。
            //            ls_IndexDrugs.Add(ls_drugindex); 
            //            ll_DrugListRow.Add(drugs.Rows.IndexOf(item));


            //        }
            //        catch (Exception e)
            //        {
            //            //error = e.Message;
            //            DevExpress.XtraEditors.XtraMessageBox.Show(e.Message);
            //            return -1;
            //        }
            //        #region 开始原来代码
            //        //if (string.IsNullOrEmpty(item["stop_date_time"].ToString()))
            //        //    ls_stoptag = "0";
            //        //else
            //        //    ls_stoptag = "1";
            //        //ls_ldt_stop_date = item["stop_date_time"].ToString();
            //        //if (string.IsNullOrEmpty(ls_ldt_stop_date))
            //        //{
            //        //    ldt_stop_date = DateTime.Parse(ls_ldt_stop_date).Date;
            //        //}
            //        //else
            //        //    ldt_stop_date = DateTime.Today.AddDays(-1);
            //        //li_repeat_indicator = int.Parse(item["repeat_indicator"].ToString());
            //        //ls_statetag = item["order_status"].ToString().Trim(); //根据HIS系统实际情况赋值,如果没有区分"停嘱标识",可以取消该项赋值，例如:'8'为作废医嘱，'2'为提交医嘱
            //        ////当前医嘱不符合条件，继续下一个循环
            //        //if (!ls_class.Equals("A")) continue;
            //        //if (ls_statetag.Equals("8")) continue;

            //        //if (li_repeat_indicator == 0 && (DateTime.Today - ldt_stop_date).Days != 0) continue;
            //        //if (ls_stoptag == "1" && li_repeat_indicator == 1 && (DateTime.Today - ldt_stop_date).Days < 0) continue;
            //        ////必须赋值，指医生下达医嘱的时间，格式 yyyy-mm-dd hh:mm:ss 例如：2005-08-12 11:12:00
            //        //if (string.IsNullOrEmpty(item["ENTER_DATE_TIME"].ToString()))
            //        //    ldt_starttime = DateTime.Parse(DateTime.Now.ToShortDateString() + " 00:00:00");
            //        //else
            //        //    ldt_starttime = DateTime.Parse(item["ENTER_DATE_TIME"].ToString());

            //        //ls_orderstag = item["repeat_indicator"].ToString(); //必须赋值，指当前医嘱是否为"长期医嘱"或"临时医嘱"传入值为"1"长期医嘱或"0"临时医嘱，而不是中文

            //        //if (ls_orderstag.Equals("0")) //判断如果为临时医嘱时,开嘱时间为停嘱时间
            //        //{
            //        //    ldt_endtime = ldt_starttime;
            //        //    ls_orderstag = "1";
            //        //}
            //        //else if (ls_orderstag.Equals("1"))
            //        //{
            //        //    if (!string.IsNullOrEmpty(item["stop_date_time"].ToString())) //必须赋值，指医生停止医嘱的时间，格式 yyyy-mm-dd hh:mm:ss  例如：2005-08-14 11:12:00
            //        //        ldt_endtime = DateTime.Parse(item["stop_date_time"].ToString());
            //        //    else
            //        //        ldt_endtime = DateTime.MinValue;
            //        //    ls_orderstag = "0";
            //        //}
            //        ////如果没有开嘱时间时,赋于当天的时间
            //        //if (ldt_endtime == DateTime.MinValue)
            //        //    ldt_endtime = DateTime.Parse(DateTime.Now.ToShortDateString() + " 23:59:59");

            //        ////*****判断当前监测的用药医嘱是否符合监测时间范围****
            //        //if (!(ldt_endtime < ldt_CheckStrat || ldt_starttime > ldt_CheckEnd)) //当前医嘱不符合监测时间范围条件，继续下一个循环
            //        //{
            //        //    //获取用户用药频次，嵌套时用户可以根据实际情况修改
            //        //    try
            //        //    {
            //        //        li_freq_counter = int.Parse(item["freq_counter"].ToString()); //频次次数
            //        //        li_freq_interval = int.Parse(item["freq_interval"].ToString()); //频率间隔
            //        //        ls_freq_interval_unit = item["freq_interval_unit"].ToString().Trim(); //频率间隔单位
            //        //        ls_frequency = "";
            //        //        switch (ls_freq_interval_unit)
            //        //        {
            //        //            case "日":
            //        //                ls_frequency = li_freq_counter.ToString() + "/" + li_freq_interval.ToString();
            //        //                break;
            //        //            case "小时":
            //        //                if (li_freq_interval <= 24)
            //        //                    ls_frequency = (Math.Floor(double.Parse((24 / li_freq_interval).ToString())) * li_freq_counter).ToString() + "/" + li_freq_counter.ToString();

            //        //                if (li_freq_interval > 24)
            //        //                    ls_frequency = li_freq_counter.ToString() + "/" + (Math.Floor(double.Parse((li_freq_interval / 24).ToString()) * li_freq_counter)).ToString();
            //        //                break;
            //        //            case "分钟":
            //        //                ls_frequency = (Math.Floor(double.Parse((24 * 60 / li_freq_interval).ToString())) * li_freq_counter).ToString() + "/" + li_freq_counter.ToString();
            //        //                break;
            //        //            default:
            //        //                break;
            //        //        }
            //        //        //获取病人用药信息
            //        //        ls_drugcode = item["order_code"].ToString().Trim(); //必须赋值，指医生下达医嘱时药品唯一编码，例如: J.TAD000000
            //        //        ls_drugname = item["order_text"].ToString().Trim(); //必须赋值，指医生下达医嘱时药品名称，例如：安定片
            //        //        ls_singledose = item["dosage"].ToString().Trim(); //必须赋值,指医生下达医嘱时每次用量 ，例如：120
            //        //        ls_drugunit = item["dosage_units"].ToString().Trim(); //必须赋值，指医生下达医嘱时给药单位剂量，例如：mg
            //        //        ls_routename = item["administration"].ToString().Trim(); //必须赋值，指医生下达医嘱时给药途径中文名称，例如：口服
            //        //        ls_grouptag = item["order_no"].ToString().Trim(); //必须赋值，指医生下达配伍医嘱时子医嘱的标识，通常HIS系统采用医嘱序号来标识，医嘱序号相同的两两医嘱表示配伍医嘱，不同的两两医嘱表示不属于配伍医嘱。
            //        //        ls_doctor = System.SystemParm.LoginUser.ID + "/" + item["doctor"].ToString().Trim(); //必须赋值，指下达医嘱时的医生 例如：01/伊丽娜。
            //        //        ls_doctor_pass_name = item["doctor"].ToString();
            //        //        sql = "select db_user from users where user_name='" + ls_doctor_pass_name + "'";
            //        //        dt = sp.GetList(sql).Tables[0];
            //        //        if (dt.Rows.Count > 0)
            //        //            ls_doctor_pass = dt.Rows[0][0].ToString();
            //        //        else
            //        //            ls_doctor_pass = "";

            //        //        //组合唯一码，用户根据实际情况组合
            //        //        ls_drugindex = ls_drugcode + "/" + ls_grouptag; //必须组合赋值，指传入病人用药医嘱接口时医嘱唯一标识码
            //        //        if (ls_orderstag.Equals("1"))
            //        //            ls_orderstag = "0";  //合理用药 值0表示长期医嘱 ,1表示临时医嘱
            //        //        else
            //        //            ls_orderstag = "1";
            //        //        tpcOrderType = "0";
            //        //        li_return = PASS4Invoke.MDC_AddScreenDrug(ls_drugindex, int.Parse(ls_grouptag), ls_drugcode, ls_drugname, ls_singledose, ls_drugunit,
            //        //                         ls_frequency, ls_routename, ls_routename,
            //        //                         ldt_starttime.ToString("yyyy-mm-dd"), ldt_endtime.ToString("yyyy-mm-dd"), ldt_starttime.ToString("yyyy-mm-dd"),
            //        //                         ls_grouptag, ls_orderstag, tpcOrderType, System.SystemParm.Deptcode, System.SystemParm.Deptname, ls_doctor_pass,
            //        //                         ls_doctor_pass_name, "", "", "", "", "", "", "");
            //        //        if (li_return != 1)
            //        //        {
            //        //            error = PASS4Invoke.MDC_GetLastError();
            //        //            error = "合理用药接口提示 [" + error + "]，请与信息科联系!";
            //        //            DevExpress.XtraEditors.XtraMessageBox.Show(error);
            //        //        }
            //        //        //将当前传入病人用药信息接口时的组合唯一码，保存在数组中，目的用于合理用药监测完成之后，重新将组合唯一码，传入接口中取回监测结果警示色的值，并将该值用于判断警示颜色图标的显示。嵌套时用户可以根据实际情况考虑是否需要保存或考虑其它方式来保存。
            //        //        ls_IndexDrugs.Add(ls_drugindex);
            //        //        ll_DrugListRow.Add(orders.Rows.IndexOf(item));


            //        //    }
            //        //    catch (Exception e)
            //        //    {
            //        //        //error = e.Message;
            //        //        DevExpress.XtraEditors.XtraMessageBox.Show(e.Message);
            //        //        return -1;
            //        //    }
            //        //}
            //        #endregion 结束
            //    }
            //    #endregion //循环获取病人用药信息
            //}


            ////------------执行审查摸式完成之后，获取警告值------------------------
            //if (SysBase.SystemParm.PassEnabled) //判断是否启用PASS系统
            //{
            //    if (ai_checkmode == 12 || ai_checkmode == 6 || ai_checkmode == 51 || ai_checkmode == 403 || ai_checkmode == 401) return -1;
            //    int li_docheck = -1;
            //    li_docheck = PASS4Invoke.MDC_DoCheck(1, 1);
            //    if (li_docheck < 0)
            //    {
            //        error = PASS4Invoke.MDC_GetLastError();
            //        error = "合理用药接口提示 [" + error + "]，请与信息科联系!";
            //        DevExpress.XtraEditors.XtraMessageBox.Show(error);
            //    }

            //    string pass_value="0";  // 0 关闭 1 打开
            //    sql = "SELECT NVL(A.PARAMETER_VALUE,'') FROM APP_CONFIGER_PARAMETER A WHERE A.APP_NAME='PASS' and a.parameter_name='PASS_DOCTWS_OUTPDOCT'";
            //    DataSet ds = sp.GetDataBySql(sql);
            //    if (ds != null && ds.Tables[0].Rows.Count > 0)
            //    {
            //        pass_value = ds.Tables[0].Rows[0][0].ToString();
            //    }
            //    if("1".Equals(pass_value)){
            //        int pos_start=-1;
		          //  int pos_end=-1;
            //        ll_c = 0;
            //        ll_l = 0;
            //        ls_passxml = "";
            //        foreach (DataRow item in drugs.Rows)
            //        {
            //            ls_drugcode = item["DRUG_CODE"].ToString(); //必须赋值，指医生下达医嘱时药品唯一编码，例如: J.TAD000000
            //            ls_grouptag = item["ORDER_NO"].ToString();//必须赋值，指医生下达配伍医嘱时子医嘱的标识，通常HIS系统采用医嘱序号来标识，医嘱序号相同的两两医嘱表示配伍医嘱，不同的两两医嘱表示不属于配伍医嘱。
            //            ls_drugindex =  ls_drugcode + "/"+ls_grouptag;
            //            if(!string.IsNullOrEmpty(ls_drugindex)){
            //                ls_passxml=PASS4Invoke.MDC_GetResultDetail(ls_drugindex);
		
		          //          pos_start=ls_passxml.IndexOf("<MODULENAME>");
		          //          pos_end=ls_passxml.IndexOf("</MODULENAME>");
            //                ls_xml_mid = ls_passxml.Substring(pos_start + 12, pos_end - pos_start - 12);
            //                if ("越权用药".Equals(ls_xml_mid) )
            //                {
            //                    if ("0".Equals(ls_orderstag))
            //                    {
            //                        ll_l++;
            //                    }
            //                    else
            //                    {
            //                        ll_c++;
            //                    }                                
            //                }

            //            }

            //        }
            //        pos_start=ls_passxml.IndexOf("<WARNING>");
		          //  pos_end=ls_passxml.IndexOf("</WARNING>");
            //        ls_xml_mid = ls_passxml.Substring(pos_start + 9, pos_end - pos_start - 9);
            //        if(ll_l>0 || ll_c>0){
            //            error = ls_xml_mid;
            //            if(ll_c>0)
            //                error = "长期医嘱药品有越权用药,禁止使用!"+ ls_xml_mid;
            //            DevExpress.XtraEditors.XtraMessageBox.Show(error);
            //            if (ll_c > 0)
            //                return -1;
            //        }

            //    }//end pass_value
            //    ll_error = 0;
            //    for (int i = 0; i < ll_DrugListRow.Count; i++) //循环获取监测结果警告值
            //    {
            //        ll_ShowDrugRow = ll_DrugListRow[i]; //获取传入的医嘱行号
            //        ls_drugindex = ls_IndexDrugs[i]; //获取传入的组合唯一码
            //        if (ls_drugindex.Length > 0)
            //        {
            //            li_Warning = PASS4Invoke.MDC_GetWarningCode(ls_drugindex);
            //            drugs.Rows[ll_ShowDrugRow]["nwarn"] = li_Warning;
            //            if (li_Warning == 1)
            //                ll_error++;
            //        }
            //    }
            //    if (ll_error > 0)
            //    {
            //        error = "PASS系统审查出存在不合理用药？继续保存吗？";
            //        if (DevExpress.XtraEditors.XtraMessageBox.Show("", error, MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
            //            return -1;

            //    }
            //}
            
            return 0;
        }
        /// <summary>
        /// 患者信息
        /// </summary>
        /// <param name="pcPatCode"></param>
        /// <param name="pcInHospNo"></param>
        /// <param name="pcVisitCode"></param>
        /// <param name="pcName"></param>
        /// <param name="pcSex"></param>
        /// <param name="pcBirthday"></param>
        /// <param name="pcHeightCM"></param>
        /// <param name="pcWeighKG"></param>
        /// <param name="pcDeptCode"></param>
        /// <param name="pcDeptName"></param>
        /// <param name="pcDoctorCode"></param>
        /// <param name="pcDoctorName"></param>
        /// <param name="piPatStatus"></param>
        /// <param name="piIsLactation"></param>
        /// <param name="piIsPregnancy"></param>
        /// <param name="pcPregStartDate"></param>
        /// <param name="piHepDamageDegree"></param>
        /// <param name="piRenDamageDegree"></param>
        /// <returns></returns>
        public static int MDC_SetPatient(String pcPatCode,String pcInHospNo,String pcVisitCode,String pcName,String pcSex,String pcBirthday,String pcHeightCM,String pcWeighKG,String pcDeptCode,String pcDeptName,String pcDoctorCode,String pcDoctorName,int piPatStatus,int piIsLactation,int piIsPregnancy,String pcPregStartDate,int piHepDamageDegree,int piRenDamageDegree)
        {
            int result = 0;
            result=PASS4Invoke.MDC_SetPatient(pcPatCode, pcInHospNo, pcVisitCode, pcName, pcSex, pcBirthday, pcHeightCM, pcWeighKG, pcDeptCode, pcDeptName, pcDoctorCode, pcDoctorName, piPatStatus, piIsLactation, piIsPregnancy, pcPregStartDate, piHepDamageDegree, piRenDamageDegree);
            return result;
        }
        /// <summary>
        /// 诊断信息
        /// </summary>
        /// <param name="pcIndex"></param>
        /// <param name="pcDiseaseCode"></param>
        /// <param name="pcDiseaseName"></param>
        /// <param name="pcRecipNo"></param>
        /// <returns></returns>
        public static int MDC_AddMedCond(string pcIndex,string pcDiseaseCode,string pcDiseaseName,string pcRecipNo)
        {
            int restult = 0;
            restult = PASS4Invoke.MDC_AddMedCond(pcIndex, pcDiseaseCode, pcDiseaseName, pcRecipNo);
            return restult;
        }
        /// <summary>
        /// 用药
        /// </summary>
        /// <param name="pcIndex"></param>
        /// <param name="pcDiseaseCode"></param>
        /// <param name="pcDiseaseName"></param>
        /// <param name="pcRecipNo"></param>
        /// <returns></returns>
        public static int MDC_AddScreenDrug(String pcIndex, String piOrderNo, String pcDrugUniqueCode, String pcDrugName, String pcDosePerTime, String pcDoseUnit, String pcFrequency, String pcRouteCode, String pcRouteName, String pcStartTime, String pcEndTime, String pcExecuteTime, String pcGroupTag, String pcIsTempDrug, String pcOrderType, String pcDeptCode, String pcDeptName, String pcDoctorCode, String pcDoctorName, String pcRecipNo, String pcNum, String pcNumUnit, String pcPurpose, String pcOprCode, String pcMediTime, String pcRemark)
        {
            int restult = 0;
            restult = PASS4Invoke.MDC_AddScreenDrug(pcIndex, piOrderNo, pcDrugUniqueCode, pcDrugName, pcDosePerTime, pcDoseUnit, pcFrequency, pcRouteCode, pcRouteName, pcStartTime, pcEndTime, pcExecuteTime, pcGroupTag, pcIsTempDrug, pcOrderType, pcDeptCode, pcDeptName, pcDoctorCode, pcDoctorName, pcRecipNo, pcNum, pcNumUnit, pcPurpose, pcOprCode, pcMediTime, pcRemark);
            return restult;
        }
        /// <summary>
        /// 审查函数
        /// </summary>
        /// <returns></returns>
        public static int MDC_DoCheck()
        {
            int restult = 0;
            restult = PASS4Invoke.MDC_DoCheck(1,1);
            return restult;
        }
        /// <summary>
        /// 药品医嘱警示级别
        /// </summary>
        /// <param name="pcIndex"></param>
        /// <returns></returns>
        public static int MDC_GetWarningCode(string pcIndex)
        {
            int restult = 0;
            restult = PASS4Invoke.MDC_GetWarningCode(pcIndex);
            return restult;
        }
        /// <summary>
        /// 一条药品医嘱的审查结果提示窗口
        /// </summary>
        /// <returns></returns>
        public static int MDC_ShowWarningHint(string pcIndex)
        {
            int restult = 0;
            restult = PASS4Invoke.MDC_ShowWarningHint(pcIndex);
            return restult;
        }
        /// <summary>
        /// 查询药品函数
        /// </summary>
        /// <param name="pcDrugUniqueCode"></param>
        /// <param name="pcDrugName"></param>
        /// <returns></returns>
        public static int MDC_DoSetDrug(string pcDrugUniqueCode,string pcDrugName)
        {
            int restult = 0;
            restult = PASS4Invoke.MDC_DoSetDrug(pcDrugUniqueCode, pcDrugName);
            return restult;
        }
        
        /// <summary>
        /// 药品信息查询函数
        /// </summary>
        /// <returns></returns>
        public static int MDC_DoRefDrug()
        {
            int restult = 0;
            restult = PASS4Invoke.MDC_DoRefDrug(51);
            return restult;
        }
        /// <summary>
        /// PASS V4退出函数
        /// </summary>
        /// <returns></returns>
        public static int MDC_Quit()
        {
            int restult = 0;
            restult = PASS4Invoke.MDC_Quit();
            return restult;
        }
    }
}
