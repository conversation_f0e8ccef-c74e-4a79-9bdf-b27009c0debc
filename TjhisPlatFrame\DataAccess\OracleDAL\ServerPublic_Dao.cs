﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Data;
using System.Collections;
using System.Windows.Forms;

namespace OracleDAL
{
    public class ServerPublic_Dao
    {
        public ServerPublic_Dao()
        { }
        #region  Method

        /// <summary>
        /// 取得服务器的时间
        /// </summary>
        public DateTime GetSysDate()
        {
            DateTime cmdresult = DateTime.Now;
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT to_char(SYSDATE,'yyyy-mm-dd hh24:mi:ss')  FROM DUAL ");
            try
            {
                using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
                {

                    if (db.OpenDB())
                    {
                        //db.BeginTransaction();
                        DataSet ds = db.SelectDataSet(strSql.ToString());
                        //db.CommitTransaction();
                        db.CloseDB();
                        if (ds != null && ds.Tables[0].Rows.Count > 0)
                        {
                            cmdresult = Convert.ToDateTime(ds.Tables[0].Rows[0][0].ToString());
                        }
                        

                    }

                }
                
            }
            catch (Exception ex)
            {
                Utility.LogFile.WriteLogAuto(ex.Message, "SPDAO");
                //throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
                
            }

            return cmdresult;
            
        }

        
        /// <summary>
        /// 获得返回值
        /// </summary>
        public string GetSingleValue(string strSql)
        {
            try
            {
                string ret = "";
                using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
                {
                    if (db.OpenDB())
                    {
                        //db.BeginTransaction();
                        DataSet ds = db.SelectDataSet(strSql);
                        //db.CommitTransaction();
                        db.CloseDB();
                        if(ds!=null && ds.Tables[0].Rows.Count>0){
                            ret = ds.Tables[0].Rows[0][0].ToString();
                        }
                    }
                    
                }
                return ret;
            }
            catch (Exception ex)
            {
                
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        
        /// <summary>
        /// 获取表数据
        /// </summary>
        public DataSet GetTableData(string tableName,string strWhere)
        {
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    String strSql = "Select * from " + tableName;
                    if (!String.IsNullOrEmpty(strWhere))
                    {
                        strSql += " Where " + strWhere;
                    }
                    //db.BeginTransaction();
                    DataSet dataset = db.SelectDataSet(new string[]{strSql},new string[]{tableName});
                    //db.CommitTransaction();
                    db.CloseDB();
                    return dataset;
                }
                else
                {
                    Utility.LogFile.WriteLogAuto("数据库连接失败！", "ServerPublicDao");
                    return null;
                }
                    
            }

        }

        /// <summary>
        /// 根据SQL语句获得一个DataSet
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public DataSet GetDataBySql(string sql)
        {
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    //db.BeginTransaction();
                    DataSet dataset = db.SelectDataSet(sql);
                    //db.CommitTransaction();
                    db.CloseDB();
                    return dataset;
                }
                else
                {
                    Utility.LogFile.WriteLogAuto("数据库连接失败！", "ServerPublicDao");
                    return null;
                }

            }
        }

        /// <summary>
        /// 根据SQL语句获得一个DataSet
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public DataSet GetDataBySql_Tran(string sql)
        {
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    DataSet dataset = db.SelectDataSet(sql);
                    db.CommitTransaction();
                    db.CloseDB();
                    return dataset;
                }
                else
                {
                    Utility.LogFile.WriteLogAuto("数据库连接失败！", "ServerPublicDao");
                    return null;
                }

            }
        }
        /// <summary>
        /// 获取存储过程数据集
        /// </summary>
        /// <param name="procedureName"></param>
        /// <param name="paramKey"></param>
        /// <param name="paramValue"></param>
        /// <param name="tableName"></param>
        /// <param name="sql"></param>
        /// <returns></returns>
        public DataSet GetProcedureDataSet(string procedureName, List<string> paramKey, List<string> paramValue, string tableName, string sql)
        {
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    DataSet dataset = db.GetProcedureDS(procedureName,paramKey,paramValue,tableName,sql);
                    db.CommitTransaction();
                    db.CloseDB();
                    return dataset;
                }
                else
                {
                    Utility.LogFile.WriteLogAuto("数据库连接失败！", "ServerPublicDao");
                    return null;
                }

            }
            
        }
        /// <summary>
        /// 获取存储过程里的挂号收费用
        /// </summary>
        /// <param name="procedureName"></param>
        /// <param name="paramKey"></param>
        /// <param name="paramValue"></param>
        /// <param name="para1">resultcode</param>
        /// <param name="para2">errmsg</param>
        /// <param name="para3"></param>
        /// <returns></returns>
        /// 
        public bool GetPubicProcedureDs(string procedureName, List<string> paramKey, List<string> paramValue, ref string para1, ref String para2, ref DataTable para3)
        {
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool bb = db.GetPubicProcedureDs(procedureName, paramKey, paramValue, ref para1, ref para2, ref para3);
                    if (para1.Equals("0") && bb == true) //过程调用成功 并且返回值为0 过程执行成功的情况下才进行提交 否则进行事物回滚
                    {
                        db.CommitTransaction();
                    }
                    else
                    {
                        db.RollbackTransaction();
                    }
                    db.CloseDB();
                    return true;
                }
                else
                {
                    Utility.LogFile.WriteLogAuto("数据库连接失败！", "ServerPublicDao");
                    return false;
                }

            }
        }

        public int SaveDataSet(DataSet dataset,Utility.OracleODP.OracleBaseClass db)
        {
            string errText = "";
           // return db.UpDate_Data(dataset);
            int ret = db.UpDate_Data(dataset, ref errText);
            if (ret <= 0)
            {
                if (ret  == 0)
                {
                    MessageBox.Show("没有需要保存的数据", "失败");
                }
                else
                {
                    MessageBox.Show(errText, "失败");
                }
                //throw new Exception(errText); 看以后需要 暂时先采用messageBox方式
            }
            return ret;
        } 
        /// <summary>
        /// 直接保存Dataset
        /// </summary>
        /// <param name="dataset"></param>
        /// <returns></returns>
        public int SaveDataSet(DataSet dataSet)
        {
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                string errText = "";
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    int ret = db.UpDate_Data(dataSet,ref errText);
                    if (ret<=0)
                    {
                        db.RollbackTransaction();
                        db.CloseDB();
                        if (ret < 0)
                        {
                            MessageBox.Show(errText, "失败");
                        }
                        //throw new Exception(errText); 看以后需要 暂时先采用messageBox方式
                    }
                    else
                    {
                        db.CommitTransaction();
                        db.CloseDB();
                    }
                   
                    return ret;
                }
                else
                {
                    return -1;
                }
            }
            //return db.UpDate_Data(dataset);
        }

        /// <summary>
        /// 护理专用保存更新DATASET
        /// </summary>
        /// <param name="dataset"></param>
        /// <returns></returns>
        public int SaveDataSetNur(DataSet dataSet)
        {
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                string errText = "";
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    int ret = db.UpDate_Data_Nur(dataSet,ref errText);
                    if (ret < 0)
                    {
                        db.RollbackTransaction();
                        db.CloseDB();
                        if (ret < 0)
                        {
                            MessageBox.Show(errText, "失败");
                        }
                        //throw new Exception(errText); 看以后需要 暂时先采用messageBox方式
                    }
                    else
                    {
                        db.CommitTransaction();
                        db.CloseDB();
                    }
                    return ret;
                }
                else
                {
                    return -1;
                }
            }
            //return db.UpDate_Data(dataset);
        }
        /// <summary>
        /// 更新多个DataSet
        /// </summary>
        public bool SaveTablesData(ArrayList dsList, Utility.OracleODP.OracleBaseClass db,ref string errText)
        {

            try
            {
               
                int ret = db.UpDate_DataSets(dsList,ref errText);
                if (ret <= 0)
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString());
            }
        }

        public bool SaveTablesData(ArrayList dsList, List<DataRow> drsChange = null)
        {
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                string errText = "";
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    int ret = db.UpDate_DataSets(dsList,ref errText ,drsChange);
                    bool retValue = false;
                    if (ret < 0)
                    {
                      
                        db.RollbackTransaction();
                        db.CloseDB();
                        MessageBox.Show(errText, "提示");
                    }
                    else
                    {
                        db.CommitTransaction();
                        retValue = true;
                        db.CloseDB();
                    }
                  
                    return retValue;
                }
                else
                {
                    return false;
                }
            }
        }

        public int SaveDsArry(ArrayList dsList, List<DataRow> drsChange = null)
        {
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                string errText = "";
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    int ret = db.UpDate_DsArray(dsList,ref errText, drsChange);
                    if (ret <= 0)
                    {
                        db.RollbackTransaction();
                        if (ret < 0)
                        {
                            MessageBox.Show(errText, "提示");
                        }
                    }
                    else
                    {
                        db.CommitTransaction();
                    }
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return -1;
                }
            }
        }
        //
        public string SaveDataSetAndDictionary(DataSet dataset, Dictionary<string, string> list)
        {
            Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass();
            if (db.OpenDB())
            {
                db.BeginTransaction();
                string errText = "";
                foreach (var item in list)
                {
                    string rev = db.ExecuteTransactionNOLimit(item.Key);
                    if (!string.IsNullOrEmpty(rev))
                    {
                        db.RollbackTransaction();
                        db.CloseDB();
                        return item.Value + rev;
                    }

                }
                if (db.UpDate_Data(dataset,ref errText) < 1)
                {
                    db.RollbackTransaction();
                    db.CloseDB();
                    return "更新ds失败！"+ errText;

                }
                db.CommitTransaction();
                db.CloseDB();

                return "";
            }
            return null;
             
        }
        //执行多条sql语句
        public string SaveTable(Dictionary<string, string> list)
        {
            Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass();
            //事物回滚

            if (db.OpenDB())
            {
                db.BeginTransaction();
                foreach (var item in list)
                {
                    string rev = db.ExecuteTransactionNOLimit(item.Key);
                    if (!string.IsNullOrEmpty(rev))
                    {
                        db.RollbackTransaction();
                        db.CloseDB();
                        return item.Value + rev;
                    }

                }
                db.CommitTransaction();
                db.CloseDB();

                return "";
            }

            return null;
        }
        //在外边事务控制
        public string SaveExcSql(Dictionary<string, string> list, Utility.OracleODP.OracleBaseClass db)
        {
            foreach (var item in list)
            {
                string rev = db.ExecuteTransactionNOLimit(item.Key);
                if (!string.IsNullOrEmpty(rev))
                {
                    //db.CloseDB();
                    return item.Value + rev;
                }

            }
            return "";
        }
        /// <summary>
        /// 根据SQL语句获得一个DataSet待数据连接
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public DataSet GetListUseDB(string sql, Utility.OracleODP.OracleBaseClass db)
        {
            
                    DataSet dataset = db.SelectDataSet(sql);
                    return dataset;
               

            
        }
        /// <summary>
        /// 获取系统时间
        /// </summary>
        /// <returns></returns>
        public string GetSysDateTime()
        {
            string strSql = "select to_char(sysdate,'yyyy-mm-dd HH24:mi:ss') from dual";
            DataSet ds = GetDataBySql(strSql);
            if (ds != null && ds.Tables[0].Rows.Count > 0)
            {
                return ds.Tables[0].Rows[0][0].ToString();
            }
            return "";
        }

        /// <summary>
        /// 参数查询
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="ParaList"></param>
        /// <param name="ValueList"></param>
        /// <returns></returns>
        public DataSet GetDataTable_Para(string sql, List<string> ParaList, ArrayList ValueList)
        {
            if (string.IsNullOrEmpty(sql) || ParaList == null || ValueList == null) return null;
            List<Oracle.ManagedDataAccess.Client.OracleParameter> paras = new List<Oracle.ManagedDataAccess.Client.OracleParameter>();
            for (int iP = 0; iP < ParaList.Count; iP++)
            {
                Oracle.ManagedDataAccess.Client.OracleParameter para = new Oracle.ManagedDataAccess.Client.OracleParameter(ParaList[iP], ValueList[iP]);
                Type tp = ValueList[iP].GetType();
                if (tp.Equals(typeof(DateTime)))
                {
                    para.OracleDbType = Oracle.ManagedDataAccess.Client.OracleDbType.Date;
                }
                else
                {
                    para.OracleDbType = Oracle.ManagedDataAccess.Client.OracleDbType.Varchar2;
                }
                paras.Add(para);
            }
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    //db.BeginTransaction();
                    DataSet dataset = db.SelectDataSet(sql, paras);
                    //db.CommitTransaction();
                    db.CloseDB();
                    if (dataset != null && dataset.Tables.Count > 0)
                    {
                        return dataset;
                    }
                    else
                        return null;
                }
                else
                {
                    Utility.LogFile.WriteLogAuto("数据库连接失败！", "ServerPublicDao");
                    return null;
                }

            }
        }
        /// <summary>
        /// 参数查询
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="ParaList"></param>
        /// <param name="ValueList"></param>
        /// <param name="db"></param>
        /// <returns></returns>
        public DataSet GetDataTable_Para(string sql, List<string> ParaList, ArrayList ValueList, Utility.OracleODP.OracleBaseClass db)
        {
            if (string.IsNullOrEmpty(sql) || ParaList == null || ValueList == null) return null;

            List<Oracle.ManagedDataAccess.Client.OracleParameter> paras = new List<Oracle.ManagedDataAccess.Client.OracleParameter>();
            for (int iP = 0; iP < ParaList.Count; iP++)
            {
                Oracle.ManagedDataAccess.Client.OracleParameter para = new Oracle.ManagedDataAccess.Client.OracleParameter(ParaList[iP], ValueList[iP]);
                Type tp = ValueList[iP].GetType();
                if (tp.Equals(typeof(DateTime)))
                {
                    para.OracleDbType = Oracle.ManagedDataAccess.Client.OracleDbType.Date;
                }
                else
                {
                    para.OracleDbType = Oracle.ManagedDataAccess.Client.OracleDbType.Varchar2;
                }
                paras.Add(para);
            }
            DataSet dataset = db.SelectDataSet(sql, paras);
            if (dataset != null )
             {
                return dataset;
             }
            else
                return null;
        }

        /// <summary>
        /// 参数查询
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="ParaList"></param>
        /// <param name="ValueList"></param>
        /// <returns></returns>
        public DataSet GetDataTable_Para_tran(string sql, List<string> ParaList, ArrayList ValueList)
        {
            if (string.IsNullOrEmpty(sql) || ParaList == null || ValueList == null) return null;
            List<Oracle.ManagedDataAccess.Client.OracleParameter> paras = new List<Oracle.ManagedDataAccess.Client.OracleParameter>();
            for (int iP = 0; iP < ParaList.Count; iP++)
            {
                Oracle.ManagedDataAccess.Client.OracleParameter para = new Oracle.ManagedDataAccess.Client.OracleParameter(ParaList[iP], ValueList[iP]);
                Type tp = ValueList[iP].GetType();
                if (tp.Equals(typeof(DateTime)))
                {
                    para.OracleDbType = Oracle.ManagedDataAccess.Client.OracleDbType.Date;
                }
                else
                {
                    para.OracleDbType = Oracle.ManagedDataAccess.Client.OracleDbType.Varchar2;
                }
                paras.Add(para);
            }
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    DataSet dataset = db.SelectDataSet(sql, paras);
                    db.CommitTransaction();
                    db.CloseDB();
                    if (dataset != null && dataset.Tables.Count > 0)
                    {
                        return dataset;
                    }
                    else
                        return null;
                }
                else
                {
                    Utility.LogFile.WriteLogAuto("数据库连接失败！", "ServerPublicDao");
                    return null;
                }

            }
        }
        #endregion

    }
}
