﻿using System;
using System.Text;
using System.Collections.Generic;
using System.Data;
using Model;
using Utility.OracleODP;

namespace NM_Service.NMService
{

    /// <summary>
    /// 工作人员字典实现类
    /// </summary> 	

    public class STAFF_DICTClient : INMService.ISTAFF_DICT,IDisposable
    {
        #region  Method
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists_STAFF_DICT(string ID)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.STAFF_DICT_Dao().Exists(ID, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }

        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add_STAFF_DICT(Model.STAFF_DICT model)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.STAFF_DICT_Dao().Add(model, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update_STAFF_DICT(Model.STAFF_DICT model)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.STAFF_DICT_Dao().Update(model, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete_STAFF_DICT(string ID)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.STAFF_DICT_Dao().Delete(ID, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.STAFF_DICT GetModel_STAFF_DICT(string ID)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    Model.STAFF_DICT ret = new OracleDAL.STAFF_DICT_Dao().GetModel(ID, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList_All_STAFF_DICT(string strWhere)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    DataSet ret = new OracleDAL.STAFF_DICT_Dao().GetList(strWhere, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>   
        public DataSet GetList_STAFF_DICT(int startIndex, int endIndex, string strWhere, string filedOrder)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    DataSet ret = new OracleDAL.STAFF_DICT_Dao().GetList(startIndex, endIndex, strWhere, filedOrder, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>  
        public System.Collections.ObjectModel.ObservableCollection<Model.STAFF_DICT> GetObservableCollection_All_STAFF_DICT(string strWhere)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    System.Collections.ObjectModel.ObservableCollection<Model.STAFF_DICT> ret = new OracleDAL.STAFF_DICT_Dao().GetObservableCollection(strWhere, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 获得ObservableCollection根据分页获得数据列表
        /// </summary>  
        public System.Collections.ObjectModel.ObservableCollection<Model.STAFF_DICT> GetObservableCollection_STAFF_DICT(int startIndex, int endIndex, string strWhere, string filedOrder)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    System.Collections.ObjectModel.ObservableCollection<Model.STAFF_DICT> ret = new OracleDAL.STAFF_DICT_Dao().GetObservableCollection(startIndex, endIndex, strWhere, filedOrder, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        public STAFF_DICT GetModelByUserName_STAFF_DICT(string UserName)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    STAFF_DICT ret = new OracleDAL.STAFF_DICT_Dao().GetModelByUserName(UserName);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        public STAFF_DICT GetModelByUserName_STAFF_DICT(string UserName, bool isHisUnitCode)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    STAFF_DICT ret = new OracleDAL.STAFF_DICT_Dao().GetModelByUserName(UserName, isHisUnitCode);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        public int LoginValidate_STAFF_DICT(string USER_NAME, string PASSWORD)
        {
            return new OracleDAL.STAFF_DICT_Dao().Exists(USER_NAME, PASSWORD);
        }

        #endregion

        #region IDisposable 成员

        /// <summary>
　　　　/// 实现IDisposable接口
　　　　/// </summary>
　　　　public void Dispose()
　　　　{
　　　　　　Dispose(true);
　　　　　　//.NET Framework 类库
　　　　　　// GC..::.SuppressFinalize 方法
　　　　　　//请求系统不要调用指定对象的终结器。
　　　　　　GC.SuppressFinalize(this);
　　　　}
　　　　/// <summary>
　　　　/// 虚方法，可供子类重写
　　　　/// </summary>
　　　　/// <param name="disposing"></param>
　　　　protected virtual void Dispose(bool disposing)
　　　　{
　　　　　　if (disposing)
　　　　　　　　{
　　　　　　　　　　// Release managed resources
　　　　　　　　}
　　　　}
　　　　/// <summary>
　　　　/// 析构函数
　　　　/// 当客户端没有显示调用Dispose()时由GC完成资源回收功能
　　　　/// </summary>
    ~STAFF_DICTClient()
　　　　{
　　　　　　Dispose();
　　　　}

        #endregion
    }
}