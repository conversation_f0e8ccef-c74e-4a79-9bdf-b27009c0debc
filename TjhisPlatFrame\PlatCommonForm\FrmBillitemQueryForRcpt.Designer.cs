﻿namespace PlatCommonForm
{
    partial class FrmBillitemQueryForRcpt
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FrmBillitemQueryForRcpt));
            this.barManager1 = new DevExpress.XtraBars.BarManager();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.cb_refresh = new DevExpress.XtraBars.BarLargeButtonItem();
            this.cb_save = new DevExpress.XtraBars.BarLargeButtonItem();
            this.cb_copy = new DevExpress.XtraBars.BarLargeButtonItem();
            this.cb_close = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.cb_childorder = new DevExpress.XtraBars.BarLargeButtonItem();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.radioGroup1 = new DevExpress.XtraEditors.RadioGroup();
            this.checkEdit1 = new DevExpress.XtraEditors.CheckEdit();
            this.textEdit1 = new DevExpress.XtraEditors.TextEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.p1 = new DevExpress.XtraEditors.PanelControl();
            this.g1 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.p2 = new DevExpress.XtraEditors.PanelControl();
            this.g2 = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.p3 = new DevExpress.XtraEditors.PanelControl();
            this.g3 = new DevExpress.XtraGrid.GridControl();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroup1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.p1)).BeginInit();
            this.p1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.g1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.p2)).BeginInit();
            this.p2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.g2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.p3)).BeginInit();
            this.p3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.g3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar2});
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.cb_childorder,
            this.cb_refresh,
            this.cb_copy,
            this.cb_save,
            this.cb_close});
            this.barManager1.MainMenu = this.bar2;
            this.barManager1.MaxItemId = 18;
            // 
            // bar2
            // 
            this.bar2.BarName = "Main menu";
            this.bar2.DockCol = 0;
            this.bar2.DockRow = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar2.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.cb_refresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.cb_save),
            new DevExpress.XtraBars.LinkPersistInfo(this.cb_copy),
            new DevExpress.XtraBars.LinkPersistInfo(this.cb_close)});
            this.bar2.OptionsBar.AllowQuickCustomization = false;
            this.bar2.OptionsBar.DrawBorder = false;
            this.bar2.OptionsBar.MultiLine = true;
            this.bar2.OptionsBar.UseWholeRow = true;
            this.bar2.Text = "Main menu";
            // 
            // cb_refresh
            // 
            this.cb_refresh.Caption = "选择";
            this.cb_refresh.Glyph = ((System.Drawing.Image)(resources.GetObject("cb_refresh.Glyph")));
            this.cb_refresh.Id = 5;
            this.cb_refresh.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("cb_refresh.LargeGlyph")));
            this.cb_refresh.Name = "cb_refresh";
            // 
            // cb_save
            // 
            this.cb_save.Caption = "打印";
            this.cb_save.Glyph = ((System.Drawing.Image)(resources.GetObject("cb_save.Glyph")));
            this.cb_save.Id = 10;
            this.cb_save.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("cb_save.LargeGlyph")));
            this.cb_save.Name = "cb_save";
            // 
            // cb_copy
            // 
            this.cb_copy.Caption = "清屏";
            this.cb_copy.Glyph = ((System.Drawing.Image)(resources.GetObject("cb_copy.Glyph")));
            this.cb_copy.Id = 6;
            this.cb_copy.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("cb_copy.LargeGlyph")));
            this.cb_copy.Name = "cb_copy";
            // 
            // cb_close
            // 
            this.cb_close.Caption = "关闭";
            this.cb_close.Glyph = ((System.Drawing.Image)(resources.GetObject("cb_close.Glyph")));
            this.cb_close.Id = 11;
            this.cb_close.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("cb_close.LargeGlyph")));
            this.cb_close.Name = "cb_close";
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Margin = new System.Windows.Forms.Padding(3, 6, 3, 6);
            this.barDockControlTop.Size = new System.Drawing.Size(1044, 73);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 521);
            this.barDockControlBottom.Margin = new System.Windows.Forms.Padding(3, 6, 3, 6);
            this.barDockControlBottom.Size = new System.Drawing.Size(1044, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 73);
            this.barDockControlLeft.Margin = new System.Windows.Forms.Padding(3, 6, 3, 6);
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 448);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1044, 73);
            this.barDockControlRight.Margin = new System.Windows.Forms.Padding(3, 6, 3, 6);
            this.barDockControlRight.Size = new System.Drawing.Size(0, 448);
            // 
            // cb_childorder
            // 
            this.cb_childorder.Caption = "子医嘱";
            this.cb_childorder.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.cb_childorder.Glyph = ((System.Drawing.Image)(resources.GetObject("cb_childorder.Glyph")));
            this.cb_childorder.Id = 3;
            this.cb_childorder.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("cb_childorder.LargeGlyph")));
            this.cb_childorder.Name = "cb_childorder";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.radioGroup1);
            this.groupControl1.Controls.Add(this.checkEdit1);
            this.groupControl1.Controls.Add(this.textEdit1);
            this.groupControl1.Controls.Add(this.labelControl1);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl1.Location = new System.Drawing.Point(0, 73);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.ShowCaption = false;
            this.groupControl1.Size = new System.Drawing.Size(1044, 44);
            this.groupControl1.TabIndex = 4;
            // 
            // radioGroup1
            // 
            this.radioGroup1.Location = new System.Drawing.Point(273, 9);
            this.radioGroup1.MenuManager = this.barManager1;
            this.radioGroup1.Name = "radioGroup1";
            this.radioGroup1.Properties.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.radioGroup1.Properties.Appearance.Options.UseBackColor = true;
            this.radioGroup1.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "不合并"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "合并")});
            this.radioGroup1.Size = new System.Drawing.Size(148, 27);
            this.radioGroup1.TabIndex = 3;
            // 
            // checkEdit1
            // 
            this.checkEdit1.Location = new System.Drawing.Point(427, 13);
            this.checkEdit1.MenuManager = this.barManager1;
            this.checkEdit1.Name = "checkEdit1";
            this.checkEdit1.Properties.Caption = "三类";
            this.checkEdit1.Size = new System.Drawing.Size(75, 22);
            this.checkEdit1.TabIndex = 2;
            // 
            // textEdit1
            // 
            this.textEdit1.Location = new System.Drawing.Point(77, 11);
            this.textEdit1.MenuManager = this.barManager1;
            this.textEdit1.Name = "textEdit1";
            this.textEdit1.Size = new System.Drawing.Size(175, 24);
            this.textEdit1.TabIndex = 1;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(13, 14);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(45, 18);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "收据号";
            // 
            // p1
            // 
            this.p1.Controls.Add(this.g1);
            this.p1.Location = new System.Drawing.Point(0, 121);
            this.p1.Name = "p1";
            this.p1.Size = new System.Drawing.Size(291, 388);
            this.p1.TabIndex = 5;
            // 
            // g1
            // 
            this.g1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.g1.Location = new System.Drawing.Point(2, 2);
            this.g1.MainView = this.gridView1;
            this.g1.MenuManager = this.barManager1;
            this.g1.Name = "g1";
            this.g1.Size = new System.Drawing.Size(287, 384);
            this.g1.TabIndex = 0;
            this.g1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.GridControl = this.g1;
            this.gridView1.Name = "gridView1";
            // 
            // p2
            // 
            this.p2.Controls.Add(this.g2);
            this.p2.Location = new System.Drawing.Point(314, 121);
            this.p2.Name = "p2";
            this.p2.Size = new System.Drawing.Size(291, 388);
            this.p2.TabIndex = 5;
            this.p2.Visible = false;
            // 
            // g2
            // 
            this.g2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.g2.Location = new System.Drawing.Point(2, 2);
            this.g2.MainView = this.gridView2;
            this.g2.MenuManager = this.barManager1;
            this.g2.Name = "g2";
            this.g2.Size = new System.Drawing.Size(287, 384);
            this.g2.TabIndex = 1;
            this.g2.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // gridView2
            // 
            this.gridView2.GridControl = this.g2;
            this.gridView2.Name = "gridView2";
            // 
            // p3
            // 
            this.p3.Controls.Add(this.g3);
            this.p3.Location = new System.Drawing.Point(621, 121);
            this.p3.Name = "p3";
            this.p3.Size = new System.Drawing.Size(291, 388);
            this.p3.TabIndex = 5;
            this.p3.Visible = false;
            // 
            // g3
            // 
            this.g3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.g3.Location = new System.Drawing.Point(2, 2);
            this.g3.MainView = this.gridView3;
            this.g3.MenuManager = this.barManager1;
            this.g3.Name = "g3";
            this.g3.Size = new System.Drawing.Size(287, 384);
            this.g3.TabIndex = 1;
            this.g3.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView3});
            // 
            // gridView3
            // 
            this.gridView3.GridControl = this.g3;
            this.gridView3.Name = "gridView3";
            // 
            // FrmBillitemQueryForRcpt
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1044, 521);
            this.Controls.Add(this.p3);
            this.Controls.Add(this.p2);
            this.Controls.Add(this.p1);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Name = "FrmBillitemQueryForRcpt";
            this.Text = "按收据号查询费用明细";
            this.Load += new System.EventHandler(this.FrmBillitemQueryForRcpt_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroup1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.p1)).EndInit();
            this.p1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.g1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.p2)).EndInit();
            this.p2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.g2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.p3)).EndInit();
            this.p3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.g3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraBars.BarLargeButtonItem cb_refresh;
        private DevExpress.XtraBars.BarLargeButtonItem cb_save;
        private DevExpress.XtraBars.BarLargeButtonItem cb_copy;
        private DevExpress.XtraBars.BarLargeButtonItem cb_close;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarLargeButtonItem cb_childorder;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.RadioGroup radioGroup1;
        private DevExpress.XtraEditors.CheckEdit checkEdit1;
        private DevExpress.XtraEditors.TextEdit textEdit1;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.PanelControl p3;
        private DevExpress.XtraGrid.GridControl g3;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraEditors.PanelControl p2;
        private DevExpress.XtraGrid.GridControl g2;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraEditors.PanelControl p1;
        private DevExpress.XtraGrid.GridControl g1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
    }
}