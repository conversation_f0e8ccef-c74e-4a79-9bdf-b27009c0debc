﻿using System;
using System.Data;
using System.Windows.Forms;
using Tjhis.Report.Custom.Common;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmLocalGridReport : frmStatisticalQueryGrid
    {
        public string tempName { get; set; }

        DataSet reportDict;
        DataSet reportParam;
        DataSet reportConfig;
        
        public frmLocalGridReport()
        {
            InitializeComponent();
        }
        public frmLocalGridReport(string reportName) : this()
        {
            this.tempName = reportName;
        }

        //NURADM_REPORT_STATISTICS_DICT   NURADM_REPORT_VS_PARAM  NURADM_REPORT_CONFIG
        protected override void TempletInfo_load()
        {
            if (this.Tag != null)
                tempName = this.Tag.ToString();
            else
                this.Tag = tempName;

            DataSet ds = new DataSet();

            string path = $@"{Application.StartupPath}\reports\{Const.customAppCode}\{tempName}.xml";

            ds.ReadXml(path);

            if (ds.Tables.Count <= 0) return;

            if (!ds.Tables.Contains("NURADM_REPORT_STATISTICS_DICT"))
                return;

            reportDict = new DataSet();
            reportDict.Tables.Add(ds.Tables["NURADM_REPORT_STATISTICS_DICT"].Copy());

            if (ds.Tables.Contains("NURADM_REPORT_CONFIG"))
            {
                reportConfig = new DataSet();
                reportConfig.Tables.Add(ds.Tables["NURADM_REPORT_CONFIG"].Copy());
            }

            if (ds.Tables.Contains("NURADM_REPORT_VS_PARAM"))
            {
                reportParam = new DataSet();
                reportParam.Tables.Add(ds.Tables["NURADM_REPORT_VS_PARAM"].Copy());
            }

            drTemplet = reportDict.Tables[0].Rows[0];
            this.TempleteFile = drTemplet["dict_name"].ToString() + "_" + drTemplet["dict_id"].ToString();
            this.TempleteSql = drTemplet["templet_sql"].ToString();

            this.Text = drTemplet["dict_name"].ToString();
        }

        protected override DataSet GetReportParamNew(string strAppName,string reportID)
        {
            return reportParam;
        }
        protected override DataSet GetReportConfig(string dictId, string strAppName)
        {
            return reportConfig.TestFun();
        }
        protected override DataRow GetTempletNew()
        {
            return reportDict?.Tables?[0]?.Rows?[0];
        }

        public bool TryFun<T>(Action<T,T> f,T args)
        {
            "".GetDataSet();
            return false;
        }

    }


    public static class Test
    {
        public static object GetCellValue(this DataSet ds, string columnName)
        {
            return ds.Tables[0].Rows[0][columnName];
        }

        public static T TestFun<T>(this T t)
        {
            return t;
        }

        public static int SaveToDB(this DataSet ds)
        {

            return 0;
        }

        public static int ExqSql(this string sql)
        {
            return 0;
        }
        /// <summary>
        /// 根据正确的sql查询返回结果集
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public static DataSet GetDataSet(this string sql)
        {
            return new DataSet();
        }

        public static string ConvertPascal(this string name, bool firstUpper = false)
        {
            string result = string.Empty;
            
            name = name.Trim();
            string[] arrStr = name.Split('_');
            for(int i=0;i<arrStr.Length;i++)
            {
                if (!firstUpper && i == 0)
                {
                    {
                        result += arrStr[i].ToLower();
                        continue;
                    }
                }
                result += arrStr[i].Substring(0, 1).ToUpper() + arrStr[i].Substring(1).ToLower();
            }

            return result;
        }

        public static string TestFun()
        {
            string a = "";

            a = a.ConvertPascal();

            DataSet dataSet = a.GetDataSet();

            return a;
        }
    }
}
