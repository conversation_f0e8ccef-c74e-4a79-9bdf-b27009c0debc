﻿/*********************************************
* 文 件 名：Cs01StringExtend
* 类 名 称：Cs01StringExtend
* 功能说明：字符串与UNICODE代码转换
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：刘成刚
* 创建时间：2019-05-24 23:04:16
* 版 本 号：1.0.0.1
* 修改时间：
* 修 改 人：
* CLR 版本：4.0.30319.42000
/*********************************************/

using System;
using System.Text;

namespace PlatCommon.Base01
{
    /// <summary>
    /// 扩展字符串类
    /// </summary>
    public static class Cs01StringExtend
    {
        /// <summary>
        /// 字符串转UNICODE代码
        /// </summary>
        /// <param name="strSource">源字符串</param>
        /// <returns></returns>
        public static string StringToUnicode(string strSource)
        {
            if (string.IsNullOrEmpty(strSource)) return string.Empty;

            char[] charbuffers = strSource.ToCharArray();
            byte[] buffer;
            StringBuilder sbResult = new StringBuilder();
            for (int i = 0; i < charbuffers.Length; i++)
            {
                buffer = System.Text.Encoding.Unicode.GetBytes(charbuffers[i].ToString());
                sbResult.Append(String.Format("{0:X2}{1:X2}", buffer[1], buffer[0]));
//                sbResult.Append(String.Format("\\u{0:X2}{1:X2}", buffer[1], buffer[0]));
            }
            return sbResult.ToString();
        }

        /// <summary>  
        /// Unicode字符串转为正常字符串  
        /// </summary>  
        /// <param name="strSource">源Unicode字符串</param>  
        /// <returns></returns>  
        public static string UnicodeToString(string strSource)
        {
            if (string.IsNullOrEmpty(strSource)) return string.Empty;

            string strReturn = string.Empty;
            string src = strSource;
            int len = strSource.Length / 4;
            for (int i = 0; i < len ; i++)
            {
                string strSub = "";
                strSub = src.Substring(0, 4);
                src = src.Substring(4);
                byte[] bytes = new byte[2];
                bytes[1] = byte.Parse(int.Parse(strSub.Substring(0, 2), System.Globalization.NumberStyles.HexNumber).ToString());
                bytes[0] = byte.Parse(int.Parse(strSub.Substring(2, 2), System.Globalization.NumberStyles.HexNumber).ToString());
                strReturn += Encoding.Unicode.GetString(bytes);
            }
            return strReturn;
        }

    }
}
