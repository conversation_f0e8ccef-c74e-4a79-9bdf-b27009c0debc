﻿namespace Tjhis.Report.Custom.Custom
{
    partial class frmParamDict
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmParamDict));
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gc_SerialNo = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_PARAM_NAME = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_EDIT_TYPE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.LUE_EditType = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.gc_CAPTION = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_DISPLAY_MEMBER = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_VALUE_MEMBER = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_SOURCE_TYPE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.LUE_SourceType = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.gc_NEXT_PARAM_NAME = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_DEFAULT_VALUE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_CONTROL_WIDTH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_PARAM_ID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_STATUS = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemCheckEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.gcDATE_FORMAT = new DevExpress.XtraGrid.Columns.GridColumn();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.btnAdd = new DevExpress.XtraBars.BarLargeButtonItem();
            this.btnEdit = new DevExpress.XtraBars.BarLargeButtonItem();
            this.btnDelete = new DevExpress.XtraBars.BarLargeButtonItem();
            this.btnSave = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.barLargeButtonItem1 = new DevExpress.XtraBars.BarLargeButtonItem();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.LUE_EditType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.LUE_SourceType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl1
            // 
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 40);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemCheckEdit1,
            this.LUE_EditType,
            this.LUE_SourceType});
            this.gridControl1.Size = new System.Drawing.Size(784, 521);
            this.gridControl1.TabIndex = 0;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gc_SerialNo,
            this.gc_PARAM_NAME,
            this.gc_EDIT_TYPE,
            this.gc_CAPTION,
            this.gc_DISPLAY_MEMBER,
            this.gc_VALUE_MEMBER,
            this.gc_SOURCE_TYPE,
            this.gc_NEXT_PARAM_NAME,
            this.gc_DEFAULT_VALUE,
            this.gc_CONTROL_WIDTH,
            this.gc_PARAM_ID,
            this.gc_STATUS,
            this.gcDATE_FORMAT});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsCustomization.AllowFilter = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.RowClick += new DevExpress.XtraGrid.Views.Grid.RowClickEventHandler(this.gridView1_RowClick);
            this.gridView1.InitNewRow += new DevExpress.XtraGrid.Views.Grid.InitNewRowEventHandler(this.gridView1_InitNewRow);
            // 
            // gc_SerialNo
            // 
            this.gc_SerialNo.Caption = "显示序号";
            this.gc_SerialNo.FieldName = "SERIAL_NO";
            this.gc_SerialNo.MinWidth = 60;
            this.gc_SerialNo.Name = "gc_SerialNo";
            this.gc_SerialNo.Visible = true;
            this.gc_SerialNo.VisibleIndex = 0;
            this.gc_SerialNo.Width = 60;
            // 
            // gc_PARAM_NAME
            // 
            this.gc_PARAM_NAME.Caption = "参数";
            this.gc_PARAM_NAME.FieldName = "PARAM_NAME";
            this.gc_PARAM_NAME.MinWidth = 76;
            this.gc_PARAM_NAME.Name = "gc_PARAM_NAME";
            this.gc_PARAM_NAME.Visible = true;
            this.gc_PARAM_NAME.VisibleIndex = 1;
            this.gc_PARAM_NAME.Width = 76;
            // 
            // gc_EDIT_TYPE
            // 
            this.gc_EDIT_TYPE.Caption = "参数控件类型";
            this.gc_EDIT_TYPE.ColumnEdit = this.LUE_EditType;
            this.gc_EDIT_TYPE.FieldName = "EDIT_TYPE";
            this.gc_EDIT_TYPE.MinWidth = 100;
            this.gc_EDIT_TYPE.Name = "gc_EDIT_TYPE";
            this.gc_EDIT_TYPE.Visible = true;
            this.gc_EDIT_TYPE.VisibleIndex = 2;
            this.gc_EDIT_TYPE.Width = 100;
            // 
            // LUE_EditType
            // 
            this.LUE_EditType.AutoHeight = false;
            this.LUE_EditType.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_CODE", "编号"),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_NAME", "项目")});
            this.LUE_EditType.Name = "LUE_EditType";
            this.LUE_EditType.NullText = "";
            // 
            // gc_CAPTION
            // 
            this.gc_CAPTION.Caption = "标题名称";
            this.gc_CAPTION.FieldName = "CAPTION";
            this.gc_CAPTION.MinWidth = 120;
            this.gc_CAPTION.Name = "gc_CAPTION";
            this.gc_CAPTION.Visible = true;
            this.gc_CAPTION.VisibleIndex = 3;
            this.gc_CAPTION.Width = 120;
            // 
            // gc_DISPLAY_MEMBER
            // 
            this.gc_DISPLAY_MEMBER.Caption = "显示列";
            this.gc_DISPLAY_MEMBER.FieldName = "DISPLAY_MEMBER";
            this.gc_DISPLAY_MEMBER.MinWidth = 100;
            this.gc_DISPLAY_MEMBER.Name = "gc_DISPLAY_MEMBER";
            this.gc_DISPLAY_MEMBER.Visible = true;
            this.gc_DISPLAY_MEMBER.VisibleIndex = 4;
            this.gc_DISPLAY_MEMBER.Width = 100;
            // 
            // gc_VALUE_MEMBER
            // 
            this.gc_VALUE_MEMBER.Caption = "数据列";
            this.gc_VALUE_MEMBER.FieldName = "VALUE_MEMBER";
            this.gc_VALUE_MEMBER.MinWidth = 100;
            this.gc_VALUE_MEMBER.Name = "gc_VALUE_MEMBER";
            this.gc_VALUE_MEMBER.Visible = true;
            this.gc_VALUE_MEMBER.VisibleIndex = 5;
            this.gc_VALUE_MEMBER.Width = 100;
            // 
            // gc_SOURCE_TYPE
            // 
            this.gc_SOURCE_TYPE.Caption = "数据源类型";
            this.gc_SOURCE_TYPE.ColumnEdit = this.LUE_SourceType;
            this.gc_SOURCE_TYPE.FieldName = "SOURCE_TYPE";
            this.gc_SOURCE_TYPE.MinWidth = 100;
            this.gc_SOURCE_TYPE.Name = "gc_SOURCE_TYPE";
            this.gc_SOURCE_TYPE.Visible = true;
            this.gc_SOURCE_TYPE.VisibleIndex = 6;
            this.gc_SOURCE_TYPE.Width = 100;
            // 
            // LUE_SourceType
            // 
            this.LUE_SourceType.AutoHeight = false;
            this.LUE_SourceType.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_CODE", "编码"),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_NAME", "项目")});
            this.LUE_SourceType.Name = "LUE_SourceType";
            this.LUE_SourceType.NullText = "";
            // 
            // gc_NEXT_PARAM_NAME
            // 
            this.gc_NEXT_PARAM_NAME.Caption = "联动参数";
            this.gc_NEXT_PARAM_NAME.FieldName = "NEXT_PARAM_NAME";
            this.gc_NEXT_PARAM_NAME.MinWidth = 120;
            this.gc_NEXT_PARAM_NAME.Name = "gc_NEXT_PARAM_NAME";
            this.gc_NEXT_PARAM_NAME.Visible = true;
            this.gc_NEXT_PARAM_NAME.VisibleIndex = 7;
            this.gc_NEXT_PARAM_NAME.Width = 120;
            // 
            // gc_DEFAULT_VALUE
            // 
            this.gc_DEFAULT_VALUE.Caption = "默认值";
            this.gc_DEFAULT_VALUE.FieldName = "DEFAULT_VALUE";
            this.gc_DEFAULT_VALUE.MinWidth = 80;
            this.gc_DEFAULT_VALUE.Name = "gc_DEFAULT_VALUE";
            this.gc_DEFAULT_VALUE.Visible = true;
            this.gc_DEFAULT_VALUE.VisibleIndex = 8;
            this.gc_DEFAULT_VALUE.Width = 80;
            // 
            // gc_CONTROL_WIDTH
            // 
            this.gc_CONTROL_WIDTH.Caption = "控件大小";
            this.gc_CONTROL_WIDTH.FieldName = "CONTROL_WIDTH";
            this.gc_CONTROL_WIDTH.MinWidth = 60;
            this.gc_CONTROL_WIDTH.Name = "gc_CONTROL_WIDTH";
            this.gc_CONTROL_WIDTH.Visible = true;
            this.gc_CONTROL_WIDTH.VisibleIndex = 9;
            this.gc_CONTROL_WIDTH.Width = 60;
            // 
            // gc_PARAM_ID
            // 
            this.gc_PARAM_ID.Caption = "参数ID";
            this.gc_PARAM_ID.FieldName = "PARAM_ID";
            this.gc_PARAM_ID.Name = "gc_PARAM_ID";
            // 
            // gc_STATUS
            // 
            this.gc_STATUS.AppearanceHeader.Options.UseTextOptions = true;
            this.gc_STATUS.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gc_STATUS.Caption = "启用";
            this.gc_STATUS.ColumnEdit = this.repositoryItemCheckEdit1;
            this.gc_STATUS.FieldName = "STATUS";
            this.gc_STATUS.MinWidth = 75;
            this.gc_STATUS.Name = "gc_STATUS";
            this.gc_STATUS.Visible = true;
            this.gc_STATUS.VisibleIndex = 11;
            // 
            // repositoryItemCheckEdit1
            // 
            this.repositoryItemCheckEdit1.AutoHeight = false;
            this.repositoryItemCheckEdit1.Name = "repositoryItemCheckEdit1";
            this.repositoryItemCheckEdit1.ValueChecked = "1";
            this.repositoryItemCheckEdit1.ValueUnchecked = "0";
            // 
            // gcDATE_FORMAT
            // 
            this.gcDATE_FORMAT.Caption = "日期格式";
            this.gcDATE_FORMAT.FieldName = "DATE_FORMAT";
            this.gcDATE_FORMAT.MinWidth = 100;
            this.gcDATE_FORMAT.Name = "gcDATE_FORMAT";
            this.gcDATE_FORMAT.Visible = true;
            this.gcDATE_FORMAT.VisibleIndex = 10;
            this.gcDATE_FORMAT.Width = 100;
            // 
            // barManager1
            // 
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar2});
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.btnAdd,
            this.btnEdit,
            this.btnDelete,
            this.btnSave,
            this.barLargeButtonItem1});
            this.barManager1.MainMenu = this.bar2;
            this.barManager1.MaxItemId = 5;
            // 
            // bar2
            // 
            this.bar2.BarName = "Main menu";
            this.bar2.DockCol = 0;
            this.bar2.DockRow = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar2.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnAdd, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnEdit, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnDelete, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnSave, "", true, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barLargeButtonItem1)});
            this.bar2.OptionsBar.DrawBorder = false;
            this.bar2.OptionsBar.DrawDragBorder = false;
            this.bar2.OptionsBar.MultiLine = true;
            this.bar2.OptionsBar.UseWholeRow = true;
            this.bar2.Text = "Main menu";
            // 
            // btnAdd
            // 
            this.btnAdd.Caption = "新增";
            this.btnAdd.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.btnAdd.Id = 0;
            this.btnAdd.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnAdd.ImageOptions.Image")));
            this.btnAdd.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnAdd.ImageOptions.LargeImage")));
            this.btnAdd.Name = "btnAdd";
            this.btnAdd.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnAdd_ItemClick);
            // 
            // btnEdit
            // 
            this.btnEdit.Caption = "编辑";
            this.btnEdit.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.btnEdit.Id = 1;
            this.btnEdit.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnEdit.ImageOptions.Image")));
            this.btnEdit.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnEdit.ImageOptions.LargeImage")));
            this.btnEdit.Name = "btnEdit";
            this.btnEdit.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnEdit_ItemClick);
            // 
            // btnDelete
            // 
            this.btnDelete.Caption = "删除";
            this.btnDelete.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.btnDelete.Id = 2;
            this.btnDelete.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnDelete.ImageOptions.Image")));
            this.btnDelete.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnDelete.ImageOptions.LargeImage")));
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnDelete_ItemClick);
            // 
            // btnSave
            // 
            this.btnSave.Caption = "保存";
            this.btnSave.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.btnSave.Id = 3;
            this.btnSave.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnSave.ImageOptions.Image")));
            this.btnSave.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnSave.ImageOptions.LargeImage")));
            this.btnSave.Name = "btnSave";
            this.btnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnSave_ItemClick);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Manager = this.barManager1;
            this.barDockControlTop.Size = new System.Drawing.Size(784, 40);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 561);
            this.barDockControlBottom.Manager = this.barManager1;
            this.barDockControlBottom.Size = new System.Drawing.Size(784, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 40);
            this.barDockControlLeft.Manager = this.barManager1;
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 521);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(784, 40);
            this.barDockControlRight.Manager = this.barManager1;
            this.barDockControlRight.Size = new System.Drawing.Size(0, 521);
            // 
            // barLargeButtonItem1
            // 
            this.barLargeButtonItem1.Caption = "关闭";
            this.barLargeButtonItem1.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.barLargeButtonItem1.Id = 4;
            this.barLargeButtonItem1.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barLargeButtonItem1.ImageOptions.Image")));
            this.barLargeButtonItem1.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barLargeButtonItem1.ImageOptions.LargeImage")));
            this.barLargeButtonItem1.Name = "barLargeButtonItem1";
            this.barLargeButtonItem1.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barLargeButtonItem1_ItemClick);
            // 
            // frmParamDict
            // 
            this.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(239)))));
            this.Appearance.Options.UseBackColor = true;
            this.ClientSize = new System.Drawing.Size(784, 561);
            this.Controls.Add(this.gridControl1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Name = "frmParamDict";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "参数维护";
            this.Load += new System.EventHandler(this.frmParamDict_Load);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.LUE_EditType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.LUE_SourceType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraBars.BarLargeButtonItem btnAdd;
        private DevExpress.XtraBars.BarLargeButtonItem btnEdit;
        private DevExpress.XtraBars.BarLargeButtonItem btnDelete;
        private DevExpress.XtraBars.BarLargeButtonItem btnSave;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraGrid.Columns.GridColumn gc_SerialNo;
        private DevExpress.XtraGrid.Columns.GridColumn gc_PARAM_NAME;
        private DevExpress.XtraGrid.Columns.GridColumn gc_EDIT_TYPE;
        private DevExpress.XtraGrid.Columns.GridColumn gc_CAPTION;
        private DevExpress.XtraGrid.Columns.GridColumn gc_DISPLAY_MEMBER;
        private DevExpress.XtraGrid.Columns.GridColumn gc_VALUE_MEMBER;
        private DevExpress.XtraGrid.Columns.GridColumn gc_SOURCE_TYPE;
        private DevExpress.XtraGrid.Columns.GridColumn gc_NEXT_PARAM_NAME;
        private DevExpress.XtraGrid.Columns.GridColumn gc_DEFAULT_VALUE;
        private DevExpress.XtraGrid.Columns.GridColumn gc_CONTROL_WIDTH;
        private DevExpress.XtraGrid.Columns.GridColumn gc_PARAM_ID;
        private DevExpress.XtraGrid.Columns.GridColumn gc_STATUS;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit repositoryItemCheckEdit1;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit LUE_EditType;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit LUE_SourceType;
        private DevExpress.XtraGrid.Columns.GridColumn gcDATE_FORMAT;
        private DevExpress.XtraBars.BarLargeButtonItem barLargeButtonItem1;
    }
}
