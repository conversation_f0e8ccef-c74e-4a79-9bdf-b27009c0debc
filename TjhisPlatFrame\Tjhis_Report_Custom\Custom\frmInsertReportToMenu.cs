﻿using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System;
using System.Data;
using System.Windows.Forms;
using Tjhis.Report.Custom.Srv;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmInsertReportToMenu : ParentForm
    {
        public string ReportName { get; set; }
        public string ReportID { get; set; }

        SrvInsertReportToMenu srv;
        DataSet dsMenuList;
        public frmInsertReportToMenu()
        {
            InitializeComponent();
        }

        private void frmInsertReportToMenu_Load(object sender, EventArgs e)
        {
            srv = new SrvInsertReportToMenu();
            GetReportMenu();
            DataTable DictTable = null;
            repMenuList.DataSource = DictTable = srv.CreateDict("质控报表-frmIndexReport;排班报表-frmScheduleReport;教学科研报表-frmStudyReport;护士长管理报表-frmDeptWorkReport");
            barMenuList.EditValue = "frmIndexReport";

            repLupMenuList.DataSource = DictTable;
        }

        void GetReportMenu()
        {
            dsMenuList = srv.GetMenusByReportID(ReportID);
            gridControl1.DataSource = dsMenuList?.Tables[0];
        }


        private void BarBtnOk_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void BarBtnCancel_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnRemoveMenu_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            gridView1.DeleteSelectedRows();

            gridView1.UpdateCurrentRow();

            srv.SaveDate(dsMenuList);
        }

        private void barBtnInsert_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            string menuType = barMenuList.EditValue?.ToString();
            int result = 0;
            if (!string.IsNullOrEmpty(menuType))
                result = srv.InsertToMenu(menuType, ReportName, ReportID);
            if (result > 0)
                GetReportMenu() ;
            else
                XtraMessageBox.Show("同一菜单内已存在相同项！", "提示");
        }
    }
}
