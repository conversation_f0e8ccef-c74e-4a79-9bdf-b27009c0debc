﻿using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Tjhis.Report.Custom.Common;
using Tjhis.Report.Custom.Srv;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmReportMove : ParentForm
    {

        srvRoleUserQuery srv;       
        DataTable dtUserApp;
      
        string appFilter;//人员过滤
        string displayFilter;//显示过滤
        public string usersArray { get; set; }

        public frmReportMove()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 加载事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void frmReportMove_Load(object sender, EventArgs e)
        {
            srv = new srvRoleUserQuery();
            InitData();
            SetSelectApp();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        void InitData()
        {
            dtUserApp = srv.GetUserApp(SystemParm.LoginUser.USER_NAME);
            dtUserApp.Columns["COL_SEL"].ReadOnly = false;
            gridControl1.DataSource = dtUserApp.DefaultView;
        }

        /// <summary>
        /// 设置已经选择过的应用程序
        /// </summary>
        void SetSelectApp()
        {
            if (string.IsNullOrEmpty(usersArray)) return;

            string[] users = usersArray.Split(',');
            List<DataRow> drUsers = dtUserApp.Select().ToList();           
            foreach (string user in users)
            {
                DataRow drUser = drUsers.Find(dr => dr["App_Code"].Equals(user));
                if (drUser != null)
                {
                    drUser["COL_SEL"] = "1";
                }
            }
        }

        /// <summary>
        /// 获取选中应用程序的字符串
        /// </summary>
        /// <returns></returns>
        string GetSelectApp()
        {
            usersArray = "";

            List<DataRow> drUsers = dtUserApp.Select("col_sel = '1'").ToList();
            drUsers.ForEach(dr => usersArray += dr["App_Code"] + ",");

            return usersArray;
        }


        /// <summary>
        /// 应用程序过滤
        /// </summary>
        /// <param name="str"></param>
        void TxtAppChanging(string str)
        {
            if (string.IsNullOrEmpty(str))
                appFilter = "";
            else
                appFilter = " and (App_Code like '%" + str + "%' or DESCRIPTION like '%" + str + "%')";
            SetFilter();
        }
        
        
        /// <summary>
        /// [查询]按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void simpleButton1_Click(object sender, EventArgs e)
        {
            TxtAppChanging(textEdit1.Text);
        }
       

        /// <summary>
        /// 确认并退出当前窗体
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void simpleButton2_Click(object sender, EventArgs e)
        {
            GetSelectApp();
            this.DialogResult = DialogResult.OK;
        }

        /// <summary>
        /// 取消并退出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void simpleButton3_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        /// <summary>
        /// 显示已选择的应用程序数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void cbDisplayChecks_CheckedChanged(object sender, EventArgs e)
        {
            if (cbDisplayChecks.Checked)
            {
                displayFilter = " and col_sel = '1'";
            }
            else
            {
                displayFilter = "";
            }
            SetFilter();
        }

        /// <summary>
        /// 设置过滤条件
        /// </summary>
        void SetFilter()
        {
            dtUserApp.DefaultView.RowFilter = " 1=1" + appFilter + displayFilter;
        }

        /// <summary>
        /// 回车事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void textEdit1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                TxtAppChanging(textEdit1.Text);
            }
        }
    }
}
