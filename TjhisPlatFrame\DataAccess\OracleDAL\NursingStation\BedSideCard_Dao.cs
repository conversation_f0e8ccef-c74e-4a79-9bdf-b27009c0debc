﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using Model;

namespace OracleDAL
{
    /// <summary>
    /// 床头卡类
    /// </summary>
    public class BedSideCardDao
    {
        /// <summary>
        /// 根据SQL语句获得一个DataSet
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public DataSet GetBedSideCardBySql(string warcodes,string otherWhere)
        {
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    StringBuilder sqlstr = new StringBuilder();
                    sqlstr.Append("SELECT NVL(BED_REC.BED_LABEL, to_char(BED_REC.BED_NO)) BED_LABEL,");
                    sqlstr.Append("(case");
                    sqlstr.Append(" when INSTR(BED_REC.bed_label, '_') > 0 then");
                    sqlstr.Append(" (select aa.bed_no");
                    sqlstr.Append(" from bed_rec aa");
                    sqlstr.Append(" where aa.ward_code = BED_REC.WARD_CODE");
                    sqlstr.Append(" and aa.bed_label =");
                    sqlstr.Append("substr(bed_rec.bed_label, 1, INSTR(bed_rec.bed_label, '_') - 1))");
                    sqlstr.Append(" else");
                    sqlstr.Append(" BED_REC.bed_no");
                    sqlstr.Append(" end) bed_no1,");
                    sqlstr.Append("nvl((select name || '的包床' from pat_master_index where bed_rec.patient_id = patient_id),'') bc,");
                    sqlstr.Append("BED_REC.WARD_CODE,");
                    sqlstr.Append("BED_REC.BED_STATUS,");
                    sqlstr.Append("BED_REC.BED_SEX_TYPE,");
                    sqlstr.Append("BED_REC.DEPT_CODE,");
                    sqlstr.Append("BED_REC.BED_APPROVED_TYPE,");
                    sqlstr.Append("BED_REC.ROOM_NO,");
                    sqlstr.Append("BED_REC.BED_CLASS,");
                    //PAT_MASTER_INDEX
                    #region pmi
                    sqlstr.Append("PAT_MASTER_INDEX.PATIENT_ID AS MPATIENT_ID,");
                    sqlstr.Append("PAT_MASTER_INDEX.INP_NO,");
                    sqlstr.Append("PAT_MASTER_INDEX.NAME,");
                    sqlstr.Append("PAT_MASTER_INDEX.NAME_PHONETIC,");
                    sqlstr.Append("PAT_MASTER_INDEX.SEX,");
                    sqlstr.Append("PAT_MASTER_INDEX.DATE_OF_BIRTH,");
                    sqlstr.Append("PAT_MASTER_INDEX.BIRTH_PLACE,");
                    sqlstr.Append("PAT_MASTER_INDEX.CITIZENSHIP,");
                    sqlstr.Append("PAT_MASTER_INDEX.NATION,");
                    sqlstr.Append("PAT_MASTER_INDEX.ID_NO,");
                    sqlstr.Append("PAT_MASTER_INDEX.IDENTITY,");
                    sqlstr.Append("PAT_MASTER_INDEX.CHARGE_TYPE CHARGE_TYPE_M,");
                    sqlstr.Append("PAT_MASTER_INDEX.UNIT_IN_CONTRACT,");
                    sqlstr.Append("PAT_MASTER_INDEX.MAILING_ADDRESS,");
                    sqlstr.Append("PAT_MASTER_INDEX.ZIP_CODE,");
                    sqlstr.Append("PAT_MASTER_INDEX.PHONE_NUMBER_HOME,");
                    sqlstr.Append("PAT_MASTER_INDEX.PHONE_NUMBER_BUSINESS,");
                    sqlstr.Append("PAT_MASTER_INDEX.NEXT_OF_KIN,");
                    sqlstr.Append("PAT_MASTER_INDEX.RELATIONSHIP,");
                    sqlstr.Append("PAT_MASTER_INDEX.NEXT_OF_KIN_ADDR,");
                    sqlstr.Append("PAT_MASTER_INDEX.NEXT_OF_KIN_ZIP_CODE,");
                    sqlstr.Append("PAT_MASTER_INDEX.NEXT_OF_KIN_PHONE,");
                    sqlstr.Append("PAT_MASTER_INDEX.LAST_VISIT_DATE,");
                    sqlstr.Append("PAT_MASTER_INDEX.VIP_INDICATOR,");
                    sqlstr.Append("PAT_MASTER_INDEX.CREATE_DATE,");
                    sqlstr.Append("PAT_MASTER_INDEX.OPERATOR,");
                    sqlstr.Append("PAT_MASTER_INDEX.SERVICE_AGENCY,");
                    sqlstr.Append("PAT_MASTER_INDEX.BUSINESS_ZIP_CODE,");
                    sqlstr.Append("PAT_MASTER_INDEX.PHOTO,");
                    sqlstr.Append("PAT_MASTER_INDEX.PATIENT_CLASS,");
                    sqlstr.Append("PAT_MASTER_INDEX.DEGREE,");
                    sqlstr.Append("PAT_MASTER_INDEX.RACE,");
                    sqlstr.Append("PAT_MASTER_INDEX.RELIGION,");
                    sqlstr.Append("PAT_MASTER_INDEX.MOTHER_LANGUAGE,");
                    sqlstr.Append("PAT_MASTER_INDEX.FOREIGN_LANGUAGE,");
                    sqlstr.Append("PAT_MASTER_INDEX.ID_TYPE,");
                    sqlstr.Append("PAT_MASTER_INDEX.VIP_NO,");
                    sqlstr.Append("PAT_MASTER_INDEX.E_NAME,");
                    sqlstr.Append("PAT_MASTER_INDEX.INSURANCE_NO,");
                    sqlstr.Append("PAT_MASTER_INDEX.INSURANCE_TYPE,");
                    sqlstr.Append("PAT_MASTER_INDEX.OCCUPATION,");
                    sqlstr.Append("PAT_MASTER_INDEX.NATIVE_PLACE,");
                    sqlstr.Append("PAT_MASTER_INDEX.MAILING_ADDRESS_CODE,");
                    sqlstr.Append("PAT_MASTER_INDEX.HEALTHY_CARD_NO,");
                    sqlstr.Append("PAT_MASTER_INDEX.MAILING_STREET_CODE,");
                    sqlstr.Append("PAT_MASTER_INDEX.ACCOUNT,");
                    sqlstr.Append("PAT_MASTER_INDEX.ALERGY,");
                    sqlstr.Append("PAT_MASTER_INDEX.MARITAL_STATUS,");
                    sqlstr.Append("PAT_MASTER_INDEX.NEXT_OF_SEX,");
                    sqlstr.Append("PAT_MASTER_INDEX.NEXT_OF_BATH,");
                    sqlstr.Append("PAT_MASTER_INDEX.NEXT_OF_ID,");
                    sqlstr.Append("PAT_MASTER_INDEX.UNIT_IN_FORCE,");
                    sqlstr.Append("PAT_MASTER_INDEX.NOW_ZIP_CODE,");
                    sqlstr.Append("PAT_MASTER_INDEX.NOW_ADDR,");
                    sqlstr.Append("PAT_MASTER_INDEX.YB_YBZH,");
                    sqlstr.Append("PAT_MASTER_INDEX.GF_GYZH,");
                    sqlstr.Append("PAT_MASTER_INDEX.GF_FLAG,");
                    sqlstr.Append("PAT_MASTER_INDEX.PATIENT_CLASS_NO,");
                    sqlstr.Append("PAT_MASTER_INDEX.VISIT_ID AS MVISIT_ID,");
                    sqlstr.Append("PAT_MASTER_INDEX.ITEM_NO,"); 
                    sqlstr.Append("PAT_MASTER_INDEX.PM_ID,");
                    sqlstr.Append("PAT_MASTER_INDEX.DISABLE,");
                    sqlstr.Append("PAT_MASTER_INDEX.FEE_CODE,");
                    sqlstr.Append("PAT_MASTER_INDEX.WORKUNIT,");
                    sqlstr.Append("PAT_MASTER_INDEX.IMAGE,");
                    #endregion
                    //PATS_IN_HOSPITAL 采用住院PATIENT_ID 和VISIT_ID
                    #region  pih
                    sqlstr.Append("PATS_IN_HOSPITAL.PATIENT_ID,");
                    sqlstr.Append("PATS_IN_HOSPITAL.VISIT_ID,");
                    sqlstr.Append("PATS_IN_HOSPITAL.WARD_CODE AS HWARD_CODE,");
                    sqlstr.Append("PATS_IN_HOSPITAL.DEPT_CODE AS HDEPT_CODE,");
                    sqlstr.Append("PATS_IN_HOSPITAL.BED_NO,");
                    sqlstr.Append("PATS_IN_HOSPITAL.ADMISSION_DATE_TIME,");
                    sqlstr.Append("PATS_IN_HOSPITAL.ADM_WARD_DATE_TIME,");
                    sqlstr.Append("decode(trunc(sysdate)- trunc(PATS_IN_HOSPITAL.ADM_WARD_DATE_TIME),0,1,0) NEW_PATIENT ,");
                    sqlstr.Append("PATS_IN_HOSPITAL.DIAGNOSIS,");
                    sqlstr.Append("PATS_IN_HOSPITAL.PATIENT_CONDITION,");
                    sqlstr.Append("PATS_IN_HOSPITAL.NURSING_CLASS,");
                    sqlstr.Append("PATS_IN_HOSPITAL.DOCTOR_IN_CHARGE,");
                    sqlstr.Append("PATS_IN_HOSPITAL.OPERATING_DATE,");
                    sqlstr.Append("PATS_IN_HOSPITAL.BILLING_DATE_TIME,");
                    sqlstr.Append("PATS_IN_HOSPITAL.PREPAYMENTS,");
                    sqlstr.Append("PATS_IN_HOSPITAL.TOTAL_COSTS,");
                    sqlstr.Append("PATS_IN_HOSPITAL.TOTAL_CHARGES,");
                    sqlstr.Append("PATS_IN_HOSPITAL.GUARANTOR,");
                    sqlstr.Append("PATS_IN_HOSPITAL.GUARANTOR_ORG,");
                    sqlstr.Append("PATS_IN_HOSPITAL.GUARANTOR_PHONE_NUM,");
                    sqlstr.Append("PATS_IN_HOSPITAL.BILL_CHECKED_DATE_TIME,");
                    sqlstr.Append("PATS_IN_HOSPITAL.SETTLED_INDICATOR,");
                    sqlstr.Append("PATS_IN_HOSPITAL.LEND_BED_NO,");
                    sqlstr.Append("PATS_IN_HOSPITAL.BED_DEPT_CODE,");
                    sqlstr.Append("PATS_IN_HOSPITAL.BED_WARD_CODE,");
                    sqlstr.Append("PATS_IN_HOSPITAL.DEPT_CODE_LEND,");
                    sqlstr.Append("PATS_IN_HOSPITAL.LEND_INDICATOR,");
                    sqlstr.Append("PATS_IN_HOSPITAL.IS_NEWBORN,");
                    sqlstr.Append("PATS_IN_HOSPITAL.DRUGALLERGY,");
                    #endregion
                    sqlstr.Append("Round(MONTHS_BETWEEN(SYSDATE, nvl(PAT_MASTER_INDEX.DATE_OF_BIRTH,SYSDATE))) age_months,");
                    sqlstr.Append("nvl(STAFF_DICT.NAME, ' ') DOCTOR_IN_CHARGE1,");
                    sqlstr.Append("nvl(PATS_IN_HOSPITAL.PREPAYMENTS - PATS_IN_HOSPITAL.TOTAL_CHARGES, 0) PREPAYMENT_BAL,");
                    sqlstr.Append(" pat_visit.CHARGE_TYPE,");
                    sqlstr.Append(" PAT_VISIT.YB_TCZF,"); //yhy 20190107
                    sqlstr.Append("bed_rec.bed_no,");
                    sqlstr.Append("'床费：' || (select nvl(price, 0)");
                    sqlstr.Append(" from bed_rec bb, current_price_list");
                    sqlstr.Append(" where bb.bed_class = current_price_list.item_code(+)");
                    sqlstr.Append(" and bed_rec.ward_code = bb.ward_code");
                    sqlstr.Append(" and bed_rec.bed_no = bb.bed_no) bed_price,");
                    
                    //sqlstr.Append("PATS_IN_HOSPITAL.is_newborn,");

                    //sqlstr.Append("CASE WHEN trunc( MONTHS_BETWEEN(SYSDATE, PAT_MASTER_INDEX.DATE_OF_BIRTH)  /12)  >5 THEN  trunc( MONTHS_BETWEEN(SYSDATE, PAT_MASTER_INDEX.DATE_OF_BIRTH )  / 12 )||'岁'");
                    //sqlstr.Append(" WHEN trunc( MONTHS_BETWEEN(SYSDATE, PAT_MASTER_INDEX.DATE_OF_BIRTH)  /12)  <5 AND trunc(MONTHS_BETWEEN(SYSDATE, PAT_MASTER_INDEX.DATE_OF_BIRTH)  /12) >=1  THEN  trunc(MONTHS_BETWEEN(SYSDATE, PAT_MASTER_INDEX.DATE_OF_BIRTH)  /12)||'岁'||trunc(mod( MONTHS_BETWEEN(SYSDATE, PAT_MASTER_INDEX.DATE_OF_BIRTH),12))||'月'");
                    //sqlstr.Append(" WHEN trunc( MONTHS_BETWEEN(SYSDATE, PAT_MASTER_INDEX.DATE_OF_BIRTH)  /12)=1 THEN   trunc(mod( MONTHS_BETWEEN(SYSDATE, PAT_MASTER_INDEX.DATE_OF_BIRTH),12))||'月'");
                    //sqlstr.Append(" WHEN trunc(mod( MONTHS_BETWEEN(SYSDATE, PAT_MASTER_INDEX.DATE_OF_BIRTH),12))<1 THEN floor(sysdate - PAT_MASTER_INDEX.DATE_OF_BIRTH)||'天'");
                    sqlstr.Append(" cpr.chineseage(PAT_MASTER_INDEX.DATE_OF_BIRTH,sysdate)  show_age,");
                    sqlstr.Append("(select count(*)");
                    sqlstr.Append(" from orders");
                    sqlstr.Append(" where order_status <= 1");
                    sqlstr.Append(" and patient_id = PATS_IN_HOSPITAL.PATIENT_ID");
                    sqlstr.Append(" and visit_id = PATS_IN_HOSPITAL.VISIT_ID) order_verify,");
                    sqlstr.Append(" COODINATOR_MASTER.STATUS,");
                    sqlstr.Append(" nvl((select count(inp_days)");
                    sqlstr.Append(" from COODINATOR_NURSE_WORK");
                    sqlstr.Append(" where COODINATOR_NURSE_WORK.patient_id =  COODINATOR_MASTER.patient_id");
                    sqlstr.Append(" and COODINATOR_NURSE_WORK.visit_id =  COODINATOR_MASTER.visit_id");
                    sqlstr.Append(" and COODINATOR_NURSE_WORK.INP_DAYS = trunc(sysdate, 'dd') - trunc(PATS_IN_HOSPITAL.admission_date_time, 'dd') + 1),");
                    sqlstr.Append(" 0) inp_days1,");
                    sqlstr.Append(" NURSING_CLASS_DICT.NURSING_CLASS_NAME,");
                    sqlstr.Append(" PATIENT_STATUS_DICT.PATIENT_STATUS_NAME,");
                    //sqlstr.Append("trunc(SYSDATE) - trunc(PATS_IN_HOSPITAL.ADMISSION_DATE_TIME) + 1 inpdays");
                    //住院天数 计算有问题？ 
                    //sqlstr.Append(" case when trunc(SYSDATE) - trunc(PATS_IN_HOSPITAL.ADMISSION_DATE_TIME) = 0 then ");
                    //sqlstr.Append(" trunc(SYSDATE) - trunc(PATS_IN_HOSPITAL.ADMISSION_DATE_TIME) + 1 ");
                    //sqlstr.Append(" when trunc(SYSDATE) - trunc(PATS_IN_HOSPITAL.ADMISSION_DATE_TIME) > 0 then ");
                    //sqlstr.Append(" trunc(SYSDATE) - trunc(PATS_IN_HOSPITAL.ADMISSION_DATE_TIME) end inpdays, ");
                    //住院天数 计算有问题？
                    sqlstr.Append("(case when round(sysdate - PATS_IN_HOSPITAL.ADMISSION_DATE_TIME) < 1 then 1 else round((sysdate - PATS_IN_HOSPITAL.ADMISSION_DATE_TIME)) end) as inpdays,");//--在院天数
                    sqlstr.Append(" pat_visit.INSUR_CHECK, ");
                    sqlstr.Append("NURSING_CLASS_DICT.NURSING_SHOW_COLOR ");
                    sqlstr.Append(" FROM BED_REC");
                    sqlstr.Append(" LEFT JOIN PATS_IN_HOSPITAL");
                    sqlstr.Append(" ON BED_REC.WARD_CODE = PATS_IN_HOSPITAL.WARD_CODE");
                    sqlstr.Append(" AND BED_REC.BED_NO = PATS_IN_HOSPITAL.BED_NO");
                    sqlstr.Append("  LEFT JOIN PAT_MASTER_INDEX");
                    sqlstr.Append("  ON PATS_IN_HOSPITAL.PATIENT_ID = PAT_MASTER_INDEX.PATIENT_ID");
                    sqlstr.Append("  LEFT JOIN COODINATOR_MASTER");
                    sqlstr.Append(" ON PATS_IN_HOSPITAL.PATIENT_ID = COODINATOR_MASTER.PATIENT_ID");
                    sqlstr.Append(" AND PATS_IN_HOSPITAL.VISIT_ID = COODINATOR_MASTER.VISIT_ID");
                    sqlstr.Append("  LEFT JOIN STAFF_DICT");
                    sqlstr.Append("  ON BED_REC.WARD_CODE = PATS_IN_HOSPITAL.WARD_CODE AND PATS_IN_HOSPITAL.DOCTOR_IN_CHARGE = STAFF_DICT.USER_NAME");
                    sqlstr.Append("  LEFT JOIN NURSING_CLASS_DICT");
                    sqlstr.Append("  ON BED_REC.WARD_CODE = PATS_IN_HOSPITAL.WARD_CODE AND PATS_IN_HOSPITAL.NURSING_CLASS = NURSING_CLASS_DICT.NURSING_CLASS_CODE");
                    sqlstr.Append("  LEFT JOIN PATIENT_STATUS_DICT");
                    sqlstr.Append("  ON BED_REC.WARD_CODE = PATS_IN_HOSPITAL.WARD_CODE AND PATS_IN_HOSPITAL.PATIENT_CONDITION = PATIENT_STATUS_DICT.PATIENT_STATUS_CODE");
                    sqlstr.Append("  LEFT JOIN pat_visit");
                    sqlstr.Append("  ON PATS_IN_HOSPITAL.PATIENT_ID=pat_visit.patient_id AND PATS_IN_HOSPITAL.visit_id=pat_visit.visit_id");

                    sqlstr.Append(" WHERE 1=1 ");
                    if (!string.IsNullOrEmpty(warcodes))
                    {
                        sqlstr.Append(" AND BED_REC.WARD_CODE in (" + warcodes + ")");
                    }
                    if (!string.IsNullOrEmpty(otherWhere))
                    {
                        sqlstr.Append(" AND " + otherWhere);
                    }
                    sqlstr.Append(" ORDER BY BED_REC.Sort_No,bed_rec.bed_no");
                    //sqlstr.Append(" ORDER BY bed_no1");
                    DataSet dataset = db.SelectDataSet(sqlstr.ToString());
                    db.CommitTransaction();
                    db.CloseDB();
                    return dataset;
                }
                else
                {
                    Utility.LogFile.WriteLogAuto("数据库连接失败！", "ServerPublicDao");
                    return null;
                }

            }
        }

        public System.Collections.ObjectModel.ObservableCollection<SEC_MENUS_DICT> GetBedSideRightMenus(string applicationcode, string username, string menugroup, string orderBy)
        {
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    StringBuilder strSql = new StringBuilder();
                    StringBuilder sqlBuff = new StringBuilder();
                    sqlBuff.Append("SELECT DISTINCT T1.* ");
                    sqlBuff.Append("FROM SEC_MENUS_DICT T1, ");
                    sqlBuff.Append("SEC_RIGHT_GROUP_VS_USERS T2, ");
                    sqlBuff.Append("SEC_RIGHT_GROUP_VS_MENUS T3 ");
                    sqlBuff.Append("WHERE T1.MENU_NAME = T3.MENU_NAME  ");
                    sqlBuff.Append("AND T1.APPLICATION_CODE = T3.APPLICATION_CODE  ");
                    sqlBuff.Append("AND T3.RIGHT_GROUP_CODE=T2.RIGHT_GROUP_CODE ");
                    sqlBuff.Append("AND T3.APPLICATION_CODE = T2.APPLICATION_CODE ");
                    sqlBuff.Append("AND T2.USER_CODE = '" + username + "' ");
                    sqlBuff.Append("AND T1.APPLICATION_CODE = '" + applicationcode + "' ");
                    //sqlBuff.Append("AND T1.SUPPER_MENU = 'PARENT'  ");
                    sqlBuff.Append("AND T1.MENU_VISIBLE = '1'  ");
                    if (!string.IsNullOrEmpty(menugroup))
                        sqlBuff.Append("AND T1.MENU_GROUP = '" + menugroup + "'  ");
                    sqlBuff.Append("ORDER BY " + orderBy +" DESC");

                    try
                    {
                        DataSet ds = db.SelectDataSet(sqlBuff.ToString());
                        db.CommitTransaction();
                        db.CloseDB();
                        if (ds != null && ds.Tables.Count > 0)
                        {
                            int cmdresult = ds.Tables[0].Rows.Count;

                            if (cmdresult > 0)
                            {
                                System.Collections.ObjectModel.ObservableCollection<SEC_MENUS_DICT> list = new System.Collections.ObjectModel.ObservableCollection<SEC_MENUS_DICT>();
                                foreach (DataRow dr in ds.Tables[0].Rows)
                                {
                                    SEC_MENUS_DICT model = new SEC_MENUS_DICT();
                                    model = CopyToModel(dr);
                                    list.Add(model);
                                }
                                return list;
                            }
                            else
                            {
                                return null;
                            }
                        }
                        else
                        {
                            return null;
                        }

                    }
                    catch (Exception ex)
                    {
                        return null;
                        //throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
                    }
                }
                else
                {
                    return null;
                }
            }
            
        }

        /// <summary>
        /// 融合电子病历后的右键菜单 梁吉  2016-07-22 
        /// </summary>
        /// <param name="applicationcode"></param>
        /// <param name="username"></param>
        /// <param name="menugroup"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        public System.Collections.ObjectModel.ObservableCollection<SEC_MENUS_DICT> GetBedSideRightMenus_EMR(string applicationcode, string username, string menugroup, string orderBy,string hisunitcode="")
        {
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    StringBuilder strSql = new StringBuilder();
                    StringBuilder sqlBuff = new StringBuilder();
                    sqlBuff.Append("SELECT DISTINCT T1.APP_CODE APPLICATION_CODE,");
                    sqlBuff.Append("T1.NODE_CODE MENU_NAME,");
                    sqlBuff.Append("'MAINWINDOW' FORM_MENU,");
                    sqlBuff.Append("T1.FORM_CONTROL,");
                    sqlBuff.Append("T1.SERIAL_NO,");
                    sqlBuff.Append("T1.NODE_TITLE MENU_TEXT,");
                    sqlBuff.Append("'' TOOL_TIPS,");
                    sqlBuff.Append("T1.PARENT_NODE_CODE SUPPER_MENU,");
                    sqlBuff.Append("T1.FILE_NAME OPEN_FILE_NAME,");
                    sqlBuff.Append("T1.FORM_ID OPEN_FORM,");
                    sqlBuff.Append("T1.WIN_OPEN_MODE OPEN_PARAM,");
                    sqlBuff.Append("T1.STATUS MENU_VISIBLE,   ");
                    sqlBuff.Append("T1.ICON_STYLE,");
                    sqlBuff.Append("T1.LARGE_ICON ,");
                    sqlBuff.Append("T1.ICON_FILE SMALL_ICON,");
                    sqlBuff.Append("T1.MENU_GROUP,");
                    sqlBuff.Append("T1.MEMO MENU_MEMOS    ");
                    sqlBuff.Append("FROM MR_APP_ENTRY T1 ");
                    sqlBuff.Append("LEFT JOIN MR_RIGHT_VS_UI_OBJECT T2 ");
                    sqlBuff.Append("ON T1.NODE_CODE = T2.UI_OBJECT_ID ");
                    sqlBuff.Append("LEFT JOIN MR_ROLE_RIGHT T3 ");
                    sqlBuff.Append("ON  T2.RIGHT_ID = T3.RIGHT_ID ");
                    sqlBuff.Append("LEFT JOIN MR_USER_ROLE T4 ");
                    sqlBuff.Append("ON T3.ROLE_CODE = T4.ROLE_CODE ");
                    sqlBuff.Append("LEFT JOIN STAFF_DICT T5 ");
                    sqlBuff.Append("ON UPPER(T5.USER_NAME) = UPPER(T4.DB_USER) ");
                    sqlBuff.Append("WHERE T5.USER_NAME='" + username + "' ");
                    sqlBuff.Append("AND T2.ENABLE = 1 ");
                    sqlBuff.Append("AND T1.STATUS = 1 ");
                    sqlBuff.Append("AND T1.FORM_CONTROL IS NOT NULL  ");
                    sqlBuff.Append("AND T1.APP_CODE= '" + applicationcode + "' ");
                    if (!string.IsNullOrEmpty(menugroup))
                        sqlBuff.Append("AND T1.MENU_GROUP = '" + menugroup + "'  ");
                    //if (!string.IsNullOrEmpty(hisunitcode))
                    //    sqlBuff.Append("AND T1.HIS_UNIT_CODE = '" + hisunitcode + "'  ");
                    sqlBuff.Append("ORDER BY " + orderBy + " DESC");

                    try
                    {
                        DataSet ds = db.SelectDataSet(sqlBuff.ToString());
                        db.CommitTransaction();
                        db.CloseDB();
                        if (ds != null && ds.Tables.Count > 0)
                        {
                            int cmdresult = ds.Tables[0].Rows.Count;

                            if (cmdresult > 0)
                            {
                                System.Collections.ObjectModel.ObservableCollection<SEC_MENUS_DICT> list = new System.Collections.ObjectModel.ObservableCollection<SEC_MENUS_DICT>();
                                foreach (DataRow dr in ds.Tables[0].Rows)
                                {
                                    SEC_MENUS_DICT model = new SEC_MENUS_DICT();
                                    model = CopyToModel(dr);
                                    list.Add(model);
                                }
                                return list;
                            }
                            else
                            {
                                return null;
                            }
                        }
                        else
                        {
                            return null;
                        }

                    }
                    catch (Exception ex)
                    {
                        return null;
                        //throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
                    }
                }
                else
                {
                    return null;
                }
            }

        }
        /// <summary>
        /// 
        /// </summary>
        protected Model.SEC_MENUS_DICT CopyToModel(DataRow dRow)
        {
            Model.SEC_MENUS_DICT model1 = new Model.SEC_MENUS_DICT();

            if (dRow["APPLICATION_CODE"] != null && dRow["APPLICATION_CODE"].ToString() != "")
            {
                model1.APPLICATION_CODE = dRow["APPLICATION_CODE"].ToString();
            }

            if (dRow["OPEN_PARAM"] != null && dRow["OPEN_PARAM"].ToString() != "")
            {
                model1.OPEN_PARAM = decimal.Parse(dRow["OPEN_PARAM"].ToString());
            }

            if (dRow["MENU_VISIBLE"] != null && dRow["MENU_VISIBLE"].ToString() != "")
            {
                model1.MENU_VISIBLE = dRow["MENU_VISIBLE"].ToString();
            }

            if (dRow["ICON_STYLE"] != null && dRow["ICON_STYLE"].ToString() != "")
            {
                model1.ICON_STYLE = dRow["ICON_STYLE"].ToString();
            }

            if (dRow["LARGE_ICON"] != null && dRow["LARGE_ICON"].ToString() != "")
            {
                model1.LARGE_ICON = dRow["LARGE_ICON"].ToString();
            }

            if (dRow["SMALL_ICON"] != null && dRow["SMALL_ICON"].ToString() != "")
            {
                model1.SMALL_ICON = dRow["SMALL_ICON"].ToString();
            }

            if (dRow["MENU_GROUP"] != null && dRow["MENU_GROUP"].ToString() != "")
            {
                model1.MENU_GROUP = dRow["MENU_GROUP"].ToString();
            }

            if (dRow["MENU_MEMOS"] != null && dRow["MENU_MEMOS"].ToString() != "")
            {
                model1.MENU_MEMOS = dRow["MENU_MEMOS"].ToString();
            }

            if (dRow["OPEN_FILE_NAME"] != null && dRow["OPEN_FILE_NAME"].ToString() != "")
            {
                model1.OPEN_FILE_NAME = dRow["OPEN_FILE_NAME"].ToString();
            }

            if (dRow["MENU_NAME"] != null && dRow["MENU_NAME"].ToString() != "")
            {
                model1.MENU_NAME = dRow["MENU_NAME"].ToString();
            }

            if (dRow["FORM_MENU"] != null && dRow["FORM_MENU"].ToString() != "")
            {
                model1.FORM_MENU = dRow["FORM_MENU"].ToString();
            }

            if (dRow["FORM_CONTROL"] != null && dRow["FORM_CONTROL"].ToString() != "")
            {
                model1.FORM_CONTROL = dRow["FORM_CONTROL"].ToString();
            }

            if (dRow["SERIAL_NO"] != null && dRow["SERIAL_NO"].ToString() != "")
            {
                model1.SERIAL_NO = decimal.Parse(dRow["SERIAL_NO"].ToString());
            }

            if (dRow["MENU_TEXT"] != null && dRow["MENU_TEXT"].ToString() != "")
            {
                model1.MENU_TEXT = dRow["MENU_TEXT"].ToString();
            }

            if (dRow["TOOL_TIPS"] != null && dRow["TOOL_TIPS"].ToString() != "")
            {
                model1.TOOL_TIPS = dRow["TOOL_TIPS"].ToString();
            }

            if (dRow["SUPPER_MENU"] != null && dRow["SUPPER_MENU"].ToString() != "")
            {
                model1.SUPPER_MENU = dRow["SUPPER_MENU"].ToString();
            }

            if (dRow["OPEN_FORM"] != null && dRow["OPEN_FORM"].ToString() != "")
            {
                model1.OPEN_FORM = dRow["OPEN_FORM"].ToString();
            }

            return model1;
        }
    }
}
