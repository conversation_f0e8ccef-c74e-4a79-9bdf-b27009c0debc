﻿using System;
using System.Data;
using System.ServiceModel;
namespace INMService
{
    /// <summary>
    /// 接口层SEC_RIGHT_GROUP_VS_MENUS
    /// </summary>
    [ServiceContract]
    public interface ISEC_RIGHT_GROUP_VS_MENUS
    {
        #region  成员方法
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        [OperationContract]
        bool Exists_SEC_RIGHT_GROUP_VS_MENUS(string APPLICATION_CODE, string RIGHT_GROUP_CODE, string MENU_NAME);
        /// <summary>
        /// 增加一条数据
        /// </summary>
        [OperationContract]
        bool Add_SEC_RIGHT_GROUP_VS_MENUS(Model.SEC_RIGHT_GROUP_VS_MENUS model);
        /// <summary>
        /// 更新一条数据
        /// </summary>
        [OperationContract]
        bool Update_SEC_RIGHT_GROUP_VS_MENUS(Model.SEC_RIGHT_GROUP_VS_MENUS model);
        /// <summary>
        /// 删除数据
        /// </summary>
        [OperationContract]
        bool Delete_SEC_RIGHT_GROUP_VS_MENUS(string APPLICATION_CODE, string RIGHT_GROUP_CODE, string MENU_NAME);
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        [OperationContract]
        Model.SEC_RIGHT_GROUP_VS_MENUS GetModel_SEC_RIGHT_GROUP_VS_MENUS(string APPLICATION_CODE, string RIGHT_GROUP_CODE, string MENU_NAME);
        /// <summary>
        /// 获得数据列表
        /// </summary>
        [OperationContract]
        DataSet GetList_All_SEC_RIGHT_GROUP_VS_MENUS(string strWhere);
        /// <summary>
        /// 获得前几行数据
        /// </summary>
        [OperationContract]
        DataSet GetList_SEC_RIGHT_GROUP_VS_MENUS(int startIndex, int endIndex, string strWhere, string filedOrder);
        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.SEC_RIGHT_GROUP_VS_MENUS> GetObservableCollection_All_SEC_RIGHT_GROUP_VS_MENUS(string strWhere);
        /// <summary>
        /// 获得ObservableCollection根据分页获得数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.SEC_RIGHT_GROUP_VS_MENUS> GetObservableCollection_SEC_RIGHT_GROUP_VS_MENUS(int startIndex, int endIndex, string strWhere, string filedOrder);
        #endregion  成员方法
    }
}