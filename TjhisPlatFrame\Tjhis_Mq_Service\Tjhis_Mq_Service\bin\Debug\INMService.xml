<?xml version="1.0"?>
<doc>
    <assembly>
        <name>INMService</name>
    </assembly>
    <members>
        <member name="T:INMService.IBILL_PATTERN_DETAIL">
            <summary>
            接口层BILL_PATTERN_DETAIL
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_DETAIL.Exists_BILL_PATTERN_DETAIL(System.String,System.Decimal,System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_DETAIL.Add_BILL_PATTERN_DETAIL(Model.BILL_PATTERN_DETAIL)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_DETAIL.Update_BILL_PATTERN_DETAIL(Model.BILL_PATTERN_DETAIL)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_DETAIL.Delete_BILL_PATTERN_DETAIL(System.String,System.Decimal,System.String)">
            <summary>
            删除数据
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_DETAIL.GetModel_BILL_PATTERN_DETAIL(System.String,System.Decimal,System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_DETAIL.GetList_All_BILL_PATTERN_DETAIL(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_DETAIL.GetList_BILL_PATTERN_DETAIL(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_DETAIL.GetObservableCollection_All_BILL_PATTERN_DETAIL(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_DETAIL.GetObservableCollection_BILL_PATTERN_DETAIL(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>
        </member>
        <member name="T:INMService.IBILL_PATTERN_MASTER">
            <summary>
            接口层BILL_PATTERN_MASTER
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_MASTER.Exists_BILL_PATTERN_MASTER(System.Decimal,System.String,System.String,System.String,System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_MASTER.Add_BILL_PATTERN_MASTER(Model.BILL_PATTERN_MASTER)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_MASTER.Update_BILL_PATTERN_MASTER(Model.BILL_PATTERN_MASTER)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_MASTER.Delete_BILL_PATTERN_MASTER(System.Decimal,System.String,System.String,System.String,System.String)">
            <summary>
            删除数据
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_MASTER.GetModel_BILL_PATTERN_MASTER(System.Decimal,System.String,System.String,System.String,System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_MASTER.GetList_All_BILL_PATTERN_MASTER(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_MASTER.GetList_BILL_PATTERN_MASTER(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_MASTER.GetObservableCollection_All_BILL_PATTERN_MASTER(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:INMService.IBILL_PATTERN_MASTER.GetObservableCollection_BILL_PATTERN_MASTER(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>
        </member>
        <member name="T:INMService.IMAIN_MENU">
            <summary>
            接口层MAIN_MENU
            </summary>
        </member>
        <member name="M:INMService.IMAIN_MENU.Exists_MAIN_MENU(System.String,System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:INMService.IMAIN_MENU.Add_MAIN_MENU(Model.MAIN_MENU)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:INMService.IMAIN_MENU.Update_MAIN_MENU(Model.MAIN_MENU)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:INMService.IMAIN_MENU.Delete_MAIN_MENU(System.String,System.String)">
            <summary>
            删除数据
            </summary>
        </member>
        <member name="M:INMService.IMAIN_MENU.GetModel_MAIN_MENU(System.String,System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:INMService.IMAIN_MENU.GetList_All_MAIN_MENU(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:INMService.IMAIN_MENU.GetList_MAIN_MENU(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>
        </member>
        <member name="M:INMService.IMAIN_MENU.GetObservableCollection_All_MAIN_MENU(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:INMService.IMAIN_MENU.GetObservableCollection_MAIN_MENU(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>
        </member>
        <member name="T:INMService.IMODELS">
            <summary>
            接口层MODELS
            </summary>
        </member>
        <member name="M:INMService.IMODELS.Exists_MODELS(System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:INMService.IMODELS.Add_MODELS(Model.MODELS)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:INMService.IMODELS.Update_MODELS(Model.MODELS)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:INMService.IMODELS.Delete_MODELS(System.String)">
            <summary>
            删除数据
            </summary>
        </member>
        <member name="M:INMService.IMODELS.GetModel_MODELS(System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:INMService.IMODELS.GetList_All_MODELS(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:INMService.IMODELS.GetList_MODELS(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>
        </member>
        <member name="M:INMService.IMODELS.GetObservableCollection_All_MODELS(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:INMService.IMODELS.GetObservableCollection_MODELS(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>
        </member>
        <member name="T:INMService.IMODEL_GROUP">
            <summary>
            接口层MODEL_GROUP
            </summary>
        </member>
        <member name="M:INMService.IMODEL_GROUP.Exists_MODEL_GROUP(System.String,System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:INMService.IMODEL_GROUP.Add_MODEL_GROUP(Model.MODEL_GROUP)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:INMService.IMODEL_GROUP.Update_MODEL_GROUP(Model.MODEL_GROUP)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:INMService.IMODEL_GROUP.Delete_MODEL_GROUP(System.String,System.String)">
            <summary>
            删除数据
            </summary>
        </member>
        <member name="M:INMService.IMODEL_GROUP.GetModel_MODEL_GROUP(System.String,System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:INMService.IMODEL_GROUP.GetList_All_MODEL_GROUP(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:INMService.IMODEL_GROUP.GetList_MODEL_GROUP(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>
        </member>
        <member name="M:INMService.IMODEL_GROUP.GetObservableCollection_All_MODEL_GROUP(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:INMService.IMODEL_GROUP.GetObservableCollection_MODEL_GROUP(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>
        </member>
        <member name="T:INMService.IBJCA_INTERFACE">
            <summary>
            接口层BJCA_INTERFACE
            </summary>
        </member>
        <member name="M:INMService.IBJCA_INTERFACE.Exists_BJCA_INTERFACE(System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:INMService.IBJCA_INTERFACE.Add_BJCA_INTERFACE(Model.BJCA_INTERFACE)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:INMService.IBJCA_INTERFACE.Update_BJCA_INTERFACE(Model.BJCA_INTERFACE)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:INMService.IBJCA_INTERFACE.Delete_BJCA_INTERFACE(System.String,System.String)">
            <summary>
            删除数据
            </summary>
        </member>
        <member name="M:INMService.IBJCA_INTERFACE.GetModel_BJCA_INTERFACE(System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:INMService.IBJCA_INTERFACE.GetList_All_BJCA_INTERFACE(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:INMService.IBJCA_INTERFACE.GetList_BJCA_INTERFACE(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>
        </member>
        <member name="M:INMService.IBJCA_INTERFACE.GetObservableCollection_All_BJCA_INTERFACE(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:INMService.IBJCA_INTERFACE.GetObservableCollection_BJCA_INTERFACE(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>
        </member>
        <member name="T:INMService.ISEC_MENUS_DICT">
            <summary>
            接口层SEC_MENUS_DICT
            </summary>
        </member>
        <member name="M:INMService.ISEC_MENUS_DICT.Exists_SEC_MENUS_DICT(System.String,System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:INMService.ISEC_MENUS_DICT.Add_SEC_MENUS_DICT(Model.SEC_MENUS_DICT)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:INMService.ISEC_MENUS_DICT.Update_SEC_MENUS_DICT(Model.SEC_MENUS_DICT)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:INMService.ISEC_MENUS_DICT.Delete_SEC_MENUS_DICT(System.String,System.String)">
            <summary>
            删除数据
            </summary>
        </member>
        <member name="M:INMService.ISEC_MENUS_DICT.GetModel_SEC_MENUS_DICT(System.String,System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:INMService.ISEC_MENUS_DICT.GetList_All_SEC_MENUS_DICT(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:INMService.ISEC_MENUS_DICT.GetList_SEC_MENUS_DICT(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>
        </member>
        <member name="M:INMService.ISEC_MENUS_DICT.GetObservableCollection_All_SEC_MENUS_DICT(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:INMService.ISEC_MENUS_DICT.GetObservableCollection_SEC_MENUS_DICT(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>
        </member>
        <member name="T:INMService.ISEC_RIGHT_GROUP">
            <summary>
            接口层SEC_RIGHT_GROUP
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP.Exists_SEC_RIGHT_GROUP(System.String,System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP.Add_SEC_RIGHT_GROUP(Model.SEC_RIGHT_GROUP)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP.Update_SEC_RIGHT_GROUP(Model.SEC_RIGHT_GROUP)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP.Delete_SEC_RIGHT_GROUP(System.String,System.String)">
            <summary>
            删除数据
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP.GetModel_SEC_RIGHT_GROUP(System.String,System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP.GetList_All_SEC_RIGHT_GROUP(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP.GetList_SEC_RIGHT_GROUP(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP.GetObservableCollection_All_SEC_RIGHT_GROUP(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP.GetObservableCollection_SEC_RIGHT_GROUP(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>
        </member>
        <member name="T:INMService.ISEC_RIGHT_GROUP_VS_MENUS">
            <summary>
            接口层SEC_RIGHT_GROUP_VS_MENUS
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_MENUS.Exists_SEC_RIGHT_GROUP_VS_MENUS(System.String,System.String,System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_MENUS.Add_SEC_RIGHT_GROUP_VS_MENUS(Model.SEC_RIGHT_GROUP_VS_MENUS)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_MENUS.Update_SEC_RIGHT_GROUP_VS_MENUS(Model.SEC_RIGHT_GROUP_VS_MENUS)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_MENUS.Delete_SEC_RIGHT_GROUP_VS_MENUS(System.String,System.String,System.String)">
            <summary>
            删除数据
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_MENUS.GetModel_SEC_RIGHT_GROUP_VS_MENUS(System.String,System.String,System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_MENUS.GetList_All_SEC_RIGHT_GROUP_VS_MENUS(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_MENUS.GetList_SEC_RIGHT_GROUP_VS_MENUS(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_MENUS.GetObservableCollection_All_SEC_RIGHT_GROUP_VS_MENUS(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_MENUS.GetObservableCollection_SEC_RIGHT_GROUP_VS_MENUS(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>
        </member>
        <member name="T:INMService.ISEC_RIGHT_GROUP_VS_USERS">
            <summary>
            接口层SEC_RIGHT_GROUP_VS_USERS
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_USERS.Exists_SEC_RIGHT_GROUP_VS_USERS(System.String,System.String,System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_USERS.Add_SEC_RIGHT_GROUP_VS_USERS(Model.SEC_RIGHT_GROUP_VS_USERS)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_USERS.Update_SEC_RIGHT_GROUP_VS_USERS(Model.SEC_RIGHT_GROUP_VS_USERS)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_USERS.Delete_SEC_RIGHT_GROUP_VS_USERS(System.String,System.String,System.String)">
            <summary>
            删除数据
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_USERS.GetModel_SEC_RIGHT_GROUP_VS_USERS(System.String,System.String,System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_USERS.GetList_All_SEC_RIGHT_GROUP_VS_USERS(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_USERS.GetList_SEC_RIGHT_GROUP_VS_USERS(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_USERS.GetObservableCollection_All_SEC_RIGHT_GROUP_VS_USERS(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_USERS.GetObservableCollection_SEC_RIGHT_GROUP_VS_USERS(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_USERS.GetUnSelectedGroups_SEC_RIGHT_GROUP_VS_USERS(System.String)">
            <summary>
            获得非本用户角色组
            </summary>
        </member>
        <member name="M:INMService.ISEC_RIGHT_GROUP_VS_USERS.GetSelectedGroups_SEC_RIGHT_GROUP_VS_USERS(System.String)">
            <summary>
            获得本用户角色组
            </summary>
        </member>
        <member name="T:INMService.ILNCA_INTERFACE">
            <summary>
            接口层LNCA_INTERFACE
            </summary>
        </member>
        <member name="M:INMService.ILNCA_INTERFACE.Exists_LNCA_INTERFACE(System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:INMService.ILNCA_INTERFACE.Add_LNCA_INTERFACE(Model.LNCA_INTERFACE)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:INMService.ILNCA_INTERFACE.Update_LNCA_INTERFACE(Model.LNCA_INTERFACE)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:INMService.ILNCA_INTERFACE.UpdateByHisID_LNCA_INTERFACE(Model.LNCA_INTERFACE)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:INMService.ILNCA_INTERFACE.Delete_LNCA_INTERFACE(System.String)">
            <summary>
            删除数据
            </summary>
        </member>
        <member name="M:INMService.ILNCA_INTERFACE.GetModel_LNCA_INTERFACE(System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:INMService.ILNCA_INTERFACE.GetList_All_LNCA_INTERFACE(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:INMService.ILNCA_INTERFACE.GetList_LNCA_INTERFACE(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>
        </member>
        <member name="M:INMService.ILNCA_INTERFACE.GetObservableCollection_All_LNCA_INTERFACE(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:INMService.ILNCA_INTERFACE.GetObservableCollection_LNCA_INTERFACE(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>
        </member>
        <member name="T:INMService.IBED_REC">
            <summary>
            接口层BED_REC
            </summary>
        </member>
        <member name="M:INMService.IBED_REC.Exists_BED_REC(System.String,System.Decimal)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:INMService.IBED_REC.Add_BED_REC(Model.BED_REC)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:INMService.IBED_REC.Update_BED_REC(Model.BED_REC)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:INMService.IBED_REC.Delete_BED_REC(System.String,System.Decimal)">
            <summary>
            删除数据
            </summary>
        </member>
        <member name="M:INMService.IBED_REC.GetModel_BED_REC(System.String,System.Decimal)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:INMService.IBED_REC.GetList_All_BED_REC(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:INMService.IBED_REC.GetList_BED_REC(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>
        </member>
        <member name="M:INMService.IBED_REC.GetObservableCollection_All_BED_REC(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:INMService.IBED_REC.GetObservableCollection_BED_REC(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>
        </member>
        <member name="M:INMService.IServerPublic.GetSysDate">
            <summary>
            取得服务器的时间
            </summary>
        </member>
        <member name="M:INMService.IServerPublic.GetList(System.String)">
            <summary>
             获得诊断别名数据列表
            </summary>
        </member>
        <member name="M:INMService.IServerPublic.GetSingleValue(System.String)">
            <summary>
            获得返回值
            </summary>
        </member>
        <member name="M:INMService.IServerPublic.SaveTablesData(System.Collections.ArrayList)">
            <summary>
            更新多个DataSet
            </summary>
        </member>
        <member name="M:INMService.IServerPublic.GetTableData(System.String,System.String)">
            <summary>
            获取表数据
            </summary>
        </member>
        <member name="M:INMService.IServerPublic.GetDataBySql(System.String)">
            <summary>
            根据SQL语句获得一个DataSet
            </summary>
        </member>
        <member name="M:INMService.IServerPublic.GetProcedureDataSet(System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.String,System.String)">
            <summary>
            获取存储过程数据集
            </summary>
        </member>
        <member name="M:INMService.IServerPublic.GetPubicProcedureDs(System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.String@,System.String@,System.Data.DataTable@)">
            <summary>
            获取存储过程数据集
            </summary>
        </member>
        <member name="M:INMService.IServerPublic.SaveDataSet(System.Data.DataSet)">
            <summary>
            保存数据集
            </summary>
            <param name="dataset"></param>
            <returns>-1 数据库异常，0 保存失败，>0 成功</returns>
        </member>
        <member name="M:INMService.IServerPublic.SaveTable(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            执行多条sql语句，事务提交
            </summary>
            <param name="list">语句列表</param>
            <returns></returns>
        </member>
        <member name="M:INMService.IServerPublic.GetDataTable_Para(System.String,System.Collections.Generic.List{System.String},System.Collections.ArrayList)">
            <summary>
            参数方式查询sql语句
            </summary>
            <param name="sql">查询sql语句</param>
            <param name="ParaList">参数名称列表</param>
            <param name="ValueList">参数值列表</param>
            <returns></returns>
        </member>
        <member name="T:INMService.ISTAFF_DICT">
            <summary>
            接口层STAFF_DICT
            </summary>
        </member>
        <member name="M:INMService.ISTAFF_DICT.Exists_STAFF_DICT(System.String)">
            <summary>
            是否存在该记录
            </summary>
        </member>
        <member name="M:INMService.ISTAFF_DICT.Add_STAFF_DICT(Model.STAFF_DICT)">
            <summary>
            增加一条数据
            </summary>
        </member>
        <member name="M:INMService.ISTAFF_DICT.Update_STAFF_DICT(Model.STAFF_DICT)">
            <summary>
            更新一条数据
            </summary>
        </member>
        <member name="M:INMService.ISTAFF_DICT.Delete_STAFF_DICT(System.String)">
            <summary>
            删除数据
            </summary>
        </member>
        <member name="M:INMService.ISTAFF_DICT.GetModel_STAFF_DICT(System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:INMService.ISTAFF_DICT.GetList_All_STAFF_DICT(System.String)">
            <summary>
            获得数据列表
            </summary>
        </member>
        <member name="M:INMService.ISTAFF_DICT.GetList_STAFF_DICT(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得前几行数据
            </summary>
        </member>
        <member name="M:INMService.ISTAFF_DICT.GetObservableCollection_All_STAFF_DICT(System.String)">
            <summary>
            获得ObservableCollection数据列表
            </summary>
        </member>
        <member name="M:INMService.ISTAFF_DICT.GetObservableCollection_STAFF_DICT(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            获得ObservableCollection根据分页获得数据列表
            </summary>
        </member>
        <member name="M:INMService.ISTAFF_DICT.LoginValidate_STAFF_DICT(System.String,System.String)">
            <summary>
            登录用户验证
            </summary>
            <param name="USER_NAME">用户名</param>
            <param name="PASSWORD">密码</param>
            <returns>返回值：-1-用户名错误，0-密码错误 1-正常登录，2-不可用账号</returns>
        </member>
        <member name="M:INMService.ISTAFF_DICT.GetModelByUserName_STAFF_DICT(System.String)">
            <summary>
            得到一个对象实体
            </summary>
        </member>
        <member name="M:INMService.IBedExchange.ExchangeBed_BedExchange(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            换床位，如果床位都有人，则交换，否则换到空床
            </summary>
            <param name="bedNoFrom">需要换床的床号</param>
            <param name="bedStatusFrom">需要换床的床状态</param>
            <param name="bedLabelFrom">需要换床的床标号</param>
            <param name="patient_IDFrom">需要换床的患者</param>
            <param name="bedNoTo">目标床号</param>
            <param name="bedStatusTo">目标床状态</param>
            <param name="bedLabelTo">目标床标号</param>
            <param name="patient_IDTo">目标床患者</param>
            <param name="wardCode">所在护理单元</param>
            <param name="db">数据源</param>
            <returns>
            -9 打开数据库连接失败
            -1 数据库异常
            0- 返回成功
            1-换床者床是空床更新换床者床位状态失败
            2-换床者床是空床更新被换床者床位状态失败
            3-被换床者床是空床更新换床者床位状态失败
            4-被换床者床是空床更新被换床者床位状态失败
            5-换床者床已有患者更新患者换到被换床者床位失败
            6-被换床者床已有患者更新患者换到换床者床位失败
            7-说明换床者是母亲，有婴儿，更新婴儿床标号到被换床者失败
            8-说明被换床者是母亲，有婴儿，更新婴儿床标号到换床者失败
            </returns>
        </member>
        <member name="M:INMService.IBedSideCard.GetBedSideCardBySql(System.String,System.String)">
            <summary>
            根据护理单元和其他条件查询床头卡片信息
            </summary>
            <param name="warcodes">护理单元编码列表，逗号间隔</param>
            <param name="otherWhere">其他where条件</param>
            <returns></returns>
        </member>
        <member name="M:INMService.IBedSideCard.GetBedSideRightMenus(System.String,System.String,System.String,System.String)">
            <summary>
            获取右键权限菜单
            </summary>
            <param name="applicationcode">应用代码</param>
            <param name="username">用户名</param>
            <param name="menugroup">菜单组</param>
            <param name="orderBy">排序字段</param>
            <returns></returns>
        </member>
        <member name="M:INMService.IRoleMantain.DeleteMenus(System.String,System.String)">
            <summary>
            删除组菜单
            </summary>
            <param name="APPLICATION_CODE"></param>
            <param name="RIGHT_GROUP_CODE"></param>
            <returns></returns>
        </member>
    </members>
</doc>
