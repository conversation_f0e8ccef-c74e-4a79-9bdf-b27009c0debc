﻿using System;
using System.Data;
using System.ServiceModel;
namespace INMService
{
    /// <summary>
    /// 接口层SEC_MENUS_DICT
    /// </summary>
    [ServiceContract]
    public interface ISEC_MENUS_DICT
    {
        #region  成员方法
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        [OperationContract]
        bool Exists_SEC_MENUS_DICT(string APPLICATION_CODE, string MENU_NAME);
        /// <summary>
        /// 增加一条数据
        /// </summary>
        [OperationContract]
        bool Add_SEC_MENUS_DICT(Model.SEC_MENUS_DICT model);
        /// <summary>
        /// 更新一条数据
        /// </summary>
        [OperationContract]
        bool Update_SEC_MENUS_DICT(Model.SEC_MENUS_DICT model);
        /// <summary>
        /// 删除数据
        /// </summary>
        [OperationContract]
        bool Delete_SEC_MENUS_DICT(string APPLICATION_CODE, string MENU_NAME);
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        [OperationContract]
        Model.SEC_MENUS_DICT GetModel_SEC_MENUS_DICT(string APPLICATION_CODE, string MENU_NAME);
        /// <summary>
        /// 获得数据列表
        /// </summary>
        [OperationContract]
        DataSet GetList_All_SEC_MENUS_DICT(string strWhere);
        /// <summary>
        /// 获得前几行数据
        /// </summary>
        [OperationContract]
        DataSet GetList_SEC_MENUS_DICT(int startIndex, int endIndex, string strWhere, string filedOrder);
        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.SEC_MENUS_DICT> GetObservableCollection_All_SEC_MENUS_DICT(string strWhere);
        /// <summary>
        /// 获得ObservableCollection根据分页获得数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.SEC_MENUS_DICT> GetObservableCollection_SEC_MENUS_DICT(int startIndex, int endIndex, string strWhere, string filedOrder);
        #endregion  成员方法
    }
}