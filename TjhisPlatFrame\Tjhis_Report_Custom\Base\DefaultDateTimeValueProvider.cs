﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Tjhis.Report.Custom.Base
{
    /// <summary>
    /// 默认时间处理器
    /// </summary>
    internal class DefaultDateTimeValueProvider
    {
        /// <summary>
        /// 时间格式根据默认字符串值获取默认时间
        /// </summary>
        /// <param name="defaultValue">默认字符串 
        /// <para>1D 表示当前日期加1日，2D表示当前时间加2日，  -1D表示当前时间减1日，-2D表示当前时间减2日，以此类推；
        /// 可以表示时间的占位符分别为：Y（年）、M（月）、D（日）、H（时）、F(分）、S（秒），不区分大小写，不写占位符默认为D（天）</para>
        /// <para>或：指定日期格式，yyyy-mm-dd；
        ///     根据设置年月日字符串，设置年月日 如设置yyyy-mm-dd，使用当前时间替换设置的格式，不区分大小写；
        ///     如设置 yyyy-mm-01 则表示设置当月的1日；
        ///     如设置 yyyy-01-01 则表示设置当年的1日</para>
        /// <para>可选填分隔符“空格” ，分隔符后的数据为指定的默认时分秒，格式为HH:mm:ss格式，如00:00:00、23:59:59等</para>
        /// <para>
        /// 举例： 
        ///     "3D": 表示当前时间加上三天的时间，不指定默认的时分秒；
        ///     "00:00:00": 表示时间为当天的0点0分0秒，不指定默认的年月日(不指定年月日默认为当天，或写成 00:00:00或 00:00:00)；
        ///     "-1D 23:59:59": 表示前一天的23点59分59秒；
        ///     "YYYY-MM-01 HH:MM:01" :表示当前年月的一日，当前小时当前分钟的第一秒；</para>
        /// </param>
        /// <returns>true/false 转换是否成功； DateTime类型为转换后字符串</returns>
        public Tuple<bool, DateTime> GetDefaultDateTime(string defaultValue)
        {
            try
            {
                if (defaultValue.Trim().Length == 0)//没有设置默认值，返回当前时间
                {
                    return new Tuple<bool, DateTime>(true, DateTime.Now);
                }

                string[] strArrary = defaultValue.Split(' ');//拆分字符串
                DateTime dtDate = DateTime.Now; //用于记录年月日
                DateTime dtTime = DateTime.Now;//用于记录时分秒

                //=========有空格分隔，空格前的内容为加减日期，后面的为时分秒设置//=========
                if (strArrary.Length > 1)
                {
                    string sDateValue = strArrary[0].Trim();//日期加减值或设置年月日
                    if (sDateValue.ToCharArray().Count(x => x == '-' ) > 1  //排除一个“-”的减号的
                        || sDateValue.Contains(@"/"))//如果包含-字符则表示设置日期 如yyyy-mm-dd 或 yyyy/mm/dd
                    {
                        //根据格式字符串转换为日期类型
                        if (!DateTime.TryParse(GetDateStrByFormatSetDate(sDateValue), out dtDate))//转换日期类型失败
                            return new Tuple<bool, DateTime>(false, DateTime.Now);
                    }
                    else//否则为加减年月日
                    {
                        dtDate = GetDataTimeByFormatAddOrSub(sDateValue).Date;
                    }

                    string sTimeValue = strArrary[1].Trim();//时分秒值                    
                    if (!DateTime.TryParse(GetTimeStrByFormatSetDate(sTimeValue), out dtTime))
                        return new Tuple<bool, DateTime>(false, DateTime.Now);

                    return new Tuple<bool, DateTime>(true, new DateTime(dtDate.Year, dtDate.Month, dtDate.Day, dtTime.Hour, dtTime.Minute, dtTime.Second));
                }

                //=========无空格分隔，要么是加减日期，要么是设置年月日，要么是设置时分秒=========
                else if (strArrary.Length == 1)
                {
                    string sDateValue = strArrary[0].Trim();
                    //判断是加减日期还是设置时分秒
                    if (sDateValue.Contains("-"))//如果是设置年月日
                    {
                        //根据格式字符串转换为日期类型
                        if (!DateTime.TryParse(GetDateStrByFormatSetDate(sDateValue), out dtDate))//转换日期类型失败
                            return new Tuple<bool, DateTime>(false, DateTime.Now);
                    }
                    else if (sDateValue.Contains(":"))//如果是设置时分秒
                    {
                        string sTimeValue = sDateValue;
                        if (!DateTime.TryParse(GetTimeStrByFormatSetDate(sTimeValue), out dtTime))
                            return new Tuple<bool, DateTime>(false, DateTime.Now);
                    }
                    else //加减年月日
                    {
                        dtDate = GetDataTimeByFormatAddOrSub(sDateValue).Date;
                    }
                    //string[] strAry = new string[] { "Y", "M", "D", "H", "F", "S" };                    
                    //if (strAry.Any(x => sDateValue.ToUpper().EndsWith(x)))//以"Y", "M", "D", "H", "F", "S"结尾的为只加减日期
                    //{
                    //    dtDate = GetDataTimeByFormatAddOrSub(sDateValue);
                    //    return new Tuple<bool, DateTime>(true, dtDate);
                    //}
                    //else//不以"Y", "M", "D", "H", "F", "S"结尾的为只设置时分秒 
                    //{
                    //    if (DateTime.TryParse(sDateValue, out DateTime dtTime))
                    //    {
                    //        dtDate = new DateTime(dtDate.Year, dtDate.Month, dtDate.Day, dtTime.Hour, dtTime.Minute, dtTime.Second);//设置年月日时分秒
                    //        return new Tuple<bool, DateTime>(true, dtDate);
                    //    }
                    //    else
                    //    {
                    //        return new Tuple<bool, DateTime>(false, dtDate);
                    //    }
                    //}
                    return new Tuple<bool, DateTime>(true, new DateTime(dtDate.Year, dtDate.Month, dtDate.Day, dtTime.Hour, dtTime.Minute, dtTime.Second));
                }

                //=========其他情况则返回当前时间=========
                else
                {
                    return new Tuple<bool, DateTime>(true, DateTime.Now);
                }
            }
            catch (Exception ex)
            {
                //异常返回失败
                return new Tuple<bool, DateTime>(false, DateTime.Now);
            }
        }



        /// <summary>
        /// 根据指定的格式字符串加/减 年/月/日/时/分/秒
        /// </summary>
        /// <param name="strValue">格式字符串</param>
        /// <returns>转换后的时间值</returns>
        private DateTime GetDataTimeByFormatAddOrSub(string strValue)
        {
            DateTime dateTime = new DateTime();
            string strForm = strValue.Last().ToString();//取最后一位如：Y/M/D....
            string strDigit = strValue.Substring(0, strValue.Length - 1);//取数字位
            if (int.TryParse(strDigit, out int iDigit))
            {
                switch (strForm.ToUpper())
                {
                    case "Y":
                        dateTime = DateTime.Now.AddYears(iDigit);
                        break;
                    case "M":
                        dateTime = DateTime.Now.AddMonths(iDigit);
                        break;
                    case "D":
                        dateTime = DateTime.Now.AddDays((double)iDigit);
                        break;
                    case "H":
                        dateTime = DateTime.Now.AddHours((double)iDigit);
                        break;
                    case "F":
                        dateTime = DateTime.Now.AddMinutes((double)iDigit);
                        break;
                    case "S":
                        dateTime = DateTime.Now.AddSeconds((double)iDigit);
                        break;
                    default:
                        dateTime = DateTime.Now.AddDays((double)iDigit);
                        break;
                }
                return dateTime;
            }
            else
            {
                return DateTime.Now;
            }
        }

        /// <summary>
        /// 根据设置年月日字符串，设置年月日 如设置yyyy-mm-dd，使用当前时间替换设置的格式，不区分大小写
        /// 如设置 yyyy-mm-01 则表示设置当月的1日
        /// 如设置 yyyy-01-01 则表示设置当年的1日
        /// </summary>
        /// <param name="DateFormat">格式字符串</param>
        /// <returns></returns>
        private string GetDateStrByFormatSetDate(string DateFormat)
        {
            string strDateTime = DateFormat;            
            if (DateFormat.ToCharArray().Count(x => x == '-') > 1 //排除一个“-”的减号的
                || DateFormat.Contains(@"/"))//如果包含-字符则表示设置日期 如yyyy-mm-dd或 yyyy/mm-dd
            {
                Regex regexYear = new Regex("YYYY", RegexOptions.IgnoreCase);
                if (regexYear.IsMatch(strDateTime))
                    strDateTime = regexYear.Replace(strDateTime, DateTime.Now.Year.ToString());

                Regex regexMonth = new Regex("MM", RegexOptions.IgnoreCase);
                if (regexMonth.IsMatch(strDateTime))
                    strDateTime = regexMonth.Replace(strDateTime, DateTime.Now.Month.ToString("d2"));

                Regex regexDay = new Regex("DD", RegexOptions.IgnoreCase);
                if (regexDay.IsMatch(strDateTime))
                    strDateTime = regexDay.Replace(strDateTime, DateTime.Now.Day.ToString("d2"));

                return strDateTime;
            }
            else
            {
                return DateTime.Now.ToString("yyyy-MM-dd");
            }
        }


        /// <summary>
        /// 根据设置时分秒字符串，设置时分秒 如设置hh:mm:dd，使用当前时间替换设置的格式，不区分大小写
        /// 如设置 hh:mm:00 则表示设置当前分钟的0秒
        /// 如设置 hh:00:00 则表示设置当前小时的0分零秒
        /// </summary>
        /// <param name="TimeFormat">格式字符串</param>
        /// <returns></returns>
        private string GetTimeStrByFormatSetDate(string TimeFormat)
        {
            string strDateTime = TimeFormat;

            if (strDateTime.Contains(":"))//如果包含:字符则表示设置时间 如hh:mm:ss
            {
                Regex regexHour = new Regex("HH", RegexOptions.IgnoreCase);
                if (regexHour.IsMatch(strDateTime))
                    strDateTime = regexHour.Replace(strDateTime, DateTime.Now.Hour.ToString("d2"));

                Regex regexMinute = new Regex("MM", RegexOptions.IgnoreCase);
                if (regexMinute.IsMatch(strDateTime))
                    strDateTime = regexMinute.Replace(strDateTime, DateTime.Now.Minute.ToString("d2"));

                Regex regexSecond = new Regex("SS", RegexOptions.IgnoreCase);
                if (regexSecond.IsMatch(strDateTime))
                    strDateTime = regexSecond.Replace(strDateTime, DateTime.Now.Second.ToString("d2"));

                return strDateTime;
            }
            else
            {
                return DateTime.Now.ToString("hh:mm:ss");
            }
        }
    }
}
