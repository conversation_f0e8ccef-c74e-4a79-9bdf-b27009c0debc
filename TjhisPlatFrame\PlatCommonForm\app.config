<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <system.serviceModel>
        <bindings>
            <basicHttpBinding>
                <binding name="webServiceSoap"/>
            </basicHttpBinding>
        </bindings>
        <client>
            <endpoint address="http://**************:1014/webService.asmx" binding="basicHttpBinding" bindingConfiguration="webServiceSoap" contract="appcommRefundService.webServiceSoap" name="webServiceSoap"/>
        </client>
    </system.serviceModel>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5.2"/></startup></configuration>
