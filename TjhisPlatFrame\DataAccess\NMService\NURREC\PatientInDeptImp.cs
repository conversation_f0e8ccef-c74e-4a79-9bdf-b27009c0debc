﻿using INMService;
//**********************************************
//说明:
//计算机名称：LINDP
//创建日期：2016/5/18 10:46:30
//作者：林大鹏
//版本号：V1.00
//**********************************************
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace NM_Service.NMService
{
    public class PatientInDeptClient : INMService.IPatientInDept,IDisposable
    {
        public string SaveTable(Dictionary<string, string> list)
        {
            return new OracleDAL.PatientInDept().SaveTable(list);
        }

        #region IDisposable 成员

        /// <summary>
　　　　/// 实现IDisposable接口
　　　　/// </summary>
　　　　public void Dispose()
　　　　{
　　　　　　Dispose(true);
　　　　　　//.NET Framework 类库
　　　　　　// GC..::.SuppressFinalize 方法
　　　　　　//请求系统不要调用指定对象的终结器。
　　　　　　GC.SuppressFinalize(this);
　　　　}
　　　　/// <summary>
　　　　/// 虚方法，可供子类重写
　　　　/// </summary>
　　　　/// <param name="disposing"></param>
　　　　protected virtual void Dispose(bool disposing)
　　　　{
　　　　　　if (disposing)
　　　　　　　　{
　　　　　　　　　　// Release managed resources
　　　　　　　　}
　　　　}
　　　　/// <summary>
　　　　/// 析构函数
　　　　/// 当客户端没有显示调用Dispose()时由GC完成资源回收功能
　　　　/// </summary>
    ~PatientInDeptClient()
　　　　{
　　　　　　Dispose();
　　　　}

        #endregion
    }
}
