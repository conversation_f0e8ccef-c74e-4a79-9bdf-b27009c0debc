﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PlatCommon.Common
{
    /// <summary>
    /// 自动ID帮助类
    /// </summary>
    public class IDHelper
    {
        /// <summary>
        /// 读取ID或序号 不回写更新ID
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <param name="hisUnitCode">医院编码</param>
        /// <param name="sCurrentIDValue">输出类型：格式化后的当前值</param>
        /// <param name="iCurrentIDValue">输出类型：当前值整数值</param>
        /// <param name="db">默认参数：用于控制事务的db，可为null</param>
        /// <param name="hasParams">可选参数：用于生成SQL语句结果前缀的参数，可为空</param>
        /// <param name="sqlFirst">可选参数：true为SQL语句结果前置， false为固定前缀前置，默认为true</param>
        /// <returns>true成功，false失败</returns>
        public static bool GetId(string typeName, string hisUnitCode, out string sCurrentIDValue, out int iCurrentIDValue, Utility.OracleODP.OracleBaseClass db = null, Hashtable hasParams = null, bool sqlFirst = true)
        {
            AutoSettingIDProvider iDProvider = new AutoSettingIDProvider();
            return iDProvider.GetId(typeName, hisUnitCode, out sCurrentIDValue, out iCurrentIDValue, db, hasParams, sqlFirst);
        }

        /// <summary>
        /// 得到更新Id值的sql语句，方法会返回update语句用于更新id值，一般用于调用者在保存前执行更新Id的SQL语句,由调用者更新Id
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <param name="hisUnitCode">医院编码</param>
        /// <param name="sCurrentIDValue">输出：格式化后的当前ID值</param>
        /// <param name="iCurrentIDValue">输出：当前ID值整数变量</param>
        /// <param name="sNextIDValue">输出类型：执行更新操作后的计算出的格式化后的新值</param>
        /// <param name="iNextIDValue">输出类型：执行更新操作后的计算出的新值</param>
        /// <param name="updateSql">输出类型：更新自动ID表的语句，用于调用者执行更新自动ID表</param>
        /// <param name="db">用于控制事务的db，必选项</param>
        /// <param name="hasParams">可选参数：用于生成SQL语句结果前缀的参数，可为空</param>
        /// <param name="sqlFirst">可选参数：true为SQL语句结果前置， false为固定前缀前置，默认为true</param>
        /// <returns>true成功，false失败</returns>
        public static bool GetUpdateIdSql(string typeName, string hisUnitCode, out string sCurrentIDValue, out int iCurrentIDValue, out string sNextIDValue, out int iNextIDValue, out string updateSql, Utility.OracleODP.OracleBaseClass db = null, Hashtable hasParams = null, bool sqlFirst = true)
        {
            AutoSettingIDProvider iDProvider = new AutoSettingIDProvider();
            return iDProvider.GetUpdateIdSql(typeName, hisUnitCode, out sCurrentIDValue, out iCurrentIDValue, out sNextIDValue, out iNextIDValue, out updateSql, db, hasParams, sqlFirst);
        }

        /// <summary>
        /// 回写Id，该方法会计算新值自动更新Id，保存到AUTO_SETTING_ID表中，一般用于保存前更新Id
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <param name="hisUnitCode">医院编码</param>
        /// <param name="iCurrentIDValue">输出类型：当前值</param>
        /// <param name="sCurrentIDValue">输出类型：格式化后的当前ID值</param>
        /// <param name="sNexIDValue">输出类型：执行更新操作后的计算出的格式化后的新值</param>
        /// <param name="iNextIDValue">输出类型：执行更新操作后的计算出的新值</param>
        /// <param name="db">用于控制事务的db，必选项</param>
        /// <param name="hasParams">可选参数：用于生成SQL语句结果前缀的参数，可为空</param>
        /// <param name="sqlFirst">可选参数：true为SQL语句结果前置， false为固定前缀前置，默认为true</param>
        /// <returns>true成功，false失败</returns>
        public static bool UpdateId(string typeName, string hisUnitCode, out string sCurrentIDValue, out int iCurrentIDValue, out string sNexIDValue, out int iNextIDValue, Utility.OracleODP.OracleBaseClass db =null, Hashtable hasParams = null, bool sqlFirst = true)
        {
            AutoSettingIDProvider iDProvider = new AutoSettingIDProvider();
            return iDProvider.UpdateId(typeName, hisUnitCode, out sCurrentIDValue, out iCurrentIDValue, out sNexIDValue, out iNextIDValue, db, hasParams, sqlFirst);
        }      
    }
}
