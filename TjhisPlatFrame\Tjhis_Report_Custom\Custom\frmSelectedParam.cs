﻿using PlatCommon.SysBase;
using System;
using System.Data;
using System.Linq;
using System.Windows.Forms;
using Tjhis.Report.Custom.Srv;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmSelectedParam : ParentForm
    {
        public DataRow[] paramRow;
        DataSet dsDict;
        public frmSelectedParam()
        {
            InitializeComponent();
        }

        private void frmSelectedParam_Load(object sender, EventArgs e)
        {
            dsDict = new srvStatisticalQuery().GetParamDict();
            dsDict.Tables[0].Columns.Add("COL_SEL",Type.GetType("System.String"));
            dsDict.Tables[0].Columns["COL_SEL"].ReadOnly = false;

            gridControl1.DataSource = dsDict.Tables[0];

            SetCheckedParam();
        }

        private void SetCheckedParam()
        {
            if(paramRow != null && dsDict != null)
            {
                foreach (DataRow dr in paramRow)
                {
                    string paramName = dr["param_name"].ToString();
                    DataRow findRow = dsDict.Tables[0].Select().ToList().Find( fd => fd["param_name"].ToString().Equals(paramName));
                    if(findRow != null)
                    {
                        findRow["COL_SEL"] = "1";
                    }
                }
            }
        }
        private void gridView1_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
           if(e.Clicks == 2)
           {
                DataRow dr = gridView1.GetFocusedDataRow();
                frmParamEdit fe= new frmParamEdit(dr);
                fe.ShowDialog();
            }
        }

        private void btnCancel_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnOk_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

            gridView1.CloseEditor();
            gridView1.UpdateCurrentRow();

            paramRow = dsDict.Tables[0].Select("COL_SEL = '1'");
            dsDict.Tables[0].Columns.Remove("COL_SEL");

            this.DialogResult = DialogResult.OK;
        }
    }
}
