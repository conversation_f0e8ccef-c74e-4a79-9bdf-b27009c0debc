﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace PlatCommonForm.comm
{
    /// <summary>
    /// 军改读卡
    /// </summary>
     public class ReadArmy
    {
        #region 军改读卡声明
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "LinkDevice")]
        public static extern int linkdevice(string devicename);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "UnlinkDevice")]
        public static extern int unlinkdevice();
        [DllImport("UsbDxqDll_10.dll",CharSet=CharSet.Ansi, EntryPoint = "PowerOn")]
        public static extern int PowerOn( byte[] recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll", EntryPoint = "PowerOff")]
        public static extern int poweroff( byte[] recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "ReadCard")]
        public static extern int readcard(int zone, int addr, int len,  byte[] recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "WriteCard")]
        public static extern int writecard(int zone, int addr, int len, string[] senddata, ref string[] recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "ChkSecret")]
        public static extern int chksecret(int keytype, string[] key, ref string[] recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "WriteSecret")]
        public static extern int writesecret(int keytype, string[] key, ref string[] recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "ReadSecret")]
        public static extern int readsecret(int keytype, ref string[] recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "HexStrToByte")]
        public static extern int hexstrtobyte(string tstr, ref string[] data);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "ByteToHexStr")]
        public static extern int bytetohexstr(byte[] data, int size,  StringBuilder tstr);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "HexByteToDec")]
        public static extern int hexbytetodec(string[] tstr, ref int dec);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "DecToHexByte")]
        public static extern int dectohexbyte(int dec, ref string[] tstr);

        [DllImport("UsbDxqDll_10.dll",CharSet=CharSet.Ansi, EntryPoint = "SetKeyVersion")]
        public static extern int setkeyversion(int keykind, int keyver,  byte[] recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "Chk1608Secret")]
        public static extern int chk1608secret(int keyno, ref byte recedata, ref UInt64 plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "Authenfic1608")]
        public static extern int authenfic1608(byte[] recedata, ref uint plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "HexByteToLong")]
        public static extern int hexbytetolong(byte[] tstr, ref int dec);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "LongToHexByte")]
        public static extern int longtohexbyte(int dec, ref string[] tstr);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "Signature")]
        public static extern int signature(string[] idinfo, string[] info, int infolen, ref string[] recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "VerifySignet")]
        public static extern int verifysignet(string[] idinfo, string info, int infolen, string infosign, ref string[] recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "WriteIDInfo")]
        public static extern int writeidinfo(string[] idinfo, ref string recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "PINOpfunc")]
        public static extern int pinopfunc(int ioptype, int slot, string[] pininfo, int pininfolen, ref string[] recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "UpdateIDInfo")]
        public static extern int updateidinfo(string[] rightinfo, ref string recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "LockUserCard")]
        public static extern int lockusercard(ref string[] recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "ReadSamIDInfo")]
        public static extern int readsamidinfo(ref string[] recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll",CharSet=CharSet.Ansi, EntryPoint = "GetICCardNo")]
        public static extern int geticcardno(int slot,  byte[] recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "PowerOnSam")]
        public static extern int poweronsam(ref string[] recedata, ref int plen);
        [DllImport("UsbDxqDll_10.dll", CharSet = CharSet.Ansi, EntryPoint = "GetUpdateIDRight")]
        public static extern int getupdateidright(string[] iccardno, string hospitalid, ref string[] recedata, ref int plen);

        #endregion 
        public int authenticate_keyno1, private_passwordno1, public_passwordno1, sign_keyno1;
        string card_type_code = "88";
        string sam_type_code = "92";
        string cardtype1 = "AT88SC1608";
        byte[] buffer1 = new byte[256];
        //连接
        public  int wf_medcard_connect(string port)
        {
            string port1 = port.Trim().ToUpper();
            if (string.IsNullOrEmpty(port1))
            {
                XtraMessageBox.Show("医疗卡读写器的端口设置不正确！", "提示");
                return -1;
            }

            if (linkdevice(port1) !=0)
            {
                XtraMessageBox.Show("连接医疗卡读写器的端口失败！", "提示");
                return -1;
            }

            string ls_sql = "select authenticate_key_no , private_password_no , public_password_no , sign_key_no from security_access_para";
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_sql).Tables[0];
            if (dt.Rows.Count > 0)
            {
                authenticate_keyno1 =int.Parse(dt.Rows[0][0].ToString());
                private_passwordno1 = int.Parse(dt.Rows[0][1].ToString());
                public_passwordno1 = int.Parse(dt.Rows[0][2].ToString());
                sign_keyno1 = int.Parse(dt.Rows[0][3].ToString());
            }
            else
            {
                XtraMessageBox.Show("取医疗卡的安全控制参数时出错！", "提示");
                return -1;
            }
            // StringBuilder recedata = new StringBuilder();
            byte[] recedata = new byte[256];
            int i = 1, plen= 0;
            if (setkeyversion(i, this.authenticate_keyno1, recedata, ref plen) != 0)
            {
                XtraMessageBox.Show("设置医疗卡双向认证密钥版本号失败！", "提示");
                return -1;                
            }
            i = 2;
            if (setkeyversion(i, this.private_passwordno1,  recedata, ref plen) != 0)
            {
                XtraMessageBox.Show("设置医疗卡私有密钥版本号失败！", "提示");
                return -1;
            }
            i = 3;
            //recedata = new StringBuilder(256);
            if (setkeyversion(i, this.public_passwordno1,  recedata, ref plen) != 0)
            {
                XtraMessageBox.Show("设置医疗卡公共密钥版本号失败！", "提示");
                return -1;
            }
            i = 4;
            if (setkeyversion(i, this.sign_keyno1,  recedata, ref plen) != 0)
            {
                XtraMessageBox.Show("设置医疗卡签名密钥版本号失败！", "提示");
                return -1;
            }
                       
            return 0;
        }
        //加电
        public int wf_power_on()
        {
            
            byte[] lc_recedata = new byte[256];

            int plen = 0;
            string card_no = "", type_code = "";
            try
            {
                if (PowerOn( lc_recedata, ref plen) != 0)
                {
                    XtraMessageBox.Show("给医疗卡加电失败！", "警告");
                    return -1;
                }
                int i_len = plen;

               // string sb_str = Encoding.GetEncoding("GBK").GetString(lc_recedata);
                StringBuilder sb = new StringBuilder(2 * i_len + 5);
                string ls_str = "";
              int li=  bytetohexstr(lc_recedata, plen - 2,  sb);

               /// XtraMessageBox.Show("" + sb.ToString(), "tishi1");
               
                card_type_code = sb.ToString();

                DataTable dt_1 = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select *from ICCARD_TYPE_DICT a where a.identifier='" + card_type_code + "'").Tables[0];
                if (dt_1.Rows.Count <= 0)
                {
                    XtraMessageBox.Show("医疗卡的型号配置不正确！", "警告");
                    return -1;
                }
                if (card_type_code.Equals("87"))
                {
                    sam_type_code = "95";
                    return 0;
                }
                if (!card_type_code.Equals("88") && !card_type_code.Equals("89"))
                {
                    sam_type_code = "99";
                    return 0;
                }

                if (wf_medcard_geticcardno(1, ref card_no, ref type_code) != 0)
                {
                    XtraMessageBox.Show("读SAM卡型号代码出错！", "警告");
                    return -1;
                }
                sam_type_code = type_code;
            }
            catch(Exception ex)
            {
                throw new Exception("信息错误！" + ex.Message);
            }
            return 0;
        }

        public int wf_medcard_geticcardno(int slot, ref string card_no, ref string type_code)
        {
            int slot_num;
            int ll_item_len=0;
            byte[] ls_item = new byte[256];
            byte[] recedata = new byte[256];
            if (card_type_code.Equals("87"))
            {
                if (wf_check_secret(0, 0)!= 0)
                {
                    XtraMessageBox.Show("读卡号出错！", "警告");
                    return -1;
                }
                //StringBuilder work_buff = new StringBuilder(20);
                string work_buff1="";
                wf_medcard_readitem_fromcard(0, "CARD_NO",ref work_buff1);
                card_no = work_buff1;
                return 0;
            }

            slot_num = slot;

            if (geticcardno(slot_num,  ls_item, ref ll_item_len) < 0)
            {
                XtraMessageBox.Show("读卡号出错1！", "警告");
                return -1;
            }
            StringBuilder work_buff = new StringBuilder(5);
    
            if (bytetohexstr(ls_item, 1,  work_buff) != 0) return -1;
            type_code = work_buff.ToString();
            int int_len = ll_item_len - 3;

            string sb_str = Encoding.GetEncoding("GBK").GetString(ls_item);

            for (int i = 0; i < int_len; i++)
            {
                recedata[i] = ls_item[i + 2];

            }
            sb_str = Encoding.ASCII.GetString(recedata);
            work_buff = new StringBuilder(2 * int_len + 2);
            if (bytetohexstr(recedata, int_len, work_buff) != 0)
            {
                return -1;
            }
            card_no = work_buff.ToString();
            return 0;

        }

        public int wf_check_secret(int sectionno,int passwordtype)
        {
            int keyno = 8 * passwordtype + sectionno + 1;
            UInt64 plen = 0;
            byte[] lc_recedata = new byte[256];
            WriteLog("密码"+keyno.ToString());
            if (chk1608secret(keyno,ref lc_recedata[0],ref plen)!= 0)
            {
                XtraMessageBox.Show("密码核对未通过,该卡可能是非法卡！", "警告");
                return -1;
            }
            return 0;
        }

        public int wf_medcard_readitem_fromcard(int accounttype,string itemname,ref string itemvalue)
        {
            string ls_sql = "select *from MEDICAL_CARD_SCHEDULE a where a.iccard_type='"+ cardtype1 + "' and a.account_type='"+ accounttype + "'and item_id='"+ itemname.ToUpper()+"'";
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_sql).Tables[0];
            if (dt.Rows.Count <= 0)
            {
                XtraMessageBox.Show("取医疗卡上 " + itemname + " 的参数出错！", "警告");
                return -1;
            }

            int section_no;
            int start_pos; ;
            int item_len;
            int numeric_indicator;
            int fee_indicator;
            int ll_item_len = 0;
            int ll_item_temp = 0;
            decimal ld_item;
            try
            {
                section_no = int.Parse(dt.Rows[0]["SECTION_NO"].ToString());
                start_pos = int.Parse(dt.Rows[0]["START_POS"].ToString());
                item_len = int.Parse(dt.Rows[0]["ITEM_LEN"].ToString());
                numeric_indicator = int.Parse(dt.Rows[0]["numeric_indicator"].ToString());
                fee_indicator = int.Parse(dt.Rows[0]["fee_indicator"].ToString());
                numeric_indicator = int.Parse(dt.Rows[0]["numeric_indicator"].ToString());
                string item_name = dt.Rows[0]["item_name"].ToString();
                byte[] ls_item = new byte[256];
                byte[] ls_ttc = new byte[4];
                if (readcard(section_no, start_pos, item_len, ls_item,ref ll_item_len) != 0)
                {
                    XtraMessageBox.Show("读医疗卡 " + itemname + " 时出错！", "警告");
                    return -1;
                }

                if (numeric_indicator == 1)
                {
                    for (int i = 0; i < 4; i++)
                    {
                        if (i <= item_len)
                        {
                            ls_ttc[i] = ls_item[i];
                            continue;
                        }

                        ls_ttc[i] = Convert.ToByte("0");//CharA(0) pb里
                    }

                    if (hexbytetolong(ls_ttc, ref ll_item_temp) < 0)
                    {
                        itemvalue = "";
                        return -1;
                    }

                    if (fee_indicator == 1)
                    {
                        ld_item = ll_item_temp / 100;
                        itemvalue = ld_item.ToString();
                    }
                    else
                    {
                        itemvalue = ll_item_temp.ToString();
                    }
                }
                else
                {
                    if (ll_item_len >= 1)
                    {
                        for (int i = 0; i < (ll_item_len - 2); i++)
                        {
                            if (ls_item[i] == 255) //待确定
                            {
                                ls_item[i] = Convert.ToByte("");
                            }
                            itemvalue = itemvalue + ls_item[i].ToString();
                        }
                    }
                    else
                    {
                        itemvalue = "";
                    }
                    itemvalue = itemvalue.Trim();
                }

            }
            catch (Exception ex)
            {
               throw new Exception("信息错误！" + ex.Message);
            }

            return 0;
        }
        public int wf_medcard_readitem(int accounttype, string itemname, ref string itemvalue)
        {
            string ls_sql = "select *from MEDICAL_CARD_SCHEDULE a where a.iccard_type='" + cardtype1 + "' and a.account_type='" + accounttype + "'and item_id='" + itemname.ToUpper() + "'";
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_sql).Tables[0];
            if (dt.Rows.Count <= 0)
            {
                XtraMessageBox.Show("取医疗卡上 " + itemname + " 的参数出错！", "警告");
                return -1;
            }
            byte[] ls_item = new byte[480];
            byte[] ls_ttc = new byte[4];

            int section_no;
            int start_pos; ;
            int item_len;
            int numeric_indicator;
            int fee_indicator;
            int ll_item_len = 0;
            int ll_item_temp = 0;
            decimal ld_item;
            try
            {

                section_no = int.Parse(dt.Rows[0]["SECTION_NO"].ToString());
                start_pos = int.Parse(dt.Rows[0]["START_POS"].ToString());
                item_len = int.Parse(dt.Rows[0]["ITEM_LEN"].ToString());
                numeric_indicator = int.Parse(dt.Rows[0]["numeric_indicator"].ToString());
                fee_indicator = int.Parse(dt.Rows[0]["fee_indicator"].ToString());
                numeric_indicator = int.Parse(dt.Rows[0]["numeric_indicator"].ToString());
                string item_name = dt.Rows[0]["item_name"].ToString();
                string ls_buffer1 = Encoding.GetEncoding("GBK").GetString(buffer1);
                byte[] lb_desc = new byte[item_len];
                //XtraMessageBox.Show("ls_buffer1"+ ls_buffer1, "提示");
                System.Array.Copy(buffer1, start_pos, lb_desc, 0, item_len);
                itemvalue = Encoding.Default.GetString(lb_desc).Trim();
                string ls_buffer2 = Encoding.GetEncoding("GBK").GetString(buffer1);
                //MessageBox.Show("ls_buffer2" + itemvalue, "提示");
                //for (int i = 0; i < item_len; i++)
                //{
                //    ls_item[i] = this.buffer1[start_pos + i]; //待确定
                //}

                //ll_item_len = item_len;

                //if (numeric_indicator == 1)
                //{
                //    for (int i = 0; i < 4; i++)
                //    {
                //        if (i <= item_len)
                //        {
                //            ls_ttc[i] = ls_item[i];
                //            continue;
                //        }

                //        ls_ttc[i] = Convert.ToByte("0");//CharA(0) pb里
                //    }

                //    if (hexbytetolong(ls_ttc, ref ll_item_temp) < 0)
                //    {
                //        itemvalue = "";
                //        return -1;
                //    }

                //    if (fee_indicator == 1)
                //    {
                //        ld_item = ll_item_temp / 100;
                //        itemvalue = ld_item.ToString();
                //    }
                //    else
                //    {
                //        itemvalue = ll_item_temp.ToString();
                //    }
                //}
                //else
                //{
                //    if (ll_item_len >= 1)
                //    {
                //        for (int i = 0; i < ll_item_len; i++)
                //        {
                //            if (ls_item[i] == 255) //待确定
                //            {
                //                ls_item[i] = Convert.ToByte("");
                //            }
                //            itemvalue = itemvalue + ls_item[i].ToString();
                //        }
                //    }
                //    else
                //    {
                //        itemvalue = "";
                //    }
                //    itemvalue = itemvalue.Trim();
                //}

            }
            catch (Exception ex)
            {
                throw new Exception("信息错误！" + ex.Message);
            }

            return 0;
        }
        //双向验证
        public int wf_medcard_authenticate()
        {
            byte[] lc_recedata = new byte[256];
            uint plen=0;
            try
            {
                if (authenfic1608(lc_recedata, ref plen) != 0)
                {
                    XtraMessageBox.Show("双向认证未通过,该卡可能是非法卡!", "警告");
                    return -1;
                }
                
               
            }
            catch (Exception ex)
            {
                throw new Exception("信息错误！" + ex.Message);
            }
            return 0;
        }

        //读卡
        public int wf_medcard_readbuffer(int accounttype,string itemname)
        {
           // XtraMessageBox.Show(""+ cardtype1+ "accounttype"+ accounttype+ "itemname"+ itemname, "提示");
            int ll_item_len = 0;
            string sql = "select t.* from MEDICAL_CARD_SCHEDULE t where t.iccard_type='"+ cardtype1 + "' and t.account_type='"+ accounttype + "' and item_id='"+ itemname + "'";
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
            if (dt.Rows.Count <= 0)
            {
                XtraMessageBox.Show("取医疗卡上"+ itemname+ "的参数出错!iccard_type" + cardtype1 + "account_type"+ accounttype, "警告");
                return -1;
            }
            int section_no = int.Parse(dt.Rows[0]["SECTION_NO"].ToString());
            int start_pos = int.Parse(dt.Rows[0]["START_POS"].ToString());
            int item_len = int.Parse(dt.Rows[0]["ITEM_LEN"].ToString());
            int numeric_indicator = int.Parse(dt.Rows[0]["NUMERIC_INDICATOR"].ToString());
            int fee_indicator = int.Parse(dt.Rows[0]["FEE_INDICATOR"].ToString());
            string item_name = (dt.Rows[0]["ITEM_NAME"].ToString());
            //读卡
           
            int return_v = readcard(section_no, start_pos, item_len, this.buffer1, ref ll_item_len);
            string ls_buffer1 = Encoding.Default.GetString(buffer1);
            //WriteLog("转换前" + ls_buffer1);
            //XtraMessageBox.Show("ls_buffer1读卡" + ls_buffer1 + "!", "警告");
            // 根据Unicode编码重新格式化字符buffer
           // uf_syb_chars();
            //WriteLog("转换后" + ls_buffer1);
            // XtraMessageBox.Show("return_v" + return_v.ToString() + "!", "警告");
            if (return_v < 0)
            {
                XtraMessageBox.Show("取医疗卡上" + itemname + "的出错!", "警告");
                return -1;
            }

             return 0;
        }



        public void uf_syb_chars()
        {
            int ll_ptr=0, ll_strlen, ll_chrlen=0, ll_chrcount=0, ll_lastpos;
 
            byte[] buffer_new = new byte[256];
            byte[] ls_empt = new byte[256];
            byte[] lc_tmp = new byte[256];
            string sql = "select t.* from MEDICAL_CARD_SCHEDULE t where t.iccard_type='HX-CPU' and t.account_type= 0 and t.section_no=0 order by section_no asc, section_sub_no asc";
            DataTable dt_ms = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
            if (dt_ms.Rows.Count > 0)
            {
                byte[] ls_bufenew = new byte[256];
                //WriteLog("准备循环" + dt_ms.Rows.Count.ToString());
                for (int i = 1; i < dt_ms.Rows.Count; i++)
                {
                    int ll_startpos = int.Parse(dt_ms.Rows[i]["start_pos"].ToString());
                    int ll_len = int.Parse(dt_ms.Rows[i]["item_len"].ToString());
                    ll_chrcount = 0;
                    ll_strlen = 0;
                    ll_lastpos = ll_ptr;
                    //WriteLog("循环开始 ll_startpos" + ll_startpos.ToString()+ "ll_len"+ ll_len.ToString()+ "ll_lastpos"+ ll_lastpos.ToString());
                    string ls_bufer = Encoding.GetEncoding("GBK").GetString(buffer1, ll_lastpos, ll_len);
                   
                    Array.Copy(buffer1, ll_lastpos, ls_bufenew, ll_startpos, ll_len);

                    ll_ptr = ll_len;
                    //while (ll_strlen < ll_len)
                    //{
                    //    WriteLog("while开始"+ "ll_strlen"+ ll_strlen.ToString()+ "ll_len"+ ll_len.ToString());

                    //    lc_tmp[0] = buffer1[ll_ptr];
                    //    WriteLog("lc_tmp值" + lc_tmp.ToString());
                    //    ll_chrlen = lc_tmp.ToString().Length; //待确认
                    //    WriteLog("ll_chrlen长度" + ll_chrlen.ToString());
                    //    if (ll_chrlen <= 0) ll_chrlen = 1;
                    //    ll_strlen = ll_strlen + ll_chrlen;
                    //    ll_ptr++;
                    //    ll_chrcount++;
                    //}
                  //  WriteLog("i "+ i.ToString()+ "ll_chrcount"+ ll_chrcount.ToString()+ "ll_ptr"+ ll_ptr.ToString());
                    
                    //for (int j = 0; j < ll_len; j++)
                    //{
                    //    //9;
                    //    if (j <= ll_chrcount)
                    //    {
                    //        // buffer_new[ll_startpos + j] = buffer1[ll_lastpos - 1 + j]
                    //        buffer_new[ll_startpos + j ] = buffer1[ll_lastpos  + j];
                    //        WriteLog("ll_startpos小于" + ll_startpos.ToString() + "j" + j.ToString() + "ALL" + (ll_startpos + j).ToString());
                    //    }
                    //    else
                    //    {
                    //        WriteLog("ll_startpos大于" + ll_startpos.ToString() + "j" + j.ToString() + "ALL" + (ll_startpos + j).ToString());
                    //        buffer_new[ll_startpos + j] = ls_empt[0];
                    //    }
                    //}
                }
                string buffer_new1 = Encoding.GetEncoding("GBK").GetString(ls_bufenew);
               // WriteLog("buffer_new1"+ buffer_new1);
                //XtraMessageBox.Show("buffer_new1"+ buffer_new1, "22");
                //for (int k=0;k<256;k++)
                //{
                    buffer1 = ls_bufenew;
                //}

            }
        }

        public int wf_medcard_accounttype()
        {
            string c="";
            int rtn_i = wf_medcard_readitem_fromcard(0, "ACCOUNT_TYPE",ref c);
            if (rtn_i != 0)
            {
                XtraMessageBox.Show("读帐户类型出错!", "警告");
                return -1;
            }
            int li_accounttype = int.Parse(c);
            
            return li_accounttype;
        }

        public int of_get_account(string as_port,ClassAccount anv_account)
        {
            string ls_temp="";
            if (wf_medcard_connect(as_port) < 0)
            {
                XtraMessageBox.Show("连接读卡器失败,申请资源失败", "提示");
                return -1;
            }
            if (wf_power_on() < 0)
            {
                XtraMessageBox.Show("加电失败", "提示");
                return -1;
            }
            if (wf_medcard_authenticate() < 0)
            {
                XtraMessageBox.Show("双向验证失败", "提示");
                return -1;
            }
            //XtraMessageBox.Show("准备校验密码!", "提示");
            if (wf_check_secret(0, 0) < 0)
            {
                XtraMessageBox.Show("读卡密码校验失败", "提示");
                return -1;
            }
            //XtraMessageBox.Show("读卡密码校验完成", "提示");
            wf_medcard_readbuffer(0, "ACCOUNT_INFO_SECTION");
            //XtraMessageBox.Show("ACCOUNT_INFO_SECTION", "提示");
            anv_account.accounttype = wf_medcard_accounttype();
            if (anv_account.accounttype < 0)
            {
                XtraMessageBox.Show("读取账户类型失败！", "提示");
            }
            else
            {
                
                wf_medcard_readbuffer(anv_account.accounttype, "ACCOUNT_INFO_SECTION");
               // string ls_buffer1 = " 130225199711152958812114190306132499          刘嘉乐  男  **************汉族          士兵                          免费医疗        ***********                         600039                    101000000020190312 ********                            ?    ";
               // Encoding end = Encoding.GetEncoding("GBK");

               // buffer1 = end.GetBytes(ls_buffer1);
                
                if (wf_medcard_readitem(anv_account.accounttype, "INSURANCE_NO", ref anv_account.insurance_no) < 0)
                {
                    XtraMessageBox.Show("读取失败！", "帐号");
                }
                else if (wf_medcard_readitem(anv_account.accounttype, "PATIENT_ID", ref anv_account.patient_id) < 0)
                {
                    XtraMessageBox.Show("读取失败！", "病人ID");
                }
                else if (wf_medcard_readitem(anv_account.accounttype, "NAME", ref anv_account.name) < 0)
                {
                    XtraMessageBox.Show("读取失败！", "姓名");
                }
                else if (wf_medcard_readitem(anv_account.accounttype, "SEX", ref anv_account.sex) < 0)
                {
                    XtraMessageBox.Show("读取失败！", "性别");
                }
                else if (wf_medcard_readitem(anv_account.accounttype, "UNIT", ref anv_account.unit) < 0)
                {
                    XtraMessageBox.Show("读取失败！", "单位");
                }
                else if (wf_medcard_readitem(anv_account.accounttype, "date_of_birth", ref ls_temp) < 0)
                {
                    XtraMessageBox.Show("读取失败！", "帐号");
                }
                else
                {
                   // XtraMessageBox.Show("" + anv_account.insurance_no.Trim(), "卡号");
                    //XtraMessageBox.Show("出生日期！"+ ls_temp, "日期");

                    anv_account.date_of_birth = DateTime.Parse(ls_temp.Substring(0, 4) + "-" + ls_temp.Substring(4, 2) + "-" + ls_temp.Substring(6, 2));
                    if (wf_medcard_readitem(anv_account.accounttype, "birth_place", ref anv_account.birth_place) < 0)
                    {
                        XtraMessageBox.Show("读取失败！", "出生地");
                    }
                    else if (wf_medcard_readitem(anv_account.accounttype, "nation", ref anv_account.nation) < 0)
                    {
                        XtraMessageBox.Show("读取失败！", "民族");
                    }
                    else if (wf_medcard_readitem(anv_account.accounttype, "identity_class", ref anv_account.identity_class) < 0)
                    {
                        XtraMessageBox.Show("读取失败！", "身份");
                    }
                    else if (wf_medcard_readitem(anv_account.accounttype, "insurance_type", ref anv_account.insurance_type) < 0)
                    {
                        XtraMessageBox.Show("读取失败！", "医保类型");
                    }
                    else if (wf_medcard_readitem(anv_account.accounttype, "grade_of_duty", ref ls_temp) < 0)
                    {
                        XtraMessageBox.Show("读取失败！", "行政职务等级");
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(ls_temp))
                        {
                            string ls_1 = "select grade_name from grade_of_duty_dict where grade_code ='"+ ls_temp + "'";
                            DataTable dt_1 = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_1).Tables[0];
                            if (dt_1.Rows.Count <= 0)
                            {
                                //anv_account.grade_of_duty = "";
                                //XtraMessageBox.Show("没有找到代码"+ ls_temp + "对应的行政职务等级名称！", "提示");
                                //goto Label_0FA0;
                            }
                            else
                            {
                                anv_account.grade_of_duty = dt_1.Rows[0][0].ToString();
                            }
                        }

                        if (wf_medcard_readitem(anv_account.accounttype, "grade_of_technic", ref ls_temp) < 0)
                        {
                            XtraMessageBox.Show("读取失败！", "技术级别");
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(ls_temp))
                            {
                                string ls_2 = "select grade_name from grade_of_technic_dict where grade_code ='" + ls_temp + "'";
                                DataTable dt_2 = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_2).Tables[0];
                                if (dt_2.Rows.Count <= 0)
                                {
                                    anv_account.grade_of_technic = "";
                                    XtraMessageBox.Show("没有找到代码" + ls_temp + "对应的技术级别名称！", "提示");
                                    //goto Label_0FA0;
                                }
                                else
                                {
                                    anv_account.grade_of_technic = dt_2.Rows[0][0].ToString();
                                }
                            }
                            else
                            {
                                anv_account.grade_of_technic = "";
                            }

                            if (wf_medcard_readitem(anv_account.accounttype, "grade_of_title", ref ls_temp) < 0)
                            {
                                XtraMessageBox.Show("读取失败！", "技术职称");
                            }
                            else
                            {
                                if (!string.IsNullOrEmpty(ls_temp))
                                {
                                    string ls_3 = "select grade_name from grade_of_title_dict where grade_code ='" + ls_temp + "'";
                                    DataTable dt_3 = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_3).Tables[0];
                                    if (dt_3.Rows.Count <= 0)
                                    {
                                        anv_account.grade_of_title = "";
                                        XtraMessageBox.Show("没有找到代码" + ls_temp + "对应的技术职务名称！", "提示");
                                        //goto Label_0FA0;
                                    }
                                    else
                                    {
                                        anv_account.grade_of_title = dt_3.Rows[0][0].ToString();
                                    }
                                }
                                else
                                {
                                    anv_account.grade_of_title = "";
                                }

                                if (wf_medcard_readitem(anv_account.accounttype, "designated_hospital", ref anv_account.hospital) < 0)
                                {
                                    XtraMessageBox.Show("读取失败！", "体系医院");
                                }
                                else if (wf_medcard_readitem(anv_account.accounttype, "designated_policlinic", ref anv_account.policlinic) < 0)
                                {
                                    XtraMessageBox.Show("读取失败！", "基层医疗机构");
                                }
                                else if (wf_medcard_readitem(anv_account.accounttype, "card_no", ref anv_account.card_no) < 0)
                                {
                                    XtraMessageBox.Show("读取失败！", "卡号");
                                }
                                else if (wf_medcard_readitem(anv_account.accounttype, "insurance_type", ref anv_account.insurance_type) < 0)
                                {
                                    XtraMessageBox.Show("读取失败！", "医保类型");
                                }
                                else if (wf_medcard_readitem(anv_account.accounttype, "EXPIRATION_DATE", ref ls_temp) < 0)
                                {
                                    XtraMessageBox.Show("读取失败！", "效期");
                                }
                                else
                                {
                                    anv_account.expiration_date = DateTime.Parse(ls_temp.Substring(0, 4) + "-" + ls_temp.Substring(4, 2) + "-" + ls_temp.Substring(6, 2));
                                    anv_account.name_phonetic = PlatCommon.Base01.Cs01PhoneticWubi.GetStrPinyin(anv_account.name).Trim().ToUpper();
                             
                                    if (wf_power_off() < 0)
                                    {
                                        XtraMessageBox.Show("去电失败！", "去电");
                                    }
                                    else
                                    {
                                        
                                        wf_medcard_disconnect();
                                        return 1;
                                    }
                                }
                            }
                        }




                    }
                }

            }


        Label_0FA0:
            wf_power_off();
            wf_medcard_disconnect();
            return 0;

        }

        public int wf_power_off()
        {
            int plen = 0;
            byte[] recedata = new byte[256];
            int  li_lef_return = poweroff(recedata,ref plen);
            if (li_lef_return != 0)
            {
                XtraMessageBox.Show("给医疗卡去电失败！", "警告");
                return -1;
            }

            return 0;
        }
        public int wf_medcard_disconnect()
        {
            if (unlinkdevice() == 0)
            {
                return 0;
            }
            else
            {
                return -1;
            }

        }
        public static void WriteLog(string str)
        {
            try
            {
                DateTime d = DateTime.Now;
                string path = AppDomain.CurrentDomain.BaseDirectory + "YBLOG_ONE/";
                string fileName = d.ToString("yyyy-MM-dd-HH") + ".txt";

                if (!Directory.Exists(path))
                    Directory.CreateDirectory(path);

                using (StreamWriter file = new StreamWriter(path + fileName, true))
                {
                    if (string.IsNullOrEmpty(str))
                        file.WriteLine("");
                    else
                    {
                        file.WriteLine(d.ToString("MM-dd HH:mm:ss") + " ==>> " + str);
                        //file.WriteLine(str);
                    }
                }
            }
            catch (Exception e)
            {
                XtraMessageBox.Show("writeLog方法异常" + e.Message);

            }

        }
    }
}
