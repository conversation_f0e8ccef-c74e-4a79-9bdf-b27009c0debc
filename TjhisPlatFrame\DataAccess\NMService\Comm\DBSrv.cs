﻿using System;
using System.Text;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Collections.ObjectModel;

//using HashItem = Comm.Object.HashItem;
//using SQL = Comm.Str.StringHelper;

namespace NM_Service.NMService
{
    public class DBSrvClient : INMService.IDBSrv,IDisposable
    {
        #region SQL语句处理
        /// <summary>
        /// 通过SQLID获取SQL语句. Sql语句保存到表NR_DICT_SQL中
        /// </summary>
        /// <param name="sqlId">sqlId</param>
        /// <param name="items">参数列表</param>
        /// <returns>sql文本</returns>
        public string GetSqlTextA(string sqlId, ObservableCollection<HashItem> items)
        {
            Hashtable hasParams = GetHashtable(items);

            // 获取SQL原文
            string sqlText = GetSqlText(sqlId);
            
            // 替换参数
            if (hasParams == null) return sqlText;

            string paramName = string.Empty;
            string paramValue = string.Empty;

            string paramName0 = string.Empty;
            string paramValue0 = string.Empty;

            foreach (DictionaryEntry entry in hasParams)
            {
                // 参数名
                paramName = "{" + entry.Key.ToString().ToUpper() + "}";        // 这种情况 变成 ''
                paramName0 = "[" + entry.Key.ToString().ToUpper() + "]";        // 这种情况 变成 原样

                // 参数值
                if (entry.Value == null)
                {
                    paramValue = "NULL";
                }
                else if (entry.Value.GetType().Equals(typeof(DateTime)))
                {
                    paramValue = SQL.GetOraDate((DateTime)(entry.Value));
                }
                else
                {
                    paramValue = SQL.SqlConvert(entry.Value.ToString());
                }

                paramValue0 = entry.Value == null ? "" : entry.Value.ToString();

                // 替换参数
                if (sqlText.IndexOf(paramName) >= 0)
                {
                    sqlText = sqlText.Replace(paramName, paramValue);
                }
                else if (sqlText.IndexOf(paramName0) >= 0)
                {
                    sqlText = sqlText.Replace(paramName0, paramValue0);
                }
            }
            
            return sqlText;
        }
        

        /// <summary>
        /// 通过SQLID获取SQL语句. Sql语句保存到表NR_DICT_SQL中。
        /// </summary>
        /// <param name="sqlId">sqlId</param>
        /// <returns>sql文本</returns>
        public string GetSqlText(string sqlId)
        {
            DataSet dsResult = null;
            
            // 获取SQL原文
            string filter = "USE_INDICATOR = '1' AND (SQL_CODE = " + SQL.SqlConvert(sqlId) + "or SQL_CODE like '" + sqlId.TrimEnd() + "+%')";
            string sql = "SELECT * FROM NR_V_DICT_SQL WHERE " + filter;

            dsResult = SelectDataA(sql, "NR_DICT_SQL", true);
            
            // SQL处理
            DataRow[] drFind = dsResult.Tables[0].Select("", "SQL_CODE");

            sql = string.Empty;

            if (drFind.Length > 0)
            {
                if (drFind[0]["SQL_CODE"].Equals(sqlId))
                {
                    return drFind[0]["SQL_TEXT"].ToString();
                }
                else
                {
                    for (int i = 0; i < drFind.Length; i++)
                    {
                        sql += drFind[i]["SQL_TEXT"].ToString();
                    }

                    sql = sql.Replace('\n', ' ');
                    sql = sql.Replace('\r', ' ');
                    return sql;
                }
            }        
            else
            {
                throw new Exception("指定的SQL_CODE: " + sqlId + " 不存在!");
            }
        }


        /// <summary>
        /// 获取SQL属性
        /// </summary>
        /// <param name="sqlId"></param>
        /// <param name="tableName"></param>
        /// <param name="blnWithKey"></param>
        /// <returns></returns>
        public bool GetSqlProperty(string sqlId, ref string tableName, ref bool blnWithKey)
        {
            DataSet dsResult = null;

            // 获取SQL原文
            string filter = "USE_INDICATOR = '1' AND (SQL_CODE = " + SQL.SqlConvert(sqlId) + "or SQL_CODE like '" + sqlId.TrimEnd() + "+%')";
            string sql = "SELECT * FROM NR_V_DICT_SQL WHERE " + filter;

            dsResult = SelectDataA(sql, "NR_DICT_SQL", true);

            // 获取属性
            if (Comm.Collection.DataSetHelper.HasRecord(dsResult))
            {
                DataRow dr = dsResult.Tables[0].Rows[0];
                tableName = dr["TABLE_NAME"].ToString();
                blnWithKey = dr["WITH_KEY"].ToString().Equals("1");

                return true;
            }

            return false;
        }
        #endregion

        
        #region 数据查询
        /// <summary>
        /// 获取下一个序列号
        /// </summary>
        /// <param name="seqName"></param>
        /// <returns></returns>
        public int GetSeq(string seqName)
        {
            string sql = "SELECT " + seqName + ".NEXTVAL FROM DUAL";

            // 查询数据
            DataSet dsResult = dsResult = SelectDataA(sql, "DUAL", false);
                        
            // 获取值
            if (Comm.Collection.DataSetHelper.HasRecord(dsResult))
            {
                DataRow dr = dsResult.Tables[0].Rows[0];

                return (int)(dr[0]);
            }

            throw new Exception("获取序列 " + seqName + " 值出错");
        }


        /// <summary>
        /// 单值查询
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public string SelectValueBySql(string sql)
        {
            // 查询数据
            DataSet dsResult = SelectDataA(sql, "tableName", false);

            // 获取值
            if (Comm.Collection.DataSetHelper.HasRecord(dsResult))
            {
                DataRow dr = dsResult.Tables[0].Rows[0];

                return dr[0].ToString();
            }
                        
            return string.Empty;
        }


        /// <summary>
        /// 单值查询
        /// </summary>
        /// <param name="sqlId"></param>
        /// <param name="items"></param>
        /// <returns></returns>
        public string SelectValue(string sqlId, ObservableCollection<HashItem> items)
        {
            string sql = GetSqlTextA(sqlId, items);
            return SelectValueBySql(sql);
        }


        /// <summary>
        /// 查询数据集
        /// </summary>
        /// <param name="sqlId"></param>
        /// <param name="items"></param>
        /// <returns></returns>
        public DataSet SelectData(string sqlId, ObservableCollection<HashItem> items)
        {
            string sql = string.Empty; 
            string tableName = string.Empty; 
            bool blnWithKey = false;
            
            Hashtable hasParams = GetHashtable(items);

            // 获取SQL属性
            sql = GetSqlTextA(sqlId, items);
            GetSqlProperty(sqlId, ref tableName, ref blnWithKey);

            // 进行查询
            return SelectDataA(sql, tableName, blnWithKey);
        }

        
        public DataSet SelectDataA(string sql, string tableName, bool blnWithKey)
        {
            if (Utility.UntilityConstant.DBConnectionMode == 0)
            {
                using (Comm.Data.OracleM db = new Comm.Data.OracleM())
                {
                    return db.SelectData(sql, tableName, blnWithKey);
                }
            }
            else
            {
                using (Comm.Data.Oracle db = new Comm.Data.Oracle())
                {
                    return db.SelectData(sql, tableName, blnWithKey);
                }
            }
        }


        public DataSet SelectDataBySql(string sql, string tableName)
        {
            return SelectDataA(sql, tableName, true);
        }


        public DataSet SelectDataBySqlKey(string sql, string tableName, bool blnWithKey)
        {
            return SelectDataA(sql, tableName, blnWithKey);
        }
        #endregion
        

        #region 数据更新
        public bool SaveDataC(ArrayList arrDataSet, ArrayList arrSql)
        {
            DataSet[] dsArr = new DataSet[arrDataSet.Count];
            for (int i = 0; i < arrDataSet.Count; i++)
            {
                dsArr[i] = (DataSet)arrDataSet[i];
            }

            if (Utility.UntilityConstant.DBConnectionMode == 0)
            {
                using (Comm.Data.OracleM db = new Comm.Data.OracleM())
                {
                    return db.SaveData(dsArr, arrSql);
                }
            }
            else
            {
                using (Comm.Data.Oracle db = new Comm.Data.Oracle())
                {
                    return db.SaveData(dsArr, arrSql);
                }
            }
        }


        public bool SaveDataB(DataSet ds, ArrayList arrSql)
        {
            DataSet[] dsArr = new DataSet[ds.Tables.Count];
            for (int i = 0; i < ds.Tables.Count; i++)
            {
                dsArr[i] = new DataSet();
                dsArr[i].Tables.Add(ds.Tables[i].Copy());
            }

            if (Utility.UntilityConstant.DBConnectionMode == 0)
            {
                using (Comm.Data.OracleM db = new Comm.Data.OracleM())
                {
                    return db.SaveData(dsArr, arrSql);
                }
            }
            else
            {
                using (Comm.Data.Oracle db = new Comm.Data.Oracle())
                {
                    return db.SaveData(dsArr, arrSql);
                }
            }
        }


        public int SaveDataA(DataSet ds)
        {
            if (Utility.UntilityConstant.DBConnectionMode == 0)
            {
                using (Comm.Data.OracleM db = new Comm.Data.OracleM())
                {
                    return db.SaveData(ds);
                }
            }
            else
            {
                using (Comm.Data.Oracle db = new Comm.Data.Oracle())
                {
                    return db.SaveData(ds);
                }
            }
        }

        public int SaveData(DataSet ds, string tableName, string sqlSel)
        {
            if (Utility.UntilityConstant.DBConnectionMode == 0)
            {
                using (Comm.Data.OracleM db = new Comm.Data.OracleM())
                {
                    return db.SaveData(ds, tableName, sqlSel);
                }
            }
            else
            {
                using (Comm.Data.Oracle db = new Comm.Data.Oracle())
                {
                    return db.SaveData(ds, tableName, sqlSel);
                }
            }
            
        }

        public int SaveData(DataSet ds)
        {
            return SaveDataA(ds);
        }


        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="arrDataSet">数据集数组</param>
        /// <param name="arrSql">SQL语句集合</param>
        /// <returns>TRUE: 成功; FALSE: 失败</returns>
        public int SaveData(object[] arrDataSet, ArrayList arrSql)
        {
            if (Utility.UntilityConstant.DBConnectionMode == 0)
            {
                using (Comm.Data.OracleM db = new Comm.Data.OracleM())
                {
                    return db.SaveData(arrDataSet, arrSql)==true?1:0;
                }
            }
            else
            {
                using (Comm.Data.Oracle db = new Comm.Data.Oracle())
                {
                    return db.SaveData(arrDataSet, arrSql) == true ? 1 : 0;
                }
            }
        }
        public int Update(ref DataSet ds)
        {
            if (Utility.UntilityConstant.DBConnectionMode == 0)
            {
                using (Comm.Data.OracleM db = new Comm.Data.OracleM())
                {
                    return db.Update(ref ds);
                }
            }
            else
            {
                using (Comm.Data.Oracle db = new Comm.Data.Oracle())
                {
                    return db.Update(ref ds);
                }
            }
        }

        public int SaveDataD(DataSet ds, string tableName, string sqlSel)
        {
            if (Utility.UntilityConstant.DBConnectionMode == 0)
            {
                using (Comm.Data.OracleM db = new Comm.Data.OracleM())
                {
                    return db.SaveData(ds, tableName, sqlSel);
                }
            }
            else
            {
                using (Comm.Data.Oracle db = new Comm.Data.Oracle())
                {
                    return db.SaveData(ds, tableName, sqlSel);
                }
            }
        }


        public int ExecuteNoQueryB(ArrayList sqlCol)
        {
            if (Utility.UntilityConstant.DBConnectionMode == 0)
            {
                using (Comm.Data.OracleM db = new Comm.Data.OracleM())
                {
                    return db.ExecuteNoQuery(sqlCol);
                }
            }
            else
            {
                using (Comm.Data.Oracle db = new Comm.Data.Oracle())
                {
                    return db.ExecuteNoQuery(sqlCol);
                }
            }
        }


        public int ExecuteNoQueryB(ObservableCollection<object> sqlCol)
        {
            if (Utility.UntilityConstant.DBConnectionMode == 0)
            {
                using (Comm.Data.OracleM db = new Comm.Data.OracleM())
                {
                    for (int i = 0; i < sqlCol.Count; i++)
                    {
                        db.ExecuteNoQuery(sqlCol[i].ToString());
                    }
                }
            }
            else
            {
                using (Comm.Data.Oracle db = new Comm.Data.Oracle())
                {
                    for (int i = 0; i < sqlCol.Count; i++)
                    {
                        db.ExecuteNoQuery(sqlCol[i].ToString());
                    }
                }
            }

            return 1;
        }


        public int ExecuteNoQuery(string sql)
        {
            if (Utility.UntilityConstant.DBConnectionMode == 0)
            {
                using (Comm.Data.OracleM db = new Comm.Data.OracleM())
                {
                    return db.ExecuteNoQuery(sql);
                }
            }
            else
            {
                using (Comm.Data.Oracle db = new Comm.Data.Oracle())
                {
                    return db.ExecuteNoQuery(sql);
                }
            }
        }


        public int ExecuteNoQueryA(string sqlId, ObservableCollection<HashItem> items)
        {
            Hashtable hasParams = GetHashtable(items);

            string sql = GetSqlTextA(sqlId, items);

            if (Utility.UntilityConstant.DBConnectionMode == 0)
            {
                using (Comm.Data.OracleM db = new Comm.Data.OracleM())
                {
                    return db.ExecuteNoQuery(sql);
                }
            }
            else
            {
                using (Comm.Data.Oracle db = new Comm.Data.Oracle())
                {
                    return db.ExecuteNoQuery(sql);
                }
            }
        }


        public int ExecuteProcedure(string sqlId, ObservableCollection<HashItem> items)
        {
            Hashtable hasParams = GetHashtable(items);

            string sql = GetSqlTextA(sqlId, items);

            if (Utility.UntilityConstant.DBConnectionMode == 0)
            {
                using (Comm.Data.OracleM db = new Comm.Data.OracleM())
                {
                    return db.ExecuteProcedure(sql);
                }
            }
            else
            {
                using (Comm.Data.Oracle db = new Comm.Data.Oracle())
                {
                    return db.ExecuteProcedure(sql);
                }
            }
        }
        #endregion


        #region 调用存储过程
        /// <summary>
        ///调用存储过程
        /// 方法的输入产生都默认Varchar2, 输出默认一个游标
        /// add by 荣守国 2015.9.7
        /// </summary>
        /// <param name="procedureName"></param>
        /// <param name="items"></param>
        /// <returns></returns>
        public DataSet GetDataByProc(string procedureName, ObservableCollection<HashItem> items)
        {
            if (Utility.UntilityConstant.DBConnectionMode == 0)
            {
                using (Comm.Data.OracleM db = new Comm.Data.OracleM())
                {
                    return db.GetDataByProcedure(procedureName, items);
                }
            }
            else
            {
                using (Comm.Data.Oracle db = new Comm.Data.Oracle())
                {
                    return db.GetDataByProcedure(procedureName, items);
                }
            }
        }
        #endregion


        #region 共通函数
        private Hashtable GetHashtable(ObservableCollection<HashItem> items)
        {
            Hashtable hasParams = new Hashtable();
            
            if (items != null)
            {
                foreach (HashItem item in items)
                {
                    hasParams[item.Name] = item.Value;
                }
            }

            return hasParams;
        }
        #endregion

        #region 新增

        #endregion
        #region IDisposable 成员

        /// <summary>
　　　　/// 实现IDisposable接口
　　　　/// </summary>
　　　　public void Dispose()
　　　　{
　　　　　　Dispose(true);
　　　　　　//.NET Framework 类库
　　　　　　// GC..::.SuppressFinalize 方法
　　　　　　//请求系统不要调用指定对象的终结器。
　　　　　　GC.SuppressFinalize(this);
　　　　}
　　　　/// <summary>
　　　　/// 虚方法，可供子类重写
　　　　/// </summary>
　　　　/// <param name="disposing"></param>
　　　　protected virtual void Dispose(bool disposing)
　　　　{
　　　　　　if (disposing)
　　　　　　　　{
　　　　　　　　　　// Release managed resources
　　　　　　　　}
　　　　}
　　　　/// <summary>
　　　　/// 析构函数
　　　　/// 当客户端没有显示调用Dispose()时由GC完成资源回收功能
　　　　/// </summary>
    ~DBSrvClient()
　　　　{
　　　　　　Dispose();
　　　　}

        #endregion
    }
}