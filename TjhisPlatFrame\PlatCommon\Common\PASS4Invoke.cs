﻿//**********************************************
//说明:合理用药接口类
//计算机名称：LINDP
//创建日期：2016/8/23 16:03:12
//作者：林大鹏
//版本号：V1.00
//**********************************************
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace PlatCommon.Common
{
    public class PASS4Invoke
    {
        //1、PASS初始化
        [DllImport("PASS4Invoke.dll",EntryPoint="MDC_Init",CharSet=CharSet.Ansi)]
        public static extern int MDC_Init(String pcCheckMode,
            String pcHisCode,
            String pcDoctorCode);

        //2、传病人基本记录信息函数
        [DllImport("PASS4Invoke.dll",EntryPoint="MDC_SetPatient",CharSet=CharSet.Ansi)]
        public static extern int MDC_SetPatient(String pcPatCode,
                    String pcInHospNo,
                    String pcVisitCode,
                    String pcName,
                    String pcSex,
                    String pcBirthday,
                    String pcHeightCM,
                    String pcWeighKG,
                    String pcDeptCode,
                    String pcDeptName,
                    String pcDoctorCode,
                    String pcDoctorName,
                    int piPatStatus,
                    int piIsLactation,
                    int piIsPregnancy,
                    String pcPregStartDate,
                    int piHepDamageDegree,
                int piRenDamageDegree);
        //3、传病人药品记录信息
        [DllImport("PASS4Invoke.dll",EntryPoint="MDC_AddScreenDrug",CharSet=CharSet.Ansi)]
        public static extern int MDC_AddScreenDrug(String pcIndex,
                    String piOrderNo,
                    String pcDrugUniqueCode,
                    String pcDrugName,
                    String pcDosePerTime,
                    String pcDoseUnit,
                    String pcFrequency,
                    String pcRouteCode,
                    String pcRouteName,
                    String pcStartTime,
                    String pcEndTime,
                    String pcExecuteTime,
                    String pcGroupTag,
                    String pcIsTempDrug,
                    String pcOrderType,
                    String pcDeptCode,
                    String pcDeptName,
                    String pcDoctorCode,
                    String pcDoctorName,
                    String pcRecipNo,
                    String pcNum,
                    String pcNumUnit,
                    String pcPurpose,
                    String pcOprCode,
                    String pcMediTime,
                    String pcRemark);
        //4、传入病人过敏史记录信息
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_AddAller", CharSet = CharSet.Ansi)]
        public static extern int MDC_AddAller(string pcIndex,
                    string pcAllerCode,
                    string pcAllerName,
                    string pcAllerSymptom);

        //5、传入病人诊断记录信息
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_AddMedCond", CharSet = CharSet.Ansi)]
        public static extern int MDC_AddMedCond(string pcIndex,
                    string pcDiseaseCode,
                    string pcDiseaseName,
                    string pcRecipNo);

        //6、传入病人手术记录信息
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_AddOperation", CharSet = CharSet.Ansi)]
        public static extern int MDC_AddOperation(string pcIndex,
                    string pcOprCode,
                    string pcOprName,
                    string pcOprStartDateTime,
                    string pcOprEndDateTime);
        //7、审查函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_DoCheck", CharSet = CharSet.Ansi)]
        public static extern int MDC_DoCheck(int piShowMode,
                    int piIsSave);

        //8、获取药品医嘱警示级别
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_GetWarningCode", CharSet = CharSet.Ansi)]
        public static extern int MDC_GetWarningCode(string pcIndex);

        //9、获取一条药品医嘱的审查结果提示窗口函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_ShowWarningHint", CharSet = CharSet.Ansi)]
        public static extern int MDC_ShowWarningHint(string pcIndex);

        //10、关闭一条药品医嘱的审查结果提示窗口函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_CloseWarningHint", CharSet = CharSet.Ansi)]
        public static extern int MDC_CloseWarningHint();

        //11、获取审查结果条数函数【一般不用】
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_GetResultItemCount", CharSet = CharSet.Ansi)]
        public static extern int MDC_GetResultItemCount(string pcIndex);

        //12、获取审查结果详细信息函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_GetResultDetail", CharSet = CharSet.Ansi)]
        public static extern string MDC_GetResultDetail(string pcIndex);

        //13、传入一个查询药品函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_DoSetDrug", CharSet = CharSet.Ansi)]
        public static extern int MDC_DoSetDrug(String pcDrugUniqueCode, String pcDrugName);

        //14、获取药品查询项目有效性函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_DoRefDrugEnable", CharSet = CharSet.Ansi)]
        public static extern string MDC_DoRefDrugEnable(int piQueryType);

        //15、查询药品信息函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_DoRefDrug", CharSet = CharSet.Ansi)]
        public static extern int MDC_DoRefDrug(int piQueryType);

        //16、关闭浮动窗口函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_CloseDrugHint", CharSet = CharSet.Ansi)]
        public static extern int MDC_CloseDrugHint();

        //17、本地参数设置窗口函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_Settings", CharSet = CharSet.Ansi)]
        public static extern int MDC_Settings();

        //18、调用药研究窗口函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_DoMediStudy", CharSet = CharSet.Ansi)]
        public static extern int MDC_DoMediStudy(string pcUseTime);

        //19、获取PASS系统最后一次错误信息函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_GetLastError", CharSet = CharSet.Ansi)]
        public static extern string MDC_GetLastError();

        //20、PASS退出
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_Quit", CharSet = CharSet.Ansi)]
        public static extern int MDC_Quit();

        //ldp后加
        //查询药品信息函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_GetDrugQueryInfo", CharSet = CharSet.Ansi)]
        public static extern int MDC_GetDrugQueryInfo(string pcDrugUniqueCode, string pcDrugName, int piQueryType, int x, int y);

    }
}
