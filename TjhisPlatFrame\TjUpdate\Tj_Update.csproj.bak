﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{A17D08D3-410F-4B2B-A270-54F0B32D0457}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Tj_Update</RootNamespace>
    <AssemblyName>Tj_Update</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>发布\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\TjhisPlatSource\TJHisPlatEXE\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>logo.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.BonusSkins.v18.1">
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Data.v18.1">
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Utils.v18.1">
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Sparkline.v18.1.Core">
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v18.1">
      <Private>True</Private>
    </Reference>
    <Reference Include="INMService">
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\INMService.dll</HintPath>
    </Reference>
    <Reference Include="Model">
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\Model.dll</HintPath>
    </Reference>
    <Reference Include="NMService">
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\NMService.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess">
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="OracleDAL">
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\OracleDAL.dll</HintPath>
    </Reference>
    <Reference Include="PlatCommon">
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\PlatCommon.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Configuration">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Core">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Deployment">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Windows.Forms">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml">
      <Private>True</Private>
    </Reference>
    <Reference Include="Utility">
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\Utility.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="EmrUpdate.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="EmrUpdate.designer.cs">
      <DependentUpon>EmrUpdate.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmDbConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmDbConfig.designer.cs">
      <DependentUpon>FrmDbConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Login.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Login.designer.cs">
      <DependentUpon>Frm_Login.cs</DependentUpon>
    </Compile>
    <Compile Include="FTPClient.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="EmrUpdate.resx">
      <DependentUpon>EmrUpdate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmDbConfig.resx">
      <DependentUpon>FrmDbConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Login.resx">
      <DependentUpon>Frm_Login.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="app.config">
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Config\connection.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </Content>
    <Content Include="logo.ico" />
    <Content Include="main.ico" />
    <Content Include="package_network.ico" />
    <None Include="Resources\Ok.png" />
    <None Include="Resources\Close.png" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.4.5">
      <Visible>False</Visible>
      <ProductName>Windows Installer 4.5</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>