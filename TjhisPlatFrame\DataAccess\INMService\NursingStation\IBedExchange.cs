﻿/*-----------------------------------------------------------------------
 * 类名称    ：IBedExchange
 * 类描述    ：
 * 创建人    ：梁吉lions
 * 创建时间  ：2016/6/4 15:48:08
 * 修改人    ：
 * 修改时间  ：
 * 修改备注  ：
 * 版本      ：
 * ----------------------------------------------------------------------
 */
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace INMService
{
    [ServiceContract]
    public interface IBedExchange
    {
        /// <summary>
        /// 换床位，如果床位都有人，则交换，否则换到空床
        /// </summary>
        /// <param name="bedNoFrom">需要换床的床号</param>
        /// <param name="bedStatusFrom">需要换床的床状态</param>
        /// <param name="bedLabelFrom">需要换床的床标号</param>
        /// <param name="patient_IDFrom">需要换床的患者</param>
        /// <param name="bedNoTo">目标床号</param>
        /// <param name="bedStatusTo">目标床状态</param>
        /// <param name="bedLabelTo">目标床标号</param>
        /// <param name="patient_IDTo">目标床患者</param>
        /// <param name="wardCode">所在护理单元</param>
        /// <param name="db">数据源</param>
        /// <returns>
        /// -9 打开数据库连接失败
        /// -1 数据库异常
        /// 0- 返回成功
        /// 1-换床者床是空床更新换床者床位状态失败
        /// 2-换床者床是空床更新被换床者床位状态失败
        /// 3-被换床者床是空床更新换床者床位状态失败
        /// 4-被换床者床是空床更新被换床者床位状态失败
        /// 5-换床者床已有患者更新患者换到被换床者床位失败
        /// 6-被换床者床已有患者更新患者换到换床者床位失败
        /// 7-说明换床者是母亲，有婴儿，更新婴儿床标号到被换床者失败
        /// 8-说明被换床者是母亲，有婴儿，更新婴儿床标号到换床者失败
        /// </returns>
        [OperationContract]
        int ExchangeBed_BedExchange(string bedNoFrom, string bedStatusFrom, string bedLabelFrom, string patient_IDFrom, string bedNoTo, string bedStatusTo, string bedLabelTo, string patient_IDTo, string wardCode);
    }
}
