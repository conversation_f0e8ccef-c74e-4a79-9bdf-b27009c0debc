﻿using PlatCommon.SysBase;
using System;
using System.Data;
using System.Windows.Forms;
using Tjhis.Report.Custom.Srv;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmSQLQueryTest : ParentForm
    {
        public delegate void DelegateUseSQL(string sqlStr);
        public event DelegateUseSQL UseSqlEvent;

        public string sql { get; set; }
        public frmSQLQueryTest()
        {
            InitializeComponent();
        }

        private void frmSQLQueryTest_Load(object sender, EventArgs e)
        {
            richTextBox1.Text = sql;
            GetData();
        }

        private void GetData()
        {
            try
            {
                if(gridView1.Columns.Count > 0)
                {
                    gridView1.Columns.Clear();
                }
                string sqlStr = string.Empty;
                if(richTextBox1.SelectedText.Length > 0)
                {
                    sqlStr = richTextBox1.SelectedText;
                }
                else
                {
                    sqlStr = richTextBox1.Text;
                }
                DataTable dt = new srvT().TestSQL(sqlStr).Tables[0];
                gridControl1.DataSource = dt;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }
        private void btnRunSql_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            GetData();
        }
    }
}
