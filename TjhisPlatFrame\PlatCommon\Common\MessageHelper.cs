﻿/*********************************************
* 文 件 名：MessageHelper
* 类 名 称：MessageHelper
* 功能说明：消息操作类
* 版权说明：北京天健源达 护理电子病历及护理管理研发部
* 创 建 人：王代迪
* 创建时间：2021年12月31日 10:10:06
* 版 本 号：1.0.0.1
* CLR 版本：4.0.30319.42000
*********************************************/
using DevExpress.XtraBars.Alerter;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;


namespace PlatCommon.Common
{
    /// <summary>
    /// 功能描述：扩展显示消息功能
    /// 
    /// </summary>	
    public class MessageHelper
    {
        static AlertControl alert = new AlertControl();

        /// <summary>
        /// 
        /// </summary>
        public static Form MainWidow;
        /// <summary>
        /// 信息弹窗
        /// </summary>
        static MessageHelper()
        {
            alert.AutoFormDelay = 2000; // 延迟
            alert.AllowHtmlText = true; // HTML文本作为窗体标题

            alert.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Skin;
            alert.LookAndFeel.UseDefaultLookAndFeel = false;

            alert.FormLoad += Alert_FormLoad;
            alert.BeforeFormShow += Alert_BeforeFormShow;
        }

        private static void Alert_BeforeFormShow(object sender, DevExpress.XtraBars.Alerter.AlertFormEventArgs e)
        {
            e.AlertForm.OpacityLevel = 1;
            e.AlertForm.AutoScroll = true;
            e.AlertForm.Size = new System.Drawing.Size(300, 150);
            e.AlertForm.BackColor = Color.White;
        }

        private static void Alert_FormLoad(object sender, DevExpress.XtraBars.Alerter.AlertFormLoadEventArgs e)
        {
            e.AlertForm.AutoScroll = true;
            e.AlertForm.Size = new System.Drawing.Size(300, 150);
            e.AlertForm.BackColor = Color.White;
        }

        /// <summary>
        /// 消息提醒
        /// </summary>
        /// <param name="forms"></param>
        /// <param name="caption"></param>
        /// <param name="text"></param>
        public static void ShowInformation(Form forms, string caption, string text)
        {
            if (string.IsNullOrWhiteSpace(caption)) { caption = "提示"; }
            alert.Show(forms, "<color=53,153,148> <b><size=12>" + caption + "</size></b></color>", "\r\n<color=black><size=10>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + text + "</size></color>");
        }

        /// <summary>
        /// 消息提示
        /// </summary>
        /// <param name="forms"></param>
        /// <param name="text"></param>
        public static void ShowInformation(Form forms, string text)
        {
            ShowInformation(forms, "提示", text);
        }

        /// <summary>
        /// 消息提示
        /// </summary>
        /// <param name="text"></param>
        public static void ShowInformation(string text)
        {
            ShowInformation(MainWidow, "提示", text);
        }

        /// <summary>
        /// 保存成功
        /// </summary>
        /// <param name="forms"></param>
        /// <param name="caption"></param>
        /// <param name="text"></param>
        public static void ShowSuccess(Form forms, string caption, string text)
        {
            if (string.IsNullOrWhiteSpace(caption)) { caption = "成功"; }
            alert.Show(forms, "<color=0,113,199> <b><size=12>" + caption + "</size></b></color>", "\r\n<color=black><size=10>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + text + "</size></color>");
        }

        /// <summary>
        /// 保存成功
        /// </summary>
        /// <param name="forms"></param>
        /// <param name="text"></param>
        public static void ShowSuccess(Form forms, string text)
        {
            ShowSuccess(forms, "成功", text);
        }

        /// <summary>
        /// 保存成功
        /// </summary>
        /// <param name="text"></param>
        public static void ShowSuccess(string text)
        {
            ShowSuccess(MainWidow, "成功", text);
        }

        /// <summary>
        /// 错误
        /// </summary>
        /// <param name="forms"></param>
        /// <param name="caption"></param>
        /// <param name="text"></param>
        public static void ShowError(Form forms, string caption, string text)
        {
            if (string.IsNullOrWhiteSpace(caption)) { caption = "错误"; }
            alert.Show(forms, "<color=216,56,60> <b><size=12>" + caption + "</size></b></color>", "\r\n<color=black><size=10>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + text + "</size></color>");
        }

        /// <summary>
        /// 错误
        /// </summary>
        /// <param name="forms"></param>
        /// <param name="text"></param>
        public static void ShowError(Form forms, string text)
        {
            ShowError(forms, "错误", text);
        }

        /// <summary>
        /// 错误
        /// </summary>
        /// <param name="forms"></param>
        /// <param name="text"></param>
        public static void ShowError(string text)
        {
            ShowError(MainWidow, "错误", text);
        }

        /// <summary>
        /// 警告
        /// </summary>
        /// <param name="forms"></param>
        /// <param name="caption"></param>
        /// <param name="text"></param>
        public static void ShowWaring(Form forms, string caption, string text)
        {
            if (string.IsNullOrWhiteSpace(caption)) { caption = "警告"; }
            alert.Show(forms, "<color=253, 124, 0> <b><size=12>" + caption + "</size></b></color>", "\r\n<color=black><size=10>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + text + "</size></color>");
        }

        /// <summary>
        /// 警告
        /// </summary>
        /// <param name="forms"></param>
        /// <param name="text"></param>
        public static void ShowWaring(Form forms, string text)
        {
            ShowWaring(forms, "警告", text);
        }

        /// <summary>
        /// 警告
        /// </summary>
        /// <param name="text"></param>
        public static void ShowWaring(string text)
        {
            ShowWaring(MainWidow, "警告", text);
        }


    }
}
