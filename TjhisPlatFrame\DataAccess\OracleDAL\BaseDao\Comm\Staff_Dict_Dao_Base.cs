﻿using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using Utility;
using Utility.OracleODP;
using Oracle.ManagedDataAccess.Client;

namespace OracleDAL
{

    /// <summary>
    /// 工作人员字典 数据库操作类
    /// </summary>

    public class STAFF_DICT_Dao_Base
    {
        #region   Method
        public bool Exists(string ID, OracleBaseClass db)
        {
            #region  init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from STAFF_DICT");
            strSql.Append(" where ");
            strSql.Append(" ID = :ID ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":ID", OracleDbType.Varchar2, 5);
            p.Value = ID;
            parameters.Add(p);

            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    int cmdresult;
                    cmdresult = int.Parse(ds.Tables[0].Rows[0][0].ToString());
                    if (cmdresult <= 0)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                    return false;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.STAFF_DICT model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into STAFF_DICT(");
            strSql.Append("EMP_NO,CREATE_DATE,PASSWORD,SYS_FLAG,STATUS,ROLEID,IP,AUTOGRAPH,COMMUNITY_CODE,SEX,ID_NO,DEPT_CODE,NATION,MAILING_ADDRESS,WORK_ID,WORK_STATE,FORMATION,NAME,INPUT_CODE,JOB,TITLE,USER_NAME,INPUT_CODE_WB,ID");
            strSql.Append(") values (");
            strSql.Append(":EMP_NO,:CREATE_DATE,:PASSWORD,:SYS_FLAG,:STATUS,:ROLEID,:IP,:AUTOGRAPH,:COMMUNITY_CODE,:SEX,:ID_NO,:DEPT_CODE,:NATION,:MAILING_ADDRESS,:WORK_ID,:WORK_STATE,:FORMATION,:NAME,:INPUT_CODE,:JOB,:TITLE,:USER_NAME,:INPUT_CODE_WB,:ID");
            strSql.Append(") ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":EMP_NO", OracleDbType.Varchar2, 10);
            p.Value = model.EMP_NO;
            parameters.Add(p);

            p = new OracleParameter(":CREATE_DATE", OracleDbType.Date);
            p.Value = model.CREATE_DATE;
            parameters.Add(p);

            p = new OracleParameter(":PASSWORD", OracleDbType.Varchar2, 255);
            p.Value = model.PASSWORD;
            parameters.Add(p);

            p = new OracleParameter(":SYS_FLAG", OracleDbType.Decimal, 5);
            p.Value = model.SYS_FLAG;
            parameters.Add(p);

            p = new OracleParameter(":STATUS", OracleDbType.Decimal, 1);
            p.Value = model.STATUS;
            parameters.Add(p);

            p = new OracleParameter(":ROLEID", OracleDbType.Decimal, 5);
            p.Value = model.ROLEID;
            parameters.Add(p);

            p = new OracleParameter(":IP", OracleDbType.Varchar2, 90);
            p.Value = model.IP;
            parameters.Add(p);

            p = new OracleParameter(":AUTOGRAPH", OracleDbType.Blob);
            p.Value = model.AUTOGRAPH;
            parameters.Add(p);

            p = new OracleParameter(":COMMUNITY_CODE", OracleDbType.Varchar2, 26);
            p.Value = model.COMMUNITY_CODE;
            parameters.Add(p);

            p = new OracleParameter(":SEX", OracleDbType.Varchar2, 4);
            p.Value = model.SEX;
            parameters.Add(p);

            p = new OracleParameter(":ID_NO", OracleDbType.Varchar2, 18);
            p.Value = model.ID_NO;
            parameters.Add(p);

            p = new OracleParameter(":DEPT_CODE", OracleDbType.Varchar2, 8);
            p.Value = model.DEPT_CODE;
            parameters.Add(p);

            p = new OracleParameter(":NATION", OracleDbType.Varchar2, 10);
            p.Value = model.NATION;
            parameters.Add(p);

            p = new OracleParameter(":MAILING_ADDRESS", OracleDbType.Varchar2, 200);
            p.Value = model.MAILING_ADDRESS;
            parameters.Add(p);

            p = new OracleParameter(":WORK_ID", OracleDbType.Varchar2, 30);
            p.Value = model.WORK_ID;
            parameters.Add(p);

            p = new OracleParameter(":WORK_STATE", OracleDbType.Varchar2, 6);
            p.Value = model.WORK_STATE;
            parameters.Add(p);

            p = new OracleParameter(":FORMATION", OracleDbType.Varchar2, 6);
            p.Value = model.FORMATION;
            parameters.Add(p);

            p = new OracleParameter(":NAME", OracleDbType.Varchar2, 20);
            p.Value = model.NAME;
            parameters.Add(p);

            p = new OracleParameter(":INPUT_CODE", OracleDbType.Varchar2, 8);
            p.Value = model.INPUT_CODE;
            parameters.Add(p);

            p = new OracleParameter(":JOB", OracleDbType.Varchar2, 8);
            p.Value = model.JOB;
            parameters.Add(p);

            p = new OracleParameter(":TITLE", OracleDbType.Varchar2, 26);
            p.Value = model.TITLE;
            parameters.Add(p);

            p = new OracleParameter(":USER_NAME", OracleDbType.Varchar2, 16);
            p.Value = model.USER_NAME;
            parameters.Add(p);

            p = new OracleParameter(":INPUT_CODE_WB", OracleDbType.Varchar2, 8);
            p.Value = model.INPUT_CODE_WB;
            parameters.Add(p);

            p = new OracleParameter(":ID", OracleDbType.Varchar2, 5);
            p.Value = model.ID;
            parameters.Add(p);
            #endregion
            try
            {

                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.STAFF_DICT model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update STAFF_DICT set ");

            strSql.Append(" EMP_NO = :EMP_NO , ");
            strSql.Append(" CREATE_DATE = :CREATE_DATE , ");
            strSql.Append(" PASSWORD = :PASSWORD , ");
            strSql.Append(" SYS_FLAG = :SYS_FLAG , ");
            strSql.Append(" STATUS = :STATUS , ");
            strSql.Append(" ROLEID = :ROLEID , ");
            strSql.Append(" IP = :IP , ");
            strSql.Append(" AUTOGRAPH = :AUTOGRAPH , ");
            strSql.Append(" COMMUNITY_CODE = :COMMUNITY_CODE , ");
            strSql.Append(" SEX = :SEX , ");
            strSql.Append(" ID_NO = :ID_NO , ");
            strSql.Append(" DEPT_CODE = :DEPT_CODE , ");
            strSql.Append(" NATION = :NATION , ");
            strSql.Append(" MAILING_ADDRESS = :MAILING_ADDRESS , ");
            strSql.Append(" WORK_ID = :WORK_ID , ");
            strSql.Append(" WORK_STATE = :WORK_STATE , ");
            strSql.Append(" FORMATION = :FORMATION , ");
            strSql.Append(" NAME = :NAME , ");
            strSql.Append(" INPUT_CODE = :INPUT_CODE , ");
            strSql.Append(" JOB = :JOB , ");
            strSql.Append(" TITLE = :TITLE , ");
            strSql.Append(" USER_NAME = :USER_NAME , ");
            strSql.Append(" INPUT_CODE_WB = :INPUT_CODE_WB , ");
            strSql.Append(" ID = :ID  ");
            strSql.Append(" where ID=:ID  ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":EMP_NO", OracleDbType.Varchar2, 10);
            p.Value = model.EMP_NO;
            parameters.Add(p);

            p = new OracleParameter(":CREATE_DATE", OracleDbType.Date);
            p.Value = model.CREATE_DATE;
            parameters.Add(p);

            p = new OracleParameter(":PASSWORD", OracleDbType.Varchar2, 255);
            p.Value = model.PASSWORD;
            parameters.Add(p);

            p = new OracleParameter(":SYS_FLAG", OracleDbType.Decimal, 5);
            p.Value = model.SYS_FLAG;
            parameters.Add(p);

            p = new OracleParameter(":STATUS", OracleDbType.Decimal, 1);
            p.Value = model.STATUS;
            parameters.Add(p);

            p = new OracleParameter(":ROLEID", OracleDbType.Decimal, 5);
            p.Value = model.ROLEID;
            parameters.Add(p);

            p = new OracleParameter(":IP", OracleDbType.Varchar2, 90);
            p.Value = model.IP;
            parameters.Add(p);

            p = new OracleParameter(":AUTOGRAPH", OracleDbType.Blob);
            p.Value = model.AUTOGRAPH;
            parameters.Add(p);

            p = new OracleParameter(":COMMUNITY_CODE", OracleDbType.Varchar2, 26);
            p.Value = model.COMMUNITY_CODE;
            parameters.Add(p);

            p = new OracleParameter(":SEX", OracleDbType.Varchar2, 4);
            p.Value = model.SEX;
            parameters.Add(p);

            p = new OracleParameter(":ID_NO", OracleDbType.Varchar2, 18);
            p.Value = model.ID_NO;
            parameters.Add(p);

            p = new OracleParameter(":DEPT_CODE", OracleDbType.Varchar2, 8);
            p.Value = model.DEPT_CODE;
            parameters.Add(p);

            p = new OracleParameter(":NATION", OracleDbType.Varchar2, 10);
            p.Value = model.NATION;
            parameters.Add(p);

            p = new OracleParameter(":MAILING_ADDRESS", OracleDbType.Varchar2, 200);
            p.Value = model.MAILING_ADDRESS;
            parameters.Add(p);

            p = new OracleParameter(":WORK_ID", OracleDbType.Varchar2, 30);
            p.Value = model.WORK_ID;
            parameters.Add(p);

            p = new OracleParameter(":WORK_STATE", OracleDbType.Varchar2, 6);
            p.Value = model.WORK_STATE;
            parameters.Add(p);

            p = new OracleParameter(":FORMATION", OracleDbType.Varchar2, 6);
            p.Value = model.FORMATION;
            parameters.Add(p);

            p = new OracleParameter(":NAME", OracleDbType.Varchar2, 20);
            p.Value = model.NAME;
            parameters.Add(p);

            p = new OracleParameter(":INPUT_CODE", OracleDbType.Varchar2, 8);
            p.Value = model.INPUT_CODE;
            parameters.Add(p);

            p = new OracleParameter(":JOB", OracleDbType.Varchar2, 8);
            p.Value = model.JOB;
            parameters.Add(p);

            p = new OracleParameter(":TITLE", OracleDbType.Varchar2, 26);
            p.Value = model.TITLE;
            parameters.Add(p);

            p = new OracleParameter(":USER_NAME", OracleDbType.Varchar2, 16);
            p.Value = model.USER_NAME;
            parameters.Add(p);

            p = new OracleParameter(":INPUT_CODE_WB", OracleDbType.Varchar2, 8);
            p.Value = model.INPUT_CODE_WB;
            parameters.Add(p);

            p = new OracleParameter(":ID", OracleDbType.Varchar2, 5);
            p.Value = model.ID;
            parameters.Add(p);
            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string ID, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from STAFF_DICT ");
            strSql.Append(" where ID=:ID ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":ID", OracleDbType.Varchar2, 5);
            p.Value = ID;
            parameters.Add(p);

            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }



        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.STAFF_DICT GetModel(string ID, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select EMP_NO, CREATE_DATE, PASSWORD, SYS_FLAG, STATUS, ROLEID, IP, AUTOGRAPH, COMMUNITY_CODE, SEX, ID_NO, DEPT_CODE, NATION, MAILING_ADDRESS, WORK_ID, WORK_STATE, FORMATION, NAME, INPUT_CODE, JOB, TITLE, USER_NAME, INPUT_CODE_WB, ID  ");
            strSql.Append("  from STAFF_DICT ");
            strSql.Append(" where ID=:ID ");
            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":ID", OracleDbType.Varchar2, 5);
            p.Value = ID;
            parameters.Add(p);
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;
                    Model.STAFF_DICT model = new Model.STAFF_DICT();

                    if (cmdresult > 0)
                    {
                        model = CopyToModel(ds.Tables[0].Rows[0]);
                        return model;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM STAFF_DICT ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得几行数据
        /// </summary>
        public DataSet GetList(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            #region 初始化参数
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM STAFF_DICT T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }
            if (strWhere.Trim() != "")
            {
                strSql.Append("  and " + strWhere);
            }
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.STAFF_DICT> GetObservableCollection(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM STAFF_DICT ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.STAFF_DICT> list = new System.Collections.ObjectModel.ObservableCollection<Model.STAFF_DICT>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.STAFF_DICT model = new Model.STAFF_DICT();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表 
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.STAFF_DICT> GetObservableCollection(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM STAFF_DICT T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }

            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.STAFF_DICT> list = new System.Collections.ObjectModel.ObservableCollection<Model.STAFF_DICT>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.STAFF_DICT model = new Model.STAFF_DICT();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion   Method
        #region
        /// <summary>
        /// 
        /// </summary>
        protected Model.STAFF_DICT CopyToModel(DataRow dRow)
        {
            Model.STAFF_DICT model1 = new Model.STAFF_DICT();

            if (dRow["EMP_NO"] != null && dRow["EMP_NO"].ToString() != "")
            {
                model1.EMP_NO = dRow["EMP_NO"].ToString();
            }

            if (dRow["CREATE_DATE"] != null && dRow["CREATE_DATE"].ToString() != "")
            {
                model1.CREATE_DATE = DateTime.Parse(dRow["CREATE_DATE"].ToString());
            }

            if (dRow["PASSWORD"] != null && dRow["PASSWORD"].ToString() != "")
            {
                model1.PASSWORD = dRow["PASSWORD"].ToString();
            }

            if (dRow["SYS_FLAG"] != null && dRow["SYS_FLAG"].ToString() != "")
            {
                model1.SYS_FLAG = decimal.Parse(dRow["SYS_FLAG"].ToString());
            }

            if (dRow["STATUS"] != null && dRow["STATUS"].ToString() != "")
            {
                model1.STATUS = decimal.Parse(dRow["STATUS"].ToString());
            }

            if (dRow["ROLEID"] != null && dRow["ROLEID"].ToString() != "")
            {
                model1.ROLEID = decimal.Parse(dRow["ROLEID"].ToString());
            }

            if (dRow["IP"] != null && dRow["IP"].ToString() != "")
            {
                model1.IP = dRow["IP"].ToString();
            }

            if (dRow["AUTOGRAPH"] != null && dRow["AUTOGRAPH"].ToString() != "")
            {
                model1.AUTOGRAPH = (byte[])dRow["AUTOGRAPH"];
            }

            if (dRow["COMMUNITY_CODE"] != null && dRow["COMMUNITY_CODE"].ToString() != "")
            {
                model1.COMMUNITY_CODE = dRow["COMMUNITY_CODE"].ToString();
            }

            if (dRow["SEX"] != null && dRow["SEX"].ToString() != "")
            {
                model1.SEX = dRow["SEX"].ToString();
            }

            if (dRow["ID_NO"] != null && dRow["ID_NO"].ToString() != "")
            {
                model1.ID_NO = dRow["ID_NO"].ToString();
            }

            if (dRow["DEPT_CODE"] != null && dRow["DEPT_CODE"].ToString() != "")
            {
                model1.DEPT_CODE = dRow["DEPT_CODE"].ToString();
            }
            if(dRow["DEPT_NAME"] != null && dRow["DEPT_NAME"].ToString() != "")
            {
                model1.DEPT_NAME = dRow["DEPT_NAME"].ToString();
            }

            if (dRow["NATION"] != null && dRow["NATION"].ToString() != "")
            {
                model1.NATION = dRow["NATION"].ToString();
            }

            if (dRow["MAILING_ADDRESS"] != null && dRow["MAILING_ADDRESS"].ToString() != "")
            {
                model1.MAILING_ADDRESS = dRow["MAILING_ADDRESS"].ToString();
            }

            if (dRow["WORK_ID"] != null && dRow["WORK_ID"].ToString() != "")
            {
                model1.WORK_ID = dRow["WORK_ID"].ToString();
            }

            if (dRow["WORK_STATE"] != null && dRow["WORK_STATE"].ToString() != "")
            {
                model1.WORK_STATE = dRow["WORK_STATE"].ToString();
            }

            if (dRow["FORMATION"] != null && dRow["FORMATION"].ToString() != "")
            {
                model1.FORMATION = dRow["FORMATION"].ToString();
            }

            if (dRow["NAME"] != null && dRow["NAME"].ToString() != "")
            {
                model1.NAME = dRow["NAME"].ToString();
            }

            if (dRow["INPUT_CODE"] != null && dRow["INPUT_CODE"].ToString() != "")
            {
                model1.INPUT_CODE = dRow["INPUT_CODE"].ToString();
            }

            if (dRow["JOB"] != null && dRow["JOB"].ToString() != "")
            {
                model1.JOB = dRow["JOB"].ToString();
            }

            if (dRow["TITLE"] != null && dRow["TITLE"].ToString() != "")
            {
                model1.TITLE = dRow["TITLE"].ToString();
            }

            if (dRow["USER_NAME"] != null && dRow["USER_NAME"].ToString() != "")
            {
                model1.USER_NAME = dRow["USER_NAME"].ToString();
            }

            if (dRow["INPUT_CODE_WB"] != null && dRow["INPUT_CODE_WB"].ToString() != "")
            {
                model1.INPUT_CODE_WB = dRow["INPUT_CODE_WB"].ToString();
            }

            if (dRow["ID"] != null && dRow["ID"].ToString() != "")
            {
                model1.ID = dRow["ID"].ToString();
            }
            //增加医院编号 张鹏20170614
            if (dRow["HIS_UNIT_CODE"] != null && dRow["HIS_UNIT_CODE"].ToString() != "")
            {
                model1.HISUNITCODE = dRow["HIS_UNIT_CODE"].ToString();
            }
            return model1;
        }

        protected Model.STAFF_DICT CopyToModel(DataRow dRow, bool isHisUnitCode = true)
        {
            Model.STAFF_DICT model1 = new Model.STAFF_DICT();

            if (dRow["EMP_NO"] != null && dRow["EMP_NO"].ToString() != "")
            {
                model1.EMP_NO = dRow["EMP_NO"].ToString();
            }

            if (dRow["CREATE_DATE"] != null && dRow["CREATE_DATE"].ToString() != "")
            {
                model1.CREATE_DATE = DateTime.Parse(dRow["CREATE_DATE"].ToString());
            }

            if (dRow["PASSWORD"] != null && dRow["PASSWORD"].ToString() != "")
            {
                model1.PASSWORD = dRow["PASSWORD"].ToString();
            }

            if (dRow["SYS_FLAG"] != null && dRow["SYS_FLAG"].ToString() != "")
            {
                model1.SYS_FLAG = decimal.Parse(dRow["SYS_FLAG"].ToString());
            }

            if (dRow["STATUS"] != null && dRow["STATUS"].ToString() != "")
            {
                model1.STATUS = decimal.Parse(dRow["STATUS"].ToString());
            }

            if (dRow["ROLEID"] != null && dRow["ROLEID"].ToString() != "")
            {
                model1.ROLEID = decimal.Parse(dRow["ROLEID"].ToString());
            }

            if (dRow["IP"] != null && dRow["IP"].ToString() != "")
            {
                model1.IP = dRow["IP"].ToString();
            }

            if (dRow["AUTOGRAPH"] != null && dRow["AUTOGRAPH"].ToString() != "")
            {
                model1.AUTOGRAPH = (byte[])dRow["AUTOGRAPH"];
            }

            if (dRow["COMMUNITY_CODE"] != null && dRow["COMMUNITY_CODE"].ToString() != "")
            {
                model1.COMMUNITY_CODE = dRow["COMMUNITY_CODE"].ToString();
            }

            if (dRow["SEX"] != null && dRow["SEX"].ToString() != "")
            {
                model1.SEX = dRow["SEX"].ToString();
            }

            if (dRow["ID_NO"] != null && dRow["ID_NO"].ToString() != "")
            {
                model1.ID_NO = dRow["ID_NO"].ToString();
            }

            if (dRow["DEPT_CODE"] != null && dRow["DEPT_CODE"].ToString() != "")
            {
                model1.DEPT_CODE = dRow["DEPT_CODE"].ToString();
            }

            if (dRow["NATION"] != null && dRow["NATION"].ToString() != "")
            {
                model1.NATION = dRow["NATION"].ToString();
            }

            if (dRow["MAILING_ADDRESS"] != null && dRow["MAILING_ADDRESS"].ToString() != "")
            {
                model1.MAILING_ADDRESS = dRow["MAILING_ADDRESS"].ToString();
            }

            if (dRow["WORK_ID"] != null && dRow["WORK_ID"].ToString() != "")
            {
                model1.WORK_ID = dRow["WORK_ID"].ToString();
            }

            if (dRow["WORK_STATE"] != null && dRow["WORK_STATE"].ToString() != "")
            {
                model1.WORK_STATE = dRow["WORK_STATE"].ToString();
            }

            if (dRow["FORMATION"] != null && dRow["FORMATION"].ToString() != "")
            {
                model1.FORMATION = dRow["FORMATION"].ToString();
            }

            if (dRow["NAME"] != null && dRow["NAME"].ToString() != "")
            {
                model1.NAME = dRow["NAME"].ToString();
            }

            if (dRow["INPUT_CODE"] != null && dRow["INPUT_CODE"].ToString() != "")
            {
                model1.INPUT_CODE = dRow["INPUT_CODE"].ToString();
            }

            if (dRow["JOB"] != null && dRow["JOB"].ToString() != "")
            {
                model1.JOB = dRow["JOB"].ToString();
            }

            if (dRow["TITLE"] != null && dRow["TITLE"].ToString() != "")
            {
                model1.TITLE = dRow["TITLE"].ToString();
            }

            if (dRow["USER_NAME"] != null && dRow["USER_NAME"].ToString() != "")
            {
                model1.USER_NAME = dRow["USER_NAME"].ToString();
            }

            if (dRow["INPUT_CODE_WB"] != null && dRow["INPUT_CODE_WB"].ToString() != "")
            {
                model1.INPUT_CODE_WB = dRow["INPUT_CODE_WB"].ToString();
            }

            if (dRow["ID"] != null && dRow["ID"].ToString() != "")
            {
                model1.ID = dRow["ID"].ToString();
            }
            //增加医院编号 张鹏20170614
            if (isHisUnitCode && dRow["HIS_UNIT_CODE"] != null && dRow["HIS_UNIT_CODE"].ToString() != "")
            {
                model1.HISUNITCODE = dRow["HIS_UNIT_CODE"].ToString();
            }
            return model1;
        }
        #endregion

    }
}

