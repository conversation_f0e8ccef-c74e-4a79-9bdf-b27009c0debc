﻿/*-----------------------------------------------------------------------
 * 类名称    ：角色组菜单服务数据库操作类
 * 功能      ：角色组信息编辑和组菜单权限编辑
 * 创建人    ：梁吉
 * 创建时间  ：2016-05-10
 * ----------------------------------------------------------------------
 */
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace OracleDAL
{
    public class RoleMantain_Dao
    {
        /// <summary>
        /// 删除主表及明细
        /// </summary>
        /// <param name="APPLICATION_CODE"></param>
        /// <param name="RIGHT_GROUP_CODE"></param>
        /// <returns></returns>
        public bool Delete(string APPLICATION_CODE, string RIGHT_GROUP_CODE)
        {
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    bool ret;
                    db.BeginTransaction();
                    ret = new SEC_RIGHT_GROUP_Dao().Delete(APPLICATION_CODE, RIGHT_GROUP_CODE, db);
                    if (ret)
                    {
                        SEC_RIGHT_GROUP_VS_MENUS_Dao gvm = new SEC_RIGHT_GROUP_VS_MENUS_Dao();
                        //存在组菜单，则删除，否则不用删除了
                        ret = ret & new SEC_RIGHT_GROUP_VS_MENUS_Dao().Delete(APPLICATION_CODE, RIGHT_GROUP_CODE, db);
                        //System.Data.DataSet ds=gvm.GetList("APPLICATION_CODE='" + APPLICATION_CODE + "' AND " + "RIGHT_GROUP_CODE='" + RIGHT_GROUP_CODE + "'", db);
                        //if (ds != null && ds.Tables[0].Rows.Count > 0)
                        //{
                        //    //存在组菜单，则删除，否则不用删除了
                        //    ret = ret & new SEC_RIGHT_GROUP_VS_MENUS_Dao().Delete(APPLICATION_CODE, RIGHT_GROUP_CODE, db);
                        //}                        
                    }
                    if (ret)
                        db.CommitTransaction();
                    
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    Utility.LogFile.WriteLogAuto("数据库连接失败！", "ServerPublicDao");
                    return false;
                }

            }

        }
    }
}
