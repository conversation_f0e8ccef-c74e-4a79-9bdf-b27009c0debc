﻿using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System;
using System.Collections;
using System.Data;
using System.Windows.Forms;
using Tjhis.Report.Custom.Common;
using Tjhis.Report.Custom.Srv;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmParamEdit : ParentForm
    {
        private srvStatisticalQuery srv;
        public DataRow drParam { get; set; }
        private DataTable _dt1;
        public frmParamEdit(DataRow dr)
        {
            InitializeComponent();
            this.drParam = dr;
        }

        public frmParamEdit(DataRow dr,DataTable dt1)
        {
            InitializeComponent();
            this.drParam = dr;
            this._dt1 = dt1;
        }

        /// <summary>
        /// Load事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void frmParamEdit_Load(object sender, EventArgs e)
        {
            srv = new srvStatisticalQuery();
            BindParamData();
            lupControlType.Properties.DataSource = srvStatisticalQuery.CreateDict(Const.CONTROL_TYPES);
            lupSourceType.Properties.DataSource = srvStatisticalQuery.CreateDict("1-SQL脚本;2-自定义选项");
        }
        /// <summary>
        /// 绑定数据
        /// </summary>
        void BindParamData()
        {
            if (drParam == null) return;

            txtSrialNo.Text = drParam["serial_no"].ToString();
            txtParamName.Text = drParam["param_name"].ToString();
            lupControlType.EditValue = drParam["edit_type"].ToString();
            txtCaption.Text = drParam["caption"].ToString();

            lupSourceType.EditValue = drParam["source_type"].ToString();
            mtxtSql.Text = drParam["data_source_sql"].ToString();
            SqlTestInitMember(mtxtSql.Text, lupSourceType.EditValue.ToString());

            lupDisplay.EditValue = drParam["display_member"].ToString();
            lupValue.EditValue = drParam["value_member"].ToString();
            txtDefaultValue.Text = drParam["default_value"].ToString();
            txtDateFormat.Text = drParam["DATE_FORMAT"].ToString();
            txtNextParam.Text = drParam["NEXT_PARAM_NAME"].ToString();

            decimal width = 100;
            decimal.TryParse(drParam["control_width"].ToString(), out width);
            spControlWidth.Value = width == 0 ? 100 : width;
        }

        /// <summary>
        /// 测试脚本内容 
        /// </summary>
        /// <param name="sql">脚本内容</param>
        /// <param name="editType">脚本类型 （1 => sql ; 0 => 自定义脚本）</param>
        void SqlTestInitMember(string sql,string editType)
        {
            if (string.IsNullOrEmpty(sql)) return;
            try
            {
                DataSet ds = null;
                if ("1".Equals(editType))
                {
                    Hashtable hasParam = srv.AddSystemParam();                                
                    //sql = $"select * from ({sql}) where rownum < 2";
                    ds = srv.TestSQL(sql, hasParam);
                }
                else
                {
                    ds = new DataSet();
                    ds.Tables.Add(srvStatisticalQuery.CreateDict(sql));
                }

                if (ds != null)
                {
                    string coustomDictStr = string.Empty;
                    foreach(DataColumn dc in ds.Tables[0].Columns)
                    {
                        coustomDictStr += dc.ColumnName+"-" +dc.ColumnName+";";
                    }
                    DataTable dsCuostomDict = srvStatisticalQuery.CreateDict(coustomDictStr);

                    lupDisplay.Properties.DataSource = dsCuostomDict;
                    lupValue.Properties.DataSource = dsCuostomDict;
                }
                else
                {
                    XtraMessageBox.Show("请检查脚本是否正确！");
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 脚本提交
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn_commit_Click(object sender, EventArgs e)
        {
            SqlTestInitMember(mtxtSql.Text, lupSourceType.EditValue.ToString());
        }

        /// <summary>
        /// 确认并提交数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn_ok_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtParamName.Text.Trim()))
            {
                MessageBox.Show("参数名称不能为空！", "系统提示");
                txtParamName.Focus();
                return;
            }
            if (string.IsNullOrEmpty(lupControlType.EditValue.ToString()))
            {
                MessageBox.Show("请选择控件类型！","系统提示");
                lupControlType.Focus();
                return;
            }
            if (_dt1 != null)
            {
                foreach (DataRow dr1 in _dt1.Rows)
                {
                    if (dr1.RowState != DataRowState.Deleted && dr1["PARAM_NAME"].ToString() == txtParamName.Text.Trim())
                    {
                        MessageBox.Show("此参数名称已经存在！", "系统提示");
                        txtParamName.Focus();
                        return;
                    }
                }
            }

            //====判断时间类型的默认值=====
            if (lupControlType.EditValue.ToString() == "3" && txtDefaultValue.Text.Trim().Length > 0)//日期类型并且设置了默认值
            {
                Tuple<bool, DateTime> ret = new Base.DefaultDateTimeValueProvider().GetDefaultDateTime(txtDefaultValue.Text.Trim());
                if (!ret.Item1)
                {
                    XtraMessageBox.Show("请输入正确的时间类型的默认值！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
            }

            int iSerialNo = 0;
            int.TryParse(txtSrialNo.Text.Trim(), out iSerialNo);
            drParam["serial_no"] = iSerialNo;
            drParam["param_name"] = txtParamName.Text;
            drParam["edit_type"] = lupControlType.EditValue;
            drParam["caption"] = txtCaption.Text.Trim();
            drParam["data_source_sql"] = mtxtSql.Text.Trim();
            drParam["value_member"] = lupValue.EditValue;
            drParam["display_member"] = lupDisplay.EditValue;
            drParam["source_type"] = lupSourceType.EditValue;
            drParam["default_value"] = txtDefaultValue.Text.Trim();
            drParam["DATE_FORMAT"] = txtDateFormat.Text.Trim();
            drParam["control_width"] = spControlWidth.Value;
            drParam["next_param_name"] = txtNextParam.Text.Trim();
            this.DialogResult = DialogResult.OK;
        }

        /// <summary>
        /// 取消并退出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn_cancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
        
        /// <summary>
        /// 脚本类型控件数值切换
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lupControlType_EditValueChanged(object sender, EventArgs e)
        {
            if (lupControlType.EditValue.Equals("1")|| lupControlType.EditValue.Equals("5"))
            {
                lupSourceTypeEdit.Enabled = true;
                lupDisplayEdit.Enabled = true;
                mtxtSql.Enabled = true;
                lupValueEdit.Enabled = true;
                btn_commit.Enabled = true;
                txtNextParam.Enabled = true;
            }
            else
            {
                lupSourceTypeEdit.Enabled = false;
                lupDisplayEdit.Enabled = false;
                mtxtSql.Enabled = false;
                lupValueEdit.Enabled = false;
                txtNextParam.Enabled = false;
                btn_commit.Enabled = false;
            }
          
            if (lupControlType.EditValue.Equals("3"))
            {
                layItemDateFormat.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always;
            }
            else
            {
                txtDateFormat.Text = null;
                layItemDateFormat.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;
            }
        }

        private void txtParamName_Properties_EditValueChanged(object sender, EventArgs e)
        {
            if(!string.IsNullOrEmpty(txtParamName.Text))
                txtParamName.Text = txtParamName.Text.ToUpper();
        }

        private void txtDateFormat_MouseHover(object sender, EventArgs e)
        {
            ToolTip toolTip1 = new ToolTip();
            toolTip1.ShowAlways = true;
            toolTip1.SetToolTip(this.txtDateFormat, "日期默认格式为 yyyy-MM-dd");
        }
    }
}
