﻿namespace TJHisPlat
{
    partial class FrmSelectOutpDoctorS
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.simpleButton1 = new DevExpress.XtraEditors.SimpleButton();
            this.gridControl2 = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.DOCTOR = new DevExpress.XtraGrid.Columns.GridColumn();
            this.QUEUE_NAME = new DevExpress.XtraGrid.Columns.GridColumn();
            this.CONSEL_DATE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.CLINIC_DURATION = new DevExpress.XtraGrid.Columns.GridColumn();
            this.AUTO_ASSIGN_PATIENT = new DevExpress.XtraGrid.Columns.GridColumn();
            this.LEAVE_INDICATOR = new DevExpress.XtraGrid.Columns.GridColumn();
            this.LEAVE_TIME = new DevExpress.XtraGrid.Columns.GridColumn();
            this.COUNSELED_NUM = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ADDRESS = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            this.SuspendLayout();
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.simpleButton1);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelControl1.Location = new System.Drawing.Point(0, 347);
            this.panelControl1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(503, 62);
            this.panelControl1.TabIndex = 0;
            // 
            // simpleButton1
            // 
            this.simpleButton1.Location = new System.Drawing.Point(330, 8);
            this.simpleButton1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.simpleButton1.Name = "simpleButton1";
            this.simpleButton1.Size = new System.Drawing.Size(168, 48);
            this.simpleButton1.TabIndex = 0;
            this.simpleButton1.Text = "确定";
            this.simpleButton1.Click += new System.EventHandler(this.simpleButton1_Click);
            // 
            // gridControl2
            // 
            this.gridControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl2.EmbeddedNavigator.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.gridControl2.Location = new System.Drawing.Point(0, 0);
            this.gridControl2.MainView = this.gridView2;
            this.gridControl2.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.gridControl2.Name = "gridControl2";
            this.gridControl2.Size = new System.Drawing.Size(503, 347);
            this.gridControl2.TabIndex = 1;
            this.gridControl2.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            this.gridControl2.DoubleClick += new System.EventHandler(this.gridControl2_DoubleClick);
            // 
            // gridView2
            // 
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.DOCTOR,
            this.QUEUE_NAME,
            this.CONSEL_DATE,
            this.CLINIC_DURATION,
            this.AUTO_ASSIGN_PATIENT,
            this.LEAVE_INDICATOR,
            this.LEAVE_TIME,
            this.COUNSELED_NUM,
            this.ADDRESS,
            this.gridColumn1,
            this.gridColumn2});
            this.gridView2.DetailHeight = 450;
            this.gridView2.FixedLineWidth = 3;
            this.gridView2.GridControl = this.gridControl2;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // DOCTOR
            // 
            this.DOCTOR.AppearanceHeader.Options.UseTextOptions = true;
            this.DOCTOR.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.DOCTOR.Caption = "医生姓名";
            this.DOCTOR.FieldName = "DOCTOR";
            this.DOCTOR.MinWidth = 23;
            this.DOCTOR.Name = "DOCTOR";
            this.DOCTOR.OptionsColumn.AllowEdit = false;
            this.DOCTOR.OptionsColumn.AllowFocus = false;
            this.DOCTOR.Visible = true;
            this.DOCTOR.VisibleIndex = 0;
            this.DOCTOR.Width = 294;
            // 
            // QUEUE_NAME
            // 
            this.QUEUE_NAME.AppearanceHeader.Options.UseTextOptions = true;
            this.QUEUE_NAME.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.QUEUE_NAME.Caption = "分诊号别";
            this.QUEUE_NAME.FieldName = "QUEUE_NAME";
            this.QUEUE_NAME.MinWidth = 23;
            this.QUEUE_NAME.Name = "QUEUE_NAME";
            this.QUEUE_NAME.OptionsColumn.AllowEdit = false;
            this.QUEUE_NAME.OptionsColumn.AllowFocus = false;
            this.QUEUE_NAME.Visible = true;
            this.QUEUE_NAME.VisibleIndex = 1;
            this.QUEUE_NAME.Width = 478;
            // 
            // CONSEL_DATE
            // 
            this.CONSEL_DATE.AppearanceHeader.Options.UseTextOptions = true;
            this.CONSEL_DATE.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.CONSEL_DATE.Caption = "坐诊时间";
            this.CONSEL_DATE.FieldName = "CONSEL_DATE";
            this.CONSEL_DATE.MinWidth = 23;
            this.CONSEL_DATE.Name = "CONSEL_DATE";
            this.CONSEL_DATE.OptionsColumn.AllowEdit = false;
            this.CONSEL_DATE.OptionsColumn.AllowFocus = false;
            this.CONSEL_DATE.Visible = true;
            this.CONSEL_DATE.VisibleIndex = 2;
            this.CONSEL_DATE.Width = 235;
            // 
            // CLINIC_DURATION
            // 
            this.CLINIC_DURATION.AppearanceHeader.Options.UseTextOptions = true;
            this.CLINIC_DURATION.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.CLINIC_DURATION.Caption = "时段";
            this.CLINIC_DURATION.FieldName = "CLINIC_DURATION";
            this.CLINIC_DURATION.MinWidth = 23;
            this.CLINIC_DURATION.Name = "CLINIC_DURATION";
            this.CLINIC_DURATION.OptionsColumn.AllowEdit = false;
            this.CLINIC_DURATION.OptionsColumn.AllowFocus = false;
            this.CLINIC_DURATION.Visible = true;
            this.CLINIC_DURATION.VisibleIndex = 3;
            this.CLINIC_DURATION.Width = 225;
            // 
            // AUTO_ASSIGN_PATIENT
            // 
            this.AUTO_ASSIGN_PATIENT.AppearanceHeader.Options.UseTextOptions = true;
            this.AUTO_ASSIGN_PATIENT.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.AUTO_ASSIGN_PATIENT.Caption = "自动";
            this.AUTO_ASSIGN_PATIENT.FieldName = "AUTO_ASSIGN_PATIENT";
            this.AUTO_ASSIGN_PATIENT.MinWidth = 23;
            this.AUTO_ASSIGN_PATIENT.Name = "AUTO_ASSIGN_PATIENT";
            this.AUTO_ASSIGN_PATIENT.OptionsColumn.AllowEdit = false;
            this.AUTO_ASSIGN_PATIENT.OptionsColumn.AllowFocus = false;
            this.AUTO_ASSIGN_PATIENT.Width = 48;
            // 
            // LEAVE_INDICATOR
            // 
            this.LEAVE_INDICATOR.AppearanceHeader.Options.UseTextOptions = true;
            this.LEAVE_INDICATOR.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.LEAVE_INDICATOR.Caption = "签退";
            this.LEAVE_INDICATOR.FieldName = "LEAVE_INDICATOR";
            this.LEAVE_INDICATOR.MinWidth = 23;
            this.LEAVE_INDICATOR.Name = "LEAVE_INDICATOR";
            this.LEAVE_INDICATOR.OptionsColumn.AllowEdit = false;
            this.LEAVE_INDICATOR.OptionsColumn.AllowFocus = false;
            this.LEAVE_INDICATOR.Width = 64;
            // 
            // LEAVE_TIME
            // 
            this.LEAVE_TIME.AppearanceHeader.Options.UseTextOptions = true;
            this.LEAVE_TIME.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.LEAVE_TIME.Caption = "签退时间";
            this.LEAVE_TIME.FieldName = "LEAVE_TIME";
            this.LEAVE_TIME.MinWidth = 23;
            this.LEAVE_TIME.Name = "LEAVE_TIME";
            this.LEAVE_TIME.OptionsColumn.AllowEdit = false;
            this.LEAVE_TIME.OptionsColumn.AllowFocus = false;
            this.LEAVE_TIME.Width = 86;
            // 
            // COUNSELED_NUM
            // 
            this.COUNSELED_NUM.Caption = "已分诊人数";
            this.COUNSELED_NUM.FieldName = "COUNSELED_NUM";
            this.COUNSELED_NUM.MinWidth = 23;
            this.COUNSELED_NUM.Name = "COUNSELED_NUM";
            this.COUNSELED_NUM.OptionsColumn.AllowEdit = false;
            this.COUNSELED_NUM.OptionsColumn.AllowFocus = false;
            this.COUNSELED_NUM.Width = 86;
            // 
            // ADDRESS
            // 
            this.ADDRESS.AppearanceHeader.Options.UseTextOptions = true;
            this.ADDRESS.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.ADDRESS.Caption = "坐诊地址";
            this.ADDRESS.FieldName = "ADDRESS";
            this.ADDRESS.MinWidth = 23;
            this.ADDRESS.Name = "ADDRESS";
            this.ADDRESS.OptionsColumn.AllowEdit = false;
            this.ADDRESS.OptionsColumn.AllowFocus = false;
            this.ADDRESS.Width = 86;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "科室名称";
            this.gridColumn1.FieldName = "DEPT_NAME";
            this.gridColumn1.MinWidth = 23;
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowEdit = false;
            this.gridColumn1.OptionsColumn.AllowFocus = false;
            this.gridColumn1.Width = 86;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "科室编码";
            this.gridColumn2.FieldName = "CLINIC_DEPT";
            this.gridColumn2.MinWidth = 23;
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Width = 86;
            // 
            // FrmSelectOutpDoctorS
            // 
            this.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(239)))));
            this.Appearance.Options.UseBackColor = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(503, 409);
            this.ControlBox = false;
            this.Controls.Add(this.gridControl2);
            this.Controls.Add(this.panelControl1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FrmSelectOutpDoctorS";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "医生坐诊列表";
            this.Load += new System.EventHandler(this.FrmSelectOutpDoctorS_Load);
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.SimpleButton simpleButton1;
        private DevExpress.XtraGrid.GridControl gridControl2;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn DOCTOR;
        private DevExpress.XtraGrid.Columns.GridColumn QUEUE_NAME;
        private DevExpress.XtraGrid.Columns.GridColumn CONSEL_DATE;
        private DevExpress.XtraGrid.Columns.GridColumn CLINIC_DURATION;
        private DevExpress.XtraGrid.Columns.GridColumn AUTO_ASSIGN_PATIENT;
        private DevExpress.XtraGrid.Columns.GridColumn LEAVE_INDICATOR;
        private DevExpress.XtraGrid.Columns.GridColumn LEAVE_TIME;
        private DevExpress.XtraGrid.Columns.GridColumn COUNSELED_NUM;
        private DevExpress.XtraGrid.Columns.GridColumn ADDRESS;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
    }
}