﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace PlatCommonForm
{
    /// <summary>
    /// 法律法规界面
    /// </summary>
    public partial class FrmReadLaw : PlatCommon.SysBase.ParentForm
    {
       
        public FrmReadLaw()
        {
            InitializeComponent();
        }
        DataTable dt = new DataTable();
        private void FrmReadLaw_Load(object sender, EventArgs e)
        {
            dt.Columns.Add(new DataColumn("KEY", typeof(string)));
            dt.Columns.Add(new DataColumn("PARENT", typeof(string)));
            dt.Columns.Add(new DataColumn("NAME", typeof(string)));
            dt.Columns.Add(new DataColumn("FULLNAME", typeof(string)));
            //string path = @"D:\政策法规";
            string path = "";
            path = System.IO.Directory.GetCurrentDirectory()+ "\\政策法规";
            DirectoryInfo root = new DirectoryInfo(path);
            dt.Rows.Add(new object[] { "0", "0", root.Name, root.FullName });
            int num = 1;
            foreach (FileInfo f in root.GetFiles("*.pdf"))
            {
                dt.Rows.Add(new object[] { num.ToString(), "0", f.Name, f.FullName });
                num++;
            }
            treeList1.DataSource = dt;
            treeList1.KeyFieldName = "KEY";
            treeList1.ParentFieldName = "PARENT";
            treeList1.ExpandAll();
        }

        private void treeList1_FocusedNodeChanged(object sender, DevExpress.XtraTreeList.FocusedNodeChangedEventArgs e)
        {
            if (e.Node != null && e.Node.Level>0)
            {
                string path = treeList1.GetFocusedRowCellValue(treeListColumn2).ToString();
                //如果不为空
                if (!string.IsNullOrEmpty(path))
                {
                    //加载预览  其中pdfViewer1 与控件的name相对应
                    this.pdfViewer1.LoadDocument(path);
                }
            }
        }
    }
}
