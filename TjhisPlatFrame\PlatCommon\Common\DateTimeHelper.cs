﻿/*********************************************
* 文 件 名：Cs02DataSetHelper
* 类 名 称：Cs02DataSetHelper
* 功能说明：DataSet操作类
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：付军
* 创建时间：2016-10-28
* 版 本 号：1.0.0.1
* 修改时间：尹志伟
* 修 改 人：2018-06-12
* CLR 版本：4.0.30319.42000
/*********************************************/

using PlatCommon.Comm;
using System;
using System.Data;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;

namespace PlatCommon.Common
{
    /// <summary>
    /// DateTime操作类
    /// </summary>
    public class DateTimeHelper
    {
        /// <summary>
        /// 计算本周起始日期（礼拜一的日期）
        /// </summary>
        /// <param name="someDate">该周中任意一天</param>
        /// <returns>返回礼拜一日期，后面的具体时、分、秒和传入值相等</returns>
        public static DateTime GetFirstDateOfWeek(DateTime someDate)
        {
            someDate = someDate.Date;
            int i = someDate.DayOfWeek - DayOfWeek.Monday;
            if (i == -1) i = 6;// i值 > = 0 ，因为枚举原因，Sunday排在最前，此时Sunday-Monday=-1，必须+7=6。
            TimeSpan ts = new TimeSpan(i, 0, 0, 0);
            return someDate.Subtract(ts);
        }


        /// <summary>
        /// 计算本周结束日期（礼拜日的日期）
        /// </summary>
        /// <param name="someDate">该周中任意一天</param>
        /// <returns>返回礼拜日日期，后面的具体时、分、秒和传入值相等</returns>
        public static DateTime GetLastDateOfWeek(DateTime someDate)
        {
            int i = someDate.DayOfWeek - DayOfWeek.Sunday;
            if (i != 0) i = 7 - i;// 因为枚举原因，Sunday排在最前，相减间隔要被7减。
            TimeSpan ts = new TimeSpan(i, 0, 0, 0);
            return someDate.Add(ts);
        }
        #region 返回当月第一天和最后一天  fza 2017-08-26
        /// <summary>
        /// 返回当月第一天
        /// </summary>
        /// <param name="someDate"></param>
        /// <returns></returns>
        public static DateTime GetFirstDateOfMonth(DateTime someDate)
        {
            DateTime d1 = new DateTime(someDate.Year, someDate.Month, 1);
            return d1;
        }
        /// <summary>
        /// 返回当月最后一天
        /// </summary>
        /// <param name="someDate"></param>
        /// <returns></returns>
        public static DateTime GetLastDateOfMonth(DateTime someDate)
        {
            DateTime d1 = new DateTime(someDate.Year, someDate.Month, 1);
            DateTime d2 = d1.AddMonths(1).AddDays(-1);
            return d2;
        }
        #endregion
        /// <summary>
        /// 对象转换为DateTime?
        /// </summary>
        /// <param name="objDate"></param>
        /// <returns></returns>
        public static DateTime? Parse(object objDate)
        {
            if (objDate == null || objDate == DBNull.Value)
            {
                return null;
            }

            DateTime? dt = objDate as DateTime?;
            if (dt != null) return dt;

            DateTime dtDate;
            if (DateTime.TryParse(objDate.ToString(), out dtDate) == true)
            {
                return dtDate;
            }

            return null;
        }

        #region 数据类型
        /// <summary>
        /// 获取DateTime的Null值
        /// </summary>
        /// <remarks>假定一个"1-1-1 12:00:00"值作为Null值</remarks>
        /// <returns>DateTime</returns>
        public static DateTime DateTime_Null()
        {
            return new DateTime(1700, 1, 1);
        }


        /// <summary>
        /// 判断DateTime是否为Null
        /// </summary>
        /// <param name="dt">DateTime</param>
        /// <returns>TRUE: 是为Null; FALSE: 不为Null</returns>
        public static bool DateTime_IsNull(ref DateTime dt)
        {
            return DateTime_Null().Date.Equals(dt.Date);
        }


        /// <summary>
        /// 把字符串转换成为日期
        /// </summary>
        /// <param name="strDateTime">日期字符串</param>
        /// <returns>日期</returns>
        public static DateTime DateTimeFromString(string strDateTime)
        {
            if (strDateTime.Trim().Length == 0)
            {
                return DateTime_Null();
            }
            else
            {
                return DateTime.Parse(strDateTime);
            }
        }

        /// <summary>
        /// 将字符串转成日期
        /// </summary>
        /// <param name="strDate"></param>
        /// <returns></returns>
        public static DateTime GetDateTime(string strDate)
        {
            DateTime dt = DateTime.MinValue;
            DateTime.TryParse(strDate, out dt);
            return dt;
        }

        /// <summary>
        /// 将对象转换为日期
        /// </summary>
        /// <param name="objValue"></param>
        /// <returns></returns>
        public static DateTime? GetDateTime(object objValue)
        {
            if (objValue == null || objValue == DBNull.Value)
            {
                return null;
            }

            return new DateTime?(Convert.ToDateTime(objValue));
        }

        /// <summary>
        /// 获取时间
        /// </summary>
        /// <param name="dtDay">指定日期</param>
        /// <param name="timeStr">时分秒字符串</param>
        /// <returns></returns>
        public static DateTime GetDateTime(DateTime dtDay, string timeStr)
        {
            timeStr = timeStr.Replace("：", ":");

            string[] parts = timeStr.Split(':');
            int hour = (parts.Length > 0 ? Convert.ToInt32(parts[0]) : 0);
            int minute = (parts.Length > 1 ? Convert.ToInt32(parts[1]) : 0);
            int second = (parts.Length > 2 ? Convert.ToInt32(parts[2]) : 0);

            return dtDay.Date.AddHours(hour).AddMinutes(minute).AddSeconds(second);
        }

        /// <summary>
        /// 从一个字符串中尽量提取日期时间字符串
        /// </summary>
        /// <remarks>
        ///  1: 年月日时分秒可以被任意非数字字符分隔
        ///  2: 年月日时分秒可以不用任何分隔符分隔,提取时按年四位数整数, 其它都有两位整数
        ///  3: 1与2两种情况的结合体也可以识别, 如 200012-2 20:12-43, 可以正确识别
        /// </remarks>
        /// <param name="dtSrc">日期时间字符串</param>
        /// <returns>TRUE: 成功; FALSE: 失败</returns>
        public static bool GetDateTime(ref string dtSrc)
        {
            string strChar = string.Empty;
            string strSect = string.Empty;
            string strSep = string.Empty;
            int pos = 0;
            int val = 0;
            int year = 0;
            int month = 0;
            int maxDays = 0;
            bool blnNum = false;
            int len = 4;

            string[] arrNum = { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" };
            string[] arrPart = { "20", "00", "00", "00", "00", "00" };

            // 字符串拆分
            dtSrc += " ";                       // " "为任意非数字字符, 目的在于方便接下的的for循环可以处理完整个字符串

            for (int i = 0; i < dtSrc.Length; i++)
            {
                strChar = dtSrc.Substring(i, 1);

                // 判断是否是数字
                blnNum = false;
                for (int j = 0; j < arrNum.Length; j++)
                {
                    if (strChar.Equals(arrNum[j]) == true)
                    {
                        blnNum = true;
                        break;
                    }
                }

                // 如果是数字
                if (blnNum == true)
                {
                    strSect += strChar;

                    if (strSect.Length > len && pos < arrPart.Length)
                    {
                        arrPart[pos++] = strSect.Substring(0, len);
                        len = 2;

                        strSect = strChar;
                    }
                }
                else
                {
                    if (strSect.Length > 0 && pos < arrPart.Length)
                    {
                        arrPart[pos++] = strSect;
                        len = 2;

                        strSect = string.Empty;
                    }
                }
            }

            if (pos < 3)
            {
                return false;
            }

            // 正确性判断
            for (int i = 0; i < arrPart.Length; i++)
            {
                val = int.Parse(arrPart[i]);

                switch (i)
                {
                    case 0:     // 年
                        if (val < 50) { val += 2000; }
                        if (50 <= val && val < 100) { val += 1900; }

                        if (val > 9999) { return false; }

                        year = val;
                        break;

                    case 1:     // 月
                        if (val < 1 || val > 12) { return false; }

                        month = val;
                        break;

                    case 2:     // 日
                        maxDays = 31;

                        if (month == 4 || month == 6 || month == 9 || month == 11)
                        {
                            maxDays = 30;
                        }

                        if (month == 2)
                        {
                            maxDays = 28;

                            if (DateTime.IsLeapYear(year) == true)
                            {
                                maxDays = 29;
                            }
                        }

                        if (val < 1 || val > maxDays) { return false; }
                        break;

                    case 3:     // 时
                        if (val < 0 || 23 < val) { return false; }
                        break;

                    case 4:     // 分
                    case 5:     // 秒
                        if (val < 0 || 59 < val) { return false; }
                        break;
                }
            }

            // 组合成日期时间字符串
            dtSrc = arrPart[0] + "-" + arrPart[1] + "-" + arrPart[2] + " " + arrPart[3] + ":" + arrPart[4] + ":" + arrPart[5];
            return true;
        }


        /// <summary>
        /// 把一个表示日期的字符串转换成短日期格式
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static string GetDateTimeShort(string dt)
        {
            if (dt.Trim().Length > 0)
            {
                return DateTime.Parse(dt).ToString("yyyy-MM-dd");
            }
            else
            {
                return string.Empty;
            }
        }


        /// <summary>
        /// 把一个表示日期的字符串转换成长日期格式
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static string GetDateTimeLong(string dt)
        {
            if (dt.Trim().Length > 0)
            {
                return DateTime.Parse(dt).ToString("yyyy-MM-dd HH:mm:ss");
            }
            else
            {
                return string.Empty;
            }
        }


        /// <summary>
        /// 获取短时间格式
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static string GetShortTime(string dt)
        {
            if (dt.Trim().Length > 0)
            {
                return DateTime.Parse(dt).ToString("yyyy-MM-dd");
                //return DateTime.Parse(dt).ToString(ComConst.FMT_DATE.TIME_SHORT);
            }
            else
            {
                return string.Empty;
            }
        }


        /// <summary>
        /// 获取月日(如8.2)
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static string GetMD(string dt)
        {
            if (dt.Trim().Length > 0)
            {
                DateTime dtValue = DateTime.Parse(dt);
                int month = dtValue.Month;
                int day = dtValue.Day;
                return month.ToString() + "." + day.ToString();
            }
            else
            {
                return string.Empty;
            }
        }


        /// <summary>
        /// 获取年月日, 如2007年12月6日
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static string GetYMD(string dt)
        {
            if (dt.Trim().Length > 0)
            {
                DateTime dtValue = DateTime.Parse(dt);

                int year = dtValue.Year;
                int month = dtValue.Month;
                int day = dtValue.Day;

                return year.ToString() + "年"
                    + month.ToString() + "月"
                    + day.ToString() + "日";
            }
            else
            {
                return string.Empty;
            }
        }


        /// <summary>
        /// 获取数字的罗马表示
        /// </summary>
        /// <param name="num"></param>
        /// <returns></returns>
        public static string GetRomaNum(int num)
        {
            if (num <= 0) { return string.Empty; }

            string[] arrNum = { "Ⅹ", "Ⅰ", "Ⅱ", "Ⅲ", "Ⅳ", "Ⅴ", "Ⅵ", "Ⅶ", "Ⅷ", "Ⅸ" };
            string romaNum = string.Empty;

            string numStr = num.ToString();

            for (int i = 0; i < numStr.Length; i++)
            {
                romaNum += arrNum[int.Parse(numStr.Substring(i, 1))];
            }

            return romaNum;
        }
        #endregion

        /// <summary>
        /// 检查一个字符串是不是合法的时间格式
        /// 如果是: 把输入字符串转换成标准的日期格式HH:MM:SS
        /// </summary>
        /// <param name="timeStr">表示时间的字符</param>
        /// <returns>TRUE: 是; FALSE: 不是</returns>
        public static bool IsTime(ref string timeStr)
        {
            const int MAX_TIME_ITEM_COUNT = 3;

            int intValue = 0;

            string[] astrItem = timeStr.Split(ComConst.STR.COLON.ToCharArray());

            if (astrItem.Length < 1) return false;

            for (int i = 0; i < astrItem.Length && i < MAX_TIME_ITEM_COUNT; i++)
            {
                if (astrItem[i].Trim().Length == 0)
                {
                    astrItem[i] = ComConst.FMT_DATE.TIME_ITEM;
                }

                // 如果不是数字字符串, 退出
                if (IsNumber(astrItem[i]) == false)
                {
                    return false;
                }

                intValue = int.Parse(astrItem[i]);

                // 小时
                if (i == 0)
                {
                    // 小时范围 0 <= X < 24
                    if (intValue < 0 || intValue > 23)
                    {
                        return false;
                    }

                    timeStr = intValue.ToString(ComConst.FMT_DATE.TIME_ITEM);
                }
                // 分钟或秒
                else
                {
                    // 分钟或秒范围 0 <= X < 60
                    if (intValue < 0 || intValue > 60)
                    {
                        return false;
                    }

                    timeStr += ComConst.STR.COLON + intValue.ToString(ComConst.FMT_DATE.TIME_ITEM);
                }
            }

            // 补足不够的部份
            for (int i = astrItem.Length; i < MAX_TIME_ITEM_COUNT; i++)
            {
                timeStr += ComConst.STR.COLON + ComConst.FMT_DATE.TIME_ITEM;
            }

            return true;
        }
        /// <summary>
		/// 判断一个字符串是不是正整数
		/// </summary>
		/// <remarks>仅包含数字</remarks>
		/// <param name="text">待验证的字符串</param>
		/// <returns>TRUE: 符合要求; FALSE: 不符合要求</returns>
		public static bool IsNumber(string text)
        {
            return Regex.IsMatch(text, @"^\d+$");
        }

        /// <summary>
        /// 判断字符串是不是日期时间,如果是转换成标准形式 YYYY-MM-DD HH:mm:ss
        /// </summary>
        /// <param name="dtStr"></param>
        /// <returns>TRUE: 是; FALSE: 否</returns>
        public static bool IsDateTime(string dtStr)
        {
#if !PDA
            DateTime dtResult;
            return DateTime.TryParse(dtStr, out dtResult);
#else
            return true;
#endif
        }
    }
}

