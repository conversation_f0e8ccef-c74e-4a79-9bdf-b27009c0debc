﻿namespace PlatCommonForm.Report
{
    partial class XtraReportPayment920
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrLabel_LB = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_DEMO = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel31 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_SKR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_JE = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel27 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel23 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_DXJE = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_ZYH = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_LXDH = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel19 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel20 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel22 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel11 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel12 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_XM = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_ZFFS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_BRIDH = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel9 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_RYKS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_SJH = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_KPRQ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_RYRQ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel14 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_CHARGE_TYPE = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel13 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrL_ZYCS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_HOSPITAL = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Dpi = 254F;
            this.Detail.HeightF = 0F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 254F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrLabel_LB
            // 
            this.xrLabel_LB.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel_LB.Dpi = 254F;
            this.xrLabel_LB.LocationFloat = new DevExpress.Utils.PointFloat(1696.191F, 389.3609F);
            this.xrLabel_LB.Name = "xrLabel_LB";
            this.xrLabel_LB.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel_LB.SizeF = new System.Drawing.SizeF(254F, 70.37875F);
            this.xrLabel_LB.StylePriority.UseBorders = false;
            this.xrLabel_LB.StylePriority.UseTextAlignment = false;
            this.xrLabel_LB.Text = "xrLabel3";
            this.xrLabel_LB.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel_DEMO
            // 
            this.xrLabel_DEMO.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Right | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel_DEMO.Dpi = 254F;
            this.xrLabel_DEMO.LocationFloat = new DevExpress.Utils.PointFloat(1516.063F, 600.4971F);
            this.xrLabel_DEMO.Name = "xrLabel_DEMO";
            this.xrLabel_DEMO.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel_DEMO.SizeF = new System.Drawing.SizeF(434.1285F, 70.37878F);
            this.xrLabel_DEMO.StylePriority.UseBorders = false;
            this.xrLabel_DEMO.StylePriority.UseTextAlignment = false;
            this.xrLabel_DEMO.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel31
            // 
            this.xrLabel31.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel31.Dpi = 254F;
            this.xrLabel31.LocationFloat = new DevExpress.Utils.PointFloat(1516.063F, 530.1183F);
            this.xrLabel31.Name = "xrLabel31";
            this.xrLabel31.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel31.SizeF = new System.Drawing.SizeF(180.1285F, 70.37878F);
            this.xrLabel31.StylePriority.UseBorders = false;
            this.xrLabel31.StylePriority.UseTextAlignment = false;
            this.xrLabel31.Text = "收款人";
            this.xrLabel31.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel_SKR
            // 
            this.xrLabel_SKR.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel_SKR.Dpi = 254F;
            this.xrLabel_SKR.LocationFloat = new DevExpress.Utils.PointFloat(1696.191F, 530.1183F);
            this.xrLabel_SKR.Name = "xrLabel_SKR";
            this.xrLabel_SKR.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel_SKR.SizeF = new System.Drawing.SizeF(254F, 70.37878F);
            this.xrLabel_SKR.StylePriority.UseBorders = false;
            this.xrLabel_SKR.StylePriority.UseTextAlignment = false;
            this.xrLabel_SKR.Text = "xrLabel3";
            this.xrLabel_SKR.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel_JE
            // 
            this.xrLabel_JE.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel_JE.Dpi = 254F;
            this.xrLabel_JE.LocationFloat = new DevExpress.Utils.PointFloat(1214.649F, 530.1184F);
            this.xrLabel_JE.Name = "xrLabel_JE";
            this.xrLabel_JE.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel_JE.SizeF = new System.Drawing.SizeF(301.4131F, 70.37872F);
            this.xrLabel_JE.StylePriority.UseBorders = false;
            this.xrLabel_JE.StylePriority.UseTextAlignment = false;
            this.xrLabel_JE.Text = "xrLabel3";
            this.xrLabel_JE.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel27
            // 
            this.xrLabel27.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel27.Dpi = 254F;
            this.xrLabel27.LocationFloat = new DevExpress.Utils.PointFloat(1034.733F, 530.1183F);
            this.xrLabel27.Name = "xrLabel27";
            this.xrLabel27.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel27.SizeF = new System.Drawing.SizeF(179.9166F, 70.37878F);
            this.xrLabel27.StylePriority.UseBorders = false;
            this.xrLabel27.StylePriority.UseTextAlignment = false;
            this.xrLabel27.Text = "金额";
            this.xrLabel27.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel23
            // 
            this.xrLabel23.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel23.Dpi = 254F;
            this.xrLabel23.LocationFloat = new DevExpress.Utils.PointFloat(68.79165F, 530.1183F);
            this.xrLabel23.Name = "xrLabel23";
            this.xrLabel23.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel23.SizeF = new System.Drawing.SizeF(179.9167F, 70.37878F);
            this.xrLabel23.StylePriority.UseBorders = false;
            this.xrLabel23.StylePriority.UseTextAlignment = false;
            this.xrLabel23.Text = "大写金额";
            this.xrLabel23.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel_DXJE
            // 
            this.xrLabel_DXJE.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel_DXJE.Dpi = 254F;
            this.xrLabel_DXJE.LocationFloat = new DevExpress.Utils.PointFloat(248.7083F, 530.1183F);
            this.xrLabel_DXJE.Name = "xrLabel_DXJE";
            this.xrLabel_DXJE.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel_DXJE.SizeF = new System.Drawing.SizeF(786.0245F, 70.37878F);
            this.xrLabel_DXJE.StylePriority.UseBorders = false;
            this.xrLabel_DXJE.StylePriority.UseTextAlignment = false;
            this.xrLabel_DXJE.Text = "xrLabel3";
            this.xrLabel_DXJE.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel_ZYH
            // 
            this.xrLabel_ZYH.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel_ZYH.Dpi = 254F;
            this.xrLabel_ZYH.LocationFloat = new DevExpress.Utils.PointFloat(695.8537F, 318.9821F);
            this.xrLabel_ZYH.Name = "xrLabel_ZYH";
            this.xrLabel_ZYH.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel_ZYH.SizeF = new System.Drawing.SizeF(227.5419F, 70.37878F);
            this.xrLabel_ZYH.StylePriority.UseBorders = false;
            this.xrLabel_ZYH.StylePriority.UseTextAlignment = false;
            this.xrLabel_ZYH.Text = "xrLabel_ZYH";
            this.xrLabel_ZYH.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel_LXDH
            // 
            this.xrLabel_LXDH.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel_LXDH.Dpi = 254F;
            this.xrLabel_LXDH.LocationFloat = new DevExpress.Utils.PointFloat(1214.649F, 459.7396F);
            this.xrLabel_LXDH.Name = "xrLabel_LXDH";
            this.xrLabel_LXDH.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel_LXDH.SizeF = new System.Drawing.SizeF(735.5416F, 70.37878F);
            this.xrLabel_LXDH.StylePriority.UseBorders = false;
            this.xrLabel_LXDH.StylePriority.UseTextAlignment = false;
            this.xrLabel_LXDH.Text = "xrLabel3";
            this.xrLabel_LXDH.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel19
            // 
            this.xrLabel19.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel19.Dpi = 254F;
            this.xrLabel19.LocationFloat = new DevExpress.Utils.PointFloat(1492.462F, 389.3608F);
            this.xrLabel19.Name = "xrLabel19";
            this.xrLabel19.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel19.SizeF = new System.Drawing.SizeF(203.7291F, 70.37878F);
            this.xrLabel19.StylePriority.UseBorders = false;
            this.xrLabel19.StylePriority.UseTextAlignment = false;
            this.xrLabel19.Text = "操作类型";
            this.xrLabel19.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel20
            // 
            this.xrLabel20.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel20.Dpi = 254F;
            this.xrLabel20.LocationFloat = new DevExpress.Utils.PointFloat(1034.733F, 459.7395F);
            this.xrLabel20.Name = "xrLabel20";
            this.xrLabel20.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel20.SizeF = new System.Drawing.SizeF(179.9166F, 70.37875F);
            this.xrLabel20.StylePriority.UseBorders = false;
            this.xrLabel20.StylePriority.UseTextAlignment = false;
            this.xrLabel20.Text = "联系电话";
            this.xrLabel20.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel22
            // 
            this.xrLabel22.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel22.Dpi = 254F;
            this.xrLabel22.LocationFloat = new DevExpress.Utils.PointFloat(555.6247F, 318.9821F);
            this.xrLabel22.Name = "xrLabel22";
            this.xrLabel22.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel22.SizeF = new System.Drawing.SizeF(140.229F, 70.37878F);
            this.xrLabel22.StylePriority.UseBorders = false;
            this.xrLabel22.StylePriority.UseTextAlignment = false;
            this.xrLabel22.Text = "住院号";
            this.xrLabel22.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel11
            // 
            this.xrLabel11.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel11.Dpi = 254F;
            this.xrLabel11.LocationFloat = new DevExpress.Utils.PointFloat(923.3955F, 318.9821F);
            this.xrLabel11.Name = "xrLabel11";
            this.xrLabel11.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel11.SizeF = new System.Drawing.SizeF(179.9166F, 70.37878F);
            this.xrLabel11.StylePriority.UseBorders = false;
            this.xrLabel11.StylePriority.UseTextAlignment = false;
            this.xrLabel11.Text = "姓名";
            this.xrLabel11.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel12
            // 
            this.xrLabel12.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel12.Dpi = 254F;
            this.xrLabel12.LocationFloat = new DevExpress.Utils.PointFloat(1034.733F, 389.3608F);
            this.xrLabel12.Name = "xrLabel12";
            this.xrLabel12.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel12.SizeF = new System.Drawing.SizeF(203.7291F, 70.37878F);
            this.xrLabel12.StylePriority.UseBorders = false;
            this.xrLabel12.StylePriority.UseTextAlignment = false;
            this.xrLabel12.Text = "支付方式";
            this.xrLabel12.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel_XM
            // 
            this.xrLabel_XM.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel_XM.Dpi = 254F;
            this.xrLabel_XM.LocationFloat = new DevExpress.Utils.PointFloat(1095.587F, 318.9821F);
            this.xrLabel_XM.Name = "xrLabel_XM";
            this.xrLabel_XM.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel_XM.SizeF = new System.Drawing.SizeF(396.8745F, 70.37878F);
            this.xrLabel_XM.StylePriority.UseBorders = false;
            this.xrLabel_XM.StylePriority.UseTextAlignment = false;
            this.xrLabel_XM.Text = "xrLabel3";
            this.xrLabel_XM.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel_ZFFS
            // 
            this.xrLabel_ZFFS.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel_ZFFS.Dpi = 254F;
            this.xrLabel_ZFFS.LocationFloat = new DevExpress.Utils.PointFloat(1238.462F, 389.3608F);
            this.xrLabel_ZFFS.Name = "xrLabel_ZFFS";
            this.xrLabel_ZFFS.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel_ZFFS.SizeF = new System.Drawing.SizeF(254F, 70.37878F);
            this.xrLabel_ZFFS.StylePriority.UseBorders = false;
            this.xrLabel_ZFFS.StylePriority.UseTextAlignment = false;
            this.xrLabel_ZFFS.Text = "xrLabel3";
            this.xrLabel_ZFFS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel7
            // 
            this.xrLabel7.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel7.Dpi = 254F;
            this.xrLabel7.LocationFloat = new DevExpress.Utils.PointFloat(68.79167F, 318.9821F);
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel7.SizeF = new System.Drawing.SizeF(179.9167F, 70.37878F);
            this.xrLabel7.StylePriority.UseBorders = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            this.xrLabel7.Text = "病人Id号";
            this.xrLabel7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel_BRIDH
            // 
            this.xrLabel_BRIDH.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel_BRIDH.Dpi = 254F;
            this.xrLabel_BRIDH.LocationFloat = new DevExpress.Utils.PointFloat(248.7083F, 318.9821F);
            this.xrLabel_BRIDH.Name = "xrLabel_BRIDH";
            this.xrLabel_BRIDH.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel_BRIDH.SizeF = new System.Drawing.SizeF(306.9164F, 70.37878F);
            this.xrLabel_BRIDH.StylePriority.UseBorders = false;
            this.xrLabel_BRIDH.StylePriority.UseTextAlignment = false;
            this.xrLabel_BRIDH.Text = "xrLabel3";
            this.xrLabel_BRIDH.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel9
            // 
            this.xrLabel9.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel9.Dpi = 254F;
            this.xrLabel9.LocationFloat = new DevExpress.Utils.PointFloat(68.79167F, 389.3608F);
            this.xrLabel9.Name = "xrLabel9";
            this.xrLabel9.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel9.SizeF = new System.Drawing.SizeF(179.9167F, 70.37878F);
            this.xrLabel9.StylePriority.UseBorders = false;
            this.xrLabel9.StylePriority.UseTextAlignment = false;
            this.xrLabel9.Text = "入院科室";
            this.xrLabel9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel_RYKS
            // 
            this.xrLabel_RYKS.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel_RYKS.Dpi = 254F;
            this.xrLabel_RYKS.LocationFloat = new DevExpress.Utils.PointFloat(248.7083F, 389.3608F);
            this.xrLabel_RYKS.Name = "xrLabel_RYKS";
            this.xrLabel_RYKS.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel_RYKS.SizeF = new System.Drawing.SizeF(786.0245F, 70.37878F);
            this.xrLabel_RYKS.StylePriority.UseBorders = false;
            this.xrLabel_RYKS.StylePriority.UseTextAlignment = false;
            this.xrLabel_RYKS.Text = "xrLabel3";
            this.xrLabel_RYKS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel_SJH
            // 
            this.xrLabel_SJH.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel_SJH.Dpi = 254F;
            this.xrLabel_SJH.LocationFloat = new DevExpress.Utils.PointFloat(1447.27F, 172.4029F);
            this.xrLabel_SJH.Name = "xrLabel_SJH";
            this.xrLabel_SJH.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel_SJH.SizeF = new System.Drawing.SizeF(540.3854F, 70.37874F);
            this.xrLabel_SJH.StylePriority.UseBorders = false;
            this.xrLabel_SJH.StylePriority.UseTextAlignment = false;
            this.xrLabel_SJH.Text = "xrLabel3";
            this.xrLabel_SJH.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel5
            // 
            this.xrLabel5.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel5.Dpi = 254F;
            this.xrLabel5.LocationFloat = new DevExpress.Utils.PointFloat(1272.645F, 172.4029F);
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel5.SizeF = new System.Drawing.SizeF(174.6249F, 70.37878F);
            this.xrLabel5.StylePriority.UseBorders = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            this.xrLabel5.Text = "收据号：";
            this.xrLabel5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel_KPRQ
            // 
            this.xrLabel_KPRQ.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel_KPRQ.Dpi = 254F;
            this.xrLabel_KPRQ.LocationFloat = new DevExpress.Utils.PointFloat(304.2709F, 237.0667F);
            this.xrLabel_KPRQ.Name = "xrLabel_KPRQ";
            this.xrLabel_KPRQ.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel_KPRQ.SizeF = new System.Drawing.SizeF(441.8538F, 70.37874F);
            this.xrLabel_KPRQ.StylePriority.UseBorders = false;
            this.xrLabel_KPRQ.StylePriority.UseTextAlignment = false;
            this.xrLabel_KPRQ.Text = "2018-12-14 19:42:21";
            this.xrLabel_KPRQ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel3
            // 
            this.xrLabel3.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel3.Dpi = 254F;
            this.xrLabel3.LocationFloat = new DevExpress.Utils.PointFloat(68.79167F, 237.0667F);
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel3.SizeF = new System.Drawing.SizeF(235.4791F, 70.37877F);
            this.xrLabel3.StylePriority.UseBorders = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            this.xrLabel3.Text = "收据日期：";
            this.xrLabel3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel4,
            this.xrLabel_DEMO,
            this.xrLabel_SKR,
            this.xrLabel31,
            this.xrLabel_JE,
            this.xrLabel27,
            this.xrLabel_DXJE,
            this.xrLabel23,
            this.xrLabel_LXDH,
            this.xrLabel20,
            this.xrL_RYRQ,
            this.xrLabel14,
            this.xrLabel_LB,
            this.xrLabel19,
            this.xrLabel_ZFFS,
            this.xrLabel12,
            this.xrLabel_RYKS,
            this.xrLabel9,
            this.xrL_CHARGE_TYPE,
            this.xrLabel13,
            this.xrLabel_XM,
            this.xrLabel11,
            this.xrLabel_ZYH,
            this.xrLabel22,
            this.xrLabel_BRIDH,
            this.xrLabel7,
            this.xrL_ZYCS,
            this.xrLabel2,
            this.xrLabel_KPRQ,
            this.xrLabel3,
            this.xrLabel_SJH,
            this.xrLabel5,
            this.xrLabel1,
            this.xrLabel_HOSPITAL});
            this.TopMargin.Dpi = 254F;
            this.TopMargin.HeightF = 687F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 254F);
            this.TopMargin.StylePriority.UseTextAlignment = false;
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel4
            // 
            this.xrLabel4.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel4.Dpi = 254F;
            this.xrLabel4.LocationFloat = new DevExpress.Utils.PointFloat(68.79165F, 600.4969F);
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel4.SizeF = new System.Drawing.SizeF(1447.271F, 70.37878F);
            this.xrLabel4.StylePriority.UseBorders = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            this.xrLabel4.Text = "此系临时收据不作报销用。此收据妥善保存，出院时凭此收据按实用数换取报销凭证";
            this.xrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrL_RYRQ
            // 
            this.xrL_RYRQ.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrL_RYRQ.Dpi = 254F;
            this.xrL_RYRQ.LocationFloat = new DevExpress.Utils.PointFloat(248.7083F, 459.7395F);
            this.xrL_RYRQ.Name = "xrL_RYRQ";
            this.xrL_RYRQ.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrL_RYRQ.SizeF = new System.Drawing.SizeF(786.0245F, 70.37881F);
            this.xrL_RYRQ.StylePriority.UseBorders = false;
            this.xrL_RYRQ.StylePriority.UseTextAlignment = false;
            this.xrL_RYRQ.Text = "xrLabel3";
            this.xrL_RYRQ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel14
            // 
            this.xrLabel14.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel14.Dpi = 254F;
            this.xrLabel14.LocationFloat = new DevExpress.Utils.PointFloat(68.79165F, 459.7396F);
            this.xrLabel14.Name = "xrLabel14";
            this.xrLabel14.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel14.SizeF = new System.Drawing.SizeF(179.9167F, 70.37878F);
            this.xrLabel14.StylePriority.UseBorders = false;
            this.xrLabel14.StylePriority.UseTextAlignment = false;
            this.xrLabel14.Text = "入院日期";
            this.xrLabel14.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrL_CHARGE_TYPE
            // 
            this.xrL_CHARGE_TYPE.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrL_CHARGE_TYPE.Dpi = 254F;
            this.xrL_CHARGE_TYPE.LocationFloat = new DevExpress.Utils.PointFloat(1696.191F, 318.9821F);
            this.xrL_CHARGE_TYPE.Name = "xrL_CHARGE_TYPE";
            this.xrL_CHARGE_TYPE.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrL_CHARGE_TYPE.SizeF = new System.Drawing.SizeF(254F, 70.37881F);
            this.xrL_CHARGE_TYPE.StylePriority.UseBorders = false;
            this.xrL_CHARGE_TYPE.StylePriority.UseTextAlignment = false;
            this.xrL_CHARGE_TYPE.Text = " ";
            this.xrL_CHARGE_TYPE.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel13
            // 
            this.xrLabel13.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel13.Dpi = 254F;
            this.xrLabel13.LocationFloat = new DevExpress.Utils.PointFloat(1492.462F, 318.9821F);
            this.xrLabel13.Name = "xrLabel13";
            this.xrLabel13.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel13.SizeF = new System.Drawing.SizeF(203.7291F, 70.37881F);
            this.xrLabel13.StylePriority.UseBorders = false;
            this.xrLabel13.StylePriority.UseTextAlignment = false;
            this.xrLabel13.Text = "费别";
            this.xrLabel13.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrL_ZYCS
            // 
            this.xrL_ZYCS.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrL_ZYCS.Dpi = 254F;
            this.xrL_ZYCS.LocationFloat = new DevExpress.Utils.PointFloat(1388.639F, 242.7817F);
            this.xrL_ZYCS.Name = "xrL_ZYCS";
            this.xrL_ZYCS.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrL_ZYCS.SizeF = new System.Drawing.SizeF(148.802F, 70.37875F);
            this.xrL_ZYCS.StylePriority.UseBorders = false;
            this.xrL_ZYCS.StylePriority.UseTextAlignment = false;
            this.xrL_ZYCS.Text = "ZYCS";
            this.xrL_ZYCS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel2
            // 
            this.xrLabel2.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel2.Dpi = 254F;
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(1176.973F, 242.7817F);
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(211.6666F, 70.3788F);
            this.xrLabel2.StylePriority.UseBorders = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            this.xrLabel2.Text = "住院次数：";
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel1
            // 
            this.xrLabel1.Dpi = 254F;
            this.xrLabel1.Font = new System.Drawing.Font("Times New Roman", 16F, System.Drawing.FontStyle.Bold);
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(605.8957F, 140.97F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(597.9581F, 90.17001F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "预交金收据";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel_HOSPITAL
            // 
            this.xrLabel_HOSPITAL.Dpi = 254F;
            this.xrLabel_HOSPITAL.Font = new System.Drawing.Font("Times New Roman", 16F, System.Drawing.FontStyle.Bold);
            this.xrLabel_HOSPITAL.LocationFloat = new DevExpress.Utils.PointFloat(25.4F, 50.80003F);
            this.xrLabel_HOSPITAL.Name = "xrLabel_HOSPITAL";
            this.xrLabel_HOSPITAL.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.xrLabel_HOSPITAL.SizeF = new System.Drawing.SizeF(1841.5F, 90.17F);
            this.xrLabel_HOSPITAL.StylePriority.UseFont = false;
            this.xrLabel_HOSPITAL.StylePriority.UseTextAlignment = false;
            this.xrLabel_HOSPITAL.Text = "XXXXXXX医院";
            this.xrLabel_HOSPITAL.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Dpi = 254F;
            this.BottomMargin.HeightF = 0F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 254F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // XtraReportPayment920
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin});
            this.Dpi = 254F;
            this.Margins = new System.Drawing.Printing.Margins(62, 64, 687, 0);
            this.PageHeight = 920;
            this.PageWidth = 2162;
            this.PaperKind = System.Drawing.Printing.PaperKind.Custom;
            this.ReportUnit = DevExpress.XtraReports.UI.ReportUnit.TenthsOfAMillimeter;
            this.SnapGridSize = 25F;
            this.Version = "17.2";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_HOSPITAL;
        private DevExpress.XtraReports.UI.XRLabel xrLabel31;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_SKR;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_JE;
        private DevExpress.XtraReports.UI.XRLabel xrLabel27;
        private DevExpress.XtraReports.UI.XRLabel xrLabel23;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_DXJE;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_ZYH;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_LXDH;
        private DevExpress.XtraReports.UI.XRLabel xrLabel19;
        private DevExpress.XtraReports.UI.XRLabel xrLabel20;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_LB;
        private DevExpress.XtraReports.UI.XRLabel xrLabel22;
        private DevExpress.XtraReports.UI.XRLabel xrLabel11;
        private DevExpress.XtraReports.UI.XRLabel xrLabel12;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_XM;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_ZFFS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_BRIDH;
        private DevExpress.XtraReports.UI.XRLabel xrLabel9;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_RYKS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_SJH;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_KPRQ;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_DEMO;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel xrL_ZYCS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel xrL_CHARGE_TYPE;
        private DevExpress.XtraReports.UI.XRLabel xrLabel13;
        private DevExpress.XtraReports.UI.XRLabel xrL_RYRQ;
        private DevExpress.XtraReports.UI.XRLabel xrLabel14;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
    }
}
