﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Collections;
using System.Reflection;
using PlatCommon.Common;
using Model;

namespace PlatCommon.SysBase
{
    /// <summary>
    /// 系统全局参数
    /// </summary>
    public class SystemParm
    {
        #region Field
        //数据库变量
        private static string database_ip;
        private static string database_user;
        private static string database_pass;
        private static string database_dbname;
        private static string _connectionString;

        //登录用户变量
        private static STAFF_DICT loginUser;
        private static string ca_pin; //用户key密码

        //医院变量
        private static string hospitalID = "0001";
        private static string hisUnitCode = "";//增加医院编号
        private static string hospitalhospital_label = "0001";

        //打印地址变量
        private static string lis_print_url = "";   //lis打印图像地址
        private static string pacs_print_url = "";   //pacs打印图像地址
        private static string dayin_print_url = "";//化验单样本采集窗体打印机

        private static string isPass = "";  //是否启用合理用药
        private static bool passEnabled = false; //合理用药是否可用
        private static bool gs_bjca_enabled = false; //北京CA是否可用
        private static bool gs_time_snap = false;  //是否启用时间戳
        private static PlatCommon.Common.UdpMessage udpClass;//消息类
        private static Tjhis.Interface.CA.CaBusiness caBusiness;//ca
        #endregion

        /// <summary>
        /// 系统版本号,从hospital_config中获取
        /// </summary>
        public static string version = "6.6";

        /// <summary>
        /// 系统发布的所属版本号查询授权和菜单时候比对用
        /// </summary>
        public readonly static string copyright_verson = "V6.6.4";

        /// <summary>
        /// 系统生成的所属医院标识 由授权发布时给生成的然后通知过来的
        /// </summary>
        public readonly static string copyright_unit = "1000061";

        /// <summary>
        /// 数据库IP
        /// </summary>
        public static string Database_ip
        {
            set { database_ip = value; }
            get { return database_ip; }
        }
        /// <summary>
        /// 数据库用户名
        /// </summary>
        public static string Database_user
        {
            set { database_user = value; }
            get { return database_user; }
        }
        /// <summary>
        /// 数据库密码
        /// </summary>
        public static string Database_pass
        {
            set { database_pass = value; }
            get { return database_pass; }
        }
        /// <summary>
        /// 数据库名称
        /// </summary>
        public static string Database_dbname
        {
            set { database_dbname = value; }
            get { return database_dbname; }
        }
        /// <summary>
        /// 获取业务系统数据库连接字符串
        /// </summary>
        public static string ConnectionString
        {
            get { return _connectionString; }
            set { _connectionString = value; }
        }
        /// <summary>
        /// 医院编码
        /// </summary>
        public static string HisUnitCode
        {
            set { hisUnitCode = value; }
            get { return hisUnitCode; }
        }
        /// <summary>
        /// 医院名称
        /// </summary>        
        public static string HospitalID
        {
            set { hospitalID = value; }
            get { return hospitalID; }
        }
        /// <summary>
        /// 医院别名 区分同名医院的不同分院 
        /// </summary>        
        public static string Hospitalhospital_label
        {
            set { hospitalhospital_label = value; }
            get { return hospitalhospital_label; }
        }
        /// <summary>
        /// 化验单样本采集窗体打印机
        /// </summary>
        public static string Dayin_print_url
        {
            get { return SystemParm.dayin_print_url; }
            set { SystemParm.dayin_print_url = value; }
        }

        /// <summary>
        /// 是否启用合理用药
        /// </summary>
        public static string IsPass
        {
            get { return isPass; }
            set { isPass = value; }
        }

        /// <summary>
        /// 合理用药是否可用
        /// </summary>
        public static bool PassEnabled
        {
            get { return passEnabled; }
            set { passEnabled = value; }
        }

        /// <summary>
        /// 北京CA是否可用
        /// </summary>
        public static bool Gs_Bjca_Enabled
        {
            get { return SystemParm.gs_bjca_enabled; }
            set { SystemParm.gs_bjca_enabled = value; }
        }

        /// <summary>
        /// 是否启用时间戳
        /// </summary>
        public static bool Gs_Time_Snap
        {
            get { return SystemParm.gs_time_snap; }
            set { SystemParm.gs_time_snap = value; }
        }

        /// <summary>
        /// 消息类
        /// </summary>        
        public static UdpMessage UdpClass
        {
            get { return SystemParm.udpClass; }
            set { SystemParm.udpClass = value; }
        }

        public static Tjhis.Interface.CA.CaBusiness CaBusiness
        {
            get { return SystemParm.caBusiness; }
            set { SystemParm.caBusiness = value; }
        }

        /// <summary>
        /// 登录用户
        /// </summary>
        public static STAFF_DICT LoginUser
        {
            set { loginUser = value; }
            get { return loginUser; }
        }
        
        /// <summary>
        /// Lis打印图像地址
        /// </summary>
        public static string Lis_print_url
        {
            get { return SystemParm.lis_print_url; }
            set { SystemParm.lis_print_url = value; }
        }
        /// <summary>
        /// PACS打印图像地址
        /// </summary>
        public static string Pacs_print_url
        {
            get { return SystemParm.pacs_print_url; }
            set { SystemParm.pacs_print_url = value; }
        }

        /// <summary>
        /// 用户key密码
        /// </summary>
        public static string CA_PIN 
        {
            get { return ca_pin; }
            set { ca_pin = value; }
        }


        #region 获取系统参数配置（表app_configer_parameter取值）
        /// <summary>
        /// 取系统参数
        /// </summary>
        /// <param name="paraName">参数名称</param>
        /// <param name="appName">应用程序名称</param>
        /// <param name="deptCode">科室代码</param>
        /// <param name="userName">用户代码</param>
        /// <param name="defaltValue">默认值</param>
        /// <returns></returns>
        public static string GetParameterValue(string paraName, string appName, string deptCode, string userName, string hisUnitCode)
        {           
            string temp_appName = appName;
            string temp_deptCode = deptCode;
            string temp_userName = userName;

            DataSet ds_result = null;
            DataTable dt_result = null;

            string unitcode = string.Empty;
            if (!string.IsNullOrEmpty(hisUnitCode))
            {
                unitcode = $" and his_unit_code='{hisUnitCode}'";
            }

            try
            {
                using (NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient())
                {
                    string searchSql1 = $"select parameter_value, app_name, dept_code, emp_no from app_configer_parameter where parameter_name = '{ paraName }' {unitcode}";

                    ds_result = spc.GetDataBySql(searchSql1);
                    if (ds_result != null && ds_result.Tables.Count > 0)
                    {
                        dt_result = ds_result.Tables[0];
                        if (dt_result.Rows.Count > 0)
                        {
                            if (!temp_appName.Equals("*") && !temp_deptCode.Equals("*") && !temp_userName.Equals("*"))
                            {
                                // 取个人级
                                string personalFilter = $"app_name = '{temp_appName}' and dept_code = '{ temp_deptCode }' and emp_no = '{temp_userName}'";
                                DataRow[] drs = dt_result.Select(personalFilter);
                                if (drs != null && drs.Length > 0)
                                {
                                    if (!string.IsNullOrEmpty(drs[0]["parameter_value"].ToString()))
                                    {
                                        return drs[0]["parameter_value"].ToString();
                                    }
                                }
                            }

                            if (!temp_appName.Equals("*") && !temp_deptCode.Equals("*"))
                            {
                                // 取科室级
                                string deptFilter = $"app_name = '{temp_appName}' and dept_code = '{ temp_deptCode }'  and emp_no = '*'";
                                DataRow[] drs = dt_result.Select(deptFilter);
                                if (drs != null && drs.Length > 0)
                                {
                                    if (!string.IsNullOrEmpty(drs[0]["parameter_value"].ToString()))
                                    {
                                        return drs[0]["parameter_value"].ToString();
                                    }
                                }
                            }

                            if (!temp_appName.Equals("*"))
                            {
                                // 取模块级
                                string deptFilter = $"app_name = '{temp_appName}' and dept_code = '*'  and emp_no = '*'";
                                DataRow[] drs = dt_result.Select(deptFilter);
                                if (drs != null && drs.Length > 0)
                                {
                                    if (!string.IsNullOrEmpty(drs[0]["parameter_value"].ToString()))
                                    {
                                        return drs[0]["parameter_value"].ToString();
                                    }
                                }
                            }

                            {
                                // 取平台级
                                string deptFilter = $"app_name = '*' and dept_code = '*'  and emp_no = '*'";
                                DataRow[] drs = dt_result.Select(deptFilter);
                                if (drs != null && drs.Length > 0)
                                {
                                    if (!string.IsNullOrEmpty(drs[0]["parameter_value"].ToString()))
                                    {
                                        return drs[0]["parameter_value"].ToString();
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                return string.Empty;
            }
            return string.Empty;
        }

        /// <summary>
        /// 取系统参数
        /// </summary>
        /// <param name="paraName">参数名称</param>
        /// <param name="appName">应用程序名称</param>
        /// <param name="deptCode">科室代码</param>
        /// <param name="userName">用户代码</param>
        /// <param name="defaltValue">默认值</param>
        /// <returns></returns>
        public static string GetParaValue(string paraName, string appName, string deptCode, string userName, string defaltValue, Utility.OracleODP.OracleBaseClass db = null)
        {
            string unitcode = SystemParm.HisUnitCode;             
            string rv = defaltValue;
            
            string str = string.Format(@"select parameter_value from comm.app_configer_parameter where parameter_name = '{0}' and app_name = '{1}' and dept_code = '{2}' and emp_no = '{3}' and his_unit_code='{4}'", paraName,appName,deptCode,userName,SystemParm.hisUnitCode);
            DataSet ds;
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            if (db != null)
            {
                ds = new NM_Service.NMService.ServerPublicClient().GetDataTable_Para(str, para, para_val, db);
            }
            else
                ds = new NM_Service.NMService.ServerPublicClient().GetDataTable_Para(str, para, para_val);
            if (ds == null)
                rv = defaltValue;
            else
            {
                if (ds.Tables[0].Rows.Count < 1)
                    rv = defaltValue;
                else
                    rv = ds.Tables[0].Rows[0][0].ToString();
            }
            return rv;
        }
        
        /// <summary>
        /// 取系统参数
        /// </summary>
        /// <param name="paraName">参数名称</param>
        /// <param name="appName">应用程序名称</param>
        /// <param name="deptCode">科室代码</param>
        /// <param name="userName">用户代码</param>
        /// <param name="defaltValue">默认值</param>
        /// <returns></returns>
        public static string GetParaValue(string paraName, string appName, string deptCode, string userName, string defaltValue, string unitcode)
        {
            string rv = defaltValue;
            string str = string.Format(@"select parameter_value from comm.app_configer_parameter where parameter_name = '{0}' and app_name = '{1}' and dept_code = '{2}' and emp_no = '{3}' ", paraName, appName, deptCode, userName);
            if (!string.IsNullOrEmpty(unitcode))
            {               
                str += " and his_unit_code='" + unitcode + "'";
            }            

            DataSet ds = new NM_Service.NMService.ServerPublicClient().GetList(str);
            if (ds == null)
                rv = defaltValue;
            else
            {
                if (ds.Tables[0].Rows.Count < 1)
                    rv = defaltValue;
                else
                    rv = ds.Tables[0].Rows[0][0].ToString();
            }
            return rv;
        }
        
        #endregion
    }

}
