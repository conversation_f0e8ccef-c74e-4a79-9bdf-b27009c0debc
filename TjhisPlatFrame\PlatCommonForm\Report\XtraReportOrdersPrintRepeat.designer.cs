﻿namespace PlatCommonForm.Report
{
    partial class XtraReportOrdersPrintRepeat
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being usefd.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.date = new DevExpress.XtraReports.UI.XRTableCell();
            this.time = new DevExpress.XtraReports.UI.XRTableCell();
            this.order_text = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.joint_line = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.doctor = new DevExpress.XtraReports.UI.XRTableCell();
            this.nurse = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrPictureBox1 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.stop_date = new DevExpress.XtraReports.UI.XRTableCell();
            this.stop_time = new DevExpress.XtraReports.UI.XRTableCell();
            this.stop_doctor = new DevExpress.XtraReports.UI.XRTableCell();
            this.stop_nurse = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrPictureBox2 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.rowNum = new DevExpress.XtraReports.UI.XRTableCell();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.t_stopdoctor = new DevExpress.XtraReports.UI.XRLabel();
            this.t_stoptime = new DevExpress.XtraReports.UI.XRLabel();
            this.t_stopdate = new DevExpress.XtraReports.UI.XRLabel();
            this.t_nurse = new DevExpress.XtraReports.UI.XRLabel();
            this.t_doctor = new DevExpress.XtraReports.UI.XRLabel();
            this.t_order_text = new DevExpress.XtraReports.UI.XRLabel();
            this.t_time = new DevExpress.XtraReports.UI.XRLabel();
            this.t_date = new DevExpress.XtraReports.UI.XRLabel();
            this.t_stop = new DevExpress.XtraReports.UI.XRLabel();
            this.t_start = new DevExpress.XtraReports.UI.XRLabel();
            this.hospital_name = new DevExpress.XtraReports.UI.XRLabel();
            this.id = new DevExpress.XtraReports.UI.XRLabel();
            this.t_id = new DevExpress.XtraReports.UI.XRLabel();
            this.inp_no = new DevExpress.XtraReports.UI.XRLabel();
            this.t_inp_no = new DevExpress.XtraReports.UI.XRLabel();
            this.bed_no = new DevExpress.XtraReports.UI.XRLabel();
            this.t_bed_no = new DevExpress.XtraReports.UI.XRLabel();
            this.dept = new DevExpress.XtraReports.UI.XRLabel();
            this.t_dept = new DevExpress.XtraReports.UI.XRLabel();
            this.name = new DevExpress.XtraReports.UI.XRLabel();
            this.t_name = new DevExpress.XtraReports.UI.XRLabel();
            this.t_stopnurse = new DevExpress.XtraReports.UI.XRLabel();
            this.t_page = new DevExpress.XtraReports.UI.XRLabel();
            this.PageFooter = new DevExpress.XtraReports.UI.PageFooterBand();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.orders_type = new DevExpress.XtraReports.UI.XRLabel();
            this.xrPictureDoctor = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureStopDoctor = new DevExpress.XtraReports.UI.XRPictureBox();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable1});
            this.Detail.HeightF = 40F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrTable1
            // 
            this.xrTable1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable1.BorderWidth = 1F;
            this.xrTable1.Font = new System.Drawing.Font("宋体", 9F);
            this.xrTable1.LocationFloat = new DevExpress.Utils.PointFloat(33.99999F, 0F);
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow1});
            this.xrTable1.SizeF = new System.Drawing.SizeF(776.9999F, 40F);
            this.xrTable1.StylePriority.UseBorders = false;
            this.xrTable1.StylePriority.UseBorderWidth = false;
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            this.xrTable1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrTableRow1
            // 
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.date,
            this.time,
            this.order_text,
            this.xrTableCell1,
            this.joint_line,
            this.xrTableCell3,
            this.xrTableCell2,
            this.doctor,
            this.nurse,
            this.stop_date,
            this.stop_time,
            this.stop_doctor,
            this.stop_nurse,
            this.rowNum});
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.Weight = 1D;
            // 
            // date
            // 
            this.date.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.date.Name = "date";
            this.date.StylePriority.UseBorders = false;
            this.date.Weight = 0.5872524576019299D;
            // 
            // time
            // 
            this.time.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.time.Name = "time";
            this.time.StylePriority.UseBorders = false;
            this.time.StylePriority.UseTextAlignment = false;
            this.time.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.time.Weight = 0.44420381833301154D;
            // 
            // order_text
            // 
            this.order_text.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.order_text.Multiline = true;
            this.order_text.Name = "order_text";
            this.order_text.StylePriority.UseBorders = false;
            this.order_text.Weight = 1.5460556151841929D;
            // 
            // xrTableCell1
            // 
            this.xrTableCell1.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell1.Name = "xrTableCell1";
            this.xrTableCell1.StylePriority.UseBorders = false;
            this.xrTableCell1.StylePriority.UseTextAlignment = false;
            this.xrTableCell1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrTableCell1.Weight = 0.40542966164832883D;
            // 
            // joint_line
            // 
            this.joint_line.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.joint_line.Name = "joint_line";
            this.joint_line.StylePriority.UseBorders = false;
            this.joint_line.StylePriority.UseTextAlignment = false;
            this.joint_line.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.joint_line.Weight = 0.17090488625065481D;
            // 
            // xrTableCell3
            // 
            this.xrTableCell3.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.StylePriority.UseBorders = false;
            this.xrTableCell3.Weight = 0.47959103075682941D;
            // 
            // xrTableCell2
            // 
            this.xrTableCell2.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Right | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell2.Name = "xrTableCell2";
            this.xrTableCell2.StylePriority.UseBorders = false;
            this.xrTableCell2.Weight = 0.48260005021539126D;
            // 
            // doctor
            // 
            this.doctor.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.doctor.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPictureDoctor});
            this.doctor.Name = "doctor";
            this.doctor.StylePriority.UseBorders = false;
            this.doctor.StylePriority.UseTextAlignment = false;
            this.doctor.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.doctor.Weight = 0.42038209598670495D;
            // 
            // nurse
            // 
            this.nurse.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.nurse.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPictureBox1});
            this.nurse.Name = "nurse";
            this.nurse.StylePriority.UseBorders = false;
            this.nurse.StylePriority.UseTextAlignment = false;
            this.nurse.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.nurse.Weight = 0.45974335393561755D;
            // 
            // xrPictureBox1
            // 
            this.xrPictureBox1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrPictureBox1.LocationFloat = new DevExpress.Utils.PointFloat(5.356762F, 5F);
            this.xrPictureBox1.Name = "xrPictureBox1";
            this.xrPictureBox1.SizeF = new System.Drawing.SizeF(44.53003F, 30F);
            this.xrPictureBox1.StylePriority.UseBorders = false;
            this.xrPictureBox1.Visible = false;
            // 
            // stop_date
            // 
            this.stop_date.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.stop_date.Name = "stop_date";
            this.stop_date.StylePriority.UseBorders = false;
            this.stop_date.Weight = 0.60268816518810886D;
            // 
            // stop_time
            // 
            this.stop_time.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.stop_time.Name = "stop_time";
            this.stop_time.StylePriority.UseBorders = false;
            this.stop_time.Weight = 0.391122565663638D;
            // 
            // stop_doctor
            // 
            this.stop_doctor.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.stop_doctor.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPictureStopDoctor});
            this.stop_doctor.Name = "stop_doctor";
            this.stop_doctor.StylePriority.UseBorders = false;
            this.stop_doctor.StylePriority.UseTextAlignment = false;
            this.stop_doctor.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.stop_doctor.Weight = 0.43366446642335066D;
            // 
            // stop_nurse
            // 
            this.stop_nurse.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.stop_nurse.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPictureBox2});
            this.stop_nurse.Name = "stop_nurse";
            this.stop_nurse.StylePriority.UseBorders = false;
            this.stop_nurse.StylePriority.UseTextAlignment = false;
            this.stop_nurse.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.stop_nurse.Weight = 0.54207974906929857D;
            // 
            // xrPictureBox2
            // 
            this.xrPictureBox2.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrPictureBox2.LocationFloat = new DevExpress.Utils.PointFloat(7.735014F, 4.999999F);
            this.xrPictureBox2.Name = "xrPictureBox2";
            this.xrPictureBox2.SizeF = new System.Drawing.SizeF(44.53003F, 30F);
            this.xrPictureBox2.StylePriority.UseBorders = false;
            this.xrPictureBox2.Visible = false;
            // 
            // rowNum
            // 
            this.rowNum.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.rowNum.Name = "rowNum";
            this.rowNum.StylePriority.UseBorders = false;
            this.rowNum.Weight = 0.054207058975620767D;
            // 
            // TopMargin
            // 
            this.TopMargin.HeightF = 15F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // BottomMargin
            // 
            this.BottomMargin.HeightF = 11F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // t_stopdoctor
            // 
            this.t_stopdoctor.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.t_stopdoctor.BorderWidth = 1F;
            this.t_stopdoctor.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.t_stopdoctor.LocationFloat = new DevExpress.Utils.PointFloat(697.5F, 126F);
            this.t_stopdoctor.Multiline = true;
            this.t_stopdoctor.Name = "t_stopdoctor";
            this.t_stopdoctor.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_stopdoctor.SizeF = new System.Drawing.SizeF(47.5F, 39.99999F);
            this.t_stopdoctor.StylePriority.UseBorders = false;
            this.t_stopdoctor.StylePriority.UseBorderWidth = false;
            this.t_stopdoctor.StylePriority.UseFont = false;
            this.t_stopdoctor.StylePriority.UseTextAlignment = false;
            this.t_stopdoctor.Text = "医生\r\n签名";
            this.t_stopdoctor.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // t_stoptime
            // 
            this.t_stoptime.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.t_stoptime.BorderWidth = 1F;
            this.t_stoptime.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.t_stoptime.LocationFloat = new DevExpress.Utils.PointFloat(653.7084F, 126F);
            this.t_stoptime.Name = "t_stoptime";
            this.t_stoptime.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_stoptime.SizeF = new System.Drawing.SizeF(43.29163F, 39.99999F);
            this.t_stoptime.StylePriority.UseBorders = false;
            this.t_stoptime.StylePriority.UseBorderWidth = false;
            this.t_stoptime.StylePriority.UseFont = false;
            this.t_stoptime.StylePriority.UseTextAlignment = false;
            this.t_stoptime.Text = "时间";
            this.t_stoptime.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // t_stopdate
            // 
            this.t_stopdate.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.t_stopdate.BorderWidth = 1F;
            this.t_stopdate.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.t_stopdate.LocationFloat = new DevExpress.Utils.PointFloat(587F, 126F);
            this.t_stopdate.Name = "t_stopdate";
            this.t_stopdate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_stopdate.SizeF = new System.Drawing.SizeF(66.70837F, 39.99999F);
            this.t_stopdate.StylePriority.UseBorders = false;
            this.t_stopdate.StylePriority.UseBorderWidth = false;
            this.t_stopdate.StylePriority.UseFont = false;
            this.t_stopdate.StylePriority.UseTextAlignment = false;
            this.t_stopdate.Text = "日期";
            this.t_stopdate.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // t_nurse
            // 
            this.t_nurse.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.t_nurse.BorderWidth = 1F;
            this.t_nurse.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.t_nurse.LocationFloat = new DevExpress.Utils.PointFloat(536.1132F, 126F);
            this.t_nurse.Multiline = true;
            this.t_nurse.Name = "t_nurse";
            this.t_nurse.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_nurse.SizeF = new System.Drawing.SizeF(49.88684F, 40.00002F);
            this.t_nurse.StylePriority.UseBorders = false;
            this.t_nurse.StylePriority.UseBorderWidth = false;
            this.t_nurse.StylePriority.UseFont = false;
            this.t_nurse.StylePriority.UseTextAlignment = false;
            this.t_nurse.Text = "执行  护士\r\n";
            this.t_nurse.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // t_doctor
            // 
            this.t_doctor.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.t_doctor.BorderWidth = 1F;
            this.t_doctor.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.t_doctor.LocationFloat = new DevExpress.Utils.PointFloat(489.5833F, 126F);
            this.t_doctor.Name = "t_doctor";
            this.t_doctor.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_doctor.SizeF = new System.Drawing.SizeF(46.52997F, 40.00002F);
            this.t_doctor.StylePriority.UseBorders = false;
            this.t_doctor.StylePriority.UseBorderWidth = false;
            this.t_doctor.StylePriority.UseFont = false;
            this.t_doctor.StylePriority.UseTextAlignment = false;
            this.t_doctor.Text = "医生\r\n";
            this.t_doctor.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // t_order_text
            // 
            this.t_order_text.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.t_order_text.BorderWidth = 1F;
            this.t_order_text.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.t_order_text.LocationFloat = new DevExpress.Utils.PointFloat(148.1667F, 126F);
            this.t_order_text.Name = "t_order_text";
            this.t_order_text.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_order_text.SizeF = new System.Drawing.SizeF(341.4166F, 39.99999F);
            this.t_order_text.StylePriority.UseBorders = false;
            this.t_order_text.StylePriority.UseBorderWidth = false;
            this.t_order_text.StylePriority.UseFont = false;
            this.t_order_text.StylePriority.UseTextAlignment = false;
            this.t_order_text.Text = "长期医嘱";
            this.t_order_text.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // t_time
            // 
            this.t_time.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.t_time.BorderWidth = 1F;
            this.t_time.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.t_time.LocationFloat = new DevExpress.Utils.PointFloat(98.99999F, 126F);
            this.t_time.Name = "t_time";
            this.t_time.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_time.SizeF = new System.Drawing.SizeF(49.16668F, 39.99996F);
            this.t_time.StylePriority.UseBorders = false;
            this.t_time.StylePriority.UseBorderWidth = false;
            this.t_time.StylePriority.UseFont = false;
            this.t_time.StylePriority.UseTextAlignment = false;
            this.t_time.Text = "时间";
            this.t_time.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // t_date
            // 
            this.t_date.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.t_date.BorderWidth = 1F;
            this.t_date.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.t_date.LocationFloat = new DevExpress.Utils.PointFloat(34F, 126F);
            this.t_date.Name = "t_date";
            this.t_date.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_date.SizeF = new System.Drawing.SizeF(65F, 39.99996F);
            this.t_date.StylePriority.UseBorders = false;
            this.t_date.StylePriority.UseBorderWidth = false;
            this.t_date.StylePriority.UseFont = false;
            this.t_date.StylePriority.UseTextAlignment = false;
            this.t_date.Text = "日期";
            this.t_date.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // t_stop
            // 
            this.t_stop.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right)));
            this.t_stop.BorderWidth = 1F;
            this.t_stop.Font = new System.Drawing.Font("宋体", 11F, System.Drawing.FontStyle.Bold);
            this.t_stop.LocationFloat = new DevExpress.Utils.PointFloat(587F, 106F);
            this.t_stop.Name = "t_stop";
            this.t_stop.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_stop.SizeF = new System.Drawing.SizeF(218F, 20F);
            this.t_stop.StylePriority.UseBorders = false;
            this.t_stop.StylePriority.UseBorderWidth = false;
            this.t_stop.StylePriority.UseFont = false;
            this.t_stop.StylePriority.UseTextAlignment = false;
            this.t_stop.Text = "停止";
            this.t_stop.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // t_start
            // 
            this.t_start.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)));
            this.t_start.BorderWidth = 1F;
            this.t_start.Font = new System.Drawing.Font("宋体", 11F, System.Drawing.FontStyle.Bold);
            this.t_start.LocationFloat = new DevExpress.Utils.PointFloat(34F, 106F);
            this.t_start.Name = "t_start";
            this.t_start.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_start.SizeF = new System.Drawing.SizeF(553F, 20F);
            this.t_start.StylePriority.UseBorders = false;
            this.t_start.StylePriority.UseBorderWidth = false;
            this.t_start.StylePriority.UseFont = false;
            this.t_start.StylePriority.UseTextAlignment = false;
            this.t_start.Text = "开始";
            this.t_start.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // hospital_name
            // 
            this.hospital_name.BorderWidth = 0F;
            this.hospital_name.Font = new System.Drawing.Font("Times New Roman", 14F, System.Drawing.FontStyle.Bold);
            this.hospital_name.LocationFloat = new DevExpress.Utils.PointFloat(39.16667F, 5.999994F);
            this.hospital_name.Name = "hospital_name";
            this.hospital_name.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.hospital_name.SizeF = new System.Drawing.SizeF(758.8333F, 30F);
            this.hospital_name.StylePriority.UseBorders = false;
            this.hospital_name.StylePriority.UseBorderWidth = false;
            this.hospital_name.StylePriority.UseFont = false;
            this.hospital_name.StylePriority.UseTextAlignment = false;
            this.hospital_name.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // id
            // 
            this.id.BorderWidth = 0F;
            this.id.LocationFloat = new DevExpress.Utils.PointFloat(693.1667F, 80.16666F);
            this.id.Name = "id";
            this.id.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.id.SizeF = new System.Drawing.SizeF(100F, 20F);
            this.id.StylePriority.UseBorderWidth = false;
            // 
            // t_id
            // 
            this.t_id.BorderWidth = 0F;
            this.t_id.LocationFloat = new DevExpress.Utils.PointFloat(663.1667F, 80.16666F);
            this.t_id.Name = "t_id";
            this.t_id.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_id.SizeF = new System.Drawing.SizeF(30F, 20F);
            this.t_id.StylePriority.UseBorderWidth = false;
            this.t_id.StylePriority.UseTextAlignment = false;
            this.t_id.Text = "ID";
            this.t_id.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopJustify;
            // 
            // inp_no
            // 
            this.inp_no.BorderWidth = 0F;
            this.inp_no.LocationFloat = new DevExpress.Utils.PointFloat(593.1667F, 80.16666F);
            this.inp_no.Name = "inp_no";
            this.inp_no.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.inp_no.SizeF = new System.Drawing.SizeF(70F, 20F);
            this.inp_no.StylePriority.UseBorderWidth = false;
            // 
            // t_inp_no
            // 
            this.t_inp_no.BorderWidth = 0F;
            this.t_inp_no.LocationFloat = new DevExpress.Utils.PointFloat(523.1667F, 80.16666F);
            this.t_inp_no.Name = "t_inp_no";
            this.t_inp_no.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_inp_no.SizeF = new System.Drawing.SizeF(70F, 20F);
            this.t_inp_no.StylePriority.UseBorderWidth = false;
            this.t_inp_no.StylePriority.UseTextAlignment = false;
            this.t_inp_no.Text = "住院号：";
            this.t_inp_no.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopJustify;
            // 
            // bed_no
            // 
            this.bed_no.BorderWidth = 0F;
            this.bed_no.LocationFloat = new DevExpress.Utils.PointFloat(473.1667F, 80.16666F);
            this.bed_no.Name = "bed_no";
            this.bed_no.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.bed_no.SizeF = new System.Drawing.SizeF(50F, 20F);
            this.bed_no.StylePriority.UseBorderWidth = false;
            // 
            // t_bed_no
            // 
            this.t_bed_no.BorderWidth = 0F;
            this.t_bed_no.LocationFloat = new DevExpress.Utils.PointFloat(413.1667F, 80.16666F);
            this.t_bed_no.Name = "t_bed_no";
            this.t_bed_no.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_bed_no.SizeF = new System.Drawing.SizeF(60F, 20F);
            this.t_bed_no.StylePriority.UseBorderWidth = false;
            this.t_bed_no.StylePriority.UseTextAlignment = false;
            this.t_bed_no.Text = "床号：";
            this.t_bed_no.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // dept
            // 
            this.dept.BorderWidth = 0F;
            this.dept.LocationFloat = new DevExpress.Utils.PointFloat(246.1667F, 80.16666F);
            this.dept.Name = "dept";
            this.dept.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.dept.SizeF = new System.Drawing.SizeF(166.8333F, 20F);
            this.dept.StylePriority.UseBorderWidth = false;
            // 
            // t_dept
            // 
            this.t_dept.BorderWidth = 0F;
            this.t_dept.LocationFloat = new DevExpress.Utils.PointFloat(186.1667F, 80.16666F);
            this.t_dept.Name = "t_dept";
            this.t_dept.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_dept.SizeF = new System.Drawing.SizeF(60F, 20F);
            this.t_dept.StylePriority.UseBorderWidth = false;
            this.t_dept.StylePriority.UseTextAlignment = false;
            this.t_dept.Text = "科室：";
            this.t_dept.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // name
            // 
            this.name.BorderWidth = 0F;
            this.name.LocationFloat = new DevExpress.Utils.PointFloat(86.16666F, 80.16666F);
            this.name.Name = "name";
            this.name.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.name.SizeF = new System.Drawing.SizeF(100F, 20F);
            this.name.StylePriority.UseBorderWidth = false;
            // 
            // t_name
            // 
            this.t_name.BorderWidth = 0F;
            this.t_name.LocationFloat = new DevExpress.Utils.PointFloat(36.16667F, 80.16666F);
            this.t_name.Name = "t_name";
            this.t_name.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_name.SizeF = new System.Drawing.SizeF(50F, 20F);
            this.t_name.StylePriority.UseBorderWidth = false;
            this.t_name.StylePriority.UseTextAlignment = false;
            this.t_name.Text = "姓名：";
            this.t_name.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // t_stopnurse
            // 
            this.t_stopnurse.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.t_stopnurse.BorderWidth = 1F;
            this.t_stopnurse.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.t_stopnurse.LocationFloat = new DevExpress.Utils.PointFloat(745F, 126F);
            this.t_stopnurse.Multiline = true;
            this.t_stopnurse.Name = "t_stopnurse";
            this.t_stopnurse.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_stopnurse.SizeF = new System.Drawing.SizeF(60F, 40F);
            this.t_stopnurse.StylePriority.UseBorders = false;
            this.t_stopnurse.StylePriority.UseBorderWidth = false;
            this.t_stopnurse.StylePriority.UseFont = false;
            this.t_stopnurse.StylePriority.UseTextAlignment = false;
            this.t_stopnurse.Text = "执行护   士签名";
            this.t_stopnurse.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // t_page
            // 
            this.t_page.LocationFloat = new DevExpress.Utils.PointFloat(344.1667F, 4.666687F);
            this.t_page.Name = "t_page";
            this.t_page.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_page.SizeF = new System.Drawing.SizeF(100F, 23F);
            // 
            // PageFooter
            // 
            this.PageFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.t_page});
            this.PageFooter.HeightF = 40.00001F;
            this.PageFooter.Name = "PageFooter";
            // 
            // PageHeader
            // 
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel1,
            this.t_nurse,
            this.t_stoptime,
            this.t_stopdate,
            this.t_stopdoctor,
            this.t_doctor,
            this.t_order_text,
            this.t_time,
            this.t_stop,
            this.t_start,
            this.hospital_name,
            this.orders_type,
            this.name,
            this.t_dept,
            this.dept,
            this.t_bed_no,
            this.bed_no,
            this.t_inp_no,
            this.inp_no,
            this.t_id,
            this.id,
            this.t_date,
            this.t_name,
            this.t_stopnurse});
            this.PageHeader.HeightF = 166F;
            this.PageHeader.Name = "PageHeader";
            // 
            // xrLabel1
            // 
            this.xrLabel1.BorderWidth = 0F;
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(776.9315F, 36F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(20F, 30F);
            this.xrLabel1.StylePriority.UseBorderWidth = false;
            this.xrLabel1.Text = ".";
            // 
            // orders_type
            // 
            this.orders_type.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.orders_type.BorderWidth = 0F;
            this.orders_type.Font = new System.Drawing.Font("Times New Roman", 18F, System.Drawing.FontStyle.Bold);
            this.orders_type.LocationFloat = new DevExpress.Utils.PointFloat(39.16667F, 44F);
            this.orders_type.Name = "orders_type";
            this.orders_type.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.orders_type.SizeF = new System.Drawing.SizeF(725F, 30F);
            this.orders_type.StylePriority.UseBorders = false;
            this.orders_type.StylePriority.UseBorderWidth = false;
            this.orders_type.StylePriority.UseFont = false;
            this.orders_type.StylePriority.UseTextAlignment = false;
            this.orders_type.Text = "长  期  医  嘱  单";
            this.orders_type.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrPictureDoctor
            // 
            this.xrPictureDoctor.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrPictureDoctor.LocationFloat = new DevExpress.Utils.PointFloat(2F, 5F);
            this.xrPictureDoctor.Name = "xrPictureDoctor";
            this.xrPictureDoctor.SizeF = new System.Drawing.SizeF(44.53003F, 30F);
            this.xrPictureDoctor.StylePriority.UseBorders = false;
            this.xrPictureDoctor.Visible = false;
            // 
            // xrPictureStopDoctor
            // 
            this.xrPictureStopDoctor.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrPictureStopDoctor.LocationFloat = new DevExpress.Utils.PointFloat(1.735044F, 5F);
            this.xrPictureStopDoctor.Name = "xrPictureStopDoctor";
            this.xrPictureStopDoctor.SizeF = new System.Drawing.SizeF(44.53003F, 30F);
            this.xrPictureStopDoctor.StylePriority.UseBorders = false;
            this.xrPictureStopDoctor.Visible = false;
            // 
            // XtraReportOrdersPrintRepeat
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageFooter,
            this.PageHeader});
            this.Margins = new System.Drawing.Printing.Margins(18, 7, 15, 11);
            this.ShowPrintMarginsWarning = false;
            this.SnapGridSize = 10F;
            this.Version = "17.2";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel hospital_name;
        private DevExpress.XtraReports.UI.XRLabel t_start;
        private DevExpress.XtraReports.UI.XRLabel t_stop;
        private DevExpress.XtraReports.UI.XRLabel t_order_text;
        private DevExpress.XtraReports.UI.XRLabel t_time;
        private DevExpress.XtraReports.UI.XRLabel t_date;
        private DevExpress.XtraReports.UI.XRLabel t_nurse;
        private DevExpress.XtraReports.UI.XRLabel t_doctor;
        private DevExpress.XtraReports.UI.XRLabel t_stopdoctor;
        private DevExpress.XtraReports.UI.XRLabel t_stoptime;
        private DevExpress.XtraReports.UI.XRLabel t_stopdate;
        private DevExpress.XtraReports.UI.XRTable xrTable1;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow1;
        private DevExpress.XtraReports.UI.XRTableCell date;
        private DevExpress.XtraReports.UI.XRTableCell time;
        private DevExpress.XtraReports.UI.XRTableCell order_text;
        private DevExpress.XtraReports.UI.XRTableCell joint_line;
        private DevExpress.XtraReports.UI.XRTableCell doctor;
        private DevExpress.XtraReports.UI.XRTableCell nurse;
        private DevExpress.XtraReports.UI.XRTableCell stop_date;
        private DevExpress.XtraReports.UI.XRTableCell stop_time;
        private DevExpress.XtraReports.UI.XRTableCell stop_doctor;
        private DevExpress.XtraReports.UI.XRTableCell stop_nurse;
        private DevExpress.XtraReports.UI.XRTableCell rowNum;
        private DevExpress.XtraReports.UI.XRLabel t_name;
        private DevExpress.XtraReports.UI.XRLabel name;
        private DevExpress.XtraReports.UI.XRLabel dept;
        private DevExpress.XtraReports.UI.XRLabel t_dept;
        private DevExpress.XtraReports.UI.XRLabel bed_no;
        private DevExpress.XtraReports.UI.XRLabel t_bed_no;
        private DevExpress.XtraReports.UI.XRLabel id;
        private DevExpress.XtraReports.UI.XRLabel t_id;
        private DevExpress.XtraReports.UI.XRLabel inp_no;
        private DevExpress.XtraReports.UI.XRLabel t_inp_no;
        private DevExpress.XtraReports.UI.XRLabel t_page;
        private DevExpress.XtraReports.UI.XRLabel t_stopnurse;
        private DevExpress.XtraReports.UI.PageFooterBand PageFooter;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.XRLabel orders_type;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell2;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell3;
        private DevExpress.XtraReports.UI.XRPictureBox xrPictureBox1;
        private DevExpress.XtraReports.UI.XRPictureBox xrPictureBox2;
        private DevExpress.XtraReports.UI.XRPictureBox xrPictureDoctor;
        private DevExpress.XtraReports.UI.XRPictureBox xrPictureStopDoctor;
    }
}
