﻿namespace PlatCommonForm
{
    partial class prepayReport
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraPrinting.BarCode.QRCodeGenerator qrCodeGenerator1 = new DevExpress.XtraPrinting.BarCode.QRCodeGenerator();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel9 = new DevExpress.XtraReports.UI.XRLabel();
            this.operator_no = new DevExpress.XtraReports.UI.XRLabel();
            this.amount = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel19 = new DevExpress.XtraReports.UI.XRLabel();
            this.upper_money = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.inp_no = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.pay_way = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.name = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.dept_admission_to = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.patient_id = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.rcpt_no = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.transact_date = new DevExpress.XtraReports.UI.XRLabel();
            this.hospital_name = new DevExpress.XtraReports.UI.XRLabel();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.xrBarCode_EHEALTH_CODE = new DevExpress.XtraReports.UI.XRBarCode();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrBarCode_EHEALTH_CODE,
            this.xrLabel1,
            this.xrLabel9,
            this.operator_no,
            this.amount,
            this.xrLabel19,
            this.upper_money,
            this.xrLabel8,
            this.inp_no,
            this.xrLabel7,
            this.pay_way,
            this.xrLabel6,
            this.name,
            this.xrLabel5,
            this.dept_admission_to,
            this.xrLabel4,
            this.patient_id,
            this.xrLabel2,
            this.rcpt_no,
            this.xrLabel3,
            this.transact_date,
            this.hospital_name});
            this.Detail.HeightF = 410.1667F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.SnapLinePadding = new DevExpress.XtraPrinting.PaddingInfo(10, 0, 10, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrLabel1
            // 
            this.xrLabel1.Font = new System.Drawing.Font("Times New Roman", 11F, System.Drawing.FontStyle.Bold);
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(46.24998F, 48.87497F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(247.5F, 23F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "预交金凭证";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // xrLabel9
            // 
            this.xrLabel9.Font = new System.Drawing.Font("宋体", 9F);
            this.xrLabel9.LocationFloat = new DevExpress.Utils.PointFloat(4.166651F, 285.4166F);
            this.xrLabel9.Name = "xrLabel9";
            this.xrLabel9.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel9.SizeF = new System.Drawing.SizeF(60.41666F, 23F);
            this.xrLabel9.StylePriority.UseFont = false;
            this.xrLabel9.StylePriority.UseTextAlignment = false;
            this.xrLabel9.Text = "大写：";
            this.xrLabel9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // operator_no
            // 
            this.operator_no.Font = new System.Drawing.Font("宋体", 10F);
            this.operator_no.LocationFloat = new DevExpress.Utils.PointFloat(67.70832F, 361.4583F);
            this.operator_no.Name = "operator_no";
            this.operator_no.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.operator_no.SizeF = new System.Drawing.SizeF(63.125F, 23F);
            this.operator_no.StylePriority.UseFont = false;
            this.operator_no.StylePriority.UseTextAlignment = false;
            this.operator_no.Text = "operator_no";
            this.operator_no.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // amount
            // 
            this.amount.Font = new System.Drawing.Font("宋体", 10F);
            this.amount.LocationFloat = new DevExpress.Utils.PointFloat(97.91665F, 323.9583F);
            this.amount.Name = "amount";
            this.amount.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.amount.SizeF = new System.Drawing.SizeF(139.5833F, 23F);
            this.amount.StylePriority.UseFont = false;
            this.amount.StylePriority.UseTextAlignment = false;
            this.amount.Text = "amount";
            this.amount.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel19
            // 
            this.xrLabel19.Font = new System.Drawing.Font("宋体", 9F);
            this.xrLabel19.LocationFloat = new DevExpress.Utils.PointFloat(4.166651F, 323.9583F);
            this.xrLabel19.Name = "xrLabel19";
            this.xrLabel19.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel19.SizeF = new System.Drawing.SizeF(93.75F, 22.99998F);
            this.xrLabel19.StylePriority.UseFont = false;
            this.xrLabel19.StylePriority.UseTextAlignment = false;
            this.xrLabel19.Text = "小写金额： ￥";
            this.xrLabel19.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // upper_money
            // 
            this.upper_money.Font = new System.Drawing.Font("宋体", 10F);
            this.upper_money.LocationFloat = new DevExpress.Utils.PointFloat(67.70832F, 285.4166F);
            this.upper_money.Name = "upper_money";
            this.upper_money.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.upper_money.SizeF = new System.Drawing.SizeF(256.25F, 23F);
            this.upper_money.StylePriority.UseFont = false;
            this.upper_money.StylePriority.UseTextAlignment = false;
            this.upper_money.Text = "upper_money";
            this.upper_money.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel8
            // 
            this.xrLabel8.Font = new System.Drawing.Font("宋体", 10F);
            this.xrLabel8.LocationFloat = new DevExpress.Utils.PointFloat(4.166651F, 361.4583F);
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.SizeF = new System.Drawing.SizeF(60.41667F, 23F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            this.xrLabel8.Text = "操作员：";
            this.xrLabel8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // inp_no
            // 
            this.inp_no.Font = new System.Drawing.Font("宋体", 10F);
            this.inp_no.LocationFloat = new DevExpress.Utils.PointFloat(220.8332F, 180.1667F);
            this.inp_no.Name = "inp_no";
            this.inp_no.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.inp_no.SizeF = new System.Drawing.SizeF(103.125F, 23F);
            this.inp_no.StylePriority.UseFont = false;
            this.inp_no.StylePriority.UseTextAlignment = false;
            this.inp_no.Text = "inp_no";
            this.inp_no.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel7
            // 
            this.xrLabel7.Font = new System.Drawing.Font("宋体", 9F);
            this.xrLabel7.LocationFloat = new DevExpress.Utils.PointFloat(164.5833F, 180.1667F);
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.SizeF = new System.Drawing.SizeF(56.25002F, 23F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            this.xrLabel7.Text = "住院号：";
            this.xrLabel7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // pay_way
            // 
            this.pay_way.Font = new System.Drawing.Font("宋体", 10F);
            this.pay_way.LocationFloat = new DevExpress.Utils.PointFloat(86.45832F, 250F);
            this.pay_way.Name = "pay_way";
            this.pay_way.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.pay_way.SizeF = new System.Drawing.SizeF(127.0833F, 23F);
            this.pay_way.StylePriority.UseFont = false;
            this.pay_way.StylePriority.UseTextAlignment = false;
            this.pay_way.Text = "pay_way";
            this.pay_way.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Font = new System.Drawing.Font("宋体", 10F);
            this.xrLabel6.LocationFloat = new DevExpress.Utils.PointFloat(4.166651F, 249.9999F);
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.SizeF = new System.Drawing.SizeF(82.29166F, 23F);
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            this.xrLabel6.Text = "支付方式：";
            this.xrLabel6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // name
            // 
            this.name.Font = new System.Drawing.Font("宋体", 10F);
            this.name.LocationFloat = new DevExpress.Utils.PointFloat(190.625F, 118.7917F);
            this.name.Name = "name";
            this.name.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.name.SizeF = new System.Drawing.SizeF(103.125F, 23F);
            this.name.StylePriority.UseFont = false;
            this.name.StylePriority.UseTextAlignment = false;
            this.name.Text = "name";
            this.name.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel5
            // 
            this.xrLabel5.Font = new System.Drawing.Font("宋体", 9F);
            this.xrLabel5.LocationFloat = new DevExpress.Utils.PointFloat(147.9167F, 118.7917F);
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.SizeF = new System.Drawing.SizeF(45.83333F, 23.00001F);
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            this.xrLabel5.Text = "姓名：";
            this.xrLabel5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // dept_admission_to
            // 
            this.dept_admission_to.Font = new System.Drawing.Font("宋体", 10F);
            this.dept_admission_to.LocationFloat = new DevExpress.Utils.PointFloat(86.45832F, 215.7499F);
            this.dept_admission_to.Name = "dept_admission_to";
            this.dept_admission_to.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.dept_admission_to.SizeF = new System.Drawing.SizeF(134.375F, 23F);
            this.dept_admission_to.StylePriority.UseFont = false;
            this.dept_admission_to.StylePriority.UseTextAlignment = false;
            this.dept_admission_to.Text = "dept_admission_to";
            this.dept_admission_to.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel4
            // 
            this.xrLabel4.Font = new System.Drawing.Font("宋体", 10F);
            this.xrLabel4.LocationFloat = new DevExpress.Utils.PointFloat(4.166651F, 215.7499F);
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.SizeF = new System.Drawing.SizeF(82.29166F, 23F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            this.xrLabel4.Text = "入院科室：";
            this.xrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // patient_id
            // 
            this.patient_id.Font = new System.Drawing.Font("宋体", 10F);
            this.patient_id.LocationFloat = new DevExpress.Utils.PointFloat(183.3333F, 83.29169F);
            this.patient_id.Name = "patient_id";
            this.patient_id.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.patient_id.SizeF = new System.Drawing.SizeF(120.8333F, 23F);
            this.patient_id.StylePriority.UseFont = false;
            this.patient_id.StylePriority.UseTextAlignment = false;
            this.patient_id.Text = "patient_id";
            this.patient_id.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel2
            // 
            this.xrLabel2.Font = new System.Drawing.Font("宋体", 9F);
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(147.9167F, 83.29166F);
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(35.41664F, 23F);
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            this.xrLabel2.Text = "ID：";
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // rcpt_no
            // 
            this.rcpt_no.Font = new System.Drawing.Font("宋体", 10F);
            this.rcpt_no.LocationFloat = new DevExpress.Utils.PointFloat(64.58335F, 180.1667F);
            this.rcpt_no.Name = "rcpt_no";
            this.rcpt_no.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.rcpt_no.SizeF = new System.Drawing.SizeF(103.125F, 23F);
            this.rcpt_no.StylePriority.UseFont = false;
            this.rcpt_no.StylePriority.UseTextAlignment = false;
            this.rcpt_no.Text = "rcpt_no";
            this.rcpt_no.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel3
            // 
            this.xrLabel3.Font = new System.Drawing.Font("宋体", 10F);
            this.xrLabel3.LocationFloat = new DevExpress.Utils.PointFloat(4.166667F, 180.1667F);
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel3.SizeF = new System.Drawing.SizeF(60.41666F, 23F);
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            this.xrLabel3.Text = "收据号：";
            this.xrLabel3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // transact_date
            // 
            this.transact_date.Font = new System.Drawing.Font("宋体", 10F);
            this.transact_date.LocationFloat = new DevExpress.Utils.PointFloat(143.75F, 361.4583F);
            this.transact_date.Name = "transact_date";
            this.transact_date.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.transact_date.SizeF = new System.Drawing.SizeF(180.2083F, 23F);
            this.transact_date.StylePriority.UseFont = false;
            this.transact_date.StylePriority.UseTextAlignment = false;
            this.transact_date.Text = "transact_date";
            this.transact_date.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // hospital_name
            // 
            this.hospital_name.Font = new System.Drawing.Font("Times New Roman", 11F, System.Drawing.FontStyle.Bold);
            this.hospital_name.LocationFloat = new DevExpress.Utils.PointFloat(46.24996F, 10.00001F);
            this.hospital_name.Name = "hospital_name";
            this.hospital_name.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.hospital_name.SizeF = new System.Drawing.SizeF(247.5F, 23F);
            this.hospital_name.StylePriority.UseFont = false;
            this.hospital_name.StylePriority.UseTextAlignment = false;
            this.hospital_name.Text = "hospital_name";
            this.hospital_name.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // TopMargin
            // 
            this.TopMargin.HeightF = 0F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // BottomMargin
            // 
            this.BottomMargin.HeightF = 0F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrBarCode_EHEALTH_CODE
            // 
            this.xrBarCode_EHEALTH_CODE.AutoModule = true;
            this.xrBarCode_EHEALTH_CODE.Font = new System.Drawing.Font("Times New Roman", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrBarCode_EHEALTH_CODE.LocationFloat = new DevExpress.Utils.PointFloat(9.999998F, 71.87497F);
            this.xrBarCode_EHEALTH_CODE.Name = "xrBarCode_EHEALTH_CODE";
            this.xrBarCode_EHEALTH_CODE.Padding = new DevExpress.XtraPrinting.PaddingInfo(10, 10, 0, 0, 100F);
            this.xrBarCode_EHEALTH_CODE.ShowText = false;
            this.xrBarCode_EHEALTH_CODE.SizeF = new System.Drawing.SizeF(134.3751F, 95.4167F);
            this.xrBarCode_EHEALTH_CODE.StylePriority.UseFont = false;
            qrCodeGenerator1.CompactionMode = DevExpress.XtraPrinting.BarCode.QRCodeCompactionMode.Byte;
            qrCodeGenerator1.ErrorCorrectionLevel = DevExpress.XtraPrinting.BarCode.QRCodeErrorCorrectionLevel.Q;
            qrCodeGenerator1.Version = DevExpress.XtraPrinting.BarCode.QRCodeVersion.Version10;
            this.xrBarCode_EHEALTH_CODE.Symbology = qrCodeGenerator1;
            // 
            // prepayReport
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin});
            this.Margins = new System.Drawing.Printing.Margins(0, 3, 0, 0);
            this.PageHeight = 400;
            this.PageWidth = 350;
            this.PaperKind = System.Drawing.Printing.PaperKind.Custom;
            this.PaperName = "预交金";
            this.Version = "17.2";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel hospital_name;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRLabel transact_date;
        private DevExpress.XtraReports.UI.XRLabel rcpt_no;
        private DevExpress.XtraReports.UI.XRLabel dept_admission_to;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.XRLabel patient_id;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel upper_money;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel inp_no;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel pay_way;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLabel name;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel xrLabel19;
        private DevExpress.XtraReports.UI.XRLabel amount;
        private DevExpress.XtraReports.UI.XRLabel xrLabel9;
        private DevExpress.XtraReports.UI.XRLabel operator_no;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRBarCode xrBarCode_EHEALTH_CODE;
    }
}
