﻿using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Utility;

namespace TJHisPlat
{
    /// <summary>
    /// 登录窗体
    /// </summary>
    public partial class FrmLoginNew : Form
    {       
        public String UserName = "";
        public String PassWord = "";
        int ii_err_cs = 0, ii_pw_err;//密码错误次数判断 设置后 错误多次后自动关闭
        string ls_init_pw;// 是否判断初始值密码 给予提示 修改密码

        public FrmLoginNew()
        {
            InitializeComponent();
        }

        private void Frm_LoginNew_Load(object sender, EventArgs e)
        {
            try
            {
                Thread t1;
                t1 = new Thread(LoadData);
                t1.Start();
                if (!string.IsNullOrEmpty(UserName))
                {
                    txtInputNo.Text = UserName;
                    txtPwd.Text = PassWord;
                    this.lblLogin_Click(lblLogin, null);
                }
                else
                {
                    txtInputNo.Text = Utility.ConfigHelper.GetConfiguration("LastLoginUser");
                    if (!string.IsNullOrEmpty(txtInputNo.Text))
                    {
                        txtPwd.Focus();
                        GetLoginData(txtInputNo.Text);
                    }
                }
            }
            catch (Exception ex)
            {
            }

        }
        /// <summary>
        /// 控制窗体控件加载刷新闪烁问题 
        /// </summary>
        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ExStyle |= 0x02000000;
                return cp;
            }
        }
        /// <summary>
        /// 加载数据
        /// </summary>
        private void LoadData()
        {
            try
            {
                string path = System.IO.Directory.GetCurrentDirectory() + "\\Images\\System\\LoginUI\\" + "LoginBackgroundImage.jpg";
                this.BackgroundImage = Image.FromFile(path);
                path = System.IO.Directory.GetCurrentDirectory() + "\\Images\\System\\LoginUI\\" + "LoginImage.png";
                panel1.BackgroundImage = Image.FromFile(path);
            
                //加载版本号
                string his_unit_code= Utility.ConfigHelper.GetConfiguration("UNITCODE");
                string strsql = " select * from  hospital_config where rownum=1 and his_unit_code='"+ his_unit_code + "' "; 
                DataSet ds = new OracleDAL.ServerPublic_Dao().GetDataBySql(strsql); 
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    this.lblVersion.Text = "版本号：" + ds.Tables[0].Rows[0]["AUTHORIZED_KEY"].ToString();
                    
                    lbhospital.Text= ds.Tables[0].Rows[0]["hospital"].ToString()+" | "+"国家医保编码"+ ds.Tables[0].Rows[0]["yb_hospitalcode"].ToString();
                    DateTime dtdate = new OracleDAL.ServerPublic_Dao().GetSysDate() ;
                    Copyright.Text = "Copyright © 1995 - "+ dtdate.ToString("yyyy") + " 北京天健源达科技股份有限公司";
                    
                    //全局变量赋值 Yzw20240410
                    SystemParm.version = ds.Tables[0].Rows[0]["AUTHORIZED_KEY"].ToString();
                    SystemParm.HospitalID = ds.Tables[0].Rows[0]["hospital"].ToString();
                }
                SystemParm.HisUnitCode = his_unit_code;

                ii_pw_err = Int32.Parse(PlatCommon.SysBase.SystemParm.GetParaValue("PW_ERR", "*", "*", "*", "0"));
                ls_init_pw = (PlatCommon.SysBase.SystemParm.GetParaValue("INIT_PW", "*", "*", "*", "0"));// 是否判断初始值密码 给予提示 修改密码
            }
            catch
            {
            }
        }
        /// <summary>
        /// 登录
        /// </summary>
        public void LoginCommandExecute()
        { 
         
            if (String.IsNullOrEmpty(txtInputNo.Text))
            {
                XtraMessageBox.Show("请输入用户！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtInputNo.Focus();             
                return;
            }
            if (String.IsNullOrEmpty(txtPwd.Text))
            {
                XtraMessageBox.Show("请输密码！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtPwd.Focus();
                return;
            }

            NM_Service.NMService.STAFF_DICTClient client = new NM_Service.NMService.STAFF_DICTClient();
            int result = client.LoginValidate_STAFF_DICT(txtInputNo.Text.ToUpper(), txtPwd.Text.ToUpper());
            switch (result)
            {
                case -1:
                    XtraMessageBox.Show("用户不存在！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break;
                case 0:
                    ii_err_cs++;
                    XtraMessageBox.Show("密码错误！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    if (ii_err_cs >= ii_pw_err && ii_pw_err > 0)
                    {
                        XtraMessageBox.Show("密码错误！超过" + ii_err_cs.ToString() + "次", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        this.Close();
                    }
                    break;
                case 1:
                    if (txtInputNo.Text.ToUpper().Equals(txtPwd.Text.ToUpper()) && ls_init_pw.Equals("1"))
                    {
                        XtraMessageBox.Show("你的密码为初始密码，请登陆后修改密码！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }

                    SystemParm.LoginUser = client.GetModelByUserName_STAFF_DICT(txtInputNo.Text.ToUpper());
                    PlatCommon.SysBase.SystemParm.HisUnitCode = Utility.ConfigHelper.GetConfiguration("UNITCODE");
                    this.UserName = txtInputNo.Text.Trim();
                    this.PassWord = txtPwd.Text.Trim();
                    //特殊用户直接进入主界面 梁吉 2016-08-25
                    if ("TJSYSTEM".Equals(PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME.ToUpper()))
                    {
                        this.DialogResult = System.Windows.Forms.DialogResult.OK;
                        this.Close();
                        return;
                    }                    
                    Utility.ConfigHelper.SetConfiguration("LastLoginUser", txtInputNo.Text.Trim());//记录最后登录用户

                    #region 根据参数初始化CA，以免用户使用用户名密码登录后，后续使用ca签名时出错。
                    bool caEnabled = Tjhis.Interface.CA.DbExt.getCaEnabled(SystemParm.HisUnitCode, "", "", "");
                    string CA_USER_ENABLED = Tjhis.Interface.CA.DbExt.getCaEnabledByUserName(txtInputNo.Text.ToUpper()) ? "1" : "0";
                    SystemParm.LoginUser.CA_ENABLED = CA_USER_ENABLED;
                    //if (caEnabled && CA_USER_ENABLED.Equals("1"))
                    if (caEnabled)
                    {
                        if (SystemParm.CaBusiness == null)
                        {
                            PlatCommon.SysBase.SystemParm.CaBusiness = new Tjhis.Interface.CA.CaBusiness();
                            PlatCommon.SysBase.SystemParm.CaBusiness.HisUnitCode = PlatCommon.SysBase.SystemParm.HisUnitCode;
                            PlatCommon.SysBase.SystemParm.CaBusiness.Init();
                        }
                    }
                    #endregion
                    this.DialogResult = System.Windows.Forms.DialogResult.OK;
                    this.Close();
                    break;
                case 2:
                    XtraMessageBox.Show("用户已锁定，请联系管理员！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break;
                default:
                    XtraMessageBox.Show("未知错误！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break;
            }
        }
        public void LoginCa()
        {
            if (String.IsNullOrEmpty(txtInputNo.Text))
            {
                XtraMessageBox.Show("请输入用户！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtInputNo.Focus();
                return;
            }
            //if (String.IsNullOrEmpty(txtPwd.Text))
            //{
            //    XtraMessageBox.Show("请输密码！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //    txtPwd.Focus();
            //    return;
            //}
            #region
            //暂时没判断科室，后期看需求调整
            PlatCommon.SysBase.SystemParm.HisUnitCode = Utility.ConfigHelper.GetConfiguration("UNITCODE");
            string user = txtInputNo.Text.ToUpper();

            bool caEnabled = Tjhis.Interface.CA.DbExt.getCaEnabled(SystemParm.HisUnitCode,"", "", "");
            
            //SystemParm.LoginUser.CA_ENABLED = Tjhis.Interface.CA.DbExt.getCaEnabledByUserName(user) ?"1":"0";
            string CA_USER_ENABLED = Tjhis.Interface.CA.DbExt.getCaEnabledByUserName(user) ? "1" : "0";
            
            if (caEnabled && CA_USER_ENABLED.Equals("1"))
            {
                if (SystemParm.CaBusiness == null)
                {
                    PlatCommon.SysBase.SystemParm.CaBusiness = new Tjhis.Interface.CA.CaBusiness();
                    PlatCommon.SysBase.SystemParm.CaBusiness.HisUnitCode = PlatCommon.SysBase.SystemParm.HisUnitCode;
                    PlatCommon.SysBase.SystemParm.CaBusiness.Init();
                }
            }
            else
            {
                XtraMessageBox.Show("该用户没有设置CA登录，请联系管理员设置，或使用账号密码登录！", "异常信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return  ;
            }
            string psw = "";

            NM_Service.NMService.STAFF_DICTClient client = new NM_Service.NMService.STAFF_DICTClient();
            Model.STAFF_DICT staffDict= client.GetModelByUserName_STAFF_DICT(txtInputNo.Text.ToUpper());
            string userId = staffDict.ID;
            bool res= PlatCommon.SysBase.SystemParm.CaBusiness.CALogin(ref userId, ref psw);

            //if (res == false && PlatCommon.SysBase.SystemParm.CaBusiness.CaMandatorySign)
            //{
            //    XtraMessageBox.Show("请输入正确的登录名，并使用CA完成登录！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //    txtPwd.Focus();
            //    return;
            //}

            if (res == false)
            {
                XtraMessageBox.Show("CA登录失败，请输入正确的登录名，并使用CA完成登录！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtPwd.Focus();
                return;
            }
            #endregion



            //SystemParm.LoginUser = client.GetModelByUserName_STAFF_DICT(txtInputNo.Text.ToUpper());
            SystemParm.LoginUser = staffDict;
            SystemParm.LoginUser.CA_ENABLED= CA_USER_ENABLED;
            this.UserName = txtInputNo.Text.Trim();
            this.PassWord = Gloobal.DecryptHIS(staffDict.PASSWORD);
            //特殊用户直接进入主界面 梁吉 2016-08-25
            if ("TJSYSTEM".Equals(PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME.ToUpper()))
            {
                this.DialogResult = System.Windows.Forms.DialogResult.OK;
                this.Close();
                return;
            }
            Utility.ConfigHelper.SetConfiguration("LastLoginUser", txtInputNo.Text.Trim());//记录最后登录用户
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.Close();
        }
        private void lblLogin_Click(object sender, EventArgs e)
        {
            try
            {
                if (lbtabca.Visible)
                    LoginCa();
                else
                    LoginCommandExecute();
            }
            catch (Exception ex)
            {
                Utility.LogFile.WriteLogAuto(ex.Message, "CA");
                XtraMessageBox.Show("登录异常，请检查数据库配置", "异常信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
        }
        private void FrmLogin_Activated(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtInputNo.Text.Trim()))
            {
                txtInputNo.Focus();
            }
            else
            {
                txtPwd.Focus();
            }
        }

        private void GetLoginData(string UserName)
        {
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select b.dept_name, a.name from staff_dict a,dept_dict b where a.dept_code=b.dept_code and upper(a.user_name)='" + UserName.ToUpper() + "'").Tables[0];
            if (dt.Rows.Count > 0)
            {
                lLoginData.Text = dt.Rows[0][0].ToString() + "   " + dt.Rows[0][1].ToString();
            }
        }
        private void FrmLogin_MouseDown(object sender, MouseEventArgs e)
        {

        }
        //右键弹出数据库设置
        private void tableLayoutPanel1_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                FrmDbConfig frmDbConfig = new FrmDbConfig();
                frmDbConfig.ShowDialog();
                if (frmDbConfig.DialogResult == DialogResult.OK)
                {
                    try
                    {
                        Utility.UntilityConstant.DataConnectionString = Utility.ConfigHelper.GetConfigConnectionStr();
                        if (string.IsNullOrEmpty(Utility.UntilityConstant.DataConnectionString))
                        {
                            XtraMessageBox.Show("未检索到数据库配置!");
                            Application.Exit();
                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        XtraMessageBox.Show(ex.Message);
                    }
                }
            }
        }

        private void lbzhlogin_Click(object sender, EventArgs e)
        {
            lbtabca.Visible = false;
            lbtablg.Visible = true;
            this.lbcalogin.Font = new System.Drawing.Font("微软雅黑", 14F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbcalogin.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(102)))), ((int)(((byte)(102)))), ((int)(((byte)(102)))));
            this.lbzhlogin.Font = new System.Drawing.Font("微软雅黑", 14F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbzhlogin.ForeColor = System.Drawing.Color.Black;
        }

        private void lbcalogin_Click(object sender, EventArgs e)
        {
            lbtabca.Visible = true ;
            lbtablg.Visible = false;
            this.lbzhlogin.Font = new System.Drawing.Font("微软雅黑", 14F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbzhlogin.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(102)))), ((int)(((byte)(102)))), ((int)(((byte)(102)))));
            this.lbcalogin.Font = new System.Drawing.Font("微软雅黑", 14F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbcalogin.ForeColor = System.Drawing.Color.Black;

            //此处增加CA登录用到的相关判断 预留/

        }

        private void txtInputNo_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)//如果输入的是回车键
            {
                GetLoginData(txtInputNo.Text);
                txtPwd.Focus();
            }
        }

        private void txtPwd_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)//如果输入的是回车键
            {
                try
                {
                    if (lbtabca.Visible)
                        LoginCa();
                    else
                        LoginCommandExecute();
                }
                catch (Exception ex)
                {
                    XtraMessageBox.Show(ex.Message + "登录异常，请检查数据库配置", "异常信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    Application.Exit();
                }
            }
        }

        private void lblLogin_MouseHover(object sender, EventArgs e)
        {
          string  path = System.IO.Directory.GetCurrentDirectory() + "\\Images\\System\\LoginUI\\" + "登录圆角.png";
          lblLogin.BackgroundImage = Image.FromFile(path);
        }

        private void lblLogin_MouseLeave(object sender, EventArgs e)
        {
           string path = System.IO.Directory.GetCurrentDirectory() + "\\Images\\System\\LoginUI\\" + "点击状态.png";
            lblLogin.BackgroundImage = Image.FromFile(path);
        }
    }
}
