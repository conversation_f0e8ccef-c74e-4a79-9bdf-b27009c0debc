﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

/// <summary>
/// 病人信息类
/// </summary>
namespace PlatCommon.Comm
{
    public class Class_pat_info
    {
        NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
        public  int Uf_Card_Memo( string _CARDNO  , ref string _PATIENTID , ref string _MEMBER_NO , ref string _ERRORTEXT)
        {
            string strMEDICAL_CARD_MEMO = "SELECT ACCOUNT_STATUS ,  PATIENT_ID , MEMBER_NO FROM MEDICAL_CARD_MEMO WHERE CARD_NO ='" + _CARDNO + "'";
            DataSet dsMEMO = spc.GetList(strMEDICAL_CARD_MEMO);
            if (dsMEMO != null && dsMEMO.Tables[0].Rows.Count > 0)
            {
                string strACCOUNT_STATUS = dsMEMO.Tables[0].Rows[0]["ACCOUNT_STATUS"].ToString();
                _PATIENTID = "";
                switch ( strACCOUNT_STATUS )
                {
                    case "0":
                        _PATIENTID = dsMEMO.Tables[0].Rows[0]["PATIENT_ID"].ToString();
                        _MEMBER_NO = dsMEMO.Tables[0].Rows[0]["MEMBER_NO"].ToString();
                        return 0 ;
                    case "1":
                        _ERRORTEXT = "此卡已被“挂失”，请取消挂失后再使用此卡.ACCOUNT_STATUS值:" + strACCOUNT_STATUS;
                        return -1;
                    case "2":
                        _ERRORTEXT = "此卡已被“注销”，不能够再使用.ACCOUNT_STATUS值:" + strACCOUNT_STATUS;
                        return -1;
                    default:
                        _ERRORTEXT = "无效标记卡，请到相关部门核实后再使用.ACCOUNT_STATUS值:" + strACCOUNT_STATUS;
                        return -1; 
                } 
            }
            else
            {
                string sqlPat = "SELECT PATIENT_ID FROM pat_master_index WHERE ID_NO ='" + _CARDNO + "'";
                DataSet dsPat = spc.GetList(sqlPat);
                if (dsPat != null && dsPat.Tables[0].Rows.Count > 0)
                {
                    _PATIENTID = dsPat.Tables[0].Rows[0]["PATIENT_ID"].ToString();
                    return 0;
                }
                else
                {
                    _PATIENTID = "";
                    _ERRORTEXT = "没有找到表身份证号为" + _CARDNO + "的患者信息";
                    return -1;
                }
            } 
        }

        public  void Uf_Pat_Info(string _PATIENT_ID , ref string _ERRORTEXT , 
            ref string _CHARGE_TYPE , ref string _NAME , ref string _NAMEPHONETIC ,
            ref string _SEX ,  ref string _UNIT_IN_CONTRACT ,
            ref string _DATE_OF_BIRTH , ref string _ID_NO )
        {
            if (string.IsNullOrEmpty(_PATIENT_ID)) return; 
            DataSet dsPAT = spc.GetList("SELECT * FROM PAT_MASTER_INDEX WHERE PATIENT_ID='"+_PATIENT_ID+"'");
            if (dsPAT != null && dsPAT.Tables[0].Rows.Count > 0)
            {
                _CHARGE_TYPE = dsPAT.Tables[0].Rows[0]["CHARGE_TYPE"].ToString();
                _NAME = dsPAT.Tables[0].Rows[0]["NAME"].ToString();
                _NAMEPHONETIC = dsPAT.Tables[0].Rows[0]["NAME_PHONETIC"].ToString();
                _SEX = dsPAT.Tables[0].Rows[0]["SEX"].ToString(); 
                _UNIT_IN_CONTRACT = dsPAT.Tables[0].Rows[0]["UNIT_IN_CONTRACT"].ToString();
                _DATE_OF_BIRTH = dsPAT.Tables[0].Rows[0]["DATE_OF_BIRTH"].ToString();
                _ID_NO = dsPAT.Tables[0].Rows[0]["ID_NO"].ToString();
            }
            else
            {
                _ERRORTEXT = "PAT_MASTER_INDEX表中没有找到病人ID号为'" +_PATIENT_ID+ "'病人信息";
                return;
            }
            if (_CHARGE_TYPE.Equals("沈阳干诊")) return;
            if (_PATIENT_ID.Equals("**********")) return; 
            string strVISIT_ID = string.Empty;
            DataSet dsPAT_VISIT = spc.GetList("SELECT MAX(VISIT_ID) FROM PAT_VISIT WHERE PATIENT_ID='"+ _PATIENT_ID + "'");
            if (dsPAT_VISIT != null && !string.IsNullOrEmpty(dsPAT_VISIT.Tables[0].Rows[0][0].ToString()))
            {
                strVISIT_ID = dsPAT_VISIT.Tables[0].Rows[0][0].ToString(); 
            }
            else
            { 
                return;
            }

            DataSet dsINP_SETTLE_MASTER = spc.GetList( "SELECT * FROM INP_SETTLE_MASTER WHERE PATIENT_ID ='" +_PATIENT_ID+ "' AND VISIT_ID = '"+ strVISIT_ID +"'");
            string strTRANSACT_TYPE = string.Empty;
            string strSETTLE_TYPE_NAME = string.Empty;
            if (dsINP_SETTLE_MASTER != null && dsINP_SETTLE_MASTER.Tables[0].Rows.Count > 0  )
            {
                strTRANSACT_TYPE = dsINP_SETTLE_MASTER.Tables[0].Rows[0]["TRANSACT_TYPE"].ToString();
                strSETTLE_TYPE_NAME = dsINP_SETTLE_MASTER.Tables[0].Rows[0]["SETTLE_TYPE_NAME"].ToString();
                if (strTRANSACT_TYPE.Equals("正常") && strSETTLE_TYPE_NAME.Equals("出院全结"))
                {

                }
                else
                {
                    _ERRORTEXT = "患者"+_PATIENT_ID+"在本医院住院中，还没有结算，不能继续挂号收费";
                    return;
                }
            }
            else
            {
                return;
            }
        }


        //查询会员等级
        public string Get_Member_name(string Member_no)
        {
            string Member_name = "";
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select member_no , member_name from medrec.Member_dict where member_no='" + Member_no + "'").Tables[0];
            if (dt.Rows.Count > 0)
            {
                Member_name = dt.Rows[0]["member_name"].ToString();
            }
            else
            {
                Member_name = "";
            }

            return Member_name;
        }
    }
}
