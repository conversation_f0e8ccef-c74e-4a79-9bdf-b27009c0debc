﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace PlatCommon.Base01
{
    /// <summary>
    /// 通用静态类
    /// </summary>
    public static class GCCLass
    {
        /// <summary>
        /// 执行过程中需要传递的消息
        /// </summary>
        public static string Message;

        /// <summary>
        /// 转换为字符
        /// </summary>
        /// <param name="obj">object</param>
        /// <returns>string</returns>
        public static string CStr(object obj)
        {
            if (obj == null) return String.Empty;
            return obj.ToString();
        }

        /// <summary>
        /// 转换为字符
        /// </summary>
        /// <param name="obj">object</param>
        /// <param name="defValue">string</param>
        /// <returns>string</returns>
        public static string CStrOrDefault(object obj, string defValue)
        {
            if (obj == null) return defValue;
            return obj.ToString();
        }

        /// <summary>
        /// 转换为字符串并删除前后空格
        /// </summary>
        /// <param name="obj">object</param>
        /// <returns>string</returns>
        public static string CTrim(object obj)
        {
            if (obj == null) return String.Empty;
            return obj.ToString().Trim();
        }

        /// <summary>
        /// null转化为转换为String.Empty，并删除前后空格
        /// </summary>
        /// <param name="strVal">string</param>
        /// <returns>string</returns>
        public static string CTrim(string strVal)
        {
            if (strVal == null) return String.Empty;
            return strVal.Trim();
        }

        /// <summary>
        /// 转换为整数
        /// </summary>
        /// <param name="obj">object</param>
        /// <returns>int</returns>
        public static int CInt(object obj)
        {
            if (obj == null) return 0;
            return Convert.ToInt32(obj);
        }

        /// <summary>
        /// 转换为整数
        /// </summary>
        /// <param name="strVal">string</param>
        /// <returns>int</returns>
        public static int CInt(string strVal)
        {
            int v;
            if (string.IsNullOrEmpty(strVal)) return 0;
            int.TryParse(strVal, out v);
            return v;
        }

        /// <summary>
        /// 转换为double
        /// </summary>
        /// <param name="obj">object</param>
        /// <returns>double</returns>
        public static double CDbl(object obj)
        {
            if (obj == null) return 0;
            return Convert.ToDouble(obj);
        }

        /// <summary>
        /// 转换为double
        /// </summary>
        /// <param name="strVal">string</param>
        /// <returns>int</returns>
        public static double CDbl(string strVal)
        {
            double v;
            if (string.IsNullOrEmpty(strVal)) return 0;
            double.TryParse(strVal, out v);
            return v;
        }

        /// <summary>
        /// 转换为decimal
        /// </summary>
        /// <param name="obj">object</param>
        /// <returns>decimal</returns>
        public static decimal CDecimal(object obj)
        {
            if (obj == null) return 0;
            return Convert.ToDecimal(obj);
        }

        /// <summary>
        /// 转换为decimal
        /// </summary>
        /// <param name="strVal">string</param>
        /// <returns>int</returns>
        public static decimal CDecimal(string strVal)
        {
            decimal v = 0M;
            if (string.IsNullOrEmpty(strVal)) return v;
            decimal.TryParse(strVal, out v);
            return v;
        }

        /// <summary>
        /// 取精确到秒的当前时间
        /// </summary>
        /// <returns>DateTime</returns>
        public static DateTime Now()
        {
            DateTime now = DateTime.Now;
            return now.AddMilliseconds(-now.Millisecond);
        }

        /// <summary>
        /// 转换为DateTime
        /// </summary>
        /// <param name="obj">object</param>
        /// <returns>为空时转为DateTime(0,1,1)</returns>
        public static DateTime CDateTime(object obj)
        {
            if (obj == null || obj == DBNull.Value) return DateTime.MinValue;
            return Convert.ToDateTime(obj);
        }

        /// <summary>
        /// 转换为DateTime
        /// </summary>
        /// <param name="obj">object</param>
        /// <returns>为空时转为DateTime.Now</returns>
        public static DateTime CDateTimeOrDefaultNow(object obj)
        {
            if (obj == null) return Now();
            return Convert.ToDateTime(obj);
        }

        /// <summary>
        /// 转换为DateTime
        /// </summary>
        /// <param name="str">string</param>
        /// <param name="defValue">DateTime</param>
        /// <returns>为空时转为defValue</returns>
        public static DateTime CDateTimeOrDefault(string str, DateTime defValue)
        {
            DateTime time;
            if (DateTime.TryParse(str, out time))
                return time;
            return defValue;
        }

        /// <summary>
        /// 转化为日期字符串
        /// </summary>
        /// <param name="obj">object</param>
        /// <returns>格式: yyyy-MM-dd HH:mm:ss</returns>
        public static string CDateTimeString(object obj)
        {
            if (obj == null) return string.Empty;
            DateTime dtime = Convert.ToDateTime(obj);
            if (dtime == DateTime.MinValue) return "";
            return dtime.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 转化为日期字符串
        /// </summary>
        /// <param name="obj">object</param>
        /// <returns>格式: yyyy-MM-dd</returns>
        public static string CDateString(object obj)
        {
            if (obj == null) return string.Empty;
            DateTime dtime = Convert.ToDateTime(obj);
            if (dtime == DateTime.MinValue) return "";
            return dtime.ToString("yyyy-MM-dd");
        }

        /// <summary>
        /// 转换为日期字符符，要求提供格式
        /// </summary>
        /// <param name="obj">object</param>
        /// <param name="fmt">格式字符串</param>
        /// <returns>string</returns>
        public static string CDateTimeString(object obj, string fmt)
        {
            if (obj == null) return string.Empty;
            DateTime dtime = Convert.ToDateTime(obj);
            if (dtime == DateTime.MinValue) return "";
            return dtime.ToString(fmt);
        }

        /// <summary>
        /// 转换为布尔值
        /// </summary>
        /// <param name="obj">object</param>
        /// <returns>bool</returns>
        public static bool CBool(object obj)
        {
            if (obj == null) return false;
            return Convert.ToBoolean(obj);
        }

        /// <summary>
        /// 字符串转换为日期
        /// </summary>
        /// <param name="timestr">格式：yyyy-MM-dd HH:mm:ss, 分隔符可以是任意，只占一个字符</param>
        /// <returns>DateTime</returns>
        public static DateTime StringToDate(string timestr)
        {
            int year = Convert.ToInt32(timestr.Substring(0, 4));
            int month = Convert.ToInt32(timestr.Substring(5, 2));
            int day = Convert.ToInt32(timestr.Substring(8, 2));
            int hour = Convert.ToInt32(timestr.Substring(11, 2));
            int minute = Convert.ToInt32(timestr.Substring(14, 2));
            int second = Convert.ToInt32(timestr.Substring(17, 2));
            DateTime time = new DateTime(year, month, day, hour, minute, second);
            return time;
        }

        /// <summary>
        /// 字符串转换为日期
        /// </summary>
        /// <param name="timestr">格式：yyyyMMddHHmmss，没有分隔符</param>
        /// <returns>DateTime</returns>
        public static DateTime StringToDateEx(string timestr)
        {
            if (timestr.Length != 14) return DateTime.MinValue;
            int year = Convert.ToInt32(timestr.Substring(0, 4));
            int month = Convert.ToInt32(timestr.Substring(4, 2));
            int day = Convert.ToInt32(timestr.Substring(6, 2));
            int hour = Convert.ToInt32(timestr.Substring(8, 2));
            int minute = Convert.ToInt32(timestr.Substring(10, 2));
            int second = Convert.ToInt32(timestr.Substring(12, 2));
            DateTime time = new DateTime(year, month, day, hour, minute, second);
            return time;
        }

        /// <summary>
        /// DataView.RowFilter字符串的转义处理
        /// </summary>
        /// <param name="strVal">string</param>
        /// <returns>转义处理后的字符串</returns>
        public static string EscapeRowFilter(string strVal)
        {
            int n = strVal.Length;
            if (n <= 0) return "";

            List<char> list = new List<char>();
            for (int i = 0; i < n; i++)
            {
                char c = strVal[i];
                switch (c)
                {
                    case '*':
                    case '%':
                    case '[':
                    case ']':
                        list.Add('['); list.Add(c); list.Add(']');
                        break;
                    case '\'':
                        list.Add('\''); list.Add('\'');
                        break;
                    default:
                        list.Add(c);
                        break;
                }
            }
            return new string(list.ToArray());
        }

        /// <summary>
        /// 返回字符串的字节长度，对于AscII字符串长度为1，其它字符长度为2
        /// </summary>
        /// <param name="s">输入字符串</param>
        /// <returns>返回字符串的字节长度</returns>
        public static int GetAscIILength(string s)
        {
            int count = System.Text.Encoding.Default.GetByteCount(s);
            return count;
        }

        /// <summary>
        /// 返回指定字节长度的字符串，对于AscII字符串长度为1，其它字符长度为2
        /// </summary>
        /// <param name="s">输入字符串</param>
        /// <param name="len">字节长度</param>
        /// <returns>返回指定字节长度的字符串</returns>
        public static string GetAscIILength(string s, int len)
        {
            int n = s.Length;
            if (n <= len / 2) return s;
            int count = 0;
            int i = 0;
            while (count < len && i < n)
            {
                char c = s[i];
                if (c <= 127)
                {
                    count++;
                }
                else
                {
                    if (count + 2 <= len) count += 2; else break;
                }
                i++;
            }
            if (i >= n) return s;
            return s.Substring(0, i);
        }

        /// <summary>
        /// 返回字节长度的字符串，对于AscII字符串长度为1，其它字符长度为2
        /// </summary>
        /// <param name="s">输入字符串</param>
        /// <param name="len">字节长度</param>
        /// <param name="length">返回字字符串的字节长度</param>
        /// <returns>返回字节长度的字符串</returns>
        public static string GetAscIILength(string s, int len, ref int length)
        {
            int n = s.Length;
            if (n == 0)
            {
                length = 0;
                return "";
            }

            int count = 0;
            int i = 0;
            while (count < len && i < n)
            {
                char c = s[i];
                if (c <= 127)
                {
                    count++;
                }
                else
                {
                    if (count + 2 <= len) count += 2; else break;
                }
                i++;
            }
            /* count 一定是 <= len */
            length = count;
            if (i >= n)
            {
                return s;
            }
            return s.Substring(0, i);
        }

        /// <summary>
        /// 根据对齐方式，返回固定字节长度的字符串，对于AscII字符串长度为1，其它字符长度为2
        /// </summary>
        /// <param name="s">输入字符串</param>
        /// <param name="len">字节长度</param>
        /// <param name="align">对齐方式：1左对齐，2右对齐，3居中对齐</param>
        /// <returns>返回用空格填空的对应对齐方式固定长度的字符串</returns>
        public static string GetAscIIAlignText(string s, int len, int align)
        {
            int length = 0;
            string str = GetAscIILength(s, len, ref length);
            if (length == len) return str;
            /* length 一定 <= len */
            switch (align)
            {
                case 2: /*右对齐*/
                    str = str.PadLeft(len, ' ');
                    break;
                case 3: /*居中对齐*/
                    int v1 = (len - length) / 2;
                    if (v1 > 0) str = " ".PadLeft(v1) + str;

                    int v2 = len - v1 - length;
                    if (v2 > 0) str += " ".PadLeft(v1);
                    break;
                default: /*左对齐*/
                    str = str + " ".PadRight(len - length);
                    break;
            }
            return str;
        }

        /// <summary>
        /// 字段移去字节长度，遇开始位置和结束位置是中文的一个字节时，移掉一个中文，加入一个英文空格
        /// </summary>
        /// <param name="str">待替换字符串</param>
        /// <param name="startIndex">起始字节位置</param>
        /// <param name="byteLen">替换字节长度</param>
        /// <param name="replaceStr">替换字符串</param>
        /// <returns>替换后的字符串</returns>
        public static string ReplaceAscIILength(string str, int startIndex, int byteLen, string replaceStr)
        {
            int charLen = str.Length;
            int strByteLen = System.Text.Encoding.Default.GetByteCount(str);
            if (startIndex >= strByteLen) return str;

            if (startIndex + byteLen > strByteLen) byteLen = strByteLen - startIndex;
            if (byteLen < 0) byteLen = 0;
            //开始位置插入
            if (startIndex == 0 && byteLen == 0)
            {
                if (!string.IsNullOrEmpty(replaceStr)) str = str.Insert(0, replaceStr);
                return str;
            }

            int pos = 0;
            int i = 0, i1, i2;
            if (startIndex == 0)
            {
                i1 = 0;
            }
            else
            {
                while (i < charLen)
                {
                    char c = str[i];
                    if (c <= 127) pos++; else pos += 2;
                    if (pos >= startIndex)
                    {
                        if (pos > startIndex)
                        {
                            pos--;
                            str = str.Remove(i, 1).Insert(i, "  ");
                        }
                        i++;
                        break;
                    }
                    i++;
                }
                i1 = i;
            }

            int endIndex = startIndex + byteLen;
            while (i < charLen && pos < endIndex)
            {
                char c = str[i];
                if (c <= 127) pos++; else pos += 2;
                if (pos >= endIndex)
                {
                    if (pos > endIndex)
                    {
                        str = str.Remove(i, 1).Insert(i, "  ");
                    }
                    i++;
                    break;
                }
                i++;
            }
            i2 = i;
            str = str.Remove(i1, i2 - i1);
            if (!string.IsNullOrEmpty(replaceStr)) str = str.Insert(i1, replaceStr);
            return str;
        }

        /// <summary>
        /// 获取重复字符串
        /// </summary>
        /// <param name="str">待重复的字符串</param>
        /// <param name="count">重复次数</param>
        /// <returns>字符串值</returns>
        public static string RepeatStr(string str, int count)
        {
            if (count <= 1) return str;
            string[] arr = new string[count];
            for (int i = 0; i < count; i++)
            {
                arr[i] = str;
            }
            return string.Join("", arr);
        }

        /// <summary>
        /// 获取重复字符串
        /// </summary>
        /// <param name="c">待重复的字符</param>
        /// <param name="count">重复次数</param>
        /// <returns>字符串值</returns>
        public static string RepeatStr(char c, int count)
        {
            if (count <= 1) return "";
            char[] arr = new char[count];
            for (int i = 0; i < count; i++)
            {
                arr[i] = c;
            }
            return new string(arr);
        }

        /// <summary>
        /// 获取重复字符串
        /// </summary>
        /// <param name="str">待重复的字符串</param>
        /// <param name="deli">中间连接字符串</param>
        /// <param name="count">重复次数</param>
        /// <returns>字符串值</returns>
        public static string RepeatStr(string str, string deli, int count)
        {
            if (count <= 1) return str;
            string[] arr = new string[count];
            for (int i = 0; i < count; i++)
            {
                arr[i] = str;
            }
            return string.Join(deli, arr);
        }

        /// <summary>
        /// 判断是否是128码字符，如果不是，可以读取Message信息
        /// </summary>
        /// <param name="s">string</param>
        /// <returns>true是，false否</returns>
        public static bool IsCode128(string s)
        {
            Message = "";
            char[] chars = s.ToCharArray();
            bool isok = true;
            foreach (char c in chars)
            {
                int v = Convert.ToInt32(c);
                if (v < 0 || v > 127)
                {
                    Message = string.Format("字符串中包含{0} charCode={1}， Code128要求0-127。", c, v);
                    isok = false;
                    break;
                }
            }
            return isok;
        }

        /// <summary>
        /// 判断是否是数据，判断方法:字符串转换为数字后，可逆转换为原来的字符串
        /// </summary>
        /// <param name="s">string</param>
        /// <returns>bool</returns>
        public static bool IsNumeric(string s)
        {
            Message = "";
            if (s == null) return false;
            string s2 = s.Trim();
            double v = CDbl(s2);
            return v.ToString() == s2;
        }


        /// <summary>
        /// 释放当前对象，变量置为null
        /// </summary>
        /// <typeparam name="T">泛型</typeparam>
        /// <param name="obj">泛型对象</param>
        public static void FreeObject<T>(ref T obj) where T : class, IDisposable
        {
            if (obj != null) obj.Dispose();
            obj = null;
        }


        /// <summary>
        /// 字符串转换为字典
        /// </summary>
        /// <param name="str">string</param>
        /// <param name="rowdeli">键值对分割字符串</param>
        /// <param name="coldeli">键值分割字符串</param>
        /// <returns>Dictionary&lt;string, string&gt;</returns>
        public static Dictionary<string, string> StringToDict(string str, string rowdeli, string coldeli)
        {
            string s2 = str.Trim();
            string[] a1 = Regex.Split(str, rowdeli);
            int count = a1.Length;

            Dictionary<string, string> dic = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            string key;
            string[] a2;
            for (int i = 0; i < count; i++)
            {
                a2 = Regex.Split(a1[i], coldeli);
                if (a2.Length > 1)
                {
                    key = a2[0].Trim();
                    if (key.Length > 0)
                    {
                        dic[key] = a2[1].Trim();
                    }
                }
            }
            return dic;
        }

        /// <summary>
        /// 字符串转换为字典
        /// </summary>
        /// <param name="str">string</param>
        /// <param name="rowdeli">键值对分割符</param>
        /// <param name="coldeli">键值分割符</param>
        /// <returns>Dictionary&lt;string, string&gt;</returns>
        public static Dictionary<string, string> StringToDict(string str, char rowdeli, char coldeli)
        {
            string s2 = str.Trim();
            string[] a1 = s2.Split(rowdeli);
            int count = a1.Length;

            Dictionary<string, string> dic = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            string key;
            string[] a2;
            for (int i = 0; i < count; i++)
            {
                a2 = a1[i].Split(coldeli);
                if (a2.Length > 1)
                {
                    key = a2[0].Trim();
                    if (key.Length > 0)
                    {
                        dic[key] = a2[1].Trim();
                    }
                }
            }
            return dic;
        }

        /// <summary>
        /// 字符串转换为字典
        /// </summary>
        /// <param name="str">string</param>
        /// <param name="rowdeli">键值对分割符</param>
        /// <param name="coldeli">键值分割符</param>
        /// <returns>Dictionary&lt;string, int&gt;</returns>
        public static Dictionary<string, int> StringToDictInt(string str, char rowdeli, char coldeli)
        {
            string s2 = str.Trim();
            string[] a1 = s2.Split(rowdeli);
            int count = a1.Length;

            Dictionary<string, int> dic = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            string key;
            string[] a2;
            for (int i = 0; i < count; i++)
            {
                a2 = a1[i].Split(coldeli);
                if (a2.Length > 1)
                {
                    key = a2[0].Trim();
                    if (key.Length > 0)
                    {
                        dic[key] = a2[1].Trim().CInt();
                    }
                }
            }
            return dic;
        }

        /// <summary>
        /// 字典转换为字符串
        /// </summary>
        /// <typeparam name="TKey">键类</typeparam>
        /// <typeparam name="TValue">键类</typeparam>
        /// <param name="dic">Dictionary&lt;TKey, TValue&gt;</param>
        /// <param name="rowdeli">键值对分割字符串</param>
        /// <param name="coldeli">键值分割字符串</param>
        /// <returns>string</returns>
        public static string DictionaryToString<TKey, TValue>(Dictionary<TKey, TValue> dic, string rowdeli, string coldeli)
        {
            int n = dic.Count;
            string[] arr = new string[n];
            n = 0;
            foreach (KeyValuePair<TKey, TValue> kv in dic)
            {
                arr[n++] = String.Format("{0}{1}{2}", kv.Key, coldeli, kv.Value.ToString());
            }
            return string.Join(rowdeli, arr);
        }

        /// <summary>
        /// 字符串转换为List(string)
        /// </summary>
        /// <param name="str">string</param>
        /// <param name="deli">分割符</param>
        /// <returns>List&lt;string&gt;</returns>
        public static List<string> StringToList(string str, char deli)
        {
            List<string> list = new List<string>();
            string[] strs = str.CStr().Split(deli);
            foreach (string s2 in strs)
            {
                string item = s2.Trim();
                if (!string.IsNullOrEmpty(item)) list.Add(item);
            }
            return list;
        }

        /// <summary>
        /// 字符串转换为List(string)
        /// </summary>
        /// <param name="str">string</param>
        /// <param name="deli">分割符</param>
        /// <returns>List&lt;string&gt;</returns>
        public static List<string> StringToList(string str, string deli)
        {
            List<string> list = new List<string>();
            string[] strs = str.CStr().Split(new string[] { deli }, StringSplitOptions.RemoveEmptyEntries);
            foreach (string s2 in strs)
            {
                string item = s2.Trim();
                if (!string.IsNullOrEmpty(item)) list.Add(item);
            }
            return list;
        }

        /// <summary>
        /// 字符串转换为List(int)
        /// </summary>
        /// <param name="str">string</param>
        /// <param name="deli">分割符</param>
        /// <returns>List&lt;int&gt;</returns>
        public static List<int> StringToListInt(string str, char deli)
        {
            List<int> list = new List<int>();
            string[] strs = str.CStr().Split(deli);
            foreach (string s2 in strs)
            {
                string item = s2.Trim();
                if (!string.IsNullOrEmpty(item))
                {
                    list.Add(item.CInt());
                }
            }
            return list;
        }

        /// <summary>
        /// 字符串转换为List(string)
        /// </summary>
        /// <param name="str">string</param>
        /// <param name="deli">分割符</param>
        /// <returns>List&lt;int&gt;</returns>
        public static List<int> StringToListInt(string str, string deli)
        {
            List<int> list = new List<int>();
            string[] strs = str.CStr().Split(new string[] { deli }, StringSplitOptions.RemoveEmptyEntries);
            foreach (string s2 in strs)
            {
                string item = s2.Trim();
                if (!string.IsNullOrEmpty(item))
                {
                    list.Add(item.CInt());
                }
            }
            return list;
        }

        /// <summary>
        /// 字典IDictionary转换为字符串
        /// </summary>
        /// <param name="dic">IDictionary</param>
        /// <param name="rowdeli">键值对分割字符串</param>
        /// <param name="coldeli">键值分割字符串</param>
        /// <returns>string</returns>
        public static string DictionaryToString(System.Collections.IDictionary dic, string rowdeli, string coldeli)
        {
            int n = dic.Count;
            string[] arr = new string[n];
            n = 0;
            foreach (DictionaryEntry kv in dic)
            {
                if (kv.Value.GetType().Name.IndexOf("Dictionary") >= 0)
                {
                    Type[] types = kv.Value.GetType().GetGenericArguments();
                    if (types.Length == 2)
                    {
                        string value = DictionaryToString((System.Collections.IDictionary)kv.Value, rowdeli, coldeli);
                        arr[n++] = String.Format("{0}{1}{{{2}}}", kv.Key, coldeli, value);
                    }
                }
                else
                {
                    arr[n++] = String.Format("{0}{1}{2}", kv.Key, coldeli, kv.Value.ToString());
                }
            }
            return string.Join(rowdeli, arr);
        }

        /// <summary>
        /// 获取对象的数据类型
        /// </summary>
        /// <param name="o">任意对象</param>
        /// <returns>EnumDataType</returns>
        public static EnumDataType GetDataType(this object o)
        {
            EnumDataType dataType;
            switch (Type.GetTypeCode(o.GetType()))
            {
                case TypeCode.Byte:
                case TypeCode.SByte:
                case TypeCode.UInt16:
                case TypeCode.UInt32:
                case TypeCode.UInt64:
                case TypeCode.Int16:
                case TypeCode.Int32:
                case TypeCode.Int64:
                case TypeCode.Decimal:
                case TypeCode.Double:
                case TypeCode.Single:
                    dataType = EnumDataType.Number;
                    break;
                case TypeCode.DateTime:
                    dataType = EnumDataType.Date;
                    break;
                default:
                    dataType = EnumDataType.String;
                    break;
            }
            return dataType;
        }

        /// <summary>
        /// 值的数据类型转换
        /// </summary>
        /// <param name="val">待转换数据</param>
        /// <param name="type">Type</param>
        /// <returns>type对应的数据值</returns>
        public static object ChangeType(object val, Type type)
        {
            if (!type.IsGenericType)
            {
                return Convert.ChangeType(val, type);
            }
            else
            {
                Type genericTypeDefinition = type.GetGenericTypeDefinition();
                if (genericTypeDefinition == typeof(Nullable<>))
                {
                    return Convert.ChangeType(val, Nullable.GetUnderlyingType(type));
                }
            }
            throw new InvalidCastException(string.Format("Invalid cast from type \"{0}\" to type \"{1}\".", val.GetType().FullName, type.FullName));
        }

        /// <summary>
        /// double类型中国式的四舍五入
        /// </summary>
        /// <param name="v">数值</param>
        /// <param name="digits">小数位数</param>
        /// <returns>中国式的四舍五入的值</returns>
        public static double Round(double v, int digits = 0)
        {
            double v2;
            if (v == 0) return 0d;
            if (digits == 0)
            {
                if (v > 0) v2 = Math.Round(v + 0.1); else v2 = Math.Round(v - 0.1);
            }
            else
            {
                double index = -(digits + 1);
                double fraction = Math.Pow(10d, index);
                if (v > 0) v2 = Math.Round(v + fraction, digits); else v2 = Math.Round(v - fraction, digits);
            }
            return v2;
        }

        /// <summary>
        /// decimal类型中国式的四舍五入
        /// </summary>
        /// <param name="v">数值</param>
        /// <param name="decimals">小数位数</param>
        /// <returns>中国式的四舍五入的值</returns>
        public static decimal Round(decimal v, int decimals = 0)
        {
            decimal v2;
            if (v == 0) return 0M;
            if (decimals == 0)
            {
                if (v > 0) v2 = Math.Round(v + 0.1M); else v2 = Math.Round(v - 0.1M);
            }
            else
            {
                double index = -(decimals + 1);
                decimal fraction = Convert.ToDecimal(Math.Pow(10d, index));
                if (v > 0) v2 = Math.Round(v + fraction, decimals); else v2 = Math.Round(v - fraction, decimals);
            }
            return v2;
        }
    }

    /// <summary>
    /// 枚举数据类型， 数字0，日期1和字符串2
    /// </summary>
    public enum EnumDataType
    {
        /// <summary>
        /// 数值
        /// </summary>
        Number = 0,
        /// <summary>
        /// 日期
        /// </summary>
        Date = 1,
        /// <summary>
        /// 字符串
        /// </summary>
        String = 2
    }
}
