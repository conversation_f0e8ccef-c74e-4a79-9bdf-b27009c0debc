﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;

namespace PlatCommon.Base02
{
    /// <summary>
    /// 文本文件类
    /// </summary>
    static public class Cs02TxtHelper
    {
        /// <summary>
        /// 文本文件转为DataTable
        /// </summary>
        /// <param name="strFileName">TXT文件名</param>
        /// <param name="cSplit">分隔字符</param>
        /// <param name="bHasTitle">是否有标题行</param>
        /// <returns></returns>
        static public DataTable Txt2DataTable(string strFileName, char cSplit, bool bHasTitle)
        {
            DataTable dtResult = new DataTable();

            if (!File.Exists(strFileName))
            {
                Cs02MessageBox.ShowError($"文本文件[{strFileName}]，不存在！");
                return dtResult;
            }

            try
            {
                StreamReader streamReader = new StreamReader(strFileName, Encoding.GetEncoding("gb2312"));

                string strFirstLine = streamReader.ReadLine();
                string[] strArrData = strFirstLine.Split(cSplit);
                int iColumnCount = strArrData.Length;

                //根据首行，生成列
                for (int i = 0; i < iColumnCount; i++)
                {
                    dtResult.Columns.Add(i.ToString(), Type.GetType("System.String"));
                }

                string strDataLine = string.Empty;

                //如果首行是标题
                if (bHasTitle)
                    strDataLine = streamReader.ReadLine();
                else
                    strDataLine = strFirstLine;

                //分析每一行数据，写入表里
                while (!string.IsNullOrEmpty(strDataLine))
                {
                    strArrData = strDataLine.Split(cSplit);

                    DataRow drNew = dtResult.NewRow();
                    for (int i = 0; i < iColumnCount; i++)
                    {
                        drNew[i] = strArrData[i];
                    }
                    dtResult.Rows.Add(drNew);

                    strDataLine = streamReader.ReadLine();
                }

                streamReader.Close();
            }
            catch (Exception ex)
            {
                Cs02MessageBox.ShowError($"读文本文件[{strFileName}]，出错了！\r\n错误信息：{ex.Message}");
            }

            return dtResult;
        }

        /// <summary>
        /// 写日志
        /// </summary>
        /// <param name="strLogFile">日志文件名</param>
        /// <param name="strFunction">执行功能</param>
        /// <param name="strInput">输入参数</param>
        /// <param name="strOutput">输出参数</param>
        static public void WriteTxtLog(string strLogFile, string strFunction, string strInput, string strOutput)
        {
            //开始写入
            StringBuilder sbWrite = new StringBuilder();

            sbWrite.Append($"执行时间：{ DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}\r\n");
            sbWrite.Append($"执行功能：{ strFunction}\r\n");
            sbWrite.Append($"执行参数：{ strInput}\r\n");
            sbWrite.Append($"返回结果：{ strOutput}\r\n\r\n");

            File.AppendAllText(strLogFile, sbWrite.ToString());
        }
    
    }
}
