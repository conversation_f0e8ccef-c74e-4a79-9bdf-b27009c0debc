﻿/*********************************************
* 命名空间 ：Tjhis.CsComm
* 类 名 称 ：Cs01ExcelHelper
* 功能说明 ：
* 作    者 ：刘成刚
* 创建时间 ：2021-01-26 15:34:27
* 更 新 人 ：
* 更新时间 ：
* 更新说明 ：
* 版 本 号 ：v1.0.0.0
* CLR 版本 ：4.0.30319.42000
* 版权说明：北京天健源达 HIS基础产品研发部
/*********************************************/

using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;

namespace PlatCommon.Base01
{
    /// <summary>
    /// DataSet与EXCEL转换
    /// </summary>
    public static class Cs01ExcelHelper
    {
        /// <summary>
        /// 将DataSet数据保存为EXCEL文件
        /// </summary>
        /// <param name="strFileName">将要保存的文件路径</param>
        /// <param name="dsSource">将要保存的DataSet数据表可多页面</param>
        public static void DataSetToExcel(string strFileName, DataSet dsSource)
        {
            if (dsSource == null)
                throw new Exception("DataSet为空!");

            if (dsSource.Tables.Count < 1)
                throw new Exception("DataSet中，没有Tables!");

            string strCon = string.Empty;
            FileInfo fileInfo = new FileInfo(strFileName);
            string strExtension = fileInfo.Extension;
            switch (strExtension)
            {
                case ".xls":
                    strCon = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + strFileName + ";Extended Properties=Excel 8.0;";
                    break;
                case ".xlsx":
                    strCon = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + strFileName + ";Extended Properties='Excel 12.0;HDR=Yes;IMEX=0;'";
                    break;
                default:
                    strCon = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + strFileName + ";Extended Properties='Excel 8.0;HDR=Yes;IMEX=0;'";
                    break;
            }
            try
            {
                using (System.Data.OleDb.OleDbConnection con = new System.Data.OleDb.OleDbConnection(strCon))
                {
                    con.Open();
                    StringBuilder strSQL = new StringBuilder();
                    System.Data.OleDb.OleDbCommand cmd;
                    try
                    {
                        for (int i = 0; i < dsSource.Tables.Count; i++)
                        {
                            //覆盖文件时可能会出现Table 'Sheet1' already exists.所以这里先删除了一下
                            cmd = new System.Data.OleDb.OleDbCommand(string.Format("drop table {0}", dsSource.Tables[i].TableName), con); 
                            cmd.ExecuteNonQuery();
                        }
                    }

                    catch { }

                    //创建表格字段
                    for (int i = 0; i < dsSource.Tables.Count; i++)
                    {
                        strSQL.Clear();
                        strSQL.Append("CREATE TABLE ").Append("[" + dsSource.Tables[i].TableName + "]");
                        strSQL.Append("(");

                        for (int j = 0; j < dsSource.Tables[i].Columns.Count; j++)
                        {
                            strSQL.Append("[" + dsSource.Tables[i].Columns[j].ColumnName + "] text,");
                        }
                        strSQL = strSQL.Remove(strSQL.Length - 1, 1);
                        strSQL.Append(")");

                        cmd = new System.Data.OleDb.OleDbCommand(strSQL.ToString(), con);
                        cmd.ExecuteNonQuery();

                        //添加数据
                        for (int k = 0; k < dsSource.Tables[i].Rows.Count; k++)
                        {
                            strSQL.Clear();
                            StringBuilder strvalue = new StringBuilder();
                            for (int j = 0; j < dsSource.Tables[i].Columns.Count; j++)
                            {
                                strvalue.Append("'" + dsSource.Tables[i].Rows[i][j].ToString() + "'");
                                if (j != dsSource.Tables[i].Columns.Count - 1)
                                {
                                    strvalue.Append(",");
                                }
                                else
                                {
                                }
                            }
                            cmd.CommandText = strSQL.Append(" insert into [" + dsSource.Tables[i].TableName + "] values (").Append(strvalue).Append(")").ToString();
                            cmd.ExecuteNonQuery();
                        }
                    }
                    con.Close();
                }
            }
            catch { }
        }

        /// <summary>
        /// 将EXCLE文件读取到DataSet表中。支持多工作表读取
        /// </summary>
        /// <param name="strFileName">要读取的文件路径</param>
        /// <param name="strSheetName">要读取的文件路径</param>
        /// <returns>返回DataSet表</returns>
        public static DataSet ExcelToDataSet(string strFileName, string strSheetName = "")
        {
            System.Data.DataSet dsResult = new System.Data.DataSet();
            string ConnectionString = string.Empty;
            FileInfo fileInfo = new FileInfo(strFileName);
            if (!fileInfo.Exists)
            {
                throw new Exception("文件不存在!");
            }
            string strExtension = fileInfo.Extension;
            switch (strExtension)                          // 连接字符串          
            {
                case ".xls":
                    ConnectionString = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + strFileName + ";Extended Properties='Excel 8.0;HDR=no;IMEX=1;'";
                    break;
                case ".xlsx":
                    ConnectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + strFileName + ";Extended Properties='Excel 12.0;HDR=no;IMEX=1;'";
                    break;
                default:
                    ConnectionString = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + strFileName + ";Extended Properties='Excel 8.0;HDR=no;IMEX=1;'";
                    break;
            }


            System.Data.OleDb.OleDbConnection con = new System.Data.OleDb.OleDbConnection(ConnectionString);
            try
            {
                con.Open();
                //若指定了工作表名
                if (!string.IsNullOrEmpty(strSheetName))
                {
                    //读Excel的过程中，发现dt末尾有些行是空的，所以在sql语句中加了Where 条件筛选符合要求的数据。OLEDB会自动生成列名F1,F2……Fn                     
                    System.Data.OleDb.OleDbCommand cmd = new System.Data.OleDb.OleDbCommand("select * from [" + strSheetName + "$] where F1 is not null ", con);
                    System.Data.OleDb.OleDbDataAdapter apt = new System.Data.OleDb.OleDbDataAdapter(cmd);
                    try
                    {
                        apt.Fill(dsResult);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"该Excel文件中未找到指定工作表名：{strSheetName}\r\n" + ex.Message);//抛出异常接收
                    }
                    dsResult.Tables.Clear();
                    dsResult.Tables.Add(strSheetName);
                }
                else
                {                   //默认读取第一个有数据的工作表                   
                    var tables = con.GetOleDbSchemaTable(System.Data.OleDb.OleDbSchemaGuid.Tables, new object[]
                    { });
                    if (tables.Rows.Count == 0)
                    {
                        throw new Exception("Excel必须包含一个表");
                    }
                    dsResult.Tables.Clear();
                    foreach (System.Data.DataRow row in tables.Rows)
                    {
                        string strSheetTableName = row["TABLE_NAME"].ToString();                       //过滤无效SheetName     

                        if (strSheetTableName.Contains("$") && strSheetTableName.Replace("'", "").EndsWith("$"))//分析文件名Contains 是否有$有为真，Replace字符替换，EndsWith看字串尾是否有指定字符
                        {
                            System.Data.DataTable tableColumns = con.GetOleDbSchemaTable(System.Data.OleDb.OleDbSchemaGuid.Columns,
                                new object[] { null, null, strSheetTableName, null });
                            if (tableColumns.Rows.Count < 2)                     //工作表列数                             
                                continue;
                            System.Data.OleDb.OleDbCommand cmd = new System.Data.OleDb.OleDbCommand("select * from [" + strSheetTableName + "] where F1 is not null", con);
                            System.Data.OleDb.OleDbDataAdapter apt = new System.Data.OleDb.OleDbDataAdapter(cmd);
                            System.Data.DataTable dt = new System.Data.DataTable();
                            apt.Fill(dt);
                            dt.TableName = strSheetTableName.Replace("'", "").Replace("$", "");
                            dsResult.Tables.Add(dt);
                        }
                    }
                }

                for (int i = 0; i < dsResult.Tables.Count; i++)
                {
                    if (dsResult.Tables[i].Rows.Count < 0)
                        throw new Exception("表必须包含数据");                       //重构字段名      
                    System.Data.DataRow headRow = dsResult.Tables[i].Rows[0];
                    foreach (System.Data.DataColumn c in dsResult.Tables[i].Columns)
                    {
                        string headValue = (headRow[c.ColumnName] == DBNull.Value || headRow[c.ColumnName] == null) ? "" : headRow[c.ColumnName].ToString().Trim();
                        if (headValue.Length == 0)
                        {
                            throw new Exception("必须输入列标题");
                        }
                        if (dsResult.Tables[i].Columns.Contains(headValue))
                        {
                            throw new Exception("不能用重复的列标题：" + headValue);
                        }
                        c.ColumnName = headValue;
                    }
                    dsResult.Tables[i].Rows.RemoveAt(0);
                }

                return dsResult;
            }
            catch (Exception ee)
            {
                throw ee;
            }
            finally
            {
                con.Close();
            }
        }

    }
}
