﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using NM_Service.NMService;
using DevExpress.XtraPrinting;

namespace PlatCommonForm
{
    public partial class frmPrepQueryPat : PlatCommon.SysBase.ParentForm
    {
        DataTable patientdt;
        int count = 0;
        public frmPrepQueryPat()
        {
            InitializeComponent();
        }
        
        private void frmPrepQueryPat_Load(object sender, EventArgs e)
        {
            DataTable dtRcpt = GetPrepaymentRcpt();
            gcClinic.DataSource = dtRcpt;
        }

        private void txtPatientid_KeyDown(object sender, KeyEventArgs e)
        {
            Keys key = e.KeyCode;
            if (key == Keys.Enter)
            {
                cb_refresh_ItemClick(null,null);
            }
        }
        #region

        /// <summary>
        /// 返回在院病人信息
        /// </summary>
        /// <param name="patientid"></param>
        /// <param name="inpno"></param>
        /// <returns></returns>
        private DataTable GetPatientByIdOrInpNo(string patientid, string inpno)
        {
            string sql = "";
            if (patientid != "")
            {
                sql = "  SELECT PAT_MASTER_INDEX.PATIENT_ID,PAT_MASTER_INDEX.INP_NO,PAT_MASTER_INDEX.NAME,PATS_IN_HOSPITAL.PREPAYMENTS,PAT_MASTER_INDEX.SEX,PAT_VISIT.CHARGE_TYPE,PAT_VISIT.ADMISSION_DATE_TIME,PAT_VISIT.DEPT_ADMISSION_TO,PAT_VISIT.VISIT_ID,pat_visit.dept_discharge_from  FROM PAT_MASTER_INDEX,PAT_VISIT,PATS_IN_HOSPITAL  WHERE ( pat_visit.patient_id = pats_in_hospital.patient_id (+)) and          ( pat_visit.visit_id = pats_in_hospital.visit_id (+)) and  ( PAT_MASTER_INDEX.PATIENT_ID = PAT_VISIT.PATIENT_ID ) and pat_master_index.patient_id='" + patientid + "' order by pat_visit.visit_id desc";
            }
            else if (patientid == "" && inpno != "")
            {
                sql = "  SELECT PAT_MASTER_INDEX.PATIENT_ID,PAT_MASTER_INDEX.INP_NO,PAT_MASTER_INDEX.NAME,PATS_IN_HOSPITAL.PREPAYMENTS,PAT_MASTER_INDEX.SEX,PAT_VISIT.CHARGE_TYPE,PAT_VISIT.ADMISSION_DATE_TIME,PAT_VISIT.DEPT_ADMISSION_TO,PAT_VISIT.VISIT_ID,'' dept_discharge_from  FROM PAT_MASTER_INDEX,PAT_VISIT,PATS_IN_HOSPITAL  WHERE ( pat_visit.patient_id = pats_in_hospital.patient_id ) and          ( pat_visit.visit_id = pats_in_hospital.visit_id ) and  ( PAT_MASTER_INDEX.PATIENT_ID = PAT_VISIT.PATIENT_ID ) and pat_master_index.INP_NO='" + inpno + "' order by pat_visit.visit_id desc";
            }
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            return dt;
        }
        /// <summary>
        /// 根据病人ID获取预交金记录
        /// </summary>
        /// <param name="patientid"></param>
        /// <param name="visitid"></param>
        /// <returns></returns>
        private DataTable GetPrepaymentRcptByPatientID(string patientid, int visitid)
        {
            string sql = "SELECT p.RCPT_NO,p.PATIENT_ID,p.TRANSACT_DATE,p.AMOUNT,p.PAY_WAY,p.TRANSACT_TYPE,p.BANK,p.CHECK_NO,p.OPERATOR_NO,p.ACCT_NO,p.ADDR,p.REFUNDED_RCPT_NO,p.VISIT_ID,DECODE(p.USED_FLAG,'0','未使用','1','使用','') USED_FLAG,p.BANK_AUOUNT_NO,p.BANK_TRANS_DATE,p.INVOICE_NO,s.name FROM PREPAYMENT_RCPT p left join staff_dict s on p.operator_no=s.user_name  WHERE p.settled_no is null and p.PATIENT_ID ='" + patientid + "' and p.VISIT_ID =" + visitid;
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            return dt;
        }
        /// <summary>
        /// 在院患者预交金记录
        /// </summary>
        /// <param name="patientid"></param>
        /// <param name="visitid"></param>
        /// <returns></returns>
        private DataTable GetPrepaymentRcpt()
        {
            string sql = @"SELECT p.RCPT_NO,
                                   p.PATIENT_ID,
                                   p.TRANSACT_DATE,
                                   p.AMOUNT,
                                   p.PAY_WAY,
                                   p.TRANSACT_TYPE,
                                   p.BANK,
                                   p.CHECK_NO,
                                   p.OPERATOR_NO,
                                   p.ACCT_NO,
                                   p.ADDR,
                                   p.REFUNDED_RCPT_NO,
                                   p.VISIT_ID,
                                   DECODE(p.USED_FLAG, '0', '未使用', '1', '使用', '') USED_FLAG,
                                   p.BANK_AUOUNT_NO,
                                   p.BANK_TRANS_DATE,
                                   p.INVOICE_NO,
                                   s.name
                              FROM PREPAYMENT_RCPT p
                              left join staff_dict s
                                on p.operator_no = s.user_name
                                join pats_in_hospital a
                                on a.patient_id = p.patient_id
                                and a.visit_id = p.visit_id
                             WHERE p.settled_no is null";
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            return dt;
        }
        #endregion
        /// <summary>
        /// 清屏
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void cb_copy_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            txtPatientid.Text = "";
            txtInpNo.Text = "";
            txtVisitID.Text = "";
            txtChargeType.Text = "";
            txtName.Text = "";
            txtSex.Text = "";
            txtInDept.Text = "";
            txtInTime.Text = "";
            txtsum.Text = "";
            txtyue.Text = "";
            gcClinic.DataSource = null;

        }
        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void cb_save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (gvRcpt.RowCount > 0)
            {
                PrintingSystem print = new DevExpress.XtraPrinting.PrintingSystem();
                PrintableComponentLink link = new PrintableComponentLink(print);
                print.Links.Add(link);
                link.Component = gcClinic;//这里可以是可打印的部件  
                string _PrintHeader = "病人预交金报表";
                string _PrintFooter = "打印人：" + PlatCommon.SysBase.SystemParm.LoginUser.NAME + "   打印时间：" + PlatCommon.Common.PublicFunction.GetSysDate();
                PageHeaderFooter phf = link.PageHeaderFooter as PageHeaderFooter;
                //link.RtfReportHeader = "                                                                  " + _PrintHeader + "\r\n";
                link.PaperKind = System.Drawing.Printing.PaperKind.A4Plus; //设置纸张  
                link.Landscape = false; //mh  
                phf.Header.Content.Clear();
                phf.Header.Content.AddRange(new string[] { "", _PrintHeader, "" });
                phf.Header.Font = new System.Drawing.Font("宋体", 16, System.Drawing.FontStyle.Bold);
                phf.Header.LineAlignment = BrickAlignment.Center;
                phf.Footer.Content.Clear();
                phf.Footer.Content.AddRange(new string[] { "", "", _PrintFooter });
                phf.Footer.Font = new System.Drawing.Font("宋体", 10, System.Drawing.FontStyle.Bold);
                phf.Footer.LineAlignment = BrickAlignment.Center;
                link.CreateDocument(); //建立文档  
                link.ShowPreviewDialog();

                //gvRcpt.Print();
            }
        } 

        private void cb_refresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            string patientid = txtPatientid.Text.Trim();
            string inpno = txtInpNo.Text.Trim();
            if (patientid == "" )
            {
                DataTable dtRcpt = GetPrepaymentRcpt();
                gcClinic.DataSource = dtRcpt;
                return;
            }
            DataTable patdt = GetPatientByIdOrInpNo(patientid, inpno);

            patientdt = patdt.Copy();
            if (patdt.Rows.Count > 0)
            {
                if (!string.IsNullOrEmpty(patdt.Rows[count]["dept_discharge_from"].ToString()))
                {
                    txtVisitID.Text = "";
                    txtChargeType.Text = "";
                    txtName.Text = "";
                    txtSex.Text = "";
                    txtInDept.Text = "";
                    txtInTime.Text = "";
                    txtsum.Text = "";
                    txtyue.Text = "";
                    gcClinic.DataSource = null;
                    XtraMessageBox.Show("病人已出院!", "提示");
                    return;
                }
                txtPatientid.Text = patdt.Rows[count]["PATIENT_ID"] == DBNull.Value ? "" : patdt.Rows[count]["PATIENT_ID"].ToString();
                txtInpNo.Text = patdt.Rows[count]["INP_NO"] == DBNull.Value ? "" : patdt.Rows[count]["INP_NO"].ToString();
                txtVisitID.Text = patdt.Rows[count]["VISIT_ID"] == DBNull.Value ? "" : patdt.Rows[count]["VISIT_ID"].ToString();
                txtChargeType.Text = patdt.Rows[count]["CHARGE_TYPE"] == DBNull.Value ? "" : patdt.Rows[count]["CHARGE_TYPE"].ToString();
                txtName.Text = patdt.Rows[count]["NAME"] == DBNull.Value ? "" : patdt.Rows[count]["NAME"].ToString();
                txtSex.Text = patdt.Rows[count]["SEX"] == DBNull.Value ? "" : patdt.Rows[count]["SEX"].ToString();
                string ry_dept_code = patdt.Rows[count]["DEPT_ADMISSION_TO"] == DBNull.Value ? "" : patdt.Rows[count]["DEPT_ADMISSION_TO"].ToString();
                //string ry_dept_name = "";
                DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select dept_name from dept_dict where dept_code='"+ry_dept_code+"'").Tables[0];
                if (dt.Rows.Count > 0)
                {
                    txtInDept.Text = dt.Rows[0][0].ToString();
                }
                txtInTime.Text = patdt.Rows[count]["ADMISSION_DATE_TIME"] == DBNull.Value ? "" : patdt.Rows[count]["ADMISSION_DATE_TIME"].ToString();
                DataTable rcptdt = GetPrepaymentRcptByPatientID(txtPatientid.Text, Convert.ToInt32(txtVisitID.Text));
                gcClinic.DataSource = rcptdt;
                decimal sum = 0;
                decimal yue = 0;
                decimal ldc_prepayments = 0;
                decimal ldc_approve = 0;
                decimal ldc_charge = 0;
                decimal ldc_prepaybalan = 0;
                decimal ldc_use = 0;
                PlatCommon.Common.PublicFunction.GetPrepayment("*", patdt.Rows[count]["PATIENT_ID"].ToString(), patdt.Rows[count]["VISIT_ID"].ToString(), ref ldc_prepayments, ref ldc_approve, ref ldc_charge, ref ldc_prepaybalan, ref ldc_use);
                sum = ldc_prepayments;
                txtsum.Text = sum.ToString("#0.00");
                yue = ldc_prepaybalan;
                txtyue.Text = yue.ToString("#0.00");
            }
            else
            {
                txtVisitID.Text = "";
                txtChargeType.Text = "";
                txtName.Text = "";
                txtSex.Text = "";
                txtInDept.Text = "";
                txtInTime.Text = "";
                txtsum.Text = "";
                txtyue.Text = "";
                gcClinic.DataSource = null;
                XtraMessageBox.Show("没有找到病人的信息!", "提示");
                return;
            }
        }

        private void cb_close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barLargeButtonItem3_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (patientdt == null) return;
            if (patientdt.Rows.Count > 0)
            {
                if (count + 1 < patientdt.Rows.Count)
                    count++;
                else count = 0;
                txtPatientid.Text = patientdt.Rows[count]["PATIENT_ID"] == DBNull.Value ? "" : patientdt.Rows[count]["PATIENT_ID"].ToString();
                txtInpNo.Text = patientdt.Rows[count]["INP_NO"] == DBNull.Value ? "" : patientdt.Rows[count]["INP_NO"].ToString();
                txtVisitID.Text = patientdt.Rows[count]["VISIT_ID"] == DBNull.Value ? "" : patientdt.Rows[count]["VISIT_ID"].ToString();
                txtChargeType.Text = patientdt.Rows[count]["CHARGE_TYPE"] == DBNull.Value ? "" : patientdt.Rows[count]["CHARGE_TYPE"].ToString();
                txtName.Text = patientdt.Rows[count]["NAME"] == DBNull.Value ? "" : patientdt.Rows[count]["NAME"].ToString();
                txtSex.Text = patientdt.Rows[count]["SEX"] == DBNull.Value ? "" : patientdt.Rows[count]["SEX"].ToString();
                txtInDept.Text = patientdt.Rows[count]["DEPT_ADMISSION_TO"] == DBNull.Value ? "" : patientdt.Rows[count]["DEPT_ADMISSION_TO"].ToString();
                txtInTime.Text = patientdt.Rows[count]["ADMISSION_DATE_TIME"] == DBNull.Value ? "" : patientdt.Rows[count]["ADMISSION_DATE_TIME"].ToString();
                DataTable rcptdt = GetPrepaymentRcptByPatientID(txtPatientid.Text, Convert.ToInt32(txtVisitID.Text));
                gcClinic.DataSource = rcptdt;
                
                DataRow[] dr = rcptdt.Select("TRANSACT_TYPE<>'作废'");
                decimal sum = 0;
                for (int i = 0; i < dr.Length; i++)
                {
                    sum = sum + Convert.ToDecimal(dr[i]["AMOUNT"]);
                }
                txtsum.Text = sum.ToString("#0.00");
                DataRow[] dryue = rcptdt.Select("TRANSACT_TYPE<>'作废' and TRANSACT_TYPE<>'结算'");
                decimal yue = 0;
                decimal ldc_prepayments = 0;
                decimal ldc_approve = 0;
                decimal ldc_charge = 0;
                decimal ldc_prepaybalan = 0;
                decimal ldc_use = 0;
                //for (int i = 0; i < dryue.Length; i++)
                //{
                //    yue = yue + Convert.ToDecimal(dr[i]["PREPAYMENTS"]);
                //}
                PlatCommon.Common.PublicFunction.GetPrepayment("*", txtPatientid.Text, txtVisitID.Text, ref ldc_prepayments, ref ldc_approve, ref ldc_charge, ref ldc_prepaybalan, ref ldc_use);
                txtyue.Text = yue.ToString("#0.00");
                //if (count + 1 < patientdt.Rows.Count)
                //    count++;
                //else count = 0;
            }
        }
    }
}
