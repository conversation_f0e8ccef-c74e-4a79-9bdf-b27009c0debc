﻿/*********************************************
* 命名空间 ：Tjhis.CsComm
* 类 名 称 ：Cs01Base64
* 功能说明 ：
* 作    者 ：刘成刚
* 创建时间 ：2020-06-10 16:59:37
* 更 新 人 ：
* 更新时间 ：
* 更新说明 ：
* 版 本 号 ：v1.0.0.0
* CLR 版本 ：4.0.30319.42000
* 版权说明：北京天健源达 HIS基础产品研发部
/*********************************************/

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Text;

namespace PlatCommon.Base01
{
    /// <summary>
    /// Base64与文件的转换
    /// </summary>
    public static class Cs01Base64
    {
        /// <summary>        
        /// base64转文件     
        /// </summary> 
        /// <param name="strBase64">Base64串</param>  
        /// <param name="strFileName">保存的文件名</param>
        /// <returns>成功</returns>
        public static Boolean Base64ToFile(string strBase64, string strFileName)
        {
            //检查字符串
            if (string.IsNullOrEmpty(strBase64)) return false;

            //检查文件名
            if (string.IsNullOrEmpty(strFileName)) return false;
            if (File.Exists(strFileName)) File.Delete(strFileName);

            try
            {
                //转换
                byte[] buffer = Convert.FromBase64String(strBase64);

                //保存为文件
                int iFileLen = buffer.Length;
                FileStream fs = new FileStream(strFileName, FileMode.OpenOrCreate);
                fs.Write(buffer, 0, iFileLen);
                fs.Close();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>        
        /// base64 转 Image       
        /// </summary> 
        /// <param name="strBase64">Base64串</param>  
        /// <param name="strFileName">保存的文件名</param>  
        /// <param name="imgFormat">图片格式</param>  
        public static Boolean Base64ToImage(string strBase64, string strFileName, ImageFormat imgFormat)
        {
            //检查字符串
            if (string.IsNullOrEmpty(strBase64)) return false;

            //检查文件名
            if (string.IsNullOrEmpty(strFileName)) return false;
            if (File.Exists(strFileName)) File.Delete(strFileName);

            try
            {
                //将base64头部信息替换
                strBase64 = strBase64.Replace("data:image/png;base64,", "");
                strBase64 = strBase64.Replace("data:image/jgp;base64,", "");
                strBase64 = strBase64.Replace("data:image/jpeg;base64,", "");
                strBase64 = strBase64.Replace("data:image/bmp;base64,", "");
                strBase64 = strBase64.Replace("data:image/gif;base64,", "");
                byte[] buffer = Convert.FromBase64String(strBase64);
                MemoryStream memStream = new MemoryStream(buffer);
                Image mImage = Image.FromStream(memStream);
                Bitmap bitmap = new Bitmap(mImage);

                //保存为图片文件
                bitmap.Save(strFileName, imgFormat);

                return true;

            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>        
        /// Image 转成 base64        
        /// </summary>        
        /// <param name="strFileFullName"></param>  
        /// <param name="imgFormat">图片格式</param>  
        public static string ImageToBase64(string strFileFullName, ImageFormat imgFormat)
        {
            string strResult = string.Empty;

            //检查文件是不是存在
            if (!File.Exists(strFileFullName)) return strResult;

            try
            {
                Bitmap bitmap = new Bitmap(strFileFullName);
                MemoryStream ms = new MemoryStream();
                bitmap.Save(ms, imgFormat);
                byte[] buffer = new byte[ms.Length];
                ms.Position = 0;
                ms.Read(buffer, 0, (int)ms.Length);
                ms.Close();
                strResult = Convert.ToBase64String(buffer);
            }
            catch (Exception)
            {
                return null;
            }

            return strResult;
        }

        /// <summary>        
        /// 文件转成 base64        
        /// </summary>        
        /// <param name="strFileFullName"></param>  
        public static string FileToBase64(string strFileFullName)
        {
            string strResult = string.Empty;

            //检查文件是不是存在
            if (!File.Exists(strFileFullName)) return strResult;

            try
            {
                //读文件
                FileStream fs = new FileStream(strFileFullName, FileMode.Open);
                int iFileLen = (int)fs.Length;
                Byte[] buffer = new byte[iFileLen];
                fs.Read(buffer, 0, iFileLen);
                fs.Close();

                //转换
                strResult = Convert.ToBase64String(buffer);
            }
            catch (Exception)
            {
                return null;
            }

            return strResult;
        }

        /// <summary>        
        /// base64 转 数据流       
        /// </summary> 
        /// <param name="strBase64">Base64串</param>  
        /// <param name="memStream">数据流</param>  
        public static Boolean Base64ToStream(string strBase64, ref MemoryStream memStream)
        {
            //检查字符串
            if (string.IsNullOrEmpty(strBase64)) return false;

            try
            {
                //将base64头部信息替换
                strBase64 = strBase64.Replace("data:image/png;base64,", "");
                strBase64 = strBase64.Replace("data:image/jgp;base64,", "");
                strBase64 = strBase64.Replace("data:image/jpeg;base64,", "");
                strBase64 = strBase64.Replace("data:image/bmp;base64,", "");
                strBase64 = strBase64.Replace("data:image/gif;base64,", "");
                byte[] buffer = Convert.FromBase64String(strBase64);
                memStream = new MemoryStream(buffer);

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        ///  数据流 转base64 
        /// </summary>
        /// <param name="memStream">数据流</param>
        /// <returns></returns>
        public static string StreamToBase64(MemoryStream memStream)
        {
            string strResult = string.Empty;

            try
            {
                byte[] buffer = new byte[memStream.Length];
                memStream.Position = 0;
                memStream.Read(buffer, 0, (int)memStream.Length);
                memStream.Close();
                strResult = Convert.ToBase64String(buffer);
            }
            catch (Exception)
            {
                return null;
            }

            return strResult;
        }
    }
}
