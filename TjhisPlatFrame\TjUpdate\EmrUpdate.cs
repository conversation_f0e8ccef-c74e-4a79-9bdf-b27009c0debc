﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.IO;
using System.Net;
using System.Diagnostics;
using System.Net.Sockets;
using NM_Service.NMService;
using PlatCommon.SysBase;
using DevExpress.XtraEditors;
/*
 * 20131122wsx(1)增加创建update文件夹机制;(2)不启用自动更新不比对
*/

namespace Tj_Update
{
    public partial class EmrUpdate : ParentForm
    {
        public string appName = "";
        public string appPath = "";//路径自定义
        public string userName = "";
        private string userPw;
        public static string FtpIp = "";
        public static string FtpUser = "";
        public static string FtpPW = "";
        public static string FtpPort = "";
        FTPClient ftp ;
        private string exe;
        private string uri;
        private string _newVersion = string.Empty;
        private int _updateNum = 0;
        private string _keyLog = string.Empty;
        string isrun = string.Empty;
        /// <summary>
        /// 更新是否使用hisunitcode
        /// </summary>
        string update_use_unitcode = "";
        /// <summary>
        /// 下载文件默认缓存大小
        /// </summary>
        string buffsize = "4096";
        /// <summary>
        /// 服务器编码，解决中文乱码
        /// </summary>
        string encodingStr = "GBK";
        bool isDebug = false;
        int is_Update = 0;//是否更新，默认不能更新，<=0不更新，否则更新
        DateTime local_Ver_Time;//本地版本时间 
        /// <summary>
        /// 当前下载文件夹
        /// </summary>
        string CurFiledir = "";
        public enum FileListStyle
        {
            UnixStyle,
            WindowsStyle,
            Unknown
        }
        
        public delegate void updateData(string value); //设置委托用来更新主界面
        public delegate void updateInfo(string filename,string value); //设置委托用来更新主界面

        updateInfo UIDel;
        updateData Progress;
        int DownloadNum = 0;
        public EmrUpdate(string path,string ApplicationName,string userId ,string passWord,string app,string isRun)
        {
            InitializeComponent();
            uri = path;//--路径
            exe = ApplicationName;
            userName = userId;
            userPw = passWord;
            appName = app;
            appPath = GetPath(appName);//获取下载路径
            isrun = isRun;
            //string ApplicationPath = System.Configuration.ConfigurationManager.AppSettings["ApplicationPath"] != null && !string.IsNullOrEmpty(System.Configuration.ConfigurationManager.AppSettings["ApplicationPath"].ToString()) ? System.Configuration.ConfigurationManager.AppSettings["ApplicationPath"].ToString() : "Client";
            //if (!string.IsNullOrEmpty(ApplicationPath))
            //{
            //    appPath += "//" + ApplicationPath;
            //}
            
        }

        private void EmrUpdate_Load(object sender, EventArgs e)
        {
            try
            {
                string debug = Utility.ConfigHelper.GetConfigString("DEBUGLOG");
                if ("1".Equals(debug)) isDebug = true;//开写日志
                ftp = new FTPClient(isDebug);
                
            }
            catch
            {

            }
            
            //当前启动的程序
            //appName = "EMR3.7";
            //
            //MessageBox.Show(uri + "|" + exe, userName + "|" + userPw + "|" + appName);
            //label1.Text = "程序更新中，请稍后．．．．．．";
            lbStart.Text = "正在更新文件："; lblfilename.Text = "";
            lblinfo.Text = "";

            update_use_unitcode = PlatCommon.SysBase.SystemParm.GetParaValue("UPDATE_USE_UNITCODE", "*", "*", "*", "0");
            if ("1".Equals(update_use_unitcode))
            {
                buffsize = PlatCommon.SysBase.SystemParm.GetParaValue("UPDATE_BUFFER_SIZE", "*", "*", "*", "4096",PlatCommon.SysBase.SystemParm.HisUnitCode);
                encodingStr = PlatCommon.SysBase.SystemParm.GetParaValue("UPDATE_ENCODING", "*", "*", "*", "GBK", PlatCommon.SysBase.SystemParm.HisUnitCode);
            }
            else
            {
                buffsize = PlatCommon.SysBase.SystemParm.GetParaValue("UPDATE_BUFFER_SIZE", "*", "*", "*", "4096","");
                encodingStr = PlatCommon.SysBase.SystemParm.GetParaValue("UPDATE_ENCODING", "*", "*", "*", "GBK","");
            }
            ftp.BufferSize = Convert.ToInt32(buffsize);
            ftp.EncodingStr = encodingStr;
            //判断是否需要更新
            is_Update = CheckUpdate();//1,对比版本时间更新，2，全部更新，0不必更新 -1 没有找到服务端版本，不更新
            if (is_Update>=1)
            {
                timer1.Enabled = true;
            }
            else
            {
                if (is_Update == 0)
                {
                    _keyLog = "本地文件是最新的，不需要更新！";
                }
                else
                {
                    _keyLog = "未找到服务端版本，不需要更新！";
                }
                
                //更新日志
                UpdateLog();
                //重新启动程序
                if (isrun == "run")
                    ReStartEmr();
                else
                    Application.Exit();
            }
        }
        /// <summary>
        /// 全部更新
        /// </summary>
        /// <returns></returns>
        private int AppUpdate()
        {
            //如果需要更新，取ftp信息
            if (FtpParam() < 0)
            {
                _keyLog = "无法获得FTP参数信息，请检查参数设置是否正确！";
                XtraMessageBox.Show(_keyLog, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return -1;
            }
            //先改名
            //System.IO.File.Delete(exe + ".old");    
            //System.IO.File.Move(exe,exe+".old");
            try
            {
                string localpath = System.Configuration.ConfigurationManager.AppSettings["ApplicationPath"] != null && !string.IsNullOrEmpty(System.Configuration.ConfigurationManager.AppSettings["ApplicationPath"].ToString()) ? System.Configuration.ConfigurationManager.AppSettings["ApplicationPath"].ToString() : "Client";
                
                if (isDebug)
                {
                    Utility.LogFile.WriteLogAuto("本地路径：" + uri + "\\" + localpath + " 服务器：" + "ftp://" + FtpIp + ":" + FtpPort + "/" + appPath, "AppUpdate");
                    Utility.LogFile.WriteLogAuto("开始时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "AppUpdate");
                }
                bool ret = downftpNew("ftp://" + FtpIp + ":" + FtpPort + "/" + appPath, "", uri + "\\" + localpath, FtpUser, FtpPW);
                //bool ret = downftpNewThread("ftp://" + FtpIp + ":" + FtpPort + "/" + appPath, "", uri + "\\" + localpath, FtpUser, FtpPW);
                if (isDebug)
                {
                    Utility.LogFile.WriteLogAuto("结束时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "AppUpdate");
                }   
                if (ret)
                    ;//System.IO.File.Delete(exe + ".old");
                else
                {
                    if (total_count == 0) return 0;//没有文件下载
                    XtraMessageBox.Show("获取服务器文件失败！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    //恢复文件名
                    //System.IO.File.Move(exe + ".old", exe);
                    return -1;
                }
            }
            catch(Exception ee)
            {
                _keyLog = "连接ftp服务器失败，请检查ftp服务是否正常！";
                XtraMessageBox.Show(_keyLog + ee.ToString(), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                //System.IO.File.Move(exe + ".old",exe);
                return -1;
            }
            //更新版本号
            if (UpdateVer() < 0)
            {
                _keyLog = "版本号更新失败，请检查程序是否已经运行！";
                XtraMessageBox.Show(_keyLog, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                System.IO.File.Move(exe + ".old", exe);
                return -1;
            }
            return 1;
        }

        /// <summary>
        /// 是否需要更新
        /// </summary>
        /// <returns>true 需要 false 不需要</returns>
        /// <returns>2 全部更新 1 对比版本时间更新 0不需要更新 -1 没有服务端版本号</returns>
        private int CheckUpdate()
        {
            try
            {
                //取出最新程序的版本号
                string newVersion = GetLastVer();
                if (newVersion==null)
                {
                    return -1;
                }
                _newVersion = newVersion;
                //取出本地的ver.txt中的版本号
                string ver = GetVer();
                //如果没有本地版本，全都更新 by lions 2019-05-5
                if ("NULL".Equals(ver))
                {
                    return 2;//没有本地版本文件，就更新全部服务端文件
                }
                //如果相同，则不需要更新，如果不同，则更新
                if (ver.CompareTo(newVersion) >= 0)
                {
                    return 0;
                }
                else
                {
                    string vertime = ver.Substring(0, 4) + "-" + ver.Substring(4, 2) + "-" + ver.Substring(6, 2) + " " + ver.Substring(8, 2) + ":" + ver.Substring(10, 2) + ":" + ver.Substring(12, 2);
                    local_Ver_Time = Convert.ToDateTime(vertime);//取当前本地版本时间
                    return 1;//服务端和本地版本不同，部分更新
                }
            }
            catch(Exception ex)
            {
                return 2;
            }
        }

        /// <summary>
        /// 获得ftp信息
        /// </summary>
        /// <returns></returns>
        private int FtpParam()
        {
            DataSet dsFtp = null;
            try
            {
                string condition = "";
                if ("1".Equals(update_use_unitcode))
                {
                    condition = " where HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
                }
                using (ServerPublicClient proxy = new ServerPublicClient())
                {
                    dsFtp = proxy.GetDataBySql("select * from comm.UPDATE_PARA " + condition);
                }

                if (dsFtp == null || dsFtp.Tables[0].Rows.Count == 0) return 0;
                foreach (DataRow dr in dsFtp.Tables[0].Rows)
                {
                    if (dr["CODE"].ToString() == "01")
                    {
                        FtpIp = dr["VALUE"].ToString();
                    }
                    else if (dr["CODE"].ToString() == "02")
                    {
                        FtpUser = dr["VALUE"].ToString();
                    }
                    else if (dr["CODE"].ToString() == "03")
                    {
                        FtpPW = dr["VALUE"].ToString();
                    }
                    else if (dr["CODE"].ToString() == "04")
                    {
                        FtpPort = dr["VALUE"].ToString();
                    }
                }
                return 1;
            }
            catch
            {
                return -1;
            }
        }

        private int UpdateVer()
        {
            if (!Directory.Exists(uri + @"\\update"))//若文件夹不存在则新建文件夹  
            {
                Directory.CreateDirectory(uri + @"\\update"); //新建文件夹  
            }
            StreamWriter sw = new StreamWriter(uri + @"\update\ver.txt", false);
            sw.WriteLine(_newVersion);
            sw.Close();//写入
            return 1;
        }

        /// <summary>
        /// 重新启动程序
        /// </summary>
        /// <returns></returns>
        private void ReStartEmr()
        {
            //参数
            string[] arg = new string[3];
            arg[2] = "";// SystemParm.AppName.ToUpper(); //"tj_app_updated";
            if (!string.IsNullOrEmpty(userName.ToUpper()) && !string.IsNullOrEmpty(userPw.ToUpper()))
            {
                arg[0] = userName;
                arg[1] = userPw;
            }
            string s = "";
            //修改成一个参数 2016-08-08 梁吉
            foreach (string ar in arg)
            {
                s = s + ar + "|";
            }

            s = s.Trim();
            if (s.Substring(s.Length - 1).Equals("|"))
            {
                s = s.Remove(s.Length - 1);
            }
            //关闭程序
            Application.Exit();
            //DeleteItselfByCMD();
            string ApplicationPath = System.Configuration.ConfigurationManager.AppSettings["ApplicationPath"] != null && !string.IsNullOrEmpty(System.Configuration.ConfigurationManager.AppSettings["ApplicationPath"].ToString()) ? System.Configuration.ConfigurationManager.AppSettings["ApplicationPath"].ToString() : "Client";
            //启动原有程序
            if (File.Exists(uri + "\\"+ ApplicationPath + "\\" + exe))
            {
                Process process = new Process();
                
                ProcessStartInfo startInfo = new ProcessStartInfo(uri + "\\"+ ApplicationPath+"\\" + exe, s);
                startInfo.WindowStyle = ProcessWindowStyle.Hidden;
                //startInfo.WorkingDirectory = uri + "\\emrclient";
                startInfo.WorkingDirectory = uri + "\\" + ApplicationPath;
                startInfo.WindowStyle = ProcessWindowStyle.Hidden;
                process.StartInfo = startInfo;
                
                process.StartInfo.UseShellExecute = false;
                process.Start();
            }
        }
        /// <summary>
        /// 删除程序自身
        /// </summary>
        private static void DeleteItselfByCMD()
        {
            ProcessStartInfo psi = new ProcessStartInfo("cmd.exe", "/C ping 1.1.1.1 -n 1 -w 1000 > Nul & Del " + Application.ExecutablePath);
            psi.WindowStyle = ProcessWindowStyle.Hidden;
            psi.CreateNoWindow = true;
            Process.Start(psi);
            Application.Exit();
        }
        /// <summary>
        /// 读取本地版本号
        /// </summary>
        /// <returns></returns>
        private string GetVer()
        {
            if (!File.Exists(uri + @"\update\ver.txt"))
            {
                return "NULL";
            }
            FileStream fs = new FileStream(uri + @"\update\ver.txt", FileMode.Open);
            StreamReader m_streamReader = new StreamReader(fs);
            m_streamReader.BaseStream.Seek(0, SeekOrigin.Begin);
            string arry = "";
            string strLine = m_streamReader.ReadLine();
            arry = strLine;
            m_streamReader.Close();
            m_streamReader.Dispose();
            fs.Close();
            fs.Dispose();
            return arry;
        }

        /// <summary>
        /// 取出最新版本号
        /// </summary>
        /// <returns></returns>
        private string GetLastVer()
        {
            using (ServerPublicClient proxy = new ServerPublicClient())
            {
                //return proxy.GetVer_APPLICATIONS_PARA(appName);
                string condition = "";
                if ("1".Equals(update_use_unitcode))
                {
                    condition = "AND HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
                }
                DataSet ds = proxy.GetDataBySql(" SELECT LASTUPLOADTIME FROM APPLICATIONS_PARA WHERE CODE ='" + appName + "' AND ISDOWNLOAD ='Y' " + condition);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    DataRow dr = ds.Tables[0].Rows[0];
                    return dr[0] == DBNull.Value ? "" : dr[0].ToString();
                }
            }
            return "";
        }
        /// <summary>
        /// 取出最新版本号
        /// </summary>
        /// <returns></returns>
        private string GetPath(string strapp)
        {
            //获取路径
            using (ServerPublicClient proxy = new ServerPublicClient())
            {
                //return proxy.GetVer_APPLICATIONS_PARA(appName);
                DataSet ds = proxy.GetDataBySql(" select REMOTEPATH from APPLICATIONS_PARA where code ='" + appName + "'");
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    DataRow dr = ds.Tables[0].Rows[0];
                    return dr[0] == DBNull.Value ? "" : dr[0].ToString();
                }
            }
            return "";
        }



        //从ftp服务器上下载文件的功能
        private void FtpDownload(string filePath, string fileName)
        {
            FtpWebRequest reqFTP;
            try
            {
                FileStream outputStream = new FileStream(filePath + "//" + fileName, FileMode.Create);
                reqFTP = (FtpWebRequest)FtpWebRequest.Create(new Uri("ftp://" + FtpIp + ":" + FtpPort + "//" + fileName));
                reqFTP.Method = WebRequestMethods.Ftp.DownloadFile;
                reqFTP.UseBinary = true;
                reqFTP.Credentials = new NetworkCredential(FtpUser, FtpPW);
                FtpWebResponse response = (FtpWebResponse)reqFTP.GetResponse();
                Stream ftpStream = response.GetResponseStream();
                long cl = response.ContentLength;
                int bufferSize = 1024*512;
                int readCount;
                byte[] buffer = new byte[bufferSize];
                readCount = ftpStream.Read(buffer, 0, bufferSize);
                while (readCount > 0)
                {
                    outputStream.Write(buffer, 0, readCount);
                    readCount = ftpStream.Read(buffer, 0, bufferSize);
                }
                ftpStream.Close();
                outputStream.Close();
                response.Close();
            }
            catch (Exception ex)
            {
                //Response.Write("Download Error：" + ex.Message);
                XtraMessageBox.Show("下载失败！", "提示");
            }
        }

        /// <summary>
        /// 获得ftp目录
        /// </summary>
        /// <returns></returns>
        public static string[] GetFtpFileList()
        {
            string[] downloadFiles;
            StringBuilder result = new StringBuilder();
            FtpWebRequest request;
            try
            {
                request = (FtpWebRequest)FtpWebRequest.Create(new Uri("ftp://" + FtpIp + ":" + FtpPort));
                request.UseBinary = true;
                request.Credentials = new NetworkCredential(FtpUser, FtpPW);
                request.Method = WebRequestMethods.Ftp.ListDirectory;
                request.UseBinary = true;

                WebResponse response = request.GetResponse();
                StreamReader reader = new StreamReader(response.GetResponseStream());

                string line = reader.ReadLine();
                while (line != null)
                {
                    result.Append(line);
                    result.Append("\n");
                    line = reader.ReadLine();
                }
                // to remove the trailing '\n' 
                result.Remove(result.ToString().LastIndexOf('\n'), 1);
                reader.Close();
                response.Close();
                return result.ToString().Split('\n');
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show(ex.Message);
                downloadFiles = null;
                return downloadFiles;
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            try
            {
                timer1.Enabled = false;
                progressBar1.Minimum = 0;
                progressBar1.Maximum = 100;
                label3.Text = DateTime.Now.ToString();

                //调用自动更新程序
                int result = AppUpdate();
                if (result == -1)
                {
                    //退出
                    this.Close();
                }
                else if (result == 0)
                {
                    //没有更新不用退出 
                    _keyLog = "服务器无更新文件";
                }
                else
                {
                    _keyLog = "更新完毕,此次更新文件" + DownloadNum + "个";
                    //更新日志
                    UpdateLog();
                }
                
                label5.Text = DateTime.Now.ToString();
                //重新启动程序
                if (isrun == "run")
                    ReStartEmr();
                else
                    Application.Exit();
            }
            catch
            {
                _keyLog = "启动更新程序失败，请检查更新程序是否存在或者路径是否正确！";
                MessageBox.Show(_keyLog, "提示");
                //更新日志
                UpdateLog();
                Application.Exit();
            }
        }

        public void UpdateLog()
        {
            //bool retValue=false;
            int retValue = 0;
            string ipStr=string.Empty;
            //IPHostEntry ipe = Dns.GetHostEntry(Dns.GetHostName());
            //IPAddress ipa=ipe.AddressList[3];
            IPAddress[] arrIPAddresses = Dns.GetHostAddresses(Dns.GetHostName());
            foreach (IPAddress ip in arrIPAddresses)
            {
                if (ip.AddressFamily.Equals(AddressFamily.InterNetwork))
                {
                    ipStr = ip.ToString();
                }
            }
            DataSet dsLog = null;
            //更新的数据集
            string ls_date="";
            using (ServerPublicClient proxy = new ServerPublicClient())
            {
                dsLog = proxy.GetDataBySql("select * from UPDATE_LOG where 1=0 ");
                ls_date = proxy.GetSysDateTime();
                //插入一行
                DataRow dr = dsLog.Tables[0].NewRow();
                dr["RQ"] = ls_date;
                dr["APP_CODE"] = appName;
                dr["WSIP"] = ipStr;
                dr["WSNAME"] = userName;
                dr["VERSION"] = _newVersion;
                dr["KEY"] = _keyLog;
                dr["UPDATE_TYPE"] = "D"; 
                if ("1".Equals(update_use_unitcode))
                {
                    dr["HIS_UNIT_CODE"] = PlatCommon.SysBase.SystemParm.HisUnitCode;
                }
                dsLog.Tables[0].Rows.Add(dr);
                retValue = proxy.SaveDataSet(dsLog);
            }
            Dictionary<string, string> idcdel = new Dictionary<string, string>();
            
            if (retValue<=0)
            {
                XtraMessageBox.Show("更新日志失败！", "提示",MessageBoxButtons.OK,MessageBoxIcon.Information);
            }
        }
        int total_count = 0;
        /// <summary>
        /// 下载方法KO
        /// </summary>
        /// <param name="ftpads">FTP路径</param>
        /// <param name="name">需要下载文件路径</param>
        /// <param name="Myads">保存的本地路径</param>
        #region old
        public bool downftp(string ftpads, string name, string Myads, string ftpuser, string ftppassword)
        {
            List<DownFileStruct> flist = new List<DownFileStruct>();
            string downloadDir = Myads + "\\" + name;
            string ftpdir = ftpads + "/" + name;
            total_count = getDownFileCount(ftpads, name, Myads, ftpuser, ftppassword ,ref flist);
            progressBarControl1.Properties.Maximum = total_count;
            progressBarControl1.Properties.Step = 1;
            progressBarControl1.Position = 0;
            string list_type = WebRequestMethods.Ftp.ListDirectoryDetails;
            string[] fullname;
            decimal total = 0;
            decimal nowNum = 1;
            DownloadNum = 1;
            UIDel = new updateInfo(updateUI);//更新进度百分比
            
            if (is_Update == 2)
            {
                fullname = ftp.ftp(ftpads, name, WebRequestMethods.Ftp.ListDirectoryDetails, ftpuser, ftppassword);
                //FileListStyle ftpType = ftp.GuessFileListStyle(fullname);
                total = fullname.Length;
            }
            else
            {
                //遍历结构体
                FTPClient.FileStruct[] fileList = ftp.ftpList(ftpads, name, list_type, ftpuser, ftppassword);
                int arryNum = fileList.Length;
                fullname = new string[arryNum];
                total = fullname.Length;
                for (int i = 0; i < fileList.Length; i++)
                {
                    if (!fileList[i].IsDirectory)
                    {
                        if (fileList[i].Name != null && !string.IsNullOrEmpty(fileList[i].Name.Trim()) && local_Ver_Time < fileList[i].CreateTime)
                        {
                            //如果本地版本时间小于服务器文件创建时间，记录更新，创建时间精确到分钟 by lions 2019-05-06
                            fullname[i] = fileList[i].Name;
                        }
                        //否则，不更新文件
                    }
                    else
                    {
                        if (list_type == "LIST")
                        {
                            //fullname[i] = "<DIR> " + fileList[i].Name;
                            nowNum += getDownFileList(ftpdir, fileList[i].Name, downloadDir, ftpuser, ftppassword);
                        }
                        else if (list_type == "NLST")
                        {
                            if (fileList[i].Name != null && !string.IsNullOrEmpty(fileList[i].Name.Trim()) && local_Ver_Time < fileList[i].CreateTime)
                            {
                                //如果本地版本时间小于服务器文件创建时间，记录更新，创建时间精确到分钟 by lions 2019-05-06
                                fullname[i] = fileList[i].Name;
                            }
                            //否则，不更新文件
                        }
                    }
                }
            }


            //Progress = new updateData(updateProgess);// 更新下载文件信息
            //判断是否为单个文件 
            //if (fullname.Length <= 2)
            //{
            //    if (fullname[fullname.Length - 1] == "")
            //    {
            //        download(downloadDir + "/" + fullname[0], ftpads + "//" + name + "/" + fullname[0], ftpuser, ftppassword);
            //    }
            //}
            //else
            //{
            //string[] onlyname = ftp.ftp(ftpads, name, WebRequestMethods.Ftp.ListDirectory, ftpuser, ftppassword);
            //string[] onlyname = ftp.ftpFile(ftpads, name, WebRequestMethods.Ftp.ListDirectoryDetails, ftpuser, ftppassword);
            if (!Directory.Exists(downloadDir))
            {
                Directory.CreateDirectory(downloadDir);
            }
            //if (fullname.Length == 0 || onlyname.Length == 0) return false;
            if (fullname.Length == 0) return false;
            foreach (string names in fullname)
            {
                if (names == null) continue;
                //判断是否具有文件夹标识<DIR>
                if (names.Contains("<DIR>"))
                {
                    string olname = names.Split(new string[] { "<DIR>" }, StringSplitOptions.None)[1].Trim();
                    downFile(ftpdir, olname, downloadDir, ftpuser, ftppassword);
                }
                else
                {
                    //修改下载，不用判断本地文件 2016-11-02 lions
                    ftp.download(downloadDir + "\\" + names, ftpads + "/" + names, ftpuser, ftppassword);
                    //Progress.Invoke(DownloadNum.ToString());
                    progressBarControl1.PerformStep();
                    progressBarControl1.Update();
                    UIDel.Invoke(names, DownloadNum + "/" + total_count + "个文件");
                    DownloadNum++;
                    //foreach (string onlynames in onlyname)
                    //{
                    //    if (onlynames == null) continue;
                    //    if (onlynames == "" || onlynames == " " || names == "")
                    //    {
                    //        break;
                    //    }
                    //    else
                    //    {
                    //        if (names.Contains(onlynames))
                    //        {
                    //            ftp.download(downloadDir + "\\" + onlynames, ftpads + "//" + onlynames, ftpuser, ftppassword);
                    //            //break;
                    //        }
                    //    }
                    //}
                }
                //nowNum++;
                //_updateNum = (int)nowNum;
                //progressBar1.Value = (int)((nowNum / total) * 100);
                //UIDel.Invoke(((int)((nowNum / total) * 100)).ToString());
            }
            return true;
        }
        #endregion
        /// <summary>
        /// ftp下载文件
        /// </summary>
        /// <param name="ftpads">服务器路径</param>
        /// <param name="name">文件名称</param>
        /// <param name="Myads">本地路径</param>
        /// <param name="ftpuser"></param>
        /// <param name="ftppassword"></param>
        /// <returns></returns>
        public bool downftpNew(string ftpads, string name, string Myads, string ftpuser, string ftppassword)
        {
            List<DownFileStruct> flist = new List<DownFileStruct>();//下载文件列表
            //改从数据库获取更新的文件  夏威 2022-09-15
            //total_count = getDownFileCount(ftpads, name, Myads, ftpuser, ftppassword, ref flist);
            total_count = getDownFileCountByDB(ftpads, Myads, ref flist);
            if (isDebug)
            {
                Utility.LogFile.WriteLogAuto("总文件数："+ total_count, "AppUpdate");
            }
            progressBarControl1.Properties.Maximum = total_count;
            progressBarControl1.Properties.Step = 1;
            progressBarControl1.Position = 0;
            DownloadNum = 0;
            UIDel = new updateInfo(updateUI);//更新进度百分比
            string names = "";
            string ftpDirectory = "";
            string localDirectory = "";
            StringBuilder sb = new StringBuilder();
            foreach (DownFileStruct file in flist)
            {
                names = file.FileName;
                ftpDirectory = file.FtpDirectory;
                localDirectory = file.LocalDirectory;
                if (string.IsNullOrEmpty(names) || string.IsNullOrEmpty(ftpDirectory) || string.IsNullOrEmpty(localDirectory)) continue;
                //修改下载，不用判断本地文件 2016-11-02 lions
                if (isDebug)
                {
                    Utility.LogFile.WriteLogAuto("下载路径：" + ftpDirectory + "/" + names+" 本地路径："+ localDirectory + "\\" + names, "AppUpdate");
                }
                ftp.download(localDirectory + "\\" + names, ftpDirectory + "/" + names, ftpuser, ftppassword);
                progressBarControl1.PerformStep();
                progressBarControl1.Update();
                DownloadNum++;
                //Progress.Invoke(DownloadNum.ToString());
                UIDel.Invoke(names, DownloadNum + "/" + total_count + "个文件");
                sb.Append(names + "|");
            }
            if (isDebug)
            {
                Utility.LogFile.WriteLogAuto("下载完成：" + DownloadNum, "AppUpdate");
                if (DownloadNum > 0)
                {
                    Utility.LogFile.WriteLogAuto("下载文件：" + sb.ToString(), "AppUpdate");
                }
                
            }
            return true;
        }
        #region 多线程，有问题
        //public bool downftpNewThread(string ftpads, string name, string Myads, string ftpuser, string ftppassword)
        //{
        //    List<DownFileStruct> flist = new List<DownFileStruct>();//下载文件列表
        //    total_count = getDownFileCount(ftpads, name, Myads, ftpuser, ftppassword, ref flist);
        //    if (isDebug)
        //    {
        //        Utility.LogFile.WriteLogAuto("总文件数：" + total_count, "AppUpdate");
        //    }
        //    progressBarControl1.Properties.Maximum = total_count;
        //    progressBarControl1.Properties.Step = 1;
        //    progressBarControl1.Position = 0;
        //    DownloadNum = 0;
        //    UIDel = new updateInfo(updateUI);//更新进度百分比
        //    Progress = new updateData(updateProgess);
        //    string names = "";
        //    string ftpDirectory = "";
        //    string localDirectory = "";
        //    StringBuilder sb = new StringBuilder();
        //    System.Threading.ThreadPool.SetMinThreads(1, 1);
        //    System.Threading.ThreadPool.SetMaxThreads(10, 10);
        //    System.Threading.ManualResetEvent _ManualEvents = new System.Threading.ManualResetEvent(false);
        //    foreach (DownFileStruct file in flist)
        //    {
        //        names = file.FileName;
        //        ftpDirectory = file.FtpDirectory;
        //        localDirectory = file.LocalDirectory;
        //        if (string.IsNullOrEmpty(names) || string.IsNullOrEmpty(ftpDirectory) || string.IsNullOrEmpty(localDirectory)) continue;
        //        //修改下载，不用判断本地文件 2016-11-02 lions
        //        if (isDebug)
        //        {
        //            Utility.LogFile.WriteLogAuto("下载路径：" + ftpDirectory + "/" + names + " 本地路径：" + localDirectory + "\\" + names, "AppUpdate");
        //        }
        //        System.Threading.ThreadPool.QueueUserWorkItem(new System.Threading.WaitCallback((o) =>
        //        {
        //            ftp.download(localDirectory + "\\" + names, ftpDirectory + "/" + names, ftpuser, ftppassword);
        //            //progressBarControl1.PerformStep();
        //            //progressBarControl1.Update();
        //            DownloadNum++;
        //            ((System.Threading.AutoResetEvent)o).Set();

        //        }), _ManualEvents);
        //        _ManualEvents.WaitOne();
        //        //Progress.Invoke(DownloadNum.ToString());
        //        Progress.Invoke(DownloadNum.ToString());
        //        UIDel.Invoke(names, DownloadNum + "/" + total_count + "个文件");
        //        sb.Append(names + "|");
        //    }


        //    if (isDebug)
        //    {
        //        Utility.LogFile.WriteLogAuto("下载完成：" + DownloadNum, "AppUpdate");
        //        if (DownloadNum > 0)
        //        {
        //            Utility.LogFile.WriteLogAuto("下载文件：" + sb.ToString(), "AppUpdate");
        //        }

        //    }
        //    return true;
        //}
        #endregion

        int getDownFileCount(string ftpads, string name, string Myads, string ftpuser, string ftppassword,ref List<DownFileStruct> flist)
        {
            string downloadDir = Myads + name;
            string ftpdir = ftpads + name;
            

            int nowNum = 0;
            string list_type = WebRequestMethods.Ftp.ListDirectoryDetails;
            if (!Directory.Exists(downloadDir))
            {
                Directory.CreateDirectory(downloadDir);
            }
            if (is_Update == 2)
            {
                string[] fullname = ftp.ftp(ftpads, name, list_type, ftpuser, ftppassword);

                int total = fullname.Length;

                //string[] onlyname = ftp.ftpFile(ftpads, name, list_type, ftpuser, ftppassword);
                
                //if (fullname.Length == 0 || onlyname.Length == 0) return 0;
                foreach (string names in fullname)
                {
                    if (string.IsNullOrEmpty(names)) continue;
                    //判断是否具有文件夹标识<DIR>
                    if (names.Contains("<DIR>"))
                    {
                        
                        string olname = names.Split(new string[] { "<DIR>" },
                        StringSplitOptions.None)[1].Trim();
                        if (isDebug)
                        {
                            Utility.LogFile.WriteLogAuto("路径名称：" + ftpdir+" names:"+ names + " 新路径：" + olname, "AppUpdate");
                        }
                        nowNum += getDownFileCount(ftpdir + "/", olname, downloadDir + "\\", ftpuser, ftppassword,ref flist);
                        //downFile(ftpdir + "//", olname, downloadDir + "\\", ftpuser, ftppassword);
                    }
                    else
                    {
                        DownFileStruct dfs = new DownFileStruct();
                        dfs.FtpDirectory = ftpdir;
                        dfs.LocalDirectory = downloadDir;
                        dfs.FileName = names;
                        flist.Add(dfs);
                        nowNum++;// 文件数量增加
                        //foreach (string onlynames in onlyname)
                        //{
                        //    if (onlynames == "" || onlynames == " " || names == "")
                        //        break;
                        //    else
                        //        if (names.Equals(onlynames))
                        //    {
                        //        //ftp.download(downloadDir + "\\" + onlynames, ftpads + name + "/" + onlynames, ftpuser, ftppassword);
                        //        nowNum++;// 文件数量增加
                        //    }

                        //}
                    }

                }
            }
            else
            {
                //遍历结构体
                FTPClient.FileStruct[] fileList = ftp.ftpList(ftpads, name, list_type, ftpuser, ftppassword);
                int arryNum = fileList.Length;
                for (int i = 0; i < fileList.Length; i++)
                {
                    if (!fileList[i].IsDirectory)
                    {
                        if (fileList[i].Name!=null && !string.IsNullOrEmpty(fileList[i].Name.Trim()) && local_Ver_Time < fileList[i].CreateTime)
                        {
                            //如果本地版本时间小于服务器文件创建时间，记录更新，创建时间精确到分钟 by lions 2019-05-06
                            DownFileStruct dfs = new DownFileStruct();
                            dfs.FtpDirectory = ftpdir;
                            dfs.LocalDirectory = downloadDir;
                            dfs.FileName = fileList[i].Name;
                            dfs.FileTime = fileList[i].CreateTime.ToString("yyyy-MM-dd HH:mm:ss");
                            flist.Add(dfs);
                            nowNum++;
                        }
                        //否则，不更新文件
                    }
                    else
                    {
                        if (list_type == "LIST")
                        {
                            //nowNum += getDownFileCount(ftpdir + "//", fileList[i].Name, downloadDir + "\\", ftpuser, ftppassword, ref flist);
                            nowNum += getDownFileCount(ftpdir + "/", fileList[i].Name, downloadDir + "\\", ftpuser, ftppassword,ref flist);
                        }
                        else if (list_type == "NLST")
                        {
                            if (fileList[i].Name != null && !string.IsNullOrEmpty(fileList[i].Name.Trim()) && local_Ver_Time < fileList[i].CreateTime)
                            {
                                //如果本地版本时间小于服务器文件创建时间，记录更新，创建时间精确到分钟 by lions 2019-05-06
                                //arryFile[i] = fileList[i].Name;
                                DownFileStruct dfs = new DownFileStruct();
                                dfs.FtpDirectory = ftpads;
                                dfs.LocalDirectory = Myads;
                                dfs.FileName = fileList[i].Name;
                                dfs.FileTime = fileList[i].CreateTime.ToString("yyyy-MM-dd HH:mm:ss");
                                flist.Add(dfs);
                                nowNum++;
                            }
                            //否则，不更新文件
                        }
                    }
                }
                
            }
            return nowNum;
        }

        //改从数据库获取更新的文件  夏威 2022-09-15
        int getDownFileCountByDB(string ftpads, string Myads,ref List<DownFileStruct> flist)
        {
            string downloadDir = Myads;
            string ftpdir = ftpads;
            DataSet dsFtp = null;
            string[] strs = ftpads.Split(new char[] { '/'});
            string appcode = strs[strs.Length-1];
            int nowNum = 0;
            if (!Directory.Exists(downloadDir))
            {
                Directory.CreateDirectory(downloadDir);
            }
            if (is_Update == 2)
            {
                using (ServerPublicClient proxy = new ServerPublicClient())
                {
                    dsFtp = proxy.GetDataBySql(string.Format("select * from app_file_ftp_update where APP_NAME='{0}'", appcode));
                }
                if (dsFtp != null && dsFtp.Tables[0].Rows.Count > 0)
                {
                    for (int i = 0; i < dsFtp.Tables[0].Rows.Count; i++)
                    {
                        DataRow dr = dsFtp.Tables[0].Rows[i];
                        DownFileStruct dfs = new DownFileStruct();
                        dfs.FtpDirectory = ftpdir;
                        dfs.LocalDirectory = downloadDir;
                        //update无法更新带.vs的文件
                      //  if (!dr["DIRECTORY"].ToString().Contains("."))
                        {
                            dfs.FtpDirectory = ftpdir + "/" + dr["DIRECTORY"].ToString();
                            dfs.LocalDirectory = downloadDir + "/" + dr["DIRECTORY"].ToString();
                            if (!Directory.Exists(dfs.LocalDirectory))
                            {
                                Directory.CreateDirectory(dfs.LocalDirectory);
                            }
                        }
                        dfs.FileName = dr["FILE_NAME"].ToString();
                        flist.Add(dfs);
                        nowNum++;
                    }
                }
            }
            else
            {
                using (ServerPublicClient proxy = new ServerPublicClient())
                {
                    dsFtp = proxy.GetDataBySql(string.Format("select * from app_file_ftp_update where UPDATE_DATE>TO_DATE('{0}', 'yyyy-mm-dd hh24:mi:ss') and  APP_NAME='{1}'", local_Ver_Time.ToString("yyyy-MM-dd HH:mm:ss"), appcode));
                }
                if (dsFtp != null && dsFtp.Tables[0].Rows.Count > 0)
                {
                    for (int i = 0; i < dsFtp.Tables[0].Rows.Count; i++)
                    {
                        DataRow dr = dsFtp.Tables[0].Rows[i];
                        DownFileStruct dfs = new DownFileStruct();
                        dfs.FtpDirectory = ftpads;
                        dfs.LocalDirectory = Myads;
                       // if (!dr["DIRECTORY"].ToString().Contains("."))
                        {
                            dfs.FtpDirectory = ftpdir + "/" + dr["DIRECTORY"].ToString();
                            dfs.LocalDirectory = downloadDir + "/" + dr["DIRECTORY"].ToString();
                            if (!Directory.Exists(dfs.LocalDirectory))
                            {
                                Directory.CreateDirectory(dfs.LocalDirectory);
                            }
                        }
                        dfs.FileName = dr["FILE_NAME"].ToString();
                        dfs.FileTime = DateTime.Parse(dr["UPDATE_DATE"].ToString()).ToString("yyyy-MM-dd HH:mm:ss");
                        flist.Add(dfs);
                        nowNum++;
                    }
                }
            }
            return nowNum;
        }
        /// <summary>
        /// 取得对应路径文件列表
        /// </summary>
        /// <param name="ftpads"></param>
        /// <param name="name"></param>
        /// <param name="Myads"></param>
        /// <param name="ftpuser"></param>
        /// <param name="ftppassword"></param>
        /// <returns></returns>
        int getDownFileList(string ftpads, string name, string Myads, string ftpuser, string ftppassword)
        {
            string cur_dir = "";
            string downloadDir = Myads + name;
            string ftpdir = ftpads + name;
            int nowNum = 0;
            string list_type = WebRequestMethods.Ftp.ListDirectoryDetails;

            //遍历结构体
            FTPClient.FileStruct[] fileList = ftp.ftpList(ftpads, name, list_type, ftpuser, ftppassword);
            int arryNum = fileList.Length;
            string[] arryFile = new string[arryNum];
            for (int i = 0; i < fileList.Length; i++)
            {
                if (!fileList[i].IsDirectory)
                {
                    if (fileList[i].Name != null && !string.IsNullOrEmpty(fileList[i].Name.Trim()) && local_Ver_Time < fileList[i].CreateTime)
                    {
                        //如果本地版本时间小于服务器文件创建时间，记录更新，创建时间精确到分钟 by lions 2019-05-06
                        if (!Directory.Exists(downloadDir))
                        {
                            Directory.CreateDirectory(downloadDir);
                        }
                        ftp.download(downloadDir + "\\" + fileList[i].Name, ftpdir + "/" + fileList[i].Name, ftpuser, ftppassword);//下载最新文件
                    }
                    //否则，不更新文件
                }
                else
                {
                    if (list_type == "LIST")
                    {
                        nowNum += getDownFileList(ftpdir + "/", fileList[i].Name, downloadDir + "\\", ftpuser, ftppassword);
                    }
                    else if (list_type == "NLST")
                    {
                        if (fileList[i].Name != null && !string.IsNullOrEmpty(fileList[i].Name.Trim()) && local_Ver_Time < fileList[i].CreateTime)
                        {
                            //如果本地版本时间小于服务器文件创建时间，记录更新，创建时间精确到分钟 by lions 2019-05-06
                            if (!Directory.Exists(downloadDir))
                            {
                                Directory.CreateDirectory(downloadDir);
                            }
                            ftp.download(downloadDir + "\\" + fileList[i].Name, ftpads + "/" + fileList[i].Name, ftpuser, ftppassword);//下载最新文件
                            nowNum++;
                        }
                        //否则，不更新文件
                    }
                }
            }
            return nowNum;
        }
        /// <summary>
        /// 下载方法KO
        /// </summary>
        /// <param name="ftpads">FTP路径</param>
        /// <param name="name">需要下载文件路径</param>
        /// <param name="Myads">保存的本地路径</param>
        public bool downFile(string ftpads, string name, string Myads, string ftpuser, string ftppassword)
        {
            string downloadDir = Myads + name;
            string ftpdir = ftpads + name;
            string[] fullname = ftp.ftp(ftpads, name, WebRequestMethods.Ftp.ListDirectoryDetails, ftpuser, ftppassword);
            //当前路径
            string cur_dir= name;
            int total = fullname.Length;
            //updateData UIDel = new updateData(updateProgess);
            int nowNum = 1;
            string[] onlyname = ftp.ftpFile(ftpads, name, WebRequestMethods.Ftp.ListDirectoryDetails, ftpuser, ftppassword);
            if (!Directory.Exists(downloadDir))
            {
                Directory.CreateDirectory(downloadDir);
            }
            if (fullname.Length == 0 || onlyname.Length == 0) return false;
            foreach (string names in fullname)
            {
                if (names == null) continue;
                //判断是否具有文件夹标识<DIR>
                if (names.Contains("<DIR>"))
                {
                    string olname = names.Split(new string[] { "<DIR>" },StringSplitOptions.None)[1].Trim();
                    downFile(ftpdir + "//", olname, downloadDir + "\\", ftpuser, ftppassword);
                }
                else
                {
                    foreach (string onlynames in onlyname)
                    {
                        if (onlynames == "" || onlynames == " " || names == "")
                            break;
                        else
                            if (names.Equals(onlynames))
                            {
                                UIDel.Invoke(names, DownloadNum + "/" + total_count + "个文件，" + "文件夹" + name +" "+ nowNum + "/" + total + "个文件");
                                progressBarControl1.PerformStep();
                                progressBarControl1.Update();
                                nowNum++;
                                DownloadNum++;
                                ftp.download(downloadDir + "\\" + onlynames, ftpads + name + "/" + onlynames, ftpuser, ftppassword);
                            }
                                
                    }
                }
                //nowNum++;
                //_updateNum = (int)nowNum;
                //progressBar1.Value = (int)(nowNum * 100 / total);
                //UIDel.Invoke(((int)(nowNum * 100 / total)).ToString());
            }
            return true;
        }
        public void updateUI(string filename,string value)
        {
            lblfilename.Text = filename;
            lblinfo.Text = value;
            lblfilename.Update();
            lblinfo.Update();
            //this.progressBar1.Value = Int32.Parse(value);
        }
        public void updateProgess(string value)
        {
            //this.progressBarControl1.Position = int.Parse(value);
            progressBarControl1.PerformStep();
            progressBarControl1.Update();
            //progressBar1.Value = Int32.Parse(value);
        }
        private void btnStart_Click(object sender, EventArgs e)
        {
            timer1.Enabled = true;
        }
    }
    /// <summary>
    /// 下载文件结构
    /// </summary>
    public class DownFileStruct
    {
        string _ftpDirectory;
        string _localDirectory;
        string _fileName;
        string _fileTime;
        /// <summary>
        /// 下载文件路径
        /// </summary>
        public string FtpDirectory { get => _ftpDirectory; set => _ftpDirectory = value; }
        /// <summary>
        /// 文件名称
        /// </summary>
        public string FileName { get => _fileName; set => _fileName = value; }
        /// <summary>
        /// 文件时间
        /// </summary>
        public string FileTime { get => _fileTime; set => _fileTime = value; }
        public string LocalDirectory { get => _localDirectory; set => _localDirectory = value; }
    }
}
