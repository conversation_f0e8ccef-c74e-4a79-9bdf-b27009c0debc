﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{6E3CAF76-AA5D-4159-A6CC-5D252BB28FA4}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>PlatCommonForm</RootNamespace>
    <AssemblyName>PlatCommonForm</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <DocumentationFile>..\..\TjhisPlatSource\TJHisPlatEXE\Client\PlatCommonForm.xml</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\DLL\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.BonusSkins.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.BonusSkins.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Data.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.Data.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Images.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.Images.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Printing.v19.1.Core, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.Printing.v19.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.Utils.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraBars.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraBars.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraEditors.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraGrid.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraGrid.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraLayout.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraLayout.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraPdfViewer.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraPdfViewer.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraPrinting.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraPrinting.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraReports.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraTreeList.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraTreeList.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="INMService, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\INMService.dll</HintPath>
    </Reference>
    <Reference Include="Model, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\Model.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NMService, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\NMService.dll</HintPath>
    </Reference>
    <Reference Include="OracleDAL, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\OracleDAL.dll</HintPath>
    </Reference>
    <Reference Include="PlatCommon, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\PlatCommon.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Core">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Deployment">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Windows.Forms">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml">
      <Private>True</Private>
    </Reference>
    <Reference Include="Utility, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\Utility.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ClassArmyPkp.cs" />
    <Compile Include="ClassOpenFormSelect.cs" />
    <Compile Include="comm\ClassAccount.cs" />
    <Compile Include="comm\ReadArmy.cs" />
    <Compile Include="FrmBillitemQueryForRcpt.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmBillitemQueryForRcpt.Designer.cs">
      <DependentUpon>FrmBillitemQueryForRcpt.cs</DependentUpon>
    </Compile>
    <Compile Include="frmDiagnosisDict.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmDiagnosisDict.designer.cs">
      <DependentUpon>frmDiagnosisDict.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmHighQuality.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmHighQuality.Designer.cs">
      <DependentUpon>FrmHighQuality.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmInputSet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmInputSet.designer.cs">
      <DependentUpon>FrmInputSet.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmInputSetItem.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmInputSetItem.designer.cs">
      <DependentUpon>FrmInputSetItem.cs</DependentUpon>
    </Compile>
    <Compile Include="frminputsetting.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frminputsetting.designer.cs">
      <DependentUpon>frminputsetting.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmInputSettingA.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmInputSettingA.Designer.cs">
      <DependentUpon>FrmInputSettingA.cs</DependentUpon>
    </Compile>
    <Compile Include="frminputsettingexstock.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frminputsettingexstock.designer.cs">
      <DependentUpon>frminputsettingexstock.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmInsuranceAdult.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmInsuranceAdult.designer.cs">
      <DependentUpon>FrmInsuranceAdult.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmInsuranceVsItemInput.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmInsuranceVsItemInput.designer.cs">
      <DependentUpon>FrmInsuranceVsItemInput.cs</DependentUpon>
    </Compile>
    <Compile Include="frmkssyymd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmkssyymd.Designer.cs">
      <DependentUpon>frmkssyymd.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmNewMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmNewMain.Designer.cs">
      <DependentUpon>FrmNewMain.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmPassword.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmPassword.designer.cs">
      <DependentUpon>FrmPassword.cs</DependentUpon>
    </Compile>
    <Compile Include="frmPkp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPkp.Designer.cs">
      <DependentUpon>frmPkp.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmPlatFormDemo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmPlatFormDemo.Designer.cs">
      <DependentUpon>FrmPlatFormDemo.cs</DependentUpon>
    </Compile>
    <Compile Include="frmPrepQueryPat.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPrepQueryPat.Designer.cs">
      <DependentUpon>frmPrepQueryPat.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmPrescAgent.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmPrescAgent.Designer.cs">
      <DependentUpon>FrmPrescAgent.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmReadLaw.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmReadLaw.Designer.cs">
      <DependentUpon>FrmReadLaw.cs</DependentUpon>
    </Compile>
    <Compile Include="frmSelectBatchNo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSelectBatchNo.designer.cs">
      <DependentUpon>frmSelectBatchNo.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmSelectInhosPatient.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmSelectInhosPatient.Designer.cs">
      <DependentUpon>FrmSelectInhosPatient.cs</DependentUpon>
    </Compile>
    <Compile Include="frmSelectPatientId.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSelectPatientId.designer.cs">
      <DependentUpon>frmSelectPatientId.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmSetDate.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmSetDate.designer.cs">
      <DependentUpon>FrmSetDate.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmValidateUser.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmValidateUser.designer.cs">
      <DependentUpon>FrmValidateUser.cs</DependentUpon>
    </Compile>
    <Compile Include="frmw_bill_pattern_call.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmw_bill_pattern_call.designer.cs">
      <DependentUpon>frmw_bill_pattern_call.cs</DependentUpon>
    </Compile>
    <Compile Include="frmydnhReadCard.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmydnhReadCard.designer.cs">
      <DependentUpon>frmydnhReadCard.cs</DependentUpon>
    </Compile>
    <Compile Include="GmcPayOff.cs" />
    <Compile Include="LogFile.cs" />
    <Compile Include="LstVirtualCardChk.cs" />
    <Compile Include="PrinterConfigFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PrinterConfigFrm.designer.cs">
      <DependentUpon>PrinterConfigFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="PrinterSetup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PrinterSetup.designer.cs">
      <DependentUpon>PrinterSetup.cs</DependentUpon>
    </Compile>
    <Compile Include="Query\Dict.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Query\Dict.designer.cs">
      <DependentUpon>Dict.cs</DependentUpon>
    </Compile>
    <Compile Include="Query\FrmPatAdmissionQuery.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Query\FrmPatAdmissionQuery.Designer.cs">
      <DependentUpon>FrmPatAdmissionQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\PatAdmissionQueryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Report\PatAdmissionQueryReport.Designer.cs">
      <DependentUpon>PatAdmissionQueryReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\prepayReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Report\prepayReport.Designer.cs">
      <DependentUpon>prepayReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Report\XPRPT_STKZYSF.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Report\XPRPT_STKZYSF.Designer.cs">
      <DependentUpon>XPRPT_STKZYSF.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\XtraReportIbillWristBandPrint.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Report\XtraReportIbillWristBandPrint.designer.cs">
      <DependentUpon>XtraReportIbillWristBandPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\XtraReportPayment920.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Report\XtraReportPayment920.Designer.cs">
      <DependentUpon>XtraReportPayment920.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\XtraReportPaymentDbgj.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Report\XtraReportPaymentDbgj.Designer.cs">
      <DependentUpon>XtraReportPaymentDbgj.cs</DependentUpon>
    </Compile>
    <Compile Include="Service References\appcommRefundService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="ViewDataHelper.cs" />
    <EmbeddedResource Include="FrmBillitemQueryForRcpt.resx">
      <DependentUpon>FrmBillitemQueryForRcpt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmDiagnosisDict.resx">
      <DependentUpon>frmDiagnosisDict.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmHighQuality.resx">
      <DependentUpon>FrmHighQuality.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmInputSet.resx">
      <DependentUpon>FrmInputSet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmInputSetItem.resx">
      <DependentUpon>FrmInputSetItem.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frminputsetting.resx">
      <DependentUpon>frminputsetting.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmInputSettingA.resx">
      <DependentUpon>FrmInputSettingA.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frminputsettingexstock.resx">
      <DependentUpon>frminputsettingexstock.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmInsuranceAdult.resx">
      <DependentUpon>FrmInsuranceAdult.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmInsuranceVsItemInput.resx">
      <DependentUpon>FrmInsuranceVsItemInput.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmkssyymd.resx">
      <DependentUpon>frmkssyymd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmNewMain.resx">
      <DependentUpon>FrmNewMain.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmPassword.resx">
      <DependentUpon>FrmPassword.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPkp.resx">
      <DependentUpon>frmPkp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPrepQueryPat.resx">
      <DependentUpon>frmPrepQueryPat.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmPrescAgent.resx">
      <DependentUpon>FrmPrescAgent.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmReadLaw.resx">
      <DependentUpon>FrmReadLaw.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSelectBatchNo.resx">
      <DependentUpon>frmSelectBatchNo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmSelectInhosPatient.resx">
      <DependentUpon>FrmSelectInhosPatient.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSelectPatientId.resx">
      <DependentUpon>frmSelectPatientId.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmSetDate.resx">
      <DependentUpon>FrmSetDate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmValidateUser.resx">
      <DependentUpon>FrmValidateUser.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmw_bill_pattern_call.resx">
      <DependentUpon>frmw_bill_pattern_call.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmydnhReadCard.resx">
      <DependentUpon>frmydnhReadCard.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PrinterConfigFrm.resx">
      <DependentUpon>PrinterConfigFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PrinterSetup.resx">
      <DependentUpon>PrinterSetup.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Query\Dict.resx">
      <DependentUpon>Dict.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Query\FrmPatAdmissionQuery.resx">
      <DependentUpon>FrmPatAdmissionQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\PatAdmissionQueryReport.resx">
      <DependentUpon>PatAdmissionQueryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\prepayReport.resx">
      <DependentUpon>prepayReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="Report\XPRPT_STKZYSF.resx">
      <DependentUpon>XPRPT_STKZYSF.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\XtraReportIbillWristBandPrint.resx">
      <DependentUpon>XtraReportIbillWristBandPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\XtraReportPayment920.resx">
      <DependentUpon>XtraReportPayment920.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\XtraReportPaymentDbgj.resx">
      <DependentUpon>XtraReportPaymentDbgj.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="app.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <None Include="Service References\appcommRefundService\webService.wsdl" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadataStorage Include="Service References\appcommRefundService\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\appcommRefundService\webService.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\appcommRefundService\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\appcommRefundService\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\appcommRefundService\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\TjhisPlatSource\TjhisInterfaceCA\TjhisInterfaceCA.csproj">
      <Project>{1051351E-92EA-4F94-96F1-8EAA5CF84244}</Project>
      <Name>TjhisInterfaceCA</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>