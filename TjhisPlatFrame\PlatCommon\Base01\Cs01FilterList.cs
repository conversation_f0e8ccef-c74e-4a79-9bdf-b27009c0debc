﻿/*********************************************
* 文 件 名：Cs01FilterList
* 类 名 称：Cs01FilterList
* 功能说明：过滤类
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：吴新才
* 创建时间：2018-05-22 15:38:26
* 版 本 号：1.0.0.1
* 修改时间：2020-02-18 15:38:26
* 修 改 人：刘成刚
* CLR 版本：4.0.30319.42000
/*********************************************/

using System;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using System.Reflection;

namespace PlatCommon.Base01
{
    /// <summary>
    /// 逻辑运算枚举
    /// </summary>
    public enum MyLogicType
    {
        /// <summary>
        /// 逻辑与
        /// </summary>
        And = 0,
        /// <summary>
        /// 逻辑或
        /// </summary>
        Or = 1
    };

    /// <summary>
    /// 运算枚举
    /// </summary>
    public enum MyOperatorName
    {
        /// <summary>
        /// 等于
        /// </summary>
        Equals = 0,
        /// <summary>
        /// 不等于
        /// </summary>
        NotEquals = 1,
        /// <summary>
        /// 包含
        /// </summary>
        Contain = 2,
        /// <summary>
        /// 不包含
        /// </summary>
        NotContain = 3,
        /// <summary>
        /// 大于
        /// </summary>
        GreaterThan = 4,
        /// <summary>
        /// 不大于
        /// </summary>
        NotGreaterThan = 5,
        /// <summary>
        /// 小于
        /// </summary>
        LessThan = 6,
        /// <summary>
        /// 不小于
        /// </summary>
        NotLessThan = 7,
        /// <summary>
        /// 匹配
        /// </summary>
        Like = 8,
        /// <summary>
        /// 不匹配
        /// </summary>
        NotLike = 9
    };

    /// <summary>
    /// 运算条件行
    /// </summary>
    public class MyFilterCondition
    {
        /// <summary>
        /// 运算类型
        /// </summary>
        public MyLogicType LogicType { get; set; } = MyLogicType.And;
        /// <summary>
        /// 字段名
        /// </summary>
        public string FieldName { get; set; }
        /// <summary>
        /// 操作名
        /// </summary>
        public MyOperatorName OpName { get; set; }
        /// <summary>
        /// 字段值
        /// </summary>
        public string fieldValue { get; set; }
        /// <summary>
        /// 过滤条件
        /// </summary>
        public List<MyFilterCondition> FilterConditions { get; set; }
    }

    /// <summary>
    /// 过滤用List类 用于过滤数据与分组汇总
    /// </summary>
    public class Cs01FilterList<T> where T : new()
    {
        private Type DataType;
        private string[] CompareFields;
        private Decimal[] SumFieldValues;
        private Decimal CDbl<T2>(T2 t)
        {
            Decimal dValue = 0.00M;
            try
            {
                dValue = Cs01Functions.CDecimal(t);
            }
            catch (Exception) { dValue = 0; }
            return dValue;
        }

        /// <summary>
        /// 过滤条件List集合, 支持()And(Or)复杂查询， 
        /// 目前接口：只支持两级条件， 条件只支持增加，如果需要改变，请重新设置FilterConditions。
        /// 条件过滤并不限级数，超过两级，请自建条件集合。
        /// </summary>
        public List<MyFilterCondition> FilterConditions { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public Cs01FilterList()
        {
            DataType = (new T()).GetType();
        }

        /// <summary>
        /// 对象数据List
        /// </summary>
        public List<T> Data = new List<T>();

        /// <summary>
        /// 判断数据类实例是否符合过滤条件
        /// </summary>
        /// <param name="t"></param>
        /// <returns></returns>
        public bool IsFilterData(T t)
        {
            return IsFilterDataEx(t, FilterConditions);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="t"></param>
        /// <param name="FilterConditions"></param>
        /// <returns></returns>
        private bool IsFilterDataEx(T t, List<MyFilterCondition> FilterConditions)
        {
            if (FilterConditions == null || FilterConditions.Count == 0)
            {
                return true;
            }
            bool isok = true;
            if (FilterConditions[0].LogicType == MyLogicType.Or) isok = false;
            bool iok = true;
            string sv;
            object value;
            Regex reg;
            foreach (MyFilterCondition fc in FilterConditions)
            {
                if (fc.FilterConditions == null)
                {
                    PropertyInfo pinfo = DataType.GetProperty(fc.FieldName);
                    value = pinfo.GetValue(t, null);
                    sv = (value == null ? "" : value.ToString());
                    if (string.IsNullOrEmpty(sv))
                    {
                        iok = false;
                    }
                    else
                    {
                        switch (fc.OpName)
                        {
                            case MyOperatorName.Equals:
                                iok = (fc.fieldValue == sv);
                                break;
                            case MyOperatorName.NotEquals:
                                iok = (fc.fieldValue != sv);
                                break;
                            case MyOperatorName.Contain:
                                iok = (sv.IndexOf(fc.fieldValue) >= 0);
                                break;
                            case MyOperatorName.NotContain:
                                iok = (sv.IndexOf(fc.fieldValue) < 0);
                                break;
                            case MyOperatorName.GreaterThan:
                            case MyOperatorName.NotLessThan:
                                if (value.IsNumericType())
                                {
                                    iok = (CDbl<object>(value) > CDbl<object>(fc.fieldValue));
                                }
                                else
                                {
                                    return (sv.CompareTo(fc.fieldValue) == -1);
                                }
                                break;
                            case MyOperatorName.NotGreaterThan:
                            case MyOperatorName.LessThan:
                                if (value.IsNumericType())
                                {
                                    iok = (CDbl<object>(value) < CDbl<object>(fc.fieldValue));
                                }
                                else
                                {
                                    return (sv.CompareTo(fc.fieldValue) == 1);
                                }
                                break;
                            case MyOperatorName.Like: //输入正则表达式
                                reg = new Regex(fc.fieldValue);
                                iok = reg.IsMatch(sv);
                                break;
                            case MyOperatorName.NotLike: //输入正则表达式
                                reg = new Regex(fc.fieldValue);
                                iok = !reg.IsMatch(sv);
                                break;
                            default:
                                iok = (fc.fieldValue == sv);
                                break;
                        }
                    }
                }
                else
                {
                    iok = IsFilterDataEx(t, fc.FilterConditions);
                }
                if (fc.LogicType == MyLogicType.And) isok = isok && iok; else isok = isok || iok;
            }
            return isok;
        }

        /// <summary>
        /// 增加一个子条件集合， 返回条件号, 增加子条件是用此返回行号
        /// </summary>
        /// <param name="logicType"></param>
        /// <returns>条件行号</returns>
        public int AddCondition(MyLogicType logicType = MyLogicType.And)
        {
            if (FilterConditions == null) FilterConditions = new List<MyFilterCondition>();
            MyFilterCondition condition = new MyFilterCondition();
            condition.LogicType = logicType;
            condition.FilterConditions = new List<MyFilterCondition>();
            FilterConditions.Add(condition);
            return FilterConditions.Count;
        }

        /// <summary>
        /// 增加过滤条件行，logicType第一行也需要， 计算时如果第一行logicType为Add，会与true运算，logicType为Or， 会与false运算
        /// </summary>
        /// <param name="strFieldName">字段名称 string</param>
        /// <param name="OpName">操作符 枚举MyOperatorName</param>
        /// <param name="strFieldValue">字段值 string</param>
        /// <param name="logicType">条件行之间的逻辑运算符 枚举MyLogicType</param>
        /// <returns>条件行号</returns>
        public int AddCondition(string strFieldName, MyOperatorName OpName, string strFieldValue, MyLogicType logicType = MyLogicType.And)
        {
            if (FilterConditions == null) FilterConditions = new List<MyFilterCondition>();
            MyFilterCondition condition = new MyFilterCondition();
            condition.LogicType = logicType;
            condition.FieldName = strFieldName;
            condition.OpName = OpName;
            condition.fieldValue = strFieldValue;
            FilterConditions.Add(condition);
            return FilterConditions.Count;
        }

        /// <summary>
        /// 增加过滤条件行子集合条件行(目前只支持一级子集合)， 增加子集合时，应先调用AddCondition(MyLogicType logicType)获得行号index
        /// </summary>
        /// <param name="iRowNo">行号</param>
        /// <param name="strFieldName">字段名称 string</param>
        /// <param name="OpName">操作符 枚举MyOperatorName</param>
        /// <param name="strFieldValue">字段值 string</param>
        /// <param name="logicType">条件行之间的逻辑运算符 枚举MyLogicType</param>
        /// <returns>条件行号</returns>
        public int AddCondition(int iRowNo, string strFieldName, MyOperatorName OpName, string strFieldValue, MyLogicType logicType = MyLogicType.And)
        {
            if (FilterConditions == null || FilterConditions.Count < iRowNo) throw new Exception($"AddCondition rowNo({iRowNo}) error。");
            MyFilterCondition condition = FilterConditions[iRowNo - 1];
            List<MyFilterCondition> FilterConditions2 = condition.FilterConditions;
            if (FilterConditions2 == null) throw new Exception($"FilterCondition[{iRowNo}] is not FilterConditions。");

            MyFilterCondition condition2 = new MyFilterCondition();
            condition2.LogicType = logicType;
            condition2.FieldName = strFieldName;
            condition2.OpName = OpName;
            condition2.fieldValue = strFieldValue;
            FilterConditions2.Add(condition2);
            return iRowNo;
        }

        /// <summary>
        /// 获取符合过滤条件的记录
        /// </summary>
        /// <returns></returns>
        public List<T> GetFilterData()
        {
            List<T> list = new List<T>();
            foreach (T t in Data)
            {
                if (IsFilterData(t))
                {
                    list.Add(t);
                }
            }
            return list;
        }

        /// <summary>
        /// 比较两个对象是否相同
        /// </summary>
        /// <param name="t1"></param>
        /// <param name="t2"></param>
        /// <returns></returns>
        private int myCompare(T t1, T t2)
        {
            foreach (string fieldName in CompareFields)
            {
                object v1, v2;
                PropertyInfo pinfo = DataType.GetProperty(fieldName);
                v1 = pinfo.GetValue(t1, null);
                v2 = pinfo.GetValue(t2, null);
                if (v1 == null && v2 == null) continue;
                if (v1 == null) return 1;
                if (v2 == null) return -1;
                if (v1.Equals(v2)) continue;
                return v1.ToString().CompareTo(v2.ToString());
            }
            return 0;
        }

        /// <summary>
        /// 按列分组汇总，生成新的数据, 如果没有指定(null), 则使用类实例的Data
        /// </summary>
        /// <param name="Data">待分组数据List</param>
        /// <param name="strArrFields">分组字段列表 string[]</param>
        /// <param name="strArrSumFields">统计字段列表 string[]</param>
        /// <returns></returns>
        public List<T> GetGroupData(List<T> Data, string[] strArrFields, string[] strArrSumFields)
        {
            if (Data == null) Data = this.Data;
            if (Data.Count == 0) return Data;
            int n = strArrSumFields.Length;
            int fn = strArrFields.Length;
            CompareFields = strArrFields;
            SumFieldValues = new decimal[n];
            Data.Sort(myCompare);
            List<T> list = new List<T>();
            T t1 = Data[0];
            T t2;
            object value;
            PropertyInfo pinfo;
            foreach (T t in Data)
            {
                if (myCompare(t, t1) == 0)
                {
                    //汇总数据
                    for (int i = 0; i < n; i++)
                    {
                        pinfo = DataType.GetProperty(strArrSumFields[i]);
                        value = pinfo.GetValue(t, null);
                        SumFieldValues[i] += CDbl<object>(value);
                    }
                }
                else
                {
                    //加汇总行
                    t2 = new T();
                    for (int i = 0; i < fn; i++)
                    {
                        pinfo = DataType.GetProperty(strArrFields[i]);
                        value = pinfo.GetValue(t1, null);
                        pinfo.SetValue(t2, value, null);
                    }
                    for (int i = 0; i < n; i++)
                    {
                        pinfo = DataType.GetProperty(strArrSumFields[i]);
                        pinfo.SetValue(t2, Convert.ChangeType(SumFieldValues[i], pinfo.PropertyType), null);
                    }
                    list.Add(t2);
                    t1 = t;
                    //初始化汇总值
                    SumFieldValues = new decimal[n];
                    for (int i = 0; i < n; i++)
                    {
                        pinfo = DataType.GetProperty(strArrSumFields[i]);
                        value = pinfo.GetValue(t, null);
                        SumFieldValues[i] = CDbl<object>(value);
                    }
                }
                list.Add(t);
            }
            //加汇总行
            t2 = new T();
            for (int i = 0; i < fn; i++)
            {
                pinfo = DataType.GetProperty(strArrFields[i]);
                value = pinfo.GetValue(t1, null);
                pinfo.SetValue(t2, value, null);
            }
            for (int i = 0; i < n; i++)
            {
                pinfo = DataType.GetProperty(strArrSumFields[i]);
                pinfo.SetValue(t2, Convert.ChangeType(SumFieldValues[i], pinfo.PropertyType), null);
            }
            list.Add(t2);

            return list;
        }
    }
}
