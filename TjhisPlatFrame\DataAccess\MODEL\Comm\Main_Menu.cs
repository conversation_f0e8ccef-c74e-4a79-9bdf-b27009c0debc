﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using System.ComponentModel;
namespace Model
{

    [DataContract]
    public class MAIN_MENU : NotificationObject
    {

        /// <summary>
        /// NODE
        /// </summary>		
        private string _node;
        [DataMember]
        public string NODE
        {
            get { return _node; }
            set
            {
                if (_node != value)
                {
                    _node = value;
                    this.RaisePropertyChanged("NODE");
                }
            }
        }
        /// <summary>
        /// PARENTNODE
        /// </summary>		
        private string _parentnode;
        [DataMember]
        public string PARENTNODE
        {
            get { return _parentnode; }
            set
            {
                if (_parentnode != value)
                {
                    _parentnode = value;
                    this.RaisePropertyChanged("PARENTNODE");
                }
            }
        }
        /// <summary>
        /// DISPLYTEXT
        /// </summary>		
        private string _displytext;
        [DataMember]
        public string DISPLYTEXT
        {
            get { return _displytext; }
            set
            {
                if (_displytext != value)
                {
                    _displytext = value;
                    this.RaisePropertyChanged("DISPLYTEXT");
                }
            }
        }
        /// <summary>
        /// ICON
        /// </summary>		
        private string _icon;
        [DataMember]
        public string ICON
        {
            get { return _icon; }
            set
            {
                if (_icon != value)
                {
                    _icon = value;
                    this.RaisePropertyChanged("ICON");
                }
            }
        }
        /// <summary>
        /// OPENCLASS
        /// </summary>		
        private string _openclass;
        [DataMember]
        public string OPENCLASS
        {
            get { return _openclass; }
            set
            {
                if (_openclass != value)
                {
                    _openclass = value;
                    this.RaisePropertyChanged("OPENCLASS");
                }
            }
        }
        /// <summary>
        /// TOOLTIP
        /// </summary>		
        private string _tooltip;
        [DataMember]
        public string TOOLTIP
        {
            get { return _tooltip; }
            set
            {
                if (_tooltip != value)
                {
                    _tooltip = value;
                    this.RaisePropertyChanged("TOOLTIP");
                }
            }
        }
        /// <summary>
        /// LARGEICON
        /// </summary>		
        private string _largeicon;
        [DataMember]
        public string LARGEICON
        {
            get { return _largeicon; }
            set
            {
                if (_largeicon != value)
                {
                    _largeicon = value;
                    this.RaisePropertyChanged("LARGEICON");
                }
            }
        }
        /// <summary>
        /// SMALLICON
        /// </summary>		
        private string _smallicon;
        [DataMember]
        public string SMALLICON
        {
            get { return _smallicon; }
            set
            {
                if (_smallicon != value)
                {
                    _smallicon = value;
                    this.RaisePropertyChanged("SMALLICON");
                }
            }
        }
        /// <summary>
        /// STYLE
        /// </summary>		
        private string _style;
        [DataMember]
        public string STYLE
        {
            get { return _style; }
            set
            {
                if (_style != value)
                {
                    _style = value;
                    this.RaisePropertyChanged("STYLE");
                }
            }
        }
        /// <summary>
        /// SORTNO
        /// </summary>		
        private decimal _sortno;
        [DataMember]
        public decimal SORTNO
        {
            get { return _sortno; }
            set
            {
                if (_sortno != value)
                {
                    _sortno = value;
                    this.RaisePropertyChanged("SORTNO");
                }
            }
        }

    }
}