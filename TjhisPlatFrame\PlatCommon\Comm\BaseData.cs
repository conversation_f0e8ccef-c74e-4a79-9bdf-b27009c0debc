﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;

namespace PlatCommon.Comm
{
    //基础数据
    public class BaseData
    {
        public DataSet DrugDictList { get; set; }
        public static DataSet DosageUnitsDictList { get; set; }
        public static DataSet PriceList { get; set; }
        public static DataSet DrugStockList { get; set; }
        public DataSet ChargeTypeDictList { get; set; }
        public static DataSet ChargePriceScheduleList { get; set; }
        public static DataSet DrugDispPropertyDictList { get; set; }
        public DataSet DrugDispensPropertyList { get; set; }
        public DataSet DeptDictList { get; set; }
        public BaseData()
        { }
        public BaseData(string storage_code)
        {
            GetDrugDictList();
            GetDosageUnitsDictList();
            //GetPriceList();
            //GetDrugStockList(storage_code);
            GetChargeTypeDictList();
            //GetChargePriceScheduleList();
            //GetDrugDispPropertyDictList();
            //GetDrugDispensPropertyList();
            GetDeptDictList();
        }
        //取药品字典
        public void GetDrugDictList()
        {
            string sql = "SELECT DRUG_DICT.DRUG_CODE,DRUG_DICT.DRUG_NAME,DRUG_DICT.DRUG_SPEC,DRUG_DICT.UNITS,DRUG_DICT.DRUG_FORM,DRUG_DICT.TOXI_PROPERTY,";
            sql += "DRUG_DICT.DOSE_PER_UNIT,DRUG_DICT.DOSE_UNITS,DRUG_DICT.DRUG_INDICATOR  FROM DRUG_DICT ";
            DrugDictList = new NM_Service.NMService.ServerPublicClient().GetList(sql);

        }
        //取药品单位
        public static void GetDosageUnitsDictList()
        {
            string sql = "SELECT COMM.DOSAGE_UNITS_DICT.DOSAGE_UNITS,COMM.DOSAGE_UNITS_DICT.BASE_UNITS,COMM.DOSAGE_UNITS_DICT.CONVERSION_RATIO FROM COMM.DOSAGE_UNITS_DICT ";
            DosageUnitsDictList = new NM_Service.NMService.ServerPublicClient().GetList(sql);
        }
        //取价表
        public static DataTable GetPriceList(string where)
        {
            string sql = "SELECT CURRENT_PRICE_LIST.ITEM_CODE,CURRENT_PRICE_LIST.ITEM_SPEC,CURRENT_PRICE_LIST.PRICE,CURRENT_PRICE_LIST.PREFER_PRICE,";
            sql += "CURRENT_PRICE_LIST.UNITS,CURRENT_PRICE_LIST.FOREIGNER_PRICE,CURRENT_PRICE_LIST.CLASS_ON_INP_RCPT, CURRENT_PRICE_LIST.CLASS_ON_RECKONING,CURRENT_PRICE_LIST.SUBJ_CODE,CURRENT_PRICE_LIST.CLASS_ON_MR FROM CURRENT_PRICE_LIST WHERE CURRENT_PRICE_LIST.ITEM_CLASS = 'A'";
            if (!string.IsNullOrEmpty(where))
                sql += " and " + where;
            PriceList = new NM_Service.NMService.ServerPublicClient().GetList(sql);
            return PriceList.Tables[0];
        }
        //取库存
        public static DataTable GetDrugStockList(string storage_code, string where)
        {
            string sql = "SELECT  DRUG_STOCK.DRUG_CODE,DRUG_STOCK.DRUG_NAME  DRUG_NAME,DRUG_STOCK.TRADE_NAME TRADE_NAME,DRUG_DICT.DOSE_PER_UNIT,";
            sql += "DRUG_DICT.DOSE_UNITS,0.0000000000  orders_dosage_in_stock_units,DRUG_STOCK.DRUG_SPEC,DRUG_STOCK.UNITS,DRUG_STOCK.FIRM_ID,";
            sql += "DRUG_STOCK.PACKAGE_SPEC,DRUG_STOCK.PACKAGE_UNITS,DRUG_STOCK.BATCH_NO,DRUG_STOCK.QUANTITY,DRUG_STOCK.BATCH_CODE,DRUG_STOCK.EXPIRE_DATE,";
            sql += "DRUG_STOCK.PURCHASE_PRICE,DRUG_STOCK.DISCOUNT,DRUG_STOCK.TRADE_PRICE,DRUG_STOCK.RETAIL_PRICE,DRUG_STOCK.SUPPLIER,DRUG_STOCK.SUB_STORAGE,";
            sql += "DRUG_STOCK.LOCATION_CODE,(select max(amount_per_package) from drug_price_list temp where  temp.drug_code =drug_stock.drug_code and ";
            sql += " temp.min_spec=drug_stock.drug_spec and    temp.drug_spec=drug_stock.package_spec and ";
            sql += "temp.firm_id = drug_stock.firm_id and    temp.START_DATE <= sysdate and  (temp.STOP_DATE >= sysdate OR temp.STOP_DATE is null )  ";
            sql += " )  amount_per_package FROM DRUG_STOCK,DRUG_DICT WHERE ( DRUG_STOCK.DRUG_CODE = DRUG_DICT.DRUG_CODE ) and  ( DRUG_DICT.DRUG_SPEC = DRUG_STOCK.DRUG_SPEC ) and  ";
            sql += " ( ( DRUG_STOCK.STORAGE = '" + storage_code + "' ) AND ( DRUG_STOCK.SUPPLY_INDICATOR = 1 ) )  AND ( DRUG_STOCK.QUANTITY > 0 )";
            if (!string.IsNullOrEmpty(where))
                sql += " and " + where;
            DrugStockList = new NM_Service.NMService.ServerPublicClient().GetList(sql);
            return DrugStockList.Tables[0];
        }
        //取费别字典
        public void GetChargeTypeDictList()
        {
            string sql = "SELECT CHARGE_TYPE_DICT.CHARGE_TYPE_NAME,CHARGE_TYPE_DICT.CHARGE_PRICE_INDICATOR FROM CHARGE_TYPE_DICT";
            ChargeTypeDictList = new NM_Service.NMService.ServerPublicClient().GetList(sql);
        }
        //取收费系数字典
        public static DataTable GetChargePriceScheduleList(string where)
        {
            string sql = "SELECT CHARGE_PRICE_SCHEDULE.CHARGE_TYPE,CHARGE_PRICE_SCHEDULE.PRICE_COEFF_NUMERATOR,CHARGE_PRICE_SCHEDULE.PRICE_COEFF_DENOMINATOR,";
            sql += " CHARGE_PRICE_SCHEDULE.CHARGE_SPECIAL_INDICATOR FROM CHARGE_PRICE_SCHEDULE";
            if (!string.IsNullOrEmpty(where))
                sql += " where " + where;
            ChargePriceScheduleList = new NM_Service.NMService.ServerPublicClient().GetList(sql);
            return ChargePriceScheduleList.Tables[0];
        }
        //取药品医嘱摆药类别字典
        public static DataTable GetDrugDispPropertyDictList(string where)
        {
            string sql = "SELECT DRUG_DISP_PROPERTY_DICT.DISPENSING_PROPERTY,DRUG_DISP_PROPERTY_DICT.DRUG_ADMINISTRATIONS FROM DRUG_DISP_PROPERTY_DICT ";
            sql += " where DRUG_DISP_PROPERTY_DICT.DISPENSING_PROPERTY = '" + where + "'";
            DrugDispPropertyDictList = new NM_Service.NMService.ServerPublicClient().GetList(sql);
            return DrugDispPropertyDictList.Tables[0];
        }
        //取药品摆药分类定义
        public static DataTable GetDrugDispensPropertyList(string where)
        {
            string sql = "SELECT PHARMACY.DRUG_DISPENS_PROPERTY.DISPENSARY,PHARMACY.DRUG_DISPENS_PROPERTY.DRUG_CODE,PHARMACY.DRUG_DISPENS_PROPERTY.DISPENSING_PROPERTY,";
            sql += "nvl(PHARMACY.DRUG_DISPENS_PROPERTY.VIRTUAL_CABINET,0) VIRTUAL_CABINET,PHARMACY.DRUG_DISPENS_PROPERTY.DRUG_SPEC,nvl(PHARMACY.DRUG_DISPENS_PROPERTY.DISPENSING_CUMULATE,3) DISPENSING_CUMULATE, ";
            sql += "nvl(PHARMACY.DRUG_DISPENS_PROPERTY.SEPARABLE,0) SEPARABLE FROM PHARMACY.DRUG_DISPENS_PROPERTY   ";
            if (!string.IsNullOrEmpty(where))
                sql += where;
            //DrugDispensPropertyList =  new NM_Service.NMService.ServerPublicClient().GetList(sql);
            return new NM_Service.NMService.ServerPublicClient().GetList(sql).Tables[0];
        }
        //取科室代码
        public void GetDeptDictList()
        {
            string sql = "SELECT COMM.DEPT_DICT.DEPT_NAME,COMM.DEPT_DICT.DEPT_CODE,COMM.DEPT_DICT.VIRTUAL_CABINET,COMM.DEPT_DICT.DISPENSING_CUMULATE FROM COMM.DEPT_DICT";
            DeptDictList = new NM_Service.NMService.ServerPublicClient().GetList(sql);
        }
        //
    }
}
