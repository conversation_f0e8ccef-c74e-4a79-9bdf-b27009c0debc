﻿using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using Utility;
using Utility.OracleODP;
using Oracle.ManagedDataAccess.Client;

namespace OracleDAL
{

    public class MODEL_GROUP_Dao_Base
    {
        #region   Method
        public bool Exists(string MODEL_CODE, string GRO<PERSON>_CODE, OracleBaseClass db)
        {
            #region  init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from MODEL_GROUP");
            strSql.Append(" where ");
            strSql.Append(" MODEL_CODE = :MODEL_CODE and  ");
            strSql.Append(" GROUP_CODE = :GROUP_CODE ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":MODEL_CODE", OracleDbType.Varchar2, 4);
            p.Value = MODEL_CODE;
            parameters.Add(p);

            p = new OracleParameter(":GROUP_CODE", OracleDbType.Varchar2, 4);
            p.Value = GROUP_CODE;
            parameters.Add(p);

            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    int cmdresult;
                    cmdresult = int.Parse(ds.Tables[0].Rows[0][0].ToString());
                    if (cmdresult <= 0)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                    return false;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.MODEL_GROUP model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into MODEL_GROUP(");
            strSql.Append("MODEL_CODE,GROUP_CODE,GROUP_NAME,GROUP_SORT_NO,ICON");
            strSql.Append(") values (");
            strSql.Append(":MODEL_CODE,:GROUP_CODE,:GROUP_NAME,:GROUP_SORT_NO,:ICON");
            strSql.Append(") ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":MODEL_CODE", OracleDbType.Varchar2, 4);
            p.Value = model.MODEL_CODE;
            parameters.Add(p);

            p = new OracleParameter(":GROUP_CODE", OracleDbType.Varchar2, 4);
            p.Value = model.GROUP_CODE;
            parameters.Add(p);

            p = new OracleParameter(":GROUP_NAME", OracleDbType.Varchar2, 50);
            p.Value = model.GROUP_NAME;
            parameters.Add(p);

            p = new OracleParameter(":GROUP_SORT_NO", OracleDbType.Decimal, 3);
            p.Value = model.GROUP_SORT_NO;
            parameters.Add(p);

            p = new OracleParameter(":ICON", OracleDbType.Varchar2, 50);
            p.Value = model.ICON;
            parameters.Add(p);
            #endregion
            try
            {

                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.MODEL_GROUP model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update MODEL_GROUP set ");

            strSql.Append(" MODEL_CODE = :MODEL_CODE , ");
            strSql.Append(" GROUP_CODE = :GROUP_CODE , ");
            strSql.Append(" GROUP_NAME = :GROUP_NAME , ");
            strSql.Append(" GROUP_SORT_NO = :GROUP_SORT_NO , ");
            strSql.Append(" ICON = :ICON  ");
            strSql.Append(" where MODEL_CODE=:MODEL_CODE and GROUP_CODE=:GROUP_CODE  ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":MODEL_CODE", OracleDbType.Varchar2, 4);
            p.Value = model.MODEL_CODE;
            parameters.Add(p);

            p = new OracleParameter(":GROUP_CODE", OracleDbType.Varchar2, 4);
            p.Value = model.GROUP_CODE;
            parameters.Add(p);

            p = new OracleParameter(":GROUP_NAME", OracleDbType.Varchar2, 50);
            p.Value = model.GROUP_NAME;
            parameters.Add(p);

            p = new OracleParameter(":GROUP_SORT_NO", OracleDbType.Decimal, 3);
            p.Value = model.GROUP_SORT_NO;
            parameters.Add(p);

            p = new OracleParameter(":ICON", OracleDbType.Varchar2, 50);
            p.Value = model.ICON;
            parameters.Add(p);
            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string MODEL_CODE, string GROUP_CODE, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from MODEL_GROUP ");
            strSql.Append(" where MODEL_CODE=:MODEL_CODE and GROUP_CODE=:GROUP_CODE ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":MODEL_CODE", OracleDbType.Varchar2, 4);
            p.Value = MODEL_CODE;
            parameters.Add(p);

            p = new OracleParameter(":GROUP_CODE", OracleDbType.Varchar2, 4);
            p.Value = GROUP_CODE;
            parameters.Add(p);

            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }



        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.MODEL_GROUP GetModel(string MODEL_CODE, string GROUP_CODE, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select MODEL_CODE, GROUP_CODE, GROUP_NAME, GROUP_SORT_NO, ICON  ");
            strSql.Append("  from MODEL_GROUP ");
            strSql.Append(" where MODEL_CODE=:MODEL_CODE and GROUP_CODE=:GROUP_CODE ");
            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":MODEL_CODE", OracleDbType.Varchar2, 4);
            p.Value = MODEL_CODE;
            parameters.Add(p);

            p = new OracleParameter(":GROUP_CODE", OracleDbType.Varchar2, 4);
            p.Value = GROUP_CODE;
            parameters.Add(p);
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;
                    Model.MODEL_GROUP model = new Model.MODEL_GROUP();

                    if (cmdresult > 0)
                    {
                        model = CopyToModel(ds.Tables[0].Rows[0]);
                        return model;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM MODEL_GROUP ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得几行数据
        /// </summary>
        public DataSet GetList(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            #region 初始化参数
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM MODEL_GROUP T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.MODEL_GROUP> GetObservableCollection(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM MODEL_GROUP ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.MODEL_GROUP> list = new System.Collections.ObjectModel.ObservableCollection<Model.MODEL_GROUP>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.MODEL_GROUP model = new Model.MODEL_GROUP();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表 
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.MODEL_GROUP> GetObservableCollection(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM MODEL_GROUP T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }

            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.MODEL_GROUP> list = new System.Collections.ObjectModel.ObservableCollection<Model.MODEL_GROUP>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.MODEL_GROUP model = new Model.MODEL_GROUP();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion   Method
        #region
        /// <summary>
        /// 
        /// </summary>
        protected Model.MODEL_GROUP CopyToModel(DataRow dRow)
        {
            Model.MODEL_GROUP model1 = new Model.MODEL_GROUP();

            if (dRow["MODEL_CODE"] != null && dRow["MODEL_CODE"].ToString() != "")
            {
                model1.MODEL_CODE = dRow["MODEL_CODE"].ToString();
            }

            if (dRow["GROUP_CODE"] != null && dRow["GROUP_CODE"].ToString() != "")
            {
                model1.GROUP_CODE = dRow["GROUP_CODE"].ToString();
            }

            if (dRow["GROUP_NAME"] != null && dRow["GROUP_NAME"].ToString() != "")
            {
                model1.GROUP_NAME = dRow["GROUP_NAME"].ToString();
            }

            if (dRow["GROUP_SORT_NO"] != null && dRow["GROUP_SORT_NO"].ToString() != "")
            {
                model1.GROUP_SORT_NO = decimal.Parse(dRow["GROUP_SORT_NO"].ToString());
            }

            if (dRow["ICON"] != null && dRow["ICON"].ToString() != "")
            {
                model1.ICON = dRow["ICON"].ToString();
            }

            return model1;
        }
        #endregion

    }
}

