﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Media.Imaging;

namespace PlatPublic.Common
{
    public static class ImageHelper
    {
        /// <summary>
        /// 图片转换byte数组
        /// </summary>
        /// <param name="imageFile"></param>
        /// <param name="fmat">指定图片保存格式</param>
        /// <returns></returns>
        public static byte[] ImageToBytes(String imageFile, System.Drawing.Imaging.ImageFormat fmat)
        {
            System.Drawing.Image img = System.Drawing.Image.FromFile(imageFile);
            System.IO.MemoryStream ms = new System.IO.MemoryStream();
            img.Save(ms, fmat);
            byte[] data;
            data = ms.ToArray();
            return data;
        }
        /// <summary>
        /// 取新尺寸大小的图片
        /// </summary>
        /// <param name="b">原图</param>
        /// <param name="destHeight">新高</param>
        /// <param name="destWidth">新宽</param>
        /// <returns></returns>
        public static Bitmap GetThumbnail(Bitmap b, int destHeight, int destWidth)
        {
            Image imgSource = b;
            ImageFormat thisFormat = imgSource.RawFormat;
            int sW = 0, sH = 0;
            // 按比例缩放           
            int sWidth = imgSource.Width;
            int sHeight = imgSource.Height;
            if (sHeight > destHeight || sWidth > destWidth)
            {
                if ((sWidth * destHeight) > (sHeight * destWidth))
                {
                    sW = destWidth;
                    sH = (destWidth * sHeight) / sWidth;
                }
                else
                {
                    sH = destHeight;
                    sW = (sWidth * destHeight) / sHeight;
                }
            }
            else
            {
                sW = sWidth;
                sH = sHeight;
            }
            Bitmap outBmp = new Bitmap(destWidth, destHeight);
            Graphics g = Graphics.FromImage(outBmp);
            g.Clear(Color.White);
            // 设置画布的描绘质量         
            g.CompositingQuality = CompositingQuality.HighQuality;
            g.SmoothingMode = SmoothingMode.HighQuality;
            g.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g.DrawImage(imgSource, new Rectangle((destWidth - sW) / 2, (destHeight - sH) / 2, sW, sH), 0, 0, imgSource.Width, imgSource.Height, GraphicsUnit.Pixel);
            g.Dispose();
            // 以下代码为保存图片时，设置压缩质量     
            EncoderParameters encoderParams = new EncoderParameters();
            long[] quality = new long[1];
            quality[0] = 100;
            EncoderParameter encoderParam = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, quality);
            encoderParams.Param[0] = encoderParam;
            //if (!string.IsNullOrEmpty(save_path))
            //{
            //    outBmp.Save(save_path);
            //    encoderParam.Dispose();
            //    encoderParam = null;
            //}
            imgSource.Dispose();
            return outBmp;
        }

        /// <summary>
        /// 转换icon图片
        /// </summary>
        /// <param name="obm"></param>
        /// <param name="preserve"></param>
        /// <param name="ICON_W"></param>
        /// <param name="ICON_H"></param>
        /// <returns></returns>
        static public Icon BitmapToIcon(Bitmap obm, bool preserve, int ICON_W, int ICON_H)
        {
            Bitmap bm;
            // if not preserving aspect
            if (!preserve)        // if not preserving aspect
                bm = new Bitmap(obm, ICON_W, ICON_H);  //   rescale from original bitmap

            // if preserving aspect drop excess significance in least significant direction
            else          // if preserving aspect
            {
                Rectangle rc = new Rectangle(0, 0, ICON_W, ICON_H);
                if (obm.Width >= obm.Height)   //   if width least significant
                {          //     rescale with width based on max icon height
                    bm = new Bitmap(obm, (ICON_H * obm.Width) / obm.Height, ICON_H);
                    rc.X = (bm.Width - ICON_W) / 2;  //     chop off excess width significance
                    if (rc.X < 0) rc.X = 0;
                }
                else         //   if height least significant
                {          //     rescale with height based on max icon width
                    bm = new Bitmap(obm, ICON_W, (ICON_W * obm.Height) / obm.Width);
                    rc.Y = (bm.Height - ICON_H) / 2; //     chop off excess height significance
                    if (rc.Y < 0) rc.Y = 0;
                }
                bm = bm.Clone(rc, bm.PixelFormat);  //   bitmap for icon rectangle
            }

            // create icon from bitmap
            Icon icon = Icon.FromHandle(bm.GetHicon()); // create icon from bitmap
            bm.Dispose();        // dispose of bitmap
            return icon;        // return icon
        }

        /// <summary>
        /// 从字节转换为图像
        /// </summary>
        /// <param name="bitmapData">图像的字节数组</param>
        /// <returns>图象</returns>
        public static Image CreateImage(Byte[] bitmapData)
        {
            MemoryStream streamBitmap = new MemoryStream(bitmapData);
            Image bitImage = System.Drawing.Image.FromStream(streamBitmap);
            return bitImage;
        }
    }
}
