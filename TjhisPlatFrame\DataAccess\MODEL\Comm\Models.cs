﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using System.ComponentModel;
namespace Model
{

    [DataContract]
    public class MODELS : NotificationObject
    {

        /// <summary>
        /// CODE
        /// </summary>		
        private string _code;
        [DataMember]
        public string CODE
        {
            get { return _code; }
            set
            {
                if (_code != value)
                {
                    _code = value;
                    this.RaisePropertyChanged("CODE");
                }
            }
        }
        /// <summary>
        /// NAME
        /// </summary>		
        private string _name;
        [DataMember]
        public string NAME
        {
            get { return _name; }
            set
            {
                if (_name != value)
                {
                    _name = value;
                    this.RaisePropertyChanged("NAME");
                }
            }
        }
        /// <summary>
        /// SORT_NO
        /// </summary>		
        private decimal _sort_no;
        [DataMember]
        public decimal SORT_NO
        {
            get { return _sort_no; }
            set
            {
                if (_sort_no != value)
                {
                    _sort_no = value;
                    this.RaisePropertyChanged("SORT_NO");
                }
            }
        }
        /// <summary>
        /// KEY
        /// </summary>		
        private string _key;
        [DataMember]
        public string KEY
        {
            get { return _key; }
            set
            {
                if (_key != value)
                {
                    _key = value;
                    this.RaisePropertyChanged("KEY");
                }
            }
        }
        /// <summary>
        /// LAST_USED_KEY
        /// </summary>		
        private string _last_used_key;
        [DataMember]
        public string LAST_USED_KEY
        {
            get { return _last_used_key; }
            set
            {
                if (_last_used_key != value)
                {
                    _last_used_key = value;
                    this.RaisePropertyChanged("LAST_USED_KEY");
                }
            }
        }

    }
}