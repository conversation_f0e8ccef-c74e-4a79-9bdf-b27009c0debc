﻿//**********************************************
//说明:临床路径患者信息
//计算机名称：LINDP
//创建日期：2017/3/10 13:46:29
//作者：林大鹏
//版本号：V1.00
//**********************************************

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace PlatCommon.Comm
{
    public class PatientCPath
    {
        #region 属性定义
        //患者id
        private string patientID;
        public string PatientID 
        { 
            set{patientID=value;}
            get{return patientID;} 
        } 
        //住院次数
        private string visitID;
        public string VisitID 
        { 
            set{visitID=value;}
            get{return visitID;} 
        }
        //模板号
        private string templeteID;
        public string TempleteID 
        { 
            set{templeteID=value;}
            get{return templeteID;} 
        }
        //模板名称
        private string templeteName;
        public string TempleteName 
        { 
            set{templeteName=value;}
            get{return templeteName;} 
        }
        //进入路径日期
        private string enterPathDate;
        public string EnterPathDate 
        { 
            set{enterPathDate=value;}
            get{return enterPathDate;} 
        }  
        //入径第几天
        private string enterPathDay;
        public string EnterPathDay 
        { 
            set{enterPathDay=value;}
            get{return enterPathDay;} 
        }  
        //出径日期
        private string endPathDATE;
        public string EndPathDATE 
        { 
            set{endPathDATE=value;}
            get{return endPathDATE;} 
        } 
        //入路径时所在科室
        private string deptCode;
        public string DeptCode 
        { 
            set{deptCode=value;}
            get{return deptCode;} 
        } 
        //入路径时所在科室名称
        private string deptName;
        public string DeptName 
        { 
            set{deptName=value;}
            get{return deptName;} 
        } 
        //0-在路径；1--正常结束路径；2--退出结束路径
        private string status;
        public string Status 
        { 
            set{status=value;}
            get{return status;} 
        }
        //疾病代码
        private string diagCode;
        public string DiagCode 
        { 
            set{diagCode=value;}
            get{return diagCode;} 
        }
        //疾病名称
        private string diagName;
        public string DiagName 
        { 
            set{diagName=value;}
            get{return diagName;} 
        }
        //最后一次执行日期
        private string lastDate;
        public string LastDate 
        { 
            set{lastDate=value;}
            get{return lastDate;} 
        }
        //审核后状态
        private string auditStatus;
        public string AuditStatus 
        {
            set { auditStatus = value; }
            get { return auditStatus; }
        }
        //住院日的参考下限
        private string templeteInpDays1;
        public string TempleteInpDays1 
        {
            set { templeteInpDays1 = value; }
            get { return templeteInpDays1; }
        }
        //住院日的参考上限
        private string templeteInpDays2;
        public string TempleteInpDays2
        {
            set { templeteInpDays2 = value; }
            get { return templeteInpDays2; }
        }
        //服务器当前日期
        private string systemDate;
        public string SystemDate
        {
            set { systemDate = value; }
            get { return systemDate; }
        }
        #endregion

    }
}
