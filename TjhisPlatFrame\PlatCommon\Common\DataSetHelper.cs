﻿/*********************************************
* 文 件 名：Cs02DataSetHelper
* 类 名 称：Cs02DataSetHelper
* 功能说明：DataSet操作类
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：付军
* 创建时间：2016-10-28
* 版 本 号：1.0.0.1
* 修改时间：尹志伟
* 修 改 人：2018-06-12
* CLR 版本：4.0.30319.42000
/*********************************************/

using System;
using System.Data;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Xml;
using System.Text.RegularExpressions;
using System.Reflection;
using System.Security.Cryptography;
using PlatCommon.Base01;
using PlatCommon.Base02;

namespace PlatCommon.Common
{
    /// <summary>
    /// DataSet操作类
    /// </summary>	
    public class DataSetHelper
    {
        IDbConnection _connection = null; // 数据库连接对象
        /// <summary>
        /// 是否与数据库连接
        /// </summary>
        public bool IsConnected
        {
            get
            {
                return (_connection != null
                     && _connection.State != ConnectionState.Broken
                     && _connection.State != ConnectionState.Closed
                     && _connection.State != ConnectionState.Connecting);
            }
        }
        /// <summary>
		/// 构造函数
		/// </summary>
        private DataSetHelper()
		{
        }


        /// <summary>
        /// 数据集是否具有记录
        /// </summary>
        /// <param name="dsData">数据集</param>
        /// <returns></returns>
        public static bool HasRecord(DataSet dsData)
        {
            return (dsData != null && dsData.Tables.Count > 0 && dsData.Tables[0].Rows.Count > 0);
        }


        /// <summary>
        /// 将行转换为列
        /// </summary>
        /// <param name="strColName">列名称</param>
        /// <param name="strColValue">列值</param>
        /// <param name="dtSrc">源DataTable</param>
        /// <returns>转换后的Datatable</returns>
        public static DataTable ConvertRowToCol(DataTable dtSrc, string strColName, string strColValue)
        {
            // 创建表
            DataTable dtDest = new DataTable(dtSrc.TableName);

            // 创建列
            foreach (DataRow drSrc in dtSrc.Rows)
            {
                string colNameValue = drSrc[strColName].ToString();
                dtDest.Columns.Add(colNameValue);
            }

            // 添加数据
            DataRow drEdit = dtDest.NewRow();

            foreach (DataRow dr in dtSrc.Rows)
            {
                string colNameValue = dr[strColName].ToString();
                drEdit[colNameValue] = dr[strColValue].ToString();
            }

            dtDest.Rows.Add(drEdit);

            return dtDest;
        }


        /// <summary>
        /// XML文件，转成DATASET 
        /// </summary>
        /// <param name="xmlFile">要转换的XML文件的文件名</param>
        /// <returns></returns>
        public static DataSet GetDataSetFromFile(string xmlFile)
        {
            StringReader stream = null;
            XmlTextReader reader = null;

            try
            {
                XmlDocument xmld = new XmlDocument();
                xmld.Load(xmlFile);
                
                DataSet xmlDS = new DataSet();
                stream = new StringReader(xmld.InnerXml);
                
                reader = new XmlTextReader(stream);
                xmlDS.ReadXml(reader);
                
                return xmlDS;
            }
            finally
            {
                if (reader != null)
                {
                    reader.Close();
                }
            }
        }


        /// <summary>
        /// 将DataSet 保存到文件中, 如果目录不存在，创建目录
        /// </summary>
        /// <param name="ds">要保存的DataSet</param>
        /// <param name="xmlFile">指定文件名</param>
        public static void DataSetToXmlFile(DataSet ds, string xmlFile)
        {
            string pathStr = Path.GetDirectoryName(xmlFile);
            
            if (!Directory.Exists(pathStr))
                Directory.CreateDirectory(pathStr);

            //每次重新生成 临时temp.xml
            if (File.Exists(xmlFile))
            {
                File.Delete(xmlFile);
            }

            ds.WriteXml(xmlFile, XmlWriteMode.WriteSchema);
        }


        /// <summary>
        /// 将DataSet 保存到文件中
       /// </summary>
        /// <param name="ds">要保存的DataSet</param>
        public static void DataSetToXmlFile(DataSet ds)
        { 
            string dsName = string.IsNullOrEmpty(ds.DataSetName) ? "ds" : ds.DataSetName;
            string xmlFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, dsName + ".xml");
             
            //每次重新生成 临时temp.xml
            if (File.Exists(xmlFile))
            {
                File.Delete(xmlFile);
            }

            ds.WriteXml(xmlFile, XmlWriteMode.WriteSchema);

        }
        

        /// <summary>
        /// 判断是否包含特定名称的列
        /// </summary>
        /// <param name="name">特定名称</param>
        /// <param name="dcc">DataColumnCollection</param>
        /// <returns></returns>
        public static bool IsInColumns(string name, DataColumnCollection dcc)
        {
            foreach (DataColumn dc in dcc)
            {
                if (dc.ColumnName.ToLower() == name.ToLower())
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 根据过滤条件得到数据集中指定列的最大值
        /// </summary>
        /// <param name="ds">数据集</param>
        /// <param name="filter">过滤条件</param>
        /// <param name="colName">指定列</param>
        /// <param name="nullVaue">找不到时的缺省值</param>
        /// <returns></returns>
        public static int GetMaxValue(DataSet ds, string filter, string colName, int nullVaue)
        {
            if (DataSetHelper.HasRecord(ds) == false) return nullVaue;

            if (ds.Tables[0].Columns.Contains(colName) == false) return nullVaue;

            int nValue = 0;
            DataRow[] drFind = ds.Tables[0].Select(filter, colName + " DESC");
            if (drFind.Length > 0)
            {
                if (int.TryParse(drFind[0][colName].ToString(), out nValue)) return nValue;
            }

            return nullVaue;
        }

        

        /// <summary>
        /// 创建字典类dataset
        /// </summary>
        /// <returns>创建的DataSet</returns>
        public static DataSet CreateDict()
        {
            DataSet dsResult = new DataSet();
            DataTable dtData = dsResult.Tables.Add("DICT_ITEM");

            dtData.Columns.Add("ITEM_NAME", typeof(String));
            dtData.Columns.Add("ITEM_CODE", typeof(String));
            dtData.Columns.Add("SHOW_ORDER", typeof(System.Int32));

            return dsResult;
        }

        /// <summary>
        /// 往字典类dataset中添加一条数据
        /// </summary>
        /// <param name="dsData">数据集</param>
        /// <param name="strItemName">项目名称</param>
        /// <param name="strItemCode">项目代码</param>
        /// <param name="iShowOrder">显示序号</param>
        public static void AddDictItem(ref DataSet dsData, string strItemName, string strItemCode, int iShowOrder)
        {
            DataRow drNew = dsData.Tables[0].NewRow();

            drNew["ITEM_NAME"] = strItemName;
            drNew["ITEM_CODE"] = strItemCode;
            drNew["SHOW_ORDER"] = iShowOrder;

            dsData.Tables[0].Rows.Add(drNew);
        }

        
        /// <summary>
        /// 复制DataRow
        /// </summary>
        /// <returns></returns>
        public static bool CopyDataRow(ref DataRow drDest, ref DataRow drSrc)
        {
            bool blnAllGet = true;

            DataTable dtDest = drDest.Table;
            DataTable dtSrc = drSrc.Table;

            foreach (DataColumn dc in dtSrc.Columns)
            {
                if (dtSrc.Columns.Contains(dc.ColumnName))
                {
                    try
                    {
                        drDest[dc.ColumnName] = drSrc[dc.ColumnName];
                    }
                    catch
                    {
                        blnAllGet = false;
                    }
                }
            }

            return blnAllGet;
        }

        /// <summary>
        /// 获取DataTable中指定列不重复的记录
        /// </summary>
        /// <param name="dtSource">需传入的数据源DataTable</param>
        /// <param name="strArrColumns">例：columns = { "DEPT_CODE", "DEPT_NAME" };  //DataTable中不重复的科室代码，科室名称</param>
        /// <returns></returns>
        public static DataTable GetDistinctDataTable(DataTable dtSource, string[] strArrColumns)
        {
            if (dtSource == null || dtSource.Rows.Count == 0) return null;
            DataView dv = new DataView(dtSource);

            DataTable dt = dv.ToTable(true, strArrColumns);
            return dt;
        }

        /// <summary>
        /// 检查DataTable中指定列是否有重复记录
        /// </summary>
        /// <param name="dtSource">需检查的DataTable</param>
        /// <param name="strArrColumns">指定检查列</param>
        /// <returns>True 有重复记录，False 无重复记录</returns>
        public static bool CheckDistinctDataTable(DataTable dtSource, string[] strArrColumns)
        {
            if (dtSource == null || dtSource.Rows.Count == 0) return false;
            DataTable dtCopy = dtSource.Copy();
            dtCopy.AcceptChanges();         //去掉删除标记行
            if (dtCopy == null || dtCopy.Rows.Count == 0) return false;
            DataView dv = new DataView(dtCopy);
            DataTable dt = dv.ToTable(true, strArrColumns);
            if (dt == null) return false;
            if (dt != null && dt.Rows.Count != dtCopy.Rows.Count)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 数据集是否具有记录
        /// </summary>
        /// <param name="dtData">数据表</param>
        /// <returns></returns>
        public static bool HasRecord(DataTable dtData)
        {
            return (dtData != null && dtData.Rows.Count > 0);
        }

        /// <summary>
        /// 获取列最大值
        /// </summary>
        /// <param name="dsData">数据集</param>
        /// <param name="strFilter">过滤串</param>
        /// <param name="strColName">列名</param>
        /// <param name="strNullValue">Null值</param>
        /// <returns></returns>
        public static string GetMaxValue(DataSet dsData, string strFilter, string strColName, string strNullValue)
        {
            if (HasRecord(dsData) == false) return strNullValue;

            return dsData.Tables[0].Compute("MAX(" + strColName + ")", strFilter).ToString();
        }

        /// <summary>
        /// 获取最小值
        /// </summary>
        /// <param name="dsData">数据集</param>
        /// <param name="strFilter">过滤串</param>
        /// <param name="strColName">列名</param>
        /// <param name="strNullValue">Null值</param>
        /// <returns></returns>
        public static string GetMinValue(DataSet dsData, string strFilter, string strColName, string strNullValue)
        {
            if (HasRecord(dsData) == false) return strNullValue;

            return dsData.Tables[0].Compute("MIN(" + strColName + ")", strFilter).ToString();
        }

        /// <summary>
        /// 获取值
        /// </summary>
        /// <param name="dsData">数据集</param>
        /// <param name="strFilter">过滤串</param>
        /// <param name="strColName">列名</param>
        /// <param name="strResult">返回查找值</param>
        public static bool GetValue(DataSet dsData, string strFilter, string strColName, ref string strResult)
        {
            if (HasRecord(dsData) == false) return false;

            DataRow[] drArrFind = dsData.Tables[0].Select(strFilter);
            if (drArrFind.Length == 0) return false;

            if (dsData.Tables[0].Columns.Contains(strColName) == false) return false;

            strResult = drArrFind[0][strColName].ToString();
            return true;
        }
      

        /// <summary>
        /// 生成字典DataSet
        /// </summary>
        /// <param name="dictItems"></param>
        /// <returns></returns>
        public static DataSet CreateDict(string[,] dictItems)
        {
            // 创建表结构
            DataSet dsResult = CreateDict();

            // 添加记录
            for (int i = 0; i < dictItems.GetLength(0); i++)
            {
                DataRow drNew = dsResult.Tables[0].NewRow();
                drNew["ITEM_CODE"] = dictItems[i, 0].Trim();
                drNew["ITEM_NAME"] = dictItems[i, 1].Trim();
                drNew["SHOW_ORDER"] = i;

                dsResult.Tables[0].Rows.Add(drNew);
            }

            return dsResult;
        }

        /// <summary>
        /// 添加新行
        /// </summary>
        /// <param name="dtData">数据表</param>
        /// <param name="strArrColNames">列名数组</param>
        /// <param name="strArrValues">值数组</param>
        public static void AddNewRow(DataTable dtData, string[] strArrColNames, string[] strArrValues)
        {
            DataRow drNew = dtData.NewRow();

            if (strArrColNames != null)
            {
                for (int i = 0; i < strArrColNames.Length; i++)
                {
                    drNew[strArrColNames[i]] = strArrValues[i];
                }
            }
            else
            {
                foreach (DataColumn dc in dtData.Columns)
                {
                    if (dc.AllowDBNull == false) dc.AllowDBNull = true;
                }
            }

            dtData.Rows.Add(drNew);
        }

        /// <summary>
        /// 添加新行
        /// </summary>
        /// <param name="dsData">数据集</param>
        /// <param name="strArrColNames">列名数组</param>
        /// <param name="strArrValues">值数组</param>
        public static void AddNewRow(DataSet dsData, string[] strArrColNames, string[] strArrValues)
        {
            AddNewRow(dsData.Tables[0], strArrColNames, strArrValues);
        }

        

        /// <summary>
        /// 获取某张表中某个字段值的列表
        /// </summary>
        /// <param name="dtData">数据表</param>
        /// <param name="strColName">列名</param>
        /// <param name="strFilter">过滤串</param>
        /// <returns></returns>
        public static ArrayList GetItems(DataTable dtData, string strColName, string strFilter)
        {
            ArrayList arr = new ArrayList();
            string strColValue = string.Empty;

            DataRow[] drArrFind = dtData.Select(strFilter, strColName);
            for (int i = 0; i < drArrFind.Length; i++)
            {
                if (i == 0)
                {
                    if (drArrFind[i][strColName] != null)
                    {
                        strColValue = drArrFind[i][strColName].ToString();
                        arr.Add(strColValue);
                    }
                    continue;
                }
                string temp = string.Empty;
                if(drArrFind[i][strColName] != null)
                {
                    temp = drArrFind[i][strColName].ToString();
                }

                if (strColValue.Equals(temp) == false)
                {
                    strColValue = temp;
                    arr.Add(strColValue);
                }
            }

            return arr;
        }

        /// <summary>
        /// 获取名称字符串, 多值之间用","分隔
        /// </summary>
        /// <param name="dsData">数据集</param>
        /// <param name="strCodeList">名称字符串</param>
        /// <param name="strColName">名称列</param>
        /// <param name="strColCode">代码列</param>
        /// <returns></returns>
        public static string GetNameList(DataSet dsData, string strCodeList, string strColName, string strColCode)
        {
            if (HasRecord(dsData) == false) return string.Empty;
            if (dsData.Tables[0].Columns.Contains(strColName) == false) return string.Empty;
            if (dsData.Tables[0].Columns.Contains(strColCode) == false) return string.Empty;

            string[] arrCode = strCodeList.Split(',');
            string result = string.Empty;

            DataRow[] drFind = null;
            for (int i = 0; i < arrCode.Length; i++)
            {
                string strCode = arrCode[i].Trim();
                if (string.IsNullOrEmpty(strCode)) continue;

                drFind = dsData.Tables[0].Select(strColCode + " = " + Cs02StringHelper.SqlConvert(strCode));
                if (drFind.Length == 0) continue;

                if (result.Length > 0) result += ",";
                result += drFind[0][strColName].ToString().Trim();
            }

            return result;
        }

        /// <summary>
        /// 表转换为实体对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dtData">数据表</param>
        /// <returns></returns>
        public static List<T> ConvertDataTableToClass<T>(DataTable dtData)
        {
            List<T> data = new List<T>();
            foreach (DataRow row in dtData.Rows)
            {
                T item = GetItem<T>(row);
                data.Add(item);
            }
            return data;
        }

        /// <summary>
        /// 数据行转换为实体对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="drData">数据行</param>
        /// <returns></returns>
        public static T GetItem<T>(DataRow drData)
        {
            Type temp = typeof(T);
            T obj = Activator.CreateInstance<T>();

            foreach (DataColumn column in drData.Table.Columns)
            {
                foreach (PropertyInfo pro in temp.GetProperties())
                {
                    if (pro.Name == column.ColumnName)
                        pro.SetValue(obj, drData[column.ColumnName], null);
                    else
                        continue;
                }
            }
            return obj;
        }

        /// <summary>
        /// 删除DataTable中的空白行
        /// </summary>
        /// <param name="dtData">数据表</param>
        public static void DeleteEmptyRow(DataTable dtData)
        {
            for (int i = dtData.Rows.Count - 1; i >= 0; i--)
            {
                // 判断是否是空行
                bool blnEmptyRow = true;
                for (int j = 0; j < dtData.Columns.Count; j++)
                {
                    if (dtData.Columns[j].ColumnName.Equals("ROWGUID") || dtData.Columns[j].ColumnName.Equals("RECORD_DATE")) continue;

                    if (string.IsNullOrEmpty(dtData.Rows[i][j].ToString().Trim()) == false)
                    {
                        blnEmptyRow = false;
                        break;
                    }
                }

                // 如果是空行, 删除
                if (blnEmptyRow) dtData.Rows[i].Delete();
            }
        }

        /// <summary>
        /// 删除DataTable中的空白行
        /// </summary>
        /// <param name="dtData">数据表</param>
        /// <param name="strArrIgnoreColumns">忽略的列数组</param>
        public static void DeleteEmptyRow(DataTable dtData, string[] strArrIgnoreColumns)
        {
            for (int i = dtData.Rows.Count - 1; i >= 0; i--)
            {
                // 判断是否是空行
                bool blnEmptyRow = true;
                for (int j = 0; j < dtData.Columns.Count; j++)
                {
                    if (dtData.Columns[j].ColumnName.Equals("ROWGUID")) continue;
                    if (strArrIgnoreColumns != null && strArrIgnoreColumns.Contains<string>(dtData.Columns[j].ColumnName)) continue;

                    if (string.IsNullOrEmpty(dtData.Rows[i][j].ToString().Trim()) == false)
                    {
                        blnEmptyRow = false;
                        break;
                    }
                }

                // 如果是空行, 删除
                if (blnEmptyRow) dtData.Rows[i].Delete();
            }
        }

        /// <summary>
        /// 把行转换为表
        /// </summary>
        /// <param name="drArrRows">行数组</param>
        /// <returns></returns>
        public static DataTable ToDataTable(DataRow[] drArrRows)
        {
            if (drArrRows == null || drArrRows.Length == 0) return null;
            DataTable dtResult = drArrRows[0].Table.Clone();

            for (int i = 0; i < drArrRows.Length; i++)
            {
                dtResult.Rows.Add(drArrRows[i].ItemArray);
            }

            return dtResult;
        }

        /// <summary>
        /// 去重复数据
        /// </summary>
        /// <param name="dtSource">源数据表</param>
        /// <param name="strArrColumns"></param>
        /// <returns></returns>
        public static DataTable GetDataTable_DistinctRecord(DataTable dtSource, string[] strArrColumns)
        {
            if (dtSource == null || dtSource.Rows.Count == 0) return null;
            DataView dv = new DataView(dtSource);
            DataTable dtResult = dv.ToTable(true, strArrColumns);
            return dtResult;
        }

        /// <summary>
        /// 合并数据表，不比较数据，只比较主键, 要求有主键, 无修改操作, 只有增删
        /// </summary>
        /// <param name="dsDest">目标表</param>
        /// <param name="dsSrc">原表</param>
        /// <returns></returns>
        public static bool UpdateTableByKey(DataSet dsDest, DataSet dsSrc)
        { 
            // 目标表中增加一个字段, 表示操作类型, 1:新增 3：删除 无操作, 最后要删除无操作的记录
            // 注意：无修改操作。该函数仅处理主键相同，记录就相同的情况。

            // 条件检查
            if (dsDest == null || dsDest.Tables.Count == 0 || dsDest.Tables[0].PrimaryKey.Length == 0)
            {
                throw new Exception("UpdateTable 的参数 dsDest 的表中必须要有主键!");
            }

            if (dsSrc == null || dsSrc.Tables.Count == 0)
            {
                throw new Exception("UpdateTable 的参数 源数据表 为空!");
            }
            
            // 以主键作为排序依据
            string sort = string.Empty;
            for (int i = 0; i < dsDest.Tables[0].PrimaryKey.Length; i++)
            {
                sort += (i > 0 ? "," : "") + dsDest.Tables[0].PrimaryKey[i].ColumnName;
            }

            // 进行比较
            DataRow[] drDests = dsDest.Tables[0].Select("", sort);
            DataRow[] drSrcs = dsSrc.Tables[0].Select("", sort);

            int idxDest = 0;
            int idxSrc = 0;
            
            DataRow drDest = null;
            DataRow drSrc = null;

            DataColumn[] keyCols = dsDest.Tables[0].PrimaryKey;     // 主键列

            int compareResult = 0;          // 两条记录主键比较结果: 0:相等 1:大于 -1:小于
            while (idxSrc < drSrcs.Length)
            {
                drSrc = drSrcs[idxSrc];

                // 比较主键
                compareResult = 0;

                if (idxDest >= drDests.Length)
                {
                    compareResult = 1;
                }
                else
                {
                    drDest = drDests[idxDest];
                    
                    for (int c = 0; c < keyCols.Length; c++)
                    {
                        compareResult = drDest[keyCols[c].ColumnName].ToString().CompareTo(drSrc[keyCols[c].ColumnName].ToString());
                        if (compareResult != 0) break;
                    }
                }

                // 依据比较结果进行处理
                switch (compareResult)
                {
                    // 如果主键相同, 进行下一条记录比较
                    case 0:
                        idxDest++;
                        idxSrc++;

                        break;

                    // 如果目标小, (即：目标表中没有源表中的记录, 要删除目标表中的记录)
                    case -1:
                        drDest.Delete();
                        idxDest++;
                        break;

                    // 如果目标大, 向目标表中插入记录
                    case 1:
                        drDest = dsDest.Tables[0].NewRow();

                        foreach (DataColumn dc in dsDest.Tables[0].Columns)
                        {
                            drDest[dc.ColumnName] = drSrc[dc.ColumnName];
                        }

                        dsDest.Tables[0].Rows.Add(drDest);

                        idxSrc++;
                        break;

                    default:
                        throw new Exception("UpdateTableByKey 函数 出现非法的比较结果!");
                }
            }

            // 删除目标表中多余的记录
            for (; idxDest < drDests.Length; idxDest++)
            {
                drDests[idxDest].Delete();
            }

            return true;
        }


        /// <summary>
        /// 合并数据表，不比较数据，只比较主键, 要求有主键, 无修改操作, 只有增删
        /// </summary>
        /// <param name="dsDest">目标表</param>
        /// <param name="dsSrc">原表</param>
        /// <param name="keyCols">主键列数组</param>
        /// <returns></returns>
        public static bool UpdateTableByKey(DataSet dsDest, DataSet dsSrc, string[] keyCols)
        {
            // 目标表中增加一个字段, 表示操作类型, 1:新增 3：删除 无操作, 最后要删除无操作的记录
            // 注意：无修改操作。该函数仅处理主键相同，记录就相同的情况。

            // 条件检查
            if (dsDest == null || dsDest.Tables.Count == 0 || dsDest.Tables[0].PrimaryKey.Length == 0)
            {
                throw new Exception("UpdateTable 的参数 dsDest 的表中必须要有主键!");
            }

            if (dsSrc == null || dsSrc.Tables.Count == 0)
            {
                throw new Exception("UpdateTable 的参数 源数据表 为空!");
            }

            // 以主键作为排序依据
            string sort = string.Empty;
            for (int i = 0; i < dsDest.Tables[0].PrimaryKey.Length; i++)
            {
                sort += (i > 0 ? "," : "") + dsDest.Tables[0].PrimaryKey[i].ColumnName;
            }

            // 进行比较
            DataRow[] drDests = dsDest.Tables[0].Select("", sort);
            DataRow[] drSrcs = dsSrc.Tables[0].Select("", sort);

            int idxDest = 0;
            int idxSrc = 0;

            DataRow drDest = null;
            DataRow drSrc = null;
            
            int compareResult = 0;          // 两条记录主键比较结果: 0:相等 1:大于 -1:小于
            while (idxSrc < drSrcs.Length)
            {
                drSrc = drSrcs[idxSrc];

                // 比较主键
                compareResult = 0;

                if (idxDest >= drDests.Length)
                {
                    compareResult = 1;
                }
                else
                {
                    drDest = drDests[idxDest];

                    for (int c = 0; c < keyCols.Length; c++)
                    {
                        compareResult = drDest[keyCols[c]].ToString().CompareTo(drSrc[keyCols[c]].ToString());
                        if (compareResult != 0) break;
                    }
                }

                // 依据比较结果进行处理
                switch (compareResult)
                {
                    // 如果主键相同, 进行下一条记录比较
                    case 0:
                        idxDest++;
                        idxSrc++;

                        break;

                    // 如果目标小, (即：目标表中没有源表中的记录, 要删除目标表中的记录)
                    case -1:
                        drDest.Delete();
                        idxDest++;
                        break;

                    // 如果目标大, 向目标表中插入记录
                    case 1:
                        drDest = dsDest.Tables[0].NewRow();

                        foreach (DataColumn dc in dsDest.Tables[0].Columns)
                        {
                            drDest[dc.ColumnName] = drSrc[dc.ColumnName];
                        }

                        dsDest.Tables[0].Rows.Add(drDest);

                        idxSrc++;
                        break;

                    default:
                        throw new Exception("UpdateTableByKey 函数 出现非法的比较结果!");
                }
            }

            // 删除目标表中多余的记录
            for (; idxDest < drDests.Length; idxDest++)
            {
                drDests[idxDest].Delete();
            }

            return true;
        }


        /// <summary>
        /// 从字符串中生成DataSet, 字符串以,进行分隔 0:不划,1:划
        /// </summary>
        /// <param name="str">字符串</param>
        /// <returns>生成的DataSet</returns>
        public static DataSet GetDictDSFromStr(string str)
        {
            string[] items = str.Split(new char[] { '，', ',' });
            DataSet ds = CreateDict();

            //AddDictItem(ref ds, "", "", -1);
            for (int i = 0; i < items.Length; i++)
            {
                string[] parts = items[i].Split(':');

                if (parts.Length > 1)
                {
                    AddDictItem(ref ds, parts[1], parts[0], i);
                }
                else
                {
                    AddDictItem(ref ds, parts[0], parts[0], i);
                }
            }

            return ds;
        }



        /// <summary>
        /// 创建只有一列的DataSet
        /// </summary>
        /// <param name="colName">列名</param>
        /// <param name="valList">值列表,  ","分隔</param>
        /// <returns></returns>
        public static DataSet CreateDict(string valList, bool nameIsInt)
        {
            // 创建表结构
            DataSet ds = CreateDict();

            // 添加记录
            string[] parts = valList.Split(',');
            for (int i = 0; i < parts.Length; i++)
            {
                DataRow drNew = ds.Tables[0].NewRow();
                drNew["ITEM_NAME"] = parts[i].Trim();
                drNew["ITEM_CODE"] = parts[i].Trim();

                if (nameIsInt)
                {
                    drNew["SHOW_ORDER"] = Converter.ToInt(parts[i]);
                }
                else
                {
                    drNew["SHOW_ORDER"] = i;
                }

                ds.Tables[0].Rows.Add(drNew);
            }

            return ds;
        }

        /// <summary>
        /// 合并数据表，不比较数据，只比较主键, 要求有主键, 无修改操作, 只有增删
        /// </summary>
        /// <param name="dtDest">目标表</param>
        /// <param name="dtSrc">原表</param>
        /// <returns></returns>
        public static void MergeTable(DataTable dtDest, DataTable dtSrc)
        { 
            // 去掉只读属性
            foreach (DataColumn dc in dtDest.Columns)
            {
                dtDest.Columns[dc.ColumnName].ReadOnly = false;
            }

            // 按主键进行排序
            string sort = string.Empty;
            for (int i = 0; i < dtDest.PrimaryKey.Length; i++)
            {
                sort = (i > 0 ? "," : "") + dtDest.PrimaryKey[i].ColumnName;
            }

            DataRow[] drDests = dtDest.Select("", sort);
            DataRow[] drSrcs = dtSrc.Select("", sort);

            // 进行记录比较
            int rowDest = 0;
            int rowSrc = 0;
            while(rowDest < drDests.Length && rowSrc < drSrcs.Length)
            {
                DataRow drDest = drDests[rowDest];
                DataRow drSrc = drSrcs[rowSrc];

                // 比较主键大小
                int compare = 0;
                string colName = string.Empty;
                for(int c = 0; c <dtDest.PrimaryKey.Length; c++)
                {
                    colName = dtDest.PrimaryKey[c].ColumnName;

                    compare = (drDest[colName].ToString().CompareTo(drSrc[colName].ToString()));
                    if (compare != 0) break;
                }

                // 如果相同
                if (compare == 0)
                {
                    rowDest++;
                    rowSrc++;

                    // 检测是否变更
                    bool blnChanged = false;
                    foreach (DataColumn dc in dtDest.Columns)
                    {
                        if (drDest[dc.ColumnName] == DBNull.Value && drSrc[dc.ColumnName] == DBNull.Value) continue;

                        if (drDest[dc.ColumnName] == DBNull.Value && drSrc[dc.ColumnName] != DBNull.Value
                         || drDest[dc.ColumnName] != DBNull.Value && drSrc[dc.ColumnName] == DBNull.Value)
                        {
                            blnChanged = true;
                            break;
                        }

                        if (drDest[dc.ColumnName] != drSrc[dc.ColumnName])
                        {
                            blnChanged = true;
                            break;
                        }
                    }

                    if (blnChanged)
                    {
                        foreach (DataColumn dc in dtDest.Columns)
                        {
                            drDest[dc.ColumnName] = drSrc[dc.ColumnName];
                        }
                    }

                    continue;
                }

                // 如果目标小, 删除目标记录
                if (compare < 0)
                {
                    drDest.Delete();
                    rowDest++;
                    continue;
                }

                // 如果目标大, 插入源记录
                if (compare > 0)
                {
                    rowSrc++;
                    
                    DataRow drNew = dtDest.NewRow();
                    drNew.ItemArray = drSrc.ItemArray;
                    dtDest.Rows.Add(drNew);

                    continue;
                }
            }

            for (int i = rowDest; i < drDests.Length; i++)
            {
                drDests[i].Delete();
            }
        }


        /// <summary>
        /// 合并DataSet
        /// </summary>
        public static void MergeDataSet(ref DataSet dsDest, DataSet dsSrc, string[] colPrimaryKeys)
        {
            foreach (DataColumn dc in dsDest.Tables[0].Columns)
            {
                dc.ReadOnly = false;
            }

            // 数据合并
            foreach (DataRow dr in dsSrc.Tables[0].Rows)
            {
                // 查找记录是否存在
                string filter = string.Empty;
                for (int i = 0; i < colPrimaryKeys.Length; i++)
                {
                    if (i > 0) filter += " AND ";
                    filter += colPrimaryKeys[i] + " = '" + dr[colPrimaryKeys[i]].ToString()+"'";
                }

                DataRow[] drFind = dsDest.Tables[0].Select(filter);
                if (drFind.Length == 0)
                {
                    DataRow drNew = dsDest.Tables[0].NewRow();
                    drNew.ItemArray = dr.ItemArray;
                    dsDest.Tables[0].Rows.Add(drNew);
                }
                else
                {
                    drFind[0].ItemArray = dr.ItemArray;
                }
            }
        }

        
		 /// <summary>
        /// 合并DataSet
        /// </summary>
        /// <param name="dtDest">目标数据表</param>
        /// <param name="dtSrc">源数据表</param>
        /// <param name="colPrimaryKeys">主键列</param>
        public static void MergeDataSetEx(ref DataTable dtDest, DataTable dtSrc, DataColumn[] colPrimaryKeys)
        {
            foreach (DataColumn dcDest in dtDest.Columns)
            {
                dcDest.ReadOnly = false;
            }

            // 数据合并
            foreach (DataRow drSrc in dtSrc.Rows)
            {
                bool bIsNullRec = false;

                // 查找记录是否存在
                string strDescFilter = string.Empty;
                for (int i = 0; i < colPrimaryKeys.Length; i++)
                {
                    if (i > 0) strDescFilter += " AND ";
                    strDescFilter += colPrimaryKeys[i].ColumnName + " = " + Cs02StringHelper.SqlConvert(drSrc[colPrimaryKeys[i].ColumnName].ToString());

                    // Key值为空，是空记录
                    if (drSrc[colPrimaryKeys[i].ColumnName].ToString().Length == 0) bIsNullRec = true;
                }

                //空记录，不处理
                if (bIsNullRec) continue;

                //在目标数据集中，查找
                DataRow[] drArrFind = dtDest.Select(strDescFilter);
                DataRow drEdit = null;

                //找不到，加入
                if (drArrFind.Length == 0)
                {
                    drEdit = dtDest.NewRow();

                    // 添加主键的值
                    for (int i = 0; i < colPrimaryKeys.Length; i++)
                    {
                        string keyColName = colPrimaryKeys[i].ColumnName.Trim();

                        if (dtDest.Columns[keyColName].DataType.Equals(typeof(System.DateTime)))
                        {
                            drEdit[keyColName] = DateTimeHelper.GetDateTime(drSrc[keyColName].ToString());
                            //drEdit[keyColName] = drSrc[keyColName];
                        }
                        else
                        {
                            if (drSrc[keyColName].ToString().Trim().Length == 0
                                && dtDest.Columns[keyColName].DataType.Equals(typeof(System.String)) == false)
                            {
                                drEdit[keyColName] = DBNull.Value;
                            }
                            else
                            {
                                drEdit[keyColName] = drSrc[keyColName].ToString();
                            }
                        }
                    }
                }
                else
                {
                    drEdit = drArrFind[0];
                }

                foreach (DataColumn dcDest in dtDest.Columns)
                {
                    bool blnIsKey = false;
                    for (int i = 0; i < colPrimaryKeys.Length; i++)
                    {
                        if (colPrimaryKeys[i].ColumnName.ToUpper().Equals(dcDest.ColumnName.ToUpper())) blnIsKey = true;
                    }

                    if (blnIsKey) continue;

                    if (dtSrc.Columns.Contains(dcDest.ColumnName) == false) continue;

                    if (dtDest.Columns[dcDest.ColumnName].DataType.Equals(typeof(System.DateTime)))
                    {
                        drEdit[dcDest.ColumnName] = DateTimeHelper.GetDateTime(drSrc[dcDest.ColumnName].ToString());
                        //drEdit[dcDest.ColumnName] = drSrc[dcDest.ColumnName];
                    }
                    else
                    {
                        if (drSrc[dcDest.ColumnName].ToString().Trim().Length == 0
                            && dtDest.Columns[dcDest.ColumnName].DataType.Equals(typeof(System.String)) == false)
                        {
                            drEdit[dcDest.ColumnName] = DBNull.Value;
                        }
                        else
                        {
                            drEdit[dcDest.ColumnName] = drSrc[dcDest.ColumnName].ToString();
                        }
                    }
                }

                if (drArrFind.Length == 0)
                {
                    dtDest.Rows.Add(drEdit);
                }
            }
        }

        /// <summary>
        /// 合并DataSet
        /// </summary>
        /// <param name="dtDest">目标数据表</param>
        /// <param name="dtSrc">源数据表</param>
        /// <param name="colPrimaryKeys">主键列</param>
        /// <param name="strFilter">过滤串</param>
        public static void MergeDataSetPart(ref DataTable dtDest, DataTable dtSrc, DataColumn[] colPrimaryKeys, string strFilter)
        {
            foreach (DataColumn dc in dtDest.Columns)
            {
                dc.ReadOnly = false;
            }

            // 数据合并
            DataRow[] drFindSrc = dtSrc.Select(strFilter);
            for (int src = 0; src < drFindSrc.Length; src++)
            {
                DataRow dr = drFindSrc[src];
                bool blnNullRec = false;

                // 查找记录是否存在
                string filter = string.Empty;
                for (int i = 0; i < colPrimaryKeys.Length; i++)
                {
                    if (i > 0) filter += " AND ";
                    filter += colPrimaryKeys[i].ColumnName + " = " + Cs02StringHelper.SqlConvert(dr[colPrimaryKeys[i].ColumnName].ToString());

                    if (dr[colPrimaryKeys[i].ColumnName].ToString().Length == 0) blnNullRec = true;        // 空记录
                }

                if (blnNullRec) continue;

                if (string.IsNullOrEmpty(strFilter) == false) filter += " AND (" + strFilter + ")";
                DataRow[] drFind = dtDest.Select(filter);
                DataRow drEdit = null;
                if (drFind.Length == 0)
                {
                    drEdit = dtDest.NewRow();

                    // 添加主键的值
                    for (int i = 0; i < colPrimaryKeys.Length; i++)
                    {
                        string keyColName = colPrimaryKeys[i].ColumnName.Trim();

                        if (dtDest.Columns[keyColName].DataType.Equals(typeof(System.DateTime)))
                        {
                            drEdit[keyColName] = DateTimeHelper.GetDateTime(dr[keyColName].ToString());
                            //drEdit[keyColName] = dr[keyColName];
                        }
                        else
                        {
                            if (dr[keyColName].ToString().Trim().Length == 0
                                && dtDest.Columns[keyColName].DataType.Equals(typeof(System.String)) == false)
                            {
                                drEdit[keyColName] = DBNull.Value;
                            }
                            else
                            {
                                drEdit[keyColName] = dr[keyColName].ToString();
                            }
                        }
                    }
                }
                else
                {
                    drEdit = drFind[0];
                }

                foreach (DataColumn dc in dtDest.Columns)
                {
                    bool blnIsKey = false;
                    for (int i = 0; i < colPrimaryKeys.Length; i++)
                    {
                        if (colPrimaryKeys[i].ColumnName.ToUpper().Equals(dc.ColumnName.ToUpper())) blnIsKey = true;
                    }

                    if (blnIsKey) continue;

                    if (dtSrc.Columns.Contains(dc.ColumnName) == false) continue;

                    if (dtDest.Columns[dc.ColumnName].DataType.Equals(typeof(System.DateTime)))
                    {
                        drEdit[dc.ColumnName] = DateTimeHelper.GetDateTime(dr[dc.ColumnName].ToString());
                        //drEdit[dc.ColumnName] = dr[dc.ColumnName];
                    }
                    else
                    {
                        if (dr[dc.ColumnName].ToString().Trim().Length == 0
                            && dtDest.Columns[dc.ColumnName].DataType.Equals(typeof(System.String)) == false)
                        {
                            drEdit[dc.ColumnName] = DBNull.Value;
                        }
                        else
                        {
                            drEdit[dc.ColumnName] = dr[dc.ColumnName].ToString();
                        }
                    }
                }

                if (drFind.Length == 0)
                {
                    dtDest.Rows.Add(drEdit);
                }
            }

            // 删除没有变更的数据
            DataRow[] drFindTemp = dtDest.Select(strFilter);
            for (int i = drFindTemp.Length - 1; i >= 0; i--)
            {
                if (drFindTemp[i].RowState == DataRowState.Unchanged) drFindTemp[i].Delete();
            }
        }

     
        /// <summary>
        /// 获取某张表中某个字段值的列表
        /// </summary>
        /// <param name="ds"></param>
        /// <param name="colName"></param>
        /// <returns></returns>
        public static ArrayList GetItems(DataTable dt, string colName)
        {
            ArrayList arr = new ArrayList();
            string colValue = string.Empty;

            DataRow[] drFind = dt.Select("", colName);
            for (int i = 0; i < drFind.Length; i++)
            {
                if (i == 0)
                {
                    colValue = Converter.ToString(drFind[i][colName]);
                    arr.Add(colValue);
                    continue;
                }

                if (colValue.Equals(Converter.ToString(drFind[i][colName])) == false)
                {
                    colValue = Converter.ToString(drFind[i][colName]);
                    arr.Add(colValue);
                }
            }

            return arr;
        }

        /// <summary>
        /// 通过sql文件获取数据源
        /// </summary>
        /// <param name="templetName"></param>
        /// <param name="hasParam"></param>
        /// <returns></returns>
        private DataSet GetSQLData(string templetName, Hashtable hasParam)
        {
            DataSet dsTemp = new DataSet();
            string fileNameShort = @"SQL\" + templetName + ".sql";  //sql语句文件
            string fileName = Path.Combine(System.Environment.CurrentDirectory, fileNameShort);//全路径文件名
            if (!File.Exists(fileName))
            {
                throw new Exception("请配置文件 " + fileName);
            }

            string sql1 = Cs01FileHelper.GetFileContent(fileName);
            byte[] key = Encoding.Unicode.GetBytes("abcd");//密钥
            byte[] data = Convert.FromBase64String(sql1);//待解密字符串

            DESCryptoServiceProvider descsp = new DESCryptoServiceProvider();//加密、解密对象
            MemoryStream MStream = new MemoryStream();//内存流对象

            //用内存流实例化解密流对象
            CryptoStream CStream = new CryptoStream(MStream, descsp.CreateDecryptor(key, key), CryptoStreamMode.Write);
            CStream.Write(data, 0, data.Length);//向加密流中写入数据
            CStream.FlushFinalBlock();//将数据压入基础流
            byte[] temp = MStream.ToArray();//从内存流中获取字节序列
            CStream.Close();//关闭加密流
            MStream.Close();//关闭内存流
            string sql = Encoding.Unicode.GetString(temp);//返回解密后的字符串
            sql = convertParameter(sql, hasParam);
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            dsTemp = spc.GetDataBySql(sql);
            return dsTemp;
        }

        /// <summary>
        /// 通过sql文件获取SQL语句
        /// </summary>
        /// <param name="templetName"></param>
        /// <param name="hasParam"></param>
        /// <returns></returns>
        private string GetSQL(string templetName, Hashtable hasParam)
        {
            string fileNameShort = @"SQL\" + templetName + ".sql";  //sql语句文件
            string fileName = Path.Combine(System.Environment.CurrentDirectory, fileNameShort);//全路径文件名
            if (!File.Exists(fileName))
            {
                throw new Exception("请配置文件 " + fileName);
            }

            string sql1 = Cs01FileHelper.GetFileContent(fileName);
            byte[] key = Encoding.Unicode.GetBytes("abcd");//密钥
            byte[] data = Convert.FromBase64String(sql1);//待解密字符串

            DESCryptoServiceProvider descsp = new DESCryptoServiceProvider();//加密、解密对象
            MemoryStream MStream = new MemoryStream();//内存流对象

            //用内存流实例化解密流对象
            CryptoStream CStream = new CryptoStream(MStream, descsp.CreateDecryptor(key, key), CryptoStreamMode.Write);
            CStream.Write(data, 0, data.Length);//向加密流中写入数据
            CStream.FlushFinalBlock();//将数据压入基础流
            byte[] temp = MStream.ToArray();//从内存流中获取字节序列
            CStream.Close();//关闭加密流
            MStream.Close();//关闭内存流
            string sql = Encoding.Unicode.GetString(temp);//返回解密后的字符串
            sql = convertParameter(sql, hasParam);
            return sql;
        }

        private string convertParameter(string sql, Hashtable hasParam)
        {
            // 替换参数

            string paramName = string.Empty;
            string paramValue = string.Empty;

            string paramName0 = string.Empty;
            string paramValue0 = string.Empty;

            foreach (DictionaryEntry entry in hasParam)
            {
                // 参数名
                paramName = "{" + entry.Key.ToString().ToUpper() + "}";         // 这种情况 变成 ''
                paramName0 = "[" + entry.Key.ToString().ToUpper() + "]";        // 这种情况 变成 原样

                // 参数值
                if (entry.Value == null)
                {
                    paramValue = "NULL";
                }
                else if (entry.Value.GetType().Equals(typeof(DateTime)))
                {
                    paramValue = Cs02StringHelper.GetOraDate((DateTime)(entry.Value));
                }
                else
                {
                    paramValue = Cs02StringHelper.SqlConvert(entry.Value.ToString());
                }

                paramValue0 = entry.Value == null ? "" : entry.Value.ToString();

                // 替换参数
                if (sql.IndexOf(paramName) >= 0)
                {
                    sql = sql.Replace(paramName, paramValue);
                }
                if (sql.IndexOf(paramName0) >= 0)
                {
                    sql = sql.Replace(paramName0, paramValue0);
                }
            }
            sql = sql.Replace(@" = ''", " IS NULL");
            return sql;
        }

        /// <summary>
        /// 获取参数
        /// </summary>
        /// <returns></returns>
        private Hashtable getParams(DataTable dtParam)
        {
            // 向SQL语句传递参数
            Hashtable hasParam = new Hashtable();
            foreach (DataRow dr in dtParam.Rows)
            {
                hasParam.Add(dr[0].ToString(), dr[1].ToString());
            }
            return hasParam;
        }
    }
}
