﻿using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System;
using System.Data;
using System.Drawing;
using Tjhis.Report.Custom.Common;
using Tjhis.Report.Custom.Srv;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmStatisticalClassDict : ParentForm
    {
        srvStatisticalQuery srv = new srvStatisticalQuery();
        private string hospitalCode = SystemParm.HisUnitCode;
        private string appName = Const.customAppCode;
        DataSet dsTempletClass;
        public frmStatisticalClassDict()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 加载事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void frmStatisticalClassDict_Load(object sender, EventArgs e)
        {
            searchLookUpEditAppCode.DataSource = srv.GetApplications().Tables[0];
            searchLookUpEditAppCode.PopupFormSize = new Size(150, 300);

            BindTempClassDict();
        }

        /// <summary>
        /// 绑定报表分类数据
        /// </summary>
        private void BindTempClassDict()
        {
            dsTempletClass = srv.GetTempletClassDict();
            gridControl1.DataSource = dsTempletClass.Tables[0];
        }

        /// <summary>
        /// 添加报表分类
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barBtnAdd_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            gridView1.CloseEditor();

            gridView1.AddNewRow();
            
            DataRow dr = gridView1.GetFocusedDataRow();
            dr["HOSPITAL_CODE"] = hospitalCode;
            dr["APP_NAME"] = appName;
            dr["class_id"] = DateTime.Now.ToString("yyyyMMddHHmmssfff");
            dr["class_name"] = "（未命名）";

            gridView1.CloseEditor();
        }

        /// <summary>
        /// 删除选中分类
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barBtnDel_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            gridView1.CloseEditor();
            gridView1.DeleteSelectedRows();
        }

        /// <summary>
        /// 保存报表分类
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barBtnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            gridView1.CloseEditor();
            gridView1.UpdateCurrentRow();

            if (srv.SaveData(dsTempletClass) > 0)
            {
                XtraMessageBox.Show("保存成功！", "提示");
            }
        }

        private void barLargeButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
    }
}
