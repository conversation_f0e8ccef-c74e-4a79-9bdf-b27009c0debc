﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace PlatCommonForm
{
    public class ClassArmyPkp
    {
        //声明dll
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "MakeoutInvo")]
        public static extern int MakeoutInvo(string sDiskNo, int bInvoType, string sInvoDetail, StringBuilder sRespBuffer);
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "BlankoutInvo")]
        public static extern int BlankoutInvo(string sDiskNo, int bBlankMode, string sInvoDetail);
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "GetDiskNo")]
        public static extern int GetDiskNo(int ucType, StringBuilder sDiskNo);// 遍历票控(管理)盘编号
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "OpenDisk")]
        public static extern int OpenDisk(string sDiskNo, StringBuilder sDiskSerialNumber);// 打开票控(管理)盘
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "SelectApp")]
        public static extern int SelectApp(string sDiskNo, int bAppType);	// 选择应用
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "CheckPIN")]
        public static extern int CheckPIN(string sDiskNo, string sPwd);// 认证使用口令
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "QueryAllInvoType")]
        public static extern int QueryAllInvoType(string sDiskNo, StringBuilder sRespBuffer);// 查询目前存在的所有票据类型
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "SelectInvoType")]
        public static extern int SelectInvoType(string sDiskNo, int bInvoType);// 选择票据类型
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "QueryInvoTemple")]
        public static extern int QueryInvoTemple(string sDiskNo, StringBuilder sRespBuffer);// 查询当前票据模板
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "ReadManageData")]
        public static extern int ReadManageData(string sDiskNo, int bTag, StringBuilder sManageData);// 读管理信息
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "GetClock")]
        public static extern int GetClock(string sDiskNo, StringBuilder sClock);	// 读取当前时间
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "QueryBuyInvo")]
        public static extern int QueryBuyInvo(string sDiskNo, StringBuilder sRespBuffer);// 查询票据领购信息
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "ReadInvoDetail")]
        public static extern int ReadInvoDetail(string sDiskNo, int bReadMode, string sCondition, StringBuilder sRespBuffer);//  读票据明细数据
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "CloseDisk")]
        public static extern int CloseDisk(string sDiskNo);	// 关闭票控(管理)盘
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "WriteDeclareData")]
        public static extern int WriteDeclareData(string sDiskNo, StringBuilder sRespBuffer);// 读票据明细数据
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "GetCurStatus")]
        public static extern int GetCurStatus(string sDiskNo, StringBuilder sStatus);	// 查询当前工作状态
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "DeleteDeclareData")]
        public static extern int DeleteDeclareData(string sDiskNo, int bDelMode, string sCondition);    // 查询当前工作状态

        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "GetErrorCode")]
        public static extern int GetErrorCode(string sDiskNo, StringBuilder sRespBuffer);
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "Asc2Hex")]
        public static extern int Asc2Hex(string asc, int len, StringBuilder hex);// ASC码转换成指定长度十六进制字符串
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "Hex2Asc")]
        public static extern int Hex2Asc(string hex, StringBuilder asc);// 十六进制字符串转换成ASC码
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "Int2Hex")]
        public static extern int Int2Hex(string dec, int len, StringBuilder hex);// 十进制字符串转换为十六进制字符串（支持64位整型）
        [DllImport("DiskCtrl.dll", CharSet = CharSet.Ansi, EntryPoint = "Hex2Int")]
        public static extern int Hex2Int(string hex, StringBuilder dec);	// 十六进制字符串转换为十进制字符串（支持64位整型）

        public string is_pkpbh = ""; //票控盘编号
        public int ii_fplx; //发票类型
        public string is_kpdwdm = "";//开票单位代码
        public string is_kpdwmc = "";
        public string is_cwbmdm = "", is_cwbmmc = "";
        public string[] is_field = new string[100];
        public string[] is_param = new string[100];
        public decimal ii_len_jddm;
        public decimal ii_len_jdhm;
        public decimal ii_len_tpdm;
        public decimal ii_len_tphm;

        public int ii_pos_type;
        public string is_bbh1 = "01";      // 版本号１－－本程序使用
        public string is_bbh2 = "01";          // 版本号２－－预留
        public string is_qtxx_sz0 = "hjje";	// 特殊含义


        public static int wf_getDiskNo(int li_i, StringBuilder ls_temp)
        {

            int li_state = GetDiskNo(li_i, ls_temp);
            if (li_state == -1)
            {
                XtraMessageBox.Show("无票控盘，代码-1", "提示");
                return -1;
            }
            else if (li_state == -2)
            {
                XtraMessageBox.Show("非票控盘，代码-2", "提示");
                return -1;
            }
            else if (li_state == -3)
            {
                XtraMessageBox.Show("非票控盘，代码-2", "提示");
                return -1;
            }
            return 0;
        }

        public int of_get_diskno(string as_disk_no, ref string as_err)
        {
            StringBuilder ls_temp = new StringBuilder(65536);
            int li_state;
            //string ls_temp = "";

            ////获取当前连接的票控盘号
            li_state = wf_getDiskNo(0, ls_temp);
            if (of_getcurstatus(as_disk_no, "GetDiskNo", "遍历票控盘编号", li_state, ref as_err) == false)
                return li_state;
            //as_disk_no = LeftA(ls_temp, 12)
            as_disk_no = strToByteLeft(ls_temp.ToString(), 0, 12);


            return 0;
        }

        public int of_blankoutinvo(int ai_fplx, string as_invono, string as_operator, ref string as_err, ref Dictionary<string, string> idc)
        {
            //打开票控盘
            string ldc_invoice_costs = "";
            int li_state = of_open(ai_fplx, ref is_pkpbh, ref as_err);
            if (li_state < 0) return li_state;
            //根据传入的参数，查找待作废发票信息
            if (!string.IsNullOrEmpty(as_invono))
            {
                string sql = "select disk_no, rcpt_date, rcpt_no, invoice_costs, invoice_status ";
                sql = sql + " from invoice_info where rcpt_type =" + ai_fplx.ToString() + " and invoice_no = '" + as_invono + "'";
                DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
                if (dt.Rows.Count > 0)
                {
                    string ls_disk_no = dt.Rows[0][0].ToString();
                    ldc_invoice_costs = dt.Rows[0]["invoice_costs"].ToString();
                    if (!ls_disk_no.Equals(is_pkpbh))
                    {
                        as_err = "本票据是编号为“" + ls_disk_no + "”票控盘开出的，请用该盘作废";
                        XtraMessageBox.Show(as_err, "提示");
                        return -1;
                    }
                }
                else
                {
                    as_err = "没有查询到作废记录";
                    return -1;
                }

                //获取当前时间
                StringBuilder ls_temp = new StringBuilder(1024);
                li_state = GetClock(is_pkpbh, ls_temp);
                if (of_getcurstatus(is_pkpbh, "GetClock", "读取当前时间", li_state, ref as_err) == false)
                    return li_state;
                string ls_temp2 = ls_temp.ToString();
                string ls_zfrq = strToByteLeft(ls_temp2, 0, 8);

                //作废 
                string ls_fpmx = ls_zfrq;
                ls_fpmx += of_switch_dec2hex(ldc_invoice_costs, 2, 12);
                ls_fpmx += of_switch_asc2hex(as_operator, 40);
                ls_fpmx += of_switch_asc2hex("", 80); //作废事由
                ls_fpmx += as_invono;
                string ls_invoice_status = "";
                //票据作废：正常作废失败后，尝试跨期作废 
                li_state = BlankoutInvo(is_pkpbh, 0, ls_fpmx);
                if (of_getcurstatus(is_pkpbh, "BlankoutInvo", "票据作废", li_state, ref as_err) == false)
                {
                    li_state = BlankoutInvo(is_pkpbh, 1, ls_fpmx);
                    if (of_getcurstatus(is_pkpbh, "BlankoutInvo", "票据作废", li_state, ref as_err) == false)
                        return li_state;
                    ls_invoice_status = "05";
                }
                else
                {
                    if (decimal.Parse(ldc_invoice_costs) == 0)
                    {
                        ls_invoice_status = "02";
                    }
                    else
                    {
                        ls_invoice_status = "03";
                    }
                }

                sql = "update invoice_info set invoice_status ='" + ls_invoice_status + "' where rcpt_type = '" + ai_fplx.ToString() + "' and invoice_no ='" + as_invono + "'";
                idc.Add(sql, "更新发票状态出错");
                //关闭
                li_state = CloseDisk(is_pkpbh);
                if (of_getcurstatus(is_pkpbh, "关闭票控盘", "", li_state, ref as_err) == false)
                    return li_state;

            }


            return li_state;
        }
        //====================================================================
        // 事件: of_makeoutinvo()
        //--------------------------------------------------------------------
        // 描述: 往票控盘中写入发票信息
        //--------------------------------------------------------------------
        // 参数:
        // 	value    	integer  	ai_fplx   	
        // 	value    	datastore	ads_data[]	 
        // 	reference	string   	as_pkpbh  	
        // 	reference	string   	as_invono   	 
        // 	reference	string   	as_skm    	
        // 	reference	string   	as_err    	 
        //--------------------------------------------------------------------\
        //--------------------------------------------------------------------
        // 返回:  integer
        //--------------------------------------------------------------------
        public int of_makeoutinvo(int ai_fplx, DataSet ads_data, ref string as_pkpbh, ref string as_invono, ref string as_skm, ref string as_err, ref Dictionary<string, string> idc, string flag = "0")
        {
            int li_temp;
            try
            {
                //of_initial_parameter();

                ii_fplx = ai_fplx;
                ads_data.Tables[0].Rows[0]["fplx"] = ii_fplx; //票据类型 
                                                              //获取当前连接的票控盘号
                StringBuilder ls_temp = new StringBuilder(65536);
                int li_state = GetDiskNo(0, ls_temp);
                if (of_getcurstatus(is_pkpbh, "GetDiskNo", "遍历票控盘编号", li_state, ref as_err) == false)
                    return li_state;
                is_pkpbh = strToByteLeft(ls_temp.ToString(), 0, 12);
                as_pkpbh = is_pkpbh;
                ads_data.Tables[0].Rows[0]["jqbh"] = as_pkpbh; //票控盘编号

                //打开
                ls_temp = new StringBuilder(65536);
                li_state = OpenDisk(is_pkpbh, ls_temp);
                if (of_getcurstatus(is_pkpbh, "OpenDisk", "打开票控盘", li_state, ref as_err) == false)
                    return li_state;

                //选择应用
                li_state = SelectApp(is_pkpbh, 3);
                if (of_getcurstatus(is_pkpbh, "selectapp", "选择应用类型", li_state, ref as_err) == false)
                    return li_state;

                //输入口令
                string ls_temp1 = "88888888";
                li_state = CheckPIN(is_pkpbh, ls_temp1);
                if (of_getcurstatus(is_pkpbh, "SelectInvoType", "认证使用口令", li_state, ref as_err) == false)
                    return li_state;

                //选择票据类型
                li_state = SelectInvoType(is_pkpbh, ai_fplx);
                if (of_getcurstatus(is_pkpbh, "SelectInvoType", "选择票据类型", li_state, ref as_err) == false)
                    return li_state;
                ls_temp = new StringBuilder(65536);

                li_state = QueryInvoTemple(is_pkpbh, ls_temp);
                //XtraMessageBox.Show("" + li_state + ls_temp.ToString(), "提示");
                if (of_getcurstatus(is_pkpbh, "QueryInvoTemple", "查询当前票据模板", li_state, ref as_err) == false)
                    return li_state;

                ii_len_jddm = of_switch_hex2dec(strToByteLeft(ls_temp.ToString(), 14, 2), 0) * 2;
                ii_len_jdhm = of_switch_hex2dec(strToByteLeft(ls_temp.ToString(), 22, 2), 0) * 2;
                ii_len_tpdm = of_switch_hex2dec(strToByteLeft(ls_temp.ToString(), 30, 2), 0) * 2;
                ii_len_tphm = of_switch_hex2dec(strToByteLeft(ls_temp.ToString(), 38, 2), 0) * 2;

                //获取管理信息
                ls_temp = new StringBuilder(65536);
                li_state = ReadManageData(is_pkpbh, 2, ls_temp);
                if (of_getcurstatus(is_pkpbh, "ReadManageData", "读管理信息", li_state, ref as_err) == false)
                    return li_state;
                is_kpdwdm = of_switch_hex2asc(strToByteLeft(ls_temp.ToString(), 18, 40)).Trim();

                ls_temp = new StringBuilder(65536);
                li_state = ReadManageData(is_pkpbh, 3, ls_temp);
                is_kpdwmc = of_switch_hex2asc(strToByteLeft(ls_temp.ToString(), 182, 160));

                is_cwbmdm = strToByteLeft(ls_temp.ToString(), 6, 16);
                int li_dw = int.Parse(of_switch_hex2dec(strToByteLeft(is_cwbmdm, 0, 1), 0).ToString());
                is_cwbmdm = strToByteRight(is_cwbmdm, 15 - li_dw);
                is_cwbmmc = of_switch_hex2asc(strToByteLeft(ls_temp.ToString(), 22, 160));
                ads_data.Tables[0].Rows[0]["cwbmdm"] = is_cwbmdm; //财务部门代码
                ads_data.Tables[0].Rows[0]["cwbmmc"] = is_cwbmmc;  //财务部门名称
                ads_data.Tables[0].Rows[0]["skdwdm"] = is_kpdwdm;  //收费单位代码
                ads_data.Tables[0].Rows[0]["skdwmc"] = is_kpdwmc; //收费单位名称

                //判断是否补发盘
                li_state = of_get_repairflag(is_pkpbh, ref as_err);
                if (li_state > 0)
                {
                    XtraMessageBox.Show("补发盘,不能开票", "提示");
                    return -1;
                }
                else if (li_state < 0)
                {
                    return -1;
                }
                //获取发票号
                string ls_invono = "";
                li_state = of_get_invono(ref ls_invono);
                if (li_state != 0)
                {
                    as_err = ls_invono;
                    return -1;
                }
                as_invono = ls_invono;
                ads_data.Tables[0].Rows[0]["jdhm"] = ls_invono;//发票号码
                                                               //获取当前时间
                ls_temp = new StringBuilder(1024);
                li_state = GetClock(is_pkpbh, ls_temp);
                if (of_getcurstatus(is_pkpbh, "GetClock", "读取当前时间", li_state, ref as_err) == false)
                    return li_state;
                string ls_temp2 = ls_temp.ToString();
                string ldtm_rcpt_date = strToByteLeft(ls_temp2, 0, 4) + "-" + strToByteLeft(ls_temp2, 4, 2) + "-" + strToByteLeft(ls_temp2, 6, 2) + " " + strToByteLeft(ls_temp2, 8, 2) + ":" + strToByteLeft(ls_temp2, 10, 2) + ":" + strToByteLeft(ls_temp2, 12, 2);
                ads_data.Tables[0].Rows[0]["kprq"] = ldtm_rcpt_date;   //开票日期、开票时间、核算日期
                ads_data.Tables[0].Rows[0]["kpsj"] = ldtm_rcpt_date;                                                        //获取写盘字符串  未完
                string ls_fpmx = "";
                string ls_dkbz = ads_data.Tables[0].Rows[0]["dkbz"].ToString();
                //---------未完
                if (of_switch_fpmx(ai_fplx, ref ads_data, ls_dkbz, ref ls_fpmx, flag) == false) return -2;
                //开票
                int li_type;
                string ls_status = "";
                decimal ldc_hjje = decimal.Parse(ads_data.Tables[0].Rows[0]["hjje"].ToString()); //判断正负票
                if (ldc_hjje < 0)
                {
                    li_type = 1;
                    ls_status = "01"; //已开具的负数发票；
                }
                else if (ldc_hjje == 0)
                {
                    as_err = "合计金额不能为空";
                    return -1;
                }
                else
                {
                    li_type = 0;
                    ls_status = "00"; //已开具的正数发票；
                }
                StringBuilder ls_skm = new StringBuilder(1024);
                //ls_fpmx = "2019062000000021a1d800000021a1d87000000008052501303830353235303120202020202020202020202030383035323530312020202020202020202020201434430000000000000020202020202020202020202020202020202020202020202020202020202020202020202020202020202020202020202020202020202020202020202018020041177200000000000023c1f5d3f1dcdf2020202020202020202020202020202020202020202020202020202020202020202020202020202020202020202020202020202020202020202020202020202020202020202020202020010c3030313030303333363333340012b5dac8fdd2bbc8fdd2bdd4bacad5b7d1b4a612b5dac8fdd2bbc8fdd2bdd4bacad5b7d1b4a608383235303335323008d7dbbacfd2bdd4ba073334373039303904201906050420190620060000000005dc0632333130343202c5ae04d7d4b7d100060000001e848006000000033450060000000016f80600000000000006000000000000060000000000000600000021a1d804c0eebeb204c0eebeb2000806cef7d2a9b7d1060000000a489a060000000a489a06bcecb2e9b7d10600000001dee20600000001dee206bbafd1e9b7d1060000000188300600000001883006bba4c0edb7d10600000000e74d0600000000e74d06d5efb2ecb7d1060000000093a8060000000093a806d6cec1c6b7d1060000000377900600000003779006b4b2cebbb7d10600000001096e0600000001096e06b2c4c1cfb7d1060000000df639060000000df639";
                li_temp = MakeoutInvo(is_pkpbh, li_type, ls_fpmx, ls_skm);
                if (of_getcurstatus(is_pkpbh, "MakeoutInvo", "票据开具", li_temp, ref as_err) == false) return li_temp;
                //XtraMessageBox.Show("值："+ li_temp+"返回内容"+ ls_skm.ToString(), "提示");
                as_skm = strToByteLeft(ls_skm.ToString(), 0, strToByteLen(ls_skm.ToString()) - int.Parse(ii_len_jddm.ToString()) - int.Parse(ii_len_jdhm.ToString()));

                //写入数据库备份
                li_state = of_backupinvo(is_pkpbh, of_get_apptype(ai_fplx), ai_fplx.ToString(), ldtm_rcpt_date, ads_data.Tables[0].Rows[0]["rcpt_no"].ToString(), ls_invono, ls_fpmx, decimal.Parse(ads_data.Tables[0].Rows[0]["hjje"].ToString()), as_skm, ls_status, ref as_err, ref idc);
                if (li_state != 0) return li_state;
                //关闭
                li_state = CloseDisk(is_pkpbh);
                if (of_getcurstatus(is_pkpbh, "CloseDisk", "关闭票控盘", li_state, ref as_err) == false) return li_state;
                // if not isvalid(w_pkp) then
                //    open(w_pkp)
                //    w_pkp.dynamic wf_set_invotype(ai_fplx)
                ////	f_write_log(1,"wf_set_invotype" )
                //end if
                //w_pkp.dynamic wf_refresh()
                //关闭


                return li_temp;

            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("内容" + ex.Message, "异常");
                return -1;


            }

        }
        public int of_blankoutcurinvo(int ai_fplx, ref string as_err)
        {
            //打开票控盘
            string ldc_invoice_costs = "";
            int li_state = of_open(ai_fplx, ref is_pkpbh, ref as_err);
            if (li_state < 0) return li_state;
            StringBuilder ls_temp = new StringBuilder(1024);
            li_state = GetClock(is_pkpbh, ls_temp);
            if (of_getcurstatus(is_pkpbh, "GetClock", "读取当前时间", li_state, ref as_err) == false)
                return li_state;
            string ls_temp2 = ls_temp.ToString();
            string ls_zfrq = strToByteLeft(ls_temp2, 0, 8);
            string ls_invono = "";
            //获取当前发票号
            li_state = of_get_invono(ref ls_invono);
            if (li_state < 0) return li_state;
            string ls_fpmx = "";
            //开票日期CCYYMMDD（4字节BCD）＋票据金额（6字节HEX）＋ 票据作废人（20字节ASC）＋作废原因（40字节ASC）＋票据代码＋票据号码
            ls_fpmx = ls_zfrq;
            ls_fpmx += of_switch_dec2hex(ldc_invoice_costs, 2, 12);
            ls_fpmx += of_switch_asc2hex("", 40);
            ls_fpmx += of_switch_asc2hex("", 80); //作废事由
            ls_fpmx += ls_invono;

            li_state = BlankoutInvo(is_pkpbh, 0, ls_fpmx);
            if (of_getcurstatus(is_pkpbh, "BlankoutInvo", "票据作废", li_state, ref as_err) == false)
                return li_state;
            //关闭
            li_state = CloseDisk(is_pkpbh);
            if (of_getcurstatus(is_pkpbh, "关闭票控盘", "", li_state, ref as_err) == false)
                return li_state;


            return 0;
        }
        public void of_initial_parameter()
        {

            for (int li_i = 0; li_i < 100; li_i++)
            {
                is_field[li_i] = "";
                is_param[li_i] = "";
            }
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1100" + "kprq", ref is_param[0], 1, 8, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1300" + "hjje", ref is_param[0], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1300" + "hjje", ref is_param[0], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1200" + "cwbmdm", ref is_param[0], 7, 16, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1200" + "skdwdm", ref is_param[0], 3, 40, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1200" + "skdwdm", ref is_param[0], 3, 40, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1100" + "kpsj", ref is_param[0], 2, 6, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1100" + "zfrq", ref is_param[0], 1, 8, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1100" + "zfsj", ref is_param[0], 2, 6, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1200" + "zfr", ref is_param[0], 3, 40, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1200" + "zfsy", ref is_param[0], 3, 80, 0);

            int gi_len_tphm, gi_len_jdhm;
            gi_len_tphm = 12;
            gi_len_jdhm = 12;

            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1200" + "jdhm", ref is_param[0], 8, gi_len_jdhm, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[1], "1200" + "tphm", ref is_param[1], 8, gi_len_tphm, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[1], "0300" + "fplx", ref is_param[1], 4, 2, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[1], "1200" + "fkdwmc", ref is_param[1], 3, 160, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[1], "0200" + "bbh1", ref is_param[1], 6, 2, 0);




            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "jqbh", ref is_param[2], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "fkdwdm", ref is_param[2], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "skdwmc", ref is_param[2], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "cwbmmc", ref is_param[2], 3, 0, 0);

            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "ywlsh", ref is_param[2], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "yljglx", ref is_param[2], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "xb", ref is_param[2], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "yblx", ref is_param[2], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "shbzh", ref is_param[2], 3, 0, 0);

            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1301" + "ybtczf", ref is_param[2], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1301" + "grzhzf", ref is_param[2], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1301" + "qtybzf", ref is_param[2], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1301" + "grzfje", ref is_param[2], 4, 12, 2);


            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "skr", ref is_param[2], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "kpr", ref is_param[2], 3, 0, 0);


            of_fpmx2skp_initial_fpmx_parameter(ref is_field[3], "0300" + "len_qtxm", ref is_param[3], 4, 4, 0);



            of_fpmx2skp_initial_fpmx_parameter(ref is_field[4], "1211" + "xmmc", ref is_param[4], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[4], "1301" + "sl", ref is_param[4], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[4], "1301" + "je", ref is_param[4], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[4], "1301" + "grzfje", ref is_param[4], 4, 12, 2);


        }

        public void of_initial_parameter_inp()
        {

            for (int li_i = 0; li_i < 100; li_i++)
            {
                is_field[li_i] = "";
                is_param[li_i] = "";
            }
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1100" + "kprq", ref is_param[0], 1, 8, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1300" + "hjje", ref is_param[0], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1300" + "hjje", ref is_param[0], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1200" + "cwbmdm", ref is_param[0], 7, 16, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1200" + "skdwdm", ref is_param[0], 3, 40, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1200" + "skdwdm", ref is_param[0], 3, 40, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1100" + "kpsj", ref is_param[0], 2, 6, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1100" + "zfrq", ref is_param[0], 1, 8, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1100" + "zfsj", ref is_param[0], 2, 6, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1200" + "zfr", ref is_param[0], 3, 40, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1200" + "zfsy", ref is_param[0], 3, 80, 0);

            int gi_len_tphm, gi_len_jdhm;
            gi_len_tphm = 12;
            gi_len_jdhm = 12;

            of_fpmx2skp_initial_fpmx_parameter(ref is_field[0], "1200" + "jdhm", ref is_param[0], 8, gi_len_jdhm, 0);

            of_fpmx2skp_initial_fpmx_parameter(ref is_field[1], "1200" + "tphm", ref is_param[1], 8, gi_len_tphm, 0);

            of_fpmx2skp_initial_fpmx_parameter(ref is_field[1], "0300" + "fplx", ref is_param[1], 4, 2, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[1], "1200" + "fkdwmc", ref is_param[1], 3, 160, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[1], "0200" + "bbh1", ref is_param[1], 6, 2, 0);




            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "jqbh", ref is_param[2], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "fkdwdm", ref is_param[2], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "skdwmc", ref is_param[2], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "cwbmmc", ref is_param[2], 3, 0, 0);

            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "ywlsh", ref is_param[2], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "yljglx", ref is_param[2], 3, 0, 0);

            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "blh", ref is_param[2], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1101" + "zyqssj", ref is_param[2], 1, 8, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1101" + "zyzzsj", ref is_param[2], 1, 8, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1301" + "zyts", ref is_param[2], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "zyh", ref is_param[2], 3, 0, 0);

            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "xb", ref is_param[2], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "yblx", ref is_param[2], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "shbzh", ref is_param[2], 3, 0, 0);
            ;
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1301" + "yjje", ref is_param[2], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1301" + "bjje", ref is_param[2], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1301" + "tfje", ref is_param[2], 4, 12, 2);

            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1301" + "ybtczf", ref is_param[2], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1301" + "grzhzf", ref is_param[2], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1301" + "qtybzf", ref is_param[2], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1301" + "grzfje", ref is_param[2], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "skr", ref is_param[2], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[2], "1211" + "kpr", ref is_param[2], 3, 0, 0);


            of_fpmx2skp_initial_fpmx_parameter(ref is_field[3], "0300" + "len_qtxm", ref is_param[3], 4, 4, 0);


            of_fpmx2skp_initial_fpmx_parameter(ref is_field[4], "1211" + "xmmc", ref is_param[4], 3, 0, 0);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[4], "1301" + "je", ref is_param[4], 4, 12, 2);
            of_fpmx2skp_initial_fpmx_parameter(ref is_field[4], "1301" + "grzfje", ref is_param[4], 4, 12, 2);



        }

        public void of_fpmx2skp_initial_fpmx_parameter(ref string as_field, string as_name, ref string as_parameter, int ai_lx, int ai_length, int ai_decimal)
        {
            string ls_name = "", ls_lx = "", ls_length = "", ls_decimal;
            if (string.IsNullOrEmpty(as_name))
            {
                ls_name = "";
            }
            else
            {
                ls_name = as_name;
            }
            ls_lx = ai_lx.ToString();
            ls_length = ai_length.ToString();
            ls_decimal = ai_decimal.ToString();
            as_field += ls_name.PadRight(20, ' ');
            as_parameter += ls_lx;
            as_parameter += ls_length.PadRight(3, ' ');
            as_parameter += ls_decimal.PadRight(2, ' ');

        }
        public string of_get_apptype(int ai_fplx)
        {
            string ls_reslut;
            switch (ai_fplx)
            {
                case 31:
                case 32:
                case 33:
                    ls_reslut = "门急诊收费";
                    break;
                case 34:
                case 35:
                case 36:
                    ls_reslut = "住院收费";
                    break;
                default:
                    ls_reslut = "其它";
                    break;
            }
            return ls_reslut;
        }
        public int of_backupinvo(string as_disk_no, string as_app_type, string as_rcpt_type, string adtm_rcpt_date, string as_rcpt_no, string as_invoice_no, string as_invoice_content, decimal adc_invoice_costs, string as_verify_code, string as_invoice_status, ref string as_err, ref Dictionary<string, string> idc)
        {
            string ls_sql = "select * from invoice_info where invoice_no ='" + as_invoice_no + "' and rcpt_type = '" + as_rcpt_type + "'";
            DataTable dt_inv = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_sql).Tables[0];
            if (dt_inv.Rows.Count > 0)
            {
                ls_sql = "update invoice_info set disk_no = '" + as_disk_no + "',app_type ='" + as_app_type + "',rcpt_type ='" + as_rcpt_type + "',rcpt_no ='" + as_rcpt_no + "',";
                ls_sql += "invoice_no = '" + as_invoice_no + "' ,invoice_costs =" + adc_invoice_costs + ", invoice_content = '" + as_invoice_content + "', verify_code ='" + as_verify_code + "', invoice_status = '" + as_invoice_status + "'";
                ls_sql += " where invoice_no ='" + as_invoice_no + "' and rcpt_type ='" + as_rcpt_type + "'";
                idc.Add(ls_sql, "更新票控盘信息数据表失败！");
            }
            else
            {
                ls_sql = " insert into invoice_info(disk_no, app_type, rcpt_type, rcpt_date, rcpt_no, invoice_no, invoice_costs, invoice_content, verify_code, invoice_status)";
                ls_sql += " values ('" + as_disk_no + "','" + as_app_type + "','" + as_rcpt_type + "', to_date('" + adtm_rcpt_date + "','yyyy-mm-dd hh24:mi:ss'),'" + as_rcpt_no + "','" + as_invoice_no + "'," + adc_invoice_costs + ",'" + as_invoice_content + "','" + as_verify_code + "','" + as_invoice_status + "')";
                idc.Add(ls_sql, "往数据库发票信息（invoice_info）表中存储票据信息出错！");
            }
            return 0;
        }

        public int of_makeoutinvo(int ai_fplx, string rcpt_no, ref string[] a_rcpt, ref string as_err, ref Dictionary<string, string> idc)
        {
            int bill_items_count = 18; //每张发票上打印的明细项目数量(也是传入票控盘的项目数量)
            DataSet lds_data = new DataSet();
            DataTable data1 = new DataTable();
            SetTable1(ref data1, "1");
            data1.TableName = "0";
            lds_data.Tables.Add(data1.Copy());
            data1 = new DataTable();
            SetTable1(ref data1, "0");
            data1.TableName = "1";
            lds_data.Tables.Add(data1.Copy());
            data1.TableName = "2";
            lds_data.Tables.Add(data1.Copy());
            data1.TableName = "3";
            lds_data.Tables.Add(data1.Copy());
            //费用主记录
            DataTable dt_rcpt = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select a.*,b.sex from outp_rcpt_master a,pat_master_index b where a.patient_id=b.patient_id and  rcpt_no='" + rcpt_no + "'").Tables[0];
            if (dt_rcpt.Rows.Count <= 0)
            {
                XtraMessageBox.Show("没有查询到费用主记录" + rcpt_no, "提示");
                return -1;
            }
            DataRow dr1 = lds_data.Tables[0].NewRow();
            dr1["RCPT_NO"] = rcpt_no;// 收据流水号
            dr1["HJJE"] = dt_rcpt.Rows[0]["total_costs"].ToString();  ///合计金额 
            dr1["FKDWMC"] = dt_rcpt.Rows[0]["name"].ToString(); //患者姓名，也是付款单位名称
            dr1["SFXKZBH"] = "";//lds_data[1].setitem(1,"sfxkzbh",ProfileString("rcpt.ini","RCPT_MASTER","license",""))   //收费许可证号
            dr1["DZ"] = "";// (1, "dz", ProfileString("rcpt.ini", "RCPT_MASTER", "address", ""))   //院址
            dr1["DH"] = "";// ProfileString("rcpt.ini","RCPT_MASTER","phone",""))   //电话
            dr1["MZH"] = dt_rcpt.Rows[0]["patient_id"].ToString(); //; //门诊号
            dr1["JYLSH"] = rcpt_no;//交易流水号
            string ls_charge_type = dt_rcpt.Rows[0]["charge_type"].ToString();
            dr1["RYLB"] = ls_charge_type; //人员类别
            dr1["SSJE"] = dt_rcpt.Rows[0]["total_charges"].ToString(); //实收金额
            dr1["SKR"] = dt_rcpt.Rows[0]["OPERATOR_NO"].ToString();  //收款人
            dr1["KPR"] = dt_rcpt.Rows[0]["OPERATOR_NO"].ToString();  //收款人
            dr1["YWLSH"] = rcpt_no;//业务流水号
            dr1["XB"] = dt_rcpt.Rows[0]["SEX"].ToString();
            dr1["YLJGLX"] = "综合医院"; ////医院类型
            dr1["BJJE"] = "0"; //  //现收支付     
            dr1["YTJE"] = "0"; ////找零支付
            string ls_insurance_type = dt_rcpt.Rows[0]["insurance_type"].ToString();
            //if (string.IsNullOrEmpty(ls_insurance_type))
            //{
            dr1["YBLX"] = ls_charge_type;
            //}
            //else
            //{
            //    dr1["YBLX"] = ls_insurance_type;
            //}
            //先取医保统筹支付
            string ls_tczf = "select nvl(sum(a.payment_amount-a.refunded_amount),0) tczf from outp_payments_money a ";
            ls_tczf = ls_tczf + " where a.money_type in ('医保统筹','统筹支付','市-统筹','省-统筹') and a.rcpt_no='" + rcpt_no + "'";
            DataTable dt_tc = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_tczf).Tables[0];
            string ls_qtzf = "select nvl(sum(a.payment_amount-a.refunded_amount),0) qtjj from outp_payments_money a ";
            ls_qtzf = ls_qtzf + " where a.money_type not  in ('医保帐户','帐户支付','医保统筹','统筹支付','银联','现金','趣医支付','银行卡','支票','微信','支付宝','市-账户','省-账户','市-统筹','省-统筹') and a.rcpt_no='" + rcpt_no + "'";
            DataTable dt_qtzf = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_qtzf).Tables[0];
            string ls_zhzf = "select nvl(sum(a.payment_amount-a.refunded_amount),0) qtjj from outp_payments_money a ";
            ls_zhzf = ls_zhzf + " where a.money_type   in ('医保帐户','帐户支付','市-账户','省-账户') and a.rcpt_no='" + rcpt_no + "'";
            DataTable dt_zhzf = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_zhzf).Tables[0];


            dr1["SHBZH"] = dt_rcpt.Rows[0]["insurance_no"].ToString(); //医保卡号或医保编号    
            dr1["YBTCZF"] = dt_tc.Rows[0][0].ToString();//dec(ProfileString("rcpt.ini","insurance","INSUR_PAY","")))   //医保基金支付
            dr1["QTYBZF"] = dt_qtzf.Rows[0][0].ToString();//dec(ProfileString("rcpt.ini","insurance","OTHER_INSUR_PAY","")))   //医保其他报销支付
            dr1["GRZHZF"] = dt_zhzf.Rows[0][0].ToString();//dec(ProfileString("rcpt.ini","insurance","ACCOUNT_PAY","")))   //医保支付
            dr1["TFJE"] = "0";
            //待跟踪
            decimal total_charge = 0, total_costs = 0;
            decimal ld_grzfje;
            decimal ld_charges;
            //个人自付 取现金类的支付方式
            total_charge = decimal.Parse(dt_rcpt.Rows[0]["total_charges"].ToString());
            total_costs = decimal.Parse(dt_rcpt.Rows[0]["total_costs"].ToString());
            string ls_mey = "select nvl(sum(b.payment_amount-b.refunded_amount),0) xjzf  from outp_payments_money b ";
            ls_mey = ls_mey + "where b.rcpt_no='" + rcpt_no + "' and b.money_type in ('银联','现金','趣医支付','银行卡','支票','微信','支付宝')";
            DataTable dt_money = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_mey).Tables[0];
            if (dt_money.Rows.Count > 0)
            {
                ld_grzfje = decimal.Parse(dt_money.Rows[0][0].ToString());
            }
            else
            {
                ld_grzfje = 0;
            }
            //if (ld_grzfje == total_costs)
            //{
            //    ld_grzfje = total_charge;
            //    if ((total_costs - ld_grzfje) > 0.1M)
            //    {
            //        ld_charges = total_costs - ld_grzfje;
            //        dr1["QTYBZF"] = ld_charges;
            //    }
            //    dr1["GRZFJE"] = ld_grzfje;
            //}
            //else if(ld_grzfje>0)
            //{
            dr1["GRZFJE"] = ld_grzfje;
            //}
            lds_data.Tables[0].Rows.Add(dr1); //形成一条数据 后续的增加就直接赋值了

            if (total_costs == 0)
            {
                XtraMessageBox.Show("总金额0元,禁止开票!~r~n请查明原因再开票收费!", "提示");
                return -1;
            }
            //明细信息
            //个别医院例如空总费用明细中名称、规格等字段中含有空格
            DataTable dt_bill = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select * from outp_bill_items where rcpt_no='" + rcpt_no + "'").Tables[0];

            if (dt_bill.Rows.Count <= 0)
            {
                XtraMessageBox.Show("没有查询到费用明细记录" + rcpt_no, "提示");
                return -1;
            }
            if (bill_items_count > dt_bill.Rows.Count)
            {
                bill_items_count = dt_bill.Rows.Count;
            }
            string ls_item_name, ls_item_spec, ls_amount, ld_costs, ld_charge;
            for (int i = 0; i < bill_items_count; i++)
            {
                DataRow dr2 = lds_data.Tables[1].NewRow();
                ls_item_name = dt_bill.Rows[i]["item_name"].ToString();
                ls_item_spec = dt_bill.Rows[i]["item_spec"].ToString();
                ls_amount = dt_bill.Rows[i]["amount"].ToString();
                ld_costs = dt_bill.Rows[i]["costs"].ToString();
                ld_charge = dt_bill.Rows[i]["charges"].ToString();
                ls_item_name = ls_item_name + " " + ls_item_spec;
                dr2["SFXM"] = ls_item_name;
                dr2["XMMC"] = ls_item_name;
                dr2["SL"] = ls_amount;
                dr2["JE"] = ld_costs;
                dr2["GRZFJE"] = ld_charge;
                lds_data.Tables[1].Rows.Add(dr2);
            }
            string ls_pkpbh = "", ls_fphm = "", ls_pkm = "";
            int li_state = of_makeoutinvo(ai_fplx, lds_data, ref ls_pkpbh, ref ls_fphm, ref ls_pkm, ref as_err, ref idc);
            if (li_state < 0)
            {
                return li_state;
            }
            //在发票上打印票控码等信息
            a_rcpt[0] = ls_fphm.ToString();
            a_rcpt[1] = ls_pkpbh + "-" + ls_pkm;
            a_rcpt[2] = ls_pkpbh;


            //更新发票号 b
            string ls_Sql = "Update outp_rcpt_master Set invoice_no ='" + a_rcpt[0] + "',PKPH = '" + a_rcpt[2] + "',PKM='" + a_rcpt[1] + "' Where rcpt_no = '" + rcpt_no + "'";
            idc.Add(ls_Sql, "更新票控盘发票号失败！");

            return li_state;
        }

        public int of_makereginvo(int ai_fplx, string rcpt_no, ref string[] a_rcpt, DataTable idw_bill, ref string as_err, ref Dictionary<string, string> idc)
        {
            int bill_items_count = 18; //每张发票上打印的明细项目数量(也是传入票控盘的项目数量)
            DataSet lds_data = new DataSet();
            DataTable data1 = new DataTable();
            SetTable1(ref data1, "1");
            data1.TableName = "0";
            lds_data.Tables.Add(data1.Copy());
            data1 = new DataTable();
            SetTable1(ref data1, "0");
            data1.TableName = "1";
            lds_data.Tables.Add(data1.Copy());
            data1.TableName = "2";
            lds_data.Tables.Add(data1.Copy());
            data1.TableName = "3";
            lds_data.Tables.Add(data1.Copy());
            //费用主记录
            DataTable dt_rcpt = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select a.*  from clinic_master a where a.clinic_no='" + rcpt_no + "'").Tables[0];
            if (dt_rcpt.Rows.Count <= 0)
            {
                XtraMessageBox.Show("没有查询到费用主记录" + rcpt_no, "提示");
                return -1;
            }
            string ls_rcpt_no = DateTime.Parse(dt_rcpt.Rows[0]["VISIT_DATE"].ToString()).ToString("yyyyMMdd") + dt_rcpt.Rows[0]["VISIT_NO"].ToString();
            DataRow dr1 = lds_data.Tables[0].NewRow();
            dr1["RCPT_NO"] = ls_rcpt_no;// 收据流水号
            dr1["HJJE"] = dt_rcpt.Rows[0]["clinic_charge"].ToString();  ///合计金额 
            dr1["FKDWMC"] = dt_rcpt.Rows[0]["name"].ToString(); //患者姓名，也是付款单位名称
            dr1["SFXKZBH"] = "";//lds_data[1].setitem(1,"sfxkzbh",ProfileString("rcpt.ini","RCPT_MASTER","license",""))   //收费许可证号
            dr1["DZ"] = "";// (1, "dz", ProfileString("rcpt.ini", "RCPT_MASTER", "address", ""))   //院址
            dr1["DH"] = "";// ProfileString("rcpt.ini","RCPT_MASTER","phone",""))   //电话
            dr1["MZH"] = dt_rcpt.Rows[0]["patient_id"].ToString(); //; //门诊号
            dr1["JYLSH"] = ls_rcpt_no;//交易流水号
            string ls_charge_type = dt_rcpt.Rows[0]["charge_type"].ToString();
            dr1["RYLB"] = ls_charge_type; //人员类别
            dr1["SSJE"] = dt_rcpt.Rows[0]["clinic_charge"].ToString(); //实收金额
            dr1["SKR"] = dt_rcpt.Rows[0]["operator"].ToString();  //收款人
            dr1["KPR"] = dt_rcpt.Rows[0]["operator"].ToString();
            dr1["YWLSH"] = ls_rcpt_no;//业务流水号
            dr1["XB"] = dt_rcpt.Rows[0]["SEX"].ToString();
            dr1["YLJGLX"] = "综合医院"; ////医院类型
            dr1["BJJE"] = dt_rcpt.Rows[0]["clinic_charge"].ToString(); //  //现收支付     
            dr1["YTJE"] = "0"; ////找零支付
            string ls_insurance_type = dt_rcpt.Rows[0]["insurance_type"].ToString();
            //if (string.IsNullOrEmpty(ls_insurance_type))
            //{
            dr1["YBLX"] = ls_charge_type;
            //}
            //else
            //{
            //    dr1["YBLX"] = ls_insurance_type;
            //}
            string ls_tczf = "select nvl(sum(a.payment_amount),0) xjzf  from clinic_payments_money a ";
            ls_tczf = ls_tczf + " where a.money_type in ('医保统筹','统筹支付','市-统筹','省-统筹') and a.clinic_no='" + rcpt_no + "'";
            DataTable dt_tc = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_tczf).Tables[0];
            string ls_qtzf = "select nvl(sum(a.payment_amount),0) xjzf  from clinic_payments_money a ";
            ls_qtzf = ls_qtzf + " where a.money_type not  in ('医保帐户','帐户支付','医保统筹','统筹支付','银联','现金','趣医支付','银行卡','支票','微信','支付宝','市-账户','省-账户','市-统筹','省-统筹') and a.clinic_no='" + rcpt_no + "'";
            DataTable dt_qtzf = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_qtzf).Tables[0];
            string ls_zhzf = "select nvl(sum(a.payment_amount),0) xjzf  from clinic_payments_money a";
            ls_zhzf = ls_zhzf + " where a.money_type   in ('医保帐户','帐户支付','市-账户','省-账户') and a.clinic_no='" + rcpt_no + "'";
            DataTable dt_zhzf = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_zhzf).Tables[0];


            dr1["SHBZH"] = dt_rcpt.Rows[0]["insurance_no"].ToString(); //医保卡号或医保编号    
            dr1["YBTCZF"] = dt_tc.Rows[0][0].ToString();//dec(ProfileString("rcpt.ini","insurance","INSUR_PAY","")))   //医保基金支付
            dr1["QTYBZF"] = dt_qtzf.Rows[0][0].ToString(); //dec(ProfileString("rcpt.ini","insurance","OTHER_INSUR_PAY","")))   //医保其他报销支付
            dr1["GRZHZF"] = dt_zhzf.Rows[0][0].ToString(); //dec(ProfileString("rcpt.ini","insurance","ACCOUNT_PAY","")))   //医保支付
            dr1["TFJE"] = "0";
            //待跟踪
            decimal total_charge = 0, total_costs = 0;
            decimal ld_grzfje;
            decimal ld_charges;
            //个人自付 取现金类的支付方式
            total_charge = decimal.Parse(dt_rcpt.Rows[0]["clinic_charge"].ToString());
            total_costs = decimal.Parse(dt_rcpt.Rows[0]["clinic_charge"].ToString());
            string ls_mey = "select nvl(sum(b.payment_amount),0) xjzf  from clinic_payments_money b ";
            ls_mey = ls_mey + "where b.clinic_no='" + rcpt_no + "' and b.money_type in ('银联','现金','趣医支付','银行卡','支票','微信','支付宝')";
            DataTable dt_money = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_mey).Tables[0];
            if (dt_money.Rows.Count > 0)
            {
                ld_grzfje = decimal.Parse(dt_money.Rows[0][0].ToString());
            }
            else
            {
                ld_grzfje = 0;
            }
            //if (ld_grzfje == total_costs)
            //{
            //    ld_grzfje = total_charge;
            //    if ((total_costs - ld_grzfje) > 0.1M)
            //    {
            //        ld_charges = total_costs - ld_grzfje;
            //        dr1["QTYBZF"] = ld_charges;
            //    }
            //    dr1["GRZFJE"] = ld_grzfje;
            //}
            //else if (ld_grzfje > 0)
            //{
            dr1["GRZFJE"] = ld_grzfje;
            //}
            lds_data.Tables[0].Rows.Add(dr1); //形成一条数据 后续的增加就直接赋值了

            if (total_costs == 0)
            {
                XtraMessageBox.Show("总金额0元,禁止开票!~r~n请查明原因再开票收费!", "提示");
                return -1;
            }
            //明细信息
            //个别医院例如空总费用明细中名称、规格等字段中含有空格
            DataTable dt_bill = idw_bill.Copy();
            if (dt_bill.Rows.Count <= 0)
            {
                XtraMessageBox.Show("没有查询到费用明细记录" + rcpt_no, "提示");
                return -1;
            }
            if (bill_items_count > dt_bill.Rows.Count)
            {
                bill_items_count = dt_bill.Rows.Count;
            }
            string ls_item_name, ls_item_spec, ls_amount, ld_costs, ld_charge;
            for (int i = 0; i < bill_items_count; i++)
            {
                DataRow dr2 = lds_data.Tables[1].NewRow();
                ls_item_name = dt_bill.Rows[i]["item_name"].ToString();
                ls_item_spec = dt_bill.Rows[i]["item_spec"].ToString();
                ls_amount = dt_bill.Rows[i]["amount"].ToString();
                ld_costs = dt_bill.Rows[i]["charges"].ToString();
                ld_charge = dt_bill.Rows[i]["charges"].ToString();
                ls_item_name = ls_item_name + " " + ls_item_spec;
                dr2["SFXM"] = ls_item_name;
                dr2["XMMC"] = ls_item_name;
                dr2["SL"] = ls_amount;
                dr2["JE"] = ld_costs;
                dr2["GRZFJE"] = ld_charge;
                lds_data.Tables[1].Rows.Add(dr2);
            }
            string ls_pkpbh = "", ls_fphm = "", ls_pkm = "";
            int li_state = of_makeoutinvo(ai_fplx, lds_data, ref ls_pkpbh, ref ls_fphm, ref ls_pkm, ref as_err, ref idc);
            if (li_state < 0)
            {
                return li_state;
            }
            //在发票上打印票控码等信息
            a_rcpt[0] = ls_fphm.ToString();
            a_rcpt[1] = ls_pkpbh + "-" + ls_pkm;
            a_rcpt[2] = ls_pkpbh;


            //更新发票号 b
            string ls_Sql = "Update clinic_master Set invoice_no ='" + a_rcpt[0] + "',PKPH = '"+ a_rcpt[2] + "',PKM='"+ a_rcpt[1] + "' Where clinic_no = '" + rcpt_no + "'";
            idc.Add(ls_Sql, "更新票控盘发票号失败！");

            return li_state;
        }

        public int of_makeinpinvo(int ai_fplx, string rcpt_no, ref string[] a_rcpt, ref string as_err, ref Dictionary<string, string> idc)
        {
            int bill_items_count = 18; //每张发票上打印的明细项目数量(也是传入票控盘的项目数量)
            DataSet lds_data = new DataSet();
            DataTable data1 = new DataTable();
            SetTable1(ref data1, "1");
            data1.TableName = "0";
            lds_data.Tables.Add(data1.Copy());
            data1 = new DataTable();
            SetTable1(ref data1, "0");
            data1.TableName = "1";
            lds_data.Tables.Add(data1.Copy());
            data1.TableName = "2";
            lds_data.Tables.Add(data1.Copy());
            //data1.TableName = "3";
            //lds_data.Tables.Add(data1.Copy());
            //费用主记录
            string ls_sql = "select a.*,a.costs total_costs,a.charges total_charges ,b.name,b.sex,c.charge_type,c.insurance_no,nvl(trunc(c.discharge_date_time-c.admission_date_time),1) zyts,";
            ls_sql = ls_sql + "c.insurance_type,c.next_of_kin_addr,c.next_of_kin_phone,c.inp_no,c.admission_date_time,c.discharge_date_time ,(select sum(e.payment_amount-e.refunded_amount) yjj from inp_payments_money e where e.money_type='预交金' and e.rcpt_no=a.rcpt_no ) yjj from inp_settle_master a ,pat_master_index b,pat_visit c where a.patient_id=c.patient_id ";
            ls_sql = ls_sql + " and a.visit_id=c.visit_id and  a.patient_id=b.patient_id and a.rcpt_no='" + rcpt_no + "'";
            DataTable dt_rcpt = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_sql).Tables[0];
            if (dt_rcpt.Rows.Count <= 0)
            {
                XtraMessageBox.Show("没有查询到费用主记录" + rcpt_no, "提示");
                return -1;
            }
            DataRow dr1 = lds_data.Tables[0].NewRow();
            dr1["RCPT_NO"] = rcpt_no;// 收据流水号
            dr1["HJJE"] = dt_rcpt.Rows[0]["total_costs"].ToString();  ///合计金额 
            dr1["FKDWMC"] = dt_rcpt.Rows[0]["name"].ToString(); //患者姓名，也是付款单位名称
            dr1["SFXKZBH"] = "";//lds_data[1].setitem(1,"sfxkzbh",ProfileString("rcpt.ini","RCPT_MASTER","license",""))   //收费许可证号
            dr1["DZ"] = dt_rcpt.Rows[0]["next_of_kin_addr"].ToString();// (1, "dz", ProfileString("rcpt.ini", "RCPT_MASTER", "address", ""))   //院址
            dr1["DH"] = dt_rcpt.Rows[0]["next_of_kin_phone"].ToString(); ;// ProfileString("rcpt.ini","RCPT_MASTER","phone",""))   //电话
            dr1["MZH"] = dt_rcpt.Rows[0]["patient_id"].ToString(); //; //门诊号
            dr1["BLH"] = dt_rcpt.Rows[0]["patient_id"].ToString();
            dr1["ZYH"] = dt_rcpt.Rows[0]["inp_no"].ToString();
            dr1["ZYTS"] = dt_rcpt.Rows[0]["ZYTS"].ToString();

            dr1["ZYQSSJ"] = DateTime.Parse(dt_rcpt.Rows[0]["admission_date_time"].ToString()).ToString("yyyy-MM-dd");

            dr1["ZYZZSJ"] = DateTime.Parse(dt_rcpt.Rows[0]["discharge_date_time"].ToString()).ToString("yyyy-MM-dd");
            dr1["YSJE"] = dt_rcpt.Rows[0]["yjj"].ToString();
            dr1["YJJE"] = dt_rcpt.Rows[0]["yjj"].ToString();

            dr1["JYLSH"] = rcpt_no;//交易流水号
            string ls_charge_type = dt_rcpt.Rows[0]["charge_type"].ToString();
            dr1["RYLB"] = ls_charge_type; //人员类别
            dr1["SSJE"] = dt_rcpt.Rows[0]["total_charges"].ToString(); //实收金额
            dr1["SKR"] = dt_rcpt.Rows[0]["OPERATOR_NO"].ToString();  //收款人
            dr1["KPR"] = dt_rcpt.Rows[0]["OPERATOR_NO"].ToString();
            dr1["YWLSH"] = rcpt_no;//业务流水号
            dr1["XB"] = dt_rcpt.Rows[0]["SEX"].ToString();
            dr1["YLJGLX"] = "综合医院"; ////医院类型
            dr1["BJJE"] = 0;  //  //现收支付     
            dr1["YTJE"] = "0"; ////找零支付
            string ls_insurance_type = dt_rcpt.Rows[0]["insurance_type"].ToString();
            //if (string.IsNullOrEmpty(ls_insurance_type))
            //{
            dr1["YBLX"] = ls_charge_type;
            //}
            //else
            //{
            //    dr1["YBLX"] = ls_insurance_type;
            //}
            dr1["SHBZH"] = dt_rcpt.Rows[0]["insurance_no"].ToString(); //医保卡号或医保编号   
            //先取医保统筹支付
            string ls_tczf = "select nvl(sum(a.payment_amount-a.refunded_amount),0) tczf from inp_payments_money a ";
            ls_tczf = ls_tczf + " where a.money_type in ('医保统筹','统筹支付','市-统筹','省-统筹') and a.rcpt_no='" + rcpt_no + "'";
            DataTable dt_tc = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_tczf).Tables[0];
            string ls_qtzf = "select nvl(sum(a.payment_amount-a.refunded_amount),0) qtjj from inp_payments_money a ";
            ls_qtzf = ls_qtzf + " where a.money_type not  in ('医保帐户','帐户支付','医保统筹','统筹支付','银联','现金','趣医支付','银行卡','支票','微信','支付宝','市-账户','省-账户','市-统筹','省-统筹') and a.rcpt_no='" + rcpt_no + "'";
            DataTable dt_qtzf = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_qtzf).Tables[0];
            string ls_zhzf = "select nvl(sum(a.payment_amount-a.refunded_amount),0) qtjj from inp_payments_money a ";
            ls_zhzf = ls_zhzf + " where a.money_type   in ('医保帐户','帐户支付','市-账户','省-账户') and a.rcpt_no='" + rcpt_no + "'";
            DataTable dt_zhzf = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_zhzf).Tables[0];


            dr1["YBTCZF"] = dt_tc.Rows[0][0].ToString().ToString();//dec(ProfileString("rcpt.ini","insurance","INSUR_PAY","")))   //医保基金支付
            dr1["QTYBZF"] = dt_qtzf.Rows[0][0].ToString();//dec(ProfileString("rcpt.ini","insurance","OTHER_INSUR_PAY","")))   //医保其他报销支付
            dr1["GRZHZF"] = dt_zhzf.Rows[0][0].ToString();//dec(ProfileString("rcpt.ini","insurance","ACCOUNT_PAY","")))   //医保支付
            dr1["TFJE"] = "0";
            //待跟踪
            decimal total_charge = 0, total_costs = 0;
            decimal ld_grzfje;
            decimal ld_charges;
            //个人自付 取现金类的支付方式
            total_charge = decimal.Parse(dt_rcpt.Rows[0]["total_charges"].ToString());
            total_costs = decimal.Parse(dt_rcpt.Rows[0]["total_costs"].ToString());
            string ls_mey = "select nvl(sum(b.payment_amount-b.refunded_amount),0) xjzf  from inp_payments_money b ";
            ls_mey = ls_mey + "where b.rcpt_no='" + rcpt_no + "' and b.money_type in ('银联','现金','趣医支付','银行卡','支票','微信','支付宝')";
            DataTable dt_money = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_mey).Tables[0];
            if (dt_money.Rows.Count > 0)
            {
                ld_grzfje = decimal.Parse(dt_money.Rows[0][0].ToString());
            }
            else
            {
                ld_grzfje = 0;
            }
            //if (ld_grzfje == total_costs)
            //{
            //    ld_grzfje = total_charge;
            //    if ((total_costs - ld_grzfje) > 0.1M)
            //    {
            //        ld_charges = total_costs - ld_grzfje;
            //        dr1["QTYBZF"] = ld_charges;
            //    }
            //    dr1["GRZFJE"] = ld_grzfje;
            //}
            //else if (ld_grzfje > 0)
            //{
            dr1["GRZFJE"] = ld_grzfje;
            //}
            lds_data.Tables[0].Rows.Add(dr1); //形成一条数据 后续的增加就直接赋值了

            if (total_costs == 0)
            {
                XtraMessageBox.Show("总金额0元,禁止开票!~r~n请查明原因再开票收费!", "提示");
                return -1;
            }
            //明细信息
            //个别医院例如空总费用明细中名称、规格等字段中含有空格
            DataTable dt_bill = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select * from inp_settle_detail a where a.rcpt_no='" + rcpt_no + "'").Tables[0];
            if (dt_bill.Rows.Count <= 0)
            {
                XtraMessageBox.Show("没有查询到费用明细记录" + rcpt_no, "提示");
                return -1;
            }
            if (bill_items_count > dt_bill.Rows.Count)
            {
                bill_items_count = dt_bill.Rows.Count;
            }
            string ls_item_name, ls_item_spec, ls_amount, ld_costs, ld_charge;
            for (int i = 0; i < bill_items_count; i++)
            {
                DataRow dr2 = lds_data.Tables[1].NewRow();
                ls_item_name = dt_bill.Rows[i]["fee_class_name"].ToString();
                ld_costs = dt_bill.Rows[i]["costs"].ToString();
                ld_charge = dt_bill.Rows[i]["payments"].ToString();
                dr2["SFXM"] = ls_item_name;
                dr2["XMMC"] = ls_item_name;
                dr2["JE"] = ld_costs;
                dr2["GRZFJE"] = ld_charge;
                lds_data.Tables[1].Rows.Add(dr2);
            }
            string ls_pkpbh = "", ls_fphm = "", ls_pkm = "";
            int li_state = of_makeoutinvo(ai_fplx, lds_data, ref ls_pkpbh, ref ls_fphm, ref ls_pkm, ref as_err, ref idc, "1");
            if (li_state < 0)
            {
                return li_state;
            }
            //在发票上打印票控码等信息
            a_rcpt[0] = ls_fphm.ToString();
            a_rcpt[1] = ls_pkpbh + "-" + ls_pkm;
            a_rcpt[2] = ls_pkpbh;


            //更新发票号 b
            string ls_Sql = "Update inp_settle_master Set invoice_no ='" + a_rcpt[0] + "',PKPH = '" + a_rcpt[2] + "',PKM='" + a_rcpt[1] + "' Where rcpt_no = '" + rcpt_no + "'";
            idc.Add(ls_Sql, "更新主记录票控盘发票号失败！");

            return li_state;
        }

        public void SetTable1(ref DataTable dt_new, string flag)
        {
            try
            {
                if (flag.Equals("1"))
                {
                    dt_new.Columns.Add("RCPT_NO"); //收据流水号
                    dt_new.Columns.Add("HJJE"); //合计金额
                    dt_new.Columns.Add("FKDWMC");//患者姓名，也是付款单位名称
                    dt_new.Columns.Add("SFXKZBH"); //收费许可证号
                    dt_new.Columns.Add("DZ");  //院址
                    dt_new.Columns.Add("DH"); //电话
                    dt_new.Columns.Add("MZH"); //门诊号
                    dt_new.Columns.Add("JYLSH"); //交易流水号
                    dt_new.Columns.Add("RYLB");  //人员类别
                    dt_new.Columns.Add("SSJE"); //实收金额
                    dt_new.Columns.Add("SKR"); //收款人
                    dt_new.Columns.Add("YWLSH");//业务流水号
                    dt_new.Columns.Add("XB");   //性别
                    dt_new.Columns.Add("YLJGLX"); //医院类型
                    dt_new.Columns.Add("BJJE"); //现收支付  
                    dt_new.Columns.Add("YTJE"); //找零支付
                    dt_new.Columns.Add("YBLX");//医保类别
                    dt_new.Columns.Add("SHBZH"); //医保卡号或医保编号
                    dt_new.Columns.Add("YBTCZF"); //医保基金支付
                    dt_new.Columns.Add("QTYBZF"); //医保其他报销支付
                    dt_new.Columns.Add("GRZHZF"); //医保支付
                    dt_new.Columns.Add("TFJE");  //应退金额

                    dt_new.Columns.Add("GRZFJE"); //个人支付
                    dt_new.Columns.Add("CWBMDM");  //财务部门代码
                    dt_new.Columns.Add("CWBMMC");//财务部门名称
                    dt_new.Columns.Add("SKDWDM"); //收费单位代码
                    dt_new.Columns.Add("SKDWMC"); //收费单位名称
                    dt_new.Columns.Add("FPLX"); //发票类型
                    dt_new.Columns.Add("JQBH"); //票控盘编号
                    dt_new.Columns.Add("JDHM"); //发票号码
                    dt_new.Columns.Add("KPRQ"); //开票日期
                    dt_new.Columns.Add("DKBZ"); //开票日期

                    dt_new.Columns.Add("XMMC");
                    dt_new.Columns.Add("BBH1");
                    dt_new.Columns.Add("FKDWDM");
                    dt_new.Columns.Add("HSRQ");
                    dt_new.Columns.Add("ZFRQ");
                    dt_new.Columns.Add("ZFSJ");
                    dt_new.Columns.Add("ZFR");
                    dt_new.Columns.Add("ZFSY");
                    dt_new.Columns.Add("TPHM");
                    dt_new.Columns.Add("FB");
                    dt_new.Columns.Add("ZYBLH");
                    dt_new.Columns.Add("ZJF");
                    dt_new.Columns.Add("SXF");
                    dt_new.Columns.Add("SYF");
                    dt_new.Columns.Add("CLF");
                    dt_new.Columns.Add("HCF");
                    dt_new.Columns.Add("CTF");
                    dt_new.Columns.Add("BCF");
                    dt_new.Columns.Add("QTF");
                    dt_new.Columns.Add("SSF");
                    dt_new.Columns.Add("ZLF");
                    dt_new.Columns.Add("FSF");
                    dt_new.Columns.Add("HYF");
                    dt_new.Columns.Add("JCF");
                    dt_new.Columns.Add("ZCF");
                    dt_new.Columns.Add("ZCY");
                    dt_new.Columns.Add("ZCHY");
                    dt_new.Columns.Add("XYA");
                    dt_new.Columns.Add("SFJD");
                    dt_new.Columns.Add("HLF");
                    dt_new.Columns.Add("CWF");
                    dt_new.Columns.Add("YSJE");
                    dt_new.Columns.Add("XSJE");
                    dt_new.Columns.Add("TKJE");
                    dt_new.Columns.Add("XMJE1");
                    dt_new.Columns.Add("XMJE2");
                    dt_new.Columns.Add("FHR");
                    dt_new.Columns.Add("KPR");
                    dt_new.Columns.Add("NAME1");
                    dt_new.Columns.Add("ZYH");
                    dt_new.Columns.Add("ZYRQ");
                    dt_new.Columns.Add("CYRQ");
                    dt_new.Columns.Add("JZRQ");
                    dt_new.Columns.Add("BZ");
                    dt_new.Columns.Add("SJJE");
                    dt_new.Columns.Add("JKR");
                    dt_new.Columns.Add("ZPHM");
                    dt_new.Columns.Add("BLH");
                    dt_new.Columns.Add("KPSJ");
                    dt_new.Columns.Add("SL");
                    dt_new.Columns.Add("ZYQSSJ");
                    dt_new.Columns.Add("ZYZZSJ");
                    dt_new.Columns.Add("ZYTS");
                    dt_new.Columns.Add("YJJE");

                }
                else
                {
                    dt_new.Columns.Add("SFXM");
                    dt_new.Columns.Add("XMMC");
                    dt_new.Columns.Add("SL");
                    dt_new.Columns.Add("JE");
                    dt_new.Columns.Add("GRZFJE");
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("" + ex.Message, "异常");
                return;
            }
        }
        public Boolean of_switch_fpmx(int ai_fplx, ref DataSet ads_data, string as_dkbz, ref string as_fpmx, string flag = "0")
        {
            int ll_begin, ll_end;
            int ll_row, ll_rowcount;
            string ls_field, ls_param;
            string ldt_rq = "", ls_str = "", ldc_sz = "";
            string ls_qtxm_field, ls_qtxm_parameter;
            if (flag.Equals("0")) //门诊
            {
                of_initial_parameter();
            }
            else
            {
                of_initial_parameter_inp();
            }

            ll_begin = 1;
            ll_end = ads_data.Tables.Count;
            if (ll_end <= 0) return false;
            as_fpmx = "";
            ll_row = ads_data.Tables[0].Rows.Count;
            int li_x = 0;
            while (strToByteLen(is_field[0]) > 0)
            {
                ls_field = strToByteLeft(is_field[0], 0, 20);

                ls_param = strToByteLeft(is_param[0], 0, 6);
                li_x++;
                DataTable dt = ads_data.Tables[0].Copy();
                int li_field = is_field[0].Length;
                of_write_readdata(ref dt, ll_row - 1, ls_field, ref ldt_rq, ref ls_str, ref ldc_sz, as_dkbz);
                as_fpmx += of_write_switchdata(ls_field, ls_param, ldt_rq, ls_str, ldc_sz);
                int li_mx = as_fpmx.Length;
                is_field[0] = strToByteLeft(is_field[0], 20, strToByteLen(is_field[0]) - 20);
                li_field = is_field[0].Length;
                is_param[0] = strToByteLeft(is_param[0], 6, strToByteLen(is_param[0]) - 6);
                int li_ll = is_param[0].Length;
            }
            ll_row = ads_data.Tables[0].Rows.Count;
            while (strToByteLen(is_field[1]) > 0)
            {
                ls_field = strToByteLeft(is_field[1], 0, 20);
                ls_param = strToByteLeft(is_param[1], 0, 6);
                DataTable dt = ads_data.Tables[0].Copy();
                of_write_readdata(ref dt, ll_row - 1, ls_field, ref ldt_rq, ref ls_str, ref ldc_sz, as_dkbz);
                as_fpmx += of_write_switchdata(ls_field, ls_param, ldt_rq, ls_str, ldc_sz);
                is_field[1] = strToByteLeft(is_field[1], 20, strToByteLen(is_field[1]) - 20);
                is_param[1] = strToByteLeft(is_param[1], 6, strToByteLen(is_param[1]) - 6);
            }
            ll_row = ads_data.Tables[0].Rows.Count;
            int li_e = 0;
            while (strToByteLen(is_field[2]) > 0)
            {
                li_e++;
                ls_field = strToByteLeft(is_field[2], 0, 20);
                ls_param = strToByteLeft(is_param[2], 0, 6);
                DataTable dt = ads_data.Tables[0].Copy();
                of_write_readdata(ref dt, ll_row - 1, ls_field, ref ldt_rq, ref ls_str, ref ldc_sz, as_dkbz);
                as_fpmx += of_write_switchdata(ls_field, ls_param, ldt_rq, ls_str, ldc_sz);
                is_field[2] = strToByteLeft(is_field[2], 20, strToByteLen(is_field[2]) - 20);
                is_param[2] = strToByteLeft(is_param[2], 6, strToByteLen(is_param[2]) - 6);

            }

            for (int ll_i = 1; ll_i < ll_end; ll_i++)
            {
                ll_rowcount = 0;
                ls_field = strToByteLeft(is_field[(ll_i + 1) * 2 - 1], 0, 20);
                ls_param = strToByteLeft(is_param[(ll_i + 1) * 2 - 1], 0, 6);
                ll_rowcount = ads_data.Tables[ll_i].Rows.Count;
                ls_str = "";
                ldc_sz = ll_rowcount.ToString();
                as_fpmx += of_write_switchdata(ls_field, ls_param, ldt_rq, ls_str, ldc_sz);//改到放到下面判断内
                for (int ll_j = 0; ll_j < ll_rowcount; ll_j++)
                {
                    ls_qtxm_field = is_field[(ll_i + 1) * 2];
                    ls_qtxm_parameter = is_param[(ll_i + 1) * 2];
                    while (strToByteLen(ls_qtxm_field) > 0)
                    {
                        ls_field = strToByteLeft(ls_qtxm_field, 0, 20);
                        ls_param = strToByteLeft(ls_qtxm_parameter, 0, 6);
                        DataTable dt = ads_data.Tables[ll_i].Copy();
                        of_write_readdata(ref dt, ll_j, ls_field, ref ldt_rq, ref ls_str, ref ldc_sz, as_dkbz);
                        as_fpmx += of_write_switchdata(ls_field, ls_param, ldt_rq, ls_str, ldc_sz);
                        ls_qtxm_field = strToByteLeft(ls_qtxm_field, 20, strToByteLen(ls_qtxm_field) - 20);
                        ls_qtxm_parameter = strToByteLeft(ls_qtxm_parameter, 6, strToByteLen(ls_qtxm_parameter) - 6);
                    }
                }
            }

            return true;
        }

        public string of_write_switchdata(string as_column, string as_parameter, string adt_rq, string as_str, string adc_sz)
        {
            string ls_len, ls_lx, ls_length, ls_decimal;
            string ls_rtn = "", ls_temp;
            int li_length, li_decimal, li_temp;
            ls_len = strToByteLeft(as_column, 2, 1);
            ls_lx = strToByteLeft(as_parameter, 0, 1);
            ls_length = strToByteLeft(as_parameter, 1, 3);
            ls_decimal = strToByteRight(as_parameter, 3);
            if (string.IsNullOrEmpty(ls_length)) ls_length = "0";
            li_length = int.Parse(ls_length);
            if (string.IsNullOrEmpty(ls_decimal)) ls_decimal = "0";
            li_decimal = int.Parse(ls_decimal);

            if (ls_lx.Equals("1"))
            {
                if (string.IsNullOrEmpty(adt_rq))
                {
                    ls_rtn = "00000000";
                }
                else
                {
                    ls_rtn = adt_rq.Substring(0, 4) + adt_rq.Substring(5, 2) + adt_rq.Substring(8, 2);
                }
            }
            else if (ls_lx.Equals("2"))
            {
                if (string.IsNullOrEmpty(adt_rq))
                {
                    ls_rtn = "000000";
                }
                else
                {
                    ls_rtn = adt_rq.Substring(11, 2) + adt_rq.Substring(14, 2) + adt_rq.Substring(17, 2);
                }
            }
            else if (ls_lx.Equals("3"))
            {
                if (string.IsNullOrEmpty(as_str))
                {
                    as_str = "";
                }
                if (ls_len.Equals("1"))
                {
                    li_length = strToByteLen(as_str) * 2;
                }
                ls_rtn = of_switch_asc2hex(as_str, li_length);
            }
            else if (ls_lx.Equals("4"))
            {
                if (string.IsNullOrEmpty(adc_sz))
                {
                    adc_sz = "0";
                }
                ls_rtn = of_switch_dec2hex(adc_sz, li_decimal, li_length);
            }
            else if (ls_lx.Equals("5"))
            {
                if (string.IsNullOrEmpty(as_str)) as_str = "";
                ls_rtn = as_str.PadRight(li_length, ' ');//补位空格

            }
            else if (ls_lx.Equals("6"))
            {
                if (string.IsNullOrEmpty(as_str)) as_str = "";
                ls_rtn = as_str;
                while (strToByteLen(ls_rtn) < li_length)
                {
                    ls_rtn = '0' + ls_rtn;
                }
            }
            else if (ls_lx.Equals("7"))
            {
                if (string.IsNullOrEmpty(as_str)) as_str = "";
                li_temp = li_length - 1 - strToByteLen(as_str);
                ls_rtn = "";
                while (strToByteLen(ls_rtn) < li_temp)
                {
                    ls_rtn = '0' + ls_rtn;
                }
                ls_rtn = (of_switch_dec2hex(li_temp.ToString(), 0, 1)) + ls_rtn + as_str;
            }
            else if (ls_lx.Equals("8"))
            {
                if (string.IsNullOrEmpty(as_str)) as_str = "";
                ls_rtn = as_str;
                while (strToByteLen(ls_rtn) < li_length)
                {
                    ls_rtn = '0' + ls_rtn;
                }
            }
            else if (ls_lx.Equals("9"))
            {
                if (string.IsNullOrEmpty(as_str)) as_str = "";
                ls_rtn = as_str;
                while (strToByteLen(ls_rtn) < li_length)
                {
                    ls_rtn = '0' + ls_rtn;
                }
            }

            if (strToByteLeft(as_column, 3, 1).Equals("1"))
            {
                ls_rtn = of_switch_dec2hex((strToByteLen(ls_rtn) / 2).ToString(), 0, 2) + ls_rtn;
            }
            return ls_rtn;
        }

        public string of_switch_dec2hex(string adc_number, int ai_decimal, int al_len)
        {
            string ls_asc = "";
            decimal ldc_number, ldc_dec;
            int ll_len, li_decimal;
            if (string.IsNullOrEmpty(adc_number))
            {
                ldc_number = 0;
            }
            else
            {
                ldc_number = decimal.Parse(adc_number);
            }
            li_decimal = ai_decimal;
            ll_len = al_len;
            ldc_dec = 1;
            for (int li_i = 0; li_i < li_decimal; li_i++)
            {
                ldc_dec = ldc_dec * 10;
            }

            ls_asc = Math.Round(ldc_number * ldc_dec, 0, MidpointRounding.AwayFromZero).ToString().Trim();
            StringBuilder ls_hex = new StringBuilder(ll_len);
            Int2Hex(ls_asc, ll_len, ls_hex);
            if (string.IsNullOrEmpty(ls_hex.ToString()))
            {
                ls_hex.Append("");
            }
            return ls_hex.ToString();
        }
        public string of_switch_asc2hex(string as_asc, int al_len)
        {
            string ls_acs;
            StringBuilder ls_hex;
            int ll_len;
            if (string.IsNullOrEmpty(as_asc))
            {
                ls_acs = "";
            }
            else
            {
                ls_acs = as_asc.Trim();
            }
            ll_len = al_len;
            ls_hex = new StringBuilder(ll_len);
            Asc2Hex(ls_acs, ll_len, ls_hex);
            if (string.IsNullOrEmpty(ls_hex.ToString())) ls_hex.Append("");

            return ls_hex.ToString();
        }
        public void of_write_readdata(ref DataTable ads_data, int al_row, string as_column, ref string adt_rq, ref string as_str, ref string adc_sz, string as_dkbz)
        {
            string ls_zt, ls_lx, ls_column;
            ls_zt = strToByteLeft(as_column, 0, 1);
            ls_lx = strToByteLeft(as_column, 1, 1);
            ls_column = strToByteLeft(as_column, 4, as_column.Length - 5).Trim();
            if (ls_column.Equals("qtxx_sz0"))
            {
                ls_zt = "1";
                ls_column = is_qtxx_sz0;
            }
            else if (ls_column.Equals("qtxx_sz"))
            {
                adc_sz = "0";
                return;
            }
            else if (ls_column.Equals("fplx"))
            {
                adc_sz = ii_fplx.ToString();
                return;
            }
            else if (ls_column.Equals("bbh1"))
            {
                as_str = is_bbh1;
                return;
            }
            else if (ls_column.Equals("bbh2"))
            {
                as_str = is_bbh2;
                return;
            }
            else if (ls_column.Equals("qtxx_str"))
            {
                as_str = "";
                return;
            }
            if (ls_zt.Equals("1"))
            {
                if (ls_lx.Equals("1"))
                {
                    adt_rq = ads_data.Rows[al_row][ls_column].ToString();
                }
                else if (ls_lx.Equals("2"))
                {
                    as_str = ads_data.Rows[al_row][ls_column].ToString();
                }
                else if (ls_lx.Equals("3"))
                {
                    adc_sz = ads_data.Rows[al_row][ls_column].ToString();
                    if (string.IsNullOrEmpty(adc_sz)) adc_sz = "0";
                    adc_sz = Math.Abs(decimal.Parse(adc_sz)).ToString();
                }
            }
            else
            {
                DateTime ld_d = new NM_Service.NMService.ServerPublicClient().GetSysDate();
                adt_rq = ld_d.ToString("yyyy-MM-dd 00:00:00");
                as_str = "";
                adc_sz = "0";
            }

            return;
        }

        //字符串转成字节串
        public string strToByteLeft(string ls_str, int startpos, int ll_len)
        {
            string return_str = "";
            Encoding end = Encoding.GetEncoding("GBK");

            byte[] lb_old = end.GetBytes(ls_str);
            if (lb_old.Length == 0) return "";
            byte[] lb_new = new byte[256];
            lb_new = lb_old;
            return_str = end.GetString(lb_new, startpos, ll_len);

            //  System.Array.Copy(lb_new, startpos, lb_desc, 0, ll_len);

            //return_str = Encoding.Default.GetString(lb_desc).Trim();

            return return_str;
        }
        public string strToByteRight(string ls_str, int ll_len)
        {
            string return_str = "";
            Encoding end = Encoding.GetEncoding("GBK");

            byte[] lb_old = end.GetBytes(ls_str);
            if (lb_old.Length == 0) return "";
            byte[] lb_new = new byte[256];
            lb_new = lb_old;

            return_str = end.GetString(lb_new, lb_new.Length - ll_len, ll_len);

            //  System.Array.Copy(lb_new, startpos, lb_desc, 0, ll_len)

            return return_str;
        }
        public int strToByteLen(string ls_str)
        {
            byte[] lb_new = Encoding.GetEncoding("GBK").GetBytes(ls_str);

            return lb_new.Length;
        }


        public int of_get_invono(ref string as_invono)
        {
            StringBuilder ls_temp = new StringBuilder(1048576);
            string ls_err = "";
            int li_state = QueryBuyInvo(is_pkpbh, ls_temp);
            if (of_getcurstatus(is_pkpbh, "QueryBuyInvo", "查询票据领购信息", li_state, ref ls_err) == false)
            {
                as_invono = ls_err;
                return li_state;
            }
            as_invono = ls_temp.ToString().Substring(int.Parse(ii_len_jddm.ToString()), int.Parse(ii_len_jdhm.ToString()));
            int li_len = int.Parse((ii_len_jddm + ii_len_jdhm).ToString());
            string ls_hex = ls_temp.ToString().Substring(li_len, 8).ToString();
            int ll_syfpzs = int.Parse(of_switch_hex2dec(ls_hex, 0).ToString());
            if (ll_syfpzs == 0)
            {
                as_invono = "没有可以使用的票据，请购买票据！";
                return -1;
            }




            return 0;
        }

        public int of_get_invono(int ai_fplx, ref string as_invono, ref string as_err)
        {
            int li_state;

            //打开票控盘
            li_state = of_open(ai_fplx, ref is_pkpbh, ref as_err);
            if (li_state < 0) return li_state;

            StringBuilder ls_fpdxx = new StringBuilder(65535);
            string ls_fpdxx1 = "";
            string ls_wkfpdm = "";

            li_state = of_get_invono(ref as_invono);
            if (li_state < 0)
            {
                as_err = as_invono;
            }

            //关闭
            li_state = CloseDisk(is_pkpbh);
            if (of_getcurstatus(is_pkpbh, "关闭票控盘", "", li_state, ref as_err)) return li_state;


            return li_state;
        }
        public int of_open(int ai_fplx, ref string as_pkpbh, ref string as_err)
        {

            try
            {

                //获取当前连接的票控盘号
                StringBuilder ls_temp = new StringBuilder(65536);
                int li_state = GetDiskNo(0, ls_temp);
                if (of_getcurstatus(is_pkpbh, "GetDiskNo", "遍历票控盘编号", li_state, ref as_err) == false)
                    return li_state;
                is_pkpbh = strToByteLeft(ls_temp.ToString(), 0, 12);
                as_pkpbh = is_pkpbh;

                //打开
                ls_temp = new StringBuilder(65536);
                li_state = OpenDisk(is_pkpbh, ls_temp);
                if (of_getcurstatus(is_pkpbh, "OpenDisk", "打开票控盘", li_state, ref as_err) == false)
                    return li_state;

                //选择应用
                li_state = SelectApp(is_pkpbh, 3);
                if (of_getcurstatus(is_pkpbh, "selectapp", "选择应用类型", li_state, ref as_err) == false)
                    return li_state;

                //输入口令
                string ls_temp1 = "88888888";
                li_state = CheckPIN(is_pkpbh, ls_temp1);
                if (of_getcurstatus(is_pkpbh, "SelectInvoType", "认证使用口令", li_state, ref as_err) == false)
                    return li_state;

                //选择票据类型
                li_state = SelectInvoType(is_pkpbh, ai_fplx);
                if (of_getcurstatus(is_pkpbh, "SelectInvoType", "选择票据类型", li_state, ref as_err) == false)
                    return li_state;
                ls_temp = new StringBuilder(65536);

                li_state = QueryInvoTemple(is_pkpbh, ls_temp);
                //XtraMessageBox.Show("" + li_state + ls_temp.ToString(), "提示");
                if (of_getcurstatus(is_pkpbh, "QueryInvoTemple", "查询当前票据模板", li_state, ref as_err) == false)
                    return li_state;

                ii_len_jddm = of_switch_hex2dec(strToByteLeft(ls_temp.ToString(), 14, 2), 0) * 2;
                ii_len_jdhm = of_switch_hex2dec(strToByteLeft(ls_temp.ToString(), 22, 2), 0) * 2;
                ii_len_tpdm = of_switch_hex2dec(strToByteLeft(ls_temp.ToString(), 30, 2), 0) * 2;
                ii_len_tphm = of_switch_hex2dec(strToByteLeft(ls_temp.ToString(), 38, 2), 0) * 2;

                //获取管理信息
                ls_temp = new StringBuilder(65536);
                li_state = ReadManageData(is_pkpbh, 2, ls_temp);
                if (of_getcurstatus(is_pkpbh, "ReadManageData", "读管理信息", li_state, ref as_err) == false)
                    return li_state;
                is_kpdwdm = of_switch_hex2asc(strToByteLeft(ls_temp.ToString(), 18, 40)).Trim();

                ls_temp = new StringBuilder(65536);
                li_state = ReadManageData(is_pkpbh, 3, ls_temp);
                is_kpdwmc = of_switch_hex2asc(strToByteLeft(ls_temp.ToString(), 182, 160));

                is_cwbmdm = strToByteLeft(ls_temp.ToString(), 6, 16);
                int li_dw = int.Parse(of_switch_hex2dec(strToByteLeft(is_cwbmdm, 0, 1), 0).ToString());
                is_cwbmdm = strToByteRight(is_cwbmdm, 15 - li_dw);
                is_cwbmmc = of_switch_hex2asc(strToByteLeft(ls_temp.ToString(), 22, 160));

            }
            catch (Exception ex)
            {
                as_err = ex.Message;
                return -1;
            }
            return 0;
        }

        public int of_get_repairflag(string as_pkpbh, ref string as_err)
        {
            StringBuilder ls_fpdxx = new StringBuilder(65535);
            string ls_fpdxx1 = "";
            string ls_wkfpdm = "";
            int li_state = QueryBuyInvo(as_pkpbh, ls_fpdxx);
            ls_fpdxx1 = ls_fpdxx.ToString();
            if (of_getcurstatus(is_pkpbh, "querybuyinvo", "查询票据领购信息", li_state, ref as_err) == false)
                return li_state;
            int li_len_jddm = int.Parse(ii_len_jddm.ToString());
            int li_len_jdhm = int.Parse(ii_len_jdhm.ToString());
            if (ii_len_jddm != 0) ls_wkfpdm = ls_fpdxx1.Substring(0, li_len_jddm);
            if (ii_len_jddm != 0) ls_fpdxx1 = ls_fpdxx1.Substring(li_len_jddm, ls_fpdxx1.Length - li_len_jddm);
            string ls_wkinvono = ls_fpdxx1.Substring(0, li_len_jdhm);
            ls_fpdxx1 = ls_fpdxx1.Substring(li_len_jdhm, ls_fpdxx1.Length - li_len_jdhm);
            string ls_syfpzs = ls_fpdxx1.Substring(0, 8);
            ls_fpdxx1 = ls_fpdxx1.Substring(8, ls_fpdxx1.Length - 8);
            ls_syfpzs = of_switch_hex2dec(ls_syfpzs, 0).ToString("###,##0");
            if (ls_syfpzs.Equals("0"))
            {
                ls_wkfpdm = "票据已用完";
                ls_wkinvono = "票据已用完";
            }
            string ls_fpds = ls_fpdxx1.Substring(0, 2);
            ls_fpdxx1 = ls_fpdxx1.Substring(2, ls_fpdxx1.Length - 2);
            int li_fpds = int.Parse(of_switch_hex2dec(ls_fpds, 0).ToString());
            string ls_bfbz, ls_lgrq, ls_lgry, ls_yxrq, ls_fpfs, ls_fpdm, ls_invono, ls_fpsys;
            decimal ldc_temp1, ldc_temp2;
            for (int li_i = 0; li_i < li_fpds; li_i++)
            {

                ls_bfbz = ls_fpdxx1.Substring(0, 8);
                ls_fpdxx1 = ls_fpdxx1.Substring(8, ls_fpdxx1.Length - 8);

                ls_lgrq = ls_fpdxx1.Substring(0, 8);
                ls_fpdxx1 = ls_fpdxx1.Substring(8, ls_fpdxx1.Length - 8);

                ls_lgry = ls_fpdxx1.Substring(0, 40);
                ls_fpdxx1 = ls_fpdxx1.Substring(40, ls_fpdxx1.Length - 40);

                ls_yxrq = ls_fpdxx1.Substring(0, 8);
                ls_fpdxx1 = ls_fpdxx1.Substring(8, ls_fpdxx1.Length - 8);

                ls_fpfs = ls_fpdxx1.Substring(0, 8);
                ls_fpdxx1 = ls_fpdxx1.Substring(8, ls_fpdxx1.Length - 8);

                ls_fpdm = ls_fpdxx1.Substring(0, li_len_jddm);
                ls_fpdxx1 = ls_fpdxx1.Substring(li_len_jddm, ls_fpdxx1.Length - li_len_jddm);

                ls_invono = ls_fpdxx1.Substring(0, li_len_jdhm);
                ls_fpdxx1 = ls_fpdxx1.Substring(li_len_jdhm, ls_fpdxx1.Length - li_len_jdhm);

                ls_fpsys = ls_fpdxx1.Substring(0, 8);
                ls_fpdxx1 = ls_fpdxx1.Substring(8, ls_fpdxx1.Length - 8);

                ls_lgrq = ls_lgrq.Substring(0, 4) + "-" + ls_lgrq.Substring(4, 2) + "-" + ls_lgrq.Substring(6, 2);

                ls_lgry = of_switch_hex2asc(ls_lgry);

                ls_yxrq = ls_yxrq.Substring(0, 4) + "-" + ls_yxrq.Substring(4, 2) + "-" + ls_yxrq.Substring(6, 2);
                ls_fpfs = of_switch_hex2dec(ls_fpfs, 0).ToString("###,##0");
                ls_fpsys = of_switch_hex2dec(ls_fpsys, 0).ToString("###,##0");
                ldc_temp1 = decimal.Parse(ls_invono);
                ldc_temp2 = decimal.Parse(ls_fpfs);

                ls_lgry = (ldc_temp1 + ldc_temp2 - 1).ToString();
                ls_lgry = ls_lgry.PadLeft(int.Parse((ii_len_jdhm - ls_lgry.Length).ToString()), '0');
                ls_bfbz = ls_bfbz.Substring(2, 2);
                if (ls_bfbz.Equals("01"))
                {
                    return 1;
                }
            }
            return 0;
        }

        public string of_switch_hex2asc(string as_hex)
        {
            StringBuilder ls_asc;
            string ls_hex;
            if (string.IsNullOrEmpty(as_hex))
            {
                ls_hex = "";
            }
            else
            {
                ls_hex = as_hex.Trim();
            }
            ls_asc = new StringBuilder(ls_hex.Length * 2);
            Hex2Asc(ls_hex, ls_asc);
            if (string.IsNullOrEmpty(ls_asc.ToString()))
            {
                ls_asc.Append("");
            }
            else
            {
                ls_asc.ToString().Trim();
            }

            return ls_asc.ToString().Trim();
        }

        public decimal of_switch_hex2dec(string as_hex, int ai_decimal)
        {
            StringBuilder ls_asc;
            string ls_hex;
            int li_decimal, li_i;
            decimal ldc_dec, ldc_number;
            if (string.IsNullOrEmpty(as_hex))
            {
                ls_hex = "";
            }
            else
            {
                ls_hex = as_hex.Trim();
            }

            if (string.IsNullOrEmpty(ai_decimal.ToString()))
            {
                li_decimal = 0;
            }
            else
            {
                li_decimal = ai_decimal;
            }

            ls_asc = new StringBuilder(ls_hex.Length * 2);
            Hex2Int(ls_hex, ls_asc);
            if (string.IsNullOrEmpty(ls_asc.ToString()))
            {
                ldc_number = 0;
            }
            else
            {
                ldc_dec = 1;
                for (li_i = 0; li_i < li_decimal; li_i++)
                {
                    ldc_dec = ldc_dec * 10;
                }
                ldc_number = decimal.Parse(ls_asc.ToString()) / ldc_dec;
            }

            return ldc_number;
        }



        public Boolean of_getcurstatus(string as_pkpbh, string as_cmd, string as_desc, int ai_state, ref string as_err)
        {

            StringBuilder ls_temp;
            switch (ai_state)
            {
                case 0:
                    return true;
                case -1:
                    switch (as_cmd)
                    {
                        case "GETDISKNO":
                        case "OPENDISK":
                            as_err = as_desc + "(" + as_cmd + ")" + "无票控(管理)(-1)";

                            return false;
                        default:
                            ls_temp = new StringBuilder(2000);
                            if (GetCurStatus(as_pkpbh, ls_temp) == 0)
                            {
                                if (string.IsNullOrEmpty(ls_temp.ToString().Trim()) || ls_temp.ToString().Equals("0000000000000000"))
                                {
                                    as_err = as_desc + "(" + as_cmd + ")" + "操作失败，返回错误代码(-1)";
                                    return false;
                                }
                                else
                                {
                                    as_err = as_desc + "(" + as_cmd + ")" + "操作失败:" + ls_temp.ToString();
                                    return false;
                                }
                            }
                            else
                            {
                                as_err = as_desc + "(" + as_cmd + ")" + "操作失败，返回错误代码(-1)";
                                return false;
                            }
                    }
                case -2:
                    switch (as_cmd.ToUpper())
                    {
                        case "GETDISKNO":
                        case "OPENDISK":
                            as_err = as_desc + "(" + as_cmd + ")" + "非票控(管理)盘(-2)";
                            return false;
                        default:
                            as_err = as_desc + "(" + as_cmd + ")" + "通讯错误(-2)";
                            return false;
                    }
                case -3:
                    switch (as_cmd.ToUpper())
                    {
                        case "ReadManageData":
                        case "MakeoutInvo":
                        case "QueryBuyInvo":
                        case "ReadInvoDetail":
                            as_err = as_desc + "(" + as_cmd + ")" + "输入参数错误或解析返回值错误(-3)";
                            return false;
                        default:
                            as_err = as_desc + "(" + as_cmd + ")" + "输入参数错误(-3)";
                            return false;
                    }
                case -4:
                    ls_temp = new StringBuilder(2000);
                    if (GetCurStatus(as_pkpbh, ls_temp) == 0)
                    {
                        if (string.IsNullOrEmpty(ls_temp.ToString().Trim()) || ls_temp.ToString().Equals("0000000000000000"))
                        {
                            as_err = as_desc + "(" + as_cmd + ")" + "操作失败，返回错误代码(-4)";
                            return false;
                        }
                        else
                        {
                            as_err = as_desc + "(" + as_cmd + ")" + "操作失败:" + ls_temp.ToString();
                            return false;
                        }
                    }
                    else
                    {
                        as_err = as_desc + "(" + as_cmd + ")" + "操作失败，返回错误代码(-4)";
                        return false;
                    }
                default:
                    as_err = as_desc + "(" + as_cmd + ")" + "操作失败，返回错误代码(-4)";
                    return false;
            }
        }

        //获取发票类型
        public string of_get_invotypes(ref string as_err)
        {
            int li_state;
            StringBuilder ls_temp = new StringBuilder(65536);

            //获取当前连接的票控盘号
            li_state = GetDiskNo(0, ls_temp);
            if (of_getcurstatus(is_pkpbh, "GetDiskNo", "遍历票控盘编号", li_state, ref as_err) == false) return "";
            is_pkpbh = strToByteLeft(ls_temp.ToString(), 0, 12);
            //打开
            ls_temp = new StringBuilder(65536);
            li_state = OpenDisk(is_pkpbh, ls_temp);
            if (of_getcurstatus(is_pkpbh, "OpenDisk", "打开票控盘", li_state, ref as_err) == false) return "";
            //选择应用
            li_state = SelectApp(is_pkpbh, 3);
            if (of_getcurstatus(is_pkpbh, "selectapp", "选择应用类型", li_state, ref as_err) == false) return "";

            //输入口令
            string ls_temp1 = "88888888";
            li_state = CheckPIN(is_pkpbh, ls_temp1);
            if (of_getcurstatus(is_pkpbh, "CheckPIN", "认证使用口令", li_state, ref as_err) == false) return "";
            //获取型号
            ls_temp = new StringBuilder(65536);
            li_state = QueryAllInvoType(is_pkpbh, ls_temp);
            if (of_getcurstatus(is_pkpbh, "QueryAllInvoType", "获取票据类型", li_state, ref as_err) == false) return "";

            //关闭
            li_state = CloseDisk(is_pkpbh);
            if (of_getcurstatus(is_pkpbh, "关闭票控盘", "", li_state, ref as_err) == false) return "";

            return ls_temp.ToString();
        }



    }
}
