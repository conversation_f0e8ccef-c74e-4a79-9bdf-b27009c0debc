﻿namespace NursingPlatform.Report
{
    partial class XtraReportIbillWristBandPrint
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraPrinting.BarCode.QRCodeGenerator qrCodeGenerator1 = new DevExpress.XtraPrinting.BarCode.QRCodeGenerator();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrPanel1 = new DevExpress.XtraReports.UI.XRPanel();
            this.lblName = new DevExpress.XtraReports.UI.XRLabel();
            this.xrBarCode1 = new DevExpress.XtraReports.UI.XRBarCode();
            this.lblSex = new DevExpress.XtraReports.UI.XRLabel();
            this.lblINP_NO = new DevExpress.XtraReports.UI.XRLabel();
            this.lblID2345678 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblTitle = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPanel1});
            this.Detail.Dpi = 254F;
            this.Detail.HeightF = 2920F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 254F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrPanel1
            // 
            this.xrPanel1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrPanel1.CanGrow = false;
            this.xrPanel1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lblName,
            this.xrBarCode1,
            this.lblSex,
            this.lblINP_NO,
            this.lblID2345678,
            this.lblTitle,
            this.xrLabel4,
            this.xrLabel3,
            this.xrLabel2,
            this.xrLabel1});
            this.xrPanel1.Dpi = 254F;
            this.xrPanel1.LocationFloat = new DevExpress.Utils.PointFloat(1.999999F, 0F);
            this.xrPanel1.Name = "xrPanel1";
            this.xrPanel1.SizeF = new System.Drawing.SizeF(2098F, 2867.083F);
            // 
            // lblName
            // 
            this.lblName.Angle = 270F;
            this.lblName.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lblName.BorderWidth = 0F;
            this.lblName.Dpi = 254F;
            this.lblName.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblName.LocationFloat = new DevExpress.Utils.PointFloat(184.9791F, 355.649F);
            this.lblName.Name = "lblName";
            this.lblName.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 3, 0, 254F);
            this.lblName.SizeF = new System.Drawing.SizeF(46.91675F, 297.1043F);
            this.lblName.StylePriority.UseBorders = false;
            this.lblName.StylePriority.UseBorderWidth = false;
            this.lblName.StylePriority.UseFont = false;
            this.lblName.StylePriority.UsePadding = false;
            this.lblName.StylePriority.UseTextAlignment = false;
            this.lblName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // xrBarCode1
            // 
            this.xrBarCode1.AutoModule = true;
            this.xrBarCode1.BarCodeOrientation = DevExpress.XtraPrinting.BarCode.BarCodeOrientation.RotateRight;
            this.xrBarCode1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrBarCode1.BorderWidth = 0F;
            this.xrBarCode1.Dpi = 254F;
            this.xrBarCode1.LocationFloat = new DevExpress.Utils.PointFloat(166.9792F, 975.9172F);
            this.xrBarCode1.Module = 5.08F;
            this.xrBarCode1.Name = "xrBarCode1";
            this.xrBarCode1.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 254F);
            this.xrBarCode1.ShowText = false;
            this.xrBarCode1.SizeF = new System.Drawing.SizeF(146F, 146F);
            this.xrBarCode1.StylePriority.UseBorders = false;
            this.xrBarCode1.StylePriority.UseBorderWidth = false;
            this.xrBarCode1.StylePriority.UsePadding = false;
            this.xrBarCode1.Symbology = qrCodeGenerator1;
            this.xrBarCode1.Draw += new DevExpress.XtraReports.UI.DrawEventHandler(this.xrBarCode1_Draw);
            // 
            // lblSex
            // 
            this.lblSex.Angle = 270F;
            this.lblSex.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lblSex.BorderWidth = 0F;
            this.lblSex.Dpi = 254F;
            this.lblSex.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblSex.LocationFloat = new DevExpress.Utils.PointFloat(184.9791F, 774.6732F);
            this.lblSex.Name = "lblSex";
            this.lblSex.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 3, 0, 254F);
            this.lblSex.SizeF = new System.Drawing.SizeF(46.91675F, 122.4969F);
            this.lblSex.StylePriority.UseBorders = false;
            this.lblSex.StylePriority.UseBorderWidth = false;
            this.lblSex.StylePriority.UseFont = false;
            this.lblSex.StylePriority.UsePadding = false;
            this.lblSex.StylePriority.UseTextAlignment = false;
            this.lblSex.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // lblINP_NO
            // 
            this.lblINP_NO.Angle = 270F;
            this.lblINP_NO.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lblINP_NO.BorderWidth = 0F;
            this.lblINP_NO.Dpi = 254F;
            this.lblINP_NO.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblINP_NO.LocationFloat = new DevExpress.Utils.PointFloat(256.0833F, 774.6732F);
            this.lblINP_NO.Name = "lblINP_NO";
            this.lblINP_NO.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 3, 0, 254F);
            this.lblINP_NO.SizeF = new System.Drawing.SizeF(46.91667F, 201.2438F);
            this.lblINP_NO.StylePriority.UseBorders = false;
            this.lblINP_NO.StylePriority.UseBorderWidth = false;
            this.lblINP_NO.StylePriority.UseFont = false;
            this.lblINP_NO.StylePriority.UsePadding = false;
            this.lblINP_NO.StylePriority.UseTextAlignment = false;
            this.lblINP_NO.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // lblID2345678
            // 
            this.lblID2345678.Angle = 270F;
            this.lblID2345678.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lblID2345678.BorderWidth = 0F;
            this.lblID2345678.Dpi = 254F;
            this.lblID2345678.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblID2345678.LocationFloat = new DevExpress.Utils.PointFloat(254.0832F, 355.6492F);
            this.lblID2345678.Name = "lblID2345678";
            this.lblID2345678.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 3, 0, 254F);
            this.lblID2345678.SizeF = new System.Drawing.SizeF(46.91673F, 260.0626F);
            this.lblID2345678.StylePriority.UseBorders = false;
            this.lblID2345678.StylePriority.UseBorderWidth = false;
            this.lblID2345678.StylePriority.UseFont = false;
            this.lblID2345678.StylePriority.UsePadding = false;
            this.lblID2345678.StylePriority.UseTextAlignment = false;
            this.lblID2345678.Text = "1234567890";
            this.lblID2345678.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // lblTitle
            // 
            this.lblTitle.Angle = 270F;
            this.lblTitle.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lblTitle.BorderWidth = 0F;
            this.lblTitle.Dpi = 254F;
            this.lblTitle.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblTitle.LocationFloat = new DevExpress.Utils.PointFloat(312.9792F, 196.3333F);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 254F);
            this.lblTitle.SizeF = new System.Drawing.SizeF(79.375F, 806.0422F);
            this.lblTitle.StylePriority.UseBorders = false;
            this.lblTitle.StylePriority.UseBorderWidth = false;
            this.lblTitle.StylePriority.UseFont = false;
            this.lblTitle.StylePriority.UsePadding = false;
            this.lblTitle.StylePriority.UseTextAlignment = false;
            this.lblTitle.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.lblTitle.WordWrap = false;
            // 
            // xrLabel4
            // 
            this.xrLabel4.Angle = 270F;
            this.xrLabel4.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel4.BorderWidth = 0F;
            this.xrLabel4.Dpi = 254F;
            this.xrLabel4.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.xrLabel4.LocationFloat = new DevExpress.Utils.PointFloat(184.9791F, 652.7533F);
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 3, 0, 254F);
            this.xrLabel4.SizeF = new System.Drawing.SizeF(46.91682F, 121.9199F);
            this.xrLabel4.StylePriority.UseBorders = false;
            this.xrLabel4.StylePriority.UseBorderWidth = false;
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UsePadding = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            this.xrLabel4.Text = "性别：";
            this.xrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.xrLabel4.WordWrap = false;
            // 
            // xrLabel3
            // 
            this.xrLabel3.Angle = 270F;
            this.xrLabel3.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel3.BorderWidth = 0F;
            this.xrLabel3.Dpi = 254F;
            this.xrLabel3.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.xrLabel3.LocationFloat = new DevExpress.Utils.PointFloat(184.9791F, 254.8963F);
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 0, 254F);
            this.xrLabel3.SizeF = new System.Drawing.SizeF(46.91681F, 100.7528F);
            this.xrLabel3.StylePriority.UseBorders = false;
            this.xrLabel3.StylePriority.UseBorderWidth = false;
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UsePadding = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            this.xrLabel3.Text = "姓名：";
            this.xrLabel3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            this.xrLabel3.WordWrap = false;
            // 
            // xrLabel2
            // 
            this.xrLabel2.Angle = 270F;
            this.xrLabel2.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel2.BorderWidth = 0F;
            this.xrLabel2.Dpi = 254F;
            this.xrLabel2.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(256.0832F, 615.7118F);
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 3, 0, 254F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(46.91678F, 158.9616F);
            this.xrLabel2.StylePriority.UseBorders = false;
            this.xrLabel2.StylePriority.UseBorderWidth = false;
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UsePadding = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            this.xrLabel2.Text = "住院号：";
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.xrLabel2.WordWrap = false;
            // 
            // xrLabel1
            // 
            this.xrLabel1.Angle = 270F;
            this.xrLabel1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel1.BorderWidth = 0F;
            this.xrLabel1.Dpi = 254F;
            this.xrLabel1.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(256.0832F, 217.8546F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 3, 0, 254F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(46.91676F, 137.7945F);
            this.xrLabel1.StylePriority.UseBorders = false;
            this.xrLabel1.StylePriority.UseBorderWidth = false;
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UsePadding = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "病人ID：";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            this.xrLabel1.WordWrap = false;
            // 
            // TopMargin
            // 
            this.TopMargin.Dpi = 254F;
            this.TopMargin.HeightF = 63.5F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 254F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Dpi = 254F;
            this.BottomMargin.HeightF = 0F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 254F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // XtraReportIbillWristBandPrint
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin});
            this.Dpi = 254F;
            this.Margins = new System.Drawing.Printing.Margins(0, 0, 64, 0);
            this.PageHeight = 2970;
            this.PageWidth = 2100;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.ReportUnit = DevExpress.XtraReports.UI.ReportUnit.TenthsOfAMillimeter;
            this.ShowPrintMarginsWarning = false;
            this.SnapGridSize = 25F;
            this.Version = "17.2";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRPanel xrPanel1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel lblTitle;
        private DevExpress.XtraReports.UI.XRLabel lblID2345678;
        private DevExpress.XtraReports.UI.XRLabel lblSex;
        private DevExpress.XtraReports.UI.XRLabel lblINP_NO;
        private DevExpress.XtraReports.UI.XRBarCode xrBarCode1;
        private DevExpress.XtraReports.UI.XRLabel lblName;
    }
}
