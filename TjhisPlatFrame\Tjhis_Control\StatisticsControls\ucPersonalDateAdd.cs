﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace Tjhis.Controls.StatisticsControls
{
    public partial class ucPersonalDateAdd : UserControl
    {
        public event EventHandler BtnSaved;
        public Data PersonalData { get; set; }

        public DateTime today
        {
            set
            {
                this.dateSchedule.Properties.MinValue = value;
            }
        }
        public ucPersonalDateAdd()
        {
            InitializeComponent();
        }
        

        public void InitDate(Data data)
        {
            this.PersonalData = data;
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            DataTable dataTable = new DataTable();
            dataTable.Columns.AddRange(new DataColumn[] {new DataColumn("VALUE",typeof(string)),new DataColumn("NAME",typeof(string)) });
            dataTable.Rows.Add("上午", "上午");
            dataTable.Rows.Add("下午", "下午");
            dataTable.Rows.Add("晚上", "晚上");
            this.combScheduleTime.Properties.DisplayMember = "NAME";
            this.combScheduleTime.Properties.ValueMember = "VALUE";
            this.combScheduleTime.Properties.Columns.Add(new DevExpress.XtraEditors.Controls.LookUpColumnInfo("NAME"));
            this.combScheduleTime.Properties.DataSource = dataTable;
        }

        public void InitData()
        {
            this.dateSchedule.EditValue = DateTime.Now;
            if (null != PersonalData)
            {
                this.dateSchedule.EditValue = PersonalData.scheduleDate;
                this.combScheduleTime.EditValue = PersonalData.scheduleTime;
                this.txtScheduleSub.EditValue = PersonalData.scheduleSub;
                this.txtScheduleMemo.EditValue = PersonalData.scheduleMemo;
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (null == this.dateSchedule.EditValue || string.IsNullOrEmpty(this.dateSchedule.EditValue.ToString()))
            {
                XtraMessageBox.Show("请选择日期！");
                this.dateSchedule.Focus();
                return;
            }
            if (string.IsNullOrEmpty(this.combScheduleTime.Text))
            {
                XtraMessageBox.Show("请选择时间！");
                this.combScheduleTime.Focus();
                return;
            }
            if (string.IsNullOrEmpty(this.txtScheduleSub.Text))
            {
                XtraMessageBox.Show("请输入主题！");
                this.txtScheduleSub.Focus();
                return;
            }
            if (string.IsNullOrEmpty(this.txtScheduleMemo.Text))
            {
                XtraMessageBox.Show("请输入内容！");
                this.txtScheduleMemo.Focus();
                return;
            }

            this.PersonalData = new Data();
            PersonalData.scheduleDate = this.dateSchedule.DateTime;
            PersonalData.scheduleTime = combScheduleTime.Text;
            PersonalData.scheduleSub = txtScheduleSub.Text;
            PersonalData.scheduleMemo = txtScheduleMemo.Text;
            BtnSaved?.Invoke(sender, e);


        }
        public class Data
        {
            public DateTime scheduleDate { get; set; }
            public string scheduleTime { get; set; }
            public string scheduleSub { get; set; }
            public string scheduleMemo { get; set; }
            public string scheduleCode { get; set; }
        }
    }
}
