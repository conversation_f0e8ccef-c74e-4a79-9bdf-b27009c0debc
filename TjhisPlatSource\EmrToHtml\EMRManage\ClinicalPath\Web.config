<?xml version="1.0"?>
<configuration>
  <configSections>
    <!--unity-->
    <section name="unity" type="Microsoft.Practices.Unity.Configuration.UnityConfigurationSection,Microsoft.Practices.Unity.Configuration"/>
    <!--EntLib 5.0-->
    <section name="dataConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings, Microsoft.Practices.EnterpriseLibrary.Data, Version=5.0.414.0, Culture=neutral"/>
  </configSections>
  <appSettings>
    <!--应用程序的名称-->
    <add key="AppTitle" value="应用程序的名称"/>
    <!--应用程序的版权-->
    <add key="AppCopyRight" value="版权"/>
    <!--应用程序的版本-->
    <add key="AppVersion" value="版本"/>
    <add key="HTML_URL" value="http://192.168.0.15:8020/EMRHTMLFILETemp/"/>
    <add key="Encrypt_Str" value="3DbXt1TFY5q8yFRARJu2BYRVQrcRcoWc"/>
    <add key="MobileWardServer_Path" value="Z:\EMRWebServer\MobileWard\EMRHTMLFILETemp\"/>
    <add key="ProcessStartTjEmrInfo_Path" value="F:\小程序集合\EMRViewClient\濮阳油田总医院\TJEmrToHtml\TJEmrToHtml\bin\x86\Release\"/>
    <add key="ProcessStartInfo_Path" value="Z:\EMRWebServer\EMRFileConvertForMobileWard\emttohtml.exe"/>
  </appSettings>
  <dataConfiguration defaultDatabase="connectionString"/>
  <connectionStrings>
    <!--数据库连接串oracle_186-->
    <!--<add name="connectionString" connectionString="Data Source = BLHK2 ; User Id = hisuser; Password = hisuser;" providerName="System.Data.OracleClient" />-->
    <add name="connectionString" connectionString="Data Source = his; User Id = system; Password =*******;" providerName="System.Data.OracleClient"/>
    <!--<add name="connectionString" connectionString="Data Source = BLHK ; User Id = system; Password = *****;" providerName="System.Data.OracleClient" />-->
    <add name="connectionString_care" connectionString="Data Source = his; User Id = system; Password = *******;" providerName="System.Data.OracleClient"/>
  </connectionStrings>
  <unity configSource="unity.config"/>
  <!--
    有关 web.config 更改的说明，请参见 http://go.microsoft.com/fwlink/?LinkId=235367。

    可在 <httpRuntime> 标记上设置以下特性。
      <system.Web>
        <httpRuntime targetFramework="4.5.2" />
      </system.Web>
  -->
  <system.web>
    <!-- 
            设置 compilation debug="true" 可将调试符号插入
            已编译的页面中。但由于这会 
            影响性能，因此只在开发过程中将此值 
            设置为 true。
        -->
    <compilation debug="true" targetFramework="4.5.2"/>
    <!--
            通过 <authentication> 节可以配置 ASP.NET 用来 
            识别进入用户的
            安全身份验证模式。 
       -->
    <authentication mode="Windows"/>
    <!--
            如果在执行请求的过程中出现未处理的错误，
            则通过 <customErrors> 节可以配置相应的处理步骤。具体说来，
            开发人员通过该节可以配置
            要显示的 html 错误页
            以代替错误堆栈跟踪。

        <customErrors mode="RemoteOnly" defaultRedirect="GenericErrorPage.htm">
            <error statusCode="403" redirect="NoAccess.htm" />
            <error statusCode="404" redirect="FileNotFound.htm" />
        </customErrors>
        -->
    <webServices>
      <protocols>
        <add name="HttpSoap"/>
        <add name="HttpPost"/>
        <add name="HttpGet"/>
        <add name="Documentation"/>
      </protocols>
    </webServices>
    <pages controlRenderingCompatibilityVersion="3.5" clientIDMode="AutoID"/>
  </system.web>
  <!-- 
        在 Internet 信息服务 7.0 下运行 ASP.NET AJAX 需要 system.webServer
        节。对早期版本的 IIS 来说则不需要此节。
    -->
</configuration>