﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace PlatCommonForm
{
    /// <summary>
    /// 入院患者查询
    /// </summary>
    public partial class FrmPatAdmissionQuery : DevExpress.XtraEditors.XtraForm
    {
        public FrmPatAdmissionQuery()
        {
            InitializeComponent();
        }

        #region 自定义方法
        private void LoadDept()
        {
            string sqlstr = "select  a.dept_name,a.dept_code from pat_visit t, dept_dict a where t.dept_admission_to = a.dept_code  NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient()";
            //DataSet ds = spc.GetDataBySql(sqlstr);
        }
        #endregion
    }
}