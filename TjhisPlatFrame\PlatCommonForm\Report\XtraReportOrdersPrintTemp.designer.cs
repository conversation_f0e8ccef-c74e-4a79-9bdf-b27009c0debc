﻿namespace PlatCommonForm.Report
{
    partial class XtraReportOrdersPrintTemp
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being usefd.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.date = new DevExpress.XtraReports.UI.XRTableCell();
            this.time = new DevExpress.XtraReports.UI.XRTableCell();
            this.order_text = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.joint_line = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.doctor = new DevExpress.XtraReports.UI.XRTableCell();
            this.verify_datetime = new DevExpress.XtraReports.UI.XRTableCell();
            this.nurse = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrPictureBox1 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.rowNum = new DevExpress.XtraReports.UI.XRTableCell();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.t_verify_datetime = new DevExpress.XtraReports.UI.XRLabel();
            this.t_doctor = new DevExpress.XtraReports.UI.XRLabel();
            this.t_order_text = new DevExpress.XtraReports.UI.XRLabel();
            this.t_time = new DevExpress.XtraReports.UI.XRLabel();
            this.t_date = new DevExpress.XtraReports.UI.XRLabel();
            this.hospital_name = new DevExpress.XtraReports.UI.XRLabel();
            this.id = new DevExpress.XtraReports.UI.XRLabel();
            this.t_id = new DevExpress.XtraReports.UI.XRLabel();
            this.inp_no = new DevExpress.XtraReports.UI.XRLabel();
            this.t_inp_no = new DevExpress.XtraReports.UI.XRLabel();
            this.bed_no = new DevExpress.XtraReports.UI.XRLabel();
            this.t_bed_no = new DevExpress.XtraReports.UI.XRLabel();
            this.dept = new DevExpress.XtraReports.UI.XRLabel();
            this.t_dept = new DevExpress.XtraReports.UI.XRLabel();
            this.name = new DevExpress.XtraReports.UI.XRLabel();
            this.t_name = new DevExpress.XtraReports.UI.XRLabel();
            this.t_nurse = new DevExpress.XtraReports.UI.XRLabel();
            this.t_page = new DevExpress.XtraReports.UI.XRLabel();
            this.PageFooter = new DevExpress.XtraReports.UI.PageFooterBand();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.orders_type = new DevExpress.XtraReports.UI.XRLabel();
            this.xrPictureBox2 = new DevExpress.XtraReports.UI.XRPictureBox();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable1});
            this.Detail.HeightF = 40F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrTable1
            // 
            this.xrTable1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable1.BorderWidth = 1F;
            this.xrTable1.Font = new System.Drawing.Font("宋体", 9F);
            this.xrTable1.LocationFloat = new DevExpress.Utils.PointFloat(38F, 0F);
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow1});
            this.xrTable1.SizeF = new System.Drawing.SizeF(772.0771F, 40F);
            this.xrTable1.StylePriority.UseBorders = false;
            this.xrTable1.StylePriority.UseBorderWidth = false;
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            this.xrTable1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrTableRow1
            // 
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.date,
            this.time,
            this.order_text,
            this.xrTableCell2,
            this.joint_line,
            this.xrTableCell1,
            this.xrTableCell3,
            this.doctor,
            this.verify_datetime,
            this.nurse,
            this.rowNum});
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.Weight = 1D;
            // 
            // date
            // 
            this.date.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.date.Name = "date";
            this.date.StylePriority.UseBorders = false;
            this.date.Weight = 0.506391456307039D;
            // 
            // time
            // 
            this.time.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.time.Name = "time";
            this.time.StylePriority.UseBorders = false;
            this.time.StylePriority.UseTextAlignment = false;
            this.time.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.time.Weight = 0.42848504120164232D;
            // 
            // order_text
            // 
            this.order_text.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.order_text.Name = "order_text";
            this.order_text.StylePriority.UseBorders = false;
            this.order_text.Weight = 1.5704623766314327D;
            // 
            // xrTableCell2
            // 
            this.xrTableCell2.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell2.Name = "xrTableCell2";
            this.xrTableCell2.StylePriority.UseBorders = false;
            this.xrTableCell2.StylePriority.UseTextAlignment = false;
            this.xrTableCell2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrTableCell2.Weight = 0.3736260473818353D;
            // 
            // joint_line
            // 
            this.joint_line.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.joint_line.Name = "joint_line";
            this.joint_line.StylePriority.UseBorders = false;
            this.joint_line.StylePriority.UseTextAlignment = false;
            this.joint_line.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.joint_line.Weight = 0.25184861941588932D;
            // 
            // xrTableCell1
            // 
            this.xrTableCell1.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell1.Name = "xrTableCell1";
            this.xrTableCell1.StylePriority.UseBorders = false;
            this.xrTableCell1.Weight = 0.43010839736380124D;
            // 
            // xrTableCell3
            // 
            this.xrTableCell3.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.StylePriority.UseBorders = false;
            this.xrTableCell3.Weight = 0.41230296978672015D;
            // 
            // doctor
            // 
            this.doctor.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.doctor.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPictureBox2});
            this.doctor.Name = "doctor";
            this.doctor.StylePriority.UseBorders = false;
            this.doctor.StylePriority.UseTextAlignment = false;
            this.doctor.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.doctor.Weight = 0.55508289354973162D;
            // 
            // verify_datetime
            // 
            this.verify_datetime.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.verify_datetime.Name = "verify_datetime";
            this.verify_datetime.StylePriority.UseBorders = false;
            this.verify_datetime.StylePriority.UseTextAlignment = false;
            this.verify_datetime.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.verify_datetime.Weight = 0.70115732711026491D;
            // 
            // nurse
            // 
            this.nurse.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.nurse.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPictureBox1});
            this.nurse.Name = "nurse";
            this.nurse.StylePriority.UseBorders = false;
            this.nurse.StylePriority.UseTextAlignment = false;
            this.nurse.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.nurse.Weight = 0.70115782997714327D;
            // 
            // xrPictureBox1
            // 
            this.xrPictureBox1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrPictureBox1.LocationFloat = new DevExpress.Utils.PointFloat(2.000127F, 3F);
            this.xrPictureBox1.Name = "xrPictureBox1";
            this.xrPictureBox1.SizeF = new System.Drawing.SizeF(86F, 33F);
            this.xrPictureBox1.StylePriority.UseBorders = false;
            this.xrPictureBox1.Visible = false;
            // 
            // rowNum
            // 
            this.rowNum.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.rowNum.Name = "rowNum";
            this.rowNum.StylePriority.UseBorders = false;
            this.rowNum.Weight = 0.084349397917053232D;
            // 
            // TopMargin
            // 
            this.TopMargin.HeightF = 18F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // BottomMargin
            // 
            this.BottomMargin.HeightF = 11F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // t_verify_datetime
            // 
            this.t_verify_datetime.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.t_verify_datetime.BorderWidth = 1F;
            this.t_verify_datetime.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.t_verify_datetime.LocationFloat = new DevExpress.Utils.PointFloat(619.25F, 117.1667F);
            this.t_verify_datetime.Multiline = true;
            this.t_verify_datetime.Name = "t_verify_datetime";
            this.t_verify_datetime.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_verify_datetime.SizeF = new System.Drawing.SizeF(90F, 40F);
            this.t_verify_datetime.StylePriority.UseBorders = false;
            this.t_verify_datetime.StylePriority.UseBorderWidth = false;
            this.t_verify_datetime.StylePriority.UseFont = false;
            this.t_verify_datetime.StylePriority.UseTextAlignment = false;
            this.t_verify_datetime.Text = "执行时间";
            this.t_verify_datetime.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // t_doctor
            // 
            this.t_doctor.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.t_doctor.BorderWidth = 1F;
            this.t_doctor.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.t_doctor.LocationFloat = new DevExpress.Utils.PointFloat(548F, 117.1667F);
            this.t_doctor.Name = "t_doctor";
            this.t_doctor.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_doctor.SizeF = new System.Drawing.SizeF(71.25F, 39.99999F);
            this.t_doctor.StylePriority.UseBorders = false;
            this.t_doctor.StylePriority.UseBorderWidth = false;
            this.t_doctor.StylePriority.UseFont = false;
            this.t_doctor.StylePriority.UseTextAlignment = false;
            this.t_doctor.Text = "医生\r\n签名";
            this.t_doctor.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // t_order_text
            // 
            this.t_order_text.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.t_order_text.BorderWidth = 1F;
            this.t_order_text.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.t_order_text.LocationFloat = new DevExpress.Utils.PointFloat(158F, 117.1667F);
            this.t_order_text.Name = "t_order_text";
            this.t_order_text.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_order_text.SizeF = new System.Drawing.SizeF(390F, 39.99999F);
            this.t_order_text.StylePriority.UseBorders = false;
            this.t_order_text.StylePriority.UseBorderWidth = false;
            this.t_order_text.StylePriority.UseFont = false;
            this.t_order_text.StylePriority.UseTextAlignment = false;
            this.t_order_text.Text = "临时医嘱";
            this.t_order_text.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // t_time
            // 
            this.t_time.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.t_time.BorderWidth = 1F;
            this.t_time.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.t_time.LocationFloat = new DevExpress.Utils.PointFloat(103F, 117.1667F);
            this.t_time.Name = "t_time";
            this.t_time.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_time.SizeF = new System.Drawing.SizeF(55.00001F, 39.99999F);
            this.t_time.StylePriority.UseBorders = false;
            this.t_time.StylePriority.UseBorderWidth = false;
            this.t_time.StylePriority.UseFont = false;
            this.t_time.StylePriority.UseTextAlignment = false;
            this.t_time.Text = "时间";
            this.t_time.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // t_date
            // 
            this.t_date.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.t_date.BorderWidth = 1F;
            this.t_date.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.t_date.LocationFloat = new DevExpress.Utils.PointFloat(38F, 117.1667F);
            this.t_date.Name = "t_date";
            this.t_date.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_date.SizeF = new System.Drawing.SizeF(65F, 39.99999F);
            this.t_date.StylePriority.UseBorders = false;
            this.t_date.StylePriority.UseBorderWidth = false;
            this.t_date.StylePriority.UseFont = false;
            this.t_date.StylePriority.UseTextAlignment = false;
            this.t_date.Text = "日期";
            this.t_date.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // hospital_name
            // 
            this.hospital_name.BorderWidth = 0F;
            this.hospital_name.Font = new System.Drawing.Font("Times New Roman", 14F, System.Drawing.FontStyle.Bold);
            this.hospital_name.LocationFloat = new DevExpress.Utils.PointFloat(40F, 8F);
            this.hospital_name.Name = "hospital_name";
            this.hospital_name.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.hospital_name.SizeF = new System.Drawing.SizeF(725F, 30F);
            this.hospital_name.StylePriority.UseBorders = false;
            this.hospital_name.StylePriority.UseBorderWidth = false;
            this.hospital_name.StylePriority.UseFont = false;
            this.hospital_name.StylePriority.UseTextAlignment = false;
            this.hospital_name.Text = "东北国际医院";
            this.hospital_name.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // id
            // 
            this.id.BorderWidth = 0F;
            this.id.LocationFloat = new DevExpress.Utils.PointFloat(698F, 93.16666F);
            this.id.Name = "id";
            this.id.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.id.SizeF = new System.Drawing.SizeF(100F, 20F);
            this.id.StylePriority.UseBorderWidth = false;
            // 
            // t_id
            // 
            this.t_id.BorderWidth = 0F;
            this.t_id.LocationFloat = new DevExpress.Utils.PointFloat(668F, 93.16666F);
            this.t_id.Name = "t_id";
            this.t_id.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_id.SizeF = new System.Drawing.SizeF(30F, 20F);
            this.t_id.StylePriority.UseBorderWidth = false;
            this.t_id.StylePriority.UseTextAlignment = false;
            this.t_id.Text = "ID";
            this.t_id.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopJustify;
            // 
            // inp_no
            // 
            this.inp_no.BorderWidth = 0F;
            this.inp_no.LocationFloat = new DevExpress.Utils.PointFloat(587F, 93.16666F);
            this.inp_no.Name = "inp_no";
            this.inp_no.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.inp_no.SizeF = new System.Drawing.SizeF(81F, 20F);
            this.inp_no.StylePriority.UseBorderWidth = false;
            // 
            // t_inp_no
            // 
            this.t_inp_no.BorderWidth = 0F;
            this.t_inp_no.LocationFloat = new DevExpress.Utils.PointFloat(517F, 93.16666F);
            this.t_inp_no.Name = "t_inp_no";
            this.t_inp_no.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_inp_no.SizeF = new System.Drawing.SizeF(70F, 20F);
            this.t_inp_no.StylePriority.UseBorderWidth = false;
            this.t_inp_no.StylePriority.UseTextAlignment = false;
            this.t_inp_no.Text = "住院号：";
            this.t_inp_no.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopJustify;
            // 
            // bed_no
            // 
            this.bed_no.BorderWidth = 0F;
            this.bed_no.LocationFloat = new DevExpress.Utils.PointFloat(467F, 93.16666F);
            this.bed_no.Name = "bed_no";
            this.bed_no.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.bed_no.SizeF = new System.Drawing.SizeF(50F, 20F);
            this.bed_no.StylePriority.UseBorderWidth = false;
            // 
            // t_bed_no
            // 
            this.t_bed_no.BorderWidth = 0F;
            this.t_bed_no.LocationFloat = new DevExpress.Utils.PointFloat(407F, 93.16666F);
            this.t_bed_no.Name = "t_bed_no";
            this.t_bed_no.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_bed_no.SizeF = new System.Drawing.SizeF(60F, 20F);
            this.t_bed_no.StylePriority.UseBorderWidth = false;
            this.t_bed_no.StylePriority.UseTextAlignment = false;
            this.t_bed_no.Text = "床号：";
            this.t_bed_no.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // dept
            // 
            this.dept.BorderWidth = 0F;
            this.dept.LocationFloat = new DevExpress.Utils.PointFloat(239F, 93.16666F);
            this.dept.Name = "dept";
            this.dept.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.dept.SizeF = new System.Drawing.SizeF(168F, 20F);
            this.dept.StylePriority.UseBorderWidth = false;
            // 
            // t_dept
            // 
            this.t_dept.BorderWidth = 0F;
            this.t_dept.LocationFloat = new DevExpress.Utils.PointFloat(179F, 93.16666F);
            this.t_dept.Name = "t_dept";
            this.t_dept.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_dept.SizeF = new System.Drawing.SizeF(60F, 20F);
            this.t_dept.StylePriority.UseBorderWidth = false;
            this.t_dept.StylePriority.UseTextAlignment = false;
            this.t_dept.Text = "科室：";
            this.t_dept.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // name
            // 
            this.name.BorderWidth = 0F;
            this.name.LocationFloat = new DevExpress.Utils.PointFloat(90.00001F, 93.16666F);
            this.name.Name = "name";
            this.name.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.name.SizeF = new System.Drawing.SizeF(88.33332F, 20F);
            this.name.StylePriority.UseBorderWidth = false;
            // 
            // t_name
            // 
            this.t_name.BorderWidth = 0F;
            this.t_name.LocationFloat = new DevExpress.Utils.PointFloat(40F, 93.16666F);
            this.t_name.Name = "t_name";
            this.t_name.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_name.SizeF = new System.Drawing.SizeF(50F, 20F);
            this.t_name.StylePriority.UseBorderWidth = false;
            this.t_name.StylePriority.UseTextAlignment = false;
            this.t_name.Text = "姓名：";
            this.t_name.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // t_nurse
            // 
            this.t_nurse.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.t_nurse.BorderWidth = 1F;
            this.t_nurse.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.t_nurse.LocationFloat = new DevExpress.Utils.PointFloat(709.2501F, 117.1667F);
            this.t_nurse.Multiline = true;
            this.t_nurse.Name = "t_nurse";
            this.t_nurse.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_nurse.SizeF = new System.Drawing.SizeF(90F, 40F);
            this.t_nurse.StylePriority.UseBorders = false;
            this.t_nurse.StylePriority.UseBorderWidth = false;
            this.t_nurse.StylePriority.UseFont = false;
            this.t_nurse.StylePriority.UseTextAlignment = false;
            this.t_nurse.Text = "执行护士签名";
            this.t_nurse.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // t_page
            // 
            this.t_page.LocationFloat = new DevExpress.Utils.PointFloat(347F, 5F);
            this.t_page.Name = "t_page";
            this.t_page.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.t_page.SizeF = new System.Drawing.SizeF(100F, 23F);
            // 
            // PageFooter
            // 
            this.PageFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.t_page});
            this.PageFooter.HeightF = 33.33333F;
            this.PageFooter.Name = "PageFooter";
            // 
            // PageHeader
            // 
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel1,
            this.t_verify_datetime,
            this.t_doctor,
            this.t_order_text,
            this.t_time,
            this.hospital_name,
            this.orders_type,
            this.name,
            this.t_dept,
            this.dept,
            this.t_bed_no,
            this.bed_no,
            this.t_inp_no,
            this.inp_no,
            this.t_id,
            this.id,
            this.t_date,
            this.t_name,
            this.t_nurse});
            this.PageHeader.HeightF = 157.1667F;
            this.PageHeader.Name = "PageHeader";
            // 
            // xrLabel1
            // 
            this.xrLabel1.BorderWidth = 0F;
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(780F, 53.33333F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(20F, 30F);
            this.xrLabel1.StylePriority.UseBorderWidth = false;
            this.xrLabel1.Text = ".";
            // 
            // orders_type
            // 
            this.orders_type.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.orders_type.BorderWidth = 0F;
            this.orders_type.Font = new System.Drawing.Font("Times New Roman", 18F, System.Drawing.FontStyle.Bold);
            this.orders_type.LocationFloat = new DevExpress.Utils.PointFloat(40F, 50F);
            this.orders_type.Name = "orders_type";
            this.orders_type.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.orders_type.SizeF = new System.Drawing.SizeF(725F, 30F);
            this.orders_type.StylePriority.UseBorders = false;
            this.orders_type.StylePriority.UseBorderWidth = false;
            this.orders_type.StylePriority.UseFont = false;
            this.orders_type.StylePriority.UseTextAlignment = false;
            this.orders_type.Text = "临  时  医  嘱  单";
            this.orders_type.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrPictureBox2
            // 
            this.xrPictureBox2.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrPictureBox2.LocationFloat = new DevExpress.Utils.PointFloat(5.999901F, 3.50001F);
            this.xrPictureBox2.Name = "xrPictureBox2";
            this.xrPictureBox2.SizeF = new System.Drawing.SizeF(61.2501F, 33F);
            this.xrPictureBox2.StylePriority.UseBorders = false;
            this.xrPictureBox2.Visible = false;
            // 
            // XtraReportOrdersPrintTemp
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageFooter,
            this.PageHeader});
            this.Margins = new System.Drawing.Printing.Margins(18, 20, 18, 11);
            this.ShowPrintMarginsWarning = false;
            this.SnapGridSize = 10F;
            this.Version = "17.2";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel hospital_name;
        private DevExpress.XtraReports.UI.XRLabel t_order_text;
        private DevExpress.XtraReports.UI.XRLabel t_time;
        private DevExpress.XtraReports.UI.XRLabel t_date;
        private DevExpress.XtraReports.UI.XRLabel t_verify_datetime;
        private DevExpress.XtraReports.UI.XRLabel t_doctor;
        private DevExpress.XtraReports.UI.XRLabel t_name;
        private DevExpress.XtraReports.UI.XRLabel name;
        private DevExpress.XtraReports.UI.XRLabel dept;
        private DevExpress.XtraReports.UI.XRLabel t_dept;
        private DevExpress.XtraReports.UI.XRLabel bed_no;
        private DevExpress.XtraReports.UI.XRLabel t_bed_no;
        private DevExpress.XtraReports.UI.XRLabel id;
        private DevExpress.XtraReports.UI.XRLabel t_id;
        private DevExpress.XtraReports.UI.XRLabel inp_no;
        private DevExpress.XtraReports.UI.XRLabel t_inp_no;
        private DevExpress.XtraReports.UI.XRLabel t_page;
        private DevExpress.XtraReports.UI.XRLabel t_nurse;
        private DevExpress.XtraReports.UI.PageFooterBand PageFooter;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.XRLabel orders_type;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRTable xrTable1;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow1;
        private DevExpress.XtraReports.UI.XRTableCell date;
        private DevExpress.XtraReports.UI.XRTableCell time;
        private DevExpress.XtraReports.UI.XRTableCell order_text;
        private DevExpress.XtraReports.UI.XRTableCell joint_line;
        private DevExpress.XtraReports.UI.XRTableCell doctor;
        private DevExpress.XtraReports.UI.XRTableCell verify_datetime;
        private DevExpress.XtraReports.UI.XRTableCell nurse;
        private DevExpress.XtraReports.UI.XRTableCell rowNum;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell2;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell3;
        private DevExpress.XtraReports.UI.XRPictureBox xrPictureBox1;
        private DevExpress.XtraReports.UI.XRPictureBox xrPictureBox2;
    }
}
