﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1825EFAD-9793-48B2-B82D-37B8C515E64E}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Tjhis.Controls</RootNamespace>
    <AssemblyName>Tjhis.Controls</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\TJHisPlatEXE\Client\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\DLL\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Charts.v19.1.Core, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Data.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Printing.v19.1.Core, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Sparkline.v19.1.Core, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Utils.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraBars.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraCharts.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraCharts.v19.1.UI, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v19.1.Wizard, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraEditors.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGrid.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraPrinting.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Comm\HelperComm.cs" />
    <Compile Include="Comm\UcButtonT.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Comm\UcButtonT.Designer.cs">
      <DependentUpon>UcButtonT.cs</DependentUpon>
    </Compile>
    <Compile Include="Comm\UcParent.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Comm\UcParent.Designer.cs">
      <DependentUpon>UcParent.cs</DependentUpon>
    </Compile>
    <Compile Include="Lines\UcLine_H.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Lines\UcLine_H.Designer.cs">
      <DependentUpon>UcLine_H.cs</DependentUpon>
    </Compile>
    <Compile Include="Lines\UcLine_L.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Lines\UcLine_L.Designer.cs">
      <DependentUpon>UcLine_L.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="StatisticsControls\RadiusUserControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="StatisticsControls\RadiusUserControl.Designer.cs">
      <DependentUpon>RadiusUserControl.cs</DependentUpon>
    </Compile>
    <Compile Include="StatisticsControls\UCClinicStatisicsData.cs" />
    <Compile Include="StatisticsControls\ucClinicStatistics.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="StatisticsControls\ucClinicStatistics.Designer.cs">
      <DependentUpon>ucClinicStatistics.cs</DependentUpon>
    </Compile>
    <Compile Include="StatisticsControls\ucCompare.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="StatisticsControls\ucCompare.Designer.cs">
      <DependentUpon>ucCompare.cs</DependentUpon>
    </Compile>
    <Compile Include="StatisticsControls\ucDeptInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="StatisticsControls\ucDeptInfo.Designer.cs">
      <DependentUpon>ucDeptInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="StatisticsControls\ucDeptRegister.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="StatisticsControls\ucDeptRegister.Designer.cs">
      <DependentUpon>ucDeptRegister.cs</DependentUpon>
    </Compile>
    <Compile Include="StatisticsControls\ucIncomeChart.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="StatisticsControls\ucIncomeChart.Designer.cs">
      <DependentUpon>ucIncomeChart.cs</DependentUpon>
    </Compile>
    <Compile Include="StatisticsControls\ucOutpVisitStatistics.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="StatisticsControls\ucOutpVisitStatistics.Designer.cs">
      <DependentUpon>ucOutpVisitStatistics.cs</DependentUpon>
    </Compile>
    <Compile Include="StatisticsControls\ucPersonalDate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="StatisticsControls\ucPersonalDate.Designer.cs">
      <DependentUpon>ucPersonalDate.cs</DependentUpon>
    </Compile>
    <Compile Include="StatisticsControls\ucPersonalInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="StatisticsControls\ucPersonalInfo.Designer.cs">
      <DependentUpon>ucPersonalInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="StatisticsControls\ucPersonalDateAdd.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="StatisticsControls\ucPersonalDateAdd.Designer.cs">
      <DependentUpon>ucPersonalDateAdd.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Comm\UcButtonT.resx">
      <DependentUpon>UcButtonT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Comm\UcParent.resx">
      <DependentUpon>UcParent.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Lines\UcLine_H.resx">
      <DependentUpon>UcLine_H.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Lines\UcLine_L.resx">
      <DependentUpon>UcLine_L.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="StatisticsControls\RadiusUserControl.resx">
      <DependentUpon>RadiusUserControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="StatisticsControls\ucClinicStatistics.resx">
      <DependentUpon>ucClinicStatistics.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="StatisticsControls\ucCompare.resx">
      <DependentUpon>ucCompare.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="StatisticsControls\ucDeptInfo.resx">
      <DependentUpon>ucDeptInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="StatisticsControls\ucDeptRegister.resx">
      <DependentUpon>ucDeptRegister.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="StatisticsControls\ucIncomeChart.resx">
      <DependentUpon>ucIncomeChart.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="StatisticsControls\ucOutpVisitStatistics.resx">
      <DependentUpon>ucOutpVisitStatistics.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="StatisticsControls\ucPersonalDate.resx">
      <DependentUpon>ucPersonalDate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="StatisticsControls\ucPersonalDateAdd.resx">
      <DependentUpon>ucPersonalDateAdd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="StatisticsControls\ucPersonalInfo.resx">
      <DependentUpon>ucPersonalInfo.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\费用统计bg.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\接诊统计bg.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\科室信息bg.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\路径%281%29.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\路径.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\平均等候时间.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\平均诊疗时间.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\未接诊.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\已接诊.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\下一个%402x.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\下一个备份%402x.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\点 %286%29.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\点 %287%29.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\man.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\编组 10.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\蒙版.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\保存1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\保存.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>