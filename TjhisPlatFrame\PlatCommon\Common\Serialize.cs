﻿//**********************************************
//说明:序列化
//计算机名称：LINDP
//创建日期：2016/5/20 11:12:46
//作者：林大鹏
//版本号：V1.00
//**********************************************

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web.Script.Serialization;

namespace PlatCommon.Common
{
    public class Serialize
    {
        public static string ScriptSerialize<T>(T t)
        {
            JavaScriptSerializer serializer = new JavaScriptSerializer();
            return serializer.Serialize(t);
        }
        //
        public static T ScriptDeserialize<T>(string strJson)
        {
            JavaScriptSerializer serializer = new JavaScriptSerializer();
            return serializer.Deserialize<T>(strJson);
        }
    }
}
