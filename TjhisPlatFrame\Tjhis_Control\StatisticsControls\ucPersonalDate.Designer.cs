﻿namespace Tjhis.Controls.StatisticsControls
{
    partial class ucPersonalDate
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.dateEdit1 = new DevExpress.XtraEditors.DateEdit();
            this.BtnPre = new DevExpress.XtraEditors.SimpleButton();
            this.BtnNext = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.BtnAdd = new DevExpress.XtraEditors.SimpleButton();
            this.MainPanel = new System.Windows.Forms.Panel();
            this.GCDate = new DevExpress.XtraGrid.GridControl();
            this.GvView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ToolTip = new DevExpress.Utils.ToolTipController();
            ((System.ComponentModel.ISupportInitialize)(this.dateEdit1.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEdit1.Properties)).BeginInit();
            this.MainPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.GCDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.GvView)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("微软雅黑", 13F, System.Drawing.FontStyle.Bold);
            this.labelControl1.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(78)))), ((int)(((byte)(87)))), ((int)(((byte)(128)))));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Appearance.Options.UseForeColor = true;
            this.labelControl1.Location = new System.Drawing.Point(23, 28);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(72, 25);
            this.labelControl1.TabIndex = 4;
            this.labelControl1.Text = "我的日程";
            // 
            // dateEdit1
            // 
            this.dateEdit1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.dateEdit1.EditValue = new System.DateTime(2022, 6, 8, 16, 9, 58, 0);
            this.dateEdit1.Location = new System.Drawing.Point(336, 28);
            this.dateEdit1.Name = "dateEdit1";
            this.dateEdit1.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.dateEdit1.Properties.DisplayFormat.FormatString = "yyyy年MM月";
            this.dateEdit1.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Custom;
            this.dateEdit1.Properties.ReadOnly = true;
            this.dateEdit1.Size = new System.Drawing.Size(93, 20);
            this.dateEdit1.TabIndex = 5;
            // 
            // BtnPre
            // 
            this.BtnPre.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.BtnPre.BackgroundImage = global::Tjhis.Controls.Properties.Resources.下一个备份_2x;
            this.BtnPre.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.BtnPre.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.BtnPre.Location = new System.Drawing.Point(305, 28);
            this.BtnPre.Name = "BtnPre";
            this.BtnPre.Size = new System.Drawing.Size(20, 20);
            this.BtnPre.TabIndex = 7;
            this.BtnPre.ToolTip = "上星期";
            this.BtnPre.Click += new System.EventHandler(this.BtnPre_Click);
            // 
            // BtnNext
            // 
            this.BtnNext.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.BtnNext.BackgroundImage = global::Tjhis.Controls.Properties.Resources.下一个_2x;
            this.BtnNext.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.BtnNext.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.BtnNext.Location = new System.Drawing.Point(435, 28);
            this.BtnNext.Name = "BtnNext";
            this.BtnNext.Size = new System.Drawing.Size(20, 20);
            this.BtnNext.TabIndex = 8;
            this.BtnNext.ToolTip = "下星期";
            this.BtnNext.Click += new System.EventHandler(this.BtnNext_Click);
            // 
            // labelControl2
            // 
            this.labelControl2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.labelControl2.Appearance.Font = new System.Drawing.Font("Tahoma", 14F);
            this.labelControl2.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(195)))), ((int)(((byte)(208)))), ((int)(((byte)(242)))));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Appearance.Options.UseForeColor = true;
            this.labelControl2.Appearance.Options.UseTextOptions = true;
            this.labelControl2.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.labelControl2.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.labelControl2.Location = new System.Drawing.Point(464, 27);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(7, 23);
            this.labelControl2.TabIndex = 9;
            this.labelControl2.Text = "|";
            // 
            // BtnAdd
            // 
            this.BtnAdd.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.BtnAdd.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold);
            this.BtnAdd.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(65)))), ((int)(((byte)(124)))), ((int)(((byte)(255)))));
            this.BtnAdd.Appearance.Options.UseFont = true;
            this.BtnAdd.Appearance.Options.UseForeColor = true;
            this.BtnAdd.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.BtnAdd.Location = new System.Drawing.Point(471, 28);
            this.BtnAdd.Name = "BtnAdd";
            this.BtnAdd.Size = new System.Drawing.Size(66, 20);
            this.BtnAdd.TabIndex = 10;
            this.BtnAdd.Text = "添加日程";
            this.BtnAdd.Click += new System.EventHandler(this.BtnAdd_Click);
            // 
            // MainPanel
            // 
            this.MainPanel.Controls.Add(this.GCDate);
            this.MainPanel.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.MainPanel.Location = new System.Drawing.Point(0, 75);
            this.MainPanel.Margin = new System.Windows.Forms.Padding(24, 3, 24, 3);
            this.MainPanel.Name = "MainPanel";
            this.MainPanel.Size = new System.Drawing.Size(626, 281);
            this.MainPanel.TabIndex = 11;
            // 
            // GCDate
            // 
            this.GCDate.Dock = System.Windows.Forms.DockStyle.Fill;
            this.GCDate.EmbeddedNavigator.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(236)))), ((int)(((byte)(240)))), ((int)(((byte)(245)))));
            this.GCDate.EmbeddedNavigator.Appearance.Options.UseBackColor = true;
            this.GCDate.Location = new System.Drawing.Point(0, 0);
            this.GCDate.MainView = this.GvView;
            this.GCDate.Margin = new System.Windows.Forms.Padding(20, 3, 20, 3);
            this.GCDate.Name = "GCDate";
            this.GCDate.Padding = new System.Windows.Forms.Padding(20, 0, 20, 0);
            this.GCDate.Size = new System.Drawing.Size(626, 281);
            this.GCDate.TabIndex = 14;
            this.GCDate.ToolTipController = this.ToolTip;
            this.GCDate.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.GvView});
            // 
            // GvView
            // 
            this.GvView.Appearance.FocusedCell.BackColor = System.Drawing.Color.White;
            this.GvView.Appearance.FocusedCell.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(78)))), ((int)(((byte)(208)))), ((int)(((byte)(135)))));
            this.GvView.Appearance.FocusedCell.Options.UseBackColor = true;
            this.GvView.Appearance.FocusedCell.Options.UseForeColor = true;
            this.GvView.Appearance.FocusedRow.BackColor = System.Drawing.Color.White;
            this.GvView.Appearance.FocusedRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(78)))), ((int)(((byte)(208)))), ((int)(((byte)(135)))));
            this.GvView.Appearance.FocusedRow.Options.UseBackColor = true;
            this.GvView.Appearance.FocusedRow.Options.UseForeColor = true;
            this.GvView.Appearance.HeaderPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(242)))), ((int)(((byte)(245)))), ((int)(((byte)(250)))));
            this.GvView.Appearance.HeaderPanel.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(195)))), ((int)(((byte)(208)))), ((int)(((byte)(242)))));
            this.GvView.Appearance.HeaderPanel.Font = new System.Drawing.Font("Tahoma", 13F);
            this.GvView.Appearance.HeaderPanel.Options.UseBackColor = true;
            this.GvView.Appearance.HeaderPanel.Options.UseBorderColor = true;
            this.GvView.Appearance.HeaderPanel.Options.UseFont = true;
            this.GvView.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.GvView.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.GvView.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.GvView.Appearance.HideSelectionRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(236)))), ((int)(((byte)(240)))), ((int)(((byte)(245)))));
            this.GvView.Appearance.HideSelectionRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(78)))), ((int)(((byte)(208)))), ((int)(((byte)(135)))));
            this.GvView.Appearance.HideSelectionRow.Options.UseBackColor = true;
            this.GvView.Appearance.HideSelectionRow.Options.UseForeColor = true;
            this.GvView.Appearance.Row.BackColor = System.Drawing.Color.White;
            this.GvView.Appearance.Row.Font = new System.Drawing.Font("Tahoma", 13F);
            this.GvView.Appearance.Row.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(78)))), ((int)(((byte)(208)))), ((int)(((byte)(135)))));
            this.GvView.Appearance.Row.Options.UseBackColor = true;
            this.GvView.Appearance.Row.Options.UseFont = true;
            this.GvView.Appearance.Row.Options.UseForeColor = true;
            this.GvView.Appearance.Row.Options.UseTextOptions = true;
            this.GvView.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.GvView.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.GvView.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(236)))), ((int)(((byte)(240)))), ((int)(((byte)(245)))));
            this.GvView.Appearance.SelectedRow.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(78)))), ((int)(((byte)(208)))), ((int)(((byte)(135)))));
            this.GvView.Appearance.SelectedRow.Options.UseBackColor = true;
            this.GvView.Appearance.SelectedRow.Options.UseForeColor = true;
            this.GvView.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.GvView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7});
            this.GvView.GridControl = this.GCDate;
            this.GvView.IndicatorWidth = 60;
            this.GvView.Name = "GvView";
            this.GvView.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.GvView.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.GvView.OptionsBehavior.AllowSortAnimation = DevExpress.Utils.DefaultBoolean.False;
            this.GvView.OptionsBehavior.Editable = false;
            this.GvView.OptionsBehavior.ReadOnly = true;
            this.GvView.OptionsCustomization.AllowSort = false;
            this.GvView.OptionsMenu.ShowGroupSortSummaryItems = false;
            this.GvView.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.GvView.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.GvView.OptionsSelection.EnableAppearanceHideSelection = false;
            this.GvView.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CellSelect;
            this.GvView.OptionsSelection.ShowCheckBoxSelectorInGroupRow = DevExpress.Utils.DefaultBoolean.False;
            this.GvView.OptionsSelection.UseIndicatorForSelection = false;
            this.GvView.OptionsView.AllowHtmlDrawHeaders = true;
            this.GvView.OptionsView.ColumnHeaderAutoHeight = DevExpress.Utils.DefaultBoolean.True;
            this.GvView.OptionsView.ShowDetailButtons = false;
            this.GvView.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.GvView.OptionsView.ShowGroupPanel = false;
            this.GvView.RowHeight = 70;
            this.GvView.CustomDrawRowIndicator += new DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventHandler(this.GvView_CustomDrawRowIndicator);
            this.GvView.CustomDrawCell += new DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventHandler(this.GvView_CustomDrawCell);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "02/12 周日";
            this.gridColumn1.FieldName = "SCHEDULE_SUB1";
            this.gridColumn1.MinWidth = 25;
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 94;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "02/13 周一";
            this.gridColumn2.FieldName = "SCHEDULE_SUB2";
            this.gridColumn2.MinWidth = 25;
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 94;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "02/14 周二";
            this.gridColumn3.FieldName = "SCHEDULE_SUB3";
            this.gridColumn3.MinWidth = 25;
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            this.gridColumn3.Width = 94;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "03/15 周三";
            this.gridColumn4.FieldName = "SCHEDULE_SUB4";
            this.gridColumn4.MinWidth = 25;
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            this.gridColumn4.Width = 94;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "03/16 周四";
            this.gridColumn5.FieldName = "SCHEDULE_SUB5";
            this.gridColumn5.MinWidth = 25;
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            this.gridColumn5.Width = 94;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "03/17 周五";
            this.gridColumn6.FieldName = "SCHEDULE_SUB6";
            this.gridColumn6.MinWidth = 25;
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            this.gridColumn6.Width = 94;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "03/18 周六";
            this.gridColumn7.FieldName = "SCHEDULE_SUB7";
            this.gridColumn7.MinWidth = 25;
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            this.gridColumn7.Width = 94;
            // 
            // ToolTip
            // 
            this.ToolTip.GetActiveObjectInfo += new DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventHandler(this.ToolTip_GetActiveObjectInfo);
            // 
            // ucPersonalDate
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.MainPanel);
            this.Controls.Add(this.BtnAdd);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.BtnNext);
            this.Controls.Add(this.BtnPre);
            this.Controls.Add(this.dateEdit1);
            this.Controls.Add(this.labelControl1);
            this.Name = "ucPersonalDate";
            this.Size = new System.Drawing.Size(626, 364);
            ((System.ComponentModel.ISupportInitialize)(this.dateEdit1.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dateEdit1.Properties)).EndInit();
            this.MainPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.GCDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.GvView)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.DateEdit dateEdit1;
        private DevExpress.XtraEditors.SimpleButton BtnPre;
        private DevExpress.XtraEditors.SimpleButton BtnNext;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SimpleButton BtnAdd;
        private System.Windows.Forms.Panel MainPanel;
        private DevExpress.XtraGrid.GridControl GCDate;
        private DevExpress.XtraGrid.Views.Grid.GridView GvView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.Utils.ToolTipController ToolTip;
    }
}
