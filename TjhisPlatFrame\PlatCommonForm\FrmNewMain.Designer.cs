﻿namespace PlatCommonForm
{
    partial class FrmNewMain
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FrmNewMain));
            this.defaultLookAndFeel1 = new DevExpress.LookAndFeel.DefaultLookAndFeel(this.components);
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.barBtnSystemSetting = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barLargeButtonItem1 = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barLargeButtonItem2 = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barLargeButtonItem3 = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            this.barEditItem_skin = new DevExpress.XtraBars.BarEditItem();
            this.comboBox_skin = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.barStaticItem_hospital = new DevExpress.XtraBars.BarStaticItem();
            this.barStaticItem4 = new DevExpress.XtraBars.BarStaticItem();
            this.bsitemIPaddress = new DevExpress.XtraBars.BarStaticItem();
            this.barButtonItem2 = new DevExpress.XtraBars.BarButtonItem();
            this.barfont = new DevExpress.XtraBars.BarEditItem();
            this.cbFont = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.standaloneBarDockControl1 = new DevExpress.XtraBars.StandaloneBarDockControl();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.standaloneBarDockControl2 = new DevExpress.XtraBars.StandaloneBarDockControl();
            this.barStaticItem1 = new DevExpress.XtraBars.BarStaticItem();
            this.skinBarSubItem1 = new DevExpress.XtraBars.SkinBarSubItem();
            this.barStaticItem2 = new DevExpress.XtraBars.BarStaticItem();
            this.barStaticItemUser = new DevExpress.XtraBars.BarStaticItem();
            this.barStaticItemUserBottom = new DevExpress.XtraBars.BarStaticItem();
            this.barStaticItem3 = new DevExpress.XtraBars.BarStaticItem();
            this.barStaticItem5 = new DevExpress.XtraBars.BarStaticItem();
            this.barStaticItem6 = new DevExpress.XtraBars.BarStaticItem();
            this.barEditItem1 = new DevExpress.XtraBars.BarEditItem();
            this.repositoryItemLookUpEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.bar3 = new DevExpress.XtraBars.Bar();
            this.xtraTabbedMdiManager1 = new DevExpress.XtraTabbedMdi.XtraTabbedMdiManager(this.components);
            this.imagefrmchild = new DevExpress.Utils.ImageCollection(this.components);
            this.imageCollectionMenu = new DevExpress.Utils.ImageCollection(this.components);
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.palMenu = new System.Windows.Forms.Panel();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.lblStaticItemLogo = new DevExpress.XtraEditors.LabelControl();
            this.lblHospital = new DevExpress.XtraEditors.LabelControl();
            this.lblClose = new DevExpress.XtraEditors.LabelControl();
            this.lblMinSize = new DevExpress.XtraEditors.LabelControl();
            this.lblDate = new DevExpress.XtraEditors.LabelControl();
            this.lblDept = new DevExpress.XtraEditors.LabelControl();
            this.lblLoginName = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.barManager2 = new DevExpress.XtraBars.BarManager(this.components);
            this.topMainMenu = new DevExpress.XtraBars.Bar();
            this.barDockControl1 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl2 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl3 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl4 = new DevExpress.XtraBars.BarDockControl();
            this.topMainMenu1 = new DevExpress.XtraBars.Bar();
            this.tablePanel_mediumMenu = new DevExpress.Utils.Layout.TablePanel();
            this.labelControl_expend = new DevExpress.XtraEditors.LabelControl();
            this.tileBar1 = new DevExpress.XtraBars.Navigation.TileBar();
            this.tileBarGroup2 = new DevExpress.XtraBars.Navigation.TileBarGroup();
            this.topMainMenu2 = new DevExpress.XtraBars.Bar();
            this.bar1 = new DevExpress.XtraBars.Bar();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBox_skin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbFont)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemLookUpEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabbedMdiManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.imagefrmchild)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.imageCollectionMenu)).BeginInit();
            this.palMenu.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.barManager2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tablePanel_mediumMenu)).BeginInit();
            this.tablePanel_mediumMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // defaultLookAndFeel1
            // 
            this.defaultLookAndFeel1.LookAndFeel.SkinName = "VS2010";
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowMoveBarOnToolbar = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AllowShowToolbarsPopup = false;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar2});
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.DockControls.Add(this.standaloneBarDockControl2);
            this.barManager1.DockControls.Add(this.standaloneBarDockControl1);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSystemSetting,
            this.barLargeButtonItem1,
            this.barStaticItem1,
            this.barLargeButtonItem2,
            this.barLargeButtonItem3,
            this.skinBarSubItem1,
            this.barButtonItem1,
            this.barStaticItem2,
            this.barStaticItemUser,
            this.barStaticItemUserBottom,
            this.barStaticItem3,
            this.barStaticItem4,
            this.bsitemIPaddress,
            this.barStaticItem5,
            this.barStaticItem6,
            this.barButtonItem2,
            this.barStaticItem_hospital,
            this.barEditItem1,
            this.barfont,
            this.barEditItem_skin});
            this.barManager1.MainMenu = this.bar2;
            this.barManager1.MaxItemId = 20;
            this.barManager1.MdiMenuMergeStyle = DevExpress.XtraBars.BarMdiMenuMergeStyle.Never;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemLookUpEdit1,
            this.cbFont,
            this.comboBox_skin});
            // 
            // bar2
            // 
            this.bar2.BarAppearance.Normal.Options.UseBorderColor = true;
            this.bar2.BarName = "Main menu";
            this.bar2.DockCol = 0;
            this.bar2.DockRow = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Standalone;
            this.bar2.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barBtnSystemSetting, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barLargeButtonItem1, "", true, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barLargeButtonItem2, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barLargeButtonItem3, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonItem1, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barEditItem_skin, true),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barStaticItem_hospital, "", true, true, false, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barStaticItem4, "", true, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.bsitemIPaddress, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonItem2, "", true, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barfont)});
            this.bar2.OptionsBar.AllowQuickCustomization = false;
            this.bar2.OptionsBar.DrawDragBorder = false;
            this.bar2.OptionsBar.MultiLine = true;
            this.bar2.OptionsBar.UseWholeRow = true;
            this.bar2.StandaloneBarDockControl = this.standaloneBarDockControl1;
            this.bar2.Text = "Main menu";
            // 
            // barBtnSystemSetting
            // 
            this.barBtnSystemSetting.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnSystemSetting.AllowDrawArrow = false;
            this.barBtnSystemSetting.AllowRightClickInMenu = false;
            this.barBtnSystemSetting.Caption = "权限管理";
            this.barBtnSystemSetting.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.barBtnSystemSetting.Id = 8;
            this.barBtnSystemSetting.Name = "barBtnSystemSetting";
            this.barBtnSystemSetting.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barBtnSystemSetting.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnSystemSetting_ItemClick);
            // 
            // barLargeButtonItem1
            // 
            this.barLargeButtonItem1.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barLargeButtonItem1.AllowDrawArrow = false;
            this.barLargeButtonItem1.AllowRightClickInMenu = false;
            this.barLargeButtonItem1.Caption = "修改密码";
            this.barLargeButtonItem1.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.barLargeButtonItem1.Id = 0;
            this.barLargeButtonItem1.Name = "barLargeButtonItem1";
            this.barLargeButtonItem1.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barLargeButtonItem1_ItemClick);
            // 
            // barLargeButtonItem2
            // 
            this.barLargeButtonItem2.Caption = "关于简介";
            this.barLargeButtonItem2.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.barLargeButtonItem2.Id = 1;
            this.barLargeButtonItem2.Name = "barLargeButtonItem2";
            this.barLargeButtonItem2.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barLargeButtonItem2.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barLargeButtonItem2_ItemClick);
            // 
            // barLargeButtonItem3
            // 
            this.barLargeButtonItem3.Caption = "程序帮助";
            this.barLargeButtonItem3.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.barLargeButtonItem3.Id = 2;
            this.barLargeButtonItem3.Name = "barLargeButtonItem3";
            this.barLargeButtonItem3.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barLargeButtonItem3.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barLargeButtonItem3_ItemClick);
            // 
            // barButtonItem1
            // 
            this.barButtonItem1.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Left;
            this.barButtonItem1.Caption = "本平台为非涉密平台，严禁处理、传输国家秘密";
            this.barButtonItem1.Id = 4;
            this.barButtonItem1.ImageOptions.LargeImageIndex = 1;
            this.barButtonItem1.Name = "barButtonItem1";
            this.barButtonItem1.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem1_ItemClick);
            // 
            // barEditItem_skin
            // 
            this.barEditItem_skin.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barEditItem_skin.Caption = "更换皮肤";
            this.barEditItem_skin.Edit = this.comboBox_skin;
            this.barEditItem_skin.EditWidth = 100;
            this.barEditItem_skin.Id = 19;
            this.barEditItem_skin.Name = "barEditItem_skin";
            // 
            // comboBox_skin
            // 
            this.comboBox_skin.AutoHeight = false;
            this.comboBox_skin.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBox_skin.DropDownItemHeight = 25;
            this.comboBox_skin.DropDownRows = 8;
            this.comboBox_skin.Name = "comboBox_skin";
            this.comboBox_skin.NullText = "更换皮肤";
            this.comboBox_skin.NullValuePrompt = "更换皮肤";
            this.comboBox_skin.NullValuePromptShowForEmptyValue = true;
            this.comboBox_skin.PopupFormMinSize = new System.Drawing.Size(0, 120);
            this.comboBox_skin.PopupFormSize = new System.Drawing.Size(0, 120);
            this.comboBox_skin.SelectedIndexChanged += new System.EventHandler(this.comboBox_skin_SelectedIndexChanged);
            // 
            // barStaticItem_hospital
            // 
            this.barStaticItem_hospital.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barStaticItem_hospital.Caption = "北京天健医院";
            this.barStaticItem_hospital.Id = 16;
            this.barStaticItem_hospital.Name = "barStaticItem_hospital";
            // 
            // barStaticItem4
            // 
            this.barStaticItem4.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barStaticItem4.Caption = "本机IP：";
            this.barStaticItem4.Id = 11;
            this.barStaticItem4.Name = "barStaticItem4";
            // 
            // bsitemIPaddress
            // 
            this.bsitemIPaddress.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.bsitemIPaddress.Caption = "未知";
            this.bsitemIPaddress.Id = 12;
            this.bsitemIPaddress.ItemAppearance.Normal.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold);
            this.bsitemIPaddress.ItemAppearance.Normal.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.bsitemIPaddress.ItemAppearance.Normal.Options.UseFont = true;
            this.bsitemIPaddress.ItemAppearance.Normal.Options.UseForeColor = true;
            this.bsitemIPaddress.Name = "bsitemIPaddress";
            this.bsitemIPaddress.RightIndent = 20;
            // 
            // barButtonItem2
            // 
            this.barButtonItem2.Caption = "法律法规";
            this.barButtonItem2.Id = 15;
            this.barButtonItem2.Name = "barButtonItem2";
            this.barButtonItem2.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barButtonItem2.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem2_ItemClick);
            // 
            // barfont
            // 
            this.barfont.Caption = "字体选择";
            this.barfont.Edit = this.cbFont;
            this.barfont.EditValue = "标准字体";
            this.barfont.EditWidth = 100;
            this.barfont.Id = 18;
            this.barfont.Name = "barfont";
            this.barfont.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barfont.EditValueChanged += new System.EventHandler(this.barfont_EditValueChanged);
            // 
            // cbFont
            // 
            this.cbFont.AutoHeight = false;
            this.cbFont.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbFont.DropDownItemHeight = 25;
            this.cbFont.DropDownRows = 8;
            this.cbFont.Items.AddRange(new object[] {
            "特大号字体",
            "大号字体",
            "中号字体",
            "标准字体"});
            this.cbFont.Name = "cbFont";
            this.cbFont.PopupFormMinSize = new System.Drawing.Size(0, 120);
            this.cbFont.PopupFormSize = new System.Drawing.Size(0, 120);
            // 
            // standaloneBarDockControl1
            // 
            this.standaloneBarDockControl1.AutoSize = true;
            this.standaloneBarDockControl1.Border = DevExpress.XtraBars.BarDockControlBorder.Top;
            this.standaloneBarDockControl1.CausesValidation = false;
            this.standaloneBarDockControl1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.standaloneBarDockControl1.Location = new System.Drawing.Point(60, 506);
            this.standaloneBarDockControl1.Manager = this.barManager1;
            this.standaloneBarDockControl1.Name = "standaloneBarDockControl1";
            this.standaloneBarDockControl1.Size = new System.Drawing.Size(1028, 25);
            this.standaloneBarDockControl1.Text = "standaloneBarDockControl1";
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Manager = this.barManager1;
            this.barDockControlTop.Size = new System.Drawing.Size(1088, 0);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 529);
            this.barDockControlBottom.Manager = this.barManager1;
            this.barDockControlBottom.Size = new System.Drawing.Size(1088, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 0);
            this.barDockControlLeft.Manager = this.barManager1;
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 529);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1088, 0);
            this.barDockControlRight.Manager = this.barManager1;
            this.barDockControlRight.Size = new System.Drawing.Size(0, 529);
            // 
            // standaloneBarDockControl2
            // 
            this.standaloneBarDockControl2.AutoSize = true;
            this.standaloneBarDockControl2.Border = DevExpress.XtraBars.BarDockControlBorder.Bottom;
            this.standaloneBarDockControl2.CausesValidation = false;
            this.standaloneBarDockControl2.Dock = System.Windows.Forms.DockStyle.Top;
            this.standaloneBarDockControl2.Location = new System.Drawing.Point(60, 36);
            this.standaloneBarDockControl2.Manager = this.barManager1;
            this.standaloneBarDockControl2.Name = "standaloneBarDockControl2";
            this.standaloneBarDockControl2.Size = new System.Drawing.Size(1028, 24);
            this.standaloneBarDockControl2.Text = "standaloneBarDockControl2";
            // 
            // barStaticItem1
            // 
            this.barStaticItem1.AllowRightClickInMenu = false;
            this.barStaticItem1.Caption = "本平台为非涉密平台，严禁处理、传输国家秘密";
            this.barStaticItem1.Id = 5;
            this.barStaticItem1.Name = "barStaticItem1";
            // 
            // skinBarSubItem1
            // 
            this.skinBarSubItem1.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.skinBarSubItem1.Caption = "更换皮肤";
            this.skinBarSubItem1.Id = 3;
            this.skinBarSubItem1.Name = "skinBarSubItem1";
            this.skinBarSubItem1.GetItemData += new System.EventHandler(this.skinBarSubItem1_GetItemData);
            // 
            // barStaticItem2
            // 
            this.barStaticItem2.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barStaticItem2.Caption = "当前用户：";
            this.barStaticItem2.Id = 6;
            this.barStaticItem2.Name = "barStaticItem2";
            // 
            // barStaticItemUser
            // 
            this.barStaticItemUser.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barStaticItemUser.Caption = "ldp";
            this.barStaticItemUser.Id = 7;
            this.barStaticItemUser.ItemAppearance.Normal.Font = new System.Drawing.Font("Tahoma", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.barStaticItemUser.ItemAppearance.Normal.ForeColor = System.Drawing.Color.Red;
            this.barStaticItemUser.ItemAppearance.Normal.Options.UseFont = true;
            this.barStaticItemUser.ItemAppearance.Normal.Options.UseForeColor = true;
            this.barStaticItemUser.Name = "barStaticItemUser";
            // 
            // barStaticItemUserBottom
            // 
            this.barStaticItemUserBottom.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barStaticItemUserBottom.AllowRightClickInMenu = false;
            this.barStaticItemUserBottom.Caption = "天健";
            this.barStaticItemUserBottom.Id = 9;
            this.barStaticItemUserBottom.ItemAppearance.Normal.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold);
            this.barStaticItemUserBottom.ItemAppearance.Normal.Options.UseFont = true;
            this.barStaticItemUserBottom.Name = "barStaticItemUserBottom";
            // 
            // barStaticItem3
            // 
            this.barStaticItem3.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barStaticItem3.AllowRightClickInMenu = false;
            this.barStaticItem3.Caption = "当前用户：";
            this.barStaticItem3.Id = 10;
            this.barStaticItem3.Name = "barStaticItem3";
            // 
            // barStaticItem5
            // 
            this.barStaticItem5.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barStaticItem5.Caption = "当前科室：";
            this.barStaticItem5.Id = 13;
            this.barStaticItem5.Name = "barStaticItem5";
            this.barStaticItem5.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            // 
            // barStaticItem6
            // 
            this.barStaticItem6.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barStaticItem6.Caption = "科室名称";
            this.barStaticItem6.Id = 14;
            this.barStaticItem6.ItemAppearance.Normal.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.barStaticItem6.ItemAppearance.Normal.ForeColor = System.Drawing.Color.Red;
            this.barStaticItem6.ItemAppearance.Normal.Options.UseFont = true;
            this.barStaticItem6.ItemAppearance.Normal.Options.UseForeColor = true;
            this.barStaticItem6.Name = "barStaticItem6";
            this.barStaticItem6.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            // 
            // barEditItem1
            // 
            this.barEditItem1.Caption = "barEditItem1";
            this.barEditItem1.Edit = this.repositoryItemLookUpEdit1;
            this.barEditItem1.EditValue = "中号字";
            this.barEditItem1.EditWidth = 90;
            this.barEditItem1.Id = 17;
            this.barEditItem1.Name = "barEditItem1";
            // 
            // repositoryItemLookUpEdit1
            // 
            this.repositoryItemLookUpEdit1.AutoHeight = false;
            this.repositoryItemLookUpEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemLookUpEdit1.Name = "repositoryItemLookUpEdit1";
            // 
            // bar3
            // 
            this.bar3.BarName = "Status bar";
            this.bar3.CanDockStyle = DevExpress.XtraBars.BarCanDockStyle.Bottom;
            this.bar3.DockCol = 0;
            this.bar3.DockRow = 0;
            this.bar3.DockStyle = DevExpress.XtraBars.BarDockStyle.Bottom;
            this.bar3.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barStaticItem1, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barStaticItem2, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barStaticItemUser)});
            this.bar3.OptionsBar.AllowQuickCustomization = false;
            this.bar3.OptionsBar.DrawDragBorder = false;
            this.bar3.OptionsBar.Hidden = true;
            this.bar3.OptionsBar.UseWholeRow = true;
            this.bar3.Text = "Status bar";
            this.bar3.Visible = false;
            // 
            // xtraTabbedMdiManager1
            // 
            this.xtraTabbedMdiManager1.ClosePageButtonShowMode = DevExpress.XtraTab.ClosePageButtonShowMode.InActiveTabPageHeader;
            this.xtraTabbedMdiManager1.Images = this.imagefrmchild;
            this.xtraTabbedMdiManager1.MdiParent = this;
            this.xtraTabbedMdiManager1.ShowToolTips = DevExpress.Utils.DefaultBoolean.False;
            this.xtraTabbedMdiManager1.SelectedPageChanged += new System.EventHandler(this.xtraTabbedMdiManager1_SelectedPageChanged);
            // 
            // imagefrmchild
            // 
            this.imagefrmchild.ImageStream = ((DevExpress.Utils.ImageCollectionStreamer)(resources.GetObject("imagefrmchild.ImageStream")));
            // 
            // imageCollectionMenu
            // 
            this.imageCollectionMenu.ImageStream = ((DevExpress.Utils.ImageCollectionStreamer)(resources.GetObject("imageCollectionMenu.ImageStream")));
            this.imageCollectionMenu.InsertGalleryImage("open2_16x16.png", "images/actions/open2_16x16.png", DevExpress.Images.ImageResourceCache.Default.GetImage("images/actions/open2_16x16.png"), 0);
            this.imageCollectionMenu.Images.SetKeyName(0, "open2_16x16.png");
            this.imageCollectionMenu.InsertGalleryImage("barseries_16x16.png", "images/filter/barseries_16x16.png", DevExpress.Images.ImageResourceCache.Default.GetImage("images/filter/barseries_16x16.png"), 1);
            this.imageCollectionMenu.Images.SetKeyName(1, "barseries_16x16.png");
            this.imageCollectionMenu.InsertGalleryImage("openhyperlink_16x16.png", "images/actions/openhyperlink_16x16.png", DevExpress.Images.ImageResourceCache.Default.GetImage("images/actions/openhyperlink_16x16.png"), 2);
            this.imageCollectionMenu.Images.SetKeyName(2, "openhyperlink_16x16.png");
            // 
            // timer1
            // 
            this.timer1.Interval = 3000;
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // palMenu
            // 
            this.palMenu.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(65)))), ((int)(((byte)(124)))), ((int)(((byte)(255)))));
            this.palMenu.Controls.Add(this.tableLayoutPanel1);
            this.palMenu.Dock = System.Windows.Forms.DockStyle.Top;
            this.palMenu.Location = new System.Drawing.Point(0, 0);
            this.palMenu.Name = "palMenu";
            this.palMenu.Size = new System.Drawing.Size(1088, 36);
            this.palMenu.TabIndex = 9;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 9;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 100F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 40F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 40F));
            this.tableLayoutPanel1.Controls.Add(this.lblStaticItemLogo, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.lblHospital, 2, 0);
            this.tableLayoutPanel1.Controls.Add(this.lblClose, 8, 0);
            this.tableLayoutPanel1.Controls.Add(this.lblMinSize, 7, 0);
            this.tableLayoutPanel1.Controls.Add(this.lblDate, 5, 0);
            this.tableLayoutPanel1.Controls.Add(this.lblDept, 4, 0);
            this.tableLayoutPanel1.Controls.Add(this.lblLoginName, 3, 0);
            this.tableLayoutPanel1.Controls.Add(this.labelControl1, 1, 0);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 1;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(1088, 36);
            this.tableLayoutPanel1.TabIndex = 0;
            // 
            // lblStaticItemLogo
            // 
            this.lblStaticItemLogo.AllowGlyphSkinning = DevExpress.Utils.DefaultBoolean.True;
            this.lblStaticItemLogo.Appearance.ForeColor = System.Drawing.Color.White;
            this.lblStaticItemLogo.Appearance.Options.UseForeColor = true;
            this.lblStaticItemLogo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lblStaticItemLogo.Location = new System.Drawing.Point(3, 3);
            this.lblStaticItemLogo.Name = "lblStaticItemLogo";
            this.lblStaticItemLogo.Size = new System.Drawing.Size(94, 30);
            this.lblStaticItemLogo.TabIndex = 0;
            this.lblStaticItemLogo.Tag = "logo.svg";
            // 
            // lblHospital
            // 
            this.lblHospital.Appearance.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Bold);
            this.lblHospital.Appearance.ForeColor = System.Drawing.Color.White;
            this.lblHospital.Appearance.Options.UseFont = true;
            this.lblHospital.Appearance.Options.UseForeColor = true;
            this.lblHospital.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lblHospital.Location = new System.Drawing.Point(123, 3);
            this.lblHospital.Name = "lblHospital";
            this.lblHospital.Size = new System.Drawing.Size(748, 30);
            this.lblHospital.TabIndex = 1;
            this.lblHospital.Text = "天健";
            // 
            // lblClose
            // 
            this.lblClose.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lblClose.Location = new System.Drawing.Point(1051, 3);
            this.lblClose.Name = "lblClose";
            this.lblClose.Size = new System.Drawing.Size(34, 30);
            this.lblClose.TabIndex = 2;
            this.lblClose.Tag = "退出.svg";
            this.lblClose.Click += new System.EventHandler(this.lblClose_Click);
            // 
            // lblMinSize
            // 
            this.lblMinSize.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lblMinSize.Location = new System.Drawing.Point(1011, 3);
            this.lblMinSize.Name = "lblMinSize";
            this.lblMinSize.Size = new System.Drawing.Size(34, 30);
            this.lblMinSize.TabIndex = 3;
            this.lblMinSize.Tag = "最小化.svg";
            this.lblMinSize.Click += new System.EventHandler(this.lblMaxSize_Click);
            // 
            // lblDate
            // 
            this.lblDate.Appearance.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lblDate.Appearance.ForeColor = System.Drawing.Color.White;
            this.lblDate.Appearance.Options.UseFont = true;
            this.lblDate.Appearance.Options.UseForeColor = true;
            this.lblDate.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lblDate.Location = new System.Drawing.Point(953, 3);
            this.lblDate.Name = "lblDate";
            this.lblDate.Size = new System.Drawing.Size(32, 30);
            this.lblDate.TabIndex = 4;
            this.lblDate.Text = "时间";
            // 
            // lblDept
            // 
            this.lblDept.Appearance.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lblDept.Appearance.ForeColor = System.Drawing.Color.White;
            this.lblDept.Appearance.Options.UseFont = true;
            this.lblDept.Appearance.Options.UseForeColor = true;
            this.lblDept.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lblDept.Location = new System.Drawing.Point(915, 3);
            this.lblDept.Name = "lblDept";
            this.lblDept.Size = new System.Drawing.Size(32, 30);
            this.lblDept.TabIndex = 5;
            this.lblDept.Text = "部门";
            // 
            // lblLoginName
            // 
            this.lblLoginName.Appearance.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.lblLoginName.Appearance.ForeColor = System.Drawing.Color.White;
            this.lblLoginName.Appearance.Options.UseFont = true;
            this.lblLoginName.Appearance.Options.UseForeColor = true;
            this.lblLoginName.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lblLoginName.Location = new System.Drawing.Point(877, 3);
            this.lblLoginName.Name = "lblLoginName";
            this.lblLoginName.Size = new System.Drawing.Size(32, 30);
            this.lblLoginName.TabIndex = 6;
            this.lblLoginName.Text = "姓名";
            // 
            // labelControl1
            // 
            this.labelControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.labelControl1.Location = new System.Drawing.Point(103, 3);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(14, 30);
            this.labelControl1.TabIndex = 7;
            this.labelControl1.Tag = "Line.svg";
            // 
            // barManager2
            // 
            this.barManager2.AllowQuickCustomization = false;
            this.barManager2.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.topMainMenu});
            this.barManager2.DockControls.Add(this.barDockControl1);
            this.barManager2.DockControls.Add(this.barDockControl2);
            this.barManager2.DockControls.Add(this.barDockControl3);
            this.barManager2.DockControls.Add(this.barDockControl4);
            this.barManager2.Form = this;
            this.barManager2.MainMenu = this.topMainMenu;
            // 
            // topMainMenu
            // 
            this.topMainMenu.BarName = "Custom 3";
            this.topMainMenu.DockCol = 0;
            this.topMainMenu.DockRow = 0;
            this.topMainMenu.DockStyle = DevExpress.XtraBars.BarDockStyle.Standalone;
            this.topMainMenu.OptionsBar.AllowQuickCustomization = false;
            this.topMainMenu.OptionsBar.DrawDragBorder = false;
            this.topMainMenu.OptionsBar.MultiLine = true;
            this.topMainMenu.OptionsBar.UseWholeRow = true;
            this.topMainMenu.StandaloneBarDockControl = this.standaloneBarDockControl2;
            this.topMainMenu.Text = "Custom 3";
            // 
            // barDockControl1
            // 
            this.barDockControl1.CausesValidation = false;
            this.barDockControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControl1.Location = new System.Drawing.Point(0, 0);
            this.barDockControl1.Manager = this.barManager2;
            this.barDockControl1.Size = new System.Drawing.Size(1088, 0);
            // 
            // barDockControl2
            // 
            this.barDockControl2.CausesValidation = false;
            this.barDockControl2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControl2.Location = new System.Drawing.Point(0, 529);
            this.barDockControl2.Manager = this.barManager2;
            this.barDockControl2.Size = new System.Drawing.Size(1088, 0);
            // 
            // barDockControl3
            // 
            this.barDockControl3.CausesValidation = false;
            this.barDockControl3.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControl3.Location = new System.Drawing.Point(0, 0);
            this.barDockControl3.Manager = this.barManager2;
            this.barDockControl3.Size = new System.Drawing.Size(0, 529);
            // 
            // barDockControl4
            // 
            this.barDockControl4.CausesValidation = false;
            this.barDockControl4.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControl4.Location = new System.Drawing.Point(1088, 0);
            this.barDockControl4.Manager = this.barManager2;
            this.barDockControl4.Size = new System.Drawing.Size(0, 529);
            // 
            // topMainMenu1
            // 
            this.topMainMenu1.BarName = "Main menu";
            this.topMainMenu1.DockCol = 0;
            this.topMainMenu1.DockRow = 0;
            this.topMainMenu1.DockStyle = DevExpress.XtraBars.BarDockStyle.Standalone;
            this.topMainMenu1.FloatLocation = new System.Drawing.Point(385, 168);
            this.topMainMenu1.FloatSize = new System.Drawing.Size(46, 22);
            this.topMainMenu1.OptionsBar.MultiLine = true;
            this.topMainMenu1.OptionsBar.UseWholeRow = true;
            this.topMainMenu1.StandaloneBarDockControl = this.standaloneBarDockControl2;
            this.topMainMenu1.Text = "Main menu";
            // 
            // tablePanel_mediumMenu
            // 
            this.tablePanel_mediumMenu.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.tablePanel_mediumMenu.Columns.AddRange(new DevExpress.Utils.Layout.TablePanelColumn[] {
            new DevExpress.Utils.Layout.TablePanelColumn(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 43F)});
            this.tablePanel_mediumMenu.Controls.Add(this.labelControl_expend);
            this.tablePanel_mediumMenu.Controls.Add(this.tileBar1);
            this.tablePanel_mediumMenu.Dock = System.Windows.Forms.DockStyle.Left;
            this.tablePanel_mediumMenu.Location = new System.Drawing.Point(0, 36);
            this.tablePanel_mediumMenu.Margin = new System.Windows.Forms.Padding(0);
            this.tablePanel_mediumMenu.Name = "tablePanel_mediumMenu";
            this.tablePanel_mediumMenu.Rows.AddRange(new DevExpress.Utils.Layout.TablePanelRow[] {
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F),
            new DevExpress.Utils.Layout.TablePanelRow(DevExpress.Utils.Layout.TablePanelEntityStyle.Absolute, 26F)});
            this.tablePanel_mediumMenu.Size = new System.Drawing.Size(60, 493);
            this.tablePanel_mediumMenu.TabIndex = 24;
            // 
            // labelControl_expend
            // 
            this.tablePanel_mediumMenu.SetColumn(this.labelControl_expend, 0);
            this.labelControl_expend.Dock = System.Windows.Forms.DockStyle.Fill;
            this.labelControl_expend.Location = new System.Drawing.Point(0, 0);
            this.labelControl_expend.Margin = new System.Windows.Forms.Padding(0);
            this.labelControl_expend.Name = "labelControl_expend";
            this.tablePanel_mediumMenu.SetRow(this.labelControl_expend, 0);
            this.labelControl_expend.Size = new System.Drawing.Size(60, 26);
            this.labelControl_expend.TabIndex = 2;
            // 
            // tileBar1
            // 
            this.tablePanel_mediumMenu.SetColumn(this.tileBar1, 0);
            this.tileBar1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tileBar1.DropDownOptions.BeakColor = System.Drawing.Color.Empty;
            this.tileBar1.Groups.Add(this.tileBarGroup2);
            this.tileBar1.IndentBetweenItems = 0;
            this.tileBar1.ItemSize = 58;
            this.tileBar1.Location = new System.Drawing.Point(1, 26);
            this.tileBar1.Margin = new System.Windows.Forms.Padding(1, 0, 1, 0);
            this.tileBar1.Name = "tileBar1";
            this.tileBar1.Orientation = System.Windows.Forms.Orientation.Vertical;
            this.tileBar1.Padding = new System.Windows.Forms.Padding(0);
            this.tablePanel_mediumMenu.SetRow(this.tileBar1, 1);
            this.tileBar1.ScrollMode = DevExpress.XtraEditors.TileControlScrollMode.ScrollButtons;
            this.tileBar1.ShowGroupText = false;
            this.tileBar1.Size = new System.Drawing.Size(58, 467);
            this.tileBar1.TabIndex = 1;
            this.tileBar1.Text = "tileBar1";
            this.tileBar1.WideTileWidth = 30;
            // 
            // tileBarGroup2
            // 
            this.tileBarGroup2.Name = "tileBarGroup2";
            // 
            // topMainMenu2
            // 
            this.topMainMenu2.BarName = "MainMenu";
            this.topMainMenu2.DockCol = 0;
            this.topMainMenu2.DockRow = 0;
            this.topMainMenu2.DockStyle = DevExpress.XtraBars.BarDockStyle.Standalone;
            this.topMainMenu2.OptionsBar.AllowQuickCustomization = false;
            this.topMainMenu2.OptionsBar.UseWholeRow = true;
            this.topMainMenu2.StandaloneBarDockControl = this.standaloneBarDockControl2;
            this.topMainMenu2.Text = "MainMenu";
            // 
            // bar1
            // 
            this.bar1.BarName = "Custom 4";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Standalone;
            this.bar1.OptionsBar.UseWholeRow = true;
            this.bar1.StandaloneBarDockControl = this.standaloneBarDockControl2;
            this.bar1.Text = "Custom 4";
            // 
            // FrmNewMain
            // 
            this.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(234)))), ((int)(((byte)(234)))));
            this.Appearance.Options.UseBackColor = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1088, 529);
            this.Controls.Add(this.standaloneBarDockControl1);
            this.Controls.Add(this.standaloneBarDockControl2);
            this.Controls.Add(this.tablePanel_mediumMenu);
            this.Controls.Add(this.palMenu);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Controls.Add(this.barDockControl3);
            this.Controls.Add(this.barDockControl4);
            this.Controls.Add(this.barDockControl2);
            this.Controls.Add(this.barDockControl1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.IsMdiContainer = true;
            this.Name = "FrmNewMain";
            this.Text = "北京天健源达科技股份有限公司";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FrmNewMain_FormClosing);
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.FrmNewMain_FormClosed);
            this.Load += new System.EventHandler(this.FrmNewMain_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBox_skin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbFont)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemLookUpEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabbedMdiManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.imagefrmchild)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.imageCollectionMenu)).EndInit();
            this.palMenu.ResumeLayout(false);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.tableLayoutPanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.barManager2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tablePanel_mediumMenu)).EndInit();
            this.tablePanel_mediumMenu.ResumeLayout(false);
            this.tablePanel_mediumMenu.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.LookAndFeel.DefaultLookAndFeel defaultLookAndFeel1;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraBars.BarLargeButtonItem barLargeButtonItem1;
        private DevExpress.XtraBars.BarLargeButtonItem barLargeButtonItem2;
        private DevExpress.XtraBars.BarLargeButtonItem barLargeButtonItem3;
        private DevExpress.XtraBars.SkinBarSubItem skinBarSubItem1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem1;
        private DevExpress.XtraBars.Bar bar3;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraTabbedMdi.XtraTabbedMdiManager xtraTabbedMdiManager1;
        private DevExpress.Utils.ImageCollection imageCollectionMenu;
        private DevExpress.XtraBars.BarStaticItem barStaticItem1;
        private DevExpress.XtraBars.BarStaticItem barStaticItem2;
        private DevExpress.XtraBars.BarStaticItem barStaticItemUser;
        private DevExpress.Utils.ImageCollection imagefrmchild;
        private DevExpress.XtraBars.BarLargeButtonItem barBtnSystemSetting;
        private DevExpress.XtraBars.BarStaticItem barStaticItem3;
        private DevExpress.XtraBars.BarStaticItem barStaticItemUserBottom;
        private DevExpress.XtraBars.BarStaticItem barStaticItem4;
        private DevExpress.XtraBars.BarStaticItem bsitemIPaddress;
        private DevExpress.XtraBars.BarStaticItem barStaticItem5;
        private DevExpress.XtraBars.BarStaticItem barStaticItem6;
        private System.Windows.Forms.Timer timer1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem2;
        private DevExpress.XtraBars.BarStaticItem barStaticItem_hospital;
        private DevExpress.XtraBars.BarEditItem barEditItem1;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repositoryItemLookUpEdit1;
        private DevExpress.XtraBars.BarEditItem barfont;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox cbFont;
        private DevExpress.XtraBars.BarEditItem barEditItem_skin;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox comboBox_skin;
        private System.Windows.Forms.Panel palMenu;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private DevExpress.XtraEditors.LabelControl lblStaticItemLogo;
        private DevExpress.XtraEditors.LabelControl lblHospital;
        private DevExpress.XtraEditors.LabelControl lblClose;
        private DevExpress.XtraEditors.LabelControl lblMinSize;
        private DevExpress.XtraEditors.LabelControl lblDate;
        private DevExpress.XtraEditors.LabelControl lblDept;
        private DevExpress.XtraEditors.LabelControl lblLoginName;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraBars.BarDockControl barDockControl3;
        private DevExpress.XtraBars.BarManager barManager2;
        private DevExpress.XtraBars.Bar topMainMenu1;
        private DevExpress.XtraBars.BarDockControl barDockControl1;
        private DevExpress.XtraBars.BarDockControl barDockControl2;
        private DevExpress.XtraBars.BarDockControl barDockControl4;
        private DevExpress.Utils.Layout.TablePanel tablePanel_mediumMenu;
        private DevExpress.XtraBars.StandaloneBarDockControl standaloneBarDockControl2;
        private DevExpress.XtraBars.Navigation.TileBar tileBar1;
        private DevExpress.XtraBars.Navigation.TileBarGroup tileBarGroup2;
        private DevExpress.XtraEditors.LabelControl labelControl_expend;
        private DevExpress.XtraBars.Bar topMainMenu2;
        private DevExpress.XtraBars.Bar topMainMenu;
        private DevExpress.XtraBars.StandaloneBarDockControl standaloneBarDockControl1;
        private DevExpress.XtraBars.Bar bar1;
    }
}