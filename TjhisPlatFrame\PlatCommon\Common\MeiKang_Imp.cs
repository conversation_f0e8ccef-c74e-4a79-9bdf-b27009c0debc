﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace PlatPublic.Common
{
    public class MeiKang_Imp
    {
        #region 美康合理用药接口  kangjia add 20170307

        #region 初始化美康合理用药接口
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_Init", CharSet = CharSet.Ansi)]

        public static extern int MDC_Init(String pcCheckMode, String pcHisCode, String pcDoctorCode);

        #endregion

        #region 传病人基本记录信息函数 
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_SetPatient", CharSet = CharSet.Ansi)]
        public static extern int MDC_SetPatient(String pcPatCode,
                    String pcInHospNo,
                    String pcVisitCode,
                    String pcName,
                    String pcSex,
                    String pcBirthday,
                    String pcHeightCM,
                    String pcWeighKG,
                    String pcDeptCode,
                    String pcDeptName,
                    String pcDoctorCode,
                    String pcDoctorName,
                    int piPatStatus,
                    int piIsLactation,
                    int piIsPregnancy,
                    String pcPregStartDate,
                    int piHepDamageDegree,
                int piRenDamageDegree);
        #endregion

        #region 传病人药品记录信息 
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_AddScreenDrug", CharSet = CharSet.Ansi)]
        public static extern int MDC_AddScreenDrug(
                    String pcIndex,
                    int piOrderNo,
                    String pcDrugUniqueCode,
                    String pcDrugName,
                    String pcDosePerTime,
                    String pcDoseUnit,
                    String pcFrequency,
                    String pcRouteCode,
                    String pcRouteName,
                    String pcStartTime,
                    String pcEndTime,
                    String pcExecuteTime,
                    String pcGroupTag,
                    String pcIsTempDrug,
                    String pcOrderType,
                    String pcDeptCode,
                    String pcDeptName,
                    String pcDoctorCode,
                    String pcDoctorName,
                    String pcRecipNo,
                    String pcNum,
                    String pcNumUnit,
                    String pcPurpose,
                    String pcOprCode,
                    String pcMediTime,
                    String pcRemark);
        #endregion

        #region 传入病人过敏史记录信息 
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_AddAller", CharSet = CharSet.Ansi)]
        public static extern int MDC_AddAller(string pcIndex,
                    string pcAllerCode,
                    string pcAllerName,
                    string pcAllerSymptom);
        #endregion

        #region 传入病人诊断记录信息
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_AddMedCond", CharSet = CharSet.Ansi)]
        public static extern int MDC_AddMedCond(string pcIndex,
                    string pcDiseaseCode,
                    string pcDiseaseName,
                    string pcRecipNo);
        #endregion

        #region 传入病人手术记录信息
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_AddOperation", CharSet = CharSet.Ansi)]
        public static extern int MDC_AddOperation(string pcIndex,
                    string pcOprCode,
                    string pcOprName,
                    string pcOprStartDateTime,
                    string pcOprEndDateTime);
        #endregion

        #region 调用病人补充信息函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_AddJsonInfo", CharSet = CharSet.Ansi)]
        public static extern int MDC_AddJsonInfo(string pcJson);
        #endregion

        #region 审查函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_DoCheck", CharSet = CharSet.Ansi)]
        public static extern int MDC_DoCheck(int piShowMode,
                    int piIsSave);
        #endregion

        #region 获取药品医嘱警示级别
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_GetWarningCode", CharSet = CharSet.Ansi)]
        public static extern int MDC_GetWarningCode(string pcIndex);
        #endregion

        #region 获取一条药品医嘱的审查结果提示窗口函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_ShowWarningHint", CharSet = CharSet.Ansi)]
        public static extern int MDC_ShowWarningHint(string pcIndex);
        #endregion

        #region 关闭一条药品医嘱的审查结果提示窗口函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_CloseWarningHint", CharSet = CharSet.Ansi)]
        public static extern int MDC_CloseWarningHint();
        #endregion

        #region 获取审查结果详细信息函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_GetResultDetail", CharSet = CharSet.Ansi,CallingConvention=CallingConvention.StdCall)]
        public static extern int MDC_GetResultDetail(string  pcIndex);
        #endregion

        #region 传入一个查询药品函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_DoSetDrug", CharSet = CharSet.Ansi)]
        public static extern int MDC_DoSetDrug(String pcDrugUniqueCode, String pcDrugName);
        #endregion

        #region 获取药品查询项目有效性函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_DoRefDrugEnable", CharSet = CharSet.Ansi)]
        public static extern string MDC_DoRefDrugEnable(int piQueryType);
        #endregion

        #region 查询药品信息函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_DoRefDrug", CharSet = CharSet.Ansi)]
        public static extern int MDC_DoRefDrug(int piQueryType);
        #endregion

        #region 关闭浮动窗口函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_CloseDrugHint", CharSet = CharSet.Ansi)]
        public static extern int MDC_CloseDrugHint();
        #endregion

        #region 本地参数设置窗口函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_Settings", CharSet = CharSet.Ansi)]
        public static extern int MDC_Settings();
        #endregion

        #region 调用药研究窗口函数
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_DoMediStudy", CharSet = CharSet.Ansi)]
        public static extern int MDC_DoMediStudy(string pcUseTime);
        #endregion

        //#region 获取PASS系统最后一次错误信息函数
        //[DllImport("PASS4Invoke.dll", EntryPoint = "MDC_GetLastError", CharSet = CharSet.Ansi)]
        //public static extern string MDC_GetLastError();
        //#endregion
        #region 药师干预系统 by lions 
        /// <summary>
        /// 根据返回值的通过1和不通过值0来进行his系统不同业务逻辑的处理
        /// </summary>
        /// <param name="pcPatCode">表示病人ID，与参数pcPatCode唯一确定一个病人，此参数不能为空。要求与MDC_SetPatient函数传入的pcPatCode参数值完全一致</param>
        /// <param name="pcInHospNo">表示病人门诊号或住院号，此参数不能为空。要求与MDC_SetPatient函数传入的pcInHospNo参数值完全一致</param>
        /// <param name="pcVisitCode">表示病人就诊次数或住院次数，与参数pcPatCode唯一确定一个病人，如果HIS系统没有此信息，则可传入"1"。要求与MDC_SetPatient函数传入的pcVisitCode参数值完全一致</param>
        /// <param name="pcRecipNo">A.	门诊传空字符串。 B.住院传医嘱唯一码，与MDC_AddScreenDrug函数传入pcindex参数值完全一致。 注意：只有在提交医嘱，对审核没有问题的医嘱准予提交，对于有问题的医嘱返回修改，则才循环此方法并此参数传入医嘱唯一码，否则传入空即可 </param>
        /// <param name="piTaskType">整型，表示病人类型：1表示住院病人（默认），2表示门诊病人。</param>
        /// <returns>药师干预结果： A.门诊 1-通过，0-不能通过 B.住院 1-通过，0-不能通过，-1-待定 </returns>
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_GetTaskStatus", CharSet = CharSet.Ansi)]
        public static extern int MDC_GetTaskStatus(
                    String pcPatCode,
                    String pcInHospNo,
                    String pcVisitCode,
                    String pcRecipNo,
                    int piTaskType);
        /// <summary>
        /// 根据json返回值操作
        /// </summary>
        /// <param name="pcPatCode">表示病人ID，与参数pcPatCode唯一确定一个病人，此参数不能为空。要求与MDC_SetPatient函数传入的pcPatCode参数值完全一致</param>
        /// <param name="pcInHospNo">表示病人门诊号或住院号，此参数不能为空。要求与MDC_SetPatient函数传入的pcInHospNo参数值完全一致</param>
        /// <param name="pcVisitCode">表示病人就诊次数或住院次数，与参数pcPatCode唯一确定一个病人，如果HIS系统没有此信息，则可传入"1"。要求与MDC_SetPatient函数传入的pcVisitCode参数值完全一致</param>
        /// <param name="pcRecipNo">A.	门诊传空字符串。 B.住院传医嘱唯一码，与MDC_AddScreenDrug函数传入pcindex参数值完全一致。 注意：只有在提交医嘱，对审核没有问题的医嘱准予提交，对于有问题的医嘱返回修改，则才循环此方法并此参数传入医嘱唯一码，否则传入空即可 </param>
        /// <param name="piTaskType">整型，表示病人类型：1表示住院病人（默认），2表示门诊病人。</param>
        /// <returns>object对象，如下例所示："Status":9,"OverTime":180,"PassTime":0,"Paused":0,"Reason":"","MHisCode":0,"CaseID":"0_2_BRBM22-05_MZH22-05_MZH22-05","TaskId":"201808280954468213","StatusNo":null,"Mobile":"","TelePhone":"","Urgent":-1,"NeedApply":0,"NeedAutoIntervene":0,"NeedCounterSign":2,"WorkerId":"4","WorkerName":"药师1","StatusDesc":"医生双签后药师复核通过","Type":2,"OrderNum":null,"NewUseReason":null}
        /// Status：任务状态。
	         ///-15 含拦截问题，医生需修改
          ///   -14 医生修改后，存在药师关注问题
          ///   -13 含药师关注问题，提请药师审核
          ///   -12 医生修改后提请药师审核
          ///   -11 自动干预系统预判不通过
          ///   -10 医生修改后重点关注提请药师审核
          ///   -9 重点关注提请药师审核
          ///   -8 医生双签后药师复核不通过
          ///   -7 医生修改后系统预判不通过
          ///   -6 医生已填写理由并双签
          ///   -5 医生提请药师审核
          ///   -4 默认状态
          ///   -3 医生修改后药师再次不通过
          ///   -2 药师首次审核不通过
          ///   -1 系统预判不通过
          ///   0 系统预判通过
          ///   1 药师首次审核通过
          ///   2 医生双签后自动通过
          ///   3 医生修改后药师审核通过
          ///   4 自动通过-已关闭系统干预功能
          ///   5 自动通过-该科室未分配审方药师
          ///   6 超时通过-药师还未获取
          ///   7 自动通过-无审方药师在线
          ///   8 自动通过-该科室审方药师不在线
          ///   9 医生双签后药师复核通过
          ///  10 系统自动干预后通过
          ///  11 医生修改后系统预判通过
          ///  12 自动通过-药师离开或退出
          ///  13 超时通过-医生修改后，药师未审核
          ///  14 超时通过-医生双签后，药师未复核
          ///  15 超时通过-药师还未审核
          ///  16 自动通过-医生修改后，已关闭干预功能
          ///  17 自动通过-医生双签后，已关闭干预功能
          ///  18 自动通过-医生修改后，药师离开或退出
          ///  19 自动通过-医生双签后，药师离开或退出
          ///  20 系统预判问题经药师确认后通过
          ///  21 自动通过-未达到药师监测标准
          ///  22 自动通过-不进行药师审方 
        /// </returns>
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_GetTaskDetail", CharSet = CharSet.Ansi)]
        public static extern object MDC_GetTaskDetail(
                    String pcPatCode,
                    String pcInHospNo,
                    String pcVisitCode,
                    String pcRecipNo,
                    int piTaskType);
        #endregion
        #region PASS退出
        [DllImport("PASS4Invoke.dll", EntryPoint = "MDC_Quit", CharSet = CharSet.Ansi)]
        public static extern int MDC_Quit();
        #endregion

        #endregion
    }
}
