﻿using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using Utility;
using Utility.OracleODP;
using Oracle.ManagedDataAccess.Client;

namespace OracleDAL
{

    public class MAIN_MENU_Dao_Base
    {
        #region   Method
        public bool Exists(string NODE, string PARENTNODE, OracleBaseClass db)
        {
            #region  init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from MAIN_MENU");
            strSql.Append(" where ");
            strSql.Append(" NODE = :NODE and  ");
            strSql.Append(" PARENTNODE = :PARENTNODE ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":NODE", OracleDbType.Varchar2, 6);
            p.Value = NODE;
            parameters.Add(p);

            p = new OracleParameter(":PARENTNODE", OracleDbType.Varchar2, 8);
            p.Value = PARENTNODE;
            parameters.Add(p);

            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    int cmdresult;
                    cmdresult = int.Parse(ds.Tables[0].Rows[0][0].ToString());
                    if (cmdresult <= 0)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                    return false;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.MAIN_MENU model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into MAIN_MENU(");
            strSql.Append("NODE,SORTNO,PARENTNODE,DISPLYTEXT,ICON,OPENCLASS,TOOLTIP,LARGEICON,SMALLICON,STYLE");
            strSql.Append(") values (");
            strSql.Append(":NODE,:SORTNO,:PARENTNODE,:DISPLYTEXT,:ICON,:OPENCLASS,:TOOLTIP,:LARGEICON,:SMALLICON,:STYLE");
            strSql.Append(") ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":NODE", OracleDbType.Varchar2, 6);
            p.Value = model.NODE;
            parameters.Add(p);

            p = new OracleParameter(":SORTNO", OracleDbType.Decimal, 3);
            p.Value = model.SORTNO;
            parameters.Add(p);

            p = new OracleParameter(":PARENTNODE", OracleDbType.Varchar2, 8);
            p.Value = model.PARENTNODE;
            parameters.Add(p);

            p = new OracleParameter(":DISPLYTEXT", OracleDbType.Varchar2, 50);
            p.Value = model.DISPLYTEXT;
            parameters.Add(p);

            p = new OracleParameter(":ICON", OracleDbType.Varchar2, 50);
            p.Value = model.ICON;
            parameters.Add(p);

            p = new OracleParameter(":OPENCLASS", OracleDbType.Varchar2, 50);
            p.Value = model.OPENCLASS;
            parameters.Add(p);

            p = new OracleParameter(":TOOLTIP", OracleDbType.Varchar2, 50);
            p.Value = model.TOOLTIP;
            parameters.Add(p);

            p = new OracleParameter(":LARGEICON", OracleDbType.Varchar2, 50);
            p.Value = model.LARGEICON;
            parameters.Add(p);

            p = new OracleParameter(":SMALLICON", OracleDbType.Varchar2, 50);
            p.Value = model.SMALLICON;
            parameters.Add(p);

            p = new OracleParameter(":STYLE", OracleDbType.Varchar2, 50);
            p.Value = model.STYLE;
            parameters.Add(p);
            #endregion
            try
            {

                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.MAIN_MENU model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update MAIN_MENU set ");

            strSql.Append(" NODE = :NODE , ");
            strSql.Append(" SORTNO = :SORTNO , ");
            strSql.Append(" PARENTNODE = :PARENTNODE , ");
            strSql.Append(" DISPLYTEXT = :DISPLYTEXT , ");
            strSql.Append(" ICON = :ICON , ");
            strSql.Append(" OPENCLASS = :OPENCLASS , ");
            strSql.Append(" TOOLTIP = :TOOLTIP , ");
            strSql.Append(" LARGEICON = :LARGEICON , ");
            strSql.Append(" SMALLICON = :SMALLICON , ");
            strSql.Append(" STYLE = :STYLE  ");
            strSql.Append(" where NODE=:NODE and PARENTNODE=:PARENTNODE  ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":NODE", OracleDbType.Varchar2, 6);
            p.Value = model.NODE;
            parameters.Add(p);

            p = new OracleParameter(":SORTNO", OracleDbType.Decimal, 3);
            p.Value = model.SORTNO;
            parameters.Add(p);

            p = new OracleParameter(":PARENTNODE", OracleDbType.Varchar2, 8);
            p.Value = model.PARENTNODE;
            parameters.Add(p);

            p = new OracleParameter(":DISPLYTEXT", OracleDbType.Varchar2, 50);
            p.Value = model.DISPLYTEXT;
            parameters.Add(p);

            p = new OracleParameter(":ICON", OracleDbType.Varchar2, 50);
            p.Value = model.ICON;
            parameters.Add(p);

            p = new OracleParameter(":OPENCLASS", OracleDbType.Varchar2, 50);
            p.Value = model.OPENCLASS;
            parameters.Add(p);

            p = new OracleParameter(":TOOLTIP", OracleDbType.Varchar2, 50);
            p.Value = model.TOOLTIP;
            parameters.Add(p);

            p = new OracleParameter(":LARGEICON", OracleDbType.Varchar2, 50);
            p.Value = model.LARGEICON;
            parameters.Add(p);

            p = new OracleParameter(":SMALLICON", OracleDbType.Varchar2, 50);
            p.Value = model.SMALLICON;
            parameters.Add(p);

            p = new OracleParameter(":STYLE", OracleDbType.Varchar2, 50);
            p.Value = model.STYLE;
            parameters.Add(p);
            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string NODE, string PARENTNODE, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from MAIN_MENU ");
            strSql.Append(" where NODE=:NODE and PARENTNODE=:PARENTNODE ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":NODE", OracleDbType.Varchar2, 6);
            p.Value = NODE;
            parameters.Add(p);

            p = new OracleParameter(":PARENTNODE", OracleDbType.Varchar2, 8);
            p.Value = PARENTNODE;
            parameters.Add(p);

            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }



        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.MAIN_MENU GetModel(string NODE, string PARENTNODE, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select NODE, SORTNO, PARENTNODE, DISPLYTEXT, ICON, OPENCLASS, TOOLTIP, LARGEICON, SMALLICON, STYLE  ");
            strSql.Append("  from MAIN_MENU ");
            strSql.Append(" where NODE=:NODE and PARENTNODE=:PARENTNODE ");
            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":NODE", OracleDbType.Varchar2, 6);
            p.Value = NODE;
            parameters.Add(p);

            p = new OracleParameter(":PARENTNODE", OracleDbType.Varchar2, 8);
            p.Value = PARENTNODE;
            parameters.Add(p);
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;
                    Model.MAIN_MENU model = new Model.MAIN_MENU();

                    if (cmdresult > 0)
                    {
                        model = CopyToModel(ds.Tables[0].Rows[0]);
                        return model;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM MAIN_MENU ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得几行数据
        /// </summary>
        public DataSet GetList(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            #region 初始化参数
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM MAIN_MENU T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.MAIN_MENU> GetObservableCollection(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM MAIN_MENU ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.MAIN_MENU> list = new System.Collections.ObjectModel.ObservableCollection<Model.MAIN_MENU>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.MAIN_MENU model = new Model.MAIN_MENU();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表 
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.MAIN_MENU> GetObservableCollection(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM MAIN_MENU T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }

            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.MAIN_MENU> list = new System.Collections.ObjectModel.ObservableCollection<Model.MAIN_MENU>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.MAIN_MENU model = new Model.MAIN_MENU();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion   Method
        #region
        /// <summary>
        /// 
        /// </summary>
        protected Model.MAIN_MENU CopyToModel(DataRow dRow)
        {
            Model.MAIN_MENU model1 = new Model.MAIN_MENU();

            if (dRow["NODE"] != null && dRow["NODE"].ToString() != "")
            {
                model1.NODE = dRow["NODE"].ToString();
            }

            if (dRow["SORTNO"] != null && dRow["SORTNO"].ToString() != "")
            {
                model1.SORTNO = decimal.Parse(dRow["SORTNO"].ToString());
            }

            if (dRow["PARENTNODE"] != null && dRow["PARENTNODE"].ToString() != "")
            {
                model1.PARENTNODE = dRow["PARENTNODE"].ToString();
            }

            if (dRow["DISPLYTEXT"] != null && dRow["DISPLYTEXT"].ToString() != "")
            {
                model1.DISPLYTEXT = dRow["DISPLYTEXT"].ToString();
            }

            if (dRow["ICON"] != null && dRow["ICON"].ToString() != "")
            {
                model1.ICON = dRow["ICON"].ToString();
            }

            if (dRow["OPENCLASS"] != null && dRow["OPENCLASS"].ToString() != "")
            {
                model1.OPENCLASS = dRow["OPENCLASS"].ToString();
            }

            if (dRow["TOOLTIP"] != null && dRow["TOOLTIP"].ToString() != "")
            {
                model1.TOOLTIP = dRow["TOOLTIP"].ToString();
            }

            if (dRow["LARGEICON"] != null && dRow["LARGEICON"].ToString() != "")
            {
                model1.LARGEICON = dRow["LARGEICON"].ToString();
            }

            if (dRow["SMALLICON"] != null && dRow["SMALLICON"].ToString() != "")
            {
                model1.SMALLICON = dRow["SMALLICON"].ToString();
            }

            if (dRow["STYLE"] != null && dRow["STYLE"].ToString() != "")
            {
                model1.STYLE = dRow["STYLE"].ToString();
            }

            return model1;
        }
        #endregion

    }
}

