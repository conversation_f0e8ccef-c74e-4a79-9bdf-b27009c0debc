﻿using System;
using System.Runtime.Serialization;

namespace Model
{
   /// <summary>
	/// 实体类MR_FILE_OPEN 。(属性说明自动提取数据库字段的描述信息)
	/// </summary>
    [DataContract]
    public class MR_FILE_OPEN
    {
        public MR_FILE_OPEN()
        { }
        #region Model
        /// <summary>
        /// 患者ID 
        /// </summary>
        [DataMember]
        public string PATIENT_ID
        {
            set;
            get;
        }
        /// <summary>
        /// 住院序号
        /// </summary>
        [DataMember]
        public int VISIT_ID
        {
            set;
            get;
        }
        /// <summary>
        /// 文件名称
        /// </summary>
        [DataMember]
        public string FILE_NAME
        {
            set;
            get;
        }
        /// <summary>
        /// 片段号
        /// </summary>
        [DataMember]
        public int FILE_NO
        {
            set;
            get;
        }
        /// <summary>
        /// 医生名称
        /// </summary>
        [DataMember]
        public string USER_NAME
        {
            set;
            get;
        }
        /// <summary>
        /// 医生ID
        /// </summary>
        [DataMember]
        public string USER_ID
        {
            set;
            get;
        }
        /// <summary>
        /// 计算机名称
        /// </summary>
        [DataMember]
        public string COMPUTER
        {
            set;
            get;
        }
        /// <summary>
        /// 锁定起始时间
        /// </summary>
        [DataMember]
        public DateTime? OPEN_TIME
        {
            set;
            get;
        }
        /// <summary>
        /// 科室名称
        /// </summary>
        [DataMember]
        public string DEPT_NAME
        {
            set;
            get;
        }
        #endregion Model
    }
}
