<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Model</name>
    </assembly>
    <members>
        <member name="T:Model.BILL_PATTERN_DETAIL">
             <summary>
            费用模板明细记录
             </summary>
        </member>
        <member name="F:Model.BILL_PATTERN_DETAIL._pattern_name">
            <summary>
            模板名称
            </summary>		
        </member>
        <member name="F:Model.BILL_PATTERN_DETAIL._item_no">
            <summary>
            项目序号
            </summary>		
        </member>
        <member name="F:Model.BILL_PATTERN_DETAIL._item_class">
            <summary>
            项目分类
            </summary>		
        </member>
        <member name="F:Model.BILL_PATTERN_DETAIL._item_code">
            <summary>
            项目代码
            </summary>		
        </member>
        <member name="F:Model.BILL_PATTERN_DETAIL._item_spec">
            <summary>
            项目规格
            </summary>		
        </member>
        <member name="F:Model.BILL_PATTERN_DETAIL._units">
            <summary>
            单位
            </summary>		
        </member>
        <member name="F:Model.BILL_PATTERN_DETAIL._performed_by">
            <summary>
            执行科室
            </summary>		
        </member>
        <member name="F:Model.BILL_PATTERN_DETAIL._amount">
            <summary>
            数量
            </summary>		
        </member>
        <member name="F:Model.BILL_PATTERN_DETAIL._dept_code">
            <summary>
            部门代码
            </summary>		
        </member>
        <member name="T:Model.BILL_PATTERN_MASTER">
             <summary>
            费用模板主记录
             </summary>
        </member>
        <member name="F:Model.BILL_PATTERN_MASTER._serial_no">
            <summary>
            序号
            </summary>		
        </member>
        <member name="F:Model.BILL_PATTERN_MASTER._pattern_name">
            <summary>
            模板名称
            </summary>		
        </member>
        <member name="F:Model.BILL_PATTERN_MASTER._input_code">
            <summary>
            输入码
            </summary>		
        </member>
        <member name="F:Model.BILL_PATTERN_MASTER._input_code_wb">
            <summary>
            五笔码
            </summary>		
        </member>
        <member name="F:Model.BILL_PATTERN_MASTER._dept_code">
            <summary>
            部门代码
            </summary>		
        </member>
        <member name="F:Model.MODEL_GROUP._model_code">
            <summary>
            MODEL_CODE
            </summary>		
        </member>
        <member name="F:Model.MODEL_GROUP._group_code">
            <summary>
            GROUP_CODE
            </summary>		
        </member>
        <member name="F:Model.MODEL_GROUP._group_name">
            <summary>
            GROUP_NAME
            </summary>		
        </member>
        <member name="F:Model.MODEL_GROUP._group_sort_no">
            <summary>
            GROUP_SORT_NO
            </summary>		
        </member>
        <member name="F:Model.MODEL_GROUP._icon">
            <summary>
            ICON
            </summary>		
        </member>
        <member name="F:Model.MAIN_MENU._node">
            <summary>
            NODE
            </summary>		
        </member>
        <member name="F:Model.MAIN_MENU._parentnode">
            <summary>
            PARENTNODE
            </summary>		
        </member>
        <member name="F:Model.MAIN_MENU._displytext">
            <summary>
            DISPLYTEXT
            </summary>		
        </member>
        <member name="F:Model.MAIN_MENU._icon">
            <summary>
            ICON
            </summary>		
        </member>
        <member name="F:Model.MAIN_MENU._openclass">
            <summary>
            OPENCLASS
            </summary>		
        </member>
        <member name="F:Model.MAIN_MENU._tooltip">
            <summary>
            TOOLTIP
            </summary>		
        </member>
        <member name="F:Model.MAIN_MENU._largeicon">
            <summary>
            LARGEICON
            </summary>		
        </member>
        <member name="F:Model.MAIN_MENU._smallicon">
            <summary>
            SMALLICON
            </summary>		
        </member>
        <member name="F:Model.MAIN_MENU._style">
            <summary>
            STYLE
            </summary>		
        </member>
        <member name="F:Model.MAIN_MENU._sortno">
            <summary>
            SORTNO
            </summary>		
        </member>
        <member name="F:Model.MODELS._code">
            <summary>
            CODE
            </summary>		
        </member>
        <member name="F:Model.MODELS._name">
            <summary>
            NAME
            </summary>		
        </member>
        <member name="F:Model.MODELS._sort_no">
            <summary>
            SORT_NO
            </summary>		
        </member>
        <member name="F:Model.MODELS._key">
            <summary>
            KEY
            </summary>		
        </member>
        <member name="F:Model.MODELS._last_used_key">
            <summary>
            LAST_USED_KEY
            </summary>		
        </member>
        <member name="T:Model.SEC_MENUS_DICT">
             <summary>
            应用程序菜单字典
             </summary>
        </member>
        <member name="F:Model.SEC_MENUS_DICT._application_code">
            <summary>
            应用程序代码(applications.application)
            </summary>		
        </member>
        <member name="F:Model.SEC_MENUS_DICT._menu_name">
            <summary>
            菜单标识
            </summary>		
        </member>
        <member name="F:Model.SEC_MENUS_DICT._form_menu">
            <summary>
            菜单所属窗体类名(区分在哪个窗体上的菜单,要加上命名空间。)
            </summary>		
        </member>
        <member name="F:Model.SEC_MENUS_DICT._form_control">
            <summary>
            菜单所属控件(如果是窗体上面的主菜单，该字段为空，如果是窗体上控件右手键菜单，该字段保存窗口上控件名称)
            </summary>		
        </member>
        <member name="F:Model.SEC_MENUS_DICT._serial_no">
            <summary>
            序号	
            </summary>		
        </member>
        <member name="F:Model.SEC_MENUS_DICT._menu_text">
            <summary>
            菜单中文描述
            </summary>		
        </member>
        <member name="F:Model.SEC_MENUS_DICT._tool_tips">
            <summary>
            菜单提示信息
            </summary>		
        </member>
        <member name="F:Model.SEC_MENUS_DICT._supper_menu">
            <summary>
            上级菜单标识
            </summary>		
        </member>
        <member name="F:Model.SEC_MENUS_DICT._open_form">
            <summary>
            打开的窗体
            </summary>		
        </member>
        <member name="F:Model.SEC_MENUS_DICT._open_param">
            <summary>
            打开窗体是否有参数
            </summary>		
        </member>
        <member name="F:Model.SEC_MENUS_DICT._menu_visible">
            <summary>
            是否可见(1-可见，0-不可见)
            </summary>		
        </member>
        <member name="F:Model.SEC_MENUS_DICT._icon_style">
            <summary>
            图标风格(1-显示大图标，2-显示小图标)
            </summary>		
        </member>
        <member name="F:Model.SEC_MENUS_DICT._large_icon">
            <summary>
            大图标(保存本地图标文件的路径)
            </summary>		
        </member>
        <member name="F:Model.SEC_MENUS_DICT._small_icon">
            <summary>
            小图标(保存本地图标文件的路径)
            </summary>		
        </member>
        <member name="F:Model.SEC_MENUS_DICT._menu_group">
            <summary>
            菜单分组标识
            </summary>		
        </member>
        <member name="F:Model.SEC_MENUS_DICT._menu_memos">
            <summary>
            备注
            </summary>		
        </member>
        <member name="F:Model.SEC_MENUS_DICT._open_file_name">
            <summary>
            打开窗体所在文件
            </summary>		
        </member>
        <member name="T:Model.SEC_RIGHT_GROUP">
             <summary>
            应用程序菜单字典
             </summary>
        </member>
        <member name="F:Model.SEC_RIGHT_GROUP._application_code">
            <summary>
            应用程序代码
            </summary>		
        </member>
        <member name="F:Model.SEC_RIGHT_GROUP._right_group_code">
            <summary>
            权限组代码
            </summary>		
        </member>
        <member name="F:Model.SEC_RIGHT_GROUP._right_group_text">
            <summary>
            权限组说明
            </summary>		
        </member>
        <member name="F:Model.SEC_RIGHT_GROUP._right_group_memos">
            <summary>
            备注	
            </summary>		
        </member>
        <member name="T:Model.SEC_RIGHT_GROUP_VS_MENUS">
             <summary>
            应用程序菜单字典
             </summary>
        </member>
        <member name="F:Model.SEC_RIGHT_GROUP_VS_MENUS._application_code">
            <summary>
            应用程序代码
            </summary>		
        </member>
        <member name="F:Model.SEC_RIGHT_GROUP_VS_MENUS._right_group_code">
            <summary>
            权限组代码
            </summary>		
        </member>
        <member name="F:Model.SEC_RIGHT_GROUP_VS_MENUS._menu_name">
            <summary>
            菜单标识
            </summary>		
        </member>
        <member name="F:Model.SEC_RIGHT_GROUP_VS_MENUS._security_memos">
            <summary>
            备注	
            </summary>		
        </member>
        <member name="T:Model.SEC_RIGHT_GROUP_VS_USERS">
             <summary>
            应用程序菜单字典
             </summary>
        </member>
        <member name="F:Model.SEC_RIGHT_GROUP_VS_USERS._application_code">
            <summary>
            应用程序代码
            </summary>		
        </member>
        <member name="F:Model.SEC_RIGHT_GROUP_VS_USERS._right_group_code">
            <summary>
            权限组代码
            </summary>		
        </member>
        <member name="F:Model.SEC_RIGHT_GROUP_VS_USERS._user_code">
            <summary>
            人员代码(staff_dict.user_name)
            </summary>		
        </member>
        <member name="F:Model.SEC_RIGHT_GROUP_VS_USERS._security_memos">
            <summary>
            备注	
            </summary>		
        </member>
        <member name="T:Model.STAFF_DICT">
             <summary>
            工作人员字典
             </summary>
        </member>
        <member name="P:Model.STAFF_DICT.EMP_NO">
            <summary>
            人员编号
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.DEPT_CODE">
            <summary>
            所在科室编码
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.DEPT_NAME">
            <summary>
            所在科室名称
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.NAME">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.INPUT_CODE">
            <summary>
            拼音码
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.JOB">
            <summary>
            工作类别
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.TITLE">
            <summary>
            职称
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.USER_NAME">
            <summary>
            系统用户名
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.INPUT_CODE_WB">
            <summary>
            五笔码
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.ID">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.CREATE_DATE">
            <summary>
            创建日期
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.PASSWORD">
            <summary>
            加密后口令
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.SYS_FLAG">
            <summary>
            是否oracle用户
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.STATUS">
            <summary>
            用户标志：1 正常 0 停用
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.ROLEID">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.IP">
            <summary>
            用户登录机子的IP
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.AUTOGRAPH">
            <summary>
            用户图片
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.COMMUNITY_CODE">
            <summary>
            社区编码
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.SEX">
            <summary>
            性别
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.ID_NO">
            <summary>
            身份证号
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.NATION">
            <summary>
            民族
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.MAILING_ADDRESS">
            <summary>
            籍贯
            </summary>
        </member>
        <member name="F:Model.STAFF_DICT._work_id">
            <summary>
            护士或医生执照号码
            </summary>		
        </member>
        <member name="F:Model.STAFF_DICT._work_state">
            <summary>
            在职状态
            </summary>		
        </member>
        <member name="F:Model.STAFF_DICT._formation">
            <summary>
            编制
            </summary>		
        </member>
        <member name="F:Model.STAFF_DICT._hisunitcode">
            <summary>
            医院编号  张鹏20170614
            </summary>		
        </member>
        <member name="F:Model.STAFF_DICT._ca_enabled">
            <summary>
            姓名
            </summary>		
        </member>
        <member name="M:Model.STAFF_DICT.CopyToModel(System.Data.DataRow)">
            <summary>
            复制信息到人员信息类(HIS)
            </summary>
        </member>
        <member name="M:Model.STAFF_DICT.CopyToModel_NURADM(System.Data.DataRow)">
            <summary>
            复制信息到人员信息类(护理管理系统)
            </summary>
        </member>
        <member name="M:Model.STAFF_DICT.CheckData(System.Object)">
            <summary>
            验证数据有效
            </summary>
            <param name="obj">数据对象</param>
            <returns></returns>
        </member>
        <member name="P:Model.STAFF_DICT.USER_GROUP">
            <summary>
            所属用户组
            </summary>
        </member>
        <member name="P:Model.STAFF_DICT.USER_SRC">
            <summary>
            用户来源,0普通用户（默认）,1管理员用户ADMIN
            </summary>          
        </member>
        <member name="P:Model.STAFF_DICT.ward_code">
            <summary>
            所在病区编码
            </summary>         
        </member>
        <member name="P:Model.EMR.EMR_3RD_FILE_INDEX.TOPIC">
            <summary>
            #患者ID
            </summary>
        </member>
        <member name="P:Model.EMR.EMR_3RD_FILE_INDEX.PATIENT_ID">
            <summary>
            #患者ID
            </summary>
        </member>
        <member name="P:Model.EMR.EMR_3RD_FILE_INDEX.VISIT_ID">
            <summary>
            #住院次数
            </summary>
        </member>
        <member name="P:Model.EMR.EMR_3RD_FILE_INDEX.FILE_NO">
            <summary>
            #文件号
            </summary>
        </member>
        <member name="P:Model.EMR.EMR_3RD_FILE_INDEX.FILE_SUB_NO">
            <summary>
            #子序号
            </summary>
        </member>
        <member name="P:Model.EMR.EMR_3RD_FILE_INDEX.SERIAL_NO">
            <summary>
            显示序号
            </summary>
        </member>
        <member name="P:Model.EMR.EMR_3RD_FILE_INDEX.REQUST_NO">
            <summary>
            文件申请号
            </summary>
        </member>
        <member name="P:Model.EMR.EMR_3RD_FILE_INDEX.APP_CODE">
            <summary>
            文件创建系统的代码
            </summary>
        </member>
        <member name="P:Model.EMR.EMR_3RD_FILE_INDEX.FILE_CONTENT_TYPE_NAME">
            <summary>
            文件内容类型名称
            </summary>
        </member>
        <member name="P:Model.EMR.EMR_3RD_FILE_INDEX.FILE_TYPE">
            <summary>
            文件类型
            </summary>
        </member>
        <member name="P:Model.EMR.EMR_3RD_FILE_INDEX.FILE_PATH">
            <summary>
            相对路径
            </summary>
        </member>
        <member name="P:Model.EMR.EMR_3RD_FILE_INDEX.FILE_NAME">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:Model.EMR.EMR_3RD_FILE_INDEX.CREATE_DATE_TIME">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Model.EMR.EMR_3RD_FILE_INDEX.FILE_CONTENT_TYPE_CODE">
            <summary>
            文件内容类型代码
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.PATIENT_ID">
            <summary>
            病人ID
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.VISIT_ID">
            <summary>
            住院标识
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.FILE_NO">
            <summary>
            文件序号
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.FILE_NAME">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.TOPIC">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.CREATOR_NAME">
            <summary>
            创建者姓名
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.CREATOR_ID">
            <summary>
            创建者ID
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.CREATE_DATE_TIME">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.LAST_MODIFY_DATE_TIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.FILE_MODIFY_DATE_TIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.FLAG">
            <summary>
            
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.FILE_ATTR">
            <summary>
            病历类别:A住院志/B病程记录/C会诊记录/T知情文件/R其它记录/H体征库/S症状库/Z知识库
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.PRINT_FLAG">
            <summary>
            打印标志，用于病程续打
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.MR_CODE">
            <summary>
            病历模板编码
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.DATA_MODEL_CODE">
            <summary>
            病历模板数据集编码
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.PARENT_NAME">
            <summary>
            上级医生签名姓名
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.PARENT_ID">
            <summary>
            上级医生签名帐号
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.FILE_FLAG">
            <summary>
            病历签名等级T－未签名 1－经治签名 2－上级签名 3－主任签名
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.MODIFY_DATE_TIME">
            <summary>
            上级医生签名的时间(系统时间，不显示在病历中)
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.EPR_INDEX">
            <summary>
            电子病历索引标志
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.SUPER_DATE_TIME">
            <summary>
            主任医生签名的时间(系统时间，不显示在病历中)
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.SUPER_ID">
            <summary>
            主任医生签名帐号
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.SUPER_NAME">
            <summary>
            主任医生签名姓名
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.SIGN_PAPER_DATETIME">
            <summary>
            经治医生签名的时间(显示在病历中的时间)
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.FILE_ORDER">
            <summary>
            病程病历排列序号
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.PARENT_SIGN_PAPER_DATETIME">
            <summary>
            上级医生签名的时间(显示在病历中的时间)
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.SUPER_SIGN_PAPER_DATETIME">
            <summary>
            主任医生签名的时间(显示在病历中的时间)
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.FILE_CREATOR_GRADE">
            <summary>
            病程片段创建等级,默认是0,经治医生为1,上级医生2,主任医生3
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.HAND_SIGN_PAPER_DATETIME">
            <summary>
            
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.EVALUATE_PASS_FALG">
            <summary>
            审核通过标志,1通过
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.PATIENT_SIGN_FLAG">
            <summary>
            患者及家属签名标志
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.PATIENT_SIGN_DATETIME">
            <summary>
            患者及家属签名时间
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.APP_NO">
            <summary>
            申请单序号,包括:会诊记录\检查检验记录编号
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.SIGNATURE_NO">
            <summary>
            签章序号
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.PATIENT_SIGNATURE_NO">
            <summary>
            病人签章序号
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.STUDY_DOCTOR_ID">
            <summary>
            实习医生id
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.STUDY_DOCTOR_NAME">
            <summary>
            实习医生姓名
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.TAKE_FLAG">
            <summary>
            领取标志0未申领，1已申领
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.FIRST_SIGN_DEPT_CODE">
            <summary>
            第一次签名时病人所在科室
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.DIAG_WRITE_INDICATOR">
            <summary>
            诊断书写方式(取值：同参数MR_ADMISSION_NOTE_TCM的取值内容列表相同)
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.SING_FLAG">
            <summary>
            住院志初次的诊断模式
            </summary>
        </member>
        <member name="P:Model.EMR.MR_FILE_INDEX.CREATE_DISPLAY_DATE_TIME">
            <summary>
            显示在病历上的创建时间(显示在病历中的时间)
            </summary>
        </member>
        <member name="P:Model.EMR.MR_TEMPLET_INDEX.Nursing_Type_ID">
            <summary>
            草稿模板标识
            </summary>
        </member>
        <member name="P:Model.EMR.MR_TEMPLET_INDEX.TEMPLET_ID">
            <summary>
            草稿模板标识
            </summary>
        </member>
        <member name="P:Model.EMR.MR_TEMPLET_INDEX.TEMPLET_FILE_NAME">
            <summary>
            草稿模板文件名
            </summary>
        </member>
        <member name="P:Model.EMR.MR_TEMPLET_INDEX.ACCESS_PATH">
            <summary>
            存取路径
            </summary>
        </member>
        <member name="P:Model.EMR.MR_TEMPLET_INDEX.TOPIC">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Model.EMR.MR_TEMPLET_INDEX.DEPT_CODE">
            <summary>
            所属科室
            </summary>
        </member>
        <member name="P:Model.EMR.MR_TEMPLET_INDEX.CREATOR_ID">
            <summary>
            创建者
            </summary>
        </member>
        <member name="P:Model.EMR.MR_TEMPLET_INDEX.CREATE_DATE_TIME">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Model.EMR.MR_TEMPLET_INDEX.LAST_MODIFY_DATE_TIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:Model.EMR.MR_TEMPLET_INDEX.PERMISSION">
            <summary>
            允许访问权 T=允许 1=不允许
            </summary>
        </member>
        <member name="P:Model.EMR.MR_TEMPLET_INDEX.DATA_MODEL_CLASS">
            <summary>
            数据模型类别
            </summary>
        </member>
        <member name="P:Model.EMR.MR_TEMPLET_INDEX.DATA_MODEL_CODE">
            <summary>
            数据模型代码
            </summary>
        </member>
        <member name="P:Model.EMR.MR_TEMPLET_INDEX.MR_TYPE_CODE">
            <summary>
            模板类型分类
            </summary>
        </member>
        <member name="P:Model.EMR.MR_TEMPLET_INDEX.OPERATE_LEVEL">
            <summary>
            操作级别
            </summary>
        </member>
        <member name="P:Model.EMR.MR_TEMPLET_INDEX.IS_NEW">
            <summary>
            模板来源
            </summary>
        </member>
        <member name="P:Model.EMR.MR_TEMPLET_INDEX.MR_VERSION">
            <summary>
            模板类型分类
            </summary>
        </member>
        <member name="M:Model.EMR.OUTP_MR_FILE_INDEX.#ctor">
            <summary>
            实体类OUTP_MR_FILE_INDEX 。(属性说明自动提取数据库字段的描述信息)
            </summary>
        </member>
        <member name="P:Model.EMR.OUTP_MR_FILE_INDEX.VISIT_DATE">
            <summary>
            就诊日期
            </summary>
        </member>
        <member name="P:Model.EMR.OUTP_MR_FILE_INDEX.VISIT_NO">
            <summary>
            就诊序号
            </summary>
        </member>
        <member name="P:Model.EMR.OUTP_MR_FILE_INDEX.FILE_NO">
            <summary>
            文件序号
            </summary>
        </member>
        <member name="P:Model.EMR.OUTP_MR_FILE_INDEX.PATIENT_ID">
            <summary>
            病人ID
            </summary>
        </member>
        <member name="P:Model.EMR.OUTP_MR_FILE_INDEX.FILE_NAME">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:Model.EMR.OUTP_MR_FILE_INDEX.TOPIC">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Model.EMR.OUTP_MR_FILE_INDEX.CREATOR_NAME">
            <summary>
            创建者姓名
            </summary>
        </member>
        <member name="P:Model.EMR.OUTP_MR_FILE_INDEX.CREATOR_ID">
            <summary>
            创建者ID
            </summary>
        </member>
        <member name="P:Model.EMR.OUTP_MR_FILE_INDEX.CREATE_DATE_TIME">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Model.EMR.OUTP_MR_FILE_INDEX.LAST_MODIFY_DATE_TIME">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:Model.EMR.OUTP_MR_FILE_INDEX.MR_CODE">
            <summary>
            病历模板编码
            </summary>
        </member>
        <member name="P:Model.EMR.OUTP_MR_FILE_INDEX.SIGNATURE_NO">
            <summary>
            实习医生id
            </summary>
        </member>
        <member name="P:Model.EMR.OUTP_MR_FILE_INDEX.STUDY_DOCTOR_ID">
            <summary>
            实习医生id
            </summary>
        </member>
        <member name="P:Model.EMR.OUTP_MR_FILE_INDEX.STUDY_DOCTOR_NAME">
            <summary>
            实习医生姓名
            </summary>
        </member>
        <member name="T:Model.LNCA_INTERFACE">
             <summary>
            辽宁CA接口
             </summary>
        </member>
        <member name="F:Model.LNCA_INTERFACE._lnca_id">
            <summary>
            辽宁ca的key设备id
            </summary>		
        </member>
        <member name="F:Model.LNCA_INTERFACE._lnca_info">
            <summary>
            辽宁ca的key信息
            </summary>		
        </member>
        <member name="F:Model.LNCA_INTERFACE._his_id">
            <summary>
            his的用户名
            </summary>		
        </member>
        <member name="F:Model.LNCA_INTERFACE._ca_img">
            <summary>
            key存图片储
            </summary>		
        </member>
        <member name="F:Model.LNCA_INTERFACE._ca_sn">
            <summary>
            key序列号
            </summary>		
        </member>
        <member name="F:Model.LNCA_INTERFACE._ca_img_str">
            <summary>
            key图片字符串格式
            </summary>		
        </member>
        <member name="T:Model.BED_REC">
             <summary>
            床位记录
             </summary>
        </member>
        <member name="F:Model.BED_REC._ward_code">
            <summary>
            病房（护理单元）代码
            </summary>		
        </member>
        <member name="F:Model.BED_REC._bed_no">
            <summary>
            床号
            </summary>		
        </member>
        <member name="F:Model.BED_REC._bed_label">
            <summary>
            床标号
            </summary>		
        </member>
        <member name="F:Model.BED_REC._room_no">
            <summary>
            房间
            </summary>		
        </member>
        <member name="F:Model.BED_REC._dept_code">
            <summary>
            所属科室代码
            </summary>		
        </member>
        <member name="F:Model.BED_REC._bed_approved_type">
            <summary>
            床位编制类型
            </summary>		
        </member>
        <member name="F:Model.BED_REC._bed_sex_type">
            <summary>
            床位类型(1-男， 2-女， 9-不限)
            </summary>		
        </member>
        <member name="F:Model.BED_REC._bed_class">
            <summary>
            床位等级
            </summary>		
        </member>
        <member name="F:Model.BED_REC._bed_status">
            <summary>
            床位状态
            </summary>		
        </member>
        <member name="F:Model.BED_REC._lend_attr">
            <summary>
            不用
            </summary>		
        </member>
        <member name="F:Model.BED_REC._lend_bed_no">
            <summary>
            不用
            </summary>		
        </member>
        <member name="F:Model.BED_REC._lend_bed_dept">
            <summary>
            不用
            </summary>		
        </member>
        <member name="F:Model.BED_REC._lend_bed_ward">
            <summary>
            不用
            </summary>		
        </member>
        <member name="F:Model.BED_REC._lock_status">
            <summary>
            是否锁住床位
            </summary>		
        </member>
        <member name="F:Model.BED_REC._lock_operator">
            <summary>
            锁床位操作员(staff_dict.user_name)
            </summary>		
        </member>
        <member name="F:Model.BED_REC._aircondition_class">
            <summary>
            空调类型
            </summary>		
        </member>
        <member name="F:Model.BED_REC._patient_id">
            <summary>
            患者标识
            </summary>		
        </member>
        <member name="T:Model.BJCA_INTERFACE">
             <summary>
            数字签名
             </summary>
        </member>
        <member name="F:Model.BJCA_INTERFACE._bjca_id">
            <summary>
            BJCA_ID
            </summary>		
        </member>
        <member name="F:Model.BJCA_INTERFACE._bjca_info">
            <summary>
            BJCA_INFO
            </summary>		
        </member>
        <member name="F:Model.BJCA_INTERFACE._his_id">
            <summary>
            HIS_ID
            </summary>		
        </member>
        <member name="F:Model.BJCA_INTERFACE._bjca_img">
            <summary>
            BJCA_IMG
            </summary>		
        </member>
        <member name="F:Model.BJCA_INTERFACE._bjca_img_str">
            <summary>
            BJCA_IMG_STR
            </summary>		
        </member>
        <member name="F:Model.BJCA_INTERFACE._ca_id">
            <summary>
            CA_ID
            </summary>		
        </member>
    </members>
</doc>
