﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraCharts;

namespace Tjhis.Controls.StatisticsControls
{
    public partial class ucOutpVisitStatistics : RadiusUserControl
    {
        /// <summary>
        /// 数据
        /// </summary>
        public Data data { get; set; }
        public ucOutpVisitStatistics()
        {
            InitializeComponent();
        }

        public override void Clear()
        {
            if (null != data)
            {
                if (null != data.ChartData)
                {
                    this.Chart.Series[0].DataSource = null;
                    this.Chart.Series[1].DataSource = null;
                    data.ChartData.Dispose();
                }
            }
            base.Clear();
        }
        /// <summary>
        /// 赋值后调用
        /// </summary>
        public override void InitData()
        {
            this.Chart.Series[0].Points.Clear();
            this.Chart.Series[1].Points.Clear();
            if(null != data)
            {
                if (null != data.ChartData)
                {
                    this.Chart.DataSource = data.ChartData;
                    //this.Chart.Series[0].DataSource = data.ChartData;
                    //this.Chart.Series[1].DataSource = data.ChartData;
                }
                if(null != data.StatisticsData)
                {
                    LSum.Text = GetLabelHtmlText(data.StatisticsData.Sum, PatUnit);
                    LRegister.Text = GetLabelHtmlText(data.StatisticsData.Register, PatUnit);
                    LOrder.Text = GetLabelHtmlText(data.StatisticsData.Order, PatUnit);
                    ucCompareSum.CompareValue = data.StatisticsData.CompareSum;
                    ucCompareRegister.CompareValue = data.StatisticsData.CompareRegister;
                    ucCompareOrder.CompareValue = data.StatisticsData.CompareOrder;
                }
            }
        }

        protected override void SetContentSize()
        {
            TablePanelMain.Width = this.Width;
            TablePanelMain.Height = this.Height - TopBannerHeight;
        }

        public class Data
        {
            public Data()
            {
                StatisticsData = new Statistics();
            }
            public void CreateTestData()
            {
                DataTable data = new DataTable();
                data = CreateBaseDataTable();
                DateTime baseTime = DateTime.Now;
                data.Rows.Add(baseTime, 500, 200);
                data.Rows.Add(baseTime.AddDays(-1), 300, 100);
                data.Rows.Add(baseTime.AddDays(-2), 400, 700);
                data.Rows.Add(baseTime.AddDays(-3), 500, 200);
                data.Rows.Add(baseTime.AddDays(-4), 600, 300);
                data.Rows.Add(baseTime.AddDays(-5), 300, 500);
                data.Rows.Add(baseTime.AddDays(-6), 100, 100);
                this.ChartData = data;
                StatisticsData.Sum = 500;
                StatisticsData.Register = 200;
                StatisticsData.Order = 100;
                StatisticsData.CompareRegister = 12.5m;
                StatisticsData.CompareOrder = -0.1m;
                StatisticsData.CompareSum = 11.4m;

            }

            public DataTable CreateBaseDataTable()
            {
                DataTable data = new DataTable();
                data.Columns.AddRange(new DataColumn[] {
                    new DataColumn("Time", typeof(DateTime)),
                    new DataColumn("Register",typeof(decimal)),
                    new DataColumn("Sum",typeof(decimal))
                });
                return data;
            }

            /// <summary>
            /// 图表数据，列1日期,列2日期对应的门诊总人数，列3日期对应的挂号人数，只要7天的数据，按日期正序排列
            /// </summary>
            public DataTable ChartData { get; set; }
            public Statistics StatisticsData { get; set; }
            public class Statistics
            {
                /// <summary>
                /// 门诊总数
                /// </summary>
                public int Sum { get; set; }
                /// <summary>
                /// 挂号人数
                /// </summary>
                public int Register { get; set; }
                /// <summary>
                /// 预约人数
                /// </summary>
                public int Order { get; set; }
                /// <summary>
                /// 同比门诊总数单位（%）
                /// </summary>
                public decimal CompareSum { get; set; }

                /// <summary>
                /// 同比挂号人数单位（%）
                /// </summary>
                public decimal CompareRegister { get; set; }

                /// <summary>
                /// 同比预约人数单位（%）
                /// </summary>
                public decimal CompareOrder { get; set; }
            }
        }
    }
}
