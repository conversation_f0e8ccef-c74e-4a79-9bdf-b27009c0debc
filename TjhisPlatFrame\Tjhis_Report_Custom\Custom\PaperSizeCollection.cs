﻿using System;
using System.Collections.Generic;
using System.Drawing.Printing;
using System.Linq;
using System.Text;

namespace Tjhis.Report.Custom.Custom
{
    public class PaperSizeCollection : System.Collections.CollectionBase
    {
        private static PaperSizeCollection myStdInstance = null;
        public static PaperSizeCollection StdInstance
        {
            get
            {
                //单位：百分之一英寸
                if (myStdInstance == null)
                {
                    myStdInstance = new PaperSizeCollection();
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A2, 420, 594); 	//A2 纸（420 毫米 × 594 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A3, 297, 420); 	//A3 纸（297 毫米 × 420 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A3Extra, 322, 445); 	//A3 extra 纸（322 毫米 × 445 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A3ExtraTransverse, 322, 445); 	//A3 extra transverse 纸（322 毫米 × 445 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A3Rotated, 420, 297); 	//A3 rotated 纸（420 毫米 × 297 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A3Transverse, 297, 420); 	//A3 transverse 纸（297 毫米 × 420 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A4, /*827*/ 210, 297); 	//A4 纸（210 毫米 × 297 毫米）。//Modified 解决使用A4纸打印时出现偏移的情况
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A4Extra, 236, 322); 	//A4 extra 纸（236 毫米 × 322 毫米）。该值是针对 PostScript 驱动程序的，仅供 Linotronic 打印机使用以节省纸张。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A4Plus, 210, 330); 	//A4 plus 纸（210 毫米 × 330 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A4Rotated, 297, 210); 	//A4 rotated 纸（297 毫米 × 210 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A4Small, 210, 297); 	//A4 small 纸（210 毫米 × 297 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A4Transverse, 210, 297); 	//A4 transverse 纸（210 毫米 × 297 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A5, 148, 210); 	//A5 纸（148 毫米 × 210 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A5Extra, 174, 235); 	//A5 extra 纸（174 毫米 × 235 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A5Rotated, 210, 148); 	//A5 rotated 纸（210 毫米 × 148 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A5Transverse, 148, 210); 	//A5 transverse 纸（148 毫米 × 210 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A6, 105, 148); 	//A6 纸（105 毫米 × 148 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.A6Rotated, 148, 105); 	//A6 rotated 纸（148 毫米 × 105 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.APlus, 227, 356); 	//SuperA/SuperA/A4 纸（227 毫米 × 356 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.B4, 250, 353); 	//B4 纸（250 × 353 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.B4Envelope, 250, 353); 	//B4 信封（250 × 353 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.B5, 176, 250); 	//B5 纸（176 毫米 × 250 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.B5Envelope, 176, 250); 	//B5 信封（176 毫米 × 250 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.B5Extra, 201, 276); 	//ISO B5 extra 纸（201 毫米 × 276 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.B5JisRotated, 257, 182); 	//JIS B5 rotated 纸（257 毫米 × 182 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.B5Transverse, 182, 257); 	//JIS B5 transverse 纸（182 毫米 × 257 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.B6Envelope, 176, 125); 	//B6 信封（176 毫米 × 125 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.B6Jis, 128, 182); 	//JIS B6 纸（128 毫米 × 182 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.BPlus, 305, 487); 	//SuperB/SuperB/A3 纸（305 毫米 × 487 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.C3Envelope, 305, 487); 	//SuperB/SuperB/A3 纸（305 毫米 × 487 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.C4Envelope, 229, 324); 	//C4 信封（229 毫米 × 324 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.C5Envelope, 162, 229); 	//C5 信封（162 毫米 × 229 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.C65Envelope, 114, 229); 	//C65 信封（114 毫米 × 229 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.C6Envelope, 114, 162); 	//C6 信封（114 毫米 × 162 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.CSheet, 114, 162); 	//C6 信封（114 毫米 × 162 毫米）。 

                    //Modified由于使用16k的纸张，在系统PagerSize枚举中没有16K的，所以默认选中自定义的选线
                    //myStdInstance.Add(System.Drawing.Printing.PaperKind.Custom, 776, 1068); // 自定义大小
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Custom, 195, 270); 	//自定义大小（195 毫米 × 270 毫米）。默认16K

                    myStdInstance.Add(System.Drawing.Printing.PaperKind.DLEnvelope, 110, 220); 	//DL 信封（110 毫米 × 220 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.DSheet, 559, 864); 	//D 纸（559 毫米 × 864 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.ESheet, 864, 1118); 	//E 纸（864 毫米 × 1118 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Executive, 184, 267); 	//Executive 纸（184 毫米 × 267 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Folio, 216, 330); 	//Folio 纸（216 毫米 × 330 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.GermanLegalFanfold, 216, 330); 	//German legal fanfold（216 毫米 × 330 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.GermanStandardFanfold, 216, 305); 	//German standard fanfold（216 毫米 × 305 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.InviteEnvelope, 220, 220); 	//Invite envelope（220 毫米 × 220 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.IsoB4, 250, 353); 	//ISO B4（250 毫米 × 353 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.ItalyEnvelope, 110, 230); 	//Italy envelope（110 毫米 × 230 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.JapaneseDoublePostcard, 200, 148); 	//Japanese double postcard（200 毫米 × 148 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.JapaneseDoublePostcardRotated, 148, 200); 	//Japanese rotated double postcard（148 毫米 × 200 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.JapanesePostcard, 100, 148); 	//Japanese postcard（100 毫米 × 148 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.JapanesePostcardRotated, 148, 100); 	//Japanese rotated postcard（148 毫米 × 100 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Ledger, 432, 279); 	//Ledger 纸（432 × 279 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Legal, 216, 356); 	//Legal 纸（216 × 356 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.LegalExtra, 236, 381); 	//Legal extra 纸（236 毫米 × 381 毫米）。该值特定于 PostScript 驱动程序，仅供 Linotronic 打印机使用以节省纸张。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Letter, 216, 279); 	//Letter 纸（216 毫米 × 279 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.LetterExtra, 236, 304); 	//Letter extra 纸（236 毫米 × 304 毫米）。该值特定于 PostScript 驱动程序，仅供 Linotronic 打印机使用以节省纸张。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.LetterExtraTransverse, 236, 305); 	//Letter extra transverse 纸（236 毫米 × 305 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.LetterPlus, 216, 322); 	//Letter plus 纸（216 毫米 毫米 × 322 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.LetterRotated, 279, 216); 	//Letter rotated 纸（279 毫米 × 216 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.LetterSmall, 216, 279); 	//Letter small 纸（216 × 279 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.LetterTransverse, 279, 279); 	//Letter transverse 纸（210 毫米 × 279 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.MonarchEnvelope, 98, 191); 	//Monarch envelope（98 毫米 × 191 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Note, 216, 279); 	//Note 纸（216 × 279 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Number10Envelope, 105, 241); 	//#10 envelope（105 × 241 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PersonalEnvelope, 92, 165); 	//6 3/4 envelope（92 毫米 × 165 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Prc16K, 146, 215); 	//PRC 16K 纸（146 × 215 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Prc16KRotated, 146, 215); 	//PRC 16K rotated 纸（146 × 215 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Prc32K, 97, 151); 	//PRC 32K 纸（97 × 151 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Prc32KBig, 97, 151); 	//PRC 32K(Big) 纸（97 × 151 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Prc32KBigRotated, 97, 151); 	//PRC 32K rotated 纸（97 × 151 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Prc32KRotated, 97, 151); 	//PRC 32K rotated 纸（97 × 151 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber1, 102, 165); 	//PRC #1 envelope（102 × 165 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber10, 324, 458); 	//PRC #10 envelope（324 × 458 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber10Rotated, 458, 324); 	//PRC #10 rotated envelope（458 × 324 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber1Rotated, 165, 102); 	//PRC #1 rotated envelope（165 × 102 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber2, 102, 176); 	//PRC #2 envelope（102 × 176 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber2Rotated, 176, 102); 	//PRC #2 rotated envelope（176 × 102 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber3, 125, 176); 	//PRC #3 envelope（125 × 176 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber3Rotated, 176, 125); 	//PRC #3 rotated envelope（176 × 125 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber4, 110, 208); 	//PRC #4 envelope（110 × 208 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber4Rotated, 208, 110); 	//PRC #4 rotated envelope（208 × 110 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber5, 110, 220); 	//PRC #5 envelope（110 × 220 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber5Rotated, 220, 110); 	//PRC #5 rotated envelope（220 × 110 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber6, 120, 230); 	//PRC #6 envelope（120 × 230 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber6Rotated, 230, 120); 	//PRC #6 rotated envelope（230 × 120 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber7, 160, 230); 	//PRC #7 envelope（160 × 230 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber7Rotated, 230, 160); 	//PRC #7 rotated envelope（230 × 160 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber8, 120, 309); 	//PRC #8 envelope（120 × 309 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber8Rotated, 309, 120); 	//PRC #8 rotated envelope（309 × 120 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber9, 229, 324); 	//PRC #9 envelope（229 × 324 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.PrcEnvelopeNumber9Rotated, 229, 324); 	//PRC #9 rotated envelope（229 × 324 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Quarto, 215, 275); 	//Quarto 纸（215 毫米 × 275 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Standard10x11, 254, 279); 	//Standard 纸（254 毫米 × 279 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Standard10x14, 254, 356); 	//Standard 纸（254 毫米 × 356 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Standard11x17, 279, 432); 	//Standard 纸（279 毫米 × 432 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Standard12x11, 305, 279); 	//Standard 纸（305 × 279 毫米）。需要 Windows 98、Windows NT 4.0 或更高版本。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Standard15x11, 381, 279); 	//Standard 纸（381 毫米 × 279 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Standard9x11, 229, 279); 	//Standard 纸（229 × 279 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Statement, 140, 216); 	//Statement 纸（140 毫米 × 216 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.Tabloid, 279, 432); 	//Tabloid 纸（279 毫米 × 432 毫米）。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.TabloidExtra, 297, 457); 	//Tabloid extra 纸（297 毫米 × 457 毫米）。该值特定于 PostScript 驱动程序，仅供 Linotronic 打印机使用以节省纸张。
                    myStdInstance.Add(System.Drawing.Printing.PaperKind.USStandardFanfold, 378, 279); 	//US standard fanfold（378 毫米 × 279 毫米）。
                }
                return myStdInstance;
            }
        }
        public MyPaperSize this[int index]
        {
            get { return (MyPaperSize)this.List[index]; }
        }
        public MyPaperSize this[System.Drawing.Printing.PaperKind vKind]
        {
            get
            {
                foreach (MyPaperSize size in this.List)
                {
                    if (size.Kind == vKind)
                    {
                        return size;
                    }
                }
                return null;
            }
        }
        public void Add(MyPaperSize size)
        {
            this.List.Add(size);
        }
        public MyPaperSize Add(System.Drawing.Printing.PaperKind vKind, int vWidth, int vHeight)
        {
            MyPaperSize size = new MyPaperSize(vKind, vWidth, vHeight);
            this.List.Add(size);
            return size;
        }
        public void Add(System.Drawing.Printing.PrinterSettings settings)
        {
            foreach (System.Drawing.Printing.PaperSize size in settings.PaperSizes)
            {
                MyPaperSize size2 = new MyPaperSize(size.Kind, size.Width, size.Height);
                this.List.Add(size2);
            }
        }
        public MyPaperSize[] ToArray()
        {
            return (MyPaperSize[])this.InnerList.ToArray(typeof(MyPaperSize));
        }

    }//public class XPaperSizeCollection : System.Collections.CollectionBase 

    public class MyPaperSize
    {
        public MyPaperSize()
        {
        }
        public MyPaperSize(System.Drawing.Printing.PaperSize size)
        {
            intKind = size.Kind;
            intWidth = size.Width;
            intHeight = size.Height;
        }
        public MyPaperSize(System.Drawing.Printing.PaperKind vKind, int vWidth, int vHeight)
        {
            intKind = vKind;
            intWidth = vWidth;
            intHeight = vHeight;
        }
        private System.Drawing.Printing.PaperKind intKind = System.Drawing.Printing.PaperKind.A4;
        public System.Drawing.Printing.PaperKind Kind
        {
            get { return intKind; }
            set
            {
                intKind = value;
                if (value != System.Drawing.Printing.PaperKind.Custom)
                {
                    MyPaperSize size = PaperSizeCollection.StdInstance[value];
                    if (size != null)
                    {
                        Width = size.Width;
                        Height = size.Height;
                    }
                }
            }
        }
        private int intWidth = 210;
        /// <summary>
        /// 纸张宽度 单位百分之一英寸
        /// </summary>
        public int Width
        {
            get { return intWidth; }
            set
            {
                intWidth = value;
            }
        }
        private int intHeight = 297;
        /// <summary>
        /// 纸张高度 单位百分之一英寸
        /// </summary>
        public int Height
        {
            get { return intHeight; }
            set
            {
                intHeight = value;
            }
        }
        public override string ToString()
        {
            return this.intKind.ToString();
        }
    }
    }
