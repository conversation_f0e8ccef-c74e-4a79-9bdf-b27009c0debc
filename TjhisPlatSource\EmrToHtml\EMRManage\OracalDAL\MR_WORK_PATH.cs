﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Microsoft.Practices.EnterpriseLibrary.Data.Sql;
using System.Data.Common;
using System.Data;
using System.ServiceModel;
using System.ServiceModel.Channels;
using Utility.DbInfo;
using Utility.YangFan;
namespace OracleDAL
{
    /// <summary>
    /// 数据访问类:MR_WORK_PATH
    /// </summary>
    public partial class MR_WORK_PATH 
    {
        public MR_WORK_PATH()
        { }
        #region  Method

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string MR_PATH)
        {
            Database db = DatabaseFactory.CreateDatabase();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from MEDREC.MR_WORK_PATH where MR_PATH=:MR_PATH ");
            try
            {
                DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
                db.AddInParameter(dbCommand, "MR_PATH", DbType.String, MR_PATH);
                int cmdresult;
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_WORK_PATH", "Exists", strSql.ToString(), endpoint.Address);
                }
                object obj = db.ExecuteScalar(dbCommand);
                if ((Object.Equals(obj, null)) || (Object.Equals(obj, System.DBNull.Value)))
                {
                    cmdresult = 0;
                }
                else
                {
                    cmdresult = int.Parse(obj.ToString());
                }
                if (cmdresult == 0)
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public void Add(Model.MR_WORK_PATH model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into MEDREC.MR_WORK_PATH(");
            strSql.Append("MR_PATH,TEMPLET_PATH,FILE_USER,FILE_PWD,IP_ADDR)");
            strSql.Append(" values (");
            strSql.Append(":MR_PATH,:TEMPLET_PATH,:FILE_USER,:FILE_PWD,:IP_ADDR)");
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
                db.AddInParameter(dbCommand, "MR_PATH", DbType.String, model.MR_PATH);
                db.AddInParameter(dbCommand, "TEMPLET_PATH", DbType.String, model.TEMPLET_PATH);
                db.AddInParameter(dbCommand, "FILE_USER", DbType.String, model.FILE_USER);
                db.AddInParameter(dbCommand, "FILE_PWD", DbType.String, model.FILE_PWD);
                db.AddInParameter(dbCommand, "IP_ADDR", DbType.String, model.IP_ADDR);
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_WORK_PATH", "Add", strSql.ToString(), endpoint.Address);
                }
                db.ExecuteNonQuery(dbCommand);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public void Update(Model.MR_WORK_PATH model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update MEDREC.MR_WORK_PATH set ");
            strSql.Append("TEMPLET_PATH=:TEMPLET_PATH,");
            strSql.Append("FILE_USER=:FILE_USER,");
            strSql.Append("FILE_PWD=:FILE_PWD,");
            strSql.Append("IP_ADDR=:IP_ADDR");
            strSql.Append(" where MR_PATH=:MR_PATH ");
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
                db.AddInParameter(dbCommand, "MR_PATH", DbType.String, model.MR_PATH);
                db.AddInParameter(dbCommand, "TEMPLET_PATH", DbType.String, model.TEMPLET_PATH);
                db.AddInParameter(dbCommand, "FILE_USER", DbType.String, model.FILE_USER);
                db.AddInParameter(dbCommand, "FILE_PWD", DbType.String, model.FILE_PWD);
                db.AddInParameter(dbCommand, "IP_ADDR", DbType.String, model.IP_ADDR);
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_WORK_PATH", "Update", strSql.ToString(), endpoint.Address);
                }
                db.ExecuteNonQuery(dbCommand);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public void Delete(string MR_PATH)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from MEDREC.MR_WORK_PATH ");
            strSql.Append(" where MR_PATH=:MR_PATH ");
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                DbCommand dbCommand = db.GetSqlStringCommand(strSql.ToString());
                db.AddInParameter(dbCommand, "MR_PATH", DbType.String, MR_PATH);
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_WORK_PATH", "Delete", strSql.ToString(), endpoint.Address);
                }
                db.ExecuteNonQuery(dbCommand);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.MR_WORK_PATH GetModel(string MR_PATH)
        {
            string strSql = "select MR_PATH,TEMPLET_PATH,FILE_USER,FILE_PWD,IP_ADDR from MEDREC.MR_WORK_PATH where MR_PATH='" + MR_PATH + "'";
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_WORK_PATH", "GetModel", strSql.ToString(), endpoint.Address);
                }
                Model.MR_WORK_PATH model = null;
                DataTable dataReader = DbHelperOra.GetDataReader(strSql);// db.ExecuteReader(dbCommand))
                if (dataReader.Rows.Count > 0)
                {
                    model = ReaderBind(dataReader.Rows[0]);
                }
                return model;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select MR_PATH,TEMPLET_PATH,FILE_USER,FILE_PWD,IP_ADDR,SERVER_TYPE ");
            strSql.Append(" FROM MEDREC.MR_WORK_PATH ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_WORK_PATH", "GetList", strSql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, strSql.ToString());
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            Database db = DatabaseFactory.CreateDatabase();
            DbCommand dbCommand = db.GetStoredProcCommand("UP_GetRecordByPage");
            db.AddInParameter(dbCommand, "tblName", DbType.AnsiString, "MR_WORK_PATH");
            db.AddInParameter(dbCommand, "fldName", DbType.AnsiString, "MR_PATH");
            db.AddInParameter(dbCommand, "PageSize", DbType.Int32, PageSize);
            db.AddInParameter(dbCommand, "PageIndex", DbType.Int32, PageIndex);
            db.AddInParameter(dbCommand, "IsReCount", DbType.Boolean, 0);
            db.AddInParameter(dbCommand, "OrderType", DbType.Boolean, 0);
            db.AddInParameter(dbCommand, "strWhere", DbType.AnsiString, strWhere);
            return db.ExecuteDataSet(dbCommand);
        }*/

        /// <summary>
        /// 获得数据列表（比DataSet效率高，推荐使用）
        /// </summary>
        public List<Model.MR_WORK_PATH> GetListArray(string strWhere)
        {
            string strSql = "select MR_PATH,TEMPLET_PATH,FILE_USER,FILE_PWD,IP_ADDR FROM MEDREC.MR_WORK_PATH ";
            if (strWhere.Trim() != "")
            {
                strSql+=" where " + strWhere;
            }
            try
            {
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_WORK_PATH", "GetListArray", strSql.ToString(), endpoint.Address);
                }
                List<Model.MR_WORK_PATH> list = new List<Model.MR_WORK_PATH>();
                //Database db = DatabaseFactory.CreateDatabase();
                DataTable dataReader = DbHelperOra.GetDataReader(strSql);//db.ExecuteReader(CommandType.Text, strSql.ToString()))
                foreach (DataRow dr in dataReader.Rows)
                {
                    list.Add(ReaderBind(dr));
                }
                return list;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得数据备份列表
        /// </summary>
        public Model.MR_WORK_PATH ReaderBind(DataRow dataReader)
        {
            Model.MR_WORK_PATH model = new Model.MR_WORK_PATH();
            model.MR_PATH = dataReader["MR_PATH"].ToString();
            model.TEMPLET_PATH = dataReader["TEMPLET_PATH"].ToString();
            model.FILE_USER = dataReader["FILE_USER"].ToString();
            model.FILE_PWD = dataReader["FILE_PWD"].ToString();
            model.IP_ADDR = dataReader["IP_ADDR"].ToString();
            return model;
        }

        /// <summary>
        /// 获取数据备份列表
        /// </summary>
        public DataSet GetBackUpList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select BACKUP_PATH,FILE_USER,FILE_PWD,IP_ADDR,SERVER_TYPE ");
            strSql.Append(" FROM MEDREC.MR_BACKUP_PATH ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                Database db = DatabaseFactory.CreateDatabase();
                if (TrackCOMM.Track_Indicator)
                {
                    OperationContext context = OperationContext.Current;
                    MessageProperties properties = context.IncomingMessageProperties;
                    RemoteEndpointMessageProperty endpoint = properties[RemoteEndpointMessageProperty.Name] as RemoteEndpointMessageProperty;
                    TrackEntity entity = new TrackEntity();
                    entity.Add("MR_WORK_PATH", "GetBackUpList", strSql.ToString(), endpoint.Address);
                }
                return db.ExecuteDataSet(CommandType.Text, strSql.ToString());
            }
            catch (Exception ex)
            {
                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion  Method
    }
}
