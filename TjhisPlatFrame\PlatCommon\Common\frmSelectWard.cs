﻿using DevExpress.XtraEditors;
//**********************************************
//说明:选择护理单元
//计算机名称：LINDP
//创建日期：2016/5/16 11:37:05
//作者：林大鹏
//版本号：V1.00
//**********************************************

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace PlatCommon.Common
{
    public partial class frmSelectWard : DevExpress.XtraEditors.XtraForm
    {
        DataSet WardList = new DataSet();
        public frmSelectWard()
        {
            InitializeComponent();

            
        }
        //取护理单元列表
        public void GetWardList(string user_code)
        {
            string str = "select a.*,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and a.group_class = '病区护士' and emp_no = '" + user_code + "' and b.HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
            WardList = new NM_Service.NMService.ServerPublicClient().GetList(str);
            gridControl1.DataSource = WardList.Tables[0];
        }

        private void simpleButton2_Click(object sender, EventArgs e)
        {
            if (DialogResult.No == XtraMessageBox.Show("确实要退出系统么?", "提示信息", MessageBoxButtons.YesNo, MessageBoxIcon.Question))
                return;
            Application.Exit();
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            DataRowView drv = gridView1.GetFocusedRow() as DataRowView;
            if (drv == null)
            {
                XtraMessageBox.Show("请选择护理单元！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            if (string.IsNullOrEmpty(drv.Row["GROUP_CODE"].ToString()))
            {
                XtraMessageBox.Show("请选择护理单元！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            //PlatCommon.SysBase.SystemParm.Ward_ID = drv.Row["GROUP_CODE"].ToString();
            //PlatCommon.SysBase.SystemParm.WardName = drv.Row["DEPT_NAME"].ToString();
            //PlatCommon.SysBase.SystemParm.Warcode_list = "'" + PlatCommon.SysBase.SystemParm.Ward_ID + "'";
            this.Close();
        }

    }
}
