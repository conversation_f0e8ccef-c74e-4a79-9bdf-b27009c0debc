﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Web;

namespace PlatCommonForm
{
    /// <summary>
    /// 电子健康卡
    /// </summary>
    public class LstVirtualCardChk
    {

        //[DllImport(@"\VirtualCardChk.dll", EntryPoint = "VirtualCardTrans")]
        [DllImport("VirtualCardChk.dll", EntryPoint = "VirtualCardTrans")]
        public static extern string VirtualCardTrans(byte[] strUrl, byte[] InData, byte[] OutData);
        //public static extern IntPtr VirtualCardTrans(IntPtr strUrl, IntPtr InData, IntPtr OutData);//[MarshalAs(UnmanagedType.VBByRefStr)] ref string
        //[DllImport(@"\VirtualCardChk.dll", EntryPoint = "Set_QRflow_Mode")]
        [DllImport("VirtualCardChk.dll", EntryPoint = "Set_QRflow_Mode")]
        public static extern int Set_QRflow_Mode(int mode);

        //[DllImport(@"\VirtualCardChk.dll", EntryPoint = "EhealthID_Print")]
        [DllImport("VirtualCardChk.dll", EntryPoint = "EhealthID_Print")]
        public static extern int EhealthID_Print(string ehealthid, string cardNo, string name);

        static System.Threading.ReaderWriterLockSlim LogWriteLock = new System.Threading.ReaderWriterLockSlim();//日志文件加锁 防止出现日志文件锁死
        NM_Service.NMService.ServerPublicClient SpcLog = new NM_Service.NMService.ServerPublicClient();
        //public  DataTable dt_cs  = new DataTable();
        //统一接口调用函数、扩展屏控制函数、静态码打印调用函数
        //实名注册电子健康卡、查询电子健康卡信息、修改电子健康卡信息、验证电子健康卡二维码、生成用户用卡授权连接（主扫方式）、查询用户用卡授权结果、注销电子健康卡
        #region GetPubParamCol 获取公共请求参数字段空数据窗口
      
        private void GetPubParamCol(ref DataTable dtPara)
        {
            NM_Service.NMService.ServerPublicClient SpcOracle = new NM_Service.NMService.ServerPublicClient();
            string as_insur_type = "公共请求参数";
            string out_Param = string.Empty;

            // { "app_id":"1BQA48ETK000A718A8C000001FFAA482","biz_content":{ "operator_id":"9527","operat or_name":"admin","out_verify_no":"20180516000058","out_verify_time":"20180516000058","tr eatment_code":"010105"},"enc_type":"SM4","key":"1BQA48ETK001A718A8C00000FE996B9B","metho d":"ehc.ehealthcode.verify","digest_type":"SM3","term_id":"35020010001","timestamp":"201 80516000058","version":"X.M.0.1"}
            string getParamSql = " select code from LST_INTERFACE.LST_DICT t where insur_type = '" + as_insur_type + "' and PARAMTYPE = 'in'  and VALUESOURCE = '数据窗口' and flag = 1";
            DataSet ds = SpcOracle.GetDataBySql(getParamSql);
            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
            {
                string ls_col1 = string.Empty;
                string ls_instr = string.Empty;
                string ls_valuesource = string.Empty;
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    dtPara.Columns.Add(dr[0].ToString());
                }
            }
        }
        #endregion
        #region 获取  method
        private string GetInterfaceCode(string as_insur_type, string as_name)
        {
            NM_Service.NMService.ServerPublicClient SpcOracle = new NM_Service.NMService.ServerPublicClient();
            string as_method = string.Empty;
            //as_insur_type = "接口名称";
            string Sqlgetvalue = "select CODE from LST_INTERFACE.LST_DICT t where t.insur_type = '" + as_insur_type + "' and name = '" + as_name + "' and flag = 1 ";
            as_method = SpcOracle.GetDataBySql(Sqlgetvalue).Tables[0].Rows[0]["CODE"].ToString();

            return as_method;
        }
        #endregion
        //生成入参串函数
        #region SetinParamValue 生成入参串函数
        /// <summary>
        /// 生成入参串函数
        /// </summary>
        /// <param name="SpcOracle">数据库连接</param>
        /// <param name="as_insur_type">交易类型</param>
        /// <param name="as_paramtype">参数类型 in-入参</param>
        /// <param name="dtPara">入参取值数据窗口</param>
        /// <param name="in_json">入参子json</param>
        /// <returns></returns>
        private string SetinParamValue(string as_insur_type, string as_paramtype, DataTable dtPara, string in_json)
        {
            NM_Service.NMService.ServerPublicClient SpcOracle = new NM_Service.NMService.ServerPublicClient();
            //string as_paramtype = 'in';
            string out_Param = string.Empty;
            string getParamSql = " select t.* from LST_INTERFACE.LST_DICT t where insur_type = '" + as_insur_type + "' and PARAMTYPE = '" + as_paramtype + "' and flag = 1 ";

            DataSet ds = SpcOracle.GetDataBySql(getParamSql);
            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
            {
                string ls_col1 = string.Empty;
                string ls_instr = string.Empty;
                string ls_dz = string.Empty;
                string ls_valuesource = string.Empty;
                foreach (DataRow dr in ds.Tables[0].Rows)
                {

                    ls_col1 = dr["CODE"] == DBNull.Value ? "" : dr["CODE"].ToString().Trim();
                    ls_valuesource = dr["VALUESOURCE"] == DBNull.Value ? "" : dr["VALUESOURCE"].ToString().Trim();
                    ls_instr = "";
                    switch (ls_valuesource)
                    {
                        case "固定值":
                            ls_instr = dr["GDZ"] == DBNull.Value ? "" : dr["GDZ"].ToString().Trim();
                            break;
                        case "数据窗口":
                            ls_instr = dtPara.Rows[0][ls_col1] == DBNull.Value ? "" : dtPara.Rows[0][ls_col1].ToString().Trim();
                            break;
                        case "JSONINSTR":
                            // ls_instr = "{" + in_json + "}";
                            ls_instr = in_json;
                            break;
                        case "时间戳":
                            ls_instr = Convert.ToString((DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 10000000);
                            break;
                        case "对照":
                            ls_dz = dr["GDZ"] == DBNull.Value ? "" : dr["GDZ"].ToString().Trim();
                            string ls_his_instr = dtPara.Rows[0][ls_col1] == DBNull.Value ? "" : dtPara.Rows[0][ls_col1].ToString().Trim();
                            // 诊疗环节对照 ,标准科室对照
                            string dzSql = "select t.* from LST_INTERFACE.LST_DICT t where insur_type = '" + ls_dz + "' and  code = '" + ls_his_instr + "'";
                            ls_instr = SpcOracle.GetDataBySql(dzSql).Tables[0].Rows[0]["LST_CODE"].ToString();
                            break;
                        case "加密":
                            continue; //不会写

                    }
                    if (ls_valuesource.Equals("JSONINSTR"))
                    {
                        out_Param += ",\"" + ls_col1 + "\":" + ls_instr;
                    }
                    else
                    {
                        out_Param += ",\"" + ls_col1 + "\":\"" + ls_instr + "\"";
                    }

                }
                out_Param = out_Param.Substring(1, out_Param.Length - ",".Length);
                out_Param = "{" + out_Param + "}";
            }
            return out_Param;
        }
        #endregion
        //实名注册电子健康卡
        #region ZcDzjkk,实名注册电子健康卡
        /// <summary>
        /// 实名注册电子健康卡
        /// </summary>
        /// <param name="SpcOracle">数据库连接</param>
        /// <param name="dt_instr">入参值数据窗口</param>
        /// <param name="as_term_id">终端编号</param>
        /// <param name="as_ip">ip</param>
        /// <param name="as_oper">操作员</param>
        /// <param name="out_json">出参串</param>
        /// <param name="dt_cs">出参数据窗口，可直接提取数据用</param>
        /// <returns></returns>
        public string ZcDzjkk(DataTable dt_instr, string as_term_id, string as_ip, string as_oper, ref string out_json, ref DataTable dt_cs)
        {
            NM_Service.NMService.ServerPublicClient SpcOracle = new NM_Service.NMService.ServerPublicClient();
            string in_json = string.Empty;
            string in_biz_json = string.Empty;
            string as_paramtype = "in";
            string as_insur_type = "实名注册电子健康卡";
            writeLog("----------------------------------------------------------------------");
            writeLog(as_insur_type + "开始");
            #region dt_instr 实名注册电子健康卡注释
            //外部注册流水号 out_register_no 数据窗口
            //外部注册时间  out_register_time 数据窗口
            //认证方式 certificate_mode    数据窗口
            //申请方式    apply_method 固定值
            //证件类型 id_type 数据窗口
            //证件号 id_no 数据窗口
            //用户姓名 user_name   数据窗口
            //用户性别    user_sex 数据窗口
            //手机号码 mobile_phone    数据窗口
            //出生日期    birthday 数据窗口
            //民族 nation  数据窗口
            //联系电话    telephone 数据窗口
            //居住地址 address 数据窗口
            //工作单位    work_unit 数据窗口
            //照片格式 photo_fomat 数据窗口
            //照片信息    photo_info 数据窗口
            //绑定卡列表 card_list   数据窗口
            //公众号关注二维码    pub_flag 数据窗口
            #endregion
            #region 生成 实名注册电子健康卡 入参 in_biz_json
            in_biz_json = SetinParamValue(as_insur_type, as_paramtype, dt_instr, "");
            #endregion
            #region  dtPara  公共请求参数注释
            // 接口名称	method	数据窗口
            // 应用编号 app_id  固定值
            // 终端编号 term_id 数据窗口
            //接口版本号 version 固定值
            //请求时间戳   timestamp 时间戳
            //摘要类型 digest_type 固定值
            //摘要值 digest 数据窗口
            //加密类型 enc_type    固定值
            //请求参数集合  biz_content JSONINSTR
            //app_key key 固定值

            #endregion
            #region  生成 入参,公共请求参数 包含子串biz_content(in_biz_json)
            DataTable dtPara = new DataTable();
            GetPubParamCol(ref dtPara);
            DataRow drPara = dtPara.NewRow();
            //获取公共请求参数
            string as_method = GetInterfaceCode("接口名称", as_insur_type);
            drPara["method"] = as_method;
            drPara["term_id"] = as_term_id;
            dtPara.Rows.Add(drPara);
            //生成入参串
            writeLog("生成InData");
            in_json = SetinParamValue("公共请求参数", as_paramtype, dtPara, in_biz_json);
            #endregion
            #region 开始调用 dll
            string as_return = string.Empty;
            string Bout_json = string.Empty;
            //调用dll
            //StringBuilder Bout_json = new StringBuilder(2048);
            UseVirtualCardTrans(in_json, as_insur_type, as_ip, as_oper, ref Bout_json, ref as_return);
            #endregion
            #region 解析出参赋值给数据窗口
           // string ls_return = GetOutParam(as_insur_type, "out", out_json.Trim());
            #endregion
            writeLog(as_insur_type + "结束");
            writeLog("----------------------------------------------------------------------");
            return out_json;
        }
        #endregion
        //验证电子健康卡二维码
        #region YzDzjkkEwm 验证电子健康卡二维码
        /// <summary>
        /// 验证电子健康卡二维码
        /// </summary> 
        /// <param name="SpcOracle">数据库连接</param>
        /// <param name="dt_instr">入参值数据窗口</param>
        /// <param name="as_term_id">终端编号</param>
        /// <param name="out_json">出参串</param>
        /// <param name="dt_cs">出参数据窗口，可直接提取数据用</param>
        /// <returns>返回值</returns>
        public string YzDzjkkEwm(NM_Service.NMService.ServerPublicClient SpcOracle,DataTable dt_instr, string as_term_id, ref string out_json,ref DataTable dt_cs )
        {
            //NM_Service.NMService.ServerPublicClient SpcOracle = new NM_Service.NMService.ServerPublicClient();
            string in_json = string.Empty;
            string in_biz_json = string.Empty;
            string as_paramtype = "in";
            string as_insur_type = "电子健康卡二维码验证"; 
            writeLog("----------------------------------------------------------------------");
            writeLog(as_insur_type + "开始");
            #region  dt_instr 电子健康卡二维码验证注释
            //  外部验证流水号 out_verify_no 数据窗口
            //外部验证时间 out_verify_time 数据窗口
            //用卡验证操作员编号 operator_id 数据窗口
            //用卡验证操作员姓名 operator_name 数据窗口
            //认证方式 certificate_mode 数据窗口
            //诊疗环节编码 treatment_code 对照(insur_type = 诊疗环节对照)
            // ip 地址  ip_addr 数据窗口
            //科室代码    office_code 对照(insur_type = 标准科室对照)
            #endregion
            # region 生成 电子健康卡二维码验证 入参 in_biz_json
            in_biz_json = SetinParamValue(as_insur_type, as_paramtype, dt_instr, "");
            #endregion
            #region  dtPara  公共请求参数注释
            // 接口名称	method	数据窗口
            // 应用编号 app_id  固定值
            // 终端编号 term_id 数据窗口
            //接口版本号 version 固定值
            //请求时间戳   timestamp 时间戳
            //摘要类型 digest_type 固定值
            //摘要值 digest 数据窗口
            //加密类型 enc_type    固定值
            //请求参数集合  biz_content JSONINSTR
            //app_key key 固定值

            #endregion
            #region  生成 入参,公共请求参数 包含子串biz_content(in_biz_json)
            DataTable dtPara = new DataTable();
            GetPubParamCol(ref dtPara);
            DataRow drPara = dtPara.NewRow();
            //获取公共请求参数
            string as_method = GetInterfaceCode("接口名称", as_insur_type);
            drPara["method"] = as_method;
            drPara["term_id"] = as_term_id;
            dtPara.Rows.Add(drPara);
            //生成入参串;
            in_json = SetinParamValue("公共请求参数", as_paramtype, dtPara, in_biz_json);
            #endregion
            #region 开始调用 dll
            string as_return = string.Empty;
            string ip = PlatCommon.Common.PublicFunction.GetLoacalIP();
            //调用dll 
            UseVirtualCardTrans(in_json, as_insur_type, ip, PlatCommon.SysBase.SystemParm.LoginUser.ID, ref out_json, ref as_return);
            #endregion
            #region 解析出参赋值给数据窗口 
            string ls_out = "out";
            writeLog(as_insur_type + "解析出参开始");
            string ls_return = GetOutParam(as_insur_type, ls_out, out_json ,ref  dt_cs );
            writeLog(as_insur_type + "解析出参结束");
            #endregion 
            writeLog(as_insur_type + "结束");
            writeLog("----------------------------------------------------------------------");

            return as_return;  

        }

        #endregion
        /// <summary>
        /// 验证电子健康卡二维码
        /// </summary>
        /// <param name="dt_instr">入参值数据窗口</param>
        /// <param name="as_term_id">终端编号</param>
        /// <returns>出参串</returns>
        public string YzDzjkkEwm(DataTable dt_instr, string as_term_id)
        {
            NM_Service.NMService.ServerPublicClient SpcOracle = new NM_Service.NMService.ServerPublicClient();
            string in_json = string.Empty;
            string in_biz_json = string.Empty;
            string as_paramtype = "in";
            string as_insur_type = "电子健康卡二维码验证";
            writeLog("----------------------------------------------------------------------");
            writeLog(as_insur_type + "开始");
            #region  dt_instr 电子健康卡二维码验证注释
            //  外部验证流水号 out_verify_no 数据窗口
            //外部验证时间 out_verify_time 数据窗口
            //用卡验证操作员编号 operator_id 数据窗口
            //用卡验证操作员姓名 operator_name 数据窗口
            //认证方式 certificate_mode 数据窗口
            //诊疗环节编码 treatment_code 对照(insur_type = 诊疗环节对照)
            // ip 地址  ip_addr 数据窗口
            //科室代码    office_code 对照(insur_type = 标准科室对照)
            #endregion
            # region 生成 电子健康卡二维码验证 入参 in_biz_json 
            in_biz_json = SetinParamValue(as_insur_type, as_paramtype, dt_instr, "");
            #endregion
            #region  dtPara  公共请求参数注释
            // 接口名称	method	数据窗口
            // 应用编号 app_id  固定值
            // 终端编号 term_id 数据窗口
            //接口版本号 version 固定值
            //请求时间戳   timestamp 时间戳
            //摘要类型 digest_type 固定值
            //摘要值 digest 数据窗口
            //加密类型 enc_type    固定值
            //请求参数集合  biz_content JSONINSTR
            //app_key key 固定值

            #endregion
            #region  生成 入参,公共请求参数 包含子串biz_content(in_biz_json)
            DataTable dtPara = new DataTable();
            GetPubParamCol(ref dtPara);
            DataRow drPara = dtPara.NewRow();
            //获取公共请求参数
            string as_method = GetInterfaceCode("接口名称", as_insur_type);
            drPara["method"] = as_method;
            drPara["term_id"] = as_term_id;
            dtPara.Rows.Add(drPara);
            //生成入参串 
            in_json = SetinParamValue("公共请求参数", as_paramtype, dtPara, in_biz_json);
           
            #endregion
            #region 开始调用 dll
            string as_return = string.Empty;
            string Bout_json = string.Empty;
            //调用dll 
            UseVirtualCardTrans(in_json, as_insur_type, dt_instr.Rows[0]["IP_ADDR"].ToString(), dt_instr.Rows[0]["OPERATOR_ID"].ToString(), ref Bout_json, ref as_return);
            #endregion
            #region 解析出参赋值给数据窗口
            string ls_out = "out";
            // string ls_return = GetOutParam(as_insur_type, ls_out, Bout_json );
            #endregion
            
            writeLog(as_insur_type + "结束");
            writeLog("----------------------------------------------------------------------");

            return Bout_json;// as_return;

        }
        //调用统一接口调用函数
        #region UseVirtualCardTrans 调用统一接口调用函数
        /// <summary>
        ///  调用统一接口调用函数
        /// </summary>        
        /// <param name="in_json">入参</param>
        /// <param name="insur_type">交易类型</param>
        /// <param name="ls_ip">IP地址</param>
        /// <param name="ls_oper">操作员</param>
        /// <param name="out_json">出参</param>
        /// <param name="as_return">返回值</param>
        [System.Runtime.ExceptionServices.HandleProcessCorruptedStateExceptions] 
        public void UseVirtualCardTrans(string in_json, string insur_type, string ls_ip, string ls_oper, ref string out_json, ref string as_return)
        {
            writeLog("本机IP=" + ls_ip);
            writeLog("操作员=" + ls_oper);
            NM_Service.NMService.ServerPublicClient SpcOracle = new NM_Service.NMService.ServerPublicClient();
            string geturl = "select t.* from LST_INTERFACE.LST_DICT t where insur_type = '请求地址' and name = 'VirtualCardTrans' and flag = 1 "; 
            string strUrl = SpcOracle.GetDataBySql(geturl).Tables[0].Rows[0]["CODE"].ToString();
            byte[] ptrurl = Encoding.Default.GetBytes(strUrl);
            writeLog("Url=" + strUrl);
            writeLog("入参=" + in_json);
            byte[] ptrIn = Encoding.Default.GetBytes(in_json);
            byte[] ptrout = new byte[2048];
            try
            {
                string as_return1 = VirtualCardTrans( ptrurl,  ptrIn, ptrout);
                as_return = as_return1;
                writeLog("返回值=" + as_return);
                out_json = Encoding.Default.GetString(ptrout);
                writeLog("出参=" + out_json);
               // InsertLog(insur_type, in_json, out_json, as_return, ls_ip, ls_oper);
            }
            catch (AccessViolationException ev)
            {
                writeLog("VirtualCardTrans_Exception=" + ev.Message);
              //  InsertLog(insur_type, in_json, out_json, ev.Message, ls_ip, ls_oper);                
            }
        }
        #endregion
        #region writeLog、InsertLog 写日志
        //写log
        private void writeLog(string str)
        {
            try
            {
                LogWriteLock.EnterWriteLock();
                DateTime d = DateTime.Now;
                string path = AppDomain.CurrentDomain.BaseDirectory + "Lstlog/";
                string fileName = d.ToString("yyyy-MM-dd-HH") + ".txt";

                if (!Directory.Exists(path))
                    Directory.CreateDirectory(path);

                using (StreamWriter file = new StreamWriter(path + fileName, true))
                {
                    if (string.IsNullOrEmpty(str))
                    { file.WriteLine(""); }
                    else if (str.Contains("-------") == true)
                    { file.WriteLine(str); }
                    else
                    {
                        file.WriteLine(d.ToString("yyyy-MM-dd HH:mm:ss") + "==>" + str);
                    }
                }
            }
            catch
            {

            }
            finally
            {
                LogWriteLock.ExitWriteLock();
            }

        }
        //写数据库log
        private void InsertLog(string as_insur_type, string in_json, string out_json, string err_message, string ls_ip, string ls_oper)
        {
            string strInsertSql = @" insert into lst_interface.lst_log t (interfacecode,insur_type,in_json,out_json,err_message,last_date_time,ip,oper)
                                     values
                                     ('lst','{0}','{1}','{2}','{3}',sysdate  ,'{4}','{5}') ";
            strInsertSql = string.Format(as_insur_type, in_json, out_json, err_message, ls_ip, ls_oper);
            Dictionary<string, string> idc = new Dictionary<string, string>();
            idc.Add(strInsertSql, "插入lst_interface.lst_log表失败！");
            string str = SpcLog.SaveTable(idc);
            if (string.IsNullOrEmpty(str) == false)
            {
                writeLog("----------------------------");
                writeLog("InsertLog==>交易类型：'" + as_insur_type + "',入参：'" + in_json + "',出参：'" + out_json + "',错误信息：'" + err_message + ",ip:" + ls_ip + ",操作员：" + ls_oper);
                writeLog("err_InsertLog:" + strInsertSql);
            }
            writeLog("--------------------------------------------------------------");
        }
        #endregion
        #region GetOutParam 解析出参函数
        /// <summary>
        /// 解析出参函数
        /// </summary>
        /// <param name="SpcOracle">数据库连接</param>
        /// <param name="as_insur_type">交易类型</param>
        /// <param name="as_paramtype">参数类型 out_出参</param>
        /// <param name="out_json">出参串</param>
        /// <param name="dt_cs">出参数据窗口</param>
        [System.Runtime.ExceptionServices.HandleProcessCorruptedStateExceptions]
        public string GetOutParam(string as_insur_type, string as_paramtype, string out_json,ref DataTable dt_cs) 
        {
            try
            {  
            if (string.IsNullOrEmpty(out_json) || out_json.Equals(""))
            { writeLog("GetOutParam out_json=null"  );  return "1"; }
          //  writeLog("GetOutParam out_json=" + out_json);
            if (out_json.Contains("respCode"))
            {
                Newtonsoft.Json.Linq.JObject joresp = (Newtonsoft.Json.Linq.JObject)Newtonsoft.Json.JsonConvert.DeserializeObject(out_json.ToUpper());

                if (out_json.ToUpper().Contains("\"RESPCODE\":\"0000\"") || out_json.ToUpper().Contains("\"RESPCODE\":0000"))
                {
                   // writeLog("RESPCODE=0000"  );
                }
                else
                {
                    writeLog("GetOutParam RESPCODE=" + out_json);
                    //   poutdata ={ "respCode":"9003","respMsg":"验签失败"}
                    dt_cs.Columns.Add("RESPCODE");
                    dt_cs.Columns.Add("RESPMSG");
                    DataRow drresp = dt_cs.NewRow();
                    drresp["RESPCODE"] = joresp["RESPCODE"];
                    drresp["RESPMSG"] = joresp["RESPMSG"];
                    dt_cs.Rows.Add(drresp);
                    return "1";
                }
            }
            string ls_biz_content = out_json ;          
            NM_Service.NMService.ServerPublicClient SpcOracle = new NM_Service.NMService.ServerPublicClient();
           
           // dt_cs = new DataTable();
            DataTable dt_outgg = new DataTable();
           
            //  Newtonsoft.Json.JsonSerializer jsonSerializer = new Newtonsoft.Json.JsonSerializer();
            Newtonsoft.Json.Linq.JObject jo = new Newtonsoft.Json.Linq.JObject();
          
            if (out_json.ToLower().Contains("biz_content") == true)
            {
              
                string getGgParamSql = " select t.* from LST_INTERFACE.LST_DICT t where insur_type = '公共响应参数' and PARAMTYPE = '" + as_paramtype + "' and flag = 1 ";
              
                dt_outgg = SpcOracle.GetDataBySql(getGgParamSql).Tables[0];
              
                foreach (DataRow drgg in dt_outgg.Rows)
                {
                    
                    dt_cs.Columns.Add(drgg["CODE"].ToString());
                }

                jo = (Newtonsoft.Json.Linq.JObject)Newtonsoft.Json.JsonConvert.DeserializeObject(out_json);
              
                ls_biz_content = jo["biz_content"].ToString() ;
            }
            string getParamSql = " select t.* from LST_INTERFACE.LST_DICT t where insur_type = '" + as_insur_type + "' and PARAMTYPE = '" + as_paramtype + "' and flag = 1 ";
            
            DataTable dt_out = SpcOracle.GetDataBySql(getParamSql).Tables[0];
            
            foreach (DataRow dr in dt_out.Rows)
            {
               
                dt_cs.Columns.Add(dr["CODE"].ToString());
            }
            Newtonsoft.Json.Linq.JObject jo1 = (Newtonsoft.Json.Linq.JObject)Newtonsoft.Json.JsonConvert.DeserializeObject(ls_biz_content);
           
            DataRow dr_insert =  dt_cs.NewRow();
            if (dt_outgg.Rows.Count > 0)
            {
               
                foreach (DataRow drgg in dt_outgg.Rows)
                {
                  
                    string ls_code = drgg["CODE"].ToString();
                    
                    dr_insert[ls_code] = jo[ls_code].ToString();
                }
            }

            foreach (DataRow dr in dt_out.Rows)
            {
               
                string ls_code1 = dr["CODE"].ToString();
             
                dr_insert[ls_code1] = jo1[ls_code1].ToString();
              
                

            }
          
            dt_cs.Rows.Add(dr_insert);
           
            }
            catch (AccessViolationException ev)
            {
                writeLog("GetOutParam_VirtualCardTrans_Exception=" + ev.Message);
             //   InsertLog(insur_type, in_json, out_json, ev.Message, ls_ip, ls_oper);
            }

            return "0";
        }
        #endregion

    }
}