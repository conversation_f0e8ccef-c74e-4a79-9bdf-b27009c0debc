﻿/*********************************************
* 文 件 名：Cs02DataSetHelper
* 类 名 称：Cs02DataSetHelper
* 功能说明：DataSet操作类
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：付军
* 创建时间：2016-10-28
* 版 本 号：1.0.0.1
* 修改时间：尹志伟
* 修 改 人：2018-06-12
* CLR 版本：4.0.30319.42000
/*********************************************/

using System;
using System.Data;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Xml;
using System.Text.RegularExpressions;
using System.Reflection;
using PlatCommon.Base01;

namespace PlatCommon.Base02
{
    /// <summary>
    /// DataSet操作类
    /// </summary>	
    public class Cs02DataSetHelper
    {
        /// <summary>
		/// 构造函数
		/// </summary>
        private Cs02DataSetHelper()
		{
        }

        /// <summary>
        /// 数据集是否具有记录
        /// </summary>
        /// <param name="dsData">数据集</param>
        /// <returns></returns>
        public static bool HasRecord(DataSet dsData)
        {
            return (dsData != null && dsData.Tables.Count > 0 && dsData.Tables[0].Rows.Count > 0);
        }

        /// <summary>
        /// 将行转换为列
        /// </summary>
        /// <param name="strColName">列名称</param>
        /// <param name="strColValue">列值</param>
        /// <param name="dtSrc">源DataTable</param>
        /// <returns>转换后的Datatable</returns>
        public static DataTable ConvertRowToCol(DataTable dtSrc, string strColName, string strColValue)
        {
            // 创建表
            DataTable dtDest = new DataTable(dtSrc.TableName);

            // 创建列
            foreach (DataRow drSrc in dtSrc.Rows)
            {
                string colNameValue = drSrc[strColName].ToString();
                dtDest.Columns.Add(colNameValue);
            }

            // 添加数据
            DataRow drEdit = dtDest.NewRow();

            foreach (DataRow dr in dtSrc.Rows)
            {
                string colNameValue = dr[strColName].ToString();
                drEdit[colNameValue] = dr[strColValue].ToString();
            }

            dtDest.Rows.Add(drEdit);

            return dtDest;
        }

        /// <summary>
        /// XML文件，转成DATASET 
        /// </summary>
        /// <param name="strXmlFile">要转换的XML文件的文件名</param>
        /// <returns></returns>
        public static DataSet GetDataSetFromFile(string strXmlFile)
        {
            StringReader stream = null;
            XmlTextReader reader = null;

            try
            {
                XmlDocument xmld = new XmlDocument();
                xmld.Load(strXmlFile);
                
                DataSet xmlDS = new DataSet();
                stream = new StringReader(xmld.InnerXml);
                
                reader = new XmlTextReader(stream);
                xmlDS.ReadXml(reader);
                
                return xmlDS;
            }
            finally
            {
                if (reader != null)
                {
                    reader.Close();
                }
            }
        }

        /// <summary>
        /// 将DataSet 保存到文件中, 如果目录不存在，创建目录
        /// </summary>
        /// <param name="dsData">要保存的DataSet</param>
        /// <param name="strXmlFile">指定文件名</param>
        public static void DataSetToXmlFile(DataSet dsData, string strXmlFile)
        {
            string pathStr = Path.GetDirectoryName(strXmlFile);
            
            if (!Directory.Exists(pathStr))
                Directory.CreateDirectory(pathStr);

            //每次重新生成 临时temp.xml
            if (File.Exists(strXmlFile))
            {
                File.Delete(strXmlFile);
            }

            dsData.WriteXml(strXmlFile, XmlWriteMode.WriteSchema);
        }

        /// <summary>
        /// 将DataSet 保存到文件中
        /// </summary>
        /// <param name="dsData">要保存的DataSet</param>
        public static void DataSetToXmlFile(DataSet dsData)
        { 
            string strName = string.IsNullOrEmpty(dsData.DataSetName) ? "ds" : dsData.DataSetName;
            string strXmlFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, strName + ".xml");
             
            //每次重新生成 临时temp.xml
            if (File.Exists(strXmlFile))
            {
                File.Delete(strXmlFile);
            }
            dsData.WriteXml(strXmlFile, XmlWriteMode.WriteSchema);
        }

        /// <summary>
        /// 判断是否包含特定名称的列
        /// </summary>
        /// <param name="strCloName">特定名称</param>
        /// <param name="dcc">DataColumnCollection</param>
        /// <returns></returns>
        public static bool IsInColumns(string strCloName, DataColumnCollection dcc)
        {
            foreach (DataColumn dc in dcc)
            {
                if (dc.ColumnName.ToLower() == strCloName.ToLower())
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 根据过滤条件得到数据集中指定列的最大值
        /// </summary>
        /// <param name="dsData">数据集</param>
        /// <param name="strFilter">过滤条件</param>
        /// <param name="strColName">指定列</param>
        /// <param name="iNullVaue">找不到时的缺省值</param>
        /// <returns></returns>
        public static int GetMaxValue(DataSet dsData, string strFilter, string strColName, int iNullVaue)
        {
            if (Cs02DataSetHelper.HasRecord(dsData) == false) return iNullVaue;

            if (dsData.Tables[0].Columns.Contains(strColName) == false) return iNullVaue;

            int iValue = 0;
            DataRow[] drArrFind = dsData.Tables[0].Select(strFilter, strColName + " DESC");
            if (drArrFind.Length > 0)
            {
                if (int.TryParse(drArrFind[0][strColName].ToString(), out iValue)) return iValue;
            }

            return iNullVaue;
        }

        /// <summary>
        /// 从字符串中生成DataSet, 字符串以,进行分隔 0:不划,1:划
        /// </summary>
        /// <param name="strSource">字符串</param>
        /// <returns>生成的DataSet</returns>
        public static DataSet GetDictDSFromStr(string strSource)
        {
            string[] strArrItems = strSource.Split(new char[] { '，', ',' });
            DataSet dsResult = CreateDict();

            //AddDictItem(ref ds, "", "", -1);
            for (int i = 0; i < strArrItems.Length; i++)
            {
                string[] parts = strArrItems[i].Split(':');

                if (parts.Length > 1)
                {
                    AddDictItem(ref dsResult, parts[1], parts[0], i);
                }
                else
                {
                    AddDictItem(ref dsResult, parts[0], parts[0], i);
                }
            }

            return dsResult;
        }

        /// <summary>
        /// 创建字典类dataset
        /// </summary>
        /// <returns>创建的DataSet</returns>
        public static DataSet CreateDict()
        {
            DataSet dsResult = new DataSet();
            DataTable dtData = dsResult.Tables.Add("DICT_ITEM");

            dtData.Columns.Add("ITEM_NAME", typeof(String));
            dtData.Columns.Add("ITEM_CODE", typeof(String));
            dtData.Columns.Add("SHOW_ORDER", typeof(System.Int32));

            return dsResult;
        }

        /// <summary>
        /// 往字典类dataset中添加一条数据
        /// </summary>
        /// <param name="dsData">数据集</param>
        /// <param name="strItemName">项目名称</param>
        /// <param name="strItemCode">项目代码</param>
        /// <param name="iShowOrder">显示序号</param>
        public static void AddDictItem(ref DataSet dsData, string strItemName, string strItemCode, int iShowOrder)
        {
            DataRow drNew = dsData.Tables[0].NewRow();

            drNew["ITEM_NAME"] = strItemName;
            drNew["ITEM_CODE"] = strItemCode;
            drNew["SHOW_ORDER"] = iShowOrder;

            dsData.Tables[0].Rows.Add(drNew);
        }

        
        /// <summary>
        /// 复制DataRow
        /// </summary>
        /// <returns></returns>
        public static bool CopyDataRow(ref DataRow drDest, ref DataRow drSrc)
        {
            bool blnAllGet = true;

            DataTable dtDest = drDest.Table;
            DataTable dtSrc = drSrc.Table;

            foreach (DataColumn dc in dtSrc.Columns)
            {
                if (dtSrc.Columns.Contains(dc.ColumnName))
                {
                    try
                    {
                        drDest[dc.ColumnName] = drSrc[dc.ColumnName];
                    }
                    catch
                    {
                        blnAllGet = false;
                    }
                }
            }

            return blnAllGet;
        }

        /// <summary>
        /// 获取DataTable中指定列不重复的记录
        /// </summary>
        /// <param name="dtSource">需传入的数据源DataTable</param>
        /// <param name="strArrColumns">例：columns = { "DEPT_CODE", "DEPT_NAME" };  //DataTable中不重复的科室代码，科室名称</param>
        /// <returns></returns>
        public static DataTable GetDistinctDataTable(DataTable dtSource, string[] strArrColumns)
        {
            if (dtSource == null || dtSource.Rows.Count == 0) return null;
            DataView dv = new DataView(dtSource);

            DataTable dt = dv.ToTable(true, strArrColumns);
            return dt;
        }

        /// <summary>
        /// 检查DataTable中指定列是否有重复记录
        /// </summary>
        /// <param name="dtSource">需检查的DataTable</param>
        /// <param name="strArrColumns">指定检查列</param>
        /// <returns>True 有重复记录，False 无重复记录</returns>
        public static bool CheckDistinctDataTable(DataTable dtSource, string[] strArrColumns)
        {
            if (dtSource == null || dtSource.Rows.Count == 0) return false;
            DataTable dtCopy = dtSource.Copy();
            dtCopy.AcceptChanges();         //去掉删除标记行
            if (dtCopy == null || dtCopy.Rows.Count == 0) return false;
            DataView dv = new DataView(dtCopy);
            DataTable dt = dv.ToTable(true, strArrColumns);
            if (dt == null) return false;
            if (dt != null && dt.Rows.Count != dtCopy.Rows.Count)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 数据集是否具有记录
        /// </summary>
        /// <param name="dtData">数据表</param>
        /// <returns></returns>
        public static bool HasRecord(DataTable dtData)
        {
            return (dtData != null && dtData.Rows.Count > 0);
        }

        /// <summary>
        /// 获取列最大值
        /// </summary>
        /// <param name="dsData">数据集</param>
        /// <param name="strFilter">过滤串</param>
        /// <param name="strColName">列名</param>
        /// <param name="strNullValue">Null值</param>
        /// <returns></returns>
        public static string GetMaxValue(DataSet dsData, string strFilter, string strColName, string strNullValue)
        {
            if (HasRecord(dsData) == false) return strNullValue;

            return dsData.Tables[0].Compute("MAX(" + strColName + ")", strFilter).ToString();
        }

        /// <summary>
        /// 获取最小值
        /// </summary>
        /// <param name="dsData">数据集</param>
        /// <param name="strFilter">过滤串</param>
        /// <param name="strColName">列名</param>
        /// <param name="strNullValue">Null值</param>
        /// <returns></returns>
        public static string GetMinValue(DataSet dsData, string strFilter, string strColName, string strNullValue)
        {
            if (HasRecord(dsData) == false) return strNullValue;

            return dsData.Tables[0].Compute("MIN(" + strColName + ")", strFilter).ToString();
        }

        /// <summary>
        /// 获取值
        /// </summary>
        /// <param name="dsData">数据集</param>
        /// <param name="strFilter">过滤串</param>
        /// <param name="strColName">列名</param>
        /// <param name="strResult">返回查找值</param>
        public static bool GetValue(DataSet dsData, string strFilter, string strColName, ref string strResult)
        {
            if (HasRecord(dsData) == false) return false;

            DataRow[] drArrFind = dsData.Tables[0].Select(strFilter);
            if (drArrFind.Length == 0) return false;

            if (dsData.Tables[0].Columns.Contains(strColName) == false) return false;

            strResult = drArrFind[0][strColName].ToString();
            return true;
        }

        /// <summary>
        /// 创建DataSet
        /// </summary>
        /// <param name="strValList">值列表,  ","分隔</param>
        /// <param name="bNameIsInt">是否数字</param>
        /// <returns></returns>
        public static DataSet CreateDict(string strValList, bool bNameIsInt)
        {
            // 创建表结构
            DataSet dsData = CreateDict();

            // 添加记录
            string[] strArrParts = strValList.Split(',');
            for (int i = 0; i < strArrParts.Length; i++)
            {
                DataRow drNew = dsData.Tables[0].NewRow();
                drNew["ITEM_NAME"] = strArrParts[i].Trim();
                drNew["ITEM_CODE"] = strArrParts[i].Trim();

                if (bNameIsInt)
                {
                    drNew["SHOW_ORDER"] = Cs01Functions.CInt(strArrParts[i]);
                }
                else
                {
                    drNew["SHOW_ORDER"] = i;
                }

                dsData.Tables[0].Rows.Add(drNew);
            }

            return dsData;
        }

        /// <summary>
        /// 生成字典DataSet
        /// </summary>
        /// <param name="dictItems"></param>
        /// <returns></returns>
        public static DataSet CreateDict(string[,] dictItems)
        {
            // 创建表结构
            DataSet dsResult = CreateDict();

            // 添加记录
            for (int i = 0; i < dictItems.GetLength(0); i++)
            {
                DataRow drNew = dsResult.Tables[0].NewRow();
                drNew["ITEM_CODE"] = dictItems[i, 0].Trim();
                drNew["ITEM_NAME"] = dictItems[i, 1].Trim();
                drNew["SHOW_ORDER"] = i;

                dsResult.Tables[0].Rows.Add(drNew);
            }

            return dsResult;
        }

        /// <summary>
        /// 添加新行
        /// </summary>
        /// <param name="dtData">数据表</param>
        /// <param name="strArrColNames">列名数组</param>
        /// <param name="strArrValues">值数组</param>
        public static void AddNewRow(DataTable dtData, string[] strArrColNames, string[] strArrValues)
        {
            DataRow drNew = dtData.NewRow();

            if (strArrColNames != null)
            {
                for (int i = 0; i < strArrColNames.Length; i++)
                {
                    drNew[strArrColNames[i]] = strArrValues[i];
                }
            }
            else
            {
                foreach (DataColumn dc in dtData.Columns)
                {
                    if (dc.AllowDBNull == false) dc.AllowDBNull = true;
                }
            }

            dtData.Rows.Add(drNew);
        }

        /// <summary>
        /// 添加新行
        /// </summary>
        /// <param name="dsData">数据集</param>
        /// <param name="strArrColNames">列名数组</param>
        /// <param name="strArrValues">值数组</param>
        public static void AddNewRow(DataSet dsData, string[] strArrColNames, string[] strArrValues)
        {
            AddNewRow(dsData.Tables[0], strArrColNames, strArrValues);
        }

        /// <summary>
        /// 获取某张表中某个字段值的列表
        /// </summary>
        /// <param name="dtData">数据表</param>
        /// <param name="strColName">列名</param>
        /// <returns></returns>
        public static ArrayList GetItems(DataTable dtData, string strColName)
        {
            return GetItems(dtData, strColName, "");
        }

        /// <summary>
        /// 获取某张表中某个字段值的列表
        /// </summary>
        /// <param name="dtData">数据表</param>
        /// <param name="strColName">列名</param>
        /// <param name="strFilter">过滤串</param>
        /// <returns></returns>
        public static ArrayList GetItems(DataTable dtData, string strColName, string strFilter)
        {
            ArrayList arr = new ArrayList();
            string strColValue = string.Empty;

            DataRow[] drArrFind = dtData.Select(strFilter, strColName);
            for (int i = 0; i < drArrFind.Length; i++)
            {
                if (i == 0)
                {
                    strColValue = Cs01Functions.CStr(drArrFind[i][strColName]);
                    arr.Add(strColValue);
                    continue;
                }

                if (strColValue.Equals(Cs01Functions.CStr(drArrFind[i][strColName])) == false)
                {
                    strColValue = Cs01Functions.CStr(drArrFind[i][strColName]);
                    arr.Add(strColValue);
                }
            }

            return arr;
        }

        /// <summary>
        /// 获取名称字符串, 多值之间用","分隔
        /// </summary>
        /// <param name="dsData">数据集</param>
        /// <param name="strCodeList">名称字符串</param>
        /// <param name="strColName">名称列</param>
        /// <param name="strColCode">代码列</param>
        /// <returns></returns>
        public static string GetNameList(DataSet dsData, string strCodeList, string strColName, string strColCode)
        {
            if (HasRecord(dsData) == false) return string.Empty;
            if (dsData.Tables[0].Columns.Contains(strColName) == false) return string.Empty;
            if (dsData.Tables[0].Columns.Contains(strColCode) == false) return string.Empty;

            string[] arrCode = strCodeList.Split(',');
            string result = string.Empty;

            DataRow[] drFind = null;
            for (int i = 0; i < arrCode.Length; i++)
            {
                string strCode = arrCode[i].Trim();
                if (string.IsNullOrEmpty(strCode)) continue;

                drFind = dsData.Tables[0].Select(strColCode + " = " + Cs02StringHelper.SqlConvert(strCode));
                if (drFind.Length == 0) continue;

                if (result.Length > 0) result += ",";
                result += drFind[0][strColName].ToString().Trim();
            }

            return result;
        }

        /// <summary>
        /// 表转换为实体对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dtData">数据表</param>
        /// <returns></returns>
        public static List<T> ConvertDataTableToClass<T>(DataTable dtData)
        {
            List<T> data = new List<T>();
            foreach (DataRow row in dtData.Rows)
            {
                T item = GetItem<T>(row);
                data.Add(item);
            }
            return data;
        }

        /// <summary>
        /// 数据行转换为实体对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="drData">数据行</param>
        /// <returns></returns>
        public static T GetItem<T>(DataRow drData)
        {
            Type temp = typeof(T);
            T obj = Activator.CreateInstance<T>();

            foreach (DataColumn column in drData.Table.Columns)
            {
                foreach (PropertyInfo pro in temp.GetProperties())
                {
                    if (pro.Name == column.ColumnName)
                        pro.SetValue(obj, drData[column.ColumnName], null);
                    else
                        continue;
                }
            }
            return obj;
        }

        /// <summary>
        /// 删除DataTable中的空白行
        /// </summary>
        /// <param name="dtData">数据表</param>
        public static void DeleteEmptyRow(DataTable dtData)
        {
            for (int i = dtData.Rows.Count - 1; i >= 0; i--)
            {
                // 判断是否是空行
                bool blnEmptyRow = true;
                for (int j = 0; j < dtData.Columns.Count; j++)
                {
                    if (dtData.Columns[j].ColumnName.Equals("ROWGUID")) continue;

                    if (string.IsNullOrEmpty(dtData.Rows[i][j].ToString().Trim()) == false)
                    {
                        blnEmptyRow = false;
                        break;
                    }
                }

                // 如果是空行, 删除
                if (blnEmptyRow) dtData.Rows[i].Delete();
            }
        }

        /// <summary>
        /// 删除DataTable中的空白行
        /// </summary>
        /// <param name="dtData">数据表</param>
        /// <param name="strArrIgnoreColumns">忽略的列数组</param>
        public static void DeleteEmptyRow(DataTable dtData, string[] strArrIgnoreColumns)
        {
            for (int i = dtData.Rows.Count - 1; i >= 0; i--)
            {
                // 判断是否是空行
                bool blnEmptyRow = true;
                for (int j = 0; j < dtData.Columns.Count; j++)
                {
                    if (dtData.Columns[j].ColumnName.Equals("ROWGUID")) continue;
                    if (strArrIgnoreColumns != null && strArrIgnoreColumns.Contains<string>(dtData.Columns[j].ColumnName)) continue;

                    if (string.IsNullOrEmpty(dtData.Rows[i][j].ToString().Trim()) == false)
                    {
                        blnEmptyRow = false;
                        break;
                    }
                }

                // 如果是空行, 删除
                if (blnEmptyRow) dtData.Rows[i].Delete();
            }
        }

        /// <summary>
        /// 把行转换为表
        /// </summary>
        /// <param name="drArrRows">行数组</param>
        /// <returns></returns>
        public static DataTable ToDataTable(DataRow[] drArrRows)
        {
            if (drArrRows == null || drArrRows.Length == 0) return null;
            DataTable dtResult = drArrRows[0].Table.Clone();

            for (int i = 0; i < drArrRows.Length; i++)
            {
                dtResult.Rows.Add(drArrRows[i].ItemArray);
            }

            return dtResult;
        }

        /// <summary>
        /// 去重复数据
        /// </summary>
        /// <param name="dtSource">源数据表</param>
        /// <param name="strArrColumns"></param>
        /// <returns></returns>
        public static DataTable GetDataTable_DistinctRecord(DataTable dtSource, string[] strArrColumns)
        {
            if (dtSource == null || dtSource.Rows.Count == 0) return null;
            DataView dv = new DataView(dtSource);
            DataTable dtResult = dv.ToTable(true, strArrColumns);
            return dtResult;
        }

        /// <summary>
        /// 合并数据表，不比较数据，只比较主键, 要求有主键, 无修改操作, 只有增删
        /// </summary>
        /// <param name="dsDest">目标表</param>
        /// <param name="dsSrc">原表</param>
        /// <returns></returns>
        public static bool UpdateTableByKey(DataSet dsDest, DataSet dsSrc)
        {
            // 目标表中增加一个字段, 表示操作类型, 1:新增 3：删除 无操作, 最后要删除无操作的记录
            // 注意：无修改操作。该函数仅处理主键相同，记录就相同的情况。

            // 条件检查
            if (dsDest == null || dsDest.Tables.Count == 0 || dsDest.Tables[0].PrimaryKey.Length == 0)
            {
                throw new Exception("UpdateTable 的参数 dsDest 的表中必须要有主键!");
            }

            if (dsSrc == null || dsSrc.Tables.Count == 0)
            {
                throw new Exception("UpdateTable 的参数 源数据表 为空!");
            }

            // 以主键作为排序依据
            string strSort = string.Empty;
            for (int i = 0; i < dsDest.Tables[0].PrimaryKey.Length; i++)
            {
                strSort += (i > 0 ? "," : "") + dsDest.Tables[0].PrimaryKey[i].ColumnName;
            }

            // 进行比较
            DataRow[] drArrDests = dsDest.Tables[0].Select("", strSort);
            DataRow[] drArrSrcs = dsSrc.Tables[0].Select("", strSort);

            int idxDest = 0;
            int idxSrc = 0;

            DataRow drDest = null;
            DataRow drSrc = null;

            DataColumn[] keyCols = dsDest.Tables[0].PrimaryKey;     // 主键列

            int compareResult = 0;          // 两条记录主键比较结果: 0:相等 1:大于 -1:小于
            while (idxSrc < drArrSrcs.Length)
            {
                drSrc = drArrSrcs[idxSrc];

                // 比较主键
                compareResult = 0;

                if (idxDest >= drArrDests.Length)
                {
                    compareResult = 1;
                }
                else
                {
                    drDest = drArrDests[idxDest];

                    for (int c = 0; c < keyCols.Length; c++)
                    {
                        compareResult = drDest[keyCols[c].ColumnName].ToString().CompareTo(drSrc[keyCols[c].ColumnName].ToString());
                        if (compareResult != 0) break;
                    }
                }

                // 依据比较结果进行处理
                switch (compareResult)
                {
                    // 如果主键相同, 进行下一条记录比较
                    case 0:
                        idxDest++;
                        idxSrc++;

                        break;

                    // 如果目标小, (即：目标表中没有源表中的记录, 要删除目标表中的记录)
                    case -1:
                        drDest.Delete();
                        idxDest++;
                        break;

                    // 如果目标大, 向目标表中插入记录
                    case 1:
                        drDest = dsDest.Tables[0].NewRow();

                        foreach (DataColumn dc in dsDest.Tables[0].Columns)
                        {
                            drDest[dc.ColumnName] = drSrc[dc.ColumnName];
                        }

                        dsDest.Tables[0].Rows.Add(drDest);

                        idxSrc++;
                        break;

                    default:
                        throw new Exception("UpdateTableByKey 函数 出现非法的比较结果!");
                }
            }

            // 删除目标表中多余的记录
            for (; idxDest < drArrDests.Length; idxDest++)
            {
                drArrDests[idxDest].Delete();
            }

            return true;
        }

        /// <summary>
        /// 合并数据表，不比较数据，只比较主键, 要求有主键, 无修改操作, 只有增删
        /// </summary>
        /// <param name="dsDest">目标表</param>
        /// <param name="dsSrc">原表</param>
        /// <param name="strArrKeyCols">主键列数组</param>
        /// <returns></returns>
        public static bool UpdateTableByKey(DataSet dsDest, DataSet dsSrc, string[] strArrKeyCols)
        {
            // 目标表中增加一个字段, 表示操作类型, 1:新增 3：删除 无操作, 最后要删除无操作的记录
            // 注意：无修改操作。该函数仅处理主键相同，记录就相同的情况。

            // 条件检查
            if (dsDest == null || dsDest.Tables.Count == 0 || dsDest.Tables[0].PrimaryKey.Length == 0)
            {
                throw new Exception("UpdateTable 的参数 dsDest 的表中必须要有主键!");
            }

            if (dsSrc == null || dsSrc.Tables.Count == 0)
            {
                throw new Exception("UpdateTable 的参数 源数据表 为空!");
            }

            // 以主键作为排序依据
            string strSort = string.Empty;
            for (int i = 0; i < dsDest.Tables[0].PrimaryKey.Length; i++)
            {
                strSort += (i > 0 ? "," : "") + dsDest.Tables[0].PrimaryKey[i].ColumnName;
            }

            // 进行比较
            DataRow[] drArrDests = dsDest.Tables[0].Select("", strSort);
            DataRow[] drArrSrcs = dsSrc.Tables[0].Select("", strSort);

            int idxDest = 0;
            int idxSrc = 0;

            DataRow drDest = null;
            DataRow drSrc = null;

            int compareResult = 0;          // 两条记录主键比较结果: 0:相等 1:大于 -1:小于
            while (idxSrc < drArrSrcs.Length)
            {
                drSrc = drArrSrcs[idxSrc];

                // 比较主键
                compareResult = 0;

                if (idxDest >= drArrDests.Length)
                {
                    compareResult = 1;
                }
                else
                {
                    drDest = drArrDests[idxDest];

                    for (int c = 0; c < strArrKeyCols.Length; c++)
                    {
                        compareResult = drDest[strArrKeyCols[c]].ToString().CompareTo(drSrc[strArrKeyCols[c]].ToString());
                        if (compareResult != 0) break;
                    }
                }

                // 依据比较结果进行处理
                switch (compareResult)
                {
                    // 如果主键相同, 进行下一条记录比较
                    case 0:
                        idxDest++;
                        idxSrc++;

                        break;

                    // 如果目标小, (即：目标表中没有源表中的记录, 要删除目标表中的记录)
                    case -1:
                        drDest.Delete();
                        idxDest++;
                        break;

                    // 如果目标大, 向目标表中插入记录
                    case 1:
                        drDest = dsDest.Tables[0].NewRow();

                        foreach (DataColumn dc in dsDest.Tables[0].Columns)
                        {
                            drDest[dc.ColumnName] = drSrc[dc.ColumnName];
                        }

                        dsDest.Tables[0].Rows.Add(drDest);

                        idxSrc++;
                        break;

                    default:
                        throw new Exception("UpdateTableByKey 函数 出现非法的比较结果!");
                }
            }

            // 删除目标表中多余的记录
            for (; idxDest < drArrDests.Length; idxDest++)
            {
                drArrDests[idxDest].Delete();
            }

            return true;
        }

        /// <summary>
        /// 合并数据表，不比较数据，只比较主键, 要求有主键, 无修改操作, 只有增删
        /// </summary>
        /// <param name="dtDest">目标表</param>
        /// <param name="dtSrc">原表</param>
        /// <returns></returns>
        public static void MergeTable(DataTable dtDest, DataTable dtSrc)
        {
            // 去掉只读属性
            foreach (DataColumn dc in dtDest.Columns)
            {
                dtDest.Columns[dc.ColumnName].ReadOnly = false;
            }

            // 按主键进行排序
            string strSort = string.Empty;
            for (int i = 0; i < dtDest.PrimaryKey.Length; i++)
            {
                strSort = (i > 0 ? "," : "") + dtDest.PrimaryKey[i].ColumnName;
            }

            DataRow[] drArrDests = dtDest.Select("", strSort);
            DataRow[] drArrSrcs = dtSrc.Select("", strSort);

            // 进行记录比较
            int rowDest = 0;
            int rowSrc = 0;
            while (rowDest < drArrDests.Length && rowSrc < drArrSrcs.Length)
            {
                DataRow drDest = drArrDests[rowDest];
                DataRow drSrc = drArrSrcs[rowSrc];

                // 比较主键大小
                int iKeyCompare = 0;
                string strColName = string.Empty;
                for (int c = 0; c < dtDest.PrimaryKey.Length; c++)
                {
                    strColName = dtDest.PrimaryKey[c].ColumnName;

                    iKeyCompare = (drDest[strColName].ToString().CompareTo(drSrc[strColName].ToString()));
                    if (iKeyCompare != 0) break;
                }

                // 如果相同
                if (iKeyCompare == 0)
                {
                    rowDest++;
                    rowSrc++;

                    // 检测是否变更
                    bool blnChanged = false;
                    foreach (DataColumn dc in dtDest.Columns)
                    {
                        if (drDest[dc.ColumnName] == DBNull.Value && drSrc[dc.ColumnName] == DBNull.Value) continue;

                        if (drDest[dc.ColumnName] == DBNull.Value && drSrc[dc.ColumnName] != DBNull.Value
                         || drDest[dc.ColumnName] != DBNull.Value && drSrc[dc.ColumnName] == DBNull.Value)
                        {
                            blnChanged = true;
                            break;
                        }

                        if (drDest[dc.ColumnName] != drSrc[dc.ColumnName])
                        {
                            blnChanged = true;
                            break;
                        }
                    }

                    if (blnChanged)
                    {
                        foreach (DataColumn dc in dtDest.Columns)
                        {
                            drDest[dc.ColumnName] = drSrc[dc.ColumnName];
                        }
                    }

                    continue;
                }

                // 如果目标小, 删除目标记录
                if (iKeyCompare < 0)
                {
                    drDest.Delete();
                    rowDest++;
                    continue;
                }

                // 如果目标大, 插入源记录
                if (iKeyCompare > 0)
                {
                    rowSrc++;

                    DataRow drNew = dtDest.NewRow();
                    drNew.ItemArray = drSrc.ItemArray;
                    dtDest.Rows.Add(drNew);

                    continue;
                }
            }

            for (int i = rowDest; i < drArrDests.Length; i++)
            {
                drArrDests[i].Delete();
            }
        }

        /// <summary>
        /// 合并DataSet
        /// </summary>
        /// <param name="dsDest">目标数据集</param>
        /// <param name="dsSrc">源数据集</param>
        /// <param name="strArrPKCol">主键列数组</param>
        public static void MergeDataSet(ref DataSet dsDest, DataSet dsSrc, string[] strArrPKCol)
        {
            foreach (DataColumn dcDest in dsDest.Tables[0].Columns)
            {
                dcDest.ReadOnly = false;
            }

            // 数据合并
            foreach (DataRow drSrc in dsSrc.Tables[0].Rows)
            {
                //根据Key，生成过滤串
                string strDestFilter = string.Empty;
                for (int i = 0; i < strArrPKCol.Length; i++)
                {
                    if (i > 0) strDestFilter += " AND ";
                    strDestFilter += strArrPKCol[i] + " = " + Cs02StringHelper.SqlConvert(drSrc[strArrPKCol[i]].ToString());
                }

                // 查找记录是否存在
                DataRow[] drArrFind = dsDest.Tables[0].Select(strDestFilter);

                //找不到，加入
                if (drArrFind.Length == 0)
                {
                    DataRow drNew = dsDest.Tables[0].NewRow();
                    drNew.ItemArray = drSrc.ItemArray;
                    dsDest.Tables[0].Rows.Add(drNew);
                }
            }
        }

        /// <summary>
        /// 合并DataSet
        /// </summary>
        /// <param name="dtDest">目标数据表</param>
        /// <param name="dtSrc">源数据表</param>
        /// <param name="colPrimaryKeys">主键列</param>
        public static void MergeDataSetEx(ref DataTable dtDest, DataTable dtSrc, DataColumn[] colPrimaryKeys)
        {
            foreach (DataColumn dcDest in dtDest.Columns)
            {
                dcDest.ReadOnly = false;
            }

            // 数据合并
            foreach (DataRow drSrc in dtSrc.Rows)
            {
                bool bIsNullRec = false;

                // 查找记录是否存在
                string strDescFilter = string.Empty;
                for (int i = 0; i < colPrimaryKeys.Length; i++)
                {
                    if (i > 0) strDescFilter += " AND ";
                    strDescFilter += colPrimaryKeys[i].ColumnName + " = " + Cs02StringHelper.SqlConvert(drSrc[colPrimaryKeys[i].ColumnName].ToString());

                    // Key值为空，是空记录
                    if (drSrc[colPrimaryKeys[i].ColumnName].ToString().Length == 0) bIsNullRec = true;
                }

                //空记录，不处理
                if (bIsNullRec) continue;

                //在目标数据集中，查找
                DataRow[] drArrFind = dtDest.Select(strDescFilter);
                DataRow drEdit = null;

                //找不到，加入
                if (drArrFind.Length == 0)
                {
                    drEdit = dtDest.NewRow();

                    // 添加主键的值
                    for (int i = 0; i < colPrimaryKeys.Length; i++)
                    {
                        string keyColName = colPrimaryKeys[i].ColumnName.Trim();

                        if (dtDest.Columns[keyColName].DataType.Equals(typeof(System.DateTime)))
                        {
                            drEdit[keyColName] = Cs02DateTimeHelper.GetDateTime(drSrc[keyColName].ToString());
                            //drEdit[keyColName] = drSrc[keyColName];
                        }
                        else
                        {
                            if (drSrc[keyColName].ToString().Trim().Length == 0
                                && dtDest.Columns[keyColName].DataType.Equals(typeof(System.String)) == false)
                            {
                                drEdit[keyColName] = DBNull.Value;
                            }
                            else
                            {
                                drEdit[keyColName] = drSrc[keyColName].ToString();
                            }
                        }
                    }
                }
                else
                {
                    drEdit = drArrFind[0];
                }

                foreach (DataColumn dcDest in dtDest.Columns)
                {
                    bool blnIsKey = false;
                    for (int i = 0; i < colPrimaryKeys.Length; i++)
                    {
                        if (colPrimaryKeys[i].ColumnName.ToUpper().Equals(dcDest.ColumnName.ToUpper())) blnIsKey = true;
                    }

                    if (blnIsKey) continue;

                    if (dtSrc.Columns.Contains(dcDest.ColumnName) == false) continue;

                    if (dtDest.Columns[dcDest.ColumnName].DataType.Equals(typeof(System.DateTime)))
                    {
                        drEdit[dcDest.ColumnName] = Cs02DateTimeHelper.GetDateTime(drSrc[dcDest.ColumnName].ToString());
                        //drEdit[dcDest.ColumnName] = drSrc[dcDest.ColumnName];
                    }
                    else
                    {
                        if (drSrc[dcDest.ColumnName].ToString().Trim().Length == 0
                            && dtDest.Columns[dcDest.ColumnName].DataType.Equals(typeof(System.String)) == false)
                        {
                            drEdit[dcDest.ColumnName] = DBNull.Value;
                        }
                        else
                        {
                            drEdit[dcDest.ColumnName] = drSrc[dcDest.ColumnName].ToString();
                        }
                    }
                }

                if (drArrFind.Length == 0)
                {
                    dtDest.Rows.Add(drEdit);
                }
            }
        }

        /// <summary>
        /// 合并DataSet
        /// </summary>
        /// <param name="dtDest">目标数据表</param>
        /// <param name="dtSrc">源数据表</param>
        /// <param name="colPrimaryKeys">主键列</param>
        /// <param name="strFilter">过滤串</param>
        public static void MergeDataSetPart(ref DataTable dtDest, DataTable dtSrc, DataColumn[] colPrimaryKeys, string strFilter)
        {
            foreach (DataColumn dc in dtDest.Columns)
            {
                dc.ReadOnly = false;
            }

            // 数据合并
            DataRow[] drFindSrc = dtSrc.Select(strFilter);
            for (int src = 0; src < drFindSrc.Length; src++)
            {
                DataRow dr = drFindSrc[src];
                bool blnNullRec = false;

                // 查找记录是否存在
                string filter = string.Empty;
                for (int i = 0; i < colPrimaryKeys.Length; i++)
                {
                    if (i > 0) filter += " AND ";
                    filter += colPrimaryKeys[i].ColumnName + " = " + Cs02StringHelper.SqlConvert(dr[colPrimaryKeys[i].ColumnName].ToString());

                    if (dr[colPrimaryKeys[i].ColumnName].ToString().Length == 0) blnNullRec = true;        // 空记录
                }

                if (blnNullRec) continue;

                if (string.IsNullOrEmpty(strFilter) == false) filter += " AND (" + strFilter + ")";
                DataRow[] drFind = dtDest.Select(filter);
                DataRow drEdit = null;
                if (drFind.Length == 0)
                {
                    drEdit = dtDest.NewRow();

                    // 添加主键的值
                    for (int i = 0; i < colPrimaryKeys.Length; i++)
                    {
                        string keyColName = colPrimaryKeys[i].ColumnName.Trim();

                        if (dtDest.Columns[keyColName].DataType.Equals(typeof(System.DateTime)))
                        {
                            drEdit[keyColName] = Cs02DateTimeHelper.GetDateTime(dr[keyColName].ToString());
                            //drEdit[keyColName] = dr[keyColName];
                        }
                        else
                        {
                            if (dr[keyColName].ToString().Trim().Length == 0
                                && dtDest.Columns[keyColName].DataType.Equals(typeof(System.String)) == false)
                            {
                                drEdit[keyColName] = DBNull.Value;
                            }
                            else
                            {
                                drEdit[keyColName] = dr[keyColName].ToString();
                            }
                        }
                    }
                }
                else
                {
                    drEdit = drFind[0];
                }

                foreach (DataColumn dc in dtDest.Columns)
                {
                    bool blnIsKey = false;
                    for (int i = 0; i < colPrimaryKeys.Length; i++)
                    {
                        if (colPrimaryKeys[i].ColumnName.ToUpper().Equals(dc.ColumnName.ToUpper())) blnIsKey = true;
                    }

                    if (blnIsKey) continue;

                    if (dtSrc.Columns.Contains(dc.ColumnName) == false) continue;

                    if (dtDest.Columns[dc.ColumnName].DataType.Equals(typeof(System.DateTime)))
                    {
                        drEdit[dc.ColumnName] = Cs02DateTimeHelper.GetDateTime(dr[dc.ColumnName].ToString());
                        //drEdit[dc.ColumnName] = dr[dc.ColumnName];
                    }
                    else
                    {
                        if (dr[dc.ColumnName].ToString().Trim().Length == 0
                            && dtDest.Columns[dc.ColumnName].DataType.Equals(typeof(System.String)) == false)
                        {
                            drEdit[dc.ColumnName] = DBNull.Value;
                        }
                        else
                        {
                            drEdit[dc.ColumnName] = dr[dc.ColumnName].ToString();
                        }
                    }
                }

                if (drFind.Length == 0)
                {
                    dtDest.Rows.Add(drEdit);
                }
            }

            // 删除没有变更的数据
            DataRow[] drFindTemp = dtDest.Select(strFilter);
            for (int i = drFindTemp.Length - 1; i >= 0; i--)
            {
                if (drFindTemp[i].RowState == DataRowState.Unchanged) drFindTemp[i].Delete();
            }
        }

        /// <summary>
        /// 合并DataSet
        /// </summary>
        /// <param name="dtDest">目标数据表</param>
        /// <param name="dtSrc">源数据表</param>
        /// <param name="colPrimaryKeys">主键列</param>
        /// <param name="strDateFieldName">修改日期列</param>
        public static void MergeDataSetEx(ref DataTable dtDest, DataTable dtSrc, DataColumn[] colPrimaryKeys, string strDateFieldName)
        {
            bool modify = false;
            foreach (DataColumn dc in dtDest.Columns)
            {
                dc.ReadOnly = false;
            }

            // 数据合并
            foreach (DataRow dr in dtSrc.Rows)
            {
                bool blnNullRec = false;

                // 查找记录是否存在
                string filter = string.Empty;
                for (int i = 0; i < colPrimaryKeys.Length; i++)
                {
                    if (i > 0) filter += " AND ";
                    filter += colPrimaryKeys[i].ColumnName + " = " + Cs02StringHelper.SqlConvert(dr[colPrimaryKeys[i].ColumnName].ToString());

                    if (dr[colPrimaryKeys[i].ColumnName].ToString().Length == 0) blnNullRec = true;        // 空记录
                }

                if (blnNullRec) continue;

                DataRow[] drFind = dtDest.Select(filter);
                DataRow drEdit = null;
                if (drFind.Length == 0)
                {
                    drEdit = dtDest.NewRow();

                    // 添加主键的值
                    for (int i = 0; i < colPrimaryKeys.Length; i++)
                    {
                        string keyColName = colPrimaryKeys[i].ColumnName.Trim();

                        if (dtDest.Columns[keyColName].DataType.Equals(typeof(System.DateTime)))
                        {
                            drEdit[keyColName] = Cs02DateTimeHelper.GetDateTime(dr[keyColName].ToString());
                            //drEdit[keyColName] = dr[keyColName];
                        }
                        else
                        {
                            if (dr[keyColName].ToString().Trim().Length == 0
                                && dtDest.Columns[keyColName].DataType.Equals(typeof(System.String)) == false)
                            {
                                drEdit[keyColName] = DBNull.Value;
                            }
                            else
                            {
                                drEdit[keyColName] = dr[keyColName].ToString();
                            }
                        }
                    }
                }
                else
                {
                    drEdit = drFind[0];
                }
                //drEdit["PDAMODIFY"] = 0;
                foreach (DataColumn dc in dtDest.Columns)
                {
                    bool blnIsKey = false;
                    for (int i = 0; i < colPrimaryKeys.Length; i++)
                    {
                        if (colPrimaryKeys[i].ColumnName.ToUpper().Equals(dc.ColumnName.ToUpper())) blnIsKey = true;
                    }

                    if (blnIsKey) continue;

                    if (dtSrc.Columns.Contains(dc.ColumnName) == false) continue;
                    //判断列内容是否相同
                    if (dtDest.Columns[dc.ColumnName].DataType.Equals(typeof(System.DateTime))) //判断时间类型是否相同
                    {
                        if (!string.IsNullOrEmpty(drEdit[dc.ColumnName].ToString().Trim()))
                        {
                            if (DateTime.Parse(drEdit[dc.ColumnName].ToString().Trim()) == DateTime.Parse(dr[dc.ColumnName].ToString().Trim())) continue;
                        }

                    }
                    else if (drEdit[dc.ColumnName].ToString().Trim() == dr[dc.ColumnName].ToString().Trim()) continue;
                    modify = true;
                    if (dtDest.Columns[dc.ColumnName].DataType.Equals(typeof(System.DateTime)))
                    {
                        drEdit[dc.ColumnName] = Cs02DateTimeHelper.GetDateTime(dr[dc.ColumnName].ToString());
                        //drEdit[dc.ColumnName] = dr[dc.ColumnName];
                    }
                    else
                    {
                        if (dr[dc.ColumnName].ToString().Trim().Length == 0
                  && dtDest.Columns[dc.ColumnName].DataType.Equals(typeof(System.String)) == false)
                        {
                            drEdit[dc.ColumnName] = DBNull.Value;
                        }
                        else
                        {
                            drEdit[dc.ColumnName] = dr[dc.ColumnName].ToString();
                        }
                    }
                }
                if (modify)
                {
                    //更新修改行的日期信息
                    drEdit[strDateFieldName] = dr[strDateFieldName];
                    //drEdit["PDAMODIFY"] = 1;
                }
                if (drFind.Length == 0)
                {
                    dtDest.Rows.Add(drEdit);
                }
            }
        }

    }
}
