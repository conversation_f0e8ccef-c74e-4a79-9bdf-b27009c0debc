<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Diagnostics.Tracing.EventSource</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Diagnostics.Tracing.ActivityTracker">
             <summary>
             Tracks activities.  This is meant to be a singleton (accessed by the ActivityTracer.Instance static property)
              
             Logically this is simply holds the m_current variable that holds the async local that holds the current ActivityInfo
             An ActivityInfo is represents a activity (which knows its creator and thus knows its path). 
            
             Most of the magic is in the async local (it gets copied to new tasks)
             
             On every start event call OnStart 
             
                 Guid activityID;
                 Guid relatedActivityID;
                 if (OnStart(activityName, out activityID, out relatedActivityID, ForceStop, options))
                     // Log Start event with activityID and relatedActivityID
                 
             On every stop event call OnStop
             
                 Guid activityID;
                 if (OnStop(activityName, ref activityID  ForceStop))
                     // Stop event with activityID
                        
             On any normal event log the event with activityTracker.CurrentActivityId
             </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.ActivityTracker.OnStart(System.String,System.String,System.Int32,System.Guid@,System.Guid@,Microsoft.Diagnostics.Tracing.EventActivityOptions)">
            <summary>
            Called on work item begins.  The activity name = providerName + activityName without 'Start' suffix.
            It updates CurrentActivityId to track.   
            
            It returns true if the Start should be logged, otherwise (if it is illegal recursion) it return false. 
            
            The start event should use as its activity ID the CurrentActivityId AFTER calling this routine and its
            RelatedActivityID the CurrentActivityId BEFORE calling this routine (the creator).  
            
            If activity tracing is not on, then activityId and relatedActivityId are not set
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.ActivityTracker.OnStop(System.String,System.String,System.Int32,System.Guid@)">
             <summary>
             Called when a work item stops.  The activity name = providerName + activityName without 'Stop' suffix.
             It updates m_current variable to track this fact.   The Stop event associated with stop should log the ActivityID associated with the event.
            
             If activity tracing is not on, then activityId and relatedActivityId are not set
             </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.ActivityTracker.Enable">
            <summary>
            Turns on activity tracking.    It is sticky, once on it stays on (race issues otherwise)
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.ActivityTracker.FindActiveActivity(System.String,Microsoft.Diagnostics.Tracing.ActivityTracker.ActivityInfo)">
            <summary>
            Searched for a active (nonstopped) activity with the given name.  Returns null if not found.  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.ActivityTracker.NormalizeActivityName(System.String,System.String,System.Int32)">
            <summary>
            Strip out "Start" or "End" suffix from activity name and add providerName prefix.
            If 'task'  it does not end in Start or Stop and Task is non-zero use that as the name of the activity
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.ActivityTracker.m_current">
            <summary>
            Async local variables have the properly that the are automatically copied whenever a task is created and used
            while that task is running.   Thus m_current 'flows' to any task that is caused by the current thread that
            last set it.   
            
            This variable points a a linked list that represents all Activities that have started but have not stopped.  
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.ActivityTracker.Instance">
            <summary>
            An activity tracker is a singleton, this is how you get the one and only instance.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.ActivityTracker.CurrentActivityId">
            <summary>
            The current activity ID.  Use this to log normal events.  
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.ActivityTracker.ActivityInfo">
            <summary>
            An ActivityInfo represents a particular activity.   It is almost read-only.   The only
            fields that change after creation are
               m_lastChildID - used to generate unique IDs for the children activities and for the most part can be ignored.
               m_stopped - indicates that this activity is dead 
            This read-only-ness is important because an activity's  m_creator chain forms the 
            'Path of creation' for the activity (which is also its unique ID) but is also used as
            the 'list of live parents' which indicate of those ancestors, which are alive (if they
            are not marked dead they are alive).   
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.ActivityTracker.ActivityInfo.CreateActivityPathGuid(System.Guid@,System.Int32@)">
             <summary>
             Logically every activity Path (see Path()) that describes the activities that caused this 
             (rooted in an activity that predates activity tracking.  
            
             We wish to encode this path in the Guid to the extent that we can.  Many of the paths have
             many small numbers in them and we take advantage of this in the encoding to output as long
             a path in the GUID as possible.   
             
             Because of the possibility of GUID collision, we only use 96 of the 128 bits of the GUID
             for encoding the path.  The last 32 bits are a simple checksum (and random number) that 
             identifies this as using the convention defined here.   
            
             It returns both the GUID which has the path as well as the offset that points just beyond
             the end of the activity (so it can be appended to).  Note that if the end is in a nibble
             (it uses nibbles instead of bytes as the unit of encoding, then it will point at the unfinished
             byte (since the top nibble can't be zero you can determine if this is true by seeing if 
             this byte is nonZero.   This offset is needed to efficiently create the ID for child activities. 
             </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.ActivityTracker.ActivityInfo.CreateOverflowGuid(System.Guid*)">
            <summary>
            If we can't fit the activity Path into the GUID we come here.   What we do is simply
            generate a 4 byte number (s_nextOverflowId).  Then look for an ancestor that has  
            sufficient space for this ID.   By doing this, we preserve the fact that this activity
            is a child (of unknown depth) from that ancestor.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.ActivityTracker.ActivityInfo.AddIdToGuid(System.Guid*,System.Int32,System.UInt32,System.Boolean)">
            Add the activity id 'id' to the output Guid 'outPtr' starting at the offset 'whereToAddId'
            Thus if this number is 6 that is where 'id' will be added.    This will return 13 (12
            is the maximum number of bytes that fit in a GUID) if the path did not fit.  
            If 'overflow' is true, then the number is encoded as an 'overflow number (which has a
            special (longer prefix) that indicates that this ID is allocated differently 
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.ActivityTracker.ActivityInfo.WriteNibble(System.Byte*@,System.Byte*,System.UInt32)">
            <summary>
            Write a single Nible 'value' (must be 0-15) to the byte buffer represented by *ptr.  
            Will not go past 'endPtr'.  Also it assumes that we never write 0 so we can detect
            whether a nibble has already been written to ptr  because it will be nonzero.   
            Thus if it is non-zero it adds to the current byte, otherwise it advances and writes
            the new byte (in the high bits) of the next byte.  
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.ActivityTracker.ActivityInfo.NumberListCodes">
            <summary>
            The encoding for a list of numbers used to make Activity  GUIDs.   Basically
            we operate on nibbles (which are nice because they show up as hex digits).  The
            list is ended with a end nibble (0) and depending on the nibble value (Below)
            the value is either encoded into nibble itself or it can spill over into the
            bytes that follow.   
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.TplEtwProvider">
            <summary>
            This is supplied by the framework.   It is has the semantics that the value is copied to any new Tasks that is created
            by the current task.   Thus all causally related code gets this value.    Note that reads and writes to this VARIABLE 
            (not what it points it) to this does not need to be protected by locks because it is inherently thread local (you always
            only get your thread local copy which means that you never have races.  
            </summary>
            
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventSource">
            <summary>
            This class is meant to be inherited by a user-defined event source in order to define a managed
            ETW provider.   Please See DESIGN NOTES above for the internal architecture.  
            The minimal definition of an EventSource simply specifies a number of ETW event methods that
            call one of the EventSource.WriteEvent overloads, <see cref="M:Microsoft.Diagnostics.Tracing.EventSource.WriteEventCore(System.Int32,System.Int32,Microsoft.Diagnostics.Tracing.EventSource.EventData*)"/>, 
            or <see cref="M:Microsoft.Diagnostics.Tracing.EventSource.WriteEventWithRelatedActivityIdCore(System.Int32,System.Guid*,System.Int32,Microsoft.Diagnostics.Tracing.EventSource.EventData*)"/> to log them. This functionality 
            is sufficient for many users.
            <para>
            To achieve more control over the ETW provider manifest exposed by the event source type, the 
            [<see cref="T:Microsoft.Diagnostics.Tracing.EventAttribute"/>] attributes can be specified for the ETW event methods.
            </para><para>
            For very advanced EventSources, it is possible to intercept the commands being given to the
            eventSource and change what filtering is done (see EventListener.EnableEvents and 
            <see cref="M:Microsoft.Diagnostics.Tracing.EventListener.DisableEvents(Microsoft.Diagnostics.Tracing.EventSource)"/>) or cause actions to be performed by the eventSource, 
            e.g. dumping a data structure (see EventSource.SendCommand and
            <see cref="M:Microsoft.Diagnostics.Tracing.EventSource.OnEventCommand(Microsoft.Diagnostics.Tracing.EventCommandEventArgs)"/>).
            </para><para>
            The eventSources can be turned on with Windows ETW controllers (e.g. logman), immediately. 
            It is also possible to control and intercept the data dispatcher programmatically.  See 
            <see cref="T:Microsoft.Diagnostics.Tracing.EventListener"/> for more.
            </para>
            </summary>
            <remarks>
            This is a minimal definition for a custom event source:
            <code>
            [EventSource(Name="Samples-Demos-Minimal")]
            sealed class MinimalEventSource : EventSource
            {
                public static MinimalEventSource Log = new MinimalEventSource();
                public void Load(long ImageBase, string Name) { WriteEvent(1, ImageBase, Name); }
                public void Unload(long ImageBase) { WriteEvent(2, ImageBase); }
                private MinimalEventSource() {}
            }
            </code>
            </remarks>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.IsEnabled">
            <summary>
            Returns true if the eventSource has been enabled at all. This is the prefered test
            to be performed before a relatively expensive EventSource operation.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.IsEnabled(Microsoft.Diagnostics.Tracing.EventLevel,Microsoft.Diagnostics.Tracing.EventKeywords)">
            <summary>
            Returns true if events with greater than or equal 'level' and have one of 'keywords' set are enabled. 
            
            Note that the result of this function is only an approximation on whether a particular
            event is active or not. It is only meant to be used as way of avoiding expensive
            computation for logging when logging is not on, therefore it sometimes returns false
            positives (but is always accurate when returning false).  EventSources are free to 
            have additional filtering.    
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.IsEnabled(Microsoft.Diagnostics.Tracing.EventLevel,Microsoft.Diagnostics.Tracing.EventKeywords,Microsoft.Diagnostics.Tracing.EventChannel)">
            <summary>
            Returns true if events with greater than or equal 'level' and have one of 'keywords' set are enabled, or
            if 'keywords' specifies a channel bit for a channel that is enabled.
            
            Note that the result of this function only an approximation on whether a particular
            event is active or not. It is only meant to be used as way of avoiding expensive
            computation for logging when logging is not on, therefore it sometimes returns false
            positives (but is always accurate when returning false).  EventSources are free to 
            have additional filtering.    
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.GetGuid(System.Type)">
            <summary>
            Returns the GUID that uniquely identifies the eventSource defined by 'eventSourceType'.  
            This API allows you to compute this without actually creating an instance of the EventSource.   
            It only needs to reflect over the type.  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.GetName(System.Type)">
            <summary>
            Returns the official ETW Provider name for the eventSource defined by 'eventSourceType'.  
            This API allows you to compute this without actually creating an instance of the EventSource.   
            It only needs to reflect over the type.  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.GenerateManifest(System.Type,System.String)">
            <summary>
            Returns a string of the XML manifest associated with the eventSourceType. The scheme for this XML is
            documented at in EventManifest Schema http://msdn.microsoft.com/en-us/library/aa384043(VS.85).aspx.
            This is the preferred way of generating a manifest to be embedded in the ETW stream as it is fast and
            the fact that it only includes localized entries for the current UI culture is an acceptable tradeoff.
            </summary>
            <param name="eventSourceType">The type of the event source class for which the manifest is generated</param>
            <param name="assemblyPathToIncludeInManifest">The manifest XML fragment contains the string name of the DLL name in
            which it is embedded.  This parameter specifies what name will be used</param>
            <returns>The XML data string</returns>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.GenerateManifest(System.Type,System.String,Microsoft.Diagnostics.Tracing.EventManifestOptions)">
            <summary>
            Returns a string of the XML manifest associated with the eventSourceType. The scheme for this XML is
            documented at in EventManifest Schema http://msdn.microsoft.com/en-us/library/aa384043(VS.85).aspx.
            Pass EventManifestOptions.AllCultures when generating a manifest to be registered on the machine. This
            ensures that the entries in the event log will be "optimally" localized.
            </summary>
            <param name="eventSourceType">The type of the event source class for which the manifest is generated</param>
            <param name="assemblyPathToIncludeInManifest">The manifest XML fragment contains the string name of the DLL name in
            which it is embedded.  This parameter specifies what name will be used</param>
            <param name="flags">The flags to customize manifest generation. If flags has bit OnlyIfNeededForRegistration specified
            this returns null when the eventSourceType does not require explicit registration</param>
            <returns>The XML data string or null</returns>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.GetSources">
            <summary>
            returns a list (IEnumerable) of all sources in the appdomain).  EventListeners typically need this.  
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.SendCommand(Microsoft.Diagnostics.Tracing.EventSource,Microsoft.Diagnostics.Tracing.EventCommand,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Send a command to a particular EventSource identified by 'eventSource'.
            Calling this routine simply forwards the command to the EventSource.OnEventCommand
            callback.  What the EventSource does with the command and its arguments are from 
            that point EventSource-specific.  
            </summary>
            <param name="eventSource">The instance of EventSource to send the command to</param>
            <param name="command">A positive user-defined EventCommand, or EventCommand.SendManifest</param>
            <param name="commandArguments">A set of (name-argument, value-argument) pairs associated with the command</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.SetCurrentThreadActivityId(System.Guid)">
            <summary>
            When a thread starts work that is on behalf of 'something else' (typically another 
            thread or network request) it should mark the thread as working on that other work.
            This API marks the current thread as working on activity 'activityID'. This API
            should be used when the caller knows the thread's current activity (the one being
            overwritten) has completed. Otherwise, callers should prefer the overload that
            return the oldActivityThatWillContinue (below).
            
            All events created with the EventSource on this thread are also tagged with the 
            activity ID of the thread. 
            
            It is common, and good practice after setting the thread to an activity to log an event
            with a 'start' opcode to indicate that precise time/thread where the new activity 
            started.
            </summary>
            <param name="activityId">A Guid that represents the new activity with which to mark 
            the current thread</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.SetCurrentThreadActivityId(System.Guid,System.Guid@)">
            <summary>
            When a thread starts work that is on behalf of 'something else' (typically another 
            thread or network request) it should mark the thread as working on that other work.
            This API marks the current thread as working on activity 'activityID'. It returns 
            whatever activity the thread was previously marked with. There is a convention that
            callers can assume that callees restore this activity mark before the callee returns. 
            To encourage this this API returns the old activity, so that it can be restored later.
            
            All events created with the EventSource on this thread are also tagged with the 
            activity ID of the thread. 
            
            It is common, and good practice after setting the thread to an activity to log an event
            with a 'start' opcode to indicate that precise time/thread where the new activity 
            started.
            </summary>
            <param name="activityId">A Guid that represents the new activity with which to mark 
            the current thread</param>
            <param name="oldActivityThatWillContinue">The Guid that represents the current activity  
            which will continue at some point in the future, on the current thread</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.GetTrait(System.String)">
            <summary>
            EventSources can have arbitrary string key-value pairs associated with them called Traits.  
            These traits are not interpreted by the EventSource but may be interpreted by EventListeners
            (e.g. like the built in ETW listener).   These traits are specififed at EventSource 
            construction time and can be retrieved by using this GetTrait API.  
            </summary>
            <param name="key">The key to look up in the set of key-value pairs passed to the EventSource constructor</param>
            <returns>The value string associated iwth key.  Will return null if there is no such key.</returns>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.ToString">
            <summary>
            Displays the name and GUID for the eventSource for debugging purposes.  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.#ctor">
            <summary>
            This is the constructor that most users will use to create their eventSource.   It takes 
            no parameters.  The ETW provider name and GUID of the EventSource are determined by the EventSource 
            custom attribute (so you can determine these things declaratively).   If the GUID for the eventSource
            is not specified in the EventSourceAttribute (recommended), it is Generated by hashing the name.
            If the ETW provider name of the EventSource is not given, the name of the EventSource class is used as
            the ETW provider name.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.#ctor(System.Boolean)">
            <summary>
            By default calling the 'WriteEvent' methods do NOT throw on errors (they silently discard the event).  
            This is because in most cases users assume logging is not 'precious' and do NOT wish to have logging failures
            crash the program. However for those applications where logging is 'precious' and if it fails the caller
            wishes to react, setting 'throwOnEventWriteErrors' will cause an exception to be thrown if WriteEvent
            fails. Note the fact that EventWrite succeeds does not necessarily mean that the event reached its destination
            only that operation of writing it did not fail. These EventSources will not generate self-describing ETW events.
            
            For compatibility only use the EventSourceSettings.ThrowOnEventWriteErrors flag instead.  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.#ctor(Microsoft.Diagnostics.Tracing.EventSourceSettings)">
            <summary>
            Construct an EventSource with additional non-default settings (see EventSourceSettings for more)  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.#ctor(Microsoft.Diagnostics.Tracing.EventSourceSettings,System.String[])">
            <summary>
            Construct an EventSource with additional non-default settings.  
            
            Also specify a list of key-value pairs called traits (you must pass an even number of strings).   
            The first string is the key and the second is the value.   These are not interpreted by EventSource
            itself but may be interprated the listeners.  Can be fetched with GetTrait(string).   
            </summary>
            <param name="settings">See EventSourceSettings for more.</param>
            <param name="traits">A collection of key-value strings (must be an even number).</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.OnEventCommand(Microsoft.Diagnostics.Tracing.EventCommandEventArgs)">
            <summary>
            This method is called when the eventSource is updated by the controller.  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.WriteEventCore(System.Int32,System.Int32,Microsoft.Diagnostics.Tracing.EventSource.EventData*)">
            <summary>
            This routine allows you to create efficient WriteEvent helpers, however the code that you use to
            do this, while straightforward, is unsafe.
            </summary>
            <remarks>
            <code>
               protected unsafe void WriteEvent(int eventId, string arg1, long arg2)
               {
                   if (IsEnabled())
                   {
                       if (arg2 == null) arg2 = "";
                       fixed (char* string2Bytes = arg2)
                       {
                           EventSource.EventData* descrs = stackalloc EventSource.EventData[2];
                           descrs[0].DataPointer = (IntPtr)(&amp;arg1);
                           descrs[0].Size = 8;
                           descrs[1].DataPointer = (IntPtr)string2Bytes;
                           descrs[1].Size = ((arg2.Length + 1) * 2);
                           WriteEventCore(eventId, 2, descrs);
                       }
                   }
               }
            </code>
            </remarks>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.WriteEventWithRelatedActivityIdCore(System.Int32,System.Guid*,System.Int32,Microsoft.Diagnostics.Tracing.EventSource.EventData*)">
            <summary>
            This routine allows you to create efficient WriteEventWithRelatedActivityId helpers, however the code 
            that you use to do this, while straightforward, is unsafe. The only difference from
            <see cref="M:Microsoft.Diagnostics.Tracing.EventSource.WriteEventCore(System.Int32,System.Int32,Microsoft.Diagnostics.Tracing.EventSource.EventData*)"/> is that you pass the relatedActivityId from caller through to this API
            </summary>
            <remarks>
            <code>
               protected unsafe void WriteEventWithRelatedActivityId(int eventId, Guid relatedActivityId, string arg1, long arg2)
               {
                   if (IsEnabled())
                   {
                       if (arg2 == null) arg2 = "";
                       fixed (char* string2Bytes = arg2)
                       {
                           EventSource.EventData* descrs = stackalloc EventSource.EventData[2];
                           descrs[0].DataPointer = (IntPtr)(&amp;arg1);
                           descrs[0].Size = 8;
                           descrs[1].DataPointer = (IntPtr)string2Bytes;
                           descrs[1].Size = ((arg2.Length + 1) * 2);
                           WriteEventWithRelatedActivityIdCore(eventId, relatedActivityId, 2, descrs);
                       }
                   }
               }
            </code>
            </remarks>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Object[])">
            <summary>
            This is the varargs helper for writing an event. It does create an array and box all the arguments so it is
            relatively inefficient and should only be used for relatively rare events (e.g. less than 100 / sec). If your
            rates are faster than that you should use <see cref="M:Microsoft.Diagnostics.Tracing.EventSource.WriteEventCore(System.Int32,System.Int32,Microsoft.Diagnostics.Tracing.EventSource.EventData*)"/> to create fast helpers for your particular 
            method signature. Even if you use this for rare events, this call should be guarded by an <see cref="M:Microsoft.Diagnostics.Tracing.EventSource.IsEnabled"/> 
            check so that the varargs call is not made when the EventSource is not active.  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.WriteEventWithRelatedActivityId(System.Int32,System.Guid,System.Object[])">
            <summary>
            This is the varargs helper for writing an event which also specifies a related activity. It is completely analogous
            to corresponding WriteEvent (they share implementation). It does create an array and box all the arguments so it is
            relatively inefficient and should only be used for relatively rare events (e.g. less than 100 / sec).  If your
            rates are faster than that you should use <see cref="M:Microsoft.Diagnostics.Tracing.EventSource.WriteEventWithRelatedActivityIdCore(System.Int32,System.Guid*,System.Int32,Microsoft.Diagnostics.Tracing.EventSource.EventData*)"/> to create fast helpers for your 
            particular method signature. Even if you use this for rare events, this call should be guarded by an <see cref="M:Microsoft.Diagnostics.Tracing.EventSource.IsEnabled"/>
            check so that the varargs call is not made when the EventSource is not active.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.Dispose">
            <summary>
            Disposes of an EventSource.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.Dispose(System.Boolean)">
            <summary>
            Disposes of an EventSource.
            </summary>
            <remarks>
            Called from Dispose() with disposing=true, and from the finalizer (~EventSource) with disposing=false.
            Guidelines:
            1. We may be called more than once: do nothing after the first call.
            2. Avoid throwing exceptions if disposing is false, i.e. if we're being finalized.
            </remarks>
            <param name="disposing">True if called from Dispose(), false if called from the finalizer.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.Finalize">
            <summary>
            Finalizer for EventSource
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.Initialize(System.Guid,System.String,System.String[])">
            <summary>
            This method is responsible for the common initialization path from our constructors. It must
            not leak any exceptions (otherwise, since most EventSource classes define a static member, 
            "Log", such an exception would become a cached exception for the initialization of the static
            member, and any future access to the "Log" would throw the cached exception).
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.LogEventArgsMismatches(System.Reflection.ParameterInfo[],System.Object[])">
            <summary>
            We expect that the arguments to the Event method and the arguments to WriteEvent match. This function 
            checks that they in fact match and logs a warning to the debugger if they don't.
            </summary>
            <param name="infos"></param>
            <param name="args"></param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.WriteStringToAllListeners(System.String,System.String)">
            <summary>
            Since this is a means of reporting errors (see ReportoutOfBandMessage) any failure encountered 
            while writing the message to any one of the listeners will be silently ignored.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.IsEnabledByDefault(System.Int32,System.Boolean,Microsoft.Diagnostics.Tracing.EventLevel,Microsoft.Diagnostics.Tracing.EventKeywords)">
            <summary>
            Returns true if 'eventNum' is enabled if you only consider the level and matchAnyKeyword filters.
            It is possible that eventSources turn off the event based on additional filtering criteria.  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.DoCommand(Microsoft.Diagnostics.Tracing.EventCommandEventArgs)">
            <summary>
            We want the eventSource to be fully initialized when we do commands because that way we can send 
            error messages and other logging directly to the event stream.   Unfortunately we can get callbacks
            when we are not fully initialized.  In that case we store them in 'commandArgs' and do them later. 
            This helper actually does all actual command logic. 
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.EnableEventForDispatcher(Microsoft.Diagnostics.Tracing.EventDispatcher,System.Int32,System.Boolean)">
            <summary>
            If 'value is 'true' then set the eventSource so that 'dispatcher' will receive event with the eventId
            of 'eventId.  If value is 'false' disable the event for that dispatcher.   If 'eventId' is out of
            range return false, otherwise true.  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.AnyEventEnabled">
            <summary>
            Returns true if any event at all is on.  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.AttributeTypeNamesMatch(System.Type,System.Type)">
            <summary>
            Evaluates if two related "EventSource"-domain types should be considered the same
            </summary>
            <param name="attributeType">The attribute type in the load context - it's associated with the running 
            EventSource type. This type may be different fromt he base type of the user-defined EventSource.</param>
            <param name="reflectedAttributeType">The attribute type in the reflection context - it's associated with
            the user-defined EventSource, and is in the same assembly as the eventSourceType passed to 
            </param>
            <returns>True - if the types should be considered equivalent, False - otherwise</returns>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.GetHelperCallFirstArg(System.Reflection.MethodInfo)">
            <summary>
            This method looks at the IL and tries to pattern match against the standard
            'boilerplate' event body 
            <code>
                { if (Enabled()) WriteEvent(#, ...) } 
            </code>
            If the pattern matches, it returns the literal number passed as the first parameter to
            the WriteEvent.  This is used to find common user errors (mismatching this
            number with the EventAttribute ID).  It is only used for validation.   
            </summary>
            <param name="method">The method to probe.</param>
            <returns>The literal value or -1 if the value could not be determined. </returns>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.ReportOutOfBandMessage(System.String,System.Boolean)">
            <summary>
            Sends an error message to the debugger (outputDebugString), as well as the EventListeners 
            It will do this even if the EventSource is not enabled.  
            TODO remove flush parameter it is not used.  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.#ctor(System.String)">
            <summary>
            Construct an EventSource with a given name for non-contract based events (e.g. those using the Write() API).
            </summary>
            <param name="eventSourceName">
            The name of the event source. Must not be null.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.#ctor(System.String,Microsoft.Diagnostics.Tracing.EventSourceSettings)">
            <summary>
            Construct an EventSource with a given name for non-contract based events (e.g. those using the Write() API).
            </summary>
            <param name="eventSourceName">
            The name of the event source. Must not be null.
            </param>
            <param name="config">
            Configuration options for the EventSource as a whole. 
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.#ctor(System.String,Microsoft.Diagnostics.Tracing.EventSourceSettings,System.String[])">
            <summary>
            Construct an EventSource with a given name for non-contract based events (e.g. those using the Write() API).
            
            Also specify a list of key-value pairs called traits (you must pass an even number of strings).   
            The first string is the key and the second is the value.   These are not interpreted by EventSource
            itself but may be interprated the listeners.  Can be fetched with GetTrait(string).   
            </summary>
            <param name="eventSourceName">
            The name of the event source. Must not be null.
            </param>
            <param name="config">
            Configuration options for the EventSource as a whole. 
            </param>
            <param name="traits">A collection of key-value strings (must be an even number).</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.Write(System.String)">
            <summary>
            Writes an event with no fields and default options.
            (Native API: EventWriteTransfer)
            </summary>
            <param name="eventName">The name of the event. Must not be null.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.Write(System.String,Microsoft.Diagnostics.Tracing.EventSourceOptions)">
            <summary>
            Writes an event with no fields.
            (Native API: EventWriteTransfer)
            </summary>
            <param name="eventName">The name of the event. Must not be null.</param>
            <param name="options">
            Options for the event, such as the level, keywords, and opcode. Unset
            options will be set to default values.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.Write``1(System.String,``0)">
            <summary>
            Writes an event.
            (Native API: EventWriteTransfer)
            </summary>
            <typeparam name="T">
            The type that defines the event and its payload. This must be an
            anonymous type or a type with an [EventData] attribute.
            </typeparam>
            <param name="eventName">
            The name for the event. If null, the event name is automatically
            determined based on T, either from the Name property of T's EventData
            attribute or from typeof(T).Name.
            </param>
            <param name="data">
            The object containing the event payload data. The type T must be
            an anonymous type or a type with an [EventData] attribute. The
            public instance properties of data will be written recursively to
            create the fields of the event.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.Write``1(System.String,Microsoft.Diagnostics.Tracing.EventSourceOptions,``0)">
            <summary>
            Writes an event.
            (Native API: EventWriteTransfer)
            </summary>
            <typeparam name="T">
            The type that defines the event and its payload. This must be an
            anonymous type or a type with an [EventData] attribute.
            </typeparam>
            <param name="eventName">
            The name for the event. If null, the event name is automatically
            determined based on T, either from the Name property of T's EventData
            attribute or from typeof(T).Name.
            </param>
            <param name="options">
            Options for the event, such as the level, keywords, and opcode. Unset
            options will be set to default values.
            </param>
            <param name="data">
            The object containing the event payload data. The type T must be
            an anonymous type or a type with an [EventData] attribute. The
            public instance properties of data will be written recursively to
            create the fields of the event.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.Write``1(System.String,Microsoft.Diagnostics.Tracing.EventSourceOptions@,``0@)">
            <summary>
            Writes an event.
            This overload is for use with extension methods that wish to efficiently
            forward the options or data parameter without performing an extra copy.
            (Native API: EventWriteTransfer)
            </summary>
            <typeparam name="T">
            The type that defines the event and its payload. This must be an
            anonymous type or a type with an [EventData] attribute.
            </typeparam>
            <param name="eventName">
            The name for the event. If null, the event name is automatically
            determined based on T, either from the Name property of T's EventData
            attribute or from typeof(T).Name.
            </param>
            <param name="options">
            Options for the event, such as the level, keywords, and opcode. Unset
            options will be set to default values.
            </param>
            <param name="data">
            The object containing the event payload data. The type T must be
            an anonymous type or a type with an [EventData] attribute. The
            public instance properties of data will be written recursively to
            create the fields of the event.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.Write``1(System.String,Microsoft.Diagnostics.Tracing.EventSourceOptions@,System.Guid@,System.Guid@,``0@)">
            <summary>
            Writes an event.
            This overload is meant for clients that need to manipuate the activityId
            and related ActivityId for the event.  
            </summary>
            <typeparam name="T">
            The type that defines the event and its payload. This must be an
            anonymous type or a type with an [EventData] attribute.
            </typeparam>
            <param name="eventName">
            The name for the event. If null, the event name is automatically
            determined based on T, either from the Name property of T's EventData
            attribute or from typeof(T).Name.
            </param>
            <param name="options">
            Options for the event, such as the level, keywords, and opcode. Unset
            options will be set to default values.
            </param>
            <param name="activityId">
            The GUID of the activity associated with this event.
            </param>
            <param name="relatedActivityId">
            The GUID of another activity that is related to this activity, or Guid.Empty
            if there is no related activity. Most commonly, the Start operation of a
            new activity specifies a parent activity as its related activity.
            </param>
            <param name="data">
            The object containing the event payload data. The type T must be
            an anonymous type or a type with an [EventData] attribute. The
            public instance properties of data will be written recursively to
            create the fields of the event.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.WriteMultiMerge(System.String,Microsoft.Diagnostics.Tracing.EventSourceOptions@,Microsoft.Diagnostics.Tracing.TraceLoggingEventTypes,System.Guid*,System.Guid*,System.Object[])">
            <summary>
            Writes an extended event, where the values of the event are the
            combined properties of any number of values. This method is
            intended for use in advanced logging scenarios that support a
            dynamic set of event context providers.
            This method does a quick check on whether this event is enabled.
            </summary>
            <param name="eventName">
            The name for the event. If null, the name from eventTypes is used.
            (Note that providing the event name via the name parameter is slightly
            less efficient than using the name from eventTypes.)
            </param>
            <param name="options">
            Optional overrides for the event, such as the level, keyword, opcode,
            activityId, and relatedActivityId. Any settings not specified by options
            are obtained from eventTypes.
            </param>
            <param name="eventTypes">
            Information about the event and the types of the values in the event.
            Must not be null. Note that the eventTypes object should be created once and
            saved. It should not be recreated for each event.
            </param>
            <param name="activityID">
            A pointer to the activity ID GUID to log 
            </param>
            <param name="childActivityID">
            A pointer to the child activity ID to log (can be null) </param>
            <param name="values">
            The values to include in the event. Must not be null. The number and types of
            the values must match the number and types of the fields described by the
            eventTypes parameter.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.WriteMultiMergeInner(System.String,Microsoft.Diagnostics.Tracing.EventSourceOptions@,Microsoft.Diagnostics.Tracing.TraceLoggingEventTypes,System.Guid*,System.Guid*,System.Object[])">
            <summary>
            Writes an extended event, where the values of the event are the
            combined properties of any number of values. This method is
            intended for use in advanced logging scenarios that support a
            dynamic set of event context providers.
            Attention: This API does not check whether the event is enabled or not. 
            Please use WriteMultiMerge to avoid spending CPU cycles for events that are 
            not enabled.
            </summary>
            <param name="eventName">
            The name for the event. If null, the name from eventTypes is used.
            (Note that providing the event name via the name parameter is slightly
            less efficient than using the name from eventTypes.)
            </param>
            <param name="options">
            Optional overrides for the event, such as the level, keyword, opcode,
            activityId, and relatedActivityId. Any settings not specified by options
            are obtained from eventTypes.
            </param>
            <param name="eventTypes">
            Information about the event and the types of the values in the event.
            Must not be null. Note that the eventTypes object should be created once and
            saved. It should not be recreated for each event.
            </param>
            <param name="activityID">
            A pointer to the activity ID GUID to log 
            </param>
            <param name="childActivityID">
            A pointer to the child activity ID to log (can be null)
            </param>
            <param name="values">
            The values to include in the event. Must not be null. The number and types of
            the values must match the number and types of the fields described by the
            eventTypes parameter.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.WriteMultiMerge(System.String,Microsoft.Diagnostics.Tracing.EventSourceOptions@,Microsoft.Diagnostics.Tracing.TraceLoggingEventTypes,System.Guid*,System.Guid*,Microsoft.Diagnostics.Tracing.EventSource.EventData*)">
            <summary>
            Writes an extended event, where the values of the event have already
            been serialized in "data".
            </summary>
            <param name="eventName">
            The name for the event. If null, the name from eventTypes is used.
            (Note that providing the event name via the name parameter is slightly
            less efficient than using the name from eventTypes.)
            </param>
            <param name="options">
            Optional overrides for the event, such as the level, keyword, opcode,
            activityId, and relatedActivityId. Any settings not specified by options
            are obtained from eventTypes.
            </param>
            <param name="eventTypes">
            Information about the event and the types of the values in the event.
            Must not be null. Note that the eventTypes object should be created once and
            saved. It should not be recreated for each event.
            </param>
            <param name="activityID">
            A pointer to the activity ID GUID to log 
            </param>
            <param name="childActivityID">
            A pointer to the child activity ID to log (can be null)
            </param> 
            <param name="data">
            The previously serialized values to include in the event. Must not be null.
            The number and types of the values must match the number and types of the 
            fields described by the eventTypes parameter.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.HexDigit(System.Char)">
            <summary>
            Returns a value 0-15 if 'c' is a hexadecimal digit.   If  it throws an argument exception.  
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSource.Name">
            <summary>
            The human-friendly name of the eventSource.  It defaults to the simple name of the class
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSource.Guid">
            <summary>
            Every eventSource is assigned a GUID to uniquely identify it to the system. 
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSource.Settings">
            <summary>
            Returns the settings for the event source instance
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSource.CurrentThreadActivityId">
            <summary>
            Retrieves the ETW activity ID associated with the current thread.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSource.ConstructionException">
            <summary>
            Because
            
                1) Logging is often optional and thus should not generate fatal errors (exceptions)
                2) EventSources are often initialized in class constructors (which propagate exceptions poorly)
                
            The event source constructor does not throw exceptions.  Instead we remember any exception that 
            was generated (it is also logged to Trace.WriteLine).
            </summary>
        </member>
        <member name="E:Microsoft.Diagnostics.Tracing.EventSource.EventCommandExecuted">
            <summary>
            Fires when a Command (e.g. Enable) comes from a an EventListener.  
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventSource.EventData">
            <summary>
            Used to construct the data structure to be passed to the native ETW APIs - EventWrite and EventWriteTransfer.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.EventData.SetMetadata(System.Byte*,System.Int32,System.Int32)">
            <summary>
            Initializes the members of this EventData object to point at a previously-pinned
            tracelogging-compatible metadata blob.
            </summary>
            <param name="pointer">Pinned tracelogging-compatible metadata blob.</param>
            <param name="size">The size of the metadata blob.</param>
            <param name="reserved">Value for reserved: 2 for per-provider metadata, 1 for per-event metadata</param>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSource.EventData.DataPointer">
            <summary>
            Address where the one argument lives (if this points to managed memory you must ensure the
            managed object is pinned.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSource.EventData.Size">
            <summary>
            Size of the argument referenced by DataPointer
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventSource.Sha1ForNonSecretPurposes">
            <summary>
            Implements the SHA1 hashing algorithm. Note that this
            implementation is for hashing public information. Do not
            use this code to hash private data, as this implementation does
            not take any steps to avoid information disclosure.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.Sha1ForNonSecretPurposes.Start">
            <summary>
            Call Start() to initialize the hash object.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.Sha1ForNonSecretPurposes.Append(System.Byte)">
            <summary>
            Adds an input byte to the hash.
            </summary>
            <param name="input">Data to include in the hash.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.Sha1ForNonSecretPurposes.Append(System.Byte[])">
            <summary>
            Adds input bytes to the hash.
            </summary>
            <param name="input">
            Data to include in the hash. Must not be null.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.Sha1ForNonSecretPurposes.Finish(System.Byte[])">
            <summary>
            Retrieves the hash value.
            Note that after calling this function, the hash object should
            be considered uninitialized. Subsequent calls to Append or
            Finish will produce useless results. Call Start() to
            reinitialize.
            </summary>
            <param name="output">
            Buffer to receive the hash value. Must not be null.
            Up to 20 bytes of hash will be written to the output buffer.
            If the buffer is smaller than 20 bytes, the remaining hash
            bytes will be lost. If the buffer is larger than 20 bytes, the
            rest of the buffer is left unmodified.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSource.Sha1ForNonSecretPurposes.Drain">
            <summary>
            Called when this.pos reaches 64.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventSource.OverideEventProvider">
            <summary>
            This class lets us hook the 'OnEventCommand' from the eventSource.  
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventProvider">
            <summary>
            Only here because System.Diagnostics.EventProvider needs one more extensibility hook (when it gets a 
            controller callback)
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventProvider.Register(System.Guid)">
            <summary>
            This method registers the controlGuid of this class with ETW. We need to be running on
            Vista or above. If not a PlatformNotSupported exception will be thrown. If for some 
            reason the ETW Register call failed a NotSupported exception will be thrown. 
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventProvider.Close">
            <summary>
            This method deregisters the controlGuid of this class with ETW.
            
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventProvider.Deregister">
            <summary>
            This method un-registers from ETW.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventProvider.GetSessions">
             <summary>
             Determines the ETW sessions that have been added and/or removed to the set of
             sessions interested in the current provider. It does so by (1) enumerating over all
             ETW sessions that enabled 'this.m_Guid' for the current process ID, and (2)
             comparing the current list with a list it cached on the previous invocation.
            
             The return value is a list of tuples, where the SessionInfo specifies the
             ETW session that was added or remove, and the bool specifies whether the
             session was added or whether it was removed from the set.
             </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventProvider.GetSessionInfoCallback(System.Int32,System.Int64,System.Collections.Generic.List{Microsoft.Diagnostics.Tracing.EventProvider.SessionInfo}@)">
            <summary>
            This method is the callback used by GetSessions() when it calls into GetSessionInfo(). 
            It updates a List{SessionInfo} based on the etwSessionId and matchAllKeywords that 
            GetSessionInfo() passes in.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventProvider.GetSessionInfo(System.Action{System.Int32,System.Int64})">
            <summary>
            This method enumerates over all active ETW sessions that have enabled 'this.m_Guid' 
            for the current process ID, calling 'action' for each session, and passing it the
            ETW session and the 'AllKeywords' the session enabled for the current provider.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventProvider.IndexOfSessionInList(System.Collections.Generic.List{Microsoft.Diagnostics.Tracing.EventProvider.SessionInfo},System.Int32)">
            <summary>
            Returns the index of the SesisonInfo from 'sessions' that has the specified 'etwSessionId'
            or -1 if the value is not present.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventProvider.GetDataFromController(System.Int32,Microsoft.Win32.UnsafeNativeMethods.ManifestEtw.EVENT_FILTER_DESCRIPTOR*,Microsoft.Diagnostics.Tracing.ControllerCommand@,System.Byte[]@,System.Int32@)">
            <summary>
            Gets any data to be passed from the controller to the provider.  It starts with what is passed
            into the callback, but unfortunately this data is only present for when the provider is active
            at the time the controller issues the command.  To allow for providers to activate after the
            controller issued a command, we also check the registry and use that to get the data.  The function
            returns an array of bytes representing the data, the index into that byte array where the data
            starts, and the command being issued associated with that data.  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventProvider.IsEnabled">
            <summary>
            IsEnabled, method used to test if provider is enabled
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventProvider.IsEnabled(System.Byte,System.Int64)">
            <summary>
            IsEnabled, method used to test if event is enabled
            </summary>
            <param name="level">
            Level  to test
            </param>
            <param name="keywords">
            Keyword  to test
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventProvider.WriteEvent(Microsoft.Diagnostics.Tracing.EventDescriptor@,System.Guid*,System.Guid*,System.Object[])">
            <summary>
            WriteEvent, method to write a parameters with event schema properties
            </summary>
            <param name="eventDescriptor">
            Event Descriptor for this event. 
            </param>
            <param name="activityID">
            A pointer to the activity ID GUID to log 
            </param>
            <param name="childActivityID">
            childActivityID is marked as 'related' to the current activity ID. 
            </param>
            <param name="eventPayload">
            Payload for the ETW event. 
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventProvider.WriteEvent(Microsoft.Diagnostics.Tracing.EventDescriptor@,System.Guid*,System.Guid*,System.Int32,System.IntPtr)">
            <summary>
            WriteEvent, method to be used by generated code on a derived class
            </summary>
            <param name="eventDescriptor">
            Event Descriptor for this event. 
            </param>
            <param name="activityID">
            A pointer to the activity ID to log 
            </param>
            <param name="childActivityID">
            If this event is generating a child activity (WriteEventTransfer related activity) this is child activity
            This can be null for events that do not generate a child activity.  
            </param>
            <param name="dataCount">
            number of event descriptors 
            </param>
            <param name="data">
            pointer  do the event data
            </param>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventProvider.SessionInfo">
            <summary>
            A struct characterizing ETW sessions (identified by the etwSessionId) as
            activity-tracing-aware or legacy. A session that's activity-tracing-aware
            has specified one non-zero bit in the reserved range 44-47 in the 
            'allKeywords' value it passed in for a specific EventProvider.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventSource.EventMetadata">
            <summary>
            Used to hold all the static information about an event.  This includes everything in the event
            descriptor as well as some stuff we added specifically for EventSource. see the
            code:m_eventData for where we use this.  
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventActivityOptions">
            <summary>
            EventActivityOptions flags allow to specify different activity related characteristics.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventActivityOptions.None">
            <summary>
            No special options are added to the event.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventActivityOptions.Disable">
            <summary>
            Disable Implicit Activity Tracking
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventActivityOptions.Recursive">
            <summary>
            Allow activity event to call itself (directly or indirectly)
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventActivityOptions.Detachable">
            <summary>
            Allows event activity to live beyond its parent.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventCounter">
            <summary>
            Provides the ability to collect statistics through EventSource
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventCounter.#ctor(System.String,Microsoft.Diagnostics.Tracing.EventSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Diagnostics.Tracing.EventCounter"/> class.
            </summary>
            <param name="name">The name.</param>
            <param name="eventSource">The event source.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventCounter.WriteMetric(System.Single)">
            <summary>
            Writes the metric.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.TraceLoggingTypeInfo`1">
            <summary>
            TraceLogging: used when implementing a custom TraceLoggingTypeInfo.
            Implementations of this type provide the behaviors that TraceLogging
            uses to turn objects into event data. TraceLogging provides default
            implementations of this type, but custom implementations can be used
            when the default TraceLogging implementation is insufficient.
            </summary>
            <typeparam name="DataType">
            The type of object that is handled by this implementation.
            </typeparam>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.TraceLoggingTypeInfo">
            <summary>
            TraceLogging: used when implementing a custom TraceLoggingTypeInfo.
            Non-generic base class for TraceLoggingTypeInfo&lt;DataType>. Do not derive
            from this class. Instead, derive from TraceLoggingTypeInfo&lt;DataType>.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingTypeInfo.WriteMetadata(Microsoft.Diagnostics.Tracing.TraceLoggingMetadataCollector,System.String,Microsoft.Diagnostics.Tracing.EventFieldFormat)">
            <summary>
            When overridden by a derived class, writes the metadata (schema) for
            this type. Note that the sequence of operations in WriteMetadata should be
            essentially identical to the sequence of operations in
            WriteData/WriteObjectData. Otherwise, the metadata and data will not match,
            which may cause trouble when decoding the event.
            </summary>
            <param name="collector">
            The object that collects metadata for this object's type. Metadata is written
            by calling methods on the collector object. Note that if the type contains
            sub-objects, the implementation of this method may need to call the
            WriteMetadata method for the type of the sub-object, e.g. by calling
            TraceLoggingTypeInfo&lt;SubType&gt;.Instance.WriteMetadata(...).
            </param>
            <param name="name">
            The name of the property that contains an object of this type, or null if this
            object is being written as a top-level object of an event. Typical usage
            is to pass this value to collector.AddGroup.
            </param>
            <param name="format">
            The format attribute for the field that contains an object of this type.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingTypeInfo.WriteObjectData(Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector,System.Object)">
            <summary>
            Refer to TraceLoggingTypeInfo.WriteObjectData for information about this
            method.
            </summary>
            <param name="collector">
            Refer to TraceLoggingTypeInfo.WriteObjectData for information about this
            method.
            </param>
            <param name="value">
            Refer to TraceLoggingTypeInfo.WriteObjectData for information about this
            method.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingTypeInfo.GetData(System.Object)">
            <summary>
            Fetches the event parameter data for internal serialization. 
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.TraceLoggingTypeInfo.Name">
            <summary>
            Gets the name to use for the event if this type is the top-level type,
            or the name to use for an implicitly-named field.
            Never null.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.TraceLoggingTypeInfo.Level">
            <summary>
            Gets the event level associated with this type. Any value in the range 0..255
            is an associated event level. Any value outside the range 0..255 is invalid and
            indicates that this type has no associated event level.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.TraceLoggingTypeInfo.Opcode">
            <summary>
            Gets the event opcode associated with this type. Any value in the range 0..255
            is an associated event opcode. Any value outside the range 0..255 is invalid and
            indicates that this type has no associated event opcode.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.TraceLoggingTypeInfo.Keywords">
            <summary>
            Gets the keyword(s) associated with this type.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.TraceLoggingTypeInfo.Tags">
            <summary>
            Gets the event tags associated with this type.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingTypeInfo`1.#ctor">
            <summary>
            Initializes a new instance of the TraceLoggingTypeInfo class with
            default settings. Uses typeof(DataType).Name for EventName and FieldName.
            Marks Level and Opcode as unset. Sets Keywords and Traits to 0.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingTypeInfo`1.#ctor(System.String,Microsoft.Diagnostics.Tracing.EventLevel,Microsoft.Diagnostics.Tracing.EventOpcode,Microsoft.Diagnostics.Tracing.EventKeywords,Microsoft.Diagnostics.Tracing.EventTags)">
            <summary>
            Initializes a new instance of the TraceLoggingTypeInfo class, using
            the specified values for the EventName, Level, Opcode, Keywords,
            FieldName, and Traits properties.
            </summary>
            <param name="name">
            The value for the Name property. Must not contain '\0' characters.
            Must not be null.
            </param>
            <param name="level">
            The value for the Level property, or -1 to mark Level as unset.
            </param>
            <param name="opcode">
            The value for the Opcode property, or -1 to mark Opcode as unset.
            </param>
            <param name="keywords">
            The value for the Keywords property.
            </param>
            <param name="tags">
            The value for the Tags property.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingTypeInfo`1.WriteData(Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector,`0@)">
            <summary>
            When overridden by a derived class, writes the data (fields) for an instance
            of DataType. Note that the sequence of operations in WriteData should be
            essentially identical to the sequence of operations in WriteMetadata. Otherwise,
            the metadata and data will not match, which may cause trouble when decoding the
            event.
            </summary>
            <param name="collector">
            The object that collects the data for the instance. Data is written by calling
            methods on the collector object. Note that if the type contains sub-objects,
            the implementation of this method may need to call the WriteData method
            for the sub-object, e.g. by calling
            TraceLoggingTypeInfo&lt;SubType&gt;.Instance.WriteData(...).
            </param>
            <param name="value">
            The value for which data is to be written.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingTypeInfo`1.WriteObjectData(Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector,System.Object)">
            <summary>
            When overridden in a derived class, writes the data (fields) for an instance
            of DataType. The default implementation of WriteObjectData calls
            WriteData(collector, (DataType)value). Normally, you will override WriteData
            and not WriteObjectData. However, if your implementation of WriteData has to
            cast the value to object, it may be more efficient to reverse this calling
            pattern, i.e. to implement WriteObjectData, and then implement WriteData as a
            call to WriteObjectData.
            </summary>
            <param name="collector">
            The object that collects the data for the instance. Data is written by calling
            methods on the collector object. Note that if the type contains sub-objects,
            the implementation of this method may need to call the WriteData method
            for the sub-object, e.g. by calling
            TraceLoggingTypeInfo&lt;SubType&gt;.Instance.WriteData(...).
            </param>
            <param name="value">
            The value for which data is to be written. Note that this value may be null
            (even for value types) if the property from which the value was read is
            missing or null.
            </param>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.TraceLoggingTypeInfo`1.Instance">
            <summary>
            Gets the type info that will be used for handling instances of
            DataType. If the instance has not already been set, this will
            call TrySetInstance(automaticSerializer) to set one, where
            automaticSerializer is the value returned from CreateDefault(),
            or a do-nothing serializer if CreateDefault() fails.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.ConcurrentSet`2">
            <summary>
            TraceLogging: A very simple lock-free add-only dictionary.
            Warning: this is a copy-by-value type. Copying performs a snapshot.
            Accessing a readonly field always makes a copy of the field, so the
            GetOrAdd method will not work as expected if called on a readonly field.
            </summary>
            <typeparam name="KeyType">
            The type of the key, used for TryGet.
            </typeparam>
            <typeparam name="ItemType">
            The type of the item, used for GetOrAdd.
            </typeparam>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.ConcurrentSetItem`2">
            <summary>
            TraceLogging: Abstract base class that must be inherited by items in a
            ConcurrentSet.
            </summary>
            <typeparam name="KeyType">Type of the set's key.</typeparam>
            <typeparam name="ItemType">Type of the derived class.</typeparam>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.DataCollector">
            <summary>
            TraceLogging: This is the implementation of the DataCollector
            functionality. To enable safe access to the DataCollector from
            untrusted code, there is one thread-local instance of this structure
            per thread. The instance must be Enabled before any data is written to
            it. The instance must be Finished before the data is passed to
            EventWrite. The instance must be Disabled before the arrays referenced
            by the pointers are freed or unpinned.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.DataCollector.Finish">
            <summary>
            Completes the list of scalars. Finish must be called before the data
            descriptor array is passed to EventWrite.
            </summary>
            <returns>
            A pointer to the next unused data descriptor, or datasEnd if they were
            all used. (Descriptors may be unused if a string or array was null.)
            </returns>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.DataCollector.BeginBufferedArray">
            <summary>
            Marks the start of a non-blittable array or enumerable.
            </summary>
            <returns>Bookmark to be passed to EndBufferedArray.</returns>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.DataCollector.EndBufferedArray(System.Int32,System.Int32)">
            <summary>
            Marks the end of a non-blittable array or enumerable.
            </summary>
            <param name="bookmark">The value returned by BeginBufferedArray.</param>
            <param name="count">The number of items in the array.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.DataCollector.BeginBuffered">
            <summary>
            Marks the start of dynamically-buffered data.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.DataCollector.EndBuffered">
            <summary>
            Marks the end of dynamically-buffered data.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EmptyStruct">
            <summary>
            TraceLogging: Empty struct indicating no payload data.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EnumHelper`1">
            <summary>
            Provides support for casting enums to their underlying type
            from within generic context.
            </summary>
            <typeparam name="UnderlyingType">
            The underlying type of the enum.
            </typeparam>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventPayload">
            <summary>
            EventPayload class holds the list of parameters and their corresponding values for user defined types passed to 
            EventSource APIs.
            Preserving the order of the elements as they were found inside user defined types is the most important characteristic of this class.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.SimpleEventTypes`1">
            <summary>
            TraceLogging: Contains the metadata needed to emit an event, optimized
            for events with one top-level compile-time-typed payload object.
            </summary>
            <typeparam name="T">
            Type of the top-level payload object. Should be EmptyStruct if the
            event has no payload.
            </typeparam>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.TraceLoggingEventTypes">
            <summary>
            TraceLogging: Used when calling EventSource.WriteMultiMerge.
            Stores the type information to use when writing the event fields.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingEventTypes.#ctor(System.String,Microsoft.Diagnostics.Tracing.EventTags,System.Type[])">
            <summary>
            Initializes a new instance of TraceLoggingEventTypes corresponding
            to the name, flags, and types provided. Always uses the default
            TypeInfo for each Type.
            </summary>
            <param name="name">
            The name to use when the name parameter passed to
            EventSource.Write is null. This value must not be null.
            </param>
            <param name="tags">
            Tags to add to the event if the tags are not set via options.
            </param>
            <param name="types">
            The types of the fields in the event. This value must not be null.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingEventTypes.#ctor(System.String,Microsoft.Diagnostics.Tracing.EventTags,Microsoft.Diagnostics.Tracing.TraceLoggingTypeInfo[])">
            <summary>
            Returns a new instance of TraceLoggingEventInfo corresponding to the name,
            flags, and typeInfos provided.
            </summary>
            <param name="name">
            The name to use when the name parameter passed to
            EventSource.Write is null. This value must not be null.
            </param>
            <param name="tags">
            Tags to add to the event if the tags are not set via options.
            </param>
            <param name="typeInfos">
            The types of the fields in the event. This value must not be null.
            </param>
            <returns>
            An instance of TraceLoggingEventInfo with DefaultName set to the specified name
            and with the specified typeInfos.
            </returns>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.TraceLoggingEventTypes.Name">
            <summary>
            Gets the default name that will be used for events with this descriptor.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.TraceLoggingEventTypes.Level">
            <summary>
            Gets the default level that will be used for events with this descriptor.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.TraceLoggingEventTypes.Opcode">
            <summary>
            Gets the default opcode that will be used for events with this descriptor.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.TraceLoggingEventTypes.Keywords">
            <summary>
            Gets the default set of keywords that will added to events with this descriptor.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.TraceLoggingEventTypes.Tags">
            <summary>
            Gets the default tags that will be added events with this descriptor.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventSourceSettings">
            <summary>
            Enables specifying event source configuration options to be used in the EventSource constructor.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventSourceSettings.Default">
            <summary>
            This specifies none of the special configuration options should be enabled.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventSourceSettings.ThrowOnEventWriteErrors">
            <summary>
            Normally an EventSource NEVER throws; setting this option will tell it to throw when it encounters errors.  
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventSourceSettings.EtwManifestEventFormat">
            <summary>
            Setting this option is a directive to the ETW listener should use manifest-based format when
            firing events. This is the default option when defining a type derived from EventSource 
            (using the protected EventSource constructors).
            Only one of EtwManifestEventFormat or EtwSelfDescribingEventFormat should be specified
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventSourceSettings.EtwSelfDescribingEventFormat">
            <summary>
            Setting this option is a directive to the ETW listener should use self-describing event format
            when firing events. This is the default option when creating a new instance of the EventSource
            type (using the public EventSource constructors).  
            Only one of EtwManifestEventFormat or EtwSelfDescribingEventFormat should be specified
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventListener">
            <summary>
            An EventListener represents a target for the events generated by EventSources (that is subclasses
            of <see cref="T:Microsoft.Diagnostics.Tracing.EventSource"/>), in the current appdomain. When a new EventListener is created
            it is logically attached to all eventSources in that appdomain. When the EventListener is Disposed, then
            it is disconnected from the event eventSources. Note that there is a internal list of STRONG references
            to EventListeners, which means that relying on the lack of references to EventListeners to clean up
            EventListeners will NOT work. You must call EventListener.Dispose explicitly when a dispatcher is no
            longer needed.
            <para>
            Once created, EventListeners can enable or disable on a per-eventSource basis using verbosity levels
            (<see cref="T:Microsoft.Diagnostics.Tracing.EventLevel"/>) and bitfields (<see cref="T:Microsoft.Diagnostics.Tracing.EventKeywords"/>) to further restrict the set of 
            events to be sent to the dispatcher. The dispatcher can also send arbitrary commands to a particular 
            eventSource using the 'SendCommand' method. The meaning of the commands are eventSource specific.
            </para><para>
            The Null Guid (that is (new Guid()) has special meaning as a wildcard for 'all current eventSources in
            the appdomain'. Thus it is relatively easy to turn on all events in the appdomain if desired.
            </para><para>
            It is possible for there to be many EventListener's defined in a single appdomain. Each dispatcher is
            logically independent of the other listeners. Thus when one dispatcher enables or disables events, it
            affects only that dispatcher (other listeners get the events they asked for). It is possible that
            commands sent with 'SendCommand' would do a semantic operation that would affect the other listeners
            (like doing a GC, or flushing data ...), but this is the exception rather than the rule.
            </para><para>
            Thus the model is that each EventSource keeps a list of EventListeners that it is sending events
            to. Associated with each EventSource-dispatcher pair is a set of filtering criteria that determine for
            that eventSource what events that dispatcher will receive.
            </para><para>
            Listeners receive the events on their 'OnEventWritten' method. Thus subclasses of EventListener must
            override this method to do something useful with the data.
            </para><para>
            In addition, when new eventSources are created, the 'OnEventSourceCreate' method is called. The
            invariant associated with this callback is that every eventSource gets exactly one
            'OnEventSourceCreate' call for ever eventSource that can potentially send it log messages. In
            particular when a EventListener is created, typically a series of OnEventSourceCreate' calls are
            made to notify the new dispatcher of all the eventSources that existed before the EventListener was
            created.
            </para>
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventListener.#ctor">
            <summary>
            Create a new EventListener in which all events start off turned off (use EnableEvents to turn
            them on).  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventListener.Dispose">
            <summary>
            Dispose should be called when the EventListener no longer desires 'OnEvent*' callbacks. Because
            there is an internal list of strong references to all EventListeners, calling 'Dispose' directly
            is the only way to actually make the listen die. Thus it is important that users of EventListener
            call Dispose when they are done with their logging.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventListener.EnableEvents(Microsoft.Diagnostics.Tracing.EventSource,Microsoft.Diagnostics.Tracing.EventLevel)">
             <summary>
             Enable all events from the eventSource identified by 'eventSource' to the current 
             dispatcher that have a verbosity level of 'level' or lower.
               
             This call can have the effect of REDUCING the number of events sent to the 
             dispatcher if 'level' indicates a less verbose level than was previously enabled.
             
             This call never has an effect on other EventListeners.
            
             </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventListener.EnableEvents(Microsoft.Diagnostics.Tracing.EventSource,Microsoft.Diagnostics.Tracing.EventLevel,Microsoft.Diagnostics.Tracing.EventKeywords)">
            <summary>
            Enable all events from the eventSource identified by 'eventSource' to the current
            dispatcher that have a verbosity level of 'level' or lower and have a event keyword
            matching any of the bits in 'matchAnyKeyword'.
            
            This call can have the effect of REDUCING the number of events sent to the 
            dispatcher if 'level' indicates a less verbose level than was previously enabled or
            if 'matchAnyKeyword' has fewer keywords set than where previously set.
            
            This call never has an effect on other EventListeners.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventListener.EnableEvents(Microsoft.Diagnostics.Tracing.EventSource,Microsoft.Diagnostics.Tracing.EventLevel,Microsoft.Diagnostics.Tracing.EventKeywords,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Enable all events from the eventSource identified by 'eventSource' to the current
            dispatcher that have a verbosity level of 'level' or lower and have a event keyword
            matching any of the bits in 'matchAnyKeyword' as well as any (eventSource specific)
            effect passing additional 'key-value' arguments 'arguments' might have.  
            
            This call can have the effect of REDUCING the number of events sent to the 
            dispatcher if 'level' indicates a less verbose level than was previously enabled or
            if 'matchAnyKeyword' has fewer keywords set than where previously set.
            
            This call never has an effect on other EventListeners.
            </summary>       
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventListener.DisableEvents(Microsoft.Diagnostics.Tracing.EventSource)">
            <summary>
            Disables all events coming from eventSource identified by 'eventSource'.  
            
            This call never has an effect on other EventListeners.      
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventListener.EventSourceIndex(Microsoft.Diagnostics.Tracing.EventSource)">
            <summary>
            EventSourceIndex is small non-negative integer (suitable for indexing in an array)
            identifying EventSource. It is unique per-appdomain. Some EventListeners might find
            it useful to store additional information about each eventSource connected to it,
            and EventSourceIndex allows this extra information to be efficiently stored in a
            (growable) array (eg List(T)).
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventListener.OnEventSourceCreated(Microsoft.Diagnostics.Tracing.EventSource)">
            <summary>
            This method is called whenever a new eventSource is 'attached' to the dispatcher.
            This can happen for all existing EventSources when the EventListener is created
            as well as for any EventSources that come into existence after the EventListener
            has been created.
            
            These 'catch up' events are called during the construction of the EventListener.
            Subclasses need to be prepared for that.
            
            In a multi-threaded environment, it is possible that 'OnEventWritten' callbacks
            for a particular eventSource to occur BEFORE the OnEventSourceCreated is issued.
            </summary>
            <param name="eventSource"></param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventListener.OnEventWritten(Microsoft.Diagnostics.Tracing.EventWrittenEventArgs)">
            <summary>
            This method is called whenever an event has been written by a EventSource for which 
            the EventListener has enabled events.  
            </summary>
            <param name="eventData"></param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventListener.AddEventSource(Microsoft.Diagnostics.Tracing.EventSource)">
            <summary>
            This routine adds newEventSource to the global list of eventSources, it also assigns the
            ID to the eventSource (which is simply the ordinal in the global list).
            
            EventSources currently do not pro-actively remove themselves from this list. Instead
            when eventSources's are GCed, the weak handle in this list naturally gets nulled, and
            we will reuse the slot. Today this list never shrinks (but we do reuse entries
            that are in the list). This seems OK since the expectation is that EventSources
            tend to live for the lifetime of the appdomain anyway (they tend to be used in
            global variables).
            </summary>
            <param name="newEventSource"></param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventListener.RemoveReferencesToListenerInEventSources(Microsoft.Diagnostics.Tracing.EventListener)">
            <summary>
            Helper used in code:Dispose that removes any references to 'listenerToRemove' in any of the
            eventSources in the appdomain.  
            
            The EventListenersLock must be held before calling this routine. 
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventListener.Validate">
            <summary>
            Checks internal consistency of EventSources/Listeners. 
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventListener.s_Listeners">
            <summary>
            The list of all listeners in the appdomain.  Listeners must be explicitly disposed to remove themselves 
            from this list.   Note that EventSources point to their listener but NOT the reverse.  
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventListener.s_EventSources">
            <summary>
            The list of all active eventSources in the appdomain.  Note that eventSources do NOT 
            remove themselves from this list this is a weak list and the GC that removes them may
            not have happened yet.  Thus it can contain event sources that are dead (thus you have 
            to filter those out.  
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventListener.s_CreatingListener">
            <summary>
            Used to disallow reentrancy.  
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventListener.s_EventSourceShutdownRegistered">
            <summary>
            Used to register AD/Process shutdown callbacks.
            </summary>
        </member>
        <member name="E:Microsoft.Diagnostics.Tracing.EventListener.EventSourceCreated">
            <summary>
            This event is raised whenever a new eventSource is 'attached' to the dispatcher.
            This can happen for all existing EventSources when the EventListener is created
            as well as for any EventSources that come into existence after the EventListener
            has been created.
            
            These 'catch up' events are called during the construction of the EventListener.
            Subclasses need to be prepared for that.
            
            In a multi-threaded environment, it is possible that 'EventSourceEventWrittenCallback' 
            events for a particular eventSource to occur BEFORE the EventSourceCreatedCallback is issued.
            </summary>
        </member>
        <member name="E:Microsoft.Diagnostics.Tracing.EventListener.EventWritten">
            <summary>
            This event is raised whenever an event has been written by a EventSource for which 
            the EventListener has enabled events.  
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventListener.EventListenersLock">
            <summary>
            Gets a global lock that is intended to protect the code:s_Listeners linked list and the
            code:s_EventSources WeakReference list.  (We happen to use the s_EventSources list as
            the lock object)
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventCommandEventArgs">
            <summary>
            Passed to the code:EventSource.OnEventCommand callback
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventCommandEventArgs.EnableEvent(System.Int32)">
            <summary>
            Enables the event that has the specified identifier.
            </summary>
            <param name="eventId">Event ID of event to be enabled</param>
            <returns>true if eventId is in range</returns>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventCommandEventArgs.DisableEvent(System.Int32)">
            <summary>
            Disables the event that have the specified identifier.
            </summary>
            <param name="eventId">Event ID of event to be disabled</param>
            <returns>true if eventId is in range</returns>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventCommandEventArgs.Command">
            <summary>
            Gets the command for the callback.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventCommandEventArgs.Arguments">
            <summary>
            Gets the arguments for the callback.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventSourceCreatedEventArgs">
            <summary>
            EventSourceCreatedEventArgs is passed to <see cref="E:Microsoft.Diagnostics.Tracing.EventListener.EventSourceCreated"/>
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSourceCreatedEventArgs.EventSource">
            <summary>
            The EventSource that is attaching to the listener.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventWrittenEventArgs">
            <summary>
            EventWrittenEventArgs is passed to the user-provided override for
            <see cref="M:Microsoft.Diagnostics.Tracing.EventListener.OnEventWritten(Microsoft.Diagnostics.Tracing.EventWrittenEventArgs)"/> when an event is fired.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventWrittenEventArgs.EventName">
            <summary>
            The name of the event.   
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventWrittenEventArgs.EventId">
            <summary>
            Gets the event ID for the event that was written.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventWrittenEventArgs.ActivityId">
            <summary>
            Gets the activity ID for the thread on which the event was written.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventWrittenEventArgs.RelatedActivityId">
            <summary>
            Gets the related activity ID if one was specified when the event was written.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventWrittenEventArgs.Payload">
            <summary>
            Gets the payload for the event.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventWrittenEventArgs.PayloadNames">
            <summary>
            Gets the payload argument names.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventWrittenEventArgs.EventSource">
            <summary>
            Gets the event source object.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventWrittenEventArgs.Keywords">
            <summary>
            Gets the keywords for the event.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventWrittenEventArgs.Opcode">
            <summary>
            Gets the operation code for the event.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventWrittenEventArgs.Task">
            <summary>
            Gets the task for the event.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventWrittenEventArgs.Tags">
            <summary>
            Any provider/user defined options associated with the event.  
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventWrittenEventArgs.Message">
            <summary>
            Gets the message for the event.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventWrittenEventArgs.Channel">
            <summary>
            Gets the channel for the event.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventWrittenEventArgs.Version">
            <summary>
            Gets the version of the event.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventWrittenEventArgs.Level">
            <summary>
            Gets the level for the event.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventSourceAttribute">
            <summary>
            Allows customizing defaults and specifying localization support for the event source class to which it is applied. 
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSourceAttribute.Name">
            <summary>
            Overrides the ETW name of the event source (which defaults to the class name)
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSourceAttribute.Guid">
            <summary>
            Overrides the default (calculated) Guid of an EventSource type. Explicitly defining a GUID is discouraged, 
            except when upgrading existing ETW providers to using event sources.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSourceAttribute.LocalizationResources">
            <summary>
            <para>
            EventSources support localization of events. The names used for events, opcodes, tasks, keywords and maps 
            can be localized to several languages if desired. This works by creating a ResX style string table 
            (by simply adding a 'Resource File' to your project). This resource file is given a name e.g. 
            'DefaultNameSpace.ResourceFileName' which can be passed to the ResourceManager constructor to read the 
            resources. This name is the value of the LocalizationResources property. 
            </para><para>
            If LocalizationResources property is non-null, then EventSource will look up the localized strings for events by 
            using the following resource naming scheme
            </para>
                <para>* event_EVENTNAME</para>
                <para>* task_TASKNAME</para>
                <para>* keyword_KEYWORDNAME</para>
                <para>* map_MAPNAME</para>
            <para>
            where the capitalized name is the name of the event, task, keyword, or map value that should be localized.   
            Note that the localized string for an event corresponds to the Message string, and can have {0} values 
            which represent the payload values.  
            </para>
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventAttribute">
            <summary>
            Any instance methods in a class that subclasses <see cref="T:Microsoft.Diagnostics.Tracing.EventSource"/> and that return void are
            assumed by default to be methods that generate an ETW event. Enough information can be deduced from the
            name of the method and its signature to generate basic schema information for the event. The
            <see cref="T:Microsoft.Diagnostics.Tracing.EventAttribute"/> class allows you to specify additional event schema information for an event if
            desired.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventAttribute.#ctor(System.Int32)">
            <summary>Construct an EventAttribute with specified eventId</summary>
            <param name="eventId">ID of the ETW event (an integer between 1 and 65535)</param>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventAttribute.EventId">
            <summary>Event's ID</summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventAttribute.Level">
            <summary>Event's severity level: indicates the severity or verbosity of the event</summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventAttribute.Keywords">
            <summary>Event's keywords: allows classification of events by "categories"</summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventAttribute.Opcode">
            <summary>Event's operation code: allows defining operations, generally used with Tasks</summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventAttribute.Task">
            <summary>Event's task: allows logical grouping of events</summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventAttribute.Channel">
            <summary>Event's channel: defines an event log as an additional destination for the event</summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventAttribute.Version">
            <summary>Event's version</summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventAttribute.Message">
            <summary>
            This can be specified to enable formatting and localization of the event's payload. You can 
            use standard .NET substitution operators (eg {1}) in the string and they will be replaced 
            with the 'ToString()' of the corresponding part of the  event payload.   
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventAttribute.Tags">
            <summary>
            User defined options associated with the event.  These do not have meaning to the EventSource but
            are passed through to listeners which given them semantics.  
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventAttribute.ActivityOptions">
            <summary>
            Allows fine control over the Activity IDs generated by start and stop events
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.NonEventAttribute">
            <summary>
            By default all instance methods in a class that subclasses code:EventSource that and return
            void are assumed to be methods that generate an event. This default can be overridden by specifying
            the code:NonEventAttribute
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.NonEventAttribute.#ctor">
            <summary>
            Constructs a default NonEventAttribute
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventChannelAttribute">
            <summary>
            EventChannelAttribute allows customizing channels supported by an EventSource. This attribute must be
            applied to an member of type EventChannel defined in a Channels class nested in the EventSource class:
            <code>
                public static class Channels
                {
                    [Channel(Enabled = true, EventChannelType = EventChannelType.Admin)]
                    public const EventChannel Admin = (EventChannel)16;
                
                    [Channel(Enabled = false, EventChannelType = EventChannelType.Operational)]
                    public const EventChannel Operational = (EventChannel)17;
                }
            </code>
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventChannelAttribute.Enabled">
            <summary>
            Specified whether the channel is enabled by default
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventChannelAttribute.EventChannelType">
            <summary>
            Legal values are in EventChannelType
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventChannelType">
            <summary>
            Allowed channel types
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventChannelType.Admin">
            <summary>The admin channel</summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventChannelType.Operational">
            <summary>The operational channel</summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventChannelType.Analytic">
            <summary>The Analytic channel</summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventChannelType.Debug">
            <summary>The debug channel</summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventCommand">
            <summary>
            Describes the pre-defined command (EventCommandEventArgs.Command property) that is passed to the OnEventCommand callback.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventCommand.Update">
            <summary>
            Update EventSource state
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventCommand.SendManifest">
            <summary>
            Request EventSource to generate and send its manifest
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventCommand.Enable">
            <summary>
            Enable event
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventCommand.Disable">
            <summary>
            Disable event
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.SessionMask">
            <summary>
            A SessionMask represents a set of (at most MAX) sessions as a bit mask. The perEventSourceSessionId
            is the index in the SessionMask of the bit that will be set. These can translate to
            EventSource's reserved keywords bits using the provided ToEventKeywords() and
            FromEventKeywords() methods.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventDispatcher">
            <summary>
            code:EventDispatchers are a simple 'helper' structure that holds the filtering state
            (m_EventEnabled) for a particular EventSource X EventListener tuple
            
            Thus a single EventListener may have many EventDispatchers (one for every EventSource 
            that that EventListener has activate) and a Single EventSource may also have many
            event Dispatchers (one for every EventListener that has activated it). 
            
            Logically a particular EventDispatcher belongs to exactly one EventSource and exactly  
            one EventListener (alhtough EventDispatcher does not 'remember' the EventSource it is
            associated with. 
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventManifestOptions">
            <summary>
            Flags that can be used with EventSource.GenerateManifest to control how the ETW manifest for the EventSource is
            generated.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventManifestOptions.None">
            <summary>
            Only the resources associated with current UI culture are included in the  manifest
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventManifestOptions.Strict">
            <summary>
            Throw exceptions for any inconsistency encountered
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventManifestOptions.AllCultures">
            <summary>
            Generate a "resources" node under "localization" for every satellite assembly provided
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventManifestOptions.OnlyIfNeededForRegistration">
            <summary>
            Generate the manifest only if the event source needs to be registered on the machine,
            otherwise return null (but still perform validation if Strict is specified)
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventManifestOptions.AllowEventSourceOverride">
            <summary>
            When generating the manifest do *not* enforce the rule that the current EventSource class
            must be the base class for the user-defined type passed in. This allows validation of .net
            event sources using the new validation code
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.ManifestBuilder">
            <summary>
            ManifestBuilder is designed to isolate the details of the message of the event from the
            rest of EventSource.  This one happens to create XML. 
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.ManifestBuilder.#ctor(System.String,System.Guid,System.String,System.Resources.ResourceManager,Microsoft.Diagnostics.Tracing.EventManifestOptions)">
            <summary>
            Build a manifest for 'providerName' with the given GUID, which will be packaged into 'dllName'.
            'resources, is a resource manager.  If specified all messages are localized using that manager.  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.ManifestBuilder.AddChannel(System.String,System.Int32,Microsoft.Diagnostics.Tracing.EventChannelAttribute)">
            <summary>
            Add a channel.  channelAttribute can be null
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.ManifestBuilder.ManifestError(System.String,System.Boolean)">
            <summary>
            When validating an event source it adds the error to the error collection.
            When not validating it throws an exception if runtimeCritical is "true".
            Otherwise the error is ignored.
            </summary>
            <param name="msg"></param>
            <param name="runtimeCritical"></param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.ManifestBuilder.GetSupportedCultures(System.Resources.ResourceManager)">
            <summary>
            There's no API to enumerate all languages an assembly is localized into, so instead
            we enumerate through all the "known" cultures and attempt to load a corresponding satellite 
            assembly
            </summary>
            <param name="resources"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.ManifestEnvelope">
            <summary>
            Used to send the m_rawManifest into the event dispatcher as a series of events.  
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventSourceException">
            <summary>
            Exception that is thrown when an error occurs during EventSource operation.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceException.#ctor">
            <summary>
            Initializes a new instance of the EventSourceException class.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the EventSourceException class with a specified error message.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the EventSourceException class with a specified error message 
            and a reference to the inner exception that is the cause of this exception.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the EventSourceException class with serialized data.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.FieldMetadata">
            <summary>
            TraceLogging: Contains the information needed to generate tracelogging
            metadata for an event field.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.FieldMetadata.name">
            <summary>
            Name of the field
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.FieldMetadata.nameSize">
            <summary>
            The number of bytes in the UTF8 Encoding of 'name' INCLUDING a null terminator.  
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.FieldMetadata.fixedCount">
            <summary>
            ETW supports fixed sized arrays. If inType has the InTypeFixedCountFlag then this is the
            statically known count for the array. It is also used to encode the number of bytes of
            custom meta-data if InTypeCustomCountFlag set.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.FieldMetadata.#ctor(System.String,Microsoft.Diagnostics.Tracing.TraceLoggingDataType,Microsoft.Diagnostics.Tracing.EventFieldTags,System.Boolean)">
            <summary>
            Scalar or variable-length array.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.FieldMetadata.#ctor(System.String,Microsoft.Diagnostics.Tracing.TraceLoggingDataType,Microsoft.Diagnostics.Tracing.EventFieldTags,System.UInt16)">
            <summary>
            Fixed-length array.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.FieldMetadata.#ctor(System.String,Microsoft.Diagnostics.Tracing.TraceLoggingDataType,Microsoft.Diagnostics.Tracing.EventFieldTags,System.Byte[])">
            <summary>
            Custom serializer
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.FieldMetadata.Encode(System.Int32@,System.Byte[])">
            <summary>
            This is the main routine for FieldMetaData.  Basically it will serialize the data in
            this structure as TraceLogging style meta-data into the array 'metaArray' starting at
            'pos' (pos is updated to reflect the bytes written).  
            
            Note that 'metaData' can be null, in which case it only updates 'pos'.  This is useful
            for a 'two pass' approach where you figure out how big to make the array, and then you
            fill it in.   
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.InvokeTypeInfo`1">
            <summary>
            TraceLogging: An implementation of TraceLoggingTypeInfo that works
            for arbitrary types. It writes all public instance properties of
            the type. Implemented using Delegate.CreateDelegate(property.Getter).
            </summary>
            <typeparam name="ContainerType">
            Type from which to read values.
            </typeparam>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.NameInfo">
            <summary>
            TraceLogging: Stores the metadata and event identifier corresponding
            to a tracelogging event type+name+tags combination.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.NameInfo.ReserveEventIDsBelow(System.Int32)">
            <summary>
            Insure that eventIds strictly less than 'eventId' will not be
            used by the SelfDescribing events.   
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.PropertyAccessor`1">
            <summary>
            TraceLogging: Each PropertyAccessor instance encapsulates the information
            needed to read a particular property from an instance of ContainerType
            and write the value to a DataCollector. Used by InvokeTypeInfo.
            </summary>
            <typeparam name="ContainerType">
            The type of the object from which properties are read.
            </typeparam>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.NonGenericProperytWriter`1">
            <summary>
            The type specific version of the property writers uses generics in a way 
            that Project N can't handle at the moment.   To avoid this we simply 
            use reflection completely.  
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.StructPropertyWriter`2">
            <summary>
            Implementation of PropertyAccessor for use when ContainerType is a
            value type.
            </summary>
            <typeparam name="ContainerType">The type of the object from which properties are read.</typeparam>
            <typeparam name="ValueType">Type of the property being read.</typeparam>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.ClassPropertyWriter`2">
            <summary>
            Implementation of PropertyAccessor for use when ContainerType is a
            reference type.
            </summary>
            <typeparam name="ContainerType">The type of the object from which properties are read.</typeparam>
            <typeparam name="ValueType">Type of the property being read.</typeparam>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.PropertyAnalysis">
            <summary>
            TraceLogging: stores the per-property information obtained by
            reflecting over a type.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.NullTypeInfo`1">
            <summary>
            TraceLogging: Type handler for empty or unsupported types.
            </summary>
            <typeparam name="DataType">The type to handle.</typeparam>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.BooleanTypeInfo">
            <summary>
            TraceLogging: Type handler for Boolean.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.ByteTypeInfo">
            <summary>
            TraceLogging: Type handler for Byte.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.SByteTypeInfo">
            <summary>
            TraceLogging: Type handler for SByte.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.Int16TypeInfo">
            <summary>
            TraceLogging: Type handler for Int16.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.UInt16TypeInfo">
            <summary>
            TraceLogging: Type handler for UInt16.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.Int32TypeInfo">
            <summary>
            TraceLogging: Type handler for Int32.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.UInt32TypeInfo">
            <summary>
            TraceLogging: Type handler for UInt32.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.Int64TypeInfo">
            <summary>
            TraceLogging: Type handler for Int64.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.UInt64TypeInfo">
            <summary>
            TraceLogging: Type handler for UInt64.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.IntPtrTypeInfo">
            <summary>
            TraceLogging: Type handler for IntPtr.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.UIntPtrTypeInfo">
            <summary>
            TraceLogging: Type handler for UIntPtr.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.DoubleTypeInfo">
            <summary>
            TraceLogging: Type handler for Double.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.SingleTypeInfo">
            <summary>
            TraceLogging: Type handler for Single.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.CharTypeInfo">
            <summary>
            TraceLogging: Type handler for Char.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.BooleanArrayTypeInfo">
            <summary>
            TraceLogging: Type handler for Boolean[].
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.ByteArrayTypeInfo">
            <summary>
            TraceLogging: Type handler for Byte[].
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.SByteArrayTypeInfo">
            <summary>
            TraceLogging: Type handler for SByte[].
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.Int16ArrayTypeInfo">
            <summary>
            TraceLogging: Type handler for Int16[].
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.UInt16ArrayTypeInfo">
            <summary>
            TraceLogging: Type handler for UInt16[].
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.Int32ArrayTypeInfo">
            <summary>
            TraceLogging: Type handler for Int32[].
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.UInt32ArrayTypeInfo">
            <summary>
            TraceLogging: Type handler for UInt32[].
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.Int64ArrayTypeInfo">
            <summary>
            TraceLogging: Type handler for Int64[].
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.UInt64ArrayTypeInfo">
            <summary>
            TraceLogging: Type handler for UInt64[].
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.IntPtrArrayTypeInfo">
            <summary>
            TraceLogging: Type handler for IntPtr[].
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.UIntPtrArrayTypeInfo">
            <summary>
            TraceLogging: Type handler for UIntPtr[].
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.CharArrayTypeInfo">
            <summary>
            TraceLogging: Type handler for Char[].
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.DoubleArrayTypeInfo">
            <summary>
            TraceLogging: Type handler for Double[].
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.SingleArrayTypeInfo">
            <summary>
            TraceLogging: Type handler for Single[].
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.StringTypeInfo">
            <summary>
            TraceLogging: Type handler for String.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.GuidTypeInfo">
            <summary>
            TraceLogging: Type handler for Guid.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.GuidArrayTypeInfo">
            <summary>
            TraceLogging: Type handler for Guid[].
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.DateTimeTypeInfo">
            <summary>
            TraceLogging: Type handler for DateTime.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.DateTimeOffsetTypeInfo">
            <summary>
            TraceLogging: Type handler for DateTimeOffset.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.TimeSpanTypeInfo">
            <summary>
            TraceLogging: Type handler for TimeSpan.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.DecimalTypeInfo">
            <summary>
            TraceLogging: Type handler for Decimal. (Note: not full-fidelity, exposed as Double.)
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.KeyValuePairTypeInfo`2">
            <summary>
            TraceLogging: Type handler for KeyValuePair.
            </summary>
            <typeparam name="K">Type of the KeyValuePair's Key property.</typeparam>
            <typeparam name="V">Type of the KeyValuePair's Value property.</typeparam>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.NullableTypeInfo`1">
            <summary>
            TraceLogging: Type handler for Nullable.
            </summary>
            <typeparam name="T">Type of the Nullable's Value property.</typeparam>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.Statics">
            <summary>
            TraceLogging: Constants and utility functions.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.Statics.MetadataForString(System.String,System.Int32,System.Int32,System.Int32)">
            <summary>
            A complete metadata chunk can be expressed as:
            length16 + prefix + null-terminated-utf8-name + suffix + additionalData.
            We assume that excludedData will be provided by some other means,
            but that its size is known. This function returns a blob containing
            length16 + prefix + name + suffix, with prefix and suffix initialized
            to 0's. The length16 value is initialized to the length of the returned
            blob plus additionalSize, so that the concatenation of the returned blob
            plus a blob of size additionalSize constitutes a valid metadata blob.
            </summary>
            <param name="name">
            The name to include in the blob.
            </param>
            <param name="prefixSize">
            Amount of space to reserve before name. For provider or field blobs, this
            should be 0. For event blobs, this is used for the tags field and will vary
            from 1 to 4, depending on how large the tags field needs to be.
            </param>
            <param name="suffixSize">
            Amount of space to reserve after name. For example, a provider blob with no
            traits would reserve 0 extra bytes, but a provider blob with a single GroupId
            field would reserve 19 extra bytes.
            </param>
            <param name="additionalSize">
            Amount of additional data in another blob. This value will be counted in the
            blob's length field, but will not be included in the returned byte[] object.
            The complete blob would then be the concatenation of the returned byte[] object
            with another byte[] object of length additionalSize.
            </param>
            <returns>
            A byte[] object with the length and name fields set, with room reserved for
            prefix and suffix. If additionalSize was 0, the byte[] object is a complete
            blob. Otherwise, another byte[] of size additionalSize must be concatenated
            with this one to form a complete blob.
            </returns>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.Statics.EncodeTags(System.Int32,System.Int32@,System.Byte[])">
            <summary>
            Serialize the low 28 bits of the tags value into the metadata stream,
            starting at the index given by pos. Updates pos. Writes 1 to 4 bytes,
            depending on the value of the tags variable. Usable for event tags and
            field tags.
            
            Note that 'metadata' can be null, in which case it only updates 'pos'.
            This is useful for a two pass approach where you figure out how big to
            make the array, and then you fill it in.   
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.Statics.Format8(Microsoft.Diagnostics.Tracing.EventFieldFormat,Microsoft.Diagnostics.Tracing.TraceLoggingDataType)">
            <summary>
            Adjusts the native type based on format.
            - If format is default, return native.
            - If format is recognized, return the canonical type for that format.
            - Otherwise remove existing format from native and apply the requested format.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.Statics.Format16(Microsoft.Diagnostics.Tracing.EventFieldFormat,Microsoft.Diagnostics.Tracing.TraceLoggingDataType)">
            <summary>
            Adjusts the native type based on format.
            - If format is default, return native.
            - If format is recognized, return the canonical type for that format.
            - Otherwise remove existing format from native and apply the requested format.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.Statics.Format32(Microsoft.Diagnostics.Tracing.EventFieldFormat,Microsoft.Diagnostics.Tracing.TraceLoggingDataType)">
            <summary>
            Adjusts the native type based on format.
            - If format is default, return native.
            - If format is recognized, return the canonical type for that format.
            - Otherwise remove existing format from native and apply the requested format.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.Statics.Format64(Microsoft.Diagnostics.Tracing.EventFieldFormat,Microsoft.Diagnostics.Tracing.TraceLoggingDataType)">
            <summary>
            Adjusts the native type based on format.
            - If format is default, return native.
            - If format is recognized, return the canonical type for that format.
            - Otherwise remove existing format from native and apply the requested format.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.Statics.FormatPtr(Microsoft.Diagnostics.Tracing.EventFieldFormat,Microsoft.Diagnostics.Tracing.TraceLoggingDataType)">
            <summary>
            Adjusts the native type based on format.
            - If format is default, return native.
            - If format is recognized, return the canonical type for that format.
            - Otherwise remove existing format from native and apply the requested format.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventSourceActivity">
            <summary>
            Provides support for EventSource activities by marking the start and
            end of a particular operation.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceActivity.#ctor(Microsoft.Diagnostics.Tracing.EventSource)">
            <summary>
            Initializes a new instance of the EventSourceActivity class that
            is attached to the specified event source. The new activity will
            not be attached to any related (parent) activity.
            The activity is created in the Initialized state.
            </summary>
            <param name="eventSource">
            The event source to which the activity information is written.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceActivity.op_Implicit(Microsoft.Diagnostics.Tracing.EventSource)~Microsoft.Diagnostics.Tracing.EventSourceActivity">
            <summary>
            You can make an activity out of just an EventSource.  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceActivity.Start``1(System.String,Microsoft.Diagnostics.Tracing.EventSourceOptions,``0)">
            <summary>
            Writes a Start event with the specified name and data.   If the start event is not active (because the provider 
            is not on or keyword-level indiates the event is off, then the returned activity is simply the 'this' poitner 
            and it is effectively like the Start d
            
            A new activityID GUID is generated and the returned
            EventSourceActivity remembers this activity and will mark every event (including the start stop and any writes)
            with this activityID.   In addition the Start activity will log a 'relatedActivityID' that was the activity
            ID before the start event.   This way event processors can form a linked list of all the activities that
            caused this one (directly or indirectly).  
            </summary>
            <param name="eventName">
            The name to use for the event.   It is strongly suggested that this name end in 'Start' (e.g. DownloadStart).  
            If you do this, then the Stop() method will automatically replace the 'Start' suffix with a 'Stop' suffix.  
            </param>
            <param name="options">Allow options (keywords, level) to be set for the write associated with this start 
            These will also be used for the stop event.</param>
            <param name="data">The data to include in the event.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceActivity.Start(System.String)">
            <summary>
            Shortcut version see Start(string eventName, EventSourceOptions options, T data) Options is empty (no keywords 
            and level==Info) Data payload is empty.  
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceActivity.Start(System.String,Microsoft.Diagnostics.Tracing.EventSourceOptions)">
            <summary>
            Shortcut version see Start(string eventName, EventSourceOptions options, T data).  Data payload is empty. 
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceActivity.Start``1(System.String,``0)">
            <summary>
            Shortcut version see Start(string eventName, EventSourceOptions options, T data) Options is empty (no keywords 
            and level==Info) 
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceActivity.Stop``1(``0)">
            <summary>
            Writes a Stop event with the specified data, and sets the activity
            to the Stopped state.  The name is determined by the eventName used in Start.
            If that Start event name is suffixed with 'Start' that is removed, and regardless
            'Stop' is appended to the result to form the Stop event name.  
            May only be called when the activity is in the Started state.
            </summary>
            <param name="data">The data to include in the event.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceActivity.Stop``1(System.String)">
            <summary>
            Used if you wish to use the non-default stop name (which is the start name with Start replace with 'Stop')
            This can be useful to indicate unusual ways of stoping (but it is still STRONGLY recommeded that
            you start with the same prefix used for the start event and you end with the 'Stop' suffix.   
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceActivity.Stop``1(System.String,``0)">
            <summary>
            Used if you wish to use the non-default stop name (which is the start name with Start replace with 'Stop')
            This can be useful to indicate unusual ways of stoping (but it is still STRONGLY recommeded that
            you start with the same prefix used for the start event and you end with the 'Stop' suffix.   
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceActivity.Write``1(System.String,Microsoft.Diagnostics.Tracing.EventSourceOptions,``0)">
            <summary>
            Writes an event associated with this activity to the eventSource associted with this activity.  
            May only be called when the activity is in the Started state.
            </summary>
            <param name="eventName">
            The name to use for the event. If null, the name is determined from
            data's type.
            </param>
            <param name="options">
            The options to use for the event.
            </param>
            <param name="data">The data to include in the event.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceActivity.Write``1(System.String,``0)">
            <summary>
            Writes an event associated with this activity.
            May only be called when the activity is in the Started state.
            </summary>
            <param name="eventName">
            The name to use for the event. If null, the name is determined from
            data's type.
            </param>
            <param name="data">The data to include in the event.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceActivity.Write(System.String,Microsoft.Diagnostics.Tracing.EventSourceOptions)">
            <summary>
            Writes a trivial event associated with this activity.
            May only be called when the activity is in the Started state.
            </summary>
            <param name="eventName">
            The name to use for the event. Must not be null.
            </param>
            <param name="options">
            The options to use for the event.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceActivity.Write(System.String)">
            <summary>
            Writes a trivial event associated with this activity.
            May only be called when the activity is in the Started state.
            </summary>
            <param name="eventName">
            The name to use for the event. Must not be null.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceActivity.Write``1(Microsoft.Diagnostics.Tracing.EventSource,System.String,Microsoft.Diagnostics.Tracing.EventSourceOptions,``0)">
            <summary>
            Writes an event to a arbitrary eventSource stamped with the activity ID of this activity.   
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.EventSourceActivity.Dispose">
            <summary>
            Releases any unmanaged resources associated with this object.
            If the activity is in the Started state, calls Stop().
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSourceActivity.EventSource">
            <summary>
            Gets the event source to which this activity writes events.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSourceActivity.Id">
            <summary>
            Gets this activity's unique identifier, or the default Guid if the
            event source was disabled when the activity was initialized.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSourceActivity.StartEventWasFired">
            <summary>
            If eventName is non-null then we logged a start event 
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector">
            <summary>
            TraceLogging: Used when implementing a custom TraceLoggingTypeInfo.
            The instance of this type is provided to the TypeInfo.WriteData method.
            All operations are forwarded to the current thread's DataCollector.
            Note that this abstraction would allow us to expose the custom
            serialization system to partially-trusted code. If we end up not
            making custom serialization public, or if we only expose it to
            full-trust code, this abstraction is unnecessary (though it probably
            doesn't hurt anything).
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.BeginBufferedArray">
            <summary>
            Marks the start of a non-blittable array or enumerable.
            </summary>
            <returns>Bookmark to be passed to EndBufferedArray.</returns>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.EndBufferedArray(System.Int32,System.Int32)">
            <summary>
            Marks the end of a non-blittable array or enumerable.
            </summary>
            <param name="bookmark">The value returned by BeginBufferedArray.</param>
            <param name="count">The number of items in the array.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddGroup">
            <summary>
            Adds the start of a group to the event.
            This has no effect on the event payload, but is provided to allow
            WriteMetadata and WriteData implementations to have similar
            sequences of calls, allowing for easier verification of correctness.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddScalar(System.Boolean)">
            <summary>
            Adds a Boolean value to the event payload.
            </summary>
            <param name="value">Value to be added.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddScalar(System.SByte)">
            <summary>
            Adds an SByte value to the event payload.
            </summary>
            <param name="value">Value to be added.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddScalar(System.Byte)">
            <summary>
            Adds a Byte value to the event payload.
            </summary>
            <param name="value">Value to be added.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddScalar(System.Int16)">
            <summary>
            Adds an Int16 value to the event payload.
            </summary>
            <param name="value">Value to be added.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddScalar(System.UInt16)">
            <summary>
            Adds a UInt16 value to the event payload.
            </summary>
            <param name="value">Value to be added.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddScalar(System.Int32)">
            <summary>
            Adds an Int32 value to the event payload.
            </summary>
            <param name="value">Value to be added.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddScalar(System.UInt32)">
            <summary>
            Adds a UInt32 value to the event payload.
            </summary>
            <param name="value">Value to be added.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddScalar(System.Int64)">
            <summary>
            Adds an Int64 value to the event payload.
            </summary>
            <param name="value">Value to be added.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddScalar(System.UInt64)">
            <summary>
            Adds a UInt64 value to the event payload.
            </summary>
            <param name="value">Value to be added.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddScalar(System.IntPtr)">
            <summary>
            Adds an IntPtr value to the event payload.
            </summary>
            <param name="value">Value to be added.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddScalar(System.UIntPtr)">
            <summary>
            Adds a UIntPtr value to the event payload.
            </summary>
            <param name="value">Value to be added.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddScalar(System.Single)">
            <summary>
            Adds a Single value to the event payload.
            </summary>
            <param name="value">Value to be added.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddScalar(System.Double)">
            <summary>
            Adds a Double value to the event payload.
            </summary>
            <param name="value">Value to be added.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddScalar(System.Char)">
            <summary>
            Adds a Char value to the event payload.
            </summary>
            <param name="value">Value to be added.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddScalar(System.Guid)">
            <summary>
            Adds a Guid value to the event payload.
            </summary>
            <param name="value">Value to be added.</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddBinary(System.String)">
            <summary>
            Adds a counted String value to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length string.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddBinary(System.Byte[])">
            <summary>
            Adds an array of Byte values to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length array.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddArray(System.Boolean[])">
            <summary>
            Adds an array of Boolean values to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length array.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddArray(System.SByte[])">
            <summary>
            Adds an array of SByte values to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length array.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddArray(System.Int16[])">
            <summary>
            Adds an array of Int16 values to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length array.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddArray(System.UInt16[])">
            <summary>
            Adds an array of UInt16 values to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length array.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddArray(System.Int32[])">
            <summary>
            Adds an array of Int32 values to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length array.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddArray(System.UInt32[])">
            <summary>
            Adds an array of UInt32 values to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length array.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddArray(System.Int64[])">
            <summary>
            Adds an array of Int64 values to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length array.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddArray(System.UInt64[])">
            <summary>
            Adds an array of UInt64 values to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length array.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddArray(System.IntPtr[])">
            <summary>
            Adds an array of IntPtr values to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length array.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddArray(System.UIntPtr[])">
            <summary>
            Adds an array of UIntPtr values to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length array.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddArray(System.Single[])">
            <summary>
            Adds an array of Single values to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length array.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddArray(System.Double[])">
            <summary>
            Adds an array of Double values to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length array.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddArray(System.Char[])">
            <summary>
            Adds an array of Char values to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length array.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddArray(System.Guid[])">
            <summary>
            Adds an array of Guid values to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length array.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingDataCollector.AddCustom(System.Byte[])">
            <summary>
            Adds an array of Byte values to the event payload.
            </summary>
            <param name="value">
            Value to be added. A null value is treated as a zero-length array.
            </param>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.TraceLoggingDataType">
            <summary>
            TraceLogging: Used when implementing a custom TraceLoggingTypeInfo.
            These are passed to metadataCollector.Add to specify the low-level
            type of a field in the event payload. Note that a "formatted"
            TraceLoggingDataType consists of a core TraceLoggingDataType value
            (a TraceLoggingDataType with a value less than 32) plus an OutType.
            Any combination of TraceLoggingDataType + OutType is valid, but not
            all are useful. In particular, combinations not explicitly listed
            below are unlikely to be recognized by decoders, and will typically
            be decoded as the corresponding core type (i.e. the decoder will
            mask off any unrecognized OutType value).
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Nil">
            <summary>
            Core type.
            Data type with no value (0-length payload).
            NOTE: arrays of Nil are illegal.
            NOTE: a fixed-length array of Nil is interpreted by the decoder as
            a struct (obsolete but retained for backwards-compatibility).
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Utf16String">
            <summary>
            Core type.
            Encoding assumes null-terminated Char16 string.
            Decoding treats as UTF-16LE string.
            NOTE: arrays of Utf16String will not be supported until M3.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.MbcsString">
            <summary>
            Core type.
            Encoding assumes null-terminated Char8 string.
            Decoding treats as MBCS string.
            NOTE: arrays of MbcsString will not be supported until M3.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Int8">
            <summary>
            Core type.
            Encoding assumes 8-bit value.
            Decoding treats as signed integer.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.UInt8">
            <summary>
            Core type.
            Encoding assumes 8-bit value.
            Decoding treats as unsigned integer.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Int16">
            <summary>
            Core type.
            Encoding assumes 16-bit value.
            Decoding treats as signed integer.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.UInt16">
            <summary>
            Core type.
            Encoding assumes 16-bit value.
            Decoding treats as unsigned integer.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Int32">
            <summary>
            Core type.
            Encoding assumes 32-bit value.
            Decoding treats as signed integer.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.UInt32">
            <summary>
            Core type.
            Encoding assumes 32-bit value.
            Decoding treats as unsigned integer.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Int64">
            <summary>
            Core type.
            Encoding assumes 64-bit value.
            Decoding treats as signed integer.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.UInt64">
            <summary>
            Core type.
            Encoding assumes 64-bit value.
            Decoding treats as unsigned integer.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Float">
            <summary>
            Core type.
            Encoding assumes 32-bit value.
            Decoding treats as Float.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Double">
            <summary>
            Core type.
            Encoding assumes 64-bit value.
            Decoding treats as Double.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Boolean32">
            <summary>
            Core type.
            Encoding assumes 32-bit value.
            Decoding treats as Boolean.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Binary">
            <summary>
            Core type.
            Encoding assumes 16-bit bytecount followed by binary data.
            Decoding treats as binary data.
            NOTE: arrays of Binary will not be supported until M3.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Guid">
            <summary>
            Core type.
            Encoding assumes 16-byte value.
            Decoding treats as GUID.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.FileTime">
            <summary>
            Core type.
            Encoding assumes 64-bit value.
            Decoding treats as FILETIME.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.SystemTime">
            <summary>
            Core type.
            Encoding assumes 16-byte value.
            Decoding treats as SYSTEMTIME.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.HexInt32">
            <summary>
            Core type.
            Encoding assumes 32-bit value.
            Decoding treats as hexadecimal unsigned integer.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.HexInt64">
            <summary>
            Core type.
            Encoding assumes 64-bit value.
            Decoding treats as hexadecimal unsigned integer.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.CountedUtf16String">
            <summary>
            Core type.
            Encoding assumes 16-bit bytecount followed by Char16 data.
            Decoding treats as UTF-16LE string.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.CountedMbcsString">
            <summary>
            Core type.
            Encoding assumes 16-bit bytecount followed by Char8 data.
            Decoding treats as MBCS string.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Struct">
            <summary>
            Core type.
            Special case: Struct indicates that this field plus the the
            subsequent N logical fields are to be considered as one logical
            field (i.e. a nested structure). The OutType is used to encode N.
            The maximum value for N is 127. This field has no payload by
            itself, but logically contains the payload of the following N
            fields. It is legal to have an array of Struct.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Char16">
            <summary>
            Formatted type.
            Encoding assumes 16-bit value.
            Decoding treats as UTF-16LE character.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Char8">
            <summary>
            Formatted type.
            Encoding assumes 8-bit value.
            Decoding treats as character.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Boolean8">
            <summary>
            Formatted type.
            Encoding assumes 8-bit value.
            Decoding treats as Boolean.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.HexInt8">
            <summary>
            Formatted type.
            Encoding assumes 8-bit value.
            Decoding treats as hexadecimal unsigned integer.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.HexInt16">
            <summary>
            Formatted type.
            Encoding assumes 16-bit value.
            Decoding treats as hexadecimal unsigned integer.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Utf16Xml">
            <summary>
            Formatted type.
            Encoding assumes null-terminated Char16 string.
            Decoding treats as UTF-16LE XML string.
            NOTE: arrays of Utf16Xml will not be supported until M3.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.MbcsXml">
            <summary>
            Formatted type.
            Encoding assumes null-terminated Char8 string.
            Decoding treats as MBCS XML string.
            NOTE: arrays of MbcsXml will not be supported until M3.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.CountedUtf16Xml">
            <summary>
            Formatted type.
            Encoding assumes 16-bit bytecount followed by Char16 data.
            Decoding treats as UTF-16LE XML.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.CountedMbcsXml">
            <summary>
            Formatted type.
            Encoding assumes 16-bit bytecount followed by Char8 data.
            Decoding treats as MBCS XML.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.Utf16Json">
            <summary>
            Formatted type.
            Encoding assumes null-terminated Char16 string.
            Decoding treats as UTF-16LE JSON string.
            NOTE: arrays of Utf16Json will not be supported until M3.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.MbcsJson">
            <summary>
            Formatted type.
            Encoding assumes null-terminated Char8 string.
            Decoding treats as MBCS JSON string.
            NOTE: arrays of MbcsJson will not be supported until M3.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.CountedUtf16Json">
            <summary>
            Formatted type.
            Encoding assumes 16-bit bytecount followed by Char16 data.
            Decoding treats as UTF-16LE JSON.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.CountedMbcsJson">
            <summary>
            Formatted type.
            Encoding assumes 16-bit bytecount followed by Char8 data.
            Decoding treats as MBCS JSON.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.TraceLoggingDataType.HResult">
            <summary>
            Formatted type.
            Encoding assumes 32-bit value.
            Decoding treats as HRESULT.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventDataAttribute">
            <summary>
            Used when authoring types that will be passed to EventSource.Write.
            EventSource.Write&lt;T> only works when T is either an anonymous type
            or a type with an [EventData] attribute. In addition, the properties
            of T must be supported property types. Supported property types include
            simple built-in types (int, string, Guid, DateTime, DateTimeOffset,
            KeyValuePair, etc.), anonymous types that only contain supported types,
            types with an [EventData] attribute, arrays of the above, and IEnumerable
            of the above.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventDataAttribute.Name">
            <summary>
            Gets or sets the name to use if this type is used for an
            implicitly-named event or an implicitly-named property.
            
            Example 1:
            
                EventSource.Write(null, new T()); // implicitly-named event
                
            The name of the event will be determined as follows:
            
            if (T has an EventData attribute and attribute.Name != null)
                eventName = attribute.Name;
            else
                eventName = typeof(T).Name;
                
            Example 2:
            
                EventSource.Write(name, new { _1 = new T() }); // implicitly-named field
                
            The name of the field will be determined as follows:
            
            if (T has an EventData attribute and attribute.Name != null)
                fieldName = attribute.Name;
            else
                fieldName = typeof(T).Name;
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventDataAttribute.Level">
            <summary>
            Gets or sets the level to use for the event.
            Invalid levels (outside the range 0..255) are treated as unset.
            Note that the Level attribute can bubble-up, i.e. if a type contains
            a sub-object (a field or property), and the sub-object's type has a
            TraceLoggingEvent attribute, the Level from the sub-object's attribute
            can affect the event's level.
            
            Example: for EventSource.Write(name, options, data), the level of the
            event will be determined as follows:
            
            if (options.Level has been set)
                eventLevel = options.Level;
            else if (data.GetType() has a TraceLoggingEvent attribute and attribute.Level has been set)
                eventLevel = attribute.Level;
            else if (a field/property contained in data has a TraceLoggingEvent attribute and attribute.Level has been set)
                eventLevel = attribute.Level;
            else
                eventLevel = EventLevel.LogAlways;
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventDataAttribute.Opcode">
            <summary>
            Gets or sets the opcode to use for the event.
            Invalid opcodes (outside the range 0..255) are treated as unset.
            Note that the Opcode attribute can bubble-up, i.e. if a type contains
            a sub-object (a field or property), and the sub-object's type has a
            TraceLoggingEvent attribute, the Opcode from the sub-object's attribute
            can affect the event's opcode.
            
            Example: for EventSource.Write(name, options, data), the opcode of the
            event will be determined as follows:
            
            if (options.Opcode has been set)
                eventOpcode = options.Opcode;
            else if (data.GetType() has a TraceLoggingEvent attribute and attribute.Opcode has been set)
                eventOpcode = attribute.Opcode;
            else if (a field/property contained in data has a TraceLoggingEvent attribute and attribute.Opcode has been set)
                eventOpcode = attribute.Opcode;
            else
                eventOpcode = EventOpcode.Info;
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventDataAttribute.Keywords">
            <summary>
            Gets or sets the keywords to use for the event.
            Note that the Keywords attribute can bubble-up, i.e. if a type contains
            a sub-object (a field or property), and the sub-object's type has a
            TraceLoggingEvent attribute, the Keywords from the sub-object's attribute
            can affect the event's keywords.
            
            Example: for EventSource.Write(name, options, data), the keywords of the
            event will be determined as follows:
            
            eventKeywords = options.Keywords;
            if (data.GetType() has a TraceLoggingEvent attribute)
                eventKeywords |= attribute.Keywords;
            if (a field/property contained in data has a TraceLoggingEvent attribute)
                eventKeywords |= attribute.Keywords;
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventDataAttribute.Tags">
            <summary>
            Gets or sets the flags for an event. These flags are ignored by ETW,
            but can have meaning to the event consumer.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventTags">
            <summary>
            Tags are flags that are not interpreted by EventSource but are passed along
            to the EventListener. The EventListener determines the semantics of the flags.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventTags.None">
            <summary>
            No special traits are added to the event.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventFieldTags">
            <summary>
            Tags are flags that are not interpreted by EventSource but are passed along
            to the EventListener. The EventListener determines the semantics of the flags.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventFieldTags.None">
            <summary>
            No special traits are added to the field.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventFieldAttribute">
            <summary>
            TraceLogging: used when authoring types that will be passed to EventSource.Write.
            Controls how a field or property is handled when it is written as a
            field in a TraceLogging event. Apply this attribute to a field or
            property if the default handling is not correct. (Apply the
            TraceLoggingIgnore attribute if the property should not be
            included as a field in the event.)
            The default for Name is null, which means that the name of the
            underlying field or property will be used as the event field's name.
            The default for PiiTag is 0, which means that the event field does not
            contain personally-identifiable information.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventFieldAttribute.Tags">
            <summary>
            User defined options for the field. These are not interpreted by the EventSource
            but are available to the Listener. See EventFieldSettings for details
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventFieldAttribute.Name">
            <summary>
            Gets or sets the name to use for the field. This defaults to null.
            If null, the name of the corresponding property will be used
            as the event field's name.
            TODO REMOVE
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventFieldAttribute.Format">
            <summary>
            Gets or sets a field formatting hint.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventFieldFormat">
            <summary>
            Provides a hint that may be used by an event listener when formatting
            an event field for display. Note that the event listener may ignore the
            hint if it does not recognize a particular combination of type and format.
            Similar to TDH_OUTTYPE.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventFieldFormat.Default">
            <summary>
            Field receives default formatting based on the field's underlying type.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventFieldFormat.String">
            <summary>
            Field should be formatted as character or string data.
            Typically applied to 8-bit or 16-bit integers.
            This is the default format for String and Char types.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventFieldFormat.Boolean">
            <summary>
            Field should be formatted as boolean data. Typically applied to 8-bit
            or 32-bit integers. This is the default format for the Boolean type.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventFieldFormat.Hexadecimal">
            <summary>
            Field should be formatted as hexadecimal data. Typically applied to
            integer types.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventFieldFormat.Xml">
            <summary>
            Field should be formatted as XML string data. Typically applied to
            strings or arrays of 8-bit or 16-bit integers.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventFieldFormat.Json">
            <summary>
            Field should be formatted as JSON string data. Typically applied to
            strings or arrays of 8-bit or 16-bit integers.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventFieldFormat.HResult">
            <summary>
            Field should be formatted as an HRESULT code. Typically applied to
            32-bit integer types.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventIgnoreAttribute">
            <summary>
            Used when authoring types that will be passed to EventSource.Write.
            By default, EventSource.Write will write all of an object's public
            properties to the event payload. Apply [EventIgnore] to a public
            property to prevent EventSource.Write from including the property in
            the event.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.TraceLoggingMetadataCollector">
            <summary>
            TraceLogging: used when implementing a custom TraceLoggingTypeInfo.
            An instance of this type is provided to the TypeInfo.WriteMetadata method.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingMetadataCollector.#ctor">
            <summary>
            Creates a root-level collector.
            </summary>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingMetadataCollector.#ctor(Microsoft.Diagnostics.Tracing.TraceLoggingMetadataCollector,Microsoft.Diagnostics.Tracing.FieldMetadata)">
            <summary>
            Creates a collector for a group.
            </summary>
            <param name="other">Parent collector</param>
            <param name="group">The field that starts the group</param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingMetadataCollector.AddGroup(System.String)">
            <summary>
            Call this method to add a group to the event and to return
            a new metadata collector that can be used to add fields to the
            group. After all of the fields in the group have been written,
            switch back to the original metadata collector to add fields
            outside of the group.
            Special-case: if name is null, no group is created, and AddGroup
            returns the original metadata collector. This is useful when
            adding the top-level group for an event.
            Note: do not use the original metadata collector while the group's
            metadata collector is in use, and do not use the group's metadata
            collector after switching back to the original.
            </summary>
            <param name="name">
            The name of the group. If name is null, the call to AddGroup is a
            no-op (collector.AddGroup(null) returns collector).
            </param>
            <returns>
            A new metadata collector that can be used to add fields to the group.
            </returns>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingMetadataCollector.AddScalar(System.String,Microsoft.Diagnostics.Tracing.TraceLoggingDataType)">
            <summary>
            Adds a scalar field to an event.
            </summary>
            <param name="name">
            The name to use for the added field. This value must not be null.
            </param>
            <param name="type">
            The type code for the added field. This must be a fixed-size type
            (e.g. string types are not supported).
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingMetadataCollector.AddBinary(System.String,Microsoft.Diagnostics.Tracing.TraceLoggingDataType)">
            <summary>
            Adds a binary-format field to an event.
            Compatible with core types: Binary, CountedUtf16String, CountedMbcsString.
            Compatible with dataCollector methods: AddBinary(string), AddArray(Any8bitType[]).
            </summary>
            <param name="name">
            The name to use for the added field. This value must not be null.
            </param>
            <param name="type">
            The type code for the added field. This must be a Binary or CountedString type.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingMetadataCollector.AddArray(System.String,Microsoft.Diagnostics.Tracing.TraceLoggingDataType)">
            <summary>
            Adds an array field to an event.
            </summary>
            <param name="name">
            The name to use for the added field. This value must not be null.
            </param>
            <param name="type">
            The type code for the added field. This must be a fixed-size type
            or a string type. In the case of a string type, this adds an array
            of characters, not an array of strings.
            </param>
        </member>
        <member name="M:Microsoft.Diagnostics.Tracing.TraceLoggingMetadataCollector.AddCustom(System.String,Microsoft.Diagnostics.Tracing.TraceLoggingDataType,System.Byte[])">
            <summary>
            Adds a custom-serialized field to an event.
            </summary>
            <param name="name">
            The name to use for the added field. This value must not be null.
            </param>
            <param name="type">The encoding type for the field.</param>
            <param name="metadata">Additional information needed to decode the field, if any.</param>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.TraceLoggingMetadataCollector.Tags">
            <summary>
            The field tags to be used for the next field.
            This will be reset to None each time a field is written.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventSourceOptions">
            <summary>
            Used when calling EventSource.Write.
            Optional overrides for event settings such as Level, Keywords, or Opcode.
            If overrides are not provided for a setting, default values will be used.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSourceOptions.Level">
            <summary>
            Gets or sets the level to use for the specified event. If this property
            is unset, the event's level will be 5 (Verbose).
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSourceOptions.Opcode">
            <summary>
            Gets or sets the opcode to use for the specified event. If this property
            is unset, the event's opcode will 0 (Info).
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSourceOptions.Keywords">
            <summary>
            Gets or sets the keywords to use for the specified event. If this
            property is unset, the event's keywords will be 0.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSourceOptions.Tags">
            <summary>
            Gets or sets the tags to use for the specified event. If this property is
            unset, the event's tags will be 0.
            </summary>
        </member>
        <member name="P:Microsoft.Diagnostics.Tracing.EventSourceOptions.ActivityOptions">
            <summary>
            Gets or sets the activity options for this specified events. If this property is
            unset, the event's activity options will be 0.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.TypeAnalysis">
            <summary>
            TraceLogging: stores the per-type information obtained by reflecting over a type.
            </summary>
        </member>
        <member name="M:Microsoft.Win32.UnsafeNativeMethods.ManifestEtw.EventWriteTransferWrapper(System.Int64,Microsoft.Diagnostics.Tracing.EventDescriptor@,System.Guid*,System.Guid*,System.Int32,Microsoft.Diagnostics.Tracing.EventProvider.EventData*)">
            <summary>
             Call the ETW native API EventWriteTransfer and checks for invalid argument error. 
             The implementation of EventWriteTransfer on some older OSes (Windows 2008) does not accept null relatedActivityId.
             So, for these cases we will retry the call with an empty Guid.
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventLevel">
            <summary>
            WindowsEventLevel. Custom values must be in the range from 16 through 255
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventLevel.LogAlways">
            <summary>
            Log always
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventLevel.Critical">
            <summary>
            Only critical errors
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventLevel.Error">
            <summary>
            All errors, including previous levels
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventLevel.Warning">
            <summary>
            All warnings, including previous levels
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventLevel.Informational">
            <summary>
            All informational events, including previous levels
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventLevel.Verbose">
            <summary>
            All events, including previous levels 
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventTask">
            <summary>
            WindowsEventTask. Custom values must be in the range from 1 through 65534
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventTask.None">
            <summary>
            Undefined task
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventOpcode">
            <summary>
            EventOpcode. Custom values must be in the range from 11 through 239
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventOpcode.Info">
            <summary>
            An informational event
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventOpcode.Start">
            <summary>
            An activity start event
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventOpcode.Stop">
            <summary>
            An activity end event 
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventOpcode.DataCollectionStart">
            <summary>
            A trace collection start event
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventOpcode.DataCollectionStop">
            <summary>
            A trace collection end event
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventOpcode.Extension">
            <summary>
            An extensional event
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventOpcode.Reply">
            <summary>
            A reply event
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventOpcode.Resume">
            <summary>
            An event representing the activity resuming from the suspension
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventOpcode.Suspend">
            <summary>
            An event representing the activity is suspended, pending another activity's completion
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventOpcode.Send">
            <summary>
            An event representing the activity is transferred to another component, and can continue to work
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventOpcode.Receive">
            <summary>
            An event representing receiving an activity transfer from another component 
            </summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventChannel">
            <summary>
            EventChannel. Custom values must be in the range from 16 through 255. Currently only predefined values allowed.
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventChannel.None">
            <summary>
            No channel
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventChannel.Admin">
            <summary>The admin channel</summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventChannel.Operational">
            <summary>The operational channel</summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventChannel.Analytic">
            <summary>The analytic channel</summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventChannel.Debug">
            <summary>The debug channel</summary>
        </member>
        <member name="T:Microsoft.Diagnostics.Tracing.EventKeywords">
            <summary>
            EventOpcode
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventKeywords.None">
            <summary>
            No events. 
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventKeywords.All">
            <summary>
            All Events 
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventKeywords.WdiContext">
            <summary>
            WDI context events
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventKeywords.WdiDiagnostic">
            <summary>
            WDI diagnostic events
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventKeywords.Sqm">
            <summary>
            SQM events
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventKeywords.AuditFailure">
            <summary>
            Failed security audits
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventKeywords.AuditSuccess">
            <summary>
            Successful security audits
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventKeywords.CorrelationHint">
            <summary>
            Transfer events where the related Activity ID is a computed value and not a GUID
            </summary>
        </member>
        <member name="F:Microsoft.Diagnostics.Tracing.EventKeywords.EventLogClassic">
            <summary>
            Events raised using classic eventlog API
            </summary>
        </member>
    </members>
</doc>
