﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using System.ComponentModel;
namespace Model
{

    /// <summary>
    ///应用程序菜单字典
    /// </summary>

    [DataContract]
    public class SEC_RIGHT_GROUP : NotificationObject
    {

        /// <summary>
        /// 应用程序代码
        /// </summary>		
        private string _application_code;
        [DataMember]
        public string APPLICATION_CODE
        {
            get { return _application_code; }
            set
            {
                if (_application_code != value)
                {
                    _application_code = value;
                    this.RaisePropertyChanged("APPLICATION_CODE");
                }
            }
        }
        /// <summary>
        /// 权限组代码
        /// </summary>		
        private string _right_group_code;
        [DataMember]
        public string RIGHT_GROUP_CODE
        {
            get { return _right_group_code; }
            set
            {
                if (_right_group_code != value)
                {
                    _right_group_code = value;
                    this.RaisePropertyChanged("RIGHT_GROUP_CODE");
                }
            }
        }
        /// <summary>
        /// 权限组说明
        /// </summary>		
        private string _right_group_text;
        [DataMember]
        public string RIGHT_GROUP_TEXT
        {
            get { return _right_group_text; }
            set
            {
                if (_right_group_text != value)
                {
                    _right_group_text = value;
                    this.RaisePropertyChanged("RIGHT_GROUP_TEXT");
                }
            }
        }
        /// <summary>
        /// 备注	
        /// </summary>		
        private string _right_group_memos;
        [DataMember]
        public string RIGHT_GROUP_MEMOS
        {
            get { return _right_group_memos; }
            set
            {
                if (_right_group_memos != value)
                {
                    _right_group_memos = value;
                    this.RaisePropertyChanged("RIGHT_GROUP_MEMOS");
                }
            }
        }

    }
}