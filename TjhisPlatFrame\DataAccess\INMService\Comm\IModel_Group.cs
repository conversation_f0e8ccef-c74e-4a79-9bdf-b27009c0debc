﻿using System;
using System.Data;
using System.ServiceModel;
namespace INMService
{
    /// <summary>
    /// 接口层MODEL_GROUP
    /// </summary>
    [ServiceContract]
    public interface IMODEL_GROUP
    {
        #region  成员方法
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        [OperationContract]
        bool Exists_MODEL_GROUP(string MODEL_CODE, string GROUP_CODE);
        /// <summary>
        /// 增加一条数据
        /// </summary>
        [OperationContract]
        bool Add_MODEL_GROUP(Model.MODEL_GROUP model);
        /// <summary>
        /// 更新一条数据
        /// </summary>
        [OperationContract]
        bool Update_MODEL_GROUP(Model.MODEL_GROUP model);
        /// <summary>
        /// 删除数据
        /// </summary>
        [OperationContract]
        bool Delete_MODEL_GROUP(string MODEL_CODE, string GROUP_CODE);
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        [OperationContract]
        Model.MODEL_GROUP GetModel_MODEL_GROUP(string MODEL_CODE, string GROUP_CODE);
        /// <summary>
        /// 获得数据列表
        /// </summary>
        [OperationContract]
        DataSet GetList_All_MODEL_GROUP(string strWhere);
        /// <summary>
        /// 获得前几行数据
        /// </summary>
        [OperationContract]
        DataSet GetList_MODEL_GROUP(int startIndex, int endIndex, string strWhere, string filedOrder);
        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.MODEL_GROUP> GetObservableCollection_All_MODEL_GROUP(string strWhere);
        /// <summary>
        /// 获得ObservableCollection根据分页获得数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.MODEL_GROUP> GetObservableCollection_MODEL_GROUP(int startIndex, int endIndex, string strWhere, string filedOrder);
        #endregion  成员方法
    }
}