﻿/*-----------------------------------------------------------------------
 * 类名称    ：LNCA_INTERFACE
 * 类描述    ：辽宁CA接口类，保存ca基本信息和his用户对照
 * 创建人    ：梁吉lions
 * 创建时间  ：2017/5/6 13:09:58
 * 修改人    ：
 * 修改时间  ：
 * 修改备注  ：
 * 版本      ：
 * ----------------------------------------------------------------------
 */
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using System.ComponentModel;
namespace Model
{

    /// <summary>
    ///辽宁CA接口
    /// </summary>

    [DataContract]
    public class LNCA_INTERFACE : NotificationObject
    {

        /// <summary>
        /// 辽宁ca的key设备id
        /// </summary>		
        private string _lnca_id;
        [DataMember]
        public string LNCA_ID
        {
            get { return _lnca_id; }
            set
            {
                if (_lnca_id != value)
                {
                    _lnca_id = value;
                    this.RaisePropertyChanged("LNCA_ID");
                }
            }
        }
        /// <summary>
        /// 辽宁ca的key信息
        /// </summary>		
        private string _lnca_info;
        [DataMember]
        public string LNCA_INFO
        {
            get { return _lnca_info; }
            set
            {
                if (_lnca_info != value)
                {
                    _lnca_info = value;
                    this.RaisePropertyChanged("LNCA_INFO");
                }
            }
        }
        /// <summary>
        /// his的用户名
        /// </summary>		
        private string _his_id;
        [DataMember]
        public string HIS_ID
        {
            get { return _his_id; }
            set
            {
                if (_his_id != value)
                {
                    _his_id = value;
                    this.RaisePropertyChanged("HIS_ID");
                }
            }
        }
        /// <summary>
        /// key存图片储
        /// </summary>		
        private byte[] _ca_img;
        [DataMember]
        public byte[] CA_IMG
        {
            get { return _ca_img; }
            set
            {
                if (_ca_img != value)
                {
                    _ca_img = value;
                    this.RaisePropertyChanged("CA_IMG");
                }
            }
        }
        /// <summary>
        /// key序列号
        /// </summary>		
        private string _ca_sn;
        [DataMember]
        public string CA_SN
        {
            get { return _ca_sn; }
            set
            {
                if (_ca_sn != value)
                {
                    _ca_sn = value;
                    this.RaisePropertyChanged("CA_SN");
                }
            }
        }
        /// <summary>
        /// key图片字符串格式
        /// </summary>		
        private string _ca_img_str;
        [DataMember]
        public string CA_IMG_STR
        {
            get { return _ca_img_str; }
            set
            {
                if (_ca_img_str != value)
                {
                    _ca_img_str = value;
                    this.RaisePropertyChanged("CA_IMG_STR");
                }
            }
        }

    }
}