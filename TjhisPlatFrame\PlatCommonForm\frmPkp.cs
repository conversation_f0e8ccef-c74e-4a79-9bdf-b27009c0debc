﻿using DevExpress.XtraEditors;
using PlatCommon.Comm;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace PlatCommonForm
{
    public partial class frmPkp : Form
    {
        ClassArmyPkp cap = new ClassArmyPkp();
        public string is_invotype = "";
        DataTable dt = null;
        public frmPkp()
        {
            InitializeComponent();
            
            SetDesktopLocation((System.Windows.Forms.SystemInformation.WorkingArea.Width - this.Width)/2,0);
        }

        private void frmPkp_Load(object sender, EventArgs e)
        {
            //总是显示在最前端
            // TopMost = true;
            //获取发票型号列表"
           string err = "";
            //获取发票型号列表
            string ls_invotypes = cap.of_get_invotypes(ref err);
            if (string.IsNullOrEmpty(ls_invotypes))
            {
                XtraMessageBox.Show(err, "提示");
                return;
            }
            dt = new DataTable();
            dt.Columns.Add("CODE");
            int li_len = ls_invotypes.Length / 3;
            for (int i = 0; i < li_len; i++)
            {
                DataRow dr = dt.NewRow();
                dr["CODE"] =int.Parse(ls_invotypes.Substring(0, 3));
                ls_invotypes = ls_invotypes.Substring(3, ls_invotypes.Length - 3);
                dt.Rows.Add(dr);
            }

            repositoryItemLookUpEdit1.DataSource = dt;
            repositoryItemLookUpEdit1.DisplayMember = "CODE";
            repositoryItemLookUpEdit1.ValueMember = "CODE";

            wf_set_invotype(is_invotype);
            wf_refresh();
        }

        private void wf_set_invotype(string li_type)
        {
            if (string.IsNullOrEmpty(li_type))
            {
                if (dt != null)
                {
                    if (dt.Rows.Count >= 1)
                    {
                        barEditItem1.EditValue = dt.Rows[0][0].ToString();
                    }
                }
            }
            else
            {
                DataRow[] drs = dt.Select("code ='" + int.Parse(li_type).ToString() + "'");
                if (drs.Length <= 0)
                {
                    XtraMessageBox.Show("票控盘不支持选定的发票型号", "提示");

                }
                else
                {
                    barEditItem1.EditValue = int.Parse(li_type).ToString();
                }
            }
        }

        public int  wf_refresh()
        {
            string ls_rcpt_type = barEditItem1.EditValue.ToString();
            if (!ls_rcpt_type.Equals(is_invotype))
            {
                ls_rcpt_type = is_invotype;
                barEditItem1.EditValue = is_invotype;
            }
            string as_err = "";
            string ls_invono = "";
            if (string.IsNullOrEmpty(ls_rcpt_type)) return 0;
            int li_ret = cap.of_get_invono(int.Parse(ls_rcpt_type),ref ls_invono,ref as_err);
            if (li_ret < 0) return -1;
            barEditItem2.EditValue = ls_invono;
            return 0;

        }
        private void barLargeButtonItem7_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Close();
        }

        private void frmPkp_Activated(object sender, EventArgs e)
        {
            //int li_len = 60;
            ////  frmPkp.Size.Width = li_len;
            string err = "";
            string ls_type = cap.of_get_invotypes(ref err);
        }

        private void barLargeButtonItem4_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            wf_refresh();
        }

        private void barLargeButtonItem3_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                string ls_invotype = barEditItem1.EditValue.ToString();
                if (string.IsNullOrEmpty(ls_invotype))
                {
                    XtraMessageBox.Show("未选择发票类型", "提示");
                }
                if (DialogResult.Yes == XtraMessageBox.Show("作废空白电子票据，确实要作废吗？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question))
                {
                    string as_err = "";
                    int li_status = cap.of_blankoutcurinvo(int.Parse(ls_invotype), ref as_err);
                    if (li_status < 0)
                    {
                        XtraMessageBox.Show(as_err, "提示");
                    }
                    else
                    {
                        wf_refresh();
                    }
                }
            }
            catch(Exception ex)
            {
                XtraMessageBox.Show("异常"+ex.Message,"提示");
            }
        }

        private void barLargeButtonItem2_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            int li_Height = this.MaximumSize.Height;
            //if (li_Height == 30)
            //{
            //    MaximumSize.Height = 200;
            //}
        }

        private void check_cancel()
        {
            string ls_invotype = barEditItem1.EditValue.ToString();
            if (string.IsNullOrEmpty(ls_invotype))
            {
                XtraMessageBox.Show("未选择发票类型", "提示");
            }
            if (string.IsNullOrEmpty(ls_invotype)) ls_invotype = "35";
            string ls_disk_no = "", ls_err="";
            if (cap.of_get_diskno(ls_disk_no,ref ls_err) < 0)
            {
                XtraMessageBox.Show(ls_err, "提示");
                return;
            }


            //作废
          string  ls_invo_no = (sle_invoice_no2.EditValue.ToString()).Trim();
            if (string.IsNullOrEmpty(ls_invo_no))
            {
                XtraMessageBox.Show("请输入指定发票号", "提示");
                return;
            }

            string sql = "select INVOICE_STATUS  from invoice_info where INVOICE_NO ='"+ ls_invo_no + "' and RCPT_TYPE ='"+ ls_invotype + "'";
            DataTable dt_new = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
            if (dt_new.Rows.Count <= 0)
            {
                XtraMessageBox.Show("查无此票据", "提示");
                return;
            }
            Dictionary<string, string> idc = new Dictionary<string, string>();
            int li_state = cap.of_blankoutinvo(int.Parse(ls_invotype), ls_invo_no, "登记作废", ref ls_err,ref idc);
            if (li_state < 0)
            {
                XtraMessageBox.Show(ls_err, "提示");
                return;
            }

            string ls_ret = new NM_Service.NMService.ServerPublicClient().SaveTable(idc);
            if (!string.IsNullOrEmpty(ls_ret))
            {
                XtraMessageBox.Show(ls_ret, "提示1");
                return;
            }

        }
    }
}
