﻿using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.Data;
using Tjhis.Report.Custom.Common;

namespace Tjhis.Report.Custom.Srv
{
    public class srvRoleUserQuery
    {
        /// <summary>
        /// 查询当前用户 是否有报表维护的权限
        /// </summary>
        /// <param name="userName"></param>
        /// <returns></returns>
        public static bool IsReportAuthority(string userName)
        {
            string sql = $@" select count(*) from mr_role_dict t1,mr_user_role t2 
                             where t1.role_code=t2.role_code
                             and t1.role_name='自定义报表-报表维护'
                             and t2.db_user='{userName}' ";
            return Convert.ToInt32(CommDataBase.ExecuteScalar(sql)) > 0;
        }


        public DataTable GetUserApp(string userName)
        {
            string sql = $@"SELECT  '0' COL_SEL, T1.App_Code, T6.DESCRIPTION
                           FROM MR_APP_ENTRY T1
                           LEFT JOIN MR_RIGHT_VS_UI_OBJECT T2   ON T1.NODE_CODE = T2.UI_OBJECT_ID
                           LEFT JOIN MR_ROLE_RIGHT T3     ON T2.RIGHT_ID = T3.RIGHT_ID
                           LEFT JOIN MR_USER_ROLE T4    ON T3.ROLE_CODE = T4.ROLE_CODE
                           LEFT JOIN STAFF_DICT T5   ON UPPER(T5.USER_NAME) = UPPER(T4.DB_USER)
                           LEFT JOIN MR_ROLE_DICT T7   ON T4.ROLE_CODE = T7.ROLE_CODE
                           LEFT JOIN MR_RIGHT_DICT T8    ON T8.RIGHT_ID = T2.RIGHT_ID
                           LEFT JOIN MR_RIGHT_TYPE_DICT T9    ON T9.RIGHT_TYPE_ID = T8.RIGHT_TYPE_ID
                           LEFT JOIN APPLICATIONS T6   ON T6.Application = T1.App_Code
                           WHERE T5.USER_NAME = '{userName}'    AND T2.ENABLE = 1    AND T1.STATUS = 1    AND T1.FORM_CONTROL IS NULL
                           AND T1.App_Code != '{Const.customAppCode}'
                           AND T7.HIS_UNIT_CODE = '{SystemParm.HisUnitCode}'
                           AND T9.HIS_UNIT_CODE = '{SystemParm.HisUnitCode}'                       
                           group by T1.App_Code,
                           T6.DESCRIPTION";
            return CommDataBase.GetDataTable(sql, "APPLICATIONS");
        }



        public List<string> GetUserRoles(string userName)
        {
            List<string> roles = new List<string>();
            string sql = $@" select role_code from mr_user_role 
                             where db_user='{userName}' ";
            DataTable dt= CommDataBase.GetDataTable(sql,"MR_USER_ROLE");
            foreach (DataRow dr in dt.Rows)
            {
                roles.Add(dr["role_code"].ToString());
            }
            return roles;
        }
    }
}
