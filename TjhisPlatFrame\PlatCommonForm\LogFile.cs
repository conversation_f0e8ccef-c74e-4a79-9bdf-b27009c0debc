﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace PlatCommonForm
{
    /// <summary>
    /// 日志文件
    /// </summary>
    public class LogFile
    {
        public static string path = AppDomain.CurrentDomain.BaseDirectory + "//log";

        public static string FileName;
        public LogFile(string _FileName)
        {
            FileName = _FileName;
        }
        /// <summary>
        /// 写日志
        /// </summary>
        public void Info(string className, string contentText)
        {
            WriteLog("Info", className, contentText);
        }
        /// <summary>
        /// bug日志
        /// </summary>
        public void Debug(string className, string contentText)
        {
            WriteLog("Debug", className, contentText);
        }
        /// <summary>
        /// 错误日志
        /// </summary>
        public void Error(string className, string contentText)
        {
            WriteLog("Error", className, contentText);
        }
        /// <summary>
        /// 写Log.txt日志文件
        /// </summary>
        /// <param name="type"></param>
        /// <param name="className"></param>
        /// <param name="contentText"></param>
        protected static void WriteLog(string type, string className, string contentText)
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
            string strLog = path + "/" + FileName.ToString() + DateTime.Now.ToString("yyyyMMdd") + ".log";
            StreamWriter swFile = File.AppendText(strLog);

            string NowTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            string writeText = NowTime + "==>" + type + ": " + className + contentText;
            swFile.WriteLine(writeText);
            swFile.Close();
        }

        public void LookUpLog(string _Date)
        {
            try
            {
                string strLog = path + "/" + FileName.ToString() + _Date.ToString() + ".log";
                System.Diagnostics.Process.Start("notepad.exe", strLog);
            }
            catch (Exception ex)
            {
                throw new Exception("LookUpLog " + ex.Message);
            }
        }

    }
}
