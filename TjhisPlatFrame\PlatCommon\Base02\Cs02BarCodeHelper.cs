﻿/*********************************************
* 命名空间 ：Tjhis.CsComm
* 类 名 称 ：Cs02BarCodeHelper
* 功能说明 ：
* 作    者 ：刘成刚
* 创建时间 ：2020-04-08 15:15:12
* 更 新 人 ：
* 更新时间 ：
* 更新说明 ：
* 版 本 号 ：v1.0.0.0
* CLR 版本 ：4.0.30319.42000
* 版权说明：北京天健源达 HIS基础产品研发部
/*********************************************/

using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;

namespace PlatCommon.Base02
{
    /// <summary>
    /// 条码类
    /// </summary>
    public static class Cs02BarCodeHelper
    {
        /// <summary>
        /// 生成128条码
        /// </summary>
        /// <param name="strBarCodeTxt">条码内容</param>
        /// <param name="size">大小</param>
        /// <param name="bShowText">是否显示文本</param>
        /// <returns></returns>
        public static Image GenBarCode128(string strBarCodeTxt, Size size, Boolean bShowText = true)
        {
            return GenBarCode(0, strBarCodeTxt, size, bShowText);
        }

        /// <summary>
        /// 生成39条码
        /// </summary>
        /// <param name="strBarCodeTxt">条码内容</param>
        /// <param name="size">大小</param>
        /// <param name="bShowText">是否显示文本</param>
        /// <returns></returns>
        public static Image GenBarCode39(string strBarCodeTxt,Size size, Boolean bShowText = true)
        {
            return GenBarCode(1, strBarCodeTxt, size, bShowText);
        }

        /// <summary>
        /// 生成39条码扩展
        /// </summary>
        /// <param name="strBarCodeTxt">条码内容</param>
        /// <param name="size">大小</param>
        /// <param name="bShowText">是否显示文本</param>
        /// <returns></returns>
        public static Image GenBarCode39Extend(string strBarCodeTxt, Size size, Boolean bShowText = true)
        {
            return GenBarCode(2, strBarCodeTxt, size, bShowText);
        }

        /// <summary>
        /// 生成93条码
        /// </summary>
        /// <param name="strBarCodeTxt">条码内容</param>
        /// <param name="size">大小</param>
        /// <param name="bShowText">是否显示文本</param>
        /// <returns></returns>
        public static Image GenBarCode93(string strBarCodeTxt, Size size, Boolean bShowText = true)
        {
            return GenBarCode(3, strBarCodeTxt, size, bShowText);
        }

        /// <summary>
        /// 生成93条码Extend
        /// </summary>
        /// <param name="strBarCodeTxt">条码内容</param>
        /// <param name="size">大小</param>
        /// <param name="bShowText">是否显示文本</param>
        /// <returns></returns>
        public static Image GenBarCode93Extend(string strBarCodeTxt, Size size, Boolean bShowText = true)
        {
            return GenBarCode(4, strBarCodeTxt, size, bShowText);
        }

        /// <summary>
        /// 生成EAN8
        /// </summary>
        /// <param name="strBarCodeTxt">条码内容</param>
        /// <param name="size">大小</param>
        /// <param name="bShowText">是否显示文本</param>
        /// <returns></returns>
        public static Image GenBarEAN8(string strBarCodeTxt, Size size, Boolean bShowText = true)
        {
            return GenBarCode(5, strBarCodeTxt, size, bShowText);
        }

        /// <summary>
        /// 生成EAN13
        /// </summary>
        /// <param name="strBarCodeTxt">条码内容</param>
        /// <param name="size">大小</param>
        /// <param name="bShowText">是否显示文本</param>
        /// <returns></returns>
        public static Image GenBarEAN13(string strBarCodeTxt, Size size, Boolean bShowText = true)
        {
            return GenBarCode(6, strBarCodeTxt, size, bShowText);
        }

        /// <summary>
        /// 生成EAN128
        /// </summary>
        /// <param name="strBarCodeTxt">条码内容</param>
        /// <param name="size">大小</param>
        /// <param name="bShowText">是否显示文本</param>
        /// <returns></returns>
        public static Image GenBarEAN128(string strBarCodeTxt, Size size, Boolean bShowText = true)
        {
            return GenBarCode(7, strBarCodeTxt, size, bShowText);
        }

        /// <summary>
        /// 生成QR二维码
        /// </summary>
        /// <param name="strBarCodeTxt">条码内容</param>
        /// <param name="size">大小</param>
        /// <param name="bShowText">是否显示文本</param>
        /// <returns></returns>
        public static Image GenBarCodeQR(string strBarCodeTxt, Size size, Boolean bShowText = true)
        {
            return GenBarCode(8, strBarCodeTxt, size, bShowText);
        }

        /// <summary>
        /// 生成PDF417二维码
        /// </summary>
        /// <param name="strBarCodeTxt">条码内容</param>
        /// <param name="size">大小</param>
        /// <param name="bShowText">是否显示文本</param>
        /// <returns></returns>
        public static Image GenBarCodePDF417(string strBarCodeTxt, Size size, Boolean bShowText = true)
        {
            return GenBarCode(9, strBarCodeTxt, size, bShowText);
        }

        /// <summary>
        /// 生成Code11二维码
        /// </summary>
        /// <param name="strBarCodeTxt">条码内容</param>
        /// <param name="size">大小</param>
        /// <param name="bShowText">是否显示文本</param>
        /// <returns></returns>
        public static Image GenBarCode11(string strBarCodeTxt, Size size, Boolean bShowText = true)
        {
            return GenBarCode(10, strBarCodeTxt, size, bShowText);
        }

        private static Image GenBarCode(int iBarCodeType, string strBarCodeTxt, Size size, Boolean bShowText)
        {
            Image imgReturn = null;
            try
            {
                BarCodeControl barCodeControl = new DevExpress.XtraEditors.BarCodeControl();

                barCodeControl.Size = size;
                barCodeControl.AutoModule = true;
                barCodeControl.ShowText = bShowText;
                barCodeControl.HorizontalTextAlignment = DevExpress.Utils.HorzAlignment.Center;

                switch (iBarCodeType)
                {
                    case 1:     //CODE 39
                        DevExpress.XtraPrinting.BarCode.Code39Generator code39Generator1 = new DevExpress.XtraPrinting.BarCode.Code39Generator();
                        barCodeControl.Symbology = code39Generator1;
                        break;
                    case 2:     //CODE 39Extended
                        DevExpress.XtraPrinting.BarCode.Code39ExtendedGenerator code39ExtendedGenerator1 = new DevExpress.XtraPrinting.BarCode.Code39ExtendedGenerator();
                        barCodeControl.Symbology = code39ExtendedGenerator1;
                        break;
                    case 3:     //CODE 93
                        DevExpress.XtraPrinting.BarCode.Code93Generator code93Generator1 = new DevExpress.XtraPrinting.BarCode.Code93Generator();
                        barCodeControl.Symbology = code93Generator1;
                        break;
                    case 4:     //CODE 93Extended
                        DevExpress.XtraPrinting.BarCode.Code93ExtendedGenerator code93ExtendedGenerator1 = new DevExpress.XtraPrinting.BarCode.Code93ExtendedGenerator();
                        barCodeControl.Symbology = code93ExtendedGenerator1;
                        break;
                    case 5:     //EAN8
                        DevExpress.XtraPrinting.BarCode.EAN8Generator ean8Generator1 = new DevExpress.XtraPrinting.BarCode.EAN8Generator();
                        barCodeControl.Symbology = ean8Generator1;
                        break;
                    case 6:     // EAN13
                        DevExpress.XtraPrinting.BarCode.EAN13Generator ean13Generator1 = new DevExpress.XtraPrinting.BarCode.EAN13Generator();
                        barCodeControl.Symbology = ean13Generator1;
                        break;
                    case 7:     //EAN128
                        DevExpress.XtraPrinting.BarCode.EAN128Generator ean128Generator1 = new DevExpress.XtraPrinting.BarCode.EAN128Generator();
                        barCodeControl.Symbology = ean128Generator1;
                        break;
                    case 8:     //CODE QR
                        DevExpress.XtraPrinting.BarCode.QRCodeGenerator qrCodeGenerator1 = new DevExpress.XtraPrinting.BarCode.QRCodeGenerator();
                        barCodeControl.Symbology = qrCodeGenerator1;
                        break;
                    case 9:     //PDF417
                        DevExpress.XtraPrinting.BarCode.PDF417Generator pdf417Generator1 = new DevExpress.XtraPrinting.BarCode.PDF417Generator();
                        barCodeControl.Symbology = pdf417Generator1;
                        break;
                    case 10:     //Code11
                        DevExpress.XtraPrinting.BarCode.Code11Generator code11Generator1 = new DevExpress.XtraPrinting.BarCode.Code11Generator();
                        barCodeControl.Symbology = code11Generator1;
                        break;
                    default:
                        DevExpress.XtraPrinting.BarCode.Code128Generator code128Generator1 = new DevExpress.XtraPrinting.BarCode.Code128Generator();
                        barCodeControl.Symbology = code128Generator1;
                        break;
                }

                barCodeControl.Text = strBarCodeTxt;
                imgReturn = barCodeControl.ExportToImage();
            }
            catch (Exception)
            {

            }

            return imgReturn;
        }
    }
}
