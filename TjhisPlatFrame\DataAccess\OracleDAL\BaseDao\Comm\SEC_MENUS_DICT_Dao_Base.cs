﻿using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using Utility;
using Utility.OracleODP;
using Oracle.ManagedDataAccess.Client;

namespace OracleDAL
{

    /// <summary>
    /// 应用程序菜单字典 数据库操作类
    /// </summary>

    public class SEC_MENUS_DICT_Dao_Base
    {
        #region   Method
        public bool Exists(string APPLICATION_CODE, string MENU_NAME, OracleBaseClass db)
        {
            #region  init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from SEC_MENUS_DICT");
            strSql.Append(" where ");
            strSql.Append(" APPLICATION_CODE = :APPLICATION_CODE and  ");
            strSql.Append(" MENU_NAME = :MENU_NAME ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":APPLICATION_CODE", OracleDbType.Varchar2, 16);
            p.Value = APPLICATION_CODE;
            parameters.Add(p);

            p = new OracleParameter(":MENU_NAME", OracleDbType.Varchar2, 200);
            p.Value = MENU_NAME;
            parameters.Add(p);

            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    int cmdresult;
                    cmdresult = int.Parse(ds.Tables[0].Rows[0][0].ToString());
                    if (cmdresult <= 0)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                    return false;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.SEC_MENUS_DICT model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into SEC_MENUS_DICT(");
            strSql.Append("APPLICATION_CODE,OPEN_PARAM,MENU_VISIBLE,ICON_STYLE,LARGE_ICON,SMALL_ICON,MENU_GROUP,MENU_MEMOS,OPEN_FILE_NAME,MENU_NAME,FORM_MENU,FORM_CONTROL,SERIAL_NO,MENU_TEXT,TOOL_TIPS,SUPPER_MENU,OPEN_FORM");
            strSql.Append(") values (");
            strSql.Append(":APPLICATION_CODE,:OPEN_PARAM,:MENU_VISIBLE,:ICON_STYLE,:LARGE_ICON,:SMALL_ICON,:MENU_GROUP,:MENU_MEMOS,:OPEN_FILE_NAME,:MENU_NAME,:FORM_MENU,:FORM_CONTROL,:SERIAL_NO,:MENU_TEXT,:TOOL_TIPS,:SUPPER_MENU,:OPEN_FORM");
            strSql.Append(") ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":APPLICATION_CODE", OracleDbType.Varchar2, 16);
            p.Value = model.APPLICATION_CODE;
            parameters.Add(p);

            p = new OracleParameter(":OPEN_PARAM", OracleDbType.Decimal, 1);
            p.Value = model.OPEN_PARAM;
            parameters.Add(p);

            p = new OracleParameter(":MENU_VISIBLE", OracleDbType.Varchar2, 1);
            p.Value = model.MENU_VISIBLE;
            parameters.Add(p);

            p = new OracleParameter(":ICON_STYLE", OracleDbType.Varchar2, 1);
            p.Value = model.ICON_STYLE;
            parameters.Add(p);

            p = new OracleParameter(":LARGE_ICON", OracleDbType.Varchar2, 100);
            p.Value = model.LARGE_ICON;
            parameters.Add(p);

            p = new OracleParameter(":SMALL_ICON", OracleDbType.Varchar2, 100);
            p.Value = model.SMALL_ICON;
            parameters.Add(p);

            p = new OracleParameter(":MENU_GROUP", OracleDbType.Varchar2, 100);
            p.Value = model.MENU_GROUP;
            parameters.Add(p);

            p = new OracleParameter(":MENU_MEMOS", OracleDbType.Varchar2, 100);
            p.Value = model.MENU_MEMOS;
            parameters.Add(p);

            p = new OracleParameter(":OPEN_FILE_NAME", OracleDbType.Varchar2, 100);
            p.Value = model.OPEN_FILE_NAME;
            parameters.Add(p);

            p = new OracleParameter(":MENU_NAME", OracleDbType.Varchar2, 200);
            p.Value = model.MENU_NAME;
            parameters.Add(p);

            p = new OracleParameter(":FORM_MENU", OracleDbType.Varchar2, 100);
            p.Value = model.FORM_MENU;
            parameters.Add(p);

            p = new OracleParameter(":FORM_CONTROL", OracleDbType.Varchar2, 100);
            p.Value = model.FORM_CONTROL;
            parameters.Add(p);

            p = new OracleParameter(":SERIAL_NO", OracleDbType.Decimal, 3);
            p.Value = model.SERIAL_NO;
            parameters.Add(p);

            p = new OracleParameter(":MENU_TEXT", OracleDbType.Varchar2, 100);
            p.Value = model.MENU_TEXT;
            parameters.Add(p);

            p = new OracleParameter(":TOOL_TIPS", OracleDbType.Varchar2, 100);
            p.Value = model.TOOL_TIPS;
            parameters.Add(p);

            p = new OracleParameter(":SUPPER_MENU", OracleDbType.Varchar2, 200);
            p.Value = model.SUPPER_MENU;
            parameters.Add(p);

            p = new OracleParameter(":OPEN_FORM", OracleDbType.Varchar2, 200);
            p.Value = model.OPEN_FORM;
            parameters.Add(p);
            #endregion
            try
            {

                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.SEC_MENUS_DICT model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update SEC_MENUS_DICT set ");

            strSql.Append(" APPLICATION_CODE = :APPLICATION_CODE , ");
            strSql.Append(" OPEN_PARAM = :OPEN_PARAM , ");
            strSql.Append(" MENU_VISIBLE = :MENU_VISIBLE , ");
            strSql.Append(" ICON_STYLE = :ICON_STYLE , ");
            strSql.Append(" LARGE_ICON = :LARGE_ICON , ");
            strSql.Append(" SMALL_ICON = :SMALL_ICON , ");
            strSql.Append(" MENU_GROUP = :MENU_GROUP , ");
            strSql.Append(" MENU_MEMOS = :MENU_MEMOS , ");
            strSql.Append(" OPEN_FILE_NAME = :OPEN_FILE_NAME , ");
            strSql.Append(" MENU_NAME = :MENU_NAME , ");
            strSql.Append(" FORM_MENU = :FORM_MENU , ");
            strSql.Append(" FORM_CONTROL = :FORM_CONTROL , ");
            strSql.Append(" SERIAL_NO = :SERIAL_NO , ");
            strSql.Append(" MENU_TEXT = :MENU_TEXT , ");
            strSql.Append(" TOOL_TIPS = :TOOL_TIPS , ");
            strSql.Append(" SUPPER_MENU = :SUPPER_MENU , ");
            strSql.Append(" OPEN_FORM = :OPEN_FORM  ");
            strSql.Append(" where APPLICATION_CODE=:APPLICATION_CODE and MENU_NAME=:MENU_NAME  ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":APPLICATION_CODE", OracleDbType.Varchar2, 16);
            p.Value = model.APPLICATION_CODE;
            parameters.Add(p);

            p = new OracleParameter(":OPEN_PARAM", OracleDbType.Decimal, 1);
            p.Value = model.OPEN_PARAM;
            parameters.Add(p);

            p = new OracleParameter(":MENU_VISIBLE", OracleDbType.Varchar2, 1);
            p.Value = model.MENU_VISIBLE;
            parameters.Add(p);

            p = new OracleParameter(":ICON_STYLE", OracleDbType.Varchar2, 1);
            p.Value = model.ICON_STYLE;
            parameters.Add(p);

            p = new OracleParameter(":LARGE_ICON", OracleDbType.Varchar2, 100);
            p.Value = model.LARGE_ICON;
            parameters.Add(p);

            p = new OracleParameter(":SMALL_ICON", OracleDbType.Varchar2, 100);
            p.Value = model.SMALL_ICON;
            parameters.Add(p);

            p = new OracleParameter(":MENU_GROUP", OracleDbType.Varchar2, 100);
            p.Value = model.MENU_GROUP;
            parameters.Add(p);

            p = new OracleParameter(":MENU_MEMOS", OracleDbType.Varchar2, 100);
            p.Value = model.MENU_MEMOS;
            parameters.Add(p);

            p = new OracleParameter(":OPEN_FILE_NAME", OracleDbType.Varchar2, 100);
            p.Value = model.OPEN_FILE_NAME;
            parameters.Add(p);

            p = new OracleParameter(":MENU_NAME", OracleDbType.Varchar2, 200);
            p.Value = model.MENU_NAME;
            parameters.Add(p);

            p = new OracleParameter(":FORM_MENU", OracleDbType.Varchar2, 100);
            p.Value = model.FORM_MENU;
            parameters.Add(p);

            p = new OracleParameter(":FORM_CONTROL", OracleDbType.Varchar2, 100);
            p.Value = model.FORM_CONTROL;
            parameters.Add(p);

            p = new OracleParameter(":SERIAL_NO", OracleDbType.Decimal, 3);
            p.Value = model.SERIAL_NO;
            parameters.Add(p);

            p = new OracleParameter(":MENU_TEXT", OracleDbType.Varchar2, 100);
            p.Value = model.MENU_TEXT;
            parameters.Add(p);

            p = new OracleParameter(":TOOL_TIPS", OracleDbType.Varchar2, 100);
            p.Value = model.TOOL_TIPS;
            parameters.Add(p);

            p = new OracleParameter(":SUPPER_MENU", OracleDbType.Varchar2, 200);
            p.Value = model.SUPPER_MENU;
            parameters.Add(p);

            p = new OracleParameter(":OPEN_FORM", OracleDbType.Varchar2, 200);
            p.Value = model.OPEN_FORM;
            parameters.Add(p);
            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string APPLICATION_CODE, string MENU_NAME, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from SEC_MENUS_DICT ");
            strSql.Append(" where APPLICATION_CODE=:APPLICATION_CODE and MENU_NAME=:MENU_NAME ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":APPLICATION_CODE", OracleDbType.Varchar2, 16);
            p.Value = APPLICATION_CODE;
            parameters.Add(p);

            p = new OracleParameter(":MENU_NAME", OracleDbType.Varchar2, 200);
            p.Value = MENU_NAME;
            parameters.Add(p);

            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }



        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.SEC_MENUS_DICT GetModel(string APPLICATION_CODE, string MENU_NAME, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select APPLICATION_CODE, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM  ");
            strSql.Append("  from SEC_MENUS_DICT ");
            strSql.Append(" where APPLICATION_CODE=:APPLICATION_CODE and MENU_NAME=:MENU_NAME ");
            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":APPLICATION_CODE", OracleDbType.Varchar2, 16);
            p.Value = APPLICATION_CODE;
            parameters.Add(p);

            p = new OracleParameter(":MENU_NAME", OracleDbType.Varchar2, 200);
            p.Value = MENU_NAME;
            parameters.Add(p);
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;
                    Model.SEC_MENUS_DICT model = new Model.SEC_MENUS_DICT();

                    if (cmdresult > 0)
                    {
                        model = CopyToModel(ds.Tables[0].Rows[0]);
                        return model;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM SEC_MENUS_DICT ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得几行数据
        /// </summary>
        public DataSet GetList(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            #region 初始化参数
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM SEC_MENUS_DICT T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }
            if (strWhere.Trim() != "")
            {
                strSql.Append("  and " + strWhere);
            }
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.SEC_MENUS_DICT> GetObservableCollection(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM SEC_MENUS_DICT ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.SEC_MENUS_DICT> list = new System.Collections.ObjectModel.ObservableCollection<Model.SEC_MENUS_DICT>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.SEC_MENUS_DICT model = new Model.SEC_MENUS_DICT();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表 
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.SEC_MENUS_DICT> GetObservableCollection(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM SEC_MENUS_DICT T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }

            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.SEC_MENUS_DICT> list = new System.Collections.ObjectModel.ObservableCollection<Model.SEC_MENUS_DICT>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.SEC_MENUS_DICT model = new Model.SEC_MENUS_DICT();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion   Method
        #region
        /// <summary>
        /// 
        /// </summary>
        protected Model.SEC_MENUS_DICT CopyToModel(DataRow dRow)
        {
            Model.SEC_MENUS_DICT model1 = new Model.SEC_MENUS_DICT();

            if (dRow["APPLICATION_CODE"] != null && dRow["APPLICATION_CODE"].ToString() != "")
            {
                model1.APPLICATION_CODE = dRow["APPLICATION_CODE"].ToString();
            }

            if (dRow["OPEN_PARAM"] != null && dRow["OPEN_PARAM"].ToString() != "")
            {
                model1.OPEN_PARAM = decimal.Parse(dRow["OPEN_PARAM"].ToString());
            }

            if (dRow["MENU_VISIBLE"] != null && dRow["MENU_VISIBLE"].ToString() != "")
            {
                model1.MENU_VISIBLE = dRow["MENU_VISIBLE"].ToString();
            }

            if (dRow["ICON_STYLE"] != null && dRow["ICON_STYLE"].ToString() != "")
            {
                model1.ICON_STYLE = dRow["ICON_STYLE"].ToString();
            }

            if (dRow["LARGE_ICON"] != null && dRow["LARGE_ICON"].ToString() != "")
            {
                model1.LARGE_ICON = dRow["LARGE_ICON"].ToString();
            }

            if (dRow["SMALL_ICON"] != null && dRow["SMALL_ICON"].ToString() != "")
            {
                model1.SMALL_ICON = dRow["SMALL_ICON"].ToString();
            }

            if (dRow["MENU_GROUP"] != null && dRow["MENU_GROUP"].ToString() != "")
            {
                model1.MENU_GROUP = dRow["MENU_GROUP"].ToString();
            }

            if (dRow["MENU_MEMOS"] != null && dRow["MENU_MEMOS"].ToString() != "")
            {
                model1.MENU_MEMOS = dRow["MENU_MEMOS"].ToString();
            }

            if (dRow["OPEN_FILE_NAME"] != null && dRow["OPEN_FILE_NAME"].ToString() != "")
            {
                model1.OPEN_FILE_NAME = dRow["OPEN_FILE_NAME"].ToString();
            }

            if (dRow["MENU_NAME"] != null && dRow["MENU_NAME"].ToString() != "")
            {
                model1.MENU_NAME = dRow["MENU_NAME"].ToString();
            }

            if (dRow["FORM_MENU"] != null && dRow["FORM_MENU"].ToString() != "")
            {
                model1.FORM_MENU = dRow["FORM_MENU"].ToString();
            }

            if (dRow["FORM_CONTROL"] != null && dRow["FORM_CONTROL"].ToString() != "")
            {
                model1.FORM_CONTROL = dRow["FORM_CONTROL"].ToString();
            }

            if (dRow["SERIAL_NO"] != null && dRow["SERIAL_NO"].ToString() != "")
            {
                model1.SERIAL_NO = decimal.Parse(dRow["SERIAL_NO"].ToString());
            }

            if (dRow["MENU_TEXT"] != null && dRow["MENU_TEXT"].ToString() != "")
            {
                model1.MENU_TEXT = dRow["MENU_TEXT"].ToString();
            }

            if (dRow["TOOL_TIPS"] != null && dRow["TOOL_TIPS"].ToString() != "")
            {
                model1.TOOL_TIPS = dRow["TOOL_TIPS"].ToString();
            }

            if (dRow["SUPPER_MENU"] != null && dRow["SUPPER_MENU"].ToString() != "")
            {
                model1.SUPPER_MENU = dRow["SUPPER_MENU"].ToString();
            }

            if (dRow["OPEN_FORM"] != null && dRow["OPEN_FORM"].ToString() != "")
            {
                model1.OPEN_FORM = dRow["OPEN_FORM"].ToString();
            }

            return model1;
        }
        #endregion

    }
}

