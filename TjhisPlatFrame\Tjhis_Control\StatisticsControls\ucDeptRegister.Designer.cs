﻿namespace Tjhis.Controls.StatisticsControls
{
    partial class ucDeptRegister
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.StackedBarSeriesLabel stackedBarSeriesLabel1 = new DevExpress.XtraCharts.StackedBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideStackedBarSeriesView sideBySideStackedBarSeriesView1 = new DevExpress.XtraCharts.SideBySideStackedBarSeriesView();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.TablePanelMain = new DevExpress.XtraEditors.PanelControl();
            this.ChartRegister = new DevExpress.XtraCharts.ChartControl();
            ((System.ComponentModel.ISupportInitialize)(this.TablePanelMain)).BeginInit();
            this.TablePanelMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ChartRegister)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(stackedBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideStackedBarSeriesView1)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("微软雅黑", 13F, System.Drawing.FontStyle.Bold);
            this.labelControl1.Appearance.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(78)))), ((int)(((byte)(87)))), ((int)(((byte)(128)))));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Appearance.Options.UseForeColor = true;
            this.labelControl1.Location = new System.Drawing.Point(23, 28);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(108, 25);
            this.labelControl1.TabIndex = 3;
            this.labelControl1.Text = "挂号科室排序";
            // 
            // TablePanelMain
            // 
            this.TablePanelMain.Appearance.BackColor = System.Drawing.Color.White;
            this.TablePanelMain.Appearance.Options.UseBackColor = true;
            this.TablePanelMain.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.TablePanelMain.Controls.Add(this.ChartRegister);
            this.TablePanelMain.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.TablePanelMain.Location = new System.Drawing.Point(0, 80);
            this.TablePanelMain.Name = "TablePanelMain";
            this.TablePanelMain.Padding = new System.Windows.Forms.Padding(0, 10, 0, 0);
            this.TablePanelMain.Size = new System.Drawing.Size(548, 318);
            this.TablePanelMain.TabIndex = 4;
            // 
            // ChartRegister
            // 
            this.ChartRegister.BorderOptions.Visibility = DevExpress.Utils.DefaultBoolean.False;
            xyDiagram1.AxisX.Tickmarks.MinorVisible = false;
            xyDiagram1.AxisX.Tickmarks.Visible = false;
            xyDiagram1.AxisX.Visibility = DevExpress.Utils.DefaultBoolean.True;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.GridLines.Visible = false;
            xyDiagram1.AxisY.Visibility = DevExpress.Utils.DefaultBoolean.False;
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            xyDiagram1.DefaultPane.BorderVisible = false;
            xyDiagram1.Rotated = true;
            this.ChartRegister.Diagram = xyDiagram1;
            this.ChartRegister.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ChartRegister.Legend.Name = "Default Legend";
            this.ChartRegister.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            this.ChartRegister.Location = new System.Drawing.Point(0, 10);
            this.ChartRegister.Margin = new System.Windows.Forms.Padding(9, 3, 9, 3);
            this.ChartRegister.Name = "ChartRegister";
            series1.ArgumentDataMember = "DEPT_NAME";
            stackedBarSeriesLabel1.TextPattern = "{V}";
            series1.Label = stackedBarSeriesLabel1;
            series1.LabelsVisibility = DevExpress.Utils.DefaultBoolean.True;
            series1.LegendName = "Default Legend";
            series1.Name = "Series 1";
            series1.ValueDataMembersSerializable = "SUM";
            sideBySideStackedBarSeriesView1.BarWidth = 0.1D;
            sideBySideStackedBarSeriesView1.Color = System.Drawing.Color.FromArgb(((int)(((byte)(79)))), ((int)(((byte)(209)))), ((int)(((byte)(136)))));
            series1.View = sideBySideStackedBarSeriesView1;
            this.ChartRegister.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1};
            this.ChartRegister.Size = new System.Drawing.Size(548, 308);
            this.ChartRegister.TabIndex = 0;
            // 
            // ucDeptRegister
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.TablePanelMain);
            this.Controls.Add(this.labelControl1);
            this.Name = "ucDeptRegister";
            this.Size = new System.Drawing.Size(548, 406);
            ((System.ComponentModel.ISupportInitialize)(this.TablePanelMain)).EndInit();
            this.TablePanelMain.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(stackedBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideStackedBarSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ChartRegister)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.PanelControl TablePanelMain;
        private DevExpress.XtraCharts.ChartControl ChartRegister;
    }
}
