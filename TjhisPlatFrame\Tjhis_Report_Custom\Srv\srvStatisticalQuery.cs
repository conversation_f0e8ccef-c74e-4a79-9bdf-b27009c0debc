﻿using DevExpress.XtraEditors;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using Tjhis.Report.Custom.Common;

namespace Tjhis.Report.Custom.Srv
{
    /// <summary>
    /// 自定义报表数据访问类
    /// </summary>
    public class srvStatisticalQuery
    {

        /// <summary>
        /// 医院代码
        /// </summary>
        private string hospitalCode = PlatCommon.SysBase.SystemParm.HisUnitCode;
        /// <summary>
        /// 应用程序名
        /// </summary>
        private string appName = Const.customAppCode;

        /// <summary>
        /// 获取报表记录
        /// </summary>
        /// <returns>报表记录集合</returns>
        public DataSet GetTempletList()
        {
            //string strSql = $@"AND APP_NAME='{appName}'";
            //string strOrderBy = "";
            //if (Const.CONST_APPNAME.Contains(appName))
            //{
            //    strSql = "";
            //    strOrderBy = "APP_NAME,";
            //}
            //string sql = $@"SELECT * FROM NURADM_REPORT_STATISTICS_DICT WHERE HOSPITAL_CODE='{hospitalCode}' {strSql} ORDER BY {strOrderBy} PARENTID, SERIAL_NO";
            string sql = $@"SELECT D.* FROM NURADM_REPORT_STATISTICS_DICT D WHERE HOSPITAL_CODE='{hospitalCode}'  ORDER BY APP_NAME, PARENTID, SERIAL_NO";

            return CommDataBase.GetDataSet(sql, "NURADM_REPORT_STATISTICS_DICT", false);
        }

        public DataTable GetReportTableEmpty()
        {
            string sql = $@"SELECT * FROM NURADM_REPORT_STATISTICS_DICT WHERE 1<>1";
            return CommDataBase.GetDataTable(sql, "NURADM_REPORT_STATISTICS_DICT", false);
        }

        public int GetMaxSerialNum(string app_name)
        {
            int serialNum = 0;
            string sql = $@"select max(SERIAL_NO) FROM NURADM_REPORT_STATISTICS_DICT WHERE APP_NAME = '{app_name}'";
            string strNum = CommDataBase.ExecuteScalar(sql);
            int.TryParse(strNum, out serialNum);

            return serialNum;
        }
        /// <summary>
        /// 根据报表ID获取报表记录
        /// </summary>
        /// <param name="reportID">报表ID</param>
        /// <param name="myAppName">应用程序名</param>
        /// <returns>报表记录行</returns>
        public DataRow GetTempletByIDNew(string reportID, string myAppName)
        {
            string sql = $@"SELECT * FROM NURADM_REPORT_STATISTICS_DICT WHERE DICT_ID='{reportID}' AND  HOSPITAL_CODE='{hospitalCode}' AND APP_NAME='{myAppName}' ";
            return CommDataBase.GetDataSet(sql, "NURADM_REPORT_STATISTICS_DICT", false)?.Tables?[0]?.Rows?[0];
        }

        /// <summary>
        /// 根据报表ID获取自定义记录配置
        /// </summary>
        /// <param name="reportID">报表ID</param>
        /// <param name="strAppName">应用程序名</param>
        /// <returns>某个报表的自定义记录配置</returns>
        internal DataSet GetReportConfig(string reportID, string strAppName)
        {
            string sql = $@"SELECT * FROM NURADM_REPORT_CONFIG WHERE HOSPITAL_CODE='{hospitalCode}'  AND DICT_ID='{reportID}' ORDER BY  SERIAL_NO";

            return CommDataBase.GetDataSet(sql, "NURADM_REPORT_CONFIG", false);
        }

        /// <summary>
        /// 根据报表ID和报表编码获取自定义记录配置
        /// </summary>
        /// <param name="reportID">报表ID</param>
        /// <param name="strAppName">应用程序名</param>
        /// <param name="iReportNo">报表编码</param>
        /// <returns></returns>
        internal DataSet GetReportConfigReportNo(string reportID, string strAppName, int iReportNo)
        {
            string sql = $@"SELECT * FROM NURADM_REPORT_CONFIG WHERE HOSPITAL_CODE='{hospitalCode}'  AND DICT_ID='{reportID}' AND REPORTNO= {iReportNo}  ORDER BY  SERIAL_NO";

            return CommDataBase.GetDataSet(sql, "NURADM_REPORT_CONFIG", false);
        }

        /// <summary>
        /// 获取报表树形列表，自定义报表画面使用
        /// </summary>
        /// <returns></returns>
        public DataSet GetTempTreeList()
        {
            string sql = $@" 
              SELECT *  FROM (SELECT '*root' DICT_ID, '报表类别' DICT_NAME, '' PARENTID, TO_CLOB('') TEMPLET_SQL, 
                '' ROLE_TYPE,'' ROLE_USERS, '' CREATE_USER, '' IS_TABLE, '' APP_NAME, '' ROLE_CODES, '0' TREELEVEL,
                '' HOSPITAL_CODE, 0 SERIAL_NO, '' REMARK, '' PAGE_SETTINGS
                FROM DUAL ) 
              UNION ALL
                SELECT *  FROM (SELECT '*' || A.APPLICATION  DICT_ID, A.DESCRIPTION DICT_NAME, '*root' PARENTID, TO_CLOB('') TEMPLET_SQL, 
                '' ROLE_TYPE,'' ROLE_USERS, '' CREATE_USER, '' IS_TABLE, A.APPLICATION APP_NAME, '' ROLE_CODES, '1' TREELEVEL,
                '' HOSPITAL_CODE, 0 SERIAL_NO, '' REMARK, '' PAGE_SETTINGS
                FROM APPLICATIONS A  ORDER BY class_flag) 
              UNION ALL
                SELECT *  FROM (SELECT '*' || SS.CLASS_ID DICT_ID, SS.CLASS_NAME DICT_NAME, '*' || SS.APP_NAME PARENTID, TO_CLOB('') TEMPLET_SQL, 
                '' ROLE_TYPE,'' ROLE_USERS, '' CREATE_USER, '' IS_TABLE, SS.APP_NAME, '' ROLE_CODES, '2' TREELEVEL,
                '' HOSPITAL_CODE, SS.SERIAL_NO, '' REMARK, '' PAGE_SETTINGS
                FROM NURADM_REPORT_STATISTICS_CLASS SS WHERE HOSPITAL_CODE='{hospitalCode}'  ORDER BY SS.SERIAL_NO) ";
              //UNION ALL 
              //  SELECT * FROM (SELECT S.DICT_ID, S.DICT_NAME, (case when S.PARENTID is null then '*'||S.APP_NAME else  '*'||S.PARENTID end) PARENTID,S.TEMPLET_SQL,
              //  S.ROLE_TYPE, S.ROLE_USERS, S.CREATE_NURSE, S.IS_TABLE,S.APP_NAME,S.ROLE_CODES , '3' TREELEVEL,
              //  S.HOSPITAL_CODE, S.SERIAL_NO, S.REMARK
              //  FROM NURADM_REPORT_STATISTICS_DICT S WHERE HOSPITAL_CODE='{hospitalCode}'  AND PARENTID IS NOT NULL ORDER BY S.SERIAL_NO) ";
            return CommDataBase.GetDataSet(sql, "NURADM_REPORT_STATISTICS_DICT", false);
        }

        /// <summary>
        /// 获取报表节点
        /// </summary>
        /// <returns></returns>
        public DataSet GetLeafTreeList(string app_name, string class_id)
        {
            string sql = $@"
            SELECT* FROM(SELECT S.DICT_ID, S.DICT_NAME, (case when S.PARENTID is null then '*' || S.APP_NAME else  '*' || S.PARENTID end) PARENTID,S.TEMPLET_SQL,
              S.ROLE_TYPE, S.ROLE_USERS, S.CREATE_NURSE, S.IS_TABLE,S.APP_NAME,S.ROLE_CODES , '3' TREELEVEL,
              S.HOSPITAL_CODE, S.SERIAL_NO, S.REMARK, S.PAGE_SETTINGS
              FROM NURADM_REPORT_STATISTICS_DICT S 
              WHERE HOSPITAL_CODE = '{hospitalCode}' 
             AND APP_NAME = '{app_name}'
             AND PARENTID = '{class_id}'
             ORDER BY S.SERIAL_NO) ";
            return CommDataBase.GetDataSet(sql, "NURADM_REPORT_STATISTICS_DICT", false);
        }

        /// <summary>
        /// 获取报表树，从系统管理进入查询报表时使用
        /// </summary>
        /// <returns></returns>
        public DataSet GetReportTreeList()
        {
            string sql = $@" 
              SELECT *  FROM (SELECT '*root' DICT_ID, '报表类别' DICT_NAME, '' PARENTID, TO_CLOB('') TEMPLET_SQL, 
                '' ROLE_TYPE,'' ROLE_USERS, '' CREATE_USER, '' IS_TABLE, '' APP_NAME, '' ROLE_CODES, '0' TREELEVEL,
                '' HOSPITAL_CODE, 0 SERIAL_NO, '' REMARK
                FROM DUAL ) 
              UNION ALL
                SELECT *  FROM (SELECT '*' || A.APPLICATION  DICT_ID, A.DESCRIPTION DICT_NAME, '*root' PARENTID, TO_CLOB('') TEMPLET_SQL, 
                '' ROLE_TYPE,'' ROLE_USERS, '' CREATE_USER, '' IS_TABLE, A.APPLICATION APP_NAME, '' ROLE_CODES, '1' TREELEVEL,
                '' HOSPITAL_CODE, 0 SERIAL_NO, '' REMARK
                FROM APPLICATIONS A  ORDER BY class_flag) 
              UNION ALL
                SELECT *  FROM (SELECT '*' || SS.CLASS_ID DICT_ID, SS.CLASS_NAME DICT_NAME, '*' || SS.APP_NAME PARENTID, TO_CLOB('') TEMPLET_SQL, 
                '' ROLE_TYPE,'' ROLE_USERS, '' CREATE_USER, '' IS_TABLE, SS.APP_NAME, '' ROLE_CODES, '2' TREELEVEL,
                '' HOSPITAL_CODE, SS.SERIAL_NO, '' REMARK
                FROM NURADM_REPORT_STATISTICS_CLASS SS WHERE HOSPITAL_CODE='{hospitalCode}'  ORDER BY SS.SERIAL_NO) 
            UNION ALL
              SELECT* FROM(SELECT S.DICT_ID, S.DICT_NAME, (case when S.PARENTID is null then '*' || S.APP_NAME else  '*' || S.PARENTID end) PARENTID,S.TEMPLET_SQL,
              S.ROLE_TYPE, S.ROLE_USERS, S.CREATE_NURSE, S.IS_TABLE,S.APP_NAME,S.ROLE_CODES , '3' TREELEVEL,
              S.HOSPITAL_CODE, S.SERIAL_NO, S.REMARK
              FROM NURADM_REPORT_STATISTICS_DICT S WHERE HOSPITAL_CODE = '{hospitalCode}'  AND PARENTID IS NOT NULL ORDER BY S.SERIAL_NO) ";
            return CommDataBase.GetDataSet(sql, "NURADM_REPORT_STATISTICS_DICT", false);
        }
        /// <summary>
        /// 获取从各个模块进入的树形列表
        /// </summary>
        /// <returns></returns>
        public DataSet GetTempTreeListModule(string appCode)
        {
            string sql = $@" 
            SELECT *  FROM (SELECT '*' || SS.CLASS_ID DICT_ID, SS.CLASS_NAME DICT_NAME, '**' PARENTID, TO_CLOB('') TEMPLET_SQL, '' ROLE_TYPE,'' ROLE_USERS, '' CREATE_USER, '' IS_TABLE, SS.APP_NAME, '' ROLE_CODES, '0' TREELEVEL
            FROM NURADM_REPORT_STATISTICS_CLASS SS WHERE HOSPITAL_CODE='{hospitalCode}' AND APP_NAME = '{appCode}' ORDER BY SS.SERIAL_NO) 
          UNION ALL 
            SELECT * FROM (SELECT S.DICT_ID, S.DICT_NAME, (case when S.PARENTID is null then '*'||S.APP_NAME else  '*'||S.PARENTID end) PARENTID,S.TEMPLET_SQL,S.ROLE_TYPE, S.ROLE_USERS, S.CREATE_NURSE, S.IS_TABLE,S.APP_NAME,S.ROLE_CODES, '1' TREELEVEL
            FROM NURADM_REPORT_STATISTICS_DICT S WHERE HOSPITAL_CODE='{hospitalCode}' AND APP_NAME = '{appCode}' AND PARENTID IS NOT NULL ORDER BY S.SERIAL_NO) ";
            return CommDataBase.GetDataSet(sql, "NURADM_REPORT_STATISTICS_DICT", false);
        }
        ///// <summary>
        ///// 获取报表参数
        ///// </summary>
        ///// <param name="ReportId"></param>
        ///// <returns></returns>
        //public DataSet GetReportParam(string ReportId = "*")
        //{
        //    string sql = $@"SELECT HOSPITAL_CODE  ,
        //                          APP_NAME    ,
        //                          REPORT_ID   ,
        //                          SERIAL_NO   ,       
        //                          PARAM_NAME  ,       
        //                          EDIT_TYPE   ,       
        //                          CAPTION     ,       
        //                          DISPLAY_MEMBER    , 
        //                          VALUE_MEMBER      , 
        //                          SOURCE_TYPE       , 
        //                          DATA_SOURCE_SQL   , 
        //                          NEXT_PARAM_NAME   , 
        //                          DEFAULT_VALUE     , 
        //                          CONTROL_WIDTH     ,
        //                          STATUS,DATE_FORMAT
        //                    FROM NURADM_REPORT_VS_PARAM WHERE  HOSPITAL_CODE='{hospitalCode}' AND APP_NAME='{appName}' AND (REPORT_ID='*' OR REPORT_ID='" + ReportId + "') ORDER BY SERIAL_NO";

        //    return _His01DbHelper.GetDataSet(sql, "NURADM_REPORT_VS_PARAM", false);
        //}
        public DataSet GetReportParamNew(string strAppName, string ReportId)// = "*")
        {
            string sql = $@"SELECT HOSPITAL_CODE  ,
                                  APP_NAME    ,
                                  REPORT_ID   ,
                                  SERIAL_NO   ,       
                                  PARAM_NAME  ,       
                                  EDIT_TYPE   ,       
                                  CAPTION     ,       
                                  DISPLAY_MEMBER    , 
                                  VALUE_MEMBER      , 
                                  SOURCE_TYPE       , 
                                  DATA_SOURCE_SQL   , 
                                  NEXT_PARAM_NAME   , 
                                  DEFAULT_VALUE     , 
                                  CONTROL_WIDTH     ,
                                  STATUS,DATE_FORMAT
                            FROM NURADM_REPORT_VS_PARAM WHERE  HOSPITAL_CODE='{hospitalCode}'  AND (REPORT_ID='*' OR REPORT_ID='" + ReportId + "') ORDER BY SERIAL_NO";

            return CommDataBase.GetDataSet(sql, "NURADM_REPORT_VS_PARAM", false);
        }

        public DataTable GetReportParamEmpty()
        {
            string sql = "SELECT * FROM NURADM_REPORT_VS_PARAM WHERE 1<>1";
            return CommDataBase.GetDataTable(sql, "NURADM_REPORT_VS_PARAM");
        }
        public DataSet GetParamDict()
        {
            string sql = $@"SELECT  HOSPITAL_CODE  ,
                                  APP_NAME    ,
                                  PARAM_ID        ,
                                  SERIAL_NO       ,
                                  PARAM_NAME      ,
                                  EDIT_TYPE       ,
                                  CAPTION         ,
                                  DISPLAY_MEMBER  ,
                                  VALUE_MEMBER    ,
                                  SOURCE_TYPE     ,
                                  DATA_SOURCE_SQL ,
                                  NEXT_PARAM_NAME ,
                                  DEFAULT_VALUE   ,
                                  CONTROL_WIDTH   ,
                                  STATUS,DATE_FORMAT
                        FROM NURADM_REPORT_PARAM WHERE  HOSPITAL_CODE='{hospitalCode}' ORDER BY SERIAL_NO";// AND APP_NAME='{appName}'删除此条件 20210916

            return CommDataBase.GetDataSet(sql, "NURADM_REPORT_PARAM", false);
        }
        ///// <summary>
        ///// 删除报表相关联的参数 数据
        ///// </summary>
        ///// <param name="ReportId"></param>
        ///// <returns></returns>
        //public int DeleteReportParam(string ReportId,string strAppName)
        //{
        //    string sql = $@"DELETE FROM NURADM_REPORT_VS_PARAM WHERE  HOSPITAL_CODE='{hospitalCode}' AND APP_NAME='{strAppName}' AND REPORT_ID='" + ReportId + "'";
        //    return Execute(sql);
        //}

        /// <summary>
        /// 获取数据源
        /// </summary>
        /// <param name="type"></param>
        /// <param name="sql"></param>
        /// <returns></returns>
        public DataTable GetEditDataSource(string type, string sql)
        {
            if (string.IsNullOrEmpty(type) || string.IsNullOrEmpty(sql)) return null;
            DataTable dt = null;
            try
            {
                if (type.Equals("1")|| type.Equals("5"))
                {
                    Hashtable hashtable = AddSystemParam();
                    sql = GetSqlText(sql, hashtable);
                    dt = CommDataBase.GetDataSet(sql, "TABLE_1", false)?.Tables?[0];
                }
                else
                {
                    dt = CreateDict(sql);
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message.ToString());
                return null;
            }
            return dt;
        }
        /// <summary>
        /// 创建字典
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static DataTable CreateDict(string str)
        {
            //1-wei;2-wei;3-wei
            string[] items = str.Split(';');
            if (items.Length <= 0) return null;

            DataTable dtDict = new DataTable();
            dtDict.Columns.Add("ITEM_CODE", Type.GetType("System.String"));
            dtDict.Columns.Add("ITEM_NAME", Type.GetType("System.String"));

            foreach (string item in items)
            {
                if (!string.IsNullOrEmpty(item))
                    dtDict.Rows.Add(item.Split('-'));
            }

            return dtDict;
        }
        /// <summary>
        /// 获取模板字典
        /// </summary>
        /// <returns></returns>
        public DataSet GetTempletClassDict()
        {
            //string strSql = $@"AND APP_NAME='{appName}'";
            //string strOrderBy = "";
            //if (Const.customAppCode.Contains(appName))
            //{
            //    strSql = "";
            //    strOrderBy = "APP_NAME,";
            //}
            string sql = $@"SELECT * FROM NURADM_REPORT_STATISTICS_CLASS WHERE HOSPITAL_CODE='{hospitalCode}' ORDER BY APP_NAME, SERIAL_NO";

            return CommDataBase.GetDataSet(sql, "NURADM_REPORT_STATISTICS_CLASS", false);
        }

        public DataSet GetTempletClassDictByAppName(string strAppName)
        {
            string sql = $@"SELECT CLASS_ID,CLASS_NAME FROM NURADM_REPORT_STATISTICS_CLASS WHERE HOSPITAL_CODE='{hospitalCode}'  ORDER BY SERIAL_NO";

            return CommDataBase.GetDataSet(sql, "NURADM_REPORT_STATISTICS_CLASS", false);
        }

        /// <summary>
        /// 根据多院区编码查询科室
        /// </summary>
        /// <returns></returns>
        public DataSet GetDeptDictByHisUnitCode()
        {
            string sql = $@"SELECT '*' DEPT_CODE,'全部' DEPT_NAME FROM DUAL
                            UNION ALL
                           SELECT FW.DEPT_CODE,FW.DEPT_NAME FROM DEPT_DICT FW WHERE FW.HIS_UNIT_CODE = '{PlatCommon.SysBase.SystemParm.HisUnitCode}' ";

            return CommDataBase.GetDataSet(sql, "DEPT_DICT", false);
        }
        /// <summary>
        /// 获取人员
        /// </summary>
        /// <param name="deptCode"></param>
        /// <returns></returns>
        public DataSet GetStaffDict(string deptCode = "*")
        {
            string sql = $@"SELECT '0' COL_SEL,
                                   P.DEPT_CODE,
                                   D.DEPT_NAME,
                                   P.EMP_NO,
                                   P.ID,
                                   P.NAME,
                                   P.INPUT_CODE
                              FROM STAFF_DICT P, DEPT_DICT D
                             WHERE P.DEPT_CODE = D.DEPT_CODE  AND D.HIS_UNIT_CODE =  '{PlatCommon.SysBase.SystemParm.HisUnitCode}'";
            if (!deptCode.Equals("*"))
            {
                sql += " AND P.DEPT_CODE = '" + deptCode + "'";
            }

            return CommDataBase.GetDataSet(sql, "STAFF_DICT", false);
        }

        public DataSet GetUserDict(string app_name)
        {
            string sql = $@"select DISTINCT DB_USER EMP_NO, '0' COL_SEL, STAFF_DICT.NAME,
                            STAFF_DICT.ID,STAFF_DICT.INPUT_CODE,
                            DEPT_DICT.DEPT_CODE, DEPT_DICT.DEPT_NAME 
                            from MR_USER_ROLE, STAFF_DICT, DEPT_DICT where
                            MR_USER_ROLE.DB_USER = STAFF_DICT.EMP_NO 
                            AND STAFF_DICT.DEPT_CODE = DEPT_DICT.DEPT_CODE  
                            AND DEPT_DICT.HIS_UNIT_CODE =  '{PlatCommon.SysBase.SystemParm.HisUnitCode}'
                            AND
                            role_code in 
                            (select  DISTINCT ROLEDICT.ROLE_CODE
                            from 
                                 MR_RIGHT_DICT  RD,
                                 MR_ROLE_RIGHT  RR,
                                 MR_ROLE_DICT ROLEDICT
                             where 
                              RD.RIGHT_ID = RR.RIGHT_ID
                             and RR.ROLE_CODE = ROLEDICT.ROLE_CODE
                             and RD.APPLICATION_CODE = '{app_name}'
                             and RD.HIS_UNIT_CODE = '{PlatCommon.SysBase.SystemParm.HisUnitCode}')
                           ";
            return CommDataBase.GetDataSet(sql, "MR_ROLE_DICT", false);
        }

        /// <summary>
        /// 测试脚本
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public DataSet TestSQL(string strSql, Hashtable hashParams)
        {
            string sql = GetSqlText(strSql, hashParams);
            string[] sqlArray = sql.Split(';');
            DataSet ds = null;
            foreach (string sqlStr in sqlArray)
            {
                ds = new DataSet();
                string upSql = $"SELECT * FROM ({sqlStr}) WHERE ROWNUM < 2";
                DataSet ds1 = CommDataBase.GetDataSet(upSql, "TABLE1", false);
                ds.Tables.Add(ds1.Tables[0].Copy());
            }
            return ds;
        }

        public DataSet TestSQLNew(string strSql, Hashtable hashParams)
        {
            string sql = GetSqlText(strSql, hashParams);
            string[] sqlArray = sql.Split(';');
            DataSet ds = new DataSet();
            for (int i = 0; i < sqlArray.Length; i++)
            {
                string upSql = $"SELECT * FROM ({sqlArray[i]})";
                DataSet ds1 = CommDataBase.GetDataSet(upSql, "TABLE" + (i + 1), false);
                ds.Tables.Add(ds1.Tables[0].Copy());
            }
            return ds;
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="ds"></param>
        /// <returns></returns>
        public int SaveData(DataSet ds)
        {
            if (ds.HasChanges())
            {

                int result = CommDataBase.SaveDataWithTrans(ds);
                if (result > 0)
                {
                    ds.AcceptChanges();
                }

                return result;
            }

            return 0;
        }

        #region 正式用的
        public DataTable[] FilterArrayMembers(DataTable[] tables)
        {
            //=======================去掉没有变化的行
            List<DataTable> filterList = new List<DataTable>();
            for (int i = 0; i < tables.Length; i++)
            {
                int rowCounts = GetUpDateRows(tables[i]);
                if (rowCounts > 0)
                {
                    //将string.Empty转为DbNull.Value                    
                    foreach (DataColumn dc in tables[i].Columns)
                    {
                        if (dc.DataType.ToString().Equals("System.String"))
                        {
                            DataRow[] drs = tables[i].Select(dc.ColumnName + "='' OR " + dc.ColumnName + "='" + string.Empty + "'");
                            for (int j = 0; j < drs.Length; j++)
                            {
                                if (!drs[j].RowState.Equals(DataRowState.Unchanged))//0505如果没变化，就不对空字符串赋值
                                    drs[j][dc.ColumnName] = DBNull.Value;
                            }
                        }
                    }
                    filterList.Add(tables[i]);//添加到带保存数组
                }
            }
            return filterList.ToArray();
        }
        /// <summary>
        /// 获取变化的行数
        /// </summary>
        /// <param name="table_in">传入DataTable</param>
        /// <returns></returns>
        public int GetUpDateRows(DataTable table_in)
        {
            List<DataRow> changedlist = table_in.AsEnumerable().Where<DataRow>(r => !r.RowState.Equals(DataRowState.Unchanged)).ToList();
            return changedlist.Count;
        }
        #endregion

        /// <summary>
        /// 保存表DataTable数组
        /// </summary>
        /// <param name="dts">要保存的表数组，每个DataTable必须指定表名，否则抛异常</param>
        /// <param name="dbHelper">ServerPublicClient对象</param>
        /// <returns></returns>
        public int SaveTables(DataTable[] adts)
        {
            for (int i = 0; i < adts.Length; i++)
            {
                DataTable dt = adts[i];
                if (string.IsNullOrEmpty(dt.TableName))
                {
                    throw new Exception($"第{i}个表未指定表名");
                }
            }
            DataTable[] dts = FilterArrayMembers(adts);
            if (dts.Length == 0)
                return 1;

            try
            {
                int nTotal = 0;//总影响行数
                string[] strColsArray = new string[dts.Length];
                for (int i = 0; i < dts.Length; i++)
                {//先准备好更新字段
                    DataTable dt = dts[i];
                    #region 查找传入表的字段，与原表对照生成要保存的字段
                    DataTable dt_orig = GetDataTable($"select * from {dt.TableName} where 1=2", dt.TableName, false);
                    strColsArray[i] = string.Empty;//最终的字段列表
                    foreach (DataColumn dc in dt_orig.Columns)
                    {
                        if (dt.Columns[dc.ColumnName] != null)
                            strColsArray[i] += dc.ColumnName + ",";
                    }
                    if (strColsArray[i].Length > 0)
                        strColsArray[i] = strColsArray[i].Remove(strColsArray[i].Length - 1);
                    #endregion
                }

                #region 正式要用的
                DataSet ds = new DataSet();
                for (int i = 0; i < dts.Length; i++)
                {
                    //nTotal += dbHelper.DataTableSave(dts[i], $"select {strColsArray[i]} from {dts[i].TableName}", false);
                    dts[i].ExtendedProperties["SQL"] = $"select {strColsArray[i]} from {dts[i].TableName}";
                    //nTotal += dbHelper.DataTableSave(dts[i], $"select * from {dts[i].TableName}", true);
                    ds.Tables.Add(dts[i]);
                }
                nTotal = CommDataBase.SaveDataWithTrans(ds);

                //20220421 BTE
                while (ds.Tables.Count > 0)
                {
                    ds.Tables.RemoveAt(ds.Tables.Count - 1);
                }
                //

                #endregion
                for (int i = 0; i < dts.Length; i++)
                {
                    dts[i].AcceptChanges();
                }
                return nTotal;
            }
            catch (Exception ex)
            {
                //dbHelper.RollBack();
                throw ex;
            }
        }
        /// <summary>
        /// 获取DataTable
        /// </summary>
        /// <param name="sql">查询语句</param>
        /// <param name="tableName">表名</param>
        /// <param name="bCaseSensitive">大小写敏感，默认不区分大小写</param>
        /// <param name="dbHelper">ServerPublicClient对象,如果传进来是null或者不传，则自动new一个</param>
        /// <returns></returns>
        public DataTable GetDataTable(string sql, string tableName = "", Boolean bCaseSensitive = false)
        {
            try
            {
                //DataTable dt = dbHelper.GetDataTable(sql, bCaseSensitive);
                tableName = string.IsNullOrEmpty(tableName) ? "table_1" : tableName;
                //DataTable dt = dbHelper.GetDataBySql(sql, tableName).Tables[0];
                DataTable dt = CommDataBase.GetDataTable(sql, tableName);
                DataSet ds = dt.DataSet;
                if (ds != null)
                    ds.Tables.RemoveAt(0);
                if (dt != null)
                    dt.TableName = tableName;

                return dt;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public int Execute(string sql)
        {
            return CommDataBase.ExecuteWithTrans(sql);
        }

        public DataSet GetReport(string dictID = "20200317164133291", string appName = "REPORT")
        {
            string hospitalCode = PlatCommon.SysBase.SystemParm.HisUnitCode;
            DataSet ds = new DataSet();
            string sql = $@"SELECT* FROM NURADM_REPORT_STATISTICS_DICT WHERE HOSPITAL_CODE='{hospitalCode}' AND REPORT_ID = '{dictID}'";
            DataTable dt = CommDataBase.GetDataTable(sql);
            dt.TableName = "NURADM_REPORT_STATISTICS_DICT";
            ds.Tables.Add(dt);

            sql = $@"SELECT HOSPITAL_CODE  ,
                                  APP_NAME    ,
                                  REPORT_ID   ,
                                  SERIAL_NO   ,       
                                  PARAM_NAME  ,       
                                  EDIT_TYPE   ,       
                                  CAPTION     ,       
                                  DISPLAY_MEMBER    , 
                                  VALUE_MEMBER      , 
                                  SOURCE_TYPE       , 
                                  DATA_SOURCE_SQL   , 
                                  NEXT_PARAM_NAME   , 
                                  DEFAULT_VALUE     , 
                                  CONTROL_WIDTH     ,
                                  STATUS,DATE_FORMAT
                            FROM NURADM_REPORT_VS_PARAM WHERE HOSPITAL_CODE='{hospitalCode}'  AND REPORT_ID='{dictID}' ORDER BY  SERIAL_NO";
            dt = CommDataBase.GetDataTable(sql);
            dt.TableName = "NURADM_REPORT_VS_PARAM";
            ds.Tables.Add(dt);

            sql = $@"SELECT * FROM NURADM_REPORT_CONFIG WHERE HOSPITAL_CODE='{hospitalCode}'  AND DICT_ID='{dictID}' ORDER BY  SERIAL_NO";
            dt = CommDataBase.GetDataTable(sql);
            dt.TableName = "NURADM_REPORT_CONFIG";
            ds.Tables.Add(dt);

            return ds;
        }

        public DataSet GetApplications()
        {
            string sql = $@"SELECT a.APPLICATION APP_NAME,a.DESCRIPTION FROM APPLICATIONS a ORDER BY a.CLASS_FLAG";
            //if (Const.CONST_APPNAME.Contains(appName))
            //{
            //    sql = $@"SELECT a.APPLICATION APP_NAME,a.DESCRIPTION FROM APPLICATIONS a";
            //}
            //else
            //{
            //    sql = $@"SELECT a.APPLICATION APP_NAME,a.DESCRIPTION FROM APPLICATIONS a WHERE a.APPLICATION = '{appName}'";
            //}
            return CommDataBase.GetDataSet(sql, "APPLICATIONS", false);
        }

        /// <summary>
        /// 根据报表ID更新自定义记录配置的应用程序名
        /// </summary>
        /// <param name="strAppNameOld">更新前应用程序名</param>
        /// <param name="strAppNameNew">更新后应用程序名</param>
        /// <param name="strDictId">报表ID</param>
        /// <returns></returns>
        public int UpdateReportConfigByID(string strAppNameOld, string strAppNameNew, string strDictId)
        {
            string sql = $@" UPDATE NURADM_REPORT_CONFIG SET APP_NAME = '{strAppNameNew}' WHERE  HOSPITAL_CODE='{hospitalCode}' AND APP_NAME='{strAppNameOld}' AND DICT_ID = '{strDictId}'";
            return CommDataBase.ExecuteWithTrans(sql);
        }

        //public int SaveReportRepx(string strAppName, string strReportId, string strReportFile)
        //{
        //    string strQuerySQL =
        //      $@"SELECT HOSPITAL_CODE,APP_NAME,REPORT_ID,REPORT_REPX FROM COMM.NURADM_REPORT_STATISTICS_DICT ";
        //    DataTable dtQuery = _His01DbHelper.GetDataTable(strQuerySQL + $" WHERE HOSPITAL_CODE='{hospitalCode}' AND APP_NAME = '{strAppName}' AND REPORT_ID = '{strReportId}' ");

        //    if (dtQuery.Rows.Count < 1)
        //    {
        //        return -1;
        //    }
        //    string strReportContent = Cs01FileHelper.GetFileContent(strReportFile);
        //    dtQuery.Rows[0]["REPORT_REPX"] = strReportContent;

        //    _His01DbHelper.BeginTransaction();
        //    try
        //    {
        //        _His01DbHelper.DataTableSave(dtQuery, strQuerySQL);
        //        _His01DbHelper.Commit();
        //        return 0;
        //    }
        //    catch (Exception ex)
        //    {
        //        _His01DbHelper.RollBack();
        //        throw ex;
        //    }
        //}

        public int SaveReportRepx(string strAppName, string strReportId, string reportNo, string strReportFile, bool isPrint)
        {
            if (string.IsNullOrEmpty(reportNo))
            {
                reportNo = "0";
            }
            string selectSql = $"SELECT HOSPITAL_CODE FROM COMM.NURADM_REPORT_REPX WHERE HOSPITAL_CODE='{hospitalCode}' AND  APP_NAME = '{strAppName}' AND REPORT_ID = '{strReportId}' AND REPORTNO = {reportNo}";
            DataSet ds = CommDataBase.GetDataSet(selectSql, "NURADM_REPORT_REPX");
            if (ds == null || ds.Tables[0].Rows.Count == 0)
            {
                string insertSql = $"INSERT INTO COMM.NURADM_REPORT_REPX (HOSPITAL_CODE,APP_NAME,REPORT_ID,REPORTNO,REPORT_REPX,REPORT_PRINT_REPX,COLUMN_WIDTH_CONFIG) VALUES ('{hospitalCode}' ,'{strAppName}', '{strReportId}', '{reportNo}', EMPTY_CLOB(),EMPTY_CLOB(),EMPTY_CLOB())";
                CommDataBase.ExecuteWithTrans(insertSql);
            }
            string strQuerySQL =
              $@"SELECT HOSPITAL_CODE,APP_NAME,REPORT_ID,REPORTNO,REPORT_REPX,REPORT_PRINT_REPX FROM COMM.NURADM_REPORT_REPX ";
            DataTable dtQuery = CommDataBase.GetDataTable(strQuerySQL + $" WHERE HOSPITAL_CODE='{hospitalCode}' AND APP_NAME = '{strAppName}' AND REPORT_ID = '{strReportId}' AND REPORTNO = {reportNo} ", "NURADM_REPORT_REPX");

            if (dtQuery.Rows.Count < 1)
            {
                return -1;
            }
            string strReportContent =GetFileContent(strReportFile, System.Text.Encoding.UTF8);
            if (isPrint)
            {
                dtQuery.Rows[0]["REPORT_PRINT_REPX"] = strReportContent;
            }
            else
            {
                dtQuery.Rows[0]["REPORT_REPX"] = strReportContent;
            }

            return CommDataBase.DataTableSave(dtQuery);
        }

        private  string GetFileContent(string fileName, System.Text.Encoding encoding)
        {
            if (File.Exists(fileName) == true)
            {
                StreamReader sr = new StreamReader(fileName, encoding);

                try
                {
                    return sr.ReadToEnd();
                }
                finally
                {
                    sr.Close();
                }
            }
            else
            {
                return string.Empty;
            }
        }
        /// <summary>
        /// 删除报表所有数据
        /// </summary>
        /// <param name="strReportId">报表ID</param>
        /// <param name="strAppName">应用程序</param>
        /// <param name="strIsTable">表格类报表="1"</param>
        /// <returns></returns>
        public int DeleteReportAll(string strReportId, string strAppName, string strIsTable, ref List<string> listReportNo)
        {
            string selectSql = $"SELECT REPORTNO FROM COMM.NURADM_REPORT_REPX WHERE HOSPITAL_CODE='{hospitalCode}' AND APP_NAME = '{strAppName}' AND REPORT_ID = '{strReportId}' ";
            DataSet ds = CommDataBase.GetDataSet(selectSql, "NURADM_REPORT_REPX");
            if (ds != null && ds.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    listReportNo.Add(dr["REPORTNO"].ToString());
                }
            }

            List<string> listSQL = new List<string>();

            //删除报表关联的参数
            string sql = $@"DELETE FROM NURADM_REPORT_VS_PARAM WHERE  HOSPITAL_CODE='{hospitalCode}' AND APP_NAME='{strAppName}' AND REPORT_ID='" + strReportId + "'";
            listSQL.Add(sql);

            //删除报表
            sql = $@" DELETE FROM NURADM_REPORT_STATISTICS_DICT WHERE  HOSPITAL_CODE='{hospitalCode}' AND APP_NAME='{strAppName}' AND DICT_ID = '{strReportId}'";
            listSQL.Add(sql);

            //删除报表模板
            sql = $@" DELETE FROM NURADM_REPORT_REPX WHERE  HOSPITAL_CODE='{hospitalCode}' AND APP_NAME='{strAppName}' AND REPORT_ID = '{strReportId}'";
            listSQL.Add(sql);

            //表格类报表删除自定义记录配置NURADM_REPORT_CONFIG
            if (strIsTable == "1")
            {
                sql = $@" DELETE FROM NURADM_REPORT_CONFIG WHERE  HOSPITAL_CODE='{hospitalCode}' AND APP_NAME='{strAppName}' AND DICT_ID = '{strReportId}'";
                listSQL.Add(sql);
            }
            return CommDataBase.ExecuteSql(listSQL) ? 0 : -1;
        }


        public DataSet GetDataStruct(string strSql, Hashtable hashParams, int reportNo)
        {
            string sql = GetSqlText(strSql, hashParams);
            string[] sqlArray = sql.Split(';');
            DataSet ds = new DataSet();
            if (reportNo > 0)//多个报表编码
            {
                string sql1 = sqlArray[reportNo - 1];
                string upSql = $"SELECT * FROM ({sql1}) WHERE 1 = 2";
                DataSet ds1 = CommDataBase.GetDataSet(upSql, "TABLE" + reportNo, false);
                ds.Tables.Add(ds1.Tables[0].Copy());
            }
            else
            {
                for (int i = 0; i < sqlArray.Length; i++)
                {
                    string upSql = $"SELECT * FROM ({sqlArray[i]}) WHERE 1 = 2";
                    DataSet ds1 = CommDataBase.GetDataSet(upSql, "TABLE_" + (i + 1), false);
                    ds.Tables.Add(ds1.Tables[0].Copy());
                }
            }

            // 添加单独的值
            if (hashParams != null)
            {
                DataTable dtSinleRow = ds.Tables.Add("SINGLE_ROW_TABLE");

                foreach (DictionaryEntry de in hashParams)
                {
                    if (de.Value.GetType().Equals(typeof(DateTime)))
                    {
                        dtSinleRow.Columns.Add(de.Key.ToString(), typeof(DateTime));
                    }
                    else
                    {
                        dtSinleRow.Columns.Add(de.Key.ToString());
                    }
                }

                DataRow dr = dtSinleRow.NewRow();
                foreach (DictionaryEntry de in hashParams)
                {
                    dr[de.Key.ToString()] = de.Value;
                }
                dtSinleRow.Rows.Add(dr);
            }
            return ds;
        }

        /// <summary>
        /// 获取SQL语句
        /// </summary>
        /// <returns></returns>
        private static string GetSqlText(string sql, Hashtable hasParams)
        {
            // 获取SQL原文
            string sqlText = sql;

            // 替换参数
            if (hasParams == null) return sqlText;

            string paramName = string.Empty;
            string paramValue = string.Empty;

            string paramName0 = string.Empty;
            string paramValue0 = string.Empty;

            foreach (DictionaryEntry entry in hasParams)
            {
                // 参数名
                paramName = "{" + entry.Key.ToString().ToUpper() + "}";         // 这种情况 变成 ''
                paramName0 = "[" + entry.Key.ToString().ToUpper() + "]";        // 这种情况 变成 原样

                // 参数值
                if (entry.Value == null)
                {
                    paramValue = "NULL";
                }
                else if (entry.Value.GetType().Equals(typeof(DateTime)))
                {
                    paramValue = PlatCommon.Base02.Cs02StringHelper.GetOraDate((DateTime)(entry.Value));
                }
                else if (entry.Value.GetType().Equals(typeof(int)) || entry.Value.GetType().Equals(typeof(float)) || entry.Value.GetType().Equals(typeof(Decimal)))
                {
                    paramValue = entry.Value.ToString();
                }
                else if (entry.Value.ToString().Contains("'"))
                {
                    paramValue = entry.Value.ToString();
                }
                else
                {
                    paramValue = PlatCommon.Base02.Cs02StringHelper.SqlConvert(entry.Value.ToString());
                }

                paramValue0 = entry.Value == null ? "" : entry.Value.ToString();

                // 替换参数
                if (sqlText.IndexOf(paramName) >= 0)
                {
                    sqlText = sqlText.Replace(paramName, paramValue);
                }
                else if (sqlText.IndexOf(paramName0) >= 0)
                {
                    sqlText = sqlText.Replace(paramName0, paramValue0);
                }
            }
            return sqlText;
        }


        /// <summary>
        /// 获取报表迁移记录
        /// </summary>
        /// <returns>报表迁移集合</returns>
        public DataSet GetAppMoveList()
        {
            string strSql = $@"AND APP_NAME='{appName}'";
            string strOrderBy = "";
            if (Const.CONST_APPNAME.Contains(appName))
            {
                strSql = "";
                strOrderBy = " ORDER BY APP_NAME,";
            }
            string sql = $@"SELECT * FROM COMM.NURADM_REPORT_MOVE WHERE HOSPITAL_CODE='{hospitalCode}' {strSql} {strOrderBy}";

            return CommDataBase.GetDataSet(sql, "NURADM_REPORT_MOVE", false);
        }


        /// <summary>
        /// 获取角色字典
        /// </summary>
        /// <returns></returns>
        public DataSet GetRoleDict(string app_name)
        {
            //string sql = $@"SELECT '0' COL_SEL,
            //                       P.ROLE_CODE,
            //                       P.ROLE_NAME,
            //                       P.VERSION,
            //                       P.HIS_UNIT_CODE,
            //                       P.MEMO                                 
            //                  FROM MR_ROLE_DICT P 
            //                 WHERE P.HIS_UNIT_CODE =  '{SystemParm.HisUnitCode}'";
            string sql = $@"    select  DISTINCT ROLEDICT.ROLE_CODE, '0' COL_SEL, ROLEDICT.ROLE_NAME,
                                   ROLEDICT.VERSION,
                                   ROLEDICT.HIS_UNIT_CODE,
                                   ROLEDICT.memo
                            from 
                                 MR_RIGHT_DICT  RD,
                                 MR_ROLE_RIGHT  RR,
                                 MR_ROLE_DICT ROLEDICT
                             where 
                              RD.RIGHT_ID = RR.RIGHT_ID
                             and RR.ROLE_CODE = ROLEDICT.ROLE_CODE
                             and RD.APPLICATION_CODE = '{app_name}'
                             and RD.HIS_UNIT_CODE = '{PlatCommon.SysBase.SystemParm.HisUnitCode}'";
            return CommDataBase.GetDataSet(sql, "MR_ROLE_DICT", false);
        }


        public void DeleteReportParam(string ReportId)
        {
            throw new NotImplementedException();
        }

        public void DeleteReportByID(string ReportId)
        {
            throw new NotImplementedException();
        }


        public int SaveReportConfig(string strAppName, string strReportId, string reportNo, string strReportFile)
        {
            if (string.IsNullOrEmpty(reportNo))
            {
                reportNo = "0";
            }
            string selectSql = $"SELECT HOSPITAL_CODE FROM COMM.NURADM_REPORT_REPX WHERE HOSPITAL_CODE='{hospitalCode}' AND  APP_NAME = '{strAppName}' AND REPORT_ID = '{strReportId}' AND REPORTNO = {reportNo}";
            DataSet ds = CommDataBase.GetDataSet(selectSql, "NURADM_REPORT_REPX");
            if (ds == null || ds.Tables[0].Rows.Count == 0)
            {
                string insertSql = $"INSERT INTO COMM.NURADM_REPORT_REPX (HOSPITAL_CODE,APP_NAME,REPORT_ID,REPORTNO,REPORT_REPX,REPORT_PRINT_REPX,COLUMN_WIDTH_CONFIG) VALUES ('{hospitalCode}' ,'{strAppName}', '{strReportId}', '{reportNo}', EMPTY_CLOB(),EMPTY_CLOB(),EMPTY_CLOB())";
                CommDataBase.ExecuteWithTrans(insertSql);
            }
            string strQuerySQL =
              $@"SELECT HOSPITAL_CODE,APP_NAME,REPORT_ID,REPORTNO,REPORT_REPX,REPORT_PRINT_REPX,COLUMN_WIDTH_CONFIG FROM COMM.NURADM_REPORT_REPX ";
            DataTable dtQuery = CommDataBase.GetDataTable(strQuerySQL + $" WHERE HOSPITAL_CODE='{hospitalCode}' AND APP_NAME = '{strAppName}' AND REPORT_ID = '{strReportId}' AND REPORTNO = {reportNo} ", "NURADM_REPORT_REPX");

            if (dtQuery.Rows.Count < 1)
            {
                return -1;
            }
            string strReportContent = GetFileContent(strReportFile);
            dtQuery.Rows[0]["COLUMN_WIDTH_CONFIG"] = strReportContent;
            return CommDataBase.DataTableSave(dtQuery);
        }

        /// <summary>
        /// 获取文件内容
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>文件内容字符串</returns>
        public static string GetFileContent(string fileName)
        {
            if (File.Exists(fileName) == true)
            {
                StreamReader sr = new StreamReader(fileName, System.Text.Encoding.UTF8);
                try
                {
                    return sr.ReadToEnd();
                }
                finally
                {
                    sr.Close();
                }
            }
            else
            {
                return string.Empty;
            }
        }


        /// <summary>
        /// 添加系统变量
        /// </summary>
        /// <returns></returns>
        public Hashtable AddSystemParam(string deptCode = "", string appCode = "")
        {

            Hashtable hashtable = new Hashtable();
            hashtable.Add("LOGIN_USER_NAME", PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME);
            hashtable.Add("HIS_UNIT_CODE", PlatCommon.SysBase.SystemParm.HisUnitCode);
            //lxm20230627新增 科室代码 和模块代码变量传入 ~
            hashtable.Add("APPCODE", appCode);
            if (!string.IsNullOrEmpty(deptCode))
            {
                hashtable.Add("STORAGE", deptCode);
            }
            else
            {
                hashtable.Add("STORAGE", Const.customDeptCode);
            }
            hashtable.Add("LOGIN_DEPT_CODE", PlatCommon.SysBase.SystemParm.LoginUser.DEPT_CODE);
            hashtable.Add("HospitalID", PlatCommon.SysBase.SystemParm.HospitalID);
            return hashtable;
        }
    }
}
