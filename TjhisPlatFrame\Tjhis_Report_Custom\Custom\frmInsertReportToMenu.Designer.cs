﻿namespace Tjhis.Report.Custom.Custom
{
    partial class frmInsertReportToMenu
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmInsertReportToMenu));
            this.barManager1 = new DevExpress.XtraBars.BarManager();
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barStaticItem1 = new DevExpress.XtraBars.BarStaticItem();
            this.barMenuList = new DevExpress.XtraBars.BarEditItem();
            this.repMenuList = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.barBtnInsert = new DevExpress.XtraBars.BarLargeButtonItem();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.bar3 = new DevExpress.XtraBars.Bar();
            this.BarBtnCancel = new DevExpress.XtraBars.BarLargeButtonItem();
            this.BarBtnOk = new DevExpress.XtraBars.BarLargeButtonItem();
            this.btnRemoveMenu = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colParentForm = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repLupMenuList = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repMenuList)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLupMenuList)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1,
            this.bar2,
            this.bar3});
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.BarBtnOk,
            this.BarBtnCancel,
            this.barMenuList,
            this.barStaticItem1,
            this.barBtnInsert,
            this.btnRemoveMenu});
            this.barManager1.MainMenu = this.bar2;
            this.barManager1.MaxItemId = 8;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repMenuList});
            this.barManager1.StatusBar = this.bar3;
            // 
            // bar1
            // 
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barStaticItem1),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.Width, this.barMenuList, "", false, true, true, 147),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnInsert)});
            this.bar1.OptionsBar.DrawBorder = false;
            this.bar1.OptionsBar.DrawDragBorder = false;
            this.bar1.Text = "Tools";
            // 
            // barStaticItem1
            // 
            this.barStaticItem1.Border = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.barStaticItem1.Caption = "添加至：";
            this.barStaticItem1.Id = 4;
            this.barStaticItem1.Name = "barStaticItem1";
            // 
            // barMenuList
            // 
            this.barMenuList.Caption = "菜单项";
            this.barMenuList.Edit = this.repMenuList;
            this.barMenuList.Id = 3;
            this.barMenuList.Name = "barMenuList";
            // 
            // repMenuList
            // 
            this.repMenuList.AutoHeight = false;
            this.repMenuList.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repMenuList.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_NAME", " ")});
            this.repMenuList.DisplayMember = "ITEM_NAME";
            this.repMenuList.Name = "repMenuList";
            this.repMenuList.NullText = "";
            this.repMenuList.ValueMember = "ITEM_VALUE";
            // 
            // barBtnInsert
            // 
            this.barBtnInsert.Caption = "添加";
            this.barBtnInsert.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.barBtnInsert.Id = 5;
            this.barBtnInsert.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barBtnInsert.ImageOptions.LargeImage")));
            this.barBtnInsert.Name = "barBtnInsert";
            this.barBtnInsert.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnInsert_ItemClick);
            // 
            // bar2
            // 
            this.bar2.BarName = "Main menu";
            this.bar2.DockCol = 0;
            this.bar2.DockRow = 1;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar2.OptionsBar.MultiLine = true;
            this.bar2.OptionsBar.UseWholeRow = true;
            this.bar2.Text = "Main menu";
            this.bar2.Visible = false;
            // 
            // bar3
            // 
            this.bar3.BarName = "Status bar";
            this.bar3.CanDockStyle = DevExpress.XtraBars.BarCanDockStyle.Bottom;
            this.bar3.DockCol = 0;
            this.bar3.DockRow = 0;
            this.bar3.DockStyle = DevExpress.XtraBars.BarDockStyle.Bottom;
            this.bar3.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.BarBtnCancel),
            new DevExpress.XtraBars.LinkPersistInfo(this.BarBtnOk),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnRemoveMenu)});
            this.bar3.OptionsBar.AllowQuickCustomization = false;
            this.bar3.OptionsBar.DrawBorder = false;
            this.bar3.OptionsBar.DrawDragBorder = false;
            this.bar3.OptionsBar.UseWholeRow = true;
            this.bar3.Text = "Status bar";
            // 
            // BarBtnCancel
            // 
            this.BarBtnCancel.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.BarBtnCancel.Caption = "取消";
            this.BarBtnCancel.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.BarBtnCancel.Id = 1;
            this.BarBtnCancel.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("BarBtnCancel.ImageOptions.Image")));
            this.BarBtnCancel.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("BarBtnCancel.ImageOptions.LargeImage")));
            this.BarBtnCancel.Name = "BarBtnCancel";
            this.BarBtnCancel.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.BarBtnCancel_ItemClick);
            // 
            // BarBtnOk
            // 
            this.BarBtnOk.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.BarBtnOk.Caption = "确定";
            this.BarBtnOk.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.BarBtnOk.Id = 0;
            this.BarBtnOk.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("BarBtnOk.ImageOptions.Image")));
            this.BarBtnOk.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("BarBtnOk.ImageOptions.LargeImage")));
            this.BarBtnOk.Name = "BarBtnOk";
            this.BarBtnOk.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.BarBtnOk_ItemClick);
            // 
            // btnRemoveMenu
            // 
            this.btnRemoveMenu.Caption = "移除菜单";
            this.btnRemoveMenu.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Left;
            this.btnRemoveMenu.Id = 6;
            this.btnRemoveMenu.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnRemoveMenu.ImageOptions.LargeImage")));
            this.btnRemoveMenu.Name = "btnRemoveMenu";
            this.btnRemoveMenu.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnRemoveMenu_ItemClick);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Manager = this.barManager1;
            this.barDockControlTop.Size = new System.Drawing.Size(320, 51);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 407);
            this.barDockControlBottom.Manager = this.barManager1;
            this.barDockControlBottom.Size = new System.Drawing.Size(320, 43);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 51);
            this.barDockControlLeft.Manager = this.barManager1;
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 356);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(320, 51);
            this.barDockControlRight.Manager = this.barManager1;
            this.barDockControlRight.Size = new System.Drawing.Size(0, 356);
            // 
            // gridControl1
            // 
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 51);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.MenuManager = this.barManager1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repLupMenuList});
            this.gridControl1.Size = new System.Drawing.Size(320, 356);
            this.gridControl1.TabIndex = 4;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colParentForm,
            this.gridColumn2,
            this.gridColumn3});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // colParentForm
            // 
            this.colParentForm.Caption = "菜单";
            this.colParentForm.ColumnEdit = this.repLupMenuList;
            this.colParentForm.FieldName = "PARENT_FORM";
            this.colParentForm.Name = "colParentForm";
            this.colParentForm.Visible = true;
            this.colParentForm.VisibleIndex = 0;
            this.colParentForm.Width = 302;
            // 
            // repLupMenuList
            // 
            this.repLupMenuList.AutoHeight = false;
            this.repLupMenuList.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repLupMenuList.DisplayMember = "ITEM_NAME";
            this.repLupMenuList.Name = "repLupMenuList";
            this.repLupMenuList.NullText = "";
            this.repLupMenuList.ValueMember = "ITEM_VALUE";
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "报表名称";
            this.gridColumn2.FieldName = "FORM_TEXT";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 512;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "gridColumn3";
            this.gridColumn3.Name = "gridColumn3";
            // 
            // frmInsertReportToMenu
            // 
            this.ClientSize = new System.Drawing.Size(320, 450);
            this.Controls.Add(this.gridControl1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.SizableToolWindow;
            this.Name = "frmInsertReportToMenu";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Load += new System.EventHandler(this.frmInsertReportToMenu_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repMenuList)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLupMenuList)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraBars.Bar bar3;
        private DevExpress.XtraBars.BarLargeButtonItem BarBtnCancel;
        private DevExpress.XtraBars.BarLargeButtonItem BarBtnOk;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarStaticItem barStaticItem1;
        private DevExpress.XtraBars.BarEditItem barMenuList;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repMenuList;
        private DevExpress.XtraBars.BarLargeButtonItem barBtnInsert;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraBars.BarLargeButtonItem btnRemoveMenu;
        private DevExpress.XtraGrid.Columns.GridColumn colParentForm;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repLupMenuList;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
    }
}
