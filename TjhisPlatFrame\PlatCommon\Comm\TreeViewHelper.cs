﻿//------------------------------------------------------------------------------------
//  类名            : Zxt.Comm.Helper.TreeViewHelper
//  功能概要        : TreeView操作
//  作成者          : 付军
//  作成日          : 2010-12-07
//  版本            : 1.0.0.0
// 
//------------------< 变更历史 >------------------------------------------------------
//  变更日期        : 
//  变更者          : 
//  变更内容        : 
//  版本            : 
//------------------------------------------------------------------------------------

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Windows.Forms;

namespace PlatCommon.Comm
{
    /// <summary>
    /// TreeView操作
    /// </summary>
    public class TreeViewHelper
    {
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public TreeViewHelper()
        {

        }
        
        
        /// <summary>
        /// 向上移动节点
        /// </summary>
        /// <param name="trv">TreeView控件</param>
        /// <param name="node">指定节点</param>
        /// <returns>TRUE: 成功; FALSE: 失败</returns>
        public static bool NodeMoveUp(ref TreeView trv, ref TreeNode node)
        {
            if (trv == null || node == null)
            {
                return false;
            }
            
            int         nodeIndex   = node.Index - 1;                   // 移动后节点的位置
            TreeNode    nodeNew     = new TreeNode();
            
            // 如果为最顶节点
            if (node.Index == 0)
            {
                return false;
            }
            else
            {
                // 创建Clone
                nodeNew = (TreeNode)node.Clone();
                
                // 添加节点
                if (node.Level == 0)
                {
                    trv.Nodes.Insert(node.PrevNode.Index, nodeNew);
                    
                    node.Remove();                                      // 移除节点
                    
                    node = trv.Nodes[nodeIndex];
                }
                else
                {
                    TreeNode nodeParent = node.Parent;
                    
                    nodeParent.Nodes.Insert(node.PrevNode.Index, nodeNew);
                    
                    node.Remove();                                      // 移除节点
                    
                    node = nodeParent.Nodes[nodeIndex];  
                }
                
                return true;
            }
        }
        
        
        /// <summary>
        /// 向下移动节点
        /// </summary>
        /// <param name="trv">TreeView控件</param>
        /// <param name="node">指定节点</param>
        /// <returns>TRUE: 成功; FALSE: 失败</returns>
        public static bool NodeMoveDown(ref TreeView trv, ref TreeNode node)
        {
            if (trv == null || node == null)
            {
                return false;
            }
            
            int      nodeIndex  = node.Index + 1;                       // 移动后节点的位置
            TreeNode nodeNew    = new TreeNode();
            
            // 如果选中的是根节点  
            if (node.Level == 0)  
            {  
                // 如果选中的不是最底的节点
                if (node.Index < trv.Nodes.Count - 1)  
                {  
                    nodeNew = (TreeNode)node.Clone();  
                    
                    trv.Nodes.Insert(node.NextNode.Index + 1, nodeNew);  
                    
                    node.Remove();
                    node = trv.Nodes[nodeIndex];
                    
                    return true;
                }  
            }
            // 如果选中节点不是根节点  
            else
            {  
                // 如果选中的不是最低的节点  
                TreeNode nodeParent = node.Parent;
                
                if (node.Index < nodeParent.Nodes.Count - 1)  
                {                  
                    nodeNew = (TreeNode)node.Clone();  
                    
                    nodeParent.Nodes.Insert(node.NextNode.Index + 1, nodeNew);  
                    
                    node.Remove();
                    node = nodeParent.Nodes[nodeIndex];
                    
                    return true;
                }  
            }
            
            return false;
        }
        
        
        /// <summary>
        /// 清除所有节点
        /// </summary>
        /// <param name="trv">TreeView控件</param>
        /// <param name="parentNode">指定节点</param>
        public static void ClearSubNodes(ref TreeView trv, TreeNode parentNode)
        {
            TreeNodeCollection nodes = null;
            if (parentNode == null)
            {
                nodes = trv.Nodes;
            }
            else
            {
                nodes = parentNode.Nodes;
            }
            
            for(int i = nodes.Count - 1; i >= 0; i--)
            {
                if (nodes[i].Nodes.Count > 0)
                {
                    ClearSubNodes(ref trv, nodes[i]);
                }
                
                nodes[i].Remove();
            }
        }
        
        
        /// <summary>
        /// 清除所有节点的Check状态
        /// </summary>
        /// <param name="trv">TreeView控件</param>
        /// <param name="parentNode">指定节点</param>
        public static void ClearChecked(ref TreeView trv, TreeNode parentNode)
        {
            TreeNodeCollection nodes = null;
            if (parentNode == null)
            {
                nodes = trv.Nodes;
            }
            else
            {
                nodes = parentNode.Nodes;
            }

            for (int i = nodes.Count - 1; i >= 0; i--)
            {
                if (nodes[i].Nodes.Count > 0)
                {
                    ClearChecked(ref trv, nodes[i]);
                }

                nodes[i].Checked = false;
            }
        }
        
        
        /// <summary>
        /// 节点Checked状波及上下级节点
        /// </summary>
        /// <param name="trv">TreeView控件</param>
        /// <param name="checkedNode">指定节点</param>
        public static void CheckedSpread(ref TreeView trv, TreeNode checkedNode)
        {
            bool blnChecked = checkedNode.Checked;
            TreeNode node   = null;
            
            // 如果是Checked, 父节点Checked
            if (blnChecked == true)
            {
                node = checkedNode.Parent;
                while(node != null)
                {
                    node.Checked = true;                    
                    node = node.Parent;
                }

                //LB20120109，如果选择的节点下面有子节点，则选中所有子节点
                DiGuiCheckTreeNode(checkedNode);
            }
            // 取消Checked
            else
            {
                // 取消所有子节点的Checked状态
                ClearChecked(ref trv, checkedNode);
                
                // 取消父节点的Checked状态
                node = checkedNode.Parent;
                while (node != null)
                {
                    blnChecked = false;
                    for(int i = 0; i < node.Nodes.Count; i++)
                    {
                        if (node.Nodes[i].Checked == true)
                        {
                            blnChecked = true;
                            break;
                        }
                    }
                    
                    if (blnChecked == false)
                    {
                        node.Checked = false;
                    }
                    
                    node = node.Parent;
                }
            }
        }

        /// <summary>
        /// LB20120109,选中一个节点的所有子节点
        /// </summary>
        /// <param name="parentNode"></param>
        public static void DiGuiCheckTreeNode(TreeNode parentNode)
        {
            if (parentNode.Nodes.Count == 0) return;
            foreach (TreeNode node in parentNode.Nodes)
            {
                node.Checked = true;
                DiGuiCheckTreeNode(node);
            }
        }
        
        /// <summary>
        /// 加载树节点
        /// </summary>
        /// <param name="trv">树控件</param>
        /// <param name="dsData">数据</param>
        /// <param name="tableName">表名</param>
        /// <param name="colNodeCode">节点代码字段名称</param>
        /// <param name="colNodeParentCode">父节点代码字段名称</param>
        /// <param name="colText">节点名称显示哪个字段的值</param>
        /// <param name="colTag">节点备注显示哪个字段的值</param>
        /// <param name="colOrder">节点排序字段</param>
        /// <param name="colIconFile">图标字段</param>
        /// <param name="iconPath">图标文件所在路径</param>
        /// <returns>TRUE: 加载成功; FALSE: 加载失败</returns>
        public static bool LoadTreeNodes(ref TreeView trv, DataSet dsData, string tableName, string colNodeCode, 
                                         string colNodeParentCode, string colText, string colTag, string colOrder, 
                                         string colIconFile, string iconPath)
        {
            int imgIndex = 0;
            //if (trv.ImageList == null)
            //{
            //    trv.ImageList = new ImageList();
            //}
            
            // 清除所有节点
            ClearSubNodes(ref trv, null);
            
            // 条件检查: 检查所有字段是否存在
            if (dsData == null || dsData.Tables.Count == 0 || dsData.Tables.Contains(tableName) == false) return false;
            DataTable dt = dsData.Tables[tableName];
            
            if (dt.Columns.Contains(colNodeCode) == false
                || dt.Columns.Contains(colNodeParentCode) == false
                || dt.Columns.Contains(colText) == false
                || dt.Columns.Contains(colOrder) == false
                || (colTag.Length > 0 && dt.Columns.Contains(colTag) == false)
                /*|| (colIconFile.Length > 0 && dt.Columns.Contains(colIconFile) == false)*/)
            {
                return false;
            }
            
            // 加载节点
            DataRow[] drFind = dt.Select(colNodeParentCode + " IS NULL OR " + colNodeParentCode + "= '' OR " + colNodeParentCode + " = '0'", colOrder);
            DataRow   drRec     = null;
            TreeNode  trvNode   = null;

            StringBuilder sb = new StringBuilder();
            for(int i = 0; i < drFind.Length; i++)
            {
                DateTime dt1 =  new NM_Service.NMService.ServerPublicClient().GetSysDate();

                drRec = drFind[i];
                
                // 节点文本
                trvNode = trv.Nodes.Add(drRec[colText].ToString());
                
                // 节点Tag
                if (colTag.Length > 0)
                {
                    trvNode.Tag = drRec[colTag].ToString();
                }
                
                // 添加图标
                //if (colIconFile.Length > 0)
                //{
                //    string iconFile = getIconFile(iconPath, drRec[colIconFile].ToString().Trim());
                //    if (iconFile.Length > 0)
                //    {
                //        trv.ImageList.Images.Add(System.Drawing.Image.FromFile(iconFile));
                //        trvNode.ImageIndex = trv.ImageList.Images.Count - 1;
                //        trvNode.SelectedImageIndex = trvNode.ImageIndex;
                //    }
                //}
                
                // 添加子节点
                LoadTreeSubNodes(trvNode, drRec[colNodeCode].ToString(), dsData, tableName, colNodeCode, 
                                 colNodeParentCode, colText, colTag, colOrder, colIconFile, iconPath);

                //DateTime dt2 = System.DateTime.Now;
                //TimeSpan ts = dt2.Subtract(dt1);
                //sb.Append(drRec[colText].ToString() + "------>" + ts.ToString()+"\r\n");
                //Console.WriteLine(drRec[colText].ToString() + "------>" + ts.ToString());
            }
            
            return true;
        }


        /// <summary>
        /// 加载树节点
        /// </summary>
        /// <param name="trv">树控件</param>
        /// <param name="dsData">数据</param>
        /// <param name="colNodeCode">节点代码字段名称</param>
        /// <param name="colNodeParentCode">父节点代码字段名称</param>
        /// <param name="colText">节点名称显示哪个字段的值</param>
        /// <param name="colTag">节点备注显示哪个字段的值</param>
        /// <param name="colOrder">节点排序字段</param>
        /// <param name="colIconFile">图标字段</param>
        /// <param name="iconPath">图标文件所在路径</param>
        /// <returns>TRUE: 加载成功; FALSE: 加载失败</returns>
        public static bool LoadTreeNodes(ref TreeView trv, DataSet dsData, string colNodeCode, string colNodeParentCode, 
                                          string colText, string colTag, string colOrder, 
                                          string colIconFile, string iconPath)
        {
            if(dsData == null || dsData.Tables.Count == 0)
            {
                return false;
            }
            
            return LoadTreeNodes(ref trv, dsData, dsData.Tables[0].TableName, colNodeCode, colNodeParentCode, colText, colTag, colOrder, colIconFile, iconPath);
        }


        /// <summary>
        /// 加载树节点(二级菜单)
        /// </summary>
        /// <param name="trv">树控件</param>
        /// <param name="dsData">数据</param>
        /// <param name="tableName">表名</param>
        /// <param name="strNodeCode">一级菜单项编码</param>
        /// <param name="colNodeCode">节点代码字段名称</param>
        /// <param name="colNodeParentCode">父节点代码字段名称</param>
        /// <param name="colText">节点名称显示哪个字段的值</param>
        /// <param name="colTag">节点备注显示哪个字段的值</param>
        /// <param name="colOrder">节点排序字段</param>
        /// <param name="colIconFile">图标字段</param>
        /// <param name="iconPath">图标文件所在路径</param>
        /// <returns>TRUE: 加载成功; FALSE: 加载失败</returns>
        public static bool LoadTreeNodes(ref TreeView trv, DataSet dsData, string tableName, string strNodeCode, 
                                         string colNodeCode, string colNodeParentCode, string colText, string colTag, string colOrder, 
                                         string colIconFile, string iconPath)
        {
            if(dsData == null || dsData.Tables.Count == 0)
            {
                return false;
            }

            int imgIndex = 0;
            if (trv.ImageList == null)
            {
                trv.ImageList = new ImageList();
            }
            
            // 清除所有节点
            ClearSubNodes(ref trv, null);
            
            // 条件检查: 检查所有字段是否存在
            if (dsData == null || dsData.Tables.Count == 0 || dsData.Tables.Contains(tableName) == false) return false;
            DataTable dt = dsData.Tables[tableName];
            
            if (dt.Columns.Contains(colNodeCode) == false
                || dt.Columns.Contains(colNodeParentCode) == false
                || dt.Columns.Contains(colText) == false
                || dt.Columns.Contains(colOrder) == false
                || (colTag.Length > 0 && dt.Columns.Contains(colTag) == false)
                || (colIconFile.Length > 0 && dt.Columns.Contains(colIconFile) == false))
            {
                return false;
            }
            
            // 加载节点
            DataRow[] drFind    = dt.Select(colNodeParentCode + "='" + strNodeCode + "'", colOrder);
            DataRow   drRec     = null;
            TreeNode  trvNode   = null;

            StringBuilder sb = new StringBuilder();
            for(int i = 0; i < drFind.Length; i++)
            {
                DateTime dt1 =  new NM_Service.NMService.ServerPublicClient().GetSysDate();

                drRec = drFind[i];
                
                // 节点文本
                trvNode = trv.Nodes.Add(drRec[colText].ToString());
                
                // 节点Tag
                if (colTag.Length > 0)
                {
                    trvNode.Tag = drRec[colTag].ToString();
                }
                
                // 添加图标
                if (colIconFile.Length > 0)
                {
                    string iconFile = getIconFile(iconPath, drRec[colIconFile].ToString().Trim());
                    if (iconFile.Length > 0)
                    {
                        trv.ImageList.Images.Add(System.Drawing.Image.FromFile(iconFile));
                        trvNode.ImageIndex = trv.ImageList.Images.Count - 1;
                        trvNode.SelectedImageIndex = trvNode.ImageIndex;
                    }
                }
                
                // 添加子节点
                LoadTreeSubNodes(trvNode, drRec[colNodeCode].ToString(), dsData, tableName, colNodeCode, 
                                 colNodeParentCode, colText, colTag, colOrder, colIconFile, iconPath);

                DateTime dt2 = new NM_Service.NMService.ServerPublicClient().GetSysDate();
                TimeSpan ts = dt2.Subtract(dt1);
                sb.Append(drRec[colText].ToString() + "------>" + ts.ToString()+"\r\n");
                //Console.WriteLine(drRec[colText].ToString() + "------>" + ts.ToString());
            }
            
            return true;
        }
        

        /// <summary>
        /// 加载树节点
        /// </summary>
        /// <param name="nodeParent">父节点</param>
        /// <param name="parentCode">父节点代码</param>
        /// <param name="dsData">数据</param>
        /// <param name="tableName">表名</param>
        /// <param name="colNodeCode">节点代码字段名称</param>
        /// <param name="colNodeParentCode">父节点代码字段名称</param>
        /// <param name="colText">节点名称显示哪个字段的值</param>
        /// <param name="colTag">节点备注显示哪个字段的值</param>
        /// <param name="colOrder">节点排序字段</param>
        /// <param name="colIconFile">图标字段</param>
        /// <param name="iconPath">图标文件所在路径</param>
        /// <returns></returns>
        public static void LoadTreeSubNodes(TreeNode nodeParent, string parentCode, DataSet dsData, string tableName, 
                                            string colNodeCode, string colNodeParentCode, string colText, string colTag, 
                                            string colOrder, string colIconFile, string iconPath)
        {
            DataTable dt = dsData.Tables[tableName];
            
            // 加载节点
            string filter = colNodeParentCode + " = '" + parentCode.Trim() + "'";
            DataRow[] drFind    = dt.Select(filter, colOrder);
            DataRow   drRec     = null;
            TreeNode  trvNode   = null;
            for (int i = 0; i < drFind.Length; i++)
            {
                drRec = drFind[i];
                
                // 节点文本
                trvNode = nodeParent.Nodes.Add(drRec[colText].ToString());
                
                // 节点Tag
                if (colTag.Length > 0)
                {
                    trvNode.Tag = drRec[colTag].ToString();
                }

                // 添加图标
                if (colIconFile.Length > 0)
                {
                    string iconFile = getIconFile(iconPath, drRec[colIconFile].ToString().Trim());
                    if (iconFile.Length > 0)
                    {
                        nodeParent.TreeView.ImageList.Images.Add(System.Drawing.Image.FromFile(iconFile));
                        trvNode.ImageIndex = nodeParent.TreeView.ImageList.Images.Count - 1;
                        trvNode.SelectedImageIndex = trvNode.ImageIndex;
                    }
                }
                
                // 添加子节点
                LoadTreeSubNodes(trvNode, drRec[colNodeCode].ToString(), dsData, tableName, colNodeCode, colNodeParentCode, colText, colTag, colOrder, colIconFile, iconPath);
            }
        }


        /// <summary>
        /// 加载树节点
        /// </summary>
        /// <param name="trv">树控件</param>
        /// <param name="dsData">数据</param>
        /// <param name="tableName">表名</param>
        /// <param name="colNodeCode">节点代码字段名称</param>
        /// <param name="codeLevelLen">每一层代码的长度</param>
        /// <param name="colText">节点名称显示哪个字段的值</param>
        /// <param name="colTag">节点备注显示哪个字段的值</param>
        /// <param name="colOrder">节点排序字段</param>
        /// <param name="colIconFile">图标字段</param>
        /// <param name="iconPath">图标文件所在路径</param>
        /// <returns>TRUE: 加载成功; FALSE: 加载失败</returns>
        public static bool LoadTreeNodes2(ref TreeView trv, DataSet dsData, string tableName, string colNodeCode,
                                         int codeLevelLen, string colText, string colTag, string colOrder,
                                         string colIconFile, string iconPath)
        {
            if (trv.ImageList == null)
            {
                trv.ImageList = new ImageList();
            }

            // 清除所有节点
            ClearSubNodes(ref trv, null);

            // 条件检查: 检查所有字段是否存在
            if (dsData == null || dsData.Tables.Count == 0 || dsData.Tables.Contains(tableName) == false) return false;
            DataTable dt = dsData.Tables[tableName];

            if (dt.Columns.Contains(colNodeCode) == false
                || dt.Columns.Contains(colText) == false
                || dt.Columns.Contains(colOrder) == false
                || (colTag.Length > 0 && dt.Columns.Contains(colTag) == false)
                || (colIconFile.Length > 0 && dt.Columns.Contains(colIconFile) == false))
            {
                return false;
            }

            // 加载节点
            //LB20120504,421医院注释掉
            //DataRow[] drFind    = dt.Select("LEN(" + colNodeCode +") = " + codeLevelLen.ToString(), colOrder);
            DataRow[] drFind = dt.Select(string.Empty, colOrder);
            DataRow   drRec     = null;
            TreeNode  trvNode   = null;
            for (int i = 0; i < drFind.Length; i++)
            {
                drRec = drFind[i];

                // 节点文本
                trvNode = trv.Nodes.Add(drRec[colText].ToString());

                // 节点Tag
                if (colTag.Length > 0)
                {
                    trvNode.Tag = drRec[colTag].ToString();
                }

                // 添加图标
                if (colIconFile.Length > 0)
                {
                    string iconFile = getIconFile(iconPath, drRec[colIconFile].ToString().Trim());
                    if (iconFile.Length > 0)
                    {
                        trv.ImageList.Images.Add(System.Drawing.Image.FromFile(iconFile));
                        trvNode.ImageIndex = trv.ImageList.Images.Count - 1;
                        trvNode.SelectedImageIndex = trvNode.ImageIndex;
                    }
                }

                // 添加子节点
                LoadTreeSubNodes2(trvNode, drRec[colNodeCode].ToString(), dsData, tableName, colNodeCode,
                                 codeLevelLen, colText, colTag, colOrder, colIconFile, iconPath);
            }

            return true;
        }
        
        
        /// <summary>
        /// 加载树节点
        /// </summary>
        /// <param name="nodeParent">父节点</param>
        /// <param name="parentCode">父节点代码</param>
        /// <param name="dsData">数据</param>
        /// <param name="tableName">表名</param>
        /// <param name="colNodeCode">节点代码字段名称</param>
        /// <param name="codeLevelLen">每一层代码的长度</param>
        /// <param name="colText">节点名称显示哪个字段的值</param>
        /// <param name="colTag">节点备注显示哪个字段的值</param>
        /// <param name="colOrder">节点排序字段</param>
        /// <param name="colIconFile">图标字段</param>
        /// <param name="iconPath">图标文件所在路径</param>
        /// <returns></returns>
        public static void LoadTreeSubNodes2(TreeNode nodeParent, string parentCode, DataSet dsData, string tableName,
                                            string colNodeCode, int codeLevelLen, string colText, string colTag,
                                            string colOrder, string colIconFile, string iconPath)
        {
            DataTable dt = dsData.Tables[tableName];

            // 加载节点
            parentCode = parentCode.Trim();
            string filter = colNodeCode + " LIKE '" + parentCode + "%' "
                            + "AND LEN(" + colNodeCode + ") = " + (parentCode.Length + codeLevelLen).ToString();
            
            DataRow[] drFind    = dt.Select(filter, colOrder);
            DataRow   drRec     = null;
            TreeNode  trvNode   = null;
            for (int i = 0; i < drFind.Length; i++)
            {
                drRec = drFind[i];

                // 节点文本
                trvNode = nodeParent.Nodes.Add(drRec[colText].ToString());

                // 节点Tag
                if (colTag.Length > 0)
                {
                    trvNode.Tag = drRec[colTag].ToString();
                }

                // 添加图标
                if (colIconFile.Length > 0)
                {
                    string iconFile = getIconFile(iconPath, drRec[colIconFile].ToString().Trim());
                    if (iconFile.Length > 0)
                    {
                        nodeParent.TreeView.ImageList.Images.Add(System.Drawing.Image.FromFile(iconFile));
                        trvNode.ImageIndex = nodeParent.TreeView.ImageList.Images.Count - 1;
                        trvNode.SelectedImageIndex = trvNode.ImageIndex;
                    }
                }

                // 添加子节点
                LoadTreeSubNodes2(trvNode, drRec[colNodeCode].ToString(), dsData, tableName, colNodeCode, codeLevelLen, colText, colTag, colOrder, colIconFile, iconPath);
            }
        }
        
        
        /// <summary>
        /// 获取当前dll的路径
        /// </summary>
        /// <returns></returns>
        private static string getDllPath()
        {
            string dllFile = System.Reflection.Assembly.GetExecutingAssembly().CodeBase;
            int    length  = dllFile.LastIndexOf("/"); 
            return dllFile.Substring(8, length - 8);
        }

        
        /// <summary>
        /// 获取图标文件的绝对路径
        /// </summary>
        /// <param name="iconPath">图标文件所有目录 (相对于当前dll)</param>
        /// <param name="iconFile">图标文件</param>
        /// <returns>图标文件的绝对路径</returns>
        private static string getIconFile(string iconPath, string iconFile)
        {
            if (iconFile.Trim().Length == 0)
            {
                return string.Empty;
            }
            
            string filePath = getDllPath();
            if (iconPath.Length > 0)
            {
                filePath = System.IO.Path.Combine(filePath, iconPath);
            }
            
            iconFile = System.IO.Path.Combine(filePath, iconFile);
            
            if (System.IO.File.Exists(iconFile) == false)
            {
                return string.Empty;
            }
            
            return iconFile;
        }
    }
}
