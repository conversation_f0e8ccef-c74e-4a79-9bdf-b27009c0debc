﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.UserSkins;
using DevExpress.Skins;
using DevExpress.LookAndFeel;
using System.Configuration;
using System.Diagnostics;
using PlatCommon.SysBase;
using System.IO;

namespace Tj_Update
{
    static class Program
    {
        static string update_use_unitcode = "";
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main(string[] args)
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            BonusSkins.Register();
            SkinManager.EnableFormSkins();
            //-- 梁吉 获取数据连接信息，增加方便跟踪的数据库连接方式
            try
            {
                Utility.UntilityConstant.DataConnectionString = Utility.ConfigHelper.GetConfigConnectionStr();
                if (string.IsNullOrEmpty(Utility.UntilityConstant.DataConnectionString))
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("获取数据库连接字符串失败！");
                    Application.Exit();
                    return;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            ////如果是OracleClient方式，初始化全局连接
            //Utility.Gloobal.SetOracleClientConnection();



            string UpdateUri = System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetAssembly(typeof(Program)).CodeBase).Replace("file:\\", "");
            string ApplicationName = ConfigurationManager.AppSettings["ApplicationName"] != null && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["ApplicationName"].ToString()) ? ConfigurationManager.AppSettings["ApplicationName"].ToString() : "NursingPlatform.exe";
            string ApplicationPath = ConfigurationManager.AppSettings["ApplicationPath"] != null && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["ApplicationPath"].ToString()) ? ConfigurationManager.AppSettings["ApplicationPath"].ToString() : "Client";
            string userId = "";
            string userPw = "";
            string app = ConfigurationManager.AppSettings["app"] != null && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["app"].ToString()) ? ConfigurationManager.AppSettings["app"].ToString() : "NURSINGPLATFORM";
            //医院编码
            PlatCommon.SysBase.SystemParm.HisUnitCode = ConfigurationManager.AppSettings["UNITCODE"] != null && !string.IsNullOrEmpty(ConfigurationManager.AppSettings["UNITCODE"].ToString()) ? ConfigurationManager.AppSettings["UNITCODE"].ToString() : "";
            string APP_DK = PlatCommon.SysBase.SystemParm.GetParaValue("APP_DK", "*", "*", "*", "", PlatCommon.SysBase.SystemParm.HisUnitCode);
            string APP_UPDATE_DIRECT = PlatCommon.SysBase.SystemParm.GetParaValue("APP_UPDATE_DIRECT", "*", "*", "*", "", PlatCommon.SysBase.SystemParm.HisUnitCode);
            if (args.Length > 0)
            {
                if (!"1".Equals(APP_DK) && RunningInstance(ApplicationPath, ApplicationName) != null)
                {
                    MessageBox.Show("程序已经有一个实例在运行！", "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                //userId = args[1].ToString();
                //userPw = args[2].ToString();
                string[] arr = args[0].Trim().Split(new string[] { "|" }, StringSplitOptions.None);
                if (arr.Length >= 2)
                {
                    userId = arr[0];
                    userPw = arr[1];

                    if (arr.Length >= 3)
                    {
                      //  PlatCommon.SysBase.SystemParm.AppName = arr[2].ToUpper(); //模块代码已经不在此处获取
                    }
                    if (!string.IsNullOrEmpty(userId) && (!string.IsNullOrEmpty(userPw)))
                    {
                        if (LoginCommand(userId, userPw))
                        {
                            update_use_unitcode = PlatCommon.SysBase.SystemParm.GetParaValue("UPDATE_USE_UNITCODE", "*", "*", "*", "0");
                            if (CheckUpdate(UpdateUri, app))
                            {
                                if (RunningInstance(ApplicationPath, ApplicationName) != null)
                                {
                                    //提示是否需要关闭更新
                                    if ("1".Equals(APP_UPDATE_DIRECT))
                                    {
                                        if (DevExpress.XtraEditors.XtraMessageBox.Show("是否关闭已打开应用，进行更新？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
                                        {
                                            Application.Exit();
                                            return;
                                        }
                                        //关闭所有程序
                                        CloseInstance(ApplicationPath, ApplicationName);
                                    }
                                    else if ("2".Equals(APP_UPDATE_DIRECT))
                                    {
                                        //直接关闭所有程序更新
                                        CloseInstance(ApplicationPath, ApplicationName);
                                    }
                                }
                                
                                Application.Run(new EmrUpdate(UpdateUri, ApplicationName, userId, userPw, app, "run"));
                            }
                            else
                            {
                                ReStartEmr(UpdateUri, ApplicationName, userId, userPw, app, "run");
                            }
                        }                            
                        else
                        {
                            Application.Exit();
                        }
                    }
                    else
                    {
                        Application.Exit();
                    }
                }
                else
                {
                    Application.Exit();
                }
                
            }
            else
            {
                if (!"1".Equals(APP_DK) && RunningInstance(ApplicationPath, ApplicationName) != null)
                {
                    MessageBox.Show("程序已经有一个实例在运行！", "系统提示", MessageBoxButtons.OK, MessageBoxIcon.Information);//form启动
                }
                else
                {
                    
                    //Frm_Login login = new Frm_Login();
                    FrmLoginNew login = new FrmLoginNew(); 
                    login.ShowDialog();
                    if (login.DialogResult == DialogResult.OK)
                    {
                        userId = login.UserName;
                        userPw = login.PassWord;
                        ////复制一个copy
                        //File.Copy(Application.ExecutablePath,Application.StartupPath+@"\update_temp.exe");
                        ////启动copy
                        //Application.Run(new EmrUpdate(UpdateUri, Application.StartupPath + @"\update_temp.exe", userId, userPw, app, "run"));
                        //return;
                        update_use_unitcode = PlatCommon.SysBase.SystemParm.GetParaValue("UPDATE_USE_UNITCODE", "*", "*", "*", "0");
                        if (CheckUpdate(UpdateUri, app))
                        {
                            //存在实例在运行，需要判断是否关闭更新
                            if (RunningInstance(ApplicationPath, ApplicationName) != null)
                            {
                                //提示是否需要关闭更新
                                if ("1".Equals(APP_UPDATE_DIRECT))
                                {
                                    if (DevExpress.XtraEditors.XtraMessageBox.Show("是否关闭已打开应用，进行更新？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
                                    {
                                        return;
                                    }
                                    //关闭所有程序
                                    CloseInstance(ApplicationPath, ApplicationName);
                                }
                                else if ("2".Equals(APP_UPDATE_DIRECT))
                                {
                                    //直接关闭所有程序更新
                                    CloseInstance(ApplicationPath, ApplicationName);
                                }
                            }
                            Application.Run(new EmrUpdate(UpdateUri, ApplicationName, userId, userPw, app, "run"));
                        }
                        else
                        {
                            ReStartEmr(UpdateUri, ApplicationName, userId, userPw, app, "run");
                        }
                        //Application.Run(new EmrUpdate(UpdateUri, ApplicationName, userId, userPw, app, "run"));
                    }
                }
            }
        }
        /// <summary>
        /// 登录验证方法
        /// </summary>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        public static bool LoginCommand(string username, string password)
        {
            NM_Service.NMService.STAFF_DICTClient client = new NM_Service.NMService.STAFF_DICTClient();
            int result = client.LoginValidate_STAFF_DICT(username, password);
            switch (result)
            {
                case -1:
                    DevExpress.XtraEditors.XtraMessageBox.Show("用户不存在！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break;
                case 0:
                    DevExpress.XtraEditors.XtraMessageBox.Show("密码错误！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break;
                case 1:
                    SystemParm.LoginUser = client.GetModelByUserName_STAFF_DICT(username);

                    Utility.ConfigHelper.SetConfiguration("LastLoginUser", username);
                    //验证通过开始更新窗体

                    return true;
                case 2:
                    DevExpress.XtraEditors.XtraMessageBox.Show("用户已锁定，请联系管理员！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break;
                default:

                    DevExpress.XtraEditors.XtraMessageBox.Show("未知错误！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break;
            }
            return false;
        }
        /// <summary>
        /// 读取本地版本号
        /// </summary>
        /// <returns></returns>
        private static string GetVer(string uri)
        {
            if (!File.Exists(uri + @"\update\ver.txt"))
            {
                return "";
            }
            FileStream fs = new FileStream(uri + @"\update\ver.txt", FileMode.Open);
            StreamReader m_streamReader = new StreamReader(fs);
            m_streamReader.BaseStream.Seek(0, SeekOrigin.Begin);
            string arry = "";
            string strLine = m_streamReader.ReadLine();
            arry = strLine;
            m_streamReader.Close();
            m_streamReader.Dispose();
            fs.Close();
            fs.Dispose();
            return arry;
        }
        /// <summary>
        /// 取出最新版本号
        /// </summary>
        /// <returns></returns>
        private static string GetLastVer(string app)
        {
            using (NM_Service.NMService.ServerPublicClient proxy = new NM_Service.NMService.ServerPublicClient())
            {
                //return proxy.GetVer_APPLICATIONS_PARA(appName);
                string condition = "";
                if ("1".Equals(update_use_unitcode))
                {
                    condition = "AND HIS_UNIT_CODE='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";
                }
                System.Data.DataSet ds = proxy.GetDataBySql(" SELECT LASTUPLOADTIME FROM APPLICATIONS_PARA WHERE CODE ='" + app + "' AND ISDOWNLOAD ='Y' "+ condition);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    System.Data.DataRow dr = ds.Tables[0].Rows[0];
                    return dr[0] == DBNull.Value ? "" : dr[0].ToString();
                }
            }
            return "";
        }
        /// <summary>
        /// 是否需要更新
        /// </summary>
        /// <returns>true 需要 false 不需要</returns>
        public static bool CheckUpdate(string uri,string app)
        {
            try
            {
                //取出最新程序的版本号
                string newVersion = GetLastVer(app);
                if (string.IsNullOrEmpty(newVersion))
                {
                    return false;
                }
                //取出本地的ver.txt中的版本号
                string ver = GetVer(uri);
                //如果相同，则不需要更新，如果不同，则更新
                if (ver.CompareTo(newVersion) >= 0)
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
            catch
            {
                return true;
            }
        }
        /// <summary>
        /// 判断
        /// </summary>
        /// <returns></returns>
        public static Process RunningInstance(string app_path,string app_name)
        {
            Process currentProcess = Process.GetCurrentProcess();
            Process[] sameProcess = Process.GetProcessesByName
     (System.IO.Path.GetFileNameWithoutExtension(AppDomain.CurrentDomain.BaseDirectory + currentProcess.ProcessName));
            
            if (sameProcess != null && sameProcess.Length > 0)
            {
                //遍历正在有相同名字运行的例程     
                foreach (Process process in sameProcess)
                {      //忽略现有的例程  
                    if (process.Id != currentProcess.Id &&
                 (currentProcess.StartTime - process.StartTime).TotalMilliseconds <= 0)
                    {
                        return process;
                    }
                }
            }
            #region 查找客户端
            sameProcess = Process.GetProcessesByName
     (System.IO.Path.GetFileNameWithoutExtension(AppDomain.CurrentDomain.BaseDirectory + app_path+@"\" + app_name));

            if (sameProcess != null && sameProcess.Length > 0)
            {
                return sameProcess[0];
            }
            #endregion
            return null;
        }


        public static void CloseInstance(string app_path, string app_name)
        {
            Process currentProcess = Process.GetCurrentProcess();
            Process[] sameProcess = Process.GetProcessesByName
     (System.IO.Path.GetFileNameWithoutExtension(AppDomain.CurrentDomain.BaseDirectory + currentProcess.ProcessName));

            if (sameProcess != null && sameProcess.Length > 0)
            {
                //遍历正在有相同名字运行的例程     
                foreach (Process process in sameProcess)
                {      //忽略现有的例程  
                    if (process.Id != currentProcess.Id &&
                 (process.StartTime - currentProcess.StartTime).TotalMilliseconds <= 0)
                    {
                        //找到程序进程,kill之。
                        process.EnableRaisingEvents = false;
                        //if (!process.CloseMainWindow())
                        //{
                        //    process.Kill();
                        //}
                        RunCmd("taskkill /im " + app_name + " /f ");
                        return;
                    }
                }
            }
            #region 查找客户端
            sameProcess = Process.GetProcessesByName
     (System.IO.Path.GetFileNameWithoutExtension(AppDomain.CurrentDomain.BaseDirectory + app_path + @"\" + app_name));

            if (sameProcess != null && sameProcess.Length > 0)
            {
                ////找到程序进程,kill之。
                //if (!sameProcess[0].CloseMainWindow())
                //{
                //    sameProcess[0].Kill(); 
                //}
                RunCmd("taskkill /im " + app_name + "* /f ");
                return;
            }
            #endregion
            return;
        }
        /// <summary>    
        /// 运行DOS命令    
        /// DOS关闭进程命令(ntsd -c q -p PID )PID为进程的ID    
        /// </summary>    
        /// <param name="command"></param>    
        /// <returns></returns>    
        public static string RunCmd(string command)
        {
            //實例一個Process類，啟動一個獨立進程    
            System.Diagnostics.Process p = new System.Diagnostics.Process();

            //Process類有一個StartInfo屬性，這個是ProcessStartInfo類，包括了一些屬性和方法，下面我們用到了他的幾個屬性：    

            p.StartInfo.FileName = "cmd.exe";           //設定程序名    
            p.StartInfo.Arguments = "/c " + command;    //設定程式執行參數    
            p.StartInfo.UseShellExecute = false;        //關閉Shell的使用    
            p.StartInfo.RedirectStandardInput = true;   //重定向標準輸入    
            p.StartInfo.RedirectStandardOutput = true;  //重定向標準輸出    
            p.StartInfo.RedirectStandardError = true;   //重定向錯誤輸出    
            p.StartInfo.CreateNoWindow = true;          //設置不顯示窗口    

            p.Start();   //啟動    

            //p.StandardInput.WriteLine(command);       //也可以用這種方式輸入要執行的命令    
            //p.StandardInput.WriteLine("exit");        //不過要記得加上Exit要不然下一行程式執行的時候會當機    

            return p.StandardOutput.ReadToEnd();        //從輸出流取得命令執行結果    

        }
        /// <summary>
        /// 重新启动程序
        /// </summary>
        /// <returns></returns>
        private static void ReStartEmr(string path, string ApplicationName, string userId, string passWord, string app, string isRun)
        {
            //参数
            string[] arg = new string[3];
            arg[2] = "";// SystemParm.AppName.ToUpper(); //"tj_app_updated";
            if (!string.IsNullOrEmpty(userId.ToUpper()) && !string.IsNullOrEmpty(passWord.ToUpper()))
            {
                arg[0] = userId;
                arg[1] = passWord;
            }
            string s = "";
            //修改成一个参数 2016-08-08 梁吉
            foreach (string ar in arg)
            {
                s = s + ar + "|";
            }

            s = s.Trim();
            if (s.Substring(s.Length - 1).Equals("||"))
            {
                s = s.Remove(s.Length - 2);
            }
            else if (s.Substring(s.Length - 1).Equals("|"))
            {
                s = s.Remove(s.Length - 1);
            }
            //关闭程序
            Application.Exit();
            //启动原有程序
            if (File.Exists(path + "\\Client\\" + ApplicationName))
            {
                Process process = new Process();
                string ApplicationPath = System.Configuration.ConfigurationManager.AppSettings["ApplicationPath"] != null && !string.IsNullOrEmpty(System.Configuration.ConfigurationManager.AppSettings["ApplicationPath"].ToString()) ? System.Configuration.ConfigurationManager.AppSettings["ApplicationPath"].ToString() : "Client";
                ProcessStartInfo startInfo = new ProcessStartInfo(path + "\\" + ApplicationPath + "\\" + ApplicationName, s);
                startInfo.WindowStyle = ProcessWindowStyle.Hidden;
                //startInfo.WorkingDirectory = uri + "\\emrclient";
                startInfo.WorkingDirectory = path + "\\" + ApplicationPath;
                startInfo.WindowStyle = ProcessWindowStyle.Hidden;
                process.StartInfo = startInfo;

                process.StartInfo.UseShellExecute = false;
                process.Start();
            }
        }
    }
}
