﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Utility
{
    public static class TJ_DataBase_Operation_Factory
    {
        /// <summary>
        /// 根据配置，创建数据库连接模式
        /// </summary>
        /// <returns></returns>
        public static TJ_DataBase_Operation_Interface CreateDataBaseOperation()
        {
            String DBMode = "";
            DBMode = ConfigHelper.GetConfigString(UntilityConstant.DataBaseConnectionMode);
            if (String.IsNullOrEmpty(DBMode))
            {
                //没有获取到相关配置，或相关配置为空
                DBMode = UntilityConstant.DataBaseConnectionDefaultMode;
            }
            switch (DBMode)
            {
                case "ODP":
                    {
                        String ODPConnectionString="";
                        ODPConnectionString=ConfigHelper.GetConnectionString();
                        ODPConnectionString=@"Data Source=(DESCRIPTION =(ADDRESS_LIST =(ADDRESS = (PROTOCOL = TCP)(HOST = *************)(PORT = 1521)))(CONNECT_DATA =(SERVICE_NAME = develop)));User ID=system;Password=*******;";
                        if (String.IsNullOrEmpty(ODPConnectionString))
                        {
                            throw new Exception("数据库连接字符串不能为空，请检查相关配置");
                        }
                        return new OracleODP.OracleBaseClass(ODPConnectionString);
                    }
                default:
                    {
                        //默认使用ODP 32位
                        String ODPConnectionString = "";
                        ODPConnectionString = ConfigHelper.GetConnectionString();
                        return new OracleODP.OracleBaseClass(ODPConnectionString);
                    }
            }

        }
    }
}
