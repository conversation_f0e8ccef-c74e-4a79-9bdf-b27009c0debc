﻿using System;
using System.Data;
using System.Text;
using System.Collections.Generic;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Microsoft.Practices.EnterpriseLibrary.Data.Sql;
using System.Data.Common;
using Utility.DbInfo;
using System.Collections;
using Utility;
using Model;
using System.Data.OracleClient;
using System.Configuration;
using System.Net;
using System.Net.Sockets;
namespace Utility.YangFan
{
    public class TrackEntity
    {

        public void Add(string Class_Name, string Method_Name, string Sql, string Client_Ip)
        {
            //杨帆 2016年9月30日  修改
            String RunStyle = TrackCOMM.MR_TRACK_RUN_STYLE;
            String RunIP = TrackCOMM.MR_TRACK_RUN_IP;
            if (!TrackCOMM.Track_Indicator)
            {
                return;
            }
            try
            {
                if (String.IsNullOrEmpty(RunStyle))
                {
                    //throw new Exception("MR_TRACK_RUN_STYLE参数值为空");
                    return;
                }
                else
                {
                    if (String.IsNullOrEmpty(RunIP))
                    {
                        //throw new Exception("MR_TRACK_RUN_IP参数值为空");
                        return;
                    }
                    String ip = RunIP.Split(':')[0];
                    String port = RunIP.Split(':')[1];
                    int iport = int.Parse(port);
                    switch (RunStyle)
                    {
                        case "0":
                            {
                                if (Client_Ip == ip)
                                {
                                    String SQLlog = DateTime.Now.ToLongTimeString() + "\r\n" +
                                                         "类名：" + Class_Name + "\r\n" +
                                                         "方法：" + Method_Name + "\r\n" +
                                                         "请求IP:" + Client_Ip + "\r\n" +
                                                         "SQL:\r\n" + Sql + "\r\n";
                                    UdpClient udp = new UdpClient();
                                    IPEndPoint localIpep = new IPEndPoint(
                                            IPAddress.Parse(ip), iport); // 本机IP和监听端口号
                                    byte[] logs = Encoding.Default.GetBytes(SQLlog);
                                    udp.Send(logs, logs.Length, localIpep);
                                }
                                break;
                               
                            }
                        case "1":
                            {
                                String SQLlog = DateTime.Now.ToLongTimeString() + "\r\n" +
                                                           "类名：" + Class_Name + "\r\n" +
                                                           "方法：" + Method_Name + "\r\n" +
                                                           "请求IP:" + Client_Ip + "\r\n" +
                                                           "SQL:\r\n" + Sql + "\r\n";
                                UdpClient udp = new UdpClient();
                                if (Client_Ip == "::1")
                                {
                                    Client_Ip = "127.0.0.1";
                                }
                                IPEndPoint localIpep = new IPEndPoint(
                                        IPAddress.Parse(Client_Ip), iport); // 本机IP和监听端口号
                                byte[] logs = Encoding.Default.GetBytes(SQLlog);
                                udp.Send(logs, logs.Length, localIpep);
                                break;
                            }
                        case "2":
                            {

                                String SQLlog = DateTime.Now.ToLongTimeString() + "\r\n" +
                                                        "类名：" + Class_Name + "\r\n" +
                                                        "方法：" + Method_Name + "\r\n" +
                                                        "请求IP:" + Client_Ip + "\r\n" +
                                                        "SQL:\r\n" + Sql + "\r\n";
                                UdpClient udp = new UdpClient();
                                IPEndPoint localIpep = new IPEndPoint(
                                        IPAddress.Parse(ip), iport); // 本机IP和监听端口号
                                byte[] logs = Encoding.Default.GetBytes(SQLlog);
                                udp.Send(logs, logs.Length, localIpep);
              
                                break;
                            }
                    }
                }
            }
            catch (Exception)
            {
                return;
            }
        }
    }
}
