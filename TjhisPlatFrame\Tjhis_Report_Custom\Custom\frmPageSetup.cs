﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace Tjhis.Report.Custom.Custom
{
    public partial class frmPageSetup : Form
    {
        public PrintPageSettings pps = null;
        /// <summary>
        /// ctor.
        /// </summary>
        /// <param name="pageSet"></param>
        public frmPageSetup(PaperKind kind, bool landscape)
        {
            InitializeComponent();
            pps = new PrintPageSettings(kind, landscape);
        }
    public frmPageSetup(PrintPageSettings pageSet)
        {
            InitializeComponent();
            pps = pageSet;
        }
        public frmPageSetup()
        {
            InitializeComponent();
            pps = new PrintPageSettings();
        }
        /// <summary>
        /// 初始化属性
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void frmPageSetup_Load(object sender, EventArgs e)
        {
            if (pps != null)
            {

                MyPaperSize[] paperList = PaperSizeCollection.StdInstance.ToArray();
                this.cmbPaperKind.Items.AddRange(paperList);
                var result = paperList.Where(w => w.Kind == pps.PaperSize.Kind).FirstOrDefault();
                cmbPaperKind.SelectedItem = result;
                this.nudWidth.Value = pps.PaperSize.Width;
                this.nudHeight.Value = pps.PaperSize.Height;

                //this.nudTopMargin.Value = (decimal)pps.Margins.Top;
                //this.nudBottomMargin.Value = (decimal)pps.Margins.Bottom;
                //this.nudLeftMargin.Value = (decimal)pps.Margins.Left;
                //this.nudRightMargin.Value = (decimal)pps.Margins.Right;

                if (pps.Landscape)
                    btnHorizontal.FlatAppearance.BorderColor = Color.Blue;
                else
                    btnVertical.FlatAppearance.BorderColor = Color.Blue;

                //pnlPreview.Invalidate();
            }
            else
            {
                btnOK.Enabled = false;
            }
        }



        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            //逻辑校验
            if (nudWidth.Value > 1000 || nudWidth.Value < 10)
            {
                MessageBox.Show("纸张宽度应介于10到1500之间");
                return;
            }
            if (nudHeight.Value > 1000 || nudHeight.Value < 10)
            {
                MessageBox.Show("纸张高度应介于10到1200之间");
                return;
            }
            //if (nudLeftMargin.Value > 500 || nudRightMargin.Value > 50 || nudTopMargin.Value > 50 || nudBottomMargin.Value > 50)
            //{
            //    MessageBox.Show("边距应介于0到500之间");
            //    return;
            //}
            pps.PaperSize = (MyPaperSize)cmbPaperKind.SelectedItem;
            //pps.Margins = new PrintPageSettings.Margin((float)this.nudLeftMargin.Value, (float)this.nudRightMargin.Value, (float)this.nudTopMargin.Value, (float)this.nudBottomMargin.Value);
            pps.Landscape = btnHorizontal.FlatAppearance.BorderColor == Color.Blue ? true : false;
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void pnlPreview_Paint(object sender, PaintEventArgs e)
        {
            //总大小为纸张大小(毫米单位)的1/3
            int width = (int)(nudWidth.Value/3);
            int height = (int)(nudHeight.Value/3);
            if (btnHorizontal.FlatAppearance.BorderColor == Color.Blue)
            {
                int temp = width;
                width = height;
                height = temp;
            }
            int paperWidth = width;
            int paperHeight = height;

            //int topMargin = (int)(nudTopMargin.Value/3);
            //int bottomMargin = (int)(nudBottomMargin.Value/3);
            //int leftMargin = (int)(nudLeftMargin.Value/3);
            //int rightmargin = (int)(nudRightMargin.Value/3);

            //pnlPreview.Width = paperWidth;
            //pnlPreview.Height = paperHeight;
            Pen myPen = new Pen(Color.Red);

            //e.Graphics.DrawLine(myPen, leftMargin, topMargin, pnlPreview.Width - rightmargin - 3, topMargin);
            //e.Graphics.DrawLine(myPen, leftMargin, topMargin, leftMargin, pnlPreview.Height - bottomMargin - 3);
            //e.Graphics.DrawLine(myPen, leftMargin, pnlPreview.Height - bottomMargin - 3, pnlPreview.Width - rightmargin - 3, pnlPreview.Height - bottomMargin - 3);
            //e.Graphics.DrawLine(myPen, pnlPreview.Width - rightmargin - 3, topMargin, pnlPreview.Width - rightmargin - 3, pnlPreview.Height - bottomMargin - 3);
        }

        private void cmbPaperKind_SelectedIndexChanged(object sender, EventArgs e)
        {
            MyPaperSize selectedSize = cmbPaperKind.SelectedItem as MyPaperSize;
            if (selectedSize != null)
            {
                nudWidth.Value = selectedSize.Width;
                nudHeight.Value = selectedSize.Height;
            }
            //pnlPreview.Invalidate();
        }

        private void nudWidth_ValueChanged(object sender, EventArgs e)
        {
            //pnlPreview.Invalidate();
        }

        private void nudHeight_ValueChanged(object sender, EventArgs e)
        {
            //pnlPreview.Invalidate();
        }

        private void nudTopMargin_ValueChanged(object sender, EventArgs e)
        {
            //pnlPreview.Invalidate();
        }

        private void nudBottomMargin_ValueChanged(object sender, EventArgs e)
        {
            //pnlPreview.Invalidate();
        }

        private void nudLeftMargin_ValueChanged(object sender, EventArgs e)
        {
            //pnlPreview.Invalidate();
        }

        private void nudRightMargin_ValueChanged(object sender, EventArgs e)
        {
            //pnlPreview.Invalidate();
        }

        private void btnVertical_Click(object sender, EventArgs e)
        {
            if (btnVertical.FlatAppearance.BorderColor == Color.Gray)
            {
                btnVertical.FlatAppearance.BorderColor = Color.Blue;
                btnHorizontal.FlatAppearance.BorderColor = Color.Gray;
                //decimal temp = nudWidth.Value;
                //nudWidth.Value = nudHeight.Value;
                //nudHeight.Value = temp;
                //pnlPreview.Invalidate();
            }
        }

        private void btnHorizontal_Click(object sender, EventArgs e)
        {
            if (btnHorizontal.FlatAppearance.BorderColor == Color.Gray)
            {
                btnVertical.FlatAppearance.BorderColor = Color.Gray;
                btnHorizontal.FlatAppearance.BorderColor = Color.Blue;
                //decimal temp = nudWidth.Value;
                //nudWidth.Value = nudHeight.Value;
                //nudHeight.Value = temp;
                //pnlPreview.Invalidate();
            }
        }
    }

    public class PrintPageSettings
    {
        public PrintPageSettings()
        {
            PaperSize = new MyPaperSize(PaperKind.A4, 210, 297);
            Margins = new Margin();
        }
        public PrintPageSettings(MyPaperSize paperSize)
        {
            PaperSize = paperSize;
            Margins = new Margin();
        }
        public PrintPageSettings(PaperKind kind, bool landscape)
        {
            MyPaperSize ps = PaperSizeCollection.StdInstance[kind];
            PaperSize = ps;
            Margins = new Margin();
            Landscape = landscape;
        }
        public MyPaperSize PaperSize { get; set; }
        public bool Landscape { get; set; } = false;
        public Margin Margins { get; set; }

        public class Margin
        {
            public Margin()
            { }
            public Margin(float left, float right, float top, float bottom)
            {
                Left = left;
                Right = right;
                Top = top;
                Bottom = bottom;
            }
            public float Left { get; set; } = 3.18f;
            public float Right{ get; set; } = 3.18f;
            public float Top { get; set; } = 2.54f;
            public float Bottom { get; set; } = 2.54f;
        };
    }
}
