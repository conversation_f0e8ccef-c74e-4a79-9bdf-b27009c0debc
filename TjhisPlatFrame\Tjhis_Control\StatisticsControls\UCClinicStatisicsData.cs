﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Tjhis.Controls.StatisticsControls
{
    public class UCClinicStatisicsData
    {
        public UCClinicStatisicsData()
        {
            Cliniced = new Cliniced();
            WaitClinic = new UnClinic();
            WaitAvgTime = new AvgTime();
            TreatAvgTime = new AvgTime();
        }
        /// <summary>
        /// 已诊人数
        /// </summary>
        public Cliniced Cliniced { get; set; }
        /// <summary>
        /// 未接诊人次
        /// </summary>
        public UnClinic WaitClinic { get; set; }
        /// <summary>
        /// 平均候诊时间
        /// </summary>
        public AvgTime WaitAvgTime { get; set; }
        /// <summary>
        /// 平均治疗时间
        /// </summary>
        public AvgTime TreatAvgTime { get; set; }
        public static UCClinicStatisicsData CreateTestData()
        {
            UCClinicStatisicsData data = new UCClinicStatisicsData();
            data.Cliniced.Sum = 1000;
            data.Cliniced.firstVisit = 800;
            data.Cliniced.visited = 200;
            data.Cliniced.other = 0;

            data.WaitClinic.Sum = 200;
            data.WaitClinic.WaitClinic = 100;
            data.WaitClinic.Other = 0;

            data.TreatAvgTime.Sum = 13;
            data.TreatAvgTime.Yesterday = 11;
            data.TreatAvgTime.CompareToYesterday = -1;

            data.WaitAvgTime.Yesterday = 20;
            data.WaitAvgTime.Sum = 21;
            data.WaitAvgTime.CompareToYesterday = -1;

            return data;
        }

    }

    /// <summary>
    /// 已诊人次
    /// </summary>
    public class Cliniced
    {
        /// <summary>
        /// 已诊人次
        /// </summary>
        public int Sum { get; set; }
        /// <summary>
        /// 初诊人次
        /// </summary>
        public int firstVisit { get; set; }
        /// <summary>
        /// 复诊人次
        /// </summary>
        public int visited { get; set; }
        /// <summary>
        /// 其他
        /// </summary>
        public int other { get; set; }
    }

    public class UnClinic
    {
        public int Sum { get; set; }
        public int WaitClinic { get; set; }
        public int Other { get; set; }
    }

    public class AvgTime
    {
        public int Sum { get; set; }
        public int Yesterday { get; set; }

        public int CompareToYesterday { get; set; }
    }

}
