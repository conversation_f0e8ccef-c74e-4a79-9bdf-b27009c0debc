﻿/*********************************************
* 文 件 名：Cs02MessageBox
* 类 名 称：Cs02MessageBox
* 功能说明：显示信息类
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：刘成刚
* 创建时间：2019-05-18 19:41:55
* 版 本 号：1.0.0.1
* 修 改 人：刘成刚
* 修改时间：2020-02-18
* CLR 版本：4.0.30319.42000
/*********************************************/
using System;
using System.Windows.Forms;
using System.Drawing;
using DevExpress.XtraEditors;
using DevExpress.Utils;
using DevExpress.XtraSplashScreen;

namespace PlatCommon.Base02
{
    /// <summary>
    /// 显示信息类
    /// </summary>
    public class Cs02MessageBox
    {
        /// <summary>
        /// DEV 的提示信息控件
        /// </summary>
        private static ToolTipController _toolTip;

        /// <summary>
        /// 显示信息
        /// </summary>
        /// <param name="strInfoMsg">信息内容</param>
        /// <param name="strTitle">信息标题</param>
        public static void ShowInfo(string strInfoMsg, string strTitle = "系统提示")
        {
            CloseWait();
            XtraMessageBox.Show(strInfoMsg, strTitle, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 显示错误
        /// </summary>
        /// <param name="strErrorMsg">错误内容</param>
        /// <param name="strTitle">错误标题</param>
        public static void ShowError(string strErrorMsg, string strTitle = "系统提示")
        {
            CloseWait();
            XtraMessageBox.Show(strErrorMsg, strTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        /// <summary>
        /// 显示警告
        /// </summary>
        /// <param name="strErrorMsg">警告内容</param>
        /// <param name="strTitle">警告标题</param>
        public static void ShowWarning(string strErrorMsg, string strTitle = "系统提示")
        {
            CloseWait();
            XtraMessageBox.Show(strErrorMsg, strTitle, MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        /// <summary>
        /// 显示问题
        /// </summary>
        /// <param name="strErrorMsg">提示内容</param>
        /// <param name="strTitle">标题</param>
        /// <param name="iDefaultButton">缺省按钮</param>
        /// <returns></returns>
        public static DialogResult ShowYesNo(string strErrorMsg, string strTitle = "系统提示", int iDefaultButton = 1)
        {
            if (iDefaultButton == 1)
                return XtraMessageBox.Show(strErrorMsg, strTitle, MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
            else
                return XtraMessageBox.Show(strErrorMsg, strTitle, MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2);
        }

        /// <summary>
        /// 显示问题
        /// </summary>
        /// <param name="strErrorMsg">提示内容</param>
        /// <param name="strTitle">标题</param>
        /// <param name="iDefaultButton">缺省按钮</param>
        /// <returns></returns>
        public static DialogResult ShowYesNoCancel(String strErrorMsg, string strTitle = "系统提示", int iDefaultButton = 1)
        {
            if (iDefaultButton == 1)
                return XtraMessageBox.Show(strErrorMsg, strTitle, MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
            else if (iDefaultButton == 2)
                return XtraMessageBox.Show(strErrorMsg, strTitle, MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2);
            else
                return XtraMessageBox.Show(strErrorMsg, strTitle, MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button3);
        }

        /// <summary>
        /// 自动关闭的提示框
        /// </summary>
        /// <param name="strErrorMsg">信息</param>
        /// <param name="strTitle">标题</param>
        /// <param name="iDelay">延迟秒数</param>
        public static void ShowAutoClose(String strErrorMsg, int iDelay = 3, string strTitle = "系统提示")
        {
            XtraMessageBoxArgs args = new XtraMessageBoxArgs();
            args.AutoCloseOptions.Delay = iDelay * 1000;
            args.Caption = strTitle;
            args.Text = strErrorMsg;
            args.Buttons = new DialogResult[] { DialogResult.OK };
            XtraMessageBox.Show(args);
        }

        /// <summary>
        /// 显示提示信息
        /// </summary>
        /// <param name="strContent">提示内容</param>
        /// <param name="iDelay">显示时间</param>
        public static void ShowToolTip(string strContent, int iDelay = 2000)
        {
            ShowToolTip("系统提示：", strContent, iDelay);
        }

        /// <summary>
        /// 显示提示信息
        /// </summary>
        /// <param name="strTitle">提示标题</param>
        /// <param name="strContent">提示内容</param>
        /// <param name="iDelay">显示时间</param>
        public static void ShowToolTip(string strTitle, string strContent, int iDelay = 2000)
        {
            Point _mousePoint = Control.MousePosition;
            if (_toolTip == null)
                _toolTip = new ToolTipController();
            _toolTip.AutoPopDelay = iDelay;
            _toolTip.ShowHint(strContent, strTitle, _mousePoint);
        }



        /// <summary>
        /// 关闭等待提示
        /// </summary>
        public static void CloseWait()
        {
            try
            {
                SplashScreenManager.CloseDefaultWaitForm();
            }
            catch (Exception)
            {
            }
        }

    }
}
