﻿/*********************************************
* 文 件 名：Cs02ExtendDev
* 类 名 称：Cs02ExtendDev
* 功能说明：DEV GridControl扩展
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：刘成刚
* 创建时间：2019-05-18 19:41:55
* 版 本 号：1.0.0.1
* 修 改 人：刘成刚
* 修改时间：2020-02-18
* CLR 版本：4.0.30319.42000
/*********************************************/

using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using System.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.XtraPrinting;
using BorderSide = DevExpress.XtraPrinting.BorderSide;
using DevExpress.Utils;
using PlatCommon.Base01;

namespace PlatCommon.Base02
{
    /// <summary>
    /// DEV GridControl扩展
    /// </summary>
    public static class Cs02ExtendDev
    {
        /// <summary>
        /// 设置Grid的标题
        /// <param name="gridView">目标gridView</param>
        /// <param name="strTitle">标题</param>
        /// </summary>
        public static void SetGridTitle(GridView gridView, string strTitle)
        {
            gridView.GroupPanelText = strTitle;
            gridView.Appearance.GroupPanel.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point, ((byte)(0)));
            gridView.OptionsView.ShowGroupPanel = true;
            gridView.OptionsMenu.EnableGroupPanelMenu = false;
            gridView.Appearance.GroupPanel.ForeColor = System.Drawing.Color.Red;
            gridView.Appearance.GroupPanel.Options.UseForeColor = true;
            gridView.Appearance.GroupPanel.Options.UseTextOptions = true;
            gridView.Appearance.GroupPanel.TextOptions.HAlignment = HorzAlignment.Center;
        }

        /// <summary>
        /// 设置汇总行的字体和颜色
        /// <param name="gridView">目标gridView</param>
        /// </summary>
        public static void SetGridFooter(GridView gridView)
        {
            gridView.OptionsView.ShowFooter = true;
            gridView.Appearance.FooterPanel.Options.UseBackColor = true;
            gridView.Appearance.FooterPanel.BackColor2 = Color.Empty;
            gridView.Appearance.FooterPanel.BackColor = Color.Green;
            gridView.Appearance.FooterPanel.Options.UseForeColor = true;
            gridView.Appearance.FooterPanel.ForeColor = Color.Blue;
            gridView.Appearance.FooterPanel.Options.UseFont = true;
            gridView.Appearance.FooterPanel.Font = new Font("宋体", 10, FontStyle.Bold);
        }

        /// <summary>
        /// 设置分组行字体和颜色
        /// <param name="gridView">目标gridView</param>
        /// </summary>
        public static void SetGridGroupRow(GridView gridView)
        {
            gridView.Appearance.GroupRow.Options.UseFont = true;
            gridView.Appearance.GroupRow.Font = new Font("宋体", 12, FontStyle.Bold); ;

            gridView.Appearance.GroupRow.Options.UseForeColor = true;
            gridView.Appearance.GroupRow.ForeColor = Color.Green;
        }

        /// <summary>
        ///设置为行多选
        /// <param name="gridView">目标gridView</param>
        /// </summary>
        public static void SetGridMultiSelect(GridView gridView)
        {
            gridView.OptionsSelection.MultiSelect = true;
            gridView.OptionsSelection.MultiSelectMode = GridMultiSelectMode.CheckBoxRowSelect;
            gridView.OptionsSelection.CheckBoxSelectorColumnWidth = 40;
        }

        /// <summary>
        /// 设置Grid的显示状态时的属性值
        /// </summary>
        /// <param name="gridView">目标gridView</param>
        public static void SetGridStatus(GridView gridView)
        {
            //不显示分组
            gridView.OptionsView.ShowGroupPanel = false;
            //不显示过滤信息
            gridView.OptionsView.ShowFilterPanelMode = ShowFilterPanelMode.Never;

            //设置为只读
            gridView.OptionsBehavior.ReadOnly = true;
            gridView.OptionsBehavior.Editable = false;

            gridView.OptionsBehavior.AllowAddRows = DefaultBoolean.False;
            gridView.OptionsBehavior.AllowDeleteRows = DefaultBoolean.False;

            //设置空白区域
            //gridView.Appearance.Empty.Options.UseBackColor = true;
            //gridView.Appearance.Empty.BackColor = Color.Silver;

            //设置为单行选中
            gridView.OptionsBehavior.EditorShowMode = EditorShowMode.Click;
            gridView.OptionsSelection.EnableAppearanceFocusedCell = false;
            //gridView.OptionsSelection.EnableAppearanceFocusedRow = true;
            //gridView.OptionsSelection.EnableAppearanceHideSelection = true;

            //设置当前行的背景色，前景色
            SetFocusedRowColor(gridView);

            //选中行的设置
            SetSelectedRowColor(gridView);

            //设置奇偶数行背景色不同
            SetRowColorChange(gridView, true);

            ////列头背景色，前景色
            //gridControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            //gridControl1.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat;

            //gridView1.Appearance.HeaderPanel.Options.UseBackColor = true;
            //gridView1.Appearance.HeaderPanel.BackColor = Color.LightSkyBlue;

            //gridView1.Appearance.HeaderPanel.Options.UseForeColor = true;
            //gridView1.Appearance.HeaderPanel.ForeColor = Color.Black;

            //列头字体
            gridView.Appearance.HeaderPanel.Options.UseFont = true;
            gridView.Appearance.HeaderPanel.Font = new Font("宋体", 12, FontStyle.Bold);

            //列头居中
            gridView.Appearance.HeaderPanel.Options.UseTextOptions = true;
            gridView.Appearance.HeaderPanel.TextOptions.HAlignment = HorzAlignment.Center;

            //关闭列宽自动
            gridView.OptionsView.ColumnAutoWidth = false;
            //设置自动高度
            gridView.OptionsView.RowAutoHeight = false;
        }

        /// <summary>
        /// 设置Grid的编辑状态时的属性值
        /// </summary>
        /// <param name="gridView">目标gridView</param>
        public static void SetGridStatusEdit(GridView gridView)
        {
            //不显示分组
            gridView.OptionsView.ShowGroupPanel = false;
            //不显示过滤信息
            gridView.OptionsView.ShowFilterPanelMode = ShowFilterPanelMode.Never;

            gridView.OptionsBehavior.AllowAddRows = DefaultBoolean.False;
            gridView.OptionsBehavior.AllowDeleteRows = DefaultBoolean.False;

            //新加行，位于最后
            //gridView.OptionsView.NewItemRowPosition = NewItemRowPosition.Bottom;
            //设置空白区域
            //gridView.Appearance.Empty.Options.UseBackColor = true;
            //gridView.Appearance.Empty.BackColor = Color.Silver;

            //设置为单行选中
            //gridView.OptionsBehavior.EditorShowMode = EditorShowMode.Click;
            //gridView.OptionsSelection.EnableAppearanceFocusedCell = false;
            //gridView.OptionsSelection.EnableAppearanceFocusedRow = true;
            //gridView.OptionsSelection.EnableAppearanceHideSelection = true;

            //设置当前行的背景色，前景色
            SetFocusedRowColor(gridView);

            //选中行的设置
            SetSelectedRowColor(gridView);

            //设置奇偶数行背景色不同
            SetRowColorChange(gridView, true);

            ////列头背景色，前景色
            //gridControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            //gridControl1.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat;

            //gridView1.Appearance.HeaderPanel.Options.UseBackColor = true;
            //gridView1.Appearance.HeaderPanel.BackColor = Color.LightSkyBlue;

            //gridView1.Appearance.HeaderPanel.Options.UseForeColor = true;
            //gridView1.Appearance.HeaderPanel.ForeColor = Color.Black;

            //列头字体
            gridView.Appearance.HeaderPanel.Options.UseFont = true;
            gridView.Appearance.HeaderPanel.Font = new Font("宋体", 12, FontStyle.Bold);
            //列头居中
            gridView.Appearance.HeaderPanel.Options.UseTextOptions = true;
            gridView.Appearance.HeaderPanel.TextOptions.HAlignment = HorzAlignment.Center;

            //关闭列宽自动
            gridView.OptionsView.ColumnAutoWidth = false;
            //设置自动高度
            gridView.OptionsView.RowAutoHeight = false;
        }

        /// <summary>
        /// 设置Grid的选择行背景
        /// </summary>
        /// <param name="gridView">目标gridView</param>
        /// <param name="bFlag">是否设置</param>
        public static void SetSelectRowColor(GridView gridView, Boolean bFlag = true)
        {
            if (bFlag)
            {
                gridView.CustomDrawCell += GridView_CustomDrawCell;
            }
            else
            {
                gridView.CustomDrawCell -= GridView_CustomDrawCell;
            }
        }

        /// <summary>
        /// 设置选择行背景蓝色，前景白色
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private static void GridView_CustomDrawCell(object sender, RowCellCustomDrawEventArgs e)
        {
            GridView gv = (sender as GridView);
            int[] iArrRowHandles = gv.GetSelectedRows();
            if (Array.IndexOf(iArrRowHandles, e.RowHandle) != -1)
            {
                e.Appearance.BackColor = Color.Blue;
                e.Appearance.ForeColor = Color.White;
            }
        }

        /// <summary>
        /// 设置显示行号
        /// </summary>
        /// <param name="gridView">目标gridView</param>
        /// <param name="strCaption">显示标题，缺省为“序号”</param>
        /// <param name="iWidth">显示宽度，缺省为60</param>
        public static void ShowRowIndicator(GridView gridView, string strCaption = "序号", int iWidth = 60)
        {
            string strTag = gridView.Tag?.ToString();
            //strCaption==序号时，加到Tag后面。否则不管它。在取值的时候，进行处理
            if (strCaption != "序号")
                gridView.Tag = strTag + ";Tag=" + strCaption;

            gridView.OptionsView.ShowIndicator = true;
            gridView.IndicatorWidth = iWidth;
            gridView.CustomDrawRowIndicator += DrawRowIndicator;
        }

        /// <summary>
        /// 行号指示
        /// </summary>
        /// <param name="sender">object</param>
        /// <param name="e">RowIndicatorCustomDrawEventArgs</param>
        private static void DrawRowIndicator(object sender, RowIndicatorCustomDrawEventArgs e)
        {
            if (e.Info.IsRowIndicator)
            {
                //行号
                if (e.RowHandle >= 0)
                    e.Info.DisplayText = (e.RowHandle + 1).ToString();
            }
            else
            {
                //行号列标题
                string strCaption = string.Empty;
                string strTag = ((GridView)sender).Tag?.ToString();
                //Tag不为空，从中分解。为空时，直接就是“序号”
                if (!string.IsNullOrEmpty(strTag))
                {
                    int iPostion = strTag.LastIndexOf("Tag=");
                    if (iPostion >= 0)
                        strCaption = strTag.Substring(iPostion + 4);
                }

                if (string.IsNullOrEmpty(strCaption))
                    strCaption = "序号";

                e.Info.DisplayText = strCaption;
            }
            e.Appearance.TextOptions.HAlignment = HorzAlignment.Center; //对齐方式
        }

        /// <summary>
        /// 设置奇数行背景色不同
        /// </summary>
        /// <param name="gridView">目标gridView</param>
        /// <param name="bChange">是否显示不同</param>
        public static void SetRowColorChange(GridView gridView, Boolean bChange)
        {
            gridView.OptionsView.EnableAppearanceEvenRow = bChange;
            gridView.OptionsView.EnableAppearanceOddRow = bChange;
        }

        /// <summary>
        /// 设置当前行的背景色，前景色
        /// </summary>
        /// <param name="gridView">目标gridView</param>
        public static void SetFocusedRowColor(GridView gridView)
        {
            gridView.Appearance.FocusedRow.Options.UseBackColor = true;
            gridView.Appearance.FocusedRow.BackColor = Color.Blue;

            gridView.Appearance.FocusedRow.Options.UseForeColor = true;
            gridView.Appearance.FocusedRow.ForeColor = Color.White;
        }

        /// <summary>
        /// 设置选中行的背景色，前景色
        /// </summary>
        /// <param name="gridView">目标gridView</param>
        public static void SetSelectedRowColor(GridView gridView)
        {
            gridView.Appearance.SelectedRow.Options.UseBackColor = true;
            gridView.Appearance.SelectedRow.Options.UseForeColor = true;

            gridView.Appearance.SelectedRow.BackColor = Color.Blue;
            gridView.Appearance.SelectedRow.ForeColor = Color.White;
        }

        /// <summary>
        /// 设置下拉选择框的数据
        /// <param name="lookUpEdit">下拉选择框</param>
        /// <param name="dtSource">显示数据源</param>
        /// <param name="strDisplay">显示字段</param>
        /// <param name="strValue">值字段</param>
        /// <param name="bShowAll">是否有全部</param>
        /// <param name="strNullText">Null显示</param>
        /// </summary> 
        public static void SetLoopUpEdit(LookUpEdit lookUpEdit, DataTable dtSource, string strDisplay, string strValue, Boolean bShowAll = false, string strNullText = "请选择...")
        {
            //进行参数分析
            if (strDisplay.Length < 1 || strValue.Length < 1) return;
            string[] strArrDisplay = strDisplay.Split(',');
            string[] strArrValue = strValue.Split(',');
            string strDisplayColumn = strArrDisplay[0];
            string strValueColumn = strArrValue[0];

            //加入全部
            if (bShowAll)
            {
                DataTable dtInculdAll = dtSource.Copy();
                DataRow drAdd = dtInculdAll.NewRow();
                drAdd[strDisplayColumn] = "全部";
                drAdd[strValueColumn] = "%";
                dtInculdAll.Rows.InsertAt(drAdd, 0);
                lookUpEdit.Properties.DataSource = dtInculdAll;
            }
            else
                lookUpEdit.Properties.DataSource = dtSource;

            //下拉框进行设置
            lookUpEdit.Properties.DisplayMember = strDisplayColumn;
            lookUpEdit.Properties.ValueMember = strValueColumn;
            lookUpEdit.Properties.NullText = strNullText;
            lookUpEdit.Properties.DropDownItemHeight = 20;
            lookUpEdit.Properties.ShowFooter = false;

            //显示列和值列不相同，就要显示下拉表的头，此时进行处理
            if (strDisplayColumn != strValueColumn)
            {
                string strDisplayHead = String.Empty;
                string strValueHead = String.Empty;

                if (strArrDisplay.Length > 1) strDisplayHead = strArrDisplay[1];
                if (strArrValue.Length > 1) strValueHead = strArrValue[1];

                lookUpEdit.Properties.ShowHeader = true;
                lookUpEdit.Properties.Columns.AddRange(new LookUpColumnInfo[] {
                    new LookUpColumnInfo(strValueColumn, strValueHead),
                    new LookUpColumnInfo(strDisplayColumn,strDisplayHead),
                    });
            }
            else
            {
                lookUpEdit.Properties.ShowHeader = false;
            }

            //自动选择第一行
            if (dtSource.Rows.Count > 0)
            {
                if (bShowAll)
                    lookUpEdit.EditValue = "%";
                else
                    lookUpEdit.EditValue = dtSource.Rows[0][strValueColumn].ToString();
            }
            return;
        }

        /// <summary>
        /// 设置下拉选择框的数据
        /// <param name="lookUpEdit">下拉选择框</param>
        /// <param name="dictData">显示数据源</param>
        /// <param name="bShowAll">是否有全部</param>
        /// <param name="strNullText">Null显示</param>
        /// </summary> 
        public static void SetLoopUpEdit(LookUpEdit lookUpEdit, Dictionary<string, string> dictData, Boolean bShowAll = false, string strNullText = "请选择...")
        {
            //加入全部
            if (bShowAll)
                dictData.Add("%", "全部");

            lookUpEdit.Properties.DataSource = dictData;

            //下拉框进行设置
            //lookUpEdit.Properties.DisplayMember = strDisplayColumn;
            //lookUpEdit.Properties.ValueMember = strValueColumn;
            lookUpEdit.Properties.NullText = strNullText;
            lookUpEdit.Properties.DropDownItemHeight = 20;
            lookUpEdit.Properties.ShowFooter = false;

            //显示列和值列不相同，就要显示下拉表的头，此时进行处理
            //if (strDisplayColumn != strValueColumn)
            //{
            //    string strDisplayHead = String.Empty;
            //    string strValueHead = String.Empty;

            //    if (strArrDisplay.Length > 1) strDisplayHead = strArrDisplay[1];
            //    if (strArrValue.Length > 1) strValueHead = strArrValue[1];

            //    lookUpEdit.Properties.ShowHeader = true;
            //    lookUpEdit.Properties.Columns.AddRange(new LookUpColumnInfo[] {
            //        new LookUpColumnInfo(strValueColumn, strValueHead),
            //        new LookUpColumnInfo(strDisplayColumn,strDisplayHead),
            //        });
            //}
            //else
            //{
            lookUpEdit.Properties.ShowHeader = false;
            //}

            //自动选择第一行
            if (dictData.Count > 0)
            {
                if (bShowAll)
                    lookUpEdit.EditValue = "%";
                else
                    lookUpEdit.EditValue = dictData.Keys.First();
            }
            return;
        }

        /// <summary>
        /// 设置查找下拉输入，无列头定义
        /// </summary>
        /// <param name="searchLookUpEdit">查找下拉控件</param>
        /// <param name="dtData">数据源</param>
        /// <param name="strValueColumn">值列</param>
        /// <param name="strDisColumn">显示列</param>
        /// <param name="bShowAll">是否有全部</param>
        /// <param name="strNullText">Null显示</param>
        public static void SetSearchLookUp(SearchLookUpEdit searchLookUpEdit, DataTable dtData, string strValueColumn, string strDisColumn, Boolean bShowAll = false, string strNullText = "请选择...")
        {
            SetSearchLookUp(searchLookUpEdit, dtData, strValueColumn, strDisColumn, null, bShowAll, strNullText);
        }

        /// <summary>
        /// 设置查找下拉输入，有列头定义
        /// </summary>
        /// <param name="searchLookUpEdit">查找下拉控件</param>
        /// <param name="dtData">数据源</param>
        /// <param name="strValueColumn">值列</param>
        /// <param name="strDisColumn">显示列</param>
        /// <param name="dtColFormat">列头定义表</param>
        /// <param name="bShowAll">是否有全部</param>
        /// <param name="strNullText">Null显示</param>
        public static void SetSearchLookUp(SearchLookUpEdit searchLookUpEdit, DataTable dtData, string strValueColumn, string strDisColumn, DataTable dtColFormat, Boolean bShowAll = false, string strNullText = "请选择...")
        {
            DataTable dtShow = dtData.Copy();

            //加入全部
            if (bShowAll)
            {
                DataRow drNewAll = dtShow.NewRow();
                drNewAll[strDisColumn] = "全部";
                drNewAll[strValueColumn] = "%";
                dtShow.Rows.InsertAt(drNewAll, 0);
                searchLookUpEdit.EditValue = "%";
            }

            searchLookUpEdit.Properties.DataSource = dtShow;
            searchLookUpEdit.Properties.DisplayMember = strDisColumn;
            searchLookUpEdit.Properties.ValueMember = strValueColumn;
            searchLookUpEdit.Properties.NullText = strNullText;
            searchLookUpEdit.Properties.ShowFooter = false;

            //如果有全部，则不能有清除
            searchLookUpEdit.Properties.ShowClearButton = !bShowAll;

            //奇偶行
            searchLookUpEdit.Properties.View.OptionsView.EnableAppearanceEvenRow = true;
            searchLookUpEdit.Properties.View.OptionsView.EnableAppearanceOddRow = true;

            //设置显示的表头
            searchLookUpEdit.BeforePopup += (sender, e) => SetsearchLookUpEditColumn(sender, e, dtColFormat);

            //选择数据
            //searchLookUpEdit.Properties.Closed += SelectData;
        }

        /// <summary>
        /// 设置GridView列的字体颜色
        /// </summary>
        /// <param name="gridView">控件</param>
        /// <param name="strExpression">表达式</param>
        /// <param name="strColumnName">列名</param>
        /// <param name="color">颜色</param>
        /// <param name="bWholeRow">整行</param>
        public static void SetColumnForeColor(GridView gridView, string strExpression, string strColumnName, Color color, Boolean bWholeRow = false)
        {
            if (gridView == null || string.IsNullOrEmpty(strColumnName)) return;
            if (!ExistField(gridView, strColumnName)) return;

            FormatConditionRuleExpression fcrExpFore = new FormatConditionRuleExpression();
            fcrExpFore.PredefinedName = "fcrExpFore";
            fcrExpFore.Appearance.ForeColor = color;
            fcrExpFore.Appearance.Options.UseForeColor = true;
            fcrExpFore.Expression = strExpression;

            int iFormatRules = gridView.FormatRules.Count;
            GridFormatRule gridFormatRuleFore = new GridFormatRule();
            gridFormatRuleFore.Column = gridView.Columns[strColumnName];
            gridFormatRuleFore.ColumnApplyTo = gridView.Columns[strColumnName];
            gridFormatRuleFore.ApplyToRow = bWholeRow;
            gridFormatRuleFore.Name = "gridFormatRuleFore" + iFormatRules.ToString();
            gridFormatRuleFore.Rule = fcrExpFore;
            gridView.FormatRules.Add(gridFormatRuleFore);
        }

        /// <summary>
        /// 设置GridView列的背景颜色
        /// </summary>
        /// <param name="gridView">控件</param>
        /// <param name="strExpression">表达式</param>
        /// <param name="strColumnName">列名</param>
        /// <param name="color">颜色</param>
        /// <param name="bWholeRow">整行</param>
        public static void SetColumnBackColor(GridView gridView, string strExpression, string strColumnName, Color color, Boolean bWholeRow = false)
        {
            if (gridView == null || string.IsNullOrEmpty(strColumnName)) return;
            if (!ExistField(gridView, strColumnName)) return;

            FormatConditionRuleExpression fcrExpBack = new FormatConditionRuleExpression();
            fcrExpBack.Appearance.BackColor = color;
            fcrExpBack.Appearance.Options.UseBackColor = true;
            fcrExpBack.Expression = strExpression;

            int iFormatRules = gridView.FormatRules.Count;
            GridFormatRule gridFormatRuleBack = new GridFormatRule();
            gridFormatRuleBack.Column = gridView.Columns[strColumnName];
            gridFormatRuleBack.ColumnApplyTo = gridView.Columns[strColumnName];
            gridFormatRuleBack.ApplyToRow = bWholeRow;
            gridFormatRuleBack.Name = "gridFormatRuleBack" + iFormatRules.ToString();
            gridFormatRuleBack.Rule = fcrExpBack;
            gridView.FormatRules.Add(gridFormatRuleBack);
        }

        /// <summary>
        /// 设置列最大输入长度
        /// </summary>
        /// <param name="gridView">控件</param>
        /// <param name="strColumnName">列名</param>
        /// <param name="iMaxLength">最大长度</param>
        public static void SetColumnMaxLength(GridView gridView, string strColumnName, int iMaxLength)
        {
            if (gridView == null || string.IsNullOrEmpty(strColumnName)) return;
            if (!ExistField(gridView, strColumnName)) return;

            int iCount = gridView.GridControl.RepositoryItems.Count;

            RepositoryItemTextEdit repositoryItemTxtEdit = new RepositoryItemTextEdit();
            repositoryItemTxtEdit.MaxLength = iMaxLength;
            repositoryItemTxtEdit.Name = "repositoryItemTxtEdit" + iCount.ToString();

            gridView.GridControl.RepositoryItems.Add(repositoryItemTxtEdit);
            gridView.Columns[strColumnName].ColumnEdit = repositoryItemTxtEdit;
        }

        /// <summary>
        /// 设置列为CheckBox输入框
        /// </summary>
        /// <param name="gridView">GridView控件</param>
        /// <param name="strColumnName">列名</param>
        /// <param name="objCheckedValue">选中的值</param>
        /// <param name="objUnCheckedValue">非选中的值</param>
        public static void SetColumnEditCheckBox(GridView gridView, string strColumnName, object objCheckedValue, object objUnCheckedValue)
        {
            if (gridView == null || string.IsNullOrEmpty(strColumnName)) return;
            if (!ExistField(gridView, strColumnName)) return;

            int iCount = gridView.GridControl.RepositoryItems.Count;
            RepositoryItemCheckEdit ItemCheckEdit = new RepositoryItemCheckEdit();

            ItemCheckEdit.Name = "ItemCheckEdit" + iCount.ToString();
            ItemCheckEdit.ValueChecked = objCheckedValue;
            ItemCheckEdit.ValueUnchecked = objUnCheckedValue;

            gridView.GridControl.RepositoryItems.Add(ItemCheckEdit);
            gridView.Columns[strColumnName].ColumnEdit = ItemCheckEdit;
        }

        /// <summary>
        /// 设置列为日期时间编辑
        /// </summary>
        /// <param name="gridView">控件</param>
        /// <param name="strColumnName">列名</param>
        public static void SetColumnEditDateTime(GridView gridView, string strColumnName)
        {
            if (gridView == null || string.IsNullOrEmpty(strColumnName)) return;
            if (!ExistField(gridView, strColumnName)) return;

            int iCount = gridView.GridControl.RepositoryItems.Count;
            RepositoryItemDateEdit ItemDateEdit = new RepositoryItemDateEdit();

            ItemDateEdit.Name = "ItemDateEdit" + iCount.ToString();
            ItemDateEdit.VistaDisplayMode = DefaultBoolean.True;
            ItemDateEdit.VistaEditTime = DefaultBoolean.True;
            ItemDateEdit.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            ItemDateEdit.DisplayFormat.FormatType = FormatType.DateTime;
            ItemDateEdit.EditFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            ItemDateEdit.EditFormat.FormatType = FormatType.DateTime;
            ItemDateEdit.Mask.EditMask = "yyyy-MM-dd HH:mm:ss";

            gridView.GridControl.RepositoryItems.Add(ItemDateEdit);
            gridView.Columns[strColumnName].ColumnEdit = ItemDateEdit;
        }

        /// <summary>
        /// 设置列为日期编辑
        /// </summary>
        /// <param name="gridView">控件</param>
        /// <param name="strColumnName">列名</param>
        public static void SetColumnEditDate(GridView gridView, string strColumnName)
        {
            if (gridView == null || string.IsNullOrEmpty(strColumnName)) return;
            if (!ExistField(gridView, strColumnName)) return;

            int iCount = gridView.GridControl.RepositoryItems.Count;
            RepositoryItemDateEdit ItemDateEdit = new RepositoryItemDateEdit();

            ItemDateEdit.Name = "ItemDateEdit" + iCount.ToString();
            ItemDateEdit.VistaDisplayMode = DefaultBoolean.True;
            ItemDateEdit.VistaEditTime = DefaultBoolean.True;
            ItemDateEdit.DisplayFormat.FormatString = "yyyy-MM-dd";
            ItemDateEdit.DisplayFormat.FormatType = FormatType.DateTime;
            ItemDateEdit.EditFormat.FormatString = "yyyy-MM-dd";
            ItemDateEdit.EditFormat.FormatType = FormatType.DateTime;
            ItemDateEdit.Mask.EditMask = "yyyy-MM-dd";

            gridView.GridControl.RepositoryItems.Add(ItemDateEdit);
            gridView.Columns[strColumnName].ColumnEdit = ItemDateEdit;
        }

        /// <summary>
        /// 设置列数字输入
        /// </summary>
        /// <param name="gridView">控件</param>
        /// <param name="strColumnName">列名</param>
        /// <param name="iPrecision">小数位数</param>
        /// <param name="iLength">整数位数</param>
        public static void SetColumnEditNumberic(GridView gridView, string strColumnName, int iPrecision, int iLength = 8)
        {
            if (gridView == null || string.IsNullOrEmpty(strColumnName)) return;
            if (!ExistField(gridView, strColumnName)) return;

            if ((iLength < 1) && (iPrecision < 1)) return;

            string strFormatStr = string.Empty.PadRight(iLength, '#');

            if (iPrecision > 0)
                strFormatStr = (strFormatStr + "0.") + string.Empty.PadRight(iPrecision, '0');

            int iCount = gridView.GridControl.RepositoryItems.Count;
            RepositoryItemTextEdit repositoryItemTxtEdit = new RepositoryItemTextEdit();
            repositoryItemTxtEdit.Name = "repositoryItemTxtEdit" + iCount.ToString();
            //编辑风格
            repositoryItemTxtEdit.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            repositoryItemTxtEdit.Mask.EditMask = strFormatStr;
            //显示风格
            repositoryItemTxtEdit.DisplayFormat.FormatType = FormatType.Numeric;
            repositoryItemTxtEdit.DisplayFormat.FormatString = "N" + iPrecision.ToString();

            //禁用滚轮
            repositoryItemTxtEdit.AllowMouseWheel = false;

            gridView.GridControl.RepositoryItems.Add(repositoryItemTxtEdit);
            gridView.Columns[strColumnName].ColumnEdit = repositoryItemTxtEdit;
        }

        /// <summary>
        /// 设置列为下拉选择
        /// </summary>
        /// <param name="gridView">控件</param>
        /// <param name="strFieldName">列名</param>
        /// <param name="dtData">数据源</param>
        /// <param name="strValueColumn">数据源列--值</param>
        /// <param name="strDisColumn">数据源列--显示</param>
        /// <param name="iWidth">显示宽度</param>
        /// <param name="bShowAll">是否有全部</param>
        /// <param name="strNullText">Null显示</param>
        public static void SetColumnEditLookUp(GridView gridView, string strFieldName, DataTable dtData, string strValueColumn, string strDisColumn, int iWidth = 0, Boolean bShowAll = false, string strNullText = "请选择...")
        {
            if (gridView == null || string.IsNullOrEmpty(strFieldName)) return;
            if (!ExistField(gridView, strFieldName)) return;

            int iCount = gridView.GridControl.RepositoryItems.Count;
            RepositoryItemLookUpEdit lookUpEdit = new RepositoryItemLookUpEdit();

            SetLoopUpEdit(lookUpEdit, dtData, strDisColumn, strValueColumn, bShowAll, strNullText);
            lookUpEdit.AcceptEditorTextAsNewValue = DefaultBoolean.True;
            lookUpEdit.Name = "searchLookUpEdit" + iCount.ToString();
            if (iWidth > 0)
                lookUpEdit.PopupWidth = iWidth;
            else
                lookUpEdit.PopupWidthMode = PopupWidthMode.ContentWidth;

            gridView.GridControl.RepositoryItems.Add(lookUpEdit);

            gridView.Columns[strFieldName].ColumnEdit = lookUpEdit;
        }

        /// <summary>
        /// 设置下拉选择框的数据
        /// <param name="lookUpEdit">下拉选择框</param>
        /// <param name="dtSource">显示数据源</param>
        /// <param name="strDisplay">显示字段</param>
        /// <param name="strValue">值字段</param>
        /// <param name="bShowAll">是否有全部</param>
        /// <param name="strNullText">Null显示</param>
        /// </summary> 
        static private void SetLoopUpEdit(RepositoryItemLookUpEdit lookUpEdit, DataTable dtSource, string strDisplay, string strValue, Boolean bShowAll = false, string strNullText = "请选择...")
        {
            //进行参数分析
            if (strDisplay.Length < 1 || strValue.Length < 1) return;
            string[] strArrDisplay = strDisplay.Split(',');
            string[] strArrValue = strValue.Split(',');
            string strDisplayColumn = strArrDisplay[0];
            string strValueColumn = strArrValue[0];

            //加入全部
            if (bShowAll)
            {
                DataTable dtInculdAll = dtSource.Copy();
                DataRow drAdd = dtInculdAll.NewRow();
                drAdd[strDisplayColumn] = "全部";
                drAdd[strValueColumn] = "%";
                dtInculdAll.Rows.InsertAt(drAdd, 0);
                lookUpEdit.DataSource = dtInculdAll;
            }
            else
                lookUpEdit.DataSource = dtSource;

            //下拉框进行设置
            lookUpEdit.DisplayMember = strDisplayColumn;
            lookUpEdit.ValueMember = strValueColumn;
            lookUpEdit.NullText = strNullText;
            lookUpEdit.DropDownItemHeight = 20;
            lookUpEdit.ShowFooter = false;

            //显示列和值列不相同，就要显示下拉表的头，此时进行处理
            if (strDisplayColumn != strValueColumn)
            {
                string strDisplayHead = String.Empty;
                string strValueHead = String.Empty;

                if (strArrDisplay.Length > 1) strDisplayHead = strArrDisplay[1];
                if (strArrValue.Length > 1) strValueHead = strArrValue[1];

                lookUpEdit.ShowHeader = true;
                lookUpEdit.Columns.AddRange(new LookUpColumnInfo[] {
                    new LookUpColumnInfo(strValueColumn, strValueHead),
                    new LookUpColumnInfo(strDisplayColumn,strDisplayHead),
                    });
            }
            else
            {
                lookUpEdit.ShowHeader = false;
            }

            return;
        }

        /// <summary>
        /// 设置列为下拉选择
        /// </summary>
        /// <param name="gridView">控件</param>
        /// <param name="strFieldName">列名</param>
        /// <param name="dictData">数据源</param>
        /// <param name="iWidth">显示宽度</param>
        /// <param name="bShowAll">是否有全部</param>
        /// <param name="strNullText">Null显示</param>
        public static void SetColumnEditLookUp(GridView gridView, string strFieldName, Dictionary<string, string> dictData, int iWidth = 0, Boolean bShowAll = false, string strNullText = "请选择...")
        {
            if (gridView == null || string.IsNullOrEmpty(strFieldName)) return;
            if (!ExistField(gridView, strFieldName)) return;

            int iCount = gridView.GridControl.RepositoryItems.Count;
            RepositoryItemLookUpEdit lookUpEdit = new RepositoryItemLookUpEdit();

            SetLoopUpEdit(lookUpEdit, dictData, bShowAll, strNullText);
            lookUpEdit.AcceptEditorTextAsNewValue = DefaultBoolean.True;
            lookUpEdit.Name = "searchLookUpEdit" + iCount.ToString();
            if (iWidth > 0)
                lookUpEdit.PopupWidth = iWidth;
            else
                lookUpEdit.PopupWidthMode = PopupWidthMode.ContentWidth;

            gridView.GridControl.RepositoryItems.Add(lookUpEdit);

            gridView.Columns[strFieldName].ColumnEdit = lookUpEdit;
        }

        /// <summary>
        /// 设置下拉选择框的数据
        /// <param name="lookUpEdit">下拉选择框</param>
        /// <param name="dictData">显示数据源</param>
        /// <param name="bShowAll">是否有全部</param>
        /// <param name="strNullText">Null显示</param>
        /// </summary> 
        static private void SetLoopUpEdit(RepositoryItemLookUpEdit lookUpEdit, Dictionary<string, string> dictData, Boolean bShowAll = false, string strNullText = "请选择...")
        {
            //加入全部
            if (bShowAll)
                dictData.Add("%", "全部");

            lookUpEdit.DataSource = dictData;

            //下拉框进行设置
            //lookUpEdit.Properties.DisplayMember = strDisplayColumn;
            //lookUpEdit.Properties.ValueMember = strValueColumn;
            lookUpEdit.NullText = strNullText;
            lookUpEdit.DropDownItemHeight = 20;
            lookUpEdit.ShowFooter = false;

            //显示列和值列不相同，就要显示下拉表的头，此时进行处理
            //if (strDisplayColumn != strValueColumn)
            //{
            //    string strDisplayHead = String.Empty;
            //    string strValueHead = String.Empty;

            //    if (strArrDisplay.Length > 1) strDisplayHead = strArrDisplay[1];
            //    if (strArrValue.Length > 1) strValueHead = strArrValue[1];

            //    lookUpEdit.Properties.ShowHeader = true;
            //    lookUpEdit.Properties.Columns.AddRange(new LookUpColumnInfo[] {
            //        new LookUpColumnInfo(strValueColumn, strValueHead),
            //        new LookUpColumnInfo(strDisplayColumn,strDisplayHead),
            //        });
            //}
            //else
            //{
            lookUpEdit.ShowHeader = false;
            //}

            return;
        }

        /// <summary>
        /// 设置列为查找下拉选择
        /// </summary>
        /// <param name="gridView">控件</param>
        /// <param name="strFieldName">列名</param>
        /// <param name="dtData">数据源</param>
        /// <param name="dtColFormat">数据源列定义</param>
        /// <param name="strValueColumn">数据源列--值</param>
        /// <param name="strDisColumn">数据源列--显示</param>
        /// <param name="iWidth">显示宽度</param>
        /// <param name="bShowAll">是否有全部</param>
        /// <param name="strNullText">Null显示</param>
        public static void SetColumnEditSearchLookUp(GridView gridView, string strFieldName, DataTable dtData, DataTable dtColFormat, string strValueColumn, string strDisColumn, int iWidth = 0, Boolean bShowAll = false, string strNullText = "请选择...")
        {
            if (gridView == null || string.IsNullOrEmpty(strFieldName)) return;
            if (!ExistField(gridView, strFieldName)) return;

            int iCount = gridView.GridControl.RepositoryItems.Count;
            RepositoryItemSearchLookUpEdit searchLookUpEdit = new RepositoryItemSearchLookUpEdit();
            searchLookUpEdit.Name = "searchLookUpEdit" + iCount.ToString();

            //searchLookUpEdit.PopupSizeable = true;
            if (iWidth > 0)
                searchLookUpEdit.PopupFormSize = new Size(iWidth, searchLookUpEdit.PopupFormSize.Height);
            else
                searchLookUpEdit.PopupWidthMode = PopupWidthMode.ContentWidth;

            SetSearchLookUp(searchLookUpEdit, dtData, strValueColumn, strDisColumn, dtColFormat, bShowAll, strNullText);

            gridView.GridControl.RepositoryItems.Add(searchLookUpEdit);
            gridView.Columns[strFieldName].ColumnEdit = searchLookUpEdit;

        }

        /// <summary>
        /// 设置Grid中查找下拉输入，有列头定义
        /// </summary>
        /// <param name="searchLookUpEdit"></param>
        /// <param name="dtData"></param>
        /// <param name="strValueColumn"></param>
        /// <param name="strDisColumn"></param>
        /// <param name="dtColFormat"></param>
        /// <param name="bShowAll"></param>
        /// <param name="strNullText">Null显示</param>
        private static void SetSearchLookUp(RepositoryItemSearchLookUpEdit searchLookUpEdit, DataTable dtData, string strValueColumn, string strDisColumn, DataTable dtColFormat, Boolean bShowAll = false, string strNullText = "请选择...")
        {
            DataTable dtShow = dtData.Copy();

            //加入全部
            if (bShowAll)
            {
                DataRow drNewAll = dtShow.NewRow();
                drNewAll[strDisColumn] = "全部";
                drNewAll[strValueColumn] = "%";
                dtShow.Rows.InsertAt(drNewAll, 0);
            }

            searchLookUpEdit.DataSource = dtShow;
            searchLookUpEdit.DisplayMember = strDisColumn;
            searchLookUpEdit.ValueMember = strValueColumn;
            searchLookUpEdit.NullText = strNullText;
            searchLookUpEdit.ShowFooter = false;

            //如果有全部，则不能有清除
            searchLookUpEdit.ShowClearButton = !bShowAll;

            //奇偶行
            searchLookUpEdit.View.OptionsView.EnableAppearanceEvenRow = true;
            searchLookUpEdit.View.OptionsView.EnableAppearanceOddRow = true;

            //设置显示的表头
            searchLookUpEdit.BeforePopup += (sender, e) => SetsearchLookUpEditColumn(sender, e, dtColFormat);
            searchLookUpEdit.PopulateViewColumns();

            //选择数据
            //searchLookUpEdit.Closed += SelectData;
            //searchLookUpEdit.Closed += (sender, e) => SelectData(sender, e, dtShow);
        }

        /// <summary>
        /// 设置弹出的标题
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        /// <param name="dtColFormat"></param>
        private static void SetsearchLookUpEditColumn(object sender, EventArgs e, DataTable dtColFormat)
        {
            if (dtColFormat == null || dtColFormat.Rows.Count < 1)
                return;

            ColumnView searchLookUpView = ((SearchLookUpEdit)sender).Properties.PopupView;

            //有自定义的
            string strCloName = String.Empty;
            string strCaption = String.Empty;
            string strFormat = String.Empty;
            string strAlignment = String.Empty;
            int iWidth;
            int iNumFlag;
            for (int i = 0; i < dtColFormat.Rows.Count; i++)
            {
                //列名
                strCloName = dtColFormat.Rows[i]["Name"].ToString().ToUpper().Trim();

                if (string.IsNullOrEmpty(strCloName)) continue;

                //看看，有没有此列
                Boolean bExistCol = false;
                for (int j = 0; j < searchLookUpView.Columns.Count; j++)
                {
                    if (searchLookUpView.Columns[j].FieldName == strCloName)
                    {
                        bExistCol = true;
                        break;
                    }

                }

                if (!bExistCol) continue;

                //标题
                strCaption = dtColFormat.Rows[i]["Caption"].ToString();
                if (!string.IsNullOrEmpty(strCaption))
                {
                    searchLookUpView.Columns[strCloName].Caption = strCaption;
                    searchLookUpView.Columns[strCloName].AppearanceHeader.TextOptions.HAlignment = HorzAlignment.Center;
                    searchLookUpView.Columns[strCloName].AppearanceHeader.Options.UseBackColor = true;
                    searchLookUpView.Columns[strCloName].AppearanceHeader.BackColor = Color.SkyBlue;
                    searchLookUpView.Columns[strCloName].AppearanceHeader.Font = new Font("宋体", 10, FontStyle.Bold);
                }

                //宽度
                iWidth = Cs01Functions.CInt(dtColFormat.Rows[i]["Width"]);
                if (iWidth > 0)
                    searchLookUpView.Columns[strCloName].Width = iWidth;

                ////是否选择输入
                //iInput = Cs01Functions.CInt(dtColFormat.Rows[i]["InputFlag"]);
                //if (iInput > 0)
                //    searchLookUpView.Rows.Add(strName, strCaption, iInput.ToString());

                // 数字字段
                iNumFlag = Cs01Functions.CInt(dtColFormat.Rows[i]["NumFlag"]);
                if (iNumFlag == 1)
                    searchLookUpView.Columns[strCloName].DisplayFormat.FormatType = FormatType.Numeric;

                //显示格式
                strFormat = dtColFormat.Rows[i]["Format"].ToString();
                if (strFormat != "")
                    searchLookUpView.Columns[strCloName].DisplayFormat.FormatString = strFormat;

                //水平方向对齐
                strAlignment = dtColFormat.Rows[i]["Alignment"].ToString();
                switch (strAlignment.ToLower())
                {
                    case "center":
                        searchLookUpView.Columns[strCloName].AppearanceCell.TextOptions.HAlignment = HorzAlignment.Center;
                        break;
                    case "right":
                        searchLookUpView.Columns[strCloName].AppearanceCell.TextOptions.HAlignment = HorzAlignment.Far;
                        break;
                    default:
                        searchLookUpView.Columns[strCloName].AppearanceCell.TextOptions.HAlignment = HorzAlignment.Near;
                        break;
                }
            }

        }

        /// <summary>
        /// 查找是否存在列
        /// </summary>
        /// <param name="gridView">目标gridView</param>
        /// <param name="strFieldName">列名</param>
        /// <returns></returns>
        public static Boolean ExistField(GridView gridView, string strFieldName)
        {
            if (gridView == null || string.IsNullOrEmpty(strFieldName))
            {
                Cs02MessageBox.ShowWarning("Grid对象为空，或者没有指定列名！");
                return false;
            }

            Boolean bFind = false;

            if (gridView.Columns.Count < 1)
            {
                Cs02MessageBox.ShowWarning("Grid对象中，没有列！");
                return false;
            }

            for (int i = 0; i < gridView.Columns.Count; i++)
            {
                if ((gridView.Columns[i].FieldName == strFieldName))
                {
                    bFind = true;
                    break;
                }
            }

            if (!bFind)
            {
                Cs02MessageBox.ShowWarning($"Grid对象中，没有列 {strFieldName} ！");
            }
            return bFind;
        }

        /// <summary>
        /// 取列的最大值
        /// </summary>
        /// <param name="gridView">控件</param>
        /// <param name="strColumnName">列名</param>
        /// <returns></returns>
        public static object GetFieldMaxValue(GridView gridView, string strColumnName)
        {
            if (gridView == null || string.IsNullOrEmpty(strColumnName)) return null;
            if (!ExistField(gridView, strColumnName)) return null;
            if (gridView.RowCount < 1) return null;

            object oMaxValue = null;
            object oCurrentValue = null;
            string strDataType = gridView.Columns[strColumnName].ColumnType.ToString();

            for (int i = 0; i < gridView.RowCount; i++)
            {
                //DataRow drCurrent = gridView.GetDataRow(i);
                //if (drCurrent == null) continue;
                // oCurrentValue = drCurrent[strColumnName];

                oCurrentValue = gridView.GetRowCellValue(i, strColumnName);

                switch (strDataType)
                {
                    case "System.Int16":
                    case "System.Int32":
                        if (Cs01Functions.CInt(oCurrentValue) > Cs01Functions.CInt(oMaxValue))
                            oMaxValue = oCurrentValue;
                        break;
                    case "System.Int64":
                        if (Cs01Functions.CLng(oCurrentValue) > Cs01Functions.CLng(oMaxValue))
                            oMaxValue = oCurrentValue;
                        break;
                    case "System.Float":
                    case "System.Double":
                    case "System.Decimal":
                        if (Cs01Functions.CDecimal(oCurrentValue) > Cs01Functions.CDecimal(oMaxValue))
                            oMaxValue = oCurrentValue;
                        break;
                    case "System.Datetime":
                        if (Cs01Functions.CDate(oCurrentValue) > Cs01Functions.CDate(oMaxValue))
                            oMaxValue = oCurrentValue;
                        break;
                    case "System.String":
                        if (oCurrentValue?.ToString().CompareTo(oMaxValue?.ToString()) == 1)
                            oMaxValue = oCurrentValue;
                        break;
                    default:
                        if (oCurrentValue?.ToString().CompareTo(oMaxValue?.ToString()) == 1)
                            oMaxValue = oCurrentValue;
                        break;
                }
            }

            return oMaxValue;
        }

        /// <summary>
        /// GridView回车是TAB
        /// </summary>
        /// <param name="gridView">目标GridView</param>
        public static void SetEnterKeyDownAsTab(GridView gridView)
        {
            gridView.KeyDown += EnterKeyDownAsTab;
        }

        /// <summary>
        /// GridView回车
        /// </summary>
        private static void EnterKeyDownAsTab(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                SendKeys.Send("{TAB}");
            }
        }

        /// <summary>
        /// 设置GRID的定制属性
        /// </summary>
        /// <param name="gridView">控件</param>
        /// <param name="bSort">允许排序</param>
        /// <param name="bFilter">允许过滤</param>
        /// <param name="bMoving">允许列移动</param>
        /// <param name="bResizing">允许拖动列宽</param>
        public static void SetGridCustom(GridView gridView, Boolean bSort, Boolean bFilter, Boolean bMoving, Boolean bResizing)
        {
            gridView.OptionsCustomization.AllowSort = bSort;
            gridView.OptionsCustomization.AllowFilter = bFilter;
            gridView.OptionsCustomization.AllowColumnMoving = bMoving;
            gridView.OptionsCustomization.AllowColumnResizing = bResizing;
        }

        /// <summary>
        /// 加入计算列,显示在最后
        /// </summary>
        /// <param name="gridView">控件</param>
        /// <param name="strColumnName">列名</param>
        /// <param name="strExpression">计算表达式</param>
        public static void AddCalcuteColumn(GridView gridView, string strColumnName, string strExpression)
        {
            DevExpress.XtraGrid.Columns.GridColumn colCalcute = new DevExpress.XtraGrid.Columns.GridColumn();
            colCalcute.Caption = strColumnName;
            colCalcute.FieldName = strColumnName;
            colCalcute.Name = strColumnName;
            colCalcute.UnboundExpression = strExpression;
            colCalcute.UnboundType = DevExpress.Data.UnboundColumnType.Decimal;
            colCalcute.Visible = true;
            gridView.Columns.Add(colCalcute);
        }

        /// <summary>
        /// 加入计算列，指定位置
        /// </summary>
        /// <param name="gridView">控件</param>
        /// <param name="strColumnName">列名</param>
        /// <param name="strExpression">计算表达式</param>
        /// <param name="iShowIndex">显示位置</param>
        public static void AddCalcuteColumn(GridView gridView, string strColumnName, string strExpression, int iShowIndex = 0)
        {
            DevExpress.XtraGrid.Columns.GridColumn colCalcute = new DevExpress.XtraGrid.Columns.GridColumn();
            colCalcute.Caption = strColumnName;
            colCalcute.FieldName = strColumnName;
            colCalcute.Name = strColumnName;
            colCalcute.UnboundExpression = strExpression;
            colCalcute.UnboundType = DevExpress.Data.UnboundColumnType.Decimal;
            colCalcute.Visible = true;
            if (iShowIndex < 0) iShowIndex = 0;
            colCalcute.VisibleIndex = iShowIndex;
            gridView.Columns.Add(colCalcute);
        }

        /// <summary>
        /// 加入计算列
        /// </summary>
        /// <param name="gridView">控件</param>
        /// <param name="strColumnName">列名</param>
        /// <param name="strExpression">计算表达式</param>
        /// <param name="ColType">数据类型</param>
        public static void AddCalcuteColumn(GridView gridView, string strColumnName, string strExpression, DevExpress.Data.UnboundColumnType ColType = DevExpress.Data.UnboundColumnType.Decimal)
        {
            DevExpress.XtraGrid.Columns.GridColumn colCalcute = new DevExpress.XtraGrid.Columns.GridColumn();
            colCalcute.Caption = strColumnName;
            colCalcute.FieldName = strColumnName;
            colCalcute.Name = strColumnName;
            colCalcute.UnboundExpression = strExpression;
            colCalcute.UnboundType = ColType;
            colCalcute.Visible = true;
            gridView.Columns.Add(colCalcute);
        }

        /// <summary>
        /// 加入计算列
        /// </summary>
        /// <param name="gridView">控件</param>
        /// <param name="strColumnName">列名</param>
        /// <param name="strExpression">计算表达式</param>
        /// <param name="iShowIndex">显示位置</param>
        /// <param name="ColType">数据类型</param>
        public static void AddCalcuteColumn(GridView gridView, string strColumnName, string strExpression, int iShowIndex = 0, DevExpress.Data.UnboundColumnType ColType = DevExpress.Data.UnboundColumnType.Decimal)
        {
            DevExpress.XtraGrid.Columns.GridColumn colCalcute = new DevExpress.XtraGrid.Columns.GridColumn();
            colCalcute.Caption = strColumnName;
            colCalcute.FieldName = strColumnName;
            colCalcute.Name = strColumnName;
            colCalcute.UnboundExpression = strExpression;
            colCalcute.UnboundType = ColType;
            colCalcute.Visible = true;
            if (iShowIndex < 0) iShowIndex = 0;
            gridView.Columns.Add(colCalcute);
        }

        /// <summary>
        /// 取列头设置表
        /// </summary>
        /// <returns></returns>
        public static DataTable GetColFormatDataTable()
        {
            DataTable dtColFormat = new DataTable();

            dtColFormat.Columns.Add("Name", Type.GetType("System.String"));
            dtColFormat.Columns.Add("Caption", Type.GetType("System.String"));
            dtColFormat.Columns.Add("Width", Type.GetType("System.Int32"));
            dtColFormat.Columns.Add("NumFlag", Type.GetType("System.Int32"));
            dtColFormat.Columns.Add("Format", Type.GetType("System.String"));
            dtColFormat.Columns.Add("Alignment", Type.GetType("System.String"));
            dtColFormat.Columns.Add("ShowFlag", Type.GetType("System.Int32"));

            return dtColFormat;
        }

        /// <summary>
        /// 选择数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private static void SelectData(object sender, ClosedEventArgs e)
        {
            switch (e.CloseMode)
            {
                case PopupCloseMode.Normal:
                    //MessageBox.Show("CloseMode:" + e.CloseMode.ToString() + "  正常选择数据");
                    break;
                case PopupCloseMode.Cancel:
                    //MessageBox.Show("CloseMode:" + e.CloseMode.ToString() + "  清除数据");
                    break;
                case PopupCloseMode.Immediate:
                    //MessageBox.Show("CloseMode:" + e.CloseMode.ToString() + "  失去焦点");
                    break;
                case PopupCloseMode.ButtonClick:
                    //MessageBox.Show("CloseMode:" + e.CloseMode.ToString());
                    break;
                case PopupCloseMode.CloseUpKey:
                    //MessageBox.Show("CloseMode:" + e.CloseMode.ToString());
                    break;
                default:
                    break;
            }

        }

        /// <summary>
        /// 选择数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        /// <param name="dtShow"></param>
        private static void SelectData(object sender, ClosedEventArgs e, DataTable dtShow)
        {
            string strKeyValue = String.Empty;
            switch (e.CloseMode)
            {
                case PopupCloseMode.Normal:
                    //MessageBox.Show("CloseMode:" + e.CloseMode.ToString() + "  正常选择数据");
                    break;
                case PopupCloseMode.Cancel:
                    //MessageBox.Show("CloseMode:" + e.CloseMode.ToString() + "  清除数据");
                    break;
                case PopupCloseMode.Immediate:
                    //MessageBox.Show("CloseMode:" + e.CloseMode.ToString() + "  失去焦点");
                    break;
                case PopupCloseMode.ButtonClick:
                    //MessageBox.Show("CloseMode:" + e.CloseMode.ToString());
                    break;
                case PopupCloseMode.CloseUpKey:
                    //MessageBox.Show("CloseMode:" + e.CloseMode.ToString());
                    break;
                default:
                    break;
            }
        }

        private static object GetInstanceByName(string className)
        {
            object obj;
            try
            {
                obj = Activator.CreateInstance(Type.GetType(className));
            }
            catch (Exception e)
            {
                throw new Exception("动态创建实例失败 /n/n=> " + className, e);
            }
            return obj;
        }

        /// <summary>
        /// 返回空白列数据的行号
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="strFieldName">列名</param>
        /// <param name="bShowMsg">是否显示信息</param>
        /// <returns></returns>
        public static int GetEmptyRow(GridView gridView, string strFieldName, Boolean bShowMsg = true)
        {
            if (gridView == null || string.IsNullOrEmpty(strFieldName)) return -1;
            if (!ExistField(gridView, strFieldName)) return -1;

            int iRowHandle = -1;
            if (string.IsNullOrEmpty(strFieldName)) return iRowHandle;

            int iRowCount = gridView.RowCount;
            for (int i = 0; i < iRowCount; i++)
            {
                //string strValue = gridView GetDataRow(i)[strFieldName]?.ToString().Trim();
                string strValue = gridView.GetRowCellValue(i, strFieldName)?.ToString().Trim();

                if (String.IsNullOrEmpty(strValue))
                {
                    iRowHandle = i;
                    break;
                }
            }

            if (iRowHandle >= 0 && bShowMsg)
            {
                string strMsg = "第 [ " + (iRowHandle + 1).ToString() + " ] 行，数据列 [ " + gridView.Columns[strFieldName].Caption + " ] 的值，不能为空！";
                Cs02MessageBox.ShowWarning(strMsg);
                gridView.FocusedRowHandle = iRowHandle;
            }

            return iRowHandle;
        }

        /// <summary>
        /// 返回空白列数据的行号
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="strArrFieldNames">列名数组</param>
        /// <param name="bShowMsg">是否显示信息</param>
        /// <returns></returns>
        public static int GetEmptyRow(GridView gridView, string[] strArrFieldNames, Boolean bShowMsg = true)
        {
            int iRowHandle = -1;
            if (strArrFieldNames.Length < 1) return iRowHandle;

            if (gridView.RowCount < 1) return iRowHandle;

            for (int i = 0; i < strArrFieldNames.Length; i++)
            {
                if (!ExistField(gridView, strArrFieldNames[i]))
                {
                    return iRowHandle;
                }
            }

            string strFieldName = string.Empty;
            for (int i = 0; i < gridView.RowCount; i++)
            {
                foreach (string strCurrentFieldName in strArrFieldNames)
                {
                    strFieldName = strCurrentFieldName;

                    if (string.IsNullOrEmpty(strFieldName)) continue;

                    string strValue = gridView.GetRowCellValue(i, strFieldName)?.ToString().Trim();

                    if (String.IsNullOrEmpty(strValue))
                    {
                        iRowHandle = i;
                        break;
                    }
                }
                if (iRowHandle >= 0) break;
            }

            if (iRowHandle >= 0 && bShowMsg)
            {
                string strMsg = "第 [ " + (iRowHandle + 1).ToString() + " ] 行，数据列 [ " + gridView.Columns[strFieldName].Caption + " ] 的值，不能为空！";
                Cs02MessageBox.ShowWarning(strMsg);
                gridView.FocusedRowHandle = iRowHandle;
            }

            return iRowHandle;
        }

        /// <summary>
        /// 返回存在数据的行号
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="strFieldName">列名</param>
        /// <param name="strFieldValue">值</param>
        /// <returns></returns>
        public static int GetExistedRow(GridView gridView, string strFieldName, string strFieldValue)
        {
            if (gridView == null || string.IsNullOrEmpty(strFieldName)) return -1;
            if (!ExistField(gridView, strFieldName)) return -1;

            int iRowHandle = -1;
            for (int i = 0; i < gridView.RowCount; i++)
            {
                //string strValue = gridView.GetDataRow(i)?[strFieldName].ToString();
                string strValue = gridView.GetRowCellValue(i, strFieldName)?.ToString();

                if (strValue == strFieldValue)
                {
                    iRowHandle = i;
                    break;
                }
            }
            return iRowHandle;
        }

        /// <summary>
        /// 返回存在数据的行号
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="dicFields">列值字典</param>
        /// <returns></returns>
        public static int GetExistedRow(GridView gridView, Dictionary<string, object> dicFields)
        {
            if (gridView == null || dicFields == null || dicFields.Count < 1) return -1;

            //检查列存在
            foreach (var item in dicFields)
            {
                if (!ExistField(gridView, item.Key))
                {
                    return -1;
                }
            }

            int iRowHandle = -1;
            Boolean bFind = true;
            //循环处理每一行
            for (int i = 0; i < gridView.RowCount; i++)
            {
                bFind = true;
                //循环处理指定的列
                foreach (var item in dicFields)
                {
                    string strValue = gridView.GetRowCellValue(i, item.Key)?.ToString();
                    if (strValue != item.Value.ToString())
                    {
                        bFind = false;
                        break;
                    }
                }

                //找到了
                if (bFind)
                {
                    iRowHandle = i;
                    break;
                }
            }
            return iRowHandle;
        }

        /// <summary>
        /// 返回数据为零的行号
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="strFieldName">列名</param>
        /// <param name="bShowMsg">是否显示信息</param>
        /// <returns></returns>
        public static int GetZeroRow(GridView gridView, string strFieldName, Boolean bShowMsg = true)
        {
            if (gridView == null || string.IsNullOrEmpty(strFieldName)) return -1;
            if (!ExistField(gridView, strFieldName)) return -1;

            int iRowHandle = -1;
            for (int i = 0; i < gridView.RowCount; i++)
            {
                //decimal dValue = Cs01Functions.CDecimal(gridView.GetDataRow(i)[strFieldName]);
                decimal dValue = Cs01Functions.CDecimal(gridView.GetRowCellValue(i, strFieldName));
                if (dValue == 0)
                {
                    iRowHandle = i;
                    break;
                }
            }

            if (iRowHandle >= 0 && bShowMsg)
            {
                string strMsg = "第 [ " + (iRowHandle + 1).ToString() + " ] 行，数据列 [ " + gridView.Columns[strFieldName].Caption + " ] 的值，不能为零！";
                Cs02MessageBox.ShowWarning(strMsg);
                gridView.FocusedRowHandle = iRowHandle;
            }

            return iRowHandle;
        }

        /// <summary>
        /// 返回数据为非正数的行号
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="strFieldName">列名</param>
        /// <param name="bShowMsg">是否显示信息</param>
        /// <returns></returns>
        public static int GetNotPositiveRow(GridView gridView, string strFieldName, Boolean bShowMsg = true)
        {
            if (gridView == null || string.IsNullOrEmpty(strFieldName)) return -1;
            if (!ExistField(gridView, strFieldName)) return -1;

            int iRowHandle = -1;
            for (int i = 0; i < gridView.RowCount; i++)
            {
                //decimal dValue = Cs01Functions.CDecimal(gridView.GetDataRow(i)[strFieldName]);
                decimal dValue = Cs01Functions.CDecimal(gridView.GetRowCellValue(i, strFieldName));

                if (dValue <= 0)
                {
                    iRowHandle = i;
                    break;
                }
            }

            if (iRowHandle >= 0 && bShowMsg)
            {
                string strMsg = "第 [ " + (iRowHandle + 1).ToString() + " ] 行，数据列 [ " + gridView.Columns[strFieldName].Caption + " ] 的值，必须大于零！";
                Cs02MessageBox.ShowWarning(strMsg);
                gridView.FocusedRowHandle = iRowHandle;
            }

            return iRowHandle;
        }

        /// <summary>
        /// 判断是否有重复数据，返回重复的行号对
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="strFieldName">列名</param>
        /// <param name="bShowMsg">是否显示信息</param>
        /// <returns></returns>
        public static int[] GetRepeatedRow(GridView gridView, string strFieldName, Boolean bShowMsg = true)
        {
            int[] iArrAows = new int[2] { -1, -1 };
            if (gridView == null || string.IsNullOrEmpty(strFieldName)) return iArrAows;
            if (!ExistField(gridView, strFieldName)) return iArrAows;

            int iRowCount = gridView.RowCount;
            string strValue = string.Empty;
            Dictionary<string, int> dic = new Dictionary<string, int>();
            for (int i = 0; i < iRowCount; i++)
            {
                //strValue = gridView.GetDataRow(i)[strFieldName].ToString();
                strValue = gridView.GetRowCellValue(i, strFieldName)?.ToString();

                if (dic.ContainsKey(strValue))
                {
                    iArrAows[1] = i + 1;
                    iArrAows[0] = dic[strValue];
                    break;
                }
                else
                {
                    dic[strValue] = i + 1;
                }
            }

            if (iArrAows[0] >= 0 && bShowMsg)
            {
                string strMsg = "第 [ " + (iArrAows[0] + 1).ToString() + " ] 行，第 [ " + (iArrAows[1] + 1).ToString() + " ] 行，数据列 [ " + gridView.Columns[strFieldName].Caption + " ] 的值 [ " + strValue + " ]，重复了！";
                Cs02MessageBox.ShowWarning(strMsg);
                gridView.FocusedRowHandle = iArrAows[0];
            }

            return iArrAows;
        }

        /// <summary>
        /// 判断是否有重复数据，返回重复的行号对
        /// </summary>
        /// <param name="gridView">GridView</param>
        /// <param name="strArrFieldNames">列名数组</param>
        /// <param name="bShowMsg">是否显示信息</param>
        /// <returns></returns>
        public static int[] GetRepeatedRow(GridView gridView, string[] strArrFieldNames, Boolean bShowMsg = true)
        {
            int[] iArrAows = new int[2] { -1, -1 };
            int iFieldsCount = strArrFieldNames.Length;
            for (int i = 0; i < iFieldsCount; i++)
            {
                if (!ExistField(gridView, strArrFieldNames[i]))
                {
                    return iArrAows;
                }
            }

            string strFieldName = string.Empty;
            string strValue = string.Empty;
            int iRowount = gridView.RowCount;

            Dictionary<string, int> dic = new Dictionary<string, int>();
            for (int i = 0; i < iRowount; i++)
            {
                //int k;
                string strFieldValue = gridView.GetRowCellValue(i, strArrFieldNames[0])?.ToString();
                strValue = strFieldValue;
                for (int k = 1; k < iFieldsCount; k++)
                {
                    strFieldName = strArrFieldNames[k];
                    strFieldValue += "." + gridView.GetRowCellValue(i, strFieldName)?.ToString();
                }
                if (dic.ContainsKey(strFieldValue))
                {
                    iArrAows[1] = i + 1;
                    iArrAows[0] = dic[strFieldValue];
                    break;
                }
                else
                {
                    dic[strFieldValue] = i + 1;
                }
            }

            if (iArrAows[0] >= 0 && bShowMsg)
            {
                string strMsg = "第 [ " + (iArrAows[0] + 1).ToString() + " ] 行，第 [ " + (iArrAows[1] + 1).ToString() + " ] 行，数据列 [ " + gridView.Columns[strFieldName].Caption + " ] 的值 [ " + strValue + " ]，重复了！";
                Cs02MessageBox.ShowWarning(strMsg);
                gridView.FocusedRowHandle = iArrAows[0];
            }

            return iArrAows;
        }

        /// <summary>
        /// 打印GridControl
        /// </summary>
        /// <param name="gcControl">打印控件</param>
        /// <param name="strTitle">标题</param>
        /// <param name="strCondition">查询条件</param>
        /// <param name="bAutoFooter">是否打印页脚</param>
        public static void GridControlPrint(GridControl gcControl, string strTitle, string strCondition, Boolean bAutoFooter = true)
        {
            if (string.IsNullOrEmpty(strTitle) && string.IsNullOrEmpty(strCondition))
            {
                gcControl.ShowPrintPreview();
                return;
            }

            PrintingSystem printsystem = new PrintingSystem();
            PrintableComponentLink link = new PrintableComponentLink(printsystem);
            printsystem.Links.Add(link);

            //联接打印的部件
            link.Component = gcControl;

            //创建报表头脚对象
            PageHeaderFooter phf = link.PageHeaderFooter as PageHeaderFooter;
            phf.IncreaseMarginsByContent = true;
            phf.Header.Content.Clear();

            //加入标题
            link.CreateMarginalHeaderArea += (sender, eventargs) => PrintCreateHeaderArea(eventargs, strTitle, strCondition);
            //if (!string.IsNullOrEmpty(strTitle))
            //{
            //    phf.Header.Content.Clear();
            //    phf.Header.Content.AddRange(new string[] { "\r\n\r\n" + strCondition, strTitle, "" });
            //    phf.Header.Font = new System.Drawing.Font("宋体", 12, System.Drawing.FontStyle.Regular);
            //    phf.Header.LineAlignment = BrickAlignment.Center;
            //}

            //加页脚
            if (bAutoFooter)
            {
                phf.Footer.Content.Clear();
                phf.Footer.Content.AddRange(new string[] { "", "打印时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "" });
                phf.Footer.Font = new System.Drawing.Font("宋体", 8, System.Drawing.FontStyle.Regular);
                phf.Footer.LineAlignment = BrickAlignment.Center;
            }

            //建立文档
            link.CreateDocument();
            //进行预览
            printsystem.PreviewFormEx.Show();
        }

        /// <summary>
        /// 打印标题区的设置
        /// </summary>
        /// <param name="e"></param>
        /// <param name="strTitle"></param>
        /// <param name="strCondition"></param>
        private static void PrintCreateHeaderArea(CreateAreaEventArgs e, string strTitle, string strCondition)
        {
            //加入标题
            if (!string.IsNullOrEmpty(strTitle))
            {
                PageInfoBrick brick = e.Graph.DrawPageInfo(PageInfo.None, strTitle, Color.DarkBlue,
                    new RectangleF(0, 0, 200, 30), BorderSide.None);
                brick.LineAlignment = BrickAlignment.Center;
                brick.Alignment = BrickAlignment.Center;
                brick.AutoWidth = true;
                brick.Font = new System.Drawing.Font("宋体", 12f, FontStyle.Bold);
            }

            //加入条件
            if (!string.IsNullOrEmpty(strCondition))
            {
                PageInfoBrick brick = e.Graph.DrawPageInfo(PageInfo.None, strCondition, Color.Black,
                new RectangleF(0, 0, 200, 30), BorderSide.None);
                brick.LineAlignment = BrickAlignment.Far;
                brick.Alignment = BrickAlignment.Near;
                brick.AutoWidth = true;
                brick.Font = new System.Drawing.Font("宋体", 9f, FontStyle.Regular);
            }
        }

    }
}
