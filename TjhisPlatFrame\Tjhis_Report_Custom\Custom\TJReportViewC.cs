﻿using System.Windows.Forms;

namespace Tjhis.Report.Custom.Custom
{
    public static class TJReportViewC
    {
        /// <summary>
        /// 打开报表视图
        /// </summary>
        /// <param name="reportID">报表ID</param>
        /// <param name="isTable">是否表格视图</param>
        /// <param name="windowState">窗体状态</param>
        public static void Show(string reportID, string isTable = "", FormWindowState windowState = FormWindowState.Maximized)
        {
            GetReportView(reportID, windowState:windowState, isTable:isTable).Show();
        }
        /// <summary>
        /// 打开报表视图 DIalog形式
        /// </summary>
        /// <param name="reportID">报表ID</param>
        /// <param name="isTable">是否表格视图</param>
        /// <param name="windowState">窗体状态</param>
        public static void ShowDialog(string reportID, string isTable = "", FormWindowState windowState = FormWindowState.Maximized)
        {
            GetReportView(reportID, windowState:windowState ,isTable:isTable).ShowDialog();
        }

        /// <summary>
        /// 获取报表视图窗体
        /// </summary>
        /// <param name="reportID">报表ID</param>
        /// <param name="isTable">是否表格视图</param>
        /// <param name="windowState">窗体状态</param>
        public static Form GetReportView(string reportID, string isTable = "", FormWindowState windowState = FormWindowState.Maximized)
        {
            Form frm = new TJReportViewF(reportID, isTable);

            frm.WindowState = windowState;
            frm.Tag = reportID;
            return frm;
        }

        /// <summary>
        /// 获取报表主窗体
        /// </summary>
        /// <param name="windowState">窗体状态</param>
        /// <returns></returns>
        public static Form GetReportMain(FormWindowState windowState = FormWindowState.Maximized)
        {
            frmReportMain fm = new frmReportMain();
            fm.WindowState = windowState;
            return fm;
        }
        /// <summary>
        /// 打开主窗体
        /// </summary>
        /// <param name="windowState">窗体状态</param>
        public static void MainShow(FormWindowState windowState = FormWindowState.Maximized)
        {
            GetReportMain(windowState).Show();
        }
        /// <summary>
        /// 打开主窗体 Dialog形式
        /// </summary>
        /// <param name="windowState">窗体状态</param>
        public static void MainShowDialog(FormWindowState windowState = FormWindowState.Maximized)
        {
            GetReportMain(windowState).ShowDialog();
        }
    }
}
