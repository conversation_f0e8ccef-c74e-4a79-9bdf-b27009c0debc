﻿using System;
using System.Text;
using System.Collections.Generic;
using System.Data;
using Model;
using Utility.OracleODP;

namespace NM_Service.NMService
{

    /// <summary>
    /// 床位记录实现类
    /// </summary> 	

    public class BED_RECClient : INMService.IBED_REC,IDisposable
    {
        #region  Method
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists_BED_REC(string WARD_CODE, decimal BED_NO)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.BED_REC_Dao().Exists(WARD_CODE, BED_NO, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }

        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add_BED_REC(Model.BED_REC model)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.BED_REC_Dao().Add(model, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update_BED_REC(Model.BED_REC model)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.BED_REC_Dao().Update(model, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete_BED_REC(string WARD_CODE, decimal BED_NO)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    bool ret = new OracleDAL.BED_REC_Dao().Delete(WARD_CODE, BED_NO, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.BED_REC GetModel_BED_REC(string WARD_CODE, decimal BED_NO)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    Model.BED_REC ret = new OracleDAL.BED_REC_Dao().GetModel(WARD_CODE, BED_NO, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList_All_BED_REC(string strWhere)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    DataSet ret = new OracleDAL.BED_REC_Dao().GetList(strWhere, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>   
        public DataSet GetList_BED_REC(int startIndex, int endIndex, string strWhere, string filedOrder)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    DataSet ret = new OracleDAL.BED_REC_Dao().GetList(startIndex, endIndex, strWhere, filedOrder, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>  
        public System.Collections.ObjectModel.ObservableCollection<Model.BED_REC> GetObservableCollection_All_BED_REC(string strWhere)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    System.Collections.ObjectModel.ObservableCollection<Model.BED_REC> ret = new OracleDAL.BED_REC_Dao().GetObservableCollection(strWhere, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 获得ObservableCollection根据分页获得数据列表
        /// </summary>  
        public System.Collections.ObjectModel.ObservableCollection<Model.BED_REC> GetObservableCollection_BED_REC(int startIndex, int endIndex, string strWhere, string filedOrder)
        {
            using (OracleBaseClass db = new OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    db.BeginTransaction();
                    System.Collections.ObjectModel.ObservableCollection<Model.BED_REC> ret = new OracleDAL.BED_REC_Dao().GetObservableCollection(startIndex, endIndex, strWhere, filedOrder, db);
                    db.CommitTransaction();
                    db.CloseDB();
                    return ret;
                }
                else
                {
                    return null;
                }
            }
        }
        #endregion

        #region IDisposable 成员

        /// <summary>
　　　　/// 实现IDisposable接口
　　　　/// </summary>
　　　　public void Dispose()
　　　　{
　　　　　　Dispose(true);
　　　　　　//.NET Framework 类库
　　　　　　// GC..::.SuppressFinalize 方法
　　　　　　//请求系统不要调用指定对象的终结器。
　　　　　　GC.SuppressFinalize(this);
　　　　}
　　　　/// <summary>
　　　　/// 虚方法，可供子类重写
　　　　/// </summary>
　　　　/// <param name="disposing"></param>
　　　　protected virtual void Dispose(bool disposing)
　　　　{
　　　　　　if (disposing)
　　　　　　　　{
　　　　　　　　　　// Release managed resources
　　　　　　　　}
　　　　}
　　　　/// <summary>
　　　　/// 析构函数
　　　　/// 当客户端没有显示调用Dispose()时由GC完成资源回收功能
　　　　/// </summary>
        ~BED_RECClient()
　　　　{
　　　　　　Dispose();
　　　　}

        #endregion
    }
}