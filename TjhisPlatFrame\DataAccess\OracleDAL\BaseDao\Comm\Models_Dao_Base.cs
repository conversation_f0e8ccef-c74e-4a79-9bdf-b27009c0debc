﻿using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using Utility;
using Utility.OracleODP;
using Oracle.ManagedDataAccess.Client;

namespace OracleDAL
{

    public class MODELS_Dao_Base
    {
        #region   Method
        public bool Exists(string CODE, OracleBaseClass db)
        {
            #region  init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from MODELS");
            strSql.Append(" where ");
            strSql.Append(" CODE = :CODE ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":CODE", OracleDbType.Varchar2, 4);
            p.Value = CODE;
            parameters.Add(p);

            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    int cmdresult;
                    cmdresult = int.Parse(ds.Tables[0].Rows[0][0].ToString());
                    if (cmdresult <= 0)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                    return false;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.MODELS model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into MODELS(");
            strSql.Append("CODE,NAME,SORT_NO,KEY,LAST_USED_KEY");
            strSql.Append(") values (");
            strSql.Append(":CODE,:NAME,:SORT_NO,:KEY,:LAST_USED_KEY");
            strSql.Append(") ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":CODE", OracleDbType.Varchar2, 4);
            p.Value = model.CODE;
            parameters.Add(p);

            p = new OracleParameter(":NAME", OracleDbType.Varchar2, 50);
            p.Value = model.NAME;
            parameters.Add(p);

            p = new OracleParameter(":SORT_NO", OracleDbType.Decimal, 3);
            p.Value = model.SORT_NO;
            parameters.Add(p);

            p = new OracleParameter(":KEY", OracleDbType.Varchar2, 50);
            p.Value = model.KEY;
            parameters.Add(p);

            p = new OracleParameter(":LAST_USED_KEY", OracleDbType.Varchar2, 50);
            p.Value = model.LAST_USED_KEY;
            parameters.Add(p);
            #endregion
            try
            {

                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.MODELS model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update MODELS set ");

            strSql.Append(" CODE = :CODE , ");
            strSql.Append(" NAME = :NAME , ");
            strSql.Append(" SORT_NO = :SORT_NO , ");
            strSql.Append(" KEY = :KEY , ");
            strSql.Append(" LAST_USED_KEY = :LAST_USED_KEY  ");
            strSql.Append(" where CODE=:CODE  ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":CODE", OracleDbType.Varchar2, 4);
            p.Value = model.CODE;
            parameters.Add(p);

            p = new OracleParameter(":NAME", OracleDbType.Varchar2, 50);
            p.Value = model.NAME;
            parameters.Add(p);

            p = new OracleParameter(":SORT_NO", OracleDbType.Decimal, 3);
            p.Value = model.SORT_NO;
            parameters.Add(p);

            p = new OracleParameter(":KEY", OracleDbType.Varchar2, 50);
            p.Value = model.KEY;
            parameters.Add(p);

            p = new OracleParameter(":LAST_USED_KEY", OracleDbType.Varchar2, 50);
            p.Value = model.LAST_USED_KEY;
            parameters.Add(p);
            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string CODE, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from MODELS ");
            strSql.Append(" where CODE=:CODE ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":CODE", OracleDbType.Varchar2, 4);
            p.Value = CODE;
            parameters.Add(p);

            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }



        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.MODELS GetModel(string CODE, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select CODE, NAME, SORT_NO, KEY, LAST_USED_KEY  ");
            strSql.Append("  from MODELS ");
            strSql.Append(" where CODE=:CODE ");
            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":CODE", OracleDbType.Varchar2, 4);
            p.Value = CODE;
            parameters.Add(p);
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;
                    Model.MODELS model = new Model.MODELS();

                    if (cmdresult > 0)
                    {
                        model = CopyToModel(ds.Tables[0].Rows[0]);
                        return model;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM MODELS ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得几行数据
        /// </summary>
        public DataSet GetList(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            #region 初始化参数
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM MODELS T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.MODELS> GetObservableCollection(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM MODELS ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.MODELS> list = new System.Collections.ObjectModel.ObservableCollection<Model.MODELS>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.MODELS model = new Model.MODELS();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表 
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.MODELS> GetObservableCollection(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM MODELS T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }

            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.MODELS> list = new System.Collections.ObjectModel.ObservableCollection<Model.MODELS>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.MODELS model = new Model.MODELS();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion   Method
        #region
        /// <summary>
        /// 
        /// </summary>
        protected Model.MODELS CopyToModel(DataRow dRow)
        {
            Model.MODELS model1 = new Model.MODELS();

            if (dRow["CODE"] != null && dRow["CODE"].ToString() != "")
            {
                model1.CODE = dRow["CODE"].ToString();
            }

            if (dRow["NAME"] != null && dRow["NAME"].ToString() != "")
            {
                model1.NAME = dRow["NAME"].ToString();
            }

            if (dRow["SORT_NO"] != null && dRow["SORT_NO"].ToString() != "")
            {
                model1.SORT_NO = decimal.Parse(dRow["SORT_NO"].ToString());
            }

            if (dRow["KEY"] != null && dRow["KEY"].ToString() != "")
            {
                model1.KEY = dRow["KEY"].ToString();
            }

            if (dRow["LAST_USED_KEY"] != null && dRow["LAST_USED_KEY"].ToString() != "")
            {
                model1.LAST_USED_KEY = dRow["LAST_USED_KEY"].ToString();
            }

            return model1;
        }
        #endregion

    }
}

