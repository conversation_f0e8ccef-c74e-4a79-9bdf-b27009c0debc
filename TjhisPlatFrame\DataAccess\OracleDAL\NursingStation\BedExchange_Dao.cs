﻿/*-----------------------------------------------------------------------
 * 类名称    ：BedExchange_Dao
 * 类描述    ：换床操作类
 * 创建人    ：梁吉lions
 * 创建时间  ：2016/6/3 16:47:02
 * 修改人    ：
 * 修改时间  ：
 * 修改备注  ：
 * 版本      ：
 * ----------------------------------------------------------------------
 */
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Utility.OracleODP;

namespace OracleDAL
{
    public class BedExchange_Dao
    {
        /// <summary>
        /// 换床位，如果床位都有人，则交换，否则换到空床
        /// </summary>
        /// <param name="bedNoFrom">需要换床的床号</param>
        /// <param name="bedStatusFrom">需要换床的床状态</param>
        /// <param name="bedLabelFrom">需要换床的床标号</param>
        /// <param name="patient_IDFrom">需要换床的患者</param>
        /// <param name="bedNoTo">目标床号</param>
        /// <param name="bedStatusTo">目标床状态</param>
        /// <param name="bedLabelTo">目标床标号</param>
        /// <param name="patient_IDTo">目标床患者</param>
        /// <param name="wardCode">所在护理单元</param>
        /// <param name="db">数据源</param>
        /// <returns>
        /// -9 打开数据库连接失败
        /// -1 数据库异常
        /// 0- 返回成功
        /// 1-换床者床是空床更新换床者床位状态失败
        /// 2-换床者床是空床更新被换床者床位状态失败
        /// 3-被换床者床是空床更新换床者床位状态失败
        /// 4-被换床者床是空床更新被换床者床位状态失败
        /// 5-换床者床已有患者更新患者换到被换床者床位失败
        /// 6-被换床者床已有患者更新患者换到换床者床位失败
        /// 7-说明换床者是母亲，有婴儿，更新婴儿床标号到被换床者失败
        /// 8-说明被换床者是母亲，有婴儿，更新婴儿床标号到换床者失败
        /// </returns>
        public int ExchangeBed(string bedNoFrom, string bedStatusFrom,string bedLabelFrom, string patient_IDFrom, string bedNoTo, string bedStatusTo, string bedLabelTo,string patient_IDTo, string wardCode)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            //返回值，默认0，成功
            int Result = 0;
            #endregion
            using (Utility.OracleODP.OracleBaseClass db = new Utility.OracleODP.OracleBaseClass())
            {
                if (db.OpenDB())
                {
                    try
                    {
                        //执行sql操作返回值
                        string retStr = "";
                        db.BeginTransaction();
                        //源床是空床，则更新源床状态1和目标床状态为0
                        if (!"1".Equals(bedStatusFrom))
                        {
                            strSql.Clear();
                            strSql.Append("UPDATE bed_rec SET bed_status = '1' WHERE (ward_code ='"+ wardCode+"') and (bed_no = "+ bedNoFrom+")");
                            retStr = db.ExecuteTransactionNOLimit(strSql.ToString());
                            if (!"".Equals(retStr))
                            {
                                //更新源床位状态失败
                                db.RollbackTransaction();
                                return 1;
                            }
                            strSql.Clear();
                            strSql.Append("UPDATE bed_rec SET bed_status = '0' WHERE (ward_code ='" + wardCode + "') and (bed_no = " + bedNoTo + ")");
                            db.ExecuteTransactionNOLimit(strSql.ToString());
                            if (!"".Equals(retStr))
                            {
                                //更新目标床位状态失败
                                db.RollbackTransaction();
                                return 2;
                            }
                        }

                        //目标床是空床，则更新目标床状态1和源床状态为0
                        if (!"1".Equals(bedStatusTo))
                        {
                            strSql.Clear();
                            strSql.Append("UPDATE bed_rec SET bed_status = '1' WHERE (ward_code ='" + wardCode + "') and (bed_no = " + bedNoTo + ")");
                            retStr = db.ExecuteTransactionNOLimit(strSql.ToString());
                            if (!"".Equals(retStr))
                            {
                                //更新源床位状态失败
                                db.RollbackTransaction();
                                return 3;
                            }
                            strSql.Clear();
                            strSql.Append("UPDATE bed_rec SET bed_status = '0' WHERE (ward_code ='" + wardCode + "') and (bed_no = " + bedNoFrom + ")");
                            db.ExecuteTransactionNOLimit(strSql.ToString());
                            if (!"".Equals(retStr))
                            {
                                //更新源床位状态失败
                                db.RollbackTransaction();
                                return 4;
                            }
                        }

                        //源床是已有患者，把患者换到目标床位
                        if (!string.IsNullOrEmpty(patient_IDFrom))
                        {
                            strSql.Clear();
                            strSql.Append("UPDATE pats_in_hospital SET bed_no = '"+ bedNoTo+"'	where patient_id = '"+ patient_IDFrom+"'");
                            retStr = db.ExecuteTransactionNOLimit(strSql.ToString());
                            if (!"".Equals(retStr))
                            {
                                //更新患者换到目标床位失败
                                db.RollbackTransaction();
                                return 5;
                            }
                           
                        }

                        //目标床是已有患者，把患者换到源床位
                        if (!string.IsNullOrEmpty(patient_IDTo))
                        {
                            strSql.Clear();
                            strSql.Append("UPDATE pats_in_hospital SET bed_no = '" + bedNoFrom + "'	where patient_id = '" + patient_IDTo + "'");
                            retStr = db.ExecuteTransactionNOLimit(strSql.ToString());
                            if (!"".Equals(retStr))
                            {
                                //更新患者换到源床位失败
                                db.RollbackTransaction();
                                return 6;
                            }

                        }

                        // 处理母亲换床问题
                        strSql.Clear();
                        strSql.Append("SELECT BED_REC.BED_NO, BED_REC.BED_LABEL FROM NEWBORN_REC, PATS_IN_HOSPITAL, BED_REC "
	                            + " WHERE PATS_IN_HOSPITAL.WARD_CODE = '"+ wardCode+"'"
                                + " AND BED_REC.WARD_CODE = '" + wardCode + "'"
                                + " AND NEWBORN_REC.PATIENT_ID_OF_MOTHER = '" + patient_IDFrom + "'"
                                + " AND NEWBORN_REC.PATIENT_ID = PATS_IN_HOSPITAL.PATIENT_ID "
                                + " AND PATS_IN_HOSPITAL.BED_NO = BED_REC.BED_NO");
                        System.Data.DataSet ds = db.SelectDataSet(strSql.ToString());
                        if (ds != null && ds.Tables[0].Rows.Count > 0)
                        {
                            // 说明换床者是母亲，有婴儿
                            string bed_No;
                            string bed_Label;
                            string newBed_Label;
                            for (int i = 0; i < ds.Tables[0].Rows.Count;i++ )
                            {
                                bed_No = "" + ds.Tables[0].Rows[i]["BED_NO"];
                                bed_Label = "" + ds.Tables[0].Rows[i]["BED_LABEL"];
                                newBed_Label =bedLabelTo + bed_Label.Substring(bed_Label.IndexOf("_"));
                                strSql.Clear();
                                strSql.Append("UPDATE BED_REC SET BED_LABEL = '" + newBed_Label + "'  WHERE (ward_code ='" + wardCode + "') and (bed_no = " + bed_No + ")");
                                retStr = db.ExecuteTransactionNOLimit(strSql.ToString());
                                if (!"".Equals(retStr))
                                {
                                    //更新婴儿床标号失败
                                    db.RollbackTransaction();
                                    return 7;
                                }
                            }
                        }

                        strSql.Clear();
                        strSql.Append("SELECT BED_REC.BED_NO, BED_REC.BED_LABEL FROM NEWBORN_REC, PATS_IN_HOSPITAL, BED_REC "
                                + " WHERE PATS_IN_HOSPITAL.WARD_CODE = '" + wardCode + "'"
                                + " AND BED_REC.WARD_CODE = '" + wardCode + "'"
                                + " AND NEWBORN_REC.PATIENT_ID_OF_MOTHER = '" + patient_IDTo + "'"
                                + " AND NEWBORN_REC.PATIENT_ID = PATS_IN_HOSPITAL.PATIENT_ID "
                                + " AND PATS_IN_HOSPITAL.BED_NO = BED_REC.BED_NO");
                        ds = db.SelectDataSet(strSql.ToString());
                        if (ds != null && ds.Tables[0].Rows.Count > 0)
                        {
                            // // 说明被换床者是母亲，有婴儿
                            string bed_No;
                            string bed_Label;
                            string newBed_Label;
                            for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
                            {
                                bed_No = "" + ds.Tables[0].Rows[i]["BED_NO"];
                                bed_Label = "" + ds.Tables[0].Rows[i]["BED_LABEL"];
                                newBed_Label = bedLabelFrom + bed_Label.Substring(bed_Label.IndexOf("_"));
                                strSql.Clear();
                                strSql.Append("UPDATE BED_REC SET BED_LABEL = '" + newBed_Label + "'  WHERE (ward_code ='" + wardCode + "') and (bed_no = " + bed_No + ")");
                                retStr = db.ExecuteTransactionNOLimit(strSql.ToString());
                                if (!"".Equals(retStr))
                                {
                                    //更新婴儿床标号失败
                                    db.RollbackTransaction();
                                    return 8;
                                }
                            }
                        }
                        db.CommitTransaction();


                    }
                    catch (Exception ex)
                    {
                        db.RollbackTransaction();
                        return -1;
                    }
                }
                else
                {
                    Result = -9;
                }
            }

            return Result;
        }
    }
}
