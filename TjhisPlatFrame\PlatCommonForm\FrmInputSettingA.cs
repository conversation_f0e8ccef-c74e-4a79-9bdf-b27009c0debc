﻿using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System;
using System.Collections;
/// <summary>
/// 输入发窗口 by lions 2018-09-14
/// </summary>
namespace PlatCommonForm
{
    public partial class FrmInputSettingA : PlatCommon.SysBase.ParentForm
    {
        private static bool IsDrag = false;
        private int enterX;
        private int enterY;
        private string inputType;
        private string inputTypeName;
        public FrmInputSettingA()
        {
            InitializeComponent();
        }
        public FrmInputSettingA(DataTable inputDs,DataTable inputstruct)
        {
            InitializeComponent();
            
            if (inputstruct != null && inputstruct.Rows.Count>0)
            {
                getInputSetting(inputstruct);
                DataRow[] inputypes = inputstruct.Select("INPUT_CODE<>'00'");
                DataTable intype = initInputtype();
                foreach (DataRow dr in inputypes)
                {
                    intype.Rows.Add(dr["DATA_COL"], dr["DATA_TITLE"]);
                }
                lookcolumn.Properties.DataSource = intype;
                lookcolumn.EditValue = "0";//默认全部
            }

            if (inputDs != null && inputDs.Rows.Count > 0)
            {
                gridControl.DataSource = inputDs;
                gridView.BestFitColumns();
            }
                

        }
        public string code { get; set; }
        public string name { get; set; }
        public string GS_INPUTSETING;//输入法
        public string GS_INPUT_TYPE;//输入码类型 五笔、拼音
        public string GS_STORAGES;//库房列表，字典是库房时使用
        public string GS_CONDITIONS;//其他条件
        public DataRow datarow { get; set; }
        DataTable dt = new DataTable();
        #region 定义方法
        private DataTable initInputtype()
        {
            DataTable dtinputType = new DataTable();
            dtinputType.Columns.Add("DATA_COL",typeof(string));
            dtinputType.Columns.Add("DATA_TITLE", typeof(string));
            dtinputType.Rows.Add("0","全部");//默认全部字段
            return dtinputType;
        }

        private void getInputSetting(DataTable dtinTypeSrc)
        {
            gridView.BeginUpdate();
            gridView.Columns.Clear();
            string ls_col_name="", ls_col_title="";
            foreach (DataRow dr in dtinTypeSrc.Rows)
            {
                //取列名
                ls_col_name = dr["DATA_COL"].ToString();
                
                ls_col_title = dr["DATA_TITLE"].ToString();
                gridView.Columns.AddVisible(ls_col_name.ToUpper(), ls_col_title);
            }
            gridView.EndUpdate();
        }
        /// <summary>
        /// 取输入法数据
        /// </summary>
        private void getInputSetting()
        {
            string ls_cols;    //显示列串
            string ls_col_name,name_col="";   //列名,名称列
            string ls_col_title;  //列标题;
            string ls_table_name,ls_tmp, ls_inputname="";
            string ls_sql = "";
            NM_Service.NMService.ServerPublicClient service = new NM_Service.NMService.ServerPublicClient();
            List<string> paras = new List<string>();
            ArrayList values = new ArrayList();
            #region 1、先查出输入发设置的表和字段
            string sql = "select * from comm.input_setting where dict_type=:GS_INPUTSETING order by show_sort";
            paras.Add("GS_INPUTSETING");
            values.Add(GS_INPUTSETING);
            DataSet ds = service.GetDataTable_Para(sql,paras,values);
            if (ds == null || ds.Tables[0].Rows.Count == 0) return;
            DataTable dstrut = ds.Tables[0].Copy();//备用
            #endregion
            #region 2、查出表里对应的字段的值
            ls_table_name = ds.Tables[0].Rows[0]["DATA_TABLE"].ToString();
            ls_cols = "";
            gridView.BeginUpdate();
            gridView.Columns.Clear();
            foreach (DataRow dr in ds.Tables[0].Rows)
            {
                //取列名
                ls_col_name = dr["DATA_COL"].ToString();
                //所有的列都列出来
                if (string.IsNullOrEmpty(ls_col_name))
                {
                    ls_cols += ls_col_name;
                }
                else
                {
                    ls_cols += ","+ls_col_name;
                }
                //取输入类型
                ls_tmp = dr["INPUT_CODE"].ToString();
                if (ls_tmp.Equals(this.GS_INPUT_TYPE))
                {
                    ls_inputname = ls_col_name;
                }
                //是否名称列
                ls_tmp = dr["FLAG_ISNAME"].ToString();
                if ("Y".Equals(ls_tmp))
                {
                    name_col = ls_col_name;
                }
                ls_col_title = dr["DATA_TITLE"].ToString();
                gridView.Columns.AddVisible(ls_col_name.ToUpper(), ls_col_title);
            }
            gridView.EndUpdate();
            if (string.IsNullOrEmpty(ls_cols))
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("没有选择数据列","提示",MessageBoxButtons.OK);
                return;
            }
            if (string.IsNullOrEmpty(name_col))
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("[" + GS_INPUTSETING + "]没有定义输入码类型为[" + GS_INPUT_TYPE + "]的输入码列", "提示", MessageBoxButtons.OK);
                return;
            }
            ls_cols = ls_cols.Remove(0, 1);
            ls_sql = "select " + ls_cols + " from " + ls_table_name;
            //药房条件
            if (!string.IsNullOrEmpty(GS_STORAGES)) {
                ls_sql += " where storage in (" + GS_STORAGES + ")";
            }

            //其他条件
            if (!string.IsNullOrEmpty(GS_CONDITIONS))
            {
                if (ls_sql.IndexOf("where") > 0)
                {
                    ls_sql = ls_sql + " and " + GS_CONDITIONS;
                }
                else
                {
                    ls_sql += " where  " + GS_CONDITIONS;
                }
                
            }
            //排序
            ls_sql += " order by " + ls_table_name +"."+ ls_inputname + " asc";
            //查询
            ds = service.GetDataBySql(ls_sql);
            
            gridControl.DataSource = ds.Tables[0];
            if (ds==null || ds.Tables[0].Rows.Count == 0)
            {
                return;
            }

            gridView.BestFitColumns();//适应窗口大小

            #endregion
            #region 3、输入码过滤 输入码类型
            if (dstrut != null && dstrut.Rows.Count > 0)
            {
                DataRow[] inputypes = dstrut.Select("INPUT_CODE<>'00'");
                DataTable intype = initInputtype();
                foreach (DataRow dr in inputypes)
                {
                    intype.Rows.Add(dr["DATA_COL"], dr["DATA_TITLE"]);
                }
                lookcolumn.Properties.DataSource = intype;
                lookcolumn.EditValue = "0";//默认全部
            }
            #endregion


        }

        private void LoadInputType()
        {
//            //装入输入码类型
//            datastore lds
//string ls_typename, ls_typecode
//string ls_sql
//long ll_row, ll_all, ll_tmp
//int i, j
//if is_input_type <> "" then
//      //清除
//      j = ddlb_1.TotalItems()

//    for i = j to 1 step - 1

//        ddlb_1.DeleteItem(i)

//    next
//    //当输入码类型已经定义了
//    lds = create datastore

//    ls_sql = "select * from comm.input_type " + &
//             "where input_code<>'00' " + &
//                "order by input_code"

//    if uf_create_ds(lds, ls_sql) = -1 then
//           destroy lds

//        return -1

//    end if


//    lds.settransobject(sqlca)

//    ll_all = lds.retrieve()

//    for ll_row = 1 to ll_all

//        ls_typename = lds.getitemstring(ll_row, "input_name")

//        ls_typecode = lds.getitemstring(ll_row, "input_code")

//        ddlb_1.additem(ls_typecode + "-" + ls_typename)

//        if ls_typecode = is_input_type then
//              ll_tmp = ll_row

//        end if

//    next

//    if ll_tmp = 0 then

//        messagebox("提示,", "您指定了无效的输入码类型(" + is_input_type + ")。")

//    end if

//    ddlb_1.selectitem(ll_tmp)


//    destroy lds
//end if

//return 0
        }

        #endregion

        private void sb_cancel_Click(Object sender, EventArgs e)
        {
            this.Close();
        }

        private void sb_clear_Click(Object sender, EventArgs e)
        {
            code = string.Empty;
            name = string.Empty;

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void sb_apply_Click(Object sender, EventArgs e)
        {
            if (gridView.DataRowCount == 0) return;
            datarow = gridView.GetDataRow(gridView.FocusedRowHandle);
            this.code = gridView.GetRowCellDisplayText(gridView.FocusedRowHandle,gridView.Columns[0]);
            this.name = gridView.GetRowCellDisplayText(gridView.FocusedRowHandle, gridView.Columns[1]);
            
            this.DialogResult = DialogResult.OK;
        }

        private void tE_search_KeyPress(Object sender, KeyPressEventArgs e)
        {
            gridView.FindFilterText = tE_search.Text.Trim();
            
        }

        private void FrmInputSettingA_Load(Object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(GS_INPUTSETING))
            {
                //DevExpress.XtraEditors.XtraMessageBox.Show("要查询的字典不能为空！", "提示", MessageBoxButtons.OK);
                return;
            }
            this.getInputSetting();
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            if (gridView.DataRowCount == 0) return;
            datarow = gridView.GetDataRow(gridView.FocusedRowHandle);
            this.code = gridView.GetRowCellDisplayText(gridView.FocusedRowHandle, gridView.Columns[0]);
            this.name = gridView.GetRowCellDisplayText(gridView.FocusedRowHandle, gridView.Columns[1]);
            this.DialogResult = DialogResult.OK;
        }

        private void gridControl_ProcessGridKey(object sender, KeyEventArgs e)
        {
            if (Keys.Enter.Equals(e.KeyCode))
            {
                //回车确认
                if (gridView.DataRowCount == 0) return;
                datarow = gridView.GetDataRow(gridView.FocusedRowHandle);
                this.code = gridView.GetRowCellDisplayText(gridView.FocusedRowHandle, gridView.Columns[0]);
                this.name = gridView.GetRowCellDisplayText(gridView.FocusedRowHandle, gridView.Columns[1]);
                this.DialogResult = DialogResult.OK;
            }
        }

        private void FrmInputSettingA_Shown(object sender, EventArgs e)
        {
            tE_search.Focus();
        }

        private void tE_search_EditValueChanging(object sender, DevExpress.XtraEditors.Controls.ChangingEventArgs e)
        {
            gridView.FindFilterText = tE_search.Text.Trim();
        }

        private void setForm_MouseDown(object sender, MouseEventArgs e)
        {
            IsDrag = true;
            enterX = e.Location.X;
            enterY = e.Location.Y;
        }
        private void setForm_MouseUp(object sender, MouseEventArgs e)
        {
            IsDrag = false;
            enterX = 0;
            enterY = 0;
        }
        private void setForm_MouseMove(object sender, MouseEventArgs e)
        {
            if (IsDrag)
            {
                Left += e.Location.X - enterX;
                Top += e.Location.Y - enterY;
            }
        }
        /// <summary>
        /// 设置过滤列
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lookcolumn_CloseUp(object sender, DevExpress.XtraEditors.Controls.CloseUpEventArgs e)
        {
            string datacol = e.Value == null ? "0" : e.Value.ToString();
            if ("0".Equals(datacol))
            {
                gridView.OptionsFind.FindFilterColumns = "*";
            }
            else
            {
                gridView.OptionsFind.FindFilterColumns = datacol;
            }
            gridView.FindFilterText = tE_search.Text.Trim();
        }
    }
}
