﻿/*-----------------------------------------------------------------------
 * 类名称    ：ILNCA_INTERFACE
 * 类描述    ：
 * 创建人    ：梁吉lions
 * 创建时间  ：2017/5/6 13:14:14
 * 修改人    ：
 * 修改时间  ：
 * 修改备注  ：
 * 版本      ：
 * ----------------------------------------------------------------------
 */
using System;
using System.Data;
using System.ServiceModel;
namespace INMService
{
    /// <summary>
    /// 接口层LNCA_INTERFACE
    /// </summary>
    [ServiceContract]
    public interface ILNCA_INTERFACE
    {
        #region  成员方法
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        [OperationContract]
        bool Exists_LNCA_INTERFACE(string LNCA_ID);
        /// <summary>
        /// 增加一条数据
        /// </summary>
        [OperationContract]
        bool Add_LNCA_INTERFACE(Model.LNCA_INTERFACE model);
        /// <summary>
        /// 更新一条数据
        /// </summary>
        [OperationContract]
        bool Update_LNCA_INTERFACE(Model.LNCA_INTERFACE model);
        /// <summary>
        /// 更新一条数据
        /// </summary>
        [OperationContract]
        bool UpdateByHisID_LNCA_INTERFACE(Model.LNCA_INTERFACE model);
        /// <summary>
        /// 删除数据
        /// </summary>
        [OperationContract]
        bool Delete_LNCA_INTERFACE(string LNCA_ID);
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        [OperationContract]
        Model.LNCA_INTERFACE GetModel_LNCA_INTERFACE(string LNCA_ID);
        /// <summary>
        /// 获得数据列表
        /// </summary>
        [OperationContract]
        DataSet GetList_All_LNCA_INTERFACE(string strWhere);
        /// <summary>
        /// 获得前几行数据
        /// </summary>
        [OperationContract]
        DataSet GetList_LNCA_INTERFACE(int startIndex, int endIndex, string strWhere, string filedOrder);
        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.LNCA_INTERFACE> GetObservableCollection_All_LNCA_INTERFACE(string strWhere);
        /// <summary>
        /// 获得ObservableCollection根据分页获得数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.LNCA_INTERFACE> GetObservableCollection_LNCA_INTERFACE(int startIndex, int endIndex, string strWhere, string filedOrder);
        #endregion  成员方法
    }
}