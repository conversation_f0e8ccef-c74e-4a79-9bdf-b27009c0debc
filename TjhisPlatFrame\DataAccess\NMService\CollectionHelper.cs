﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace NM_Service
{
    /// <summary>
    /// 集合帮助类
    /// </summary>
    public class CollectionHelper
    {
        /// <summary>
		/// 本对象不能实例化
		/// </summary>
        private CollectionHelper()
		{
        }


        ///// <summary>
        ///// 将Hashtable转换为List<HashItem>
        ///// </summary>
        ///// <param name="hasParams">hashtable</param>
        ///// <returns></returns>
        //static public ObservableCollection<HashItem> GetHashItem(Hashtable hasParams)
        //{
        //    ObservableCollection<HashItem> items = new ObservableCollection<HashItem>();
            
        //    foreach (DictionaryEntry entry in hasParams)
        //    {
        //        HashItem item = new HashItem();
        //        item.Name = entry.Key.ToString();
        //        item.Value = entry.Value;
        //        items.Add(item);
        //    }

        //    return items;
        //}


        ///// <summary>
        ///// 将List<HashItem> 转换为 Hashtable
        ///// </summary>
        ///// <param name="items"></param>
        ///// <returns></returns>
        //static public Hashtable GetHashtable(List<HashItem> items)
        //{
        //    Hashtable hasParams = new Hashtable();
        //    foreach (HashItem item in items)
        //    {
        //        hasParams[item.Name] = item.Value;
        //    }

        //    return hasParams;
        //}

        /// <summary>
        /// 向SQL集合中添加Sql
        /// </summary>
        /// <param name="idc"></param>
        /// <param name="sql"></param>
        /// <param name="errMsg"></param>
        public static void DictionaryAddItem(ref Dictionary<string, string> idc, string sql, string errMsg)
        {
            while (idc.ContainsKey(sql))
            {
                sql += " ";
            }

            idc.Add(sql, errMsg);
        }

    }
}

