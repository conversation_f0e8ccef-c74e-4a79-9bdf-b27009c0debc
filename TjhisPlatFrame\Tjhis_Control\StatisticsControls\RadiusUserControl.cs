﻿using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.Drawing.Drawing2D;
using System;

namespace Tjhis.Controls.StatisticsControls
{
    public partial class RadiusUserControl : UserControl
    {
        #region 控件圆角实现常量
        private const int LeftTopStartAngle = 180;
        private const int LeftTopSweepAngle = 90;
        private const int StartAngleRightTop = 270;
        private const int SweepAngleRightTop = 90;
        private const int StartAngleRightBottom = 0;
        private const int SweepAngleRightBottom = 90;
        private const int StartAngleLeftBottom = 90;
        private const int SweepAngleLeftBottom = 90;
        #endregion
        /// <summary>
        /// 上标题栏高度
        /// </summary>
        public const int TopBannerHeight = 79;
        private const int ButtonY = 31;

        #region 右侧按钮位置控制
        private const string RightButtonText = "...";
        private const int RightButtonHeight = 18;
        private const int RightButtonContentWidth = 10;
        private const int RightButtonBorderWidth = 12;
        #endregion
        [CategoryAttribute("设计"), DisplayNameAttribute("右侧按钮左边距")]
        public int ButtonMarginLeft { get; set; } = 10;
        [CategoryAttribute("设计"), DisplayNameAttribute("圆角")]
        public int Radius { get; set; } = 10;
        [CategoryAttribute("设计"), DisplayNameAttribute("标题")]
        public string TitleText { get; set; }
        [CategoryAttribute("设计"), DisplayNameAttribute("背景颜色")]
        public override Color BackColor { get => base.BackColor; set => base.BackColor = value; }

        [CategoryAttribute("设计"), DisplayNameAttribute("圆角内部颜色")]
        public Color RadiusColor { get; set; } = Color.White;
        [CategoryAttribute("设计"), DisplayNameAttribute("是否显示按钮")]
        public bool ShowRightButton { get; set; }


        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ExStyle &= ~0x02000000;
                return cp;
            }
        }

        private Font ButtonTextFont;

        private Rectangle RightButtonRect;
        private Rectangle RightButtonBackRect;
        private bool rightButtonRectOnMouse;
        private bool RightButtonRectOnMouse
        {
            get
            {
                return rightButtonRectOnMouse;
            }
            set
            {
                if(this.rightButtonRectOnMouse != value)
                {
                    this.Invalidate();
                }
                this.rightButtonRectOnMouse = value;
            }
        }
        public RadiusUserControl()
        {
            InitializeComponent();
            SetStyle(ControlStyles.UserPaint, true);
            SetStyle(ControlStyles.AllPaintingInWmPaint, true);
            SetStyle(ControlStyles.OptimizedDoubleBuffer, true);
            
            ButtonTextFont = new Font("微软雅黑", 12);
            RightButtonRectOnMouse = false;
            this.AutoScaleMode = AutoScaleMode.Dpi;
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            
            Graphics graphics = e.Graphics;
            Rectangle rectangle = e.ClipRectangle;
            
            graphics.SmoothingMode = SmoothingMode.AntiAlias;
            if (Radius > 0)
            {
                graphics.FillPath(new SolidBrush(RadiusColor), DrawRoundRect(rectangle.X, rectangle.Y, rectangle.Width, rectangle.Height));
            }

            if (!ShowRightButton)
            {
                return;
            }
            RightButtonRect = new Rectangle(this.Width - ButtonMarginLeft - RightButtonBorderWidth, ButtonY, RightButtonContentWidth, RightButtonHeight);
            RightButtonBackRect = new Rectangle(this.Width - ButtonMarginLeft - RightButtonBorderWidth, ButtonY, RightButtonBorderWidth, RightButtonHeight);
            StringFormat stringFormat;
            if (RightButtonRectOnMouse)
            {

                graphics.FillRectangle(new SolidBrush(Color.Gray), RightButtonBackRect);
                stringFormat = new StringFormat(StringFormatFlags.DirectionVertical);
                stringFormat.Alignment = StringAlignment.Center;
                stringFormat.LineAlignment = StringAlignment.Near;
                graphics.DrawString(RightButtonText, ButtonTextFont, new SolidBrush(Color.Black), RightButtonRect, stringFormat);
                stringFormat.Dispose();
            }
            else
            {
                stringFormat = new StringFormat(StringFormatFlags.DirectionVertical);
                stringFormat.Alignment = StringAlignment.Near;
                stringFormat.LineAlignment = StringAlignment.Near;
                graphics.DrawString(RightButtonText, ButtonTextFont, new SolidBrush(Color.Black), RightButtonRect, stringFormat);
                stringFormat.Dispose();
            }

        }

        protected override void OnMouseMove(MouseEventArgs e)
        {
            base.OnMouseMove(e);
            RightButtonRectOnMouse = this.RightButtonRect.Contains(e.Location);
        }

        protected override void OnMouseClick(MouseEventArgs e)
        {
            base.OnMouseClick(e);
            if (RightButtonRectOnMouse)
            {
                if(this.ContextMenuStrip != null)
                {
                    this.ContextMenuStrip.Show(this.RectangleToScreen(this.RightButtonRect).Location);
                }
            }
        }
        

        private GraphicsPath DrawRoundRect(int x,int y,int width,int height)
        {
            GraphicsPath graphicsPath = new GraphicsPath();
            graphicsPath.AddArc(x, y, Radius, Radius, LeftTopStartAngle, LeftTopSweepAngle);
            graphicsPath.AddArc(x + width - Radius, y, Radius, Radius, StartAngleRightTop, SweepAngleRightTop);
            graphicsPath.AddArc(x + width - Radius, y + height - Radius, Radius, Radius, StartAngleRightBottom, SweepAngleRightBottom);
            graphicsPath.AddArc(x, y + height - Radius, Radius, Radius, StartAngleLeftBottom, SweepAngleLeftBottom);
            return graphicsPath;
        }
        /// <summary>
        /// 设置内容控件的大小，在窗体大小改变时调用
        /// </summary>
        protected virtual void SetContentSize()
        {
        }

        /// <summary>
        /// 设置内容控件的大小，在窗体大小改变时调用
        /// </summary>
        [Obsolete("此方法已弃用，请使用SetContentSize()代替")]
        protected virtual void SetContentSize(Control control)
        {
            control.Width = this.Width;
            control.Height = this.Height - TopBannerHeight;
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            SetContentSize();
        }
        
        /// <summary>
        /// 加载数据
        /// </summary>
        public virtual void InitData() { }
        /// <summary>
        /// 释放资源
        /// </summary>
        public virtual void Clear()
        {
            if(this.ButtonTextFont != null)
            {
                this.ButtonTextFont.Dispose();
            }
        }

        protected virtual string GetLabelHtmlWithMarkText(int compareToYesterday)
        {
            if (compareToYesterday == 0)
            {
                return GetLabelHtmlText(compareToYesterday, TimeUnit);
            }
            string sign = compareToYesterday > 0 ? SignPlus : SignMinus;
            return string.Concat(string.Format(string.Format(LabelTextHtmlFontLargeSize, compareToYesterday), string.Format(LabelTextHtmlFontColor, sign)), GetLabelUnitString(TimeUnit));
        }

        protected virtual string GetLabelHtmlText(int value, string unit)
        {
            return string.Concat(string.Format(LabelTextHtmlFontLargeSize, value), GetLabelUnitString(unit));
        }

        protected virtual string GetLabelHtmlText(decimal value, string unit)
        {
            return string.Concat(string.Format(LabelTextHtmlFontLargeSize, value), GetLabelUnitString(unit));
        }

        protected virtual string GetLabelUnitString(string unit)
        {
            return string.Format(LabelTextHtmlFontColor, string.Format(LabelTextHtmlFontSamllSize, unit));
        }

        /// <summary>
        /// 设置同比控件值
        /// </summary>
        /// <param name="ucCompare"></param>
        /// <param name="compareValue"></param>
        protected virtual void SetucCompareValue(ucCompare ucCompare, decimal compareValue)
        {
            ucCompare.CompareValue = compareValue;
        }

        protected const string LabelTextHtmlFontLargeSize = "<size=17>{0}</size>";
        protected const string LabelTextHtmlFontSamllSize = "<size=11>{0}</size>";
        protected const string LabelTextHtmlFontColor = "<color=#A9B5D1>{0}</color>";
        protected const string PatUnit = "人次";
        protected virtual string TimeUnit { get; set; }  = "min";
        protected const string SignPlus = "+";
        protected const string SignMinus = "-";
        protected const string CNYUnit = "元";
        protected string[] WeekString = { "周日", "周一", "周二", "周三", "周四", "周五", "周六" };
        
    }
}
