﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Linq;
using OracleDAL;
using Utility;
using System.Diagnostics;
using System.Threading;
using licenseKey;
using System.IO;
using System.Text;
using System.Runtime.InteropServices;

namespace ClinicalPath
{ 
    /// <summary>
    /// MobileWard 的摘要说明
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    [ToolboxItem(false)]
    // 若要允许使用 ASP.NET AJAX 从脚本中调用此 Web 服务，请取消对下行的注释。
    // [System.Web.Script.Services.ScriptService]
    public class MobileWard : System.Web.Services.WebService
    {
        EnLicense lkey = new EnLicense();
        [WebMethod(Description = "获取病人住院的病历文件信息，包括病历文件名称、序号类型等")]
        public string GetEMRFileName(string patientID, string visitID)
        {
            string result;
            try
            {
                string text = this.CheckEMRExpire();
                if (text != "")
                {
                    result = text;
                }
                else
                {
                    string sql = "select a.file_no,a.topic,a.creator_name,a.create_date_time,a.create_display_date_time,a.file_flag,b.name file_attr,a.file_order from mr_file_index a " +
                        "left join mr_item_class b on a.file_attr=b.code where a.patient_id='" + patientID + "' and a.visit_id=" + visitID;
                    MR_FILE_INDEX serverPublic = new MR_FILE_INDEX();
                    DataTable dt = serverPublic.GetDataSetList(sql).Tables[0];
                    result = this.DataTable2Json(dt);
                }
            }
            catch (Exception ex)
            {
                result = "error:" + ex.ToString();
            }
            return result;
        }

        [WebMethod(Description = "获取病人门诊的病历文件信息，包括病历文件名称、序号类型等")]
        public string GetOutpEMRFileName(string visitDate, string visitNO)
        {
            string result;
            try
            {
                string text = this.CheckEMRExpire();
                if (text != "")
                {
                    result = text;
                }
                else
                {
                    string sql = "select a.file_no,a.topic,a.creator_name,a.file_flag,a.file_attr from outp_mr_file_index a " +
                        "left join mr_item_class b on a.file_attr=b.code where a.visit_date=to_date('" + visitDate.ToString().Split(' ')[0] + "','yyyy-MM-dd') and a.visit_no=" + visitNO;
                    MR_FILE_INDEX serverPublic = new MR_FILE_INDEX();
                    DataTable dt = serverPublic.GetDataSetList(sql).Tables[0];
                    result = this.DataTable2Json(dt);
                }
            }
            catch (Exception ex)
            {
                result = "error:" + ex.ToString();
            }
            return result;
        }
        private string DataTable2Json(DataTable dt)
        {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append("{\"");
            if (dt.Rows.Count > 0)
            {
                stringBuilder.Append("success\":[");
            }
            else
            {
                stringBuilder.Append("error\":[");
            }
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                stringBuilder.Append("{");
                for (int j = 0; j < dt.Columns.Count; j++)
                {
                    stringBuilder.Append("\"");
                    stringBuilder.Append(dt.Columns[j].ColumnName);
                    string value = string.Empty;
                    if (dt.Columns[j].ColumnName.ToUpper() == "TOPIC")
                    {
                        value = dt.Rows[i][j].ToString().Replace("'", "’").Replace("\"", "“");
                    }
                    else
                    {
                        value = dt.Rows[i][j].ToString();
                    }
                    stringBuilder.Append("\":\"");
                    stringBuilder.Append(value);
                    stringBuilder.Append("\",");
                }
                stringBuilder.Remove(stringBuilder.Length - 1, 1);
                stringBuilder.Append("},");
            }
            if (stringBuilder.ToString().EndsWith(","))
            {
                stringBuilder.Remove(stringBuilder.Length - 1, 1);
            }
            stringBuilder.Append("]");
            stringBuilder.Append("}");
            return stringBuilder.ToString();
        }
        /// <summary>
        /// 获取病人的病历文件另存的html的格式的文件的路径
        /// 如果文件未生成：1、查看控件是否注册成功
        ///                 2、路径配置是否正确
        ///                 3、iis目录下日志文件是否有报错
        /// </summary>
        /// <param name="patientID"></param>
        /// <param name="visitID"></param>
        /// <param name="fileNo"></param>
        /// <returns></returns>
        [WebMethod(Description = "获取住院病人的病历文件另存的html的格式的文件的路径,参数：病人ID，住院次数，文件顺序号")]
        public string GetPatientInpFilePath(string patientID, string visitID, string fileNo)
        {
            //string errorStr = CheckEMRExpire();
            //if (errorStr != "")
            //    return errorStr;
            string file_Name = string.Empty;
            string openPath = GetOpenFilePath(patientID, visitID, fileNo, ref file_Name);
            //if (!File.Exists(openPath))
            //{ return "未找到病历文件，查找路径：" + openPath; }
            string fileName = patientID + visitID + file_Name + ".html";
            string localPath = ConfigHelper.GetConfigString("MobileWardServer_Path");
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string str = patientID + ";" + file_Name + "&" + localPath + fileName; 
            string filePath = ConfigHelper.GetConfigString("ProcessStartTjEmrInfo_Path");
            StreamWrite(filePath + "args.txt", str);
            sw.Stop();
            Thread.Sleep(5000);
            return ConfigHelper.GetConfigString("HTML_URL") + fileName;
        }
        /// <summary>
        /// 获取门诊病人的病人的病历文件另存的html的格式的文件的路径
        /// 如果文件未生成：1、查看控件是否注册成功
        ///                 2、路径配置是否正确
        ///                 3、iis目录下日志文件是否有报错
        /// </summary>
        /// <param name="patientID"></param>
        /// <param name="visitID"></param>
        /// <param name="fileNo"></param>
        /// <returns></returns>
        [WebMethod(Description = "获取门诊病人的病历文件另存的html的格式的文件的路径,参数：病人ID，就诊日期,就诊序号，文件顺序号")]
        public string GetPatientOutpFilePath(string patientID,string visitDate, string visitNo, string fileNo)
        {
            //string errorStr = CheckEMRExpire();
            //if (errorStr != "")
            //    return errorStr;
            string file_Name = string.Empty;
            string openPath = GetOutpFilePath(visitDate, visitNo, fileNo,ref file_Name,ref patientID);
            //if (!System.IO.File.Exists(openPath))
            //{ return "未找到病历文件，查找路径：" + openPath; }
            string fileName = patientID + visitNo + file_Name + ".html";
            string localPath = ConfigHelper.GetConfigString("MobileWardServer_Path");
            Stopwatch sw = new Stopwatch();
            sw.Start();
            string str = patientID + ";" + file_Name + "&" + localPath + fileName;
            string filePath = ConfigHelper.GetConfigString("ProcessStartTjEmrInfo_Path");
            StreamWrite(filePath + "args.txt", str);
            sw.Stop();
            Thread.Sleep(5000);
            return ConfigHelper.GetConfigString("HTML_URL") + fileName;
        }
        private void StreamWrite(string fileName, string strValue)
        {

            if (!File.Exists(fileName))
            {
                FileStream fileStream = File.Create(fileName);
                fileStream.Close();
            }
            else
                Thread.Sleep(2000);
            StreamWriter streamWriter = new StreamWriter(fileName, false, Encoding.UTF8);
            streamWriter.Write(strValue);
            streamWriter.Close();
            streamWriter.Dispose();
        }
        //<summary>
        //判断授权码
        //</summary>
        //<returns></returns>
        public string CheckEMRExpire()
        {
            try
            {
                //判断授权码
                string reValue = "";
                bool isView = false;
                DataSet ds_hospital = null;
                string hosptial = "";
                //取医院名称
                HOSPITAL_CONFIG hos = new HOSPITAL_CONFIG();
                ds_hospital = hos.GetList("");
                if (ds_hospital != null && ds_hospital.Tables[0].Rows.Count > 0)
                {
                    hosptial = ds_hospital.Tables[0].Rows[0]["HOSPITAL"].ToString();
                }
                else
                {
                    return "未找到医院信息数据！";
                }
               // reValue =CheckAuthorization(hosptial, "EMRVIEW", "4.2", ref isView);
                //if (isView)
                //{
                     return "";
                //}
                //return reValue;// "授权验证不通过，请更换授权码！";
            }
            catch (Exception ex)
            {
                return "验证授权出错：" + ex.ToString();
            }
        }
        /// <summary>
        /// 检查应用程序的授权
        /// </summary>
        /// <returns></returns>
        public string CheckAuthorization(string hospitalName, string appCode, string appVersion, ref bool retflag)
        {
            KEY_DICT proxy = new KEY_DICT();
            // 获取授权码
            string sql = "select ENCRYPT_CODE from encrypt_dict " +
                          "where application_code = '" + appCode + "' and application_version = '" + appVersion + "'";
            string key = proxy.GetENCRYPT_CODE(sql);// dbConn.SelectValueBySql(sql);
            if (key.Trim().Length == 0)
            {
                retflag = false;
                return "您没有此子系统注册码，如果您要使用，请与供应商联系。";
            }
            // 解码
            key = DecryptNew(key);
            if (key.Trim().Length == 0)
            {
                retflag = false;
                return "解密错误，请与供应商联系。";
            }
            return CheckProduct(hospitalName, appCode, appVersion, key, ref retflag);
        }

            [DllImport(@"EncryptDecrypt.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode)]
            public static extern bool DecryptStringForCS(string cipher, StringBuilder outputText);

            /// <summary>
            /// 解密字符串
            /// </summary>
            /// <param name="Source">要解密的字符串</param>
            /// <param name="Key">密钥</param>
            /// <returns>解密后的字符串</returns>
            private static string DecryptNew(string src)
            {
                StringBuilder key = new StringBuilder(1000);
                string tmpStr = src.Replace("\r\n", "");
                if (DecryptStringForCS(tmpStr, key))
                {
                    return key.ToString();
                }
                else
                {
                    return string.Empty;
                }
            }
        private string CheckProduct(string hospitalName, string appCode, string appVersion, string key, ref bool retflag)
        {
            string temp = string.Empty;
            try
            {
                if (string.IsNullOrEmpty(key) || key.Trim().Length < 1)
                {
                    retflag = false;
                    return "无效的授权信息！";
                }
                string[] infos = key.Split(',');
                if (infos.Length < 5)
                {
                    retflag = false;
                    return "授权信息不完整！";
                }
                //医院信息
                // 判断医院名称
                if (hospitalName.Trim().Length == 0)
                {
                    retflag = false;
                    return "请设置医院名称！";
                }
                if (key.StartsWith(hospitalName) == false)
                {
                    retflag = false;
                    return "您没有购买天健电子病历系统[" + appCode + appVersion + "]，如果您要使用，请与供应商联系！";
                }
                //程序
                temp = infos[2];
                if (temp.StartsWith(appCode) == false)
                {
                    retflag = false;
                    return "您没有购买天健电子病历系统[" + appCode + appVersion + "]，如果您要使用，请与供应商联系！";
                }
                // 判断使用日期
                temp = infos[3];
                DateTime dtUsage = DateTime.Now;
                DateTime.TryParse(temp, out dtUsage);
                // 现在的日期
                DateTime dtNow = DateTime.Now;
                TimeSpan tspan = dtUsage.Subtract(dtNow);
                if (tspan.TotalDays < 30)
                {
                    if (tspan.TotalDays < 0)
                    {
                        retflag = false;
                        return "天健电子病历系统[" + appCode + appVersion + "]已过期，不能继续使用，请您立刻与供应商联系！";
                    }
                    else
                    {
                        retflag = true;
                        return " 天健电子病历系统[" + appCode + appVersion + "]将在[" + tspan.Days.ToString() + "]天后过期,请及时与供应商联系！";
                    }
                }
                //版本号
                temp = infos[4];
                if (!temp.Trim().Equals(appVersion))
                {
                    retflag = false;
                    return "您当前系统[" + appCode + appVersion + "]的版本号和授权信息中的版本号不符,请及时与供应商联系！";
                }
                retflag = true;
                return " ";
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }
        /// <summary>
        /// 取病历文件的路径
        /// </summary>
        /// <param name="patientID"></param>
        /// <param name="visitID"></param>
        /// <param name="fileNo"></param>
        /// <returns></returns>
        private string GetOpenFilePath(string patientID, string visitID, string fileNo,ref string fileName)
        {
            DataSet ds = null;
            MR_WORK_PATH proxy = new MR_WORK_PATH();
            ds = proxy.GetList(string.Empty);
            if (ds == null || ds.Tables[0].Rows.Count == 0)
                return "";
            DataRow dr = ds.Tables[0].Rows[0];
            string MR_PATH = dr["MR_PATH"].ToString();
            Model.MR_FILE_INDEX mr_file_index_model = null;
            MR_FILE_INDEX proxyfile = new MR_FILE_INDEX();
            mr_file_index_model = proxyfile.GetModel(patientID, Convert.ToInt32(visitID), Convert.ToInt32(fileNo));
            if (mr_file_index_model == null)
                return "";
            string openPath = MR_PATH + "\\" + GetPathbyPatientID(patientID) + mr_file_index_model.FILE_NAME;
            fileName = mr_file_index_model.FILE_NAME;
            return openPath;
        }
        /// <summary>
        /// 取病历文件的路径
        /// </summary>
        /// <param name="patientID"></param>
        /// <param name="visitID"></param>
        /// <param name="fileNo"></param>
        /// <returns></returns>
        private string GetOutpFilePath(string visitDate, string visitNo, string fileNo, ref string fileName,ref string patientID)
        {
            DataSet ds = null;
            MR_WORK_PATH proxy = new MR_WORK_PATH();
            ds = proxy.GetList(string.Empty);
            if (ds == null || ds.Tables[0].Rows.Count == 0)
                return "";
            DataRow dr = ds.Tables[0].Rows[0];
            string MR_PATH = dr["MR_PATH"].ToString();
            Model.OUTP_MR_FILE_INDEX mr_file_index_model = null;
            MR_FILE_INDEXDAL proxyfile = new MR_FILE_INDEXDAL();
            mr_file_index_model = proxyfile.GetModelOUTP(DateTime.Parse(visitDate),int.Parse(visitNo),int.Parse(fileNo));
            if (mr_file_index_model == null)
                return "";
            fileName = mr_file_index_model.FILE_NAME;
            patientID = mr_file_index_model.PATIENT_ID;
            string openPath = MR_PATH + "\\" +GetPathbyPatientID(patientID) + mr_file_index_model.FILE_NAME;
            return openPath;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="PatientID"></param>
        /// <returns></returns>
        public static string GetPathbyPatientID(string PatientID)
        {
            if (PatientID.Length < 2)
                PatientID = PatientID.PadLeft(2, '0');
            return PatientID.Substring(PatientID.Length - 2, 2) + @"\" + PatientID.Remove(PatientID.Length - 2).PadLeft(8, '0') + @"\";
        }
    }
}
