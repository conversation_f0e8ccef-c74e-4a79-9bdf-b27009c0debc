﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace INMService
{
    [ServiceContract]
    public interface IServerPublic
    {
        /// <summary>
        /// 取得服务器的时间
        /// </summary>
        [OperationContract]        
        DateTime GetSysDate();
        /// <summary>
        ///  获得诊断别名数据列表
        /// </summary>
        [OperationContract]
        DataSet GetList(string strSql);
        /// <summary>
        /// 获得返回值
        /// </summary>
        [OperationContract]
        String GetSingleValue(string strSql);
        /// <summary>
        /// 更新多个DataSet
        /// </summary>
        [OperationContract]
        bool SaveTablesData(System.Collections.ArrayList dsList);
        /// <summary>
        /// 获取表数据
        /// </summary>
        [OperationContract]
        DataSet GetTableData(string tableName, string strWhere);
        /// <summary>
        /// 根据SQL语句获得一个DataSet
        /// </summary>
        [OperationContract]
        DataSet GetDataBySql(string sql);

        DataSet GetDataBySql_Tran(string sql);
        
        /// <summary>
        /// 获取存储过程数据集
        /// </summary>
        [OperationContract]
        DataSet GetProcedureDataSet(string procedureName, List<string> paramKey, List<string> paramValue, string tableName, string sql);
        /// <summary>
        /// 获取存储过程数据集
        /// </summary>
        [OperationContract]
        bool GetPubicProcedureDs(string procedureName, List<string> paramKey, List<string> paramValue, ref string para1, ref String para2, ref DataTable para3);
        /// <summary>
        /// 保存数据集
        /// </summary>
        /// <param name="dataset"></param>
        /// <returns>-1 数据库异常，0 保存失败，>0 成功</returns>
        [OperationContract]
        int SaveDataSet(DataSet dataset);
        [OperationContract]
        int SaveDataSet(DataSet dataset, Utility.OracleODP.OracleBaseClass db);
        /// <summary>
        /// 执行多条sql语句，事务提交
        /// </summary>
        /// <param name="list">语句列表</param>
        /// <returns></returns>
        [OperationContract]
        string SaveTable(Dictionary<string, string> list);
        [OperationContract]
        string GetSysDateTime();
        [OperationContract]
        string SaveExcSql(Dictionary<string, string> list, Utility.OracleODP.OracleBaseClass db);
        [OperationContract]
        DataSet GetListUseDB(string sql, Utility.OracleODP.OracleBaseClass db);
        [OperationContract]
        string SaveDataSetAndDictionary(DataSet dataset, Dictionary<string, string> list);
        /// <summary>
        /// 参数方式查询sql语句
        /// </summary>
        /// <param name="sql">查询sql语句</param>
        /// <param name="ParaList">参数名称列表</param>
        /// <param name="ValueList">参数值列表</param>
        /// <returns></returns>
        [OperationContract]
        DataSet GetDataTable_Para(string sql, List<string> ParaList, System.Collections.ArrayList ValueList);
        [OperationContract]
        DataSet GetDataTable_Para_tran(string sql, List<string> ParaList, System.Collections.ArrayList ValueList);
        [OperationContract]
        DataSet GetDataTable_Para(string sql, List<string> ParaList, ArrayList ValueList, Utility.OracleODP.OracleBaseClass db);

    }
}
