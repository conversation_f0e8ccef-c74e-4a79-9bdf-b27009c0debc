﻿/*********************************************
* 文 件 名：Cs02XMLSetting
* 类 名 称：Cs02XMLSetting
* 功能说明：系统设置XML文件类
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：刘成刚
* 创建时间：2019-05-18 19:41:55
* 版 本 号：1.0.0.1
* 修 改 人：陆丕刚
* 修改时间：2020-02-18
* CLR 版本：4.0.30319.42000
/*********************************************/

using System;
using System.Collections.Generic;
using System.IO;
using System.Xml;
using System.Data;
using System.Text;
using System.Web;

namespace PlatCommon.Base02
{
    /// <summary>
    /// 系统设置XML文件类
    /// </summary>
    public static class Cs02XMLSetting
    {
        /// <summary>
        /// 加载XML文件
        /// </summary>
        /// <param name="strXmlFileName">XML文件名</param>
        /// <returns></returns>
        private static XmlDocument GetDocument(string strXmlFileName)
        {
            XmlDocument xmlDoc = new XmlDocument();
            XmlReaderSettings settings = new XmlReaderSettings();
            //settings.IgnoreComments = true;
            try
            {
                XmlReader reader = XmlReader.Create(strXmlFileName, settings);
                xmlDoc.Load(reader);
                reader.Close();
                return xmlDoc;
            }
            catch (Exception ex)
            {
                Cs02MessageBox.ShowError("XML打开失败！\r\n错误信息：" + ex.Message);
                return null;
            }

        }

        /// <summary>
        /// 读取某一节(Section)的某一个值(Key)
        /// </summary>
        /// <param name="strXmlFileName">XML文件名</param>
        /// <param name="strSection">Section</param>
        /// <param name="strKey">Key</param>
        /// <param name="strDefalutValue">缺省值</param>
        /// <returns></returns>
        public static string ReadKey(string strXmlFileName, string strSection, string strKey, string strDefalutValue)
        {
            if (string.IsNullOrEmpty(strXmlFileName))
            {
                Cs02MessageBox.ShowError("请输入文件名！");
                return strDefalutValue;
            }

            if (!File.Exists(strXmlFileName))
                return strDefalutValue;

            XmlDocument xmlDoc = GetDocument(strXmlFileName);
            if (xmlDoc == null)
                return strDefalutValue;

            //查找根<function>
            XmlNode root = xmlDoc.DocumentElement;
            if (root == null)
            {
                return strDefalutValue;
            }
            var keyNode = root.SelectSingleNode($"{strSection}/{strKey}");
            var strValue = keyNode?.InnerText ?? strDefalutValue;
            //获取到根<function>的所有的子节点
            // XmlNodeList sectionList = root.ChildNodes;


            ////遍历第一层节点，找到Section
            //foreach (XmlNode sectionNode in sectionList)
            //{
            //    if (sectionNode.Name.ToLower() != strSection.ToLower()) continue;

            //    //找到了，进行取值
            //    XmlNodeList keyList = sectionNode.ChildNodes;
            //    foreach (XmlNode keyNode in keyList)
            //    {
            //        //查找KEY
            //        if (keyNode.Name.ToLower() != strKey.ToLower()) continue;
            //        strValue = keyNode.InnerText;
            //        break;
            //    }

            //    //取值完了，退出
            //    break;
            //}
            return strValue;
        }

        /// <summary>
        /// 设置某一节(Section)的某一个值(Key)的值
        /// </summary>
        /// <param name="strXmlFileName">XML文件名</param>
        /// <param name="strSection">Section</param>
        /// <param name="strKey">Key</param>
        /// <param name="strValue">值</param>
        /// <returns></returns>
        public static bool WriteKey(string strXmlFileName, string strSection, string strKey, string strValue)
        {
            if (string.IsNullOrEmpty(strXmlFileName))
            {
                Cs02MessageBox.ShowError("请输入文件名！");
                return false;
            }

            XmlDocument xmlDoc;
            if (!File.Exists(strXmlFileName))
            {
                //文件不存在
                xmlDoc = new XmlDocument();
                xmlDoc.LoadXml("<?xml version=\"1.0\" encoding=\"gb2312\" ?>" +
                                "<Setting>" +
                                "</Setting>");
            }
            else
            {
                xmlDoc = GetDocument(strXmlFileName);
                if (xmlDoc == null)
                    return false;
            }

            string xPath = $"{strSection}/{strKey}";
            //查找根<Setting>
            XmlNode rootNode = xmlDoc.DocumentElement;
            if (rootNode == null)
            {
                rootNode = xmlDoc.CreateElement("Setting");
                xmlDoc.AppendChild(rootNode);
            }
            var xmlNode = xmlDoc.DocumentElement?.SelectSingleNode(xPath);
            if (xmlNode != null)
            {
                xmlNode.InnerText = strValue;
                xmlDoc.Save(strXmlFileName);
            }
            else
            {
                string[] temps = xPath.Split('/');
                XmlNode parentNode = xmlDoc.DocumentElement;
                for (int i = 0; i < temps.Length; i++)
                {
                    xmlNode = parentNode?.SelectSingleNode(temps[i]);
                    if (xmlNode == null)
                    {
                        xmlNode = xmlDoc.CreateElement(temps[i]);
                        parentNode?.AppendChild(xmlNode);
                        parentNode = xmlNode;
                    }
                    else
                    {
                        parentNode = xmlNode;
                    }
                }
                xmlNode = xmlDoc.DocumentElement?.SelectSingleNode(xPath);
                if (xmlNode != null)
                {
                    xmlNode.InnerText = strValue;
                    xmlDoc.Save(strXmlFileName);
                }
            }
            //bool bFindSection = false;
            //bool bFindKey = false;
            ////获取到根<Setting>的所有的子节点
            //XmlNodeList SectionList = rootNode.ChildNodes;

            ////遍历第一层节点，找到Section
            //foreach (XmlNode SectionNode in SectionList)
            //{
            //    if (SectionNode.Name.ToLower() != strSection.ToLower()) continue;

            //    //找到了Section，查找KEY
            //    XmlNodeList KeyList = SectionNode.ChildNodes;
            //    foreach (XmlNode KeyNode in KeyList)
            //    {
            //        //查找KEY
            //        if (KeyNode.Name.ToLower() != strKey.ToLower()) continue;

            //        //找到了KEY，进行赋值
            //        KeyNode.InnerText = strValue;
            //        bFindKey = true;
            //        break;
            //    }

            //    if (!bFindKey)
            //    {
            //        //没有找到Key，则生成Key,再加入Section
            //        XmlElement KeyElem = xmlDoc.CreateElement(strKey);
            //        KeyElem.InnerText = strValue;

            //        //Key加入Section
            //        SectionNode.AppendChild(KeyElem);
            //    }
            //    //赋值完了，退出
            //    bFindSection = true;
            //    break;
            //}
            //if (!bFindSection)
            //{
            //    //生成Section
            //    XmlElement SectionElem = xmlDoc.CreateElement(strSection);

            //    //生成Key
            //    XmlElement KeyElem = xmlDoc.CreateElement(strKey);
            //    KeyElem.InnerText = strValue;

            //    //Key加入Section
            //    SectionElem.AppendChild(KeyElem);

            //    //Section加入根
            //    rootNode.AppendChild(SectionElem);
            //}
            //xmlDoc.Save(strXmlFileName);

            return true;
        }

        /// <summary>
        /// 读取某一节(Section)的所有值(Key)
        /// </summary>
        /// <param name="strXmlFileName">XML文件名</param>
        /// <param name="strSection">Section</param>
        /// <returns></returns>
        public static Dictionary<string, string> ReadSection(string strXmlFileName, string strSection)
        {
            if (string.IsNullOrEmpty(strXmlFileName))
            {
                Cs02MessageBox.ShowError("请输入文件名！");
                return null;
            }

            Dictionary<string, string> keyDictionary = new Dictionary<string, string>();
            if (!File.Exists(strXmlFileName))
                return keyDictionary;

            XmlDocument xmlDoc = GetDocument(strXmlFileName);
            if (xmlDoc == null) return keyDictionary;

            //查找根<function>
            XmlNode root = xmlDoc.DocumentElement;
            if (root == null)
            {
                return keyDictionary;
            }
            //获取到根<function>的所有的子节点
            XmlNode sectionNode = root.SelectSingleNode($"{strSection}");
            if (sectionNode == null)
            {
                return keyDictionary;
            }
            foreach (XmlNode keyNode in sectionNode.ChildNodes)
            {
                keyDictionary.Add(keyNode.Name, keyNode.InnerText.Trim());
            }

            ////遍历第一层节点，找到Section
            //foreach (XmlNode sectionNode in sectionList)
            //{
            //    if (sectionNode.Name.ToLower() != strSection.ToLower()) continue;

            //    //找到了，进行取值
            //    XmlNodeList keyList = sectionNode.ChildNodes;
            //    foreach (XmlNode keyNode in keyList)
            //    {
            //        //查找KEY
            //        keyDictionary.Add(keyNode.Name, keyNode.InnerText.Trim());
            //    }

            //    //取值完了，退出
            //    break;
            //}

            return keyDictionary;
        }

        /// <summary>
        /// 设置某一节(Section)的所有值(Key)
        /// </summary>
        /// <param name="strXmlFileName">XML文件名</param>
        /// <param name="strSection">Section</param>
        /// <param name="keyDictionary">数据字典</param>
        /// <returns></returns>
        public static bool WriteSection(string strXmlFileName, string strSection, Dictionary<string, string> keyDictionary)
        {
            if (string.IsNullOrEmpty(strXmlFileName))
            {
                Cs02MessageBox.ShowError("请输入文件名！");
                return false;
            }

            if (keyDictionary == null || keyDictionary.Keys.Count < 1)
            {
                return true;
            }

            XmlDocument xmlDoc;
            if (!File.Exists(strXmlFileName))
            {
                //文件不存在
                xmlDoc = new XmlDocument();
                xmlDoc.LoadXml("<?xml version=\"1.0\" encoding=\"gb2312\" ?>" +
                                "<Setting>" +
                                "</Setting>");
            }
            else
            {
                xmlDoc = GetDocument(strXmlFileName);
                if (xmlDoc == null)
                    return false;
            }

            //查找根<function>
            XmlNode rootNode = xmlDoc.SelectSingleNode("Setting");
            if (rootNode == null)
            {
                rootNode = xmlDoc.CreateElement("Setting");
                xmlDoc.AppendChild(rootNode);
            }
            //获取到根<function>的所有的子节点
            XmlNodeList sectionList = rootNode.ChildNodes;

            bool bFindSection = false;
            //遍历第一层节点，找到Section
            foreach (XmlNode sectionNode in sectionList)
            {
                if (sectionNode.Name.ToLower() != strSection.ToLower()) continue;

                //找到了，进行处理
                bFindSection = true;
                XmlNodeList keyList = sectionNode.ChildNodes;
                //对KeyDictionary 进行循环

                foreach (var keyItem in keyDictionary)
                {
                    var bFindKey = false;
                    foreach (XmlNode keyNode in keyList)
                    {
                        //查找KEY
                        if (keyNode.Name.ToLower() != keyItem.Key.ToLower()) continue;

                        //找到了KEY，进行赋值
                        keyNode.InnerText = keyItem.Value;
                        bFindKey = true;
                        break;
                    }

                    if (!bFindKey)
                    {
                        //没有找到Key，则生成Key,再加入Section
                        XmlElement KeyElem = xmlDoc.CreateElement(keyItem.Key);
                        KeyElem.InnerText = keyItem.Value;

                        //Key加入Section
                        sectionNode.AppendChild(KeyElem);
                    }
                }

                //赋值完了，退出
                bFindSection = true;
                break;
            }

            //没有找到Section
            if (!bFindSection)
            {
                //生成Section
                XmlElement sectionElem = xmlDoc.CreateElement(strSection);

                foreach (var keyItem in keyDictionary)
                {
                    //生成Key
                    XmlElement keyElem = xmlDoc.CreateElement(keyItem.Key);
                    keyElem.InnerText = keyItem.Value;

                    //Key加入Section
                    sectionElem.AppendChild(keyElem);
                }

                //Section加入根
                rootNode.AppendChild(sectionElem);
            }

            xmlDoc.Save(strXmlFileName);

            return true;
        }

        /// <summary>
        /// 读取XML到DataTable
        /// </summary>
        /// <param name="strXmlFileName">XML文件名</param>
        /// <returns></returns>
        public static DataTable ReadXmlToDataTable(string strXmlFileName)
        {
            XmlReaderSettings settings = new XmlReaderSettings();
            XmlReader reader = null;
            settings.IgnoreComments = true;
            try
            {
                reader = XmlReader.Create(strXmlFileName, settings);
                DataSet dsXmlData = new DataSet();
                dsXmlData.ReadXml(reader);
                return dsXmlData.Tables[0];
            }
            catch (Exception ex)
            {
                Cs02MessageBox.ShowError("XML打开失败！\r\n错误信息：" + ex.Message);
                return null;
            }
            finally
            {
                reader.Close();
            }
        }

        /// <summary>
        /// 读取Xml串某一节(Section)的某一个值(Key)
        /// </summary>
        /// <param name="strSourceXML">源XML串</param>
        /// <param name="strSection">Section</param>
        /// <param name="strKey">Key</param>
        /// <param name="strDefalutValue">缺省值</param>
        public static string ReadXMLKeyFromStr(string strSourceXML, string strSection, string strKey, string strDefalutValue)
        {
            if (string.IsNullOrEmpty(strSourceXML))
                return strDefalutValue;

            XmlDocument xmlDoc = new XmlDocument();
            try
            {
                xmlDoc.LoadXml(strSourceXML);
            }
            catch (Exception)
            {
                return strDefalutValue;
            }

            if (xmlDoc == null)
                return strDefalutValue;

            //查找根<function>
            XmlNode root = xmlDoc.DocumentElement;
            if (root == null)
            {
                return strDefalutValue;
            }
            var keyNode = root.SelectSingleNode($"{strSection}/{strKey}");
            var strValue = keyNode?.InnerText ?? strDefalutValue;

            return strValue;
        }

        /// <summary>
        /// 读取Xml串某一节(Section)的所有值(Key)
        /// </summary>
        /// <param name="strSourceXML">源XML串</param>
        /// <param name="strSection">节</param>
        /// <returns></returns>
        public static Dictionary<string, string> ReadSectionFromStr(string strSourceXML, string strSection)
        {
            Dictionary<string, string> dicSection = new Dictionary<string, string>();

            if (string.IsNullOrEmpty(strSourceXML))
                return dicSection;

            XmlDocument xmlDoc = new XmlDocument();
            try
            {
                xmlDoc.LoadXml(strSourceXML);
            }
            catch (Exception)
            {
            }

            //查找根<function>
            XmlNode root = xmlDoc.DocumentElement;
            if (root == null)
            {
                return dicSection;
            }

            //获取到根的所有的子节点
            XmlNode sectionNode = null;
            if (strSection.IndexOf(',') <= 0)
            {
                sectionNode = root.SelectSingleNode($"{strSection}");
            }
            else
            {
                XmlNode nodeCurrent = root;
                string[] strArrSection = strSection.Split(',');
                for (int i = 0; i < strArrSection.Length; i++)
                {
                    nodeCurrent = nodeCurrent.SelectSingleNode($"{strArrSection[i]}");
                }
                sectionNode = nodeCurrent;
            }

            if (sectionNode == null)
            {
                return dicSection;
            }

            foreach (XmlNode keyNode in sectionNode.ChildNodes)
            {
                dicSection.Add(keyNode.Name, keyNode.InnerText.Trim());
            }

            return dicSection;
        }


        /// <summary> 
        /// 将DataTable对象转换成XML字符串 
        /// </summary> 
        /// <param name="dtSource">DataTable对象</param> 
        /// <returns>XML字符串</returns> 
        public static string DataTableToXml(DataTable dtSource)
        {
            if (dtSource == null || dtSource.Rows.Count < 1)
            {
                return string.Empty;
            }

            MemoryStream ms = null;
            XmlTextWriter XmlWt = null;
            try
            {
                ms = new MemoryStream();
                //根据ms实例化XmlWt 
                XmlWt = new XmlTextWriter(ms, Encoding.Unicode);

                //获取ds中的数据 
                dtSource.WriteXml(XmlWt);
                int iLength = (int)ms.Length;
                byte[] bArrData = new byte[iLength];
                ms.Seek(0, SeekOrigin.Begin);
                ms.Read(bArrData, 0, iLength);

                //返回Unicode编码的文本 
                UnicodeEncoding ucode = new UnicodeEncoding();
                return ucode.GetString(bArrData).Trim();
            }
            catch (System.Exception ex)
            {
                throw ex;
            }
            finally
            {
                //释放资源 
                if (XmlWt != null)
                {
                    XmlWt.Close();
                    ms.Close();
                    ms.Dispose();
                }
            }

        }

        /// <summary> 
        /// 将DataSet对象中指定的Table转换成XML字符串 
        /// </summary> 
        /// <param name="dsSource">DataSet对象</param> 
        /// <param name="iTableIndex">DataSet对象中的Table索引</param> 
        /// <returns>XML字符串</returns> 
        public static string DataSetToXml(DataSet dsSource, int iTableIndex = 0)
        {
            if (dsSource == null || dsSource.Tables.Count < 1)
            {
                return string.Empty;
            }

            if (iTableIndex >= 0 && iTableIndex < dsSource.Tables.Count)
            {
                return DataTableToXml(dsSource.Tables[iTableIndex]);
            }
            else
            {
                return DataTableToXml(dsSource.Tables[0]);
            }
        }

        /// <summary> 
        /// 将DataView对象转换成XML字符串 
        /// </summary> 
        /// <param name="dvSource">DataView对象</param> 
        /// <returns>XML字符串</returns> 
        public static string DataViewToXml(DataView dvSource)
        {
            return DataTableToXml(dvSource.Table);
        }

        /// <summary> 
        /// 将DataTable对象数据保存为XML文件 
        /// </summary> 
        /// <param name="dtSource">DataSet</param> 
        /// <param name="strFilePath">XML文件路径</param> 
        /// <returns>bool值</returns> 
        public static bool DataTableToXmlFile(DataTable dtSource, string strFilePath)
        {
            if ((dtSource == null) || (!string.IsNullOrEmpty(strFilePath)))
            {
                return false;
            }

            string strFileFullPath = HttpContext.Current.Server.MapPath(strFilePath);
            MemoryStream ms = null;
            XmlTextWriter XmlWt = null;
            try
            {
                ms = new MemoryStream();
                //根据ms实例化XmlWt 
                XmlWt = new XmlTextWriter(ms, Encoding.Unicode);

                //获取ds中的数据 
                dtSource.WriteXml(XmlWt);
                int iLength = (int)ms.Length;
                byte[] bArrData = new byte[iLength];
                ms.Seek(0, SeekOrigin.Begin);
                ms.Read(bArrData, 0, iLength);
                //返回Unicode编码的文本 
                UnicodeEncoding ucode = new UnicodeEncoding();

                //写文件 
                StreamWriter sw = new StreamWriter(strFileFullPath);
                sw.WriteLine("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
                sw.WriteLine(ucode.GetString(bArrData).Trim());
                sw.Close();
                return true;
            }
            catch (System.Exception ex)
            {
                throw ex;
            }
            finally
            {
                //释放资源 
                if (XmlWt != null)
                {
                    XmlWt.Close();
                    ms.Close();
                    ms.Dispose();
                }
            }

        }

        /// <summary> 
        /// 将DataSet对象中指定的Table转换成XML文件 
        /// </summary> 
        /// <param name="dsSource">DataSet对象</param> 
        /// <param name="iTableIndex">DataSet对象中的Table索引</param> 
        /// <param name="strFilePath">xml文件路径</param> 
        /// <returns>bool]值</returns> 
        public static bool DataSetToXmlFile(DataSet dsSource, int iTableIndex, string strFilePath)
        {
            if (dsSource == null || dsSource.Tables.Count < 1)
            {
                return false;
            }

            if (iTableIndex >= 0 && iTableIndex < dsSource.Tables.Count)
            {
                return DataTableToXmlFile(dsSource.Tables[iTableIndex], strFilePath);
            }
            else
            {
                return DataTableToXmlFile(dsSource.Tables[0], strFilePath);
            }
        }

        /// <summary> 
        /// 将DataView对象转换成XML文件 
        /// </summary> 
        /// <param name="dvSource">DataView对象</param> 
        /// <param name="strFilePath">xml文件路径</param> 
        /// <returns>bool]值</returns> 
        public static bool DataViewToXmlFile(DataView dvSource, string strFilePath)
        {
            return DataTableToXmlFile(dvSource.Table, strFilePath);
        }

        /// <summary> 
        /// 将Xml串转换成DataSet对象 
        /// </summary> 
        /// <param name="strXML">Xml内容字符串</param> 
        /// <returns>DataSet对象</returns> 
        public static DataSet XmlToDataSet(string strXML)
        {
            if (string.IsNullOrEmpty(strXML))
            {
                return null;
            }

            StringReader StrStream = null;
            XmlTextReader Xmlrdr = null;
            try
            {
                DataSet ds = new DataSet();
                //读取字符串中的信息 
                StrStream = new StringReader(strXML);
                //获取StrStream中的数据 
                Xmlrdr = new XmlTextReader(StrStream);
                //ds获取Xmlrdr中的数据                
                ds.ReadXml(Xmlrdr);
                return ds;
            }
            catch (Exception e)
            {
                throw e;
            }
            finally
            {
                //释放资源 
                if (Xmlrdr != null)
                {
                    Xmlrdr.Close();
                    StrStream.Close();
                    StrStream.Dispose();
                }
            }

        }

        ///<summary> 
        /// 将Xml字符串转换成DataTable对象 
        /// </summary> 
        /// <param name="strXML">Xml字符串</param> 
        /// <returns>DataTable对象</returns> 
        public static DataTable XmlToDataTable(string strXML)
        {
            DataSet dsReturn = XmlToDataSet(strXML);
            if (dsReturn == null)
            {
                return null;
            }
            return dsReturn.Tables[0];
        }

        /// <summary> 
        /// 读取Xml串信息,并转换成DataSet对象 
        /// </summary> 
        /// <param name="strFilePath">Xml文件地址</param> 
        /// <returns>DataSet对象</returns> 
        public static DataSet XmlFileToDataSet(string strFilePath)
        {
            if (!string.IsNullOrEmpty(strFilePath))
            {
                return null;
            }

            string strFileFullPath = HttpContext.Current.Server.MapPath(strFilePath);
            if (!File.Exists(strFileFullPath))
            {
                return null;
            }

            StringReader StrStream = null;
            XmlTextReader Xmlrdr = null;
            try
            {
                XmlDocument xmldoc = new XmlDocument();
                //根据地址加载Xml文件 
                xmldoc.Load(strFileFullPath);

                DataSet dsReturn = new DataSet();
                //读取文件中的字符流 
                StrStream = new StringReader(xmldoc.InnerXml);

                //获取StrStream中的数据 
                Xmlrdr = new XmlTextReader(StrStream);

                //ds获取Xmlrdr中的数据 
                dsReturn.ReadXml(Xmlrdr);
                return dsReturn;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                //释放资源 
                if (Xmlrdr != null)
                {
                    Xmlrdr.Close();
                    StrStream.Close();
                    StrStream.Dispose();
                }
            }
        }

    }


}
