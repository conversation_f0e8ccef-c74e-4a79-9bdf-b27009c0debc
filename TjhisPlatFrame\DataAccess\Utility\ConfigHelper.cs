﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Configuration;
using System.Web;
using System.Data;
using System.Windows.Forms;
using System.Xml;
using System.IO;

namespace Utility
{
    public class ConfigHelper
    {
        #region GetConfiguration

        public static string GetConfiguration(string key)
        {
            Configuration configuration = null;                 //Configuration对象
            AppSettingsSection appSection = null;               //AppSection对象 

            configuration = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);

            //取得AppSetting节
            appSection = configuration.AppSettings;

            //赋值并保存
            return appSection.Settings[key].Value;
        }
        
        /**//// <summary>
        /// 取得appSettings里的值
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>值</returns>
        public static string GetConfigString(string key)
        {
            return ConfigurationManager.AppSettings[key];
        }

        /// <summary>
        /// 得到AppSettings中的配置bool信息.
        /// Gets the config bool.
        /// </summary>
        /// <param name="key">The key.</param>
        /// <returns></returns>
        public static bool GetConfigBool(string key)
        {
            bool result = false;
            string cfgVal = GetConfigString(key);
            if (null != cfgVal && string.Empty != cfgVal)
            {
                try
                {
                    result = bool.Parse(cfgVal);
                }
                catch (FormatException)
                {
                    // Ignore format exceptions.
                }
            }

            return result;
        }

        /// <summary>
        /// 得到AppSettings中的配置decimal信息.
        /// Gets the config decimal.
        /// </summary>
        /// <param name="Key">The key.</param>
        /// <returns></returns>
        public static decimal GetConfigDecimal(string Key)
        {
            decimal result = 0;
            string cfgVal = GetConfigString(Key);
            if (null != cfgVal && string.Empty != cfgVal)
            {
                try
                {
                    result = decimal.Parse(cfgVal);
                }
                catch (FormatException)
                {
                    // Ignore format exceptions.
                }
            }

            return result;
        }

        /// <summary>
        /// 得到AppSettings中的配置int信息.
        /// Gets the config int.
        /// </summary>
        /// <param name="key">The key.</param>
        /// <returns></returns>
        public static int GetConfigInt(string key)
        {
            int result = 0;
            string cfgVal = GetConfigString(key);
            if (null != cfgVal && string.Empty != cfgVal)
            {
                try
                {
                    result = int.Parse(cfgVal);
                }
                catch (FormatException)
                {
                    // Ignore format exceptions.
                }
            }

            return result;
        }
        #endregion

        #region GetConfigurationList
        /**//// <summary>
        /// 取得appSettings里的值列表
        /// </summary>
        /// <param name="filePath">配置文件路径</param>
        /// <returns>值列表</returns>
        public static KeyValueConfigurationCollection GetConfigurationList(string filePath)
        {
            AppSettingsSection appSection = null;                       //AppSection对象
            Configuration configuration = null;                         //Configuration对象     
            KeyValueConfigurationCollection k = null;                   //返回的键值对类型

            configuration = ConfigurationManager.OpenExeConfiguration(filePath);

            //取得AppSettings节
            appSection = (AppSettingsSection)configuration.Sections["appSettings"];

            //取得AppSetting节的键值对
            k = appSection.Settings;

            return k;
                    
        }
        #endregion

       #region SetConfiguration
        /**//// <summary>
        /// 设置appSetting的值
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        /// <param name="filePath">App.config文件路径</param>
        public static void SetConfiguration(string key, string value,string filePath)
        {
            Configuration configuration = null;                 //Configuration对象
            AppSettingsSection appSection = null;               //AppSection对象 
          
            configuration = ConfigurationManager.OpenExeConfiguration(filePath);
            
            //取得AppSetting节
            appSection = configuration.AppSettings;

            //赋值并保存
            appSection.Settings[key].Value = value;
            configuration.Save(ConfigurationSaveMode.Modified);
            

        }

        public static void SetConfiguration(string key, string value)
        {
            Configuration configuration = null;                 //Configuration对象
            AppSettingsSection appSection = null;               //AppSection对象 

            configuration = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);

            //取得AppSetting节
            appSection = configuration.AppSettings;

            //赋值并保存
            appSection.Settings[key].Value = value;
            configuration.Save(ConfigurationSaveMode.Modified);

        }
        #endregion

        #region GetConnectionString

        public static string GetConnectionString()
        {
            return ConfigurationManager.AppSettings["connectionString"];
        }
        #endregion 
        #region 读config
        /// <summary>
        /// 
        /// </summary>
        /// <param name="EncryptPwd"></param>
        /// <returns></returns>
        private static String DecryptHIS(String EncryptPwd)
        {
            String ret = "";
            int k = 0;
            String lsstr2;
            if (String.IsNullOrEmpty(EncryptPwd))
            {
                return ret;
            }
            for (int i = 0; i < EncryptPwd.Length; i++)
            {
                lsstr2 = EncryptPwd.Substring(i, 1);
                k = Convert.ToByte(char.Parse(lsstr2));
                if ((i % 2) != 0)
                {
                    k = k - i - 1 + 32;
                }
                else
                {
                    k = k + i + 1 - 8;
                }

                ret = ret + Convert.ToChar(k);
            }

            return ret;

        }
        /// <summary>
        /// 获取配置文件中的数据
        /// </summary>
        /// <returns></returns>
        public static string GetConfigConnectionStr()
        {
            string connectring = string.Empty;
            try
            {
                ///////////////////////////////////////////////////
                // 改为从xml文档中读取数据库配置
                // 2014-03-16
                ///////////////////////////////////////////////////
                string fileName = Environment.CurrentDirectory + @"\Config\connection.xml";
                DataSet ds = new DataSet();
                ds.ReadXml(fileName);
                DataTable table = ds.Tables["connection"];
                DataRow row = table.Rows[0];
                string DBConnectionMode= row["DBConnectionMode"].ToString();
                if(string.IsNullOrEmpty(DBConnectionMode)) DBConnectionMode="0";
                UntilityConstant.DBConnectionMode = int.Parse(DBConnectionMode);
                string ip = row["ip"].ToString();
                string server = row["server"].ToString();
                string user = row["user"].ToString();
                string password = row["password"].ToString();
                byte[] convertByte = Convert.FromBase64String(password);
                string conpwd = Encoding.UTF8.GetString(convertByte);
                password = DecryptHIS(conpwd);
                string port = row["port"].ToString();
                connectring = "Data Source=" + ip + ":" + port + "/" + server + ";User ID=" + user + ";Password=" + password;
                string dbCharsetUS7ASCII = row["DbCharsetUS7ASCII"].ToString();
                if (DBConnectionMode.Equals("0"))
                {
                    connectring += $";Unicode={"0".Equals(dbCharsetUS7ASCII)};";
                }
                if (DBConnectionMode.Equals("2"))
                {
                    connectring += $";Unicode={"0".Equals(dbCharsetUS7ASCII)};";
                }
          //      connectring = "Provider=MSDAORA.1;Data Source=(DESCRIPTION =(ADDRESS_LIST =(ADDRESS = (PROTOCOL = TCP)" +
          //"(HOST = " + ip + ")(PORT = " + port + ")))(CONNECT_DATA =(SERVICE_NAME = " + server + ")));" +
          //"User ID=" + user + ";Password=" + password + ";";
                return connectring;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message) ;
                return ex.Message;
            }
        }
        #endregion


        public static string GetConfigByXml(string xmlname, string tablename, string keys)
        {
            string values = "";
            try
            {
                string fileName = Environment.CurrentDirectory + @"\Config\" + xmlname;
                DataSet ds = new DataSet();
                ds.ReadXml(fileName);
                DataTable table = ds.Tables[tablename];
                DataRow row = table.Rows[0];
                values = row[keys].ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                return "";
            }
            return values;
        }
        public static string SetConfigByXml(string xmlname, string tablename, string keys, string values)
        {

            try
            {
                string fileName = Environment.CurrentDirectory + @"\Config\" + xmlname;
                DataSet ds = new DataSet();
                ds.ReadXml(fileName);
                DataTable table = ds.Tables[tablename];
                DataRow row = table.Rows[0];
                row[keys] = values;
                ds.WriteXml(fileName);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                return "";
            }
            return values;
        }

    }
}
