﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 15
VisualStudioVersion = 15.0.28307.1684
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "INMService", "INMService\INMService.csproj", "{222241CE-14AC-427B-92B9-0C307AE0FC24}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Model", "MODEL\Model.csproj", "{9F136B85-9E87-42E5-B33B-A1BCEEFB6022}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NMService", "NMService\NMService.csproj", "{CD4F72F4-1735-4B92-961D-E5D3CD4F7EBD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Utility", "Utility\Utility.csproj", "{480DE8D0-5D9C-41DF-8FD6-8772E5655552}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OracleDAL", "OracleDAL\OracleDAL.csproj", "{FE5E4667-03E5-4F69-ACD9-4374542A38D3}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{222241CE-14AC-427B-92B9-0C307AE0FC24}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{222241CE-14AC-427B-92B9-0C307AE0FC24}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{222241CE-14AC-427B-92B9-0C307AE0FC24}.Debug|x86.ActiveCfg = Debug|x86
		{222241CE-14AC-427B-92B9-0C307AE0FC24}.Debug|x86.Build.0 = Debug|x86
		{222241CE-14AC-427B-92B9-0C307AE0FC24}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{222241CE-14AC-427B-92B9-0C307AE0FC24}.Release|Any CPU.Build.0 = Release|Any CPU
		{222241CE-14AC-427B-92B9-0C307AE0FC24}.Release|x86.ActiveCfg = Release|x86
		{222241CE-14AC-427B-92B9-0C307AE0FC24}.Release|x86.Build.0 = Release|x86
		{9F136B85-9E87-42E5-B33B-A1BCEEFB6022}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9F136B85-9E87-42E5-B33B-A1BCEEFB6022}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9F136B85-9E87-42E5-B33B-A1BCEEFB6022}.Debug|x86.ActiveCfg = Debug|Any CPU
		{9F136B85-9E87-42E5-B33B-A1BCEEFB6022}.Debug|x86.Build.0 = Debug|Any CPU
		{9F136B85-9E87-42E5-B33B-A1BCEEFB6022}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9F136B85-9E87-42E5-B33B-A1BCEEFB6022}.Release|Any CPU.Build.0 = Release|Any CPU
		{9F136B85-9E87-42E5-B33B-A1BCEEFB6022}.Release|x86.ActiveCfg = Release|Any CPU
		{9F136B85-9E87-42E5-B33B-A1BCEEFB6022}.Release|x86.Build.0 = Release|Any CPU
		{CD4F72F4-1735-4B92-961D-E5D3CD4F7EBD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CD4F72F4-1735-4B92-961D-E5D3CD4F7EBD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CD4F72F4-1735-4B92-961D-E5D3CD4F7EBD}.Debug|x86.ActiveCfg = Debug|x86
		{CD4F72F4-1735-4B92-961D-E5D3CD4F7EBD}.Debug|x86.Build.0 = Debug|x86
		{CD4F72F4-1735-4B92-961D-E5D3CD4F7EBD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CD4F72F4-1735-4B92-961D-E5D3CD4F7EBD}.Release|Any CPU.Build.0 = Release|Any CPU
		{CD4F72F4-1735-4B92-961D-E5D3CD4F7EBD}.Release|x86.ActiveCfg = Release|x86
		{CD4F72F4-1735-4B92-961D-E5D3CD4F7EBD}.Release|x86.Build.0 = Release|x86
		{480DE8D0-5D9C-41DF-8FD6-8772E5655552}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{480DE8D0-5D9C-41DF-8FD6-8772E5655552}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{480DE8D0-5D9C-41DF-8FD6-8772E5655552}.Debug|x86.ActiveCfg = Debug|x86
		{480DE8D0-5D9C-41DF-8FD6-8772E5655552}.Debug|x86.Build.0 = Debug|x86
		{480DE8D0-5D9C-41DF-8FD6-8772E5655552}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{480DE8D0-5D9C-41DF-8FD6-8772E5655552}.Release|Any CPU.Build.0 = Release|Any CPU
		{480DE8D0-5D9C-41DF-8FD6-8772E5655552}.Release|x86.ActiveCfg = Release|x86
		{480DE8D0-5D9C-41DF-8FD6-8772E5655552}.Release|x86.Build.0 = Release|x86
		{FE5E4667-03E5-4F69-ACD9-4374542A38D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FE5E4667-03E5-4F69-ACD9-4374542A38D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FE5E4667-03E5-4F69-ACD9-4374542A38D3}.Debug|x86.ActiveCfg = Debug|x86
		{FE5E4667-03E5-4F69-ACD9-4374542A38D3}.Debug|x86.Build.0 = Debug|x86
		{FE5E4667-03E5-4F69-ACD9-4374542A38D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FE5E4667-03E5-4F69-ACD9-4374542A38D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{FE5E4667-03E5-4F69-ACD9-4374542A38D3}.Release|x86.ActiveCfg = Release|x86
		{FE5E4667-03E5-4F69-ACD9-4374542A38D3}.Release|x86.Build.0 = Release|x86
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {1DA91C30-25C2-4312-96AF-EBD538AF700F}
	EndGlobalSection
EndGlobal
