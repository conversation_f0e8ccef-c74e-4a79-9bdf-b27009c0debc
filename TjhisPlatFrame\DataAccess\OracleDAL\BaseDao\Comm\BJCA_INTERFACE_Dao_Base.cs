﻿/*-----------------------------------------------------------------------
 * 类名称    ：Class1
 * 类描述    ：
 * 创建人    ：梁吉lions
 * 创建时间  ：2016/9/18 10:12:59
 * 修改人    ：
 * 修改时间  ：
 * 修改备注  ：
 * 版本      ：
 * ----------------------------------------------------------------------
 */
using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using Utility;
using Utility.OracleODP;
using Oracle.ManagedDataAccess.Client;

namespace OracleDAL
{

    /// <summary>
    /// 数字签名 数据库操作类
    /// </summary>

    public class BJCA_INTERFACE_Dao_Base
    {
        #region   Method
        public bool Exists(string BJCA_ID, OracleBaseClass db)
        {
            #region  init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from COMM.BJCA_INTERFACE");
            strSql.Append(" where ");
            strSql.Append(" BJCA_ID = :BJCA_ID ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":BJCA_ID", OracleDbType.Varchar2, 50);
            p.Value = BJCA_ID;
            parameters.Add(p);

            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    int cmdresult;
                    cmdresult = int.Parse(ds.Tables[0].Rows[0][0].ToString());
                    if (cmdresult <= 0)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                    return false;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.BJCA_INTERFACE model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into COMM.BJCA_INTERFACE(");
            strSql.Append("BJCA_ID,BJCA_INFO,HIS_ID,BJCA_IMG,BJCA_IMG_STR,CA_ID");
            strSql.Append(") values (");
            strSql.Append(":BJCA_ID,:BJCA_INFO,:HIS_ID,:BJCA_IMG,:BJCA_IMG_STR,:CA_ID");
            strSql.Append(") ");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":BJCA_ID", OracleDbType.Varchar2, 50);
            p.Value = model.BJCA_ID;
            parameters.Add(p);

            p = new OracleParameter(":BJCA_INFO", OracleDbType.Clob, 4000);
            p.Value = model.BJCA_INFO;
            parameters.Add(p);

            p = new OracleParameter(":HIS_ID", OracleDbType.Varchar2, 50);
            p.Value = model.HIS_ID;
            parameters.Add(p);

            p = new OracleParameter(":BJCA_IMG", OracleDbType.Blob);
            p.Value = model.BJCA_IMG;
            parameters.Add(p);

            p = new OracleParameter(":BJCA_IMG_STR", OracleDbType.Varchar2, 4000);
            p.Value = model.BJCA_IMG_STR;
            parameters.Add(p);

            p = new OracleParameter(":CA_ID", OracleDbType.Varchar2, 50);
            p.Value = model.CA_ID;
            parameters.Add(p);
            #endregion
            try
            {

                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.BJCA_INTERFACE model, OracleBaseClass db)
        {
            #region init parameters
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update COMM.BJCA_INTERFACE set ");

            strSql.Append(" BJCA_ID = :BJCA_ID , ");
            strSql.Append(" BJCA_INFO = :BJCA_INFO , ");
            strSql.Append(" HIS_ID = :HIS_ID , ");
            strSql.Append(" BJCA_IMG = :BJCA_IMG , ");
            strSql.Append(" BJCA_IMG_STR = :BJCA_IMG_STR , ");
            strSql.Append(" CA_ID = :CA_ID  ");
            strSql.Append(" where BJCA_ID=:BJCA_ID and HIS_ID=: HIS_ID  BJCA_INTERFACE");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":BJCA_ID", OracleDbType.Varchar2, 50);
            p.Value = model.BJCA_ID;
            parameters.Add(p);

            p = new OracleParameter(":BJCA_INFO", OracleDbType.Clob, 4000);
            p.Value = model.BJCA_INFO;
            parameters.Add(p);

            p = new OracleParameter(":HIS_ID", OracleDbType.Varchar2, 50);
            p.Value = model.HIS_ID;
            parameters.Add(p);

            p = new OracleParameter(":BJCA_IMG", OracleDbType.Blob);
            p.Value = model.BJCA_IMG;
            parameters.Add(p);

            p = new OracleParameter(":BJCA_IMG_STR", OracleDbType.Varchar2, 4000);
            p.Value = model.BJCA_IMG_STR;
            parameters.Add(p);

            p = new OracleParameter(":CA_ID", OracleDbType.Varchar2, 50);
            p.Value = model.CA_ID;
            parameters.Add(p);
            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string BJCA_ID,string HIS_ID ,OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from COMM.BJCA_INTERFACE ");
            strSql.Append(" where BJCA_ID=:BJCA_ID and HIS_ID=:HIS_ID");

            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":BJCA_ID", OracleDbType.Varchar2, 50);
            p.Value = BJCA_ID;
            parameters.Add(p);
            p = new OracleParameter(":HIS_ID", OracleDbType.Varchar2, 50);
            p.Value = HIS_ID;
            parameters.Add(p);
            #endregion
            try
            {
                return db.ExecuteTransaction(strSql.ToString(), parameters);
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }



        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.BJCA_INTERFACE GetModel(string BJCA_ID, OracleBaseClass db)
        {
            #region 初始化参数
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select BJCA_ID, BJCA_INFO, HIS_ID, BJCA_IMG, BJCA_IMG_STR, CA_ID  ");
            strSql.Append("  from COMM.BJCA_INTERFACE ");
            strSql.Append(" where BJCA_ID=:BJCA_ID ");
            List<OracleParameter> parameters = new List<OracleParameter>();
            OracleParameter p;

            p = new OracleParameter(":BJCA_ID", OracleDbType.Varchar2, 50);
            p.Value = BJCA_ID;
            parameters.Add(p);
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString(), parameters);
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;
                    Model.BJCA_INTERFACE model = new Model.BJCA_INTERFACE();

                    if (cmdresult > 0)
                    {
                        model = CopyToModel(ds.Tables[0].Rows[0]);
                        return model;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }

        }


        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM COMM.BJCA_INTERFACE ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得几行数据
        /// </summary>
        public DataSet GetList(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            #region 初始化参数
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM COMM.BJCA_INTERFACE T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }
            if (strWhere.Trim() != "")
            {
                strSql.Append("  and " + strWhere);
            }
            #endregion
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.BJCA_INTERFACE> GetObservableCollection(string strWhere, OracleBaseClass db)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM COMM.BJCA_INTERFACE ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.BJCA_INTERFACE> list = new System.Collections.ObjectModel.ObservableCollection<Model.BJCA_INTERFACE>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.BJCA_INTERFACE model = new Model.BJCA_INTERFACE();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }

        /// <summary>
        /// 获得ObservableCollection数据列表 
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<Model.BJCA_INTERFACE> GetObservableCollection(int startIndex, int endIndex, string strWhere, string filedOrder, OracleBaseClass db)
        {
            String strOrder = "";
            if (!string.IsNullOrEmpty(filedOrder) && !"".Equals(filedOrder.Trim()))
            {
                strOrder = " order by " + filedOrder;
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            strSql.Append(" * ");
            strSql.Append(" FROM (SELECT T.*,ROWNUM rn FROM COMM.BJCA_INTERFACE T " + strOrder + ") ");
            if (endIndex <= 0)
            {
                strSql.Append(" where rn >= " + startIndex);
            }
            else
            {
                strSql.Append(" where rn >= " + startIndex + " AND rn <= " + endIndex);
            }

            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            try
            {
                DataSet ds = db.SelectDataSet(strSql.ToString());
                if (ds != null && ds.Tables.Count > 0)
                {
                    int cmdresult = ds.Tables[0].Rows.Count;

                    if (cmdresult > 0)
                    {
                        System.Collections.ObjectModel.ObservableCollection<Model.BJCA_INTERFACE> list = new System.Collections.ObjectModel.ObservableCollection<Model.BJCA_INTERFACE>();
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            Model.BJCA_INTERFACE model = new Model.BJCA_INTERFACE();
                            model = CopyToModel(dr);
                            list.Add(model);
                        }
                        return list;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + strSql.ToString());
            }
        }
        #endregion   Method
        #region
        /// <summary>
        /// 
        /// </summary>
        protected Model.BJCA_INTERFACE CopyToModel(DataRow dRow)
        {
            Model.BJCA_INTERFACE model1 = new Model.BJCA_INTERFACE();

            if (dRow["BJCA_ID"] != null && dRow["BJCA_ID"].ToString() != "")
            {
                model1.BJCA_ID = dRow["BJCA_ID"].ToString();
            }

            if (dRow["HIS_ID"] != null && dRow["HIS_ID"].ToString() != "")
            {
                model1.HIS_ID = dRow["HIS_ID"].ToString();
            }

            if (dRow["BJCA_IMG"] != null && dRow["BJCA_IMG"].ToString() != "")
            {
                model1.BJCA_IMG = (byte[])dRow["BJCA_IMG"];
            }

            if (dRow["BJCA_IMG_STR"] != null && dRow["BJCA_IMG_STR"].ToString() != "")
            {
                model1.BJCA_IMG_STR = dRow["BJCA_IMG_STR"].ToString();
            }

            if (dRow["CA_ID"] != null && dRow["CA_ID"].ToString() != "")
            {
                model1.CA_ID = dRow["CA_ID"].ToString();
            }

            return model1;
        }
        #endregion

    }
}

