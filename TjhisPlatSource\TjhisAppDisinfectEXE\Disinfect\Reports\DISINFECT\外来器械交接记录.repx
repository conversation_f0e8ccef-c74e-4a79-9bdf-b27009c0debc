/// <XRTypeInfo>
///   <AssemblyFullName>DevExpress.XtraReports.v18.1, Version=18.1.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</AssemblyFullName>
///   <AssemblyLocation>C:\Windows\Microsoft.Net\assembly\GAC_MSIL\DevExpress.XtraReports.v18.1\v4.0_18.1.12.0__b88d1754d700e49a\DevExpress.XtraReports.v18.1.dll</AssemblyLocation>
///   <TypeName>DevExpress.XtraReports.UI.XtraReport</TypeName>
///   <Localization>zh-Hans</Localization>
///   <Version>18.1</Version>
///   <Resources>
///     <Resource Name="XtraReportSerialization.XtraReport">
/// zsrvvgEAAACRAAAAbFN5c3RlbS5SZXNvdXJjZXMuUmVzb3VyY2VSZWFkZXIsIG1zY29ybGliLCBWZXJzaW9uPTQuMC4wLjAsIEN1bHR1cmU9bmV1dHJhbCwgUHVibGljS2V5VG9rZW49Yjc3YTVjNTYxOTM0ZTA4OSNTeXN0ZW0uUmVzb3VyY2VzLlJ1bnRpbWVSZXNvdXJjZVNldAIAAAACAAAAAAAAAFBBRFBBRFA141seO0eBSgAAAAAxAAAADAEAACwkAHQAaABpAHMALgBEAGEAdABhAFMAbwB1AHIAYwBlAFMAYwBoAGUAbQBhAAAAAAASJAB0AGgAaQBzAC4AVABhAGcA0goAAAHPFTw/eG1sIHZlcnNpb249IjEuMCI/Pg0KPHhzOnNjaGVtYSBpZD0iTmV3RGF0YVNldCIgeG1sbnM9IiIgeG1sbnM6eHM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDEvWE1MU2NoZW1hIiB4
/// bWxuczptc2RhdGE9InVybjpzY2hlbWFzLW1pY3Jvc29mdC1jb206eG1sLW1zZGF0YSIgeG1sbnM6bXNwcm9wPSJ1cm46c2NoZW1hcy1taWNyb3NvZnQtY29tOnhtbC1tc3Byb3AiPg0KICA8eHM6ZWxlbWVudCBuYW1lPSJOZXdEYXRhU2V0IiBtc2RhdGE6SXNEYXRhU2V0PSJ0cnVlIiBtc2RhdGE6VXNlQ3VycmVudExvY2FsZT0idHJ1ZSI+DQogICAgPHhzOmNvbXBsZXhUeXBlPg0KICAgICAgPHhzOmNob2ljZSBtaW5PY2N1cnM9IjAiIG1heE9jY3Vycz0idW5ib3VuZGVkIj4NCiAgICAgICAgPHhzOmVsZW1lbnQgbmFtZT0iVGFibGVEYXRhIiBtc3Byb3A6QmFzZVRhYmxlLjI9IkJFRF9SRUMiIG1zcHJvcDpCYXNlVGFibGUuMD0iUFJFUEFZTUVOVF9SQ1BUIiBt
/// c3Byb3A6QmFzZVRhYmxlLjE9IlBBVF9NQVNURVJfSU5ERVgiPg0KICAgICAgICAgIDx4czpjb21wbGV4VHlwZT4NCiAgICAgICAgICAgIDx4czpzZXF1ZW5jZT4NCiAgICAgICAgICAgICAgPHhzOmVsZW1lbnQgbmFtZT0iSE9TUElUQUxfTkFNRSIgbXNkYXRhOlJlYWRPbmx5PSJ0cnVlIiBtc3Byb3A6T3JhRGJUeXBlPSIxMjYiIG1pbk9jY3Vycz0iMCI+DQogICAgICAgICAgICAgICAgPHhzOnNpbXBsZVR5cGU+DQogICAgICAgICAgICAgICAgICA8eHM6cmVzdHJpY3Rpb24gYmFzZT0ieHM6c3RyaW5nIj4NCiAgICAgICAgICAgICAgICAgICAgPHhzOm1heExlbmd0aCB2YWx1ZT0iNDAiIC8+DQogICAgICAgICAgICAgICAgICA8L3hzOnJlc3RyaWN0aW9uPg0K
/// ICAgICAgICAgICAgICAgIDwveHM6c2ltcGxlVHlwZT4NCiAgICAgICAgICAgICAgPC94czplbGVtZW50Pg0KICAgICAgICAgICAgICA8eHM6ZWxlbWVudCBuYW1lPSJQQVRJRU5UX0lEIiBtc3Byb3A6QmFzZUNvbHVtbj0iUEFUSUVOVF9JRCIgbXNwcm9wOk9yYURiVHlwZT0iMTI2IiBtaW5PY2N1cnM9IjAiPg0KICAgICAgICAgICAgICAgIDx4czpzaW1wbGVUeXBlPg0KICAgICAgICAgICAgICAgICAgPHhzOnJlc3RyaWN0aW9uIGJhc2U9InhzOnN0cmluZyI+DQogICAgICAgICAgICAgICAgICAgIDx4czptYXhMZW5ndGggdmFsdWU9IjIwIiAvPg0KICAgICAgICAgICAgICAgICAgPC94czpyZXN0cmljdGlvbj4NCiAgICAgICAgICAgICAgICA8L3hzOnNpbXBs
/// ZVR5cGU+DQogICAgICAgICAgICAgIDwveHM6ZWxlbWVudD4NCiAgICAgICAgICAgICAgPHhzOmVsZW1lbnQgbmFtZT0iTkFNRSIgbXNwcm9wOkJhc2VDb2x1bW49Ik5BTUUiIG1zcHJvcDpPcmFEYlR5cGU9IjEyNiIgbWluT2NjdXJzPSIwIj4NCiAgICAgICAgICAgICAgICA8eHM6c2ltcGxlVHlwZT4NCiAgICAgICAgICAgICAgICAgIDx4czpyZXN0cmljdGlvbiBiYXNlPSJ4czpzdHJpbmciPg0KICAgICAgICAgICAgICAgICAgICA8eHM6bWF4TGVuZ3RoIHZhbHVlPSI0MCIgLz4NCiAgICAgICAgICAgICAgICAgIDwveHM6cmVzdHJpY3Rpb24+DQogICAgICAgICAgICAgICAgPC94czpzaW1wbGVUeXBlPg0KICAgICAgICAgICAgICA8L3hzOmVsZW1lbnQ+DQog
/// ICAgICAgICAgICAgIDx4czplbGVtZW50IG5hbWU9IkFNT1VOVCIgbXNkYXRhOlJlYWRPbmx5PSJ0cnVlIiBtc3Byb3A6T3JhRGJUeXBlPSIxMDciIHR5cGU9InhzOmRlY2ltYWwiIG1pbk9jY3Vycz0iMCIgLz4NCiAgICAgICAgICAgICAgPHhzOmVsZW1lbnQgbmFtZT0iVklTSVRfSUQiIG1zcHJvcDpCYXNlQ29sdW1uPSJWSVNJVF9JRCIgbXNwcm9wOk9yYURiVHlwZT0iMTExIiB0eXBlPSJ4czpzaG9ydCIgbWluT2NjdXJzPSIwIiAvPg0KICAgICAgICAgICAgICA8eHM6ZWxlbWVudCBuYW1lPSJCRURfTEFCRUwiIG1zcHJvcDpCYXNlQ29sdW1uPSJCRURfTEFCRUwiIG1zcHJvcDpPcmFEYlR5cGU9IjEyNiIgbWluT2NjdXJzPSIwIj4NCiAgICAgICAgICAgICAg
/// ICA8eHM6c2ltcGxlVHlwZT4NCiAgICAgICAgICAgICAgICAgIDx4czpyZXN0cmljdGlvbiBiYXNlPSJ4czpzdHJpbmciPg0KICAgICAgICAgICAgICAgICAgICA8eHM6bWF4TGVuZ3RoIHZhbHVlPSI4IiAvPg0KICAgICAgICAgICAgICAgICAgPC94czpyZXN0cmljdGlvbj4NCiAgICAgICAgICAgICAgICA8L3hzOnNpbXBsZVR5cGU+DQogICAgICAgICAgICAgIDwveHM6ZWxlbWVudD4NCiAgICAgICAgICAgIDwveHM6c2VxdWVuY2U+DQogICAgICAgICAgPC94czpjb21wbGV4VHlwZT4NCiAgICAgICAgPC94czplbGVtZW50Pg0KICAgICAgICA8eHM6ZWxlbWVudCBuYW1lPSJTSU5HTEVfUk9XX1RBQkxFIj4NCiAgICAgICAgICA8eHM6Y29tcGxleFR5cGU+DQog
/// ICAgICAgICAgICA8eHM6c2VxdWVuY2U+DQogICAgICAgICAgICAgIDx4czplbGVtZW50IG5hbWU9IldBUkRfQ09ERSIgdHlwZT0ieHM6c3RyaW5nIiBtaW5PY2N1cnM9IjAiIC8+DQogICAgICAgICAgICAgIDx4czplbGVtZW50IG5hbWU9IldBUkRfTkFNRSIgdHlwZT0ieHM6c3RyaW5nIiBtaW5PY2N1cnM9IjAiIC8+DQogICAgICAgICAgICAgIDx4czplbGVtZW50IG5hbWU9IkRBVEVfQkVHSU4iIHR5cGU9InhzOnN0cmluZyIgbWluT2NjdXJzPSIwIiAvPg0KICAgICAgICAgICAgPC94czpzZXF1ZW5jZT4NCiAgICAgICAgICA8L3hzOmNvbXBsZXhUeXBlPg0KICAgICAgICA8L3hzOmVsZW1lbnQ+DQogICAgICA8L3hzOmNob2ljZT4NCiAgICA8L3hzOmNvbXBs
/// ZXhUeXBlPg0KICA8L3hzOmVsZW1lbnQ+DQo8L3hzOnNjaGVtYT4B8wRTRUxFQ1QgKHNlbGVjdCBob3NwaXRhbCBmcm9tIGhvc3BpdGFsX2NvbmZpZykgaG9zcGl0YWxfbmFtZSxhLlBBVElFTlRfSUQsICAgICAgIGMuTkFNRSwgICAgICAgc3VtKGEuQU1PVU5UKSBBTU9VTlQsICAgICAgIGEuVklTSVRfSUQsICAgICAgIGQuQkVEX0xBQkVMICBGUk9NIFBSRVBBWU1FTlRfUkNQVCBhLCBQQVRTX0lOX0hPU1BJVEFMIGIsIFBBVF9NQVNURVJfSU5ERVggYywgQkVEX1JFQyBkIHdoZXJlIGEuVFJBTlNBQ1RfVFlQRSA9ICfkuqTmrL4nICAgYW5kIChhLlJFRlVOREVEX1JDUFRfTk8gPSAnJyBvciBhLlJFRlVOREVEX1JDUFRfTk8gaXMgbnVsbCkgICBBTkQgYS5QQVRJ
/// RU5UX0lEID0gYy5QQVRJRU5UX0lEICAgQU5EIGEuUEFUSUVOVF9JRCA9IGIuUEFUSUVOVF9JRCAgIEFORCBhLlZJU0lUX0lEID0gYi5WSVNJVF9JRCAgIEFORCBiLldBUkRfQ09ERSA9IGQuV0FSRF9DT0RFICAgQU5EIGIuQkVEX05PID0gZC5CRURfTk8gICBhbmQgYi5XQVJEX0NPREUgPSB7V0FSRENPREV9IGdyb3VwIGJ5IGEuUEFUSUVOVF9JRCwgICAgICAgICAgYy5OQU1FLCAgICAgICAgICBhLlZJU0lUX0lELCAgICAgICAgICBiLkJFRF9OTywgICAgICAgICAgZC5CRURfTEFCRUwgb3JkZXIgYnkgYi5CRURfTk8=</Resource>
///   </Resources>
/// </XRTypeInfo>
namespace XtraReportSerialization {
    
    public class XtraReport : DevExpress.XtraReports.UI.XtraReport {
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.XRTable table2;
        private DevExpress.XtraReports.UI.XRTableRow tableRow2;
        private DevExpress.XtraReports.UI.XRTableCell tableCell14;
        private DevExpress.XtraReports.UI.XRTableCell tableCell15;
        private DevExpress.XtraReports.UI.XRTableCell tableCell16;
        private DevExpress.XtraReports.UI.XRTableCell tableCell17;
        private DevExpress.XtraReports.UI.XRTableCell tableCell18;
        private DevExpress.XtraReports.UI.XRTableCell tableCell19;
        private DevExpress.XtraReports.UI.XRTableCell tableCell20;
        private DevExpress.XtraReports.UI.XRTableCell tableCell21;
        private DevExpress.XtraReports.UI.XRTableCell tableCell22;
        private DevExpress.XtraReports.UI.XRTableCell tableCell23;
        private DevExpress.XtraReports.UI.XRTableCell tableCell24;
        private DevExpress.XtraReports.UI.XRTableCell tableCell25;
        private DevExpress.XtraReports.UI.XRTableCell tableCell26;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.XRTable table1;
        private DevExpress.XtraReports.UI.XRTableRow tableRow1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell2;
        private DevExpress.XtraReports.UI.XRTableCell tableCell3;
        private DevExpress.XtraReports.UI.XRTableCell tableCell4;
        private DevExpress.XtraReports.UI.XRTableCell tableCell5;
        private DevExpress.XtraReports.UI.XRTableCell tableCell6;
        private DevExpress.XtraReports.UI.XRTableCell tableCell7;
        private DevExpress.XtraReports.UI.XRTableCell tableCell8;
        private DevExpress.XtraReports.UI.XRTableCell tableCell9;
        private DevExpress.XtraReports.UI.XRTableCell tableCell10;
        private DevExpress.XtraReports.UI.XRTableCell tableCell11;
        private DevExpress.XtraReports.UI.XRTableCell tableCell12;
        private DevExpress.XtraReports.UI.XRTableCell tableCell13;
        private DevExpress.XtraReports.UI.XRLabel label10;
        private DevExpress.XtraReports.UI.XRLabel label1;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter;
        private DevExpress.XtraReports.UI.XRLine line4;
        private DevExpress.XtraReports.Parameters.Parameter HOSPITAL_NAME;
        private DevExpress.XtraReports.Parameters.Parameter OPER_NURSE;
        private DevExpress.XtraReports.Parameters.Parameter DEPT_NAME;
        private System.Resources.ResourceManager _resources;
        private string _resourceString;
        public XtraReport() {
            this._resourceString = DevExpress.XtraReports.Serialization.XRResourceManager.GetResourceFor("XtraReportSerialization.XtraReport");
            this.InitializeComponent();
        }
        private System.Resources.ResourceManager resources {
            get {
                if (_resources == null) {
                    this._resources = new DevExpress.XtraReports.Serialization.XRResourceManager(this._resourceString);
                }
                return this._resources;
            }
        }
        private void InitializeComponent() {
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.ReportFooter = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.table2 = new DevExpress.XtraReports.UI.XRTable();
            this.tableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell14 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell15 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell16 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell17 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell18 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell19 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell20 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell21 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell22 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell23 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell24 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell25 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell26 = new DevExpress.XtraReports.UI.XRTableCell();
            this.table1 = new DevExpress.XtraReports.UI.XRTable();
            this.label10 = new DevExpress.XtraReports.UI.XRLabel();
            this.label1 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell11 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell12 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell13 = new DevExpress.XtraReports.UI.XRTableCell();
            this.line4 = new DevExpress.XtraReports.UI.XRLine();
            this.HOSPITAL_NAME = new DevExpress.XtraReports.Parameters.Parameter();
            this.OPER_NURSE = new DevExpress.XtraReports.Parameters.Parameter();
            this.DEPT_NAME = new DevExpress.XtraReports.Parameters.Parameter();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // TopMargin
            // 
            this.TopMargin.HeightF = 6F;
            this.TopMargin.Name = "TopMargin";
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.table2});
            this.Detail.Font = new System.Drawing.Font("Times New Roman", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Detail.HeightF = 26.45833F;
            this.Detail.Name = "Detail";
            this.Detail.StylePriority.UseFont = false;
            // 
            // BottomMargin
            // 
            this.BottomMargin.HeightF = 43F;
            this.BottomMargin.Name = "BottomMargin";
            // 
            // PageHeader
            // 
            this.PageHeader.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.table1,
                        this.label10,
                        this.label1});
            this.PageHeader.HeightF = 115F;
            this.PageHeader.Name = "PageHeader";
            this.PageHeader.StylePriority.UseBorders = false;
            this.PageHeader.StylePriority.UseTextAlignment = false;
            this.PageHeader.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // ReportFooter
            // 
            this.ReportFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.line4});
            this.ReportFooter.HeightF = 58.3333F;
            this.ReportFooter.Name = "ReportFooter";
            // 
            // table2
            // 
            this.table2.LocationFloat = new DevExpress.Utils.PointFloat(0F, 1.458327F);
            this.table2.Name = "table2";
            this.table2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.table2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow2});
            this.table2.SizeF = new System.Drawing.SizeF(827F, 25F);
            // 
            // tableRow2
            // 
            this.tableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell14,
                        this.tableCell15,
                        this.tableCell16,
                        this.tableCell17,
                        this.tableCell18,
                        this.tableCell19,
                        this.tableCell20,
                        this.tableCell21,
                        this.tableCell22,
                        this.tableCell23,
                        this.tableCell24,
                        this.tableCell25,
                        this.tableCell26});
            this.tableRow2.Name = "tableRow2";
            this.tableRow2.Weight = 1D;
            // 
            // tableCell14
            // 
            this.tableCell14.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[RECEIVE_DAY]")});
            this.tableCell14.Multiline = true;
            this.tableCell14.Name = "tableCell14";
            this.tableCell14.Text = "tableCell14";
            this.tableCell14.Weight = 0.50568470001220711D;
            // 
            // tableCell15
            // 
            this.tableCell15.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[RECEIVE_TIME]")});
            this.tableCell15.Multiline = true;
            this.tableCell15.Name = "tableCell15";
            this.tableCell15.Text = "tableCell15";
            this.tableCell15.Weight = 0.6642812728881835D;
            // 
            // tableCell16
            // 
            this.tableCell16.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[APPOINT_DEPT]")});
            this.tableCell16.Multiline = true;
            this.tableCell16.Name = "tableCell16";
            this.tableCell16.Text = "tableCell16";
            this.tableCell16.Weight = 0.67925219592872677D;
            // 
            // tableCell17
            // 
            this.tableCell17.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[NAME]")});
            this.tableCell17.Multiline = true;
            this.tableCell17.Name = "tableCell17";
            this.tableCell17.Text = "tableCell17";
            this.tableCell17.Weight = 0.67007046642478874D;
            // 
            // tableCell18
            // 
            this.tableCell18.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[INP_NO]")});
            this.tableCell18.Multiline = true;
            this.tableCell18.Name = "tableCell18";
            this.tableCell18.Text = "tableCell18";
            this.tableCell18.Weight = 0.49531486230956751D;
            // 
            // tableCell19
            // 
            this.tableCell19.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[PACK_NAME]")});
            this.tableCell19.Multiline = true;
            this.tableCell19.Name = "tableCell19";
            this.tableCell19.Text = "tableCell19";
            this.tableCell19.Weight = 0.79079830018757125D;
            // 
            // tableCell20
            // 
            this.tableCell20.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[EX_AMOUNT]")});
            this.tableCell20.Multiline = true;
            this.tableCell20.Name = "tableCell20";
            this.tableCell20.Text = "tableCell20";
            this.tableCell20.Weight = 0.42009195496398211D;
            // 
            // tableCell21
            // 
            this.tableCell21.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[IMPLANT_AMOUNT]")});
            this.tableCell21.Multiline = true;
            this.tableCell21.Name = "tableCell21";
            this.tableCell21.Text = "tableCell21";
            this.tableCell21.Weight = 0.33965116265796597D;
            // 
            // tableCell22
            // 
            this.tableCell22.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[BOX_AMOUNT]")});
            this.tableCell22.Multiline = true;
            this.tableCell22.Name = "tableCell22";
            this.tableCell22.Text = "tableCell22";
            this.tableCell22.Weight = 0.49010401893806588D;
            // 
            // tableCell23
            // 
            this.tableCell23.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[SCHEDULE_DATE]")});
            this.tableCell23.Multiline = true;
            this.tableCell23.Name = "tableCell23";
            this.tableCell23.Text = "tableCell23";
            this.tableCell23.Weight = 1.1159861144273691D;
            // 
            // tableCell24
            // 
            this.tableCell24.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[PACK_FIRM]")});
            this.tableCell24.Multiline = true;
            this.tableCell24.Name = "tableCell24";
            this.tableCell24.Text = "tableCell24";
            this.tableCell24.Weight = 0.74117469395565083D;
            // 
            // tableCell25
            // 
            this.tableCell25.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[EXTERNAL_SUPPLIER]")});
            this.tableCell25.Multiline = true;
            this.tableCell25.Name = "tableCell25";
            this.tableCell25.Text = "tableCell25";
            this.tableCell25.Weight = 0.699403507154458D;
            // 
            // tableCell26
            // 
            this.tableCell26.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Table_1].[RECEIVE_NURSE]")});
            this.tableCell26.Multiline = true;
            this.tableCell26.Name = "tableCell26";
            this.tableCell26.Text = "tableCell26";
            this.tableCell26.Weight = 0.67876840223830781D;
            // 
            // table1
            // 
            this.table1.LocationFloat = new DevExpress.Utils.PointFloat(3.861173F, 76.4583F);
            this.table1.Name = "table1";
            this.table1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.table1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow1});
            this.table1.SizeF = new System.Drawing.SizeF(823.1389F, 36.45833F);
            // 
            // label10
            // 
            this.label10.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
                        new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Parameters].[HOSPITAL_NAME]")});
            this.label10.LocationFloat = new DevExpress.Utils.PointFloat(260.162F, 0F);
            this.label10.Multiline = true;
            this.label10.Name = "label10";
            this.label10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label10.SizeF = new System.Drawing.SizeF(206.25F, 23F);
            this.label10.StylePriority.UseTextAlignment = false;
            this.label10.Text = "dffsfsf";
            this.label10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label1
            // 
            this.label1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label1.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.LocationFloat = new DevExpress.Utils.PointFloat(3.000069F, 38.625F);
            this.label1.Name = "label1";
            this.label1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label1.SizeF = new System.Drawing.SizeF(722.9998F, 23F);
            this.label1.StylePriority.UseBorders = false;
            this.label1.StylePriority.UseFont = false;
            this.label1.StylePriority.UseTextAlignment = false;
            this.label1.Text = "外来器械交接记录";
            this.label1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // tableRow1
            // 
            this.tableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell1,
                        this.tableCell2,
                        this.tableCell3,
                        this.tableCell4,
                        this.tableCell5,
                        this.tableCell6,
                        this.tableCell7,
                        this.tableCell8,
                        this.tableCell9,
                        this.tableCell10,
                        this.tableCell11,
                        this.tableCell12,
                        this.tableCell13});
            this.tableRow1.Name = "tableRow1";
            this.tableRow1.Weight = 0.64864869144905324D;
            // 
            // tableCell1
            // 
            this.tableCell1.Multiline = true;
            this.tableCell1.Name = "tableCell1";
            this.tableCell1.Text = "日期";
            this.tableCell1.Weight = 0.46875D;
            // 
            // tableCell2
            // 
            this.tableCell2.Multiline = true;
            this.tableCell2.Name = "tableCell2";
            this.tableCell2.Text = "接收时间";
            this.tableCell2.Weight = 0.66666671752929685D;
            // 
            // tableCell3
            // 
            this.tableCell3.Multiline = true;
            this.tableCell3.Name = "tableCell3";
            this.tableCell3.Text = "申请科室";
            this.tableCell3.Weight = 0.67708335876464854D;
            // 
            // tableCell4
            // 
            this.tableCell4.Multiline = true;
            this.tableCell4.Name = "tableCell4";
            this.tableCell4.Text = "患者姓名";
            this.tableCell4.Weight = 0.66666664123535146D;
            // 
            // tableCell5
            // 
            this.tableCell5.Multiline = true;
            this.tableCell5.Name = "tableCell5";
            this.tableCell5.Text = "住院号";
            this.tableCell5.Weight = 0.49999954223632859D;
            // 
            // tableCell6
            // 
            this.tableCell6.Multiline = true;
            this.tableCell6.Name = "tableCell6";
            this.tableCell6.Text = "手术包名称";
            this.tableCell6.Weight = 0.79166778564453111D;
            // 
            // tableCell7
            // 
            this.tableCell7.Multiline = true;
            this.tableCell7.Name = "tableCell7";
            this.tableCell7.Text = "器械\r\n数量";
            this.tableCell7.Weight = 0.42055369054145286D;
            // 
            // tableCell8
            // 
            this.tableCell8.Multiline = true;
            this.tableCell8.Name = "tableCell8";
            this.tableCell8.Text = "植入\r\n数量";
            this.tableCell8.Weight = 0.3400244864902523D;
            // 
            // tableCell9
            // 
            this.tableCell9.Multiline = true;
            this.tableCell9.Name = "tableCell9";
            this.tableCell9.Text = "器械盒\r\n数量";
            this.tableCell9.Weight = 0.49064269853349307D;
            // 
            // tableCell10
            // 
            this.tableCell10.Multiline = true;
            this.tableCell10.Name = "tableCell10";
            this.tableCell10.Text = "预计手术时间";
            this.tableCell10.Weight = 1.117212724101128D;
            // 
            // tableCell11
            // 
            this.tableCell11.Multiline = true;
            this.tableCell11.Name = "tableCell11";
            this.tableCell11.Text = "公司名称";
            this.tableCell11.Weight = 0.74198918239419243D;
            // 
            // tableCell12
            // 
            this.tableCell12.Multiline = true;
            this.tableCell12.Name = "tableCell12";
            this.tableCell12.Text = "送器械人";
            this.tableCell12.Weight = 0.700172244790886D;
            // 
            // tableCell13
            // 
            this.tableCell13.Multiline = true;
            this.tableCell13.Name = "tableCell13";
            this.tableCell13.Text = "收器械人";
            this.tableCell13.Weight = 0.67951537840995713D;
            // 
            // line4
            // 
            this.line4.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.line4.LocationFloat = new DevExpress.Utils.PointFloat(3.861173F, 0F);
            this.line4.Name = "line4";
            this.line4.SizeF = new System.Drawing.SizeF(823.1389F, 11.54168F);
            this.line4.StylePriority.UseBorders = false;
            // 
            // HOSPITAL_NAME
            // 
            this.HOSPITAL_NAME.Description = "Parameter1";
            this.HOSPITAL_NAME.Name = "HOSPITAL_NAME";
            // 
            // OPER_NURSE
            // 
            this.OPER_NURSE.Description = "Parameter1";
            this.OPER_NURSE.Name = "OPER_NURSE";
            // 
            // DEPT_NAME
            // 
            this.DEPT_NAME.Description = "Parameter1";
            this.DEPT_NAME.Name = "DEPT_NAME";
            this.DEPT_NAME.Type = typeof(int);
            this.DEPT_NAME.ValueInfo = "0";
            // 
            // XtraReport
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
                        this.TopMargin,
                        this.Detail,
                        this.BottomMargin,
                        this.PageHeader,
                        this.ReportFooter});
            this.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.DataSourceSchema = resources.GetString("$this.DataSourceSchema");
            this.Margins = new System.Drawing.Printing.Margins(0, 0, 6, 43);
            this.Name = "XtraReport";
            this.PageHeight = 1169;
            this.PageWidth = 827;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.Parameters.AddRange(new DevExpress.XtraReports.Parameters.Parameter[] {
                        this.HOSPITAL_NAME,
                        this.OPER_NURSE,
                        this.DEPT_NAME});
            this.ShowPrintMarginsWarning = false;
            this.Tag = resources.GetString("$this.Tag");
            this.Version = "18.1";
            ((System.ComponentModel.ISupportInitialize)(this.table2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();
        }
    }
}
