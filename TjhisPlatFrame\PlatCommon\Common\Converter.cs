﻿//-----------------------------------------------------------------------
//  系统名称        : Net开发平台
//  子系统名称      : 共通函数
//  功能概要        : 对象安全转换类
//  创建人          : 
//  创建时间        : 2015-07-30
//-----------------------------------------------------------------------
using System;
using System.Globalization;

namespace PlatCommon.Common
{
    /// <summary>
    /// 功能描述：对象安全转换类  
    /// </summary>
    public static class Converter
    {
        #region 变量
        private static String[] Ls_ShZ = { "零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾" };
        private static String[] Ls_DW_Zh = { "元", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿", "拾", "佰", "仟", "万" };
        private static String[] Num_DW = { "", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿", "拾", "佰", "仟", "万" };
        private static String[] Ls_DW_X = { "角", "分" };
        #endregion

        /// <summary>
        /// 把对象转换为字符串
        /// </summary>
        /// <param name="obj">要被转换的对象</param>
        /// <returns>字符串</returns>
        public static string ToString(this object obj)
        {
            if (obj == null) return string.Empty;
            if (obj == DBNull.Value) return string.Empty;

            return obj.ToString();
        }
        /// <summary>
        /// 转换为字符串
        /// </summary>
        /// <param name="obj">转换目标对象</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>字符串</returns>
        public static string ToString(this object obj, string defaultValue = null)
        {
            if (Convert.IsDBNull(obj))
            {
                return string.IsNullOrWhiteSpace(defaultValue) ? string.Empty : defaultValue;
            }
            else if (obj == null)
            {
                return string.IsNullOrWhiteSpace(defaultValue) ? string.Empty : defaultValue;
            }
            else if (obj.ToString().Trim()?.Length == 0)
            {
                return string.IsNullOrWhiteSpace(defaultValue) ? string.Empty : defaultValue;
            }
            return obj.ToString();
        }

        /// <summary>
        /// 把对象转换为Int
        /// </summary>
        /// <param name="obj">要被转换的对象</param>
        /// <returns>数字</returns>
        public static int ToInt(this object obj)
        {
            if (obj == null) return default(int);
            int result = default(int);
            int.TryParse(obj.ToString(), out result);
            return result;
        }


        /// <summary>
        /// 把对象转换为Int
        /// </summary>
        /// <param name="obj">要被转换的对象</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>数字</returns>
        public static int ToInt(this object obj, int defValue)
        {
            if (obj == null) return defValue;
            if (obj == DBNull.Value) return defValue;
            if (obj.ToString().Trim()?.Length == 0) return defValue;
            int result = default(int);
            int.TryParse(obj.ToString(), out result);
            return result;
        }


        /// <summary>
        /// 把对象转换为Deci
        /// </summary>
        /// <param name="obj">要被转换的对象</param>
        /// <returns>数字</returns>
        public static decimal ToDeci(this object obj)
        {
            if (obj == null) return default(decimal);
            decimal result = default(decimal);
            decimal.TryParse(obj.ToString(), out result);
            return result;
        }


        /// <summary>
        /// 金额小写转中文大写。
        /// 整数支持到万亿；小数部分支持到分(超过两位将进行Banker舍入法处理)
        /// </summary>
        /// <param name="Num">需要转换的双精度浮点数</param>
        /// <returns>转换后的字符串</returns>
        public static String ToMoneyStr(Double Num)
        {
            Boolean iXSh_bool = false;//是否含有小数，默认没有(0则视为没有)
            Boolean iZhSh_bool = true;//是否含有整数,默认有(0则视为没有)

            string NumStr;//整个数字字符串
            string NumStr_Zh;//整数部分
            string NumSr_X = "";//小数部分
            string NumStr_DQ;//当前的数字字符
            string NumStr_R = "";//返回的字符串

            Num = Math.Round(Num, 2);//四舍五入取两位

            //各种非正常情况处理
            if (Num < 0)
                return "不转换欠条";
            if (Num > *************.99)
                return "很难想象谁会有这么多钱！";
            if (Num == 0)
                return Ls_ShZ[0];

            //判断是否有整数
            if (Num < 1.00)
                iZhSh_bool = false;

            NumStr = Num.ToString();

            NumStr_Zh = NumStr;//默认只有整数部分
            if (NumStr_Zh.Contains("."))
            {//分开整数与小数处理
                NumStr_Zh = NumStr.Substring(0, NumStr.IndexOf("."));
                NumSr_X = NumStr.Substring((NumStr.IndexOf(".") + 1), (NumStr.Length - NumStr.IndexOf(".") - 1));
                iXSh_bool = true;
            }

            if (NumSr_X == "" || int.Parse(NumSr_X) <= 0)
            {//判断是否含有小数部分
                iXSh_bool = false;
            }

            if (iZhSh_bool)
            {//整数部分处理
                // NumStr_Zh = Reversion_Str(NumStr_Zh);// 反转字符串

                // 反转字符串
                char[] cs = NumStr_Zh.ToCharArray();
                Array.Reverse(cs);
                NumStr_Zh = new string(cs);

                for (int a = 0; a < NumStr_Zh.Length; a++)
                {//整数部分转换
                    NumStr_DQ = NumStr_Zh.Substring(a, 1);
                    if (int.Parse(NumStr_DQ) != 0)
                        NumStr_R = Ls_ShZ[int.Parse(NumStr_DQ)] + Ls_DW_Zh[a] + NumStr_R;
                    else if (a == 0 || a == 4 || a == 8)
                    {
                        if (NumStr_Zh.Length > 8 && a == 4)
                            continue;
                        NumStr_R = Ls_DW_Zh[a] + NumStr_R;
                    }
                    else if (int.Parse(NumStr_Zh.Substring(a - 1, 1)) != 0)
                        NumStr_R = Ls_ShZ[int.Parse(NumStr_DQ)] + NumStr_R;

                }

                if (!iXSh_bool)
                    return NumStr_R + "整";

                //NumStr_R += "零";
            }

            for (int b = 0; b < NumSr_X.Length; b++)
            {//小数部分转换
                NumStr_DQ = NumSr_X.Substring(b, 1);
                if (int.Parse(NumStr_DQ) != 0)
                    NumStr_R += Ls_ShZ[int.Parse(NumStr_DQ)] + Ls_DW_X[b];
                else if (b != 1 && iZhSh_bool)
                    NumStr_R += Ls_ShZ[int.Parse(NumStr_DQ)];
            }

            return NumStr_R;
        }
        /// <summary>
        /// 转换为日期时间
        /// </summary>
        /// <param name="obj">转换目标对象</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>日期时间</returns>
        public static DateTime ToDateTime(this object obj, DateTime? defaultValue = null)
        {
            DateTime result = DateTime.MinValue;
            if (Convert.IsDBNull(obj))
            {
                return defaultValue ?? result;
            }
            else if (obj == null)
            {
                return defaultValue ?? result;
            }
            else if (obj.ToString().Trim()?.Length == 0)
            {
                return defaultValue ?? result;
            }
            if (DateTime.TryParse(obj.ToString(), out result))
            {
                DateTimeFormatInfo format = new DateTimeFormatInfo
                {
                    ShortDatePattern = Constants.DATE_SHORT,
                    LongTimePattern = Constants.TIME
                };
                result = Convert.ToDateTime(obj.ToString(), format);
            }
            return result;
        }

        /// <summary>
        /// 转换为小数
        /// </summary>
        /// <param name="obj">转换目标对象</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>小数</returns>
        public static decimal ToDecimal(this object obj, decimal? defaultValue = null)
        {
            decimal result = 0;
            if (Convert.IsDBNull(obj))
            {
                return defaultValue ?? result;
            }
            else if (obj == null)
            {
                return defaultValue ?? result;
            }
            else if (obj.ToString().Trim()?.Length == 0)
            {
                return defaultValue ?? result;
            }
            decimal.TryParse(obj.ToString(), out result);
            return result;
        }


        /// <summary>
        /// 转换为日期
        /// </summary>
        /// <param name="obj">转换目标对象</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>日期</returns>
        public static DateTime ToDate(this object obj, DateTime? defaultValue = null)
        {
            DateTime result = DateTime.MinValue;
            if (Convert.IsDBNull(obj))
            {
                return defaultValue ?? result;
            }
            else if (obj == null)
            {
                return defaultValue ?? result;
            }
            else if (obj.ToString().Trim()?.Length == 0)
            {
                return defaultValue ?? result;
            }
            if (DateTime.TryParse(obj.ToString(), out result))
            {
                DateTimeFormatInfo format = new DateTimeFormatInfo
                {
                    ShortDatePattern = Constants.DATE_SHORT
                };
                result = Convert.ToDateTime(obj.ToString(), format);
            }
            return result;
        }


        /// <summary>
        /// 转换为整数
        /// </summary>
        /// <param name="obj">转换目标对象</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>整数</returns>
        public static int ToInt(this object obj, int? defaultValue = null)
        {
            int result = 0;
            if (Convert.IsDBNull(obj))
            {
                return defaultValue ?? result;
            }
            else if (obj == null)
            {
                return defaultValue ?? result;
            }
            else if (obj.ToString().Trim()?.Length == 0)
            {
                return defaultValue ?? result;
            }
            int.TryParse(obj.ToString(), out result);
            return result;
        }

        /// <summary>
        /// 判断类型是否是数值类型
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns></returns>
        public static bool IsNumericType(this Type type)
        {
            switch (Type.GetTypeCode(type))
            {
                case TypeCode.Byte:
                case TypeCode.SByte:
                case TypeCode.UInt16:
                case TypeCode.UInt32:
                case TypeCode.UInt64:
                case TypeCode.Int16:
                case TypeCode.Int32:
                case TypeCode.Int64:
                case TypeCode.Decimal:
                case TypeCode.Double:
                case TypeCode.Single:
                    return true;
                default:
                    return false;
            }
        }
    }
}
