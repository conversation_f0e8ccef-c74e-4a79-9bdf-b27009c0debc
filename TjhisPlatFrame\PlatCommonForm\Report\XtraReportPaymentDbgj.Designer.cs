﻿namespace PlatCommonForm.Report
{
    partial class XtraReportPaymentDbgj
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_LB = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_DEMO = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel10 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_GH = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel35 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel33 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel31 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_SKR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel29 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_KHH = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_JE = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_ZH = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel27 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel28 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel23 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_DXJE = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_ZYH = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_LXDH = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel19 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel20 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel22 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel15 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_XB = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel11 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel12 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_XM = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_ZFFS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_BRIDH = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel9 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_RYKS = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_SJH = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel_KPRQ = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.xrLabel_HOSPITAL = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.PageFooter = new DevExpress.XtraReports.UI.PageFooterBand();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel2,
            this.xrLabel1,
            this.xrLabel_LB,
            this.xrLabel_DEMO,
            this.xrLabel10,
            this.xrLabel8,
            this.xrLabel6,
            this.xrLabel4,
            this.xrLabel_GH,
            this.xrLabel35,
            this.xrLabel33,
            this.xrLabel31,
            this.xrLabel_SKR,
            this.xrLabel29,
            this.xrLabel_KHH,
            this.xrLabel_JE,
            this.xrLabel_ZH,
            this.xrLabel27,
            this.xrLabel28,
            this.xrLabel23,
            this.xrLabel_DXJE,
            this.xrLabel_ZYH,
            this.xrLabel_LXDH,
            this.xrLabel19,
            this.xrLabel20,
            this.xrLabel22,
            this.xrLabel15,
            this.xrLabel_XB,
            this.xrLabel11,
            this.xrLabel12,
            this.xrLabel_XM,
            this.xrLabel_ZFFS,
            this.xrLabel7,
            this.xrLabel_BRIDH,
            this.xrLabel9,
            this.xrLabel_RYKS,
            this.xrLabel_SJH,
            this.xrLabel5,
            this.xrLabel_KPRQ,
            this.xrLabel3});
            this.Detail.HeightF = 342.2906F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrLabel2
            // 
            this.xrLabel2.Font = new System.Drawing.Font("Times New Roman", 11F, System.Drawing.FontStyle.Bold);
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(82.29176F, 311.0823F);
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(515.6249F, 23.00002F);
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            this.xrLabel2.Text = "银行卡缴费，持原银行卡办理结算";
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel1
            // 
            this.xrLabel1.Font = new System.Drawing.Font("Times New Roman", 11F, System.Drawing.FontStyle.Bold);
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(82.29166F, 287.0406F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(515.6249F, 24.04169F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "此收据出院结账时收回，请勿丢失";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel_LB
            // 
            this.xrLabel_LB.Borders = DevExpress.XtraPrinting.BorderSide.Left;
            this.xrLabel_LB.LocationFloat = new DevExpress.Utils.PointFloat(373.9582F, 93.12455F);
            this.xrLabel_LB.Name = "xrLabel_LB";
            this.xrLabel_LB.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_LB.SizeF = new System.Drawing.SizeF(100F, 27.70818F);
            this.xrLabel_LB.StylePriority.UseBorders = false;
            this.xrLabel_LB.StylePriority.UseTextAlignment = false;
            this.xrLabel_LB.Text = "xrLabel3";
            this.xrLabel_LB.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel_DEMO
            // 
            this.xrLabel_DEMO.Borders = DevExpress.XtraPrinting.BorderSide.Right;
            this.xrLabel_DEMO.LocationFloat = new DevExpress.Utils.PointFloat(359.375F, 203.9573F);
            this.xrLabel_DEMO.Name = "xrLabel_DEMO";
            this.xrLabel_DEMO.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_DEMO.SizeF = new System.Drawing.SizeF(259.375F, 19.70825F);
            this.xrLabel_DEMO.StylePriority.UseBorders = false;
            this.xrLabel_DEMO.Text = "xrLabel_DEMO";
            // 
            // xrLabel10
            // 
            this.xrLabel10.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Right | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel10.Font = new System.Drawing.Font("Times New Roman", 9F);
            this.xrLabel10.LocationFloat = new DevExpress.Utils.PointFloat(293.7498F, 259.6656F);
            this.xrLabel10.Name = "xrLabel10";
            this.xrLabel10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel10.SizeF = new System.Drawing.SizeF(325.0002F, 18F);
            this.xrLabel10.StylePriority.UseBorders = false;
            this.xrLabel10.StylePriority.UseFont = false;
            this.xrLabel10.StylePriority.UseTextAlignment = false;
            this.xrLabel10.Text = "三、此收据丢失不补";
            this.xrLabel10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel8
            // 
            this.xrLabel8.Borders = DevExpress.XtraPrinting.BorderSide.Right;
            this.xrLabel8.Font = new System.Drawing.Font("Times New Roman", 9F);
            this.xrLabel8.LocationFloat = new DevExpress.Utils.PointFloat(293.7498F, 241.6656F);
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.SizeF = new System.Drawing.SizeF(325.0002F, 18F);
            this.xrLabel8.StylePriority.UseBorders = false;
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            this.xrLabel8.Text = "二、此收据不准报销只作收款临时凭证";
            this.xrLabel8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Borders = DevExpress.XtraPrinting.BorderSide.Right;
            this.xrLabel6.Font = new System.Drawing.Font("Times New Roman", 9F);
            this.xrLabel6.LocationFloat = new DevExpress.Utils.PointFloat(293.7498F, 223.6656F);
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.SizeF = new System.Drawing.SizeF(325.0002F, 17.99998F);
            this.xrLabel6.StylePriority.UseBorders = false;
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            this.xrLabel6.Text = "一、此收据无收费专用章无效";
            this.xrLabel6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel4
            // 
            this.xrLabel4.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel4.LocationFloat = new DevExpress.Utils.PointFloat(293.7498F, 176.2491F);
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.SizeF = new System.Drawing.SizeF(325.0002F, 27.70819F);
            this.xrLabel4.StylePriority.UseBorders = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            this.xrLabel4.Text = "签字";
            this.xrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel_GH
            // 
            this.xrLabel_GH.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel_GH.LocationFloat = new DevExpress.Utils.PointFloat(119.7917F, 241.6655F);
            this.xrLabel_GH.Name = "xrLabel_GH";
            this.xrLabel_GH.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_GH.SizeF = new System.Drawing.SizeF(173.9581F, 36F);
            this.xrLabel_GH.StylePriority.UseBorders = false;
            this.xrLabel_GH.StylePriority.UseTextAlignment = false;
            this.xrLabel_GH.Text = "xrLabel3";
            this.xrLabel_GH.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel35
            // 
            this.xrLabel35.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel35.LocationFloat = new DevExpress.Utils.PointFloat(48.95833F, 241.6655F);
            this.xrLabel35.Name = "xrLabel35";
            this.xrLabel35.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel35.SizeF = new System.Drawing.SizeF(70.83333F, 36F);
            this.xrLabel35.StylePriority.UseBorders = false;
            this.xrLabel35.StylePriority.UseTextAlignment = false;
            this.xrLabel35.Text = "工号";
            this.xrLabel35.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel33
            // 
            this.xrLabel33.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel33.Font = new System.Drawing.Font("Times New Roman", 8F);
            this.xrLabel33.LocationFloat = new DevExpress.Utils.PointFloat(293.7498F, 203.9573F);
            this.xrLabel33.Name = "xrLabel33";
            this.xrLabel33.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel33.SizeF = new System.Drawing.SizeF(65.62518F, 19.70825F);
            this.xrLabel33.StylePriority.UseBorders = false;
            this.xrLabel33.StylePriority.UseFont = false;
            this.xrLabel33.StylePriority.UseTextAlignment = false;
            this.xrLabel33.Text = "注意事项";
            this.xrLabel33.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel31
            // 
            this.xrLabel31.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel31.LocationFloat = new DevExpress.Utils.PointFloat(48.95833F, 203.9573F);
            this.xrLabel31.Name = "xrLabel31";
            this.xrLabel31.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel31.SizeF = new System.Drawing.SizeF(70.83333F, 37.70824F);
            this.xrLabel31.StylePriority.UseBorders = false;
            this.xrLabel31.StylePriority.UseTextAlignment = false;
            this.xrLabel31.Text = "收款人";
            this.xrLabel31.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel_SKR
            // 
            this.xrLabel_SKR.Borders = DevExpress.XtraPrinting.BorderSide.Right;
            this.xrLabel_SKR.LocationFloat = new DevExpress.Utils.PointFloat(119.7917F, 203.9573F);
            this.xrLabel_SKR.Name = "xrLabel_SKR";
            this.xrLabel_SKR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_SKR.SizeF = new System.Drawing.SizeF(173.9581F, 37.70824F);
            this.xrLabel_SKR.StylePriority.UseBorders = false;
            this.xrLabel_SKR.StylePriority.UseTextAlignment = false;
            this.xrLabel_SKR.Text = "xrLabel3";
            this.xrLabel_SKR.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel29
            // 
            this.xrLabel29.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel29.LocationFloat = new DevExpress.Utils.PointFloat(48.9583F, 176.2491F);
            this.xrLabel29.Name = "xrLabel29";
            this.xrLabel29.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel29.SizeF = new System.Drawing.SizeF(70.83333F, 27.70818F);
            this.xrLabel29.StylePriority.UseBorders = false;
            this.xrLabel29.StylePriority.UseTextAlignment = false;
            this.xrLabel29.Text = "开户行";
            this.xrLabel29.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel_KHH
            // 
            this.xrLabel_KHH.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel_KHH.LocationFloat = new DevExpress.Utils.PointFloat(119.7917F, 176.2491F);
            this.xrLabel_KHH.Name = "xrLabel_KHH";
            this.xrLabel_KHH.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_KHH.SizeF = new System.Drawing.SizeF(173.9581F, 27.70818F);
            this.xrLabel_KHH.StylePriority.UseBorders = false;
            this.xrLabel_KHH.StylePriority.UseTextAlignment = false;
            this.xrLabel_KHH.Text = "xrLabel3";
            this.xrLabel_KHH.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel_JE
            // 
            this.xrLabel_JE.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel_JE.LocationFloat = new DevExpress.Utils.PointFloat(434.3749F, 120.8327F);
            this.xrLabel_JE.Name = "xrLabel_JE";
            this.xrLabel_JE.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_JE.SizeF = new System.Drawing.SizeF(184.3752F, 27.70818F);
            this.xrLabel_JE.StylePriority.UseBorders = false;
            this.xrLabel_JE.StylePriority.UseTextAlignment = false;
            this.xrLabel_JE.Text = "xrLabel3";
            this.xrLabel_JE.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel_ZH
            // 
            this.xrLabel_ZH.Borders = DevExpress.XtraPrinting.BorderSide.Right;
            this.xrLabel_ZH.LocationFloat = new DevExpress.Utils.PointFloat(119.7917F, 148.5409F);
            this.xrLabel_ZH.Name = "xrLabel_ZH";
            this.xrLabel_ZH.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_ZH.SizeF = new System.Drawing.SizeF(498.9583F, 27.70818F);
            this.xrLabel_ZH.StylePriority.UseBorders = false;
            this.xrLabel_ZH.StylePriority.UseTextAlignment = false;
            this.xrLabel_ZH.Text = "xrLabel3";
            this.xrLabel_ZH.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel27
            // 
            this.xrLabel27.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel27.LocationFloat = new DevExpress.Utils.PointFloat(373.9582F, 120.8327F);
            this.xrLabel27.Name = "xrLabel27";
            this.xrLabel27.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel27.SizeF = new System.Drawing.SizeF(60.41669F, 27.70818F);
            this.xrLabel27.StylePriority.UseBorders = false;
            this.xrLabel27.StylePriority.UseTextAlignment = false;
            this.xrLabel27.Text = "金额";
            this.xrLabel27.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel28
            // 
            this.xrLabel28.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel28.LocationFloat = new DevExpress.Utils.PointFloat(48.95833F, 148.5409F);
            this.xrLabel28.Name = "xrLabel28";
            this.xrLabel28.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel28.SizeF = new System.Drawing.SizeF(70.83333F, 27.70818F);
            this.xrLabel28.StylePriority.UseBorders = false;
            this.xrLabel28.StylePriority.UseTextAlignment = false;
            this.xrLabel28.Text = "帐号";
            this.xrLabel28.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel23
            // 
            this.xrLabel23.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel23.LocationFloat = new DevExpress.Utils.PointFloat(48.9583F, 120.8327F);
            this.xrLabel23.Name = "xrLabel23";
            this.xrLabel23.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel23.SizeF = new System.Drawing.SizeF(70.83333F, 27.70818F);
            this.xrLabel23.StylePriority.UseBorders = false;
            this.xrLabel23.StylePriority.UseTextAlignment = false;
            this.xrLabel23.Text = "大写金额";
            this.xrLabel23.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel_DXJE
            // 
            this.xrLabel_DXJE.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel_DXJE.LocationFloat = new DevExpress.Utils.PointFloat(119.7916F, 120.8327F);
            this.xrLabel_DXJE.Name = "xrLabel_DXJE";
            this.xrLabel_DXJE.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_DXJE.SizeF = new System.Drawing.SizeF(254.1666F, 27.70818F);
            this.xrLabel_DXJE.StylePriority.UseBorders = false;
            this.xrLabel_DXJE.StylePriority.UseTextAlignment = false;
            this.xrLabel_DXJE.Text = "xrLabel3";
            this.xrLabel_DXJE.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel_ZYH
            // 
            this.xrLabel_ZYH.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel_ZYH.LocationFloat = new DevExpress.Utils.PointFloat(529.1666F, 93.12455F);
            this.xrLabel_ZYH.Name = "xrLabel_ZYH";
            this.xrLabel_ZYH.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_ZYH.SizeF = new System.Drawing.SizeF(89.58344F, 27.70818F);
            this.xrLabel_ZYH.StylePriority.UseBorders = false;
            this.xrLabel_ZYH.StylePriority.UseTextAlignment = false;
            this.xrLabel_ZYH.Text = "xrLabel_ZYH";
            this.xrLabel_ZYH.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel_LXDH
            // 
            this.xrLabel_LXDH.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel_LXDH.LocationFloat = new DevExpress.Utils.PointFloat(119.7916F, 93.12455F);
            this.xrLabel_LXDH.Name = "xrLabel_LXDH";
            this.xrLabel_LXDH.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_LXDH.SizeF = new System.Drawing.SizeF(173.9583F, 27.70818F);
            this.xrLabel_LXDH.StylePriority.UseBorders = false;
            this.xrLabel_LXDH.StylePriority.UseTextAlignment = false;
            this.xrLabel_LXDH.Text = "xrLabel3";
            this.xrLabel_LXDH.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel19
            // 
            this.xrLabel19.Borders = DevExpress.XtraPrinting.BorderSide.Left;
            this.xrLabel19.LocationFloat = new DevExpress.Utils.PointFloat(293.7498F, 93.12455F);
            this.xrLabel19.Name = "xrLabel19";
            this.xrLabel19.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel19.SizeF = new System.Drawing.SizeF(80.20831F, 27.70818F);
            this.xrLabel19.StylePriority.UseBorders = false;
            this.xrLabel19.StylePriority.UseTextAlignment = false;
            this.xrLabel19.Text = "类别";
            this.xrLabel19.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel20
            // 
            this.xrLabel20.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel20.LocationFloat = new DevExpress.Utils.PointFloat(48.9583F, 93.12455F);
            this.xrLabel20.Name = "xrLabel20";
            this.xrLabel20.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel20.SizeF = new System.Drawing.SizeF(70.83333F, 27.70818F);
            this.xrLabel20.StylePriority.UseBorders = false;
            this.xrLabel20.StylePriority.UseTextAlignment = false;
            this.xrLabel20.Text = "联系电话";
            this.xrLabel20.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel22
            // 
            this.xrLabel22.Borders = DevExpress.XtraPrinting.BorderSide.Left;
            this.xrLabel22.LocationFloat = new DevExpress.Utils.PointFloat(473.9582F, 93.12455F);
            this.xrLabel22.Name = "xrLabel22";
            this.xrLabel22.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel22.SizeF = new System.Drawing.SizeF(55.20828F, 27.70818F);
            this.xrLabel22.StylePriority.UseBorders = false;
            this.xrLabel22.StylePriority.UseTextAlignment = false;
            this.xrLabel22.Text = "住院号";
            this.xrLabel22.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel15
            // 
            this.xrLabel15.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel15.LocationFloat = new DevExpress.Utils.PointFloat(473.9582F, 65.41637F);
            this.xrLabel15.Name = "xrLabel15";
            this.xrLabel15.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel15.SizeF = new System.Drawing.SizeF(55.20828F, 27.70818F);
            this.xrLabel15.StylePriority.UseBorders = false;
            this.xrLabel15.StylePriority.UseTextAlignment = false;
            this.xrLabel15.Text = "性别";
            this.xrLabel15.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel_XB
            // 
            this.xrLabel_XB.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel_XB.LocationFloat = new DevExpress.Utils.PointFloat(529.1666F, 65.41637F);
            this.xrLabel_XB.Name = "xrLabel_XB";
            this.xrLabel_XB.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_XB.SizeF = new System.Drawing.SizeF(89.58344F, 27.70818F);
            this.xrLabel_XB.StylePriority.UseBorders = false;
            this.xrLabel_XB.StylePriority.UseTextAlignment = false;
            this.xrLabel_XB.Text = "xrLabel_XB";
            this.xrLabel_XB.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel11
            // 
            this.xrLabel11.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel11.LocationFloat = new DevExpress.Utils.PointFloat(48.9583F, 65.41637F);
            this.xrLabel11.Name = "xrLabel11";
            this.xrLabel11.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel11.SizeF = new System.Drawing.SizeF(70.83333F, 27.70818F);
            this.xrLabel11.StylePriority.UseBorders = false;
            this.xrLabel11.StylePriority.UseTextAlignment = false;
            this.xrLabel11.Text = "姓名";
            this.xrLabel11.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel12
            // 
            this.xrLabel12.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel12.LocationFloat = new DevExpress.Utils.PointFloat(293.7498F, 65.41637F);
            this.xrLabel12.Name = "xrLabel12";
            this.xrLabel12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel12.SizeF = new System.Drawing.SizeF(80.20831F, 27.70818F);
            this.xrLabel12.StylePriority.UseBorders = false;
            this.xrLabel12.StylePriority.UseTextAlignment = false;
            this.xrLabel12.Text = "支付方式";
            this.xrLabel12.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel_XM
            // 
            this.xrLabel_XM.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel_XM.LocationFloat = new DevExpress.Utils.PointFloat(119.7916F, 65.41637F);
            this.xrLabel_XM.Name = "xrLabel_XM";
            this.xrLabel_XM.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_XM.SizeF = new System.Drawing.SizeF(173.9582F, 27.70818F);
            this.xrLabel_XM.StylePriority.UseBorders = false;
            this.xrLabel_XM.StylePriority.UseTextAlignment = false;
            this.xrLabel_XM.Text = "xrLabel3";
            this.xrLabel_XM.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel_ZFFS
            // 
            this.xrLabel_ZFFS.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel_ZFFS.LocationFloat = new DevExpress.Utils.PointFloat(373.9582F, 65.41637F);
            this.xrLabel_ZFFS.Name = "xrLabel_ZFFS";
            this.xrLabel_ZFFS.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_ZFFS.SizeF = new System.Drawing.SizeF(100F, 27.70818F);
            this.xrLabel_ZFFS.StylePriority.UseBorders = false;
            this.xrLabel_ZFFS.StylePriority.UseTextAlignment = false;
            this.xrLabel_ZFFS.Text = "xrLabel3";
            this.xrLabel_ZFFS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel7
            // 
            this.xrLabel7.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel7.LocationFloat = new DevExpress.Utils.PointFloat(48.95833F, 37.70819F);
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.SizeF = new System.Drawing.SizeF(70.83333F, 27.70818F);
            this.xrLabel7.StylePriority.UseBorders = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            this.xrLabel7.Text = "病人Id号";
            this.xrLabel7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel_BRIDH
            // 
            this.xrLabel_BRIDH.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel_BRIDH.LocationFloat = new DevExpress.Utils.PointFloat(119.7916F, 37.70819F);
            this.xrLabel_BRIDH.Name = "xrLabel_BRIDH";
            this.xrLabel_BRIDH.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_BRIDH.SizeF = new System.Drawing.SizeF(173.9582F, 27.70818F);
            this.xrLabel_BRIDH.StylePriority.UseBorders = false;
            this.xrLabel_BRIDH.StylePriority.UseTextAlignment = false;
            this.xrLabel_BRIDH.Text = "xrLabel3";
            this.xrLabel_BRIDH.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel9
            // 
            this.xrLabel9.Borders = DevExpress.XtraPrinting.BorderSide.Left;
            this.xrLabel9.LocationFloat = new DevExpress.Utils.PointFloat(293.7498F, 37.70819F);
            this.xrLabel9.Name = "xrLabel9";
            this.xrLabel9.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel9.SizeF = new System.Drawing.SizeF(80.20831F, 27.70818F);
            this.xrLabel9.StylePriority.UseBorders = false;
            this.xrLabel9.StylePriority.UseTextAlignment = false;
            this.xrLabel9.Text = "入院科室";
            this.xrLabel9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel_RYKS
            // 
            this.xrLabel_RYKS.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right)));
            this.xrLabel_RYKS.LocationFloat = new DevExpress.Utils.PointFloat(373.9581F, 37.70819F);
            this.xrLabel_RYKS.Name = "xrLabel_RYKS";
            this.xrLabel_RYKS.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_RYKS.SizeF = new System.Drawing.SizeF(244.7919F, 27.70818F);
            this.xrLabel_RYKS.StylePriority.UseBorders = false;
            this.xrLabel_RYKS.StylePriority.UseTextAlignment = false;
            this.xrLabel_RYKS.Text = "xrLabel3";
            this.xrLabel_RYKS.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel_SJH
            // 
            this.xrLabel_SJH.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel_SJH.LocationFloat = new DevExpress.Utils.PointFloat(373.9582F, 10.00001F);
            this.xrLabel_SJH.Name = "xrLabel_SJH";
            this.xrLabel_SJH.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_SJH.SizeF = new System.Drawing.SizeF(244.7918F, 27.70817F);
            this.xrLabel_SJH.StylePriority.UseBorders = false;
            this.xrLabel_SJH.StylePriority.UseTextAlignment = false;
            this.xrLabel_SJH.Text = "xrLabel3";
            this.xrLabel_SJH.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel5
            // 
            this.xrLabel5.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel5.LocationFloat = new DevExpress.Utils.PointFloat(293.7498F, 10.00001F);
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.SizeF = new System.Drawing.SizeF(80.20831F, 27.70818F);
            this.xrLabel5.StylePriority.UseBorders = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            this.xrLabel5.Text = "收据号";
            this.xrLabel5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel_KPRQ
            // 
            this.xrLabel_KPRQ.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel_KPRQ.LocationFloat = new DevExpress.Utils.PointFloat(119.7917F, 10.00001F);
            this.xrLabel_KPRQ.Name = "xrLabel_KPRQ";
            this.xrLabel_KPRQ.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_KPRQ.SizeF = new System.Drawing.SizeF(173.9582F, 27.70817F);
            this.xrLabel_KPRQ.StylePriority.UseBorders = false;
            this.xrLabel_KPRQ.StylePriority.UseTextAlignment = false;
            this.xrLabel_KPRQ.Text = "2018-12-14 19:42:21";
            this.xrLabel_KPRQ.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel3
            // 
            this.xrLabel3.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel3.LocationFloat = new DevExpress.Utils.PointFloat(48.95833F, 10.00001F);
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel3.SizeF = new System.Drawing.SizeF(70.83333F, 27.70818F);
            this.xrLabel3.StylePriority.UseBorders = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            this.xrLabel3.Text = "开票日期";
            this.xrLabel3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel_HOSPITAL});
            this.TopMargin.HeightF = 55.50002F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrLabel_HOSPITAL
            // 
            this.xrLabel_HOSPITAL.Font = new System.Drawing.Font("Times New Roman", 16F, System.Drawing.FontStyle.Bold);
            this.xrLabel_HOSPITAL.LocationFloat = new DevExpress.Utils.PointFloat(82.2916F, 20.00001F);
            this.xrLabel_HOSPITAL.Name = "xrLabel_HOSPITAL";
            this.xrLabel_HOSPITAL.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel_HOSPITAL.SizeF = new System.Drawing.SizeF(515.625F, 35.5F);
            this.xrLabel_HOSPITAL.StylePriority.UseFont = false;
            this.xrLabel_HOSPITAL.StylePriority.UseTextAlignment = false;
            this.xrLabel_HOSPITAL.Text = "东北国际医院住院预交金收据";
            this.xrLabel_HOSPITAL.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // BottomMargin
            // 
            this.BottomMargin.HeightF = 0F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // PageFooter
            // 
            this.PageFooter.HeightF = 2.083333F;
            this.PageFooter.Name = "PageFooter";
            // 
            // XtraReportPaymentDbgj
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageFooter});
            this.Margins = new System.Drawing.Printing.Margins(100, 100, 56, 0);
            this.PageHeight = 400;
            this.PaperKind = System.Drawing.Printing.PaperKind.Custom;
            this.Version = "17.2";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_HOSPITAL;
        private DevExpress.XtraReports.UI.PageFooterBand PageFooter;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_GH;
        private DevExpress.XtraReports.UI.XRLabel xrLabel35;
        private DevExpress.XtraReports.UI.XRLabel xrLabel33;
        private DevExpress.XtraReports.UI.XRLabel xrLabel31;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_SKR;
        private DevExpress.XtraReports.UI.XRLabel xrLabel29;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_KHH;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_JE;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_ZH;
        private DevExpress.XtraReports.UI.XRLabel xrLabel27;
        private DevExpress.XtraReports.UI.XRLabel xrLabel28;
        private DevExpress.XtraReports.UI.XRLabel xrLabel23;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_DXJE;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_ZYH;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_LXDH;
        private DevExpress.XtraReports.UI.XRLabel xrLabel19;
        private DevExpress.XtraReports.UI.XRLabel xrLabel20;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_LB;
        private DevExpress.XtraReports.UI.XRLabel xrLabel22;
        private DevExpress.XtraReports.UI.XRLabel xrLabel15;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_XB;
        private DevExpress.XtraReports.UI.XRLabel xrLabel11;
        private DevExpress.XtraReports.UI.XRLabel xrLabel12;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_XM;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_ZFFS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_BRIDH;
        private DevExpress.XtraReports.UI.XRLabel xrLabel9;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_RYKS;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_SJH;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_KPRQ;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.XRLabel xrLabel10;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLabel xrLabel_DEMO;
    }
}
