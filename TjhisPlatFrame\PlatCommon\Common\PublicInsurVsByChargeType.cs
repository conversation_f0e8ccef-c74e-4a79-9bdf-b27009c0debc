﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using NM_Service.NMService;
using DevExpress.XtraEditors;

namespace PlatCommon.Common
{
    public class PublicInsurVsByChargeType
    {

        public static int GetInsurMessage(string ChargeType, string item_code, string as_item_name, ref string message)
        {
            string sql = "select a.interfacecode from insurance.tj_interface_vs_charge a where a.chargetype='" + ChargeType + "'";
            DataTable dt = new ServerPublicClient().GetDataBySql(sql).Tables[0];
            string ls_tsbz, ls_gs, ls_jbyl;
            string ls_6 = "", ls_5 = "", ls_1 = "";
            string ls_grade, ls_zfbl, ls_isdrug, ls_item_name = "";

            if (dt.Rows.Count > 0)
            {
                string interfacecode = dt.Rows[0]["interfacecode"].ToString();
                if (interfacecode.Equals("hldyb"))
                {
                    sql = "select a.item_name, a.insur_level dj, b.aka302 tsbz, a.insur_scale zfbl, b.gs_code gs, b.cka802 jbyl ,a.isdrug from insurance.tj_vs_price a, insurance.drugs_dict_hldyb b ";
                    sql = sql + " where a.item_code ='" + item_code + "' and a.insur_code=b.insur_item_code union all ";
                    sql = sql + " select a.item_name, a.insur_level dj,'' tsbz,a.insur_scale zfbl,b.gs_code gs,b.cka802 jbyl,a.isdrug from insurance.tj_vs_price a,insurance.items_dict_hldyb b ";
                    sql = sql + " where a.item_code ='" + item_code + "' and a.insur_code = b.insur_item_code union all";
                    sql = sql + " select a.item_name, a.insur_level dj,'' tsbz,a.insur_scale zfbl,b.gs_code gs,b.cka802 jbyl,a.isdrug from insurance.tj_vs_price a,insurance.facility_dict_hldyb b ";
                    sql = sql + " where a.item_code ='" + item_code + "' and a.insur_code = b.insur_item_code";
                    DataTable tj_vs = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];

                    if (tj_vs.Rows.Count > 0)
                    {
                        ls_gs = tj_vs.Rows[0]["gs"].ToString();
                        ls_tsbz = tj_vs.Rows[0]["tsbz"].ToString();
                        ls_jbyl = tj_vs.Rows[0]["jbyl"].ToString();
                        ls_grade = tj_vs.Rows[0]["dj"].ToString();
                        ls_zfbl = tj_vs.Rows[0]["zfbl"].ToString();
                        ls_isdrug = tj_vs.Rows[0]["isdrug"].ToString();
                        ls_item_name = tj_vs.Rows[0]["item_name"].ToString();
                        if (ls_isdrug.Equals("1")) //药品
                        {
                            if (ls_gs.Equals("1")) //是否工伤
                            {
                                ls_6 = "是";
                            }
                            else
                            {
                                ls_6 = "否";
                            }
                            if (ls_jbyl.Equals("1")) //是否基本医疗
                            {
                                ls_5 = "是";
                            }
                            else
                            {
                                ls_5 = "否";
                            }
                            if (ls_zfbl.Equals("1")) //如果自付比例100%
                            {
                                if (ls_tsbz.Equals("01")) //如果特殊备注
                                {
                                    if (ls_grade.Equals("1"))  //医保等级
                                    {
                                        ls_1 = "甲类(自费)限门诊用药";
                                    }
                                    else if (ls_grade.Equals("2"))
                                    {
                                        ls_1 = "乙类(自费)限门诊用药";
                                    }
                                    else
                                    {
                                        ls_1 = "丙类(自费)";
                                    }
                                }
                                else
                                {
                                    if (ls_grade.Equals("1")) //医保等级
                                    {
                                        ls_1 = "甲类(自费)";
                                    }
                                    else if (ls_grade.Equals("2"))
                                    {
                                        ls_1 = "乙类(自费)";
                                    }
                                    else
                                    {
                                        ls_1 = "丙类(自费)";
                                    }
                                }
                            }
                            else
                            {
                                if (ls_tsbz.Equals("01")) //如果特殊备注
                                {
                                    if (ls_grade.Equals("1"))  //医保等级
                                    {
                                        ls_1 = "甲类限门诊用药";
                                    }
                                    else if (ls_grade.Equals("2"))
                                    {
                                        ls_1 = "乙类限门诊用药";
                                    }
                                    else
                                    {
                                        ls_1 = "丙类";
                                    }
                                }
                                else
                                {
                                    if (ls_grade.Equals("1")) //医保等级
                                    {
                                        ls_1 = "甲类";
                                    }
                                    else if (ls_grade.Equals("2"))
                                    {
                                        ls_1 = "乙类)";
                                    }
                                    else
                                    {
                                        ls_1 = "丙类";
                                    }
                                }
                            }
                        }
                        else if (ls_isdrug.Equals("2")) //非药品
                        {
                            if (ls_gs.Equals("1")) //是否工伤
                            {
                                ls_6 = "是";
                            }
                            else
                            {
                                ls_6 = "否";
                            }
                            if (ls_jbyl.Equals("1")) //是否基本医疗
                            {
                                ls_5 = "是";
                            }
                            else
                            {
                                ls_5 = "否";
                            }
                            if (ls_grade.Equals("1"))
                            {
                                ls_1 = "甲类";
                            }
                            else
                            {
                                ls_1 = "乙类";
                            }
                        }
                        else
                        {
                            if (ls_gs.Equals("1")) //是否工伤
                            {
                                ls_6 = "是";
                            }
                            else
                            {
                                ls_6 = "否";
                            }
                            if (ls_jbyl.Equals("1")) //是否基本医疗
                            {
                                ls_5 = "是";
                            }
                            else
                            {
                                ls_5 = "否";
                            }
                            ls_1 = "甲类";
                        }
                    }
                    else
                    {
                        ls_1 = "丙类";
                        ls_5 = "否";
                        ls_6 = "否";
                    }
                    message = "项目 {" + as_item_name + "}  医保属性为:{  " + ls_1 + "  基本医疗标志:  " + ls_5 + "  工伤标志:  " + ls_6 + "}";
                }
                else
                {
                    message = "";
                }
            }


            return 1;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ChargeType"></param>
        /// <param name="item_code"></param>
        /// <param name="as_item_name"></param>
        /// <param name="insurance_type"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static string CheckInsurMessage(string ChargeType, string item_code, string as_item_name)
        {
            string sql = "select parameter_value from app_configer_parameter a where a.parameter_name = 'INSURANCE_CHARGE_TYPE' AND HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' and app_name = 'OBILLING'";
            ServerPublicClient spc = new ServerPublicClient();

            DataTable dt = spc.GetDataBySql(sql).Tables[0];

            if (dt.Rows.Count > 0)
            {
                string chargelist = dt.Rows[0][0].ToString();
                int l = chargelist.IndexOf(ChargeType);
                if (chargelist.IndexOf(ChargeType) >= 0)
                {
                    sql = "select a.interfacecode from insurance.tj_interface_vs_charge a where a.chargetype='" + ChargeType + "'";
                    dt = spc.GetDataBySql(sql).Tables[0];
                    if (dt.Rows.Count > 0)
                    {
                        string interfacecode = dt.Rows[0]["interfacecode"].ToString();
                        sql = "select nvl(a.isupload,'') from insurance.tj_vs_price a where a.interfacecode = '" + interfacecode + "' and item_code = '" + item_code + "'";
                        dt = spc.GetDataBySql(sql).Tables[0];
                        if (dt.Rows.Count > 0)
                        {
                            string rev = dt.Rows[0][0].ToString();
                            if (!rev.Equals("1"))
                                return "项目【" + as_item_name + "】，医保目录对照未上传（或未审批）药品请联系药局，其它项目请联系医保科！";
                            else
                                return "";
                        }
                        else
                            return "项目【" + as_item_name + "】，医保未对照，药品请联系药局，其它项目请联系医保科！";
                    }
                    else
                        return "判断医保审核时未找到医保接口编码表tj_interface_vs_charge 费别为" + ChargeType;
                }
                else
                    return "";
            }
            else
            {
                return "";
            }


            //return 1;
        }
        public static int GetInsurlevel(string ChargeType, string item_code, string as_item_name, string insurance_type, ref string message)
        {
            string sql = "select a.interfacecode from insurance.tj_interface_vs_charge a where a.chargetype='" + ChargeType + "'";
            DataTable dt = new ServerPublicClient().GetDataBySql(sql).Tables[0];
            string ls_tsbz, ls_gs, ls_jbyl;
            string ls_6 = "", ls_5 = "", ls_1 = "";
            string ls_grade, ls_zfbl, ls_isdrug, ls_item_name = "";


            if (dt.Rows.Count > 0)
            {
                string interfacecode = dt.Rows[0]["interfacecode"].ToString();
                string s_grade = "", is_grade = "", is_zfbl = "";
                if (interfacecode.Equals("hldyb"))
                {
                    if (string.IsNullOrEmpty(insurance_type)) insurance_type = "21";

                    string ls_medtype = insurance_type.Substring(0, 1);
                    sql = "select a.item_name, a.insur_level dj, b.aka302 tsbz, a.insur_scale zfbl, b.gs_code gs, b.cka802 jbyl ,a.isdrug from insurance.tj_vs_price a, insurance.drugs_dict_hldyb b ";
                    sql = sql + " where a.item_code ='" + item_code + "' and a.insur_code=b.insur_item_code union all ";
                    sql = sql + " select a.item_name, a.insur_level dj,'' tsbz,a.insur_scale zfbl,b.gs_code gs,b.cka802 jbyl,a.isdrug from insurance.tj_vs_price a,insurance.items_dict_hldyb b ";
                    sql = sql + " where a.item_code ='" + item_code + "' and a.insur_code = b.insur_item_code union all";
                    sql = sql + " select a.item_name, a.insur_level dj,'' tsbz,a.insur_scale zfbl,b.gs_code gs,b.cka802 jbyl,a.isdrug from insurance.tj_vs_price a,insurance.facility_dict_hldyb b ";
                    sql = sql + " where a.item_code ='" + item_code + "' and a.insur_code = b.insur_item_code";
                    DataTable tj_vs = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];

                    if (tj_vs.Rows.Count > 0)
                    {
                        ls_gs = tj_vs.Rows[0]["gs"].ToString();
                        ls_tsbz = tj_vs.Rows[0]["tsbz"].ToString();
                        ls_jbyl = tj_vs.Rows[0]["jbyl"].ToString();
                        ls_grade = tj_vs.Rows[0]["dj"].ToString();
                        ls_zfbl = tj_vs.Rows[0]["zfbl"].ToString();
                        ls_isdrug = tj_vs.Rows[0]["isdrug"].ToString();
                        ls_item_name = tj_vs.Rows[0]["item_name"].ToString();
                        if (ls_medtype.Equals("5"))
                        {
                            if (ls_jbyl.Equals("0") && ls_gs.Equals("1"))
                            {
                                is_grade = "1";
                            }
                            else if (ls_gs.Equals("0"))
                            {
                                is_grade = "3";
                            }
                            else
                            {
                                is_grade = ls_grade;
                            }
                        }
                        else if (ls_jbyl.Equals("0"))
                        {
                            is_grade = "3";
                        }
                        else
                        {
                            is_grade = ls_grade;
                        }

                        if (is_grade.Equals("1"))
                        {
                            s_grade = "甲类";
                        }
                        else if (is_grade.Equals("2"))
                        {
                            s_grade = "乙类";
                        }
                        else
                        {
                            s_grade = "丙类";
                        }
                    }
                    else
                    {
                        s_grade = "丙类";
                    }
                    //message = "项目 {" + as_item_name + "}  医保属性为:{  " + ls_1 + "  基本医疗标志:  " + ls_5 + "  工伤标志:  " + ls_6 + "}";
                    message = s_grade;
                }
                else if (interfacecode.Equals("hldtb"))
                {
                    string sql1 = "select decode(a.insur_level,'1','甲类','2','乙类','9','丙类','丙类')insur_level from insurance.tj_vs_price a where a.interfacecode='hldtb' and a.item_code='" + item_code + "' ";
                    DataTable insur = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql1).Tables[0];
                    if (insur.Rows.Count > 0)
                    {
                        s_grade = insur.Rows[0]["insur_level"].ToString();
                    }
                    else
                    {
                        s_grade = "丙类";
                    }
                    message = s_grade;
                }
                else
                {
                    s_grade = "";
                    message = s_grade;
                }
            }


            return 1;
        }


        //yhy20210817 贯标去国家码开始
        public static int GetGjCode(string ChargeType, string item_code, string as_itemClass, string as_item_spec, ref string gj_code, ref string gj_name)
        {
            string sql = "select t.insur_code as gj_code, t.insur_name as gj_name from insurance.tj_vs_price t where t.interfacecode = 'ptyb' and t.item_code = '" + item_code + "'";
            if (as_itemClass.Equals("A") || as_itemClass.Equals("B"))
            {
                sql += " and t.item_spec = '" + as_item_spec + "'";
            }
            DataTable dt_vs = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
            if (dt_vs.Rows.Count > 0)
            {
                gj_code = dt_vs.Rows[0]["gj_code"].ToString();
                gj_name = dt_vs.Rows[0]["gj_name"].ToString();
            }
            return 0;
        }
        //yhy20210818 手术贯标国家码开始
        public static int GetGjCodeIcd9(string ChargeType, string item_code, ref string gj_code, ref string gj_name)
        {
            string sql = @"select  tvo.gj_code,tvo.gj_name from insurance.tj_vs_other tvo 
where tvo.interfacecode = 'ptyb' and tvo.other_type = 'ICD9HISVS医保VS国临' AND TVO.HIS_CODE ='" + item_code + "'";

            DataTable dt_vs = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
            if (dt_vs.Rows.Count > 0)
            {
                gj_code = dt_vs.Rows[0]["gj_code"].ToString();
                gj_name = dt_vs.Rows[0]["gj_name"].ToString();
            }
            return 0;
        }
        /// <summary>
        /// 本地医保码
        /// </summary>
        /// <param name="ChargeType"></param>
        /// <param name="item_code"></param>
        /// <param name="insur_code"></param>
        /// <param name="insur_name"></param>
        /// <returns></returns>
        public static int GetInsurCodeIcd9(string ChargeType, string item_code, ref string insur_code, ref string insur_name)
        {
            string sql = @"select  tvo.insur_code,tvo.insur_name from insurance.tj_vs_other tvo 
where tvo.interfacecode = 'ptyb' and tvo.other_type = 'ICD9HISVS医保VS国临' AND TVO.HIS_CODE ='" + item_code + "'";

            DataTable dt_vs = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
            if (dt_vs.Rows.Count > 0)
            {
                insur_code = dt_vs.Rows[0]["insur_code"].ToString();
                insur_name = dt_vs.Rows[0]["insur_name"].ToString();
            }
            return 0;
        }
        //yhy20210818 人员贯标
        public static int GetGjCodeStaff(string tusername, ref string gj_code, ref string gj_name)
        {
            string sql = @"   select b.staff_code,a.name from staff_dict a, insurance.t_staff_dict b
 where a.id_no = b.certificate_code(+)
 and a.user_name = '" + tusername + "'";
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
            if (dt != null && dt.Rows.Count > 0)
            {
                gj_code = dt.Rows[0][0].ToString();
                gj_name = dt.Rows[0][1].ToString();
            }
            return 0;
        }
        public static DataTable GetClinicVsGjDataTable(string clinicItemCode, string ItemClass)
        {
            DataTable dt = new DataTable();

            string sqla = @"select distinct t.gjbm as gj_code, t.insur_name as gj_name,t.insur_code sjbm,t.insur_name sjmc,t.item_code  HIS_CODE, t.item_name  HIS_NAME from insurance.tj_vs_price t, clinic_vs_charge a where t.interfacecode = 'ptyb' and a.charge_item_code = t.item_code
   and a.charge_item_class = t.item_class
   and a.clinic_item_code in ('" + clinicItemCode + "')";
            //sqla += " and a.clinic_item_class ='"+ItemClass+"'";
            dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sqla).Tables[0];
            return dt;
        }

        /// <summary>
        /// 获取医保字典信息
        /// </summary>
        /// <param name="ChargeType">费别</param>
        /// <param name="item_code">编码</param>
        /// <param name="as_item_name">名称</param>
        /// <param name="as_item_spec">规格</param>
        /// <param name="as_unit">单位</param>
        /// <param name="flag">使用规格标志</param>
        /// <param name="message">医保信息类</param>
        /// <returns>有对照 则返回1 无对照 则返回-1 非医保费别 返回0</returns>
        public static int GetInsurVsPrice(string ChargeType, string item_code, string as_item_name, string as_item_spec, string as_unit, string flag, ClassInsurVsMessage message, string psn_type = "")
        {
            string sql = "select a.interfacecode from insurance.tj_interface_vs_charge a where a.chargetype='" + ChargeType + "'";
            DataTable dt = new ServerPublicClient().GetDataBySql(sql).Tables[0];
            string ls_tsbz, ls_gs, ls_jbyl;
            string ls_6 = "", ls_5 = "", ls_1 = "";
            string ls_grade, ls_zfbl, ls_isdrug, ls_item_name = "";
            string sqlp = string.Empty;
            string tmaterialcode = string.Empty;
            if (dt.Rows.Count > 0)
            {
                if (string.IsNullOrEmpty(tmaterialcode)) tmaterialcode = item_code;

                string interfacecode = dt.Rows[0]["interfacecode"].ToString();
                string s_grade = "", is_grade = "", is_zfbl = "";
                if (interfacecode == "ptyb")
                {
                    sql = "select decode(c.chrgitm_lv,'01','甲类','02','乙类','03','丙类',c.chrgitm_lv) insur_level,d.selfpay_prop insur_scale,a.insur_code,a.insur_name, e.hilist_pric_uplmt_amt MAX_PRICE, nvl(c.lmt_used_flag,0) insur_xzbs, (case when c.lmt_used_flag='1' then c.memo else '' end) insur_xzsm from insurance.tj_vs_price a left join insurance.insur_dict1312_ptyb c on c.hilist_code = a.insur_code left join insurance.insur_dict1319_ptyb d on d.hilist_code = a.insur_code left join insurance.insur_dict1318_ptyb e on e.hilist_code = a.insur_code where a.interfacecode = '" + interfacecode + "' and item_code = '" + tmaterialcode + "' and (d.selfpay_prop_psn_type is null or d.selfpay_prop_psn_type='" + psn_type + "') ";
                    if (flag.Equals("1"))
                    {
                        sql = sql + " and item_spec ='" + as_item_spec + "' and units='" + as_unit + "' ";
                    }
                }
                else
                {
                    sql = "select decode(a.insur_level,'1','甲类','2','乙类','3','丙类',a.insur_level) insur_level,a.insur_scale,a.insur_code,a.insur_name,a.MAX_PRICE,'' insur_xzbs,'' insur_xzsm from insurance.tj_vs_price a where a.interfacecode='" + interfacecode + "' and item_code='" + tmaterialcode + "' ";
                    if (flag.Equals("1"))
                    {
                        sql = sql + " and item_spec ='" + as_item_spec + "' and units='" + as_unit + "' ";
                    }
                }


                DataTable dt_vs = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
                if (dt_vs.Rows.Count > 0)
                {
                    message.insur_level = dt_vs.Rows[0]["insur_level"].ToString();
                    message.insur_scale = dt_vs.Rows[0]["insur_scale"].ToString();
                    message.insur_memo = dt_vs.Rows[0]["MAX_PRICE"].ToString();
                    message.insur_code = dt_vs.Rows[0]["insur_code"].ToString();
                    message.insur_name = dt_vs.Rows[0]["insur_name"].ToString();
                    message.insur_xzbs = dt_vs.Rows[0]["insur_xzbs"].ToString();
                    message.insur_xzsm = dt_vs.Rows[0]["insur_xzsm"].ToString();
                    return 1;
                }
                else
                {
                    message.insur_level = "丙类";
                    message.insur_scale = "1";
                    message.insur_memo = "";
                    message.insur_xzbs = "0";
                    return -1;
                }

            }
            else
            {
                return 0;
            }

        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ChargeType">费别</param>
        /// <param name="patient_id">住院 patient_id，门诊 CLINIC_NO</param>
        /// <param name="visit_id">住院 visit_id，门诊 空</param>
        /// <param name="item_code">编码</param>
        /// <param name="as_item_name">名称</param>
        /// <param name="as_item_spec">规格</param>
        /// <param name="as_unit">单位</param>
        /// <param name="flag">使用规格标志</param>
        /// <param name="message">医保信息类</param>
        /// <param name="insur_adult">返回审批标志</param>
        /// <returns></returns>
        public static int GetInsurInfo(string ChargeType, string patient_id, string visit_id, string item_class, string item_code, string as_item_name, string as_item_spec, string as_unit, string flag, ref ClassInsurVsMessage message, ref string insur_adult)
        {
            string psn_type = GetPsnType(ChargeType, patient_id, visit_id);
            int ret = GetInsurVsPrice(ChargeType, item_code, as_item_name, as_item_spec, as_unit, flag, message, psn_type);
            if (ret == 1)
            {
                if (message.insur_xzbs == "1"/*&& (item_class=="I"|| item_class=="A"|| item_class=="B")*/)
                {
                    FrmInsuranceAdult frm = new FrmInsuranceAdult(item_code, as_item_name, as_item_spec, message.insur_code, message.insur_name, message.insur_level, message.insur_scale, message.insur_xzbs, message.insur_xzsm);
                    frm.ShowDialog();
                    insur_adult = frm.insur_adult;
                }
            }
            //if (string.IsNullOrEmpty(insur_adult))
            //{
            //    insur_adult = "0";
            //}
            return ret;
        }

        public static string GetPsnType(string ChargeType, string PatientID, string VisitID)
        {
            string psn_type = "";
            if (ChargeType == "平台医保")
            {
                string sql = "";
                if (!string.IsNullOrEmpty(VisitID))
                {
                    sql = "select t.out_insuinfo_insutype from insurance.pats_dict_ptyb t ,pat_visit a where t.out_baseinfo_psn_no=a.insurance_no and a.patient_id='" + PatientID + "' and a.visit_id='" + VisitID + "'";
                }
                else
                {
                    sql = "select t.out_insuinfo_insutype from insurance.pats_dict_ptyb t ,clinic_master a where t.out_baseinfo_psn_no=a.insurance_no and a.clinic_no='" + PatientID + "'";
                }
                psn_type = new NM_Service.NMService.ServerPublicClient().GetSingleValue(sql);
            }
            return psn_type;
        }
    }
}
