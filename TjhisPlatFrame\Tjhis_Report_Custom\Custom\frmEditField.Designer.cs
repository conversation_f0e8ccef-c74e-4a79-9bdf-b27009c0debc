﻿using DevExpress.XtraLayout;

namespace Tjhis.Report.Custom.Custom
{
    partial class frmEditField
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmEditField));
            DevExpress.XtraGrid.GridFormatRule gridFormatRule1 = new DevExpress.XtraGrid.GridFormatRule();
            DevExpress.XtraEditors.FormatConditionRuleValue formatConditionRuleValue1 = new DevExpress.XtraEditors.FormatConditionRuleValue();
            DevExpress.XtraEditors.Controls.EditorButtonImageOptions editorButtonImageOptions1 = new DevExpress.XtraEditors.Controls.EditorButtonImageOptions();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject1 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject2 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject3 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject4 = new DevExpress.Utils.SerializableAppearanceObject();
            this.layoutControl1 = new DevExpress.XtraLayout.LayoutControl();
            this.vColumnsDetail = new DevExpress.XtraVerticalGrid.VGridControl();
            this.repositoryItemCheckEdit3 = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.repositoryItemLookUpEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.repositoryItemButtonEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit();
            this.repLupPreNames = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.repLupFormatType = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.baseSet = new DevExpress.XtraVerticalGrid.Rows.CategoryRow();
            this.rSerialNo = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.rColumnName = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.rColumnField = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.rColumnWidth = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.rVisible = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.rColumnFormatString = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.rColumnFormatType = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.summarySet = new DevExpress.XtraVerticalGrid.Rows.CategoryRow();
            this.rIfSummary = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.rSummaryType = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.rSummaryDisplay = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.unBoundSet = new DevExpress.XtraVerticalGrid.Rows.CategoryRow();
            this.rUnbound = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.rExpression = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.rUnboundType = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.formatRuleSet = new DevExpress.XtraVerticalGrid.Rows.CategoryRow();
            this.rFormatRule = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.rRuleExpression = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.rPreDefiendName = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.rColumnID = new DevExpress.XtraVerticalGrid.Rows.EditorRow();
            this.gcDesign = new DevExpress.XtraGrid.GridControl();
            this.gvDesign = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.barBtnAdd1 = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnDelete1 = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave1 = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnAdd = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barBtnDelete = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarLargeButtonItem();
            this.barReportNo = new DevExpress.XtraBars.BarEditItem();
            this.lookUpReportNo = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.barLargeButtonItem1 = new DevExpress.XtraBars.BarLargeButtonItem();
            this.bar3 = new DevExpress.XtraBars.Bar();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.SERIAL_NO = new DevExpress.XtraGrid.Columns.GridColumn();
            this.COLUMN_NAME = new DevExpress.XtraGrid.Columns.GridColumn();
            this.COLUMN_FIELD = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repLupFields = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.COLUMN_WIDTH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemSpinEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.IF_GROUP = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemCheckEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.GROUP_INDEX = new DevExpress.XtraGrid.Columns.GridColumn();
            this.IF_SUMMARY = new DevExpress.XtraGrid.Columns.GridColumn();
            this.SUMMARY_TYPE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repLupSummrayType = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.UNBOUND = new DevExpress.XtraGrid.Columns.GridColumn();
            this.EXPRESSION = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemButtonEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit();
            this.UNBOUND_TYPE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repLupUnboundType = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.VISIBLE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemCheckEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.COLUMN_ID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem3 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.splitterItem1 = new DevExpress.XtraLayout.SplitterItem();
            this.panel1 = new System.Windows.Forms.Panel();
            this.layoutControlItem4 = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).BeginInit();
            this.layoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.vColumnsDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemLookUpEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemButtonEdit2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLupPreNames)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLupFormatType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcDesign)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvDesign)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lookUpReportNo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLupFields)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLupSummrayType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemButtonEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLupUnboundType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitterItem1)).BeginInit();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem4)).BeginInit();
            this.SuspendLayout();
            // 
            // layoutControl1
            // 
            this.layoutControl1.Controls.Add(this.panel1);
            this.layoutControl1.Controls.Add(this.vColumnsDetail);
            this.layoutControl1.Controls.Add(this.gridControl1);
            this.layoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl1.Location = new System.Drawing.Point(0, 69);
            this.layoutControl1.Name = "layoutControl1";
            this.layoutControl1.Root = this.layoutControlGroup1;
            this.layoutControl1.Size = new System.Drawing.Size(784, 469);
            this.layoutControl1.TabIndex = 0;
            this.layoutControl1.Text = "layoutControl1";
            // 
            // vColumnsDetail
            // 
            this.vColumnsDetail.Appearance.RecordValue.Options.UseTextOptions = true;
            this.vColumnsDetail.Appearance.RecordValue.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.vColumnsDetail.Appearance.RecordValue.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.vColumnsDetail.Appearance.RowHeaderPanel.BackColor = System.Drawing.Color.White;
            this.vColumnsDetail.Appearance.RowHeaderPanel.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold);
            this.vColumnsDetail.Appearance.RowHeaderPanel.Options.UseBackColor = true;
            this.vColumnsDetail.Appearance.RowHeaderPanel.Options.UseFont = true;
            this.vColumnsDetail.Appearance.RowHeaderPanel.Options.UseTextOptions = true;
            this.vColumnsDetail.Appearance.RowHeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.vColumnsDetail.Appearance.VertLine.BorderColor = System.Drawing.Color.DeepSkyBlue;
            this.vColumnsDetail.Appearance.VertLine.Options.UseBorderColor = true;
            this.vColumnsDetail.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.vColumnsDetail.Cursor = System.Windows.Forms.Cursors.Default;
            this.vColumnsDetail.LayoutStyle = DevExpress.XtraVerticalGrid.LayoutViewStyle.SingleRecordView;
            this.vColumnsDetail.Location = new System.Drawing.Point(287, 2);
            this.vColumnsDetail.Name = "vColumnsDetail";
            this.vColumnsDetail.OptionsFilter.AllowFilter = false;
            this.vColumnsDetail.OptionsFilter.AllowFilterEditor = false;
            this.vColumnsDetail.OptionsFilter.AllowMRUFilterList = false;
            this.vColumnsDetail.OptionsView.FixedLineWidth = 3;
            this.vColumnsDetail.OptionsView.MinRowAutoHeight = 28;
            this.vColumnsDetail.OptionsView.ShowButtons = false;
            this.vColumnsDetail.OptionsView.ShowFilterPanelMode = DevExpress.XtraVerticalGrid.ShowFilterPanelMode.Never;
            this.vColumnsDetail.RecordWidth = 135;
            this.vColumnsDetail.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemCheckEdit3,
            this.repositoryItemLookUpEdit1,
            this.repositoryItemButtonEdit2,
            this.repLupPreNames,
            this.repLupFormatType});
            this.vColumnsDetail.RowHeaderWidth = 65;
            this.vColumnsDetail.Rows.AddRange(new DevExpress.XtraVerticalGrid.Rows.BaseRow[] {
            this.baseSet,
            this.summarySet,
            this.unBoundSet,
            this.formatRuleSet,
            this.rColumnID});
            this.vColumnsDetail.Size = new System.Drawing.Size(495, 223);
            this.vColumnsDetail.TabIndex = 6;
            // 
            // repositoryItemCheckEdit3
            // 
            this.repositoryItemCheckEdit3.AutoHeight = false;
            this.repositoryItemCheckEdit3.GlyphAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.repositoryItemCheckEdit3.Name = "repositoryItemCheckEdit3";
            // 
            // repositoryItemLookUpEdit1
            // 
            this.repositoryItemLookUpEdit1.AutoHeight = false;
            this.repositoryItemLookUpEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemLookUpEdit1.Name = "repositoryItemLookUpEdit1";
            // 
            // repositoryItemButtonEdit2
            // 
            this.repositoryItemButtonEdit2.AutoHeight = false;
            this.repositoryItemButtonEdit2.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.repositoryItemButtonEdit2.Name = "repositoryItemButtonEdit2";
            this.repositoryItemButtonEdit2.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.repositoryItemButtonEdit2.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.repositoryItemButtonEdit2_ButtonClick);
            // 
            // repLupPreNames
            // 
            this.repLupPreNames.AutoHeight = false;
            this.repLupPreNames.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repLupPreNames.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_NAME", " ")});
            this.repLupPreNames.DisplayMember = "ITEM_NAME";
            this.repLupPreNames.Name = "repLupPreNames";
            this.repLupPreNames.NullText = "";
            this.repLupPreNames.ValueMember = "ITEM_CODE";
            // 
            // repLupFormatType
            // 
            this.repLupFormatType.AutoHeight = false;
            this.repLupFormatType.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repLupFormatType.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_NAME", "格式化类型")});
            this.repLupFormatType.DisplayMember = "ITEM_NAME";
            this.repLupFormatType.Name = "repLupFormatType";
            this.repLupFormatType.NullText = "";
            this.repLupFormatType.ValueMember = "ITEM_CODE";
            // 
            // baseSet
            // 
            this.baseSet.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold);
            this.baseSet.Appearance.Options.UseFont = true;
            this.baseSet.ChildRows.AddRange(new DevExpress.XtraVerticalGrid.Rows.BaseRow[] {
            this.rSerialNo,
            this.rColumnField,
            this.rColumnWidth,
            this.rColumnFormatString,
            this.rColumnFormatType});
            this.baseSet.Height = 28;
            this.baseSet.Name = "baseSet";
            this.baseSet.Properties.Caption = "基础设置";
            this.baseSet.Properties.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("baseSet.Properties.ImageOptions.Image")));
            // 
            // rSerialNo
            // 
            this.rSerialNo.Appearance.Options.UseTextOptions = true;
            this.rSerialNo.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.rSerialNo.ChildRows.AddRange(new DevExpress.XtraVerticalGrid.Rows.BaseRow[] {
            this.rColumnName});
            this.rSerialNo.Name = "rSerialNo";
            this.rSerialNo.Properties.Caption = "序号";
            this.rSerialNo.Properties.FieldName = "SERIAL_NO";
            // 
            // rColumnName
            // 
            this.rColumnName.Height = 28;
            this.rColumnName.Name = "rColumnName";
            this.rColumnName.Properties.Caption = "列标题";
            this.rColumnName.Properties.FieldName = "COLUMN_NAME";
            // 
            // rColumnField
            // 
            this.rColumnField.Name = "rColumnField";
            this.rColumnField.Properties.Caption = "绑定字段";
            this.rColumnField.Properties.FieldName = "COLUMN_FIELD";
            // 
            // rColumnWidth
            // 
            this.rColumnWidth.Appearance.Options.UseTextOptions = true;
            this.rColumnWidth.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.rColumnWidth.ChildRows.AddRange(new DevExpress.XtraVerticalGrid.Rows.BaseRow[] {
            this.rVisible});
            this.rColumnWidth.Name = "rColumnWidth";
            this.rColumnWidth.Properties.Caption = "列宽";
            this.rColumnWidth.Properties.FieldName = "COLUMN_WIDTH";
            // 
            // rVisible
            // 
            this.rVisible.Name = "rVisible";
            this.rVisible.Properties.Caption = "隐藏字段";
            this.rVisible.Properties.FieldName = "VISIBLE";
            this.rVisible.Properties.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            // 
            // rColumnFormatString
            // 
            this.rColumnFormatString.Name = "rColumnFormatString";
            this.rColumnFormatString.Properties.Caption = "格式化字符串";
            this.rColumnFormatString.Properties.FieldName = "FORMAT_STRING";
            // 
            // rColumnFormatType
            // 
            this.rColumnFormatType.Name = "rColumnFormatType";
            this.rColumnFormatType.Properties.Caption = "格式化类型";
            this.rColumnFormatType.Properties.FieldName = "FORMAT_TYPE";
            this.rColumnFormatType.Properties.RowEdit = this.repLupFormatType;
            // 
            // summarySet
            // 
            this.summarySet.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold);
            this.summarySet.Appearance.Options.UseFont = true;
            this.summarySet.ChildRows.AddRange(new DevExpress.XtraVerticalGrid.Rows.BaseRow[] {
            this.rIfSummary,
            this.rSummaryType,
            this.rSummaryDisplay});
            this.summarySet.Height = 28;
            this.summarySet.Name = "summarySet";
            this.summarySet.Properties.Caption = "是否启用合计";
            this.summarySet.Properties.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("summarySet.Properties.ImageOptions.Image")));
            // 
            // rIfSummary
            // 
            this.rIfSummary.Name = "rIfSummary";
            this.rIfSummary.Properties.Caption = "合计";
            this.rIfSummary.Properties.FieldName = "IF_SUMMARY";
            this.rIfSummary.Properties.RowEdit = this.repositoryItemCheckEdit3;
            this.rIfSummary.Properties.Value = true;
            // 
            // rSummaryType
            // 
            this.rSummaryType.Name = "rSummaryType";
            this.rSummaryType.Properties.Caption = "合计类型";
            this.rSummaryType.Properties.FieldName = "SUMMARY_TYPE";
            // 
            // rSummaryDisplay
            // 
            this.rSummaryDisplay.Name = "rSummaryDisplay";
            this.rSummaryDisplay.Properties.Caption = "合计格式";
            this.rSummaryDisplay.Properties.FieldName = "SUMMARY_DISPLAY";
            // 
            // unBoundSet
            // 
            this.unBoundSet.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold);
            this.unBoundSet.Appearance.Options.UseFont = true;
            this.unBoundSet.ChildRows.AddRange(new DevExpress.XtraVerticalGrid.Rows.BaseRow[] {
            this.rUnbound,
            this.rExpression,
            this.rUnboundType});
            this.unBoundSet.Name = "unBoundSet";
            this.unBoundSet.Properties.Caption = "设置非绑定列";
            this.unBoundSet.Properties.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("unBoundSet.Properties.ImageOptions.Image")));
            // 
            // rUnbound
            // 
            this.rUnbound.Name = "rUnbound";
            this.rUnbound.Properties.Caption = "非绑定列";
            this.rUnbound.Properties.FieldName = "IF_UNBOUND";
            // 
            // rExpression
            // 
            this.rExpression.Name = "rExpression";
            this.rExpression.Properties.Caption = "表达式";
            this.rExpression.Properties.FieldName = "EXPRESSION";
            // 
            // rUnboundType
            // 
            this.rUnboundType.Name = "rUnboundType";
            this.rUnboundType.Properties.Caption = "非绑定类型";
            this.rUnboundType.Properties.FieldName = "UNBOUND_TYPE";
            // 
            // formatRuleSet
            // 
            this.formatRuleSet.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold);
            this.formatRuleSet.Appearance.Options.UseFont = true;
            this.formatRuleSet.ChildRows.AddRange(new DevExpress.XtraVerticalGrid.Rows.BaseRow[] {
            this.rFormatRule,
            this.rRuleExpression,
            this.rPreDefiendName});
            this.formatRuleSet.Name = "formatRuleSet";
            this.formatRuleSet.Properties.Caption = "按规则格式化样式";
            this.formatRuleSet.Properties.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("formatRuleSet.Properties.ImageOptions.Image")));
            // 
            // rFormatRule
            // 
            this.rFormatRule.Name = "rFormatRule";
            this.rFormatRule.Properties.Caption = "启用规则";
            this.rFormatRule.Properties.FieldName = "IS_FORMATRULE";
            this.rFormatRule.Properties.RowEdit = this.repositoryItemCheckEdit3;
            // 
            // rRuleExpression
            // 
            this.rRuleExpression.Name = "rRuleExpression";
            this.rRuleExpression.Properties.Caption = "规则表达式";
            this.rRuleExpression.Properties.FieldName = "RULE_EXPRESSION";
            this.rRuleExpression.Properties.RowEdit = this.repositoryItemButtonEdit2;
            // 
            // rPreDefiendName
            // 
            this.rPreDefiendName.Name = "rPreDefiendName";
            this.rPreDefiendName.Properties.Caption = "样式";
            this.rPreDefiendName.Properties.FieldName = "PREDEFINED_NAME";
            this.rPreDefiendName.Properties.RowEdit = this.repLupPreNames;
            // 
            // rColumnID
            // 
            this.rColumnID.Name = "rColumnID";
            this.rColumnID.Properties.Caption = "列代码";
            this.rColumnID.Properties.FieldName = "COLUMN_ID";
            this.rColumnID.Properties.ReadOnly = true;
            // 
            // gcDesign
            // 
            this.gcDesign.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcDesign.Location = new System.Drawing.Point(0, 0);
            this.gcDesign.MainView = this.gvDesign;
            this.gcDesign.MenuManager = this.barManager1;
            this.gcDesign.Name = "gcDesign";
            this.gcDesign.Size = new System.Drawing.Size(778, 217);
            this.gcDesign.TabIndex = 5;
            this.gcDesign.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvDesign});
            // 
            // gvDesign
            // 
            this.gvDesign.GridControl = this.gcDesign;
            this.gvDesign.Name = "gvDesign";
            this.gvDesign.OptionsView.ColumnAutoWidth = false;
            this.gvDesign.OptionsView.ShowGroupPanel = false;
            // 
            // barManager1
            // 
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1,
            this.bar2,
            this.bar3});
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnAdd1,
            this.barBtnDelete1,
            this.barBtnSave1,
            this.barReportNo,
            this.barBtnAdd,
            this.barBtnDelete,
            this.barBtnSave,
            this.barLargeButtonItem1});
            this.barManager1.MainMenu = this.bar2;
            this.barManager1.MaxItemId = 9;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.lookUpReportNo});
            this.barManager1.StatusBar = this.bar3;
            // 
            // bar1
            // 
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 1;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.Text = "Tools";
            this.bar1.Visible = false;
            // 
            // bar2
            // 
            this.bar2.BarName = "Main menu";
            this.bar2.DockCol = 0;
            this.bar2.DockRow = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar2.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barBtnAdd1, "", false, true, false, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barBtnDelete1, "", false, true, false, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barBtnSave1, "", false, true, false, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barBtnAdd, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barBtnDelete, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnSave),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.Width, this.barReportNo, "", false, true, true, 119),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barLargeButtonItem1, "", true, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph)});
            this.bar2.OptionsBar.DrawBorder = false;
            this.bar2.OptionsBar.DrawDragBorder = false;
            this.bar2.OptionsBar.MultiLine = true;
            this.bar2.OptionsBar.UseWholeRow = true;
            this.bar2.Text = "Main menu";
            // 
            // barBtnAdd1
            // 
            this.barBtnAdd1.Caption = "新增";
            this.barBtnAdd1.Id = 0;
            this.barBtnAdd1.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barBtnAdd1.ImageOptions.Image")));
            this.barBtnAdd1.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barBtnAdd1.ImageOptions.LargeImage")));
            this.barBtnAdd1.Name = "barBtnAdd1";
            // 
            // barBtnDelete1
            // 
            this.barBtnDelete1.Caption = "删除";
            this.barBtnDelete1.Id = 1;
            this.barBtnDelete1.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barBtnDelete1.ImageOptions.Image")));
            this.barBtnDelete1.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barBtnDelete1.ImageOptions.LargeImage")));
            this.barBtnDelete1.Name = "barBtnDelete1";
            // 
            // barBtnSave1
            // 
            this.barBtnSave1.Caption = "保存";
            this.barBtnSave1.Id = 2;
            this.barBtnSave1.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barBtnSave1.ImageOptions.Image")));
            this.barBtnSave1.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barBtnSave1.ImageOptions.LargeImage")));
            this.barBtnSave1.Name = "barBtnSave1";
            // 
            // barBtnAdd
            // 
            this.barBtnAdd.Caption = "新增";
            this.barBtnAdd.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.barBtnAdd.Id = 5;
            this.barBtnAdd.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barBtnAdd.ImageOptions.Image")));
            this.barBtnAdd.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barBtnAdd.ImageOptions.LargeImage")));
            this.barBtnAdd.Name = "barBtnAdd";
            // 
            // barBtnDelete
            // 
            this.barBtnDelete.Caption = "删除";
            this.barBtnDelete.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.barBtnDelete.Id = 6;
            this.barBtnDelete.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barBtnDelete.ImageOptions.Image")));
            this.barBtnDelete.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barBtnDelete.ImageOptions.LargeImage")));
            this.barBtnDelete.Name = "barBtnDelete";
            // 
            // barBtnSave
            // 
            this.barBtnSave.Caption = "保存";
            this.barBtnSave.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.barBtnSave.Id = 7;
            this.barBtnSave.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barBtnSave.ImageOptions.Image")));
            this.barBtnSave.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barBtnSave.ImageOptions.LargeImage")));
            this.barBtnSave.Name = "barBtnSave";
            // 
            // barReportNo
            // 
            this.barReportNo.Edit = this.lookUpReportNo;
            this.barReportNo.Id = 4;
            this.barReportNo.Name = "barReportNo";
            // 
            // lookUpReportNo
            // 
            this.lookUpReportNo.AutoHeight = false;
            this.lookUpReportNo.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.lookUpReportNo.Name = "lookUpReportNo";
            this.lookUpReportNo.NullText = "";
            // 
            // barLargeButtonItem1
            // 
            this.barLargeButtonItem1.Caption = "关闭";
            this.barLargeButtonItem1.CaptionAlignment = DevExpress.XtraBars.BarItemCaptionAlignment.Right;
            this.barLargeButtonItem1.Id = 8;
            this.barLargeButtonItem1.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barLargeButtonItem1.ImageOptions.Image")));
            this.barLargeButtonItem1.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barLargeButtonItem1.ImageOptions.LargeImage")));
            this.barLargeButtonItem1.Name = "barLargeButtonItem1";
            this.barLargeButtonItem1.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barLargeButtonItem1_ItemClick);
            // 
            // bar3
            // 
            this.bar3.BarName = "Status bar";
            this.bar3.CanDockStyle = DevExpress.XtraBars.BarCanDockStyle.Bottom;
            this.bar3.DockCol = 0;
            this.bar3.DockRow = 0;
            this.bar3.DockStyle = DevExpress.XtraBars.BarDockStyle.Bottom;
            this.bar3.OptionsBar.AllowQuickCustomization = false;
            this.bar3.OptionsBar.DrawDragBorder = false;
            this.bar3.OptionsBar.UseWholeRow = true;
            this.bar3.Text = "Status bar";
            this.bar3.Visible = false;
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Manager = this.barManager1;
            this.barDockControlTop.Size = new System.Drawing.Size(784, 69);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 538);
            this.barDockControlBottom.Manager = this.barManager1;
            this.barDockControlBottom.Size = new System.Drawing.Size(784, 23);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 69);
            this.barDockControlLeft.Manager = this.barManager1;
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 469);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(784, 69);
            this.barDockControlRight.Manager = this.barManager1;
            this.barDockControlRight.Size = new System.Drawing.Size(0, 469);
            // 
            // gridControl1
            // 
            this.gridControl1.Location = new System.Drawing.Point(2, 2);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repLupFields,
            this.repositoryItemCheckEdit1,
            this.repLupSummrayType,
            this.repositoryItemSpinEdit1,
            this.repositoryItemButtonEdit1,
            this.repLupUnboundType,
            this.repositoryItemCheckEdit2});
            this.gridControl1.Size = new System.Drawing.Size(276, 223);
            this.gridControl1.TabIndex = 4;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Appearance.FocusedCell.BackColor = System.Drawing.Color.DodgerBlue;
            this.gridView1.Appearance.FocusedCell.ForeColor = System.Drawing.Color.White;
            this.gridView1.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gridView1.Appearance.FocusedCell.Options.UseForeColor = true;
            this.gridView1.Appearance.FocusedRow.BackColor = System.Drawing.Color.DodgerBlue;
            this.gridView1.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.DodgerBlue;
            this.gridView1.Appearance.FocusedRow.BorderColor = System.Drawing.Color.Black;
            this.gridView1.Appearance.FocusedRow.ForeColor = System.Drawing.Color.White;
            this.gridView1.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gridView1.Appearance.FocusedRow.Options.UseBorderColor = true;
            this.gridView1.Appearance.FocusedRow.Options.UseForeColor = true;
            this.gridView1.Appearance.HideSelectionRow.BackColor = System.Drawing.Color.DodgerBlue;
            this.gridView1.Appearance.HideSelectionRow.ForeColor = System.Drawing.Color.White;
            this.gridView1.Appearance.HideSelectionRow.Options.UseBackColor = true;
            this.gridView1.Appearance.HideSelectionRow.Options.UseForeColor = true;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.SERIAL_NO,
            this.COLUMN_NAME,
            this.COLUMN_FIELD,
            this.COLUMN_WIDTH,
            this.IF_GROUP,
            this.GROUP_INDEX,
            this.IF_SUMMARY,
            this.SUMMARY_TYPE,
            this.UNBOUND,
            this.EXPRESSION,
            this.UNBOUND_TYPE,
            this.VISIBLE,
            this.COLUMN_ID});
            gridFormatRule1.Name = "Format0";
            gridFormatRule1.Rule = formatConditionRuleValue1;
            this.gridView1.FormatRules.Add(gridFormatRule1);
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsView.ShowColumnHeaders = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.False;
            this.gridView1.OptionsView.ShowIndicator = false;
            this.gridView1.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.False;
            this.gridView1.RowHeight = 25;
            // 
            // SERIAL_NO
            // 
            this.SERIAL_NO.AppearanceCell.Options.UseTextOptions = true;
            this.SERIAL_NO.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.SERIAL_NO.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.SERIAL_NO.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.SERIAL_NO.Caption = "序号";
            this.SERIAL_NO.DisplayFormat.FormatString = "{1}.";
            this.SERIAL_NO.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Custom;
            this.SERIAL_NO.FieldName = "SERIAL_NO";
            this.SERIAL_NO.Name = "SERIAL_NO";
            this.SERIAL_NO.Visible = true;
            this.SERIAL_NO.VisibleIndex = 0;
            this.SERIAL_NO.Width = 42;
            // 
            // COLUMN_NAME
            // 
            this.COLUMN_NAME.AppearanceCell.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold);
            this.COLUMN_NAME.AppearanceCell.Options.UseFont = true;
            this.COLUMN_NAME.AppearanceCell.Options.UseTextOptions = true;
            this.COLUMN_NAME.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.COLUMN_NAME.Caption = "显示名";
            this.COLUMN_NAME.FieldName = "COLUMN_NAME";
            this.COLUMN_NAME.Name = "COLUMN_NAME";
            this.COLUMN_NAME.Visible = true;
            this.COLUMN_NAME.VisibleIndex = 1;
            this.COLUMN_NAME.Width = 96;
            // 
            // COLUMN_FIELD
            // 
            this.COLUMN_FIELD.Caption = "绑定字段";
            this.COLUMN_FIELD.ColumnEdit = this.repLupFields;
            this.COLUMN_FIELD.FieldName = "COLUMN_FIELD";
            this.COLUMN_FIELD.Name = "COLUMN_FIELD";
            this.COLUMN_FIELD.Width = 113;
            // 
            // repLupFields
            // 
            this.repLupFields.AutoHeight = false;
            this.repLupFields.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repLupFields.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_NAME", "字段")});
            this.repLupFields.DisplayMember = "ITEM_NAME";
            this.repLupFields.Name = "repLupFields";
            this.repLupFields.NullText = "[选择绑定字段]";
            this.repLupFields.ValueMember = "ITEM_CODE";
            // 
            // COLUMN_WIDTH
            // 
            this.COLUMN_WIDTH.Caption = "列宽";
            this.COLUMN_WIDTH.ColumnEdit = this.repositoryItemSpinEdit1;
            this.COLUMN_WIDTH.FieldName = "COLUMN_WIDTH";
            this.COLUMN_WIDTH.Name = "COLUMN_WIDTH";
            this.COLUMN_WIDTH.Width = 48;
            // 
            // repositoryItemSpinEdit1
            // 
            this.repositoryItemSpinEdit1.AutoHeight = false;
            this.repositoryItemSpinEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemSpinEdit1.Name = "repositoryItemSpinEdit1";
            // 
            // IF_GROUP
            // 
            this.IF_GROUP.Caption = "是否分组";
            this.IF_GROUP.ColumnEdit = this.repositoryItemCheckEdit1;
            this.IF_GROUP.FieldName = "IF_GROUP";
            this.IF_GROUP.Name = "IF_GROUP";
            this.IF_GROUP.Width = 57;
            // 
            // repositoryItemCheckEdit1
            // 
            this.repositoryItemCheckEdit1.AutoHeight = false;
            this.repositoryItemCheckEdit1.Name = "repositoryItemCheckEdit1";
            this.repositoryItemCheckEdit1.ValueChecked = "1";
            this.repositoryItemCheckEdit1.ValueUnchecked = "0";
            // 
            // GROUP_INDEX
            // 
            this.GROUP_INDEX.Caption = "分组索引";
            this.GROUP_INDEX.FieldName = "GROUP_INDEX";
            this.GROUP_INDEX.Name = "GROUP_INDEX";
            this.GROUP_INDEX.Width = 59;
            // 
            // IF_SUMMARY
            // 
            this.IF_SUMMARY.Caption = "合计";
            this.IF_SUMMARY.ColumnEdit = this.repositoryItemCheckEdit1;
            this.IF_SUMMARY.FieldName = "IF_SUMMARY";
            this.IF_SUMMARY.Name = "IF_SUMMARY";
            this.IF_SUMMARY.Width = 36;
            // 
            // SUMMARY_TYPE
            // 
            this.SUMMARY_TYPE.Caption = "合计类型";
            this.SUMMARY_TYPE.ColumnEdit = this.repLupSummrayType;
            this.SUMMARY_TYPE.FieldName = "SUMMARY_TYPE";
            this.SUMMARY_TYPE.Name = "SUMMARY_TYPE";
            this.SUMMARY_TYPE.Width = 65;
            // 
            // repLupSummrayType
            // 
            this.repLupSummrayType.AutoHeight = false;
            this.repLupSummrayType.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repLupSummrayType.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_NAME", "合计")});
            this.repLupSummrayType.DisplayMember = "ITEM_NAME";
            this.repLupSummrayType.Name = "repLupSummrayType";
            this.repLupSummrayType.NullText = "[合计类型]";
            this.repLupSummrayType.ValueMember = "ITEM_CODE";
            // 
            // UNBOUND
            // 
            this.UNBOUND.Caption = "自定义";
            this.UNBOUND.ColumnEdit = this.repositoryItemCheckEdit1;
            this.UNBOUND.FieldName = "IF_UNBOUND";
            this.UNBOUND.Name = "UNBOUND";
            this.UNBOUND.Width = 46;
            // 
            // EXPRESSION
            // 
            this.EXPRESSION.Caption = "表达式";
            this.EXPRESSION.ColumnEdit = this.repositoryItemButtonEdit1;
            this.EXPRESSION.FieldName = "EXPRESSION";
            this.EXPRESSION.Name = "EXPRESSION";
            this.EXPRESSION.Width = 118;
            // 
            // repositoryItemButtonEdit1
            // 
            this.repositoryItemButtonEdit1.AutoHeight = false;
            this.repositoryItemButtonEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Ellipsis, "编辑表达式", -1, true, true, false, editorButtonImageOptions1, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject1, serializableAppearanceObject2, serializableAppearanceObject3, serializableAppearanceObject4, "", null, null, DevExpress.Utils.ToolTipAnchor.Default)});
            this.repositoryItemButtonEdit1.Name = "repositoryItemButtonEdit1";
            this.repositoryItemButtonEdit1.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.repositoryItemButtonEdit1.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.repositoryItemButtonEdit1_ButtonClick);
            // 
            // UNBOUND_TYPE
            // 
            this.UNBOUND_TYPE.Caption = "类型";
            this.UNBOUND_TYPE.ColumnEdit = this.repLupUnboundType;
            this.UNBOUND_TYPE.FieldName = "UNBOUND_TYPE";
            this.UNBOUND_TYPE.Name = "UNBOUND_TYPE";
            // 
            // repLupUnboundType
            // 
            this.repLupUnboundType.AutoHeight = false;
            this.repLupUnboundType.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repLupUnboundType.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_NAME", " ")});
            this.repLupUnboundType.DisplayMember = "ITEM_NAME";
            this.repLupUnboundType.Name = "repLupUnboundType";
            this.repLupUnboundType.NullText = "[选择类型]";
            this.repLupUnboundType.ValueMember = "ITEM_CODE";
            // 
            // VISIBLE
            // 
            this.VISIBLE.Caption = "隐藏";
            this.VISIBLE.ColumnEdit = this.repositoryItemCheckEdit2;
            this.VISIBLE.FieldName = "VISIBLE";
            this.VISIBLE.Name = "VISIBLE";
            this.VISIBLE.Width = 32;
            // 
            // repositoryItemCheckEdit2
            // 
            this.repositoryItemCheckEdit2.AutoHeight = false;
            this.repositoryItemCheckEdit2.Name = "repositoryItemCheckEdit2";
            this.repositoryItemCheckEdit2.ValueChecked = "1";
            this.repositoryItemCheckEdit2.ValueGrayed = "";
            this.repositoryItemCheckEdit2.ValueUnchecked = "0";
            // 
            // COLUMN_ID
            // 
            this.COLUMN_ID.Caption = "代码";
            this.COLUMN_ID.FieldName = "COLUMN_ID";
            this.COLUMN_ID.Name = "COLUMN_ID";
            this.COLUMN_ID.OptionsColumn.AllowEdit = false;
            this.COLUMN_ID.Width = 151;
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem1,
            this.layoutControlItem3,
            this.layoutControlGroup2,
            this.splitterItem1});
            this.layoutControlGroup1.Name = "layoutControlGroup1";
            this.layoutControlGroup1.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.layoutControlGroup1.Size = new System.Drawing.Size(784, 469);
            this.layoutControlGroup1.TextVisible = false;
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.gridControl1;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(280, 227);
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            // 
            // layoutControlItem3
            // 
            this.layoutControlItem3.Control = this.vColumnsDetail;
            this.layoutControlItem3.Location = new System.Drawing.Point(285, 0);
            this.layoutControlItem3.Name = "layoutControlItem3";
            this.layoutControlItem3.Size = new System.Drawing.Size(499, 227);
            this.layoutControlItem3.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem3.TextVisible = false;
            // 
            // layoutControlGroup2
            // 
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem4});
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 227);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.layoutControlGroup2.Size = new System.Drawing.Size(784, 242);
            this.layoutControlGroup2.Spacing = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.layoutControlGroup2.Text = "预览";
            // 
            // splitterItem1
            // 
            this.splitterItem1.AllowHotTrack = true;
            this.splitterItem1.Location = new System.Drawing.Point(280, 0);
            this.splitterItem1.Name = "splitterItem1";
            this.splitterItem1.Size = new System.Drawing.Size(5, 227);
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.gcDesign);
            this.panel1.Location = new System.Drawing.Point(3, 249);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(778, 217);
            this.panel1.TabIndex = 7;
            // 
            // layoutControlItem4
            // 
            this.layoutControlItem4.Control = this.panel1;
            this.layoutControlItem4.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem4.Name = "layoutControlItem4";
            this.layoutControlItem4.Size = new System.Drawing.Size(782, 221);
            this.layoutControlItem4.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem4.TextVisible = false;
            // 
            // frmEditField
            // 
            this.Appearance.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(239)))));
            this.Appearance.Options.UseBackColor = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(784, 561);
            this.Controls.Add(this.layoutControl1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Name = "frmEditField";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "绑定字段";
            this.Load += new System.EventHandler(this.frmEditField_Load);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).EndInit();
            this.layoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.vColumnsDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemLookUpEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemButtonEdit2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLupPreNames)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLupFormatType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcDesign)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvDesign)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lookUpReportNo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLupFields)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLupSummrayType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemButtonEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLupUnboundType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitterItem1)).EndInit();
            this.panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem4)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private LayoutControl layoutControl1;
        private LayoutControlGroup layoutControlGroup1;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;
        private DevExpress.XtraGrid.Columns.GridColumn SERIAL_NO;
        private DevExpress.XtraGrid.Columns.GridColumn COLUMN_NAME;
        private DevExpress.XtraGrid.Columns.GridColumn COLUMN_FIELD;
        private DevExpress.XtraGrid.Columns.GridColumn IF_GROUP;
        private DevExpress.XtraGrid.Columns.GridColumn GROUP_INDEX;
        private DevExpress.XtraGrid.Columns.GridColumn IF_SUMMARY;
        private DevExpress.XtraGrid.Columns.GridColumn SUMMARY_TYPE;
        private DevExpress.XtraGrid.Columns.GridColumn COLUMN_WIDTH;
        private DevExpress.XtraGrid.Columns.GridColumn COLUMN_ID;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraBars.Bar bar3;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtnAdd1;
        private DevExpress.XtraBars.BarButtonItem barBtnDelete1;
        private DevExpress.XtraBars.BarButtonItem barBtnSave1;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repLupFields;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit repositoryItemCheckEdit1;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repLupSummrayType;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repositoryItemSpinEdit1;
        private DevExpress.XtraGrid.GridControl gcDesign;
        private DevExpress.XtraGrid.Views.Grid.GridView gvDesign;
        private LayoutControlGroup layoutControlGroup2;
        private DevExpress.XtraGrid.Columns.GridColumn UNBOUND;
        private DevExpress.XtraGrid.Columns.GridColumn EXPRESSION;
        private DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit repositoryItemButtonEdit1;
        private DevExpress.XtraGrid.Columns.GridColumn UNBOUND_TYPE;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repLupUnboundType;
        private DevExpress.XtraGrid.Columns.GridColumn VISIBLE;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit repositoryItemCheckEdit2;
        private DevExpress.XtraVerticalGrid.VGridControl vColumnsDetail;
        private LayoutControlItem layoutControlItem3;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit repositoryItemCheckEdit3;
        private SplitterItem splitterItem1;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repositoryItemLookUpEdit1;
        private DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit repositoryItemButtonEdit2;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repLupPreNames;
        private DevExpress.XtraVerticalGrid.Rows.CategoryRow baseSet;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rSerialNo;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rColumnName;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rColumnField;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rColumnWidth;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rVisible;
        private DevExpress.XtraVerticalGrid.Rows.CategoryRow summarySet;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rIfSummary;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rSummaryType;
        private DevExpress.XtraVerticalGrid.Rows.CategoryRow unBoundSet;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rUnbound;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rExpression;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rUnboundType;
        private DevExpress.XtraVerticalGrid.Rows.CategoryRow formatRuleSet;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rFormatRule;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rRuleExpression;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rPreDefiendName;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rColumnID;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rSummaryDisplay;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rColumnFormatString;
        private DevExpress.XtraVerticalGrid.Rows.EditorRow rColumnFormatType;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repLupFormatType;
        private DevExpress.XtraBars.BarEditItem barReportNo;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit lookUpReportNo;
        private DevExpress.XtraBars.BarLargeButtonItem barBtnAdd;
        private DevExpress.XtraBars.BarLargeButtonItem barBtnDelete;
        private DevExpress.XtraBars.BarLargeButtonItem barBtnSave;
        private DevExpress.XtraBars.BarLargeButtonItem barLargeButtonItem1;
        private System.Windows.Forms.Panel panel1;
        private LayoutControlItem layoutControlItem4;
    }
}