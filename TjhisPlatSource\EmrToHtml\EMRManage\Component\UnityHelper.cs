﻿/*------------------------------------------------------*/
//Copyright (C) 2010 
//版权所有.
//文件名：UnityHelper.cs
//文件描述：通过Unity实组装对象泛型类。
//创建标识：Hyey.wl 20101122
//修改标识：
//修改描述：
/*------------------------------------------------------*/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Configuration;

using Microsoft.Practices.Unity;
using Microsoft.Practices.Unity.Configuration;

namespace Utility
{
    /// <summary>
    /// 通过Unity实组装对象泛型
    /// </summary>
    public static class UnityHelper
    {
         /// <summary>
        /// 通过Unity实组装对象泛型方法
         /// </summary>
         /// <typeparam name="T"></typeparam>
        /// <param name="containername">容器名</param>
         /// <param name="typename">类型名</param>
         /// <returns></returns>
        public static T UnityToT<T>(string containername,string typename)
        {
            IUnityContainer container = CreateContainer(containername);
            T model = container.Resolve<T>(typename);
            return model;
        }

        /// <summary>
        /// 通过Unity实组装对象泛型方法
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="containername">容器名</param>
        /// <returns></returns>
        public static T UnityToT<T>(string containername)
        {
            IUnityContainer container = CreateContainer(containername);
            T model = container.Resolve<T>(typeof(T).Name);
            return model;
        }

        /// <summary>
        /// DAL层 容器装载装用方法
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static T UnityToT<T>()
        {
            string containername = typeof(T).Name;
            IUnityContainer container = CreateContainer(containername);
            T model = container.Resolve<T>(typeof(T).Name);
            return model;
        }

   
        /// <summary>
        /// 通过配置创建UnityContainer
        /// </summary>
        /// <returns></returns>
        public static IUnityContainer CreateContainer(string containername)
        {
            /*
            IUnityContainer container = new UnityContainer();
           
            
            //ExeConfigurationFileMap file = new ExeConfigurationFileMap();
            //file.ExeConfigFilename = Environment.CurrentDirectory + "//App.config";//配置文件路径
            //Configuration config = ConfigurationManager.OpenMappedExeConfiguration(file, ConfigurationUserLevel.None);

            UnityConfigurationSection section = (UnityConfigurationSection)System.Configuration.ConfigurationManager.GetSection("unity");

            //UnityConfigurationSection section = (UnityConfigurationSection)config.GetSection("unity");
            try
            {
                section.Containers[containername].Configure(container);
            }
            catch(Exception e)
            {
               
               
            }
            return container;
             * */
            //ExeConfigurationFileMap file = new ExeConfigurationFileMap();
            //file.ExeConfigFilename = Environment.CurrentDirectory + "//App.config";//配置文件路径
            //Configuration config = ConfigurationManager.OpenMappedExeConfiguration(file, ConfigurationUserLevel.None);

            IUnityContainer container = new UnityContainer();
            UnityConfigurationSection section = (UnityConfigurationSection)
                System.Configuration.ConfigurationManager.GetSection("unity");
            section.Containers[containername].Configure(container);
            return container;
        }


        #region
        ///// <summary>
        ///// 通过Unity实组装对象泛型方法
        ///// </summary>
        ///// <typeparam name="T"></typeparam>
        ///// <param name="entity"></param>
        ///// <returns></returns>
        //public static T UnityToT<T>(string containername)
        //{
        //    IUnityContainer container = CreateContainer(containername);
        //    T model = container.Resolve<T>();
        //    return model;
        //}

        //public static IUnityContainer CreateContainer(string containername)
        //{
        //IUnityContainer container = new UnityContainer();


        //UnityConfigurationSection section = (UnityConfigurationSection)System.Configuration.ConfigurationManager..GetSection("unity");
        //Environment.CurrentDirectory
        /*
        #if DEBUG
                    string applicationName =
                        Environment.GetCommandLineArgs()[0];
        #else
                   string applicationName =
                  Environment.GetCommandLineArgs()[0]+ ".exe";
        #endif

        string exePath = System.IO.Path.Combine(
            Environment.CurrentDirectory, applicationName);


        System.Configuration.Configuration config =
        System.Configuration.ConfigurationManager.OpenExeConfiguration(exePath);
        */
        //string val = config.AppSettings.Settings["Key"].Value;

        //String ConString = System.Configuration.ConfigurationSettings.AppSettings["TrustPayConnectMethod"];
        //try
        // {
        //  section.Containers[containername].Configure(container);
        //}
        //catch (Exception e)
        //{


        // }
        //  return container;
        //}


        #endregion
    }
}
