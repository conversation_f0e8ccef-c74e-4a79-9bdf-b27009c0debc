﻿/*********************************************
* 文 件 名：Cs01FtpClient
* 类 名 称：Cs01FtpClient
* 功能说明：Ftp客户端类
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：吴新才
* 创建时间：2018-05-22 15:38:26
* 版 本 号：*******
* 修改时间：2020-02-18 15:38:26
* 修 改 人：刘成刚
* CLR 版本：4.0.30319.42000
/*********************************************/

using System.Data;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Text;
using System;

namespace PlatCommon.Base01
{
    /// <summary>
    /// FTP客户端参数
    /// </summary>
    public enum Cs01FtpClientParameter
    {
        /// <summary>
        /// 主机名
        /// </summary>
        Host = 0,
        /// <summary>
        /// 用户名
        /// </summary>
        UserId = 1,
        /// <summary>
        /// 用户密码
        /// </summary>
        Password = 2,
        /// <summary>
        /// 端口号
        /// </summary>
        Port = 3
    }

    /// <summary>
    /// FTP客户端
    /// </summary>
    public class Cs01FtpClient
    {
        #region 构造函数
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="strHost">主机名称</param>
        /// <param name="strUserId">用户名</param>
        /// <param name="strPassword">密码</param>
        /// <param name="iPort">端口号</param>
        public Cs01FtpClient(string strHost, string strUserId, string strPassword, int iPort = 21)
        {
            this._strUserId = strUserId;
            this._strPassword = strPassword;
            this._iPort = iPort;
            this._strHost = getMyHost(strHost);
        }

        /// <summary>
        /// 从数据表中读参数
        /// </summary>
        /// <param name="dtParameter">参数数据表</param>
        /// <param name="p">参数对象</param>
        /// <returns></returns>
        private string getDataTableValue(DataTable dtParameter, Cs01FtpClientParameter p)
        {
            string code = "";
            switch (p)
            {
                case Cs01FtpClientParameter.Host:
                    code = "01";
                    break;
                case Cs01FtpClientParameter.UserId:
                    code = "02";
                    break;
                case Cs01FtpClientParameter.Password:
                    code = "03";
                    break;
                case Cs01FtpClientParameter.Port:
                    code = "04";
                    break;
                default:
                    break;
            }
            DataRow[] drs = dtParameter.Select($"CODE='{code}'");
            if (drs.Length == 0) return "";
            return drs[0]["VALUE"].ToString();
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dtParameter">参数数据表</param>
        public Cs01FtpClient(DataTable dtParameter)
        {
            string host = getDataTableValue(dtParameter, Cs01FtpClientParameter.Host);
            string userId = getDataTableValue(dtParameter, Cs01FtpClientParameter.UserId);
            string password = getDataTableValue(dtParameter, Cs01FtpClientParameter.Password);
            int port = Cs01Functions.CInt(getDataTableValue(dtParameter, Cs01FtpClientParameter.Port));

            this._strUserId = userId;
            this._strPassword = password;
            this._iPort = port;
            this._strHost = getMyHost(host);
        }

        /// <summary>
        /// 生成主机串
        /// </summary>
        /// <param name="strFtpAddr">FTP服务器地址</param>
        /// <returns></returns>
        private string getMyHost(string strFtpAddr)
        {
            string host = "";
            if (strFtpAddr.ToLower().StartsWith("ftp://"))
            {
                host = strFtpAddr.TrimEnd('/');
            }
            else
            {
                host = "ftp://" + strFtpAddr.TrimEnd('/');
            }

            return host + ":" + _iPort.ToString();
        }

        /// <summary>
        /// 创建FTP工具
        /// </summary>
        /// <param name="strHost">主机名称</param>
        /// <param name="strUserId">用户名</param>
        /// <param name="strPassword">密码</param>
        /// <param name="iPort">端口</param>
        /// <param name="enableSsl">允许Ssl</param>
        /// <param name="proxy">代理</param>
        /// <param name="useBinary">允许二进制</param>
        /// <param name="usePassive">允许被动模式</param>
        public Cs01FtpClient(string strHost, string strUserId, string strPassword, int iPort, IWebProxy proxy, bool enableSsl, bool useBinary, bool usePassive)
        {
            this._strUserId = strUserId;
            this._strPassword = strPassword;
            this._iPort = iPort;

            this.proxy = proxy;
            this.enableSsl = enableSsl;
            this.useBinary = useBinary;
            this.usePassive = usePassive;
            this._strHost = getMyHost(strHost);
        }
        #endregion 构造函数

        #region 变量
        #region 主机
        private string _strHost = string.Empty;
        /// <summary>
        /// 主机
        /// </summary>
        #endregion

        #region 登录用户名
        private string _strUserId = string.Empty;
        #endregion

        #region 密码
        private string _strPassword = string.Empty;
        #endregion

        #region 端口
        private int _iPort = 21;
        #endregion

        #region 代理
        IWebProxy proxy = null;
        /// <summary>
        /// 代理
        /// </summary>
        #endregion

        #region 是否允许Ssl
        private bool enableSsl = false;
        /// <summary>
        /// EnableSsl 
        /// </summary>
        public bool EnableSsl
        {
            get
            {
                return enableSsl;
            }
        }
        #endregion

        #region 使用被动模式
        private bool usePassive = true;
        /// <summary>
        /// 被动模式
        /// </summary>
        public bool UsePassive
        {
            get
            {
                return usePassive;
            }
            set
            {
                this.usePassive = value;
            }
        }
        #endregion

        #region 二进制方式
        private bool useBinary = true;
        /// <summary>
        /// 二进制方式
        /// </summary>
        public bool UseBinary
        {
            get
            {
                return useBinary;
            }
            set
            {
                this.useBinary = value;
            }
        }
        #endregion

        #region 远端路径
        private string remotePath = "/";
        /// <summary>
        /// 远端路径
        /// <para>
        ///     返回FTP服务器上的当前路径(可以是 / 或 /a/../ 的形式)
        /// </para>
        /// </summary>
        public string RemotePath
        {
            get
            {
                return remotePath;
            }
            set
            {
                string result = "/";
                if (!string.IsNullOrEmpty(value) && value != "/")
                {
                    result = "/" + value.TrimStart('/').TrimEnd('/') + "/";
                }
                this.remotePath = result;
            }
        }
        #endregion
        #endregion 变量

        #region 判断能否连接到服务器
        /// <summary>
        /// 连接判断参数是否设置正确或者FTP服务器是否启用
        /// </summary>
        /// <returns></returns>
        public bool CanConnected()
        {
            bool isok = false;
            FtpWebRequest request;
            try
            {
                //根据uri创建FtpWebRequest对象
                request = (FtpWebRequest)FtpWebRequest.Create(new Uri(_strHost));

                //设置用户名和密码
                request.Credentials = new NetworkCredential(_strUserId, _strPassword);
                request.Method = WebRequestMethods.Ftp.ListDirectory;
                request.UseBinary = true;

                FtpWebResponse response = (FtpWebResponse)request.GetResponse();

                isok = true;
            }
            catch (Exception ex)
            {
                isok = false;
                throw (ex);
            }
            return isok;
        }
        #endregion

        #region 创建一个FTP连接
        /// <summary>
        /// 创建一个FTP请求
        /// </summary>
        /// <param name="url">请求地址</param>
        /// <param name="method">请求方法</param>
        /// <returns>FTP请求</returns>
        private FtpWebRequest CreateRequest(string url, string method)
        {
            //建立连接
            FtpWebRequest request = (FtpWebRequest)FtpWebRequest.Create(new Uri(url));
            request.Credentials = new NetworkCredential(this._strUserId, this._strPassword);
            request.Proxy = this.proxy;
            request.KeepAlive = false;//命令执行完毕之后关闭连接
            request.UseBinary = useBinary;
            request.UsePassive = usePassive;
            request.EnableSsl = enableSsl;
            request.Method = method;
            return request;

        }
        #endregion

        #region 上传一个文件到远端路径下
        /// <summary>
        /// 把文件上传到FTP服务器的RemotePath下
        /// </summary>
        /// <param name="localFile">本地文件信息</param>
        /// <param name="remoteFileName">要保存到的文件名称包含扩展名</param>
        public bool Upload(FileInfo localFile, string remoteFileName)
        {
            bool result = false;
            if (localFile.Exists)
            {
                string url = "";
                if (remoteFileName.StartsWith("/"))
                {
                    url = _strHost + remoteFileName;
                }
                else
                {
                    url = _strHost + RemotePath + remoteFileName;
                }

                FtpWebRequest request = CreateRequest(url, WebRequestMethods.Ftp.UploadFile);

                //上传数据
                using (Stream rs = request.GetRequestStream())
                using (FileStream fs = localFile.OpenRead())
                {
                    byte[] buffer = new byte[4096];//4K
                    int count = fs.Read(buffer, 0, buffer.Length);//每次从流中读4个字节再写入缓冲区
                    while (count > 0)
                    {
                        rs.Write(buffer, 0, count);
                        count = fs.Read(buffer, 0, buffer.Length);
                    }
                    fs.Close();
                    result = true;
                }
                return result;
            }
            throw new Exception(string.Format("本地文件不存在,文件路径:{0}", localFile.FullName));
        }
        #endregion

        #region 上传文件内容到远端路径文件中
        /// <summary>
        /// 把文件内容上传到FTP服务器的RemotePath下的文件中
        /// </summary>
        /// <param name="fileText">文件内容</param>
        /// <param name="remoteFileName">要保存到的文件名称包含扩展名</param>
        public bool Upload(string fileText, string remoteFileName)
        {
            string url = _strHost + RemotePath + remoteFileName;
            FtpWebRequest request = CreateRequest(url, WebRequestMethods.Ftp.UploadFile);

            //上传数据
            Stream rs = request.GetRequestStream();
            byte[] buffer = Encoding.UTF8.GetBytes(fileText);
            rs.Write(buffer, 0, buffer.Length);
            rs.Close();
            rs.Dispose();
            return true;
        }
        #endregion

        #region 从FTP服务器上下载文件
        /// <summary>
        /// 从当前目录下下载文件
        /// 如果本地文件存在,则从本地文件结束的位置开始下载.
        /// </summary>
        /// <param name="strServerFileName">服务器上的文件名称</param>
        /// <param name="strLocalFileName">本地文件名称</param>
        /// <returns>返回一个值,指示是否下载成功</returns>
        public bool Download(string strServerFileName, string strLocalFileName)
        {
            bool result = false;
            using (FileStream fs = new FileStream(strLocalFileName, FileMode.OpenOrCreate)) //创建或打开本地文件
            {
                //建立连接
                string url = "";
                if (strServerFileName.StartsWith("/"))
                {
                    url = _strHost + strServerFileName;
                }
                else
                {
                    url = _strHost + RemotePath + strServerFileName;
                }

                FtpWebRequest request = CreateRequest(url, WebRequestMethods.Ftp.DownloadFile);
                request.ContentOffset = fs.Length;
                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                {
                    fs.Position = fs.Length;
                    byte[] buffer = new byte[4096];//4K
                    int count = response.GetResponseStream().Read(buffer, 0, buffer.Length);
                    while (count > 0)
                    {
                        fs.Write(buffer, 0, count);
                        count = response.GetResponseStream().Read(buffer, 0, buffer.Length);
                    }
                    response.GetResponseStream().Close();
                }
                result = true;
            }
            return result;
        }
        #endregion

        #region 重命名FTP服务器上的文件
        /// <summary>
        /// 文件更名
        /// </summary>
        /// <param name="strOldFileName">原文件名</param>
        /// <param name="strNewFileName">新文件名</param>
        /// <returns>返回一个值,指示更名是否成功</returns>
        public bool Rename(string strOldFileName, string strNewFileName)
        {
            bool result = false;
            //建立连接
            string url = "";
            if (strOldFileName.StartsWith("/"))
            {
                url = _strHost + strOldFileName;
            }
            else
            {
                url = _strHost + RemotePath + strOldFileName;
            }

            FtpWebRequest request = CreateRequest(url, WebRequestMethods.Ftp.Rename);
            request.RenameTo = strNewFileName;
            using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
            {
                result = true;
            }
            return result;
        }
        #endregion

        #region 从当前目录下获取文件列表
        /// <summary>
        /// 获取当前目录下文件列表
        /// </summary>
        /// <returns></returns>
        public List<string> GetFileList()
        {
            List<string> result = new List<string>();
            //建立连接
            string url = _strHost + RemotePath;
            FtpWebRequest request = CreateRequest(url, WebRequestMethods.Ftp.ListDirectory);
            using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
            {
                StreamReader reader = new StreamReader(response.GetResponseStream(), System.Text.Encoding.UTF8);//中文文件名
                string line = reader.ReadLine();
                while (line != null)
                {
                    result.Add(line);
                    line = reader.ReadLine();
                }
            }
            return result;
        }
        #endregion

        #region 从FTP服务器上获取文件夹列表
        /// <summary>
        /// 获取详细列表
        /// </summary>
        /// <returns></returns>
        public List<string> GetFileDetails()
        {
            List<string> result = new List<string>();
            //建立连接
            string url = _strHost + RemotePath;
            FtpWebRequest request = CreateRequest(url, WebRequestMethods.Ftp.ListDirectoryDetails);
            using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
            {
                StreamReader reader = new StreamReader(response.GetResponseStream());//中文文件名
                string line = reader.ReadLine();
                while (line != null)
                {
                    result.Add(line);
                    line = reader.ReadLine();
                }
            }
            return result;
        }
        #endregion

        #region 从FTP服务器上删除文件

        /// <summary>
        /// 删除FTP服务器上的文件
        /// </summary>
        /// <param name="strFileName">文件名称</param>
        /// <returns>返回一个值,指示是否删除成功</returns>
        public bool DeleteFile(string strFileName)
        {
            bool result = false;
            //建立连接
            string url = "";
            if (strFileName.StartsWith("/"))
            {
                url = _strHost + strFileName;
            }
            else
            {
                url = _strHost + RemotePath + strFileName;
            }

            FtpWebRequest request = CreateRequest(url, WebRequestMethods.Ftp.DeleteFile);
            using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
            {
                result = true;
            }

            return result;
        }
        #endregion

        #region 在FTP服务器上创建目录
        /// <summary>
        /// 在当前目录下创建文件夹
        /// </summary>
        /// <param name="strDirName">文件夹名称</param>
        /// <returns>返回一个值,指示是否创建成功</returns>
        public bool MakeDirectory(string strDirName)
        {
            bool result = false;
            //建立连接
            string url = "";
            if (strDirName.StartsWith("/"))
            {
                url = _strHost + strDirName;
            }
            else
            {
                url = _strHost + RemotePath + strDirName;
            }

            FtpWebRequest request = CreateRequest(url, WebRequestMethods.Ftp.MakeDirectory);
            request.Credentials = new NetworkCredential(_strUserId, _strPassword);
            using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
            {
                result = true;
            }
            return result;
        }
        #endregion

        #region 从FTP服务器上删除目录
        /// <summary>
        /// 删除文件夹（只能是空文件夹）
        /// </summary>
        /// <param name="strDirName">文件夹名称</param>
        /// <returns>返回一个值,指示是否删除成功</returns>
        public bool DeleteDirectory(string strDirName)
        {
            bool result = false;
            //建立连接
            string url;
            if (strDirName.StartsWith("/"))
            {
                url = _strHost + strDirName;
            }
            else
            {
                url = _strHost + RemotePath + strDirName;
            }
            FtpWebRequest request = CreateRequest(url, WebRequestMethods.Ftp.RemoveDirectory);
            using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
            {
                result = true;
            }
            return result;
        }

        /// <summary>
        /// 删除文件夹（包括文件与子文件夹）
        /// </summary>
        /// <param name="strDirName">文件夹名称</param>
        /// <returns></returns>
        public bool DeletetTreeDirectory(string strDirName)
        {
            string _remotePath = remotePath;
            strDirName = strDirName.Trim();
            int i = strDirName.LastIndexOf("/");
            string subDirName;
            if (i == 0)
            {
                _remotePath = "/";
                RemotePath = strDirName;
                subDirName = "";
            }
            else
            {
                if (strDirName.StartsWith("/"))
                {
                    RemotePath = strDirName;
                }
                else
                {
                    RemotePath = remotePath + strDirName;
                }
                subDirName = strDirName.Substring(i + 1);
            }


            Dictionary<string, int> dic = new Dictionary<string, int>();
            List<string> listDir = GetFileDetails();
            foreach (string line in listDir)
            {
                string dirName2 = ParseDirectoryName(line);
                if (string.IsNullOrEmpty(dirName2)) continue;
                dic[dirName2] = 1;
            }

            List<string> listfile = GetFileList();
            foreach (string fileName in listfile)
            {
                if (string.IsNullOrEmpty(fileName)) continue;
                if (!dic.ContainsKey(fileName))
                {
                    DeleteFile(fileName);
                }
            }

            foreach (KeyValuePair<string, int> kv in dic)
            {
                string dirName2 = kv.Key;
                if (string.IsNullOrEmpty(dirName2)) continue;
                DeletetTreeDirectory($"{remotePath}{dirName2}");
            }

            remotePath = _remotePath;
            if (!string.IsNullOrEmpty(subDirName))
            {
                DeleteDirectory(subDirName);
            }
            return true;
        }

        #endregion

        #region 从FTP服务器上获取文件大小
        /// <summary>
        /// 获取文件大小
        /// </summary>
        /// <param name="strFileName">文件名称</param>
        /// <returns></returns>
        public long GetFileSize(string strFileName)
        {
            long result = 0;
            //建立连接
            string url = "";
            if (strFileName.StartsWith("/"))
            {
                url = _strHost + strFileName;
            }
            else
            {
                url = _strHost + RemotePath + strFileName;
            }

            FtpWebRequest request = CreateRequest(url, WebRequestMethods.Ftp.GetFileSize);
            using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
            {
                result = response.ContentLength;
            }

            return result;
        }
        #endregion

        #region 给FTP服务器上的文件追加内容
        /// <summary>
        /// 给FTP服务器上的文件追加内容
        /// </summary>
        /// <param name="localFile">本地文件</param>
        /// <param name="strRemoteFileName">FTP服务器上的文件</param>
        /// <returns>返回一个值,指示是否追加成功</returns>
        public bool Append(FileInfo localFile, string strRemoteFileName)
        {
            if (localFile.Exists)
            {
                using (FileStream fs = new FileStream(localFile.FullName, FileMode.Open))
                {
                    return Append(fs, strRemoteFileName);
                }
            }
            throw new Exception(string.Format("本地文件不存在,文件路径:{0}", localFile.FullName));
        }

        /// <summary>
        /// 给FTP服务器上的文件追加内容
        /// </summary>
        /// <param name="stream">数据流(可通过设置偏移来实现从特定位置开始上传)</param>
        /// <param name="strRemoteFileName">FTP服务器上的文件</param>
        /// <returns>返回一个值,指示是否追加成功</returns>
        public bool Append(Stream stream, string strRemoteFileName)
        {
            bool result = false;
            if (stream != null && stream.CanRead)
            {
                //建立连接
                string url = "";
                if (strRemoteFileName.StartsWith("/"))
                {
                    url = _strHost + strRemoteFileName;
                }
                else
                {
                    url = _strHost + RemotePath + strRemoteFileName;
                }

                FtpWebRequest request = CreateRequest(url, WebRequestMethods.Ftp.AppendFile);
                using (Stream rs = request.GetRequestStream())
                {
                    //上传数据
                    byte[] buffer = new byte[4096];//4K
                    int count = stream.Read(buffer, 0, buffer.Length);
                    while (count > 0)
                    {
                        rs.Write(buffer, 0, count);
                        count = stream.Read(buffer, 0, buffer.Length);
                    }
                    result = true;
                }
            }
            return result;
        }
        #endregion

        #region 获取FTP服务器上的当前路径
        /// <summary>
        /// 获取FTP服务器上的当前路径
        /// </summary>
        public string CurrentDirectory
        {
            get
            {
                string result = string.Empty;
                string url = _strHost + RemotePath;
                FtpWebRequest request = CreateRequest(url, WebRequestMethods.Ftp.PrintWorkingDirectory);
                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                {
                    string temp = response.StatusDescription;
                    int start = temp.IndexOf('"') + 1;
                    int end = temp.LastIndexOf('"');
                    if (end >= start)
                    {
                        result = temp.Substring(start, end - start);
                    }
                }
                return result;

            }
        }
        #endregion

        #region 检查FTP服务器当前路径上是否存在某个文件
        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        /// <param name="strFileName">要检查的文件名</param>
        /// <returns>返回一个值,指示要检查的文件是否存在</returns>
        public bool CheckFileExist(string strFileName)
        {
            bool result = false;
            if (strFileName != null && strFileName.Trim().Length > 0)
            {
                strFileName = strFileName.Trim();
                List<string> files = GetFileList();
                if (files != null && files.Count > 0)
                {
                    foreach (string file in files)
                    {
                        if (file.ToLower() == strFileName.ToLower())
                        {
                            result = true;
                            break;
                        }
                    }
                }
            }
            return result;
        }
        #endregion

        #region 判断文件夹是否存在

        /// <summary>
        /// 判断ftp上的文件目录是否存在
        /// </summary>
        /// <param name="path">文件目录</param>
        /// <returns></returns>
        public bool RemoteFtpDirExists(string path)
        {
            path = _strHost + path;
            FtpWebRequest reqFtp = (FtpWebRequest)FtpWebRequest.Create(new Uri(path));
            reqFtp.UseBinary = true;
            reqFtp.Credentials = new NetworkCredential(_strUserId, _strPassword);
            reqFtp.Method = WebRequestMethods.Ftp.ListDirectory;
            FtpWebResponse resFtp = null;
            try
            {
                resFtp = (FtpWebResponse)reqFtp.GetResponse();
                FtpStatusCode code = resFtp.StatusCode;//OpeningData
                resFtp.Close();
                return true;
            }
            catch
            {
                if (resFtp != null)
                {
                    resFtp.Close();
                }
                return false;
            }
        }
        #endregion

        /// <summary>
        /// 判断文件夹是否存在
        /// </summary>
        /// <param name="strRemotePath"></param>
        /// <returns></returns>
        public bool _DirectoryExist(string strRemotePath)
        {
            int i = remotePath.LastIndexOf("/");
            string relativePath;
            string _remotePath = RemotePath;

            if (i == 0)
            {
                RemotePath = "/";
                relativePath = remotePath.Substring(1);
            }
            else
            {
                if (remotePath.StartsWith("/"))
                {
                    RemotePath = remotePath.Substring(0, i);
                }
                else
                {
                    RemotePath = RemotePath + remotePath.Substring(0, i);
                }
                relativePath = remotePath.Substring(i + 1);
            }
            string URI = _strHost + RemotePath;

            bool isok = false;
            FtpWebRequest reqFTP = (FtpWebRequest)FtpWebRequest.Create(new Uri(URI));
            reqFTP.UseBinary = true;
            reqFTP.Credentials = new NetworkCredential(_strUserId, _strPassword);
            reqFTP.Method = WebRequestMethods.Ftp.ListDirectoryDetails;
            string e = "";
            StreamReader reader = null;
            FtpWebResponse response = null;
            try
            {
                response = (FtpWebResponse)reqFTP.GetResponse();
                reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8);
                while (!reader.EndOfStream)
                {
                    string line = reader.ReadLine();
                    string dirname = ParseDirectoryName(line);
                    if (!string.IsNullOrEmpty(dirname))
                    {
                        if (string.Compare(dirname, relativePath, true) == 0)
                        {
                            isok = true;
                            break;
                        }
                    }
                }
                if (!isok) e = "目录不存在";
            }
            catch (Exception ex)
            {
                e = ex.Message;
            }
            finally
            {
                if (reader != null) reader.Close();
                if (response != null) response.Close();
            }

            RemotePath = _remotePath;
            return isok;
        }

        /// <summary>
        /// 解析文件夹名称
        /// </summary>
        /// <param name="line">FTP数据行</param>
        /// <returns></returns>
        private string ParseDirectoryName(string line)
        {
            string dirname = "";
            int k = line.Trim().IndexOf("<DIR>");
            if (k >= 0)
            {
                dirname = line.Substring(k + 5).Trim();
            }
            return dirname;
        }

        /// <summary>
        /// 解析文件名称
        /// </summary>
        /// <param name="line">FTP数据行</param>
        /// <returns></returns>
        private string ParseFileName(string line)
        {
            string filename = "";
            int k = line.Trim().IndexOf("<File>");
            if (k >= 0)
            {
                filename = line.Substring(k + 6).Trim();
            }
            return filename;
        }
    }

}
