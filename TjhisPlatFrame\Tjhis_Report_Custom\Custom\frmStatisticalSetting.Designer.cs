﻿namespace Tjhis.Report.Custom.Custom
{
    partial class frmStatisticalSetting
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmStatisticalSetting));
            this.layoutControl1 = new DevExpress.XtraLayout.LayoutControl();
            this.gcReportParam = new DevExpress.XtraGrid.GridControl();
            this.dgvReportParam = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gc_SerialNo = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_PARAM_NAME = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_EDIT_TYPE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.LUE_EditType = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.gc_CAPTION = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_DISPLAY_MEMBER = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_VALUE_MEMBER = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_SOURCE_TYPE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.LUE_SourceType = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.gc_NEXT_PARAM_NAME = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_DEFAULT_VALUE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gc_CONTROL_WIDTH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcTemplet = new DevExpress.XtraGrid.GridControl();
            this.dgvTemple = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcSe = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemLookUpEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.gcId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcClass = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repLupTempletClass = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.gcName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcIsTable = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemCheckEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.gcShowMenu = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcRe = new DevExpress.XtraGrid.Columns.GridColumn();
            this.txtSql = new DevExpress.XtraEditors.MemoEdit();
            this.btnTest = new DevExpress.XtraEditors.SimpleButton();
            this.btnSaveSql = new DevExpress.XtraEditors.SimpleButton();
            this.sBtnTempClass = new DevExpress.XtraEditors.SimpleButton();
            this.cbPublic = new DevExpress.XtraEditors.CheckEdit();
            this.cbPrivate = new DevExpress.XtraEditors.CheckEdit();
            this.cbSet = new DevExpress.XtraEditors.CheckEdit();
            this.txtRole = new DevExpress.XtraEditors.ButtonEdit();
            this.sbtnReportParam = new DevExpress.XtraEditors.SimpleButton();
            this.sbtnRparamSave = new DevExpress.XtraEditors.SimpleButton();
            this.sbtnRparamAdd = new DevExpress.XtraEditors.SimpleButton();
            this.sbtnOpenParamDict = new DevExpress.XtraEditors.SimpleButton();
            this.sbtnRparamDel = new DevExpress.XtraEditors.SimpleButton();
            this.sbtnRparamEdit = new DevExpress.XtraEditors.SimpleButton();
            this.btnBindingField = new DevExpress.XtraEditors.SimpleButton();
            this.sBtnExportXml = new DevExpress.XtraEditors.SimpleButton();
            this.btnAdd1 = new DevExpress.XtraEditors.SimpleButton();
            this.btnDelete1 = new DevExpress.XtraEditors.SimpleButton();
            this.btnSave1 = new DevExpress.XtraEditors.SimpleButton();
            this.sbtnAddTo1 = new DevExpress.XtraEditors.SimpleButton();
            this.btnCopyCreate1 = new DevExpress.XtraEditors.SimpleButton();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlItem23 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem24 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem25 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem26 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem27 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem6 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem12 = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup4 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem5 = new DevExpress.XtraLayout.LayoutControlItem();
            this.btnTestControl = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem7 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem20 = new DevExpress.XtraLayout.LayoutControlItem();
            this.groupReportParam = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem16 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem15 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem17 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem14 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem19 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem18 = new DevExpress.XtraLayout.LayoutControlItem();
            this.splitterItem2 = new DevExpress.XtraLayout.SplitterItem();
            this.groupReportRole = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem10 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem11 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem9 = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem8 = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem10 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem7 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem9 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlItem22 = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).BeginInit();
            this.layoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcReportParam)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvReportParam)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.LUE_EditType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.LUE_SourceType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcTemplet)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvTemple)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemLookUpEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLupTempletClass)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSql.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbPublic.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbPrivate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbSet.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRole.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem23)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem24)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem25)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem26)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem27)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.btnTestControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupReportParam)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem18)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitterItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupReportRole)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem22)).BeginInit();
            this.SuspendLayout();
            // 
            // layoutControl1
            // 
            this.layoutControl1.Controls.Add(this.gcReportParam);
            this.layoutControl1.Controls.Add(this.gcTemplet);
            this.layoutControl1.Controls.Add(this.txtSql);
            this.layoutControl1.Controls.Add(this.btnTest);
            this.layoutControl1.Controls.Add(this.btnSaveSql);
            this.layoutControl1.Controls.Add(this.sBtnTempClass);
            this.layoutControl1.Controls.Add(this.cbPublic);
            this.layoutControl1.Controls.Add(this.cbPrivate);
            this.layoutControl1.Controls.Add(this.cbSet);
            this.layoutControl1.Controls.Add(this.txtRole);
            this.layoutControl1.Controls.Add(this.sbtnReportParam);
            this.layoutControl1.Controls.Add(this.sbtnRparamSave);
            this.layoutControl1.Controls.Add(this.sbtnRparamAdd);
            this.layoutControl1.Controls.Add(this.sbtnOpenParamDict);
            this.layoutControl1.Controls.Add(this.sbtnRparamDel);
            this.layoutControl1.Controls.Add(this.sbtnRparamEdit);
            this.layoutControl1.Controls.Add(this.btnBindingField);
            this.layoutControl1.Controls.Add(this.sBtnExportXml);
            this.layoutControl1.Controls.Add(this.btnAdd1);
            this.layoutControl1.Controls.Add(this.btnDelete1);
            this.layoutControl1.Controls.Add(this.btnSave1);
            this.layoutControl1.Controls.Add(this.sbtnAddTo1);
            this.layoutControl1.Controls.Add(this.btnCopyCreate1);
            this.layoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl1.Location = new System.Drawing.Point(0, 0);
            this.layoutControl1.Margin = new System.Windows.Forms.Padding(0);
            this.layoutControl1.Name = "layoutControl1";
            this.layoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(137, 101, 865, 627);
            this.layoutControl1.Root = this.layoutControlGroup1;
            this.layoutControl1.Size = new System.Drawing.Size(1050, 617);
            this.layoutControl1.TabIndex = 0;
            this.layoutControl1.Text = "layoutControl1";
            // 
            // gcReportParam
            // 
            this.gcReportParam.Location = new System.Drawing.Point(580, 135);
            this.gcReportParam.MainView = this.dgvReportParam;
            this.gcReportParam.Name = "gcReportParam";
            this.gcReportParam.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.LUE_EditType,
            this.LUE_SourceType});
            this.gcReportParam.Size = new System.Drawing.Size(358, 126);
            this.gcReportParam.TabIndex = 23;
            this.gcReportParam.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.dgvReportParam});
            // 
            // dgvReportParam
            // 
            this.dgvReportParam.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gc_SerialNo,
            this.gc_PARAM_NAME,
            this.gc_EDIT_TYPE,
            this.gc_CAPTION,
            this.gc_DISPLAY_MEMBER,
            this.gc_VALUE_MEMBER,
            this.gc_SOURCE_TYPE,
            this.gc_NEXT_PARAM_NAME,
            this.gc_DEFAULT_VALUE,
            this.gc_CONTROL_WIDTH});
            this.dgvReportParam.GridControl = this.gcReportParam;
            this.dgvReportParam.Name = "dgvReportParam";
            this.dgvReportParam.OptionsCustomization.AllowFilter = false;
            this.dgvReportParam.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CellSelect;
            this.dgvReportParam.OptionsView.ColumnAutoWidth = false;
            this.dgvReportParam.OptionsView.ShowGroupPanel = false;
            this.dgvReportParam.RowClick += new DevExpress.XtraGrid.Views.Grid.RowClickEventHandler(this.dgvReportParam_RowClick);
            this.dgvReportParam.InitNewRow += new DevExpress.XtraGrid.Views.Grid.InitNewRowEventHandler(this.dgvReportParam_InitNewRow);
            // 
            // gc_SerialNo
            // 
            this.gc_SerialNo.Caption = "显示序号";
            this.gc_SerialNo.FieldName = "SERIAL_NO";
            this.gc_SerialNo.MinWidth = 60;
            this.gc_SerialNo.Name = "gc_SerialNo";
            this.gc_SerialNo.OptionsColumn.AllowEdit = false;
            this.gc_SerialNo.Visible = true;
            this.gc_SerialNo.VisibleIndex = 0;
            this.gc_SerialNo.Width = 60;
            // 
            // gc_PARAM_NAME
            // 
            this.gc_PARAM_NAME.Caption = "参数";
            this.gc_PARAM_NAME.FieldName = "PARAM_NAME";
            this.gc_PARAM_NAME.MinWidth = 76;
            this.gc_PARAM_NAME.Name = "gc_PARAM_NAME";
            this.gc_PARAM_NAME.OptionsColumn.ReadOnly = true;
            this.gc_PARAM_NAME.Visible = true;
            this.gc_PARAM_NAME.VisibleIndex = 1;
            this.gc_PARAM_NAME.Width = 103;
            // 
            // gc_EDIT_TYPE
            // 
            this.gc_EDIT_TYPE.Caption = "参数控件类型";
            this.gc_EDIT_TYPE.ColumnEdit = this.LUE_EditType;
            this.gc_EDIT_TYPE.FieldName = "EDIT_TYPE";
            this.gc_EDIT_TYPE.MinWidth = 100;
            this.gc_EDIT_TYPE.Name = "gc_EDIT_TYPE";
            this.gc_EDIT_TYPE.OptionsColumn.AllowEdit = false;
            this.gc_EDIT_TYPE.Visible = true;
            this.gc_EDIT_TYPE.VisibleIndex = 4;
            this.gc_EDIT_TYPE.Width = 100;
            // 
            // LUE_EditType
            // 
            this.LUE_EditType.AutoHeight = false;
            this.LUE_EditType.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_CODE", "编码"),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_NAME", "项目")});
            this.LUE_EditType.Name = "LUE_EditType";
            this.LUE_EditType.NullText = "";
            // 
            // gc_CAPTION
            // 
            this.gc_CAPTION.Caption = "标题名称";
            this.gc_CAPTION.FieldName = "CAPTION";
            this.gc_CAPTION.MinWidth = 120;
            this.gc_CAPTION.Name = "gc_CAPTION";
            this.gc_CAPTION.OptionsColumn.AllowEdit = false;
            this.gc_CAPTION.Visible = true;
            this.gc_CAPTION.VisibleIndex = 2;
            this.gc_CAPTION.Width = 120;
            // 
            // gc_DISPLAY_MEMBER
            // 
            this.gc_DISPLAY_MEMBER.Caption = "显示列";
            this.gc_DISPLAY_MEMBER.FieldName = "DISPLAY_MEMBER";
            this.gc_DISPLAY_MEMBER.MinWidth = 100;
            this.gc_DISPLAY_MEMBER.Name = "gc_DISPLAY_MEMBER";
            this.gc_DISPLAY_MEMBER.OptionsColumn.AllowEdit = false;
            this.gc_DISPLAY_MEMBER.Visible = true;
            this.gc_DISPLAY_MEMBER.VisibleIndex = 6;
            this.gc_DISPLAY_MEMBER.Width = 100;
            // 
            // gc_VALUE_MEMBER
            // 
            this.gc_VALUE_MEMBER.Caption = "数据列";
            this.gc_VALUE_MEMBER.FieldName = "VALUE_MEMBER";
            this.gc_VALUE_MEMBER.MinWidth = 100;
            this.gc_VALUE_MEMBER.Name = "gc_VALUE_MEMBER";
            this.gc_VALUE_MEMBER.OptionsColumn.AllowEdit = false;
            this.gc_VALUE_MEMBER.Visible = true;
            this.gc_VALUE_MEMBER.VisibleIndex = 7;
            this.gc_VALUE_MEMBER.Width = 100;
            // 
            // gc_SOURCE_TYPE
            // 
            this.gc_SOURCE_TYPE.Caption = "数据源类型";
            this.gc_SOURCE_TYPE.ColumnEdit = this.LUE_SourceType;
            this.gc_SOURCE_TYPE.FieldName = "SOURCE_TYPE";
            this.gc_SOURCE_TYPE.MinWidth = 100;
            this.gc_SOURCE_TYPE.Name = "gc_SOURCE_TYPE";
            this.gc_SOURCE_TYPE.OptionsColumn.AllowEdit = false;
            this.gc_SOURCE_TYPE.Visible = true;
            this.gc_SOURCE_TYPE.VisibleIndex = 8;
            this.gc_SOURCE_TYPE.Width = 100;
            // 
            // LUE_SourceType
            // 
            this.LUE_SourceType.AutoHeight = false;
            this.LUE_SourceType.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_CODE", "编码"),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("ITEM_NAME", "项目")});
            this.LUE_SourceType.Name = "LUE_SourceType";
            this.LUE_SourceType.NullText = "";
            // 
            // gc_NEXT_PARAM_NAME
            // 
            this.gc_NEXT_PARAM_NAME.Caption = "联动参数";
            this.gc_NEXT_PARAM_NAME.FieldName = "NEXT_PARAM_NAME";
            this.gc_NEXT_PARAM_NAME.MinWidth = 120;
            this.gc_NEXT_PARAM_NAME.Name = "gc_NEXT_PARAM_NAME";
            this.gc_NEXT_PARAM_NAME.OptionsColumn.AllowEdit = false;
            this.gc_NEXT_PARAM_NAME.Visible = true;
            this.gc_NEXT_PARAM_NAME.VisibleIndex = 9;
            this.gc_NEXT_PARAM_NAME.Width = 120;
            // 
            // gc_DEFAULT_VALUE
            // 
            this.gc_DEFAULT_VALUE.Caption = "默认值";
            this.gc_DEFAULT_VALUE.FieldName = "DEFAULT_VALUE";
            this.gc_DEFAULT_VALUE.MinWidth = 80;
            this.gc_DEFAULT_VALUE.Name = "gc_DEFAULT_VALUE";
            this.gc_DEFAULT_VALUE.OptionsColumn.AllowEdit = false;
            this.gc_DEFAULT_VALUE.Visible = true;
            this.gc_DEFAULT_VALUE.VisibleIndex = 3;
            this.gc_DEFAULT_VALUE.Width = 80;
            // 
            // gc_CONTROL_WIDTH
            // 
            this.gc_CONTROL_WIDTH.Caption = "控件大小";
            this.gc_CONTROL_WIDTH.FieldName = "CONTROL_WIDTH";
            this.gc_CONTROL_WIDTH.MinWidth = 60;
            this.gc_CONTROL_WIDTH.Name = "gc_CONTROL_WIDTH";
            this.gc_CONTROL_WIDTH.OptionsColumn.AllowEdit = false;
            this.gc_CONTROL_WIDTH.Visible = true;
            this.gc_CONTROL_WIDTH.VisibleIndex = 5;
            this.gc_CONTROL_WIDTH.Width = 60;
            // 
            // gcTemplet
            // 
            this.gcTemplet.Location = new System.Drawing.Point(3, 64);
            this.gcTemplet.MainView = this.dgvTemple;
            this.gcTemplet.Name = "gcTemplet";
            this.gcTemplet.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repLupTempletClass,
            this.repositoryItemCheckEdit1,
            this.repositoryItemLookUpEdit1});
            this.gcTemplet.Size = new System.Drawing.Size(564, 498);
            this.gcTemplet.TabIndex = 4;
            this.gcTemplet.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.dgvTemple});
            // 
            // dgvTemple
            // 
            this.dgvTemple.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcSe,
            this.gridColumn1,
            this.gcId,
            this.gcClass,
            this.gcName,
            this.gcIsTable,
            this.gcShowMenu,
            this.gcRe});
            this.dgvTemple.GridControl = this.gcTemplet;
            this.dgvTemple.Name = "dgvTemple";
            this.dgvTemple.OptionsCustomization.AllowFilter = false;
            this.dgvTemple.OptionsView.ColumnAutoWidth = false;
            this.dgvTemple.OptionsView.ShowGroupPanel = false;
            this.dgvTemple.CustomRowCellEdit += new DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventHandler(this.dgvTemple_CustomRowCellEdit);
            this.dgvTemple.SelectionChanged += new DevExpress.Data.SelectionChangedEventHandler(this.dgvTemple_SelectionChanged);
            this.dgvTemple.ShowingEditor += new System.ComponentModel.CancelEventHandler(this.dgvTemple_ShowingEditor);
            this.dgvTemple.InitNewRow += new DevExpress.XtraGrid.Views.Grid.InitNewRowEventHandler(this.dgvTemple_InitNewRow);
            this.dgvTemple.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.dgvTemple_FocusedRowChanged);
            this.dgvTemple.CellValueChanged += new DevExpress.XtraGrid.Views.Base.CellValueChangedEventHandler(this.dgvTemple_CellValueChanged);
            // 
            // gcSe
            // 
            this.gcSe.Caption = "序号";
            this.gcSe.FieldName = "SERIAL_NO";
            this.gcSe.Name = "gcSe";
            this.gcSe.Visible = true;
            this.gcSe.VisibleIndex = 0;
            this.gcSe.Width = 34;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "应用程序名";
            this.gridColumn1.ColumnEdit = this.repositoryItemLookUpEdit1;
            this.gridColumn1.FieldName = "APP_NAME";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 1;
            this.gridColumn1.Width = 150;
            // 
            // repositoryItemLookUpEdit1
            // 
            this.repositoryItemLookUpEdit1.AutoHeight = false;
            this.repositoryItemLookUpEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemLookUpEdit1.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("DESCRIPTION", "应用程序名")});
            this.repositoryItemLookUpEdit1.DisplayMember = "DESCRIPTION";
            this.repositoryItemLookUpEdit1.Name = "repositoryItemLookUpEdit1";
            this.repositoryItemLookUpEdit1.NullText = "";
            this.repositoryItemLookUpEdit1.ValueMember = "APP_NAME";
            this.repositoryItemLookUpEdit1.EditValueChanged += new System.EventHandler(this.repositoryItemLookUpEdit1_EditValueChanged);
            // 
            // gcId
            // 
            this.gcId.Caption = "ID";
            this.gcId.FieldName = "DICT_ID";
            this.gcId.Name = "gcId";
            this.gcId.OptionsColumn.AllowEdit = false;
            this.gcId.Width = 40;
            // 
            // gcClass
            // 
            this.gcClass.Caption = "报表分类";
            this.gcClass.ColumnEdit = this.repLupTempletClass;
            this.gcClass.FieldName = "PARENTID";
            this.gcClass.Name = "gcClass";
            this.gcClass.Visible = true;
            this.gcClass.VisibleIndex = 2;
            this.gcClass.Width = 92;
            // 
            // repLupTempletClass
            // 
            this.repLupTempletClass.AutoHeight = false;
            this.repLupTempletClass.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repLupTempletClass.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("CLASS_ID", "分类ID"),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo("CLASS_NAME", "分类名称")});
            this.repLupTempletClass.DisplayMember = "CLASS_NAME";
            this.repLupTempletClass.Name = "repLupTempletClass";
            this.repLupTempletClass.NullText = "";
            this.repLupTempletClass.ValueMember = "CLASS_ID";
            // 
            // gcName
            // 
            this.gcName.Caption = "报表名称";
            this.gcName.FieldName = "DICT_NAME";
            this.gcName.Name = "gcName";
            this.gcName.Visible = true;
            this.gcName.VisibleIndex = 3;
            this.gcName.Width = 152;
            // 
            // gcIsTable
            // 
            this.gcIsTable.Caption = "表格";
            this.gcIsTable.ColumnEdit = this.repositoryItemCheckEdit1;
            this.gcIsTable.FieldName = "IS_TABLE";
            this.gcIsTable.Name = "gcIsTable";
            this.gcIsTable.Visible = true;
            this.gcIsTable.VisibleIndex = 4;
            this.gcIsTable.Width = 32;
            // 
            // repositoryItemCheckEdit1
            // 
            this.repositoryItemCheckEdit1.AutoHeight = false;
            this.repositoryItemCheckEdit1.Name = "repositoryItemCheckEdit1";
            this.repositoryItemCheckEdit1.ValueChecked = "1";
            this.repositoryItemCheckEdit1.ValueUnchecked = "";
            // 
            // gcShowMenu
            // 
            this.gcShowMenu.Caption = "主菜单";
            this.gcShowMenu.ColumnEdit = this.repositoryItemCheckEdit1;
            this.gcShowMenu.FieldName = "SHOW_MENU";
            this.gcShowMenu.Name = "gcShowMenu";
            this.gcShowMenu.Visible = true;
            this.gcShowMenu.VisibleIndex = 5;
            this.gcShowMenu.Width = 45;
            // 
            // gcRe
            // 
            this.gcRe.Caption = "备注";
            this.gcRe.FieldName = "REMARK";
            this.gcRe.Name = "gcRe";
            this.gcRe.Visible = true;
            this.gcRe.VisibleIndex = 6;
            this.gcRe.Width = 72;
            // 
            // txtSql
            // 
            this.txtSql.Location = new System.Drawing.Point(580, 290);
            this.txtSql.Name = "txtSql";
            this.txtSql.Size = new System.Drawing.Size(356, 322);
            this.txtSql.StyleController = this.layoutControl1;
            this.txtSql.TabIndex = 8;
            this.txtSql.Enter += new System.EventHandler(this.txtSql_Enter);
            // 
            // btnTest
            // 
            this.btnTest.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnTest.ImageOptions.Image")));
            this.btnTest.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.ImageAlignToText.LeftCenter;
            this.btnTest.Location = new System.Drawing.Point(940, 316);
            this.btnTest.Name = "btnTest";
            this.btnTest.Size = new System.Drawing.Size(105, 22);
            this.btnTest.StyleController = this.layoutControl1;
            this.btnTest.TabIndex = 9;
            this.btnTest.Text = "测试";
            this.btnTest.Click += new System.EventHandler(this.btnTest_Click);
            // 
            // btnSaveSql
            // 
            this.btnSaveSql.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnSaveSql.ImageOptions.Image")));
            this.btnSaveSql.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.ImageAlignToText.LeftCenter;
            this.btnSaveSql.Location = new System.Drawing.Point(940, 342);
            this.btnSaveSql.Name = "btnSaveSql";
            this.btnSaveSql.Size = new System.Drawing.Size(105, 22);
            this.btnSaveSql.StyleController = this.layoutControl1;
            this.btnSaveSql.TabIndex = 10;
            this.btnSaveSql.Text = "保存";
            this.btnSaveSql.Click += new System.EventHandler(this.btnSaveSql_Click);
            // 
            // sBtnTempClass
            // 
            this.sBtnTempClass.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("sBtnTempClass.ImageOptions.Image")));
            this.sBtnTempClass.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.ImageAlignToText.LeftCenter;
            this.sBtnTempClass.Location = new System.Drawing.Point(2, 2);
            this.sBtnTempClass.Name = "sBtnTempClass";
            this.sBtnTempClass.Size = new System.Drawing.Size(99, 38);
            this.sBtnTempClass.StyleController = this.layoutControl1;
            this.sBtnTempClass.TabIndex = 11;
            this.sBtnTempClass.Text = "分类维护";
            this.sBtnTempClass.Click += new System.EventHandler(this.sBtnTempClass_Click);
            // 
            // cbPublic
            // 
            this.cbPublic.Location = new System.Drawing.Point(580, 66);
            this.cbPublic.Name = "cbPublic";
            this.cbPublic.Properties.Caption = "公开";
            this.cbPublic.Size = new System.Drawing.Size(46, 19);
            this.cbPublic.StyleController = this.layoutControl1;
            this.cbPublic.TabIndex = 12;
            this.cbPublic.CheckedChanged += new System.EventHandler(this.cbPublic_CheckedChanged);
            // 
            // cbPrivate
            // 
            this.cbPrivate.Location = new System.Drawing.Point(630, 76);
            this.cbPrivate.Name = "cbPrivate";
            this.cbPrivate.Properties.Caption = "私密";
            this.cbPrivate.Size = new System.Drawing.Size(46, 19);
            this.cbPrivate.StyleController = this.layoutControl1;
            this.cbPrivate.TabIndex = 13;
            this.cbPrivate.CheckedChanged += new System.EventHandler(this.cbPrivate_CheckedChanged);
            // 
            // cbSet
            // 
            this.cbSet.Location = new System.Drawing.Point(680, 76);
            this.cbSet.Name = "cbSet";
            this.cbSet.Properties.Caption = "自定义";
            this.cbSet.Size = new System.Drawing.Size(58, 19);
            this.cbSet.StyleController = this.layoutControl1;
            this.cbSet.TabIndex = 14;
            this.cbSet.CheckedChanged += new System.EventHandler(this.cbSet_CheckedChanged);
            // 
            // txtRole
            // 
            this.txtRole.Location = new System.Drawing.Point(742, 76);
            this.txtRole.Name = "txtRole";
            this.txtRole.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txtRole.Size = new System.Drawing.Size(293, 20);
            this.txtRole.StyleController = this.layoutControl1;
            this.txtRole.TabIndex = 15;
            this.txtRole.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.txtRole_ButtonClick);
            this.txtRole.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.txtRole_EditValueChanging);
            this.txtRole.DoubleClick += new System.EventHandler(this.txtRole_DoubleClick);
            // 
            // sbtnReportParam
            // 
            this.sbtnReportParam.AutoWidthInLayoutControl = true;
            this.sbtnReportParam.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("sbtnReportParam.ImageOptions.Image")));
            this.sbtnReportParam.Location = new System.Drawing.Point(105, 2);
            this.sbtnReportParam.MinimumSize = new System.Drawing.Size(80, 38);
            this.sbtnReportParam.Name = "sbtnReportParam";
            this.sbtnReportParam.Size = new System.Drawing.Size(92, 38);
            this.sbtnReportParam.StyleController = this.layoutControl1;
            this.sbtnReportParam.TabIndex = 16;
            this.sbtnReportParam.Text = "参数字典";
            this.sbtnReportParam.Click += new System.EventHandler(this.sbtnReportParam_Click);
            // 
            // sbtnRparamSave
            // 
            this.sbtnRparamSave.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("sbtnRparamSave.ImageOptions.Image")));
            this.sbtnRparamSave.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.ImageAlignToText.LeftCenter;
            this.sbtnRparamSave.Location = new System.Drawing.Point(942, 239);
            this.sbtnRparamSave.Name = "sbtnRparamSave";
            this.sbtnRparamSave.Size = new System.Drawing.Size(103, 22);
            this.sbtnRparamSave.StyleController = this.layoutControl1;
            this.sbtnRparamSave.TabIndex = 18;
            this.sbtnRparamSave.Text = "应用";
            this.sbtnRparamSave.Click += new System.EventHandler(this.sbtnRparamSave_Click);
            // 
            // sbtnRparamAdd
            // 
            this.sbtnRparamAdd.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("sbtnRparamAdd.ImageOptions.Image")));
            this.sbtnRparamAdd.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.ImageAlignToText.LeftCenter;
            this.sbtnRparamAdd.Location = new System.Drawing.Point(942, 161);
            this.sbtnRparamAdd.Name = "sbtnRparamAdd";
            this.sbtnRparamAdd.Size = new System.Drawing.Size(103, 22);
            this.sbtnRparamAdd.StyleController = this.layoutControl1;
            this.sbtnRparamAdd.TabIndex = 19;
            this.sbtnRparamAdd.Text = "新建";
            this.sbtnRparamAdd.Click += new System.EventHandler(this.sbtnRparamAdd_Click);
            // 
            // sbtnOpenParamDict
            // 
            this.sbtnOpenParamDict.Location = new System.Drawing.Point(942, 135);
            this.sbtnOpenParamDict.Name = "sbtnOpenParamDict";
            this.sbtnOpenParamDict.Size = new System.Drawing.Size(103, 22);
            this.sbtnOpenParamDict.StyleController = this.layoutControl1;
            this.sbtnOpenParamDict.TabIndex = 20;
            this.sbtnOpenParamDict.Text = "选择参数";
            this.sbtnOpenParamDict.Click += new System.EventHandler(this.sbtnOpenParamDict_Click);
            // 
            // sbtnRparamDel
            // 
            this.sbtnRparamDel.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("sbtnRparamDel.ImageOptions.Image")));
            this.sbtnRparamDel.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.ImageAlignToText.LeftCenter;
            this.sbtnRparamDel.Location = new System.Drawing.Point(942, 213);
            this.sbtnRparamDel.Name = "sbtnRparamDel";
            this.sbtnRparamDel.Size = new System.Drawing.Size(103, 22);
            this.sbtnRparamDel.StyleController = this.layoutControl1;
            this.sbtnRparamDel.TabIndex = 21;
            this.sbtnRparamDel.Text = "删除";
            this.sbtnRparamDel.Click += new System.EventHandler(this.sbtnRparamDel_Click);
            // 
            // sbtnRparamEdit
            // 
            this.sbtnRparamEdit.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("sbtnRparamEdit.ImageOptions.Image")));
            this.sbtnRparamEdit.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.ImageAlignToText.LeftCenter;
            this.sbtnRparamEdit.Location = new System.Drawing.Point(942, 187);
            this.sbtnRparamEdit.Name = "sbtnRparamEdit";
            this.sbtnRparamEdit.Size = new System.Drawing.Size(103, 22);
            this.sbtnRparamEdit.StyleController = this.layoutControl1;
            this.sbtnRparamEdit.TabIndex = 24;
            this.sbtnRparamEdit.Text = "编辑";
            this.sbtnRparamEdit.Click += new System.EventHandler(this.sbtnRparamEdit_Click);
            // 
            // btnBindingField
            // 
            this.btnBindingField.Location = new System.Drawing.Point(940, 290);
            this.btnBindingField.Name = "btnBindingField";
            this.btnBindingField.Size = new System.Drawing.Size(105, 22);
            this.btnBindingField.StyleController = this.layoutControl1;
            this.btnBindingField.TabIndex = 25;
            this.btnBindingField.Text = "绑定字段";
            this.btnBindingField.Click += new System.EventHandler(this.simpleButton1_Click);
            // 
            // sBtnExportXml
            // 
            this.sBtnExportXml.Location = new System.Drawing.Point(738, 2);
            this.sBtnExportXml.Name = "sBtnExportXml";
            this.sBtnExportXml.Size = new System.Drawing.Size(310, 22);
            this.sBtnExportXml.StyleController = this.layoutControl1;
            this.sBtnExportXml.TabIndex = 27;
            this.sBtnExportXml.Text = "存储数据结构";
            this.sBtnExportXml.Click += new System.EventHandler(this.sBtnExportXml_Click);
            // 
            // btnAdd1
            // 
            this.btnAdd1.Location = new System.Drawing.Point(3, 566);
            this.btnAdd1.Name = "btnAdd1";
            this.btnAdd1.Size = new System.Drawing.Size(73, 22);
            this.btnAdd1.StyleController = this.layoutControl1;
            this.btnAdd1.TabIndex = 28;
            this.btnAdd1.Text = "新增";
            this.btnAdd1.Click += new System.EventHandler(this.btnAdd_Click);
            // 
            // btnDelete1
            // 
            this.btnDelete1.Location = new System.Drawing.Point(3, 592);
            this.btnDelete1.Name = "btnDelete1";
            this.btnDelete1.Size = new System.Drawing.Size(151, 22);
            this.btnDelete1.StyleController = this.layoutControl1;
            this.btnDelete1.TabIndex = 29;
            this.btnDelete1.Text = "删除";
            this.btnDelete1.Click += new System.EventHandler(this.btnDelete_Click);
            // 
            // btnSave1
            // 
            this.btnSave1.Location = new System.Drawing.Point(158, 592);
            this.btnSave1.Name = "btnSave1";
            this.btnSave1.Size = new System.Drawing.Size(409, 22);
            this.btnSave1.StyleController = this.layoutControl1;
            this.btnSave1.TabIndex = 30;
            this.btnSave1.Text = "保存";
            this.btnSave1.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // sbtnAddTo1
            // 
            this.sbtnAddTo1.Location = new System.Drawing.Point(186, 566);
            this.sbtnAddTo1.Name = "sbtnAddTo1";
            this.sbtnAddTo1.Size = new System.Drawing.Size(308, 22);
            this.sbtnAddTo1.StyleController = this.layoutControl1;
            this.sbtnAddTo1.TabIndex = 31;
            this.sbtnAddTo1.Text = "添加至...";
            this.sbtnAddTo1.Click += new System.EventHandler(this.sbtnAddTo_Click);
            // 
            // btnCopyCreate1
            // 
            this.btnCopyCreate1.Location = new System.Drawing.Point(498, 566);
            this.btnCopyCreate1.Name = "btnCopyCreate1";
            this.btnCopyCreate1.Size = new System.Drawing.Size(69, 22);
            this.btnCopyCreate1.StyleController = this.layoutControl1;
            this.btnCopyCreate1.TabIndex = 32;
            this.btnCopyCreate1.Text = "复制并创建";
            this.btnCopyCreate1.Click += new System.EventHandler(this.btnCopyCreate_Click);
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup2,
            this.layoutControlItem6,
            this.layoutControlItem12,
            this.emptySpaceItem1,
            this.layoutControlGroup4,
            this.groupReportParam,
            this.splitterItem2,
            this.groupReportRole,
            this.layoutControlItem22});
            this.layoutControlGroup1.Name = "Root";
            this.layoutControlGroup1.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.layoutControlGroup1.Size = new System.Drawing.Size(1050, 617);
            this.layoutControlGroup1.TextVisible = false;
            // 
            // layoutControlGroup2
            // 
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem1,
            this.emptySpaceItem2,
            this.layoutControlItem23,
            this.layoutControlItem24,
            this.layoutControlItem25,
            this.layoutControlItem26,
            this.layoutControlItem27});
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 42);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.layoutControlGroup2.Size = new System.Drawing.Size(570, 575);
            this.layoutControlGroup2.Spacing = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.layoutControlGroup2.Text = "报表列表";
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.gcTemplet;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(568, 502);
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            // 
            // emptySpaceItem2
            // 
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(77, 502);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(106, 26);
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlItem23
            // 
            this.layoutControlItem23.Control = this.btnAdd1;
            this.layoutControlItem23.Location = new System.Drawing.Point(0, 502);
            this.layoutControlItem23.Name = "layoutControlItem23";
            this.layoutControlItem23.Size = new System.Drawing.Size(77, 26);
            this.layoutControlItem23.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem23.TextVisible = false;
            // 
            // layoutControlItem24
            // 
            this.layoutControlItem24.Control = this.btnDelete1;
            this.layoutControlItem24.Location = new System.Drawing.Point(0, 528);
            this.layoutControlItem24.Name = "layoutControlItem24";
            this.layoutControlItem24.Size = new System.Drawing.Size(155, 26);
            this.layoutControlItem24.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem24.TextVisible = false;
            // 
            // layoutControlItem25
            // 
            this.layoutControlItem25.Control = this.btnSave1;
            this.layoutControlItem25.Location = new System.Drawing.Point(155, 528);
            this.layoutControlItem25.Name = "layoutControlItem25";
            this.layoutControlItem25.Size = new System.Drawing.Size(413, 26);
            this.layoutControlItem25.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem25.TextVisible = false;
            // 
            // layoutControlItem26
            // 
            this.layoutControlItem26.Control = this.sbtnAddTo1;
            this.layoutControlItem26.Location = new System.Drawing.Point(183, 502);
            this.layoutControlItem26.Name = "layoutControlItem26";
            this.layoutControlItem26.Size = new System.Drawing.Size(312, 26);
            this.layoutControlItem26.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem26.TextVisible = false;
            // 
            // layoutControlItem27
            // 
            this.layoutControlItem27.Control = this.btnCopyCreate1;
            this.layoutControlItem27.Location = new System.Drawing.Point(495, 502);
            this.layoutControlItem27.MaxSize = new System.Drawing.Size(73, 26);
            this.layoutControlItem27.MinSize = new System.Drawing.Size(73, 26);
            this.layoutControlItem27.Name = "layoutControlItem27";
            this.layoutControlItem27.Size = new System.Drawing.Size(73, 26);
            this.layoutControlItem27.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem27.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem27.TextVisible = false;
            // 
            // layoutControlItem6
            // 
            this.layoutControlItem6.Control = this.sBtnTempClass;
            this.layoutControlItem6.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem6.MaxSize = new System.Drawing.Size(103, 42);
            this.layoutControlItem6.MinSize = new System.Drawing.Size(103, 42);
            this.layoutControlItem6.Name = "layoutControlItem6";
            this.layoutControlItem6.Size = new System.Drawing.Size(103, 42);
            this.layoutControlItem6.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem6.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem6.TextVisible = false;
            // 
            // layoutControlItem12
            // 
            this.layoutControlItem12.Control = this.sbtnReportParam;
            this.layoutControlItem12.Location = new System.Drawing.Point(103, 0);
            this.layoutControlItem12.MaxSize = new System.Drawing.Size(96, 42);
            this.layoutControlItem12.MinSize = new System.Drawing.Size(96, 42);
            this.layoutControlItem12.Name = "layoutControlItem12";
            this.layoutControlItem12.Size = new System.Drawing.Size(96, 42);
            this.layoutControlItem12.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem12.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem12.TextVisible = false;
            // 
            // emptySpaceItem1
            // 
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(199, 0);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.emptySpaceItem1.Size = new System.Drawing.Size(537, 42);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlGroup4
            // 
            this.layoutControlGroup4.ExpandButtonMode = DevExpress.Utils.Controls.ExpandButtonMode.Inverted;
            this.layoutControlGroup4.ExpandButtonVisible = true;
            this.layoutControlGroup4.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem5,
            this.btnTestControl,
            this.layoutControlItem7,
            this.layoutControlItem20});
            this.layoutControlGroup4.Location = new System.Drawing.Point(575, 266);
            this.layoutControlGroup4.Name = "layoutControlGroup4";
            this.layoutControlGroup4.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.layoutControlGroup4.Size = new System.Drawing.Size(475, 351);
            this.layoutControlGroup4.Text = "SQL脚本";
            // 
            // layoutControlItem5
            // 
            this.layoutControlItem5.Control = this.txtSql;
            this.layoutControlItem5.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem5.MinSize = new System.Drawing.Size(14, 20);
            this.layoutControlItem5.Name = "layoutControlItem5";
            this.layoutControlItem5.Size = new System.Drawing.Size(360, 326);
            this.layoutControlItem5.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem5.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem5.TextVisible = false;
            // 
            // btnTestControl
            // 
            this.btnTestControl.Control = this.btnTest;
            this.btnTestControl.Location = new System.Drawing.Point(360, 26);
            this.btnTestControl.Name = "btnTestControl";
            this.btnTestControl.Size = new System.Drawing.Size(109, 26);
            this.btnTestControl.Text = "测试";
            this.btnTestControl.TextSize = new System.Drawing.Size(0, 0);
            this.btnTestControl.TextVisible = false;
            // 
            // layoutControlItem7
            // 
            this.layoutControlItem7.Control = this.btnSaveSql;
            this.layoutControlItem7.Location = new System.Drawing.Point(360, 52);
            this.layoutControlItem7.Name = "layoutControlItem7";
            this.layoutControlItem7.Size = new System.Drawing.Size(109, 274);
            this.layoutControlItem7.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem7.TextVisible = false;
            // 
            // layoutControlItem20
            // 
            this.layoutControlItem20.Control = this.btnBindingField;
            this.layoutControlItem20.Location = new System.Drawing.Point(360, 0);
            this.layoutControlItem20.Name = "layoutControlItem20";
            this.layoutControlItem20.Size = new System.Drawing.Size(109, 26);
            this.layoutControlItem20.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem20.TextVisible = false;
            // 
            // groupReportParam
            // 
            this.groupReportParam.ExpandButtonMode = DevExpress.Utils.Controls.ExpandButtonMode.Inverted;
            this.groupReportParam.ExpandButtonVisible = true;
            this.groupReportParam.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem16,
            this.layoutControlItem15,
            this.layoutControlItem17,
            this.layoutControlItem14,
            this.layoutControlItem19,
            this.layoutControlItem18});
            this.groupReportParam.Location = new System.Drawing.Point(575, 111);
            this.groupReportParam.Name = "groupReportParam";
            this.groupReportParam.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.groupReportParam.Size = new System.Drawing.Size(475, 155);
            this.groupReportParam.Text = "报表参数";
            this.groupReportParam.CustomButtonClick += new DevExpress.XtraBars.Docking2010.BaseButtonEventHandler(this.groupReportParam_CustomButtonClick);
            // 
            // layoutControlItem16
            // 
            this.layoutControlItem16.Control = this.sbtnOpenParamDict;
            this.layoutControlItem16.Location = new System.Drawing.Point(362, 0);
            this.layoutControlItem16.MaxSize = new System.Drawing.Size(0, 26);
            this.layoutControlItem16.MinSize = new System.Drawing.Size(61, 26);
            this.layoutControlItem16.Name = "layoutControlItem16";
            this.layoutControlItem16.Size = new System.Drawing.Size(107, 26);
            this.layoutControlItem16.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem16.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem16.TextVisible = false;
            // 
            // layoutControlItem15
            // 
            this.layoutControlItem15.Control = this.sbtnRparamAdd;
            this.layoutControlItem15.Location = new System.Drawing.Point(362, 26);
            this.layoutControlItem15.MaxSize = new System.Drawing.Size(0, 26);
            this.layoutControlItem15.MinSize = new System.Drawing.Size(56, 26);
            this.layoutControlItem15.Name = "layoutControlItem15";
            this.layoutControlItem15.Size = new System.Drawing.Size(107, 26);
            this.layoutControlItem15.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem15.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem15.TextVisible = false;
            // 
            // layoutControlItem17
            // 
            this.layoutControlItem17.Control = this.sbtnRparamDel;
            this.layoutControlItem17.Location = new System.Drawing.Point(362, 78);
            this.layoutControlItem17.MaxSize = new System.Drawing.Size(0, 26);
            this.layoutControlItem17.MinSize = new System.Drawing.Size(56, 26);
            this.layoutControlItem17.Name = "layoutControlItem17";
            this.layoutControlItem17.Size = new System.Drawing.Size(107, 26);
            this.layoutControlItem17.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem17.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem17.TextVisible = false;
            // 
            // layoutControlItem14
            // 
            this.layoutControlItem14.Control = this.sbtnRparamSave;
            this.layoutControlItem14.Location = new System.Drawing.Point(362, 104);
            this.layoutControlItem14.Name = "layoutControlItem14";
            this.layoutControlItem14.Size = new System.Drawing.Size(107, 26);
            this.layoutControlItem14.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem14.TextVisible = false;
            // 
            // layoutControlItem19
            // 
            this.layoutControlItem19.Control = this.gcReportParam;
            this.layoutControlItem19.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem19.Name = "layoutControlItem19";
            this.layoutControlItem19.Size = new System.Drawing.Size(362, 130);
            this.layoutControlItem19.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem19.TextVisible = false;
            // 
            // layoutControlItem18
            // 
            this.layoutControlItem18.Control = this.sbtnRparamEdit;
            this.layoutControlItem18.Location = new System.Drawing.Point(362, 52);
            this.layoutControlItem18.MaxSize = new System.Drawing.Size(0, 26);
            this.layoutControlItem18.MinSize = new System.Drawing.Size(56, 26);
            this.layoutControlItem18.Name = "layoutControlItem18";
            this.layoutControlItem18.Size = new System.Drawing.Size(107, 26);
            this.layoutControlItem18.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem18.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem18.TextVisible = false;
            // 
            // splitterItem2
            // 
            this.splitterItem2.AllowHotTrack = true;
            this.splitterItem2.Location = new System.Drawing.Point(570, 42);
            this.splitterItem2.Name = "splitterItem2";
            this.splitterItem2.Size = new System.Drawing.Size(5, 575);
            // 
            // groupReportRole
            // 
            this.groupReportRole.ExpandButtonMode = DevExpress.Utils.Controls.ExpandButtonMode.Inverted;
            this.groupReportRole.ExpandButtonVisible = true;
            this.groupReportRole.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem10,
            this.layoutControlItem11,
            this.layoutControlItem9,
            this.layoutControlItem8,
            this.emptySpaceItem10,
            this.emptySpaceItem7,
            this.emptySpaceItem9});
            this.groupReportRole.Location = new System.Drawing.Point(575, 42);
            this.groupReportRole.Name = "groupReportRole";
            this.groupReportRole.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            this.groupReportRole.Size = new System.Drawing.Size(475, 69);
            this.groupReportRole.Text = "报表权限";
            // 
            // layoutControlItem10
            // 
            this.layoutControlItem10.Control = this.cbSet;
            this.layoutControlItem10.Location = new System.Drawing.Point(100, 10);
            this.layoutControlItem10.MaxSize = new System.Drawing.Size(62, 24);
            this.layoutControlItem10.MinSize = new System.Drawing.Size(62, 24);
            this.layoutControlItem10.Name = "layoutControlItem10";
            this.layoutControlItem10.Size = new System.Drawing.Size(62, 24);
            this.layoutControlItem10.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem10.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem10.TextVisible = false;
            // 
            // layoutControlItem11
            // 
            this.layoutControlItem11.Control = this.txtRole;
            this.layoutControlItem11.Location = new System.Drawing.Point(162, 10);
            this.layoutControlItem11.MaxSize = new System.Drawing.Size(0, 24);
            this.layoutControlItem11.MinSize = new System.Drawing.Size(54, 24);
            this.layoutControlItem11.Name = "layoutControlItem11";
            this.layoutControlItem11.Size = new System.Drawing.Size(297, 24);
            this.layoutControlItem11.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem11.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem11.TextVisible = false;
            // 
            // layoutControlItem9
            // 
            this.layoutControlItem9.Control = this.cbPrivate;
            this.layoutControlItem9.Location = new System.Drawing.Point(50, 10);
            this.layoutControlItem9.MaxSize = new System.Drawing.Size(50, 24);
            this.layoutControlItem9.MinSize = new System.Drawing.Size(50, 24);
            this.layoutControlItem9.Name = "layoutControlItem9";
            this.layoutControlItem9.Size = new System.Drawing.Size(50, 24);
            this.layoutControlItem9.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem9.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem9.TextVisible = false;
            // 
            // layoutControlItem8
            // 
            this.layoutControlItem8.Control = this.cbPublic;
            this.layoutControlItem8.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem8.MaxSize = new System.Drawing.Size(50, 24);
            this.layoutControlItem8.MinSize = new System.Drawing.Size(50, 24);
            this.layoutControlItem8.Name = "layoutControlItem8";
            this.layoutControlItem8.Size = new System.Drawing.Size(50, 34);
            this.layoutControlItem8.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem8.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem8.TextVisible = false;
            // 
            // emptySpaceItem10
            // 
            this.emptySpaceItem10.AllowHotTrack = false;
            this.emptySpaceItem10.Location = new System.Drawing.Point(50, 0);
            this.emptySpaceItem10.MaxSize = new System.Drawing.Size(0, 10);
            this.emptySpaceItem10.MinSize = new System.Drawing.Size(10, 10);
            this.emptySpaceItem10.Name = "emptySpaceItem10";
            this.emptySpaceItem10.Size = new System.Drawing.Size(419, 10);
            this.emptySpaceItem10.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.emptySpaceItem10.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem7
            // 
            this.emptySpaceItem7.AllowHotTrack = false;
            this.emptySpaceItem7.Location = new System.Drawing.Point(459, 10);
            this.emptySpaceItem7.MaxSize = new System.Drawing.Size(10, 24);
            this.emptySpaceItem7.MinSize = new System.Drawing.Size(10, 24);
            this.emptySpaceItem7.Name = "emptySpaceItem7";
            this.emptySpaceItem7.Size = new System.Drawing.Size(10, 24);
            this.emptySpaceItem7.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.emptySpaceItem7.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem9
            // 
            this.emptySpaceItem9.AllowHotTrack = false;
            this.emptySpaceItem9.Location = new System.Drawing.Point(0, 34);
            this.emptySpaceItem9.MaxSize = new System.Drawing.Size(0, 10);
            this.emptySpaceItem9.MinSize = new System.Drawing.Size(10, 10);
            this.emptySpaceItem9.Name = "emptySpaceItem9";
            this.emptySpaceItem9.Size = new System.Drawing.Size(469, 10);
            this.emptySpaceItem9.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.emptySpaceItem9.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlItem22
            // 
            this.layoutControlItem22.Control = this.sBtnExportXml;
            this.layoutControlItem22.Location = new System.Drawing.Point(736, 0);
            this.layoutControlItem22.Name = "layoutControlItem22";
            this.layoutControlItem22.Size = new System.Drawing.Size(314, 42);
            this.layoutControlItem22.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem22.TextVisible = false;
            // 
            // frmStatisticalSetting
            // 
            this.ClientSize = new System.Drawing.Size(1050, 617);
            this.Controls.Add(this.layoutControl1);
            this.MinimizeBox = false;
            this.Name = "frmStatisticalSetting";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "自定义报表维护";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.Load += new System.EventHandler(this.frmStatisticalSetting_Load);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).EndInit();
            this.layoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcReportParam)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvReportParam)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.LUE_EditType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.LUE_SourceType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcTemplet)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvTemple)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemLookUpEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLupTempletClass)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSql.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbPublic.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbPrivate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbSet.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRole.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem23)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem24)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem25)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem26)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem27)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.btnTestControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupReportParam)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitterItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupReportRole)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem22)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraLayout.LayoutControl layoutControl1;
        private DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;
        private DevExpress.XtraGrid.GridControl gcTemplet;
        private DevExpress.XtraGrid.Views.Grid.GridView dgvTemple;
        private DevExpress.XtraEditors.MemoEdit txtSql;
        private DevExpress.XtraEditors.SimpleButton btnTest;
        private DevExpress.XtraEditors.SimpleButton btnSaveSql;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;
        private DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem2;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem5;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem7;
        private DevExpress.XtraLayout.LayoutControlItem btnTestControl;
        private DevExpress.XtraGrid.Columns.GridColumn gcSe;
        private DevExpress.XtraGrid.Columns.GridColumn gcId;
        private DevExpress.XtraGrid.Columns.GridColumn gcName;
        private DevExpress.XtraGrid.Columns.GridColumn gcRe;
        private DevExpress.XtraGrid.Columns.GridColumn gcClass;
        private DevExpress.XtraEditors.SimpleButton sBtnTempClass;
        private DevExpress.XtraEditors.CheckEdit cbPublic;
        private DevExpress.XtraEditors.CheckEdit cbPrivate;
        private DevExpress.XtraEditors.CheckEdit cbSet;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem6;
        private DevExpress.XtraLayout.LayoutControlGroup groupReportRole;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem10;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem11;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem9;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem8;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem10;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem7;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repLupTempletClass;
        private DevExpress.XtraEditors.ButtonEdit txtRole;
        private DevExpress.XtraEditors.SimpleButton sbtnReportParam;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem12;
        private DevExpress.XtraEditors.SimpleButton sbtnRparamSave;
        private DevExpress.XtraEditors.SimpleButton sbtnRparamAdd;
        private DevExpress.XtraEditors.SimpleButton sbtnOpenParamDict;
        private DevExpress.XtraEditors.SimpleButton sbtnRparamDel;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem15;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem14;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem16;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem17;
        private DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup4;
        private DevExpress.XtraLayout.LayoutControlGroup groupReportParam;
        private DevExpress.XtraGrid.GridControl gcReportParam;
        private DevExpress.XtraGrid.Views.Grid.GridView dgvReportParam;
        private DevExpress.XtraGrid.Columns.GridColumn gc_SerialNo;
        private DevExpress.XtraGrid.Columns.GridColumn gc_PARAM_NAME;
        private DevExpress.XtraGrid.Columns.GridColumn gc_EDIT_TYPE;
        private DevExpress.XtraGrid.Columns.GridColumn gc_CAPTION;
        private DevExpress.XtraGrid.Columns.GridColumn gc_DISPLAY_MEMBER;
        private DevExpress.XtraGrid.Columns.GridColumn gc_VALUE_MEMBER;
        private DevExpress.XtraGrid.Columns.GridColumn gc_SOURCE_TYPE;
        private DevExpress.XtraGrid.Columns.GridColumn gc_NEXT_PARAM_NAME;
        private DevExpress.XtraGrid.Columns.GridColumn gc_DEFAULT_VALUE;
        private DevExpress.XtraGrid.Columns.GridColumn gc_CONTROL_WIDTH;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem19;
        private DevExpress.XtraEditors.SimpleButton sbtnRparamEdit;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem18;
        private DevExpress.XtraLayout.SplitterItem splitterItem2;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem9;
        private DevExpress.XtraEditors.SimpleButton btnBindingField;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem20;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit LUE_EditType;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit LUE_SourceType;
        private DevExpress.XtraGrid.Columns.GridColumn gcIsTable;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit repositoryItemCheckEdit1;
        private DevExpress.XtraGrid.Columns.GridColumn gcShowMenu;
        private DevExpress.XtraEditors.SimpleButton sBtnExportXml;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repositoryItemLookUpEdit1;
        private DevExpress.XtraEditors.SimpleButton btnAdd1;
        private DevExpress.XtraEditors.SimpleButton btnDelete1;
        private DevExpress.XtraEditors.SimpleButton btnSave1;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem23;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem24;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem25;
        private DevExpress.XtraEditors.SimpleButton sbtnAddTo1;
        private DevExpress.XtraEditors.SimpleButton btnCopyCreate1;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem26;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem27;
    }
}
