﻿/*********************************************
* 文 件 名：Cs02ExtendTable
* 类 名 称：Cs02ExtendTable
* 功能说明：DataTable扩展
* 版权说明：北京天健源达 HIS基础产品研发部
* 创 建 人：刘成刚
* 创建时间：2019-05-18 19:41:55
* 版 本 号：1.0.0.1
* 修 改 人：刘成刚
* 修改时间：2020-02-18
* CLR 版本：4.0.30319.42000
/*********************************************/

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using PlatCommon.Base01;

namespace PlatCommon.Base02
{
    /// <summary>
    /// DataTable扩展
    /// </summary>
    public static class Cs02ExtendTable
    {
        /// <summary>
        /// 将DataTable按列排序
        /// <param name="dtSourceTable">排序前DataTable</param>
        /// <param name="strSortFields">排序字段strFieldNames</param>
        /// <returns>排序后DataTable</returns>
        /// </summary> 
        public static DataTable TableSort(DataTable dtSourceTable, string strSortFields)
        {
            //传入的参数检查
            if (dtSourceTable.Rows.Count == 0)
                return dtSourceTable;

            if (strSortFields.Length < 1)
                return dtSourceTable;

            //复制表结构
            DataTable dtResultTable = dtSourceTable.Clone();
            //排序
            DataRow[] sortRows = dtSourceTable.Select(null, strSortFields);
            dtResultTable = sortRows.CopyToDataTable();
            return dtResultTable;
        }

        /// <summary>
        /// 将DataTable分组
        /// <param name="dtSourceTable">要分组的DataTable</param>
        /// <param name="strGroupFields">分组字段strGroupFields</param>
        /// <returns>分组后DataTable</returns>
        /// </summary> 
        public static DataTable TableGroup(DataTable dtSourceTable, string strGroupFields)
        {
            //传入的参数检查
            if (dtSourceTable.Rows.Count == 0)
                return dtSourceTable;

            if (strGroupFields.Length < 1)
                return dtSourceTable;

            //分解分组的字段
            string[] strArrGroupField = strGroupFields.Split(',');

            //生成结果表的结构
            DataTable dtResultTable = new DataTable();
            DataColumn dcResultColumn;
            for (int i = 0; i < dtSourceTable.Columns.Count; i++)
            {
                if (Array.IndexOf(strArrGroupField, dtSourceTable.Columns[i].ColumnName) >= 0)
                {
                    dcResultColumn = new DataColumn(dtSourceTable.Columns[i].ColumnName, dtSourceTable.Columns[i].DataType);
                    dtResultTable.Columns.Add(dcResultColumn);
                }
            }

            //旧行的数据初始化
            string[] strArrFieldValue = strGroupFields.Split(',');
            for (int i = 0; i < strArrFieldValue.Length; i++)
            {
                strArrFieldValue[i] = "";
            }

            //对源表进行排序，用排序表进行数据处理
            DataTable dtSortTable = TableSort(dtSourceTable, strGroupFields);

            DataRow drSortRow, drResultRow;
            Boolean bIsNewRow = false;
            for (int i = 0; i < dtSortTable.Rows.Count; i++)
            {
                drSortRow = dtSortTable.Rows[i];

                //判断是不是新行
                bIsNewRow = false;
                for (int j = 0; j < strArrGroupField.Length; j++)
                {
                    if (drSortRow[strArrGroupField[j]]?.ToString() != strArrFieldValue[j])
                    {
                        bIsNewRow = true;
                        break;
                    }
                }

                //结果中加入新行
                if (bIsNewRow)
                {
                    drResultRow = dtResultTable.NewRow();
                    for (int j = 0; j < strArrGroupField.Length; j++)
                    {
                        drResultRow[strArrGroupField[j]] = drSortRow[strArrGroupField[j]];
                        strArrFieldValue[j] = drSortRow[strArrGroupField[j]]?.ToString();
                    }
                    dtResultTable.Rows.Add(drResultRow);
                }
            }
            return dtResultTable;
        }

        /// <summary>
        /// 将DataTable转为交叉表
        /// </summary>
        /// <param name="dtSourceTable">交叉表源</param>
        /// <param name="strFixFields">固定字段</param>
        /// <param name="strCrossField">可变字段</param>
        /// <param name="strCrossValueField">可变值字段</param>
        /// <returns>转为交叉表后的DataTable</returns>
        public static DataTable ChangeToCross(DataTable dtSourceTable, string strFixFields, string strCrossField, string strCrossValueField)
        {
            //传入的参数检查
            if (dtSourceTable.Rows.Count == 0)
                return dtSourceTable;

            if (strFixFields.Length < 1)
                return dtSourceTable;

            if (strCrossField.Length < 1)
                return dtSourceTable;

            if (strCrossValueField.Length < 1)
                return dtSourceTable;

            DataTable dtResultTable = new DataTable();
            DataColumn dcSourceColumn, dcResultColumn;
            //先处理固定的列，除了传入的交叉列以外，其它的都是固定列
            for (int i = 0; i < dtSourceTable.Columns.Count; i++)
            {
                dcSourceColumn = dtSourceTable.Columns[i];
                if (strFixFields.IndexOf(dtSourceTable.Columns[i].ColumnName) >= 0)
                {
                    dcResultColumn = new DataColumn(dcSourceColumn.ColumnName, dcSourceColumn.DataType);
                    dtResultTable.Columns.Add(dcResultColumn);
                }
            }

            //处理交叉列：根据交叉列的列名先进行分组，再加将分组结果，作为列加入结果表中
            DataTable dtGroupCross = TableGroup(dtSourceTable, strCrossField);
            for (int i = 0; i < dtGroupCross.Rows.Count; i++)
            {
                dcResultColumn = new DataColumn();
                dcResultColumn.ColumnName = dtGroupCross.Rows[i][strCrossField]?.ToString();
                dcResultColumn.DataType = typeof(decimal);
                dtResultTable.Columns.Add(dcResultColumn);
            }
            //加入合计列
            dcResultColumn = new DataColumn();
            dcResultColumn.ColumnName = "合计";
            dcResultColumn.DataType = typeof(decimal);
            dtResultTable.Columns.Add(dcResultColumn);

            //分解固定的字段
            string[] strArrFixField = strFixFields.Split(',');
            //旧行的数据初始化
            string[] strArrFieldValue = strFixFields.Split(',');
            for (int i = 0; i < strArrFieldValue.Length; i++)
            {
                strArrFieldValue[i] = "";
            }

            //源数据，进行排序
            DataTable dtSortTable = TableSort(dtSourceTable, strFixFields);

            DataRow drSortRow = null;
            DataRow drResultRow = null;
            Boolean bIsNewRow = false;
            decimal dRowSum = 0;

            for (int i = 0; i < dtSortTable.Rows.Count; i++)
            {
                drSortRow = dtSortTable.Rows[i];

                //判断是不是新行
                bIsNewRow = false;
                for (int j = 0; j < strArrFixField.Length; j++)
                {
                    if (drSortRow[strArrFixField[j]]?.ToString() != strArrFieldValue[j])
                    {
                        bIsNewRow = true;
                        break;
                    }
                }

                //是新行
                if (bIsNewRow)
                {
                    //是新行且不是第一行，加入以前生成的行,
                    if (i > 0)
                    {
                        //计算合计列
                        drResultRow["合计"] = dRowSum;
                        dtResultTable.Rows.Add(drResultRow);
                        dRowSum = 0;
                    }
                    //再生成一新行，赋固定字段的值
                    drResultRow = dtResultTable.NewRow();

                    for (int j = 0; j < strArrFixField.Length; j++)
                    {
                        drResultRow[strArrFixField[j]] = drSortRow[strArrFixField[j]];
                        strArrFieldValue[j] = drSortRow[strArrFixField[j]]?.ToString();
                    }
                }
                //交叉列赋值
                drResultRow[drSortRow[strCrossField].ToString()] = drSortRow[strCrossValueField];
                if (drSortRow[strCrossValueField] != DBNull.Value)
                    dRowSum += Math.Round(Cs01Functions.CDecimal(drSortRow[strCrossValueField]), 4, MidpointRounding.AwayFromZero);

                //加入最后一行，
                if (i == (dtSortTable.Rows.Count - 1))
                {
                    drResultRow["合计"] = dRowSum;
                    dtResultTable.Rows.Add(drResultRow);
                }
            }

            return dtResultTable;
        }

        /// <summary>
        /// 将DataTable分组计算
        /// <param name="dtSourceTable">分组计算源表</param>
        /// <param name="strGroupFields">分组字段</param>
        /// <param name="strCountFields">计数字段</param>
        /// <param name="strSumFields">求和字段</param>
        /// <param name="strAvgFields">平均值字段</param>
        /// <returns>分组计算后的DataTable</returns>
        /// </summary>
        public static DataTable TableGroupCalcute(DataTable dtSourceTable, string strGroupFields, string strCountFields, string strSumFields, string strAvgFields)
        {
            //传入的参数检查
            if (dtSourceTable.Rows.Count == 0)
                return dtSourceTable;

            if (strGroupFields.Length < 1)
                return dtSourceTable;

            //分解分组字段数组
            string[] strArrGroupField = strGroupFields.Split(',');
            //分组字段值数组
            string[] strArrGroupValue = new string[strArrGroupField.Length];

            //分解计数字段数组
            Boolean bHasCountField = false;
            string[] strArrCountField = null;
            if (strCountFields.Length > 0)
            {
                bHasCountField = true;
                strCountFields.Split(',');
            }

            //分解求和字段数组
            Boolean bHasSumField = false;
            string[] strArrSumField = null;
            decimal[] iArrSumValue = null;
            if (strSumFields.Length > 0)
            {
                bHasSumField = true;
                strArrSumField = strSumFields.Split(',');
                iArrSumValue = new decimal[strArrSumField.Length];
            }

            //分解平均字段数组
            Boolean bHasAvgField = false;
            string[] strArrAvgField = null;
            decimal[] iArrAvgValue = null;
            if (strAvgFields.Length > 0)
            {
                bHasAvgField = true;
                strArrAvgField = strAvgFields.Split(',');
                iArrAvgValue = new decimal[strArrAvgField.Length];
            }

            DataTable dtResultTable = new DataTable();
            DataColumn dcSourceColumn, dcResultColumn;

            //结果表的列处理
            for (int i = 0; i < dtSourceTable.Columns.Count; i++)
            {
                dcSourceColumn = dtSourceTable.Columns[i];
                //处理分组的列
                if (strGroupFields.IndexOf(dtSourceTable.Columns[i].ColumnName) >= 0)
                {
                    dcResultColumn = new DataColumn(dcSourceColumn.ColumnName, dcSourceColumn.DataType);
                    dtResultTable.Columns.Add(dcResultColumn);
                }

                //处理计数的列
                if (bHasCountField)
                {
                    if (strCountFields.IndexOf(dtSourceTable.Columns[i].ColumnName) >= 0)
                    {
                        dcResultColumn = new DataColumn(dcSourceColumn.ColumnName + "_COUNT", typeof(int));
                        dtResultTable.Columns.Add(dcResultColumn);
                    }
                }

                //处理求和的列
                if (bHasSumField)
                {
                    if (strSumFields.IndexOf(dtSourceTable.Columns[i].ColumnName) >= 0)
                    {
                        dcResultColumn = new DataColumn(dcSourceColumn.ColumnName + "_SUM", typeof(decimal));
                        dtResultTable.Columns.Add(dcResultColumn);
                    }
                }

                //处理平均值的列
                if (bHasAvgField)
                {
                    if (strAvgFields.IndexOf(dtSourceTable.Columns[i].ColumnName) >= 0)
                    {
                        dcResultColumn = new DataColumn(dcSourceColumn.ColumnName + "_AVG", typeof(decimal));
                        dtResultTable.Columns.Add(dcResultColumn);
                    }
                }
            }

            //源数据，进行排序
            DataTable dtSortTable = TableSort(dtSourceTable, strGroupFields);

            DataRow drSortRow = null;
            DataRow drResultRow = null;
            Boolean bIsNewRow = false;
            int iRowCount = 0;

            for (int i = 0; i < dtSortTable.Rows.Count; i++)
            {
                drSortRow = dtSortTable.Rows[i];

                //判断是不是新行
                bIsNewRow = false;
                for (int j = 0; j < strArrGroupValue.Length; j++)
                {
                    if (drSortRow[strArrGroupField[j]]?.ToString() != strArrGroupValue[j])
                    {
                        bIsNewRow = true;
                        break;
                    }
                }

                //是新行
                if (bIsNewRow)
                {
                    //是新行且不是第一行，加入以前生成的行,
                    if (i > 0)
                    {
                        //设置计数的列
                        if (bHasCountField)
                        {
                            for (int j = 0; j < strArrCountField.Length; j++)
                            {
                                drResultRow[strArrCountField[j] + "_COUNT"] = iRowCount;
                            }
                        }

                        //处理求和的列
                        if (bHasSumField)
                        {
                            for (int j = 0; j < strArrSumField.Length; j++)
                            {
                                drResultRow[strArrSumField[j] + "_SUM"] = iArrSumValue[j];
                                iArrSumValue[j] = 0;
                            }
                        }

                        //处理平均值的列
                        if (bHasAvgField)
                        {
                            for (int j = 0; j < strArrAvgField.Length; j++)
                            {
                                drResultRow[strArrAvgField[j] + "_AVG"] = Math.Round(iArrAvgValue[j] / iRowCount, 4, MidpointRounding.AwayFromZero);
                                iArrAvgValue[j] = 0;
                            }
                        }

                        //结果中加入行
                        dtResultTable.Rows.Add(drResultRow);

                        //行计数置0
                        iRowCount = 0;
                    }

                    //再生成一新行，赋分组字段的值
                    drResultRow = dtResultTable.NewRow();

                    for (int j = 0; j < strArrGroupField.Length; j++)
                    {
                        drResultRow[strArrGroupField[j]] = drSortRow[strArrGroupField[j]];
                        strArrGroupValue[j] = drSortRow[strArrGroupField[j]]?.ToString();
                    }
                }

                iRowCount++;

                //进行求和计算
                if (bHasSumField)
                {
                    for (int j = 0; j < strArrSumField.Length; j++)
                    {
                        if (drSortRow[strArrSumField[j]] == DBNull.Value) continue;
                        iArrSumValue[j] += Math.Round(Cs01Functions.CDecimal(drSortRow[strArrSumField[j]]), 4,MidpointRounding.AwayFromZero);
                    }
                }

                //进行平均值计算
                if (bHasAvgField)
                {
                    for (int j = 0; j < strArrAvgField.Length; j++)
                    {
                        if (drSortRow[strArrSumField[j]] == DBNull.Value) continue;
                        iArrAvgValue[j] += Math.Round(Cs01Functions.CDecimal(drSortRow[strArrAvgField[j]]), 4, MidpointRounding.AwayFromZero);
                    }
                }

                //最后一行
                if (i == (dtSortTable.Rows.Count - 1))
                {
                    //设置计数的列
                    if (bHasCountField)
                    {
                        for (int j = 0; j < strArrCountField.Length; j++)
                        {
                            drResultRow[strArrCountField[j] + "_COUNT"] = iRowCount;
                        }
                    }

                    //处理求和的列
                    if (bHasSumField)
                    {
                        for (int j = 0; j < strArrSumField.Length; j++)
                        {
                            drResultRow[strArrSumField[j] + "_SUM"] = iArrSumValue[j];
                        }
                    }

                    //处理平均值的列
                    if (bHasAvgField)
                    {
                        for (int j = 0; j < strArrAvgField.Length; j++)
                        {
                            drResultRow[strArrAvgField[j] + "_AVG"] = Math.Round(iArrAvgValue[j] / iRowCount, 4, MidpointRounding.AwayFromZero);
                        }
                    }
                    //加入最后一行，
                    dtResultTable.Rows.Add(drResultRow);
                }
            }

            return dtResultTable;
        }

        //string strQuerySQL = @" 
        //  SELECT AA.PATIENT_ID,
        //         AA.VISIT_ID, 
        //         AA.AMOUNT,
        //         AA.CHARGES,
        //         AA.COSTS, 
        //         AA.ORDERED_BY,
        //         AA.PERFORMED_BY 
        //  FROM INP_BILL_DETAIL AA 
        //  WHERE AA.BILLING_DATE_TIME >= TO_DATE('2019-02-27', 'YYYY-MM-DD') 
        //      AND AA.AMOUNT > 0 
        //      AND AA.ITEM_CODE = '**********' ";

        //    DataTable dtTest = Cs02ExtendTable.TableGroupCalcute(dbHelper.GetDataTable(strQuerySQL),
        //        "PATIENT_ID,VISIT_ID", "PATIENT_ID", "CHARGES,COSTS", "CHARGES,COSTS");

        //string strQuerySQL = @"
        //     SELECT A.ORDERED_BY AS DEPT_CODE,
        //          (SELECT DEPT_NAME FROM DEPT_DICT WHERE DEPT_CODE = A.ORDERED_BY) AS DEPT_NAME,
        //          (SELECT FEE_CLASS_CODE || FEE_CLASS_NAME FROM INP_RCPT_FEE_DICT WHERE FEE_CLASS_CODE = A.CLASS_ON_INP_RCPT) AS FEE_CLASS,
        //          SUM(A.CHARGES) AS CLASS_CHARGES 
        //     FROM INP_BILL_DETAIL A
        //     WHERE (A.BILLING_DATE_TIME BETWEEN TO_DATE('2019-02-27 00:00:00','yyyy-MM-dd HH24:mi:ss') 
        //         AND TO_DATE('2019-03-01 00:00:00','yyyy-MM-dd HH24:mi:ss'))
        //     GROUP BY A.ORDERED_BY,A.CLASS_ON_INP_RCPT ";

        //DataTable dtTest = Cs02ExtendTable.ChangeToCross(dbHelper.GetDataTable(strQuerySQL), "DEPT_CODE,DEPT_NAME", "FEE_CLASS", "CLASS_CHARGES");

    }
}
