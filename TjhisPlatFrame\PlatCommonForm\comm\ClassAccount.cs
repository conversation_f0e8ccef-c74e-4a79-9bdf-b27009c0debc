﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace PlatCommonForm.comm
{
    public class ClassAccount
    {
        public int accounttype;
        public string insurance_no;
        public string patient_id;
        public string name;
        public string sex;
        public DateTime date_of_birth;
        public string birth_place;
        public string nation;
        public string identity_class;
        public string insurance_type;
        public string charge_type;
        public string identity;
        public string name_phonetic;
        public string unit_in_contract;
        public decimal total_medical_costs;
        public decimal account_balance;
        public string grade_of_duty;
        public string grade_of_technic;
        public string grade_of_title;
        public string policlinic;
        public string unit;
        public string hospital;
        public string card_no;
        public string account_status;
        public DateTime expiration_date;
        public string extrainfo;
        public string id_no = "";
        public string ARMED_SERVICES = "";
        public static int of_get_account(ref ClassAccount iacctount, ref DataTable dt_amy)
        {
            //string ls_sql = "SELECT name ,sex ,date_of_birth ,birth_place ,nation ,identity_class ,insurance_type ,patient_id ,name_phonetic ,unit ,account_balance ,grade_of_duty ,grade_of_title ,grade_of_technic ,DESIGNATED_POLICLINIC ,DESIGNATED_HOSPITAL ,expiration_date ,account_status ,id_no,ARMED_SERVICES";
            //ls_sql = ls_sql + " FROM INSURANCE_ACCOUNTS ,armyman_extra_info WHERE insurance_accounts.insurance_no =armyman_extra_info.insurance_no(+) and INSURANCE_ACCOUNTS.ACCOUNT_STATUS = '0' and INSURANCE_ACCOUNTS.Id_No = '" + iacctount.id_no + "' and rownum = 1";
            string ls_sql = @"select *
   from(select name,
                sex,
                date_of_birth,
                birth_place,
                nation,
                identity_class,
                insurance_type,
                patient_id,
                name_phonetic,
                unit,
                account_balance,
                grade_of_duty,
                grade_of_title,
                grade_of_technic,
                DESIGNATED_POLICLINIC,
                DESIGNATED_HOSPITAL,
                expiration_date,
                account_status,
                id_no,
                ARMED_SERVICES,
                row_number() over(partition by patient_Id order by update_date_time desc) rn
           FROM INSURANCE_ACCOUNTS, armyman_extra_info
          WHERE insurance_accounts.insurance_no =
                armyman_extra_info.insurance_no(+)
            and INSURANCE_ACCOUNTS.ACCOUNT_STATUS = '0'
            and update_date_time is not null
            and (INSURANCE_ACCOUNTS.insurance_no = '" + iacctount.insurance_no + @"' or INSURANCE_ACCOUNTS.security_card_no = '" + iacctount.card_no + @"'))
  where rn = 1
    and rownum = 1";
            //XtraMessageBox.Show(ls_sql, "ls_sql");
            dt_amy = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_sql).Tables[0];
            if (dt_amy.Rows.Count > 0)
            {
                //iacctount.name = dt_amy.Rows[0]["name"].ToString();
                //iacctount.sex = dt_amy.Rows[0]["sex"].ToString();
                //iacctount.date_of_birth = DateTime.Parse(dt_amy.Rows[0]["date_of_birth"].ToString());
                //iacctount.birth_place = dt_amy.Rows[0]["birth_place"].ToString();
                //iacctount.nation = dt_amy.Rows[0]["nation"].ToString();
                //iacctount.identity_class = dt_amy.Rows[0]["identity_class"].ToString();
                //iacctount.insurance_type = dt_amy.Rows[0]["insurance_type"].ToString();
                iacctount.patient_id = dt_amy.Rows[0]["patient_id"].ToString();
                //iacctount.name_phonetic = dt_amy.Rows[0]["name_phonetic"].ToString();
                //iacctount.unit = dt_amy.Rows[0]["unit"].ToString();
                string ls_acct = dt_amy.Rows[0]["account_balance"].ToString();
                if (string.IsNullOrEmpty(ls_acct)) ls_acct = "0";
                iacctount.account_balance = decimal.Parse(ls_acct);
                //iacctount.grade_of_duty = dt_amy.Rows[0]["grade_of_duty"].ToString();
                //iacctount.grade_of_title = dt_amy.Rows[0]["grade_of_title"].ToString();
                //iacctount.grade_of_technic = dt_amy.Rows[0]["grade_of_technic"].ToString();
                //iacctount.policlinic = dt_amy.Rows[0]["DESIGNATED_POLICLINIC"].ToString();
                //iacctount.hospital = dt_amy.Rows[0]["DESIGNATED_HOSPITAL"].ToString();
                //iacctount.expiration_date = DateTime.Parse(dt_amy.Rows[0]["expiration_date"].ToString());
                //iacctount.account_status = (dt_amy.Rows[0]["account_status"].ToString());
                iacctount.id_no = dt_amy.Rows[0]["id_no"].ToString();
                iacctount.identity = iacctount.identity_class;
                iacctount.ARMED_SERVICES = dt_amy.Rows[0]["ARMED_SERVICES"].ToString();
            }
            

            return 0;
        }

        public static string of_get_service_class(string as_grade_of_duty, string as_grade_of_technic, string as_identity_class)
        {
            string ls_service_class = "";
            if (string.IsNullOrEmpty(as_grade_of_duty)) as_grade_of_duty = "";
            if (string.IsNullOrEmpty(as_grade_of_technic)) as_grade_of_technic = "";
            if (string.IsNullOrEmpty(as_identity_class)) as_identity_class = "";

            if (as_grade_of_duty.Equals("军委委员以上级") || as_grade_of_duty.Equals("正大区级") || as_grade_of_duty.Equals("副大区级") || as_grade_of_duty.Equals("正军级") || as_grade_of_duty.Equals("副军级") || as_grade_of_technic.Equals("特级") || as_grade_of_technic.Equals("1级") || as_grade_of_technic.Equals("2级") || as_grade_of_technic.Equals("3级"))
            {
                ls_service_class = "军以上干部";
            }
            else if (as_grade_of_duty.Equals("正师级") || as_grade_of_duty.Equals("副师级") || as_grade_of_technic.Equals("4级") || as_grade_of_technic.Equals("5级") || as_grade_of_technic.Equals("6级") || as_grade_of_technic.Equals("7级"))
            {
                ls_service_class = "师职干部";
            }
            else if (as_identity_class.Contains("抗") || as_identity_class.Contains("团"))
            {
                ls_service_class = "抗团";
            }
            else if (as_grade_of_duty.Equals("团级") || as_grade_of_technic.Equals("9级") || as_grade_of_technic.Equals("8级"))
            {
                ls_service_class = "团级干部";
            }
            else if (as_grade_of_duty.Equals("营级") || as_grade_of_duty.Equals("连级") || as_grade_of_duty.Equals("排级") || as_grade_of_technic.Equals("11级") || as_grade_of_technic.Equals("12级") || as_grade_of_technic.Equals("13级") || as_grade_of_technic.Equals("14级") || as_grade_of_technic.Equals("10级"))
            {
                ls_service_class = "团以下干部";
            }
            else
            {
                ls_service_class = "其他";
            }

            return ls_service_class;
        }

        public static string of_validate_insurance_type(string as_insurance_type)
        {
            string ls_insurance_type = "";
            switch (as_insurance_type)
            {
                case "正常转诊免费":
                case "非正常转诊免费":
                case "免费医疗":
                    ls_insurance_type = "免费医疗";
                    break;
                case "正常转诊优惠":
                case "非正常转诊优惠":
                case "优惠医疗":
                    ls_insurance_type = "优惠医疗";
                    break;
                case "正常转诊职工":
                case "非正常转诊职工":
                case "职工医疗":
                    ls_insurance_type = "职工医疗";
                    break;

            }

            return ls_insurance_type;
        }

        public static Boolean of_is_in_blacklist(ClassAccount iacctount)
        {
            string ls_Sql = "select * from  MEDICAL_CARD_BLACKLIST a where (card_no='" + iacctount.card_no + "' or card_no='" + iacctount.insurance_no + "' or insurance_no='" + iacctount.insurance_no + "' ) and( a.end_date is null or a.end_date >sysdate) ";
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql(ls_Sql).Tables[0];
            if (dt.Rows.Count > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }
}
