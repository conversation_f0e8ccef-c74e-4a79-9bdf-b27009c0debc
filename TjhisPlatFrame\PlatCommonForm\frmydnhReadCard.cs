﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using PlatCommon.SysBase;

namespace PlatCommonForm
{
    public partial class frmydnhReadCard : ParentForm
    {
        public string is_idno="";
        public frmydnhReadCard()
        {
            InitializeComponent();
        }

        private void textEdit1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                is_idno = textEdit1.Text;
                if (is_idno.Length != 15 && is_idno.Length != 18)
                {
                    XtraMessageBox.Show("身份证号录入错误！", "提示");
                    return;
                }
                Close();
            }
        }
    }
}
