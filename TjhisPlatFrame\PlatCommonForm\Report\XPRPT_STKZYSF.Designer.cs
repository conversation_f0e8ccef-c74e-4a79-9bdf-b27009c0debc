﻿namespace PlatCommonForm.Report
{
    partial class XPRPT_STKZYSF
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xlbOper = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.xlbDate = new DevExpress.XtraReports.UI.XRLabel();
            this.xlbRcptNo = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine1 = new DevExpress.XtraReports.UI.XRLine();
            this.xrLine2 = new DevExpress.XtraReports.UI.XRLine();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel9 = new DevExpress.XtraReports.UI.XRLabel();
            this.xlbPatientID = new DevExpress.XtraReports.UI.XRLabel();
            this.xlbInpNo = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel11 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel14 = new DevExpress.XtraReports.UI.XRLabel();
            this.xlbName = new DevExpress.XtraReports.UI.XRLabel();
            this.xlbDept = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel15 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel18 = new DevExpress.XtraReports.UI.XRLabel();
            this.xlbChargeType = new DevExpress.XtraReports.UI.XRLabel();
            this.xlbClinicType = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel21 = new DevExpress.XtraReports.UI.XRLabel();
            this.xlbPrice = new DevExpress.XtraReports.UI.XRLabel();
            this.xlbPriceD = new DevExpress.XtraReports.UI.XRLabel();
            this.xlbSumPrice = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel23 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel25 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel26 = new DevExpress.XtraReports.UI.XRLabel();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xlbOper,
            this.xrLabel8,
            this.xrLabel2,
            this.xlbDate,
            this.xlbRcptNo,
            this.xrLabel5,
            this.xrLine1,
            this.xrLine2,
            this.xrLabel6,
            this.xrLabel7,
            this.xrLabel1,
            this.xrLabel9,
            this.xlbPatientID,
            this.xlbInpNo,
            this.xrLabel11,
            this.xrLabel14,
            this.xlbName,
            this.xlbDept,
            this.xrLabel15,
            this.xrLabel18,
            this.xlbChargeType,
            this.xlbClinicType,
            this.xrLabel21,
            this.xlbPrice,
            this.xlbPriceD,
            this.xlbSumPrice,
            this.xrLabel23,
            this.xrLabel25,
            this.xrLabel26});
            this.Detail.HeightF = 370F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xlbOper
            // 
            this.xlbOper.LocationFloat = new DevExpress.Utils.PointFloat(561.5002F, 199.0834F);
            this.xlbOper.Name = "xlbOper";
            this.xlbOper.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xlbOper.SizeF = new System.Drawing.SizeF(102.4999F, 23.00002F);
            this.xlbOper.StylePriority.UseTextAlignment = false;
            this.xlbOper.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrLabel8
            // 
            this.xrLabel8.LocationFloat = new DevExpress.Utils.PointFloat(9.999974F, 337.2084F);
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.SizeF = new System.Drawing.SizeF(680F, 23.25F);
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            this.xrLabel8.Text = "我院将用科技保护您的财务，用科技呵护您的健康，为您的健康保驾护航！";
            this.xrLabel8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrLabel2
            // 
            this.xrLabel2.Font = new System.Drawing.Font("Times New Roman", 11F);
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(9.000026F, 33.58333F);
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(680F, 23F);
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            this.xrLabel2.Text = "住院预交金收据";
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xlbDate
            // 
            this.xlbDate.Font = new System.Drawing.Font("Times New Roman", 10F);
            this.xlbDate.LocationFloat = new DevExpress.Utils.PointFloat(24.00003F, 61.75F);
            this.xlbDate.Name = "xlbDate";
            this.xlbDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xlbDate.SizeF = new System.Drawing.SizeF(326.6667F, 23F);
            this.xlbDate.StylePriority.UseFont = false;
            this.xlbDate.StylePriority.UseTextAlignment = false;
            this.xlbDate.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xlbRcptNo
            // 
            this.xlbRcptNo.Font = new System.Drawing.Font("Times New Roman", 10F);
            this.xlbRcptNo.LocationFloat = new DevExpress.Utils.PointFloat(524.8334F, 61.75001F);
            this.xlbRcptNo.Name = "xlbRcptNo";
            this.xlbRcptNo.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xlbRcptNo.SizeF = new System.Drawing.SizeF(164.1666F, 23F);
            this.xlbRcptNo.StylePriority.UseFont = false;
            this.xlbRcptNo.StylePriority.UseTextAlignment = false;
            this.xlbRcptNo.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel5
            // 
            this.xrLabel5.LocationFloat = new DevExpress.Utils.PointFloat(418.1668F, 61.75001F);
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.SizeF = new System.Drawing.SizeF(106.6666F, 23F);
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            this.xrLabel5.Text = "收据号：";
            this.xrLabel5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLine1
            // 
            this.xrLine1.LocationFloat = new DevExpress.Utils.PointFloat(9.000026F, 84.75001F);
            this.xrLine1.Name = "xrLine1";
            this.xrLine1.SizeF = new System.Drawing.SizeF(680F, 11.33333F);
            // 
            // xrLine2
            // 
            this.xrLine2.LocationFloat = new DevExpress.Utils.PointFloat(10.00001F, 239.7084F);
            this.xrLine2.Name = "xrLine2";
            this.xrLine2.SizeF = new System.Drawing.SizeF(680F, 11.33333F);
            // 
            // xrLabel6
            // 
            this.xrLabel6.LocationFloat = new DevExpress.Utils.PointFloat(10.00001F, 253.0417F);
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.SizeF = new System.Drawing.SizeF(680F, 48.24997F);
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            this.xrLabel6.Text = "注：患者预交金收据以最后一张交款收据有效，同时前预交金收据作废。我院从2017年4月1日开始住院患者实名制管理，住院登记时未登记身份证信息的患者，请在48小时内到" +
    "住院处补录身份证信息，预期不办者，影响出院后信息查询，后果自负。";
            this.xrLabel6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrLabel7
            // 
            this.xrLabel7.LocationFloat = new DevExpress.Utils.PointFloat(9.999974F, 301.7083F);
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.SizeF = new System.Drawing.SizeF(680F, 33.24997F);
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            this.xrLabel7.Text = "2017年5月31日起，患者来我院办理住院登记时须提供准确手机号码，以便我院凭借该号码向患者推送入院及出院结帐信息。出院结帐时请携带银行卡、身份证。";
            this.xrLabel7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrLabel1
            // 
            this.xrLabel1.Font = new System.Drawing.Font("Times New Roman", 11F);
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(9F, 6.916656F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(680F, 23F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "葫芦岛市中心医院";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel9
            // 
            this.xrLabel9.LocationFloat = new DevExpress.Utils.PointFloat(24.00003F, 107.0834F);
            this.xrLabel9.Name = "xrLabel9";
            this.xrLabel9.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel9.SizeF = new System.Drawing.SizeF(51.66667F, 23.00001F);
            this.xrLabel9.Text = "ID号：";
            // 
            // xlbPatientID
            // 
            this.xlbPatientID.LocationFloat = new DevExpress.Utils.PointFloat(75.66663F, 107.0834F);
            this.xlbPatientID.Name = "xlbPatientID";
            this.xlbPatientID.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xlbPatientID.SizeF = new System.Drawing.SizeF(139.1667F, 23.00001F);
            this.xlbPatientID.StylePriority.UseTextAlignment = false;
            this.xlbPatientID.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xlbInpNo
            // 
            this.xlbInpNo.LocationFloat = new DevExpress.Utils.PointFloat(331.5F, 107.0834F);
            this.xlbInpNo.Name = "xlbInpNo";
            this.xlbInpNo.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xlbInpNo.SizeF = new System.Drawing.SizeF(122.5F, 23.00001F);
            this.xlbInpNo.StylePriority.UseTextAlignment = false;
            this.xlbInpNo.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrLabel11
            // 
            this.xrLabel11.LocationFloat = new DevExpress.Utils.PointFloat(263.1667F, 107.0834F);
            this.xrLabel11.Name = "xrLabel11";
            this.xrLabel11.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel11.SizeF = new System.Drawing.SizeF(68.33334F, 23.00001F);
            this.xrLabel11.Text = "住院号：";
            // 
            // xrLabel14
            // 
            this.xrLabel14.LocationFloat = new DevExpress.Utils.PointFloat(473.1667F, 107.0834F);
            this.xrLabel14.Name = "xrLabel14";
            this.xrLabel14.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel14.SizeF = new System.Drawing.SizeF(51.66667F, 23.00001F);
            this.xrLabel14.Text = "姓名：";
            // 
            // xlbName
            // 
            this.xlbName.LocationFloat = new DevExpress.Utils.PointFloat(524.8333F, 107.0834F);
            this.xlbName.Name = "xlbName";
            this.xlbName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xlbName.SizeF = new System.Drawing.SizeF(139.1667F, 23.00001F);
            this.xlbName.StylePriority.UseTextAlignment = false;
            this.xlbName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xlbDept
            // 
            this.xlbDept.LocationFloat = new DevExpress.Utils.PointFloat(75.66663F, 130.0834F);
            this.xlbDept.Name = "xlbDept";
            this.xlbDept.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xlbDept.SizeF = new System.Drawing.SizeF(239.1667F, 23F);
            this.xlbDept.StylePriority.UseTextAlignment = false;
            this.xlbDept.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrLabel15
            // 
            this.xrLabel15.LocationFloat = new DevExpress.Utils.PointFloat(24.00003F, 130.0834F);
            this.xrLabel15.Name = "xrLabel15";
            this.xrLabel15.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel15.SizeF = new System.Drawing.SizeF(51.66667F, 23.00001F);
            this.xrLabel15.Text = "科室：";
            // 
            // xrLabel18
            // 
            this.xrLabel18.LocationFloat = new DevExpress.Utils.PointFloat(326.5001F, 130.0834F);
            this.xrLabel18.Name = "xrLabel18";
            this.xrLabel18.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel18.SizeF = new System.Drawing.SizeF(88.33334F, 23F);
            this.xrLabel18.Text = "支付方式：";
            // 
            // xlbChargeType
            // 
            this.xlbChargeType.LocationFloat = new DevExpress.Utils.PointFloat(414.8334F, 130.0834F);
            this.xlbChargeType.Name = "xlbChargeType";
            this.xlbChargeType.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xlbChargeType.SizeF = new System.Drawing.SizeF(109.9999F, 23F);
            this.xlbChargeType.StylePriority.UseTextAlignment = false;
            this.xlbChargeType.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xlbClinicType
            // 
            this.xlbClinicType.LocationFloat = new DevExpress.Utils.PointFloat(524.8334F, 130.0834F);
            this.xlbClinicType.Name = "xlbClinicType";
            this.xlbClinicType.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xlbClinicType.SizeF = new System.Drawing.SizeF(139.1666F, 23F);
            this.xlbClinicType.StylePriority.UseTextAlignment = false;
            this.xlbClinicType.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // xrLabel21
            // 
            this.xrLabel21.LocationFloat = new DevExpress.Utils.PointFloat(109.5F, 153.0834F);
            this.xrLabel21.Name = "xrLabel21";
            this.xrLabel21.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel21.SizeF = new System.Drawing.SizeF(51.66667F, 23.00001F);
            this.xrLabel21.Text = "金额：";
            // 
            // xlbPrice
            // 
            this.xlbPrice.LocationFloat = new DevExpress.Utils.PointFloat(161.1667F, 153.0834F);
            this.xlbPrice.Name = "xlbPrice";
            this.xlbPrice.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xlbPrice.SizeF = new System.Drawing.SizeF(178.3333F, 23F);
            this.xlbPrice.StylePriority.UseTextAlignment = false;
            this.xlbPrice.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xlbPriceD
            // 
            this.xlbPriceD.LocationFloat = new DevExpress.Utils.PointFloat(161.1667F, 176.0834F);
            this.xlbPriceD.Name = "xlbPriceD";
            this.xlbPriceD.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xlbPriceD.SizeF = new System.Drawing.SizeF(178.3333F, 23F);
            this.xlbPriceD.StylePriority.UseTextAlignment = false;
            this.xlbPriceD.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xlbSumPrice
            // 
            this.xlbSumPrice.LocationFloat = new DevExpress.Utils.PointFloat(230.25F, 199.0834F);
            this.xlbSumPrice.Name = "xlbSumPrice";
            this.xlbSumPrice.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xlbSumPrice.SizeF = new System.Drawing.SizeF(148.3333F, 23F);
            this.xlbSumPrice.StylePriority.UseTextAlignment = false;
            this.xlbSumPrice.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrLabel23
            // 
            this.xrLabel23.LocationFloat = new DevExpress.Utils.PointFloat(103.5833F, 199.0834F);
            this.xrLabel23.Name = "xrLabel23";
            this.xrLabel23.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel23.SizeF = new System.Drawing.SizeF(126.6667F, 23F);
            this.xrLabel23.Text = "患者预交金累计：";
            // 
            // xrLabel25
            // 
            this.xrLabel25.LocationFloat = new DevExpress.Utils.PointFloat(378.5833F, 199.0834F);
            this.xrLabel25.Name = "xrLabel25";
            this.xrLabel25.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel25.SizeF = new System.Drawing.SizeF(35F, 23F);
            this.xrLabel25.StylePriority.UseTextAlignment = false;
            this.xrLabel25.Text = "元";
            this.xrLabel25.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrLabel26
            // 
            this.xrLabel26.LocationFloat = new DevExpress.Utils.PointFloat(473.1667F, 199.0834F);
            this.xrLabel26.Name = "xrLabel26";
            this.xrLabel26.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel26.SizeF = new System.Drawing.SizeF(88.33334F, 23.00002F);
            this.xrLabel26.Text = "收款员号：";
            // 
            // TopMargin
            // 
            this.TopMargin.HeightF = 0F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // BottomMargin
            // 
            this.BottomMargin.HeightF = 0F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // XPRPT_STKZYSF
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin});
            this.Margins = new System.Drawing.Printing.Margins(10, 10, 0, 0);
            this.PageHeight = 400;
            this.PageWidth = 718;
            this.PaperKind = System.Drawing.Printing.PaperKind.Custom;
            this.Version = "15.2";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel xlbDate;
        private DevExpress.XtraReports.UI.XRLabel xlbRcptNo;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLine xrLine1;
        private DevExpress.XtraReports.UI.XRLine xrLine2;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel9;
        private DevExpress.XtraReports.UI.XRLabel xlbPatientID;
        private DevExpress.XtraReports.UI.XRLabel xlbInpNo;
        private DevExpress.XtraReports.UI.XRLabel xrLabel11;
        private DevExpress.XtraReports.UI.XRLabel xrLabel14;
        private DevExpress.XtraReports.UI.XRLabel xlbName;
        private DevExpress.XtraReports.UI.XRLabel xlbDept;
        private DevExpress.XtraReports.UI.XRLabel xrLabel15;
        private DevExpress.XtraReports.UI.XRLabel xrLabel18;
        private DevExpress.XtraReports.UI.XRLabel xlbChargeType;
        private DevExpress.XtraReports.UI.XRLabel xlbClinicType;
        private DevExpress.XtraReports.UI.XRLabel xrLabel21;
        private DevExpress.XtraReports.UI.XRLabel xlbPrice;
        private DevExpress.XtraReports.UI.XRLabel xlbPriceD;
        private DevExpress.XtraReports.UI.XRLabel xlbSumPrice;
        private DevExpress.XtraReports.UI.XRLabel xrLabel23;
        private DevExpress.XtraReports.UI.XRLabel xrLabel25;
        private DevExpress.XtraReports.UI.XRLabel xrLabel26;
        private DevExpress.XtraReports.UI.XRLabel xlbOper;
    }
}
