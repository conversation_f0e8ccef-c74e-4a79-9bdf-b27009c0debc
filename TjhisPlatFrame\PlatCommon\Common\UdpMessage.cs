﻿//**********************************************
//说明:消息发送
//计算机名称：LINDP
//创建日期：2016/5/20 11:10:47
//作者：林大鹏
//版本号：V1.00
//**********************************************
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;

namespace PlatCommon.Common
{
    // 通过委托回调机制显示消息内容
    public delegate void ShowMessageCallBack(CustomMessage cm);
    /// <summary>
    /// UDP消息
    /// </summary>
    public class UdpMessage
    {
        private UdpClient udpClient;
        private UdpClient udpClientRec;
        private int port = 8001;
        private int recport = 8001;
        public ShowMessageCallBack showMessageCallBack;
        public ShowMessageCallBack showDoctMessageCallBack;
        public UdpMessage(int sendport, int recport, string ipaddr)
        {
            try
            {
                udpClient = new UdpClient(sendport);
                this.port = sendport;
                this.recport = recport;
                //udpClient.Connect(IPAddress.Parse(ipaddr), recport);

                udpClientRec = new UdpClient(recport);
            }
            catch (SocketException se)
            {

            }
        }
        public UdpMessage(int port)
        {
            this.port = port;
        }
        //发送数据
        public void SendMessage(CustomMessage p)
        {
            //UdpClient myUdpClient = new UdpClient();
            IPEndPoint iep = new IPEndPoint(IPAddress.Broadcast, recport);
            //允许发送和接收广播数据报
            //myUdpClient.EnableBroadcast = true;
            udpClient.EnableBroadcast = true;
            Byte[] sendBytes = Encoding.GetEncoding("gbk").GetBytes(Serialize.ScriptSerialize<CustomMessage>(p));
            udpClient.Send(sendBytes, sendBytes.Length,iep);
            //myUdpClient.Send(sendBytes, sendBytes.Length, iep);
        }
        //接收数据
        //public Byte[] Receive()
        //{
        //    IPEndPoint RemoteIpEndPoint = new IPEndPoint(IPAddress.Any, 0);
        //    return udpClientRec.Receive(ref RemoteIpEndPoint);
        //}
        //
        public void ReceiveMsg()
        {

            while (true)
            {
                try
                {
                    //IPEndPoint RemoteIpEndPoint = new IPEndPoint(IPAddress.Any, 0);
                    //在本机指定的端口接收
                    //udpClient = new UdpClient(port);
                    IPEndPoint remote = null;
                    Byte[] receiveBytes = udpClientRec.Receive(ref remote);
                    //Byte[] receiveBytes = udpClient.Receive(ref remote);
                    CustomMessage pn = new CustomMessage();

                    string returnData = Encoding.GetEncoding("gbk").GetString(receiveBytes);//Encoding.ASCII.GetString(receiveBytes);
                    pn = (CustomMessage)Serialize.ScriptDeserialize<CustomMessage>(returnData);
                    showMessageCallBack.Invoke(pn);

                }
                catch (Exception e)
                {

                }
            }
        }

    }

}
