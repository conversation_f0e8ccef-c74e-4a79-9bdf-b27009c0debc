﻿using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using System.Data;
using System.Windows.Forms;

namespace PlatCommonForm.Report
{
    public partial class XtraReportPaymentDbgj : DevExpress.XtraReports.UI.XtraReport
    {
        public XtraReportPaymentDbgj()
        {
            InitializeComponent();
            
        }
        public XtraReportPaymentDbgj(string _RCPT_NO, string _FLAG)
        {
            InitializeComponent();
            Data_Clear();
            Data_Set(_RCPT_NO, _FLAG);
        }
        private void Data_Set( string _RCPT_NO , string _FLAG)
        {
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            string strPREPAYMENT_RCPT = "SELECT A.TRANSACT_DATE ,  A.PATIENT_ID , A.VISIT_ID , A.AMOUNT , " +
                "A.<PERSON>Y_<PERSON>Y , A.TRANSACT_TYPE , <PERSON><PERSON>_NO , <PERSON>.VISIT_ID , " +
                "A.CHECK_NO , <PERSON><PERSON> , <PERSON>.RCPT_NO ,  " +
                "B.INP_NO , B.ADMISSION_DATE_TIME , B.CHARGE_TYPE , B.DEPT_ADMISSION_TO , " +
                "C.NAME , C.SEX , C.PHONE_NUMBER_HOME , D.DEPT_NAME" +  
                " FROM PREPAYMENT_RCPT A , PAT_VISIT B , PAT_MASTER_INDEX C , DEPT_DICT D" +
                " WHERE A.PATIENT_ID = B.PATIENT_ID AND A.VISIT_ID = B.VISIT_ID AND A.PATIENT_ID = C.PATIENT_ID " +
                " AND B.DEPT_ADMISSION_TO = D.DEPT_CODE " +
                " AND RCPT_NO ='"+ _RCPT_NO + "'";
            DataSet ds = spc.GetList( strPREPAYMENT_RCPT );
            if (ds != null && ds.Tables[0].Rows.Count > 0)
            {
                this.xrLabel_KPRQ.Text = ds.Tables[0].Rows[0]["TRANSACT_DATE"].ToString();  
                this.xrLabel_BRIDH.Text = ds.Tables[0].Rows[0]["PATIENT_ID"].ToString();
                this.xrLabel_XM.Text = ds.Tables[0].Rows[0]["NAME"].ToString();
                this.xrLabel_LXDH.Text = ds.Tables[0].Rows[0]["PHONE_NUMBER_HOME"].ToString();

                string strAMOUNT = ds.Tables[0].Rows[0]["AMOUNT"].ToString() ;

                //this.xrLabel_DXJE.Text = DaXie(strAMOUNT );
                this.xrLabel_DXJE.Text = CmycurD(Convert.ToDecimal(strAMOUNT));
                this.xrLabel_ZH.Text = ds.Tables[0].Rows[0]["CHECK_NO"].ToString();
                this.xrLabel_KHH.Text = ds.Tables[0].Rows[0]["BANK"].ToString();
                this.xrLabel_SKR.Text = ds.Tables[0].Rows[0]["OPERATOR_NO"].ToString();
                this.xrLabel_GH.Text = ds.Tables[0].Rows[0]["OPERATOR_NO"].ToString();

                this.xrLabel_SJH.Text = ds.Tables[0].Rows[0]["RCPT_NO"].ToString();
                this.xrLabel_RYKS.Text = ds.Tables[0].Rows[0]["DEPT_NAME"].ToString();
                this.xrLabel_ZFFS.Text = ds.Tables[0].Rows[0]["PAY_WAY"].ToString();
                this.xrLabel_XB.Text = ds.Tables[0].Rows[0]["SEX"].ToString();
                this.xrLabel_LB.Text = ds.Tables[0].Rows[0]["TRANSACT_TYPE"].ToString();
                this.xrLabel_ZYH.Text = ds.Tables[0].Rows[0]["INP_NO"].ToString();
                this.xrLabel_JE.Text = "￥" + ds.Tables[0].Rows[0]["AMOUNT"].ToString() +"   "+ ds.Tables[0].Rows[0]["CHARGE_TYPE"].ToString();

                if ( !string.IsNullOrEmpty(_FLAG))
                { 
                    this.xrLabel_DEMO.Text = "补打收据，补打人:" + PlatCommon.SysBase.SystemParm.LoginUser.NAME; 
                }
            }
            else
            {
                MessageBox.Show( "没有找到预交金收据号为: " + _RCPT_NO + "数据" ,"温馨提示"); 
            }
            
        }
        private void Data_Clear()
        {
            this.xrLabel_KPRQ.Text = "";
            this.xrLabel_BRIDH.Text = "";
            this.xrLabel_XM.Text = "";
            this.xrLabel_LXDH.Text = "";
            this.xrLabel_DXJE.Text = "";
            this.xrLabel_ZH.Text = "";
            this.xrLabel_KHH.Text = "";
            this.xrLabel_SKR.Text = "";
            this.xrLabel_GH.Text = "";

            this.xrLabel_SJH.Text = "";
            this.xrLabel_RYKS.Text = "";
            this.xrLabel_ZFFS.Text = "";
            this.xrLabel_XB.Text = "";
            this.xrLabel_LB.Text = "";
            this.xrLabel_ZYH.Text = "";
            this.xrLabel_JE.Text = "";
            this.xrLabel_DEMO.Text = ""; 
        }

        /// <summary>
        /// 大小写转换
        /// </summary>    
        private string DaXie(string money)
        {
            //将小写金额转换成大写金额
            double MyNumber = Convert.ToDouble(money);
            String[] MyScale = { "分", "角", "元", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿", "拾", "佰", "仟", "兆", "拾", "佰", "仟" };
            String[] MyBase = { "零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖" };
            String
             M = "";
            bool
             isPoint = false;
            if (money.IndexOf(".") != -1)
            {
                money =
                 money.Remove(money.IndexOf("."), 1);
                isPoint = true;
            }
            for (int
             i =
             money.Length;
             i > 0;
             i--)
            {
                int MyData = Convert.ToInt16(money[money.Length -
                 i].ToString());
                M += MyBase[MyData];
                if (isPoint == true)
                {
                    M += MyScale[i - 1];
                }
                else
                {
                    M += MyScale[i + 1];
                }
            }
            return
             M;
        }
        /// <summary> 
        /// 转换人民币大小金额 
        /// </summary> 
        /// <param name="num">金额</param> 
        /// <returns>返回大写形式</returns> 
        public static string CmycurD(decimal num)
        {
            string str1 = "零壹贰叁肆伍陆柒捌玖";            //0-9所对应的汉字 
            string str2 = "万仟佰拾亿仟佰拾万仟佰拾元角分"; //数字位所对应的汉字 
            string str3 = "";    //从原num值中取出的值 
            string str4 = "";    //数字的字符串形式 
            string str5 = "";  //人民币大写金额形式 
            int i;    //循环变量 
            int j;    //num的值乘以100的字符串长度 
            string ch1 = "";    //数字的汉语读法 
            string ch2 = "";    //数字位的汉字读法 
            int nzero = 0;  //用来计算连续的零值是几个 
            int temp;            //从原num值中取出的值 

            num = Math.Round(Math.Abs(num), 2);    //将num取绝对值并四舍五入取2位小数 
            str4 = ((long)(num * 100)).ToString();        //将num乘100并转换成字符串形式 
            j = str4.Length;      //找出最高位 
            if (j > 15) { return "溢出"; }
            str2 = str2.Substring(15 - j);   //取出对应位数的str2的值。如：200.55,j为5所以str2=佰拾元角分 

            //循环取出每一位需要转换的值 
            for (i = 0; i < j; i++)
            {
                str3 = str4.Substring(i, 1);          //取出需转换的某一位的值 
                temp = Convert.ToInt32(str3);      //转换为数字 
                if (i != (j - 3) && i != (j - 7) && i != (j - 11) && i != (j - 15))
                {
                    //当所取位数不为元、万、亿、万亿上的数字时 
                    if (str3 == "0")
                    {
                        ch1 = "";
                        ch2 = "";
                        nzero = nzero + 1;
                    }
                    else
                    {
                        if (str3 != "0" && nzero != 0)
                        {
                            ch1 = "零" + str1.Substring(temp * 1, 1);
                            ch2 = str2.Substring(i, 1);
                            nzero = 0;
                        }
                        else
                        {
                            ch1 = str1.Substring(temp * 1, 1);
                            ch2 = str2.Substring(i, 1);
                            nzero = 0;
                        }
                    }
                }
                else
                {
                    //该位是万亿，亿，万，元位等关键位 
                    if (str3 != "0" && nzero != 0)
                    {
                        ch1 = "零" + str1.Substring(temp * 1, 1);
                        ch2 = str2.Substring(i, 1);
                        nzero = 0;
                    }
                    else
                    {
                        if (str3 != "0" && nzero == 0)
                        {
                            ch1 = str1.Substring(temp * 1, 1);
                            ch2 = str2.Substring(i, 1);
                            nzero = 0;
                        }
                        else
                        {
                            if (str3 == "0" && nzero >= 3)
                            {
                                ch1 = "";
                                ch2 = "";
                                nzero = nzero + 1;
                            }
                            else
                            {
                                if (j >= 11)
                                {
                                    ch1 = "";
                                    nzero = nzero + 1;
                                }
                                else
                                {
                                    ch1 = "";
                                    ch2 = str2.Substring(i, 1);
                                    nzero = nzero + 1;
                                }
                            }
                        }
                    }
                }
                if (i == (j - 11) || i == (j - 3))
                {
                    //如果该位是亿位或元位，则必须写上 
                    ch2 = str2.Substring(i, 1);
                }
                str5 = str5 + ch1 + ch2;

                if (i == j - 1 && str3 == "0")
                {
                    //最后一位（分）为0时，加上“整” 
                    str5 = str5 + '整';
                }
            }
            if (num == 0)
            {
                str5 = "零元整";
            }
            return str5;
        }

        /**/
        /// <summary> 
        /// 一个重载，将字符串先转换成数字在调用CmycurD(decimal num) 
        /// </summary> 
        /// <param name="num">用户输入的金额，字符串形式未转成decimal</param> 
        /// <returns></returns> 
        public static string CmycurD(string numstr)
        {
            try
            {
                decimal num = Convert.ToDecimal(numstr);
                return CmycurD(num);
            }
            catch
            {
                return "非数字形式！";
            }
        }
    }
}
