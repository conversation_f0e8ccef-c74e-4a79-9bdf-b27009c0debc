﻿using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using System.Data;

namespace PlatCommonForm.Report
{
    public partial class XPRPT_STKZYSF : DevExpress.XtraReports.UI.XtraReport
    {
        public XPRPT_STKZYSF()
        {
            InitializeComponent();
        }
        public XPRPT_STKZYSF(DataSet ds ,string hospital)
        {
            InitializeComponent();
            xrLabel1.Text = hospital;
            SetPrint(ds);
        }
        public void SetPrint(DataSet dtrcpt)
        {
           if (dtrcpt.Tables[0].Rows.Count > 0)
           {
               this.xlbDate.Text = dtrcpt.Tables[0].Rows[0]["TRANSACT_DATE"]==DBNull.Value?"":dtrcpt.Tables[0].Rows[0]["TRANSACT_DATE"].ToString();
               this.xlbRcptNo.Text = dtrcpt.Tables[0].Rows[0]["RCPT_NO"] == DBNull.Value ? "" : dtrcpt.Tables[0].Rows[0]["RCPT_NO"].ToString();
               this.xlbPatientID.Text = dtrcpt.Tables[0].Rows[0]["PATIENT_ID"] == DBNull.Value ? "" : dtrcpt.Tables[0].Rows[0]["PATIENT_ID"].ToString();
               this.xlbInpNo.Text = dtrcpt.Tables[0].Rows[0]["INP_NO"] == DBNull.Value ? "" : dtrcpt.Tables[0].Rows[0]["INP_NO"].ToString();
               this.xlbName.Text = dtrcpt.Tables[0].Rows[0]["NAME"] == DBNull.Value ? "" : dtrcpt.Tables[0].Rows[0]["NAME"].ToString();
               this.xlbDept.Text = dtrcpt.Tables[0].Rows[0]["DEPTNAME"] == DBNull.Value ? "" : dtrcpt.Tables[0].Rows[0]["DEPTNAME"].ToString();
               this.xlbChargeType.Text = dtrcpt.Tables[0].Rows[0]["PAY_WAY"] == DBNull.Value ? "" : dtrcpt.Tables[0].Rows[0]["PAY_WAY"].ToString();
               this.xlbClinicType.Text = dtrcpt.Tables[0].Rows[0]["CHARGE_TYPE"] == DBNull.Value ? "" : dtrcpt.Tables[0].Rows[0]["CHARGE_TYPE"].ToString();
               this.xlbPrice.Text = dtrcpt.Tables[0].Rows[0]["AMOUNT"] == DBNull.Value ? "0" : dtrcpt.Tables[0].Rows[0]["AMOUNT"].ToString();
               //string xlbPriceD = dtrcpt.Tables[0].Rows[0]["AMOUNT"] == DBNull.Value ? "" : dtrcpt.Tables[0].Rows[0]["TRANSACT_DATE"].ToString();
               xlbSumPrice.Text = dtrcpt.Tables[0].Rows[0]["SUMAMOUNT"] == DBNull.Value ? "" : dtrcpt.Tables[0].Rows[0]["SUMAMOUNT"].ToString();
               xlbOper.Text = dtrcpt.Tables[0].Rows[0]["OPERATOR_NO"] == DBNull.Value ? "" : dtrcpt.Tables[0].Rows[0]["OPERATOR_NO"].ToString();
               //if (string.IsNullOrEmpty(gt_charges))
               //{ gt_charges = "0"; }
               decimal gs_charges = Math.Round(Convert.ToDecimal(xlbPrice.Text), 4);
               if (gs_charges >= 0)
               {
                   this.xlbPriceD.Text = DaXie(xlbPrice.Text);
               }
               else
               {
                   this.xlbPriceD.Text = "负" + DaXie(Math.Abs(gs_charges).ToString());
               }
               
           }


       }

        /// <summary>
        /// 大小写转换
        /// </summary>    
        private string DaXie(string money)
        {
            //将小写金额转换成大写金额
            double MyNumber = Convert.ToDouble(money);
            String[] MyScale = { "分", "角", "元", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿", "拾", "佰", "仟", "兆", "拾", "佰", "仟" };
            String[] MyBase = { "零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖" };
            String
             M = "";
            bool
             isPoint = false;
            if (money.IndexOf(".") != -1)
            {
                money =
                 money.Remove(money.IndexOf("."), 1);
                isPoint = true;
            }
            for (int
             i =
             money.Length;
             i > 0;
             i--)
            {
                int MyData = Convert.ToInt16(money[money.Length -
                 i].ToString());
                M += MyBase[MyData];
                if (isPoint == true)
                {
                    M += MyScale[i - 1];
                }
                else
                {
                    M += MyScale[i + 1];
                }
            }
            return
             M;
        }
    }
}
