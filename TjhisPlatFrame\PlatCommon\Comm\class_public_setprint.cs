﻿
using PlatCommon.Comm;
using System;
using System.Collections.Generic;
using System.Drawing.Printing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace PlatCommon.Comm
{
     public class class_public_setprint
    {


        /// <summary>
        /// 加载打印机列表
        /// </summary>
        public static void print_load_printname(string printname)
        {
            PrintDocument print = new PrintDocument();
            string sDefault = print.PrinterSettings.PrinterName;//默认打印机名
            int ls_find = 0;

            foreach (string sPrint in PrinterSettings.InstalledPrinters)//获取所有打印机名称
            {

                if (sPrint == printname)
                {
                    ls_find = SetDefaultPrinter(printname);
                }
            }

            if (ls_find.Equals("0"))
            {
                PrintDialog printDialog1 = new System.Windows.Forms.PrintDialog();
                if (printDialog1.ShowDialog() == DialogResult.OK)//弹出选择印表机的窗体
                {
                }
            }
        }

        //设置默认打印机 
        public static int SetDefaultPrinter(string printname)
        {
            if (Externs.SetDefaultPrinter(printname)) //设置默认打印机
            {
                return 0;
            }
            else
            {
                return -1; ;
            }
        }

        //设置默认打印机 
        public static string  GetDefaultPrinter()
        {
            string printname;
            PrintDocument print = new PrintDocument();
            printname = print.PrinterSettings.PrinterName;//默认打印机名
            return printname;
        }

    }
}
