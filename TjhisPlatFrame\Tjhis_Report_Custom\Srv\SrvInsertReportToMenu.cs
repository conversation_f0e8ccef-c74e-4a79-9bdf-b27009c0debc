﻿using System;
using System.Data;
using Tjhis.Report.Custom.Common;

namespace Tjhis.Report.Custom.Srv
{
    public class SrvInsertReportToMenu
    {     

        public int InsertToMenu(string reportType, string reportName, string reportID)
        {  
            int dictID = 0;
            string sql = @"select max(to_number(id)+1 )from nuradm.report_form_dict t";  //获取最大ID值
            dictID = int.Parse(CommDataBase.ExecuteScalar(sql).ToString());

            sql = $@"select count(*) from nuradm.report_form_dict t where t.open_param = '{reportID}' and PARENT_FORM = '{reportType}'";  //获取在同一报表集合内是否已经存在，如果已经存在直接返回
            int count = int.Parse(CommDataBase.ExecuteScalar(sql).ToString());
            if (count > 0) return 0;

            string insterSql = $@"insert into nuradm.report_form_dict (ID, PARENT_FORM, FORM_TEXT, OPEN_FORM, FORM_DLL, OPEN_PARAM, SERIAL_NO, FORM_VISIBLE, FORM_GROUP)
                             values ('{dictID}', '{reportType}', '{reportName}', 'TJHIS.Report.Custom.frmStatisticalQueryDoc', 'TJHIS_Report_Custom.dll', '{reportID}', 99, '1', '0')";

            return CommDataBase.ExecuteWithTrans(insterSql);
        }

        public DataSet GetMenusByReportID(string reportID)
        {
            string sql = $@"select * from nuradm.report_form_dict where OPEN_PARAM ='{reportID}'";
            return CommDataBase.GetDataSet(sql, "report_form_dict");
        }

        public int SaveDate(DataSet ds)
        {
            return CommDataBase.SaveDataWithTrans(ds);
        }
        /// <summary>
        /// 创建字典
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public DataTable CreateDict(string str)
        {
            //1-wei;2-wei;3-wei
            string[] items = str.Split(';');
            if (items.Length <= 0) return null;

            DataTable dtDict = new DataTable();
            dtDict.Columns.Add("ITEM_NAME", Type.GetType("System.String"));
            dtDict.Columns.Add("ITEM_VALUE", Type.GetType("System.String"));

            foreach (string item in items)
            {
                if (!string.IsNullOrEmpty(item))
                    dtDict.Rows.Add(item.Split('-'));
            }

            return dtDict;
        }
    }
}
