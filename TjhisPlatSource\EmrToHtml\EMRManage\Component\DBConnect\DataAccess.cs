﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Collections;
using System.Data.Common;

namespace Utility
{
   public class DataAccess
    {
       private  static IDataAccess dataBase = new OracleDataAccess(false);
       //private static IDataAccess dataBase = new MicrosoftEnterprise();
       //private static IDataAccess dataBase = new OledbDataAccess(false);

       public static DbConnection GetDbConnection()
       {
           return dataBase.GetDbConnection();
       }
        #region IDataAccess 成员

        public static DataTable GetDataTable(string sql)
        {
            return dataBase.GetDataTable(sql);
        }

        public static DataTable GetDataTable(System.Data.Common.DbCommand dbCommand)
        {
            return dataBase.GetDataTable(dbCommand);
        }

        public static bool ExecuteNonQuery(string sql)
        {
            return dataBase.ExecuteNonQuery(sql);
        }

        public static bool ExecuteNonQuery(System.Data.Common.DbCommand dbCommand)
        {
            return dataBase.ExecuteNonQuery(dbCommand);
        }

        public static object ExecuteScalar(string sql)
        {
            return dataBase.ExecuteScalar(sql);
        }

        public static object ExecuteScalar(System.Data.Common.DbCommand dbCommand)
        {
            return dataBase.ExecuteScalar(dbCommand);
        }

        public static bool ExecuteTransaction(List<string> sql)
        {
            return dataBase.ExecuteTransaction(sql);
        }

        public static bool ExecuteTransaction(List<System.Data.Common.DbCommand> dbCommands)
        {
            return dataBase.ExecuteTransaction(dbCommands);
        }
        public static bool ExecuteTransaction(ArrayList sqlCommands, CustomCommandType type)
        {
            return dataBase.ExecuteTransaction(sqlCommands, type);
        }
        #endregion
    }
}
