﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using Oracle.ManagedDataAccess.Client;
using Oracle.ManagedDataAccess.Types;

namespace Utility.OracleODP
{
    /// <summary>
    /// 数据库访问基类
    /// </summary>
    public class OracleBaseClass : IDisposable, Utility.TJ_DataBase_Operation_Interface
    {
        //析构方法的调用状态 ， true 为正常调用 false为析构函数调用
        private bool IsDisposed = false;
        #region 官网高效连接，不能跟踪
        //数据库连接
        public OracleConnection connection;
        //数据读取器
        public OracleDataReader dataReader;
        //数据适配器
        public OracleDataAdapter dataAdapter;
        //数据命令控制器
        public OracleCommand command;
        //数据库事务控制器
        public OracleTransaction Transaction;
        #endregion
        #region 针对.net的客户端连接，OracleClient，调试可跟踪
        //数据库连接
        public System.Data.OracleClient.OracleConnection connectionClient;
        //数据读取器
        public System.Data.OracleClient.OracleDataReader dataReaderClient;
        //数据适配器
        public System.Data.OracleClient.OracleDataAdapter dataAdapterClient;
        //数据命令控制器
        public System.Data.OracleClient.OracleCommand commandClient;
        //数据库事务控制器
        public System.Data.OracleClient.OracleTransaction TransactionClient;
        #region 第三种连接方式 by lions 2019-05-15 解决某些字符集问题
        public Devart.Data.Oracle.OracleConnection connectionDev;
        public Devart.Data.Oracle.OracleDataReader dataReaderDev;
        public Devart.Data.Oracle.OracleDataAdapter dataAdapterDev;
        public Devart.Data.Oracle.OracleCommand commandDev;
        public Devart.Data.Oracle.OracleTransaction TransactionDev;
        #endregion
        #endregion
        //定义数据集
        public DataSet dataset;
        //数据库连接字符串(web.config来配置)，可以动态更改connectionString支持多数据库.		
        public static string connectionString
        {
            get
            {
                return UntilityConstant.DataConnectionString;
            }
        }
        /// <summary>构造函数
        /// 构造函数
        /// </summary>
        public OracleBaseClass()
        {
            if (!String.IsNullOrEmpty(connectionString))
            {
                //梁吉 2016-06-07 判断是否新的连接形式
                if (Utility.UntilityConstant.DBConnectionMode == 0)
                {
                    //使用公共连接
                    connectionClient = new System.Data.OracleClient.OracleConnection(connectionString);
                }
                else if (UntilityConstant.DBConnectionMode == 2)
                {
                    connectionDev = new Devart.Data.Oracle.OracleConnection(connectionString);
                }
                else
                {
                    connection = new OracleConnection(connectionString);
                }
            }
            else
            {
                throw new Exception("数据库连接串为空");
            }
        }
        /// <summary>构造函数
        /// 构造函数
        /// </summary>
        /// <param name="DBConnectString">ODP数据库连接串，格式如：Data Source=(DESCRIPTION =(ADDRESS_LIST =(ADDRESS = (PROTOCOL = TCP)(HOST = *************)(PORT = 1521)))(CONNECT_DATA =(SERVICE_NAME = develop)));User ID=system;Password=*******;</param>
        public OracleBaseClass(String DBConnectString)
        {
            //获取OracleBase_Settings.settings 下的DBConnectString 字符串 作为连接串
            if (!String.IsNullOrEmpty(DBConnectString))
            {
                //梁吉 2016-06-07 判断是否新的连接形式
                if (UntilityConstant.DBConnectionMode == 0)
                {
                    //使用公共连接
                    connectionClient = new System.Data.OracleClient.OracleConnection(DBConnectString);
                }
                else if (UntilityConstant.DBConnectionMode == 2)
                {
                    connectionDev = new Devart.Data.Oracle.OracleConnection(DBConnectString);
                }
                else
                {
                    connection = new OracleConnection(DBConnectString);
                }
            }
            else
            {
                throw new Exception("传入数据库初始化连接串为空");
            }
        }
        /// <summary>析构函数
        /// 析构函数
        /// </summary>
        ~OracleBaseClass()
        {
            Dispose(false);
        }
        /// <summary>实现资源释IDisposable放接口
        /// 实现资源释IDisposable放接口
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        /// <summary>为析构函数调用重写资源释IDisposable放接口Dispose()方法
        /// 为析构函数调用重写资源释IDisposable放接口Dispose()方法
        /// </summary>
        /// <param name="Disposing"></param>
        protected virtual void Dispose(bool Disposing)
        {
            if (IsDisposed)
            {
                return;
            }
            if (!IsDisposed)
            {
                if (Disposing)
                { //清理托管资源
                    if (connection != null)
                        connection.Dispose();
                    if (dataAdapter != null)
                        dataAdapter.Dispose();
                    if (this.dataReader != null)
                        dataReader.Dispose();
                    if (this.Transaction != null)
                        Transaction.Dispose();
                    if (dataset != null)
                        dataset.Dispose();
                    if (command != null)
                        command.Dispose();
                    if (connectionClient != null)
                        connectionClient.Dispose();
                    if (dataAdapterClient != null)
                        dataAdapterClient.Dispose();
                    if (commandClient != null)
                        commandClient.Dispose();
                    if (dataReaderClient != null)
                        dataReaderClient.Dispose();
                    if (TransactionClient != null)
                        TransactionClient.Dispose();
                    if (commandDev != null)
                        commandDev.Dispose();
                    if (connectionDev != null)
                        connectionDev.Dispose();
                    if (dataAdapterDev != null)
                        dataAdapterDev.Dispose();
                    if (dataReaderDev != null)
                        dataReaderDev.Dispose();
                    if (TransactionDev != null)
                        TransactionDev.Dispose();
                }
                //清理非托管资源 
            }
            IsDisposed = true;
        }
        /// <summary>打开数据库
        /// 打开数据库
        /// </summary>
        /// <returns>true or false</returns>
        public bool OpenDB()
        {
            try
            {
                if (UntilityConstant.DBConnectionMode == 0)
                {
                    //使用公共连接
                    //if (connectionClient.State!= ConnectionState.Open)
                    connectionClient.Open();
                }
                else if (UntilityConstant.DBConnectionMode == 2)
                {
                    connectionDev.Open();
                }
                else
                {
                    connection.Open();
                }

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception("打开数据库连接失败!" , ex);
            }
        }
        /// <summary>关闭数据库
        /// 关闭数据库
        /// </summary>
        /// <returns>true or false</returns>
        public bool CloseDB()
        {
            try
            {
                if (UntilityConstant.DBConnectionMode == 0)
                {
                    connectionClient.Close();
                }
                else if (UntilityConstant.DBConnectionMode == 2)
                {
                    connectionDev.Close();
                }
                else
                {
                    connection.Close();
                }

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception("关闭数据库失败：" + ex.Message, ex);
            }
        }
        /// <summary>
        /// 执行一条计算查询结果语句，返回查询结果（object）。
        /// </summary>
        /// <param name="SQLString">计算查询结果语句</param>
        /// <returns>查询结果（object）</returns>
        public object GetSingle(string SQLString)
        {
            DataSet ds = SelectDataSet(SQLString);
            if(ds!=null && ds.Tables.Count>0 && ds.Tables[0].Rows.Count > 0)
            {
                return ds.Tables[0].Rows[0][0];
            }
            else
            {
                return null;
            }
            //try
            //{
            //    if (UntilityConstant.DBConnectionMode == 0)
            //    {
            //        System.Data.OracleClient.OracleCommand command = new System.Data.OracleClient.OracleCommand(SQLString, connectionClient);
            //        object obj = command.ExecuteReader();
            //        if ((Object.Equals(obj, null)) || (Object.Equals(obj, System.DBNull.Value)))
            //        {
            //            return "0";
            //        }
            //        else
            //        {
            //            return obj;
            //        }
            //    }
            //    else
            //    {
            //        OracleCommand command = new OracleCommand(SQLString, connection);
            //        object obj = command.ExecuteReader();
            //        if ((Object.Equals(obj, null)) || (Object.Equals(obj, System.DBNull.Value)))
            //        {
            //            return "0";
            //        }
            //        else
            //        {
            //            return obj;
            //        }
            //    }

            //}
            //catch (OracleException e)
            //{
            //    return "-1";
            //    //throw new Exception("读取数据失败，SQL：" + SQLString);
            //}

        }
        #region 查询方法
        /// <summary>查询数据
        /// 查询数据
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <param name="Parameters">SQL对应参数列表</param>
        /// <returns>数据读取器</returns>
        public OracleDataReader SelectData(String sqlString, List<OracleParameter> Parameters)
        {
            try
            {
                if (Parameters == null)
                {
                    return SelectData(sqlString);
                }
                else
                {
                    OracleCommand command = connection.CreateCommand();
                    command.CommandText = sqlString;
                    foreach (OracleParameter var in Parameters)
                    {
                        command.Parameters.Add(var);
                    }
                    OracleDataReader ldreader = command.ExecuteReader();
                    return ldreader;

                }

            }
            catch (Exception ex)
            {
                String ParameterString = "";
                foreach (OracleParameter var in Parameters)
                {
                    ParameterString += "参数：" + var.ParameterName + " 值：" + var.Value + "\r\n";
                }
                throw new Exception("读取数据失败，SQL：" + sqlString + ",参数内容：" + ParameterString, ex);
            }
        }
        /// <summary>查询数据
        /// 查询数据
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <returns>数据库读取器</returns>
        public OracleDataReader SelectData(String sqlString)
        {
            try
            {
                OracleCommand command = new OracleCommand(sqlString, connection);
                OracleDataReader ldreader = command.ExecuteReader();
                return ldreader;
            }
            catch (Exception ex)
            {
                throw new Exception("读取数据失败，SQL：" + sqlString, ex);
            }
        }
        /// <summary>获取数据集
        /// 获取数据集
        /// 查询SQL存在datatable的DisplayExpression属性
        /// 查询SQL存在datatable的ExtendedProperties属性"SQL"关键字对应的值;
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <returns>数据集，默认tablename为“table”</returns>
        public DataSet SelectDataSet(String sqlString)
        {
            DataSet ds = new DataSet();
            ds.RemotingFormat = SerializationFormat.Binary;
            try
            {
                if (UntilityConstant.DBConnectionMode == 0)
                {
                    if (commandClient != null)
                    {
                        //if (commandClient.Transaction == null)
                        //{
                        //    commandClient.Transaction = TransactionClient;
                        //}
                        commandClient.CommandText = sqlString;
                        commandClient.Parameters.Clear();
                    }
                    else
                    {
                        commandClient = new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                    }
                    dataAdapterClient = new System.Data.OracleClient.OracleDataAdapter(commandClient);
                    dataAdapterClient.Fill(ds, "table");
                }
                else if (UntilityConstant.DBConnectionMode == 2)
                {
                    if (commandDev != null)
                    {
                        //if (commandClient.Transaction == null)
                        //{
                        //    commandClient.Transaction = TransactionClient;
                        //}
                        commandDev.CommandText = sqlString;
                        commandDev.Parameters.Clear();
                    }
                    else
                    {
                        commandDev = new Devart.Data.Oracle.OracleCommand(sqlString,connectionDev); ;
                    }
                    dataAdapterDev = new Devart.Data.Oracle.OracleDataAdapter(commandDev);
                    dataAdapterDev.Fill(ds, "table");
                }
                else
                {
                    if (command != null)
                    {
                        //if (command.Transaction == null)
                        //    command.Transaction = Transaction;
                        command.CommandText = sqlString;
                        command.Parameters.Clear();
                    }
                    else
                        command = new OracleCommand(sqlString, connection);
                    dataAdapter = new OracleDataAdapter(command);
                    dataAdapter.Fill(ds, "table");

                }
                ds.Tables["table"].ExtendedProperties.Add("SQL", sqlString);
                return ds;
            }
            catch (Exception ex)
            {
                Utility.LogFile.WriteLogAuto("获取数据集失败，SQL:" + sqlString + ex.Message, "OralceBaseClass");
                throw new Exception("获取数据集失败，SQL:" + sqlString, ex);
            }
        }
        /// <summary>获取数据集
        /// 获取数据集
        /// 查询SQL存在datatable的DisplayExpression属性
        /// 查询SQL存在datatable的ExtendedProperties属性"SQL"关键字对应的值;
        /// 查询SQL的参数存在datatable的ExtendedProperties属性"Parameters"关键字对应的值 类型为;List<OracleParameter> Parameters
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <param name="Parameters">参数列表</param>
        /// <returns>数据集，默认tablename为“table”</returns>
        public DataSet SelectDataSet(String sqlString, List<OracleParameter> Parameters)
        {
            DataSet ds = new DataSet();
            ds.RemotingFormat = SerializationFormat.Binary;
            try
            {
                if (UntilityConstant.DBConnectionMode == 0)
                {
                    if (commandClient != null)
                    {
                        //if (commandClient.Transaction == null)
                        //{
                        //    commandClient.Transaction = TransactionClient;
                        //}
                        commandClient.CommandText = sqlString;
                        commandClient.Parameters.Clear();
                    }
                    else
                    {
                        commandClient = new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                    }
                    //commandClient = new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                    foreach (OracleParameter var in Parameters)
                    {
                        System.Data.OracleClient.OracleParameter var1 = new System.Data.OracleClient.OracleParameter();
                        //转换成非直连参数 梁吉 2016-06-08
                        var1.ParameterName = var.ParameterName;
                        var1.OracleType = GetOracleType(var.OracleDbType);
                        var1.Value = var.Value;
                        commandClient.Parameters.Add(var1);
                    }
                    dataAdapterClient = new System.Data.OracleClient.OracleDataAdapter(commandClient);
                    dataAdapterClient.Fill(ds, "table");
                }
                else if (UntilityConstant.DBConnectionMode == 2)
                {
                    if (commandDev != null)
                    {
                        //if (commandClient.Transaction == null)
                        //{
                        //    commandClient.Transaction = TransactionClient;
                        //}
                        commandDev.CommandText = sqlString;
                        commandDev.Parameters.Clear();
                    }
                    else
                    {
                        commandDev = new Devart.Data.Oracle.OracleCommand(sqlString,connectionDev);
                    }
                    foreach (OracleParameter var in Parameters)
                    {
                        Devart.Data.Oracle.OracleParameter var1 = new Devart.Data.Oracle.OracleParameter();
                        //转换成非直连参数 梁吉 2016-06-08
                        var1.ParameterName = var.ParameterName;
                        var1.OracleDbType = GetOracleDevType(var.OracleDbType);
                        var1.Value = var.Value;
                        commandDev.Parameters.Add(var1);
                    }
                    dataAdapterDev = new Devart.Data.Oracle.OracleDataAdapter(commandDev);
                    dataAdapterDev.Fill(ds, "table");
                }
                else
                {
                    if (command != null)
                    {
                        //if (command.Transaction == null)
                        //    command.Transaction = Transaction;
                        
                        command.CommandText = sqlString;
                        command.Parameters.Clear();
                    }
                    else
                        command = new OracleCommand(sqlString, connection);
                    //command.Transaction = Transaction;
                    //command.Parameters.Clear();
                    //command.CommandText = sqlString;
                    foreach (OracleParameter var in Parameters)
                    {

                        command.Parameters.Add(var);
                    }
                    dataAdapter = new OracleDataAdapter(command);
                    dataAdapter.Fill(ds, "table");
                }

                ds.Tables["table"].ExtendedProperties.Add("SQL", sqlString);
                ds.Tables["table"].ExtendedProperties.Add("Parameters", Parameters);
                return ds;
            }
            catch (Exception ex)
            {
                String ParameterString = "";
                foreach (OracleParameter var in Parameters)
                {
                    ParameterString += "参数：" + var.ParameterName + " 值：" + var.Value + "\r\n";
                }
                return null;
                //throw new Exception("读取数据失败，SQL：" + sqlString + ",参数内容：" + ParameterString, ex);
            }
        }

        /// <summary>获取数据集
        /// 获取数据集
        /// 查询SQL存在datatable的DisplayExpression属性
        /// 查询SQL存在datatable的ExtendedProperties属性"SQL"关键字对应的值;
        /// </summary>
        /// <param name="sqlStrings">SQL语句数组</param>
        /// <param name="TableNames">表名数组（建议使用全称）</param>
        /// <returns>数据集</returns>
        public DataSet SelectDataSet(String[] sqlStrings, String[] TableNames)
        {
            if (sqlStrings == null || TableNames == null)
            {
                throw new Exception("此方法入参全不能为空");
            }
            if (sqlStrings.Length != TableNames.Length)
            {
                throw new Exception("SQL语句数组与表明数组的数量不相等，不能明确SQL语句与表名之间一一对应的关系，不能进行获取数据集的操作。");
            }
            DataSet ds = new DataSet();
            ds.RemotingFormat = SerializationFormat.Binary;
            try
            {
                if (UntilityConstant.DBConnectionMode == 0)
                {
                    for (int i = 0; i < sqlStrings.Length; i++)
                    {
                        if (commandClient != null)
                        {
                            //if (commandClient.Transaction == null)
                            //{
                            //    commandClient.Transaction = TransactionClient;
                            //}
                            commandClient.CommandText = sqlStrings[i];
                            commandClient.Parameters.Clear();
                        }
                        else
                        {
                            commandClient = new System.Data.OracleClient.OracleCommand(sqlStrings[i], connectionClient);
                        }
                        //commandClient = new System.Data.OracleClient.OracleCommand(sqlStrings[i], connectionClient);
                        dataAdapterClient = new System.Data.OracleClient.OracleDataAdapter(commandClient);
                        dataAdapterClient.Fill(ds, TableNames[i]);
                        ds.Tables[TableNames[i]].ExtendedProperties.Add("SQL", sqlStrings[i]);
                    }
                }
                else if (UntilityConstant.DBConnectionMode == 2)
                {
                    for (int i = 0; i < sqlStrings.Length; i++)
                    {
                        if (commandDev != null)
                        {
                            commandDev.CommandText = sqlStrings[i];
                            commandDev.Parameters.Clear();
                        }
                        else
                        {
                            commandDev = new Devart.Data.Oracle.OracleCommand(sqlStrings[i], connectionDev);
                        }
                        dataAdapterDev = new Devart.Data.Oracle.OracleDataAdapter(commandDev);
                        dataAdapterDev.Fill(ds, TableNames[i]);
                        ds.Tables[TableNames[i]].ExtendedProperties.Add("SQL", sqlStrings[i]);
                    }
                }
                else
                {
                    for (int i = 0; i < sqlStrings.Length; i++)
                    {
                        if (command != null)
                        {
                            command.CommandText = sqlStrings[i];
                            command.Parameters.Clear();
                        }
                        else
                        {
                            command = new OracleCommand(sqlStrings[i], connection);
                        }
                        
                        dataAdapter = new OracleDataAdapter(command);
                        dataAdapter.Fill(ds, TableNames[i]);
                        ds.Tables[TableNames[i]].ExtendedProperties.Add("SQL", sqlStrings[i]);
                    }
                }
                return ds;
            }
            catch (Exception ex)
            {
                String sqls = "";
                for (int i = 0; i < sqlStrings.Length; i++)
                {
                    sqls += sqlStrings[i] + "，表名：" + TableNames[i] + "\r\n";
                }
                Utility.LogFile.WriteLogAuto("获取数据集失败，SQL:" + sqls + ex.Message, "OralceBaseClass");
                throw new Exception("获取数据集失败，SQL:" + sqls, ex);
            }
        }
        /// <summary>获取数据集
        /// 获取数据集
        /// 查询SQL存在datatable的DisplayExpression属性
        /// 查询SQL存在datatable的ExtendedProperties属性"SQL"关键字对应的值;
        /// 查询SQL的参数存在datatable的ExtendedProperties属性"Parameters"关键字对应的值 类型为;List<OracleParameter> Parameters
        /// </summary>
        /// <param name="sqlStrings">SQL语句数组</param>
        /// <param name="TableNames">表名数组（建议使用全称）</param>
        /// <param name="Parameters_Array">参数列表数组</param>
        /// <returns></returns>
        public DataSet SelectDataSet(String[] sqlStrings, String[] TableNames, List<OracleParameter>[] Parameters_Array)
        {
            if (Parameters_Array == null)
            {
                return SelectDataSet(sqlStrings, TableNames);
            }
            if (sqlStrings == null || TableNames == null)
            {
                throw new Exception("此方法入参全不能为空");
            }
            if (sqlStrings.Length != TableNames.Length)
            {
                throw new Exception("SQL语句数组与表明数组的数量不相等，不能明确SQL语句与表名之间一一对应的关系，不能进行获取数据集的操作。");
            }
            DataSet ds = new DataSet();
            ds.RemotingFormat = SerializationFormat.Binary;
            try
            {
                if (UntilityConstant.DBConnectionMode == 0)
                {
                    for (int i = 0; i < sqlStrings.Length; i++)
                    {
                        if (commandClient != null)
                        {
                            //if (commandClient.Transaction == null)
                            //{
                            //    commandClient.Transaction = TransactionClient;
                            //}
                            commandClient.CommandText = sqlStrings[i];
                            commandClient.Parameters.Clear();
                        }
                        else
                        {
                            commandClient = new System.Data.OracleClient.OracleCommand(sqlStrings[i], connectionClient);
                        }
                        //commandClient = new System.Data.OracleClient.OracleCommand(sqlStrings[i], connectionClient);
                        foreach (OracleParameter var in Parameters_Array[i])
                        {
                            System.Data.OracleClient.OracleParameter var1 = new System.Data.OracleClient.OracleParameter();
                            //转换成非直连参数 梁吉 2016-06-08
                            var1.ParameterName = var.ParameterName;
                            var1.OracleType = GetOracleType(var.OracleDbType);
                            var1.Value = var.Value;
                            commandClient.Parameters.Add(var1);
                        }
                        dataAdapterClient = new System.Data.OracleClient.OracleDataAdapter(commandClient);
                        dataAdapterClient.Fill(ds, TableNames[i]);
                        ds.Tables[TableNames[i]].ExtendedProperties.Add("SQL", sqlStrings[i]);
                        ds.Tables[TableNames[i]].ExtendedProperties.Add("Parameters", Parameters_Array[i]);
                    }
                }
                else if (UntilityConstant.DBConnectionMode == 2)
                {
                    for (int i = 0; i < sqlStrings.Length; i++)
                    {
                        if (commandDev != null)
                        {
                            commandDev.CommandText = sqlStrings[i];
                            commandDev.Parameters.Clear();
                        }
                        else
                        {
                            commandDev = new Devart.Data.Oracle.OracleCommand(sqlStrings[i], connectionDev);
                        }
                        foreach (OracleParameter var in Parameters_Array[i])
                        {
                            Devart.Data.Oracle.OracleParameter var1 = new Devart.Data.Oracle.OracleParameter();
                            //转换成非直连参数 梁吉 2016-06-08
                            var1.ParameterName = var.ParameterName;
                            var1.OracleDbType = GetOracleDevType(var.OracleDbType);
                            var1.Value = var.Value;
                            commandDev.Parameters.Add(var1);
                        }
                        dataAdapterDev = new Devart.Data.Oracle.OracleDataAdapter(commandDev);
                        dataAdapterDev.Fill(ds, TableNames[i]);
                        ds.Tables[TableNames[i]].ExtendedProperties.Add("SQL", sqlStrings[i]);
                        ds.Tables[TableNames[i]].ExtendedProperties.Add("Parameters", Parameters_Array[i]);
                    }
                }
                else
                {
                    for (int i = 0; i < sqlStrings.Length; i++)
                    {
                        if (command != null)
                        {
                            command.CommandText = sqlStrings[i];
                            command.Parameters.Clear();
                        }
                        else
                        {
                            command = new OracleCommand(sqlStrings[i], connection);
                        }
                        foreach (OracleParameter var in Parameters_Array[i])
                        {
                            command.Parameters.Add(var);
                        }
                        dataAdapter = new OracleDataAdapter(command);
                        dataAdapter.Fill(ds, TableNames[i]);
                        ds.Tables[TableNames[i]].ExtendedProperties.Add("SQL", sqlStrings[i]);
                        ds.Tables[TableNames[i]].ExtendedProperties.Add("Parameters", Parameters_Array[i]);
                    }
                }

                return ds;
            }
            catch (Exception ex)
            {
                String sqls = "";
                for (int i = 0; i < sqlStrings.Length; i++)
                {
                    sqls += sqlStrings[i] + "，表名：" + TableNames[i] + "\r\n";
                    foreach (OracleParameter var in Parameters_Array[i])
                    {
                        sqls += "参数：" + var.ParameterName + "，值：" + var.Value;
                    }
                }
                throw new Exception("获取数据集失败，SQL:" + sqls, ex);
            }
        }
        /// <summary>
        ///传参获取数据集，适合blob类型字段做条件
        /// </summary>
        /// <param name="sqlString"></param>
        /// <param name="ParaNames"></param>
        /// <param name="ParaTypes"></param>
        /// <param name="ParaValues"></param>
        /// <returns></returns>
        public DataSet SelectDataSet(String sqlString, List<String> ParaNames, List<OracleDbType> ParaTypes, List<String> ParaValues)
        {
            DataSet ds = new DataSet();
            ds.RemotingFormat = SerializationFormat.Binary;
            try
            {
                if (UntilityConstant.DBConnectionMode == 0)
                {
                    if (commandClient != null)
                    {
                        //if (commandClient.Transaction == null)
                        //{
                        //    commandClient.Transaction = TransactionClient;
                        //}
                        commandClient.CommandText = sqlString;
                        commandClient.Parameters.Clear();
                    }
                    else
                    {
                        commandClient = new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                    }
                    //commandClient = new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                    for (int k = 0; k < ParaNames.Count; k++)
                    {
                        System.Data.OracleClient.OracleParameter var1 = new System.Data.OracleClient.OracleParameter();
                        //转换成非直连参数 梁吉 2016-06-08
                        var1.ParameterName = ParaNames[k];
                        var1.OracleType = GetOracleType(ParaTypes[k]);
                        var1.Value = ParaValues[k];
                        commandClient.Parameters.Add(var1);
                    }
                    dataAdapterClient = new System.Data.OracleClient.OracleDataAdapter(commandClient);
                    dataAdapterClient.Fill(ds, "table");
                    ds.Tables["table"].ExtendedProperties.Add("SQL", sqlString);
                    ds.Tables["table"].ExtendedProperties.Add("ParaNames", ParaNames);
                    ds.Tables["table"].ExtendedProperties.Add("ParaTypes", ParaTypes);
                    ds.Tables["table"].ExtendedProperties.Add("ParaValues", ParaValues);
                }
                else if (UntilityConstant.DBConnectionMode == 2)
                {
                    if (commandDev != null)
                    {
                        commandDev.CommandText = sqlString;
                        commandDev.Parameters.Clear();
                    }
                    else
                    {
                        commandDev = new Devart.Data.Oracle.OracleCommand(sqlString, connectionDev);
                    }

                    for (int k = 0; k < ParaNames.Count; k++)
                    {
                        Devart.Data.Oracle.OracleParameter var1 = new Devart.Data.Oracle.OracleParameter();
                        //转换成非直连参数 梁吉 2016-06-08
                        var1.ParameterName = ParaNames[k];
                        var1.OracleDbType = GetOracleDevType(ParaTypes[k]);
                        var1.Value = ParaValues[k];
                        commandDev.Parameters.Add(var1);
                    }
                    dataAdapterDev = new Devart.Data.Oracle.OracleDataAdapter(commandDev);
                    dataAdapterDev.Fill(ds, "table");
                    ds.Tables["table"].ExtendedProperties.Add("SQL", sqlString);
                    ds.Tables["table"].ExtendedProperties.Add("ParaNames", ParaNames);
                    ds.Tables["table"].ExtendedProperties.Add("ParaTypes", ParaTypes);
                    ds.Tables["table"].ExtendedProperties.Add("ParaValues", ParaValues);
                }
                else
                {
                    if (command != null)
                    {
                        command.CommandText = sqlString;
                        command.Parameters.Clear();
                    }
                    else
                    {
                        command = new OracleCommand(sqlString, connection);
                    }
                    
                    for (int k = 0; k < ParaNames.Count; k++)
                    {
                        OracleParameter p = new OracleParameter();
                        p.ParameterName = ParaNames[k];
                        p.OracleDbType = ParaTypes[k];
                        p.Value = ParaValues[k];
                        command.Parameters.Add(p);
                    }
                    dataAdapter = new OracleDataAdapter(command);
                    dataAdapter.Fill(ds, "table");
                    ds.Tables["table"].ExtendedProperties.Add("SQL", sqlString);
                    ds.Tables["table"].ExtendedProperties.Add("ParaNames", ParaNames);
                    ds.Tables["table"].ExtendedProperties.Add("ParaTypes", ParaTypes);
                    ds.Tables["table"].ExtendedProperties.Add("ParaValues", ParaValues);
                }

                return ds;
            }
            catch (Exception ex)
            {
                String ParameterString = "";
                for (int k = 0; k < ParaNames.Count; k++)
                {
                    OracleParameter p = new OracleParameter();
                    p.ParameterName = ParaNames[k];
                    //p.OracleDbType = ParaTypes[k];
                    p.Value = ParaValues[k];
                    ParameterString += "参数：" + ParaNames[k] + " 值：" + ParaValues[k] + "\r\n";
                }

                throw new Exception("读取数据失败，SQL：" + sqlString + ",参数内容：" + ParameterString, ex);
            }
        }
        #endregion
        #region 带事务操作执行方法
        /// <summary>执行一个事务 
        /// 执行一个事务 默认共享锁 IsolationLevel.ReadCommitted
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <returns>执行事务影响行数</returns>
        public int ExecuteATransaction(String sqlString)
        {
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                commandClient = connectionClient.CreateCommand();
                TransactionClient = connectionClient.BeginTransaction(IsolationLevel.ReadCommitted);
                commandClient.Transaction = TransactionClient;
                try
                {
                    commandClient.CommandText = sqlString;
                    commandClient.CommandType = CommandType.Text;
                    int n = commandClient.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        TransactionClient.Commit();
                        return n;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        TransactionClient.Rollback();
                        throw new Exception("执行事务失败，影响零行，SQL:" + sqlString);
                    }

                }
                catch (Exception ex)
                {
                    TransactionClient.Rollback();
                    throw new Exception("执行事务失败，SQL:" + sqlString, ex);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                commandDev = connectionDev.CreateCommand();
                TransactionDev = connectionDev.BeginTransaction(IsolationLevel.ReadCommitted);
                commandDev.Transaction = TransactionDev;
                try
                {
                    commandDev.CommandText = sqlString;
                    commandDev.CommandType = CommandType.Text;
                    int n = commandDev.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        TransactionDev.Commit();
                        return n;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        TransactionDev.Rollback();
                        throw new Exception("执行事务失败，影响零行，SQL:" + sqlString);
                    }

                }
                catch (Exception ex)
                {
                    TransactionDev.Rollback();
                    throw new Exception("执行事务失败，SQL:" + sqlString, ex);
                }
            }
            else
            {
                command = connection.CreateCommand();
                Transaction = connection.BeginTransaction(IsolationLevel.ReadCommitted);
                command.Transaction = Transaction;
                try
                {
                    command.CommandText = sqlString;
                    command.CommandType = CommandType.Text;
                    int n = command.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        Transaction.Commit();
                        return n;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        Transaction.Rollback();
                        throw new Exception("执行事务失败，影响零行，SQL:" + sqlString);
                    }

                }
                catch (Exception ex)
                {
                    Transaction.Rollback();
                    throw new Exception("执行事务失败，SQL:" + sqlString, ex);
                }
            }

        }
        /// <summary>执行一个事物
        /// 执行一个事物
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <param name="Parameters">参数列表</param>
        /// <returns>执行事务影响行数</returns>
        public int ExecuteATransaction(String sqlString, List<OracleParameter> Parameters)
        {
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                commandClient = connectionClient.CreateCommand();
                TransactionClient = connectionClient.BeginTransaction(IsolationLevel.ReadCommitted);
                commandClient.Transaction = TransactionClient;
                try
                {
                    commandClient.CommandText = sqlString;
                    commandClient.CommandType = CommandType.Text;
                    foreach (OracleParameter var in Parameters)
                    {
                        System.Data.OracleClient.OracleParameter var1 = new System.Data.OracleClient.OracleParameter();
                        //转换成非直连参数 梁吉 2016-06-08
                        var1.ParameterName = var.ParameterName;
                        var1.OracleType = GetOracleType(var.OracleDbType);
                        var1.Value = var.Value;
                        commandClient.Parameters.Add(var1);
                    }
                    int n = commandClient.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        TransactionClient.Commit();
                        return n;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        TransactionClient.Rollback();
                        throw new Exception("执行事务失败，影响零行，SQL:" + sqlString);
                    }

                }
                catch (Exception ex)
                {
                    TransactionClient.Rollback();
                    throw new Exception("执行事务失败，SQL:" + sqlString, ex);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                commandDev = connectionDev.CreateCommand();
                TransactionDev = connectionDev.BeginTransaction(IsolationLevel.ReadCommitted);
                commandDev.Transaction = TransactionDev;
                try
                {
                    commandDev.CommandText = sqlString;
                    commandDev.CommandType = CommandType.Text;
                    foreach (OracleParameter var in Parameters)
                    {
                        Devart.Data.Oracle.OracleParameter var1 = new Devart.Data.Oracle.OracleParameter();
                        //转换成非直连参数 梁吉 2016-06-08
                        var1.ParameterName = var.ParameterName;
                        var1.OracleDbType = GetOracleDevType(var.OracleDbType);
                        var1.Value = var.Value;
                        commandDev.Parameters.Add(var1);
                    }
                    int n = commandDev.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        TransactionDev.Commit();
                        return n;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        TransactionDev.Rollback();
                        throw new Exception("执行事务失败，影响零行，SQL:" + sqlString);
                    }

                }
                catch (Exception ex)
                {
                    TransactionDev.Rollback();
                    throw new Exception("执行事务失败，SQL:" + sqlString, ex);
                }
            }
            else
            {
                command = connection.CreateCommand();
                Transaction = connection.BeginTransaction(IsolationLevel.ReadCommitted);
                command.Transaction = Transaction;
                try
                {
                    command.CommandText = sqlString;
                    command.CommandType = CommandType.Text;
                    foreach (OracleParameter var in Parameters)
                    {
                        command.Parameters.Add(var);
                    }
                    int n = command.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        Transaction.Commit();
                        return n;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        Transaction.Rollback();
                        throw new Exception("执行事务失败，影响零行，SQL:" + sqlString);
                    }

                }
                catch (Exception ex)
                {
                    Transaction.Rollback();
                    String exmessage = "";
                    foreach (OracleParameter var in Parameters)
                    {
                        exmessage += "参数：" + var.ParameterName + "，值：" + var.Value + "\r\n";
                    }
                    throw new Exception("执行事务失败，SQL:" + sqlString + "\r\n 参数列表：" + exmessage, ex);
                }
            }

        }
        /// <summary>执行一个事务
        /// 执行一个事务
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <param name="_IsolationLevel">数据库锁级别</param>
        /// <returns>执行事务影响行数</returns>
        public int ExecuteATransaction(String sqlString, IsolationLevel _IsolationLevel)
        {
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                commandClient = connectionClient.CreateCommand();
                TransactionClient = connectionClient.BeginTransaction(_IsolationLevel);
                commandClient.Transaction = TransactionClient;
                try
                {
                    commandClient.CommandText = sqlString;
                    commandClient.CommandType = CommandType.Text;
                    int n = commandClient.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        TransactionClient.Commit();
                        return n;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        TransactionClient.Rollback();
                        throw new Exception("执行事务失败，影响零行，SQL:" + sqlString);
                    }

                }
                catch (Exception ex)
                {
                    TransactionClient.Rollback();
                    throw new Exception("执行事务失败，SQL:" + sqlString, ex);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                commandDev = connectionDev.CreateCommand();
                TransactionDev = connectionDev.BeginTransaction(_IsolationLevel);
                commandDev.Transaction = TransactionDev;
                try
                {
                    commandDev.CommandText = sqlString;
                    commandDev.CommandType = CommandType.Text;
                    int n = commandDev.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        TransactionDev.Commit();
                        return n;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        TransactionDev.Rollback();
                        throw new Exception("执行事务失败，影响零行，SQL:" + sqlString);
                    }

                }
                catch (Exception ex)
                {
                    TransactionDev.Rollback();
                    throw new Exception("执行事务失败，SQL:" + sqlString, ex);
                }
            }
            else
            {
                command = connection.CreateCommand();
                Transaction = connection.BeginTransaction(_IsolationLevel);
                command.Transaction = Transaction;
                try
                {
                    command.CommandText = sqlString;
                    command.CommandType = CommandType.Text;
                    int n = command.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        Transaction.Commit();
                        return n;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        Transaction.Rollback();
                        throw new Exception("执行事务失败，影响零行，SQL:" + sqlString);
                    }

                }
                catch (Exception ex)
                {
                    Transaction.Rollback();
                    throw new Exception("执行事务失败，SQL:" + sqlString, ex);
                }
            }

        }
        /// <summary>执行一个事务
        /// 执行一个事务
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <param name="Parameters">参数列表</param>
        /// <param name="_IsolationLevel">数据库锁类型</param>
        /// <returns>执行事务影响行数</returns>
        public int ExecuteATransaction(String sqlString, List<OracleParameter> Parameters, IsolationLevel _IsolationLevel)
        {
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                commandClient = connectionClient.CreateCommand();
                TransactionClient = connectionClient.BeginTransaction(_IsolationLevel);
                commandClient.Transaction = TransactionClient;
                try
                {
                    commandClient.CommandText = sqlString;
                    commandClient.CommandType = CommandType.Text;
                    foreach (OracleParameter var in Parameters)
                    {
                        System.Data.OracleClient.OracleParameter var1 = new System.Data.OracleClient.OracleParameter();
                        //转换成非直连参数 梁吉 2016-06-08
                        var1.ParameterName = var.ParameterName;
                        var1.OracleType = GetOracleType(var.OracleDbType);
                        var1.Value = var.Value;
                        commandClient.Parameters.Add(var1);
                    }
                    int n = commandClient.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        TransactionClient.Commit();
                        return n;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        TransactionClient.Rollback();
                        throw new Exception("执行事务失败，影响零行，SQL:" + sqlString);
                    }

                }
                catch (Exception ex)
                {
                    TransactionClient.Rollback();
                    throw new Exception("执行事务失败，SQL:" + sqlString, ex);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                commandDev = connectionDev.CreateCommand();
                TransactionDev = connectionDev.BeginTransaction(_IsolationLevel);
                commandDev.Transaction = TransactionDev;
                try
                {
                    commandDev.CommandText = sqlString;
                    commandDev.CommandType = CommandType.Text;
                    foreach (OracleParameter var in Parameters)
                    {
                        Devart.Data.Oracle.OracleParameter var1 = new Devart.Data.Oracle.OracleParameter();
                        //转换成非直连参数 梁吉 2016-06-08
                        var1.ParameterName = var.ParameterName;
                        var1.OracleDbType = GetOracleDevType(var.OracleDbType);
                        var1.Value = var.Value;
                        commandDev.Parameters.Add(var1);
                    }
                    int n = commandDev.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        TransactionDev.Commit();
                        return n;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        TransactionDev.Rollback();
                        throw new Exception("执行事务失败，影响零行，SQL:" + sqlString);
                    }

                }
                catch (Exception ex)
                {
                    TransactionDev.Rollback();
                    throw new Exception("执行事务失败，SQL:" + sqlString, ex);
                }
            }
            else
            {
                command = connection.CreateCommand();
                Transaction = connection.BeginTransaction(_IsolationLevel);
                command.Transaction = Transaction;
                try
                {
                    command.CommandText = sqlString;
                    command.CommandType = CommandType.Text;
                    foreach (OracleParameter var in Parameters)
                    {
                        command.Parameters.Add(var);
                    }
                    int n = command.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        Transaction.Commit();
                        return n;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        Transaction.Rollback();
                        throw new Exception("执行事务失败，影响零行，SQL:" + sqlString);
                    }

                }
                catch (Exception ex)
                {
                    Transaction.Rollback();
                    String exmessage = "";
                    foreach (OracleParameter var in Parameters)
                    {
                        exmessage += "参数：" + var.ParameterName + "，值：" + var.Value + "\r\n";
                    }
                    throw new Exception("执行事务失败，SQL:" + sqlString + "\r\n 参数列表：" + exmessage, ex);
                }
            }

        }
        /// <summary>
        /// 执行一个事务
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <param name="ParaNames">参数字段名列表</param>
        /// <param name="ParaTypes">参数类型列表</param>
        /// <param name="ParaValues">参数列表</param>
        /// <param name="_IsolationLevel">数据库锁类型</param>
        /// <returns>执行事务影响行数</returns>
        public int ExecuteATransaction(String sqlString, List<String> ParaNames, List<OracleDbType> ParaTypes, List<String> ParaValues, IsolationLevel _IsolationLevel)
        {
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                commandClient = connectionClient.CreateCommand();
                if ((int)_IsolationLevel <= -1)
                {
                    _IsolationLevel = IsolationLevel.ReadCommitted;
                }
                TransactionClient = connectionClient.BeginTransaction(_IsolationLevel);
                commandClient.Transaction = TransactionClient;
                try
                {
                    commandClient.CommandText = sqlString;
                    commandClient.CommandType = CommandType.Text;
                    for (int k = 0; k < ParaNames.Count; k++)
                    {
                        System.Data.OracleClient.OracleParameter var1 = new System.Data.OracleClient.OracleParameter();
                        //转换成非直连参数 梁吉 2016-06-08
                        var1.ParameterName = ParaNames[k];
                        var1.OracleType = GetOracleType(ParaTypes[k]);
                        var1.Value = ParaValues[k];
                        commandClient.Parameters.Add(var1);
                    }

                    int n = commandClient.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        TransactionClient.Commit();
                        return n;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        TransactionClient.Rollback();
                        throw new Exception("执行事务失败，影响零行，SQL:" + sqlString);
                    }

                }
                catch (Exception ex)
                {
                    TransactionClient.Rollback();
                    throw new Exception("执行事务失败，SQL:" + sqlString, ex);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                commandDev = connectionDev.CreateCommand();
                if ((int)_IsolationLevel <= -1)
                {
                    _IsolationLevel = IsolationLevel.ReadCommitted;
                }
                TransactionDev = connectionDev.BeginTransaction(_IsolationLevel);
                commandDev.Transaction = TransactionDev;
                try
                {
                    commandDev.CommandText = sqlString;
                    commandDev.CommandType = CommandType.Text;
                    for (int k = 0; k < ParaNames.Count; k++)
                    {
                        Devart.Data.Oracle.OracleParameter var1 = new Devart.Data.Oracle.OracleParameter();
                        //转换成非直连参数 梁吉 2016-06-08
                        var1.ParameterName = ParaNames[k];
                        var1.OracleDbType = GetOracleDevType(ParaTypes[k]);
                        var1.Value = ParaValues[k];
                        commandDev.Parameters.Add(var1);
                    }

                    int n = commandDev.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        TransactionDev.Commit();
                        return n;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        TransactionDev.Rollback();
                        throw new Exception("执行事务失败，影响零行，SQL:" + sqlString);
                    }

                }
                catch (Exception ex)
                {
                    TransactionDev.Rollback();
                    throw new Exception("执行事务失败，SQL:" + sqlString, ex);
                }
            }
            else
            {
                command = connection.CreateCommand();
                if ((int)_IsolationLevel <= -1)
                {
                    _IsolationLevel = IsolationLevel.ReadCommitted;
                }
                Transaction = connection.BeginTransaction(_IsolationLevel);
                command.Transaction = Transaction;
                try
                {
                    command.CommandText = sqlString;
                    command.CommandType = CommandType.Text;

                    for (int k = 0; k < ParaNames.Count; k++)
                    {
                        OracleParameter p = new OracleParameter();
                        p.ParameterName = ParaNames[k];
                        p.OracleDbType = ParaTypes[k];
                        p.Value = ParaValues[k];
                        command.Parameters.Add(p);
                    }
                    int n = command.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        Transaction.Commit();
                        return n;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        Transaction.Rollback();
                        throw new Exception("执行事务失败，影响零行，SQL:" + sqlString);
                    }

                }
                catch (Exception ex)
                {
                    Transaction.Rollback();
                    String exmessage = "";
                    for (int k = 0; k < ParaNames.Count; k++)
                    {
                        OracleParameter p = new OracleParameter();
                        p.ParameterName = ParaNames[k];
                        //p.OracleDbType = ParaTypes[k];
                        p.Value = ParaValues[k];
                        exmessage += "参数：" + ParaNames[k] + " 值：" + ParaValues[k] + "\r\n";
                    }
                    throw new Exception("执行事务失败，SQL:" + sqlString + "\r\n 参数列表：" + exmessage, ex);
                }
            }

        }
        /// <summary>开始事务，初始化事务
        /// 开始事务，初始化事务 默认锁类型：IsolationLevel.ReadCommitted
        /// </summary>
        /// <returns>true or false</returns>
        #endregion
        #region 事务操作
        public bool BeginTransaction()
        {
            try
            {
                //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
                if (UntilityConstant.DBConnectionMode == 0)
                {
                    commandClient = connectionClient.CreateCommand();
                    TransactionClient = connectionClient.BeginTransaction(IsolationLevel.ReadCommitted);
                    commandClient.Transaction = TransactionClient;
                }
                else if (UntilityConstant.DBConnectionMode == 2)
                {
                    commandDev = connectionDev.CreateCommand();
                    TransactionDev = connectionDev.BeginTransaction(IsolationLevel.ReadCommitted);
                    commandDev.Transaction = TransactionDev;
                }
                else
                {
                    command = connection.CreateCommand();
                    Transaction = connection.BeginTransaction(IsolationLevel.ReadCommitted);
                    command.Transaction = Transaction;
                }
                
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception("初始化事务失败", ex);
            }

        }
        /// <summary>开始事务，初始化事务
        /// 开始事务，初始化事务
        /// </summary>
        /// <param name="_IsolationLevel">数据库事务锁类型</param>
        /// <returns>true or false</returns>
        public bool BeginTransaction(IsolationLevel _IsolationLevel)
        {
            try
            {
                //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
                if (UntilityConstant.DBConnectionMode == 0)
                {
                    commandClient = connectionClient.CreateCommand();
                    TransactionClient = connectionClient.BeginTransaction(_IsolationLevel);
                    commandClient.Transaction = TransactionClient;
                }
                else if (UntilityConstant.DBConnectionMode == 2)
                {
                    commandDev = connectionDev.CreateCommand();
                    TransactionDev = connectionDev.BeginTransaction(_IsolationLevel);
                    commandDev.Transaction = TransactionDev;
                }
                else
                {
                    command = connection.CreateCommand();
                    Transaction = connection.BeginTransaction(_IsolationLevel);
                    command.Transaction = Transaction;
                }

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception("初始化事务失败", ex);
            }

        }
        /// <summary>提交事务
        /// 提交事务
        /// </summary>
        /// <returns>true or false</returns>
        public bool CommitTransaction()
        {
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    TransactionClient.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    TransactionClient.Rollback();
                    throw new Exception("提交事务失败", ex);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    TransactionDev.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    TransactionDev.Rollback();
                    throw new Exception("提交事务失败", ex);
                }
            }
            else
            {
                try
                {
                    Transaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    if (Transaction.Connection != null)
                            Transaction.Rollback();
                    throw new Exception("提交事务失败", ex);
                }
            }

        }
        /// <summary>回滚事务
        /// 回滚事务
        /// </summary>
        /// <returns>true or false</returns>
        public bool RollbackTransaction()
        {
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    TransactionClient.Rollback();
                    return true;
                }
                catch (Exception ex)
                {
                    TransactionClient.Rollback();
                    //throw new Exception("事务回滚异常", ex);
                    return false;
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    TransactionDev.Rollback();
                    return true;
                }
                catch (Exception ex)
                {
                    TransactionDev.Rollback();
                    //throw new Exception("事务回滚异常", ex);
                    return false;
                }
            }
            else
            {
                try
                {
                    Transaction.Rollback();
                    return true;
                }
                catch (Exception ex)
                {
                    if (Transaction.Connection != null)
                        Transaction.Rollback();
                    //throw new Exception("事务回滚异常", ex);
                    return false;
                }
            }

        }
        #endregion
        /// <summary> 执行事务
        /// 执行事务
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <returns>true or false</returns>
        public bool ExecuteTransaction(String sqlString)
        {
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    commandClient.CommandText = sqlString;
                    commandClient.CommandType = CommandType.Text;
                    int n = commandClient.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        return true;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的 返回处理，事务回滚
                        //TransactionClient.Rollback();
                        return false;
                        //throw new Exception("执行事务失败，影响行数零行");
                    }
                }
                catch (Exception ex)
                {
                    //TransactionClient.Rollback();
                    return false;
                    //throw new Exception("执行事务失败", ex);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    commandDev.CommandText = sqlString;
                    commandDev.CommandType = CommandType.Text;
                    int n = commandDev.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        return true;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的 返回处理，事务回滚
                        //TransactionClient.Rollback();
                        return false;
                        //throw new Exception("执行事务失败，影响行数零行");
                    }
                }
                catch (Exception ex)
                {
                    //TransactionClient.Rollback();
                    return false;
                    //throw new Exception("执行事务失败", ex);
                }
            }
            else
            {
                try
                {
                    command.CommandText = sqlString;
                    command.CommandType = CommandType.Text;
                    int n = command.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        return true;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的 返回处理事务回滚
                        //Transaction.Rollback();
                        return false;
                        //throw new Exception("执行事务失败，影响行数零行");
                    }
                }
                catch (Exception ex)
                {
                    return false;
                    //Transaction.Rollback();
                    //throw new Exception("执行事务失败", ex);
                }
            }

        }
        /// <summary> 执行事务无限制行数
        /// 执行事务无限制行数
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <returns>string</returns>
        public string ExecuteTransactionNOLimit(String sqlString)
        {
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    commandClient.Parameters.Clear();
                    commandClient.CommandText = sqlString;
                    commandClient.CommandType = CommandType.Text;
                    int n = commandClient.ExecuteNonQuery();
                    return "";
                }
                catch (Exception ex)
                {
                    //TransactionClient.Rollback();
                    return ex.Message;
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    commandDev.Parameters.Clear();
                    commandDev.CommandText = sqlString;
                    commandDev.CommandType = CommandType.Text;
                    int n = commandDev.ExecuteNonQuery();
                    return "";
                }
                catch (Exception ex)
                {
                    //TransactionClient.Rollback();
                    return ex.Message;
                }
            }
            else
            {
                try
                {
                    command.Parameters.Clear();
                    command.CommandText = sqlString;
                    command.CommandType = CommandType.Text;
                    int n = command.ExecuteNonQuery();
                    return "";

                }
                catch (Exception ex)
                {
                    //Transaction.Rollback();
                    return ex.Message;
                }
            }

        }
        /// <summary>执行事务
        /// 执行事务
        /// </summary>
        /// <param name="sqlString">SQL语句</param>
        /// <param name="Parameters">参数列表</param>
        /// <returns>true or false</returns>
        public bool ExecuteTransaction(String sqlString, List<OracleParameter> Parameters)
        {
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    commandClient.CommandText = sqlString;
                    commandClient.CommandType = CommandType.Text;

                    foreach (OracleParameter var in Parameters)
                    {
                        System.Data.OracleClient.OracleParameter var1 = new System.Data.OracleClient.OracleParameter();
                        //转换成非直连参数 梁吉 2016-06-08
                        var1.ParameterName = var.ParameterName;
                        var1.OracleType = GetOracleType(var.OracleDbType);
                        var1.Value = var.Value;
                        commandClient.Parameters.Add(var1);
                    }
                    int n = commandClient.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        return true;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        //Transaction.Rollback();
                        return false;
                        //throw new Exception("执行事务失败，影响行数零行");
                    }
                }
                catch (Exception ex)
                {
                    return false;//返回后处理事务回滚
                    //TransactionClient.Rollback();
                    //throw new Exception("执行事务失败", ex);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    commandDev.CommandText = sqlString;
                    commandDev.CommandType = CommandType.Text;

                    foreach (OracleParameter var in Parameters)
                    {
                        Devart.Data.Oracle.OracleParameter var1 = new Devart.Data.Oracle.OracleParameter();
                        //转换成非直连参数 梁吉 2016-06-08
                        var1.ParameterName = var.ParameterName;
                        var1.OracleDbType = GetOracleDevType(var.OracleDbType);
                        var1.Value = var.Value;
                        commandDev.Parameters.Add(var1);
                    }
                    int n = commandDev.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        return true;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        //Transaction.Rollback();
                        return false;
                        //throw new Exception("执行事务失败，影响行数零行");
                    }
                }
                catch (Exception ex)
                {
                    return false;//返回后处理事务回滚
                    //TransactionClient.Rollback();
                    //throw new Exception("执行事务失败", ex);
                }
            }
            else
            {
                try
                {
                    command.CommandText = sqlString;
                    command.CommandType = CommandType.Text;
                    foreach (OracleParameter var in Parameters)
                    {
                        command.Parameters.Add(var);
                    }
                    int n = command.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        return true;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的 返回后处理事务回滚
                        //Transaction.Rollback();
                        return false;
                        //throw new Exception("执行事务失败，影响行数零行");
                    }
                }
                catch (Exception ex)
                {
                    return false;
                    //Transaction.Rollback();
                    //throw new Exception("执行事务失败", ex);
                }
            }

        }
        /// <summary>
        /// 执行事务
        /// </summary>
        /// <param name="sqlString">语句</param>
        /// <param name="ParaNames">参数名</param>
        /// <param name="ParaTypes"></param>
        /// <param name="ParaValues">参数值列表</param>
        /// <param name="_IsolationLevel">锁定等级</param>
        /// <returns>true or false</returns>
        public bool ExecuteTransaction(String sqlString, List<String> ParaNames, List<OracleDbType> ParaTypes, List<String> ParaValues)
        {
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    commandClient.CommandText = sqlString;
                    commandClient.CommandType = CommandType.Text;
                    for (int k = 0; k < ParaNames.Count; k++)
                    {
                        System.Data.OracleClient.OracleParameter var1 = new System.Data.OracleClient.OracleParameter();
                        //转换成非直连参数 梁吉 2016-06-08
                        var1.ParameterName = ParaNames[k];
                        var1.OracleType = GetOracleType(ParaTypes[k]);
                        var1.Value = ParaValues[k];
                        commandClient.Parameters.Add(var1);
                    }

                    int n = commandClient.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        //TransactionClient.Commit();
                        return true;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的 返回后处理事务回滚
                        //TransactionClient.Rollback();
                        return false;
                    }

                }
                catch (Exception ex)
                {
                    return false;
                    //TransactionClient.Rollback();
                    //throw new Exception("执行事务失败，SQL:" + sqlString, ex);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    commandDev.CommandText = sqlString;
                    commandDev.CommandType = CommandType.Text;
                    for (int k = 0; k < ParaNames.Count; k++)
                    {
                        Devart.Data.Oracle.OracleParameter var1 = new Devart.Data.Oracle.OracleParameter();
                        //转换成非直连参数 梁吉 2016-06-08
                        var1.ParameterName = ParaNames[k];
                        var1.OracleDbType = GetOracleDevType(ParaTypes[k]);
                        var1.Value = ParaValues[k];
                        commandDev.Parameters.Add(var1);
                    }

                    int n = commandDev.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        //TransactionClient.Commit();
                        return true;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的 返回后处理事务回滚
                        //TransactionClient.Rollback();
                        return false;
                    }

                }
                catch (Exception ex)
                {
                    return false;
                    //TransactionClient.Rollback();
                    //throw new Exception("执行事务失败，SQL:" + sqlString, ex);
                }
            }
            else
            {
                try
                {
                    command.CommandText = sqlString;
                    command.CommandType = CommandType.Text;
                    for (int k = 0; k < ParaNames.Count; k++)
                    {
                        OracleParameter p = new OracleParameter();
                        p.ParameterName = ParaNames[k];
                        p.OracleDbType = ParaTypes[k];
                        p.Value = ParaValues[k];
                        command.Parameters.Add(p);
                    }
                    int n = command.ExecuteNonQuery();
                    if (n > 0)
                    {
                        //执行事务影响行数大于零
                        return true;
                    }
                    else
                    {
                        //执行事务影响行数等于零，这明显是有问题的
                        //Transaction.Rollback();
                        return false;
                        //throw new Exception("执行事务失败，影响行数零行");
                    }
                }
                catch (Exception ex)
                {
                    return false;
                    //Transaction.Rollback();
                    //throw new Exception("执行事务失败", ex);
                }
            }

        }
        /// <summary>数据更新
        /// 数据更新
        /// 优先提取ExtendedProperties属性中的SQL与Parameters键
        /// 然后是DisplayExpression，此时已默认为字符串SQL,不会再添加数据库参数
        /// 最后是= "select *from " + dt.TableName;
        /// </summary>
        /// <param name="dt">数据表入参DataTable，必须符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在，以及tablename是否存在数据库查询上的意义</param>
        /// <returns>成功更新的行数</returns>
        public int UpData_A_Data(DataTable dt)
        {
            String sqlString = "";
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    List<OracleParameter> Parameters = null;
                    //首先从自定义信息中获取查询语句
                    if (dt.ExtendedProperties.Count > 0)
                    {
                        //判断是否包含SQL
                        if (dt.ExtendedProperties.ContainsKey("SQL"))
                        {
                            sqlString = dt.ExtendedProperties["SQL"].ToString();
                        }
                        //判断是否包含参数
                        if (dt.ExtendedProperties.ContainsKey("Parameters"))
                        {
                            Parameters = dt.ExtendedProperties["Parameters"] as List<OracleParameter>;
                        }
                    }
                    else
                    {
                        //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                        sqlString = "select *from " + dt.TableName;

                    }
                    TransactionClient = connectionClient.BeginTransaction();
                    System.Data.OracleClient.OracleCommand cmd = connectionClient.CreateCommand(); //new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                    cmd.CommandText = sqlString;
                    if (Parameters != null)
                    {
                        foreach (OracleParameter var in Parameters)
                        {
                            System.Data.OracleClient.OracleParameter var1 = new System.Data.OracleClient.OracleParameter();
                            //转换成非直连参数 梁吉 2016-06-08
                            var1.ParameterName = var.ParameterName;
                            var1.OracleType = GetOracleType(var.OracleDbType);
                            var1.Value = var.Value;
                            cmd.Parameters.Add(var1);
                        }
                    }
                    System.Data.OracleClient.OracleDataAdapter rar = new System.Data.OracleClient.OracleDataAdapter(cmd);
                    System.Data.OracleClient.OracleCommandBuilder cmdBuiler = new System.Data.OracleClient.OracleCommandBuilder(rar);
                    int DBRows = -1;
                    using (DataTable temp = dt.GetChanges())
                    {
                        if (temp != null)
                        {
                            DBRows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            TransactionClient.Commit();
                            dt.AcceptChanges();
                        }
                    }
                    return DBRows;
                }
                catch (Exception ex)
                {
                    TransactionClient.Rollback();
                    throw new Exception("更新数据失败，SQL:" + sqlString, ex);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    List<OracleParameter> Parameters = null;
                    //首先从自定义信息中获取查询语句
                    if (dt.ExtendedProperties.Count > 0)
                    {
                        //判断是否包含SQL
                        if (dt.ExtendedProperties.ContainsKey("SQL"))
                        {
                            sqlString = dt.ExtendedProperties["SQL"].ToString();
                        }
                        //判断是否包含参数
                        if (dt.ExtendedProperties.ContainsKey("Parameters"))
                        {
                            Parameters = dt.ExtendedProperties["Parameters"] as List<OracleParameter>;
                        }
                    }
                    else
                    {
                        //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                        sqlString = "select *from " + dt.TableName;

                    }
                    TransactionDev = connectionDev.BeginTransaction();
                    Devart.Data.Oracle.OracleCommand cmd = connectionDev.CreateCommand();
                    cmd.CommandText = sqlString;
                    if (Parameters != null)
                    {
                        foreach (OracleParameter var in Parameters)
                        {
                            Devart.Data.Oracle.OracleParameter var1 = new Devart.Data.Oracle.OracleParameter();
                            //转换成非直连参数 梁吉 2016-06-08
                            var1.ParameterName = var.ParameterName;
                            var1.OracleDbType = GetOracleDevType(var.OracleDbType);
                            var1.Value = var.Value;
                            cmd.Parameters.Add(var1);
                        }
                    }
                    Devart.Data.Oracle.OracleDataAdapter rar = new Devart.Data.Oracle.OracleDataAdapter(cmd);
                    Devart.Data.Oracle.OracleCommandBuilder cmdBuiler = new Devart.Data.Oracle.OracleCommandBuilder(rar);
                    
                    int DBRows = -1;
                    using (DataTable temp = dt.GetChanges())
                    {
                        if (temp != null)
                        {
                            DBRows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            TransactionDev.Commit();
                            dt.AcceptChanges();
                        }
                    }
                    return DBRows;
                }
                catch (Exception ex)
                {
                    TransactionDev.Rollback();
                    throw new Exception("更新数据失败，SQL:" + sqlString, ex);
                }
            }
            else
            {
                try
                {
                    List<OracleParameter> Parameters = null;
                    //首先从自定义信息中获取查询语句
                    if (dt.ExtendedProperties.Count > 0)
                    {
                        //判断是否包含SQL
                        if (dt.ExtendedProperties.ContainsKey("SQL"))
                        {
                            sqlString = dt.ExtendedProperties["SQL"].ToString();
                        }
                        //判断是否包含参数
                        if (dt.ExtendedProperties.ContainsKey("Parameters"))
                        {
                            Parameters = dt.ExtendedProperties["Parameters"] as List<OracleParameter>;
                        }
                    }
                    else
                    {
                        //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数

                        sqlString = "select *from " + dt.TableName;
                        //throw new Exception("入参DataTable，不符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在");

                    }
                    Transaction = connection.BeginTransaction();
                    OracleCommand cmd = new OracleCommand(sqlString, connection);
                    if (Parameters != null)
                    {
                        foreach (OracleParameter var in Parameters)
                        {
                            cmd.Parameters.Add(var);
                        }
                    }
                    OracleDataAdapter rar = new OracleDataAdapter(cmd);
                    OracleCommandBuilder cmdBuiler = new OracleCommandBuilder(rar);
                    int DBRows = -1;
                    //if (dt.HasErrors)
                    //{
                    //    DataTable temp = dt;
                    //    DBRows = rar.Update(temp);
                    //    //2016 梁吉 提交上次表修改
                    //    temp.AcceptChanges();
                    //    Transaction.Commit();
                    //}
                    using (DataTable temp = dt.GetChanges())
                    {
                        if (temp != null)
                        {
                            DBRows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            TransactionDev.Commit();
                            dt.AcceptChanges();
                        }
                    }
                    return DBRows;

                }
                catch (Exception ex)
                {
                    Transaction.Rollback();
                    throw new Exception("更新数据失败，SQL:" + sqlString, ex);
                }
            }

        }
        /// <summary>数据更新
        /// 数据更新
        /// </summary>
        /// <param name="dt">数据表</param>
        /// <param name="sqlString">SQL语句</param>
        /// <returns>成功更新的行数</returns>
        public int UpData_A_Data(DataTable dt, String sqlString)
        {
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    System.Data.OracleClient.OracleCommand cmd = connectionClient.CreateCommand(); //new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                    cmd.CommandText = sqlString;
                    TransactionClient = connectionClient.BeginTransaction();
                    System.Data.OracleClient.OracleDataAdapter rar = new System.Data.OracleClient.OracleDataAdapter(cmd);
                    System.Data.OracleClient.OracleCommandBuilder cmdBuiler = new System.Data.OracleClient.OracleCommandBuilder(rar);
                    int DBRows = -1;
                    //if (dt.HasErrors)
                    //{
                    //    DataTable temp = dt;
                    //    DBRows = rar.Update(temp);
                    //    //2016 梁吉 提交上次表修改
                    //    temp.AcceptChanges();
                    //    TransactionClient.Commit();
                    //}
                    using (DataTable temp = dt.GetChanges())
                    {
                        if (temp != null)
                        {
                            DBRows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            TransactionClient.Commit();
                            dt.AcceptChanges();
                        }
                    }
                    return DBRows;
                }
                catch (Exception ex)
                {
                    TransactionClient.Rollback();
                    throw new Exception("更新数据失败，SQL:" + sqlString, ex);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    Devart.Data.Oracle.OracleCommand cmd = connectionDev.CreateCommand(); 
                    cmd.CommandText = sqlString;
                    TransactionDev = connectionDev.BeginTransaction();
                    Devart.Data.Oracle.OracleDataAdapter rar = new Devart.Data.Oracle.OracleDataAdapter(cmd);
                    Devart.Data.Oracle.OracleCommandBuilder cmdBuiler = new Devart.Data.Oracle.OracleCommandBuilder(rar);
                    int DBRows = 0;
                    //if (dt.HasErrors)
                    //{
                    //    DataTable temp = dt;
                    //    DBRows = rar.Update(temp);
                    //    //2016 梁吉 提交上次表修改
                    //    temp.AcceptChanges();
                    //    TransactionDev.Commit();
                    //}
                    using (DataTable temp = dt.GetChanges())
                    {
                        if (temp != null)
                        {
                            DBRows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            TransactionDev.Commit();
                            dt.AcceptChanges();
                        }
                    }
                    return DBRows;
                }
                catch (Exception ex)
                {
                    TransactionDev.Rollback();
                    throw new Exception("更新数据失败，SQL:" + sqlString, ex);
                }
            }
            else
            {
                try
                {
                    OracleCommand cmd = new OracleCommand(sqlString, connection);
                    Transaction = connection.BeginTransaction();
                    OracleDataAdapter rar = new OracleDataAdapter(cmd);
                    OracleCommandBuilder cmdBuiler = new OracleCommandBuilder(rar);
                    int DBRows = 0;
                    //if (dt.HasErrors)
                    //if (dt.HasErrors)
                    //{
                    //    DataTable temp = dt;
                    //    DBRows = rar.Update(temp);
                    //    //2016 梁吉 提交上次表修改
                    //    temp.AcceptChanges();
                    //    Transaction.Commit();
                    //}
                    using (DataTable temp = dt.GetChanges())
                    {
                        if (temp != null)
                        {
                            DBRows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            Transaction.Commit();
                            dt.AcceptChanges();
                        }
                    }
                    return DBRows;
                }
                catch (Exception ex)
                {
                    Transaction.Rollback();
                    throw new Exception("更新数据失败，SQL:" + sqlString, ex);
                }
            }

        }
        /// <summary>更新数据
        /// 更新数据 
        /// 优先提取ExtendedProperties属性中的SQL与Parameters键
        /// 然后是DisplayExpression，此时已默认为字符串SQL,不会再添加数据库参数
        /// 最后是= "select *from " + dt.TableName;
        /// </summary>
        /// <param name="ds">数据集包含的DataTable，必须符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在，以及tablename是否存在数据库查询上的意义</param>
        /// <returns>成功更新的行数</returns>
        public int UpData_A_Data(DataSet ds)
        {
            String sqlString = "";
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    TransactionClient = connectionClient.BeginTransaction();
                    int DBRows = 0;
                    foreach (DataTable table in ds.Tables)
                    {
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (table.ExtendedProperties.Count > 0)
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }
                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                            sqlString = "select *from " + table.TableName;
                            //throw new Exception("入参DataTable，不符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在");

                        }
                        System.Data.OracleClient.OracleCommand cmd = connectionClient.CreateCommand(); //new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                        cmd.CommandText = sqlString;
                        cmd.Transaction = TransactionClient;
                        System.Data.OracleClient.OracleDataAdapter rar = new System.Data.OracleClient.OracleDataAdapter(cmd);
                        System.Data.OracleClient.OracleCommandBuilder cmdBuiler = new System.Data.OracleClient.OracleCommandBuilder(rar);

                        //if (ds.HasChanges())
                        //{
                        //    DataTable temp = table;
                        //    DBRows = rar.Update(temp);
                        //    //2016 梁吉 提交上次表修改
                        //    temp.AcceptChanges();

                        //}
                        using (DataTable temp = table.GetChanges())
                        {
                            if (temp != null)
                            {
                                DBRows = rar.Update(temp);
                                //2016 梁吉 提交上次表修改
                            }
                            else
                            {
                                TransactionClient.Rollback();
                                return -2;//返回没有数据变化需要更新
                            }
                        }
                    }
                    TransactionClient.Commit();
                    ds.AcceptChanges();
                    return DBRows;

                }
                catch (Exception ex)
                {
                    TransactionClient.Rollback();
                    throw new Exception("更新数据失败，SQL:" + sqlString, ex);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    TransactionDev = connectionDev.BeginTransaction();
                    int DBRows = 0;
                    foreach (DataTable table in ds.Tables)
                    {
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (table.ExtendedProperties.Count > 0)
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }
                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                            sqlString = "select *from " + table.TableName;
                            //throw new Exception("入参DataTable，不符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在");

                        }
                        Devart.Data.Oracle.OracleCommand cmd = connectionDev.CreateCommand(); //new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                        cmd.CommandText = sqlString;
                        cmd.Transaction = TransactionDev;
                        Devart.Data.Oracle.OracleDataAdapter rar = new Devart.Data.Oracle.OracleDataAdapter(cmd);
                        Devart.Data.Oracle.OracleCommandBuilder cmdBuiler = new Devart.Data.Oracle.OracleCommandBuilder(rar);

                        //if (ds.HasChanges())
                        //{
                        //    DataTable temp = table;
                        //    DBRows = rar.Update(temp);
                        //    //2016 梁吉 提交上次表修改
                        //    temp.AcceptChanges();

                        //}
                        using (DataTable temp = table.GetChanges())
                        {
                            if (temp != null)
                            {
                                DBRows = rar.Update(temp);
                                //2016 梁吉 提交上次表修改
                            }
                            else
                            {
                                TransactionDev.Rollback();
                                return -2;//返回没有数据变化需要更新
                            }
                        }
                    }
                    TransactionDev.Commit();
                    ds.AcceptChanges();
                    return DBRows;

                }
                catch (Exception ex)
                {
                    TransactionDev.Rollback();
                    throw new Exception("更新数据失败，SQL:" + sqlString, ex);
                }
            }
            else
            {
                try
                {
                    Transaction = connection.BeginTransaction();
                    int DBRows = 0;

                    foreach (DataTable table in ds.Tables)
                    {
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (table.ExtendedProperties.Count > 0)
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }
                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                            sqlString = "select *from " + table.TableName;
                            //throw new Exception("入参DataTable，不符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在");

                        }
                        OracleCommand cmd = new OracleCommand(sqlString, connection);
                        OracleDataAdapter rar = new OracleDataAdapter(cmd);
                        OracleCommandBuilder cmdBuiler = new OracleCommandBuilder(rar);

                        //if (ds.HasChanges())
                        //{
                        //    DataTable temp = table;
                        //    DBRows += rar.Update(temp);
                        //    //2016 梁吉 提交上次表修改
                        //    temp.AcceptChanges();
                        //}
                        using (DataTable temp = table.GetChanges())
                        {
                            if (temp != null)
                            {
                                DBRows = rar.Update(temp);
                                //2016 梁吉 提交上次表修改
                            }
                            else
                            {
                                Transaction.Rollback();
                                return -2;//返回没有数据变化需要更新
                            }
                        }
                    }
                    Transaction.Commit();
                    ds.AcceptChanges();
                    return DBRows;
                }
                catch (Exception ex)
                {
                    Transaction.Rollback();
                    throw new Exception("更新数据失败，SQL:" + sqlString, ex);
                }
            }

        }
        /// <summary>
        /// 批量更新dataset
        /// </summary>
        /// <param name="dsList"></param>
        /// <returns></returns>
        public int UpData_A_DataSets(ArrayList dsList)
        {
            String sqlString = "";
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    TransactionClient = connectionClient.BeginTransaction();
                    int DBRows = 0;
                    foreach (Object obj in dsList)
                    {
                        DataSet ds = (DataSet)obj;
                        DataTable table = ds.Tables[0];
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (ds.ExtendedProperties.Count > 0)
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }
                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                            sqlString = "select * from " + table.TableName;
                            //throw new Exception("入参DataTable，不符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在");

                        }
                        System.Data.OracleClient.OracleCommand cmd = connectionClient.CreateCommand(); //new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                        cmd.CommandText = sqlString;
                        cmd.Transaction = TransactionClient;
                        System.Data.OracleClient.OracleDataAdapter rar = new System.Data.OracleClient.OracleDataAdapter(cmd);
                        System.Data.OracleClient.OracleCommandBuilder cmdBuiler = new System.Data.OracleClient.OracleCommandBuilder(rar);
                        int rows = 0;
                        if (ds.HasChanges())
                        {
                            using (DataTable temp = table.GetChanges())
                            {
                                rows = rar.Update(temp);
                                if (rows == 0)
                                {
                                    TransactionClient.Rollback();
                                    LogFile.WriteLogAuto("更新数据集失败|SQL:" + sqlString, "OracleBaseClass");
                                    return 0;
                                }
                                //2016 梁吉 提交上次表修改
                                //temp.AcceptChanges();
                                DBRows += rows;
                            }
                                
                        }
                        else
                        {
                            TransactionClient.Rollback();
                            LogFile.WriteLogAuto("更新数据集失败|数据集无变化:" + sqlString, "OracleBaseClass");
                            return -2;
                        }
                    }
                    TransactionClient.Commit();
                    foreach (Object obj in dsList)
                    {
                        DataSet ds = (DataSet)obj;
                        ds.AcceptChanges();
                    }
                    return DBRows;

                }
                catch (Exception ex)
                {
                    TransactionClient.Rollback();
                    throw new Exception("更新数据失败，SQL:" + sqlString, ex);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    TransactionDev = connectionDev.BeginTransaction();
                    int DBRows = 0;
                    foreach (Object obj in dsList)
                    {
                        DataSet ds = (DataSet)obj;
                        DataTable table = ds.Tables[0];
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (ds.ExtendedProperties.Count > 0)
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }
                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                            sqlString = "select * from " + table.TableName;
                            //throw new Exception("入参DataTable，不符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在");

                        }
                        Devart.Data.Oracle.OracleCommand cmd = connectionDev.CreateCommand(); //new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                        cmd.CommandText = sqlString;
                        cmd.Transaction = TransactionDev;
                        Devart.Data.Oracle.OracleDataAdapter rar = new Devart.Data.Oracle.OracleDataAdapter(cmd);
                        Devart.Data.Oracle.OracleCommandBuilder cmdBuiler = new Devart.Data.Oracle.OracleCommandBuilder(rar);
                        int rows = 0;
                        if (ds.HasChanges())
                        {
                            using (DataTable temp = table.GetChanges())
                            {
                                rows = rar.Update(temp);
                                if (rows == 0)
                                {
                                    TransactionDev.Rollback();
                                    LogFile.WriteLogAuto("更新数据集失败|SQL:" + sqlString, "OracleBaseClass");
                                    return 0;
                                }
                                //2016 梁吉 提交上次表修改
                                //temp.AcceptChanges();
                                DBRows += rows;
                            }
                        }
                        else
                        {
                            TransactionDev.Rollback();
                            LogFile.WriteLogAuto("更新数据集失败|数据集无变化:" + sqlString, "OracleBaseClass");
                            return -2;
                        }
                    }
                    TransactionDev.Commit();
                    foreach (Object obj in dsList)
                    {
                        DataSet ds = (DataSet)obj;
                        ds.AcceptChanges();
                    }
                    return DBRows;

                }
                catch (Exception ex)
                {
                    TransactionDev.Rollback();
                    throw new Exception("更新数据失败，SQL:" + sqlString, ex);
                }
            }
            else
            {
                Transaction = connection.BeginTransaction();
                try
                {

                    int DBRows = 0;
                    foreach (Object obj in dsList)
                    {
                        DataSet ds = (DataSet)obj;
                        DataTable table = ds.Tables[0];
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (ds.ExtendedProperties.Count > 0)
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }
                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                            sqlString = "select * from " + table.TableName;
                            //throw new Exception("入参DataTable，不符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在");

                        }
                        OracleCommand cmd = new OracleCommand(sqlString, connection);
                        OracleDataAdapter rar = new OracleDataAdapter(cmd);
                        OracleCommandBuilder cmdBuiler = new OracleCommandBuilder(rar);
                        int rows = 0;
                        if (ds.HasChanges())
                        {
                            //DataTable temp = table;
                            //rows = rar.Update(temp);
                            //if (rows == 0)
                            //{
                            //    Transaction.Rollback();
                            //    LogFile.WriteLogAuto("更新数据集失败|SQL:" + sqlString, "OracleBaseClass");
                            //    return 0;
                            //}
                            ////2016 梁吉 提交上次表修改
                            //temp.AcceptChanges();
                            using (DataTable temp = table.GetChanges())
                            {
                                rows = rar.Update(temp);
                                if (rows == 0)
                                {
                                    Transaction.Rollback();
                                    LogFile.WriteLogAuto("更新数据集失败|SQL:" + sqlString, "OracleBaseClass");
                                    return 0;
                                }
                                //2016 梁吉 提交上次表修改
                                //temp.AcceptChanges();
                                //DBRows += rows;
                            }
                            DBRows += rows;

                        }
                        else
                        {
                            Transaction.Rollback();
                            LogFile.WriteLogAuto("更新数据集失败|数据集无变化:" + sqlString, "OracleBaseClass");
                            return -2;
                        }
                    }

                    Transaction.Commit();
                    foreach (Object obj in dsList)
                    {
                        DataSet ds = (DataSet)obj;
                        ds.AcceptChanges();
                    }
                    return DBRows;
                }
                catch (Exception ex)
                {
                    Transaction.Rollback();
                    throw new Exception("更新数据失败，SQL:" + sqlString, ex);
                }
            }

        }

        /// <summary>
        /// 数据更新  事务中控制 2016-03-18 by 梁吉
        /// 优先提取ExtendedProperties属性中的SQL与Parameters键
        /// 然后是DisplayExpression，此时已默认为字符串SQL,不会再添加数据库参数
        /// 最后是= "select *from " + dt.TableName;
        /// </summary>
        /// <param name="dt">数据表入参DataTable，必须符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在，以及tablename是否存在数据库查询上的意义</param>
        /// <returns>成功更新的行数</returns>
        public int UpDate_Data(DataTable dt)
        {
            String sqlString = "";
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    List<OracleParameter> Parameters = null;
                    //首先从自定义信息中获取查询语句
                    if (dt.ExtendedProperties.Count > 0)
                    {
                        //判断是否包含SQL
                        if (dt.ExtendedProperties.ContainsKey("SQL"))
                        {
                            sqlString = dt.ExtendedProperties["SQL"].ToString();
                        }
                        //判断是否包含参数
                        if (dt.ExtendedProperties.ContainsKey("Parameters"))
                        {
                            Parameters = dt.ExtendedProperties["Parameters"] as List<OracleParameter>;
                        }
                    }
                    else
                    {
                        //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                        sqlString = "select * from " + dt.TableName;

                    }
                    //System.Data.OracleClient.OracleCommand cmd = connectionClient.CreateCommand(); //new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                    //cmd.CommandText = sqlString;
                    if (commandClient != null)
                    {
                        if (commandClient.Transaction == null)
                        {
                            commandClient.Transaction = TransactionClient;
                        }
                        commandClient.CommandText = sqlString;
                    }
                    else
                    {
                        commandClient = new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                    }
                    if (Parameters != null)
                    {
                        foreach (OracleParameter var in Parameters)
                        {
                            System.Data.OracleClient.OracleParameter var1 = new System.Data.OracleClient.OracleParameter();
                            //转换成非直连参数 梁吉 2016-06-08
                            var1.ParameterName = var.ParameterName;
                            var1.OracleType = GetOracleType(var.OracleDbType);
                            var1.Value = var.Value;
                            commandClient.Parameters.Add(var1);
                        }
                    }
                    System.Data.OracleClient.OracleDataAdapter rar = new System.Data.OracleClient.OracleDataAdapter(commandClient);
                    System.Data.OracleClient.OracleCommandBuilder cmdBuiler = new System.Data.OracleClient.OracleCommandBuilder(rar);
                    int DBRows = 0;
                    using (DataTable temp = dt.GetChanges())
                    {
                        if (temp != null)
                        {
                            DBRows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            if (DBRows > 0) dt.AcceptChanges();
                        }
                    }
                    //if (dt.GetChanges()!=null)
                    //{
                    //    DataTable temp = dt;
                    //    DBRows = rar.Update(temp);
                    //    //2016 梁吉 提交上次表修改
                    //    temp.AcceptChanges();
                    //}
                    return DBRows;
                }
                catch (Exception ex)
                {
                    return -1;//异常更新
                    //TransactionClient.Rollback();
                    //throw new Exception("更新数据失败，SQL:" + ex.Message);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    List<OracleParameter> Parameters = null;
                    //首先从自定义信息中获取查询语句
                    if (dt.ExtendedProperties.Count > 0)
                    {
                        //判断是否包含SQL
                        if (dt.ExtendedProperties.ContainsKey("SQL"))
                        {
                            sqlString = dt.ExtendedProperties["SQL"].ToString();
                        }
                        //判断是否包含参数
                        if (dt.ExtendedProperties.ContainsKey("Parameters"))
                        {
                            Parameters = dt.ExtendedProperties["Parameters"] as List<OracleParameter>;
                        }
                    }
                    else
                    {
                        sqlString = "select * from " + dt.TableName;

                    }
                    if (commandDev != null)
                    {
                        if (commandDev.Transaction == null)
                        {
                            commandDev.Transaction = TransactionDev;
                        }
                        commandDev.CommandText = sqlString;
                    }
                    else
                    {
                        commandDev = new Devart.Data.Oracle.OracleCommand(sqlString, connectionDev);
                    }
                    if (Parameters != null)
                    {
                        foreach (OracleParameter var in Parameters)
                        {
                            Devart.Data.Oracle.OracleParameter var1 = new Devart.Data.Oracle.OracleParameter();
                            //转换成非直连参数 梁吉 2016-06-08
                            var1.ParameterName = var.ParameterName;
                            var1.OracleDbType = GetOracleDevType(var.OracleDbType);
                            var1.Value = var.Value;
                            commandDev.Parameters.Add(var1);
                        }
                    }
                    else
                    {
                        commandDev.Parameters.Clear();
                    }
                    Devart.Data.Oracle.OracleDataAdapter rar = new Devart.Data.Oracle.OracleDataAdapter(commandDev);
                    Devart.Data.Oracle.OracleCommandBuilder cmdBuiler = new Devart.Data.Oracle.OracleCommandBuilder(rar);
                    int DBRows = 0;
                    using (DataTable temp = dt.GetChanges())
                    {
                        if (temp != null)
                        {
                            DBRows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            if (DBRows > 0) dt.AcceptChanges();
                        }
                    }
                    //if (dt.GetChanges() != null)
                    //{
                    //    DataTable temp = dt;
                    //    DBRows = rar.Update(temp);
                    //    //2016 梁吉 提交上次表修改
                    //    temp.AcceptChanges();
                    //}
                    return DBRows;
                }
                catch (Exception ex)
                {
                    return -1;//异常更新
                    //TransactionClient.Rollback();
                    //throw new Exception("更新数据失败，SQL:" + ex.Message);
                }
            }
            else
            {
                try
                {

                    List<OracleParameter> Parameters = null;
                    //首先从自定义信息中获取查询语句
                    if (dt.ExtendedProperties.Count > 0)
                    {
                        //判断是否包含SQL
                        if (dt.ExtendedProperties.ContainsKey("SQL"))
                        {
                            sqlString = dt.ExtendedProperties["SQL"].ToString();
                        }
                        //判断是否包含参数
                        if (dt.ExtendedProperties.ContainsKey("Parameters"))
                        {
                            Parameters = dt.ExtendedProperties["Parameters"] as List<OracleParameter>;
                        }
                    }
                    else
                    {
                        //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                        sqlString = "select * from " + dt.TableName;
                        //throw new Exception("入参DataTable，不符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在");

                    }
                    //OracleCommand cmd = new OracleCommand(sqlString, connection);
                    if (command != null)
                    {
                        if (command.Transaction == null)
                        {
                            command.Transaction = Transaction;
                        }
                        command.CommandText = sqlString;
                    }
                    else
                    {
                        command = new OracleCommand(sqlString, connection);
                    }
                    if (Parameters != null)
                    {
                        foreach (OracleParameter var in Parameters)
                        {
                            command.Parameters.Add(var.Clone());
                        }
                    }
                    OracleDataAdapter rar = new OracleDataAdapter(command);
                    OracleCommandBuilder cmdBuiler = new OracleCommandBuilder(rar);
                    int DBRows = 0;
                    using (DataTable temp = dt.GetChanges())
                    {
                        if (temp != null)
                        {
                            DBRows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            if (DBRows > 0) dt.AcceptChanges();
                        }
                        
                    }
                    //if (dt.GetChanges()!=null)
                    //{
                    //    DataTable temp = dt;
                    //    DBRows = rar.Update(temp);
                    //    //2016 梁吉 提交上次表修改
                    //    temp.AcceptChanges();
                    //}
                    return DBRows;
                }
                catch (Exception ex)
                {
                    //throw new Exception("更新数据失败，SQL:" + ex.Message);
                    return -1;//更新异常
                    //Transaction.Rollback();
                    
                }
            }
        }
        /// <summary>数据更新
        /// 数据更新  事务中控制 2016-03-18 by 梁吉
        /// </summary>
        /// <param name="dt">数据表</param>
        /// <param name="sqlString">SQL语句</param>
        /// <returns>成功更新的行数</returns>
        public int UpDate_Data(DataTable dt, String sqlString)
        {
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    //System.Data.OracleClient.OracleCommand cmd = new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                    //System.Data.OracleClient.OracleCommand cmd = connectionClient.CreateCommand(); //new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                    //cmd.CommandText = sqlString;
                    if (commandClient != null)
                    {
                        if (commandClient.Transaction == null)
                        {
                            commandClient.Transaction = TransactionClient;
                        }
                        commandClient.CommandText = sqlString;
                    }
                    else
                    {
                        commandClient = new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                    }
                    System.Data.OracleClient.OracleDataAdapter rar = new System.Data.OracleClient.OracleDataAdapter(commandClient);
                    System.Data.OracleClient.OracleCommandBuilder cmdBuiler = new System.Data.OracleClient.OracleCommandBuilder(rar);
                    int DBRows = 0;

                    using (DataTable temp = dt.GetChanges())
                    {
                        if (temp != null)
                        {
                            DBRows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            if (DBRows > 0) dt.AcceptChanges();
                        }
                        
                    }
                    //if (dt.HasErrors)
                    //{
                    //    DataTable temp = dt;
                    //    DBRows = rar.Update(temp);
                    //    //2016 梁吉 提交上次表修改
                    //    temp.AcceptChanges();
                    //}
                    return DBRows;
                }
                catch (Exception ex)
                {
                    //return -1;
                    //TransactionClient.Rollback();
                    throw new Exception("更新数据失败，SQL:" + ex.Message);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    if (commandDev != null)
                    {
                        if (commandDev.Transaction == null)
                        {
                            commandDev.Transaction = TransactionDev;
                        }
                        commandDev.CommandText = sqlString;
                    }
                    else
                    {
                        commandDev = new Devart.Data.Oracle.OracleCommand(sqlString,connectionDev);
                    }
                    Devart.Data.Oracle.OracleDataAdapter rar = new Devart.Data.Oracle.OracleDataAdapter(commandDev);
                    Devart.Data.Oracle.OracleCommandBuilder cmdBuiler = new Devart.Data.Oracle.OracleCommandBuilder(rar);
                    int DBRows = 0;
                    using (DataTable temp = dt.GetChanges())
                    {
                        if (temp != null)
                        {
                            DBRows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            if (DBRows > 0) dt.AcceptChanges();
                        }

                    }
                    //if (dt.HasErrors)
                    //{
                    //    DataTable temp = dt;
                    //    DBRows = rar.Update(temp);
                    //    //2016 梁吉 提交上次表修改
                    //    temp.AcceptChanges();
                    //}
                    return DBRows;
                }
                catch (Exception ex)
                {
                    //return -1;
                    //TransactionClient.Rollback();
                    throw new Exception("更新数据失败，SQL:" + ex.Message);
                }
            }
            else
            {
                try
                {
                    //OracleCommand cmd = new OracleCommand(sqlString, connection);
                    if (command != null)
                    {
                        if (command.Transaction == null)
                        {
                            command.Transaction = Transaction;
                        }
                        command.CommandText = sqlString;
                    }
                    else
                    {
                        command = new OracleCommand(sqlString, connection);
                    }
                    OracleDataAdapter rar = new OracleDataAdapter(command);
                    OracleCommandBuilder cmdBuiler = new OracleCommandBuilder(rar);
                    int DBRows = 0;
                    using (DataTable temp = dt.GetChanges())
                    {
                        if (temp != null)
                        {
                            DBRows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            if (DBRows > 0) dt.AcceptChanges();
                        }

                    }
                    //if (dt.HasErrors)
                    //{
                    //    DataTable temp = dt;
                    //    DBRows = rar.Update(temp);
                    //    //2016 梁吉 提交上次表修改
                    //    temp.AcceptChanges();
                    //}
                    return DBRows;
                }
                catch (Exception ex)
                {
                    return -1;//更新异常
                    //Transaction.Rollback();
                    //throw new Exception("更新数据失败，SQL:" + sqlString, ex);
                }
            }

        }
        /// <summary>更新数据
        /// 更新数据   事务中控制 2016-03-18 by 梁吉
        /// 优先提取ExtendedProperties属性中的SQL与Parameters键
        /// 然后是DisplayExpression，此时已默认为字符串SQL,不会再添加数据库参数
        /// 最后是= "select *from " + dt.TableName;
        /// </summary>
        /// <param name="ds">数据集包含的DataTable，必须符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在，以及tablename是否存在数据库查询上的意义</param>
        /// <returns>成功更新的行数</returns>
        public int UpDate_Data(DataSet ds,ref string errText)
        {
            String sqlString = "";
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    int DBRows = 0;
                    foreach (DataTable table in ds.Tables)
                    {
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (table.ExtendedProperties.Count > 0)
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }
                            else
                            {
                                sqlString = "select * from " + table.TableName;
                            }
                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                            sqlString = "select * from " + table.TableName;
                            //throw new Exception("入参DataTable，不符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在");

                        }
                        //System.Data.OracleClient.OracleCommand cmd = connectionClient.CreateCommand(); //new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                        //cmd.CommandText = sqlString;
                        if (commandClient != null)
                        {
                            if (commandClient.Transaction == null)
                            {
                                commandClient.Transaction = TransactionClient;
                            }
                            commandClient.CommandText = sqlString;
                            commandClient.Parameters.Clear();
                        }
                        else
                        {
                            commandClient = new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                        }
                        if (Parameters != null)
                        {
                            foreach (OracleParameter var in Parameters)
                            {
                                System.Data.OracleClient.OracleParameter var1 = new System.Data.OracleClient.OracleParameter();
                                //转换成非直连参数 梁吉 2016-06-08
                                var1.ParameterName = var.ParameterName;
                                var1.OracleType = GetOracleType(var.OracleDbType);
                                var1.Value = var.Value;
                                commandClient.Parameters.Add(var1);
                            }
                        }
                        System.Data.OracleClient.OracleDataAdapter rar = new System.Data.OracleClient.OracleDataAdapter(commandClient);
                        System.Data.OracleClient.OracleCommandBuilder cmdBuiler = new System.Data.OracleClient.OracleCommandBuilder(rar);
                        DataTable temp = table.GetChanges();
                        if (temp!=null)
                        {
                            int rows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            //table.AcceptChanges();
                            if (rows <= 0)
                            {
                                LogFile.WriteLogAuto(sqlString+"|更新数据集错误","ORACLEBASECLASS");
                                errText= sqlString + "|更新数据集错误01";
                                return -2;
                            }
                            DBRows += rows;
                        }
                        //if (ds.HasChanges())
                        //{
                        //    DataTable temp = table;
                        //    DBRows = rar.Update(temp);
                        //    //2016 梁吉 提交上次表修改
                        //    temp.AcceptChanges();

                        //}

                    }
                    ds.AcceptChanges();
                    return DBRows;

                }
                catch (Exception ex)
                {
                    LogFile.WriteLogAuto(sqlString + "|更新数据集异常"+ex.Message, "ORACLEBASECLASS");
                    errText = sqlString + "|更新数据集异常01" + ex.Message;
                    return -1;
                    //TransactionClient.Rollback();
                    //throw new Exception("更新数据失败，SQL:" + ex.Message);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    int DBRows = 0;
                    foreach (DataTable table in ds.Tables)
                    {
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (table.ExtendedProperties.Count > 0)
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }
                            else
                            {
                                sqlString = "select * from " + table.TableName;
                            }
                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            sqlString = "select * from " + table.TableName;
                        }
                        if (commandDev != null)
                        {
                            if (commandDev.Transaction == null)
                            {
                                commandDev.Transaction = TransactionDev;
                            }
                            commandDev.CommandText = sqlString;
                            commandDev.Parameters.Clear();
                        }
                        else
                        {
                            commandDev = new Devart.Data.Oracle.OracleCommand(sqlString, connectionDev);
                        }
                        if (Parameters != null)
                        {
                            foreach (OracleParameter var in Parameters)
                            {
                                Devart.Data.Oracle.OracleParameter var1 = new Devart.Data.Oracle.OracleParameter();
                                //转换成非直连参数 梁吉 2016-06-08
                                var1.ParameterName = var.ParameterName;
                                var1.OracleDbType = GetOracleDevType(var.OracleDbType);
                                var1.Value = var.Value;
                                commandDev.Parameters.Add(var1);
                            }
                        }
                        Devart.Data.Oracle.OracleDataAdapter rar = new Devart.Data.Oracle.OracleDataAdapter(commandDev);
                        Devart.Data.Oracle.OracleCommandBuilder cmdBuiler = new Devart.Data.Oracle.OracleCommandBuilder(rar);
                        DataTable temp = table.GetChanges();
                        if (temp != null)
                        {
                            int rows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            //table.AcceptChanges();
                            if (rows <= 0)
                            {
                                LogFile.WriteLogAuto(sqlString + "|更新数据集错误02", "ORACLEBASECLASS");
                                errText = sqlString + "|更新数据集错误02"  ;
                                return -2;
                            }
                            DBRows += rows;
                        }
                        //if (ds.HasChanges())
                        //{
                        //    DataTable temp = table;
                        //    DBRows = rar.Update(temp);
                        //    //2016 梁吉 提交上次表修改
                        //    temp.AcceptChanges();

                        //}

                    }
                    ds.AcceptChanges();
                    return DBRows;

                }
                catch (Exception ex)
                {
                    LogFile.WriteLogAuto(ex.Message + "|更新数据集异常", "ORACLEBASECLASS");
                    errText = "|更新数据集异常02" + ex.Message;
                    return -1;
                    //TransactionClient.Rollback();
                    //throw new Exception("更新数据失败，SQL:" + ex.Message);
                }
            }
            else
            {
                try
                {
                    int DBRows = 0;
                    foreach (DataTable table in ds.Tables)
                    {
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (table.ExtendedProperties.Count > 0)
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }
                            else
                            {
                                sqlString = "select * from " + table.TableName;
                            }
                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                            sqlString = "select * from " + table.TableName;
                            //throw new Exception("入参DataTable，不符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在");

                        }
                        if (command != null)
                        {
                            if (command.Transaction == null)
                            {
                                command.Transaction = Transaction;
                            }
                            command.CommandText = sqlString;
                            
                        }
                        else
                        {
                            command = new OracleCommand(sqlString, connection);
                        }
                        if (Parameters !=null && Parameters.Count > 0)
                        {
                            command.Parameters.Clear();
                            command.Parameters.AddRange(Parameters.ToArray());
                        }
                        //OracleCommand cmd = new OracleCommand(sqlString, connection);
                        //OracleDataAdapter rar = new OracleDataAdapter(cmd);
                        OracleDataAdapter rar = new OracleDataAdapter(command);
                        OracleCommandBuilder cmdBuiler = new OracleCommandBuilder(rar);
                        DataTable temp = table.GetChanges();
                        if (temp != null)
                        {
                            int rows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            //table.AcceptChanges();
                            if (rows <= 0)
                            {
                                LogFile.WriteLogAuto(sqlString + "|更新数据集错误", "ORACLEBASECLASS");
                                errText = sqlString + "|更新数据集错误03";
                                return -2;
                            }
                            DBRows += rows;
                        }
                        //if (ds.HasChanges())
                        //{
                        //    DataTable temp = table;
                        //    DBRows += rar.Update(temp);
                        //    //2016 梁吉 提交上次表修改
                        //    temp.AcceptChanges();
                        //}
                    }
                    ds.AcceptChanges();
                    return DBRows;
                }
                catch (Exception ex)
                {
                    //Transaction.Rollback();
                    LogFile.WriteLogAuto(ex.Message+ "|更新数据集异常", "ORACLEBASECLASS");
                    errText =  "|更新数据集错误03"+ ex.Message;
                    return -1;
                    //throw new Exception("更新数据失败，SQL:" + ex.Message);
                }
            }

        }
        /// <summary>
        /// 批量更新dataset  事务中控制 2016-03-18 by 梁吉 20220412 modify by pwf
        /// </summary>
        /// <param name="dsList"></param>
        /// <returns></returns>
        public int UpDate_DataSets(ArrayList dsList, ref string errText , List<DataRow> drsChange = null)
        {
            String sqlString = "";

            // 调整成：按顺序更新变更行：删除、调整、添加   梁吉 2022-04-13
            DataRowState[] rowStates = { DataRowState.Deleted, DataRowState.Modified, DataRowState.Added };

            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    int DBRows = 0;
                    foreach (Object obj in dsList)
                    {
                        // 跳过无调整的ds
                        DataSet ds = (DataSet)obj;
                        if (!ds.HasChanges()) continue;

                        DataTable table = ds.Tables[0];
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (table.ExtendedProperties.Count > 0 || table.ExtendedProperties.ContainsKey("UpdateSQL"))
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("UpdateSQL"))
                            {
                                sqlString = table.ExtendedProperties["UpdateSQL"].ToString();
                            }
                            else if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }

                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                            sqlString = "select * from " + table.TableName;
                            //throw new Exception("入参DataTable，不符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在");

                        }
                        //System.Data.OracleClient.OracleCommand cmd = connectionClient.CreateCommand(); //new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                        //cmd.CommandText = sqlString;
                        if (commandClient != null)
                        {
                            if (commandClient.Transaction == null)
                            {
                                commandClient.Transaction = TransactionClient;
                            }
                            commandClient.CommandText = sqlString;
                        }
                        else
                        {
                            commandClient = new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                        }
                        System.Data.OracleClient.OracleDataAdapter rar = new System.Data.OracleClient.OracleDataAdapter(commandClient);
                        System.Data.OracleClient.OracleCommandBuilder cmdBuiler = new System.Data.OracleClient.OracleCommandBuilder(rar);
                        int rows = 0;
                        //if (ds.HasChanges())
                        //{
                        //    DataTable temp = table;
                        //    rows = rar.Update(temp);
                        //    if (rows == 0)
                        //    {
                        //        LogFile.WriteLogAuto("更新数据集失败|SQL:" + sqlString, "OracleBaseClass");
                        //        return 0;
                        //    }
                        //    //2016 梁吉 提交上次表修改
                        //    temp.AcceptChanges();
                        //    DBRows += rows;
                        //}
                        List<DataRow> drs = (drsChange != null && drsChange.Count > 0 && drsChange[0].Table == table) ? drsChange : table.AsEnumerable().ToList();
                        foreach (DataRowState state in rowStates)
                        {
                            if (drs.FirstOrDefault(r => r.RowState == state) == null) continue;

                            //2016 梁吉 提交上次表修改
                            rows = rar.Update(drs.Where(r => r.RowState == state).ToArray());
                            if (rows < 0)
                            {
                                LogFile.WriteLogAuto(sqlString + "|rows=" + rows + "更新数据集UpDate_DataSets错误", "ORACLEBASECLASS");
                                errText = sqlString + "|rows=" + rows + "更新数据集UpDate_DataSets错误";
                                return -2;
                            }
                            DBRows += rows;
                        }
                    }
                    foreach (Object obj in dsList)
                    {
                        DataSet ds = (DataSet)obj;
                        if (drsChange != null && drsChange.Count > 0 && drsChange[0].Table.DataSet == ds)
                        {
                            drsChange.ForEach(r => {
                                if (r.RowState == DataRowState.Added || r.RowState == DataRowState.Modified) r.AcceptChanges();
                            });
                        }
                        else
                        {
                            ds.AcceptChanges();
                        }
                    }
                    return DBRows;

                }
                catch (Exception ex)
                {
                    LogFile.WriteLogAuto(ex.Message + "|更新数据集UpDate_DataSets异常", "ORACLEBASECLASS");
                    errText = ex.Message + "|更新数据集UpDate_DataSets异常";
                    return -1;
                    //TransactionClient.Rollback();
                    //throw new Exception("更新数据失败，SQL:" + ex.Message);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    int DBRows = 0;
                    foreach (Object obj in dsList)
                    {
                        DataSet ds = (DataSet)obj;
                        if (!ds.HasChanges()) continue;

                        DataTable table = ds.Tables[0];
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (table.ExtendedProperties.Count > 0 || table.ExtendedProperties.ContainsKey("UpdateSQL"))
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("UpdateSQL"))
                            {
                                sqlString = table.ExtendedProperties["UpdateSQL"].ToString();
                            }
                            else if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }
                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                            sqlString = "select * from " + table.TableName;

                        }
                        if (commandDev != null)
                        {
                            if (commandDev.Transaction == null)
                            {
                                commandDev.Transaction = TransactionDev;
                            }
                            commandDev.CommandText = sqlString;
                        }
                        else
                        {
                            commandDev = new Devart.Data.Oracle.OracleCommand(sqlString, connectionDev);
                        }
                        Devart.Data.Oracle.OracleDataAdapter rar = new Devart.Data.Oracle.OracleDataAdapter(commandDev);
                        Devart.Data.Oracle.OracleCommandBuilder cmdBuiler = new Devart.Data.Oracle.OracleCommandBuilder(rar);
                        int rows = 0;

                        List<DataRow> drs = (drsChange != null && drsChange.Count > 0 && drsChange[0].Table == table) ? drsChange : table.AsEnumerable().ToList();
                        foreach (DataRowState state in rowStates)
                        {
                            if (drs.FirstOrDefault(r => r.RowState == state) == null) continue;

                            //2016 梁吉 提交上次表修改
                            rows = rar.Update(drs.Where(r => r.RowState == state).ToArray());
                            if (rows < 0)
                            {
                                LogFile.WriteLogAuto(sqlString + "|rows=" + rows + "更新数据集UpDate_DataSets错误", "ORACLEBASECLASS");
                                errText = sqlString + "|rows=" + rows + "更新数据集UpDate_DataSets错误";
                                return -2;
                            }
                            DBRows += rows;
                        }
                    }
                    foreach (Object obj in dsList)
                    {
                        DataSet ds = (DataSet)obj;
                        if (drsChange != null && drsChange.Count > 0 && drsChange[0].Table.DataSet == ds)
                        {
                            drsChange.ForEach(r => {
                                if (r.RowState == DataRowState.Added || r.RowState == DataRowState.Modified) r.AcceptChanges();
                            });
                        }
                        else
                        {
                            ds.AcceptChanges();
                        }
                    }
                    return DBRows;

                }
                catch (Exception ex)
                {
                    LogFile.WriteLogAuto(ex.Message + "|更新数据集UpDate_DataSets异常", "ORACLEBASECLASS");
                    errText = ex.Message + "|更新数据集UpDate_DataSets异常";
                    return -1;
                    //TransactionClient.Rollback();
                    //throw new Exception("更新数据失败，SQL:" + ex.Message);
                }
            }
            else
            {
                try
                {
                    int DBRows = 0;
                    foreach (Object obj in dsList)
                    {
                        DataSet ds = (DataSet)obj;
                        if (!ds.HasChanges()) continue;

                        DataTable table = ds.Tables[0];
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (table.ExtendedProperties.Count > 0 || table.ExtendedProperties.ContainsKey("UpdateSQL"))
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("UpdateSQL"))
                            {
                                sqlString = table.ExtendedProperties["UpdateSQL"].ToString();
                            }
                            else if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }
                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                            sqlString = "select * from " + table.TableName;
                            //throw new Exception("入参DataTable，不符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在");

                        }
                        OracleCommand cmd = new OracleCommand(sqlString, connection);
                        OracleDataAdapter rar = new OracleDataAdapter(cmd);
                        OracleCommandBuilder cmdBuiler = new OracleCommandBuilder(rar);
                        int rows = 0;

                        List<DataRow> drs = (drsChange != null && drsChange.Count > 0 && drsChange[0].Table == table) ? drsChange : table.AsEnumerable().ToList();
                        foreach (DataRowState state in rowStates)
                        {
                            if (drs.FirstOrDefault(r => r.RowState == state) == null) continue;

                            //2016 梁吉 提交上次表修改
                            rows = rar.Update(drs.Where(r => r.RowState == state).ToArray());
                            if (rows < 0)
                            {
                                LogFile.WriteLogAuto(sqlString + "|rows=" + rows + "更新数据集UpDate_DataSets错误", "ORACLEBASECLASS");
                                errText = sqlString + "|rows=" + rows + "更新数据集UpDate_DataSets错误";
                                return -2;
                            }
                            DBRows += rows;
                        }
                    }
                    foreach (Object obj in dsList)
                    {
                        DataSet ds = (DataSet)obj;
                        if (drsChange != null && drsChange.Count > 0 && drsChange[0].Table.DataSet == ds)
                        {
                            drsChange.ForEach(r => {
                                if (r.RowState == DataRowState.Added || r.RowState == DataRowState.Modified) r.AcceptChanges();
                            });
                        }
                        else
                        {
                            ds.AcceptChanges();
                        }
                    }
                    return DBRows;
                }
                catch (Exception ex)
                {
                    LogFile.WriteLogAuto(ex.Message + "|更新数据集UpDate_DataSets异常", "ORACLEBASECLASS");
                    errText = ex.Message + "|更新数据集UpDate_DataSets异常";
                    return -1;
                    //Transaction.Rollback();
                    //throw new Exception("更新数据失败，SQL:" + ex.Message);
                }
            }
        }
        /// <summary>
        /// 更新ds数据集列表，返回更新的ds数量，如果异常回滚事务
        /// </summary>
        /// <param name="dsList"></param>
        /// <param name="drsChange"></param>
        /// <returns></returns>
        public int UpDate_DsArray(ArrayList dsList,ref string errText, List<DataRow> drsChange = null)
        {
            String sqlString = "";

            // 调整成：按顺序更新变更行：删除、调整、添加   梁吉 2022-04-13
            DataRowState[] rowStates = { DataRowState.Deleted, DataRowState.Modified, DataRowState.Added };
            int DsCount = 0;
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    
                    foreach (Object obj in dsList)
                    {
                        // 跳过无调整的ds
                        DataSet ds = (DataSet)obj;
                        if (!ds.HasChanges()) continue;

                        DataTable table = ds.Tables[0];
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (table.ExtendedProperties.Count > 0 || table.ExtendedProperties.ContainsKey("UpdateSQL"))
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("UpdateSQL"))
                            {
                                sqlString = table.ExtendedProperties["UpdateSQL"].ToString();
                            }
                            else if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }

                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                            sqlString = "select * from " + table.TableName;
                            //throw new Exception("入参DataTable，不符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在");

                        }
                        //System.Data.OracleClient.OracleCommand cmd = connectionClient.CreateCommand(); //new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                        //cmd.CommandText = sqlString;
                        if (commandClient != null)
                        {
                            if (commandClient.Transaction == null)
                            {
                                commandClient.Transaction = TransactionClient;
                            }
                            commandClient.CommandText = sqlString;
                        }
                        else
                        {
                            commandClient = new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                        }
                        System.Data.OracleClient.OracleDataAdapter rar = new System.Data.OracleClient.OracleDataAdapter(commandClient);
                        System.Data.OracleClient.OracleCommandBuilder cmdBuiler = new System.Data.OracleClient.OracleCommandBuilder(rar);
                        int rows = 0;
                        List<DataRow> drs = (drsChange != null && drsChange.Count > 0 && drsChange[0].Table == table) ? drsChange : table.AsEnumerable().ToList();
                        foreach (DataRowState state in rowStates)
                        {
                            if (drs.FirstOrDefault(r => r.RowState == state) == null) continue;

                            //2016 梁吉 提交上次表修改
                            rows = rar.Update(drs.Where(r => r.RowState == state).ToArray());
                            if (rows < 0)
                            {
                                LogFile.WriteLogAuto(sqlString + "|rows=" + rows + "更新数据集UpDate_DataSets错误", "ORACLEBASECLASS");
                                errText = sqlString + "|rows=" + rows + "更新数据集UpDate_DataSets错误";
                                return -2;
                            }
                        }
                    }

                }
                catch (Exception ex)
                {
                    LogFile.WriteLogAuto(ex.Message + "|更新数据集UpDate_DataSets异常", "ORACLEBASECLASS");
                    errText = ex.Message + "|更新数据集UpDate_DataSets异常";
                    return -1;
                    //TransactionClient.Rollback();
                    //throw new Exception("更新数据失败，SQL:" + ex.Message);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    foreach (Object obj in dsList)
                    {
                        DataSet ds = (DataSet)obj;
                        if (!ds.HasChanges()) continue;

                        DataTable table = ds.Tables[0];
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (table.ExtendedProperties.Count > 0 || table.ExtendedProperties.ContainsKey("UpdateSQL"))
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("UpdateSQL"))
                            {
                                sqlString = table.ExtendedProperties["UpdateSQL"].ToString();
                            }
                            else if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }
                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                            sqlString = "select * from " + table.TableName;

                        }
                        if (commandDev != null)
                        {
                            if (commandDev.Transaction == null)
                            {
                                commandDev.Transaction = TransactionDev;
                            }
                            commandDev.CommandText = sqlString;
                        }
                        else
                        {
                            commandDev = new Devart.Data.Oracle.OracleCommand(sqlString, connectionDev);
                        }
                        Devart.Data.Oracle.OracleDataAdapter rar = new Devart.Data.Oracle.OracleDataAdapter(commandDev);
                        Devart.Data.Oracle.OracleCommandBuilder cmdBuiler = new Devart.Data.Oracle.OracleCommandBuilder(rar);
                        int rows = 0;

                        List<DataRow> drs = (drsChange != null && drsChange.Count > 0 && drsChange[0].Table == table) ? drsChange : table.AsEnumerable().ToList();
                        foreach (DataRowState state in rowStates)
                        {
                            if (drs.FirstOrDefault(r => r.RowState == state) == null) continue;

                            //2016 梁吉 提交上次表修改
                            rows = rar.Update(drs.Where(r => r.RowState == state).ToArray());
                            if (rows < 0)
                            {
                                LogFile.WriteLogAuto(sqlString + "|rows=" + rows + "更新数据集UpDate_DataSets错误", "ORACLEBASECLASS");
                                errText = sqlString + "|rows=" + rows + "更新数据集UpDate_DataSets错误";
                                return -2;
                            }
                        }
                    }
                    

                }
                catch (Exception ex)
                {
                    LogFile.WriteLogAuto(ex.Message + "|更新数据集UpDate_DataSets异常", "ORACLEBASECLASS");
                    errText = ex.Message + "|更新数据集UpDate_DataSets异常";
                    return -1;
                    //TransactionClient.Rollback();
                    //throw new Exception("更新数据失败，SQL:" + ex.Message);
                }
            }
            else
            {
                try
                {
                    foreach (Object obj in dsList)
                    {
                        DataSet ds = (DataSet)obj;
                        if (!ds.HasChanges()) continue;

                        DataTable table = ds.Tables[0];
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (table.ExtendedProperties.Count > 0 || table.ExtendedProperties.ContainsKey("UpdateSQL"))
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("UpdateSQL"))
                            {
                                sqlString = table.ExtendedProperties["UpdateSQL"].ToString();
                            }
                            else if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }
                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                            sqlString = "select * from " + table.TableName;
                            //throw new Exception("入参DataTable，不符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在");

                        }
                        OracleCommand cmd = new OracleCommand(sqlString, connection);
                        OracleDataAdapter rar = new OracleDataAdapter(cmd);
                        OracleCommandBuilder cmdBuiler = new OracleCommandBuilder(rar);
                        int rows = 0;

                        List<DataRow> drs = (drsChange != null && drsChange.Count > 0 && drsChange[0].Table == table) ? drsChange : table.AsEnumerable().ToList();
                        foreach (DataRowState state in rowStates)
                        {
                            if (drs.FirstOrDefault(r => r.RowState == state) == null) continue;

                            //2016 梁吉 提交上次表修改
                            rows = rar.Update(drs.Where(r => r.RowState == state).ToArray());
                            if (rows < 0)
                            {
                                LogFile.WriteLogAuto(sqlString + "|rows=" + rows + "更新数据集UpDate_DataSets错误", "ORACLEBASECLASS");
                                errText = sqlString + "|rows=" + rows + "更新数据集UpDate_DataSets错误";
                                return -2;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogFile.WriteLogAuto(ex.Message + "|更新数据集UpDate_DataSets异常", "ORACLEBASECLASS");
                    errText = ex.Message + "|更新数据集UpDate_DataSets异常";
                    return -1;
                    //Transaction.Rollback();
                    //throw new Exception("更新数据失败，SQL:" + ex.Message);
                }
            }
            DsCount = dsList.Count;
            foreach (Object obj in dsList)
            {
                DataSet ds = (DataSet)obj;
                if (drsChange != null && drsChange.Count > 0 && drsChange[0].Table.DataSet == ds)
                {
                    drsChange.ForEach(r => {
                        if (r.RowState == DataRowState.Added || r.RowState == DataRowState.Modified) r.AcceptChanges();
                    });
                }
                else
                {
                    ds.AcceptChanges();
                }
            }
            return DsCount;
        }
        /// <summary>
        /// 获取存储过程里的挂号收费用
        /// </summary>
        /// <param name="procedureName"></param>
        /// <param name="paramKey"></param>
        /// <param name="paramValue"></param>
        /// <param name="para1">resultcode</param>
        /// <param name="para2">errmsg</param>
        /// <param name="para3"></param>
        /// <returns></returns>
        /// 
        public bool GetPubicProcedureDs(string procedureName, List<string> paramKey, List<string> paramValue, ref string para1, ref String para2, ref DataTable para3)
        {
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    //定义OracleCommand对象,设置命令类型为存储过程   
                    commandClient = new System.Data.OracleClient.OracleCommand(procedureName, connectionClient);
                    commandClient.CommandType = CommandType.StoredProcedure;//设置执行类型为存储过程  
                    //根据存储过程的参数个数及类型生成参数对象  
                    for (int i = 0; i < paramKey.Count; i++)
                    {
                        System.Data.OracleClient.OracleParameter p1 = new System.Data.OracleClient.OracleParameter(paramKey[i], paramValue[i]);
                        //设置参数的输入输出类型,默认为输入  
                        p1.Direction = ParameterDirection.Input;
                        //按照存储过程参数顺序把参数依次加入到OracleCommand对象参数集合中  
                        commandClient.Parameters.Add(p1);
                    }
                    System.Data.OracleClient.OracleParameter p2 = new System.Data.OracleClient.OracleParameter();
                    p2.OracleType = System.Data.OracleClient.OracleType.VarChar;
                    p2.Size = 10;
                    p2.ParameterName = "resultcode";
                    p2.Direction = ParameterDirection.Output;
                    commandClient.Parameters.Add(p2);

                    p2 = new System.Data.OracleClient.OracleParameter();
                    p2.OracleType = System.Data.OracleClient.OracleType.VarChar;
                    p2.Size = 200;
                    p2.ParameterName = "errMsg";
                    p2.Direction = ParameterDirection.Output;
                    commandClient.Parameters.Add(p2);

                    p2 = new System.Data.OracleClient.OracleParameter();
                    p2.OracleType = System.Data.OracleClient.OracleType.Cursor;
                    p2.ParameterName = "curOut";
                    p2.Direction = ParameterDirection.Output;
                    commandClient.Parameters.Add(p2);

                    if (commandClient != null)
                    {
                        if (commandClient.Transaction == null)
                        {
                            commandClient.Transaction = TransactionClient;
                        }
                    }
                    System.Data.OracleClient.OracleDataAdapter dataAdapter = new System.Data.OracleClient.OracleDataAdapter(commandClient);
                    para3 = new DataTable();
                    int res = dataAdapter.Fill(para3);

                    //int res = commandClient.ExecuteNonQuery();

                    if (res < 0)
                    {
                        para1 = commandClient.Parameters["resultcode"].Value.ToString();
                        para2 = commandClient.Parameters["errMsg"].Value.ToString();

                        return false;
                    }
                    else
                    {
                        para1 = commandClient.Parameters["resultcode"].Value.ToString();
                        para2 = commandClient.Parameters["errMsg"].Value.ToString();
                        //para3 = commandClient.Parameters["curOut"].Value as DataTable;
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    return false;
                    //throw new Exception("获取数据集失败，存储过程:" + procedureName + ex.Message);
                }

            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    //定义OracleCommand对象,设置命令类型为存储过程   
                    commandDev = new Devart.Data.Oracle.OracleCommand(procedureName, connectionDev);
                    commandDev.CommandType = CommandType.StoredProcedure;//设置执行类型为存储过程  
                    //根据存储过程的参数个数及类型生成参数对象  
                    for (int i = 0; i < paramKey.Count; i++)
                    {
                        Devart.Data.Oracle.OracleParameter p1 = new Devart.Data.Oracle.OracleParameter(paramKey[i], paramValue[i]);
                        //设置参数的输入输出类型,默认为输入  
                        p1.Direction = ParameterDirection.Input;
                        //按照存储过程参数顺序把参数依次加入到OracleCommand对象参数集合中  
                        commandDev.Parameters.Add(p1);
                    }
                    Devart.Data.Oracle.OracleParameter p2 = new Devart.Data.Oracle.OracleParameter();
                    p2.OracleDbType = Devart.Data.Oracle.OracleDbType.VarChar;
                    p2.Size = 10;
                    p2.ParameterName = "resultcode";
                    p2.Direction = ParameterDirection.Output;
                    commandDev.Parameters.Add(p2);

                    p2 = new Devart.Data.Oracle.OracleParameter();
                    p2.OracleDbType = Devart.Data.Oracle.OracleDbType.VarChar;
                    p2.Size = 200;
                    p2.ParameterName = "errMsg";
                    p2.Direction = ParameterDirection.Output;
                    commandDev.Parameters.Add(p2);

                    p2 = new Devart.Data.Oracle.OracleParameter();
                    p2.OracleDbType = Devart.Data.Oracle.OracleDbType.Cursor;
                    p2.ParameterName = "curOut";
                    p2.Direction = ParameterDirection.Output;
                    commandDev.Parameters.Add(p2);

                    if (commandDev != null)
                    {
                        if (commandDev.Transaction == null)
                        {
                            commandDev.Transaction = TransactionDev;
                        }
                    }
                    Devart.Data.Oracle.OracleDataAdapter dataAdapter = new Devart.Data.Oracle.OracleDataAdapter(commandDev);
                    para3 = new DataTable();
                    int res = dataAdapter.Fill(para3);

                    //int res = commandClient.ExecuteNonQuery();

                    if (res < 0)
                    {
                        para1 = commandDev.Parameters["resultcode"].Value.ToString();
                        para2 = commandDev.Parameters["errMsg"].Value.ToString();

                        return false;
                    }
                    else
                    {
                        para1 = commandDev.Parameters["resultcode"].Value.ToString();
                        para2 = commandDev.Parameters["errMsg"].Value.ToString();
                        //para3 = commandClient.Parameters["curOut"].Value as DataTable;
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    return false;
                    //throw new Exception("获取数据集失败，存储过程:" + procedureName + ex.Message);
                }

            }
            else
            {
                try
                {
                    //定义OracleCommand对象,设置命令类型为存储过程   
                    command = new OracleCommand(procedureName, connection);
                    command.CommandType = CommandType.StoredProcedure;//设置执行类型为存储过程  

                    //根据存储过程的参数个数及类型生成参数对象  
                    for (int i = 0; i < paramKey.Count; i++)
                    {
                        OracleParameter p1 = new OracleParameter(paramKey[i], paramValue[i]);
                        //设置参数的输入输出类型,默认为输入  
                        p1.Direction = ParameterDirection.Input;
                        //按照存储过程参数顺序把参数依次加入到OracleCommand对象参数集合中  
                        command.Parameters.Add(p1);
                    }

                    if (command != null)
                    {
                        if (command.Transaction == null)
                        {
                            command.Transaction = Transaction;
                        }
                    }

                    OracleParameter p2 = new OracleParameter();
                    p2.OracleDbType = OracleDbType.Varchar2;
                    p2.Size = 10;
                    p2.ParameterName = "resultcode";
                    p2.Direction = ParameterDirection.Output;
                    command.Parameters.Add(p2);

                    OracleParameter p3 = new OracleParameter();
                    p3.OracleDbType = OracleDbType.Varchar2;
                    p3.Size = 200;
                    p3.ParameterName = "errMsg";
                    p3.Direction = ParameterDirection.Output;
                    command.Parameters.Add(p3);

                    OracleParameter p4 = new OracleParameter();
                    p4.OracleDbType = OracleDbType.RefCursor;
                    p4.ParameterName = "curOut";
                    p4.Direction = ParameterDirection.Output;
                    command.Parameters.Add(p4);


                    //执行,结果集填入datatable中  
                    OracleDataAdapter dataAdapter = new OracleDataAdapter(command);
                    para3 = new DataTable();
                    int res = dataAdapter.Fill(para3);
                    //int res = commandClient.ExecuteNonQuery();
                    if (res < 0)
                    {
                        para1 = command.Parameters["resultcode"].Value.ToString();
                        para2 = command.Parameters["errMsg"].Value.ToString();

                        return false;
                    }
                    else
                    {
                        para1 = command.Parameters["resultcode"].Value.ToString();
                        para2 = command.Parameters["errMsg"].Value.ToString();
                        //para3 = commandClient.Parameters["curOut"].Value as DataTable;
                        return true;
                    }

                }
                catch (Exception ex)
                {
                    return false;
                    //throw new Exception("获取数据集失败，存储过程:" + procedureName, ex);
                }
            }

        }
        public DataSet GetProcedureDS(string procedureName, List<string> paramKey, List<string> paramValue, string tableName, string sql)
        {
            DataSet ds = new DataSet();
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    //定义OracleCommand对象,设置命令类型为存储过程   
                    commandClient = new System.Data.OracleClient.OracleCommand(procedureName, connectionClient);
                    commandClient.CommandType = CommandType.StoredProcedure;//设置执行类型为存储过程  

                    //根据存储过程的参数个数及类型生成参数对象  
                    for (int i = 0; i < paramKey.Count; i++)
                    {
                        System.Data.OracleClient.OracleParameter p1 = new System.Data.OracleClient.OracleParameter(paramKey[i], paramValue[i]);
                        //设置参数的输入输出类型,默认为输入  
                        p1.Direction = ParameterDirection.Input;
                        //按照存储过程参数顺序把参数依次加入到OracleCommand对象参数集合中  
                        commandClient.Parameters.Add(p1);
                    }

                    //执行,结果集填入datatable中  
                    System.Data.OracleClient.OracleDataAdapter dataAdapter = new System.Data.OracleClient.OracleDataAdapter(commandClient);
                    dataAdapter.Fill(ds);

                }
                catch (Exception ex)
                {
                    Utility.LogFile.WriteLogAuto("获取数据集失败，存储过程:" + procedureName + ex.Message, "OralceBaseClass");
                    throw new Exception("获取数据集失败，存储过程:" + procedureName, ex);
                }
                return ds;
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    //定义OracleCommand对象,设置命令类型为存储过程   
                    commandDev = new Devart.Data.Oracle.OracleCommand(procedureName, connectionDev);
                    commandDev.CommandType = CommandType.StoredProcedure;//设置执行类型为存储过程  

                    //根据存储过程的参数个数及类型生成参数对象  
                    for (int i = 0; i < paramKey.Count; i++)
                    {
                        Devart.Data.Oracle.OracleParameter p1 = new Devart.Data.Oracle.OracleParameter(paramKey[i], paramValue[i]);
                        //设置参数的输入输出类型,默认为输入  
                        p1.Direction = ParameterDirection.Input;
                        //按照存储过程参数顺序把参数依次加入到OracleCommand对象参数集合中  
                        commandDev.Parameters.Add(p1);
                    }

                    //执行,结果集填入datatable中  
                    Devart.Data.Oracle.OracleDataAdapter dataAdapter = new Devart.Data.Oracle.OracleDataAdapter(commandDev);
                    dataAdapter.Fill(ds);

                }
                catch (Exception ex)
                {
                    Utility.LogFile.WriteLogAuto("获取数据集失败，存储过程:" + procedureName + ex.Message, "OralceBaseClass");
                    throw new Exception("获取数据集失败，存储过程:" + procedureName, ex);
                }
                return ds;
            }
            else
            {
                try
                {
                    //定义OracleCommand对象,设置命令类型为存储过程   
                    command = new OracleCommand(procedureName, connection);
                    command.CommandType = CommandType.StoredProcedure;//设置执行类型为存储过程  

                    //根据存储过程的参数个数及类型生成参数对象  
                    for (int i = 0; i < paramKey.Count; i++)
                    {
                        OracleParameter p1 = new OracleParameter(paramKey[i], paramValue[i]);
                        //设置参数的输入输出类型,默认为输入  
                        p1.Direction = ParameterDirection.Input;
                        //按照存储过程参数顺序把参数依次加入到OracleCommand对象参数集合中  
                        command.Parameters.Add(p1);
                    }

                    //执行,结果集填入datatable中  
                    OracleDataAdapter dataAdapter = new OracleDataAdapter(command);
                    dataAdapter.Fill(ds);

                }
                catch (Exception ex)
                {
                    Utility.LogFile.WriteLogAuto("获取数据集失败，存储过程:" + procedureName + ex.Message, "OralceBaseClass");
                    throw new Exception("获取数据集失败，存储过程:" + procedureName, ex);
                }
                return ds;
            }

        }
        /// <summary>
        /// 数据库类型转换 转换位客户端连接
        /// </summary>
        /// <param name="dbType">要转换的类型</param>
        /// <returns></returns>
        public System.Data.OracleClient.OracleType GetOracleType(OracleDbType dbType)
        {
            System.Data.OracleClient.OracleType result;
            switch (dbType)
            {
                case OracleDbType.Decimal:
                    result = System.Data.OracleClient.OracleType.Number;
                    break;
                case OracleDbType.Varchar2:
                    result = System.Data.OracleClient.OracleType.VarChar;
                    break;
                case OracleDbType.NVarchar2:
                    result = System.Data.OracleClient.OracleType.NVarChar;
                    break;
                case OracleDbType.Date:
                    result = System.Data.OracleClient.OracleType.DateTime;
                    break;
                case OracleDbType.Blob:
                    result = System.Data.OracleClient.OracleType.Blob;
                    break;
                case OracleDbType.Byte:
                    result = System.Data.OracleClient.OracleType.Byte;
                    break;
                case OracleDbType.Char:
                    result = System.Data.OracleClient.OracleType.Char;
                    break;
                case OracleDbType.Clob:
                    result = System.Data.OracleClient.OracleType.Clob;
                    break;
                case OracleDbType.Int32:
                    result = System.Data.OracleClient.OracleType.Int32;
                    break;
                case OracleDbType.Long:
                    result = System.Data.OracleClient.OracleType.Number;
                    break;
                case OracleDbType.TimeStamp:
                    result = System.Data.OracleClient.OracleType.Timestamp;
                    break;
                case OracleDbType.XmlType:
                    result = System.Data.OracleClient.OracleType.Clob;
                    break;
                case OracleDbType.LongRaw:
                    result = System.Data.OracleClient.OracleType.LongRaw;
                    break;
                case OracleDbType.Int16:
                    result = System.Data.OracleClient.OracleType.Int16;
                    break;
                case OracleDbType.Int64:
                    result = System.Data.OracleClient.OracleType.UInt32;
                    break;
                case OracleDbType.Single:
                    result = System.Data.OracleClient.OracleType.Float;
                    break;
                case OracleDbType.BFile:
                    result = System.Data.OracleClient.OracleType.BFile;
                    break;
                case OracleDbType.NChar:
                    result = System.Data.OracleClient.OracleType.NChar;
                    break;
                case OracleDbType.Raw:
                    result = System.Data.OracleClient.OracleType.Raw;
                    break;
                case OracleDbType.NClob:
                    result = System.Data.OracleClient.OracleType.NClob;
                    break;
                case OracleDbType.IntervalYM:
                    result = System.Data.OracleClient.OracleType.IntervalYearToMonth;
                    break;
                case OracleDbType.IntervalDS:
                    result = System.Data.OracleClient.OracleType.IntervalDayToSecond;
                    break;
                default://默认无用
                    result = System.Data.OracleClient.OracleType.VarChar;
                    break;
            }
            return result;
        }


        /// <summary>
        /// 数据库类型转换 转换为Devart连接
        /// </summary>
        /// <param name="dbType">要转换的类型</param>
        /// <returns></returns>
        public Devart.Data.Oracle.OracleDbType GetOracleDevType(OracleDbType dbType)
        {
            Devart.Data.Oracle.OracleDbType result;
            switch (dbType)
            {
                case OracleDbType.Decimal:
                    result = Devart.Data.Oracle.OracleDbType.Number;
                    break;
                case OracleDbType.Varchar2:
                    result = Devart.Data.Oracle.OracleDbType.VarChar;
                    break;
                case OracleDbType.NVarchar2:
                    result = Devart.Data.Oracle.OracleDbType.NVarChar;
                    break;
                case OracleDbType.Date:
                    result = Devart.Data.Oracle.OracleDbType.Date;
                    break;
                case OracleDbType.Blob:
                    result = Devart.Data.Oracle.OracleDbType.Blob;
                    break;
                case OracleDbType.Byte:
                    result = Devart.Data.Oracle.OracleDbType.Byte;
                    break;
                case OracleDbType.Char:
                    result = Devart.Data.Oracle.OracleDbType.Char;
                    break;
                case OracleDbType.Clob:
                    result = Devart.Data.Oracle.OracleDbType.Clob;
                    break;
                case OracleDbType.Int32:
                    result = Devart.Data.Oracle.OracleDbType.Integer;
                    break;
                case OracleDbType.Long:
                    result = Devart.Data.Oracle.OracleDbType.Long;
                    break;
                case OracleDbType.TimeStamp:
                    result = Devart.Data.Oracle.OracleDbType.TimeStamp;
                    break;
                case OracleDbType.XmlType:
                    result = Devart.Data.Oracle.OracleDbType.Xml;
                    break;
                case OracleDbType.LongRaw:
                    result = Devart.Data.Oracle.OracleDbType.LongRaw;
                    break;
                case OracleDbType.Int16:
                    result = Devart.Data.Oracle.OracleDbType.Int16;
                    break;
                case OracleDbType.Int64:
                    result = Devart.Data.Oracle.OracleDbType.Int64;
                    break;
                case OracleDbType.Single:
                    result = Devart.Data.Oracle.OracleDbType.Float;
                    break;
                case OracleDbType.BFile:
                    result = Devart.Data.Oracle.OracleDbType.BFile;
                    break;
                case OracleDbType.NChar:
                    result = Devart.Data.Oracle.OracleDbType.NChar;
                    break;
                case OracleDbType.Raw:
                    result = Devart.Data.Oracle.OracleDbType.Raw;
                    break;
                case OracleDbType.NClob:
                    result = Devart.Data.Oracle.OracleDbType.NClob;
                    break;
                case OracleDbType.IntervalYM:
                    result = Devart.Data.Oracle.OracleDbType.IntervalYM;
                    break;
                case OracleDbType.IntervalDS:
                    result = Devart.Data.Oracle.OracleDbType.IntervalDS;
                    break;
                default://默认无用
                    result = Devart.Data.Oracle.OracleDbType.VarChar;
                    break;
            }
            return result;
        }



        /// <summary>
        ///护理专用
        /// </summary>
        /// <param name="ds"></param>
        /// <returns></returns>
        public int UpDate_Data_Nur(DataSet ds,ref string errText)
        {
            String sqlString = "";
            //增加非直连数据库模式，使用客户端 梁吉 2016-06-08
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    int DBRows = 0;
                    foreach (DataTable table in ds.Tables)
                    {
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (table.ExtendedProperties.Count > 0)
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }
                            else
                            {
                                sqlString = "select * from " + table.TableName;
                            }
                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                            sqlString = "select * from " + table.TableName;
                            //throw new Exception("入参DataTable，不符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在");

                        }
                        //System.Data.OracleClient.OracleCommand cmd = connectionClient.CreateCommand(); //new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                        //cmd.CommandText = sqlString;
                        if (commandClient != null)
                        {
                            if (commandClient.Transaction == null)
                            {
                                commandClient.Transaction = TransactionClient;
                            }
                            commandClient.CommandText = sqlString;
                            commandClient.Parameters.Clear();
                        }
                        else
                        {
                            commandClient = new System.Data.OracleClient.OracleCommand(sqlString, connectionClient);
                        }
                        if (Parameters != null)
                        {
                            foreach (OracleParameter var in Parameters)
                            {
                                System.Data.OracleClient.OracleParameter var1 = new System.Data.OracleClient.OracleParameter();
                                //转换成非直连参数 梁吉 2016-06-08
                                var1.ParameterName = var.ParameterName;
                                var1.OracleType = GetOracleType(var.OracleDbType);
                                var1.Value = var.Value;
                                commandClient.Parameters.Add(var1);
                            }
                        }
                        System.Data.OracleClient.OracleDataAdapter rar = new System.Data.OracleClient.OracleDataAdapter(commandClient);
                        System.Data.OracleClient.OracleCommandBuilder cmdBuiler = new System.Data.OracleClient.OracleCommandBuilder(rar);
                        DataTable temp = table.GetChanges();
                        if (temp != null)
                        {
                            int rows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            //table.AcceptChanges();
                            if (rows <= 0)
                            {
                                LogFile.WriteLogAuto(sqlString + "|更新数据集错误01", "ORACLEBASECLASS");
                                errText = sqlString + "|更新数据集错误01" ;
                                return -2;
                            }
                            DBRows += rows;
                        }
                        //if (ds.HasChanges())
                        //{
                        //    DataTable temp = table;
                        //    DBRows = rar.Update(temp);
                        //    //2016 梁吉 提交上次表修改
                        //    temp.AcceptChanges();

                        //}

                    }
                    ds.AcceptChanges();
                    return DBRows;

                }
                catch (Exception ex)
                {
                    LogFile.WriteLogAuto(sqlString + "|更新数据集异常" + ex.Message, "ORACLEBASECLASS");
                    return -1;
                    //TransactionClient.Rollback();
                    //throw new Exception("更新数据失败，SQL:" + ex.Message);
                }
            }
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    int DBRows = 0;
                    foreach (DataTable table in ds.Tables)
                    {
                        List<OracleParameter> Parameters = null;
                        //首先从自定义信息中获取查询语句
                        if (table.ExtendedProperties.Count > 0)
                        {
                            //判断是否包含SQL
                            if (table.ExtendedProperties.ContainsKey("SQL"))
                            {
                                sqlString = table.ExtendedProperties["SQL"].ToString();
                            }
                            else
                            {
                                sqlString = "select * from " + table.TableName;
                            }
                            //判断是否包含参数
                            if (table.ExtendedProperties.ContainsKey("Parameters"))
                            {
                                Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                            }
                        }
                        else
                        {
                            sqlString = "select * from " + table.TableName;
                        }
                        if (commandDev != null)
                        {
                            if (commandDev.Transaction == null)
                            {
                                commandDev.Transaction = TransactionDev;
                            }
                            commandDev.CommandText = sqlString;
                            commandDev.Parameters.Clear();
                        }
                        else
                        {
                            commandDev = new Devart.Data.Oracle.OracleCommand(sqlString, connectionDev);
                        }
                        if (Parameters != null)
                        {
                            foreach (OracleParameter var in Parameters)
                            {
                                Devart.Data.Oracle.OracleParameter var1 = new Devart.Data.Oracle.OracleParameter();
                                //转换成非直连参数 梁吉 2016-06-08
                                var1.ParameterName = var.ParameterName;
                                var1.OracleDbType = GetOracleDevType(var.OracleDbType);
                                var1.Value = var.Value;
                                commandDev.Parameters.Add(var1);
                            }
                        }
                        Devart.Data.Oracle.OracleDataAdapter rar = new Devart.Data.Oracle.OracleDataAdapter(commandDev);
                        Devart.Data.Oracle.OracleCommandBuilder cmdBuiler = new Devart.Data.Oracle.OracleCommandBuilder(rar);
                        DataTable temp = table.GetChanges();
                        if (temp != null)
                        {
                            int rows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            //table.AcceptChanges();
                            if (rows <= 0)
                            {
                                LogFile.WriteLogAuto(sqlString + "|更新数据集错误02", "ORACLEBASECLASS");
                                errText = sqlString + "|更新数据集错误02";
                                return -2;
                            }
                            DBRows += rows;
                        }
                        //if (ds.HasChanges())
                        //{
                        //    DataTable temp = table;
                        //    DBRows = rar.Update(temp);
                        //    //2016 梁吉 提交上次表修改
                        //    temp.AcceptChanges();

                        //}

                    }
                    ds.AcceptChanges();
                    return DBRows;

                }
                catch (Exception ex)
                {
                    LogFile.WriteLogAuto(ex.Message + "|更新数据集异常", "ORACLEBASECLASS");
                    return -1;
                    //TransactionClient.Rollback();
                    //throw new Exception("更新数据失败，SQL:" + ex.Message);
                }
            }
            else
            {
                try
                {
                    int DBRows = 0;
                    foreach (DataTable table in ds.Tables)
                    {
                        List<OracleParameter> Parameters = null;
                        ////首先从自定义信息中获取查询语句
                        //if (table.ExtendedProperties.Count > 0)
                        //{
                        //    //判断是否包含SQL
                        //    if (table.ExtendedProperties.ContainsKey("SQL"))
                        //    {
                        //        sqlString = table.ExtendedProperties["SQL"].ToString();
                        //    }
                        //    else
                        //    {
                        //        sqlString = "select * from " + table.TableName;
                        //    }
                        //    //判断是否包含参数
                        //    if (table.ExtendedProperties.ContainsKey("Parameters"))
                        //    {
                        //        Parameters = table.ExtendedProperties["Parameters"] as List<OracleParameter>;
                        //    }
                        //}
                        //else
                        //{
                        //    //从datatable的DisplayExpression属性 提取SQL 此时忽略是否存在参数
                        //    sqlString = "select * from " + table.TableName;
                        //    //throw new Exception("入参DataTable，不符合自动更新规则，请查看DataTable的DisplayExpression属性是否已赋值或ExtendedProperties下SQL，Parameters关键字是否存在");

                        //}
                        sqlString = "SELECT * FROM " + table.TableName + " where 1=2";
                        if (command != null)
                        {
                            if (command.Transaction == null)
                            {
                                command.Transaction = Transaction;
                            }
                            command.CommandText = sqlString;

                        }
                        else
                        {
                            command = new OracleCommand(sqlString, connection);
                        }
                        if (Parameters != null && Parameters.Count > 0)
                        {
                            command.Parameters.Clear();
                            command.Parameters.AddRange(Parameters.ToArray());
                        }
                        //OracleCommand cmd = new OracleCommand(sqlString, connection);
                        //OracleDataAdapter rar = new OracleDataAdapter(cmd);
                        OracleDataAdapter rar = new OracleDataAdapter(command);
                        OracleCommandBuilder cmdBuiler = new OracleCommandBuilder(rar);
                        DataTable temp = table.GetChanges();
                        if (temp != null)
                        {
                            int rows = rar.Update(temp);
                            //2016 梁吉 提交上次表修改
                            //table.AcceptChanges();
                            if (rows <= 0)
                            {
                                LogFile.WriteLogAuto(sqlString + "|更新数据集错误03", "ORACLEBASECLASS");
                                errText = sqlString + "|更新数据集错误03";
                                return -2;
                            }
                            DBRows += rows;
                        }
                        //if (ds.HasChanges())
                        //{
                        //    DataTable temp = table;
                        //    DBRows += rar.Update(temp);
                        //    //2016 梁吉 提交上次表修改
                        //    temp.AcceptChanges();
                        //}
                    }
                    ds.AcceptChanges();
                    return DBRows;
                }
                catch (Exception ex)
                {
                    //Transaction.Rollback();
                    LogFile.WriteLogAuto(ex.Message + "|更新数据集异常", "ORACLEBASECLASS");
                    errText = ex.Message + "|更新数据集异常";
                    return -1;
                    //throw new Exception("更新数据失败，SQL:" + ex.Message);
                }
            }

        }

        #region 存储过程

        /// <summary>
        /// 执行存储过程返回int
        /// </summary>
        /// <param name="procedureName">存储过程名</param>
        /// <param name="dicParam">参数字典</param>
        /// <returns>返回int</returns>
        public int ExecuteSP(string procedureName, Dictionary<string, object> dicParam)
        {
            if (string.IsNullOrEmpty(procedureName)) return 0;            
            //1、模式一：OracleClient 连接方式
            if (UntilityConstant.DBConnectionMode == 0)
            {
                try
                {
                    //定义OracleCommand对象,设置命令类型为存储过程
                    if (connectionClient == null)
                    {
                        connectionClient = new System.Data.OracleClient.OracleConnection(connectionString);
                    }
                    if (connectionClient == null)
                    {
                        LogFile.WriteLogAuto("ExecuteSP connectionClient 为空", "ORACLEBASECLASS");
                        return 0;
                    }
                    commandClient = new System.Data.OracleClient.OracleCommand(procedureName, connectionClient);
                    commandClient.CommandType = CommandType.StoredProcedure;//设置执行类型为存储过程  
                    //根据存储过程的参数个数及类型生成参数对象  
                    foreach (KeyValuePair<string, object> kv in dicParam)
                    {
                        System.Data.OracleClient.OracleParameter p1 = new System.Data.OracleClient.OracleParameter(kv.Key, kv.Value);
                        //设置参数的输入输出类型,默认为输入  
                        p1.Direction = ParameterDirection.Input;
                        //按照存储过程参数顺序把参数依次加入到OracleCommand对象参数集合中  
                        commandClient.Parameters.Add(p1);
                    }
                    
                    connectionClient.Open();
                    int num = commandClient.ExecuteNonQuery();
                    return num;
                }
                catch (Exception ex)
                {
                    LogFile.WriteLogAuto("OracleBaseClass.ExecuteSP 执行存储过程异常:" + ex.Message, "ORACLEBASECLASS");
                    return 0;
                }
                finally
                {
                    if (connectionClient.State == ConnectionState.Open)
                    {
                        connectionClient.Close();
                    }
                }
            }
            //2、模式二：Devart 连接方式
            else if (UntilityConstant.DBConnectionMode == 2)
            {
                try
                {
                    //定义OracleCommand对象,设置命令类型为存储过程
                    if (connectionDev == null)
                    {
                        connectionDev = new Devart.Data.Oracle.OracleConnection(connectionString);
                    }
                    if (connectionDev == null)
                    {
                        LogFile.WriteLogAuto("ExecuteSP connectionDev 为空", "ORACLEBASECLASS");
                        return 0;
                    }
                    commandDev = new Devart.Data.Oracle.OracleCommand(procedureName, connectionDev);
                    commandDev.CommandType = CommandType.StoredProcedure;//设置执行类型为存储过程  
                    //根据存储过程的参数个数及类型生成参数对象  
                    foreach (KeyValuePair<string, object> kv in dicParam)
                    {
                        Devart.Data.Oracle.OracleParameter p1 = new Devart.Data.Oracle.OracleParameter(kv.Key, kv.Value);
                        //设置参数的输入输出类型,默认为输入  
                        p1.Direction = ParameterDirection.Input;
                        //按照存储过程参数顺序把参数依次加入到OracleCommand对象参数集合中  
                        commandDev.Parameters.Add(p1);
                    }                    
                    connectionDev.Open();
                    int num = commandDev.ExecuteNonQuery();
                    return num;
                    
                }
                catch (Exception ex)
                {
                    LogFile.WriteLogAuto("OracleBaseClass.ExecuteSP 执行存储过程异常:" + ex.Message, "ORACLEBASECLASS");
                    return 0;
                }
                finally
                {
                    if (connectionDev.State == ConnectionState.Open)
                    {
                        connectionDev.Close();
                    }
                }

            }
            //3、模式三（默认）：DBConnectionMode = 1，ManagedDataAccess 连接方式
            else
            {
                try
                {
                    //定义OracleCommand对象,设置命令类型为存储过程
                    if (connection == null)
                    {
                        connection = new OracleConnection(connectionString);
                    }
                    if (connection == null)
                    {
                        LogFile.WriteLogAuto("ExecuteSP connection 为空", "ORACLEBASECLASS");
                        return 0;
                    }
                    command = new OracleCommand(procedureName, connection);
                    command.CommandType = CommandType.StoredProcedure;//设置执行类型为存储过程  

                    if (dicParam == null)
                    {
                        throw new Exception("存储过程参数列表为空");
                    }
                    //根据存储过程的参数个数及类型生成参数对象  
                    foreach (KeyValuePair<string, object> kv in dicParam)
                    {
                        OracleParameter p1 = new OracleParameter(kv.Key, kv.Value);
                        //设置参数的输入输出类型,默认为输入  
                        p1.Direction = ParameterDirection.Input;
                        //按照存储过程参数顺序把参数依次加入到OracleCommand对象参数集合中  
                        command.Parameters.Add(p1);
                    }                    
                    connection.Open();
                    int num = command.ExecuteNonQuery();
                    
                    return num;

                }
                catch (Exception ex)
                {
                    LogFile.WriteLogAuto("OracleBaseClass.ExecuteSP 执行存储过程异常:" + ex.Message, "ORACLEBASECLASS");
                    return 0;
                }
                finally
                {
                    if(connection.State == ConnectionState.Open)
                    {
                        connection.Close();
                    }
                }
            }
        }

        #endregion 存储过程
    }
}
