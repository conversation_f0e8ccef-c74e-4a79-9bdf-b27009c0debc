﻿using System;
using System.Data;
using System.ServiceModel;
namespace INMService
{
    /// <summary>
    /// 接口层BED_REC
    /// </summary>
    [ServiceContract]
    public interface IBED_REC
    {
        #region  成员方法
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        [OperationContract]
        bool Exists_BED_REC(string WARD_CODE, decimal BED_NO);
        /// <summary>
        /// 增加一条数据
        /// </summary>
        [OperationContract]
        bool Add_BED_REC(Model.BED_REC model);
        /// <summary>
        /// 更新一条数据
        /// </summary>
        [OperationContract]
        bool Update_BED_REC(Model.BED_REC model);
        /// <summary>
        /// 删除数据
        /// </summary>
        [OperationContract]
        bool Delete_BED_REC(string WARD_CODE, decimal BED_NO);
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        [OperationContract]
        Model.BED_REC GetModel_BED_REC(string WARD_CODE, decimal BED_NO);
        /// <summary>
        /// 获得数据列表
        /// </summary>
        [OperationContract]
        DataSet GetList_All_BED_REC(string strWhere);
        /// <summary>
        /// 获得前几行数据
        /// </summary>
        [OperationContract]
        DataSet GetList_BED_REC(int startIndex, int endIndex, string strWhere, string filedOrder);
        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.BED_REC> GetObservableCollection_All_BED_REC(string strWhere);
        /// <summary>
        /// 获得ObservableCollection根据分页获得数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.BED_REC> GetObservableCollection_BED_REC(int startIndex, int endIndex, string strWhere, string filedOrder);
        #endregion  成员方法
    }
}