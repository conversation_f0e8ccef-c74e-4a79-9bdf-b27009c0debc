﻿using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Utility;

namespace Tj_Update
{
    public partial class FrmLoginNew : Form
    {
       
        String AppName = "";
        public String UserName = "";
        public String PassWord = "";
        int ii_err_cs = 0, ii_pw_err;//密码错误次数判断 设置后 错误多次后自动关闭
        string ls_init_pw;// 是否判断初始值密码 给予提示 修改密码
        int iLockTime = 0;// 锁定分钟数

        public FrmLoginNew()
        {
            InitializeComponent();
        }

        private void Frm_LoginNew_Load(object sender, EventArgs e)
        {

            try
            {
                //Thread t1;
                //t1 = new Thread(LoadData);
                //t1.Start();
                LoadData();
                if (!string.IsNullOrEmpty(UserName))
                {
                    txtInputNo.Text = UserName;
                    txtPwd.Text = PassWord;
                    this.lblLogin_Click(lblLogin, null);
                }
                else
                {
                    txtInputNo.Text = Utility.ConfigHelper.GetConfiguration("LastLoginUser");
                    if (!string.IsNullOrEmpty(txtInputNo.Text))
                    {
                        txtPwd.Focus();
                        GetLoginData(txtInputNo.Text);
                    }
                }
            }
            catch (Exception ex)
            {
            }

        }
        /// <summary>
        /// 控制窗体控件加载刷新闪烁问题 
        /// </summary>
        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ExStyle |= 0x02000000;
                return cp;
            }
        }
        private void LoadData()
        {
            try
            {
                string path = System.IO.Directory.GetCurrentDirectory() + "\\Image\\" + "LoginBackgroundImage.jpg";
                this.BackgroundImage = Image.FromFile(path);
                path = System.IO.Directory.GetCurrentDirectory() + "\\Image\\" + "LoginImage.png";
                panel1.BackgroundImage = Image.FromFile(path);
            }
            catch
            {
            }
            //加载版本号
            string his_unit_code= Utility.ConfigHelper.GetConfiguration("UNITCODE");
            string strsql = " select * from  hospital_config where rownum=1 and his_unit_code='"+ his_unit_code + "' "; 
            DataSet ds = new OracleDAL.ServerPublic_Dao().GetDataBySql(strsql); 
            if (ds != null && ds.Tables[0].Rows.Count > 0)
            {
                this.lblVersion.Text = "版本号:" + ds.Tables[0].Rows[0]["AUTHORIZED_KEY"].ToString();
                SystemParm.version = ds.Tables[0].Rows[0]["AUTHORIZED_KEY"].ToString();
                lbhospital.Text= ds.Tables[0].Rows[0]["hospital"].ToString()+" | "+"国家医保编码"+ ds.Tables[0].Rows[0]["yb_hospitalcode"].ToString();
                DateTime dtdate = new OracleDAL.ServerPublic_Dao().GetSysDate() ;
                Copyright.Text = "Copyright © 1995 - "+ dtdate.ToString("yyyy") + " 北京天健源达科技股份有限公司";
            }

            ii_pw_err = Int32.Parse(PlatCommon.SysBase.SystemParm.GetParaValue("PW_ERR", "*", "*", "*", "0"));
            ls_init_pw = (PlatCommon.SysBase.SystemParm.GetParaValue("INIT_PW", "*", "*", "*", "0"));// 是否判断初始值密码 给予提示 修改密码
            iLockTime = Int32.Parse(PlatCommon.SysBase.SystemParm.GetParaValue("LockTime", "*", "*", "*", "0"));// 锁定分钟数
        }
        public void LoginCommandExecute()
        { 
         
            if (String.IsNullOrEmpty(txtInputNo.Text))
            {
                XtraMessageBox.Show("请输入用户！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtInputNo.Focus();             
                return;
            }
            if (String.IsNullOrEmpty(txtPwd.Text))
            {
                XtraMessageBox.Show("请输密码！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtPwd.Focus();
                return;
            }
            //查询账户是否被锁定
            //如果存在锁定时间 并且当前时间与锁定时间在约定时间内 给与提示不让登录
            string sqll = "select  ceil((sysdate- lock_time)* 24 * 60) from staff_dict where upper(user_name)='" + txtInputNo.Text.ToUpper() + "'    ";
            DataTable dt_lock = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sqll).Tables[0];
            if (dt_lock.Rows.Count > 0)
            {
                string ls_lock = dt_lock.Rows[0][0].ToString();
                if (!string.IsNullOrEmpty(ls_lock)) // 
                {
                    int ics = iLockTime - int.Parse(ls_lock);
                    if (ics > 0)
                    {
                        XtraMessageBox.Show("账户已被锁定" + "请{ " + ics.ToString() + " }分钟后再试！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        this.Close();
                        return;
                    }
                }
            }

            NM_Service.NMService.STAFF_DICTClient client = new NM_Service.NMService.STAFF_DICTClient();
            int result = client.LoginValidate_STAFF_DICT(txtInputNo.Text.ToUpper(), txtPwd.Text.ToUpper());
            switch (result)
            {
                case -1:
                    XtraMessageBox.Show("用户不存在！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break;
                case 0: 
                    ii_err_cs++;
                    XtraMessageBox.Show("密码错误！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    if (ii_err_cs >= ii_pw_err && ii_pw_err > 0)
                    {
                        //超过错误次数 用户锁定
               
                        string  lsdate = new NM_Service.NMService.ServerPublicClient().GetSysDate().ToString("yyyy-MM-dd HH:mm:ss");

                        string sqlu = " update staff_dict set  lock_time =to_date('"+ lsdate + "','yyyy-mm-dd hh24:mi:ss') where upper(user_name)='"+ txtInputNo.Text.ToUpper() + "'   ";
                        Dictionary<string, string> idc = new Dictionary<string, string>();
                        idc.Add(sqlu, "更新锁定时间失败");
                        string ls_ret = new NM_Service.NMService.ServerPublicClient().SaveTable(idc);
 
 
                        XtraMessageBox.Show("密码错误！超过" + ii_err_cs.ToString() + "次,已锁定,请{"+iLockTime.ToString()+"}分钟后再试", "错误信息" + ls_ret, MessageBoxButtons.OK, MessageBoxIcon.Error);
                        this.Close();
                    }
                    break;
                     
                case 1:


                    if (txtInputNo.Text.ToUpper().Equals(txtPwd.Text.ToUpper()) && ls_init_pw.Equals("1"))
                    {
                        XtraMessageBox.Show("你的密码为初始密码，请登陆后修改密码！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }

                    SystemParm.LoginUser = client.GetModelByUserName_STAFF_DICT(txtInputNo.Text.ToUpper());
                    PlatCommon.SysBase.SystemParm.HisUnitCode = Utility.ConfigHelper.GetConfiguration("UNITCODE");
                    this.UserName = txtInputNo.Text.Trim();
                    this.PassWord = txtPwd.Text.Trim();
                    //特殊用户直接进入主界面 梁吉 2016-08-25
                    if ("TJSYSTEM".Equals(PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME.ToUpper()))
                    {
                        this.DialogResult = System.Windows.Forms.DialogResult.OK;
                        this.Close();
                        return;
                    }
                    #region
                    //判断角色  梁吉融合电子病历菜单 2016-07-26
                    //string strsql = "SELECT T1.RIGHT_TYPE_CODE  FROM MR_RIGHT_TYPE_DICT  T1 "
                    //            + "LEFT JOIN  MR_RIGHT_DICT T2 "
                    //            + "ON T1.RIGHT_TYPE_ID= T2.RIGHT_TYPE_ID "
                    //            + "LEFT JOIN MR_ROLE_RIGHT T3 "
                    //            + "ON T2.RIGHT_ID=T3.RIGHT_ID "
                    //            + "LEFT JOIN MR_USER_ROLE T4 "
                    //            + "ON T4.ROLE_CODE = T3.ROLE_CODE "
                    //            + "WHERE T4.DB_USER='" + txtInputNo.Text.ToUpper() + "' "
                    //            + "AND UPPER(T1.RIGHT_TYPE_CODE)<>'EMR' "
                    //            + "GROUP BY T1.RIGHT_TYPE_CODE";

                    ////DataTable dt = new NM_Service.NMService.ServerPublicClient().GetList("select application_code from sec_right_group_vs_users where user_code = '" + txtInputNo.Text.ToUpper() + "' group by application_code").Tables[0];
                    //DataTable dt = new NM_Service.NMService.ServerPublicClient().GetList(strsql).Tables[0];

                    //if (dt.Rows.Count > 1)
                    //{
                    //    //过滤登录appname，有参数传，判断对应权限是否存在参数app
                    //    int exitApp= 0;
                    //    if (!string.IsNullOrEmpty(SystemParm.AppName))
                    //    {
                    //        DataRow[] rows = dt.Select("RIGHT_TYPE_CODE='" + SystemParm.AppName + "'");
                    //        exitApp = rows.Length;
                    //    }


                    //    if (exitApp <= 0)
                    //    {
                    //        //打开选择窗口
                    //        FrmSelectModel fsm = new FrmSelectModel();
                    //        fsm.ShowDialog();
                    //        SystemParm.AppName = fsm.ModelName;
                    //        if (string.IsNullOrEmpty(SystemParm.AppName))
                    //        {
                    //            this.DialogResult = System.Windows.Forms.DialogResult.No;
                    //            this.Close();
                    //            return;
                    //        }
                    //    }

                    //}
                    //else
                    //{
                    //    if (dt.Rows.Count == 1)
                    //        SystemParm.AppName = dt.Rows[0][0].ToString();
                    //    else
                    //    {
                    //        XtraMessageBox.Show("您无权使用本系统！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    //        this.DialogResult = System.Windows.Forms.DialogResult.No;
                    //        this.Close();
                    //        return;
                    //    }
                    //}
                    #endregion
                    //医生站和护士站都用到

                    //PlatCommon.Common.PublicFunction.InitSystemPara();



                    #region
                    //if (SystemParm.AppName.Equals("NURSWS"))
                    //{ 
                    //    if (!PlatCommon.Common.PublicFunction.GetWarcodelist(SystemParm.LoginUser.EMP_NO))
                    //    {
                    //        this.DialogResult = System.Windows.Forms.DialogResult.No;
                    //        this.Close();
                    //        return;
                    //    }
                    //    else
                    //    {
                    //        Comm.Context.Current.User.WARD_ID = PlatCommon.SysBase.SystemParm.Ward_ID;
                    //        //梁吉 2016-07-12 修改参数初始化
                    //        PlatCommon.SysBase.SystemParm.BedWithDept = PlatCommon.SysBase.SystemParm.GetParaValue("BEDWITHDEPT", SystemParm.AppName, "*", "*", "0");
                    //        PlatCommon.SysBase.SystemParm.ClinicClass = PlatCommon.SysBase.SystemParm.GetParaValue("CLINIC_CLASS", SystemParm.AppName, "*", "*", "0");
                    //        if (!"1".Equals(SystemParm.ClinicClass))
                    //        {
                    //            PlatCommon.SysBase.SystemParm.ClinicClass = PlatCommon.SysBase.SystemParm.GetParaValue("CLINIC_CLASS", "*", "*", "*", "0");
                    //        }
                    //        PlatCommon.SysBase.SystemParm.NewBornOut = PlatCommon.SysBase.SystemParm.GetParaValue("NEW_BORN_OUT", SystemParm.AppName, "*", "*", "0");
                    //        //InitSystemPara();

                    //        Utility.ConfigHelper.SetConfiguration("LastLoginUser", txtInputNo.Text.Trim());
                    //        this.DialogResult = System.Windows.Forms.DialogResult.OK;
                    //        this.Close();
                    //        return;
                    //    }
                    //}
                    //else
                    //{
                    //NM_Service.NMService.ServerPublicClient sc = new NM_Service.NMService.ServerPublicClient();

                    //string sqlstr = "select a.group_code,b.dept_name from STAFF_VS_GROUP a,dept_dict b where a.group_code = b.dept_code and a.group_class = '病区医生' and emp_no = '" + SystemParm.LoginUser.EMP_NO + "'";
                    //System.Data.DataSet ds = sc.GetDataBySql(sqlstr);
                    //if (ds == null)
                    //{
                    //    XtraMessageBox.Show("您无权使用本系统！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    //    this.DialogResult = System.Windows.Forms.DialogResult.No;
                    //    this.Close();
                    //    return ;
                    //}
                    //int count = ds.Tables[0].Rows.Count;
                    //if (count < 1)
                    //{
                    //    XtraMessageBox.Show("您无权使用本系统！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    //    this.DialogResult = System.Windows.Forms.DialogResult.No;
                    //    this.Close();
                    //    return ;
                    //}
                    //if (count == 1)
                    //{
                    //    PlatCommon.SysBase.SystemParm.Deptcode = ds.Tables[0].Rows[0][0].ToString();
                    //    PlatCommon.SysBase.SystemParm.Deptname = ds.Tables[0].Rows[0][1].ToString();
                    //    PlatCommon.SysBase.SystemParm.Dept_list = "'" + PlatCommon.SysBase.SystemParm.Deptcode + "'";
                    //    PlatCommon.SysBase.SystemParm.Warcode_list = "'" + PlatCommon.Common.PublicFunction.GetWarcodeVsDept(PlatCommon.SysBase.SystemParm.Deptcode) + "'";
                    //    PlatCommon.SysBase.SystemParm.Ward_ID = PlatCommon.Common.PublicFunction.GetWarcodeVsDept(PlatCommon.SysBase.SystemParm.Deptcode);
                    //}
                    //else
                    //{
                    //    String ret = "",ret1="";
                    //    if (ds != null && ds.Tables[0].Rows.Count > 0)
                    //    {
                    //        SystemParm.Ward_ID = ds.Tables[0].Rows[0][0].ToString();
                    //        foreach (System.Data.DataRow dr in ds.Tables[0].Rows)
                    //        {
                    //            ret += "'" + dr[0] + "',";
                    //            if (!string.IsNullOrEmpty(PlatCommon.Common.PublicFunction.GetWarcodeVsDept(dr[0].ToString())))
                    //            {
                    //                ret1 += "'" + PlatCommon.Common.PublicFunction.GetWarcodeVsDept(dr[0].ToString()) + "',";
                    //            }
                    //        }
                    //        if (!"".Equals(ret))
                    //            ret = ret.Remove(ret.Length - 1);
                    //        if (!"".Equals(ret1))
                    //            ret1 = ret1.Remove(ret1.Length - 1);
                    //    }
                    //    PlatCommon.SysBase.SystemParm.Dept_list = ret;
                    //    PlatCommon.SysBase.SystemParm.Warcode_list = ret1;
                    //}
                    #endregion
                    Utility.ConfigHelper.SetConfiguration("LastLoginUser", txtInputNo.Text.Trim());

                    #region 根据参数初始化CA，以免用户使用用户名密码登录后，后续使用ca签名时出错。
                    bool caEnabled = Tjhis.Interface.CA.DbExt.getCaEnabled(SystemParm.HisUnitCode, "", "", "");
                    string CA_USER_ENABLED = Tjhis.Interface.CA.DbExt.getCaEnabledByUserName(txtInputNo.Text.ToUpper()) ? "1" : "0";
                    SystemParm.LoginUser.CA_ENABLED = CA_USER_ENABLED;
                    if (caEnabled && CA_USER_ENABLED.Equals("1"))
                    {
                        if (SystemParm.CaBusiness == null)
                        {
                            PlatCommon.SysBase.SystemParm.CaBusiness = new Tjhis.Interface.CA.CaBusiness();
                            PlatCommon.SysBase.SystemParm.CaBusiness.HisUnitCode = PlatCommon.SysBase.SystemParm.HisUnitCode;
                            PlatCommon.SysBase.SystemParm.CaBusiness.Init();
                        }
                    }
                    #endregion

                    this.DialogResult = System.Windows.Forms.DialogResult.OK;
                    this.Close();
                    //}
                    break;
                case 2:
                    XtraMessageBox.Show("用户已锁定，请联系管理员！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break;
                default:
                    XtraMessageBox.Show("未知错误！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break;
            }
        }

        public void LoginCa()
        {
            if (String.IsNullOrEmpty(txtInputNo.Text))
            {
                XtraMessageBox.Show("请输入用户！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtInputNo.Focus();
                return;
            }
            //if (String.IsNullOrEmpty(txtPwd.Text))
            //{
            //    XtraMessageBox.Show("请输密码！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //    txtPwd.Focus();
            //    return;
            //}
            #region
            //暂时没判断科室，后期看需求调整
            PlatCommon.SysBase.SystemParm.HisUnitCode = Utility.ConfigHelper.GetConfiguration("UNITCODE");
            string user = txtInputNo.Text.ToUpper();

            bool caEnabled = Tjhis.Interface.CA.DbExt.getCaEnabled(SystemParm.HisUnitCode, "", "", "");

            //SystemParm.LoginUser.CA_ENABLED = Tjhis.Interface.CA.DbExt.getCaEnabledByUserName(user) ?"1":"0";
            string CA_USER_ENABLED = Tjhis.Interface.CA.DbExt.getCaEnabledByUserName(user) ? "1" : "0";

            if (caEnabled && CA_USER_ENABLED.Equals("1"))
            {
                if (SystemParm.CaBusiness == null)
                {
                    PlatCommon.SysBase.SystemParm.CaBusiness = new Tjhis.Interface.CA.CaBusiness();
                    PlatCommon.SysBase.SystemParm.CaBusiness.HisUnitCode = PlatCommon.SysBase.SystemParm.HisUnitCode;
                    PlatCommon.SysBase.SystemParm.CaBusiness.Init();
                }
            }
            else
            {
                XtraMessageBox.Show("该用户没有设置CA登录，请联系管理员设置，或使用账号密码登录！", "异常信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            string psw = "";

            NM_Service.NMService.STAFF_DICTClient client = new NM_Service.NMService.STAFF_DICTClient();
            Model.STAFF_DICT staffDict = client.GetModelByUserName_STAFF_DICT(txtInputNo.Text.ToUpper());
            string userId = staffDict.ID;
            bool res = PlatCommon.SysBase.SystemParm.CaBusiness.CALogin(ref userId, ref psw);

            //if (res == false && PlatCommon.SysBase.SystemParm.CaBusiness.CaMandatorySign)
            //{
            //    XtraMessageBox.Show("请输入正确的登录名，并使用CA完成登录！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //    txtPwd.Focus();
            //    return;
            //}

            if (res == false)
            {
                XtraMessageBox.Show("CA登录失败，请输入正确的登录名，并使用CA完成登录！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtPwd.Focus();
                return;
            }
            #endregion



            //SystemParm.LoginUser = client.GetModelByUserName_STAFF_DICT(txtInputNo.Text.ToUpper());
            SystemParm.LoginUser = staffDict;
            SystemParm.LoginUser.CA_ENABLED = CA_USER_ENABLED;
            this.UserName = txtInputNo.Text.Trim();
            this.PassWord = this.PassWord = Gloobal.DecryptHIS(staffDict.PASSWORD);

            //this.PassWord = txtPwd.Text.Trim();
            //特殊用户直接进入主界面 梁吉 2016-08-25
            if ("TJSYSTEM".Equals(PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME.ToUpper()))
            {
                this.DialogResult = System.Windows.Forms.DialogResult.OK;
                this.Close();
                return;
            }
            Utility.ConfigHelper.SetConfiguration("LastLoginUser", txtInputNo.Text.Trim());//记录最后登录用户
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.Close();
        }

        private void lblLogin_Click(object sender, EventArgs e)
        {
            try
            {
                if (lbtabca.Visible)
                    LoginCa();
                else
                    LoginCommandExecute();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message + "登录异常，请检查数据库配置", "异常信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
        }

 

 

        private void FrmLogin_Activated(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtInputNo.Text.Trim()))
                txtInputNo.Focus();
            else
                txtPwd.Focus();
        }

        private void GetLoginData(string UserName)
        {
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql("select b.dept_name, a.name from staff_dict a,dept_dict b where a.dept_code=b.dept_code and upper(a.user_name)='" + UserName.ToUpper() + "'").Tables[0];
            if (dt.Rows.Count > 0)
            {
                lLoginData.Text = dt.Rows[0][0].ToString() + "   " + dt.Rows[0][1].ToString();
            }
        }

 

        private void FrmLogin_MouseDown(object sender, MouseEventArgs e)
        {

        }

        private void tableLayoutPanel1_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                FrmDbConfig frmDbConfig = new FrmDbConfig();
                frmDbConfig.ShowDialog();
                if (frmDbConfig.DialogResult == DialogResult.OK)
                {
                    try
                    {
                        //-- 梁吉 获取数据连接信息，增加方便跟踪的数据库连接方式
                        Utility.UntilityConstant.DataConnectionString = Utility.ConfigHelper.GetConfigConnectionStr();
                        ////如果是OracleClient方式，初始化全局连接
                        //Utility.Gloobal.SetOracleClientConnection();
                        if (string.IsNullOrEmpty(Utility.UntilityConstant.DataConnectionString))
                        {
                            Application.Exit();
                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        XtraMessageBox.Show(ex.Message);
                    }
                }
            }
        }

        private void lbzhlogin_Click(object sender, EventArgs e)
        {
            lbtabca.Visible = false;
            lbtablg.Visible = true;
            //this.lbcalogin.Font = new System.Drawing.Font("微软雅黑", 14F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbcalogin.Font = new System.Drawing.Font("微软雅黑", 14F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbcalogin.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(102)))), ((int)(((byte)(102)))), ((int)(((byte)(102)))));
            this.lbzhlogin.Font = new System.Drawing.Font("微软雅黑", 14F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbzhlogin.ForeColor = System.Drawing.Color.Black;
        }

        private void lbcalogin_Click(object sender, EventArgs e)
        {
            lbtabca.Visible = true ;
            lbtablg.Visible = false;
            //this.lbcalogin.Font = new System.Drawing.Font("微软雅黑", 14F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbzhlogin.Font = new System.Drawing.Font("微软雅黑", 14F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbzhlogin.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(102)))), ((int)(((byte)(102)))), ((int)(((byte)(102)))));
            this.lbcalogin.Font = new System.Drawing.Font("微软雅黑", 14F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbcalogin.ForeColor = System.Drawing.Color.Black;

            //此处增加CA登录用到的相关判断 预留/

        }

        private void txtInputNo_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)//如果输入的是回车键
            {
                GetLoginData(txtInputNo.Text);
                txtPwd.Focus();
            }
        }

        private void txtPwd_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)//如果输入的是回车键
            {
                try
                {
                    if (lbtabca.Visible)
                        LoginCa();
                    else
                        LoginCommandExecute();
                }
                catch (Exception ex)
                {
                    XtraMessageBox.Show(ex.Message + "登录异常，请检查数据库配置", "异常信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    Application.Exit();
                }
            }
        }

        private void lblLogin_MouseHover(object sender, EventArgs e)
        {
          string  path = System.IO.Directory.GetCurrentDirectory() + "\\Image\\" + "登录圆角.png";
          lblLogin.BackgroundImage = Image.FromFile(path);
        }

        private void lblLogin_MouseLeave(object sender, EventArgs e)
        {
           string path = System.IO.Directory.GetCurrentDirectory() + "\\Image\\" + "点击状态.png";
            lblLogin.BackgroundImage = Image.FromFile(path);
        }

        private void panel1_Paint(object sender, PaintEventArgs e)
        {

        }
    }
}
