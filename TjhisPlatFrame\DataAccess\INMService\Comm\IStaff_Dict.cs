﻿using System;
using System.Data;
using System.ServiceModel;
namespace INMService
{
    /// <summary>
    /// 接口层STAFF_DICT
    /// </summary>
    [ServiceContract]
    public interface ISTAFF_DICT
    {
        #region  成员方法
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        [OperationContract]
        bool Exists_STAFF_DICT(string ID);
        /// <summary>
        /// 增加一条数据
        /// </summary>
        [OperationContract]
        bool Add_STAFF_DICT(Model.STAFF_DICT model);
        /// <summary>
        /// 更新一条数据
        /// </summary>
        [OperationContract]
        bool Update_STAFF_DICT(Model.STAFF_DICT model);
        /// <summary>
        /// 删除数据
        /// </summary>
        [OperationContract]
        bool Delete_STAFF_DICT(string ID);
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        [OperationContract]
        Model.STAFF_DICT GetModel_STAFF_DICT(string ID);
        /// <summary>
        /// 获得数据列表
        /// </summary>
        [OperationContract]
        DataSet GetList_All_STAFF_DICT(string strWhere);
        /// <summary>
        /// 获得前几行数据
        /// </summary>
        [OperationContract]
        DataSet GetList_STAFF_DICT(int startIndex, int endIndex, string strWhere, string filedOrder);
        /// <summary>
        /// 获得ObservableCollection数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.STAFF_DICT> GetObservableCollection_All_STAFF_DICT(string strWhere);
        /// <summary>
        /// 获得ObservableCollection根据分页获得数据列表
        /// </summary>
        [OperationContract]
        System.Collections.ObjectModel.ObservableCollection<Model.STAFF_DICT> GetObservableCollection_STAFF_DICT(int startIndex, int endIndex, string strWhere, string filedOrder);
        /// <summary>
        /// 登录用户验证
        /// </summary>
        /// <param name="USER_NAME">用户名</param>
        /// <param name="PASSWORD">密码</param>
        /// <returns>返回值：-1-用户名错误，0-密码错误 1-正常登录，2-不可用账号</returns>
        [OperationContract]
        int LoginValidate_STAFF_DICT(string USER_NAME, string PASSWORD);
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        [OperationContract]
        Model.STAFF_DICT GetModelByUserName_STAFF_DICT(string UserName);
        #endregion  成员方法
    }
}