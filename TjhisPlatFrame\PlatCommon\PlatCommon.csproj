﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{32FDD55B-760C-4E3F-B2A1-2BE0C02CCE76}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>PlatCommon</RootNamespace>
    <AssemblyName>PlatCommon</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
    <DocumentationFile>..\..\TjhisPlatSource\TJHisPlatEXE\Client\PlatCommon.xml</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\DLL\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ChnCharInfo">
      <HintPath>E:\backup\ChnCharInfo.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Data.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.Data.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Printing.v19.1.Core, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.Printing.v19.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.Utils.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraBars.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraBars.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraEditors.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraGrid.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraGrid.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraLayout.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraLayout.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraPrinting.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraPrinting.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraReports.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v19.1.Extensions, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraReports.v19.1.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraTreeList.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\DevExpress.XtraTreeList.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="INMService, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\INMService.dll</HintPath>
    </Reference>
    <Reference Include="Model, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\Model.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NMService, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\NMService.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="Spire.Pdf, Version=6.1.4.2040, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\Spire.Pdf.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="Utility, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\TjhisPlatSource\TJHisPlatEXE\Client\Utility.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Base01\Cs01Base64.cs" />
    <Compile Include="Base01\Cs01DtExtend.cs" />
    <Compile Include="Base01\Cs01ExcelHelper.cs" />
    <Compile Include="Base01\Cs01FileHelper.cs" />
    <Compile Include="Base01\Cs01FilterList.cs" />
    <Compile Include="Base01\Cs01FtpClient.cs" />
    <Compile Include="Base01\Cs01Functions.cs" />
    <Compile Include="Base01\Cs01IniFile.cs" />
    <Compile Include="Base01\Cs01MoneryToChinese.cs" />
    <Compile Include="Base01\Cs01Pdf.cs" />
    <Compile Include="Base01\Cs01PhoneticWubi.cs" />
    <Compile Include="Base01\Cs01Security.cs" />
    <Compile Include="Base01\Cs01ShowInfo.cs" />
    <Compile Include="Base01\Cs01StringExtend.cs" />
    <Compile Include="Base01\Cs01Win32API.cs" />
    <Compile Include="Base01\GCCLass.cs" />
    <Compile Include="Base01\GCClassExtension.cs" />
    <Compile Include="Base02\Cs02BarCodeHelper.cs" />
    <Compile Include="Base02\Cs02Controls.cs" />
    <Compile Include="Base02\Cs02DataSetHelper.cs" />
    <Compile Include="Base02\Cs02DateTimeHelper.cs" />
    <Compile Include="Base02\Cs02ExtendDev.cs" />
    <Compile Include="Base02\Cs02ExtendTable.cs" />
    <Compile Include="Base02\Cs02HttpHelper.cs" />
    <Compile Include="Base02\Cs02JsonHelper.cs" />
    <Compile Include="Base02\Cs02MessageBox.cs" />
    <Compile Include="Base02\Cs02MyEval.cs" />
    <Compile Include="Base02\Cs02NotificationObject.cs" />
    <Compile Include="Base02\Cs02StringHelper.cs" />
    <Compile Include="Base02\Cs02TxtHelper.cs" />
    <Compile Include="Base02\Cs02XMLSetting.cs" />
    <Compile Include="Base02\DataTableHelper.cs" />
    <Compile Include="Common\AutoSettingIDProvider.cs" />
    <Compile Include="Common\ClassInsurVsMessage.cs" />
    <Compile Include="Common\ClosedLoopBusiness.cs" />
    <Compile Include="Common\Constants.cs" />
    <Compile Include="Common\Converter.cs" />
    <Compile Include="Common\CriticalValueBusiness.cs" />
    <Compile Include="Common\CriticaValueList.cs" />
    <Compile Include="Common\CustomMessage.cs" />
    <Compile Include="Common\database.cs" />
    <Compile Include="Common\DataGridViewHelper.cs" />
    <Compile Include="Common\DataSetHelper.cs" />
    <Compile Include="Common\DateTimeHelper.cs" />
    <Compile Include="Common\frmInputSettingDict.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Common\frmInputSettingDict.Designer.cs">
      <DependentUpon>frmInputSettingDict.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\frminputsettingoutp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Common\frminputsettingoutp.designer.cs">
      <DependentUpon>frminputsettingoutp.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\FrmInsuranceAdult.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Common\FrmInsuranceAdult.designer.cs">
      <DependentUpon>FrmInsuranceAdult.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\frmSelectWard.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Common\frmSelectWard.Designer.cs">
      <DependentUpon>frmSelectWard.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\IDHelper.cs" />
    <Compile Include="Common\IExamAppoints.cs" />
    <Compile Include="Common\ImageHelper.cs" />
    <Compile Include="Common\ListViewHelper.cs" />
    <Compile Include="Common\MeiKang_Imp.cs" />
    <Compile Include="Common\MeiKang_Transfer.cs" />
    <Compile Include="Common\MessageDisplay.cs" />
    <Compile Include="Common\MessageHelper.cs" />
    <Compile Include="Common\Pass4.cs" />
    <Compile Include="Common\PASS4Invoke.cs" />
    <Compile Include="Common\PublicFunction.cs" />
    <Compile Include="Common\PublicInsurVsByChargeType.cs" />
    <Compile Include="Common\Result.cs" />
    <Compile Include="Common\RMBCapitalization.cs" />
    <Compile Include="Common\Serialize.cs" />
    <Compile Include="Common\SMSInfo.cs" />
    <Compile Include="Common\SpellAndWbConfig.cs" />
    <Compile Include="Common\SqlConnectionPublic.cs" />
    <Compile Include="Common\TreatmentRoom.cs" />
    <Compile Include="Common\ucc2_his21_prepayment.cs" />
    <Compile Include="Common\ucc2_his33_card_manager.cs" />
    <Compile Include="Common\UdpMessage.cs" />
    <Compile Include="Common\ViewDataHelper.cs" />
    <Compile Include="Common\w_his33_input_pwd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Common\w_his33_input_pwd.Designer.cs">
      <DependentUpon>w_his33_input_pwd.cs</DependentUpon>
    </Compile>
    <Compile Include="Common\XtraGridViewHelper.cs" />
    <Compile Include="Common\XtraReportHelper.cs" />
    <Compile Include="Common\XtraTreeListHelper.cs" />
    <Compile Include="Comm\AuxiliaryDictionary.cs" />
    <Compile Include="Comm\BaseData.cs" />
    <Compile Include="Comm\CallBackArgs.cs" />
    <Compile Include="Comm\ClassKfzCdssLyzh.cs" />
    <Compile Include="Comm\class_obilling_public.cs" />
    <Compile Include="Comm\class_pat_info.cs" />
    <Compile Include="Comm\class_public_setprint.cs" />
    <Compile Include="Comm\CLINIC_MASTER.cs" />
    <Compile Include="Comm\ComConst.cs" />
    <Compile Include="Comm\DataType.cs" />
    <Compile Include="Comm\Externs.cs" />
    <Compile Include="Comm\GetInternetConStatus.cs" />
    <Compile Include="Comm\GfHis13GetFirstLetter.cs" />
    <Compile Include="Comm\HISConvertor.cs" />
    <Compile Include="Comm\IME.cs" />
    <Compile Include="Comm\InHospitalPatient.cs" />
    <Compile Include="Comm\NR_Code.cs" />
    <Compile Include="Comm\OperMode.cs" />
    <Compile Include="Comm\OutpPatient.cs" />
    <Compile Include="Comm\PatientCPath.cs" />
    <Compile Include="Comm\PodMedcine.cs" />
    <Compile Include="Comm\TreeViewHelper.cs" />
    <Compile Include="Comm\Utility.cs" />
    <Compile Include="Comm\WorkArg.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SysBase\ClassValueCopier.cs" />
    <Compile Include="SysBase\ClassValueCopier2.cs" />
    <Compile Include="SysBase\CommonFuntion.cs" />
    <Compile Include="SysBase\DockParentForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SysBase\DoctorParameter.cs" />
    <Compile Include="SysBase\ParentForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SysBase\ParentUserControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SysBase\SystemParm.cs" />
    <Compile Include="SysBase\ValidateValue.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Common\frmInputSettingDict.resx">
      <DependentUpon>frmInputSettingDict.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\frminputsettingoutp.resx">
      <DependentUpon>frminputsettingoutp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\FrmInsuranceAdult.resx">
      <DependentUpon>FrmInsuranceAdult.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\frmSelectWard.resx">
      <DependentUpon>frmSelectWard.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Common\w_his33_input_pwd.resx">
      <DependentUpon>w_his33_input_pwd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SysBase\ParentForm.resx">
      <DependentUpon>ParentForm.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\TjhisPlatSource\TjhisInterfaceCA\TjhisInterfaceCA.csproj">
      <Project>{1051351e-92ea-4f94-96f1-8eaa5cf84244}</Project>
      <Name>TjhisInterfaceCA</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>