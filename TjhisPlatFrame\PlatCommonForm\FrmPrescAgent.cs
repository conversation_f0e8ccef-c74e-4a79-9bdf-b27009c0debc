﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.WinExplorer;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors;
using System.Collections;
using PlatCommon.Common;
using NM_Service.NMService;

namespace PlatCommonForm
{
    public partial class FrmPrescAgent : PlatCommon.SysBase.ParentForm
    {
        public FrmPrescAgent()
        {
            InitializeComponent();
        }
        public string gs_patientId;
        public string gs_visitId;
        public string gs_clinicNo;
        public string gs_PrescNo;
        private string tname = string.Empty;
        public string gs_flag = string.Empty;//判断状态 1 处方继续打印
        private DataTable dtp = new DataTable();
        private DataTable dtpmi = new DataTable(); //主索引
        private void simpleButton1_Click(object sender, EventArgs e)
        {
            string tid_no = textId_No.Text;
            if(string.IsNullOrEmpty(tid_no))
            {
                XtraMessageBox.Show("患者身份证不能为空", "提示");
                return;
            }
            string tadress = textEditadress.Text;
            if (string.IsNullOrEmpty(tadress))
            {
                XtraMessageBox.Show("患者家庭住址不能为空", "提示");
                return;
            }
            string ttel = textEdittel.Text;
            if (string.IsNullOrEmpty(ttel))
            {
                XtraMessageBox.Show("患者联系电话不能为空", "提示");
                return;
            }
            string dbrname = textEdit1.Text;
            string dbrsex = textEdit2.Text;
            string dbrage = textEdit3.Text;
            string dbridno = textEdit4.Text;
            if (gs_visitId.Equals("0"))
            {
         
                if (string.IsNullOrEmpty(dbrname))
                {
                    XtraMessageBox.Show("代办人姓名不能为空", "提示");
                    return;
                }
                
                if (string.IsNullOrEmpty(dbrsex)&&!string.IsNullOrEmpty(dbrname))
                {
                    XtraMessageBox.Show("代办人性别不能为空", "提示");
                    return;
                }
                
                if (string.IsNullOrEmpty(dbrage) && !string.IsNullOrEmpty(dbrname))
                {
                    XtraMessageBox.Show("代办人年龄不能为空", "提示");
                    return;
                }
                
                if (string.IsNullOrEmpty(dbridno) && !string.IsNullOrEmpty(dbrname))
                {
                    XtraMessageBox.Show("代办人身份证不能为空", "提示");
                    return;
                }
            }
            string sqla = "select name   from pat_master_index where patient_id = '" + gs_patientId + "'";
            DataTable dta = new ServerPublicClient().GetDataBySql(sqla).Tables[0];
            Dictionary<string, string> idc = new Dictionary<string, string>();
            StringBuilder sb = new StringBuilder();
            if (dta != null && dta.Rows.Count > 0)
            {
                tname = dta.Rows[0][0].ToString();
            }
            if (dtp != null && dtp.Rows.Count > 0)
            {
                string tdate = dtp.Rows[0]["OPEN_DATE"].ToString();
                if (!string.IsNullOrEmpty(tdate))
                {
                    tdate = "to_date('" + tdate + "','yyyy-mm-dd hh24:mi:ss')";
                }

                sb.Append("update PHARMACY.DRUG_PRESC_INFORMATION set ");
                sb.Append(" AGENT_NAME = '" + dbrname + "',");
                sb.Append(" AGENT_SEX = '" + dbrsex + "',");
                sb.Append(" AGENT_AGE = '" + dbrage + "',");
                sb.Append(" AGENT_ID_NO = '" + dbridno + "' ");
                if (gs_visitId.Equals("0"))
                {
                    sb.Append(" where clinic_no ='" + gs_clinicNo + "' and PRESC_NO = '" + gs_PrescNo + "' ");
                    sb.Append(" and OPEN_DATE = " + tdate);
                }
                else
                {
                    sb.Append(" where PATIENT_ID ='" + gs_patientId + "' and PRESC_NO = '" + gs_PrescNo + "' and VISIT_ID = '" + gs_visitId + "' ");
                    sb.Append(" and OPEN_DATE = " + tdate);
                }
                idc.Add(sb.ToString(), "更新代办人信息失败!PHARMACY.DRUG_PRESC_INFORMATION");
            }
            else
            {                
                sb.Append("Insert Into PHARMACY.DRUG_PRESC_INFORMATION (");
                sb.Append(" PATIENT_ID,VISIT_ID,CLINIC_NO,NAME,PRESC_NO,");
                sb.Append(" AGENT_NAME,AGENT_SEX,AGENT_AGE,AGENT_ID_NO,OPEN_DATE)");
                sb.Append(" values( '" + gs_patientId + "','" + gs_visitId + "','" + gs_clinicNo + "','" + tname + "','" + gs_PrescNo + "',");
                sb.Append("'" + dbrname + "','" + dbrsex + "','" + dbrage + "','" + dbridno + "',sysdate)");
                idc.Add(sb.ToString(), "插入代办人信息失败!PHARMACY.DRUG_PRESC_INFORMATION");
            }
            string sqlb= " update pat_master_index set phone_number_home = '"+ttel+"',mailing_address = '"+tadress+ "',id_no ='" + tid_no + "' where patient_id = '" + gs_patientId + "'";
            idc.Add(sqlb, "更新病人主记录电话和住址失败！");
            string result = new OracleDAL.ServerPublic_Dao().SaveTable(idc);
            if (!string.IsNullOrEmpty(result))
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(result, "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            gs_flag = "1"; //判断状态 1 处方继续打印
            this.Close();
        }

        private void FrmPrescAgent_Load(object sender, EventArgs e)
        {
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            DataTable dtsex = new DataTable();
            dtsex.Columns.Add("SEXNAME", typeof(string));
            dtsex.Rows.Add(new object[] { "男" });
            dtsex.Rows.Add(new object[] { "女" });
            dtsex.Rows.Add(new object[] { "未知" });
            textEdit2.Properties.DataSource = dtsex;
            textEdit2.Properties.DisplayMember = "SEXNAME";
            textEdit2.Properties.ValueMember = "SEXNAME";
            StringBuilder sb = new StringBuilder();
            if (gs_visitId.Equals("0"))
            {
                sb.Append(" select AUDIT_DOT,AUDIT_DATETIME,AGENT_NAME,AGENT_SEX,AGENT_AGE,AGENT_ID_NO,OPEN_DATE ");
                sb.Append(" from PHARMACY.DRUG_PRESC_INFORMATION  ");
                sb.Append(" where clinic_no ='" + gs_clinicNo + "' and PRESC_NO = '" + gs_PrescNo + "'  order by OPEN_DATE desc");
            }
            else
            {

                sb.Append(" select AUDIT_DOT,AUDIT_DATETIME,AGENT_NAME,AGENT_SEX,AGENT_AGE,AGENT_ID_NO,OPEN_DATE ");
                sb.Append(" from PHARMACY.DRUG_PRESC_INFORMATION  ");
                sb.Append(" where PATIENT_ID ='" + gs_patientId + "' and PRESC_NO = '" + gs_PrescNo + "' and VISIT_ID = '" + gs_visitId + "'  order by OPEN_DATE desc");
            }
            dtp = spc.GetDataBySql(sb.ToString()).Tables[0];
            if (dtp != null && dtp.Rows.Count > 0)
            {
                this.textEdit1.Text = dtp.Rows[0]["AGENT_NAME"].ToString(); //代办人姓名
                this.textEdit2.Text = dtp.Rows[0]["AGENT_SEX"].ToString();//代办人性别
                this.textEdit3.Text = dtp.Rows[0]["AGENT_AGE"].ToString();//代办人年龄
                this.textEdit4.Text = dtp.Rows[0]["AGENT_ID_NO"].ToString();//代办人身份证
            }
            string sqla = " select phone_number_home,mailing_address,sex,id_no from pat_master_index where patient_id = '" + gs_patientId + "'";
            DataTable dta = spc.GetDataBySql(sqla.ToString()).Tables[0];
            if (dta != null && dta.Rows.Count > 0)
            {
                dtpmi = dta;
                this.textEditadress.Text = dtpmi.Rows[0]["mailing_address"].ToString();
                this.textEdittel.Text = dtpmi.Rows[0]["phone_number_home"].ToString();
                this.textId_No.Text = dtpmi.Rows[0]["ID_NO"].ToString();
            }
        }

    }
}
