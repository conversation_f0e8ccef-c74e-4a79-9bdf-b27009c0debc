﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Model;
using Utility.OracleODP;

namespace OracleDAL
{
    /*
     * 主窗体操作类
     */ 
    public class MainFormDao:IDisposable
    {
        public DataTable GetMenuTable(string user_Name,string his_Unit_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(@"SELECT distinct T1.APP_CODE,T1.NODE_CODE,T1.NODE_TITLE,T1.NODE_TYPE,T1.PARENT_NODE_CODE,T1.SERIAL_NO,T1.FILE_NAME, ");
            strSql.Append(" T1.FORM_ID,T1.ICON_FILE,T1.HAS_TOOLBAR_ITEM,T1.STATUS,T1.WIN_OPEN_MODE,T1.MEMO,T1.WIN_PARAMETER,T1.FORM_CONTROL,T1.MENU_GROUP,");
            strSql.Append(" T1.LARGE_ICON,T1.ICON_STYLE,T1.OPEN_PARAM,T1.DISPLAY_TEXT,T1.HIS_UNIT_CODE,T1.ICON_PRESS,");
            strSql.Append(" T6.APPLICATION_NAME,T6.Encrypt_Code,T6.APPLICATION_VERSION  FROM CPR.MR_APP_ENTRY T1  ");
            strSql.Append(" LEFT JOIN CPR.MR_RIGHT_VS_UI_OBJECT T2 ON T1.NODE_CODE = T2.UI_OBJECT_ID ");
            strSql.Append(" LEFT JOIN CPR.MR_ROLE_RIGHT T3 ON T2.RIGHT_ID = T3.RIGHT_ID LEFT JOIN CPR.MR_USER_ROLE T4 ON T3.ROLE_CODE = T4.ROLE_CODE  ");
            strSql.Append(" LEFT JOIN COMM.STAFF_DICT T5 ON UPPER(T5.USER_NAME) = UPPER(T4.DB_USER) LEFT JOIN CPR.MR_ROLE_DICT T7 ON T4.ROLE_CODE=T7.ROLE_CODE AND T7.HIS_UNIT_CODE=T1.HIS_UNIT_CODE  ");
            strSql.Append(" LEFT JOIN CPR.MR_RIGHT_DICT T8 ON T8.RIGHT_ID=T2.RIGHT_ID    ");
            strSql.Append(" LEFT JOIN COMM.encrypt_dict T6 ON T6.APPLICATION_CODE = T1.App_Code  AND T1.HIS_UNIT_CODE=T6.HIS_UNIT_CODE ");
            strSql.Append(" WHERE t1.App_Code=t6.APPLICATION_CODE and T2.ENABLE = 1 AND T1.STATUS = 1 AND T1.FORM_CONTROL IS NULL AND UPPER(T1.NODE_TYPE)<>'MODULE' ");
            strSql.Append($"  AND T1.HIS_UNIT_CODE= '{his_Unit_Code}' AND T5.USER_NAME = '{user_Name}' ORDER BY T1.APP_CODE,T1.SERIAL_NO,T1.NODE_CODE");
            // UPPER(T1.PARENT_NODE_CODE)<>'PARENT' AND
            //sql += " group by T1.App_Code,T6.APPLICATION_NAME,T6.Encrypt_Code,T6.HIS_UNIT_CODE order by T1.App_Code ";
            try
            {
                using (OracleBaseClass db = new OracleBaseClass())
                {
                    if (db.OpenDB())
                    {
                        DataSet ds = db.SelectDataSet(strSql.ToString());
                        db.CloseDB();
                        if (ds != null && ds.Tables.Count > 0)
                        {
                            return ds.Tables[0];
                        }
                        return null;
                    }
                    else
                    {
                        
                        MessageBox.Show("打开数据库连接失败！", "提示");
                        return null;
                    }

                }

            }
            catch (Exception ex)
            {
                MessageBox.Show("查询用户信息异常!" + ex.ToString() + "|SQL:" + strSql.ToString(), "提示");
                return null;
            }
        }

        #region IDisposable Support
        private bool disposedValue = false; // 要检测冗余调用

        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    // TODO: 释放托管状态(托管对象)。
                    
                }
                GC.SuppressFinalize(this);
                // TODO: 释放未托管的资源(未托管的对象)并在以下内容中替代终结器。
                // TODO: 将大型字段设置为 null。

                disposedValue = true;
            }
        }

        // TODO: 仅当以上 Dispose(bool disposing) 拥有用于释放未托管资源的代码时才替代终结器。
        ~MainFormDao()
        {
            // 请勿更改此代码。将清理代码放入以上 Dispose(bool disposing) 中。
            Dispose(false);
        }

        // 添加此代码以正确实现可处置模式。
        public void Dispose()
        {
            // 请勿更改此代码。将清理代码放入以上 Dispose(bool disposing) 中。
            Dispose(true);
            // TODO: 如果在以上内容中替代了终结器，则取消注释以下行。
            // GC.SuppressFinalize(this);
        }
        #endregion

    }
}
