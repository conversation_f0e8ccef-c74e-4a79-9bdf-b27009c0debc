﻿using Microsoft.International.Converters.PinYinConverter;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;

namespace PlatCommon.SysBase
{
    public class CommonFuntion
    {
        /// <summary>
        /// 转换icon图片
        /// </summary>
        /// <param name="obm"></param>
        /// <param name="preserve"></param>
        /// <returns></returns>
        static public Icon BitmapToIcon(Bitmap obm, bool preserve, int ICON_W, int ICON_H)
        {
            Bitmap bm;
            // if not preserving aspect
            if (!preserve)        // if not preserving aspect
                bm = new Bitmap(obm, ICON_W, ICON_H);  //   rescale from original bitmap

            // if preserving aspect drop excess significance in least significant direction
            else          // if preserving aspect
            {
                Rectangle rc = new Rectangle(0, 0, ICON_W, ICON_H);
                if (obm.Width >= obm.Height)   //   if width least significant
                {          //     rescale with width based on max icon height
                    bm = new Bitmap(obm, (ICON_H * obm.Width) / obm.Height, ICON_H);
                    rc.X = (bm.Width - ICON_W) / 2;  //     chop off excess width significance
                    if (rc.X < 0) rc.X = 0;
                }
                else         //   if height least significant
                {          //     rescale with height based on max icon width
                    bm = new Bitmap(obm, ICON_W, (ICON_W * obm.Height) / obm.Width);
                    rc.Y = (bm.Height - ICON_H) / 2; //     chop off excess height significance
                    if (rc.Y < 0) rc.Y = 0;
                }
                bm = bm.Clone(rc, bm.PixelFormat);  //   bitmap for icon rectangle
            }

            // create icon from bitmap
            Icon icon = Icon.FromHandle(bm.GetHicon()); // create icon from bitmap
            bm.Dispose();        // dispose of bitmap
            return icon;        // return icon
        }
    }
}
