﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using System.ComponentModel;
namespace Model
{

    /// <summary>
    ///费用模板主记录
    /// </summary>
    [DataContract]
    public class BILL_PATTERN_MASTER : NotificationObject
    {

        /// <summary>
        /// 序号
        /// </summary>		
        private decimal _serial_no;
        [DataMember]
        public decimal SERIAL_NO
        {
            get { return _serial_no; }
            set
            {
                if (_serial_no != value)
                {
                    _serial_no = value;
                    this.RaisePropertyChanged("SERIAL_NO");
                }
            }
        }
        /// <summary>
        /// 模板名称
        /// </summary>		
        private string _pattern_name;
        [DataMember]
        public string PATTERN_NAME
        {
            get { return _pattern_name; }
            set
            {
                if (_pattern_name != value)
                {
                    _pattern_name = value;
                    this.RaisePropertyChanged("PATTERN_NAME");
                }
            }
        }
        /// <summary>
        /// 输入码
        /// </summary>		
        private string _input_code;
        [DataMember]
        public string INPUT_CODE
        {
            get { return _input_code; }
            set
            {
                if (_input_code != value)
                {
                    _input_code = value;
                    this.RaisePropertyChanged("INPUT_CODE");
                }
            }
        }
        /// <summary>
        /// 五笔码
        /// </summary>		
        private string _input_code_wb;
        [DataMember]
        public string INPUT_CODE_WB
        {
            get { return _input_code_wb; }
            set
            {
                if (_input_code_wb != value)
                {
                    _input_code_wb = value;
                    this.RaisePropertyChanged("INPUT_CODE_WB");
                }
            }
        }
        /// <summary>
        /// 部门代码
        /// </summary>		
        private string _dept_code;
        [DataMember]
        public string DEPT_CODE
        {
            get { return _dept_code; }
            set
            {
                if (_dept_code != value)
                {
                    _dept_code = value;
                    this.RaisePropertyChanged("DEPT_CODE");
                }
            }
        }

    }
}