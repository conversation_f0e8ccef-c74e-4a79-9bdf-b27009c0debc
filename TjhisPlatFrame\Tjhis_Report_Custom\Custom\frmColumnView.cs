﻿using PlatCommon.SysBase;
using System;
using System.Data;
using Tjhis.Report.Custom.Srv;

namespace Tjhis.Report.Custom.Custom
{
    public partial class FrmColumnView : ParentForm
    {
        srvT srv = new srvT();
        public string tableName { get; set; }
        public DataTable dtCols { get; set; }
        public FrmColumnView()
        {
            InitializeComponent();
        }

        public FrmColumnView(string tableName)
        {
            InitializeComponent();
            this.tableName = tableName;
            this.Text = tableName;
            
        }
        private void frmColumnView_Load(object sender, EventArgs e)
        {
            labTableName.Caption = tableName;

            dtCols = srv.GetTableColums(tableName).Tables[0];
            dtCols.Columns.Add("COL_SEL", Type.GetType("System.String"));
            dtCols.Columns["COL_SEL"].ReadOnly = false;

            gridControl1.DataSource = dtCols;
        }

        private void barLargeButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barCheckItem1_CheckedChanged(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            string ischeck = "";
            if (barCheckItem1.Checked)
                ischeck = "1";
            else
            {
                ischeck = "";
            }
            
            for (int i = 0; i < gridView1.RowCount; i++)
            {
                gridView1.SetRowCellValue(i, gridColumn1,ischeck);
                DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs ev = new DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs(i,gridColumn1,ischeck);
                if (ColumnChangeEvent != null)
                {
                    ColumnChangeEvent(this.gridView1, ev);
                }
            }
        }
        public delegate void ColumnChange(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e);
        public event ColumnChange ColumnChangeEvent;
        private void gridView1_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            
        }

        private void gridView1_CellValueChanging(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (e.Column.FieldName.Equals("COL_SEL"))
            {
                if (ColumnChangeEvent != null)
                {
                    ColumnChangeEvent(sender, e);
                }
            }
        }

        public void SetFiledStatus(string name,string status)
        {
            DataRow[] dr = dtCols.Select(" column_name = '"+name+"'");
            foreach(DataRow d in dr)
            {
                d["col_sel"] = status;
            }
        }
        
    }
}
