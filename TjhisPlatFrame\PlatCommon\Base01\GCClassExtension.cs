﻿using System;
using System.Data;
using System.Reflection;
using System.Collections.Generic;

namespace PlatCommon.Base01
{
    /// <summary>
    /// 索引类型结构
    /// </summary>
    public struct IndexType
    {
        /// <summary>
        /// 索引下标
        /// </summary>
        public int index;
        /// <summary>
        /// 数据类型
        /// </summary>
        public int type;
    }

    /// <summary>
    /// 静态扩展类
    /// </summary>
    public static class GCClassExtention
    {
        /// <summary>
        /// 取键和值为字符串的字典的值，不存在时返回为空字符串
        /// </summary>
        /// <param name="dic">Dictionary&lt;string, string&gt;</param>
        /// <param name="key">键值</param>
        /// <returns>string</returns>
        public static string CStr(this Dictionary<string, string> dic, string key)
        {
            if (!dic.ContainsKey(key)) return "";
            return dic[key];
        }

        /// <summary>
        /// 取键为字符串的字典的值，不存在时返回为default(T)
        /// </summary>
        /// <typeparam name="T">泛型</typeparam>
        /// <param name="dic">Dictionary&lt;string, T&gt;</param>
        /// <param name="key">键值</param>
        /// <returns>T</returns>
        public static T CValue<T>(this Dictionary<string, T> dic, string key)
        {
            if (!dic.ContainsKey(key)) return default(T);
            return dic[key];
        }

        /// <summary>
        /// 取DateTime的字符串，DateTime.MinValue为""
        /// </summary>
        /// <param name="date">DateTime</param>
        /// <returns>string</returns>
        public static string CDateStr(this DateTime date)
        {
            if (date == DateTime.MinValue) return "";
            return date.ToString("yyyy-MM-dd");
        }

        /// <summary>
        /// 取DateTime的字符串，DateTime.MinValue为""
        /// </summary>
        /// <param name="time">DateTime</param>
        /// <returns>string</returns>
        public static string CDateTimeStr(this DateTime time)
        {
            if (time == DateTime.MinValue) return "";
            return time.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 取对象的字符串，null对象为""
        /// </summary>
        /// <param name="obj">Object</param>
        /// <returns>string</returns>
        /// <example>string str = obj.CStr();</example>
        public static string CStr(this Object obj)
        {
            if (obj == null) return "";
            return obj.ToString();
        }

        /// <summary>
        /// 取对象的int值，null对象为
        /// </summary>
        /// <param name="obj">Object</param>
        /// <returns>int</returns>
        /// <example>int val = obj.CInt();</example>
        public static int CInt(this Object obj)
        {
            if (obj == null) return 0;
            int v = 0;
            int.TryParse(obj.ToString(), out v);
            return v;
        }

        /// <summary>
        /// 取对象的Double值，null对象为0
        /// </summary>
        /// <param name="obj">Object</param>
        /// <returns>double</returns>
        /// <example>double val = obj.CDouble();</example>
        public static double CDouble(this Object obj)
        {
            if (obj == null) return 0;
            double v = 0.0;
            double.TryParse(obj.ToString(), out v);
            return v;
        }

        /// <summary>
        /// 取对象的Decimal值，null对象为0
        /// </summary>
        /// <param name="obj">Object</param>
        /// <returns>decimal</returns>
        /// <example>decimal val = obj.CDecimal();</example>
        public static decimal CDecimal(this Object obj)
        {
            if (obj == null) return 0;
            decimal v = 0M;
            decimal.TryParse(obj.ToString(), out v);
            return v;
        }

        /// <summary>
        /// 取对象的DateTime值，非日期型时为DateTime.MinValue
        /// </summary>
        /// <param name="obj">Object</param>
        /// <returns>decimal</returns>
        /// <example>DateTime time = obj.CDateTime();</example>
        public static DateTime CDateTime(this Object obj)
        {
            if (obj == null) return DateTime.MinValue;
            DateTime date = DateTime.MinValue;
            DateTime.TryParse(obj.ToString(), out date);
            return date;
        }

        /// <summary>
        /// 取DataTable中Cell值的int值，非整数时为0
        /// </summary>
        /// <param name="dr">DataRow</param>
        /// <param name="fieldname">string</param>
        /// <returns>int</returns>
        public static int CInt(this DataRow dr, string fieldname)
        {
            if (dr == null) return 0;
            Object obj = dr[fieldname];
            if (obj == DBNull.Value) return 0;
            return Convert.ToInt32(obj);
        }

        /// <summary>
        /// 取DataTable中Cell值的int值，非整数时为0
        /// </summary>
        /// <param name="dr">DataRow</param>
        /// <param name="index">index</param>
        /// <returns>int</returns>
        public static int CInt(this DataRow dr, int index)
        {
            if (dr == null) return 0;
            Object obj = dr[index];
            if (obj == DBNull.Value) return 0;
            return Convert.ToInt32(obj);
        }

        /// <summary>
        /// 取DataTable中Cell值的double值，非整数时为0
        /// </summary>
        /// <param name="dr">DataRow</param>
        /// <param name="fieldname">string</param>
        /// <returns>double</returns>
        public static double CDouble(this DataRow dr, string fieldname)
        {
            if (dr == null) return 0;
            Object obj = dr[fieldname];
            if (obj == DBNull.Value) return 0;
            return Convert.ToDouble(obj);
        }

        /// <summary>
        /// 取DataTable中Cell值的double值，非整数时为0
        /// </summary>
        /// <param name="dr">DataRow</param>
        /// <param name="fieldname">string</param>
        /// <returns>decimal</returns>
        public static decimal CDecimal(this DataRow dr, string fieldname)
        {
            if (dr == null) return 0;
            Object obj = dr[fieldname];
            if (obj == DBNull.Value) return 0;
            return Convert.ToDecimal(obj);
        }


        /// <summary>
        /// 取DataTable中Cell值的double值，非整数时为0
        /// </summary>
        /// <param name="dr">DataRow</param>
        /// <param name="index">int</param>
        /// <returns>double</returns>
        public static double CDouble(this DataRow dr, int index)
        {
            if (dr == null) return 0;
            Object obj = dr[index];
            if (obj == DBNull.Value) return 0;
            return Convert.ToDouble(obj);
        }

        /// <summary>
        /// 将行数据转成对应的实体(属性转换)
        /// </summary>
        /// <typeparam name="T">泛型</typeparam>
        /// <param name="dataRow">DataRow</param>
        /// <returns>T</returns>
        /// <example>string sql = $"select * from CPR.MR_TEMPLET_VERIFY_FLOW where templet_id={GCSQL.SqlString(templetId)}";<br/>MODEL_MR_TEMPLET_VERIFY_FLOW item = OracleSrv.ItemLoad&lt;MODEL_MR_TEMPLET_VERIFY_FLOW&gt;(sql);;</example>
        public static T ToEntity<T>(this DataRow dataRow) where T : class, new()
        {
            if (dataRow == null)
            {
                return default(T);
            }
            T entity = new T();
            PropertyInfo[] pInfos = entity.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
            foreach (PropertyInfo pinfo in pInfos)
            {
                DataColumnCollection cols = dataRow.Table.Columns;
                string fieldName = pinfo.Name;
                int i = cols.IndexOf(fieldName);
                object value;
                Type type2;
                if (i >= 0 && dataRow[i] != DBNull.Value)
                {
                    value = dataRow[i];
                    type2 = pinfo.PropertyType;
                    if (type2 != cols[i].DataType)
                    {
                        if (type2.FullName.StartsWith("System.Nullable`1"))
                        {
                            if (!type2.FullName.Contains(cols[i].DataType.FullName))
                            {
                                value = Convert.ChangeType(value, Nullable.GetUnderlyingType(type2));
                            }
                        }
                        else
                        {
                            value = Convert.ChangeType(value, type2);
                        }
                    }
                    pinfo.SetValue(entity, value, null);
                }
            }
            return entity;
        }

        /// <summary>
        /// 将行数据转成对应的实体(字段转换)
        /// </summary>
        /// <typeparam name="T">泛型</typeparam>
        /// <param name="dataRow">DataRow</param>
        /// <returns>T</returns>
        public static T ToEntityFields<T>(this DataRow dataRow) where T : class, new()
        {
            if (dataRow == null)
            {
                return default(T);
            }
            T entity = new T();
            FieldInfo[] fInfos = entity.GetType().GetFields();
            foreach (FieldInfo finfo in fInfos)
            {
                DataColumnCollection cols = dataRow.Table.Columns;
                string fieldName = finfo.Name;
                int i = cols.IndexOf(fieldName);
                object value;
                Type type2;
                if (i >= 0 && dataRow[i] != DBNull.Value)
                {
                    value = dataRow[i];
                    type2 = finfo.FieldType;
                    if (type2.IsValueType && type2 != cols[i].DataType)
                    {
                        value = Convert.ChangeType(value, type2);
                    }
                    finfo.SetValue(entity, value);
                }
            }
            return entity;
        }


 
        /// <summary>
        /// 将实体数据写入DataRow
        /// </summary>
        /// <typeparam name="T">泛型</typeparam>
        /// <param name="dataRow">DataRow</param>
        /// <param name="obj">实体对象</param>
        /// <returns>写入的列数</returns>
        public static int SetEntityToDataRow<T>(DataRow dataRow, T obj)
        {
            PropertyInfo[] pInfos = obj.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
            int count = 0;
            foreach (PropertyInfo pinfo in pInfos)
            {
                DataColumnCollection cols = dataRow.Table.Columns;
                string fieldName = pinfo.Name;
                int i = cols.IndexOf(fieldName);
                object value;
                Type type2;
                if (i >= 0)
                {
                    count++;
                    value = pinfo.GetValue(obj, null);
                    if (value is null)
                    {
                        dataRow[fieldName] = DBNull.Value;
                    }
                    else
                    {
                        type2 = pinfo.PropertyType;
                        if (type2 != cols[i].DataType)
                        {
                            value = Convert.ChangeType(value, type2);
                        }
                        dataRow[fieldName] = value;
                    }
                }
            }
            return count;
        }
    }
}
