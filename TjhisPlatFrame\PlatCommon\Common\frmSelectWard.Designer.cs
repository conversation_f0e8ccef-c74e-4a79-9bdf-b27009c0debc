﻿using System.ComponentModel;
using System;
using System.Drawing;
using System.Windows.Forms;
namespace PlatCommon.Common
{
    partial class frmSelectWard
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.simpleButton2 = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButton1 = new DevExpress.XtraEditors.SimpleButton();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.GROUP_CODE = new DevExpress.XtraGrid.Columns.GridColumn();
            this.DEPT_NAME = new DevExpress.XtraGrid.Columns.GridColumn();
            ((ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((ISupportInitialize)(this.gridControl1)).BeginInit();
            ((ISupportInitialize)(this.gridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.simpleButton2);
            this.panelControl1.Controls.Add(this.simpleButton1);
            this.panelControl1.Dock = DockStyle.Bottom;
            this.panelControl1.Location = new Point(0, 246);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new Size(331, 58);
            this.panelControl1.TabIndex = 0;
            // 
            // simpleButton2
            // 
            this.simpleButton2.Location = new Point(161, 10);
            this.simpleButton2.Name = "simpleButton2";
            this.simpleButton2.Size = new Size(75, 36);
            this.simpleButton2.TabIndex = 1;
            this.simpleButton2.Text = "关闭";
            this.simpleButton2.Visible = false;
            this.simpleButton2.Click += new EventHandler(this.simpleButton2_Click);
            // 
            // simpleButton1
            // 
            this.simpleButton1.Location = new Point(246, 10);
            this.simpleButton1.Name = "simpleButton1";
            this.simpleButton1.Size = new Size(75, 36);
            this.simpleButton1.TabIndex = 0;
            this.simpleButton1.Text = "确定";
            this.simpleButton1.Click += new EventHandler(this.simpleButton1_Click);
            // 
            // gridControl1
            // 
            this.gridControl1.Dock = DockStyle.Fill;
            this.gridControl1.Location = new Point(0, 0);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new Size(331, 246);
            this.gridControl1.TabIndex = 1;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.GROUP_CODE,
            this.DEPT_NAME});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.DoubleClick += new EventHandler(this.simpleButton1_Click);
            // 
            // GROUP_CODE
            // 
            this.GROUP_CODE.AppearanceHeader.Options.UseTextOptions = true;
            this.GROUP_CODE.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.GROUP_CODE.Caption = "编码";
            this.GROUP_CODE.FieldName = "GROUP_CODE";
            this.GROUP_CODE.Name = "GROUP_CODE";
            this.GROUP_CODE.OptionsColumn.AllowEdit = false;
            this.GROUP_CODE.Visible = true;
            this.GROUP_CODE.VisibleIndex = 0;
            this.GROUP_CODE.Width = 106;
            // 
            // DEPT_NAME
            // 
            this.DEPT_NAME.AppearanceHeader.Options.UseTextOptions = true;
            this.DEPT_NAME.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.DEPT_NAME.Caption = "名称";
            this.DEPT_NAME.FieldName = "DEPT_NAME";
            this.DEPT_NAME.Name = "DEPT_NAME";
            this.DEPT_NAME.OptionsColumn.AllowEdit = false;
            this.DEPT_NAME.Visible = true;
            this.DEPT_NAME.VisibleIndex = 1;
            this.DEPT_NAME.Width = 207;
            // 
            // frmSelectWard
            // 
            this.AutoScaleDimensions = new SizeF(7F, 14F);
            this.AutoScaleMode =AutoScaleMode.Font;
            this.ClientSize = new Size(331, 304);
            this.ControlBox = false;
            this.Controls.Add(this.gridControl1);
            this.Controls.Add(this.panelControl1);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frmSelectWard";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "选择护理单元";
            this.TopMost = true;
            ((ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            ((ISupportInitialize)(this.gridControl1)).EndInit();
            ((ISupportInitialize)(this.gridView1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.SimpleButton simpleButton2;
        private DevExpress.XtraEditors.SimpleButton simpleButton1;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn GROUP_CODE;
        private DevExpress.XtraGrid.Columns.GridColumn DEPT_NAME;
    }
}