﻿
using DevExpress.XtraEditors;
using PlatCommon.SysBase;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
namespace Tj_Update
{
    public partial class Frm_Login : Form
    {
        String AppName = "";
        public String UserName = "";
        public String PassWord = "";
        int ii_err_cs=0,ii_pw_err;//密码错误次数判断 设置后 错误多次后自动关闭
        string ls_init_pw;// 是否判断初始值密码 给予提示 修改密码
        
        public Frm_Login()
        {
            InitializeComponent();
            //txtInputNo.Focus();
        }
        

        
        private void txtInputNo_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)//如果输入的是回车键
            {
                txtPwd.Focus();
            }
        }

        private void lblLogin_Click(object sender, EventArgs e)
        {
            try
            {
                LoginCommandExecute();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("登录异常，请检查数据看配置", "异常信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
            
        }

       

        private void lblExit_Click(object sender, EventArgs e)
        {
            this.Close();
            //Application.Exit();
        }
        public void LoginCommandExecute()
        {
            
            if (String.IsNullOrEmpty(txtInputNo.Text))
            {
                XtraMessageBox.Show("请输入用户！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtInputNo.Focus();
                return;
            }
            if (String.IsNullOrEmpty(txtPwd.Text))
            {
                XtraMessageBox.Show("请输密码！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtPwd.Focus();
                return;
            }
            
            NM_Service.NMService.STAFF_DICTClient client = new NM_Service.NMService.STAFF_DICTClient();
            int result = client.LoginValidate_STAFF_DICT(txtInputNo.Text.ToUpper(), txtPwd.Text.ToUpper());
            switch (result)
            {
                case -1:
                    XtraMessageBox.Show("用户不存在！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break;
                case 0:
                    ii_err_cs++;
                    XtraMessageBox.Show("密码错误！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    if (ii_err_cs >= ii_pw_err && ii_pw_err > 0) 
                    {
                        XtraMessageBox.Show("密码错误！超过" +ii_err_cs.ToString()+"次", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        this.Close();
                    }
                    break;
                case 1:

                    if (txtInputNo.Text.ToUpper().Equals(txtPwd.Text.ToUpper()) && ls_init_pw.Equals("1")) 
                    {
                        XtraMessageBox.Show("你的密码为初始密码，请登陆后修改密码！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }

                    SystemParm.LoginUser = client.GetModelByUserName_STAFF_DICT(txtInputNo.Text.ToUpper());
                    this.UserName = txtInputNo.Text.Trim();
                    this.PassWord = txtPwd.Text.Trim();
                    Utility.ConfigHelper.SetConfiguration("LastLoginUser", txtInputNo.Text.Trim());
                    //验证通过开始更新窗体
                    DialogResult = System.Windows.Forms.DialogResult.OK;
                    break;
                case 2:
                    XtraMessageBox.Show("用户已锁定，请联系管理员！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    break;
                default:
                    this.Close();
                    XtraMessageBox.Show("未知错误！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break;
            }

        }
        
        private void txtPwd_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)//如果输入的是回车键
            {
                try
                {
                    LoginCommandExecute();
                }
                catch (Exception ex)
                {
                    XtraMessageBox.Show("登录异常，请检查数据看配置", "异常信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    Application.Exit();
                }
            }
        }

        private void Frm_Login_Load(object sender, EventArgs e)
        {
            try
            {
                //加载版本号
                string strsql = " select * from  hospital_config where rownum=1";
                DataSet ds = new OracleDAL.ServerPublic_Dao().GetDataBySql(strsql);
                if (ds != null && ds.Tables[0].Rows.Count > 0)
                {
                    this.lblVersion.Text = "版本号：" + ds.Tables[0].Rows[0]["AUTHORIZED_KEY"].ToString();
                    SystemParm.version= ds.Tables[0].Rows[0]["AUTHORIZED_KEY"].ToString(); //版本号赋值给全局变量用来给各个模块使用
                }
                txtInputNo.Text = Utility.ConfigHelper.GetConfiguration("LastLoginUser");
                ii_pw_err = Int32.Parse(PlatCommon.SysBase.SystemParm.GetParaValue("PW_ERR", "*", "*", "*", "0"));
                ls_init_pw = (PlatCommon.SysBase.SystemParm.GetParaValue("INIT_PW", "*", "*", "*", "0"));// 是否判断初始值密码 给予提示 修改密码
            }
            catch (Exception ex)
            {
               // XtraMessageBox.Show("" + ex.Message);
            }
            
        }

        private void Frm_Login_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                FrmDbConfig frmDbConfig = new FrmDbConfig();
                frmDbConfig.ShowDialog();
                if (frmDbConfig.DialogResult == DialogResult.OK)
                {
                    try
                    {
                        //-- 梁吉 获取数据连接信息，增加方便跟踪的数据库连接方式
                        Utility.UntilityConstant.DataConnectionString = Utility.ConfigHelper.GetConfigConnectionStr();
                        ////如果是OracleClient方式，初始化全局连接
                        //Utility.Gloobal.SetOracleClientConnection();
                        if (string.IsNullOrEmpty(Utility.UntilityConstant.DataConnectionString))
                        {
                            Application.Exit();
                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        XtraMessageBox.Show(ex.Message);
                    }
                }
            }
        }

        private void Frm_Login_Activated(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtInputNo.Text.Trim()))
                txtInputNo.Focus();
            else
                txtPwd.Focus();
        }
    }
}
